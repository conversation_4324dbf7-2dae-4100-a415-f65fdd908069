package dss

import (
	"app/org/scs/erpv2/api/service/auth"

	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"log"
	"net/url"
)

type Dss struct{}

// Dss.Auth is called by the API as /erp/dss/Auth with post body {"name": "foo"}
func (this *Dss) Auth(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var deviceSN string
	var authTime string

	queryUrl, _ := url.Parse(req.Url)
	form := queryUrl.Query()
	if nil != form {
		if nil != form["device"] {
			deviceSN = form["device"][0]
		}

		if nil != form["authTime"] {
			authTime = form["authTime"][0]
		}
	}

	log.Printf("[%v]Auth <- %v, authTime: %v", deviceSN, auth.User(ctx).GetClientIp(), authTime)

	data := map[string]interface{}{
		"access_token":  "access_token_" + deviceSN,
		"refresh_token": "refresh_token_" + deviceSN,
	}
	return response.SendResp2Dss(rsp, data)
}

type SignInReq struct {
	Device      string `json:"device"`
	DriverCode  string `json:"driverCode"`
	SignTimeUtc string `json:"signTime"`
	TestValue   int64  `json:"testValue"`
	Temperature string `json:"temperature"`
	Emotion     int64  `json:"emotion"`
	ChannelCode string `json:"channelCode"`
	HealthCheck int64  `json:"healthCheck"`
	HealthData  struct {
		SystolicPressure  int64 `json:"systolicPressure"`
		DiastolicPressure int64 `json:"diastolicPressure"`
		OxygenValue       int64 `json:"OxygenValue"`
		HeartRate         int64 `json:"heartRate"`
	} `json:"healthData"`
	QuestionRecord struct {
		ChannelId          string `json:"channelId"`
		ChannelCode        string `json:"channelCode"`
		ChannelName        string `json:"channelName"`
		DeviceId           string `json:"deviceId"`
		OrgId              string `json:"orgId"`
		OrgName            string `json:"orgName"`
		QuestionAnswerList struct {
			AnswerCode    string `json:"answerCode"`
			AnswerContent string `json:"answerContent"`
			AnswerDate    string `json:"answerDate"`
			DetailId      string `json:"detailId"`
			QuestionId    string `json:"questionId"`
			QuestionNo    string `json:"questionNo"`
			QuestionTitle string `json:"questionTitle"`
			QuestionType  string `json:"questionType"`
			RecordId      string `json:"recordId"`
		} `json:"questionAnswerList"`
		SubjectId   string `json:"subjectId"`
		SubjectName string `json:"subjectName"`
		UserCode    string `json:"userCode"`
		UserId      string `json:"userId"`
		UserName    string `json:"userName"`
	} `json:"questionRecord"`
}

// Dss.SignIn is called by the API as /erp/dss/SignIn with post body {"name": "foo"}
func (this *Dss) SignIn(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var item SignInReq
	if err := json.Unmarshal([]byte(req.Body), &item); nil != err {
		log.Printf(" json.Unmarshal[err]: %v, req.Body: %v", err, string(req.Body))
		return response.SendResp2Dss(rsp, nil)
	}

	log.Printf("[%v]reqBody: %v", item.Device, string(req.Body))

	data := map[string]interface{}{
		"testResult": 0,
		"tempResult": 0,
		"healthResult": map[string]interface{}{
			"systolicPressure":  1,
			"diastolicPressure": 1,
			"OxygenValue":       1,
			"heartRate":         1,
		},
		"result": 0,
		"quesId": 0,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.SignOut is called by the API as /erp/dss/SignOut with post body {"name": "foo"}
func (this *Dss) SignOut(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return response.SendResp2Dss(rsp, nil)
}

// Dss.GetScreensaverInfo is called by the API as /erp/dss/GetScreensaverInfo with post body {"name": "foo"}
func (this *Dss) GetScreensaverInfo(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.SearchVehicles is called by the API as /erp/dss/SearchVehicles with post body {"name": "foo"}
func (this *Dss) SearchVehicles(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.GetRoutineCheckData is called by the API as /erp/dss/GetRoutineCheckData with post body {"name": "foo"}
func (this *Dss) GetRoutineCheckData(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.UpRoutineCheckData is called by the API as /erp/dss/UpRoutineCheckData with post body {"name": "foo"}
func (this *Dss) UpRoutineCheckData(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.GetDriverPlans is called by the API as /erp/dss/GetDriverPlans with post body {"name": "foo"}
func (this *Dss) GetDriverPlans(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.GetDriverPromiseRecord is called by the API as /erp/dss/GetDriverPromiseRecord with post body {"name": "foo"}
func (this *Dss) GetDriverPromiseRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.SaveDriverPromiseRecord is called by the API as /erp/dss/SaveDriverPromiseRecord with post body {"name": "foo"}
func (this *Dss) SaveDriverPromiseRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.GetDriverNotify is called by the API as /erp/dss/GetDriverNotify with post body {"name": "foo"}
func (this *Dss) GetDriverNotify(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.GetRoutineCheckDataRecord is called by the API as /erp/dss/GetRoutineCheckDataRecord with post body {"name": "foo"}
func (this *Dss) GetRoutineCheckDataRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.GetStatisticBillList is called by the API as /erp/dss/GetStatisticBillList with post body {"name": "foo"}
func (this *Dss) GetStatisticBillList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.GetAccidentList is called by the API as /erp/dss/GetAccidentList with post body {"name": "foo"}
func (this *Dss) GetAccidentList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.GetViolationList is called by the API as /erp/dss/GetViolationList with post body {"name": "foo"}
func (this *Dss) GetViolationList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.GetOverSpeedList is called by the API as /erp/dss/GetOverSpeedList with post body {"name": "foo"}
func (this *Dss) GetOverSpeedList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.Selfcheck is called by the API as /erp/dss/Selfcheck with post body {"name": "foo"}
func (this *Dss) Selfcheck(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

///dss/device/getConfig/v1.1?access_token=xxx&device=2020090901
// Dss.GetConfig is called by the API as /erp/dss/GetConfig with post body {"name": "foo"}
func (this *Dss) GetConfig(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var deviceSN string
	var access_token string

	queryUrl, _ := url.Parse(req.Url)
	form := queryUrl.Query()
	if nil != form {
		if nil != form["device"] {
			deviceSN = form["device"][0]
		}

		if nil != form["access_token"] {
			access_token = form["access_token"][0]
		}
	}

	log.Printf("[%v]access_token: %v", deviceSN, access_token)

	data := map[string]interface{}{
		"promiseParam": map[string]interface{}{
			"content":  "岗前承诺",
			"duration": 0,
		},
		"promiseSwitch": 0,
		"healthCheck":   1,
		"moodCheck":     1,
		"safetyExam":    1,
		"healthParam": map[string]interface{}{
			"systolicPressure":  []int64{100, 150},
			"diastolicPressure": []int64{100, 150},
			"heartRate":         []int64{100, 150},
			"oxygen":            100,
		},
		"videoParam": map[string]interface{}{
			"upflag":   0,
			"timespan": "0",
			"url":      "",
			"saveday":  14,
		},
		"tempUrl": "",
	}

	return response.SendResp2Dss(rsp, data)
}

type PostLogReq struct {
	Device     string `json:"device"`
	LogUser    string `json:"logUser"`
	LogType    int64  `json:"logType"`
	LogText    string `json:"logText"`
	LogTimeUtc string `json:"logTime"`
}

// Dss.PostLog is called by the API as /erp/dss/PostLog with post body {"name": "foo"}
func (this *Dss) PostLog(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var item PostLogReq
	if err := json.Unmarshal([]byte(req.Body), &item); nil != err {
		log.Printf(" json.Unmarshal[err]: %v, req.Body: %v", err, string(req.Body))
		return response.SendResp2Dss(rsp, nil)
	}

	log.Printf("[%v]item: %v", item.Device, item)

	return response.SendResp2Dss(rsp, nil)
}

// Dss.GetQuestionContent is called by the API as /erp/dss/GetQuestionContent with post body {"name": "foo"}
func (this *Dss) GetQuestionContent(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.AddQuestionRecord is called by the API as /erp/dss/AddQuestionRecord with post body {"name": "foo"}
func (this *Dss) AddQuestionRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.GetDeviceQuestion is called by the API as /erp/dss/GetDeviceQuestion with post body {"name": "foo"}
func (this *Dss) GetDeviceQuestion(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.ReqData is called by the API as /erp/dss/ReqData with post body {"name": "foo"}
func (this *Dss) ReqData(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

// Dss.ResData is called by the API as /erp/dss/ResData with post body {"name": "foo"}
func (this *Dss) ResData(ctx context.Context, req *api.Request, rsp *api.Response) error {
	data := map[string]interface{}{
		"access_token": nil,
	}
	return response.SendResp2Dss(rsp, data)
}

type LoginReq struct {
	Device      string `json:"device"`
	AuthType    int64  `json:"authType"`
	AuthValue   string `json:"authValue"`
	AuthTimeUtc string `json:"authTime"`
	Temperature string `json:"temperature"`
}

// Dss.Login is called by the API as /erp/dss/Login with post body {"name": "foo"}
func (this *Dss) Login(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var item LoginReq
	if err := json.Unmarshal([]byte(req.Body), &item); nil != err {
		log.Printf(" json.Unmarshal[err]: %v, req.Body: %v", err, string(req.Body))
		return response.SendResp2Dss(rsp, nil)
	}

	log.Printf("[%v]reqBody: %v", item.Device, string(req.Body))

	data := map[string]interface{}{
		"driverCode":  "4a138a33",
		"driverName":  "司机姓名",
		"workCode":    "112233",
		"signInType":  1,
		"signOutType": 1,
		"bluetooth":   "",
		"healthCheck": "0",
	}
	return response.SendResp2Dss(rsp, data)
}

type FaceCheckReq struct {
	Device string `json:"device"`
}

// Dss.FaceCheck is called by the API as /erp/dss/FaceCheck with post body {"name": "foo"}
func (this *Dss) FaceCheck(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var item FaceCheckReq
	if err := json.Unmarshal([]byte(req.Body), &item); nil != err {
		log.Printf(" json.Unmarshal[err]: %v, req.Body: %v", err, string(req.Body))
		return response.SendResp2Dss(rsp, nil)
	}

	log.Printf("[%v]reqBody: %v", item.Device, string(req.Body))

	data := map[string]interface{}{
		"driverCode": "4a138a33",
		"emotion":    "0", //人脸登录
	}
	return response.SendResp2Dss(rsp, data)
}

type HeartbeatReq struct {
	Device  string `json:"device"`
	TimeUtc string `json:"time"`
}

// Dss.Heartbeat is called by the API as /erp/dss/Heartbeat with post body {"name": "foo"}
func (this *Dss) Heartbeat(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var item HeartbeatReq
	if err := json.Unmarshal([]byte(req.Body), &item); nil != err {
		log.Printf(" json.Unmarshal[err]: %v, req.Body: %v", err, string(req.Body))
		return response.SendResp2Dss(rsp, nil)
	}

	log.Printf("[%v]reqBody: %v", item.Device, string(req.Body))

	return response.SendResp2Dss(rsp, nil)
}

type GetFtpAddressReq struct {
	Device string `json:"device"`
}

// Dss.GetFtpAddress is called by the API as /erp/dss/GetFtpAddress with post body {"name": "foo"}
func (this *Dss) GetFtpAddress(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var item GetFtpAddressReq
	if err := json.Unmarshal([]byte(req.Body), &item); nil != err {
		log.Printf(" json.Unmarshal[err]: %v, req.Body: %v", err, string(req.Body))
		return response.SendResp2Dss(rsp, nil)
	}

	log.Printf("[%v]reqBody: %v", item.Device, string(req.Body))

	return response.SendResp2Dss(rsp, nil)
}

type UpLoadVideoReq struct {
	Device  string `json:"device"`
	TestId  string `json:"testId"`
	TimeUtc string `json:"time"`
	Video   string `json:"video"`
}

// Dss.UpLoadVideo is called by the API as /erp/dss/UpLoadVideo with post body {"name": "foo"}
func (this *Dss) UpLoadVideo(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var item UpLoadVideoReq
	if err := json.Unmarshal([]byte(req.Body), &item); nil != err {
		log.Printf(" json.Unmarshal[err]: %v, req.Body: %v", err, string(req.Body))
		return response.SendResp2Dss(rsp, nil)
	}

	log.Printf("[%v]reqBody: %v", item.Device, string(req.Body))

	return response.SendResp2Dss(rsp, nil)
}

type FaceRegisterReq struct {
	Device     string `json:"device"`
	AuthType   string `json:"authType"`
	DriverCode string `json:"driverCode"`
	FaceValue  string `json:"faceValue"`
	RegTimeUtc string `json:"regTime"`
}

// Dss.FaceRegister is called by the API as /erp/dss/FaceRegister with post body {"name": "foo"}
func (this *Dss) FaceRegister(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var item FaceRegisterReq
	if err := json.Unmarshal([]byte(req.Body), &item); nil != err {
		log.Printf(" json.Unmarshal[err]: %v, req.Body: %v", err, string(req.Body))
		return response.SendResp2Dss(rsp, nil)
	}

	log.Printf("[%v]reqBody: %v", item.Device, string(req.Body))

	return response.SendResp2Dss(rsp, nil)
}
