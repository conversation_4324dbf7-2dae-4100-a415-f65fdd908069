package ticket

import (
	"app/org/scs/erpv2/api/handler/ticket/countmoney"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	ticket2 "app/org/scs/erpv2/api/model/ticket"
	oet_scs_srv_public "app/org/scs/erpv2/api/proto/rpc/corporation"
	protoVehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"errors"
	"github.com/micro/go-micro/v2/api/proto"
	"math"
	"time"
)

type TicketBankCheck struct {
}

type DifferenceParam struct {
	ticket2.TicketBankCheckDifferenceRecord
	model.Paginator
	InComeAt  model.LocalTime
	BankMoney int64
}

func (t *TicketBankCheck) ListDifference(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DifferenceParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.Error("GetTopCorporationId == 0")
		return response.Error(rsp, response.Forbidden)
	}

	records, i, err := param.GetBy(topCorporationId, param.Paginator)
	if err != nil {
		log.ErrorFields("getby error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	data := map[string]interface{}{
		"Items":      records,
		"TotalCount": i,
	}

	return response.Success(rsp, data)
}

func (t *TicketBankCheck) EditDifference(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DifferenceParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.Error("GetTopCorporationId == 0")
		return response.Error(rsp, response.Forbidden)
	}

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	err = param.TransactionUpdateAllToDone(tx, topCorporationId)
	if err != nil {
		log.ErrorFields("TransactionUpdateAllToDone error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	param.TicketBankCheckDifferenceRecord.GroupId = topCorporationId
	param.TicketBankCheckDifferenceRecord.Status = ticket2.DOING_TBCDRS_1

	err = param.TransactionCreate(tx)
	if err != nil {
		log.ErrorFields("TransactionCreate error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)
}

// Check 银行校对
func (t *TicketBankCheck) Check(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DifferenceParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.BranchId == 0 || time.Time(param.InComeAt).IsZero() || param.BankMoney == 0 {
		log.ErrorFields("params missing", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsMissing)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId == 0 {
		log.ErrorFields("topCorporationId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.Forbidden)
	}

	//验证是否为分公司id
	detailById := rpc.GetCorporationDetailById(ctx, param.BranchId)
	if detailById == nil {
		log.ErrorFields("detailById == nil, param.branchId error", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if detailById.BranchId != param.BranchId {
		log.ErrorFields("detailById.BranchId != param.BranchId, param.branchId error", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 查询差额设置
	var diff ticket2.TicketBankCheckDifferenceRecord
	err = diff.GetDifference(topCorporationId)
	if err != nil {
		log.ErrorFields("GetDifference error", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.DbQueryFail)
	}

	// 查询公司收入
	branchCount, err := (&ticket2.TicketCountMoneys{}).GetByBranchCount(param.BranchId, time.Time(param.InComeAt))
	if err != nil {
		log.ErrorFields("GetByBranchCount error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	// 计算判断差额
	calcMoney := param.BankMoney - branchCount.TotalAmount
	if calcMoney == 0 {
		// 差额为0

	}

	var state int64 // 1-小于等于差额 2-大于差额
	if math.Abs(float64(calcMoney)) > float64(diff.Difference) {
		// 大于设置差额
		state = 2

	} else {
		state = 1
	}

	data := map[string]interface{}{
		"CountMoney": branchCount.TotalAmount,
		"CalcMoney":  calcMoney,
		"State":      state,
	}
	return util.SendResp(rsp, "", "0", data)

}

type TicketMoneyParam struct {
	ticket2.TicketCountMoneys
	BankMoney int64
	CalcMoney int64 // 差额 银行清点金额-点钞金额 单位分
}

// EditDifferenceCountMoney 修改点钞数据 添加校对记录
func (t *TicketBankCheck) EditDifferenceCountMoney(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TicketMoneyParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.BankMoney == 0 || param.TicketCountMoneys.Id == 0 {
		log.ErrorFields("param missing", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	// 判断是否有修改权限
	getById := (&param.TicketCountMoneys).GetById(param.TicketCountMoneys.Id)
	oldData := getById

	if countmoney.IsDateLock(getById.IncomeAt, getById.GroupId) {
		log.Error("数据不可修改 票款日期为==", getById.IncomeAt)
		return util.SendResp(rsp, "", "OP7702", nil)
	}

	now := time.Now()

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()
	// 更新点钞数据
	// ！！！此处的param.AddMoney  param.SubtractMoney 是前端传的全量
	getById.AddMoney = param.AddMoney
	getById.SubtractMoney = param.SubtractMoney
	getById.TotalAmount = getById.TotalAmount + (param.AddMoney - oldData.AddMoney) - (param.SubtractMoney - oldData.SubtractMoney)
	if getById.TotalAmount < 0 {
		log.ErrorFields("getById .TotalAmount < 0", map[string]interface{}{"err": nil})
		err = errors.New("param error")
		return response.Error(rsp, response.ParamsInvalid)
	}
	getById.EditStatus = ticket2.Changed_2
	err = getById.EditDiff(tx)
	if err != nil {
		log.ErrorFields("EditDiff error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}
	// 添加校对记录

	// 查询校对记录，有则添加，无则新增
	record, i, err := (&ticket2.TicketBankCheckRecord{}).GetBranchRecord(getById.BranchId, getById.IncomeAt, now)
	if err != nil {
		log.ErrorFields("GetBranchRecord error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	if i > 0 {
		// 存在数据
		if record.Id <= 0 {
			log.ErrorFields("record.Id <= 0 error", map[string]interface{}{"err": err})
			err = errors.New("record.Id <= 0")
			return response.Error(rsp, response.DbQueryFail)
		} else {
			// 添加变更记录
			var ccr ticket2.TicketBankCheckChangeRecord
			ccr.GroupId = record.GroupId
			ccr.CompanyId = record.CompanyId
			ccr.BranchId = record.BranchId
			ccr.TicketBankCheckRecordId = record.Id
			ccr.LineId = getById.LineId
			ccr.Line = getById.Line
			ccr.License = getById.License
			ccr.IncomeAt = record.IncomeAt
			ccr.BankMoney = param.BankMoney
			ccr.IncludedDifferenceMoney = getById.TotalAmount
			ccr.NotIncludedDifferenceMoney = getById.TotalAmount - getById.AddMoney + getById.SubtractMoney
			err = ccr.TransactionCreate(tx)
			if err != nil {
				log.ErrorFields("ccr.TransactionCreate error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}

			// 修改校对记录值
			err = record.TransactionUpdate(tx, record.Id, map[string]interface{}{"BankMoney": param.BankMoney, "BranchIncludedDifferenceMoney": record.BranchIncludedDifferenceMoney + (param.AddMoney - oldData.AddMoney) - (param.SubtractMoney - oldData.SubtractMoney)})
			if err != nil {
				log.ErrorFields("record.TransactionUpdate error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbUpdateFail)
			}
		}
	} else {
		// 不存在，新增

		// 查询公司收入 查询时此次事务未提交 差额数据为0
		var branchCount ticket2.GetByBranchCountRsp
		branchCount, err = (&ticket2.TicketCountMoneys{}).GetByBranchCount(getById.BranchId, getById.IncomeAt)
		if err != nil {
			log.ErrorFields("GetByBranchCount error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbQueryFail)
		}

		// 校对记录
		var cr ticket2.TicketBankCheckRecord
		cr.GroupId = getById.GroupId
		cr.CompanyId = getById.CompanyId
		cr.BranchId = getById.BranchId
		cr.IncomeAt = model.LocalTime(getById.IncomeAt)
		cr.BankMoney = param.BankMoney
		cr.BranchIncludedDifferenceMoney = branchCount.TotalAmount + param.AddMoney - param.SubtractMoney
		cr.BranchNotIncludedDifferenceMoney = branchCount.TotalAmount
		cr.CheckedAt = model.LocalTime(now)

		// 校对记录变更记录
		var ccr ticket2.TicketBankCheckChangeRecord
		ccr.GroupId = cr.GroupId
		ccr.CompanyId = cr.CompanyId
		ccr.BranchId = cr.BranchId
		//ccr.TicketBankCheckRecordId = record.Id
		ccr.LineId = getById.LineId
		ccr.Line = getById.Line
		ccr.License = getById.License
		ccr.IncomeAt = cr.IncomeAt
		ccr.BankMoney = cr.BankMoney
		ccr.IncludedDifferenceMoney = getById.TotalAmount
		ccr.NotIncludedDifferenceMoney = getById.TotalAmount - getById.AddMoney + getById.SubtractMoney

		cr.ChangeRecords = append(cr.ChangeRecords, ccr)

		err = cr.TransactionCreate(tx)
		if err != nil {
			log.ErrorFields("cr.TransactionCreate error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	// 记录日志
	countmoney.CreateLogger(ctx, oldData, getById)

	return response.Success(rsp, nil)
}

// AutoEditDifferenceCountMoney 系统自动计算修改点钞数据 添加校对记录
func (t *TicketBankCheck) AutoEditDifferenceCountMoney(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TicketMoneyParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.CalcMoney == 0 || param.BankMoney == 0 {
		log.ErrorFields("param missing", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	getById := (&param.TicketCountMoneys).GetById(param.TicketCountMoneys.Id)
	oldData := getById

	//if countmoney.IsDateLock(getById.IncomeAt, getById.GroupId) {
	//	log.Error("数据不可修改 票款日期为==", getById.IncomeAt)
	//	return util.SendResp(rsp, "", "OP7702", nil)
	//}

	now := time.Now()

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()
	// 更新点钞数据

	if param.CalcMoney > 0 {
		// 银行清点金额 > 分公司点钞金额
		// 应该增加收入
		param.AddMoney = param.CalcMoney
	} else {
		// 银行清点金额 < 分公司点钞金额
		// 应该减少收入
		param.SubtractMoney = int64(math.Abs(float64(param.CalcMoney)))
	}

	// ！！！此处的param.AddMoney  param.SubtractMoney 是增量
	getById.AddMoney += param.AddMoney
	getById.SubtractMoney += param.SubtractMoney
	getById.TotalAmount = getById.TotalAmount + param.AddMoney - param.SubtractMoney
	if getById.TotalAmount < 0 {
		log.ErrorFields("getById .TotalAmount < 0", map[string]interface{}{"err": nil})
		err = errors.New("param error")
		return response.Error(rsp, response.ParamsInvalid)
	}
	getById.EditStatus = ticket2.Changed_2
	err = getById.EditDiff(tx)
	if err != nil {
		log.ErrorFields("EditDiff error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}
	// 添加校对记录

	// 查询校对记录，有则添加，无则新增
	record, i, err := (&ticket2.TicketBankCheckRecord{}).GetBranchRecord(getById.BranchId, getById.IncomeAt, now)
	if err != nil {
		log.ErrorFields("GetBranchRecord error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	if i > 0 {
		// 存在数据
		if record.Id <= 0 {
			log.ErrorFields("record.Id <= 0 error", map[string]interface{}{"err": err})
			err = errors.New("record.Id <= 0")
			return response.Error(rsp, response.DbQueryFail)
		} else {
			// 添加变更记录
			var ccr ticket2.TicketBankCheckChangeRecord
			ccr.GroupId = record.GroupId
			ccr.CompanyId = record.CompanyId
			ccr.BranchId = record.BranchId
			ccr.TicketBankCheckRecordId = record.Id
			ccr.LineId = getById.LineId
			ccr.Line = getById.Line
			ccr.License = getById.License
			ccr.IncomeAt = record.IncomeAt
			ccr.BankMoney = param.BankMoney
			ccr.IncludedDifferenceMoney = getById.TotalAmount
			ccr.NotIncludedDifferenceMoney = getById.TotalAmount - getById.AddMoney + getById.SubtractMoney
			err = ccr.TransactionCreate(tx)
			if err != nil {
				log.ErrorFields("ccr.TransactionCreate error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}

			//
			// 修改校对记录值
			err = record.TransactionUpdate(tx, record.Id, map[string]interface{}{"BankMoney": param.BankMoney, "BranchIncludedDifferenceMoney": record.BranchIncludedDifferenceMoney + param.AddMoney - param.SubtractMoney})
			if err != nil {
				log.ErrorFields("record.TransactionUpdate error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbUpdateFail)
			}
		}
	} else {
		// 不存在，新增

		// 查询公司收入 查询时此次事务未提交 差额数据为0
		var branchCount ticket2.GetByBranchCountRsp
		branchCount, err = (&ticket2.TicketCountMoneys{}).GetByBranchCount(getById.BranchId, getById.IncomeAt)
		if err != nil {
			log.ErrorFields("GetByBranchCount error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbQueryFail)
		}

		// 校对记录
		var cr ticket2.TicketBankCheckRecord
		cr.GroupId = getById.GroupId
		cr.CompanyId = getById.CompanyId
		cr.BranchId = getById.BranchId
		cr.IncomeAt = model.LocalTime(getById.IncomeAt)
		cr.BankMoney = param.BankMoney
		cr.BranchIncludedDifferenceMoney = branchCount.TotalAmount + param.AddMoney - param.SubtractMoney
		cr.BranchNotIncludedDifferenceMoney = branchCount.TotalAmount
		cr.CheckedAt = model.LocalTime(now)

		// 校对记录变更记录
		var ccr ticket2.TicketBankCheckChangeRecord
		ccr.GroupId = cr.GroupId
		ccr.CompanyId = cr.CompanyId
		ccr.BranchId = cr.BranchId
		//ccr.TicketBankCheckRecordId = record.Id
		ccr.LineId = getById.LineId
		ccr.Line = getById.Line
		ccr.License = getById.License
		ccr.IncomeAt = cr.IncomeAt
		ccr.BankMoney = cr.BankMoney
		ccr.IncludedDifferenceMoney = getById.TotalAmount
		ccr.NotIncludedDifferenceMoney = getById.TotalAmount - getById.AddMoney + getById.SubtractMoney

		cr.ChangeRecords = append(cr.ChangeRecords, ccr)

		err = cr.TransactionCreate(tx)
		if err != nil {
			log.ErrorFields("cr.TransactionCreate error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	// 记录日志
	countmoney.CreateLogger(ctx, oldData, getById)

	return response.Success(rsp, nil)
}

type CheckRecordParam struct {
	ticket2.TicketBankCheckRecord
	StartCheckedAt model.LocalTime
	EndCheckedAt   model.LocalTime
	model.Paginator
}

// ListCheckRecord 银行校对记录列表
func (t *TicketBankCheck) ListCheckRecord(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param CheckRecordParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 检查是否是分公司

	var corpIds []int64

	if param.TicketBankCheckRecord.BranchId > 0 {
		corpIds = append(corpIds, param.TicketBankCheckRecord.BranchId)
	}

	provider := service.AuthCorporationIdProvider(ctx, corpIds)

	records, totalCount, err := param.TicketBankCheckRecord.GetBy(provider, time.Time(param.StartCheckedAt), time.Time(param.EndCheckedAt), param.Paginator)
	if err != nil {
		log.ErrorFields("GetBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	mapCorp := make(map[int64]*oet_scs_srv_public.CorporationItem) //map[机构id]

	for i, record := range records {
		if corp, ok := mapCorp[record.BranchId]; ok {
			records[i].Branch = corp.Name
		} else {
			corporationItem := rpc.GetCorporationById(ctx, record.BranchId)
			if corporationItem != nil {
				records[i].Branch = corporationItem.Name
				mapCorp[record.BranchId] = corporationItem
			}
		}
	}

	data := map[string]interface{}{
		"Items":      records,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)
}

// ListCheckChangeRecord 银行校对记录变更记录列表
func (t *TicketBankCheck) ListCheckChangeRecord(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param CheckRecordParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.ErrorFields("GetTopCorporationId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.Forbidden)
	}

	records, totalCount, err := (&ticket2.TicketBankCheckChangeRecord{}).GetByTicketBankCheckRecordId(param.TicketBankCheckRecord.Id, param.Paginator)
	if err != nil {
		log.ErrorFields("GetByTicketBankCheckRecordId error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	mapCorp := make(map[int64]*oet_scs_srv_public.CorporationItem)                        //map[机构id]
	mapDetailCorp := make(map[int64]*oet_scs_srv_public.GetCorporationDetailByIdResponse) // map[机构id]

	for i, record := range records {
		// 分公司
		if corp, ok := mapCorp[record.BranchId]; ok {
			records[i].Branch = corp.Name
		} else {
			corporationItem := rpc.GetCorporationById(ctx, record.BranchId)
			if corporationItem != nil {
				records[i].Branch = corporationItem.Name
				mapCorp[record.BranchId] = corporationItem
			}
		}

		// 车队
		// 根据车牌号查询车辆所属车队

		vehicleInfo := rpc.GetVehicleWithLicense(ctx, &protoVehicle.GetVehicleWithLicenseRequest{
			License:       record.License,
			CorporationId: topCorporationId,
		})

		if detailCorp, ok := mapDetailCorp[vehicleInfo.SonCorporationId]; ok {
			records[i].Fleet = detailCorp.FleetName
		} else {
			detailById := rpc.GetCorporationDetailById(ctx, vehicleInfo.SonCorporationId)
			if detailById != nil {
				records[i].Fleet = detailById.FleetName
				mapDetailCorp[vehicleInfo.SonCorporationId] = detailById
			}
		}
	}

	data := map[string]interface{}{
		"Items":      records,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)
}
