package countticket

import (
	"app/org/scs/erpv2/api/handler/ticket/countmoney"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/ticket"
	protooetvehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	go_api "github.com/micro/go-micro/v2/api/proto"
	"github.com/micro/go-micro/v2/client"
	log "github.com/micro/go-micro/v2/logger"
	"time"
)

// 票务-点票

type CountTicket struct {
	Client client.Client

	ticket.TicketCountMoneys
	Ids          []int64 // 主键id
	IncomeAt     int64
	ReportAt     int64
	Licenses     []string
	BranchIds    []int64
	DataStatuses []int64         // 查询状态 0正常 1日期异常 2数据异常 (3所有异常); []:全部数据，[0]:正常数据，[0, 1]:正常数据，日期异常
	TimeType     ticket.TimeType // 日期类型 1录入时间 2票款时间
	Keyword      string
	StartAt      int64
	EndAt        int64
	Scene        string `json:"Scene"`
	model.Paginator
	Order string // 根据 TotalAmount 字段的排序方式，desc(降序)/asc(升序)
}

func (c *CountTicket) Add(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q CountTicket
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	if q.Type != ticket.TICKET {
		log.Error("q.Type != ticketmoney.TICKET err=")
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	// 查询 传入日期(当天) 该分公司下该车辆是否已近录入数据

	r := time.Unix(q.IncomeAt, 0) // 录入票款时间

	if q.License != "" {

		startAt := time.Date(r.Year(), r.Month(), r.Day(), 0, 0, 0, 0, time.Local)
		endAt := time.Date(r.Year(), r.Month(), r.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 1)

		IsExists, err := (&q.TicketCountMoneys).IsExistsLicenseOnDay(q.BranchId, q.License, startAt, endAt, q.TimeSlot)
		if err != nil {
			log.Error("IsExistsLicenseOnDay err=", err.Error())
			return util.SendResp(rsp, "", "OP1004", nil)
		}

		if IsExists {
			log.Error("IsExists == true 已录入车辆数据")
			return util.SendResp(rsp, "", "OP7700", nil)
		}
	}

	corporationType := rpc.GetCorporationDetailById(ctx, q.BranchId)
	if corporationType != nil {
		q.GroupId = corporationType.GroupId
		q.CompanyId = corporationType.CompanyId
		//q.BranchId = corporationType.BranchId
		q.DepartmentId = corporationType.DepartmentId
		//q.FleetId = corporationType.FleetId // 获取车属车队
	}

	// 获取线路信息
	// rpc 获取线路信息
	lineItem, subCorpIds := rpc.GetLineWithId(ctx, q.LineId)
	if lineItem != nil {
		q.Line = lineItem.Name
	}

	// 获取录入员信息
	// 获取录入员信息
	// 录入员使用当前操作人
	// 获取当前用户的StaffId
	user := auth.User(ctx).GetUser()
	q.InputName = user.Name
	q.InputUserId = user.Id
	staff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	if staff != nil {
		q.InputStaffIdStr = staff.StaffId
	}

	// 获取司机信息
	// rpc 通过StaffId获取员工信息
	driver := rpc.GetStaffWithId(ctx, q.DriverStaffId)
	if driver != nil {
		q.DriverName = driver.Name
		q.DriverStaffIdStr = driver.StaffId
	}

	// 查找车辆编号
	// rpc 获取车辆信息
	vehicleItem := rpc.GetVehicleWithLicense(ctx, &protooetvehicle.GetVehicleWithLicenseRequest{
		License:       q.License,
		CorporationId: user.TopCorporationId,
	})
	if vehicleItem != nil {
		q.VehicleCode = vehicleItem.Code

	}

	// 获取复核员信息
	//rev := rpc.GetStaffWithId(ctx, q.ReviewerStaffId)
	//if rev != nil {
	//	q.ReviewerName = rev.Name
	//	q.ReviewerStaffIdStr = rev.StaffId
	//}

	// 获取车队信息
	// 优先使用线路归属的车队， 如果没有车队 则使用车辆归属的车队
	if lineItem == nil {
		if vehicleItem == nil {

		} else {
			detail := rpc.GetCorporationDetailById(ctx, vehicleItem.SonCorporationId)
			if detail != nil {

				q.FleetId = detail.FleetId // 获取录入车辆归属车队
			}
		}
	} else {
		for _, subCorporationId := range subCorpIds {
			detail := rpc.GetCorporationDetailById(ctx, subCorporationId)

			// todo 目前台州有4条线路同时在2个不同分公司的车队下 如果同一条线路在同一个分公司的不同车队下 这么写有问题
			if detail != nil && detail.BranchId == q.BranchId {

				q.FleetId = detail.FleetId // 获取录入车辆归属车队
			}
		}
	}

	// 转换时间
	q.TicketCountMoneys.ReportAt = time.Unix(q.ReportAt, 0)
	q.TicketCountMoneys.IncomeAt = r

	// 计算纸币总张数
	q.PaperTotalCount = q.Paper10000 + q.Paper5000 + q.Paper2000 + q.Paper1000 + q.Paper500 + q.Paper200 + q.Paper100 + q.Paper50 + q.Paper20 + q.Paper10

	// 计算纸币总金额 单位分
	q.PaperTotalAmount = q.Paper10000*10000 + q.Paper5000*5000 + q.Paper2000*2000 + q.Paper1000*1000 + q.Paper500*500 + q.Paper200*200 + q.Paper100*100 + q.Paper50*50 + q.Paper20*20 + q.Paper10*10

	// 计算硬币总个数
	q.CoinTotalCount = q.Coin100 + q.Coin50 + q.Coin10

	// 计算硬币总金额 单位分
	q.CoinTotalAmount = q.Coin100*100 + q.Coin50*50 + q.Coin10*10

	// 计算残次币总金额   残次币总金额=残次币计数*100 单位分
	defect := (q.MoneyFake + q.MoneyForeign + q.MoneyOther) * 100

	// 计算总金额 单位分 总金额=硬币总金额+纸币总金额-残次币总金额
	q.TotalAmount = q.PaperTotalAmount + q.CoinTotalAmount - defect
	// 计算应收总金额 单位分 总金额=硬币总金额+纸币总金额
	q.ReceivableAmount = q.PaperTotalAmount + q.CoinTotalAmount

	// 计算票张数
	q.TicketTotalCount = q.Ticket100 + q.Ticket150 + q.Ticket200 + q.Ticket250 + q.Ticket300 + q.Ticket350 + q.Ticket400 + q.Ticket450 + q.Ticket500 + q.Ticket550 + q.Ticket600 + q.Ticket650 + q.Ticket700 + q.Ticket750 + q.Ticket800 + q.Ticket850 + q.Ticket900 + q.Ticket950 + q.Ticket1000

	// 计算票总金额
	q.TicketTotalAmount = q.Ticket100*100 + q.Ticket150*150 + q.Ticket200*200 + q.Ticket250*250 + q.Ticket300*300 + q.Ticket350*350 + q.Ticket400*400 + q.Ticket450*450 + q.Ticket500*500 + q.Ticket550*550 + q.Ticket600*600 + q.Ticket650*650 + q.Ticket700*700 + q.Ticket750*750 + q.Ticket800*800 + q.Ticket850*850 + q.Ticket900*900 + q.Ticket950*950 + q.Ticket1000*1000

	q.Status = ticket.TO_BE_REVIEWED

	// 编辑状态
	q.TicketCountMoneys.EditStatus = ticket.NotChanged_1

	// 检查设置data状态
	(&q.TicketCountMoneys).SetTicketDataStatus()

	q.InputDate = time.Unix(q.IncomeAt, 0)

	err = (&q.TicketCountMoneys).Add()
	if err != nil {

		if model.IsViolatesUniqueConstraint(err) {
			log.Error("unique_violation", err.Error())
			return util.SendResp(rsp, "", "OP7700", nil)
		}

		log.Error("q.TicketCountMoneys.Add()", err.Error())
		return util.SendResp(rsp, "", "OP1004", nil)
	}

	// 记录日志
	countmoney.CreateLogger(ctx, ticket.TicketCountMoneys{}, q.TicketCountMoneys)

	return util.SendResp(rsp, "", "0", nil)
}

func (c *CountTicket) Edit(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q CountTicket
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	getById := (&q.TicketCountMoneys).GetById(q.Id)

	if countmoney.IsDateLock(getById.IncomeAt, getById.GroupId) {
		log.Error("数据不可修改 票款日期为==", getById.IncomeAt)
		return util.SendResp(rsp, "", "OP7702", nil)
	}

	if getById.License == "" && q.License != "" {
		r := time.Unix(q.IncomeAt, 0)
		startAt := time.Date(r.Year(), r.Month(), r.Day(), 0, 0, 0, 0, time.Local)
		endAt := time.Date(r.Year(), r.Month(), r.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 1)

		IsExists, err := (&q.TicketCountMoneys).IsExistsLicenseOnDay(q.BranchId, q.License, startAt, endAt, q.TimeSlot)
		if err != nil {
			log.Error("IsExistsLicenseOnDay err=", err.Error())
			return util.SendResp(rsp, "", "OP1004", nil)
		}

		if IsExists {
			log.Error("IsExists == true 已录入车辆数据")
			return util.SendResp(rsp, "", "OP7700", nil)
		}
	}

	// rpc 获取车辆信息
	vehicleItem := rpc.GetVehicleWithLicense(ctx, &protooetvehicle.GetVehicleWithLicenseRequest{
		License:       q.License,
		CorporationId: auth.User(ctx).GetTopCorporationId(),
	})
	if vehicleItem != nil {
		q.VehicleCode = vehicleItem.Code
	}

	// 获取司机信息
	// rpc 通过StaffId获取员工信息
	driver := rpc.GetStaffWithId(ctx, q.DriverStaffId)
	if driver != nil {
		q.DriverName = driver.Name
		q.DriverStaffIdStr = driver.StaffId
	}

	// 获取线路信息
	// rpc 获取线路信息
	lineItem, subCorpIds := rpc.GetLineWithId(ctx, q.LineId)
	if lineItem != nil {
		q.Line = lineItem.Name
	}

	// 获取车队信息
	// 优先使用线路归属的车队， 如果没有车队 则使用车辆归属的车队
	if lineItem == nil {
		if vehicleItem == nil {

		} else {
			detail := rpc.GetCorporationDetailById(ctx, vehicleItem.SonCorporationId)
			if detail != nil {

				q.TicketCountMoneys.FleetId = detail.FleetId // 获取录入车辆归属车队
			}
		}
	} else {
		for _, subCorporationId := range subCorpIds {
			detail := rpc.GetCorporationDetailById(ctx, subCorporationId)

			// todo 目前台州有4条线路同时在2个不同分公司的车队下 如果同一条线路在同一个分公司的不同车队下 这么写有问题
			if detail != nil && detail.BranchId == q.TicketCountMoneys.BranchId {

				q.TicketCountMoneys.FleetId = detail.FleetId // 获取录入车辆归属车队
			}
		}
	}

	// 转换时间
	q.TicketCountMoneys.IncomeAt = time.Unix(q.IncomeAt, 0)
	q.TicketCountMoneys.ReportAt = time.Unix(q.ReportAt, 0)
	q.TicketCountMoneys.InputDate = time.Unix(q.IncomeAt, 0)

	// 计算纸币总张数
	q.PaperTotalCount = q.Paper10000 + q.Paper5000 + q.Paper2000 + q.Paper1000 + q.Paper500 + q.Paper200 + q.Paper100 + q.Paper50 + q.Paper20 + q.Paper10

	// 计算纸币总金额 单位分
	q.PaperTotalAmount = q.Paper10000*10000 + q.Paper5000*5000 + q.Paper2000*2000 + q.Paper1000*1000 + q.Paper500*500 + q.Paper200*200 + q.Paper100*100 + q.Paper50*50 + q.Paper20*20 + q.Paper10*10

	// 计算硬币总个数
	q.CoinTotalCount = q.Coin100 + q.Coin50 + q.Coin10

	// 计算硬币总金额 单位分
	q.CoinTotalAmount = q.Coin100*100 + q.Coin50*50 + q.Coin10*10

	// 计算残次币总金额   残次币总金额=残次币计数*100 单位分
	defect := (q.MoneyFake + q.MoneyForeign + q.MoneyOther) * 100

	// 计算总金额 单位分 总金额=硬币总金额+纸币总金额-残次币总金额 + (银行差额)
	q.TotalAmount = q.PaperTotalAmount + q.CoinTotalAmount - defect + getById.AddMoney - getById.SubtractMoney
	// 计算应收总金额 单位分 总金额=硬币总金额+纸币总金额
	q.ReceivableAmount = q.PaperTotalAmount + q.CoinTotalAmount

	// 编辑状态
	q.TicketCountMoneys.EditStatus = ticket.Changed_2

	// 检查设置data状态
	(&q.TicketCountMoneys).SetTicketDataStatus()

	err = (&q.TicketCountMoneys).EditTicket()
	if err != nil {
		if model.IsViolatesUniqueConstraint(err) {
			log.Error("unique_violation", err.Error())
			return util.SendResp(rsp, "", "OP7700", nil)
		}
		log.Error("q.TicketCountMoneys.Edit()", err.Error())
		return util.SendResp(rsp, "", "OP1004", nil)
	}

	// 记录日志
	countmoney.CreateLogger(ctx, getById, q.TicketCountMoneys)

	return util.SendResp(rsp, "", "0", nil)
}

func (c *CountTicket) List(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q CountTicket
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}
	var ids []int64

	if len(q.BranchIds) == 0 {
		detailById := rpc.GetCorporationDetailById(ctx, auth.User(ctx).GetCorporationId())
		if detailById == nil {
			log.Error("GetCorporationDetailById detailById == nil")
			return util.SendResp(rsp, "", "OP7004", nil)
		}
		ids = append(ids, detailById.GroupId)
		ids = append(ids, detailById.CompanyId)
		ids = append(ids, detailById.BranchId)
		ids = append(ids, detailById.DepartmentId)
		ids = append(ids, detailById.FleetId)
	}

	list, totalCount, total := (&q.TicketCountMoneys).List(q.Licenses, ids, q.BranchIds, q.DataStatuses, q.Status, q.EditStatus, q.TimeType, q.TimeSlot, q.LineId, q.FleetId, q.DriverStaffId, q.ReviewerUserId, q.InputUserId, ticket.TICKET, q.Keyword, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0), q.Order, q.Scene, q.Paginator)

	for i, _ := range list {
		// 时间转换
		(&list[i].TimestampTz).ToUnix()

		// 获取分公司信息
		//(&list[i].CorpId).GetNames(ctx, c.Client, []model.GetNameEnum{model.BRANCH})
		corporationItem := rpc.GetCorporationById(ctx, list[i].BranchId)
		if corporationItem != nil {
			list[i].Branch = corporationItem.Name
		}

		// 获取车队
		corporationItem2 := rpc.GetCorporationById(ctx, list[i].FleetId)
		if corporationItem2 != nil {
			list[i].Fleet = corporationItem2.Name
		}
	}

	data := map[string]interface{}{
		"Items":      list,
		"TotalCount": totalCount,
		"Total":      total,
	}
	return util.SendResp(rsp, "", "0", data)
}

func (c *CountTicket) Delete(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q CountTicket
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	getById := (&q.TicketCountMoneys).GetById(q.Id)

	if countmoney.IsDateLock(getById.IncomeAt, getById.GroupId) {
		log.Error("数据不可修改 票款日期为==", getById.IncomeAt)
		return util.SendResp(rsp, "", "OP7702", nil)
	}

	if getById.Status > 1 {
		log.Error("getById.Status > 1")
		return util.SendResp(rsp, "", "OP7701", nil)
	}

	err = (&q.TicketCountMoneys).Delete(q.Id)
	if err != nil {
		log.Error("q.TicketCountMoneys.Delete()", err.Error())
		return util.SendResp(rsp, "", "OP1004", nil)
	}

	return util.SendResp(rsp, "", "0", nil)
}

type TicketLogger struct {
	ticket.TicketLogger
	Keyword string `json:"Keyword"`
	model.Paginator
}

func (c *CountTicket) Logger(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q TicketLogger
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}
	tickLoggers, totalCount := (&ticket.TicketLogger{}).List(q.TicketMoneyId, q.Keyword, q.Paginator)
	data := map[string]interface{}{
		"Items":      tickLoggers,
		"TotalCount": totalCount,
	}
	return util.SendResp(rsp, "", "0", data)
}

func (c *CountTicket) AllReview(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q CountTicket
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	searchStatus := q.Status           // 获取筛选 复核 字段
	searchInput := q.InputUserId       // 获取筛选 复核 字段
	searchReviewer := q.ReviewerUserId // 获取筛选 复核 字段

	// 复核员使用当前操作人 录入员不变
	user := auth.User(ctx).GetUser()
	q.ReviewerUserId = user.Id
	q.ReviewerName = user.Name
	staff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	if staff != nil {
		q.ReviewerStaffIdStr = staff.StaffId
	}

	q.Status = ticket.REVIEWED // 更改状态

	err = (&q.TicketCountMoneys).AllReview(q.Licenses, q.BranchIds, searchStatus, q.TimeType, q.LineId, q.FleetId, q.DataStatus, q.DriverStaffId, searchReviewer, searchInput, ticket.MONEY, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0))
	if err != nil {
		log.Error("q.TicketCountMoneys.AllReview()", err.Error())
		return util.SendResp(rsp, "", "OP1004", nil)
	}

	// 记录日志
	getById := (&q.TicketCountMoneys).GetById(q.Id)
	countmoney.CreateLogger(ctx, getById, q.TicketCountMoneys)

	return util.SendResp(rsp, "", "0", nil)
}

func (c *CountTicket) BatchReview(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q CountTicket
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	// 复核员使用当前操作人 录入员不变
	user := auth.User(ctx).GetUser()
	q.ReviewerUserId = user.Id
	q.ReviewerName = user.Name
	staff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	if staff != nil {
		q.ReviewerStaffIdStr = staff.StaffId
	}

	q.Status = ticket.REVIEWED

	err = (&q.TicketCountMoneys).BatchReview(q.Ids)
	if err != nil {
		log.Error("q.TicketCountMoneys.AllReview()", err.Error())
		return util.SendResp(rsp, "", "OP1004", nil)
	}

	// 记录日志
	getById := (&q.TicketCountMoneys).GetById(q.Id)
	countmoney.CreateLogger(ctx, getById, q.TicketCountMoneys)

	return util.SendResp(rsp, "", "0", nil)
}
