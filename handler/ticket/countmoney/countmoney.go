package countmoney

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/ticket"
	protooetvehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	context "context"
	"encoding/json"
	"fmt"
	"github.com/micro/go-micro/v2/api/proto"
	"github.com/micro/go-micro/v2/client"
	log "github.com/micro/go-micro/v2/logger"
	"time"
)

// 票务-点钞管理

type CountMoney struct {
	Client client.Client

	ticket.TicketCountMoneys
	Ids          []int64 // 主键id
	IncomeAt     int64
	ReportAt     int64    // 录入时间 录入时间并不完全等于创建时间
	Licenses     []string // 左侧树传车辆
	BranchIds    []int64
	DataStatuses []int64         // 查询状态 0正常 1日期异常 2数据异常 (3所有异常); []:全部数据，[0]:正常数据，[0, 1]:正常数据，日期异常
	TimeType     ticket.TimeType // 日期类型 1录入时间 2票款时间
	Keyword      string          // 搜索框传 车辆、线路
	StartAt      int64
	EndAt        int64
	Scene        string `json:"Scene"`
	model.Paginator

	Order string // 根据 TotalAmount 字段的排序方式，desc(降序)/asc(升序)
}

func FindDifferentField(oldData, newData ticket.TicketCountMoneys) (map[string]interface{}, map[string]interface{}) {
	oldField, err := util.StructToMapByReflect(oldData, "json")
	if err != nil {
		log.Error("StructToMapByReflect oldData err", err)
		return nil, nil
	}

	newField, err := util.StructToMapByReflect(newData, "json")
	if err != nil {
		log.Error("StructToMapByReflect newData err", err)
		return nil, nil
	}

	exceptField := []string{"Id", "CreatedAt", "UpdatedAt", "GroupId", "Group", "CompanyId", "Company",
		"DepartmentId", "Department", "FleetId", "Fleet"}

	for key := range oldField {
		if util.Include(exceptField, key) {
			delete(oldField, key)
			delete(newField, key)
		}

		_, isInt64 := oldField[key].(int64)
		_, isString := oldField[key].(string)
		_, isTime := oldField[key].(*model.LocalTime)
		if isInt64 || isString || isTime {
			if oldField[key] == newField[key] {
				delete(oldField, key)
				delete(newField, key)
			}
		} else {
			oldTmp, _ := json.Marshal(oldField[key])
			newTmp, _ := json.Marshal(newField[key])
			if string(oldTmp) == string(newTmp) {
				delete(oldField, key)
				delete(newField, key)
			}
		}
	}

	return oldField, newField
}

func CreateLogger(ctx context.Context, oldData, newData ticket.TicketCountMoneys) {
	var archiveLogger ticket.TicketLogger
	if oldData.Id == 0 {
		archiveLogger.Scene = ticket.SCENE_CREATE
		afterData, _ := json.Marshal(newData)
		archiveLogger.AfterData = model.JSON(afterData)
	} else {
		archiveLogger.Scene = ticket.SCENE_UPDATE
		oldDiff, newDiff := FindDifferentField(oldData, newData)
		before, _ := json.Marshal(oldDiff)
		after, _ := json.Marshal(newDiff)

		archiveLogger.BeforeData = model.JSON(before)
		archiveLogger.AfterData = model.JSON(after)
	}

	archiveLogger.TicketMoneyId = newData.Id
	archiveLogger.Ip = auth.User(ctx).GetClientIp()
	archiveLogger.ParseOpUser(ctx)

	_ = archiveLogger.Create()
}

// v0.3.5 编辑、删除时间限制：每月的28号24：00锁定票款日期在本月25日24：00前的数据
// true -> 锁定数据/无修改权限  false-> 数据未锁定/有修改权限
func IsDateLock(t time.Time, groupId int64) bool {

	if (&ticket.TicketDataPermission{}).GetHasPermission(groupId) == util.StatusForTrue {
		// 有修改权限，返回数据未锁定
		return false
	}

	now := time.Now()
	// 28日24时之前皆可修改
	lockTime := time.Date(now.Year(), now.Month(), 28, 0, 0, 0, 0, time.Local).AddDate(0, 0, 1)

	if lockTime.Sub(now) > 0 {
		//	上月26-本月25 可以修改
		last26 := time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)

		//return t.Sub(last26) >= 0 && now.Sub(t) > 0
		return last26.Sub(t) > 0 || t.Sub(now) > 0
	} else {
		// 只能修改 这个月26-下个月25的数据
		this26 := time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local)

		//return t.Sub(this26) >= 0 && now.Sub(t) > 0
		return !(this26.Sub(t) <= 0)
	}
}

func (c *CountMoney) Add(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q CountMoney
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	if q.Type != ticket.MONEY {
		log.Error("q.Type != ticketmoney.MONEY err=")
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	// 查询 传入日期(当天) 该分公司下该车辆是否已近录入数据
	// 规则：票款时间一天内，同一个时段只能有一条数据
	r := time.Unix(q.IncomeAt, 0)

	if q.License != "" {

		startAt := time.Date(r.Year(), r.Month(), r.Day(), 0, 0, 0, 0, time.Local)
		endAt := time.Date(r.Year(), r.Month(), r.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 1)

		IsExists, err := (&q.TicketCountMoneys).IsExistsLicenseOnDay(q.BranchId, q.License, startAt, endAt, q.TimeSlot)
		if err != nil {
			log.Error("IsExistsLicenseOnDay err=", err.Error())
			return util.SendResp(rsp, "", "OP1004", nil)
		}

		if IsExists {
			log.Error("IsExists == true 已录入车辆数据")
			return util.SendResp(rsp, "", "OP7700", nil)
		}
	}

	corporationType := rpc.GetCorporationDetailById(ctx, q.BranchId)
	if corporationType != nil {
		q.GroupId = corporationType.GroupId
		q.CompanyId = corporationType.CompanyId
		//q.BranchId = corporationType.BranchId
		q.DepartmentId = corporationType.DepartmentId
		//q.FleetId = corporationType.FleetId  // 获取录入车辆归属车队
	}

	// 获取线路信息
	// rpc 获取线路信息
	lineItem, subCorpIds := rpc.GetLineWithId(ctx, q.LineId)
	if lineItem != nil {
		q.Line = lineItem.Name
	}

	// 获取录入员信息
	// 录入员使用当前操作人
	// 获取当前用户的StaffId
	user := auth.User(ctx).GetUser()
	q.InputName = user.Name
	q.InputUserId = user.Id

	staff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	if staff != nil {
		q.InputStaffIdStr = staff.StaffId
	}

	// 获取司机信息
	// rpc 通过StaffId获取员工信息
	driver := rpc.GetStaffWithId(ctx, q.DriverStaffId)
	if driver != nil {
		q.DriverName = driver.Name
		q.DriverStaffIdStr = driver.StaffId
	}

	// 查找车辆编号
	// rpc 获取车辆信息
	vehicleItem := rpc.GetVehicleWithLicense(ctx, &protooetvehicle.GetVehicleWithLicenseRequest{
		License:       q.License,
		CorporationId: user.TopCorporationId,
	})
	if vehicleItem != nil {
		q.VehicleCode = vehicleItem.Code
	}

	if q.FleetId == 0 {
		// 获取车队信息
		// 优先使用线路归属的车队， 如果没有车队 则使用车辆归属的车队
		fmt.Printf("######################lineItem = %+v, vehicleItem = %+v , subids= %+v \n", lineItem, vehicleItem, subCorpIds)
		if lineItem == nil {
			if vehicleItem == nil {

			} else {
				detail := rpc.GetCorporationDetailById(ctx, vehicleItem.SonCorporationId)
				if detail != nil {
					fmt.Printf("#############################rpc.GetCorporationDetailById = %+v \n", detail)
					q.FleetId = detail.FleetId // 获取录入车辆归属车队
				}
			}
		} else {
			for _, subCorporationId := range subCorpIds {
				detail := rpc.GetCorporationDetailById(ctx, subCorporationId)
				fmt.Printf("#############################rpc.GetCorporationDetailById = %+v \n", detail)
				// todo 目前台州有4条线路同时在2个不同分公司的车队下 如果同一条线路在同一个分公司的不同车队下 这么写有问题
				if detail != nil && detail.BranchId == q.BranchId {

					fmt.Printf("#############@@@@@@@@##########rpc.GetCorporationDetailById = %+v \n", detail)
					q.FleetId = detail.FleetId // 获取录入车辆归属车队
				}
			}
		}
	}

	// 获取复核员信息
	//rev := rpc.GetStaffWithId(ctx, q.ReviewerStaffId)
	//if rev != nil {
	//	q.ReviewerName = rev.Name
	//	q.ReviewerStaffIdStr = rev.StaffId
	//}

	// 转换时间
	q.TicketCountMoneys.ReportAt = time.Unix(q.ReportAt, 0)
	q.TicketCountMoneys.IncomeAt = r

	// 计算纸币总张数
	q.PaperTotalCount = q.Paper10000 + q.Paper5000 + q.Paper2000 + q.Paper1000 + q.Paper500 + q.Paper200 + q.Paper100 + q.Paper50 + q.Paper20 + q.Paper10

	// 计算纸币总金额 单位分
	q.PaperTotalAmount = q.Paper10000*10000 + q.Paper5000*5000 + q.Paper2000*2000 + q.Paper1000*1000 + q.Paper500*500 + q.Paper200*200 + q.Paper100*100 + q.Paper50*50 + q.Paper20*20 + q.Paper10*10

	// 计算硬币总个数
	q.CoinTotalCount = q.Coin100 + q.Coin50 + q.Coin10

	// 计算硬币总金额 单位分
	q.CoinTotalAmount = q.Coin100*100 + q.Coin50*50 + q.Coin10*10

	// 计算残次币总金额   残次币总金额=残次币计数*100 单位分
	defect := (q.MoneyFake + q.MoneyForeign + q.MoneyOther) * 100

	// 计算总金额 单位分 总金额=硬币总金额+纸币总金额-残次币总金额
	q.TotalAmount = q.PaperTotalAmount + q.CoinTotalAmount - defect

	// 计算应收总金额 单位分 总金额=硬币总金额+纸币总金额
	q.ReceivableAmount = q.PaperTotalAmount + q.CoinTotalAmount

	q.Status = ticket.TO_BE_REVIEWED

	// 编辑状态
	q.TicketCountMoneys.EditStatus = ticket.NotChanged_1

	// 检查设置data状态
	(&q.TicketCountMoneys).SetTicketDataStatus()

	q.InputDate = time.Unix(q.IncomeAt, 0)

	err = (&q.TicketCountMoneys).Add()
	if err != nil {

		if model.IsViolatesUniqueConstraint(err) {
			log.Error("unique_violation", err.Error())
			return util.SendResp(rsp, "", "OP7700", nil)
		}

		log.Error("q.TicketCountMoneys.Add()", err.Error())
		return util.SendResp(rsp, "", "OP1004", nil)
	}

	// 记录日志
	CreateLogger(ctx, ticket.TicketCountMoneys{}, q.TicketCountMoneys)

	return util.SendResp(rsp, "", "0", nil)
}

func (c *CountMoney) Edit(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q CountMoney
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	getById := (&q.TicketCountMoneys).GetById(q.Id)

	if IsDateLock(getById.IncomeAt, getById.GroupId) {
		log.Error("数据不可修改 票款日期为==", getById.IncomeAt)
		return util.SendResp(rsp, "", "OP7702", nil)
	}

	if getById.License == "" && q.License != "" {
		r := time.Unix(q.IncomeAt, 0)
		startAt := time.Date(r.Year(), r.Month(), r.Day(), 0, 0, 0, 0, time.Local)
		endAt := time.Date(r.Year(), r.Month(), r.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 1)

		IsExists, err := (&q.TicketCountMoneys).IsExistsLicenseOnDay(q.BranchId, q.License, startAt, endAt, q.TimeSlot)
		if err != nil {
			log.Error("IsExistsLicenseOnDay err=", err.Error())
			return util.SendResp(rsp, "", "OP1004", nil)
		}

		if IsExists {
			log.Error("IsExists == true 已录入车辆数据")
			return util.SendResp(rsp, "", "OP7700", nil)
		}
	}

	// rpc 获取车辆信息
	vehicleItem := rpc.GetVehicleWithLicense(ctx, &protooetvehicle.GetVehicleWithLicenseRequest{
		License:       q.License,
		CorporationId: auth.User(ctx).GetTopCorporationId(),
	})
	if vehicleItem != nil {
		q.VehicleCode = vehicleItem.Code

	}

	// 获取司机信息
	// rpc 通过StaffId获取员工信息
	driver := rpc.GetStaffWithId(ctx, q.DriverStaffId)
	if driver != nil {
		q.DriverName = driver.Name
		q.DriverStaffIdStr = driver.StaffId
	}

	// 获取线路信息
	// rpc 获取线路信息
	lineItem, subCorpIds := rpc.GetLineWithId(ctx, q.LineId)
	if lineItem != nil {
		q.Line = lineItem.Name
	}

	// 获取车队信息
	// 优先使用线路归属的车队， 如果没有车队 则使用车辆归属的车队
	if q.FleetId == 0 {
		if lineItem == nil {
			if vehicleItem == nil {

			} else {
				detail := rpc.GetCorporationDetailById(ctx, vehicleItem.SonCorporationId)
				if detail != nil {

					q.TicketCountMoneys.FleetId = detail.FleetId // 获取录入车辆归属车队
				}
			}
		} else {
			for _, subCorporationId := range subCorpIds {
				detail := rpc.GetCorporationDetailById(ctx, subCorporationId)

				// todo 目前台州有4条线路同时在2个不同分公司的车队下 如果同一条线路在同一个分公司的不同车队下 用 branchId 判断这么写有问题
				if detail != nil && detail.BranchId == q.TicketCountMoneys.BranchId {
					q.TicketCountMoneys.FleetId = detail.FleetId // 获取录入车辆归属车队
				}
			}
		}
	}

	// 转换时间
	q.TicketCountMoneys.IncomeAt = time.Unix(q.IncomeAt, 0)
	q.TicketCountMoneys.ReportAt = time.Unix(q.ReportAt, 0)
	q.TicketCountMoneys.InputDate = time.Unix(q.IncomeAt, 0)

	// 计算纸币总张数
	q.PaperTotalCount = q.Paper10000 + q.Paper5000 + q.Paper2000 + q.Paper1000 + q.Paper500 + q.Paper200 + q.Paper100 + q.Paper50 + q.Paper20 + q.Paper10

	// 计算纸币总金额 单位分
	q.PaperTotalAmount = q.Paper10000*10000 + q.Paper5000*5000 + q.Paper2000*2000 + q.Paper1000*1000 + q.Paper500*500 + q.Paper200*200 + q.Paper100*100 + q.Paper50*50 + q.Paper20*20 + q.Paper10*10

	// 计算硬币总个数
	q.CoinTotalCount = q.Coin100 + q.Coin50 + q.Coin10

	// 计算硬币总金额 单位分
	q.CoinTotalAmount = q.Coin100*100 + q.Coin50*50 + q.Coin10*10

	// 计算残次币总金额   残次币总金额=残次币计数*100 单位分
	defect := (q.MoneyFake + q.MoneyForeign + q.MoneyOther) * 100

	// 计算总金额 单位分 总金额=硬币总金额+纸币总金额-残次币总金额 + (银行差额)
	q.TotalAmount = q.PaperTotalAmount + q.CoinTotalAmount - defect + getById.AddMoney - getById.SubtractMoney

	// 计算应收总金额 单位分 总金额=硬币总金额+纸币总金额
	q.ReceivableAmount = q.PaperTotalAmount + q.CoinTotalAmount

	// 编辑状态
	q.TicketCountMoneys.EditStatus = ticket.Changed_2

	// 检查设置data状态
	(&q.TicketCountMoneys).SetTicketDataStatus()

	err = (&q.TicketCountMoneys).Edit()
	if err != nil {
		if model.IsViolatesUniqueConstraint(err) {
			log.Error("unique_violation", err.Error())
			return util.SendResp(rsp, "", "OP7700", nil)
		}
		log.Error("q.TicketCountMoneys.Edit()", err.Error())
		return util.SendResp(rsp, "", "OP1004", nil)
	}

	// 记录日志
	CreateLogger(ctx, getById, q.TicketCountMoneys)

	return util.SendResp(rsp, "", "0", nil)
}

func (c *CountMoney) List(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q CountMoney
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	var ids []int64

	if len(q.BranchIds) == 0 {
		detailById := rpc.GetCorporationDetailById(ctx, auth.User(ctx).GetCorporationId())
		if detailById == nil {
			log.Error("GetCorporationDetailById detailById == nil")
			return util.SendResp(rsp, "", "OP7004", nil)
		}
		ids = append(ids, detailById.GroupId)
		ids = append(ids, detailById.CompanyId)
		ids = append(ids, detailById.BranchId)
		ids = append(ids, detailById.DepartmentId)
		ids = append(ids, detailById.FleetId)
	}

	list, totalCount, total := (&q.TicketCountMoneys).List(q.Licenses, ids, q.BranchIds, q.DataStatuses, q.Status, q.EditStatus, q.TimeType, q.TimeSlot, q.LineId, q.FleetId, q.DriverStaffId, q.ReviewerUserId, q.InputUserId, ticket.MONEY, q.Keyword, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0), q.Order, q.Scene, q.Paginator)

	for i, _ := range list {
		// 时间转换
		(&list[i].TimestampTz).ToUnix()

		// 获取分公司信息
		//(&list[i].CorpId).GetNames(ctx, c.Client, []model.GetNameEnum{model.BRANCH})
		corporationItem := rpc.GetCorporationById(ctx, list[i].BranchId)
		if corporationItem != nil {
			list[i].Branch = corporationItem.Name
		}

		// 获取车队
		corporationItem2 := rpc.GetCorporationById(ctx, list[i].FleetId)
		if corporationItem2 != nil {
			list[i].Fleet = corporationItem2.Name
		}
	}

	data := map[string]interface{}{
		"Items":      list,
		"TotalCount": totalCount,
		"Total":      total,
	}
	return util.SendResp(rsp, "", "0", data)
}

func (c *CountMoney) Delete(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q CountMoney
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	getById := (&q.TicketCountMoneys).GetById(q.Id)

	if IsDateLock(getById.IncomeAt, getById.GroupId) {
		log.Error("数据不可修改 票款日期为==", getById.IncomeAt)
		return util.SendResp(rsp, "", "OP7702", nil)
	}

	if getById.Status > 1 {
		log.Error("getById.Status > 1")
		return util.SendResp(rsp, "", "OP7701", nil)
	}

	err = (&q.TicketCountMoneys).Delete(q.Id)
	if err != nil {
		log.Error("q.TicketCountMoneys.Delete()", err.Error())
		return util.SendResp(rsp, "", "OP1004", nil)
	}

	return util.SendResp(rsp, "", "0", nil)
}

type TicketLogger struct {
	ticket.TicketLogger
	Keyword string `json:"Keyword"`
	model.Paginator
}

func (c *CountMoney) Logger(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q TicketLogger
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}
	tickLoggers, totalCount := (&ticket.TicketLogger{}).List(q.TicketMoneyId, q.Keyword, q.Paginator)
	data := map[string]interface{}{
		"Items":      tickLoggers,
		"TotalCount": totalCount,
	}
	return util.SendResp(rsp, "", "0", data)
}

// 全部复核
func (c *CountMoney) AllReview(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q CountMoney
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	searchStatus := q.Status           // 获取筛选 复核 字段
	searchInput := q.InputUserId       // 获取筛选 复核 字段
	searchReviewer := q.ReviewerUserId // 获取筛选 复核 字段
	user := auth.User(ctx).GetUser()
	q.ReviewerUserId = user.Id
	q.ReviewerName = user.Name
	staff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	if staff != nil {
		q.ReviewerStaffIdStr = staff.StaffId
	}
	q.Status = ticket.REVIEWED // 更改状态

	err = (&q.TicketCountMoneys).AllReview(q.Licenses, q.BranchIds, searchStatus, q.TimeType, q.LineId, q.FleetId, q.DataStatus, q.DriverStaffId, searchReviewer, searchInput, ticket.MONEY, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0))
	if err != nil {
		log.Error("q.TicketCountMoneys.AllReview()", err.Error())
		return util.SendResp(rsp, "", "OP1004", nil)
	}

	// 记录日志
	getById := (&q.TicketCountMoneys).GetById(q.Id)
	CreateLogger(ctx, getById, q.TicketCountMoneys)

	return util.SendResp(rsp, "", "0", nil)
}

func (c *CountMoney) BatchReview(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q CountMoney
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	// 获取录入员信息
	// 复核员使用当前操作人 录入员不变
	// 获取当前用户的StaffId
	user := auth.User(ctx).GetUser()
	q.ReviewerUserId = user.Id
	q.ReviewerName = user.Name
	staff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	if staff != nil {
		q.ReviewerStaffIdStr = staff.StaffId
	}

	q.Status = ticket.REVIEWED

	err = (&q.TicketCountMoneys).BatchReview(q.Ids)
	if err != nil {
		log.Error("q.TicketCountMoneys.AllReview()", err.Error())
		return util.SendResp(rsp, "", "OP1004", nil)
	}

	// 记录日志
	getById := (&q.TicketCountMoneys).GetById(q.Id)
	CreateLogger(ctx, getById, q.TicketCountMoneys)

	return util.SendResp(rsp, "", "0", nil)
}

// UpdateFleet 更新历史数据的车队ID
func UpdateFleet() {
	i := 1
	limit := 1000
	for {
		var ticket ticket.TicketCountMoneys
		records := ticket.GetDontHasFleetRecord(limit)

		if len(records) == 0 || (i*limit) >= 28000 {
			fmt.Printf("update_all_success: %v \r\n", i)
			break
		}

		for index := range records {
			// 获取线路信息
			// rpc 获取线路信息
			lineItem, subCorpIds := rpc.GetLineWithId(context.Background(), records[index].LineId)

			// 查找车辆编号
			// rpc 获取车辆信息
			vehicleItem := rpc.GetVehicleWithLicense(context.Background(), &protooetvehicle.GetVehicleWithLicenseRequest{
				License:       records[index].License,
				CorporationId: records[index].GroupId,
			})

			var fleetId int64

			// 获取车队信息
			// 优先使用线路归属的车队， 如果没有车队 则使用车辆归属的车队
			fmt.Printf("######################lineItem = %+v, vehicleItem = %+v , subids= %+v \n", lineItem, vehicleItem, subCorpIds)
			if lineItem == nil {
				if vehicleItem != nil {
					detail := rpc.GetCorporationDetailById(context.Background(), vehicleItem.SonCorporationId)
					if detail != nil {
						fmt.Printf("#############################rpc.GetCorporationDetailById = %+v \n", detail)
						fleetId = detail.FleetId // 获取录入车辆归属车队
					}
				}
			} else {
				for _, subCorporationId := range subCorpIds {
					detail := rpc.GetCorporationDetailById(context.Background(), subCorporationId)

					fmt.Printf("#############################rpc.GetCorporationDetailById = %+v \n", detail)
					// todo 目前台州有4条线路同时在2个不同分公司的车队下 如果同一条线路在同一个分公司的不同车队下 这么写有问题
					if detail != nil && detail.BranchId == records[index].BranchId {
						fmt.Printf("#############@@@@@@@@##########rpc.GetCorporationDetailById = %+v \n", detail)
						fleetId = detail.FleetId // 获取录入车辆归属车队
					}
				}
			}

			if fleetId > 0 {
				err := ticket.UpdateFleetId(records[index].Id, fleetId)
				if err != nil {
					fmt.Printf("ticket.UpdateFleetId error: %+v \r\n", err)
				}
			}
		}

		i++
	}
}
