package report

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	ticketModel "app/org/scs/erpv2/api/model/ticket"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	go_api "github.com/micro/go-micro/v2/api/proto"
)

type CharterBusIncomeRequest struct {
	ticketModel.CharteredBusIncome
	model.Paginator
}

func (r *Report) CharterBusIncomeCreate(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param CharterBusIncomeRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.ReportAt.ToTime().IsZero() || param.CorporationId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	param.Corporations.Build(param.CorporationId)
	param.OpUser.ParseOpUser(ctx)

	if param.Price > 0 {
		param.TotalPeople = param.TotalMoney / param.Price
	}

	err = param.CharteredBusIncome.Create()
	if err != nil {
		log.ErrorFields("Create error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (r *Report) CharterBusIncomeList(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param CharterBusIncomeRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	incomes, count := (&ticketModel.CharteredBusIncome{}).GetBy(param.CorporationId, param.ReportAt.ToTime(), param.Paginator)

	for i := range incomes {
		incomes[i].CorporationId, incomes[i].CorporationName = incomes[i].Corporations.GetCorporation()
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      incomes,
		"TotalCount": count,
	})
}

func (r *Report) CharterBusIncomeExport(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return r.CharterBusIncomeList(ctx, req, rsp)
}

func (r *Report) CharterBusIncomeUpdate(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param CharterBusIncomeRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.ReportAt.ToTime().IsZero() || param.CorporationId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	income := (&ticketModel.CharteredBusIncome{}).FirstBy(param.Id)
	if income.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.Corporations.Build(param.CorporationId)
	param.OpUser.ParseOpUser(ctx)
	if param.Price > 0 {
		param.TotalPeople = param.TotalMoney / param.Price
	}
	err = param.CharteredBusIncome.Update()
	if err != nil {
		log.ErrorFields("Update error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (r *Report) CharterBusIncomeDelete(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param CharterBusIncomeRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	income := (&ticketModel.CharteredBusIncome{}).FirstBy(param.Id)
	if income.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = income.Delete()
	if err != nil {
		log.ErrorFields("Delete error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}
