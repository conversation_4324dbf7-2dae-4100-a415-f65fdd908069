package report

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	exportModel "app/org/scs/erpv2/api/model/export"
	ticketModel "app/org/scs/erpv2/api/model/ticket"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	exportService "app/org/scs/erpv2/api/service/export"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	go_api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type CorporationLineIncomeRequest struct {
	ReportAt      model.LocalTime `json:"ReportAt"`
	CorporationId int64           `json:"CorporationId"`
}

func (r *Report) CalcCorporationLineIncome(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	if service.IsProcessingCalcCorporationLineIcReport {
		return response.Error(rsp, "OP9908")
	}
	var param CharterBusIncomeRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	year := param.ReportAt.ToTime().Year()
	month := param.ReportAt.ToTime().Month()
	reportMonth := time.Date(year, month, 1, 0, 0, 0, 0, time.Local)
	StartAt := time.Date(year, month-1, 26, 0, 0, 0, 0, time.Local)
	EndAt := time.Date(year, month, 25, 0, 0, 0, 0, time.Local)
	var calcReport = service.CalcCorporationLineIcReport{
		TopCorporationId: auth.User(ctx).GetTopCorporationId(),
		CorporationId:    param.CorporationId,
		ReportMonth:      reportMonth,
		StartAt:          StartAt,
		EndAt:            EndAt,
	}

	calcReport.ParseOpUser(ctx)

	go calcReport.Process()

	return response.Success(rsp, nil)
}

func (r *Report) CorporationLineIncomeReport(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param CharterBusIncomeRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	year := param.ReportAt.ToTime().Year()
	month := param.ReportAt.ToTime().Month()
	reportMonth := time.Date(year, month, 1, 0, 0, 0, 0, time.Local)
	reports := (&ticketModel.CorporationLineIncomeReport{}).GetBy(param.CorporationId, reportMonth)

	return response.Success(rsp, reports)
}

func (r *Report) CorporationLineIncomeReportExport(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param CharterBusIncomeRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	year := param.ReportAt.ToTime().Year()
	month := param.ReportAt.ToTime().Month()
	reportMonth := time.Date(year, month, 1, 0, 0, 0, 0, time.Local)

	var startAt = time.Date(year, month-1, 26, 0, 0, 0, 0, time.Local)
	var endAt = time.Date(year, month, 25, 0, 0, 0, 0, time.Local)
	var fileName = fmt.Sprintf("公司线路人次营收表%s-%s.xlsx", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))
	paramByte, _ := json.Marshal(param)
	exportFileRecord, err := exportService.CreateExportFileRecord(auth.User(ctx).GetUserId(), fileName, (&ticketModel.CorporationLineIncomeReport{}).TableName(), paramByte, startAt, endAt)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	go ExportCorporationLineIncomeReport(exportFileRecord, param.CorporationId, reportMonth)

	return response.Success(rsp, nil)
}

func TestExport() {
	reportMonth := time.Date(2024, 4, 1, 0, 0, 0, 0, time.Local)
	var startAt = time.Date(2024, 3, 26, 0, 0, 0, 0, time.Local)
	var endAt = time.Date(2024, 4, 25, 0, 0, 0, 0, time.Local)
	var fileName = fmt.Sprintf("公司线路人次营收表%s-%s.xlsx", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))
	paramByte, _ := json.Marshal(CorporationLineIncomeRequest{})
	exportFileRecord, err := exportService.CreateExportFileRecord(434, fileName, (&ticketModel.CorporationLineIncomeReport{}).TableName(), paramByte, startAt, endAt)
	if err != nil {
		return
	}

	go ExportCorporationLineIncomeReport(exportFileRecord, 0, reportMonth)
}

func ExportCorporationLineIncomeReport(exportFile exportModel.ExportFile, corporationId int64, reportMonth time.Time) {
	reports := (&ticketModel.CorporationLineIncomeReport{}).GetBy(corporationId, reportMonth)

	var reportMap = make(map[int64][]ticketModel.CorporationLineIncomeReport)

	for _, report := range reports {
		reportMap[report.CorporationId] = append(reportMap[report.CorporationId], report)
	}

	err := exportService.ExportCorporationLineIncomeReport(reportMap, exportFile)

	if err != nil {
		exportFile.ErrorCode = err.Error()
		err = exportFile.UpdateFail()
	} else {
		err = exportFile.UpdateSuccess()
	}
	log.ErrorFields("ExportCorporationLineIncomeReport exportFile.UpdateStatus error", map[string]interface{}{"err": err})
}
