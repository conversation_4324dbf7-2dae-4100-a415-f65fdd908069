package report

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/ticket"
	"app/org/scs/erpv2/api/model/ticket/mixReport"
	protocorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	protooetline "app/org/scs/erpv2/api/proto/rpc/oetline"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	go_api "github.com/micro/go-micro/v2/api/proto"
	"github.com/micro/go-micro/v2/client"
	log "github.com/micro/go-micro/v2/logger"
	"sort"
	"time"
)

type Report struct {
	Client client.Client

	ticket.TicketCountMoneys
	BranchIds     []int64  // 传分公司
	Licenses      []string // 传车辆
	License       string   // 传车辆
	LineIds       []int64  // 传线路
	LineId        int64    // 传线路
	FleetIds      []int64  // 传车队
	StaffIds      []int64  // 传乘务员
	StaffId       int64    // 传乘务员
	Keyword       string   // 搜索框传 车辆、线路
	StartAt       int64
	EndAt         int64
	TimeType      ticket.TimeType      // 日期类型 1录入时间 2票款时间
	TimeDimension ticket.TimeDimension // 日期维度 1按月统计 2按天统计
	IsExport      bool                 // 当前请求是否为导出报表
	ExportType    int64                // 1点钞数据 2票款数据
	model.Paginator
}

func (r *Report) ListDefective(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Report
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}
	list, totalCount, total := (&q.TicketCountMoneys).ListDefective(q.TimeType, q.BranchIds, q.FleetIds, q.LineIds, q.Licenses, q.Keyword, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0), q.Paginator)

	//
	rpcTmpCorporation := make(map[int64]*protocorporation.CorporationItem) // map[corporationId]机构信息

	for i, item := range list {
		// 获取分公司
		if oetCorporationItem, ok := rpcTmpCorporation[item.BranchId]; ok {
			if oetCorporationItem != nil {
				list[i].Branch = oetCorporationItem.Name
			}
		} else {
			corporationItem := rpc.GetCorporationById(ctx, item.BranchId)
			rpcTmpCorporation[item.BranchId] = corporationItem
			if corporationItem != nil {
				list[i].Branch = corporationItem.Name
			}
		}

		// 获取车队
		if oetCorporationItem, ok := rpcTmpCorporation[item.FleetId]; ok {
			if oetCorporationItem != nil {
				list[i].Fleet = oetCorporationItem.Name
			}
		} else {
			corporationItem := rpc.GetCorporationById(ctx, item.FleetId)
			rpcTmpCorporation[item.FleetId] = corporationItem
			if corporationItem != nil {
				list[i].Fleet = corporationItem.Name
			}
		}
	}

	data := map[string]interface{}{
		"Items":      list,
		"TotalCount": totalCount,
		"Total":      total,
	}
	return util.SendResp(rsp, "", "0", data)
}

// 公司营收报表  实际是分公司

type IncomeBranchRsp struct {
	SubTotalMonth          []SubTotal // 每月小计
	TotalMoneyTicketAmount int64      // 票款总计
	TotalMoneyCardAmount   int64      // 刷卡总计
	TotalMoneyCodeAmount   int64      // 扫码总计
	TotalAmount            int64      // 金额总计
	TotalCountVehicle      int64      // 车辆数总计
	AVGCountVehicle        int64      // 总计平均单车
}

type SubTotal struct {
	SubTotalCountVehicle      int64 // 小计车辆数
	SubTotalMoneyTicketAmount int64 // 小计票款收入
	SubTotalMoneyCardAmount   int64 // 小计刷卡收入
	SubTotalMoneyCodeAmount   int64 // 小计扫码收入
	SubTotalAmount            int64 // 小计收入
	SubTotalAVGVIncomeVehicle int64 // 小计平均单车收入 = SubTotalAmount/SubTotalCountVehicles
	SubTotalItem              []mixReport.SubTotalItem
}

func (r *Report) IncomeCompany(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Report
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	if len(q.BranchIds) == 0 {
		log.Error("len(q.BranchIds) == 0")
		return util.SendResp(rsp, "", "0", nil)
	}

	// 设置时间
	// 月份为上月26日-本月25日。例如选择9-10月份，则为8月26日-10月25日.
	// startAt <= ReportAt < endAt

	var (
		startAt, endAt time.Time
	)

	if q.TimeDimension == ticket.TIME_MONTHLY_1 {
		s := time.Unix(q.StartAt, 0)
		e := time.Unix(q.EndAt, 0)

		startAt = time.Date(s.Year(), s.Month(), 26, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)

		endAt = time.Date(e.Year(), e.Month(), 26, 0, 0, 0, 0, time.Local)
	} else if q.TimeDimension == ticket.TIME_DAILY_2 {
		startAt = time.Unix(q.StartAt, 0)

		endAt = time.Unix(q.EndAt, 0)

	}

	//rpcTmpCorporation := make(map[int64]*protocorporation.CorporationItem) // map[corporationId]机构信息

	subTotalItems, err := (&mixReport.TicketMoneyMixReportRecord{}).IncomeBranch(q.TimeType, q.BranchIds, startAt, endAt)
	if err != nil {
		log.Error("IncomeBranch err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	// 处理数据
	tmpData := make(map[string]map[int64]mixReport.SubTotalItem) // map[date]map[branchId]

	if q.TimeDimension == ticket.TIME_MONTHLY_1 {
		for _, item := range subTotalItems {

			date, err := time.Parse("2006-01-02", item.Month)
			if err != nil {
				log.Error("time.Parse err, item.Month=", item.Month)
				continue
			}

			// 假设 date 为2022年7月的某一天  那么 7月份 取值集合为 [6-26, 7-25]
			if date.Day() >= 26 {
				// 属于下一个月的数据
				date = time.Date(date.Year(), date.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, 1, 0)
			}

			reMonth := date.Format("2006-01") // 重置月份

			if _, ok := tmpData[reMonth]; !ok {
				tmpData[reMonth] = make(map[int64]mixReport.SubTotalItem)
			}

			if _, ok := tmpData[reMonth][item.BranchId]; ok {
				countV := tmpData[reMonth][item.BranchId].CountVehicle + item.CountVehicle
				ticketM := tmpData[reMonth][item.BranchId].MoneyTicketAmount + item.MoneyTicketAmount
				cardM := tmpData[reMonth][item.BranchId].MoneyCardAmount + item.MoneyCardAmount
				codeM := tmpData[reMonth][item.BranchId].MoneyCodeAmount + item.MoneyCodeAmount
				TotalM := ticketM + cardM + codeM
				var avg int64
				if countV == 0 {
					avg = 0
				} else {
					avg = TotalM / countV
				}

				tmpData[reMonth][item.BranchId] = mixReport.SubTotalItem{
					Month:             reMonth,
					BranchId:          item.BranchId,
					Branch:            item.Branch,
					CountVehicle:      countV,
					MoneyTicketAmount: ticketM,
					MoneyCardAmount:   cardM,
					MoneyCodeAmount:   codeM,
					AVGIncomeVehicle:  avg,
					TotalAmount:       TotalM,
				}
			} else {
				item.TotalAmount = item.MoneyTicketAmount + item.MoneyCardAmount + item.MoneyCodeAmount
				if item.CountVehicle == 0 {
					item.AVGIncomeVehicle = 0
				} else {
					item.AVGIncomeVehicle = item.TotalAmount / item.CountVehicle
				}
				tmpData[reMonth][item.BranchId] = item
			}
		}

	} else if q.TimeDimension == ticket.TIME_DAILY_2 {
		for _, item := range subTotalItems {

			if _, ok := tmpData[item.Month]; !ok {
				tmpData[item.Month] = make(map[int64]mixReport.SubTotalItem)
			}
			item.TotalAmount = item.MoneyTicketAmount + item.MoneyCardAmount + item.MoneyCodeAmount
			if item.CountVehicle == 0 {
				item.AVGIncomeVehicle = 0
			} else {
				item.AVGIncomeVehicle = item.TotalAmount / item.CountVehicle
			}

			tmpData[item.Month][item.BranchId] = item
		}

	}

	var rspD IncomeBranchRsp

	for _, branchDatas := range tmpData {

		var st SubTotal

		for _, item := range branchDatas {
			//var branch string
			//
			//// 获取分公司
			//if oetCorporationItem, ok := rpcTmpCorporation[branchid]; ok {
			//	if oetCorporationItem != nil {
			//		branch = oetCorporationItem.Name
			//	}
			//} else {
			//	corporationItem := rpc.GetCorporationById(ctx, branchid)
			//	rpcTmpCorporation[branchid] = corporationItem
			//	if corporationItem != nil {
			//		branch = corporationItem.Name
			//	}
			//}

			//sti := ticketmoney.SubTotalItem{
			//	Month:             month,
			//	BranchId:          branchid,
			//	Branch:            branch,
			//	CountVehicle:      item.CountVehicle,
			//	MoneyTicketAmount: item.MoneyTicketAmount,
			//	AVGIncomeVehicle:  item.AVGIncomeVehicle,
			//}

			st.SubTotalMoneyTicketAmount += item.MoneyTicketAmount
			st.SubTotalMoneyCardAmount += item.MoneyCardAmount
			st.SubTotalMoneyCodeAmount += item.MoneyCodeAmount
			st.SubTotalAmount += item.TotalAmount
			st.SubTotalCountVehicle += item.CountVehicle
			st.SubTotalItem = append(st.SubTotalItem, item)

			rspD.TotalMoneyTicketAmount += item.MoneyTicketAmount
			rspD.TotalMoneyCardAmount += item.MoneyCardAmount
			rspD.TotalMoneyCodeAmount += item.MoneyCodeAmount
			rspD.TotalAmount += item.TotalAmount
			rspD.TotalCountVehicle += item.CountVehicle

			if st.SubTotalCountVehicle == 0 {
				st.SubTotalAVGVIncomeVehicle = 0
				log.Error("st.SubTotalCountVehicle == 0")
			} else {
				st.SubTotalAVGVIncomeVehicle = st.SubTotalAmount / st.SubTotalCountVehicle
			}
		}
		// 分公司排序
		sort.SliceStable(st.SubTotalItem, func(i, j int) bool {
			var iBranch string
			var jBranch string
			if len(st.SubTotalItem) > 0 {
				iBranch = util.ChineseNumberReplace(st.SubTotalItem[i].Branch)
			}

			if len(st.SubTotalItem) > 0 {
				jBranch = util.ChineseNumberReplace(st.SubTotalItem[j].Branch)
			}

			return iBranch < jBranch
		})

		rspD.SubTotalMonth = append(rspD.SubTotalMonth, st)
	}

	if rspD.TotalCountVehicle == 0 {
		rspD.AVGCountVehicle = 0
		log.Error("st.SubTotalCountVehicle == 0")
	} else {
		rspD.AVGCountVehicle = rspD.TotalAmount / rspD.TotalCountVehicle
	}

	// 日期排序
	sort.SliceStable(rspD.SubTotalMonth, func(i, j int) bool {
		var iMonth string
		var jMonth string

		if len(rspD.SubTotalMonth) > 0 {
			if len(rspD.SubTotalMonth[i].SubTotalItem) > 0 {
				iMonth = rspD.SubTotalMonth[i].SubTotalItem[0].Month // 同一个month 任意取一个切片
			}
		}
		if len(rspD.SubTotalMonth) > 0 {
			if len(rspD.SubTotalMonth[j].SubTotalItem) > 0 {
				jMonth = rspD.SubTotalMonth[j].SubTotalItem[0].Month // 同一个month 任意取一个切片
			}
		}

		return iMonth < jMonth
	})

	data := map[string]interface{}{
		"Item": rspD,
	}
	return util.SendResp(rsp, "", "0", data)
}

type IncomeLineRsp struct {
	TotalCountVehicle      int64 // 车辆数总计
	TotalMoneyTicketAmount int64 // 票款总计
	TotalMoneyCardAmount   int64 // 实体卡收入
	TotalMoneyCodeAmount   int64 // 虚拟卡收入
	TotalAmount            int64
	Data                   []IncomeLineRspDaily
}

type IncomeLineRspDaily struct {
	TotalCountVehicle      int64 // 车辆数总计
	TotalMoneyTicketAmount int64 // 票款总计
	TotalMoneyCardAmount   int64 // 实体卡收入
	TotalMoneyCodeAmount   int64 // 虚拟卡收入
	TotalAmount            int64
	Data                   []IncomeLineRspDailyBranch
}

type IncomeLineRspDailyBranch struct {
	TotalCountVehicle      int64 // 车辆数总计
	TotalMoneyTicketAmount int64 // 票款总计
	TotalMoneyCardAmount   int64 // 实体卡收入
	TotalMoneyCodeAmount   int64 // 虚拟卡收入
	TotalAmount            int64
	Data                   []IncomeLineRspDailyBranchFleet
}

type IncomeLineRspDailyBranchFleet struct {
	TotalCountVehicle      int64 // 车辆数总计
	TotalMoneyTicketAmount int64 // 票款总计
	TotalMoneyCardAmount   int64 // 实体卡收入
	TotalMoneyCodeAmount   int64 // 虚拟卡收入
	TotalAmount            int64
	Data                   []mixReport.IncomeLineRspItem // 每个线路数据
}

// IncomeLine 线路营收报表
func (r *Report) IncomeLine(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Report
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	rpcTmpLine := make(map[int64]*protooetline.OetLineItem)                // map[LineId]线路信息
	rpcTmpCorporation := make(map[int64]*protocorporation.CorporationItem) // map[corporationId]机构信息

	if q.IsExport {
		//lineRsp, err := (&q.TicketCountMoneys).IncomeLine(q.TimeType, q.BranchIds, q.LineIds, q.FleetIds, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0))
		//if err != nil {
		//	log.Error("IncomeLine err=", err.Error())
		//	return util.SendResp(rsp, "", "OP1004", nil)
		//}
		lineRsp, err := (&mixReport.TicketMoneyMixReportRecord{}).IncomeLine(q.TimeType, q.BranchIds, q.LineIds, q.FleetIds, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0))
		if err != nil {
			log.Error("IncomeLine err=", err.Error())
			return util.SendResp(rsp, "", "OP1004", nil)
		}
		// 处理数据
		tmpData := make(map[string]map[int64]map[int64]map[int64]mixReport.IncomeLineRspItem) // map[date]map[branchId]map[fleetId]map[lineId]

		for _, item := range lineRsp.Data {
			if _, ok := tmpData[item.DateAt]; !ok {
				tmpData[item.DateAt] = make(map[int64]map[int64]map[int64]mixReport.IncomeLineRspItem)
			}
			if _, ok := tmpData[item.DateAt][item.BranchId]; !ok {
				tmpData[item.DateAt][item.BranchId] = make(map[int64]map[int64]mixReport.IncomeLineRspItem)
			}
			if _, ok := tmpData[item.DateAt][item.BranchId][item.FleetId]; !ok {
				tmpData[item.DateAt][item.BranchId][item.FleetId] = make(map[int64]mixReport.IncomeLineRspItem)
			}
			data, ok := tmpData[item.DateAt][item.BranchId][item.FleetId][item.LineId]
			if !ok {
				data = item
			} else {
				data.CountVehicle += item.CountVehicle
				data.MoneyTicketAmount += item.MoneyTicketAmount
				data.MoneyCardAmount += item.MoneyCardAmount
				data.MoneyCodeAmount += item.MoneyCodeAmount
				data.TotalAmount += item.MoneyTicketAmount + item.MoneyCodeAmount + item.MoneyCardAmount
			}

			tmpData[item.DateAt][item.BranchId][item.FleetId][item.LineId] = data

		}

		var rspD IncomeLineRsp

		for _, branchData := range tmpData {
			var ilrDaily IncomeLineRspDaily

			for branchId, fleetData := range branchData {
				var ilrdBranch IncomeLineRspDailyBranch
				var branch string

				// 获取分公司
				if oetCorporationItem, ok := rpcTmpCorporation[branchId]; ok {
					if oetCorporationItem != nil {
						branch = oetCorporationItem.Name
					}
				} else {
					corporationItem := rpc.GetCorporationById(ctx, branchId)
					rpcTmpCorporation[branchId] = corporationItem
					if corporationItem != nil {
						branch = corporationItem.Name
					}
				}

				for fleetId, LineData := range fleetData {
					var ilrdbFleet IncomeLineRspDailyBranchFleet
					var fleet string

					// 获取车队
					if oetCorporationItem, ok := rpcTmpCorporation[fleetId]; ok {
						if oetCorporationItem != nil {
							fleet = oetCorporationItem.Name
						}
					} else {
						corporationItem := rpc.GetCorporationById(ctx, fleetId)
						rpcTmpCorporation[fleetId] = corporationItem
						if corporationItem != nil {
							fleet = corporationItem.Name
						}
					}

					for lineId, item := range LineData {
						var line string
						// 获取线路信息
						if oetLineItem, ok := rpcTmpLine[lineId]; ok {
							if oetLineItem != nil {
								line = oetLineItem.Name
							}
						} else {
							lineItem, _ := rpc.GetLineWithId(ctx, lineId)
							rpcTmpLine[lineId] = lineItem
							if lineItem != nil {
								line = lineItem.Name
							}
						}

						rspItem := mixReport.IncomeLineRspItem{
							DateAt:            item.DateAt,
							BranchId:          branchId,
							Branch:            branch,
							FleetId:           fleetId,
							Fleet:             fleet,
							LineId:            lineId,
							Line:              line,
							CountVehicle:      item.CountVehicle,
							MoneyTicketAmount: item.MoneyTicketAmount,
							MoneyCardAmount:   item.MoneyCardAmount,
							MoneyCodeAmount:   item.MoneyCodeAmount,
						}

						ilrdbFleet.TotalCountVehicle += item.CountVehicle
						ilrdbFleet.TotalMoneyTicketAmount += item.MoneyTicketAmount
						ilrdbFleet.TotalMoneyCodeAmount += item.MoneyCodeAmount
						ilrdbFleet.TotalMoneyCardAmount += item.MoneyCardAmount
						ilrdbFleet.TotalAmount += item.MoneyTicketAmount + item.MoneyCodeAmount + item.MoneyCardAmount
						ilrdbFleet.Data = append(ilrdbFleet.Data, rspItem)

						ilrdBranch.TotalCountVehicle += item.CountVehicle
						ilrdBranch.TotalMoneyCodeAmount += item.MoneyCodeAmount
						ilrdBranch.TotalMoneyCardAmount += item.MoneyCardAmount
						ilrdBranch.TotalMoneyTicketAmount += item.MoneyTicketAmount
						ilrdBranch.TotalAmount += item.MoneyTicketAmount + item.MoneyCodeAmount + item.MoneyCardAmount

						ilrDaily.TotalCountVehicle += item.CountVehicle
						ilrDaily.TotalMoneyCodeAmount += item.MoneyCodeAmount
						ilrDaily.TotalMoneyCardAmount += item.MoneyCardAmount
						ilrDaily.TotalMoneyTicketAmount += item.MoneyTicketAmount
						ilrDaily.TotalAmount += item.MoneyTicketAmount + item.MoneyCodeAmount + item.MoneyCardAmount

						rspD.TotalCountVehicle += item.CountVehicle
						rspD.TotalMoneyCodeAmount += item.MoneyCodeAmount
						rspD.TotalMoneyCardAmount += item.MoneyCardAmount
						rspD.TotalMoneyTicketAmount += item.MoneyTicketAmount
						rspD.TotalAmount += item.MoneyTicketAmount + item.MoneyCodeAmount + item.MoneyCardAmount
					}
					// 车队切片 线路排序
					sort.SliceStable(ilrdbFleet.Data, func(i, j int) bool {

						return ilrdbFleet.Data[i].Line < ilrdbFleet.Data[j].Line
					})

					ilrdBranch.Data = append(ilrdBranch.Data, ilrdbFleet)

				}
				// 分公司切片 车队排序
				sort.SliceStable(ilrdBranch.Data, func(i, j int) bool {
					var iFleet string
					var jFleet string
					if len(ilrdBranch.Data[i].Data) > 0 {
						iFleet = util.ChineseNumberReplace(ilrdBranch.Data[i].Data[0].Fleet) // 同车队 任意取切片一个元素
					}

					if len(ilrdBranch.Data[j].Data) > 0 {
						jFleet = util.ChineseNumberReplace(ilrdBranch.Data[j].Data[0].Fleet)
					}
					return iFleet < jFleet
				})
				ilrDaily.Data = append(ilrDaily.Data, ilrdBranch)

			}
			// 每日数据切片 分公司排序
			sort.SliceStable(ilrDaily.Data, func(i, j int) bool {
				var iBranch string
				var jBranch string

				iBranch = util.ChineseNumberReplace(ilrDaily.Data[i].Data[0].Data[0].Branch)
				jBranch = util.ChineseNumberReplace(ilrDaily.Data[j].Data[0].Data[0].Branch)

				return iBranch < jBranch
			})
			rspD.Data = append(rspD.Data, ilrDaily)
		}
		// 日期排序
		sort.SliceStable(rspD.Data, func(i, j int) bool {

			return rspD.Data[i].Data[0].Data[0].Data[0].DateAt < rspD.Data[j].Data[0].Data[0].Data[0].DateAt
		})

		data := map[string]interface{}{
			"Item": rspD,
		}
		return util.SendResp(rsp, "", "0", data)
	} else {
		lineRsp, err := (&mixReport.TicketMoneyMixReportRecord{}).IncomeLine(q.TimeType, q.BranchIds, q.LineIds, q.FleetIds, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0))
		if err != nil {
			log.Error("IncomeLine err=", err.Error())
			return util.SendResp(rsp, "", "OP1004", nil)
		}

		for i, item := range lineRsp.Data {
			lineRsp.Data[i].TotalAmount = item.MoneyTicketAmount + item.MoneyCardAmount + item.MoneyCodeAmount

			lineRsp.TotalMoneyTicketAmount += item.MoneyTicketAmount
			lineRsp.TotalMoneyCardAmount += item.MoneyCardAmount
			lineRsp.TotalMoneyCodeAmount += item.MoneyCodeAmount
			lineRsp.TotalCountVehicle += item.CountVehicle

			// 获取线路信息
			// rpc 获取线路信息

			//if oetLineItem, ok := rpcTmpLine[item.LineId]; ok {
			//	if oetLineItem != nil {
			//		lineRsp.Data[i].Line = oetLineItem.Name
			//	}
			//} else {
			//	lineItem, _ := rpc.GetLineWithId(ctx, item.LineId)
			//	rpcTmpLine[item.LineId] = lineItem
			//	if lineItem != nil {
			//		lineRsp.Data[i].Line = lineItem.Name
			//	}
			//}
			//
			//// 获取分公司
			//if oetCorporationItem, ok := rpcTmpCorporation[item.BranchId]; ok {
			//	if oetCorporationItem != nil {
			//		lineRsp.Data[i].Branch = oetCorporationItem.Name
			//	}
			//} else {
			//	corporationItem := rpc.GetCorporationById(ctx, item.BranchId)
			//	rpcTmpCorporation[item.BranchId] = corporationItem
			//	if corporationItem != nil {
			//		lineRsp.Data[i].Branch = corporationItem.Name
			//	}
			//}
			//
			//// 获取车队信息
			//if oetCorporationItem, ok := rpcTmpCorporation[item.FleetId]; ok {
			//	if oetCorporationItem != nil {
			//		lineRsp.Data[i].Fleet = oetCorporationItem.Name
			//	}
			//} else {
			//	corporationItem := rpc.GetCorporationById(ctx, item.FleetId)
			//	rpcTmpCorporation[item.FleetId] = corporationItem
			//	if corporationItem != nil {
			//		lineRsp.Data[i].Fleet = corporationItem.Name
			//	}
			//}

		}

		lineRsp.TotalAmount = lineRsp.TotalMoneyTicketAmount + lineRsp.TotalMoneyCodeAmount + lineRsp.TotalMoneyCardAmount
		// 排序

		sortTime := func(i, j *mixReport.IncomeLineRspItem) bool {

			return i.DateAt < j.DateAt
		}

		sortBranch := func(i, j *mixReport.IncomeLineRspItem) bool {

			return util.ChineseNumberReplace(i.Branch) < util.ChineseNumberReplace(j.Branch)
		}
		sortFleet := func(i, j *mixReport.IncomeLineRspItem) bool {
			return util.ChineseNumberReplace(i.Fleet) < util.ChineseNumberReplace(j.Fleet)
		}

		sortLine := func(i, j *mixReport.IncomeLineRspItem) bool {
			return i.Line < j.Line
		}

		mixReport.OrderByLine([]mixReport.LessFuncLine{sortTime, sortBranch, sortFleet, sortLine}).Sort(lineRsp.Data)

		// 分页
		if int64(q.Limit+q.Offset) >= lineRsp.TotalCount {
			if int64(q.Offset) < lineRsp.TotalCount {
				lineRsp.Data = lineRsp.Data[q.Offset:]
			} else {
			}
		} else {
			lineRsp.Data = lineRsp.Data[q.Offset:(q.Offset + q.Limit)]
		}

		data := map[string]interface{}{
			"Item": lineRsp,
		}
		return util.SendResp(rsp, "", "0", data)
	}
}

type IVExport struct {
	DatesAt []time.Time // 对应票款的时间

	DateAndMoney DateAndMoney

	BranchId int64  // 分公司id
	Branch   string // 分公司

	LineId int64  // 线路id
	Line   string // 线路

	License     string // 车牌号
	VehicleCode string // 车编号

	AttendantName       string // 乘务员
	AttendantStaffIdStr string //乘务员工号

	MoneyTicketAmount []int64 // 票款
	TotalAmount       int64   // MoneyTicketAmount 总计
}

type IVEResp struct {
	TotalMoneyTicketAmount []int64 // 总计
	Data                   []IVERespBranch
}

type IVERespBranch struct {
	TotalMoneyTicketAmount []int64 // 分公司总计
	Data                   []IVERespLine
}

type IVERespLine struct {
	TotalMoneyTicketAmount []int64 // 线路总计
	Data                   []IVExport
}

type DateAndMoney struct {
	Date  string // 20060102
	Money int64
}

// IncomeVehicle 车辆营收报表
func (r *Report) IncomeVehicle(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Report
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	rpcTmpLine := make(map[int64]*protooetline.OetLineItem)                // map[LineId]线路信息
	rpcTmpCorporation := make(map[int64]*protocorporation.CorporationItem) // map[corporationId]机构信息
	//fmt.Println(q.IsExport)
	if q.IsExport {
		//q.ExportType = 2
		if q.ExportType == 2 {
			vehicleRsp := (&mixReport.TicketMoneyMixReportRecord{}).IncomeVehicle(q.TimeType, q.BranchIds, q.FleetIds, q.LineIds, q.Licenses, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0))
			for i, _ := range vehicleRsp.Data {
				vehicleRsp.Data[i].TotalAmount = vehicleRsp.Data[i].MoneyTicketAmount + vehicleRsp.Data[i].MoneyCardAmount + vehicleRsp.Data[i].MoneyCodeAmount
				vehicleRsp.TotalMoneyTicketAmount += vehicleRsp.Data[i].MoneyTicketAmount
				vehicleRsp.TotalMoneyCardAmount += vehicleRsp.Data[i].MoneyCardAmount
				vehicleRsp.TotalMoneyCodeAmount += vehicleRsp.Data[i].MoneyCodeAmount
				// 转换时间
				vehicleRsp.Data[i].DateAtUnix = vehicleRsp.Data[i].DateAt.Unix()
			}
			vehicleRsp.TotalAmount = vehicleRsp.TotalMoneyTicketAmount + vehicleRsp.TotalMoneyCardAmount + vehicleRsp.TotalMoneyCodeAmount
			// 排序
			sortTime := func(i, j *mixReport.IncomeVehicleRspItem) bool {
				return i.DateAt.Format("20060102") < j.DateAt.Format("20060102")
			}

			sortBranch := func(i, j *mixReport.IncomeVehicleRspItem) bool {

				return util.ChineseNumberReplace(i.Branch) < util.ChineseNumberReplace(j.Branch)
			}
			sortFleet := func(i, j *mixReport.IncomeVehicleRspItem) bool {
				return util.ChineseNumberReplace(i.Fleet) < util.ChineseNumberReplace(j.Fleet)
			}

			sortLine := func(i, j *mixReport.IncomeVehicleRspItem) bool {
				return i.Line < j.Line
			}

			sortLicense := func(i, j *mixReport.IncomeVehicleRspItem) bool {
				return i.License < j.License
			}

			mixReport.OrderBy([]mixReport.LessFunc{sortTime, sortBranch, sortFleet, sortLine, sortLicense}).Sort(vehicleRsp.Data)
			var rspD IVEResp
			branchMap := make(map[int64]IVERespBranch)
			lineMap := make(map[int64]map[int64]IVERespLine)
			licenseMap := make(map[int64]map[int64]map[string]IVExport)
			licenseIndex := make(map[int64]map[int64]map[string]int) //
			lineIndex := make(map[int64]map[int64]int)               // [公司][线路][下标]
			totalDay := 0
			dayIndex := 0
			dayToIndexMap := make(map[int64]int) // 时间和下标映射
			point := q.StartAt                   // 指针
			for point <= q.EndAt {
				dayToIndexMap[point] = dayIndex
				dayIndex++
				totalDay++
				point += 24 * 60 * 60
			}
			for _, data := range vehicleRsp.Data {
				if _, ok := licenseMap[data.BranchId]; !ok {
					licenseMap[data.BranchId] = make(map[int64]map[string]IVExport)
				}
				if _, ok := licenseMap[data.BranchId][data.LineId]; !ok {
					licenseMap[data.BranchId][data.LineId] = make(map[string]IVExport)
				}
				ive, ok := licenseMap[data.BranchId][data.LineId][data.License]
				if data.LineId == 0 {
					data.Line = "无线路"
				}
				if !ok {
					ive = IVExport{
						DatesAt: nil,
						DateAndMoney: DateAndMoney{
							Date:  data.DateAt.Format("20060102"),
							Money: 0,
						},
						BranchId:          data.BranchId,
						Branch:            data.Branch,
						LineId:            data.LineId,
						Line:              data.Line,
						License:           data.License,
						VehicleCode:       data.VehicleCode,
						MoneyTicketAmount: make([]int64, totalDay+1),
						TotalAmount:       0,
					}
				}
				newTime := data.DateAt.In(time.Local)
				newTime = time.Date(newTime.Year(), newTime.Month(), newTime.Day(), 0, 0, 0, 0, time.Local)
				//fmt.Println(newTime)
				//fmt.Println(newTime.Unix())
				ive.MoneyTicketAmount[dayToIndexMap[newTime.Unix()]] += data.TotalAmount
				var tempTotal int64
				for l := range ive.MoneyTicketAmount {
					if l != len(ive.MoneyTicketAmount)-1 {
						tempTotal += ive.MoneyTicketAmount[l]
					} else {
						ive.MoneyTicketAmount[l] = tempTotal
					}
				}
				licenseMap[data.BranchId][data.LineId][data.License] = ive
				// 线路
				if _, ok := lineMap[ive.BranchId]; !ok {
					lineMap[ive.BranchId] = make(map[int64]IVERespLine)
				}
				iVERespLine, ok := lineMap[ive.BranchId][ive.LineId]
				if !ok {
					iVERespLine = IVERespLine{
						Data:                   []IVExport{},
						TotalMoneyTicketAmount: make([]int64, totalDay+1),
					}
				}
				if _, ok := licenseIndex[ive.BranchId]; !ok {
					licenseIndex[ive.BranchId] = make(map[int64]map[string]int)
				}
				if _, ok := licenseIndex[ive.BranchId][ive.LineId]; !ok {
					licenseIndex[ive.BranchId][ive.LineId] = make(map[string]int)
				}
				licenseIndexData, has := licenseIndex[ive.BranchId][ive.LineId][ive.License]
				if !has {
					licenseIndexData = -1
				}
				if licenseIndexData == -1 {
					//iVERespLine.TotalMoneyTicketAmount = []int64{0, 0}
					iVERespLine.Data = append(iVERespLine.Data, licenseMap[ive.BranchId][ive.LineId][ive.License])
					licenseIndexData = len(iVERespLine.Data) - 1
				} else {
					iVERespLine.Data[licenseIndexData] = licenseMap[ive.BranchId][ive.LineId][ive.License]
				}
				licenseIndex[ive.BranchId][ive.LineId][ive.License] = licenseIndexData
				TotalMoneyTicketAmount := make([]int64, totalDay+1)
				for _, d := range iVERespLine.Data {
					for l, v := range d.MoneyTicketAmount {
						TotalMoneyTicketAmount[l] += v
					}
				}
				iVERespLine.TotalMoneyTicketAmount = TotalMoneyTicketAmount
				//iVERespLine.TotalMoneyTicketAmount = []int64{total1, total2}
				lineMap[ive.BranchId][ive.LineId] = iVERespLine
				// 再是 branch
				iVERespBranch, ok := branchMap[ive.BranchId]
				if !ok {
					iVERespBranch = IVERespBranch{
						Data:                   []IVERespLine{},
						TotalMoneyTicketAmount: make([]int64, totalDay+1),
					}
				}
				_, has = lineIndex[ive.BranchId]
				if !has {
					lineIndex[ive.BranchId] = make(map[int64]int)
				}
				_, has = lineIndex[ive.BranchId][ive.LineId]
				if !has {
					lineIndex[ive.BranchId][ive.LineId] = -1
				}
				if lineIndex[data.BranchId][ive.LineId] == -1 {
					iVERespBranch.TotalMoneyTicketAmount = []int64{0, 0}
					iVERespBranch.Data = append(iVERespBranch.Data, lineMap[ive.BranchId][ive.LineId])
					lineIndex[ive.BranchId][ive.LineId] = len(iVERespBranch.Data) - 1
				} else {
					iVERespBranch.Data[lineIndex[ive.BranchId][ive.LineId]] = lineMap[ive.BranchId][ive.LineId]
				}
				TotalMoneyTicketAmount = make([]int64, totalDay+1)
				for _, d := range iVERespBranch.Data {
					for l, v := range d.TotalMoneyTicketAmount {
						TotalMoneyTicketAmount[l] += v
					}
				}
				iVERespBranch.TotalMoneyTicketAmount = TotalMoneyTicketAmount
				branchMap[ive.BranchId] = iVERespBranch
			}
			//var total1, total2 int64
			//fmt.Println("==============totalDay===========")
			//fmt.Println(totalDay)
			rspD.TotalMoneyTicketAmount = make([]int64, totalDay+1)
			TotalMoneyTicketAmount := make([]int64, totalDay+1)
			for _, value := range branchMap {
				rspD.Data = append(rspD.Data, value)
				//fmt.Println(len(value.TotalMoneyTicketAmount))
				for l, v := range value.TotalMoneyTicketAmount {
					TotalMoneyTicketAmount[l] += v
				}
			}
			rspD.TotalMoneyTicketAmount = TotalMoneyTicketAmount
			data := map[string]interface{}{
				"Item": rspD,
			}
			return util.SendResp(rsp, "", "0", data)
		} else {
			var rspD IVEResp
			s := time.Unix(q.StartAt, 0)
			e := time.Unix(q.EndAt, 0)
			ticketCountMoneys, _ := (&q.TicketCountMoneys).IncomeVehicleExportAll(q.TimeType, q.BranchIds, q.FleetIds, q.LineIds, q.License, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0))
			// 处理查询出来的全部数据
			tmpTicketLicense := make(map[string][]IVExport) // map[license]
			if q.TimeType == ticket.TIME_INPUT_1 {
				for _, money := range ticketCountMoneys {
					tmpTicketLicense[money.License] = append(tmpTicketLicense[money.License], IVExport{
						DatesAt: nil,
						DateAndMoney: DateAndMoney{
							Date:  money.ReportAt.Format("20060102"),
							Money: money.TotalAmount,
						},
						BranchId:          money.BranchId,
						Branch:            "",
						LineId:            money.LineId,
						Line:              "",
						License:           money.License,
						VehicleCode:       money.VehicleCode,
						MoneyTicketAmount: nil,
						TotalAmount:       0,
					})
				}
			} else if q.TimeType == ticket.TIME_TICKET_2 {
				for _, money := range ticketCountMoneys {
					tmpTicketLicense[money.License] = append(tmpTicketLicense[money.License], IVExport{
						DatesAt: nil,
						DateAndMoney: DateAndMoney{
							Date:  money.IncomeAt.Format("20060102"),
							Money: money.TotalAmount,
						},
						BranchId:          money.BranchId,
						Branch:            "",
						LineId:            money.LineId,
						Line:              "",
						License:           money.License,
						VehicleCode:       money.VehicleCode,
						MoneyTicketAmount: nil,
						TotalAmount:       0,
					})
				}
			}

			// 结构
			group := (&q.TicketCountMoneys).IncomeVehicleExportGroup(q.TimeType, q.BranchIds, q.FleetIds, q.LineIds, q.License, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0))

			tmp := make(map[int64]map[int64]map[string]byte) // map[分公司][线路][车辆]

			for _, item := range group {

				if _, ok := tmp[item.BranchId]; !ok {
					tmp[item.BranchId] = make(map[int64]map[string]byte)
				}

				if _, ok := tmp[item.BranchId][item.LineId]; !ok {
					tmp[item.BranchId][item.LineId] = make(map[string]byte)
				}

				if _, ok := tmp[item.BranchId][item.LineId][item.License]; !ok {
					tmp[item.BranchId][item.LineId][item.License] = 0
				}

			}

			days := util.DiffNatureDays(q.StartAt, q.EndAt) // 查询时间间隔
			// 前端查询 14日 则会传值 2022-07-14 00:00:00 - 2022-07-15 00:00:00  实际查询左闭右开 并没有15日 应当减去1天
			if e.Hour() == 0 && e.Minute() == 0 && e.Second() == 0 {
				days--
			}

			//var rspD IVEResp
			rspD.TotalMoneyTicketAmount = make([]int64, days+1) // +1 是总计

			for branchId, lineData := range tmp {
				var b IVERespBranch
				b.TotalMoneyTicketAmount = make([]int64, days+1)
				var branch string

				// 获取分公司
				if oetCorporationItem, ok := rpcTmpCorporation[branchId]; ok {
					if oetCorporationItem != nil {
						branch = oetCorporationItem.Name
					}
				} else {
					corporationItem := rpc.GetCorporationById(ctx, branchId)
					rpcTmpCorporation[branchId] = corporationItem
					if corporationItem != nil {
						branch = corporationItem.Name
					}
				}

				for lineId, vehicleData := range lineData {
					var l IVERespLine
					l.TotalMoneyTicketAmount = make([]int64, days+1) // 初始化切片，在下面的步骤中直接赋值
					var line string
					// 获取线路信息
					if oetLineItem, ok := rpcTmpLine[lineId]; ok {
						if oetLineItem != nil {
							line = oetLineItem.Name
						}
					} else {
						lineItem, _ := rpc.GetLineWithId(ctx, lineId)
						rpcTmpLine[lineId] = lineItem
						if lineItem != nil {
							line = lineItem.Name
						}
					}

					for license, _ := range vehicleData {
						var (
							dates     []DateAndMoney
							datesFill []DateAndMoney // 按查询时间 补充当天无数据

							ticketMoneys []int64

							vehicleCode string // 同一辆车code为同一个
							totalAmount int64  // 计算总金额 放到 ticketMoneys 最后一个元素
						)

						for _, export := range tmpTicketLicense[license] {
							if export.BranchId == branchId && export.LineId == lineId {
								dates = append(dates, export.DateAndMoney)

								vehicleCode = export.VehicleCode

								totalAmount += export.DateAndMoney.Money

							}
						}

						// 按时间排序 查询时间段内没有数据填充0

						// 一天多个票款 时段不同 累计
						tmpDates := make(map[string]DateAndMoney) // map[20060102]
						//var tmpDatesIdx []string
						for _, dateAndMoney := range dates {

							if _, ok := tmpDates[dateAndMoney.Date]; ok {
								tmpDates[dateAndMoney.Date] = DateAndMoney{
									Date:  dateAndMoney.Date,
									Money: tmpDates[dateAndMoney.Date].Money + dateAndMoney.Money,
								}

							} else {
								tmpDates[dateAndMoney.Date] = dateAndMoney
							}

						}

						// 遍历计算每天数据 没有则填充
						for i := 0; i < int(days); i++ {
							// 从开始日期开始
							format := time.Date(s.Year(), s.Month(), s.Day(), 0, 0, 0, 0, time.UTC).AddDate(0, 0, i).Format("20060102")
							if dam, ok := tmpDates[format]; ok {
								datesFill = append(datesFill, dam)

								l.TotalMoneyTicketAmount[i] += dam.Money
								b.TotalMoneyTicketAmount[i] += dam.Money
								rspD.TotalMoneyTicketAmount[i] += dam.Money
							} else {
								datesFill = append(datesFill, DateAndMoney{
									Date:  format,
									Money: 0,
								})
							}
						}

						for _, dam := range datesFill {
							ticketMoneys = append(ticketMoneys, dam.Money)
						}
						// 将合计追加至切片
						ticketMoneys = append(ticketMoneys, totalAmount)
						l.TotalMoneyTicketAmount[days] += totalAmount    // 赋值给线路合计最后一位
						b.TotalMoneyTicketAmount[days] += totalAmount    // 赋值给分公司合计最后一位
						rspD.TotalMoneyTicketAmount[days] += totalAmount // 赋值给合计最后一位

						l.Data = append(l.Data, IVExport{
							//DatesAt:           nil,
							//DateAt:            time.Time{},
							//Amount:            0,
							BranchId:          branchId,
							Branch:            branch,
							LineId:            lineId,
							Line:              line,
							License:           license,
							VehicleCode:       vehicleCode,
							MoneyTicketAmount: ticketMoneys,
							//TotalAmount:       0,
						})

					}

					// 线路切片车辆排序 l.Date
					//
					sort.SliceStable(l.Data, func(i, j int) bool {
						return l.Data[i].License < l.Data[j].License
					})

					b.Data = append(b.Data, l)
				}
				// 分公司切片 线路排序
				sort.SliceStable(b.Data, func(i, j int) bool {
					var iLine string
					var jLine string
					if len(b.Data[i].Data) > 0 {
						iLine = b.Data[i].Data[0].Line // 线路切片中线路都一样 所以取切片任意一个元素
					}

					if len(b.Data[j].Data) > 0 {
						jLine = b.Data[j].Data[0].Line // 线路切片中线路都一样 所以取切片任意一个元素
					}

					return iLine < jLine

				})

				rspD.Data = append(rspD.Data, b)
			}

			// 全部分公司切片 分公司排序
			sort.SliceStable(rspD.Data, func(i, j int) bool {
				var iLine string
				var jLine string
				if len(rspD.Data[i].Data) > 0 {
					if len(rspD.Data[i].Data[0].Data) > 0 {
						iLine = util.ChineseNumberReplace(rspD.Data[i].Data[0].Data[0].Branch)
					}
				}

				if len(rspD.Data[j].Data) > 0 {
					if len(rspD.Data[j].Data[0].Data) > 0 {
						jLine = util.ChineseNumberReplace(rspD.Data[j].Data[0].Data[0].Branch)
					}
				}

				return iLine < jLine
			})

			data := map[string]interface{}{
				"Item": rspD,
			}
			return util.SendResp(rsp, "", "0", data)
		}

	} else {
		vehicleRsp := (&mixReport.TicketMoneyMixReportRecord{}).IncomeVehicle(q.TimeType, q.BranchIds, q.FleetIds, q.LineIds, q.Licenses, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0))

		for i, _ := range vehicleRsp.Data {
			vehicleRsp.Data[i].TotalAmount = vehicleRsp.Data[i].MoneyTicketAmount + vehicleRsp.Data[i].MoneyCardAmount + vehicleRsp.Data[i].MoneyCodeAmount
			vehicleRsp.TotalMoneyTicketAmount += vehicleRsp.Data[i].MoneyTicketAmount
			vehicleRsp.TotalMoneyCardAmount += vehicleRsp.Data[i].MoneyCardAmount
			vehicleRsp.TotalMoneyCodeAmount += vehicleRsp.Data[i].MoneyCodeAmount
			// 转换时间
			vehicleRsp.Data[i].DateAtUnix = vehicleRsp.Data[i].DateAt.Unix()

			//// 获取线路信息
			//// rpc 获取线路信息
			//
			//if oetLineItem, ok := rpcTmpLine[item.LineId]; ok {
			//	if oetLineItem != nil {
			//		vehicleRsp.Data[i].Line = oetLineItem.Name
			//	}
			//} else {
			//	lineItem, _ := rpc.GetLineWithId(ctx, item.LineId)
			//	rpcTmpLine[item.LineId] = lineItem
			//	if lineItem != nil {
			//		vehicleRsp.Data[i].Line = lineItem.Name
			//	}
			//}
			//
			//// 获取分公司
			//if oetCorporationItem, ok := rpcTmpCorporation[item.BranchId]; ok {
			//	if oetCorporationItem != nil {
			//		vehicleRsp.Data[i].Branch = oetCorporationItem.Name
			//	}
			//} else {
			//	corporationItem := rpc.GetCorporationById(ctx, item.BranchId)
			//	rpcTmpCorporation[item.BranchId] = corporationItem
			//	if corporationItem != nil {
			//		vehicleRsp.Data[i].Branch = corporationItem.Name
			//	}
			//}
			//
			//// 获取分车队
			//if oetCorporationItem, ok := rpcTmpCorporation[item.FleetId]; ok {
			//	if oetCorporationItem != nil {
			//		vehicleRsp.Data[i].Fleet = oetCorporationItem.Name
			//	}
			//} else {
			//	corporationItem := rpc.GetCorporationById(ctx, item.FleetId)
			//	rpcTmpCorporation[item.FleetId] = corporationItem
			//	if corporationItem != nil {
			//		vehicleRsp.Data[i].Fleet = corporationItem.Name
			//	}
			//}

		}

		vehicleRsp.TotalAmount = vehicleRsp.TotalMoneyTicketAmount + vehicleRsp.TotalMoneyCardAmount + vehicleRsp.TotalMoneyCodeAmount

		// 排序
		sortTime := func(i, j *mixReport.IncomeVehicleRspItem) bool {

			return i.DateAt.Format("20060102") < j.DateAt.Format("20060102")
		}

		sortBranch := func(i, j *mixReport.IncomeVehicleRspItem) bool {

			return util.ChineseNumberReplace(i.Branch) < util.ChineseNumberReplace(j.Branch)
		}
		sortFleet := func(i, j *mixReport.IncomeVehicleRspItem) bool {
			return util.ChineseNumberReplace(i.Fleet) < util.ChineseNumberReplace(j.Fleet)
		}

		sortLine := func(i, j *mixReport.IncomeVehicleRspItem) bool {
			return i.Line < j.Line
		}

		sortLicense := func(i, j *mixReport.IncomeVehicleRspItem) bool {
			return i.License < j.License
		}

		mixReport.OrderBy([]mixReport.LessFunc{sortTime, sortBranch, sortFleet, sortLine, sortLicense}).Sort(vehicleRsp.Data)

		// 分页
		if int64(q.Limit+q.Offset) >= vehicleRsp.TotalCount {
			if int64(q.Offset) < vehicleRsp.TotalCount {
				vehicleRsp.Data = vehicleRsp.Data[q.Offset:]
			} else {
			}
		} else {
			vehicleRsp.Data = vehicleRsp.Data[q.Offset:(q.Offset + q.Limit)]
		}

		data := map[string]interface{}{
			"Item": vehicleRsp,
		}
		return util.SendResp(rsp, "", "0", data)
	}
}

// AttendantDaily 乘务员日报
func (r *Report) AttendantDaily(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Report
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}
	if len(q.StaffIds) == 0 {
		log.Error("len(q.StaffIds) == 0")
		return util.SendResp(rsp, "", "0", nil)
	}
	attendantDate, err := (&q.TicketCountMoneys).AttendantDate(q.TimeType, q.BranchIds, q.LineId,
		q.StaffId, q.License, q.Keyword, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0), q.Paginator)
	if err != nil {
		log.Error("AttendantDate err=", err.Error())
		return util.SendResp(rsp, "", "OP1004", nil)
	}
	//
	cli1 := protocorporation.NewCorporationService("oet.scs.srv.public", r.Client)

	for i, item := range attendantDate.Data {

		// 转换时间
		attendantDate.Data[i].ReportAtUnix = attendantDate.Data[i].ReportAt.Unix()

		// 获取分公司
		getById, err := cli1.GetById(ctx, &protocorporation.GetCorporationByIdRequest{
			Id: item.BranchId,
		})
		if err != nil {
			log.Error("cli1.GetById err= ", err)
			//return util.SendResp(rsp, "", "OP7006", nil)

		} else {
			if getById == nil {
				log.Error("getById == nil", err)
				//return util.SendResp(rsp, "", "OP7006", nil)

			} else {
				attendantDate.Data[i].Branch = getById.Name
			}
		}

	}
	data := map[string]interface{}{
		"Item": attendantDate,
	}
	return util.SendResp(rsp, "", "0", data)
}

// MultiTicket 多票制
func (r *Report) MultiTicket(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Report
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}
	//if len(q.Licenses) == 0 {
	//	log.Error("len(q.Licenses) == 0")
	//	return util.SendResp(rsp, "", "0", nil)
	//}
	multiTicket, err := (&q.TicketCountMoneys).MultiTicket(q.TimeType, q.BranchIds, q.LineId, q.License, time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0), q.Paginator)
	if err != nil {
		log.Error("MultiTicket err=", err.Error())
		return util.SendResp(rsp, "", "OP1004", nil)
	}
	//
	cli1 := protocorporation.NewCorporationService("oet.scs.srv.public", r.Client)

	for i, item := range multiTicket.Data {

		// 转换时间
		multiTicket.Data[i].ReportAtUnix = multiTicket.Data[i].ReportAt.Unix()

		// 获取分公司
		getById, err := cli1.GetById(ctx, &protocorporation.GetCorporationByIdRequest{
			Id: item.BranchId,
		})
		if err != nil {
			log.Error("cli1.GetById err= ", err)
			//return util.SendResp(rsp, "", "OP7006", nil)

		} else {
			if getById == nil {
				log.Error("getById == nil", err)
				//return util.SendResp(rsp, "", "OP7006", nil)

			} else {
				multiTicket.Data[i].Branch = getById.Name
			}
		}

	}
	data := map[string]interface{}{
		"Item": multiTicket,
	}
	return util.SendResp(rsp, "", "0", data)
}
