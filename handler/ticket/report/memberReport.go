package report

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	go_api "github.com/micro/go-micro/v2/api/proto"
)

type DwhMemberRechargeRecord struct {
	ID               int64           `json:"Id" gorm:"primaryKey;column:id;type:bigint;default:nextval('recharge_records_id_seq'::regclass)"`
	TopCorporationID int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint"`
	WalletRecordID   int64           `json:"WalletRecordId" gorm:"column:walletrecordid;type:smallint"`
	Code             string          `json:"Code" gorm:"column:code;type:varchar(50)"`
	AccountID        int64           `json:"AccountId" gorm:"column:accountid;type:integer"`
	Nickname         string          `json:"Nickname" gorm:"column:nickname;type:varchar(50)"`
	Sex              bool            `json:"Sex" gorm:"column:sex;type:bool;default:true"`
	Age              int64           `json:"Age" gorm:"column:age;type:smallint;default:0"`
	IdCardNo         string          `json:"IdCardNo" gorm:"column:idcardno;type:varchar(18)"`
	Address          string          `json:"Address" gorm:"column:address;type:text"`
	BirthAt          model.LocalTime `json:"BirthAt" gorm:"column:birthat;type:timestamptz;default:CURRENT_TIMESTAMP"`
	GroupTypeID      int64           `json:"GroupTypeId" gorm:"column:grouptypeid;type:integer;default:0"`
	GroupTypeName    string          `json:"GroupTypeName" gorm:"column:grouptypename;type:text"`
	GroupID          int64           `json:"GroupId" gorm:"column:groupid;type:integer;default:0"`
	GroupName        string          `json:"GroupName" gorm:"column:groupname;type:text"`
	Phone            string          `json:"Phone" gorm:"column:phone;type:varchar(11)"`
	HeadPortrait     string          `json:"HeadPortrait" gorm:"column:headportrait;type:varchar(255)"`
	AccountType      int64           `json:"AccountType" gorm:"column:accounttype;type:smallint"`
	DiscountID       int64           `json:"DiscountId" gorm:"column:discountid;type:integer"`
	DiscountName     string          `json:"DiscountName" gorm:"column:discountname;type:varchar(50)"`
	MiniappID        string          `json:"MiniappId" gorm:"column:miniappid;type:varchar(100)"`
	Money            int64           `json:"Money" gorm:"column:money;type:integer"`
	PrevBalance      int64           `json:"PrevBalance" gorm:"column:prevbalance;type:integer"`
	NowBalance       int64           `json:"NowBalance" gorm:"column:nowbalance;type:integer"`
	PayChannel       int64           `json:"PayChannel" gorm:"column:paychannel;type:smallint"`
	PayType          int64           `json:"PayType" gorm:"column:paytype;type:smallint"`
	PayStatus        int64           `json:"PayStatus" gorm:"column:paystatus;type:smallint;default:0"`
	PayAt            model.LocalTime `json:"PayAt" gorm:"column:payat;type:timestamptz;default:CURRENT_TIMESTAMP"`
	IsAdmin          bool            `json:"IsAdmin" gorm:"column:isadmin;type:bool;default:false"`
	AddedBy          int64           `json:"AddedBy" gorm:"column:addedby;type:int8"`
	Remark           string          `json:"Remark" gorm:"column:remark;type:varchar(100)"`
	CreatedAt        model.LocalTime `json:"CreatedAt" gorm:"column:createdat;type:timestamptz;default:CURRENT_TIMESTAMP"`
	UpdatedAt        model.LocalTime `json:"UpdatedAt" gorm:"column:updatedat;type:timestamptz;default:CURRENT_TIMESTAMP"`
	TraceID          string          `json:"TraceId" gorm:"column:traceid;type:varchar(50)"`
	FormAccountID    int64           `json:"FormAccountId" gorm:"column:formaccountid;type:integer;default:0"`
	FormNickname     string          `json:"FormNickname" gorm:"column:formnickname;type:varchar(50)"`
	FormIdCardNo     string          `json:"FormIdCardNo" gorm:"column:formidcardno;type:varchar(18)"`
	RechargeType     int64           `json:"RechargeType" gorm:"column:rechargetype;type:smallint;default:0"`

	StartAt model.LocalTime `json:"StartAt" gorm:"-"`
	EndAt   model.LocalTime `json:"EndAt" gorm:"-"`
	model.Paginator
}

func (r *Report) MemberChargeRecord(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DwhMemberRechargeRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	tx := model.DB().Model(&DwhMemberRechargeRecord{})
	if param.AccountType > 0 {
		tx.Where("AccountType = ?", param.AccountType)
	}

	if param.DiscountID > 0 {
		tx.Where("DiscountId = ?", param.DiscountID)
	}

	if param.IdCardNo != "" {
		tx.Where("IdCardNo LIKE ?", "%"+param.IdCardNo+"%")
	}

	if param.PayType > 0 {
		tx.Where("PayType = ?", param.PayType)
	}

	if param.Nickname != "" {
		tx.Where("Nickname LIKE ?", "%"+param.Nickname+"%")
	}

	if !param.StartAt.ToTime().IsZero() {
		tx.Where("PayAt >= ?", param.StartAt)
	}

	if !param.EndAt.ToTime().IsZero() {
		tx.Where("PayAt <= ?", param.EndAt)
	}
	var count int64
	tx.Count(&count)

	var records []DwhMemberRechargeRecord
	tx.Order("PayAt DESC").Limit(param.Limit).Offset(param.Offset).Find(&records)

	return response.Success(rsp, map[string]interface{}{"Item": records, "TotalCount": count})
}

type DwhMemberBusRecord struct {
	ID               int64           `json:"Id" gorm:"primaryKey;column:id;type:integer;default:nextval('bus_records_id_seq'::regclass)"`
	TopCorporationID int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:int8"`
	WalletRecordID   int64           `json:"WalletRecordId" gorm:"column:walletrecordid;type:integer"`
	VehicleID        int64           `json:"VehicleId" gorm:"column:vehicleid;type:integer"`
	License          string          `json:"License" gorm:"column:license;type:text;default:''"`
	DeviceID         int64           `json:"DeviceId" gorm:"column:deviceid;type:integer"`
	DeviceSN         string          `json:"DeviceSn" gorm:"column:devicesn;type:text"`
	AccountID        int64           `json:"AccountId" gorm:"column:accountid;type:integer"`
	Nickname         string          `json:"Nickname" gorm:"column:nickname;type:varchar(50);default:''"`
	Sex              bool            `json:"Sex" gorm:"column:sex;type:bool;default:true"`
	Age              int64           `json:"Age" gorm:"column:age;type:smallint;default:0"`
	IdCardNo         string          `json:"IdCardNo" gorm:"column:idcardno;type:varchar(18);default:''"`
	Address          string          `json:"Address" gorm:"column:address;type:text;default:''"`
	BirthAt          model.LocalTime `json:"BirthAt" gorm:"column:birthat;type:timestamptz;default:CURRENT_TIMESTAMP"`
	GroupTypeID      int64           `json:"GroupTypeId" gorm:"column:grouptypeid;type:integer;default:0"`
	GroupTypeName    string          `json:"GroupTypeName" gorm:"column:grouptypename;type:text;default:''"`
	GroupID          int64           `json:"GroupId" gorm:"column:groupid;type:integer;default:0"`
	GroupName        string          `json:"GroupName" gorm:"column:groupname;type:text;default:''"`
	Phone            string          `json:"Phone" gorm:"column:phone;type:varchar(11);default:''"`
	HeadPortrait     string          `json:"HeadPortrait" gorm:"column:headportrait;type:varchar(255);default:''"`
	AccountType      int64           `json:"AccountType" gorm:"column:accounttype;type:smallint"`
	DiscountID       int64           `json:"DiscountId" gorm:"column:discountid;type:integer"`
	DiscountName     string          `json:"DiscountName" gorm:"column:discountname;type:varchar(50)"`
	Money            int64           `json:"Money" gorm:"column:money;type:integer"`
	PrevBalance      int64           `json:"PrevBalance" gorm:"column:prevbalance;type:integer"`
	NowBalance       int64           `json:"NowBalance" gorm:"column:nowbalance;type:integer"`
	PayChannel       int64           `json:"PayChannel" gorm:"column:paychannel;type:smallint"`
	PayType          int64           `json:"PayType" gorm:"column:paytype;type:smallint"`
	IsVerify         bool            `json:"IsVerify" gorm:"column:isverify;type:bool;default:false"`
	Remark           string          `json:"Remark" gorm:"column:remark;type:varchar(100);default:''"`
	CreatedAt        model.LocalTime `json:"CreatedAt" gorm:"column:createdat;type:timestamptz;default:CURRENT_TIMESTAMP"`
	UpdatedAt        model.LocalTime `json:"UpdatedAt" gorm:"column:updatedat;type:timestamptz;default:CURRENT_TIMESTAMP"`
	Type             int64           `json:"Type" gorm:"column:type;type:smallint;default:0"`

	StartAt model.LocalTime `json:"StartAt" gorm:"-"`
	EndAt   model.LocalTime `json:"EndAt" gorm:"-"`
	model.Paginator
}

func (r *Report) DwhMemberBusRecord(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DwhMemberBusRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return util.SendResp(rsp, "", "OP1001", nil)
	}

	tx := model.DB().Model(&DwhMemberBusRecord{})
	if param.AccountType > 0 {
		tx.Where("AccountType = ?", param.AccountType)
	}

	if param.DiscountID > 0 {
		tx.Where("DiscountId = ?", param.DiscountID)
	}

	if param.IdCardNo != "" {
		tx.Where("IdCardNo LIKE ?", "%"+param.IdCardNo+"%")
	}

	if param.Nickname != "" {
		tx.Where("Nickname LIKE ?", "%"+param.Nickname+"%")
	}
	if param.License != "" {
		tx.Where("License LIKE ?", "%"+param.License+"%")
	}

	if !param.StartAt.ToTime().IsZero() {
		tx.Where("CreatedAt >= ?", param.StartAt)
	}

	if !param.EndAt.ToTime().IsZero() {
		tx.Where("CreatedAt <= ?", param.EndAt)
	}
	var count int64
	tx.Count(&count)

	var records []DwhMemberBusRecord
	tx.Order("CreatedAt DESC").Limit(param.Limit).Offset(param.Offset).Find(&records)

	return response.Success(rsp, map[string]interface{}{"Item": records, "TotalCount": count})
}
