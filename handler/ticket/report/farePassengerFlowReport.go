package report

import (
	"app/org/scs/erpv2/api/database"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model/ticket"
	protoLine "app/org/scs/erpv2/api/proto/rpc/oetline"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	go_api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type FarePassengerFlowReportHandler struct {
	//CorporationIds []int64  `json:"CorporationIds"`
	LineIds     []int64  `json:"LineIds"`
	ReportMonth string   `json:"ReportMonth"` // 计算月份 YYYYMM
	TimeRange   []string `json:"TimeRange"`   // 时间范围 YYYYMMDD,YYYYMMDD
}

func (m *FarePassengerFlowReportHandler) List(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var form FarePassengerFlowReportHandler
	code := response.BindFormWithoutValidator(req, &form)
	if code != "" {
		return response.Error(rsp, code)
	}
	if form.LineIds == nil || len(form.LineIds) == 0 {
		return response.Error(rsp, "请选择路线")
	}
	//if form.ReportMonth == "" {
	//	return response.Error(rsp, "请选择月份")
	//}
	if form.TimeRange == nil || len(form.TimeRange) != 2 {
		return response.Error(rsp, "请选择时间范围")
	}

	list, err := (&ticket.FarePassengerFlowReport{}).List(form.LineIds, form.TimeRange[0], form.TimeRange[1])
	if err != nil {
		log.ErrorFields("FarePassengerFlowReport list error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, list)
}

func (m *FarePassengerFlowReportHandler) Calculate(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var form FarePassengerFlowReportHandler
	code := response.BindFormWithoutValidator(req, &form)
	if code != "" {
		return response.Error(rsp, code)
	}
	if ticket.FarePassengerFlowReportCalculateFlag {
		return response.Error(rsp, "数据计算中")
	}

	if form.LineIds == nil || len(form.LineIds) == 0 {
		return response.Error(rsp, "请选择路线")
	}
	if form.ReportMonth == "" {
		return response.Error(rsp, "请选择月份")
	}

	now, _ := time.ParseInLocation("200601", form.ReportMonth, time.Local)
	start := time.Date(now.Year(), now.Month()-1, 26, 0, 0, 0, 0, time.Local).Format("20060102")
	end := time.Date(now.Year(), now.Month(), 25, 0, 0, 0, 0, time.Local).Format("20060102")

	var oetLines []*protoLine.OetLineItem
	for _, lineId := range form.LineIds {
		line, _ := rpc.GetLineWithId(ctx, lineId)
		if line != nil {
			oetLines = append(oetLines, line)
		}
	}
	if oetLines == nil || len(oetLines) == 0 {
		return response.Error(rsp, "没有查询到路线")
	}
	db, err := database.InitIcMysqlConnect()
	if err != nil {
		log.ErrorFields(" database.InitIcMysqlConnect error", map[string]interface{}{"error": err})
		//return response.Error(rsp, "数据库连接失败")
	}
	go service.CalculateFarePassengerFlowReport(db, oetLines, form.LineIds, start, end, form.ReportMonth)
	return response.Success(rsp, "数据计算中")
}
