package ticket

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	ticketModel "app/org/scs/erpv2/api/model/ticket"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"github.com/micro/go-micro/v2/api/proto"
	"github.com/micro/go-micro/v2/client"
	"time"
)

type TicketDataPermission struct {
	client.Client
}

type DataPermission struct {
	ticketModel.TicketDataPermissionRecord
	CreatedAtStartAt model.LocalTime
	CreatedAtEndAt   model.LocalTime
	model.Paginator
}

func (tdp *TicketDataPermission) Add(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DataPermission
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.Error("GetTopCorporationId == 0")
		return response.Error(rsp, response.Forbidden)
	}

	s := time.Time(param.TicketDataPermissionRecord.StartAt)
	e := time.Time(param.TicketDataPermissionRecord.EndAt)
	now := time.Now()

	if s.IsZero() || e.IsZero() {
		log.ErrorFields("param missing", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsMissing)
	}

	//param.TicketDataPermissionRecord.Id = model.Id()
	param.TicketDataPermissionRecord.GroupId = topCorporationId

	param.TicketDataPermissionRecord.IsCancel = util.StatusForFalse

	// 判断状态
	// 开始时间 <= 当前时间 <= 结束时间  ===>  进行中

	if now.Sub(s) >= 0 && e.Sub(now) >= 0 {
		param.TicketDataPermissionRecord.Status = ticketModel.DOING_TDPS_1
	} else {
		param.TicketDataPermissionRecord.Status = ticketModel.DONE_TDPS_2
	}
	param.TicketDataPermissionRecord.ParseOpUser(ctx)
	err = param.TicketDataPermissionRecord.Create()
	if err != nil {
		log.ErrorFields("TicketDataPermission Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func (tdp *TicketDataPermission) List(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DataPermission
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	ticketDataPermissions, totalCount, err := param.TicketDataPermissionRecord.GetBy(param.Status, time.Time(param.CreatedAtStartAt), time.Time(param.CreatedAtEndAt), param.Paginator)
	if err != nil {
		log.ErrorFields("TicketDataPermission GetBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	data := map[string]interface{}{
		"Items":      ticketDataPermissions,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)
}

func (tdp *TicketDataPermission) Cancel(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DataPermission
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.TicketDataPermissionRecord.Id == 0 {
		log.ErrorFields("param TicketDataPermissionRecord.Id == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsMissing)
	}

	err = param.TicketDataPermissionRecord.UpdateIsCancelTrue(param.TicketDataPermissionRecord.Id)
	if err != nil {
		log.ErrorFields("TicketDataPermission UpdateIsCancel error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)
}
