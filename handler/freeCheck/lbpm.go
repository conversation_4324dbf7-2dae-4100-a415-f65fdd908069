package freeCheck

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	messageModel "app/org/scs/erpv2/api/model/message"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"strconv"
	"time"
)

type LbpmProcessParam struct {
	TemplateFormId string `json:"TemplateFormId"`
	ProcessId      string `json:"ProcessId"`
	FormInstanceId int64  `json:"FormInstanceId"`
	LbpmParam      string `json:"LbpmParam"`
	ItemId         int64  `json:"ItemId"`
	ActionType     int64  `json:"ActionType"`
}

func (fc *FreeCheck) LbpmProcessParam(ctx context.Context, req *api.Request, rsp *api.Response) error {
	if !config.Config.Lbpm.Enable {
		return response.Success(rsp, map[string]interface{}{"FormId": "", "ProcessId": "", "IsOptionAuth": false, "Token": "", "IsEnableLbpm": false})
	}

	//获取员工
	user := auth.User(ctx).GetUser()
	if user == nil || user.Id == 0 {
		log.ErrorFields("auth.GetUser is nil", map[string]interface{}{"userId": auth.User(ctx).GetUserId()})
		return response.Error(rsp, response.FAIL)
	}

	var param LbpmProcessParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.TemplateFormId == "" {
		return response.Error(rsp, response.ParamsMissing)
	}

	var formId lbpmApi.FormId
	var process processModel.LbpmApplyProcess
	var isOptionAuth bool
	var processAuth processService.ProcessOperationAuth
	if param.ItemId == 0 {
		formId, process, err = InitProcessDraft(user, param)
		if err != nil {
			return response.Error(rsp, response.FAIL)
		}
		isOptionAuth = true
	} else {
		formId, process, err = GetProcessInstance(param)
		if err != nil {
			return response.Error(rsp, response.FAIL)
		}

		//判断当前登陆人是不是流程当前节点的处理人
		isOptionAuth = (&processModel.LbpmApplyProcessHasHandler{}).IsProcessHandler(user.Id, process.FormInstanceId, util.ProcessNodeTypeForApprove, util.ProcessNodeHandleStatusForDoing)

		//当前登录用户对流程的所有操作权限
		processAuth = processService.LbpmProcessOperationAuth(process, user.Id)
	}

	//登录token
	//var account = fmt.Sprintf("%s:%v", user.Phone, time.Now().UnixNano()/1e6)
	//token := aes.ECBEncrypt([]byte(account), []byte(config.LbpmAESKey))
	token, err := processService.GetSSOSessionId(user.Phone)
	if err != nil {
		log.ErrorFields("GetSSOSessionId error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, map[string]interface{}{
		"FormId":       formId,
		"ProcessId":    process.ProcessId,
		"IsOptionAuth": isOptionAuth,
		"IsEnableLbpm": true,
		"ProcessAuth":  processAuth,
		"Token":        token,
	})
}

// GetProcessInstance 获取流程实例
func GetProcessInstance(param LbpmProcessParam) (lbpmApi.FormId, processModel.LbpmApplyProcess, error) {
	var process processModel.LbpmApplyProcess

	if param.ProcessId != "" {
		err := process.GetApproveProcessByProcessId(param.TemplateFormId, param.ProcessId, param.ItemId, []int64{})
		if err != nil {
			log.ErrorFields("process.GetApproveProcessByProcessId error", map[string]interface{}{"err": err})
			return lbpmApi.FormId{}, process, errors.New("process.GetApproveProcessByProcessId error")
		}
	} else {
		err := process.GetApproveProcess(param.TemplateFormId, param.ItemId, []int64{})
		if err != nil {
			log.ErrorFields("process.GetApproveProcess error", map[string]interface{}{"err": err})
			return lbpmApi.FormId{}, process, errors.New("process.GetApproveProcess error")
		}
	}

	formId := lbpmApi.FormId{
		SysId:          process.SysId,
		ModelId:        process.ModelId,
		TemplateFormId: process.TemplateFormId,
		FormInstanceId: fmt.Sprintf("%v", process.FormInstanceId),
	}

	return formId, process, nil

}

// InitProcessDraft 初始化流程实例
func InitProcessDraft(user *auth.AuthUser, param LbpmProcessParam) (lbpmApi.FormId, processModel.LbpmApplyProcess, error) {
	var template map[string]string
	for i := range config.LbpmFormTemplates {
		if config.LbpmFormTemplates[i]["TemplateFormId"] == param.TemplateFormId {
			template = config.LbpmFormTemplates[i]
		}
	}

	if template == nil || len(template) == 0 {
		return lbpmApi.FormId{}, processModel.LbpmApplyProcess{}, errors.New("template not found")
	}
	formInstanceId := util.GenerateId()
	formId := lbpmApi.FormId{
		SysId:          config.Config.Lbpm.SysId,
		ModelId:        template["ModelId"],
		TemplateFormId: template["TemplateFormId"],
		FormInstanceId: fmt.Sprintf("%v", formInstanceId),
	}

	//获取流程ID
	processId, err := lbpmApi.CallLbpmCreateProcessService(formId, lbpmApi.Creator{LoginName: user.Phone}, lbpmApi.ExParam{DocSubject: user.Name + "的" + template["TemplateFormName"]})

	if err != nil {
		log.ErrorFields("lbpmService.CallLbpmCreateProcessService error", map[string]interface{}{"err": err})
		return lbpmApi.FormId{}, processModel.LbpmApplyProcess{}, errors.New("lbpmService.CallLbpmCreateProcessService error")
	}

	//初始化流程草稿  存入流程审批表
	var process = processModel.LbpmApplyProcess{
		FormInstanceId:   formInstanceId,
		TopCorporationId: user.GetTopCorporationId(),
		ApplyUserId:      user.Id,
		ApplyUserName:    user.Name,
		ApplyUserMobile:  user.Phone,
		ProcessId:        processId,
		SysId:            config.Config.Lbpm.SysId,
		ModelId:          formId.ModelId,
		ModelName:        template["ModelName"],
		TemplateFormId:   formId.TemplateFormId,
		TemplateFormName: template["TemplateFormName"],
		Title:            template["TemplateFormName"],
		Status:           util.ProcessStatusForDraft,
	}

	err = process.Create()
	if err != nil {
		log.ErrorFields("process.Create error", map[string]interface{}{"err": err})
		return lbpmApi.FormId{}, processModel.LbpmApplyProcess{}, errors.New("process.Create error")
	}

	if err != nil {
		log.ErrorFields("lbpmService.CallLbpmCreateProcessService error", map[string]interface{}{"err": err})
		return lbpmApi.FormId{}, processModel.LbpmApplyProcess{}, errors.New("lbpmService.CallLbpmCreateProcessService error")
	}

	fmt.Printf("formId=====%+v, processId === %+v \r\n", formId, processId)

	return formId, process, nil
}

type IframeParam struct {
	OperationType string           `json:"operationType"`
	Param         IframeInnerParam `json:"param"`
}
type IframeInnerParam struct {
	JumpToNodeId string `json:"jumpToNodeId"`
}

// LbpmApproveProcess 流程审批
func (fc *FreeCheck) LbpmApproveProcess(ctx context.Context, req *api.Request, rsp *api.Response) error {
	user := auth.User(ctx).GetUser()
	if user == nil || user.Id == 0 {
		log.ErrorFields("auth.User(ctx).GetUser() is nil", map[string]interface{}{"userId": auth.User(ctx).GetUserId()})
		return response.Error(rsp, response.FAIL)
	}

	var param LbpmProcessParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.ProcessId == "" {
		log.ErrorFields("param.ProcessId is required", nil)
		return response.Error(rsp, response.ParamsMissing)
	}

	var status = []int64{util.ProcessStatusForDoing}
	if param.ActionType == util.ProcessActionTypeForAbandon {
		status = append(status, util.ProcessStatusForTerminate, util.ProcessStatusForRefuse)
	}

	var process processModel.LbpmApplyProcess
	err = process.GetProcess(param.ProcessId, status)
	if err != nil || process.FormInstanceId == 0 || process.ItemId != param.ItemId {
		log.ErrorFields("process.GetProcess error:doing process", map[string]interface{}{"err": err, "processId": param.ProcessId})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//起草人废弃、撤回流程
	if param.ActionType == util.ProcessActionTypeForAbandon || param.ActionType == util.ProcessActionTypeForTerminate {
		if process.ApplyUserId != user.Id {
			log.ErrorFields("process.ApplyUserId != user.Id", nil)
			return response.Error(rsp, response.Forbidden)
		}
		param.LbpmParam = buildCreatorTerminateAndAbandonLbpmParam(param.ProcessId, param.ActionType)
	}

	if param.LbpmParam == "" {
		log.ErrorFields("param.LbpmParam is required", nil)
		return response.Error(rsp, response.ParamsMissing)
	}
	//获取流程当前处理人
	approvingHandlers := (&processModel.LbpmApplyProcessHasHandler{}).GetProcessHandlers(process.FormInstanceId)

	err = processService.HandleProcess(process, processService.HandlerUser{
		Id:     user.Id,
		Name:   user.Name,
		Mobile: user.Phone,
	}, param.LbpmParam)

	if err != nil {
		log.ErrorFields("processService.HandleProcess error", map[string]interface{}{"err": err, "processId": param.ProcessId})
		return response.Error(rsp, response.FAIL)
	}

	//起草人废弃
	if param.ActionType == util.ProcessActionTypeForAbandon {
		creatorAbandonProcess(process)
		// 流程超时预警告警
		go service.CheckProcessTimeOutAlarm(approvingHandlers)
		return response.Success(rsp, nil)
	}

	//起草人撤回
	if param.ActionType == util.ProcessActionTypeForTerminate {
		creatorTerminateProcess(process)
		// 流程超时预警告警
		go service.CheckProcessTimeOutAlarm(approvingHandlers)
		go service.RemoveProcessCurrentHandler(process.FormInstanceId)
		return response.Success(rsp, nil)
	}

	var iframeParam IframeParam
	_ = json.Unmarshal([]byte(param.LbpmParam), &iframeParam)

	//如果当前审批结果是驳回，并且是驳回到起草节点，则需更新流程状态为驳回，更新业务数据状态为驳回
	if iframeParam.OperationType == util.ProcessNodeHandleResultForRefuse && iframeParam.Param.JumpToNodeId == util.ProcessNodeNameForCreate {
		handlerRefuseProcess(process)
	}

	now := model.LocalTime(time.Now())
	//查询待处理的记录
	var processHandler processModel.LbpmApplyProcessHasHandler
	err = processHandler.ProcessHandlerForUser(user.Id, process.FormInstanceId)
	if err != nil {
		//存入处理人到审批表
		processHandler = processModel.LbpmApplyProcessHasHandler{
			FormInstanceId: process.FormInstanceId,
			ProcessId:      process.ProcessId,
			NodeType:       util.ProcessNodeTypeForApprove,
			UserId:         user.Id,
			UserName:       user.Name,
			UserMobile:     user.Phone,
			ProcessParam:   []byte(param.LbpmParam),
			Status:         util.ProcessNodeHandleStatusForDone,
			Result:         iframeParam.OperationType,
			StartAt:        &now,
			EndAt:          &now,
			ClientType:     auth.User(ctx).GetClientType(),
		}
		_ = processHandler.Create()
	} else {
		//更新审批人处理状态
		processHandler.ProcessParam = []byte(param.LbpmParam)
		processHandler.Status = util.ProcessNodeHandleStatusForDone
		processHandler.Result = iframeParam.OperationType
		processHandler.EndAt = &now
		processHandler.ClientType = auth.User(ctx).GetClientType()

		err := processHandler.Update()
		if err != nil {
			log.ErrorFields("LbpmApproveProcess.processHandler.Update error", map[string]interface{}{"err": err, "processHandler": processHandler})
		}
		//如果当前节点是并行审批，则需要更新其他审批人的审批状态
		if processHandler.ApprovalType == util.ProcessNodeApprovalTypeForConcurrence {
			err := processHandler.ConcurrenceUpdate()
			if err != nil {
				log.ErrorFields("LbpmApproveProcess.processHandler.ConcurrenceUpdate error", map[string]interface{}{"err": err, "processHandler": processHandler})
			}
		}
	}

	//更新流程消息状态
	_ = (&messageModel.Message{}).UpdateProcessMessageStatus(process.FormInstanceId, process.TableName(), user.Id)

	//并行审批，其他审批人的消息要改为已处理
	if processHandler.ApprovalType == util.ProcessNodeApprovalTypeForConcurrence {
		concurrenceHandlers := processHandler.GetConcurrenceHandler()
		for i := range concurrenceHandlers {
			_ = (&messageModel.Message{}).UpdateProcessMessageStatus(process.FormInstanceId, process.TableName(), concurrenceHandlers[i].UserId)
		}
	}

	// 流程超时预警告警
	go service.CheckProcessTimeOutAlarm(approvingHandlers)

	return response.Success(rsp, nil)
}

// LbpmApprovingProcess 正在审批的流程
func (fc *FreeCheck) LbpmApprovingProcess(ctx context.Context, req *api.Request, rsp *api.Response) error {
	user := auth.User(ctx).GetUser()
	if user == nil || user.Id == 0 {
		return response.Success(rsp, nil)
	}

	processes, err := processService.GetApprovingList(processService.HandlerUser{Mobile: user.Phone}, "")

	if err != nil {
		log.ErrorFields("processService.GetApprovingList error", map[string]interface{}{"err": err})
		return response.Success(rsp, nil)
	}

	var formInstanceIds []int64
	for i := range processes {
		formInstanceId, _ := strconv.ParseInt(processes[i].FormInstanceId, 10, 64)
		formInstanceIds = append(formInstanceIds, formInstanceId)
	}

	var process processModel.LbpmApplyProcess
	records := process.GetApprovingProcess(formInstanceIds, []int64{util.ProcessStatusForDoing})

	return response.Success(rsp, map[string]interface{}{"Items": records, "TotalCount": len(records)})
}

// LbpmProcessFormValue 提交流程时的表单信息
func (fc *FreeCheck) LbpmProcessFormValue(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LbpmProcessParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.ItemId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	processes := (&processModel.LbpmApplyProcess{}).List(param.ItemId, "", param.TemplateFormId, []int64{util.ApplyStatusForDoing}, model.Paginator{Limit: 1})

	if len(processes) == 0 {
		log.ErrorFields("LbpmApplyProcess.List not fund", nil)
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, processes[0].Param)
}

// LbpmProcessInstance 流程实例信息
func (fc *FreeCheck) LbpmProcessInstance(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LbpmProcessParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.FormInstanceId == 0 || param.ProcessId == "" {
		return response.Error(rsp, response.ParamsMissing)
	}

	var process processModel.LbpmApplyProcess
	err = process.FindBy(param.FormInstanceId)

	if err != nil || process.ProcessId != param.ProcessId {
		log.ErrorFields("process.FindBy is error", map[string]interface{}{"err": err, "process": process})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	return response.Success(rsp, map[string]interface{}{
		"ItemId":         process.ItemId,
		"TemplateFormId": process.TemplateFormId,
		"FormInstanceId": process.FormInstanceId,
		"Param":          process.Param,
	})
}

// 流程废弃和撤回时构造参数
func buildCreatorTerminateAndAbandonLbpmParam(processId string, actionType int64) string {

	buildParam := map[string]interface{}{
		"taskId":       "",
		"processId":    processId,
		"activityType": "reviewNode",
	}

	var byteParam []byte

	//起草人撤回流程
	if actionType == util.ProcessActionTypeForTerminate {
		buildParam["operationType"] = "drafter_return" // 撤回操作（固定值）
		buildParam["param"] = map[string]interface{}{
			"operationName": "撤回",
			"notifyType":    "todo",
			"auditNote":     "",
		}
		byteParam, _ = json.Marshal(buildParam)
	}
	//起草人废弃流程
	if actionType == util.ProcessActionTypeForAbandon {
		buildParam["operationType"] = "drafter_abandon" // 废弃操作（固定值）
		buildParam["param"] = map[string]interface{}{
			"operationName": "废弃",
			"notifyType":    "todo",
			"auditNote":     "",
		}
		byteParam, _ = json.Marshal(buildParam)
	}

	return string(byteParam)
}

// 起草人撤回流程
func creatorTerminateProcess(process processModel.LbpmApplyProcess) {
	//更新流程为撤回状态
	_ = process.UpdateColumn("status", util.ProcessStatusForTerminate)

	//更新业务数据状态为撤回
	model.DB().Table(process.ItemTableName).Where("Id = ?", process.ItemId).Update(process.ItemTableStatusField, util.ApplyStatusForTerminate)
	//车辆调动流程状态发生改变通知提醒
	if process.TemplateFormId == config.VehicleMigrationApplyFormTemplate {
		go service.SendVehicleMigrationProcessStatusChangeMsg(process.ItemId)
	}
	//司机调动流程状态发生改变通知提醒
	if process.TemplateFormId == config.DriverMigrationApplyFormTemplate {
		go service.SendDriverMigrationProcessStatusChangeMsg(process.ItemId)
	}
	//更新此流程已发消息但还未审批的人的审批状态为取消
	_ = (&processModel.LbpmApplyProcessHasHandler{}).TerminateOrAbandonUpdate(process.FormInstanceId)

	//更新当前流程所对应的未处理的消息状态为已处理
	_ = (&messageModel.Message{}).ProcessTerminatedOrAbandonedUpdateMessageStatus(process.FormInstanceId, process.TableName())
}

// 起草人废弃流程
func creatorAbandonProcess(process processModel.LbpmApplyProcess) {
	//更新流程为废弃状态
	_ = process.UpdateStatus(util.ProcessStatusForAbandon)

	//更新业务数据状态为废弃
	model.DB().Table(process.ItemTableName).Where("Id = ?", process.ItemId).Update(process.ItemTableStatusField, util.ApplyStatusForAbandon)

	//车辆调动流程状态发生改变通知提醒
	if process.TemplateFormId == config.VehicleMigrationApplyFormTemplate {
		go service.SendVehicleMigrationProcessStatusChangeMsg(process.ItemId)
	}
	//司机调动流程状态发生改变通知提醒
	if process.TemplateFormId == config.DriverMigrationApplyFormTemplate {
		go service.SendDriverMigrationProcessStatusChangeMsg(process.ItemId)
	}

	//更新此流程已发消息但还未审批的人的审批状态为取消
	_ = (&processModel.LbpmApplyProcessHasHandler{}).TerminateOrAbandonUpdate(process.FormInstanceId)

	//更新当前流程所对应的未处理的消息状态为已处理
	_ = (&messageModel.Message{}).ProcessTerminatedOrAbandonedUpdateMessageStatus(process.FormInstanceId, process.TableName())
}

// 处理人驳回流程到起草节点
func handlerRefuseProcess(process processModel.LbpmApplyProcess) {
	//更新流程为拒绝状态
	_ = process.UpdateColumn("status", util.ProcessStatusForRefuse)

	//更新业务数据状态为拒绝
	model.DB().Table(process.ItemTableName).Where("Id = ?", process.ItemId).Update(process.ItemTableStatusField, util.ApplyStatusForReject)

	//车辆调动流程状态发生改变通知提醒
	if process.TemplateFormId == config.VehicleMigrationApplyFormTemplate {
		go service.SendVehicleMigrationProcessStatusChangeMsg(process.ItemId)
	}

	//司机调动流程状态发生改变通知提醒
	if process.TemplateFormId == config.DriverMigrationApplyFormTemplate {
		go service.SendDriverMigrationProcessStatusChangeMsg(process.ItemId)
	}
}

type LbpmCashierFormParam struct {
	safety.CashierPayMoneyParam
	ProcessId string `json:"ProcessId"`
	ItemId    int64  `json:"ItemId"`
}

func (fc *FreeCheck) LbpmCashierUpdateForm(ctx context.Context, req *api.Request, rsp *api.Response) error {
	user := auth.User(ctx).GetUser()
	if user == nil || user.Id == 0 {
		log.ErrorFields("auth.User(ctx).GetUser() is nil", map[string]interface{}{"userId": auth.User(ctx).GetUserId()})
		return response.Error(rsp, response.FAIL)
	}

	var param LbpmCashierFormParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.IsMustPay == util.StatusForTrue && (time.Time(param.PayMoneyAt).IsZero() || len(param.PayMoneyFile) == 0) {
		return response.Error(rsp, response.ParamsMissing)
	}

	var process processModel.LbpmApplyProcess
	err = process.GetProcess(param.ProcessId, []int64{util.ProcessStatusForDoing})
	if err != nil || process.ItemId != param.ItemId {
		log.ErrorFields("process.GetProcess not found || process.ItemId != param.ItemId", map[string]interface{}{"err": err, "process.ItemId": process.ItemId})
		return response.Error(rsp, response.FAIL)
	}
	var formStep int64
	model.DB().Table(process.ItemTableName).Select("formStep").Where("Id = ?", param.ItemId).Scan(&formStep)
	if formStep != util.ProcessFormStepForCashier {
		log.ErrorFields("formStep != 100", map[string]interface{}{"formStep": formStep})
		return response.Error(rsp, response.Forbidden)
	}
	param.FormStep = formStep + 1
	err = model.DB().Table(process.ItemTableName).Select("IsMustPay", "PayMoneyAt", "PayMoneyFile", "FormStep").Where("Id = ?", param.ItemId).Updates(&param).Error
	if err != nil {
		log.ErrorFields("table update fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, nil)
}
