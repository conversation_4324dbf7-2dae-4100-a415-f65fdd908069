package freeCheck

import (
	"app/org/scs/erpv2/api/handler/base"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	protoschedule "app/org/scs/erpv2/api/proto/rpc/iss"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

func (fc *FreeCheck) LineList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return base.LineList(ctx, req, rsp)
}

type LinePlanClassRequest struct {
	Lines []LinePlanClassRequestParam
}

type LinePlanClassRequestParam struct {
	LineId        int64 `json:"LineId"`
	CorporationId int64 `json:"CorporationId"`
}

type LinePlanClassItem struct {
	LineId            int64 `json:"LineId"`
	CorporationId     int64 `json:"CorporationId"`
	PlanClassIdxCount int64 `json:"PlanClassIdxCount"`
}

func (fc *FreeCheck) LinePlanClassCount(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LinePlanClassRequest

	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var request []*protoschedule.LineCorpItem

	for i := range param.Lines {
		request = append(request, &protoschedule.LineCorpItem{
			CorporationId: param.Lines[i].CorporationId,
			LineId:        param.Lines[i].LineId,
		})
	}

	linePlanClass := rpc.GetLineCardCount(ctx, request)

	var results []LinePlanClassItem
	for _, item := range linePlanClass {
		results = append(results, LinePlanClassItem{
			LineId:            item.LineId,
			CorporationId:     item.CorporationId,
			PlanClassIdxCount: item.ClassIdx,
		})
	}

	return response.Success(rsp, results)
}

type LineListParam struct {
	LineIds        []int64         `json:"LineIds"`
	Name           string          `json:"Name"`
	StartAt        model.LocalTime `json:"StartAt"`
	EndAt          model.LocalTime `json:"EndAt"`
	CorporationIds []int64         `json:"CorporationIds"`
	CorporationId  int64           `json:"CorporationId"`
}
type AllLineListItem struct {
	Id              int64  `json:"Id"`
	Name            string `json:"Name"`
	CorporationId   int64  `json:"CorporationId"`
	CorporationName string `json:"CorporationName"`
	LineAttr        int64  `json:"LineAttr"`       //线路属性，1-常规公交；2-定制公交；3-公务车；4-校车；5-旅游专线; 6-村村通
	RunStatus       int64  `json:"RunStatus"`      //营运状态，0-停运；1-营运
	MigrationState  int64  `json:"MigrationState"` //0-迁入，1-迁出
}

// AllLineList 获取当前用户下所有线路，包含历史存在的线路
func (fc *FreeCheck) AllLineList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineListParam

	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	authUser := auth.User(ctx).GetUser()
	endAt := param.EndAt.ToTime().AddDate(0, 0, 1)

	//获取用户有权限看到的所有线路 包括历史
	lines := rpc.GetHistoricalLinesWithUserId(ctx, authUser.Id, param.Name, param.StartAt.ToTime(), endAt)

	var results []AllLineListItem
	for _, line := range lines {
		if len(param.CorporationIds) > 0 {
			if util.IncludeInt64(param.CorporationIds, line.SubCorporationId) {
				results = append(results, AllLineListItem{
					Id:              line.Id,
					Name:            line.Name,
					CorporationId:   line.SubCorporationId,
					CorporationName: line.SubCorporation,
					LineAttr:        line.LineAttribute,
					RunStatus:       line.RunStatus,
					MigrationState:  line.MigrationState,
				})
			}
			continue
		}

		results = append(results, AllLineListItem{
			Id:              line.Id,
			Name:            line.Name,
			CorporationId:   line.SubCorporationId,
			CorporationName: line.SubCorporation,
			LineAttr:        line.LineAttribute,
			RunStatus:       line.RunStatus,
			MigrationState:  line.MigrationState,
		})
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(results),
	})
}

// LineHasAllCorp 线路归属的机构，包含历史
func (fc *FreeCheck) LineHasAllCorp(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineListParam

	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	endAt := param.EndAt.ToTime().AddDate(0, 0, 1)
	corps := rpc.GetHistoricalCorpsWithLineId(ctx, param.LineIds, param.StartAt.ToTime(), endAt)

	return response.Success(rsp, map[string]interface{}{
		"Items":      corps,
		"TotalCount": len(corps),
	})
}

type LineItem struct {
	Id        int64  `json:"Id"`
	Name      string `json:"Name"`
	LineAttr  int64  `json:"LineAttr"`  //线路属性，1-常规公交；2-定制公交；3-公务车；4-校车；5-旅游专线; 6-村村通
	RunStatus int64  `json:"RunStatus"` //营运状态，0-停运；1-营运
}

func (fc *FreeCheck) CorporationLineList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineListParam

	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//获取用户有权限看到的所有线路 包括历史
	lines := rpc.GetLinesWithCorporationId(ctx, param.CorporationId)

	var results []LineItem
	for _, line := range lines {
		results = append(results, LineItem{
			Id:        line.Id,
			Name:      line.Name,
			LineAttr:  line.LineAttribute,
			RunStatus: line.RunStatus,
		})
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(results),
	})
}

type LinePlanListItem struct {
	Id            int64  `json:"Id"`
	Name          string `json:"Name"`
	LineId        int64  `json:"LineId"`
	CorporationId int64  `json:"CorporationId"`
	IsDefault     int    `json:"IsDefault"`  //是否默认 1是 2否
	IsArchived    int    `json:"IsArchived"` //是否归档 1是 2否
}

// LinePlanList 线路计划列表
func (fc *FreeCheck) LinePlanList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LinePlanClassRequestParam

	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	plans := rpc.GetLineTaskWithLineId(ctx, param.CorporationId, param.LineId)

	var results []LinePlanListItem
	for _, plan := range plans {
		//归档的行车计划剔除
		if plan.IsArchived == util.StatusForTrue {
			continue
		}
		var isDefault = util.StatusForFalse
		if plan.IsDefault == util.StatusForTrue {
			isDefault = util.StatusForTrue
		}
		var isArchived = util.StatusForFalse
		if plan.IsArchived == util.StatusForTrue {
			isArchived = util.StatusForTrue
		}

		results = append(results, LinePlanListItem{
			Id:            plan.Id,
			Name:          plan.Name,
			CorporationId: plan.CorporationId,
			LineId:        plan.LineId,
			IsDefault:     isDefault,
			IsArchived:    isArchived,
		})
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(results),
	})
}

type ShowLinePlanRequest struct {
	Id int64 `json:"Id"`
}
type LinePlanDetailItem struct {
	Id             int64  `json:"Id"`
	LineName       string `json:"LineName"`
	LineId         int64  `json:"LineId"`
	Index          int64  `json:"Index"`
	ServiceType    int64  `json:"ServiceType"`
	TripType       int64  `json:"TripType"`
	PlanDepartStop string `json:"PlanDepartStop"`
	PlanArriveStop string `json:"PlanArriveStop"`
	PlanDepartAt   int64  `json:"PlanDepartAt"`
	PlanArriveAt   int64  `json:"PlanArriveAt"`
}

// ShowLinePlan 线路计划详情
func (fc *FreeCheck) ShowLinePlan(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ShowLinePlanRequest

	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	plans := rpc.GetLineTaskDetail(ctx, param.Id)

	var results []LinePlanDetailItem
	for _, plan := range plans {
		results = append(results, LinePlanDetailItem{
			Id:             plan.Id,
			LineName:       plan.Line,
			LineId:         plan.LineId,
			Index:          plan.ClassIdx,
			ServiceType:    plan.ServiceType,
			TripType:       plan.TripType,
			PlanDepartStop: plan.PlanDepartStop,
			PlanArriveStop: plan.PlanArriveStop,
			PlanDepartAt:   plan.PlanDepartTime,
			PlanArriveAt:   plan.PlanArriveTime,
		})
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(results),
	})
}
