package freeCheck

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model/workOrder"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	go_api "github.com/micro/go-micro/v2/api/proto"
)

func (fc *FreeCheck) MiniWorkOrderCreate(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param workOrder.MiniWorkOrder
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.Code = util.GenerateIdentifier()
	err = param.Create()
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}
