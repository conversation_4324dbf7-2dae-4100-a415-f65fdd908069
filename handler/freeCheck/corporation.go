package freeCheck

import (
	"app/org/scs/erpv2/api/log"
	oet_scs_srv_app "app/org/scs/erpv2/api/proto/rpc/oetline"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"strconv"
)

type ListRequestParams struct {
	AuthId      string `validate:"required"` // *用户唯一标识
	ParentId    int64
	ParentIdStr string // 兼容小程序无法使用大整数
	Order       string `validate:"required,oneof=desc asc"`
	IsAuth      int64  `json:"IsAuth"`
}

type ListResponse struct {
	Id          int64  `json:"Id"`          // 机构id
	IdStr       string `json:"IdStr"`       // 机构id string
	Name        string `json:"Name"`        // 机构名
	ParentId    int64  `json:"ParentId"`    // 父级机构id
	ParentIdStr string `json:"ParentIdStr"` // 父级机构id string
	Type        int64  `json:"Type"`        //类型
}

func (fc *FreeCheck) CorporationList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q ListRequestParams
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsMissing)
	}
	if q.ParentIdStr != "" {
		parseInt, err := strconv.ParseInt(q.ParentIdStr, 10, 64)
		if err != nil {
			log.Error("strconv.ParseInt err=", err.Error())
			return response.Error(rsp, response.ParamsInvalid)
		}

		q.ParentId = parseInt
	}

	if q.ParentId == 0 {
		q.ParentId = auth.User(ctx).GetTopCorporationId()
	}

	treeItems, treeTotalCount, err := rpc.CorporationTree(ctx, q.ParentId, q.Order)

	if err != nil || treeItems == nil {
		return response.Success(rsp, nil)
	}

	var rspD []ListResponse
	var corporationMap = make(map[int64]ListResponse)
	for _, item := range treeItems {
		var listItem = ListResponse{
			Id:          item.Id,
			IdStr:       strconv.FormatInt(item.Id, 10),
			Name:        item.Name,
			ParentId:    item.ParentId,
			ParentIdStr: strconv.FormatInt(item.ParentId, 10),
			Type:        item.Type,
		}
		rspD = append(rspD, listItem)
		corporationMap[listItem.Id] = listItem
	}

	if q.IsAuth == util.StatusForTrue {
		authCorporationIds := service.AuthCorporationIdProvider(ctx, []int64{})
		if len(authCorporationIds) == 0 {
			rspD = []ListResponse{}
		} else {
			rspD = []ListResponse{}
			for i := range authCorporationIds {
				LoopFindCorporation(authCorporationIds[i], corporationMap, &rspD)
			}
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      rspD,
		"TotalCount": treeTotalCount,
	})
}

func LoopFindCorporation(curCorporationId int64, allCorporations map[int64]ListResponse, results *[]ListResponse) {
	if listItem, ok := allCorporations[curCorporationId]; ok {
		delete(allCorporations, curCorporationId)
		*results = append(*results, listItem)
		if listItem.ParentId > 0 {
			LoopFindCorporation(listItem.ParentId, allCorporations, results)
		}
	}
}

type CorporationListTreeReq struct {
	CorporationId int64 // 机构 Id
}
type CorporationListTreeRsp struct {
	Id       int64 // 机构 Id
	ParentId int64
	Type     int64
	Name     string // 机构名
	Drivers  []DriverInfo
}
type DriverInfo struct {
	Id            int64  // 司机id
	CorporationId int64  // 机构 Id
	Name          string // 司机名
	DriverType    string //驾驶证
	LineId        int64
	LineName      string
}

func (fc *FreeCheck) CorporationListTree(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CorporationListTreeReq
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var rspD []CorporationListTreeRsp
	briefItems, _, err := rpc.CorporationTree(ctx, param.CorporationId, "ASC")
	if err != nil {
		log.ErrorFields("rpc.CorporationTree error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	var LineMap = make(map[int64]*oet_scs_srv_app.OetLineItem)
	for _, item := range briefItems {
		i := CorporationListTreeRsp{
			Id:       item.Id,
			ParentId: item.ParentId,
			Type:     item.Type,
			Name:     item.Name,
			Drivers:  nil,
		}

		//
		corporationIdFalse := rpc.GetStaffsWithCorporationIdFalse(ctx, item.Id)

		for _, staffItem := range corporationIdFalse {
			fmt.Printf("staffItem:%+v \n", staffItem)
			driver := DriverInfo{
				Id:            staffItem.Id,
				CorporationId: staffItem.CorporationId,
				Name:          staffItem.Name,
				DriverType:    staffItem.DrvLicenseTypeStr,
				LineId:        staffItem.LineId,
			}
			if _, ok := LineMap[staffItem.LineId]; ok {
				driver.LineName = LineMap[staffItem.LineId].Name
			} else {
				line, _ := rpc.GetLineWithId(ctx, staffItem.LineId)
				if line != nil {
					driver.LineName = line.Name
					LineMap[staffItem.LineId] = line
				}
			}
			i.Drivers = append(i.Drivers, driver)
		}

		rspD = append(rspD, i)
	}

	return response.Success(rsp, map[string]interface{}{
		"Items": rspD,
	})
}
