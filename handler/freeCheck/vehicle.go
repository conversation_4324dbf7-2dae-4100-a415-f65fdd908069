package freeCheck

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/handler/base"
	"app/org/scs/erpv2/api/handler/safety"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	protoSchedule "app/org/scs/erpv2/api/proto/rpc/iss"
	protoVehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type VehicleArchive struct {
	License string
}

type VehicleOperateRsp struct {
	LastMonth LastMonth
	Yesterday Yesterday
}

type LastMonth struct {
	OperateDay           int64 // 营运天数
	Mileage              int64 // 里程
	ImplementationRate   int64 // 执行率
	ApprovedTotalMileage int64 // 核定总里程
	DeadheadMileage      int64 // 空驶里程
	GpsMileage           int64 // GPS里程
}

type Yesterday struct {
	//OperateDay           int64 // 营运天数
	Mileage              int64 // 里程
	ImplementationRate   int64 // 执行率
	ApprovedTotalMileage int64 // 核定总里程
	DeadheadMileage      int64 // 空驶里程
	GpsMileage           int64 // GPS里程
}

func (fc *FreeCheck) VehicleList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return base.VehicleList(ctx, req, rsp)
}

func (fc *FreeCheck) VehicleListAll(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return base.VehicleListAll(ctx, req, rsp)
}

func (fc *FreeCheck) VehicleOperate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q VehicleArchive
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.License == "" {
		log.ErrorFields("q.License == ''", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var monthStartAt, monthEndAt time.Time         // 左闭右闭
	var yesterdayStartAt, yesterdayEndAt time.Time // 左闭右闭

	// 计算时间
	now := time.Now()
	if now.Day() >= 26 {
		monthStartAt = time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)
		monthEndAt = time.Date(now.Year(), now.Month(), 25, 23, 59, 59, 999999, time.Local)
	} else {
		monthStartAt = time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local).AddDate(0, -2, 0)
		monthEndAt = time.Date(now.Year(), now.Month(), 25, 23, 59, 59, 999999, time.Local).AddDate(0, -1, 0)
	}

	yesterdayStartAt = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, -1)
	yesterdayEndAt = time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999, time.Local).AddDate(0, 0, -1)

	vehicleItem := rpc.GetVehicleWithLicense(ctx, &protoVehicle.GetVehicleWithLicenseRequest{
		License:       q.License,
		CorporationId: auth.User(ctx).GetTopCorporationId(),
	})

	lastMonthData := rpc.VehicleRunFormSum(ctx, vehicleItem.Id, monthStartAt.Unix(), monthEndAt.Unix())
	yesterdayData := rpc.VehicleRunFormSum(ctx, vehicleItem.Id, yesterdayStartAt.Unix(), yesterdayEndAt.Unix())

	rspD := VehicleOperateRsp{
		Yesterday: Yesterday{
			Mileage:              yesterdayData.ActualRunMileage,
			ImplementationRate:   0,
			ApprovedTotalMileage: yesterdayData.PlanActualRunMileage + yesterdayData.PlanActualRunUnloadMileage,
			DeadheadMileage:      yesterdayData.ActualRunUnloadMileage,
			GpsMileage:           yesterdayData.ActualGpsMileage,
		},
		LastMonth: LastMonth{
			OperateDay:           lastMonthData.RunDay,
			Mileage:              lastMonthData.ActualRunMileage,
			ImplementationRate:   0,
			ApprovedTotalMileage: lastMonthData.PlanActualRunMileage + lastMonthData.PlanActualRunUnloadMileage,
			DeadheadMileage:      lastMonthData.ActualRunUnloadMileage,
			GpsMileage:           lastMonthData.ActualGpsMileage,
		},
	}

	if yesterdayData.PlanCount != 0 {
		rspD.Yesterday.ImplementationRate = yesterdayData.ActualCount * 100 / yesterdayData.PlanCount
	}

	if lastMonthData.PlanCount != 0 {
		rspD.LastMonth.ImplementationRate = lastMonthData.ActualCount * 100 / lastMonthData.PlanCount
	}

	return response.Success(rsp, rspD)
}

type VehicleSafetyRsp struct {
	AccidentCount              int64 // 事故总数
	VehicleDamageAccidentCount int64 // 车损事故数
}

func (fc *FreeCheck) VehicleSafety(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q VehicleArchive
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.License == "" {
		log.ErrorFields("q.License == ''", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var s, e time.Time // 左闭右开

	// 计算时间
	now := time.Now()
	if now.Day() >= 26 {
		s = time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)
		e = time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local)
	} else {
		s = time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local).AddDate(0, -2, 0)
		e = time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)
	}

	var rspD VehicleSafetyRsp

	// 事故数据
	accidents, err := (&safety.TrafficAccident{}).GetCountWithOption(0, q.License, s, e)
	if err != nil {
		log.ErrorFields("TrafficAccident GetCountWithOption == err", map[string]interface{}{"err": err})
		//return response.Error(rsp, response.DbQueryFail)
	}
	for _, ac := range accidents {
		rspD.AccidentCount++
		if ac.HurtStatus == util.AccidentHurtStatus_3 || ac.HurtStatus == util.AccidentHurtStatus_4 { // 车损
			rspD.VehicleDamageAccidentCount++
		}
	}

	return response.Success(rsp, rspD)
}

type VehiclePlanScheduleRequest struct {
	VehicleId int64 `json:"VehicleId"`
	PlanAt    int64 `json:"PlanAt"`
}

// GetVehiclePlanSchedule 车辆排班信息
func (fc *FreeCheck) GetVehiclePlanSchedule(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehiclePlanScheduleRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.VehicleId == 0 || param.PlanAt == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	planAt := time.Unix(param.PlanAt, 0)
	startAt := time.Date(planAt.Year(), planAt.Month(), planAt.Day(), 0, 0, 0, 0, time.Local)
	endAt := startAt.AddDate(0, 0, 1)

	plans := rpc.GetFirstLastPlanScheduleWithVehicleId(ctx, &protoSchedule.GetFirstLastPlanScheduleWithVehicleIdRequest{
		VehicleId:         param.VehicleId,
		ExpectDepartAtGte: startAt.Unix(),
		ExpectDepartAtLte: endAt.Unix(),
		Status:            0,
		PlanSchType:       2,
	})

	if plans == nil || plans[0] == nil {
		return response.Success(rsp, map[string]interface{}{
			"IsExist": false,
		})
	}
	var driverId = plans[0].ActualDriverId
	if driverId == 0 {
		driverId = plans[0].PlanDriverId
	}
	var corporationId int64
	var driverName, corporationName, lineName string
	if driverId > 0 {
		staffInfo := rpc.GetStaffWithId(ctx, driverId)
		if staffInfo != nil {
			driverName = staffInfo.Name
			corporationId = staffInfo.CorporationId
		}
	}

	if corporationId > 0 {
		corporation := rpc.GetCorporationDetailById(ctx, corporationId)
		if corporation != nil {
			corporationName = corporation.Item.Name
		}
	}

	if plans[0].LineId > 0 {
		lineInfo, _ := rpc.GetLineWithId(ctx, plans[0].LineId)
		lineName = lineInfo.Name
	}

	return response.Success(rsp, map[string]interface{}{
		"IsExist":         true,
		"VehicleId":       param.VehicleId,
		"LineId":          plans[0].LineId,
		"LineName":        lineName,
		"DriverId":        driverId,
		"DriverName":      driverName,
		"CorporationId":   corporationId,
		"CorporationName": corporationName,
	})
}

type VehicleInsuranceRequest struct {
	VehicleId int64           `json:"VehicleId"`
	Date      model.LocalTime `json:"Date"`
}

func (fc *FreeCheck) GetVehicleInsurance(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleInsuranceRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	insurances := (&safetyModel.VehicleInsurance{}).GetVehicleValidInsurance(param.VehicleId, time.Time(param.Date))
	for i := range insurances {
		if insurances[i].InsuranceFilePath != "" {
			insurances[i].InsuranceFileUrl = config.Config.StaticFileHttpPrefix + insurances[i].InsuranceFilePath
		}
	}

	return response.Success(rsp, insurances)
}

type hasHistoryVehicleListItem struct {
	Id              int64  `json:"Id"`
	License         string `json:"Name"`
	CorporationId   int64  `json:"CorporationId"`
	CorporationName string `json:"CorporationName"`
	LineId          int64  `json:"LineId"`
	LineName        string `json:"LineName"`
	Code            string `json:"Code"`
	MigrationState  int64  `json:"MigrationState"` //0-迁入，1-迁出
	UseStatus       int64  `json:"UseStatus"`      //使用状态, 1-使用中；2-已报废
}

func (fc *FreeCheck) HasHistoryVehicleList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param HasHistoryListParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	endAt := param.EndAt.ToTime().AddDate(0, 0, 1)
	vehicles := rpc.GetHistoricalVehiclesWithUserId(ctx, auth.User(ctx).GetUserId(), param.StartAt.ToTime(), endAt)

	var results []hasHistoryVehicleListItem
	for i := range vehicles {
		var result = hasHistoryVehicleListItem{
			Id:              vehicles[i].Id,
			License:         vehicles[i].License,
			CorporationId:   vehicles[i].SubCorporationId,
			CorporationName: vehicles[i].SubCorporationName,
			LineId:          vehicles[i].LineId,
			LineName:        vehicles[i].LineName,
			UseStatus:       vehicles[i].UseStatus,
			MigrationState:  vehicles[i].MigrationState,
		}

		results = append(results, result)
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(results),
	})
}
