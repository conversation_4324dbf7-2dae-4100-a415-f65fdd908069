package freeCheck

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"context"
	api "github.com/micro/go-micro/v2/api/proto"
)

func (fc *FreeCheck) GetH5Permissions(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var authUser = auth.User(ctx).GetUser()
	log.ErrorFields("auth.user=: ", map[string]interface{}{"authUser": authUser})

	log.ErrorFields("authUser.GetPermissions() info", map[string]interface{}{"err": authUser.GetPermissions()})
	var apiPermissionMap = make(map[string]bool)
	for _, v := range config.Global.H5Permissions {
		apiPermissionMap[v] = authUser.HasPermission(v)
	}

	var data = map[string]interface{}{
		"Item": map[string]interface{}{
			"Permissions": apiPermissionMap,
		},
	}

	return response.Success(rsp, data)

}
