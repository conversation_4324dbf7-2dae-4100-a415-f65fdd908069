package freeCheck

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model/safety"
	settingModel "app/org/scs/erpv2/api/model/setting"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type DeviceFactoryListForm struct {
	Name       string `json:"Name"`
	ClassId    int64  `json:"ClassId"`
	CategoryId int64  `json:"CategoryId"`
	PresetType int64  `json:"PresetType"` //1责任人 2品牌方 3过保维修方 4供货方  5保修方
}

func (fc *FreeCheck) DeviceFactoryList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DeviceFactoryListForm
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	factories := (&settingModel.DeviceFactory{}).GetAll(auth.User(ctx).GetTopCorporationId(), param.Name, param.ClassId, param.CategoryId, param.PresetType)

	return response.Success(rsp, factories)

}

type DeviceFactoryUserListForm struct {
	WorkOrderId int64 `json:"WorkOrderId"`
	Grade       int64 `json:"Grade"` //级别 1设置的一级指派人 2设置的下级指派人（当前登录人的下级，包括二级和三级）
}

func (fc *FreeCheck) DeviceFactoryUserList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DeviceFactoryUserListForm
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	workOrder := (&workOrderModel.WorkOrder{}).FirstBy(param.WorkOrderId)
	if workOrder.Id == 0 {
		log.ErrorFields("workOrder not exist", map[string]interface{}{"workOrderId": param.WorkOrderId})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var factoryUsers []settingModel.DeviceFactoryHasUser

	var sceneTypes []int64
	if workOrder.IsInWarranty == util.StatusForTrue {
		sceneTypes = append(sceneTypes, util.DeviceFactoryUserSceneTypeForHandler)
	} else {
		sceneTypes = append(sceneTypes, util.DeviceFactoryUserSceneTypeForRepairer)
	}
	//设置的一级指派人
	if param.Grade == 1 {
		factoryUsers = (&settingModel.DeviceFactoryHasUser{}).GetBy(workOrder.MaintainerFactoryId, 0, sceneTypes)
	} else {
		factoryUser := (&settingModel.DeviceFactoryHasUser{}).GetByUser(workOrder.MaintainerFactoryId, auth.User(ctx).GetUserId(), sceneTypes)
		if factoryUser.Id == 0 {
			return response.Success(rsp, []interface{}{})
		}

		factoryUsers = (&settingModel.DeviceFactoryHasUser{}).GetBy(workOrder.MaintainerFactoryId, factoryUser.Id, sceneTypes)
	}
	return response.Success(rsp, factoryUsers)

}

type CheckOperationReportIsEnableUpdateParam struct {
	Type    int64 `json:"Type"`
	FleetId int64 `json:"FleetId"`
}

func (fc *FreeCheck) CheckOperationReportIsEnableUpdate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CheckOperationReportIsEnableUpdateParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	setting := (&settingModel.GlobalSetting{}).GetBy(auth.User(ctx).GetTopCorporationId(), param.Type)

	var enableUpdate = true
	var settingItem settingModel.GlobalSettingItemForOperationReport
	_ = json.Unmarshal(setting.SettingItem, &settingItem)
	fmt.Printf("settingItem:%+v \n", map[string]interface{}{"ddd": settingItem})
	fmt.Printf("param:%+v \n", map[string]interface{}{"ddd": param})
	if settingItem.StartDay > 0 && settingItem.EndDay > 0 && settingItem.StartDay <= settingItem.EndDay {
		nowDay := int64(time.Now().Day())
		if nowDay >= settingItem.StartDay && nowDay <= settingItem.EndDay {
			if !util.IncludeInt64(settingItem.DisabledFleetIds, param.FleetId) {
				enableUpdate = false
			}
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"EnableUpdate": enableUpdate,
	})
}

func (fc *FreeCheck) ListTopViolationCategory(ctx context.Context, req *api.Request, rsp *api.Response) error {
	cates := (&safety.QualityAssessmentCate{}).GetAll(auth.User(ctx).GetTopCorporationId())
	var results []safety.QualityAssessmentCate
	for i := range cates {
		if cates[i].ParentId == 0 {
			for j := range cates {
				if cates[j].ParentId == cates[i].Id {
					cates[i].Children = append(cates[i].Children, cates[j])
				}
			}
			results = append(results, cates[i])
		}
	}

	for i := range results {
		if len(results[i].Children) > 0 {
			for j := range results[i].Children {
				results[i].Children[j].Categories = (&safety.ViolationCategory{}).ListTop(results[i].Children[j].Id)
			}
		}
	}

	return response.Success(rsp, results)
}
