package freeCheck

import (
	operationHandler "app/org/scs/erpv2/api/handler/operation/management"
	"app/org/scs/erpv2/api/log"
	protooetline "app/org/scs/erpv2/api/proto/rpc/oetline"
	protoLineStation "app/org/scs/erpv2/api/proto/rpc/oetlinestation"
	protoStation "app/org/scs/erpv2/api/proto/rpc/station"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type LineStationRequest struct {
	LineName string `json:"LineName"`
	Offset   int64  `json:"Offset"`
	Limit    int64  `json:"Limit"`
}
type LineStationItem struct {
	LineId             int64  `json:"LineId"`
	LineName           string `json:"LineName"`
	LineType           int64  `json:"LineType"`
	UpLineStations     []*protoLineStation.OetLineStationBriefItem
	DownLineStations   []*protoLineStation.OetLineStationBriefItem
	CircleLineStations []*protoLineStation.OetLineStationBriefItem
}

func (fc *FreeCheck) LineStationList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineStationRequest

	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(param)
	if err != nil {
		log.ErrorFields("util.Validator().Struct error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 获取用户userid
	userId := auth.User(ctx).GetUserId()
	if userId == 0 {
		log.ErrorFields("md.GetWebUserId, userId = 0", nil)
		return response.Error(rsp, "403")
	}

	lineItems, totalCount := rpc.GetLinesWithUserId(ctx, &protooetline.GetLinesWithUserIdRequest{
		UserId:          userId,
		Name:            param.LineName,
		Offset:          param.Offset,
		Limit:           param.Limit,
		IsLineAttribute: 1,
	})

	var lineIds []int64
	lineMap := make(map[int64]*protooetline.OetLineItem)
	for i := range lineItems {
		lineIds = append(lineIds, lineItems[i].Id)
		lineMap[lineItems[i].Id] = lineItems[i]
	}

	var items []LineStationItem
	if len(lineIds) > 0 {
		lineStations := rpc.GetLineStationInfosWithLineIds(ctx, lineIds)
		if len(lineStations) > 0 {
			for i := range lineStations {
				var item = LineStationItem{
					LineId: lineStations[i].LineId,
				}
				lineInfo := lineMap[item.LineId]
				item.LineName = lineInfo.Name
				item.LineType = lineInfo.LineType
				item.UpLineStations = lineStations[i].UpLineStations
				item.DownLineStations = lineStations[i].DownLineStations
				item.CircleLineStations = lineStations[i].CircleLineStations

				items = append(items, item)
			}
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      items,
		"TotalCount": totalCount,
	})
}

type LineStationsRequest struct {
	LineId int64 `json:"LineId"` // 线路ID
	Sheet  int64 `json:"Sheet"`  // 1: 上行 2:下行 3:环形
	Except int64 `json:"Except"` //  需要过滤的站点类型,-1 不过滤, 0-中途站, 1-起点站, 2-终点站, 3-普通采样点
}

type FreeCheckLineStationItem struct {
	LineId   int64                                       `json:"LineId"`
	LineName string                                      `json:"LineName"`
	LineType int64                                       `json:"LineType"`
	Items    []*protoLineStation.OetLineStationBriefItem `json:"Items"`
}

// 精确查找线路站点
func (fc *FreeCheck) LineStations(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineStationsRequest

	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.LineId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	resp := rpc.GetLineStationInfoWithLineId(ctx, &protoLineStation.GetLineStationInfoWithLineIdRequest{
		LineId: param.LineId,
		Except: param.Except,
	})

	if resp == nil {
		return response.Error(rsp, response.DbQueryFail)
	}

	var retItems = make([]*protoLineStation.OetLineStationBriefItem, 0)

	if param.Sheet > 0 {
		switch param.Sheet {
		case util.LineSheetForUp, util.LineSheetForCircle:
			for _, item := range resp.Line1 {
				retItems = append(retItems, item)
			}
		case util.LineSheetForDown:
			for _, item := range resp.Line2 {
				retItems = append(retItems, item)
			}
		}
	} else {
		for _, item := range resp.Line1 {
			retItems = append(retItems, item)
		}
		for _, item := range resp.Line2 {
			retItems = append(retItems, item)
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"Item": FreeCheckLineStationItem{
			LineId:   param.LineId,
			LineName: resp.LineName,
			LineType: resp.LineType,
			Items:    retItems,
		},
	})

}

func (fc *FreeCheck) ListStation(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param operationHandler.ListStationParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var topCorpId = auth.User(ctx).GetTopCorporationId()
	if topCorpId == 0 {
		return response.Error(rsp, "PA1106")
	}
	var protoStationOpt = protoStation.GetStationsWithOption2Request{
		CorporationId:           topCorpId,
		AdminIsTrAtIveDivisions: param.CityCodes,
		StreetName:              param.StreetName,
		Name:                    param.Name,
		Lines:                   param.Lines,
		OwnedRoads:              param.OwnedRoads,
		ElecBusBoardTypes:       param.ElecBusBoardTypes,
		StationIds:              param.StationIds,
		Offset:                  int64(param.Offset),
		Limit:                   int64(param.Limit),
		Order:                   param.Order,
	}

	var (
		completionTimeGte = time.Time(param.CompletionTimeGteStr).Unix()
		completionTimeLte = time.Time(param.CompletionTimeLteStr).Unix()
	)
	if completionTimeGte > 0 {
		protoStationOpt.CompletionStartTime = completionTimeGte
	}
	if completionTimeLte > 0 {
		protoStationOpt.CompletionEndTime = completionTimeLte + 24*60*60 - 1
	}

	items, totalCount := rpc.GetStationsWithOption2(ctx, &protoStationOpt)

	return response.Success(rsp, map[string]interface{}{
		"Items":      items,
		"TotalItems": totalCount,
	})

}
