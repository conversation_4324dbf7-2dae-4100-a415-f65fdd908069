package freeCheck

import (
	"app/org/scs/erpv2/api/handler/base"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	protocorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"strings"
)

type FreeCheck struct {
}

func (fc *FreeCheck) GetStaffByStaffIdStr(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return base.GetStaffByStaffIdStr(ctx, req, rsp)
}

func (fc *FreeCheck) StaffList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param base.RequestParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, nil)
	results, err := base.GetStaffList(ctx, corporationIds, param.Name, param.JobCode, base.NonMoreOption)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(results),
	})
}

func (fc *FreeCheck) AllStaffList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param base.RequestParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	results, err := GetCorporationAndChildStaffList(ctx, auth.User(ctx).GetTopCorporationId(), param.Name, param.JobCode)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(results),
	})
}

// GetCorporationAndChildStaffList 机构以及子机构下的所有人员
func GetCorporationAndChildStaffList(ctx context.Context, corporationId int64, name, jobCode string) ([]map[string]interface{}, error) {
	oetStaffs := rpc.GetStaffsWithCorporationId(ctx, corporationId, name)
	if oetStaffs == nil {
		log.ErrorFields("rpc GetStaffsWithCorporationId is nil", map[string]interface{}{"corporationId": corporationId})
		return nil, nil
	}

	var results []map[string]interface{}

	rpcTmpCorporation := make(map[int64]*protocorporation.CorporationItem) // map[corporationId]机构信息

	for i := range oetStaffs {
		if jobCode != "" && !strings.Contains(oetStaffs[i].StaffId, jobCode) {
			continue
		}

		var corpName string

		// 获取所属机构
		if oetCorporationItem, ok := rpcTmpCorporation[oetStaffs[i].CorporationId]; ok {
			if oetCorporationItem != nil {
				corpName = oetCorporationItem.Name
			}
		} else {
			corporationItem := rpc.GetCorporationById(ctx, oetStaffs[i].CorporationId)
			rpcTmpCorporation[oetStaffs[i].CorporationId] = corporationItem
			if corporationItem != nil {
				corpName = corporationItem.Name
			}
		}

		results = append(results, map[string]interface{}{
			"Id":            oetStaffs[i].Id,
			"Name":          oetStaffs[i].Name,
			"StaffIdStr":    oetStaffs[i].StaffId,
			"JobStatus":     oetStaffs[i].WorkingState,
			"WorkPostType":  util.MasterToErp[oetStaffs[i].Occupation],
			"CorporationId": oetStaffs[i].CorporationId,
			"Corporation":   corpName,
			"Phone":         oetStaffs[i].Phone,
		})
	}
	return results, nil
}

type HasHistoryListParam struct {
	StartAt model.LocalTime `json:"StartAt"`
	EndAt   model.LocalTime `json:"EndAt"`
}
type hasHistoryStaffListItem struct {
	Id              int64  `json:"Id"`
	Name            string `json:"Name"`
	CorporationId   int64  `json:"CorporationId"`
	CorporationName string `json:"CorporationName"`
	LineId          int64  `json:"LineId"`
	LineName        string `json:"LineName"`
	Code            string `json:"Code"`
	MigrationState  int64  `json:"MigrationState"` //0-迁入，1-迁出
	WorkPostType    int64  `json:"WorkPostType"`   //岗位类型
}

func (fc *FreeCheck) HasHistoryStaffList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param HasHistoryListParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	endAt := param.EndAt.ToTime().AddDate(0, 0, 1)
	staffs := rpc.GetHistoricalStaffsWithUserId(ctx, auth.User(ctx).GetUserId(), param.StartAt.ToTime(), endAt)

	var results []hasHistoryStaffListItem
	for i := range staffs {
		var result = hasHistoryStaffListItem{
			Id:              staffs[i].Id,
			Name:            staffs[i].Name,
			CorporationId:   staffs[i].CorporationId,
			CorporationName: staffs[i].CorporationName,
			LineId:          staffs[i].LineId,
			LineName:        staffs[i].LineName,
			Code:            staffs[i].Code,
			MigrationState:  staffs[i].MigrationState,
			WorkPostType:    util.MasterToErp[staffs[i].Occupation],
		}

		results = append(results, result)
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(results),
	})
}

type StaffLeaveCountParam struct {
	StaffId int64           `json:"StaffId"`
	StartAt model.LocalTime `json:"StartAt"`
	EndAt   model.LocalTime `json:"EndAt"`
}

func (fc *FreeCheck) StaffLeaveCount(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffLeaveCountParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	records := (&hrModel.ApplyLeaveRecord{}).StaffLeaveCount(param.StaffId, param.StartAt.ToTime(), param.EndAt.ToTime())

	return response.Success(rsp, records)
}
