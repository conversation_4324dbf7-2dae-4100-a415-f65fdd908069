package freeCheck

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/handler/base"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	protocorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"strings"
)

type FreeCheck struct {
}

func (fc *FreeCheck) GetStaffByStaffIdStr(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return base.GetStaffByStaffIdStr(ctx, req, rsp)
}

func (fc *FreeCheck) StaffList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param base.RequestParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, nil)
	results, err := base.GetStaffList(ctx, corporationIds, param.Name, param.JobCode, base.NonMoreOption)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(results),
	})
}

func (fc *FreeCheck) AllStaffList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param base.RequestParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	results, err := GetCorporationAndChildStaffList(ctx, auth.User(ctx).GetTopCorporationId(), param.Name, param.JobCode)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(results),
	})
}

// GetCorporationAndChildStaffList 机构以及子机构下的所有人员
func GetCorporationAndChildStaffList(ctx context.Context, corporationId int64, name, jobCode string) ([]map[string]interface{}, error) {
	oetStaffs := rpc.GetStaffsWithCorporationId(ctx, corporationId, name)
	if oetStaffs == nil {
		log.ErrorFields("rpc GetStaffsWithCorporationId is nil", map[string]interface{}{"corporationId": corporationId})
		return nil, nil
	}

	var results []map[string]interface{}

	rpcTmpCorporation := make(map[int64]*protocorporation.CorporationItem) // map[corporationId]机构信息

	for i := range oetStaffs {
		if jobCode != "" && !strings.Contains(oetStaffs[i].StaffId, jobCode) {
			continue
		}

		var corpName string

		// 获取所属机构
		if oetCorporationItem, ok := rpcTmpCorporation[oetStaffs[i].CorporationId]; ok {
			if oetCorporationItem != nil {
				corpName = oetCorporationItem.Name
			}
		} else {
			corporationItem := rpc.GetCorporationById(ctx, oetStaffs[i].CorporationId)
			rpcTmpCorporation[oetStaffs[i].CorporationId] = corporationItem
			if corporationItem != nil {
				corpName = corporationItem.Name
			}
		}

		results = append(results, map[string]interface{}{
			"Id":            oetStaffs[i].Id,
			"Name":          oetStaffs[i].Name,
			"StaffIdStr":    oetStaffs[i].StaffId,
			"JobStatus":     oetStaffs[i].WorkingState,
			"WorkPostType":  util.MasterToErp[oetStaffs[i].Occupation],
			"CorporationId": oetStaffs[i].CorporationId,
			"Corporation":   corpName,
			"Phone":         oetStaffs[i].Phone,
		})
	}
	return results, nil
}

type HasHistoryListParam struct {
	StartAt model.LocalTime `json:"StartAt"`
	EndAt   model.LocalTime `json:"EndAt"`
}
type hasHistoryStaffListItem struct {
	Id              int64  `json:"Id"`
	Name            string `json:"Name"`
	CorporationId   int64  `json:"CorporationId"`
	CorporationName string `json:"CorporationName"`
	LineId          int64  `json:"LineId"`
	LineName        string `json:"LineName"`
	Code            string `json:"Code"`
	MigrationState  int64  `json:"MigrationState"` //0-迁入，1-迁出
	WorkPostType    int64  `json:"WorkPostType"`   //岗位类型
}

func (fc *FreeCheck) HasHistoryStaffList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param HasHistoryListParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	endAt := param.EndAt.ToTime().AddDate(0, 0, 1)
	staffs := rpc.GetHistoricalStaffsWithUserId(ctx, auth.User(ctx).GetUserId(), param.StartAt.ToTime(), endAt)

	var results []hasHistoryStaffListItem
	for i := range staffs {
		var result = hasHistoryStaffListItem{
			Id:              staffs[i].Id,
			Name:            staffs[i].Name,
			CorporationId:   staffs[i].CorporationId,
			CorporationName: staffs[i].CorporationName,
			LineId:          staffs[i].LineId,
			LineName:        staffs[i].LineName,
			Code:            staffs[i].Code,
			MigrationState:  staffs[i].MigrationState,
			WorkPostType:    util.MasterToErp[staffs[i].Occupation],
		}

		results = append(results, result)
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(results),
	})
}

type StaffLeaveCountParam struct {
	StaffId int64           `json:"StaffId"`
	StartAt model.LocalTime `json:"StartAt"`
	EndAt   model.LocalTime `json:"EndAt"`
}

func (fc *FreeCheck) StaffLeaveCount(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffLeaveCountParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	records := (&hrModel.ApplyLeaveRecord{}).StaffLeaveCount(param.StaffId, param.StartAt.ToTime(), param.EndAt.ToTime())

	return response.Success(rsp, records)
}

func (fc *FreeCheck) HeadimgShow(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param hrModel.HeadImgApproval
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = param.FindByStaffId()
	if err != nil {
		log.ErrorFields("find err", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	if param.Id == 0 {
		//获取主数据数据
		oetStaff := rpc.GetStaffWithId(ctx, param.StaffId)
		if oetStaff == nil {
			log.ErrorFields("GetStaffWithId fail", map[string]interface{}{"err": err, "staffId": param.StaffId})
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		if oetStaff.HeadImg != "" {
			arr := strings.Split(oetStaff.HeadImg, "/")
			fileName := arr[len(arr)-1]
			fileNameArr := strings.Split(fileName, ".")
			suffix := ""
			if len(fileNameArr) > 1 {
				suffix = fileNameArr[1]
			}
			HeadImg := []hrModel.HeadImg{
				{
					Name:   fileName,
					Path:   oetStaff.HeadImg,
					Suffix: suffix,
					Url:    fmt.Sprintf("%s%s", oetStaff.InnerHeadPortrait, oetStaff.HeadImg),
				},
			}
			bytes, _ := json.Marshal(&HeadImg)
			param.HeadImg = model.JSON(bytes)
		}
		param.HeadImgState = -1
	}
	return response.Success(rsp, param)
}

type UpdateHeadImgForm struct {
	StaffId int64      `json:"StaffId"`
	HeadImg model.JSON `json:"HeadImg"`
}

func (fc *FreeCheck) UpdateHeadImg(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form UpdateHeadImgForm
	err := json.Unmarshal([]byte(req.Body), &form)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	err = service.CreateOrUpdateHeadImgApproval(ctx, form.StaffId, form.HeadImg)
	if err != nil {
		log.ErrorFields("CreateOrUpdateHeadImgApproval error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

func (sta *FreeCheck) MineInfo(ctx context.Context, req *api.Request, rsp *api.Response) error {
	user := auth.User(ctx).GetUser()

	var staffArchive hrModel.StaffArchive

	oetStaff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	if oetStaff == nil {
		log.ErrorFields("GetStaffWithPhone fail", map[string]interface{}{"phone": user.Phone})
		return response.Success(rsp, staffArchive)
	}

	//获取ERP数据
	err := staffArchive.FindMineInfoByStaffId(oetStaff.Id)
	if err != nil {
		log.ErrorFields("StaffArchive.FindMineInfoByStaffId fail", map[string]interface{}{"err": err, "id": oetStaff.Id})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	service.OetStaffFieldToErpStaffField(oetStaff, &staffArchive)

	staffArchive.FileHttpPrefix = config.Config.StaticFileHttpPrefix
	staffArchive.IdStr = fmt.Sprintf("%v", staffArchive.Id)
	staffArchive.IsProcessHandler = processService.CheckIsProcessRelater(staffArchive.Id, config.StaffArchiveEditApplyFormTemplate, user.Id)

	corporationItem := rpc.GetCorporationById(ctx, staffArchive.MasterDataFieldForJob.CorporationId)
	if corporationItem != nil {
		staffArchive.CorporationName = corporationItem.Name
		staffArchive.CorporationType = corporationItem.Type
	}
	return response.Success(rsp, staffArchive)
}
