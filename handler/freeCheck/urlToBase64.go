package freeCheck

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"io/ioutil"
	"os"
	"strings"
)

type UrlToBase64StringRequest struct {
	Url string `json:"Url"`
}

// UrlToBase64String 前端传一个图片url地址，返回base64字符串
func (fc *FreeCheck) UrlToBase64String(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param UrlToBase64StringRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	// url: StaticFileHttpPrefix + WebRoot ...
	if !strings.HasPrefix(param.Url, config.Config.StaticFileHttpPrefix) {
		log.ErrorFields("url路径前缀错误", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	index := strings.Index(param.Url, config.Config.WebRoot)

	webRootPath := param.Url[index:]

	//获取本地文件
	file, err := os.Open(fmt.Sprintf("%s%s", config.Config.AbsDirPath, webRootPath))
	if err != nil {
		log.ErrorFields("获取本地文件错误", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	defer file.Close()
	fileByte, _ := ioutil.ReadAll(file)

	var base64Str string
	if strings.HasSuffix(param.Url, util.Default_Suffix) {
		base64Str = "data:image/png;base64," + base64.StdEncoding.EncodeToString(fileByte)
	} else {
		base64Str = base64.StdEncoding.EncodeToString(fileByte)
	}

	return response.SuccessBigString(rsp, &base64Str)
}
