package freeCheck

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	exportModel "app/org/scs/erpv2/api/model/export"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"io/ioutil"
)

type ExportFileRequest struct {
	Id    int64  `json:"Id"`
	Scene string `json:"Scene"`
	model.Paginator
}

func (fc *FreeCheck) ExportFileList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ExportFileRequest

	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	records, count := (&exportModel.ExportFile{}).GetBy(auth.User(ctx).GetUserId(), param.Scene, param.Paginator)
	for i := range records {
		records[i].FileUrl = config.Config.StaticFileHttpPrefix + records[i].Path
	}

	return response.Success(rsp, map[string]interface{}{
		"TotalCount": count,
		"Items":      records,
	})
}

func (fc *FreeCheck) GetExportFileToBase64(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ExportFileRequest

	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	exportFile := (&exportModel.ExportFile{}).FirstBy(param.Id)

	if exportFile.Id == 0 || exportFile.OpUserId != auth.User(ctx).GetUserId() {
		return response.Error(rsp, response.ParamsInvalid)
	}

	file, err := ioutil.ReadFile(config.Config.AbsDirPath + exportFile.Path)
	if err != nil {
		log.ErrorFields("ReadFile error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	encoded := base64.StdEncoding.EncodeToString(file)

	return response.Success(rsp, encoded)
}
