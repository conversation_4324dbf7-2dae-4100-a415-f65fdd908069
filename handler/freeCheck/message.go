package freeCheck

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	messageModel "app/org/scs/erpv2/api/model/message"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service/auth"
	messageService "app/org/scs/erpv2/api/service/message"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"gorm.io/gorm"
	"time"
)

type SendRecvType int64

type Message struct {
	messageModel.Message

	StartAt    int64
	EndAt      int64
	SendOrRecv SendRecvType //
	Types      []string
	Origins    []string
	model.Paginator

	CorporationIds []int64 // 车属机构
	LineId         int64   // 线路
	License        string  // 车牌
	Driver         string  // 司机

	CurrentHandlerUserName string // 当前处理人
	LbpmProcessStatus      int64  // 流程状态
	Order                  string // asc desc
	OrderBy                string // 排序字段 ApplyAt(事故提交时间)/HappenAt(事故发生时间)
}

type ListRsp struct {
	messageModel.Message
	Attach map[string]interface{} `json:"Attach"`
}

type ProcessAccidentId struct {
	TrafficAccidentId int64 `json:"TrafficAccidentId"`
}

func getAccident(j model.JSON) map[string]interface{} {
	rsp := map[string]interface{}{}
	// 获取事故id
	var process processModel.LbpmApplyProcess
	err := json.Unmarshal(j, &process)
	if err != nil {
		fmt.Println("############## json.Unmarshal process err=", err)
		return rsp
	}

	var obj ProcessAccidentId
	err = json.Unmarshal(process.Param, &obj)
	if err != nil {
		fmt.Println("############## json.Unmarshal obj err=", err)
		return rsp
	}

	// 查询事故表中的车牌、事故发生单位
	var thisTrafficAccident safetyModel.TrafficAccident
	err = (&thisTrafficAccident).FindBy(obj.TrafficAccidentId)
	if err != nil {
		log.ErrorFields("TrafficAccident FindBy fail", map[string]interface{}{"err": err})
		return rsp
	}
	_, corp := thisTrafficAccident.GetCorporation()
	rsp = map[string]interface{}{
		"License":     thisTrafficAccident.License,
		"Corporation": corp,
	}

	return rsp
}

func (fc *FreeCheck) MessageList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	if !auth.User(ctx).HasUser() {
		return response.Success(rsp, nil)
	}

	var q Message
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	var e, s time.Time

	if q.StartAt > 0 {
		s = time.Unix(q.StartAt, 0)
	}

	if q.EndAt > 0 {
		e = time.Unix(q.EndAt, 0)
	}

	omitOrigins := []string{
		config.TrafficAccidentReportFormTemplate,
		config.TrafficAccidentEditFormTemplate,
		config.TrafficAccidentPaymentMoneyFormTemplate,
		config.TrafficAccidentLendMoneyFormTemplate,
		config.TrafficAccidentDrawbackMoneyFormTemplate,
		config.TrafficAccidentBranchCloseFormTemplate,
		config.TrafficAccidentCloseFormTemplate,
	}

	q.RecvUserId = auth.User(ctx).GetUserId()

	list, totalCount, err := (&q.Message).List(nil, q.Origins, nil, s, e, omitOrigins, q.Paginator)
	if err != nil {
		log.Error("List err==", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	var rspD []ListRsp

	for _, msg := range list {
		item := ListRsp{
			Message: msg,
			Attach:  nil,
		}

		switch msg.Origin {
		case config.TrafficAccidentPaymentMoneyFormTemplate,
			config.TrafficAccidentDrawbackMoneyFormTemplate,
			config.TrafficAccidentLendMoneyFormTemplate,
			config.TrafficAccidentBranchCloseFormTemplate:
			item.Attach = getAccident(msg.RelationParam)

		}

		rspD = append(rspD, item)
	}

	data := map[string]interface{}{
		"Items":      rspD,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)
}

func (fc *FreeCheck) MessageRead(ctx context.Context, req *api.Request, rsp *api.Response) error {
	if !auth.User(ctx).HasUser() {
		return response.Error(rsp, response.Forbidden)
	}

	var q Message
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	// Read() 回写结构体
	err = (&q.Message).Read(tx, auth.User(ctx).GetUserId())
	if err != nil {
		log.Error("Read err==", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}
	err = messageService.DispatchMessageHandler(tx, []int64{q.Message.RelationId}, auth.User(ctx).GetUserId(), q.Message.Type)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}
func (fc *FreeCheck) MessageReadAll(ctx context.Context, req *api.Request, rsp *api.Response) error {
	if !auth.User(ctx).HasUser() {
		return response.Error(rsp, response.Forbidden)
	}
	var q Message
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	q.RecvUserId = auth.User(ctx).GetUserId()

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	omitOrigins := []string{
		config.TrafficAccidentReportFormTemplate,
		config.TrafficAccidentEditFormTemplate,
		config.TrafficAccidentPaymentMoneyFormTemplate,
		config.TrafficAccidentLendMoneyFormTemplate,
		config.TrafficAccidentDrawbackMoneyFormTemplate,
		config.TrafficAccidentBranchCloseFormTemplate,
		config.TrafficAccidentCloseFormTemplate,
	}

	var list []messageModel.Message
	list, err = (&q.Message).ReadAll(tx, omitOrigins)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error("err == gorm.ErrRecordNotFound")
			return response.Success(rsp, nil)
		}
		log.Error("ReadAll err==", err.Error())
		return response.Error(rsp, response.DbUpdateFail)
	}

	var handlers = make(map[string][]int64)
	for i := range list {
		handlers[list[i].Type] = append(handlers[list[i].Type], list[i].RelationId)
	}
	for messageType := range handlers {
		err = messageService.DispatchMessageHandler(tx, handlers[messageType], auth.User(ctx).GetUserId(), messageType)
		if err != nil {
			return response.Error(rsp, response.FAIL)
		}
	}

	return response.Success(rsp, nil)
}
func (fc *FreeCheck) MessageUnReadList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	if !auth.User(ctx).HasUser() {
		return response.Success(rsp, map[string]interface{}{
			"UnReadCount":    0,
			"UnProcessCount": 0,
		})
	}

	var q Message
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	q.RecvUserId = auth.User(ctx).GetUserId()

	omitOrigins := []string{
		config.TrafficAccidentReportFormTemplate,
		config.TrafficAccidentEditFormTemplate,
		config.TrafficAccidentPaymentMoneyFormTemplate,
		config.TrafficAccidentLendMoneyFormTemplate,
		config.TrafficAccidentDrawbackMoneyFormTemplate,
		config.TrafficAccidentBranchCloseFormTemplate,
		config.TrafficAccidentCloseFormTemplate,
	}

	list, err := (&q.Message).UnReadList(omitOrigins)
	if err != nil {
		log.Error("UnReadList err==", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	var (
		unReadCount    = 0
		unProcessCount = 0
	)

	for _, messages := range list {
		if messages.ReadType == messageModel.MESSAGE_READ_1 {
			unReadCount++
		} else if messages.ReadType == messageModel.MESSAGE_PROCESS_2 {
			unProcessCount++
		}
	}

	data := map[string]interface{}{
		"UnReadCount":    unReadCount,
		"UnProcessCount": unProcessCount,
	}

	return response.Success(rsp, data)

}

func (fc *FreeCheck) MessageDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	if !auth.User(ctx).HasUser() {
		return response.Error(rsp, response.Forbidden)
	}
	var param Message
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	message := (&messageModel.Message{}).GetDetail(param.Id)
	if message.Id == 0 {
		log.ErrorFields("message.GetDetail error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if message.RecvUserId != auth.User(ctx).GetUserId() {
		return response.Error(rsp, response.Forbidden)
	}

	err = (&messageModel.Message{}).Delete([]int64{message.Id})
	if err != nil {
		log.ErrorFields("message.Delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, nil)
}

// TrafficAccidentListHandle 安全事故处理-列表
func (fc *FreeCheck) TrafficAccidentListHandle(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Message
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Order == "" {
		param.Order = "desc"
	}
	if param.OrderBy == "" {
		param.OrderBy = "ApplyAt"
	}

	var e, s time.Time

	if param.StartAt > 0 {
		s = time.Unix(param.StartAt, 0)
	}

	if param.EndAt > 0 {
		e = time.Unix(param.EndAt, 0)
	}

	// 默认查询所有安全事故
	if len(param.Origins) == 0 {
		param.Origins = []string{
			config.TrafficAccidentReportFormTemplate,
			config.TrafficAccidentEditFormTemplate,
			config.TrafficAccidentPaymentMoneyFormTemplate,
			config.TrafficAccidentLendMoneyFormTemplate,
			config.TrafficAccidentDrawbackMoneyFormTemplate,
			config.TrafficAccidentBranchCloseFormTemplate,
			config.TrafficAccidentCloseFormTemplate,
		}
	}

	// 使用pg数据库json查询语法，需要数据库版本 9.3.14及以上

	msgRsp, totalCount, err := (&messageModel.Message{}).ListTrafficAccidentProcessHandle(auth.User(ctx).GetUserId(), param.SendUserName, param.Origins, param.CorporationIds,
		param.LineId, param.License, param.Driver, param.ReadStatus, param.ProcessStatus, s, e, param.Paginator, param.LbpmProcessStatus, param.CurrentHandlerUserName, param.OrderBy, param.Order)
	if err != nil {
		log.Error("List err==", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	data := map[string]interface{}{
		"Items":      msgRsp,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)

}

// TrafficAccidentReadAllHandle 安全事故处理-全部已读
func (fc *FreeCheck) TrafficAccidentReadAllHandle(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Message
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	// 使用pg数据库json查询语法，需要数据库版本 9.3.14及以上

	origins := []string{
		config.TrafficAccidentReportFormTemplate,
		config.TrafficAccidentEditFormTemplate,
		config.TrafficAccidentPaymentMoneyFormTemplate,
		config.TrafficAccidentLendMoneyFormTemplate,
		config.TrafficAccidentDrawbackMoneyFormTemplate,
		config.TrafficAccidentBranchCloseFormTemplate,
		config.TrafficAccidentCloseFormTemplate,
	}

	err = (&messageModel.Message{}).ReadAllTrafficAccidentProcessHandle(auth.User(ctx).GetUserId(), origins)
	if err != nil {
		log.Error("List err==", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)

}

// TrafficAccidentUnReadList 安全事故处理-未读数量
func (fc *FreeCheck) TrafficAccidentUnReadList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	if !auth.User(ctx).HasUser() {
		return response.Success(rsp, map[string]interface{}{
			"UnReadCount":    0,
			"UnProcessCount": 0,
		})
	}

	var q Message
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	origins := []string{
		config.TrafficAccidentReportFormTemplate,
		config.TrafficAccidentEditFormTemplate,
		config.TrafficAccidentPaymentMoneyFormTemplate,
		config.TrafficAccidentLendMoneyFormTemplate,
		config.TrafficAccidentDrawbackMoneyFormTemplate,
		config.TrafficAccidentBranchCloseFormTemplate,
		config.TrafficAccidentCloseFormTemplate,
	}

	list, err := (&messageModel.Message{}).UnReadTrafficAccidentProcessHandleList(auth.User(ctx).GetUserId(), origins)
	if err != nil {
		log.Error("UnReadTrafficAccidentProcessHandleList err==", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	var (
		unReadCount    = 0
		unProcessCount = 0
	)

	for _, messages := range list {
		if messages.ReadType == messageModel.MESSAGE_READ_1 {
			unReadCount++
		} else if messages.ReadType == messageModel.MESSAGE_PROCESS_2 {
			unProcessCount++
		}
	}

	data := map[string]interface{}{
		"UnReadCount":    unReadCount,
		"UnProcessCount": unProcessCount,
	}

	return response.Success(rsp, data)

}
