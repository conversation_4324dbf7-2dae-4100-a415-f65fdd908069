package freeCheck

import (
	"app/org/scs/erpv2/api/log"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

type UserParam struct {
	Name  string `json:"Name"`
	Phone string `json:"Phone"`
}

type UserListItem struct {
	Id              int64
	Account         string
	Name            string
	CorporationId   int64
	CorporationName string
	ExpireAt        int64
}

func (fc *FreeCheck) UserList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param UserParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	users := rpc.ListMainUser(ctx, auth.User(ctx).GetTopCorporationId(), param.Name, param.Phone, 1)

	var userItems []UserListItem

	for i := range users {
		userItems = append(userItems, UserListItem{
			Id:              users[i].Id,
			Account:         users[i].Username,
			Name:            users[i].Nickname,
			CorporationId:   users[i].CorporationId,
			CorporationName: users[i].Corporation,
			ExpireAt:        users[i].ExpiredAt,
		})
	}

	return response.Success(rsp, map[string]interface{}{"Items": userItems, "TotalCount": len(userItems)})
}

// UserStatus 当前登录用户的状态
func (fc *FreeCheck) UserStatus(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var hasStaff bool
	var workPostType int64
	user := auth.User(ctx).GetUser()
	userStaff := rpc.GetStaffWithPhone(ctx, auth.User(ctx).GetCorporationId(), user.Phone)

	if userStaff != nil {
		hasStaff = true
		//主数据岗位类型：0-司机; 1-乘务员;2-管理员;3-辅工; 4-辅岗; 5-干部; 6-仓管人员; 7-安保人员; 10-其他;
		//ERP岗位类型：1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他
		masterToErp := map[int64]int64{2: 1, 5: 2, 0: 3, 1: 4, 7: 5, 3: 6, 6: 7, 10: 8}
		workPostType = masterToErp[userStaff.Occupation]
	}

	return response.Success(rsp, map[string]interface{}{
		"HasStaff":     hasStaff,
		"HasUser":      auth.User(ctx).HasUser(),
		"UserInfo":     auth.User(ctx).GetUser(),
		"WorkPostType": workPostType,
	})
}

func (fc *FreeCheck) SaveSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param settingModel.UserSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	authUser := auth.User(ctx).GetUser()

	param.TopCorporationId = authUser.TopCorporationId
	param.UserId = authUser.Id
	param.UserName = authUser.Name

	err = param.UpdateOrCreate()
	if err != nil {
		log.ErrorFields("UpdateOrCreate error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (fc *FreeCheck) GetSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	authUser := auth.User(ctx).GetUser()
	setting := (&settingModel.UserSetting{}).GetByUser(authUser.Id)

	return response.Success(rsp, setting.SettingItem)
}
