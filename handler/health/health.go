package handler

import (
	"bytes"
	"context"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/micro/go-micro/v2/client"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/common/expfmt"
	"net/http"
)

type Health struct {
	Client client.Client
}

func (h *Health) Metrics(ctx context.Context, req *api.Request, rsp *api.Response) error {
	entry, err := prometheus.DefaultGatherer.Gather()
	if err != nil {
		return nil
	}

	buf := bytes.NewBuffer(nil)

	var header = make(http.Header, 0)
	for _, value := range req.Header {
		header.Add(value.Key, value.Values[0])
	}

	contentType := expfmt.Negotiate(header)
	enc := expfmt.NewEncoder(buf, contentType)

	for _, met := range entry {
		if err := enc.Encode(met); err != nil {
			return nil
		}
	}
	rsp.Body = string(buf.Bytes())
	return nil
}
