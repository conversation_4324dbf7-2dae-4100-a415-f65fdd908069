package workOrder

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/workOrder"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	go_api "github.com/micro/go-micro/v2/api/proto"
)

type MiniWorkOrderListParam struct {
	workOrder.MiniWorkOrder
	model.Paginator
}

func (w *WorkOrder) MiniWorkOrderList(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param MiniWorkOrderListParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	records, count := (&workOrder.MiniWorkOrder{}).GetBy(param.Code, param.CateType, param.CreatorPhone, param.Paginator)
	return response.Success(rsp, map[string]interface{}{
		"Items":      records,
		"TotalCount": count,
	})
}
func (w *WorkOrder) MiniWorkOrderApply(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param workOrder.MiniWorkOrder
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	record := (&workOrder.MiniWorkOrder{}).FirstBy(param.Id)
	if record.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	authUser := auth.User(ctx).GetUser()
	param.ApplyStatus = util.StatusForTrue
	param.HandleCorporationName = authUser.CorporationName
	param.HandleCorporationId = authUser.CorporationId
	param.HandleStaffId = authUser.Id
	param.HandleStaffName = authUser.Name

	err = param.UpdateApply()
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, nil)
}
