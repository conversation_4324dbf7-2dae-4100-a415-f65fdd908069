package workOrder

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/handler/common"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	commonModel "app/org/scs/erpv2/api/model/common"
	settingModel "app/org/scs/erpv2/api/model/setting"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	protoCorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	staffProto "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	protoVehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	protoParking "app/org/scs/erpv2/api/proto/rpc/parking"
	protoStation "app/org/scs/erpv2/api/proto/rpc/station"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/micro/go-micro/v2/api/proto"
	uuid "github.com/satori/go.uuid"
	"github.com/tealeg/xlsx"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

type Device struct {
	//workOrderModel.DeviceModel
	//model.Paginator
	//DeviceClassDictIds       []int64
	//DeviceCategoryDictIds    []int64
	//SupplierDictIds          []int64
	//ResponsibleDepartmentIds []int64
	//Ids                      []int64
	//StartAt                  model.LocalTime
	//EndAt                    model.LocalTime

	//IdStr                       string   // 设备型号id 兼容wx小程序大整型
	//IdStrs                      []string // 设备型号id 兼容wx小程序大整型
	//DeviceClassDictIdStr        string   // *设备大类字典id 兼容wx小程序大整型
	//DeviceCategoryDictIdStr     string   // *设备种类字典id 兼容wx小程序大整型
	//SupplierDictIdStr           string   // *供货方字典id 兼容wx小程序大整型
	//ResponsibleDepartmentIdStr  string   // *责任部门id 机构类型为部门 兼容wx小程序大整型
	//DeviceClassDictIdStrs       []string // *设备大类字典id 兼容wx小程序大整型
	//DeviceCategoryDictIdStrs    []string // *设备种类字典id 兼容wx小程序大整型
	//SupplierDictIdStrs          []string // *供货方字典id 兼容wx小程序大整型
	//ResponsibleDepartmentIdStrs []string // *责任部门id 机构类型为部门 兼容wx小程序大整型
	//CorporationIdStr            string   // 车属机构id 兼容wx小程序大整型

	//CorporationId int64
	//
	//LineIds        []int64
	//CorporationIds []int64
	//
	//AssociationStatus          int64
	//Code                       string
	//FactoryCode                string //厂家编号
	//AssociationObjectType      int64
	//AssociationObjectName      string // 模糊搜索关联对象
	//ExactAssociationObjectName string // 精确搜索关联对象
	//AssociationObjectId        int64  //关联对象ID
	//PurchaseAt                 int64  //采购日期
	//Batch                      string
	//ExpireAt                   model.LocalTime //过期日期
	//MaintainerDictId           int64           //过保维修方ID
	//ExpireStatus               int64           //1在保  2过保
}

type DeviceBindChildDevice struct {
	FactoryCode            string `json:"FactoryCode"`            //主设备厂家编号
	ChildDeviceCode        string `json:"ChildDeviceCode"`        //子设备编号
	ChildDeviceFactoryCode string `json:"ChildDeviceFactoryCode"` //子设备厂家编号
}

func (d *Device) AddDeviceCategory(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q common.Dict
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.Error("GetTopCorporationId == 0")
		return response.Error(rsp, response.Forbidden)
	}

	if q.Dict.DictKey == "" || q.Dict.DictValue == "" || q.Dict.ParentId == 0 || len(q.LiableStaffIds) == 0 {
		log.Error("q.Dict.DictKey == '' || q.Dict.DictValue == ''|| q.Dict.ParentId == 0 || len(q.Dict.DevicePresets)==0")
		return response.Error(rsp, response.ParamsMissing)
	}

	// 创建字典
	// 创建设备预设

	q.Dict.Id = model.Id()
	q.Dict.GroupId = topCorporationId
	q.Dict.DictType = commonModel.REPAIR_CATEGORY_14
	code, err := (q.Dict).GetCodeWithOptions()
	if err != nil {
		log.Error("GetCodeWithOptions err == ", err)
		return response.Error(rsp, response.DbQueryFail)
	}
	if code == "" {
		q.Dict.DictCode = "1"
	} else {
		parseInt, err := strconv.ParseInt(code, 10, 64)
		if err != nil {
			log.Error("ParseInt err == ", err)
			return response.Error(rsp, response.FAIL)
		}

		q.Dict.DictCode = strconv.FormatInt(parseInt+1, 10)

	}
	q.Dict.ParentIdPath = fmt.Sprintf("%s,%s", strconv.FormatInt(q.Dict.ParentId, 10), strconv.FormatInt(q.Dict.Id, 10))

	for _, staffId := range q.LiableStaffIds {
		var dp = workOrderModel.DevicePreset{
			ClassId: q.Dict.ParentId,
			DictId:  q.Dict.Id,
			Type:    util.DevicePresetForLiable,
			ItemId:  staffId,
		}
		q.Dict.DevicePresets = append(q.Dict.DevicePresets, dp)
	}

	err = (q.Dict).Add()
	if err != nil {
		log.Error("Add err == ", err)
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)

}

func (d *Device) EditDeviceCategory(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q common.Dict
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.Error("GetTopCorporationId == 0")
		return response.Error(rsp, response.Forbidden)
	}

	if q.Dict.DictKey == "" || q.Dict.DictValue == "" || q.Dict.Id == 0 {
		log.Error("q.Dict.DictKey == '' || q.Dict.DictValue == '' || q.Dict.Id == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	tx := model.DB().Begin()
	for _, preset := range q.Dict.DevicePresets {
		if preset.Option != "delete" && preset.ItemId == 0 {
			tx.Rollback()
			log.ErrorFields("q.DevicePresets create LiableStaffId missing", nil)
			return response.Error(rsp, response.ParamsMissing)
		}
		switch strings.ToLower(preset.Option) {
		case "create":
			dict := (&commonModel.Dict{}).FirstById(q.Id)
			preset.ClassId = dict.ParentId
			preset.Type = util.DevicePresetForLiable
			preset.DictId = q.Id
			err = preset.TransactionCreate(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("TransactionCreate err", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}

		case "edit":
			if preset.Id == 0 {
				tx.Rollback()
				log.ErrorFields("q.DevicePresets edit Id missing", nil)
				return response.Error(rsp, response.ParamsMissing)
			}
			err = preset.TransactionUpdates(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("TransactionUpdates err", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbUpdateFail)
			}
		case "delete":
			err = (&workOrderModel.DevicePreset{}).TransactionDelete(tx, preset.Id)
			if err != nil {
				log.Error("TransactionDelete err", err)
				return response.Error(rsp, response.DbDeleteFail)
			}
		}
	}

	// 查询该设备种类下的责任人数量
	// 如果为0 则修改失败
	count := (&workOrderModel.DevicePreset{}).CountLiable(tx, q.Id)
	if count == 0 {
		tx.Rollback()
		log.ErrorFields("liable count = 0", nil)
		return response.Error(rsp, response.DbSaveFail)
	}

	err = (q.Dict).TransactionEdit(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("TransactionEdit err ", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	tx.Commit()

	return response.Success(rsp, nil)

}

func (d *Device) EditPreset(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q common.Dict
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()

	for _, preset := range q.Dict.DevicePresets {
		option := strings.ToLower(preset.Option)
		if option != "delete" {
			if preset.ItemId == 0 {
				tx.Rollback()
				return response.Error(rsp, response.ParamsMissing)
			}
		}

		switch option {
		case "create":
			dict := (&commonModel.Dict{}).FirstById(q.Id)
			preset.ClassId = dict.ParentId
			preset.DictId = q.Id
			err = preset.TransactionCreate(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("TransactionCreate err", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}
		case "edit":
			if preset.Id == 0 {
				log.ErrorFields("q.DevicePresets edit Id missing", nil)
				return response.Error(rsp, response.ParamsMissing)
			}
			err = preset.TransactionUpdates(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("TransactionUpdates err", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbUpdateFail)
			}
		case "delete":
			if preset.Id == 0 {
				tx.Rollback()
				log.ErrorFields("q.DevicePresets delete Id missing", nil)
				return response.Error(rsp, response.ParamsMissing)
			}
			err = (&workOrderModel.DevicePreset{}).TransactionDelete(tx, preset.Id)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("TransactionDelete err", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbDeleteFail)
			}
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

type ListPresetParam struct {
	DeviceClassDictId    int64
	DeviceCategoryDictId int64
}

func (d *Device) ListPreset(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q ListPresetParam
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	listPreset, err := (&commonModel.Dict{}).ListPreset(q.DeviceClassDictId, q.DeviceCategoryDictId)
	if err != nil {
		log.Error("ListPreset err=", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	mapCorp := make(map[int64]*protoCorporation.CorporationItem) //map[机构id]机构信息
	mapStaff := make(map[int64]*staffProto.OetStaffItem)         //map[staffid]人员信息

	for i, dict := range listPreset {
		for j, preset := range dict.DevicePresets {
			if preset.Type == util.DevicePresetForLiable {
				var corporationId int64
				if staffItem, ok := mapStaff[preset.ItemId]; ok && staffItem != nil {
					listPreset[i].DevicePresets[j].StaffName = staffItem.Name
					corporationId = staffItem.CorporationId
				} else {
					oetStaffItem := rpc.GetStaffWithId(ctx, preset.ItemId)
					if oetStaffItem != nil {
						listPreset[i].DevicePresets[j].StaffName = oetStaffItem.Name
						mapStaff[preset.ItemId] = oetStaffItem
						corporationId = oetStaffItem.CorporationId
					}
				}
				if corp, ok := mapCorp[corporationId]; ok && corp != nil {
					listPreset[i].DevicePresets[j].Corporation = corp.Name
				} else {
					if corporationId > 0 {
						oetCorp := rpc.GetCorporationById(ctx, corporationId)
						if oetCorp != nil {
							listPreset[i].DevicePresets[j].Corporation = oetCorp.Name
							mapCorp[corporationId] = oetCorp
						}
					}
				}
			} else {
				deviceFactory := (&settingModel.DeviceFactory{}).FirstBy(preset.ItemId)
				listPreset[i].DevicePresets[j].ShotName = deviceFactory.ShotName
				listPreset[i].DevicePresets[j].Name = deviceFactory.Name
			}
		}
	}
	return response.Success(rsp, listPreset)
}

type DeviceDetail struct {
	workOrderModel.DeviceDetail
	workOrderModel.DeviceModelBatchCodeColumn

	Ids                        []int64 `json:"Ids"`
	LineIds                    []int64 `json:"LineIds"`
	CorporationIds             []int64 `json:"CorporationIds"`
	DeviceClassDictIds         []int64 `json:"DeviceClassDictIds"`
	DeviceCategoryDictIds      []int64 `json:"DeviceCategoryDictIds"`
	SupplierFactoryIds         []int64 `json:"SupplierFactoryIds"`
	ResponsibleDepartmentIds   []int64 `json:"ResponsibleDepartmentIds"`
	ExpireStatus               int64   `json:"ExpireStatus"`               //1在保  2过保
	DeviceModelName            string  `json:"Name"`                       //设备型号名称
	ExactAssociationObjectName string  `json:"ExactAssociationObjectName"` // 精确搜索关联对象
	AssociationStatus          int64   `json:"AssociationStatus"`
	DeviceCategoryDictId       int64   `json:"DeviceCategoryDictId"`
	CorporationId              int64   `json:"CorporationId"`
	model.Paginator

	ChildDeviceIds []int64 `json:"ChildDeviceIds"` //子设备ID
	BatchAddType   int64   `json:"BatchAddType"`   //1按数量 2按厂家编号
	BatchAddCount  int     `json:"BatchAddCount"`
	//BatchAddFileData            string                       `json:"BatchAddFileData"`
	ChildDeviceModelDetails []workOrderModel.ChildDevice `json:"ChildDeviceModelDetails"` //按数量批量新增时  有子设备时需要新增的子设备
	FactoryCodes            []DeviceBindChildDevice      `json:"FactoryCodes"`            //按厂家编号新增时  导入的厂家编号以及绑定的子设备
	VehicleUseStatus        int64                        `json:"VehicleUseStatus"`        //车辆使用状态 1使用中 2已报废
}

func (d *Device) AddDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DeviceDetail
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	if err = util.Validator().Struct(param.DeviceDetail); err != nil {
		log.ErrorFields("param validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	if param.BatchAddCount == 0 {
		param.BatchAddCount = 1
	}

	groupId := auth.User(ctx).GetTopCorporationId()

	// code 厂家简称+设备种类简称+采购日期（YYYYMMDD）+数量（四位数）
	// 查询型号信息
	var deviceModel workOrderModel.DeviceModel
	err = (&deviceModel).Find(param.DeviceDetail.DeviceModelId)
	if err != nil {
		log.Error("DeviceModel Find err =", err)
		return response.Error(rsp, response.ParamsInvalid)
	}
	// 查询厂家简称、设备种类简称
	category := (&commonModel.Dict{}).FirstById(deviceModel.DeviceCategoryDictId)
	if category.Id == 0 {
		log.ErrorFields("Dict.FirstById is empty", nil)
		return response.Error(rsp, response.DbSaveFail)
	}

	//查询批次信息
	batchCode := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(param.DeviceModelBatchCodeId)
	if batchCode.Id == 0 {
		log.ErrorFields("DeviceModel Find err =", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//查询品牌方信息
	factory := (&settingModel.DeviceFactory{}).FirstBy(deviceModel.BrandFactoryId)

	//查询品牌方
	codeStr := fmt.Sprintf("%s%s%s", factory.ShotName, category.DictValue, time.Time(batchCode.PurchaseAt).Format("20060102"))

	// 查询数据库中 厂家简称、设备种类简称、采购日期相同的 日期最近的 code
	var dd workOrderModel.DeviceDetail
	var startWith int64
	err = (&dd).GetByCode(groupId, codeStr)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			//	没有找到 从0001开始
			startWith = 1
		} else {
			log.Error("GetByCode err =", err.Error())
			return response.Error(rsp, response.DbQueryFail)
		}
	} else {
		parseInt, _ := strconv.ParseInt(dd.Code[len(dd.Code)-4:], 10, 64)
		startWith = parseInt + 1
	}

	param.DeviceDetail.GroupId = groupId

	tx := model.DB().Begin()

	if param.BatchAddType == 2 {
		var factoryCodeMap = make(map[string][]DeviceBindChildDevice)
		if len(param.FactoryCodes) > 0 {
			for i := range param.FactoryCodes {
				factoryCodeMap[param.FactoryCodes[i].FactoryCode] = append(factoryCodeMap[param.FactoryCodes[i].FactoryCode], param.FactoryCodes[i])
			}
		}

		for factoryCode, childDevices := range factoryCodeMap {
			param.DeviceDetail.Code = fmt.Sprintf("%s%04d", codeStr, startWith)
			qrcode, err := generateDeviceQrcode(param.DeviceDetail.Code, false)
			if err != nil {
				tx.Rollback()
				log.Error("二维码生成错误", err.Error())
				return response.Error(rsp, response.DbSaveFail)
			}
			param.DeviceDetail.QrCodeFilePath = qrcode
			param.DeviceDetail.FactoryCode = factoryCode

			//新增主设备
			err = param.DeviceDetail.TransactionCreate(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("DeviceDetail.TransactionCreate err", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}

			//关联子设备
			var childDeviceIds []int64
			for i := range childDevices {
				var child workOrderModel.ChildDevice
				if childDevices[i].ChildDeviceCode != "" {
					child = (&workOrderModel.ChildDevice{}).FirstByCode(childDevices[i].ChildDeviceCode)
				} else {
					child = (&workOrderModel.ChildDevice{}).FirstByFactoryCode(childDevices[i].ChildDeviceFactoryCode)
				}
				if child.Id == 0 {
					tx.Rollback()
					log.ErrorFields("ChildDevice is not found", map[string]interface{}{"code": childDevices[i].ChildDeviceCode, "factoryCode": childDevices[i].ChildDeviceFactoryCode})
					return response.Error(rsp, response.DbSaveFail)
				}
				if child.AssociationDeviceId > 0 || child.FreezeStatus != workOrderModel.ChildDeviceFreezeStatusForDefault {
					tx.Rollback()
					log.ErrorFields("ChildDevice is binding", map[string]interface{}{"code": childDevices[i].ChildDeviceCode, "factoryCode": childDevices[i].ChildDeviceFactoryCode})
					return response.Error(rsp, response.DbSaveFail)
				}

				childDeviceIds = append(childDeviceIds, child.Id)
			}

			err = (&workOrderModel.ChildDevice{}).TransactionUpdateAssociationDeviceIdByIds(tx, childDeviceIds, param.DeviceDetail.Id)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("ChildDevice.TransactionUpdateAssociationDeviceIdByIds error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}
			startWith++
		}
	} else {
		var sequenceMap = make(map[string]int64)
		for i := 0; i < param.BatchAddCount; i++ {
			param.DeviceDetail.Code = fmt.Sprintf("%s%04d", codeStr, startWith)
			qrcode, err := generateDeviceQrcode(param.DeviceDetail.Code, false)
			if err != nil {
				tx.Rollback()
				log.Error("二维码生成错误", err.Error())
				return response.Error(rsp, response.DbSaveFail)
			}
			param.DeviceDetail.QrCodeFilePath = qrcode

			//新增主设备
			err = param.DeviceDetail.TransactionCreate(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("DeviceDetail.TransactionCreate err", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}
			//非批量新增时 在有子设备时 需进行关联
			if param.BatchAddType != 1 && param.BatchAddCount == 1 && len(param.ChildDeviceIds) > 0 {
				err := (&workOrderModel.ChildDevice{}).TransactionUpdateAssociationDeviceIdByIds(tx, param.ChildDeviceIds, param.DeviceDetail.Id)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("ChildDevice.TransactionUpdateAssociationDeviceIdByIds err", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbSaveFail)
				}
			}
			//按数量批量新增  如果有子设备时  需要新增子设备，并将子设备绑定当前主设备
			if param.BatchAddType == 1 && len(param.ChildDeviceModelDetails) > 0 {
				for _, childDevice := range param.ChildDeviceModelDetails {
					if childDevice.ModelId == 0 || childDevice.BatchCode == "" {
						tx.Rollback()
						log.ErrorFields("ChildDeviceModelDetails required params missing", nil)
						return response.Error(rsp, response.ParamsMissing)
					}
					//获取型号、批次下最大的设备编号序号
					seqMapKey := fmt.Sprintf("%v_%s", childDevice.ModelId, childDevice.BatchCode)
					var currentSeq int64
					if _, ok := sequenceMap[seqMapKey]; ok {
						currentSeq = sequenceMap[seqMapKey]
					} else {
						currentSeq = (&workOrderModel.ChildDevice{}).GetMaxCodeSeq(childDevice.ModelId, childDevice.BatchCode)
					}
					sequenceMap[seqMapKey] = currentSeq + 1

					//查询设备型号简称
					cate := (&workOrderModel.ChildDeviceCate{}).FirstById(childDevice.ModelId)
					childDevice.CateId = cate.ParentId
					childDevice.TopCorporationId = auth.User(ctx).GetTopCorporationId()
					childDevice.CodeSeq = currentSeq + 1
					childDevice.Code = fmt.Sprintf("%s%s%05d", cate.ShotName, childDevice.BatchCode, currentSeq+1)
					qrcodePath, err := generateDeviceQrcode(childDevice.Code, true)
					if err != nil {
						tx.Rollback()
						log.ErrorFields("ChildDevice generateDeviceQrcode error", map[string]interface{}{"err": err})
						return response.Error(rsp, response.FAIL)
					}
					childDevice.QrCodePath = qrcodePath
					childDevice.AssociationDeviceId = param.DeviceDetail.Id
					err = childDevice.TransactionCreate(tx)
					if err != nil {
						tx.Rollback()
						log.ErrorFields("ChildDevice TransactionCreate error", map[string]interface{}{"err": err})
						return response.Error(rsp, response.FAIL)
					}
				}
			}
			startWith++
		}
	}
	tx.Commit()

	return response.Success(rsp, nil)
}

type DeviceDetailListItem struct {
	workOrderModel.DeviceDetail
	workOrderModel.DeviceModelColumn
	workOrderModel.DeviceModelBatchCodeColumn

	LineId       int64                        `json:"LineId"` // 如果是车载设备 该字段为绑定车辆的线路id, 否则该字段值为空
	Line         string                       `json:"Line"`
	Corporation  string                       `json:"Corporation"` // 归属机构
	ChildDevices []workOrderModel.ChildDevice `json:"ChildDevices"`
}

func (d *Device) ExportListDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return ListDetail(ctx, req, rsp)
}

func (d *Device) ListDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return ListDetail(ctx, req, rsp)
}

func ListDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q DeviceDetail
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	groupId := auth.User(ctx).GetTopCorporationId()

	var (
		list       []workOrderModel.DeviceDetail
		totalCount int64
	)

	if len(q.LineIds) == 0 && len(q.CorporationIds) == 0 {
		list, totalCount, err = (&workOrderModel.DeviceDetail{}).List(groupId, q.AssociationObjectId, q.AssociationStatus, q.Code, q.FactoryCode, q.AssociationObjectName, q.ExactAssociationObjectName,
			q.DeviceClassDictIds, q.DeviceCategoryDictIds, q.SupplierFactoryIds, q.DeviceModelName,
			q.BatchCode, q.ExpireStatus, q.MaintainerFactoryId, time.Time(q.PurchaseAt), time.Time(q.ExpireAt), q.Paginator)
		if err != nil {
			log.Error("List err =", err)
			return response.Error(rsp, response.DbQueryFail)
		}
	} else {
		vehicleIds := rpc.GetVehicleIdsByLineIdsAndCorporationIds(ctx, groupId, q.LineIds, q.CorporationIds)
		items, _, err := (&workOrderModel.DeviceDetail{}).List(groupId, q.AssociationObjectId, q.AssociationStatus, q.Code, q.FactoryCode, q.AssociationObjectName, q.ExactAssociationObjectName,
			q.DeviceClassDictIds, q.DeviceCategoryDictIds, q.SupplierFactoryIds, q.DeviceModelName,
			q.BatchCode, q.ExpireStatus, q.MaintainerFactoryId, time.Time(q.PurchaseAt), time.Time(q.ExpireAt), model.Paginator{Offset: 0, Limit: 99999})

		if err != nil {
			log.Error("List err =", err)
			return response.Error(rsp, response.DbQueryFail)
		}

		for _, detail := range items {
			if detail.AssociationObjectType == util.DeviceAssociationObjectForVehicle {
				for _, vehicleId := range vehicleIds {
					if detail.AssociationObjectId == vehicleId {
						list = append(list, detail)
					}
				}
			}
		}
		totalCount = int64(len(list))
		// 分页
		if q.Limit+q.Offset >= int(totalCount) {
			if q.Offset < int(totalCount) {
				list = list[q.Offset:]
			}
		} else {
			list = list[q.Offset:(q.Offset + q.Limit)]
		}
	}

	var results []DeviceDetailListItem

	for _, detail := range list {
		item := DeviceDetailListItem{
			DeviceDetail: detail,
		}

		//查询型号
		deviceModel := (&workOrderModel.DeviceModel{}).FirstBy(detail.DeviceModelId)
		item.DeviceModelColumn = deviceModel.DeviceModelColumn

		//查询批次信息
		batchCode := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(detail.DeviceModelBatchCodeId)
		item.DeviceModelBatchCodeColumn = batchCode.DeviceModelBatchCodeColumn

		isUsed, err := (&workOrderModel.WorkOrder{}).IsUsed(detail.Id)
		if err != nil {
			log.Error("IsUsed err =", err)
			return response.Error(rsp, response.DbQueryFail)
		}
		if isUsed {
			item.DeviceDetail.IsUsed = util.StatusForTrue
		} else {
			item.DeviceDetail.IsUsed = util.StatusForFalse
		}

		// 重写拼接图片路径
		item.QrCodeFilePath = fmt.Sprintf("%s%s", config.Config.StaticFileHttpPrefix, item.QrCodeFilePath)

		if item.AssociationObjectType == util.DeviceAssociationObjectForVehicle && item.AssociationObjectName != "" {
			vehicleItem := rpc.GetVehicleWithId(ctx, item.AssociationObjectId)
			if vehicleItem != nil {
				item.Corporation = rpc.GetCorporationNameById(ctx, vehicleItem.SonCorporationId)
			}
		}
		if item.AssociationObjectType == util.DeviceAssociationObjectForParking && item.AssociationObjectName != "" {
			parkingItem := rpc.GetParkingWithId(ctx, item.AssociationObjectId)
			if parkingItem != nil {
				item.Corporation = rpc.GetCorporationNameById(ctx, parkingItem.CorporationId)
			}
		}
		if item.AssociationObjectType == util.DeviceAssociationObjectForStation && item.AssociationObjectName != "" {
			oetStationItem := rpc.GetStationWithId(ctx, item.AssociationObjectId)
			if oetStationItem != nil {
				item.Corporation = rpc.GetCorporationNameById(ctx, oetStationItem.CorporationId)
			}
		}
		childDevices := (&workOrderModel.ChildDevice{}).GetByAssociationDeviceId(item.Id)
		for i := range childDevices {
			mod := (&workOrderModel.ChildDeviceCate{}).FirstById(childDevices[i].ModelId)
			childDevices[i].ModelName = mod.Name
		}

		item.ChildDevices = childDevices
		results = append(results, item)
	}

	data := map[string]interface{}{
		"Items":      results,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)
}

// ListDetailPurchase 相同设备种类采购日期列表
func (d *Device) ListDetailPurchase(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q DeviceDetail
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	groupId := auth.User(ctx).GetTopCorporationId()

	batchCode := (&workOrderModel.DeviceModelBatchCode{}).GetPurchaseAt(groupId, q.DeviceCategoryDictId)
	if err != nil {
		log.Error("DeviceModelBatchCode.GetPurchaseAt err =", err)
		return response.Error(rsp, response.DbQueryFail)
	}
	data := map[string]interface{}{
		"Items":      batchCode,
		"TotalCount": len(batchCode),
	}

	return response.Success(rsp, data)
}

func (d *Device) EditDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q DeviceDetail
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()
	err = (&q.DeviceDetail).TransactionEdit(tx)
	if err != nil {
		tx.Rollback()
		log.Error("Edit err == ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	//更新子设备
	for i := range q.ChildDeviceModelDetails {
		childDevice := (&ChildDevice{}).FirstById(q.ChildDeviceModelDetails[i].Id)
		if childDevice.AssociationDeviceId > 0 {
			err = q.ChildDeviceModelDetails[i].TransactionUpdate(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("ChildDevice.TransactionUpdate err ", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbUpdateFail)
			}
		} else {
			err = q.ChildDeviceModelDetails[i].TransactionUpdateAssociationDeviceIdByIds(tx, []int64{childDevice.Id}, q.DeviceDetail.Id)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("ChildDevice.TransactionUpdateAssociationDeviceIdByIds err ", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbUpdateFail)
			}
		}
	}
	tx.Commit()

	return response.Success(rsp, nil)
}

type ImportParam struct {
	FileData string `json:"FileData"`
}

func (d *Device) ImportDetailUpdate(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param ImportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			_ = response.Error(rsp, response.FAIL)
		}
		return
	}()

	// 0设备编号 1厂家编号 2批次号
	sheet := excelFile.Sheets[0]
	var importFailDevices []string
	var successCount int64

	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 3 {
			continue
		}

		if row.Cells[0].String() == "" || row.Cells[2].String() == "" {
			importFailDevices = append(importFailDevices, row.Cells[0].String())
			continue
		}

		//根据设备编号查询设备详情
		var deviceDetail workOrderModel.DeviceDetail
		err := deviceDetail.GetByCode(auth.User(ctx).GetTopCorporationId(), row.Cells[0].String())
		if err != nil {
			importFailDevices = append(importFailDevices, row.Cells[0].String())
			log.ErrorFields("deviceDetail.GetByCode err", map[string]interface{}{"err": err})
			continue
		}

		//查询设备详情对应的型号
		deviceModel := (&workOrderModel.DeviceModel{}).FirstBy(deviceDetail.DeviceModelId)
		if deviceModel.Id == 0 {
			importFailDevices = append(importFailDevices, row.Cells[0].String())
			log.ErrorFields("DeviceModel.FirstBy err", map[string]interface{}{"err": err})
			continue
		}

		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 0:
				deviceDetail.Code = row.Cells[0].String()
			case 1:
				deviceDetail.FactoryCode = row.Cells[1].String()
			case 2:
				//根据型号查询 此型号下有没有这个批次号
				batchCode := (&workOrderModel.DeviceModelBatchCode{}).FirstByBatchCode(deviceModel.Id, row.Cells[2].String())

				if batchCode.Id == 0 {
					importFailDevices = append(importFailDevices, row.Cells[0].String())
					log.ErrorFields("row.Cells[2].GetTime err", map[string]interface{}{"err": err})
					continue
				}
				deviceDetail.DeviceModelBatchCodeId = batchCode.Id
			}
		}

		//更新设备详情
		if err := deviceDetail.Edit(); err != nil {
			importFailDevices = append(importFailDevices, deviceDetail.Code)
			log.ErrorFields("deviceDetail.Edit err", map[string]interface{}{"err": err})
			continue
		}
		successCount++
	}

	return response.Success(rsp, map[string]interface{}{
		"SuccessCount": successCount,
		"FailCount":    len(importFailDevices),
		"FailItems":    importFailDevices,
	})
}

func (d *Device) DeleteDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q DeviceDetail
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	for i := range q.Ids {
		var detail workOrderModel.DeviceDetail
		_ = detail.FindBy(q.Ids[i])
		if detail.AssociationObjectId > 0 {
			return response.Error(rsp, response.DbDeleteFail)
		}
	}

	tx := model.DB().Begin()
	err = (&workOrderModel.DeviceDetail{}).TransactionDelete(tx, q.Ids)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("DeviceDetail.TransactionDelete err ", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	//解绑子设备
	err = (&workOrderModel.ChildDevice{}).TransactionUnBind(tx, q.Ids)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("ChildDevice.TransactionUnBind err ", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

func (d *Device) GetDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q DeviceDetail
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId == 0 {
		log.ErrorFields("topCorporationId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.Forbidden)
	}

	deviceDetail := (&workOrderModel.DeviceDetail{}).FirstByCode(topCorporationId, q.Code)
	if deviceDetail.Id == 0 {
		log.Error("DeviceDetail.FirstByCode err =", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	// 重写拼接图片路径
	deviceDetail.QrCodeFilePath = fmt.Sprintf("%s%s", config.Config.StaticFileHttpPrefix, deviceDetail.QrCodeFilePath)

	item := DeviceDetailListItem{
		DeviceDetail: deviceDetail,
	}

	//查询型号
	deviceModel := (&workOrderModel.DeviceModel{}).FirstBy(deviceDetail.DeviceModelId)
	item.DeviceModelColumn = deviceModel.DeviceModelColumn

	//查询批次信息
	batchCode := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(deviceDetail.DeviceModelBatchCodeId)
	item.DeviceModelBatchCodeColumn = batchCode.DeviceModelBatchCodeColumn

	if deviceDetail.AssociationObjectType == util.DeviceAssociationObjectForVehicle && deviceDetail.AssociationObjectName != "" {
		vehicleItem := rpc.GetVehicleWithLicense(ctx, &protoVehicle.GetVehicleWithLicenseRequest{
			License:       deviceDetail.AssociationObjectName,
			CorporationId: topCorporationId,
		})
		if vehicleItem != nil {
			lineItem, _ := rpc.GetLineWithId(ctx, vehicleItem.LineId)
			if lineItem != nil {
				item.LineId = lineItem.Id
				item.Line = lineItem.Name
			}
			item.Corporation = rpc.GetCorporationNameById(ctx, vehicleItem.SonCorporationId)
		}
	}
	if deviceDetail.AssociationObjectType == util.DeviceAssociationObjectForParking && deviceDetail.AssociationObjectName != "" {
		parkingItem := rpc.GetParkingWithId(ctx, deviceDetail.AssociationObjectId)
		if parkingItem != nil {
			item.Corporation = rpc.GetCorporationNameById(ctx, parkingItem.CorporationId)
		}
	}
	if deviceDetail.AssociationObjectType == util.DeviceAssociationObjectForStation && deviceDetail.AssociationObjectName != "" {
		oetStationItem := rpc.GetStationWithId(ctx, deviceDetail.AssociationObjectId)
		if oetStationItem != nil {
			item.Corporation = rpc.GetCorporationNameById(ctx, oetStationItem.CorporationId)
		}
	}

	childDevices := (&workOrderModel.ChildDevice{}).GetByAssociationDeviceId(deviceDetail.Id)
	for i := range childDevices {
		mod := (&workOrderModel.ChildDeviceCate{}).FirstById(childDevices[i].ModelId)
		childDevices[i].ModelName = mod.Name
	}

	item.ChildDevices = childDevices

	return response.Success(rsp, item)
}

func (d *Device) ExportQrcodeDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q DeviceDetail
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(q.Ids) == 0 {
		log.Error("len(q.Ids) == 0", err.Error())
		return response.Error(rsp, response.ParamsMissing)
	}

	// 找出所有的设备二维码的磁盘地址 /mnt/www/webroot/.../xxx.png
	// 将所有二维码打包压缩
	// 返回该压缩包url https://xxxxxx/webroot/.../xxx.zip

	// 路径前缀 /mnt/www/
	prefixPath := config.Config.AbsDirPath

	// 相对路径 webroot/erp/...
	relativePath := fmt.Sprintf(`%s/erp/%v`, config.Config.WebRoot, "device_qrcodes/")

	// 完整路径 /mnt/www/webroot/erp/...
	fullPath := fmt.Sprintf("%s%s", prefixPath, relativePath)

	err = util.VerifyMkdirExistAndCreate(fullPath)
	if err != nil {
		fmt.Println("VerifyMkdirExistAndCreate error ==", err)
		return response.Error(rsp, response.DbSaveFail)
	}

	newUuid := uuid.NewV4()
	uuidStr := newUuid.String()
	newFileName := fmt.Sprintf("%s%s", uuidStr, ".zip")

	zipFilePath := fmt.Sprintf("%s%s", fullPath, newFileName)

	var files []string   // 待打包压缩的图片地址 /mnt/www/webroot/.../xxx.png
	var asNames []string // 图片重命名

	for i, id := range q.Ids {
		var dd workOrderModel.DeviceDetail
		err = (&dd).FindBy(id)
		if err != nil {
			log.Error("FindBy err =", err, "Id ==", id)
			continue
		}

		if dd.QrCodeFilePath != "" {
			//
			files = append(files, fmt.Sprintf("%s%s", config.Config.AbsDirPath, dd.QrCodeFilePath))

			// 序号_设备编号_关联对象
			asNames = append(asNames, fmt.Sprintf("%d_%s_%s%s", i, dd.Code, dd.AssociationObjectName, util.Default_Suffix))
		}

	}

	err = util.FilesToZip(files, zipFilePath, asNames)
	if err != nil {
		fmt.Println("VerifyMkdirExistAndCreate error ==", err)
		return response.Error(rsp, response.DbSaveFail)
	}

	// 压缩包url
	zipUrl := fmt.Sprintf("%s%s%s", config.Config.StaticFileHttpPrefix, relativePath, newFileName)

	return response.Success(rsp, zipUrl)
}

func (d *Device) PrintQrcodeDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	// 空接口 前端作权限使用
	return response.Success(rsp, nil)
}

type BatchAddAssociationReq struct {
	Association []BatchAddAssociationReqItem
}

type BatchAddAssociationReqItem struct {
	AssociationObjectType int64
	AssociationObjectId   int64
	AssociationObjectName string
	DetailIds             []int64
	DetailIdStrs          []string // 设备明细id 兼容wx小程序大整型
}

func (d *Device) BatchAddAssociation(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q BatchAddAssociationReq
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	for _, item := range q.Association {

		// compatible with bigint
		if len(item.DetailIdStrs) > 0 {
			// !--------------------------------
			//  >>>>>> 修改循环局部变量 item <<<<<<
			// !--------------------------------
			item.DetailIds = util.StringSliceToInt64Slice(item.DetailIdStrs)
		}

		err = (&workOrderModel.DeviceDetail{}).Bind(item.DetailIds, item.AssociationObjectType, item.AssociationObjectId, item.AssociationObjectName)
		if err != nil {
			log.Error("Edit err == ", err, "param ==", q, "item ==", item)
			return response.Error(rsp, response.DbUpdateFail)
		}
	}

	return response.Success(rsp, nil)
}

type BatchAssociationObjectParam struct {
	Items []BatchAssociationObjectItem `json:"Items"`
}
type BatchAssociationObjectItem struct {
	AssociationObjectType int64
	AssociationObjectId   int64
	AssociationObjectName string
	DeviceId              int64
}

func (d *Device) BatchAssociationObject(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param BatchAssociationObjectParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	for _, item := range param.Items {
		err = (&workOrderModel.DeviceDetail{}).Bind([]int64{item.DeviceId}, item.AssociationObjectType, item.AssociationObjectId, item.AssociationObjectName)
		if err != nil {
			log.Error("Edit err == ", err, "param ==", param, "item ==", item)
			return response.Error(rsp, response.DbUpdateFail)
		}
	}

	return response.Success(rsp, nil)
}

type ListAssociationRsp struct {
	AssociationObjectName string       `json:"AssociationObjectName"`
	AssociationObjectId   int64        `json:"AssociationObjectId"`
	Corporation           string       `json:"Corporation"`
	ItemCode              string       `json:"ItemCode"`
	StationDirection      int64        `json:"StationDirection"` // 站点方向，1:东向西,2:西向东,3:南向北,4:北向南
	VehicleUseStatus      int64        `json:"VehicleUseStatus"` //车辆状态  1使用中  2已报废
	Devices               []DeviceItem `json:"Devices"`
}

type DeviceItem struct {
	workOrderModel.DeviceDetail

	DeviceCategoryDictId    int64
	DeviceCategoryDictKey   string
	DeviceCategoryDictIdStr string // *设备种类字典id 兼容wx小程序大整型
	BatchCode               string
	PurchaseAt              model.LocalTime
}

type AssociationObjectItem struct {
	Id   int64
	Name string
}

func (d *Device) ImportDeviceAssociation(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param ImportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			_ = response.Error(rsp, response.FAIL)
		}
		return
	}()

	//车辆
	vehicleItems, _ := rpc.GetVehiclesWithTopCorporationId(ctx, auth.User(ctx).GetTopCorporationId())
	var vehicleMap = make(map[string]AssociationObjectItem)
	for i := range vehicleItems {
		vehicleMap[vehicleItems[i].License] = AssociationObjectItem{
			Id:   vehicleItems[i].Id,
			Name: vehicleItems[i].License,
		}
	}

	//场站
	parkingItems := rpc.GetListWithCorpId(ctx, auth.User(ctx).GetTopCorporationId(), "", 0)
	var parkingMap = make(map[string]AssociationObjectItem)
	for i := range parkingItems {
		parkingMap[parkingItems[i].Code] = AssociationObjectItem{
			Id:   parkingItems[i].Id,
			Name: parkingItems[i].Name,
		}
	}

	//站台
	stationItems := rpc.GetStationsWithOption(ctx, auth.User(ctx).GetTopCorporationId(), "")
	var stationMap = make(map[string]AssociationObjectItem)
	for i := range stationItems {
		stationMap[stationItems[i].Code] = AssociationObjectItem{
			Id:   stationItems[i].Id,
			Name: stationItems[i].Name,
		}
	}
	// 0设备编号 2关联对象
	sheet := excelFile.Sheets[0]
	var importFailDevices []string
	var successCount int64

	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 2 {
			continue
		}

		if row.Cells[0].String() == "" || row.Cells[1].String() == "" {
			importFailDevices = append(importFailDevices, row.Cells[0].String())
			continue
		}

		//根据设备编号查询设备详情
		var deviceDetail workOrderModel.DeviceDetail
		err := deviceDetail.GetByCode(auth.User(ctx).GetTopCorporationId(), row.Cells[0].String())
		if err != nil {
			importFailDevices = append(importFailDevices, row.Cells[0].String())
			log.ErrorFields("deviceDetail.GetByCode err", map[string]interface{}{"err": err})
			continue
		}
		if deviceDetail.AssociationObjectId > 0 {
			importFailDevices = append(importFailDevices, row.Cells[0].String())
			log.ErrorFields("设备已经绑定了对象，不可重复绑定", map[string]interface{}{"code": row.Cells[0].String()})
			continue
		}

		//根据设备型号查询设备种类
		var deviceModel workOrderModel.DeviceModel
		err = deviceModel.Find(deviceDetail.DeviceModelId)
		if err != nil {
			importFailDevices = append(importFailDevices, row.Cells[0].String())
			log.ErrorFields("deviceModel.Find err", map[string]interface{}{"err": err})
			continue
		}

		//获取设备大类
		classDict := (&commonModel.Dict{}).FirstById(deviceModel.DeviceClassDictId)
		if err != nil {
			importFailDevices = append(importFailDevices, row.Cells[0].String())
			log.ErrorFields("Dict.FirstById err", map[string]interface{}{"err": err})
			continue
		}
		var associationType int64
		var associationId int64
		var associationName string
		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 1:
				//车载
				if classDict.ObjectType == util.DeviceAssociationObjectForVehicle {
					if _, ok := vehicleMap[row.Cells[1].String()]; !ok {
						importFailDevices = append(importFailDevices, row.Cells[0].String())
						log.ErrorFields("关联对象没找到", map[string]interface{}{"关联对象": row.Cells[0].String()})
						continue
					}
					associationType = util.DeviceAssociationObjectForVehicle
					associationId = vehicleMap[row.Cells[1].String()].Id
					associationName = vehicleMap[row.Cells[1].String()].Name
				}
				//场站
				if classDict.ObjectType == util.DeviceAssociationObjectForParking {
					if _, ok := parkingMap[row.Cells[1].String()]; !ok {
						importFailDevices = append(importFailDevices, row.Cells[0].String())
						log.ErrorFields("关联对象没找到", map[string]interface{}{"关联对象": row.Cells[0].String()})
						continue
					}
					associationType = util.DeviceAssociationObjectForParking
					associationId = parkingMap[row.Cells[1].String()].Id
					associationName = parkingMap[row.Cells[1].String()].Name
				}
				//站台
				if classDict.ObjectType == util.DeviceAssociationObjectForStation {
					if _, ok := stationMap[row.Cells[1].String()]; !ok {
						importFailDevices = append(importFailDevices, row.Cells[0].String())
						log.ErrorFields("关联对象没找到", map[string]interface{}{"关联对象": row.Cells[0].String()})
						continue
					}
					associationType = util.DeviceAssociationObjectForStation
					associationId = stationMap[row.Cells[1].String()].Id
					associationName = stationMap[row.Cells[1].String()].Name
				}
			}
		}
		err = (&workOrderModel.DeviceDetail{}).Bind([]int64{deviceDetail.Id}, associationType, associationId, associationName)
		if err != nil {
			importFailDevices = append(importFailDevices, row.Cells[0].String())
			log.ErrorFields("DeviceDetail.Bind error", map[string]interface{}{"err": err})
			continue
		}
		successCount++
	}

	return response.Success(rsp, map[string]interface{}{
		"SuccessCount": successCount,
		"FailCount":    len(importFailDevices),
		"FailItems":    importFailDevices,
	})
}

func (d *Device) ExportListAssociation(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return ListAssociation(ctx, req, rsp)
}

func (d *Device) ListAssociation(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return ListAssociation(ctx, req, rsp)
}

func ListAssociation(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DeviceDetail
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	groupId := auth.User(ctx).GetTopCorporationId()

	// 获取用户userid
	userId := auth.User(ctx).GetUserId()

	if param.CorporationId == 0 {
		param.CorporationId = groupId
	}

	fmt.Printf("param.CorporationId: %+v \n", param.CorporationId)

	if (time.Time(param.PurchaseAt).Unix() > 0 || param.BatchCode != "" || param.Code != "") && param.AssociationStatus == util.StatusForFalse {
		log.ErrorFields("ListAssociation objectIds is empty", nil)
		return response.Success(rsp, map[string]interface{}{
			"Items":      nil,
			"TotalCount": 0,
		})
	}

	//已经绑定过设备的对象ID
	objectIds := (&workOrderModel.DeviceDetail{}).GetAssociationObjectIds(groupId, param.Code, param.AssociationObjectName,
		param.DeviceClassDictIds, param.DeviceCategoryDictIds, param.BatchCode, time.Time(param.PurchaseAt))

	var exceptObjectIds []int64
	if param.AssociationStatus > 0 {
		//allAssociationObjectIds := (&workOrderModel.DeviceDetail{}).GetAssociationObjectIds(groupId, "", param.AssociationObjectName, param.DeviceClassDictIds, param.DeviceCategoryDictIds, "", time.Time{})
		if param.AssociationStatus == util.StatusForTrue && len(objectIds) == 0 {
			return response.Success(rsp, map[string]interface{}{
				"Items":      nil,
				"TotalCount": 0,
			})
		}

		if param.AssociationStatus == util.StatusForFalse {
			exceptObjectIds = objectIds
			objectIds = []int64{}
		}
	} else {
		objectIds = []int64{}
	}

	log.ErrorFields("ListAssociation objectIds:", map[string]interface{}{"objectIds": objectIds, "exceptObjectIds": exceptObjectIds})

	var results []ListAssociationRsp
	var count int64
	switch param.AssociationObjectType {
	case util.DeviceAssociationObjectForVehicle:
		oetVehicleItems, totalCount := rpc.GetVehiclesWithOption(ctx, &protoVehicle.GetVehiclesWithOptionRequest{
			CorporationId:   param.CorporationId,
			License:         param.AssociationObjectName,
			LineIds:         param.LineIds,
			VehicleIds:      objectIds,
			NotVehicleIds:   exceptObjectIds,
			Offset:          int64(param.Offset),
			Limit:           int64(param.Limit),
			UseStatus:       param.VehicleUseStatus,
			IsShowChildNode: true,
			UserId:          userId,
		})
		for i := range oetVehicleItems {
			var result = ListAssociationRsp{
				AssociationObjectId:   oetVehicleItems[i].Id,
				AssociationObjectName: oetVehicleItems[i].License,
				ItemCode:              oetVehicleItems[i].Code,
				Corporation:           rpc.GetCorporationNameById(ctx, oetVehicleItems[i].SonCorporationId),
				VehicleUseStatus:      oetVehicleItems[i].UseStatus,
			}

			//查询绑定的设备
			deviceDetails, _ := (&workOrderModel.DeviceDetail{}).GetByAssociationObjectId(groupId, util.DeviceAssociationObjectForVehicle, oetVehicleItems[i].Id, param.DeviceCategoryDictIds)
			var devices []DeviceItem
			for j := range deviceDetails {
				var deviceModel workOrderModel.DeviceModel
				_ = deviceModel.Find(deviceDetails[j].DeviceModelId)
				deviceBatch := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(deviceDetails[j].DeviceModelBatchCodeId)
				devices = append(devices, DeviceItem{
					DeviceDetail:            deviceDetails[j],
					DeviceCategoryDictId:    deviceModel.DeviceCategoryDictId,
					DeviceCategoryDictKey:   deviceModel.DeviceCategoryDictKey,
					DeviceCategoryDictIdStr: fmt.Sprintf("%v", deviceModel.DeviceCategoryDictId),
					BatchCode:               deviceBatch.BatchCode,
					PurchaseAt:              deviceBatch.PurchaseAt,
				})
			}

			result.Devices = devices

			results = append(results, result)
		}
		count = totalCount
	case util.DeviceAssociationObjectForParking:
		parkingItems, totalCount := rpc.GetParkingListWithOpt(ctx, &protoParking.GetListWithCorpIdRequest{
			CorporationId: groupId,
			ParkingType:   1,
			Name:          param.AssociationObjectName,
			ParkingIds:    objectIds,
			NotParkingIds: exceptObjectIds,
			Offset:        int64(param.Offset),
			Limit:         int64(param.Limit),
		})
		for i := range parkingItems {
			var result = ListAssociationRsp{
				AssociationObjectId:   parkingItems[i].Id,
				AssociationObjectName: parkingItems[i].Name,
				ItemCode:              parkingItems[i].Code,
				//Corporation:           rpc.GetCorporationNameById(ctx, parkingItems[i].CorporationId),
			}

			//查询绑定的设备
			deviceDetails, _ := (&workOrderModel.DeviceDetail{}).GetByAssociationObjectId(groupId, util.DeviceAssociationObjectForParking, parkingItems[i].Id, param.DeviceCategoryDictIds)
			var devices []DeviceItem
			for j := range deviceDetails {
				var deviceModel workOrderModel.DeviceModel
				_ = deviceModel.Find(deviceDetails[j].DeviceModelId)
				deviceBatch := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(deviceDetails[j].DeviceModelBatchCodeId)
				devices = append(devices, DeviceItem{
					DeviceDetail:            deviceDetails[j],
					DeviceCategoryDictId:    deviceModel.DeviceCategoryDictId,
					DeviceCategoryDictKey:   deviceModel.DeviceCategoryDictKey,
					DeviceCategoryDictIdStr: fmt.Sprintf("%v", deviceModel.DeviceCategoryDictId),
					BatchCode:               deviceBatch.BatchCode,
					PurchaseAt:              deviceBatch.PurchaseAt,
				})
			}

			result.Devices = devices

			results = append(results, result)
		}
		count = totalCount
	case util.DeviceAssociationObjectForStation:
		// 获取 所有站点
		stationItems, totalCount := rpc.GetStationsWithOpt(ctx, &protoStation.GetStationsWithOptionRequest{
			CorporationId: groupId,
			StationIds:    objectIds,
			NotStationIds: exceptObjectIds,
			Name:          param.AssociationObjectName,
			Offset:        int64(param.Offset),
			Limit:         int64(param.Limit),
		})

		for i := range stationItems {
			var result = ListAssociationRsp{
				AssociationObjectId:   stationItems[i].Id,
				AssociationObjectName: stationItems[i].Name,
				ItemCode:              stationItems[i].Code,
				StationDirection:      stationItems[i].Direction,
				//Corporation:           rpc.GetCorporationNameById(ctx, stationItems[i].CorporationId),
			}

			//查询绑定的设备
			deviceDetails, _ := (&workOrderModel.DeviceDetail{}).GetByAssociationObjectId(groupId, util.DeviceAssociationObjectForStation, stationItems[i].Id, param.DeviceCategoryDictIds)
			var devices []DeviceItem
			for j := range deviceDetails {
				var deviceModel workOrderModel.DeviceModel
				_ = deviceModel.Find(deviceDetails[j].DeviceModelId)
				deviceBatch := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(deviceDetails[j].DeviceModelBatchCodeId)
				devices = append(devices, DeviceItem{
					DeviceDetail:            deviceDetails[j],
					DeviceCategoryDictId:    deviceModel.DeviceCategoryDictId,
					DeviceCategoryDictKey:   deviceModel.DeviceCategoryDictKey,
					DeviceCategoryDictIdStr: fmt.Sprintf("%v", deviceModel.DeviceCategoryDictId),
					BatchCode:               deviceBatch.BatchCode,
					PurchaseAt:              deviceBatch.PurchaseAt,
				})
			}

			result.Devices = devices

			results = append(results, result)
		}
		count = totalCount
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": count,
	})
}

func (d *Device) Dissociation(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q DeviceDetail
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = (&workOrderModel.DeviceDetail{}).Unbind(q.Ids)
	if err != nil {
		log.Error("Unbind err == ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

func UpdateQrcode() {
	//time.Sleep(120 * time.Second)
	// 找出所有明细二维码为空的数据
	var details []workOrderModel.DeviceDetail
	err := model.DB().Model(&workOrderModel.DeviceDetail{}).Where("QrCodeFilePath = ''").Scan(&details).Error
	if err != nil {
		log.ErrorFields("get detail error", map[string]interface{}{"err": err})
		return
	}

	for _, detail := range details {
		// 相对路径 webroot/erp/...
		relativePath := fmt.Sprintf(`%s/erp/%v/%s/`, config.Config.WebRoot, "device_qrcodes", time.Time(detail.CreatedAt).Format("20060102"))

		// 完整路径 /mnt/www/webroot/erp/...
		fullPath := fmt.Sprintf("%s%s", config.Config.AbsDirPath, relativePath)

		// 生成二维码
		qrcodeParam := fmt.Sprintf("%s?DeviceCode=%s", config.Config.DeviceQrcodeUrl, detail.Code)
		newUuid := uuid.NewV4()
		uuidStr := newUuid.String()
		newFileName := fmt.Sprintf("%s%s", uuidStr, util.Default_Suffix)

		err := util.SaveQrcode(fullPath, newFileName, qrcodeParam)
		if err != nil {
			log.Error("二维码生成错误", err.Error())
			continue
		}

		_ = model.DB().Model(&workOrderModel.DeviceDetail{}).Where("Id=?", detail.Id).Update("QrCodeFilePath", fmt.Sprintf(`%s%s`, relativePath, newFileName)).Error

	}
}

// 生成设备二维码
func generateDeviceQrcode(deviceCode string, isChildDevice bool) (string, error) {
	// 相对路径 webroot/erp/...
	relativePath := fmt.Sprintf(`%s/erp/%v/%s/`, config.Config.WebRoot, "device_qrcodes", time.Now().Format("20060102"))

	// 完整路径 /mnt/www/webroot/erp/...
	fullPath := fmt.Sprintf("%s%s", config.Config.AbsDirPath, relativePath)
	qrcodeLink := fmt.Sprintf("%s?DeviceCode=%s", config.Config.DeviceQrcodeUrl, deviceCode)

	if isChildDevice {
		qrcodeLink = fmt.Sprintf("%s&DeviceType=%v", qrcodeLink, 2)
	}

	newFileName := fmt.Sprintf("%s%s", uuid.NewV4().String(), util.Default_Suffix)

	err := util.SaveQrcode(fullPath, newFileName, qrcodeLink)
	if err != nil {
		log.ErrorFields("二维码生成错误", map[string]interface{}{"err": err})
		return "", err
	}

	return fmt.Sprintf(`%s%s`, relativePath, newFileName), nil
}

type DeviceDetailTmp struct {
	model.PkId
	GroupId                int64 `json:"GroupId"      gorm:"column:groupid;type:bigint;comment:集团Id;uniqueIndex:device_group_code;index:device_details_groupid_type_obj_idx"`
	DeviceModelId          int64 `json:"DeviceModelId" gorm:"column:devicemodelid;type:bigint;comment:关联设备型号id;" validate:"required"`
	DeviceModelBatchCodeId int64 `json:"DeviceModelBatchCodeId" gorm:"column:devicemodelbatchcodeid;type:bigint;comment:关联设备型号批次id;" validate:"required"`

	Price                int64           `json:"Price" gorm:"column:price;comment:设备单价 单位分;type:integer" validate:"required"`
	Batch                string          `json:"Batch" gorm:"column:batch;comment:批次号;type:varchar(200);" validate:"required"`
	PurchaseAt           int64           `json:"PurchaseAt" gorm:"column:purchaseat;type:integer;comment:采购日期;" validate:"required"`
	ExpireAt             model.LocalTime `json:"ExpireAt" gorm:"column:expireat;comment:过保日期;type:timestamp" validate:"required"`
	MaintainerFactoryId  int64           `json:"MaintainerFactoryId" gorm:"column:maintainerfactoryid;comment:过保维修方id（device_factories表）;type:bigint" validate:"required"`
	MaintainerFactoryKey string          `json:"MaintainerFactoryKey" gorm:"column:maintainerfactorykey;comment:过保维修方名称;type:varchar(100)" validate:"required"`

	model.Timestamp
}

func SyncHistoryBatchCode() {
	time.Sleep(2 * time.Minute)
	var deviceDetails []DeviceDetailTmp
	model.DB().Model(&workOrderModel.DeviceDetail{}).Scan(&deviceDetails)

	for i := range deviceDetails {
		//查询批次是否存在
		var batchCode workOrderModel.DeviceModelBatchCode
		model.DB().Model(&workOrderModel.DeviceModelBatchCode{}).Where("DeviceModelId = ? AND BatchCode = ?", deviceDetails[i].DeviceModelId, deviceDetails[i].Batch).First(&batchCode)

		if batchCode.Id > 0 {
			model.DB().Model(&workOrderModel.DeviceDetail{}).Where("Id = ?", deviceDetails[i].Id).Update("DeviceModelBatchCodeId", batchCode.Id)
		} else {
			//创建批次
			batchCode.Id = model.Id()
			batchCode.DeviceModelId = deviceDetails[i].DeviceModelId
			batchCode.Price = deviceDetails[i].Price
			batchCode.BatchCode = deviceDetails[i].Batch
			purchaseAt, _ := time.ParseInLocation("20060102", fmt.Sprintf("%v", deviceDetails[i].PurchaseAt), time.Local)
			batchCode.PurchaseAt = model.LocalTime(purchaseAt)
			batchCode.ExpireAt = deviceDetails[i].ExpireAt
			batchCode.MaintainerFactoryId = deviceDetails[i].MaintainerFactoryId
			batchCode.MaintainerFactoryKey = deviceDetails[i].MaintainerFactoryKey

			err := model.DB().Create(&batchCode).Error
			if err != nil {
				continue
			}

			model.DB().Model(&workOrderModel.DeviceDetail{}).Where("Id = ?", deviceDetails[i].Id).Update("DeviceModelBatchCodeId", batchCode.Id)
		}
	}
}
