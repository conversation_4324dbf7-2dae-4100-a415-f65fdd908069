package workOrder

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	workOrder "app/org/scs/erpv2/api/model/common"
	processModel "app/org/scs/erpv2/api/model/process"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	go_api "github.com/micro/go-micro/v2/api/proto"
)

type PetitionWorkOrderRequest struct {
	workOrderModel.PetitionWorkOrder
	IsRestart     bool            `json:"IsRestart"`
	ProcessId     string          `json:"ProcessId"`
	StartAt       model.LocalTime `json:"StartAt"`
	EndAt         model.LocalTime `json:"EndAt"`
	StartReportAt model.LocalTime `json:"StartReportAt"`
	EndReportAt   model.LocalTime `json:"EndReportAt"`
	UserName      string          `json:"UserName"`
	CorporationId int64           `json:"CorporationId"`
	HandleStatus  int64           `json:"HandleStatus"` //处理状态 1待处理 2已处理 3已完成 0全部
	HandlerName   string          `json:"HandlerName"`
	model.Paginator
}

func (w *WorkOrder) AddPetition(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param PetitionWorkOrderRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal err=", map[string]interface{}{"error": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	authUser := auth.User(ctx).GetUser()

	param.Corporations.Build(authUser.CorporationId)
	param.OpUserName = authUser.Name
	param.OpUserId = authUser.Id

	fromDict := (&workOrder.Dict{}).FirstById(param.FromDictId)
	param.FromDictKey = fromDict.DictKey

	cateDict := (&workOrder.Dict{}).FirstById(param.CateDictId)
	param.CateDictKey = cateDict.DictKey

	toCorporation := rpc.GetCorporationById(ctx, param.ToCorporationId)
	if toCorporation != nil {
		param.ToCorporationName = toCorporation.Name
	}

	line, _ := rpc.GetLineWithId(ctx, param.LineId)
	if line != nil {
		param.LineName = line.Name
	}

	ToUser := rpc.GetUserInfoById(ctx, param.ToApplyUserId)
	var applyUserAccount string
	var fleetCode string
	if ToUser != nil {
		param.ToApplyUserName = ToUser.Nickname
		applyUserAccount = ToUser.Username

		corporation := rpc.GetCorporationDetailById(ctx, ToUser.CorporationId)
		if corporation != nil {
			fleetCode = corporation.Item.Virtual
		}
	}

	tx := model.DB().Begin()
	// 判断是新建还是重新发起
	if param.IsRestart {
		petition := (&workOrderModel.PetitionWorkOrder{}).FirstBy(param.Id)
		if petition.Id == 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}

		param.ApplyStatus = util.ApplyStatusForDoing
		param.FormStep = util.ProcessFormStepStart
		// 更新表单
		err = param.PetitionWorkOrder.UpdateAll(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("PetitionWorkOrder.UpdateAll err", map[string]interface{}{"error": err})
			return response.Error(rsp, response.FAIL)
		}
	} else {
		err = param.PetitionWorkOrder.Create(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("PetitionWorkOrder.Create err", map[string]interface{}{"error": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	//发起流程审批
	if config.Config.Lbpm.Enable {
		byteParam, _ := json.Marshal(param)
		formData := map[string]interface{}{"ToApplyUserAccount": applyUserAccount, "IsAccept": util.StatusForTrue, "FleetCode": fleetCode}

		if param.IsRestart && param.ProcessId != "" {
			//重新发起流程
			err = processService.RestartProcess(authUser, param.ProcessId, string(byteParam), formData)
		} else {
			//发起新的流程
			processTitle := fmt.Sprintf("%s提交的审批/%s", authUser.Name, param.Code)
			_, err = processService.NewDispatchProcess(authUser, config.PetitionWorkOrderReportFormTemplate, processTitle, param.PetitionWorkOrder.Id, param.PetitionWorkOrder.TableName(), param.PetitionWorkOrder.ApplyStatusFieldName(), string(byteParam), formData)
		}

		if err != nil {
			tx.Rollback()
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	tx.Commit()

	return response.Success(rsp, map[string]interface{}{
		"Id": param.PetitionWorkOrder.Id,
	})
}

type PetitionWorkOrderHandleRequest struct {
	Id                    int64      `json:"Id"`
	HandleResult          string     `json:"HandleResult"`
	HandleFiles           model.JSON `json:"HandleFiles"`
	IsAccept              int64      `json:"IsAccept"`
	RelatedStaffId        int64      `json:"RelatedStaffId"`
	RelatedStaffName      string     `json:"RelatedStaffName"`
	RelatedVehicleId      int64      `json:"RelatedVehicleId"`
	RelatedVehicleLicense string     `json:"RelatedVehicleLicense"`
}

func (w *WorkOrder) PetitionWorkOrderHandle(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param PetitionWorkOrderHandleRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal err=", map[string]interface{}{"error": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	petition := (&workOrderModel.PetitionWorkOrder{}).FirstBy(param.Id)
	if petition.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	if petition.FormStep != util.ProcessFormStepTwo && petition.FormStep != util.ProcessFormStepFour && petition.FormStep != util.ProcessFormStepSix && petition.FormStep != util.ProcessFormStepEight && petition.FormStep != util.ProcessFormStepTen {
		return response.Success(rsp, nil)
	}

	if petition.FormStep == util.ProcessFormStepTwo || petition.FormStep == util.ProcessFormStepSix {
		if len(param.HandleFiles) == 0 {
			return response.Error(rsp, response.ParamsMissing)
		}
	}
	var handleResult workOrderModel.PetitionWorkOrderHandleResult
	if param.IsAccept == 0 {
		param.IsAccept = util.StatusForTrue
	}

	handleResult.PetitionWorkOrderId = petition.Id
	handleResult.HandleResult = param.HandleResult
	handleResult.HandleFiles = param.HandleFiles
	handleResult.FormStep = petition.FormStep
	handleResult.IsAccept = param.IsAccept
	handleResult.RelatedStaffId = param.RelatedStaffId
	handleResult.RelatedStaffName = param.RelatedStaffName
	handleResult.RelatedVehicleId = param.RelatedVehicleId
	handleResult.RelatedVehicleLicense = param.RelatedVehicleLicense

	authUser := auth.User(ctx).GetUser()
	handleResult.CorporationId = authUser.CorporationId
	handleResult.CorporationName = authUser.CorporationName
	handleResult.OpUserId = authUser.Id
	handleResult.OpUserName = authUser.Name

	petition.FormStep = petition.FormStep + 1

	tx := model.DB().Begin()
	err = petition.UpdateFormStep(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("PetitionWorkOrderHandle.UpdateFormStep err", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	err = handleResult.Create(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("PetitionWorkOrderHandle.Create err", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	//更新流程参数
	err = processService.ResetProcessFormFieldValue(petition.Id, petition.TableName(), map[string]interface{}{
		"IsAccept": param.IsAccept,
	})

	if err != nil {
		tx.Rollback()
		log.ErrorFields("processService.ResetProcessFormFieldValue error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	tx.Commit()

	return response.Success(rsp, nil)
}

func (w *WorkOrder) PetitionWorkOrderList(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param PetitionWorkOrderRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal err=", map[string]interface{}{"error": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	workOrders, count, cateDictCounts := (&workOrderModel.PetitionWorkOrder{}).GetBy(param.Code, param.UniqueNo, param.ToCorporationId, param.ToApplyUserName, param.LineId,
		param.FromDictId, param.CateDictId, param.StartReportAt.ToTime(), param.EndReportAt.ToTime(), param.StartAt.ToTime(), param.EndAt.ToTime(),
		param.CorporationId, param.UserName, param.HandlerName, param.ApplyStatus, param.Paginator)
	for i := range workOrders {
		var process processModel.LbpmApplyProcess
		err = (&process).GetProcessByItemId(workOrders[i].Id, workOrders[i].TableName())
		if err != nil {
			log.ErrorFields("process.GetProcessByItemId error", map[string]interface{}{"err": err})
			continue
		}

		workOrders[i].CorporationId, workOrders[i].CorporationName = workOrders[i].Corporations.GetCorporation()

		// 查询当前流程流转至哪个人，以及人归属的机构
		handlers := (&processModel.LbpmApplyProcessHasHandler{}).GetProcessHandlers(process.FormInstanceId)
		if len(handlers) > 0 {
			workOrders[i].TransferAt = handlers[0].StartAt
		}
		workOrders[i].TransferUserName = process.CurrentHandlerUserName
		if workOrders[i].ApplyStatus == util.ApplyStatusForDone {
			workOrders[i].ApplyTotalTimeLen = process.DoneAt.ToTime().Unix() - process.ApplyAt.ToTime().Unix()
		}
	}

	//cateDictCounts := (&workOrderModel.PetitionWorkOrder{}).CountByCateDict()
	return response.Success(rsp, map[string]interface{}{
		"Items":      workOrders,
		"TotalCount": count,
		"CateCounts": cateDictCounts,
	})
}

func (w *WorkOrder) PetitionWorkOrderMineList(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param PetitionWorkOrderRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal err=", map[string]interface{}{"error": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	workOrders, count := (&workOrderModel.PetitionWorkOrder{}).GetMineList(auth.User(ctx).GetUserId(), param.HandleStatus, param.Code, param.UniqueNo, param.UserName, param.CorporationId, param.FromDictId, param.CateDictId, param.LineId, param.ToCorporationId, param.StartAt.ToTime(), param.EndAt.ToTime(), param.Paginator)
	for i := range workOrders {
		var process processModel.LbpmApplyProcess
		err = (&process).GetProcessByItemId(workOrders[i].Id, workOrders[i].TableName())
		if err != nil {
			log.ErrorFields("process.GetProcessByItemId error", map[string]interface{}{"err": err})
			continue
		}
		workOrders[i].CorporationId, workOrders[i].CorporationName = workOrders[i].Corporations.GetCorporation()

		// 查询当前流程流转至哪个人，以及人归属的机构
		handlers := (&processModel.LbpmApplyProcessHasHandler{}).GetProcessHandlers(process.FormInstanceId)
		if len(handlers) > 0 {
			workOrders[i].TransferAt = handlers[0].StartAt
		}
		workOrders[i].TransferUserName = process.CurrentHandlerUserName
	}
	return response.Success(rsp, map[string]interface{}{
		"Items":      workOrders,
		"TotalCount": count,
	})
}

func (w *WorkOrder) PetitionWorkOrderShow(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param PetitionWorkOrderRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal err=", map[string]interface{}{"error": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	petition := (&workOrderModel.PetitionWorkOrder{}).FirstBy(param.Id)
	if petition.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	petition.CorporationId, petition.CorporationName = petition.Corporations.GetCorporation()
	petition.HandleResults = (&workOrderModel.PetitionWorkOrderHandleResult{}).GetByPetitionId(petition.Id)

	return response.Success(rsp, petition)
}
