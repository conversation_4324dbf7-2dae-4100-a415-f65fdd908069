package workOrder

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	commonModel "app/org/scs/erpv2/api/model/common"
	processModel "app/org/scs/erpv2/api/model/process"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"sort"
	"time"
)

type WorkOrderReport struct {
	StartAt model.LocalTime `json:"StartAt"`
	EndAt   model.LocalTime `json:"EndAt"`
	model.Paginator
}

func (w *WorkOrder) getParam(req *api.Request) (time.Time, time.Time, error) {
	var param WorkOrderReport
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return time.Time{}, time.Time{}, err
	}

	startTime := time.Time(param.StartAt)
	endTime := time.Time(param.EndAt)
	if startTime.IsZero() {
		startTime = time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, time.Local)
		endTime = startTime.AddDate(1, 0, 0)
	} else {
		endTime = time.Time(param.EndAt).AddDate(0, 0, 1)
	}

	return startTime, endTime, nil
}
func (w *WorkOrder) YearSumReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, err := w.getParam(req)
	if err != nil {
		log.ErrorFields("getParam error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//所有设备数
	var deviceTotalCount int64
	model.DB().Model(&workOrderModel.DeviceDetail{}).Count(&deviceTotalCount)

	//工单总数
	workOrderTotalCount := (&workOrderModel.WorkOrder{}).GetCount(startTime, endTime)

	//所有工单ID
	workOrderIds := (&workOrderModel.WorkOrder{}).GetIds(startTime, endTime, 0)

	//维修总金额
	totalMoney := (&workOrderModel.WorkOrderRepairSparePart{}).GetTotalMoney(workOrderIds) + (&workOrderModel.WorkOrderReplaceChildDevice{}).GetTotalMoney(workOrderIds)

	mobileClientCount := (&processModel.LbpmApplyProcessHasHandler{}).GetClientTypeCount([]string{config.DeviceWorkOrderReportFormTemplate}, workOrderIds, "mobile")
	pcClientCount := (&processModel.LbpmApplyProcessHasHandler{}).GetClientTypeCount([]string{config.DeviceWorkOrderReportFormTemplate}, workOrderIds, "pc")
	//移动审批率
	var mobileApprovalRate int64
	if mobileClientCount+pcClientCount > 0 {
		mobileApprovalRate = int64((float64(mobileClientCount) / float64(mobileClientCount+pcClientCount)) * 10000)
	}

	//维修人员总工时
	repairTotalWorkTime := 0

	//审批通过的工单
	workOrderDoneIds := (&workOrderModel.WorkOrder{}).GetIds(startTime, endTime, util.ApplyStatusForDone)

	var totalFlowTime, avgFlowTime, totalRepairTime, avgRepairTime, totalConfirmTime, avgConfirmTime int64
	if len(workOrderDoneIds) > 0 {
		//工单平均流转时长
		totalFlowTime = (&workOrderModel.WorkOrder{}).GetTotalFlowTime(workOrderDoneIds)
		avgFlowTime = totalFlowTime / int64(len(workOrderDoneIds))

		//工单平均维修时长
		totalRepairTime = (&workOrderModel.WorkOrder{}).GetTotalRepairTime(workOrderDoneIds)
		avgRepairTime = totalRepairTime / int64(len(workOrderDoneIds))

		//工单平均确认时长
		totalConfirmTime = (&workOrderModel.WorkOrder{}).GetTotalConfirmTime(workOrderDoneIds)
		avgConfirmTime = totalConfirmTime / int64(len(workOrderDoneIds))
	}

	return response.Success(rsp, map[string]interface{}{
		"DeviceTotalCount":    deviceTotalCount,
		"WorkOrderTotalCount": workOrderTotalCount,
		"TotalMoney":          totalMoney,
		"MobileApprovalRate":  mobileApprovalRate,
		"RepairTotalWorkTime": repairTotalWorkTime,
		"AvgFlowTime":         avgFlowTime,
		"AvgRepairTime":       avgRepairTime,
		"AvgConfirmTime":      avgConfirmTime,
	})
}

func (w *WorkOrder) FleetRankReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, err := w.getParam(req)
	if err != nil {
		log.ErrorFields("getParam error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	dict := (&commonModel.Dict{}).FirstByDictType(util.DictTypeForDeviceClass, util.DeviceAssociationObjectForVehicle)

	if dict.Id == 0 {
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	records := (&workOrderModel.WorkOrder{}).GetCorporationWorkOrderCount(startTime, endTime, dict.Id)

	sort.SliceStable(records, func(i, j int) bool {
		return records[i].WorkOrderCount > records[j].WorkOrderCount
	})
	return response.Success(rsp, records)
}

func (w *WorkOrder) LineRankReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, err := w.getParam(req)
	if err != nil {
		log.ErrorFields("getParam error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	dict := (&commonModel.Dict{}).FirstByDictType(util.DictTypeForDeviceClass, util.DeviceAssociationObjectForVehicle)

	if dict.Id == 0 {
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	records := (&workOrderModel.WorkOrder{}).GetLineWorkOrderCount(startTime, endTime, dict.Id)

	sort.SliceStable(records, func(i, j int) bool {
		return records[i].WorkOrderCount > records[j].WorkOrderCount
	})

	return response.Success(rsp, records)
}

type ItemRate struct {
	ItemId   int64   `json:"ItemId"`
	ItemName string  `json:"ItemName"`
	Rate     float64 `json:"Rate"`
}

func (w *WorkOrder) DeviceCateBrokenRateReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, err := w.getParam(req)
	if err != nil {
		log.ErrorFields("getParam error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	deviceCates := (&commonModel.Dict{}).GetByDictType(util.DictTypeForDeviceCate)

	if len(deviceCates) == 0 {
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	//获取所有报修过的设备
	allRepairDevices := (&workOrderModel.WorkOrder{}).GetDeviceWorkOrderCount(startTime, endTime, 0)

	var deviceWorkOrderCount = make(map[int64]int64)
	for i := range allRepairDevices {
		deviceWorkOrderCount[allRepairDevices[i].DeviceId] = allRepairDevices[i].WorkOrderCount
	}

	var rates []ItemRate
	for i := 0; i < len(deviceCates); i++ {
		//统计每个种类下有多少设备
		allDeviceIds := (&workOrderModel.DeviceDetail{}).GetDeviceIdByCateId(deviceCates[i].Id)

		//判断设备中的维修次数
		var repairCount int64
		for j := range allDeviceIds {
			if _, ok := deviceWorkOrderCount[allDeviceIds[j]]; ok {
				repairCount += deviceWorkOrderCount[allDeviceIds[j]]
			}
		}

		var rate float64
		if len(allDeviceIds) > 0 {
			rate = float64(repairCount) / float64(len(allDeviceIds))
		}
		rates = append(rates, ItemRate{
			ItemId:   deviceCates[i].Id,
			ItemName: deviceCates[i].DictKey,
			Rate:     rate,
		})
	}
	return response.Success(rsp, rates)
}

func (w *WorkOrder) DeviceModelBrokenRateReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, err := w.getParam(req)
	if err != nil {
		log.ErrorFields("getParam error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	deviceModels := (&workOrderModel.DeviceModel{}).GetAll()

	if len(deviceModels) == 0 {
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	//获取所有报修过的设备
	allRepairDevices := (&workOrderModel.WorkOrder{}).GetDeviceWorkOrderCount(startTime, endTime, 0)

	var deviceWorkOrderCount = make(map[int64]int64)
	for i := range allRepairDevices {
		deviceWorkOrderCount[allRepairDevices[i].DeviceId] = allRepairDevices[i].WorkOrderCount
	}

	var rates []ItemRate
	for i := 0; i < len(deviceModels); i++ {
		//统计每个型号下有多少设备
		allDeviceIds := (&workOrderModel.DeviceDetail{}).GetDeviceIdByModelId(deviceModels[i].Id, nil)

		//判断设备中的维修次数
		var repairCount int64
		for j := range allDeviceIds {
			if _, ok := deviceWorkOrderCount[allDeviceIds[j]]; ok {
				repairCount += deviceWorkOrderCount[allDeviceIds[j]]
			}
		}
		var rate float64
		if len(allDeviceIds) > 0 {
			rate = float64(repairCount) / float64(len(allDeviceIds))
		}
		rates = append(rates, ItemRate{
			ItemId:   deviceModels[i].Id,
			ItemName: deviceModels[i].Name,
			Rate:     rate,
		})
	}
	return response.Success(rsp, rates)
}

func (w *WorkOrder) WithInDeviceModelBrokenRateReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, err := w.getParam(req)
	if err != nil {
		log.ErrorFields("getParam error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//获取在时间内没过保的批次
	deviceBatchCodes := (&workOrderModel.DeviceModelBatchCode{}).GetWithIn(endTime)
	if len(deviceBatchCodes) == 0 {
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	var modelIds = make(map[int64]bool)
	var batchCodeIds []int64
	for i := range deviceBatchCodes {
		modelIds[deviceBatchCodes[i].DeviceModelId] = true
		batchCodeIds = append(batchCodeIds, deviceBatchCodes[i].Id)
	}

	//获取所有报修过的设备
	allRepairDevices := (&workOrderModel.WorkOrder{}).GetDeviceWorkOrderCount(startTime, endTime, 0)

	var deviceWorkOrderCount = make(map[int64]int64)
	for i := range allRepairDevices {
		deviceWorkOrderCount[allRepairDevices[i].DeviceId] = allRepairDevices[i].WorkOrderCount
	}

	var rates []ItemRate
	for modelId := range modelIds {
		//统计每个型号下有多少设备
		allDeviceIds := (&workOrderModel.DeviceDetail{}).GetDeviceIdByModelId(modelId, batchCodeIds)

		//判断设备中的维修次数
		var repairCount int64
		for j := range allDeviceIds {
			if _, ok := deviceWorkOrderCount[allDeviceIds[j]]; ok {
				repairCount += deviceWorkOrderCount[allDeviceIds[j]]
			}
		}
		var rate float64
		if len(allDeviceIds) > 0 {
			rate = float64(repairCount) / float64(len(allDeviceIds))
		}
		modelInfo := (&workOrderModel.DeviceModel{}).FirstBy(modelId)
		rates = append(rates, ItemRate{
			ItemId:   modelId,
			ItemName: modelInfo.Name,
			Rate:     rate,
		})
	}
	return response.Success(rsp, rates)
}

func (w *WorkOrder) WithOutDeviceModelBrokenRateReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, err := w.getParam(req)
	if err != nil {
		log.ErrorFields("getParam error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//获取在时间内过保的批次
	deviceBatchCodes := (&workOrderModel.DeviceModelBatchCode{}).GetWithOut(endTime)
	if len(deviceBatchCodes) == 0 {
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	var modelIds = make(map[int64]bool)
	var batchCodeIds []int64
	for i := range deviceBatchCodes {
		modelIds[deviceBatchCodes[i].DeviceModelId] = true
		batchCodeIds = append(batchCodeIds, deviceBatchCodes[i].Id)
	}

	//获取所有报修过的设备
	allRepairDevices := (&workOrderModel.WorkOrder{}).GetDeviceWorkOrderCount(startTime, endTime, util.StatusForFalse)

	var deviceWorkOrderCount = make(map[int64]int64)
	for i := range allRepairDevices {
		deviceWorkOrderCount[allRepairDevices[i].DeviceId] = allRepairDevices[i].WorkOrderCount
	}

	var rates []ItemRate
	for modelId := range modelIds {
		//统计每个型号下有多少设备
		allDeviceIds := (&workOrderModel.DeviceDetail{}).GetDeviceIdByModelId(modelId, batchCodeIds)

		//判断设备中的维修次数
		var repairCount int64
		for j := range allDeviceIds {
			if _, ok := deviceWorkOrderCount[allDeviceIds[j]]; ok {
				repairCount += deviceWorkOrderCount[allDeviceIds[j]]
			}
		}
		var rate float64
		if len(allDeviceIds) > 0 {
			rate = float64(repairCount) / float64(len(allDeviceIds))
		}
		modelInfo := (&workOrderModel.DeviceModel{}).FirstBy(modelId)
		rates = append(rates, ItemRate{
			ItemId:   modelId,
			ItemName: modelInfo.Name,
			Rate:     rate,
		})
	}
	return response.Success(rsp, rates)
}

type ChildDeviceModelRank struct {
	ChildDeviceModelId   int64  `json:"ChildDeviceId"`
	ChildDeviceModelName string `json:"ChildDeviceCode"`
	ChangeCount          int64  `json:"ChangeCount"`
	SumMoney             int64  `json:"SumMoney"`
}

func (w *WorkOrder) ChildDeviceCountRankReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, err := w.getParam(req)
	if err != nil {
		log.ErrorFields("getParam error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//获取所有报修过的设备
	workOrderIds := (&workOrderModel.WorkOrder{}).GetIds(startTime, endTime, 0)
	replaceChildDevices := (&workOrderModel.WorkOrderReplaceChildDevice{}).GetReplaceChildDevice(workOrderIds)

	var rankMap = make(map[int64]ChildDeviceModelRank)
	for i := range replaceChildDevices {
		childDevice := (&workOrderModel.ChildDevice{}).FirstById(replaceChildDevices[i].NewChildDeviceId)
		if rank, ok := rankMap[childDevice.ModelId]; ok {
			rank.ChangeCount += 1
			rankMap[childDevice.ModelId] = rank
		} else {
			childCate := (&workOrderModel.ChildDeviceCate{}).FirstById(childDevice.CateId)
			childModel := (&workOrderModel.ChildDeviceCate{}).FirstById(childDevice.ModelId)
			rankMap[childDevice.ModelId] = ChildDeviceModelRank{
				ChildDeviceModelId:   childDevice.ModelId,
				ChildDeviceModelName: fmt.Sprintf("%s-%s", childCate.Name, childModel.Name),
				ChangeCount:          1,
			}
		}
	}

	var ranks []ChildDeviceModelRank
	for i := range rankMap {
		ranks = append(ranks, rankMap[i])
	}

	sort.SliceStable(ranks, func(i, j int) bool {
		return ranks[i].ChangeCount > ranks[j].ChangeCount
	})
	return response.Success(rsp, ranks)
}

func (w *WorkOrder) ChildDeviceMoneyRankReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, err := w.getParam(req)
	if err != nil {
		log.ErrorFields("getParam error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//获取所有报修过的设备
	//获取所有报修过的设备
	workOrderIds := (&workOrderModel.WorkOrder{}).GetIds(startTime, endTime, 0)
	replaceChildDevices := (&workOrderModel.WorkOrderReplaceChildDevice{}).GetReplaceChildDevice(workOrderIds)

	var rankMap = make(map[int64]ChildDeviceModelRank)
	for i := range replaceChildDevices {
		childDevice := (&workOrderModel.ChildDevice{}).FirstById(replaceChildDevices[i].NewChildDeviceId)
		if rank, ok := rankMap[childDevice.ModelId]; ok {
			rank.SumMoney += replaceChildDevices[i].Price
			rankMap[childDevice.ModelId] = rank
		} else {
			childCate := (&workOrderModel.ChildDeviceCate{}).FirstById(childDevice.CateId)
			childModel := (&workOrderModel.ChildDeviceCate{}).FirstById(childDevice.ModelId)
			rankMap[childDevice.ModelId] = ChildDeviceModelRank{
				ChildDeviceModelId:   childDevice.ModelId,
				ChildDeviceModelName: fmt.Sprintf("%s-%s", childCate.Name, childModel.Name),
				SumMoney:             replaceChildDevices[i].Price,
			}
		}
	}

	var ranks []ChildDeviceModelRank
	for i := range rankMap {
		ranks = append(ranks, rankMap[i])
	}

	sort.SliceStable(ranks, func(i, j int) bool {
		return ranks[i].SumMoney > ranks[j].SumMoney
	})
	return response.Success(rsp, ranks)
}
