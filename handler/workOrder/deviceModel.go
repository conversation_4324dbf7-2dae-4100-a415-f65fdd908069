package workOrder

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	settingModel "app/org/scs/erpv2/api/model/setting"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	go_api "github.com/micro/go-micro/v2/api/proto"
)

type DeviceModel struct {
	workOrderModel.DeviceModel
	DeviceClassDictIds       []int64 `json:"DeviceClassDictIds"`
	DeviceCategoryDictIds    []int64 `json:"DeviceCategoryDictIds"`
	SupplierFactoryIds       []int64 `json:"SupplierFactoryIds"`
	ResponsibleDepartmentIds []int64 `json:"ResponsibleDepartmentIds"`
	Ids                      []int64 `json:"Ids"`
	MaintainerFactoryKey     string  `json:"MaintainerFactoryKey"`
	IsRepairStatus           int64   `json:"IsRepairStatus"` //是否在保 1是 2否
	model.Paginator
}

func (d *Device) AddModel(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DeviceModel
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err = util.Validator().Struct(param); err != nil {
		log.ErrorFields("param validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}
	if len(param.BatchCodes) > 0 {
		for i := range param.BatchCodes {
			if err = util.Validator().Struct(param.BatchCodes[i]); err != nil {
				log.ErrorFields("param.BatchCodes validate fail", map[string]interface{}{"err": err})
				return response.Error(rsp, response.ParamsMissing)
			}
		}
	}
	param.GroupId = auth.User(ctx).GetTopCorporationId()

	//同品牌、同型号、同供货方不能重复
	if (&workOrderModel.DeviceModel{}).IsExistBy(param.Name, param.BrandFactoryId, param.SupplierFactoryId) {
		log.ErrorFields("unique key is conflict", nil)
		return response.Error(rsp, "OP9001")
	}

	// 获取责任人信息
	oetStaffItem := rpc.GetStaffWithId(ctx, param.LiableStaffId)
	if oetStaffItem != nil {
		param.LiableStaffName = oetStaffItem.Name
		param.LiableCorporationId = oetStaffItem.CorporationId

		corporationItem := rpc.GetCorporationById(ctx, oetStaffItem.CorporationId)
		if corporationItem != nil {
			param.LiableCorporation = corporationItem.Name
		}
	}

	//获取品牌方
	brand := (&settingModel.DeviceFactory{}).FirstBy(param.BrandFactoryId)
	param.BrandFactoryKey = brand.SimpleName

	//获取保修方
	handler := (&settingModel.DeviceFactory{}).FirstBy(param.HandlerFactoryId)
	param.HandlerFactoryKey = handler.SimpleName

	//获取供货方
	supplier := (&settingModel.DeviceFactory{}).FirstBy(param.SupplierFactoryId)
	param.SupplierFactoryKey = supplier.SimpleName

	tx := model.DB().Begin()
	err = (&param.DeviceModel).TransactionAdd(tx)
	if err != nil {
		tx.Rollback()
		if model.IsViolatesUniqueConstraint(err) {
			log.Error("unique_violation", err.Error())
			return response.Error(rsp, "OP9001")
		}
		log.Error("Add err == ", err)
		return response.Error(rsp, response.DbSaveFail)
	}

	if len(param.BatchCodes) > 0 {
		for i := range param.BatchCodes {
			param.BatchCodes[i].DeviceModelId = param.DeviceModel.Id
			err = param.BatchCodes[i].TransactionAdd(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("param.BatchCodes.TransactionAdd error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}

			if len(param.BatchCodes[i].ChildDeviceCates) > 0 {
				for j := range param.BatchCodes[i].ChildDeviceCates {
					param.BatchCodes[i].ChildDeviceCates[j].DeviceModelId = param.DeviceModel.Id
					param.BatchCodes[i].ChildDeviceCates[j].DeviceModelBatchCodeId = param.BatchCodes[i].Id
				}
				err = (&workOrderModel.DeviceModelBatchCodeHasChildDeviceCate{}).TransactionBatchCreate(tx, param.BatchCodes[i].ChildDeviceCates)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("DeviceModelBatchCodeHasChildDeviceCate.TransactionBatchCreate error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbSaveFail)
				}
			}
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

func (d *Device) ListModel(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DeviceModel
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	groupId := auth.User(ctx).GetTopCorporationId()

	param.GroupId = groupId
	deviceModels, totalCount, err := (&param.DeviceModel).List(groupId, param.DeviceClassDictIds, param.DeviceCategoryDictIds, param.SupplierFactoryKey, param.BrandFactoryKey, param.HandlerFactoryKey, param.MaintainerFactoryKey, param.ResponsibleDepartmentIds, param.Name, param.IsRepairStatus, param.Paginator)

	if err != nil {
		log.Error("List err =", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	for i := range deviceModels {
		//查询批次信息
		deviceModels[i].BatchCodes = (&workOrderModel.DeviceModelBatchCode{}).GetBy(deviceModels[i].Id, param.MaintainerFactoryKey, param.IsRepairStatus)
		//型号下有设备  不可以删除
		deviceModels[i].IsExistDevice = (&workOrderModel.DeviceDetail{}).ExistsByModelId(deviceModels[i].Id)
	}

	data := map[string]interface{}{
		"Items":      deviceModels,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)
}

func (d *Device) ShowModel(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DeviceModel
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	deviceModel := (&workOrderModel.DeviceModel{}).FirstBy(param.Id)

	if deviceModel.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	//查询批次号
	deviceModel.BatchCodes = (&workOrderModel.DeviceModelBatchCode{}).GetBy(deviceModel.Id, "", 0)
	if len(deviceModel.BatchCodes) > 0 {
		//查询子设备信息
		for i := range deviceModel.BatchCodes {
			deviceModel.BatchCodes[i].ChildDeviceCates = (&workOrderModel.DeviceModelBatchCodeHasChildDeviceCate{}).GetByBatchCodeId(deviceModel.BatchCodes[i].Id)
			//批次下有设备 不可以删除
			deviceModel.BatchCodes[i].IsExistDevice = (&workOrderModel.DeviceDetail{}).ExistsByBatchCodeId(deviceModel.BatchCodes[i].Id)
		}
	}
	//型号下有设备  不可以删除
	deviceModel.IsExistDevice = (&workOrderModel.DeviceDetail{}).ExistsByModelId(deviceModel.Id)

	return response.Success(rsp, deviceModel)
}

func (d *Device) EditModel(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DeviceModel
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil || param.Id == 0 {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err = util.Validator().Struct(param); err != nil {
		log.ErrorFields("param validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}
	if len(param.BatchCodes) > 0 {
		for i := range param.BatchCodes {
			if err = util.Validator().Struct(param.BatchCodes[i]); err != nil {
				log.ErrorFields("param.BatchCodes validate fail", map[string]interface{}{"err": err})
				return response.Error(rsp, response.ParamsMissing)
			}
		}
	}

	//同品牌、同型号、同供货方不能重复
	deviceModel := (&workOrderModel.DeviceModel{}).FindBy(param.Name, param.BrandFactoryId, param.SupplierFactoryId)
	if deviceModel.Id > 0 && deviceModel.Id != param.DeviceModel.Id {
		log.ErrorFields("unique key is conflict", nil)
		return response.Error(rsp, "OP9001")
	}

	// 获取责任人信息
	oetStaffItem := rpc.GetStaffWithId(ctx, param.DeviceModel.LiableStaffId)
	if oetStaffItem != nil {
		param.LiableStaffName = oetStaffItem.Name
		param.LiableCorporationId = oetStaffItem.CorporationId

		corporationItem := rpc.GetCorporationById(ctx, oetStaffItem.CorporationId)
		if corporationItem != nil {
			param.LiableCorporation = corporationItem.Name
		}
	}
	//获取品牌方
	brand := (&settingModel.DeviceFactory{}).FirstBy(param.BrandFactoryId)
	param.BrandFactoryKey = brand.SimpleName

	//获取保修方
	handler := (&settingModel.DeviceFactory{}).FirstBy(param.HandlerFactoryId)
	param.HandlerFactoryKey = handler.SimpleName

	//获取供货方
	supplier := (&settingModel.DeviceFactory{}).FirstBy(param.SupplierFactoryId)
	param.SupplierFactoryKey = supplier.SimpleName

	tx := model.DB().Begin()
	err = (&param.DeviceModel).TransactionEdit(tx)
	if err != nil {
		tx.Rollback()
		log.Error("Edit err =", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	if len(param.BatchCodes) > 0 {
		for i := range param.BatchCodes {
			param.BatchCodes[i].DeviceModelId = param.DeviceModel.Id
			if param.BatchCodes[i].ActionType == "update" && param.BatchCodes[i].Id > 0 {
				err = param.BatchCodes[i].TransactionUpdate(tx)
			}
			if param.BatchCodes[i].ActionType == "create" {
				err = param.BatchCodes[i].TransactionAdd(tx)
			}
			if param.BatchCodes[i].ActionType == "delete" && param.BatchCodes[i].Id > 0 {
				err = param.BatchCodes[i].TransactionDelete(tx)
			}

			if err != nil {
				tx.Rollback()
				log.ErrorFields("param.BatchCodes.Edit error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}

			if param.BatchCodes[i].Id > 0 {
				//删除关联的子设备
				err = (&workOrderModel.DeviceModelBatchCodeHasChildDeviceCate{}).TransactionDeleteBy(tx, []int64{param.BatchCodes[i].Id})
				if err != nil {
					tx.Rollback()
					log.ErrorFields("DeviceModelBatchCodeHasChildDeviceCate.TransactionDeleteBy err =", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbUpdateFail)
				}
			}

			if (param.BatchCodes[i].ActionType == "update" || param.BatchCodes[i].ActionType == "create") && len(param.BatchCodes[i].ChildDeviceCates) > 0 {
				for j := range param.BatchCodes[i].ChildDeviceCates {
					param.BatchCodes[i].ChildDeviceCates[j].DeviceModelId = param.DeviceModel.Id
					param.BatchCodes[i].ChildDeviceCates[j].DeviceModelBatchCodeId = param.BatchCodes[i].Id
				}
				err = (&workOrderModel.DeviceModelBatchCodeHasChildDeviceCate{}).TransactionBatchCreate(tx, param.BatchCodes[i].ChildDeviceCates)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("DeviceModelBatchCodeHasChildDeviceCate.TransactionBatchCreate error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbSaveFail)
				}
			}
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

type BatchBindChildModel struct {
	BatchCodeIds     []int64                                                 `json:"BatchCodeIds"`
	ChildDeviceCates []workOrderModel.DeviceModelBatchCodeHasChildDeviceCate `json:"ChildDeviceCates"`
}

func (d *Device) BatchBindChildModel(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param BatchBindChildModel
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(param.ChildDeviceCates) == 0 || len(param.BatchCodeIds) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var childDeviceCates []workOrderModel.DeviceModelBatchCodeHasChildDeviceCate

	for i := range param.BatchCodeIds {
		batchCode := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(param.BatchCodeIds[i])
		if batchCode.Id == 0 {
			continue
		}
		for j := range param.ChildDeviceCates {
			childDeviceCates = append(childDeviceCates, workOrderModel.DeviceModelBatchCodeHasChildDeviceCate{
				DeviceModelId:          batchCode.DeviceModelId,
				DeviceModelBatchCodeId: param.BatchCodeIds[i],
				ChildDeviceCateId:      param.ChildDeviceCates[j].ChildDeviceCateId,
				ChildDeviceModelId:     param.ChildDeviceCates[j].ChildDeviceModelId,
			})
		}
	}

	err = (&workOrderModel.DeviceModelBatchCodeHasChildDeviceCate{}).BatchCreate(childDeviceCates)
	if err != nil {
		log.ErrorFields("DeviceModelBatchCodeHasChildDeviceCate.BatchCreate error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func (d *Device) DeleteModel(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DeviceModel
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 查询型号是否存在设备明细（创建了设备）
	for i := range param.Ids {
		if (&workOrderModel.DeviceDetail{}).ExistsByModelId(param.Ids[i]) {
			log.Error("设备型号存在设备,不可删除")
			return response.Error(rsp, response.DbDeleteFail)
		}
	}

	tx := model.DB().Begin()
	err = (&workOrderModel.DeviceModel{}).TransactionDelete(tx, param.Ids)
	if err != nil {
		tx.Rollback()
		log.Error("Delete err =", err)
		return response.Error(rsp, response.DbDeleteFail)
	}
	//删除关联的批次
	err = (&workOrderModel.DeviceModelBatchCode{}).TransactionDeleteByModelIds(tx, param.Ids)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("DeviceModelBatchCode.TransactionDeleteByModelIds err =", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	//删除关联的子设备
	err = (&workOrderModel.DeviceModelBatchCodeHasChildDeviceCate{}).TransactionDeleteBy(tx, param.Ids)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("DeviceModelBatchCodeHasChildDeviceCate.TransactionDeleteBy err =", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}
	tx.Commit()

	return response.Success(rsp, nil)
}

func (d *Device) ModelHasChildDeviceModel(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param DeviceModel
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	var deviceModel workOrderModel.DeviceModel
	err = deviceModel.Find(param.Id)
	if err != nil {
		log.ErrorFields("deviceModel.Find err=", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	//查询子设备型号
	childDeviceCates := (&workOrderModel.DeviceModelBatchCodeHasChildDeviceCate{}).GetByModelId(deviceModel.Id)
	for m := range childDeviceCates {
		cate := (&workOrderModel.ChildDeviceCate{}).FirstById(childDeviceCates[m].ChildDeviceCateId)
		childDeviceCates[m].ChildDeviceCateName = cate.Name
		mod := (&workOrderModel.ChildDeviceCate{}).FirstById(childDeviceCates[m].ChildDeviceModelId)
		childDeviceCates[m].ChildDeviceModelName = mod.Name
		//查询子设备型号下未绑定过的子设备
		childDeviceCates[m].ChildDevices = (&workOrderModel.ChildDevice{}).GetByModelId(mod.Id, workOrderModel.ChildDeviceFreezeStatusForDefault)
	}

	return response.Success(rsp, childDeviceCates)
}

func (d *Device) BatchCodeHasChildDeviceModel(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param workOrderModel.DeviceModelBatchCode
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	batchCode := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(param.Id)
	if err != nil {
		log.ErrorFields("DeviceModelBatchCode.FirstBy err=", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//查询批次下的子设备型号
	childDeviceCates := (&workOrderModel.DeviceModelBatchCodeHasChildDeviceCate{}).GetByBatchCodeId(batchCode.Id)
	for m := range childDeviceCates {
		cate := (&workOrderModel.ChildDeviceCate{}).FirstById(childDeviceCates[m].ChildDeviceCateId)
		childDeviceCates[m].ChildDeviceCateName = cate.Name
		mod := (&workOrderModel.ChildDeviceCate{}).FirstById(childDeviceCates[m].ChildDeviceModelId)
		childDeviceCates[m].ChildDeviceModelName = mod.Name
		//查询子设备型号下未绑定过的子设备
		childDeviceCates[m].ChildDevices = (&workOrderModel.ChildDevice{}).GetByModelId(mod.Id, workOrderModel.ChildDeviceFreezeStatusForDefault)
	}

	return response.Success(rsp, childDeviceCates)
}
