package workOrder

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	commonModel "app/org/scs/erpv2/api/model/common"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/model/setting"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	protooetvehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	"github.com/micro/go-micro/v2/api/proto"
	"strconv"
	"strings"
	"time"
)

type WorkOrder struct {
	workOrderModel.WorkOrder
	ProcessId string // 蓝凌返回的流程id
	LbpmParam string // 前端嵌入页面获取数据
	IsReStart bool   // 是否是 驳回、撤回重新发起
	StartAt   model.LocalTime
	EndAt     model.LocalTime
	model.Paginator
	Device       []workOrderModel.WorkOrderRepairSparePart
	CustomStatus int64 // 0:和我相关的所有工单 1:待(我)处理 2:流转中(未结束状态下，我处理之前或之后)

	DeviceCategoryDictId int64   // 设备种类id
	DeviceClassDictIds   []int64 // 设备大类id
	DeviceModelName      string  // 设备型号

	IdStr                   string   // 工单id 兼容wx小程序大整型
	PetitionOriginDictIdStr string   // *信访来源字典id 兼容wx小程序大整型
	PetitionTypeDictIdStr   string   // 信访分类字典id 兼容wx小程序大整型
	RecvCorporationIdStr    string   // *去向部门 兼容wx小程序大整型
	RepairClassDictIdStr    string   // *设备大类字典id 兼容wx小程序大整型
	FkRepairDeviceIdStr     string   // *报修设备id 兼容wx小程序大整型
	MaintainerDictIdStr     string   // *维修方字典id 兼容wx小程序大整型
	CreatorDepartmentIdStr  string   // 创建部门 兼容wx小程序大整型
	DeviceClassDictIdStrs   []string // *设备大类字典id 兼容wx小程序大整型
	DeviceCategoryDictIdStr string   // 设备种类 兼容wx小程序大整型
	ActualDevicesIdStr      string   // 实际设备(明细)id 兼容wx小程序大整型
	ProcessCurrentHandler   string
	ReplaceChildDevices     []workOrderModel.WorkOrderReplaceChildDevice `json:"ReplaceChildDevices"` //更换子设备时有值
	ObjectName              string                                       `json:"ObjectName"`

	ProcessTotalStartNum  int64 `json:"ProcessTotalStartNum"`  //总时长开始天数
	ProcessTotalEndNum    int64 `json:"ProcessTotalEndNum"`    //总时长结束天数
	RepairStartNum        int64 `json:"RepairStartNum"`        //维修时长开始天数
	RepairEndNum          int64 `json:"RepairEndNum"`          //维修时长结束天数
	RepairConfirmStartNum int64 `json:"RepairConfirmStartNum"` //维修确认时长开始天数
	RepairConfirmEndNum   int64 `json:"RepairConfirmEndNum"`   //维修确认时长结束天数
}

// ProcessParams 发起流程存的 json 参数，添加消息额外字段
type CreateProcessParam struct {
	RepairCategoryDictId  int64  // 设备种类id
	RepairCategoryDictKey string // 设备种类
	workOrderModel.WorkOrder
}

func (w *WorkOrder) Add(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q WorkOrder
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	// compatible with bigint
	if q.PetitionOriginDictIdStr != "" {
		q.PetitionOriginDictId, _ = strconv.ParseInt(q.PetitionOriginDictIdStr, 10, 64)
	}

	// compatible with bigint
	if q.PetitionTypeDictIdStr != "" {
		q.PetitionTypeDictId, _ = strconv.ParseInt(q.PetitionTypeDictIdStr, 10, 64)
	}

	// compatible with bigint
	if q.RecvCorporationIdStr != "" {
		q.RecvCorporationId, _ = strconv.ParseInt(q.RecvCorporationIdStr, 10, 64)
	}

	// compatible with bigint
	if q.RepairClassDictIdStr != "" {
		q.RepairClassDictId, _ = strconv.ParseInt(q.RepairClassDictIdStr, 10, 64)
	}

	// compatible with bigint
	if q.FkRepairDeviceIdStr != "" {
		q.FkRepairDeviceId, _ = strconv.ParseInt(q.FkRepairDeviceIdStr, 10, 64)
	}

	// compatible with bigint
	if q.MaintainerDictIdStr != "" {
		q.MaintainerFactoryId, _ = strconv.ParseInt(q.MaintainerDictIdStr, 10, 64)
	}

	if q.Type == 0 {
		log.Error("q.Type == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	// 获取当前用户的StaffId
	user := auth.User(ctx).GetUser()

	//q.WorkOrder.Id = model.Id()
	q.WorkOrder.GroupId = user.TopCorporationId
	q.WorkOrder.Code = util.GenerateIdentifier()

	q.WorkOrder.CreatorUserId = user.Id
	q.WorkOrder.CreatorUserName = user.Name
	q.WorkOrder.CreatorCorporationId = auth.User(ctx).GetCorporationId()

	detailById := rpc.GetCorporationDetailById(ctx, q.WorkOrder.CreatorCorporationId)
	if detailById == nil || detailById.Item == nil {
		log.Error("detailById == nil || detailById.Item == nil corpId == ", q.WorkOrder.CreatorCorporationId)
		return response.Error(rsp, response.Forbidden)
	}

	q.WorkOrder.CreatorCorporation = detailById.Item.Name
	q.CreatorDepartmentId = detailById.DepartmentId
	q.CreatorDepartment = detailById.DepartmentName
	q.TopCorporationName = detailById.GroupName

	q.WorkOrder.ApplyStatus = util.ApplyStatusForDoing
	if q.WorkOrder.RepairMethod == util.WorkOrderRepairMethodForNone {
		q.WorkOrder.RepairMethod = util.WorkOrderRepairMethodForFix
	}

	errCode := AddDeviceWorkOrder(ctx, &q, user)
	if errCode != response.SUCCESS {
		return response.Error(rsp, errCode)
	}
	return response.Success(rsp, nil)
}

func AddDeviceWorkOrder(ctx context.Context, param *WorkOrder, user *auth.AuthUser) string {
	//if param.WorkOrder.RepairClassDictId == 0 || param.WorkOrder.RepairClassDictKey == "" ||
	//	param.WorkOrder.FkRepairDeviceId == 0 || param.WorkOrder.RepairDevice == "" || param.WorkOrder.Content == "" ||
	//	param.WorkOrder.MaintainerFactoryId == 0 || param.WorkOrder.MaintainerFactoryKey == "" {
	//	log.Error("q.WorkOrder.RepairClassDictId == 0")
	//	return response.ParamsMissing
	//}

	param.WorkOrder.ActualDevicesId = param.WorkOrder.FkRepairDeviceId
	//判断设备是否在报修中
	//isUsed, _ := (&workOrderModel.WorkOrder{}).IsUsed(param.WorkOrder.FkRepairDeviceId)
	//if isUsed {
	//	return "OP7514"
	//}

	param.WorkOrder.FormStep = util.ProcessFormStepStart

	//查询大类
	classDict := (&commonModel.Dict{}).FirstById(param.WorkOrder.RepairClassDictId)

	var vehicleCorporationCode string
	if classDict.ObjectType == util.DeviceAssociationObjectForVehicle {
		// 获取车属线路、机构
		vehicleItem := rpc.GetVehicleWithLicense(ctx, &protooetvehicle.GetVehicleWithLicenseRequest{
			License:       param.License,
			CorporationId: user.TopCorporationId,
		})
		if vehicleItem == nil {
			log.Error("vehicleItem not found")
			return response.ParamsInvalid
		}

		param.WorkOrder.LineId = vehicleItem.LineId
		param.WorkOrder.VehicleCorporationId = vehicleItem.SonCorporationId

		lineItem, _ := rpc.GetLineWithId(ctx, param.WorkOrder.LineId)
		if lineItem != nil {
			param.WorkOrder.Line = lineItem.Name
		}

		corp := rpc.GetCorporationById(ctx, param.WorkOrder.VehicleCorporationId)
		if corp == nil {
			log.Error("GetCorporationById is nil")
			return response.ParamsInvalid
		}
		param.WorkOrder.VehicleCorporation = corp.Name
		vehicleCorporationCode = corp.Virtual

	}

	if param.WorkOrder.RepairMethod == util.WorkOrderRepairMethodForFleet {
		param.WorkOrder.FormStep = util.ProcessFormStepTwo
	}

	// 根据报修设备明细id FkRepairDeviceId 获取责任部门
	deviceDetail := (&workOrderModel.DeviceDetail{}).FirstById(param.WorkOrder.FkRepairDeviceId)
	if deviceDetail.Id == 0 {
		log.Error("DeviceDetail FirstById is error")
		return response.DbQueryFail
	}
	deviceModel := (&workOrderModel.DeviceModel{}).FirstBy(deviceDetail.DeviceModelId)

	deviceBatchCode := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(deviceDetail.DeviceModelBatchCodeId)

	// 获取责任部门(机构)编号
	corId := rpc.GetCorporationDetailById(ctx, deviceModel.LiableCorporationId)
	if corId == nil {
		log.Error("rpc GetCorporationDetailById not found")
		oetStaffItem := rpc.GetStaffWithId(ctx, deviceModel.LiableStaffId)
		corId = rpc.GetCorporationDetailById(ctx, oetStaffItem.CorporationId)
		if corId == nil {
			log.Error("rpc GetCorporationDetailById not found[2]")
			return response.FAIL
		}
	}

	// 判断是否保
	now := time.Now()
	nowDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	expireAt := time.Time(deviceBatchCode.ExpireAt)
	expireDay := time.Date(expireAt.Year(), expireAt.Month(), expireAt.Day(), 0, 0, 0, 0, time.Local)
	if expireDay.Sub(nowDay) >= 0 {
		// 过保时间大于现在时间 => 在保
		param.WorkOrder.IsInWarranty = util.StatusForTrue
	} else {
		param.WorkOrder.IsInWarranty = util.StatusForFalse
	}

	var factoryMainUserAccounts []string
	//获取厂家的指派负责人
	factoryUsers := (&setting.DeviceFactoryHasUser{}).GetBy(param.WorkOrder.MaintainerFactoryId, 0, []int64{util.DeviceFactoryUserSceneTypeForMain})
	for _, factoryUser := range factoryUsers {
		userInfo := rpc.GetUserInfoById(ctx, factoryUser.UserId)
		if userInfo != nil {
			factoryMainUserAccounts = append(factoryMainUserAccounts, userInfo.Username)
		}
	}

	tx := model.DB().Begin()

	// 判断是新建还是重新发起
	if param.IsReStart {
		// 更新表单
		err := (&param.WorkOrder).UpdateRepair(tx)
		if err != nil {
			tx.Rollback()
			log.Error("WorkOrder UpdateRepair err =", err)
			return response.DbSaveFail
		}
		// 清空备件
		err = (&workOrderModel.WorkOrderRepairSparePart{}).TransactionDelete(tx, param.WorkOrder.Id)
		if err != nil {
			tx.Rollback()
			log.Error("WorkOrderRepairSparePart TransactionDelete err =", err)
			return response.DbDeleteFail
		}
		// 更新状态
		err = (&param.WorkOrder).UpdatesTx(tx, map[string]interface{}{"ApplyStatus": util.ApplyStatusForDoing})
		if err != nil {
			tx.Rollback()
			log.Error("UpdatesTx err =", err)
			return response.DbSaveFail
		}
		// 重新发起
		// 获取processId
		var process processModel.LbpmApplyProcess
		err = (&process).GetProcessByItemId(param.Id, param.TableName())
		if err != nil {
			tx.Rollback()
			log.Error("GetProcessByItemId err =", err)
			return response.DbSaveFail
		}

		formData := map[string]interface{}{
			"RepairMethod":                 param.WorkOrder.RepairMethod,
			"FleetCode":                    vehicleCorporationCode,
			"DeviceStatus":                 0,
			"IsExpire":                     param.WorkOrder.IsInWarranty,                // --- lbpm "设备是否在保" // 1在保 2过保
			"HandlerName":                  deviceModel.HandlerFactoryKey,               // --- lbpm "设备保修方"
			"Maintainer":                   deviceBatchCode.MaintainerFactoryKey,        // --- lbpm "设备过保维修方"
			"IsMaterialMissing":            0,                                           // 是否缺物料 // 流程中更改数据
			"AssignMainUserAccounts":       strings.Join(factoryMainUserAccounts, ",,"), //指派负责人
			"AssignFirstStepUserAccounts":  "",                                          //分配的第一级
			"AssignSecondStepUserAccounts": "",                                          //分配的第二级
			"RepairUserAccounts":           "",                                          //指派的维修人员 流程中指派
		}

		// 保存json参数
		processParams := CreateProcessParam{
			RepairCategoryDictId:  deviceModel.DeviceCategoryDictId,  // 为消息中心添加额外字段
			RepairCategoryDictKey: deviceModel.DeviceCategoryDictKey, // 为消息中心添加额外字段
			WorkOrder:             param.WorkOrder,
		}

		processParam, err := json.Marshal(processParams)
		if err != nil {
			tx.Rollback()
			log.Error("Marshal err =", err)
			return response.FAIL
		}

		err = processService.RestartProcess(user, process.ProcessId, string(processParam), formData)
		if err != nil {
			tx.Rollback()
			log.Error("RestartProcess err =", err)
			return response.FAIL
		}
	} else {
		err := (&param.WorkOrder).Add(tx)
		if err != nil {
			tx.Rollback()
			log.Error("WorkOrder Add err =", err)
			return response.DbSaveFail
		}

		formData := map[string]interface{}{
			"RepairMethod":                 param.WorkOrder.RepairMethod,
			"FleetCode":                    vehicleCorporationCode,
			"DeviceStatus":                 0,
			"IsExpire":                     param.WorkOrder.IsInWarranty,                // --- lbpm "设备是否在保" // 1在保 2过保
			"HandlerName":                  deviceModel.HandlerFactoryKey,               // --- lbpm "设备供保修方"
			"Maintainer":                   deviceBatchCode.MaintainerFactoryKey,        // --- lbpm "设备过保维修方"
			"IsMaterialMissing":            0,                                           // 是否缺物料 // 流程中更改数据
			"AssignMainUserAccounts":       strings.Join(factoryMainUserAccounts, ",,"), //指派负责人
			"AssignFirstStepUserAccounts":  "",                                          //分配的第一级
			"AssignSecondStepUserAccounts": "",                                          //分配的第二级
			"RepairUserAccounts":           "",                                          //指派的维修人员 流程中指派
		}

		// 保存json参数
		processParams := CreateProcessParam{
			RepairCategoryDictId:  deviceModel.DeviceCategoryDictId,  // 为消息中心添加额外字段
			RepairCategoryDictKey: deviceModel.DeviceCategoryDictKey, // 为消息中心添加额外字段
			WorkOrder:             param.WorkOrder,
		}

		processParam, err := json.Marshal(processParams)
		if err != nil {
			tx.Rollback()
			log.Error("Marshal err =", err)
			return response.FAIL
		}

		// 关联对象所属机构， 车辆->车属机构 站台、场站-> Group
		var corpName string
		if deviceDetail.AssociationObjectType == util.DeviceAssociationObjectForVehicle {
			corpName = param.WorkOrder.VehicleCorporation
		} else {
			corpName = param.TopCorporationName
		}

		// 报修大类/报修种类/报修关联对象机构/报修关联对象/报修人
		processTitle := fmt.Sprintf("%s/%s/%s/%s/%s/设备报修工单审批流程", deviceModel.DeviceClassDictKey, deviceModel.DeviceCategoryDictKey, corpName, deviceDetail.AssociationObjectName, param.WorkOrder.CreatorUserName)
		_, err = processService.NewDispatchProcess(user, config.DeviceWorkOrderReportFormTemplate, processTitle, param.WorkOrder.Id, (&param.WorkOrder).TableName(), param.WorkOrder.ApplyStatusFieldName(), string(processParam), formData)

		if err != nil {
			tx.Rollback()
			log.Error("DispatchProcess err =", err)
			return response.FAIL
		}
	}
	tx.Commit()
	return response.SUCCESS
}

type WorkOrderListItem struct {
	workOrderModel.WorkOrder
	TransferCorporation          string           // 流转至机构
	TransferAt                   *model.LocalTime // 流转到达时间
	TransferUserName             string           // 流转到人员
	Price                        int64            // *报修设备总金额 单位分 所选所有备件金额
	DeviceCategoryDictId         int64            // 设备种类id
	DeviceCategoryDictKey        string           // 设备种类
	DeviceModelName              string           // 设备型号
	DoneAt                       *model.LocalTime // 流程结束时间
	IdStr                        string           // 工单id 兼容wx小程序大整型
	CreatorDepartmentIdStr       string           // 创建部门id 兼容wx小程序大整型
	DeviceClassDictId            int64            // 报修设备大类id (协议定为此字段) 取值为 workOrderModel.WorkOrder.RepairClassDictId
	DeviceClassDictKey           string           // 报修设备大类 (协议定为此字段) 取值为 workOrderModel.WorkOrder.RepairClassDictKey
	DeviceClassDictIdStr         string
	DeviceCategoryDictIdStr      string
	ProcessId                    string
	RepairDeviceFactoryCode      string                   //报修设备厂家编号
	ActualDeviceCategoryName     string                   //实际设备种类
	ActualDeviceCode             string                   //实际设备编号
	ActualDeviceFactoryCode      string                   //实际设备厂家编号
	RepairChildDeviceModel       string                   //报修子设备型号
	RepairChildDeviceCode        string                   //报修子设备编号
	RepairChildDeviceFactoryCode string                   //报修子设备厂家编号
	MaterialInfo                 []map[string]interface{} //物料信息
	ReplaceDeviceInfo            []map[string]interface{} //更换设备信息
	ReplaceChildDeviceInfo       []map[string]interface{} //更换子设备信息
	DeviceBatchCode              string
	DeviceExpireAt               model.LocalTime
	BrandFactoryKey              string
	HandlerFactoryKey            string
	SupplierFactoryKey           string
	Children                     []WorkOrderListItem ` json:"children"`
}

func (w *WorkOrder) List(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q WorkOrder
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	user := auth.User(ctx).GetUser()

	var workOrderTypes []string
	switch q.WorkOrder.Type {
	case 0:
		workOrderTypes = []string{config.DeviceWorkOrderReportFormTemplate, config.PetitionWorkOrderReportFormTemplate}
	case 1:
		workOrderTypes = []string{config.PetitionWorkOrderReportFormTemplate}
	case 2:
		workOrderTypes = []string{config.DeviceWorkOrderReportFormTemplate}
	default:
		log.Error("q.WorkOrder.Type err, it =", q.WorkOrder.Type)
		return response.Error(rsp, response.ParamsInvalid)
	}

	var workOrderItems []WorkOrderListItem

	list, totalCount, err := (&workOrderModel.WorkOrder{}).MyList(q.CustomStatus, user.Id, workOrderTypes, q.Code, int64(q.ApplyStatus), q.CreatorUserId, q.ProcessCurrentHandler, q.Content, q.ObjectName, q.Line, time.Time(q.StartAt), time.Time(q.EndAt), q.Paginator)

	if err != nil {
		log.Error("MyList err =", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	for _, order := range list {
		item := WorkOrderListItem{
			WorkOrder:              order,
			IdStr:                  strconv.FormatInt(order.Id, 10),
			CreatorDepartmentIdStr: strconv.FormatInt(order.CreatorDepartmentId, 10),
		}

		var process processModel.LbpmApplyProcess
		err = process.GetProcessByItemId(order.Id, order.TableName())
		if err == nil {
			handlers := (&processModel.LbpmApplyProcessHasHandler{}).GetProcessHandlers(process.FormInstanceId)
			if len(handlers) > 0 {
				item.TransferAt = handlers[0].StartAt
			}

			item.DoneAt = process.DoneAt
			item.TransferUserName = process.CurrentHandlerUserName
		}

		// 获取报修设备大类、种类
		deviceDetail := (&workOrderModel.DeviceDetail{}).FirstById(order.FkRepairDeviceId)
		deviceModel := (&workOrderModel.DeviceModel{}).FirstBy(deviceDetail.DeviceModelId)
		item.DeviceCategoryDictId = deviceModel.DeviceCategoryDictId
		item.DeviceCategoryDictKey = deviceModel.DeviceCategoryDictKey
		item.DeviceClassDictId = deviceModel.DeviceClassDictId
		item.DeviceClassDictKey = deviceModel.DeviceClassDictKey

		workOrderItems = append(workOrderItems, item)
	}

	data := map[string]interface{}{
		"Items":      workOrderItems,
		"TotalCount": totalCount,
	}
	return response.Success(rsp, data)
}

func (w *WorkOrder) ListAll(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q WorkOrder
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	// compatible with bigint
	if q.CreatorDepartmentIdStr != "" {
		q.CreatorDepartmentId, _ = strconv.ParseInt(q.CreatorDepartmentIdStr, 10, 64)
	}

	// compatible with bigint
	if len(q.DeviceClassDictIdStrs) > 0 {
		q.DeviceClassDictIds = util.StringSliceToInt64Slice(q.DeviceClassDictIdStrs)
	}

	// compatible with bigint
	if q.DeviceCategoryDictIdStr != "" {
		q.DeviceCategoryDictId, _ = strconv.ParseInt(q.DeviceCategoryDictIdStr, 10, 64)
	}

	groupId := auth.User(ctx).GetTopCorporationId()

	var vehicleDeviceCorpIs []int64 // 车载设备大类 车属机构
	if len(q.DeviceClassDictIds) > 0 {
		classDict := (&commonModel.Dict{}).FirstById(q.DeviceClassDictIds[0])
		if err == nil {
			if classDict.ObjectType == util.DeviceAssociationObjectForVehicle {
				vehicleDeviceCorpIs = service.AuthCorporationIdProvider(ctx, nil) // 查询车载设备工单列表 需要权限
			}
		}
	}

	workOrders, totalCount, err := (&workOrderModel.WorkOrder{}).AllList(groupId, vehicleDeviceCorpIs,
		q.Type, q.Title, q.Code, int64(q.MaintenanceType), q.ApplyStatus, q.CreatorUserName, q.CreatorDepartmentId,
		q.CreatorCorporationId, q.License, q.UpParking, q.Station, q.ObjectName, q.DeviceClassDictIds,
		q.DeviceCategoryDictId, q.DeviceModelName, q.ProcessCurrentHandler, q.Content, q.RepairDevice,
		q.Line, q.ProcessTotalStartNum, q.ProcessTotalEndNum, q.RepairStartNum, q.RepairEndNum, q.RepairConfirmStartNum,
		q.RepairConfirmEndNum, time.Time(q.StartAt), time.Time(q.EndAt), 0, q.Paginator)

	if err != nil {
		log.Error("AllList err =", err)
		return response.Error(rsp, response.DbQueryFail)
	}
	var workOrderItems []WorkOrderListItem
	for index, pOrder := range workOrders {
		order := pOrder
		item := WorkOrderListItem{
			WorkOrder:              order,
			IdStr:                  strconv.FormatInt(order.Id, 10),
			CreatorDepartmentIdStr: strconv.FormatInt(order.CreatorDepartmentId, 10),
			DeviceClassDictIdStr:   strconv.FormatInt(order.RepairClassDictId, 10),
			DeviceClassDictId:      order.RepairClassDictId,
			DeviceClassDictKey:     order.RepairClassDictKey,
		}

		spareParts, err := (&workOrderModel.WorkOrderRepairSparePart{}).GetByWorkOrderId(order.Id)
		if err != nil {
			log.Error("GetByWorkOrderId err =", err)
			//return errors.New("GetByWorkOrderId error")
		}

		var materials []workOrderModel.WorkOrderRepairSparePart
		var replaceDevices []workOrderModel.WorkOrderRepairSparePart
		for _, part := range spareParts {
			item.Price += part.Price
			if part.FkDevicesId > 0 {
				replaceDevices = append(replaceDevices, part)
			} else {
				materials = append(materials, part)
			}
		}

		if len(materials) > 0 {
			for _, material := range materials {
				item.MaterialInfo = append(item.MaterialInfo, map[string]interface{}{
					"MaterialName":  material.Name,
					"MaterialPrice": material.Price,
					"MaterialCount": material.Num,
					"MaterialMoney": material.Num * material.Price,
				})
			}
		}

		if len(replaceDevices) > 0 {
			for rd := range replaceDevices {
				replaceDevice := (&workOrderModel.DeviceDetail{}).FirstById(replaceDevices[rd].FkDevicesId)
				replaceDeviceModel := (&workOrderModel.DeviceModel{}).FirstBy(replaceDevice.DeviceModelId)
				replaceDeviceModelBatchCode := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(replaceDevice.DeviceModelBatchCodeId)

				//purchaseAt, _ := time.Parse("20060102", strconv.FormatInt(replaceDeviceModelBatchCode.PurchaseAt, 10))
				item.ReplaceDeviceInfo = append(item.ReplaceDeviceInfo, map[string]interface{}{
					"ReplaceDeviceCode":       replaceDevice.Code,
					"ReplaceDeviceModel":      replaceDeviceModel.Name,
					"ReplaceDevicePurchaseAt": time.Time(replaceDeviceModelBatchCode.PurchaseAt).Format("2006-01-02"),
					"ReplaceDeviceExpireAt":   replaceDeviceModelBatchCode.ExpireAt,
					"ReplaceDeviceBatch":      replaceDeviceModelBatchCode.BatchCode,
					"ReplaceDeviceMoney":      replaceDevices[rd].Num * replaceDevices[rd].Price,
					"ReplaceDeviceBrand":      replaceDeviceModel.BrandFactoryKey,
					"ReplaceDeviceSupplier":   replaceDeviceModel.SupplierFactoryKey,
				})
			}
		}

		replaceChildDevices := (&workOrderModel.WorkOrderReplaceChildDevice{}).GetByWorkOrderId(order.Id)
		for _, part := range replaceChildDevices {
			item.Price += part.Price

			childDevice := (&workOrderModel.ChildDevice{}).FirstById(part.NewChildDeviceId)
			var repairChildDeviceModel string
			if childDevice.ModelId > 0 {
				childDeviceModel := (&workOrderModel.ChildDeviceCate{}).FirstById(childDevice.ModelId)
				repairChildDeviceModel = childDeviceModel.Name
			}
			item.ReplaceChildDeviceInfo = append(item.ReplaceChildDeviceInfo, map[string]interface{}{
				"ReplaceChildDeviceModel":       repairChildDeviceModel,
				"ReplaceChildDeviceCode":        childDevice.Code,
				"ReplaceChildDeviceFactoryCode": childDevice.FactoryCode,
				"ReplaceChildDeviceMoney":       part.Price,
			})
		}

		// 获取设备型号、设备明细信息
		deviceDetail := (&workOrderModel.DeviceDetail{}).FirstById(order.FkRepairDeviceId)
		deviceModel := (&workOrderModel.DeviceModel{}).FirstBy(deviceDetail.DeviceModelId)
		deviceModelBatch := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(deviceDetail.DeviceModelBatchCodeId)
		item.DeviceCategoryDictId = deviceModel.DeviceCategoryDictId
		item.DeviceCategoryDictIdStr = strconv.FormatInt(deviceModel.DeviceCategoryDictId, 10)
		item.DeviceCategoryDictKey = deviceModel.DeviceCategoryDictKey
		item.DeviceModelName = deviceModel.Name
		item.RepairDeviceFactoryCode = deviceDetail.FactoryCode
		item.DeviceBatchCode = deviceModelBatch.BatchCode
		item.DeviceExpireAt = deviceModelBatch.ExpireAt
		item.BrandFactoryKey = deviceModel.BrandFactoryKey
		item.HandlerFactoryKey = deviceModel.HandlerFactoryKey
		item.SupplierFactoryKey = deviceModel.SupplierFactoryKey

		//实际报修设备
		if order.ActualDevicesId > 0 {
			var actualDevice workOrderModel.DeviceDetail
			actualDeviceDetail := (&workOrderModel.DeviceDetail{}).FirstById(order.ActualDevicesId)
			actualDeviceModel := (&workOrderModel.DeviceModel{}).FirstBy(actualDeviceDetail.DeviceModelId)
			item.ActualDeviceCategoryName = actualDeviceModel.DeviceCategoryDictKey
			item.ActualDeviceCode = actualDevice.Code
			item.ActualDeviceFactoryCode = actualDevice.FactoryCode
		}

		//报修子设备
		if order.ChildDeviceId > 0 {
			childDevice := (&workOrderModel.ChildDevice{}).FirstById(order.ChildDeviceId)
			item.RepairChildDeviceCode = childDevice.Code
			item.RepairChildDeviceFactoryCode = childDevice.FactoryCode

			if childDevice.ModelId > 0 {
				childDeviceModel := (&workOrderModel.ChildDeviceCate{}).FirstById(childDevice.ModelId)
				item.RepairChildDeviceModel = childDeviceModel.Name
			}
		}

		var process processModel.LbpmApplyProcess
		err = (&process).GetProcessByItemId(order.Id, order.TableName())
		if err != nil {
			log.ErrorFields("process.GetProcessByItemId error", map[string]interface{}{"err": err})
			//return
		}

		// 查询当前流程流转至哪个人，以及人归属的机构
		handlers := (&processModel.LbpmApplyProcessHasHandler{}).GetProcessHandlers(process.FormInstanceId)
		if len(handlers) > 0 {
			item.TransferAt = handlers[0].StartAt
		}
		item.DoneAt = process.DoneAt
		item.TransferUserName = process.CurrentHandlerUserName

		workOrderItems = append(workOrderItems, item)

		children, _, _ := (&workOrderModel.WorkOrder{}).AllList(groupId, vehicleDeviceCorpIs,
			q.Type, q.Title, q.Code, int64(q.MaintenanceType), q.ApplyStatus, q.CreatorUserName, q.CreatorDepartmentId,
			q.CreatorCorporationId, q.License, q.UpParking, q.Station, q.ObjectName, q.DeviceClassDictIds,
			q.DeviceCategoryDictId, q.DeviceModelName, q.ProcessCurrentHandler, q.Content, q.RepairDevice,
			q.Line, q.ProcessTotalStartNum, q.ProcessTotalEndNum, q.RepairStartNum, q.RepairEndNum, q.RepairConfirmStartNum,
			q.RepairConfirmEndNum, time.Time(q.StartAt), time.Time(q.EndAt), pOrder.Id, model.Paginator{Limit: 0})
		if children != nil {
			for _, cOrder := range children {
				order := cOrder
				item := WorkOrderListItem{
					WorkOrder:              order,
					IdStr:                  strconv.FormatInt(order.Id, 10),
					CreatorDepartmentIdStr: strconv.FormatInt(order.CreatorDepartmentId, 10),
					DeviceClassDictIdStr:   strconv.FormatInt(order.RepairClassDictId, 10),
					DeviceClassDictId:      order.RepairClassDictId,
					DeviceClassDictKey:     order.RepairClassDictKey,
				}

				spareParts, err := (&workOrderModel.WorkOrderRepairSparePart{}).GetByWorkOrderId(order.Id)
				if err != nil {
					log.Error("GetByWorkOrderId err =", err)
					//return errors.New("GetByWorkOrderId error")
				}

				var materials []workOrderModel.WorkOrderRepairSparePart
				var replaceDevices []workOrderModel.WorkOrderRepairSparePart
				for _, part := range spareParts {
					item.Price += part.Price
					if part.FkDevicesId > 0 {
						replaceDevices = append(replaceDevices, part)
					} else {
						materials = append(materials, part)
					}
				}

				if len(materials) > 0 {
					for _, material := range materials {
						item.MaterialInfo = append(item.MaterialInfo, map[string]interface{}{
							"MaterialName":  material.Name,
							"MaterialPrice": material.Price,
							"MaterialCount": material.Num,
							"MaterialMoney": material.Num * material.Price,
						})
					}
				}

				if len(replaceDevices) > 0 {
					for rd := range replaceDevices {
						replaceDevice := (&workOrderModel.DeviceDetail{}).FirstById(replaceDevices[rd].FkDevicesId)
						replaceDeviceModel := (&workOrderModel.DeviceModel{}).FirstBy(replaceDevice.DeviceModelId)
						replaceDeviceModelBatchCode := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(replaceDevice.DeviceModelBatchCodeId)

						//purchaseAt, _ := time.Parse("20060102", strconv.FormatInt(replaceDeviceModelBatchCode.PurchaseAt, 10))
						item.ReplaceDeviceInfo = append(item.ReplaceDeviceInfo, map[string]interface{}{
							"ReplaceDeviceCode":       replaceDevice.Code,
							"ReplaceDeviceModel":      replaceDeviceModel.Name,
							"ReplaceDevicePurchaseAt": time.Time(replaceDeviceModelBatchCode.PurchaseAt).Format("2006-01-02"),
							"ReplaceDeviceExpireAt":   replaceDeviceModelBatchCode.ExpireAt,
							"ReplaceDeviceBatch":      replaceDeviceModelBatchCode.BatchCode,
							"ReplaceDeviceMoney":      replaceDevices[rd].Num * replaceDevices[rd].Price,
							"ReplaceDeviceBrand":      replaceDeviceModel.BrandFactoryKey,
							"ReplaceDeviceSupplier":   replaceDeviceModel.SupplierFactoryKey,
						})
					}
				}

				replaceChildDevices := (&workOrderModel.WorkOrderReplaceChildDevice{}).GetByWorkOrderId(order.Id)
				for _, part := range replaceChildDevices {
					item.Price += part.Price

					childDevice := (&workOrderModel.ChildDevice{}).FirstById(part.NewChildDeviceId)
					var repairChildDeviceModel string
					if childDevice.ModelId > 0 {
						childDeviceModel := (&workOrderModel.ChildDeviceCate{}).FirstById(childDevice.ModelId)
						repairChildDeviceModel = childDeviceModel.Name
					}
					item.ReplaceChildDeviceInfo = append(item.ReplaceChildDeviceInfo, map[string]interface{}{
						"ReplaceChildDeviceModel":       repairChildDeviceModel,
						"ReplaceChildDeviceCode":        childDevice.Code,
						"ReplaceChildDeviceFactoryCode": childDevice.FactoryCode,
						"ReplaceChildDeviceMoney":       part.Price,
					})
				}

				// 获取设备型号、设备明细信息
				deviceDetail := (&workOrderModel.DeviceDetail{}).FirstById(order.FkRepairDeviceId)
				deviceModel := (&workOrderModel.DeviceModel{}).FirstBy(deviceDetail.DeviceModelId)
				deviceModelBatch := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(deviceDetail.DeviceModelBatchCodeId)
				item.DeviceCategoryDictId = deviceModel.DeviceCategoryDictId
				item.DeviceCategoryDictIdStr = strconv.FormatInt(deviceModel.DeviceCategoryDictId, 10)
				item.DeviceCategoryDictKey = deviceModel.DeviceCategoryDictKey
				item.DeviceModelName = deviceModel.Name
				item.RepairDeviceFactoryCode = deviceDetail.FactoryCode
				item.DeviceBatchCode = deviceModelBatch.BatchCode
				item.DeviceExpireAt = deviceModelBatch.ExpireAt
				item.BrandFactoryKey = deviceModel.BrandFactoryKey
				item.HandlerFactoryKey = deviceModel.HandlerFactoryKey
				item.SupplierFactoryKey = deviceModel.SupplierFactoryKey

				//实际报修设备
				if order.ActualDevicesId > 0 {
					var actualDevice workOrderModel.DeviceDetail
					actualDeviceDetail := (&workOrderModel.DeviceDetail{}).FirstById(order.ActualDevicesId)
					actualDeviceModel := (&workOrderModel.DeviceModel{}).FirstBy(actualDeviceDetail.DeviceModelId)
					item.ActualDeviceCategoryName = actualDeviceModel.DeviceCategoryDictKey
					item.ActualDeviceCode = actualDevice.Code
					item.ActualDeviceFactoryCode = actualDevice.FactoryCode
				}

				//报修子设备
				if order.ChildDeviceId > 0 {
					childDevice := (&workOrderModel.ChildDevice{}).FirstById(order.ChildDeviceId)
					item.RepairChildDeviceCode = childDevice.Code
					item.RepairChildDeviceFactoryCode = childDevice.FactoryCode

					if childDevice.ModelId > 0 {
						childDeviceModel := (&workOrderModel.ChildDeviceCate{}).FirstById(childDevice.ModelId)
						item.RepairChildDeviceModel = childDeviceModel.Name
					}
				}

				var process processModel.LbpmApplyProcess
				err = (&process).GetProcessByItemId(order.Id, order.TableName())
				if err != nil {
					log.ErrorFields("process.GetProcessByItemId error", map[string]interface{}{"err": err})
					//return
				}

				// 查询当前流程流转至哪个人，以及人归属的机构
				handlers := (&processModel.LbpmApplyProcessHasHandler{}).GetProcessHandlers(process.FormInstanceId)
				if len(handlers) > 0 {
					item.TransferAt = handlers[0].StartAt
				}
				item.DoneAt = process.DoneAt
				item.TransferUserName = process.CurrentHandlerUserName

				workOrderItems[index].Children = append(workOrderItems[index].Children, item)
			}
		}

	}

	data := map[string]interface{}{
		"Items":      workOrderItems,
		"TotalCount": totalCount,
	}
	return response.Success(rsp, data)

}

type MergeReq struct {
	MainWorkOrderId int64   `json:"MainWorkOrderId"  validate:"required"`
	ChildrenIds     []int64 `json:"ChildrenIds"  validate:"required"`
}

func (w *WorkOrder) Merge(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var form MergeReq
	err := json.Unmarshal([]byte(req.Body), &form)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	err = util.Validator().Struct(&form)
	if err != nil {
		return response.Error(rsp, response.ParamsMissing)
	}
	if len(form.ChildrenIds) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	// 合并工单
	err = (&workOrderModel.WorkOrder{}).Merge(form.MainWorkOrderId, form.ChildrenIds)
	if err != nil {
		log.Error("Merge err=", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	return response.Success(rsp, nil)
}

type GetDetailRepairRsp struct {
	workOrderModel.WorkOrder
	SparePart        []SparePart
	IsProcessHandler bool

	// 导出字段
	ExportResult          string // 处理结果
	ExportUndertaker      string // 承办单位
	ExportOpLeader        string // 签发领导
	ExportOpUser          string // 经办人
	RepairCategoryDictId  int64  // 报修设备种类id
	RepairCategoryDictKey string // 报修设备种类
	RepairModelName       string // 报修设备型号
	RepairFactoryCode     string //维修设备厂家编号
	ActualCategoryDictId  int64  // 实际设备种类id
	ActualCategoryDictKey string // 实际设备种类
	ActualCode            string // 实际设备编号
	ActualFactoryCode     string //实际设备厂家编号

	IdStr                   string // 工单id 兼容wx小程序大整型
	CreatorCorporationIdStr string // 创建人所属机构id 兼容wx小程序大整型
	CreatorDepartmentIdStr  string // 创建部门id 兼容wx小程序大整型
	PetitionOriginDictIdStr string // *信访来源字典id 兼容wx小程序大整型
	PetitionTypeDictIdStr   string // 信访分类字典id 兼容wx小程序大整型
	RecvCorporationIdStr    string // *去向部门 兼容wx小程序大整型

	RepairClassDictIdStr    string          // *设备大类字典id 兼容wx小程序大整型
	RepairCategoryDictIdStr string          // 设备种类id 兼容wx小程序大整型
	FkRepairDeviceIdStr     string          // *报修设备id 兼容wx小程序大整型
	MaintainerDictIdStr     string          // *维修方字典id 兼容wx小程序大整型
	ActualDevicesIdStr      string          // 实际设备(明细)id 兼容wx小程序大整型
	ActualCategoryDictIdStr string          // 实际设备种类id 兼容wx小程序大整型
	ExpireAt                model.LocalTime // 设备过保时间
	ChildDeviceCode         string          `json:"ChildDeviceCode" gorm:"-"`        //子设备编号
	ChildDeviceFactoryCode  string          `json:"ChildDeviceFactoryCode" gorm:"-"` //子设备厂家编号
	RepairMethod            int64           `json:"RepairMethod"`                    //是否直接报修 1

	ReplaceChildDevices []workOrderModel.WorkOrderReplaceChildDevice `json:"ReplaceChildDevices"`
}

type SparePart struct {
	CurrentUserId   int64
	CurrentUserName string
	Device          []WorkOrderRepairSpareDeviceDetail
}
type WorkOrderRepairSpareDeviceDetail struct {
	workOrderModel.WorkOrderRepairSparePart
	ModelName               string          // 设备型号
	PurchaseAt              model.LocalTime // 采购日期
	ExpireAt                model.LocalTime // 过保时间
	Batch                   string          // 入库批次（备注）
	DeviceCategoryDictIdStr string          // 设备种类id 兼容wx小程序大整型
	FkDevicesIdStr          string          // 设备明细id =0为物料 >0为设备 兼容wx小程序大整型
}

type ProcessParam struct {
	Param Param
}

type Param struct {
	AuditNote string `json:"auditNote"` // 评论
}

func (w *WorkOrder) GetDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q WorkOrder
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	// compatible with bigint
	if q.IdStr != "" {
		q.Id, _ = strconv.ParseInt(q.IdStr, 10, 64)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsInvalid)
	}

	var order workOrderModel.WorkOrder
	err = (&order).GetDetail(q.Id)
	if err != nil {
		log.Error("GetDetail err =", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	user := auth.User(ctx).GetUser()

	if order.Type == workOrderModel.PETITION_1 {

		var rspD GetDetailRepairRsp
		rspD.WorkOrder = order
		//rspD.SparePart = sps

		rspD.IdStr = strconv.FormatInt(order.Id, 10)
		rspD.CreatorCorporationIdStr = strconv.FormatInt(order.CreatorCorporationId, 10)
		rspD.CreatorDepartmentIdStr = strconv.FormatInt(order.CreatorDepartmentId, 10)
		rspD.PetitionOriginDictIdStr = strconv.FormatInt(order.PetitionOriginDictId, 10)
		rspD.PetitionTypeDictIdStr = strconv.FormatInt(order.PetitionTypeDictId, 10)
		rspD.RecvCorporationIdStr = strconv.FormatInt(order.RecvCorporationId, 10)

		// 获取审批人列表【抄送人、发起人除外】
		var lbpmApplyProcessHasHandlers []processModel.LbpmApplyProcessHasHandler
		lbpmApplyProcessHasHandlers, err = (&processModel.LbpmApplyProcessHasHandler{}).GetApprovingHandler(config.PetitionWorkOrderReportFormTemplate, rspD.WorkOrder.TemplateFormId(), rspD.WorkOrder.Id)
		if err != nil {
			log.Error("LbpmApplyProcessHasHandler GetApprovingHandler err =", err)
			return response.Error(rsp, response.DbQueryFail)
		}

		if len(lbpmApplyProcessHasHandlers) > 0 {

			var pp ProcessParam
			err = json.Unmarshal([]byte(lbpmApplyProcessHasHandlers[len(lbpmApplyProcessHasHandlers)-1].ProcessParam), &pp)
			if err != nil {
				log.Error("json.Unmarshal =", err)
				return response.Error(rsp, response.DbQueryFail)
			}

			rspD.ExportResult = pp.Param.AuditNote

			rspD.ExportOpUser = lbpmApplyProcessHasHandlers[0].UserName

			oetStaffItem := rpc.GetStaffWithId(ctx, lbpmApplyProcessHasHandlers[0].UserId)
			if oetStaffItem != nil {
				corporationItem := rpc.GetCorporationById(ctx, oetStaffItem.CorporationId)
				if corporationItem != nil {
					rspD.ExportUndertaker = corporationItem.Name
				}
			}

			rspD.ExportOpLeader = lbpmApplyProcessHasHandlers[len(lbpmApplyProcessHasHandlers)-1].UserName

		}

		rspD.IsProcessHandler = processService.CheckIsProcessRelater(rspD.WorkOrder.Id, rspD.WorkOrder.TemplateFormId(), user.Id)

		return response.Success(rsp, rspD)

	} else if order.Type == workOrderModel.DEVICE_REPAIR_2 {
		// 查询备件
		spareParts, err := (&workOrderModel.WorkOrderRepairSparePart{}).GetByWorkOrderId(q.Id)
		if err != nil {
			log.Error("GetByWorkOrderId err =", err)
			return response.Error(rsp, response.DbQueryFail)
		}

		tmp := make(map[int64][]workOrderModel.WorkOrderRepairSparePart) // map[currentStaffId 选择设备人]
		var tmpIdx []int64

		for _, part := range spareParts {
			if _, ok := tmp[part.CurrentUserId]; !ok {
				tmpIdx = append(tmpIdx, part.CurrentUserId)
			}
			tmp[part.CurrentUserId] = append(tmp[part.CurrentUserId], part)
		}

		var sps []SparePart
		for _, idx := range tmpIdx {
			sparePart := SparePart{
				CurrentUserId:   tmp[idx][0].CurrentUserId,
				CurrentUserName: tmp[idx][0].CurrentUserName,
			}

			for _, part := range tmp[idx] {
				spareDeviceDetail := WorkOrderRepairSpareDeviceDetail{
					WorkOrderRepairSparePart: part,
					DeviceCategoryDictIdStr:  strconv.FormatInt(part.DeviceCategoryDictId, 10),
				}

				if part.FkDevicesId > 0 {
					spareDeviceDetail.FkDevicesIdStr = strconv.FormatInt(part.FkDevicesId, 10)
					deviceDetail := (&workOrderModel.DeviceDetail{}).FirstById(part.FkDevicesId)
					deviceModel := (&workOrderModel.DeviceModel{}).FirstBy(deviceDetail.DeviceModelId)
					deviceBatchCode := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(deviceDetail.DeviceModelBatchCodeId)
					if err == nil {
						spareDeviceDetail.ModelName = deviceModel.Name
						spareDeviceDetail.PurchaseAt = deviceBatchCode.PurchaseAt
						spareDeviceDetail.ExpireAt = deviceBatchCode.ExpireAt
						spareDeviceDetail.Batch = deviceBatchCode.BatchCode
					}
				}

				sparePart.Device = append(sparePart.Device, spareDeviceDetail)
			}

			sps = append(sps, sparePart)
		}

		var rspD GetDetailRepairRsp
		rspD.WorkOrder = order
		rspD.SparePart = sps
		rspD.IsProcessHandler = processService.CheckIsProcessRelater(rspD.WorkOrder.Id, rspD.WorkOrder.TemplateFormId(), user.Id)

		rspD.IdStr = strconv.FormatInt(order.Id, 10)
		rspD.CreatorCorporationIdStr = strconv.FormatInt(order.CreatorCorporationId, 10)
		rspD.CreatorDepartmentIdStr = strconv.FormatInt(order.CreatorDepartmentId, 10)
		rspD.RepairClassDictIdStr = strconv.FormatInt(order.RepairClassDictId, 10)

		rspD.FkRepairDeviceIdStr = strconv.FormatInt(order.FkRepairDeviceId, 10)
		rspD.MaintainerDictIdStr = strconv.FormatInt(order.MaintainerFactoryId, 10)
		rspD.RepairMethod = order.RepairMethod

		orderDevice := (&workOrderModel.DeviceDetail{}).FirstById(order.FkRepairDeviceId)
		orderDeviceModel := (&workOrderModel.DeviceModel{}).FirstBy(orderDevice.DeviceModelId)
		rspD.RepairCategoryDictId = orderDeviceModel.DeviceCategoryDictId
		rspD.RepairCategoryDictIdStr = strconv.FormatInt(orderDeviceModel.DeviceCategoryDictId, 10)
		rspD.RepairCategoryDictKey = orderDeviceModel.DeviceCategoryDictKey
		rspD.RepairModelName = orderDeviceModel.Name
		rspD.RepairFactoryCode = orderDevice.FactoryCode

		actualDevice := (&workOrderModel.DeviceDetail{}).FirstById(order.ActualDevicesId)
		actualDeviceModel := (&workOrderModel.DeviceModel{}).FirstBy(actualDevice.DeviceModelId)
		actualDeviceBatchCode := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(actualDevice.DeviceModelBatchCodeId)
		rspD.ActualCategoryDictId = actualDeviceModel.DeviceCategoryDictId
		rspD.ActualCategoryDictKey = actualDeviceModel.DeviceCategoryDictKey
		rspD.ActualCode = actualDevice.Code
		rspD.ExpireAt = actualDeviceBatchCode.ExpireAt
		rspD.ActualFactoryCode = actualDevice.FactoryCode

		if order.ChildDeviceId > 0 {
			childDevice := (&workOrderModel.ChildDevice{}).FirstById(order.ChildDeviceId)
			rspD.ChildDeviceCode = childDevice.Code
			rspD.ChildDeviceFactoryCode = childDevice.FactoryCode
		}

		replaceChildren := (&workOrderModel.WorkOrderReplaceChildDevice{}).GetByWorkOrderId(order.Id)
		for r := range replaceChildren {
			oldChild := (&workOrderModel.ChildDevice{}).FirstById(replaceChildren[r].OldChildDeviceId)
			replaceChildren[r].OldChildDeviceCode = oldChild.Code
			replaceChildren[r].OldChildDeviceFactoryCode = oldChild.FactoryCode
			oldDeviceCate := (&workOrderModel.ChildDeviceCate{}).FirstById(oldChild.ModelId)
			replaceChildren[r].OldChildDeviceModelName = oldDeviceCate.Name

			newChild := (&workOrderModel.ChildDevice{}).FirstById(replaceChildren[r].NewChildDeviceId)
			replaceChildren[r].NewChildDeviceCode = newChild.Code
			replaceChildren[r].NewChildDeviceFactoryCode = newChild.FactoryCode
			newDeviceCate := (&workOrderModel.ChildDeviceCate{}).FirstById(newChild.ModelId)
			replaceChildren[r].NewChildDeviceModelName = newDeviceCate.Name
		}
		rspD.ReplaceChildDevices = replaceChildren

		return response.Success(rsp, rspD)
	} else {
		log.Error("order.Type err")
		return response.Error(rsp, response.Forbidden)
	}
}

// 选备件 改状态
func (w *WorkOrder) RepairChoose(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q WorkOrder
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	// compatible with bigint
	if q.IdStr != "" {
		q.Id, _ = strconv.ParseInt(q.IdStr, 10, 64)
	}

	// compatible with bigint
	if q.ActualDevicesIdStr != "" {
		q.ActualDevicesId, _ = strconv.ParseInt(q.ActualDevicesIdStr, 10, 64)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 获取流程
	var process processModel.LbpmApplyProcess
	err = (&process).GetFormInstanceId((&q.WorkOrder).TemplateFormId(), (&q.WorkOrder).TableName(), q.Id)
	if err != nil {
		log.Error("GetFormInstanceId err = ", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	if q.IsMaterialMissing == 0 {
		q.IsMaterialMissing = 2
	}

	err = processService.ResetProcessFormFieldValue(q.Id, (&q.WorkOrder).TableName(), map[string]interface{}{
		"IsMaterialMissing": q.IsMaterialMissing,
	})
	if err != nil {
		log.Error("ResetProcessFormFieldValue err = ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	// 获取报修工单
	workOrder := (&workOrderModel.WorkOrder{}).FirstBy(q.Id)
	if workOrder.Id == 0 {
		log.Error("WorkOrder.FirstBy err =", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	// 判断当前是否缺失物料
	if q.IsMaterialMissing == util.StatusForTrue {
		// 缺失物料修改状态
		err := workOrder.UpdateColumns(q.Id, map[string]interface{}{
			"IsMaterialMissing": q.IsMaterialMissing,
			"FormStep":          workOrder.FormStep + 1,
		})
		if err != nil {
			log.Error("WorkOrder UpdateColumns err=", err.Error())
			return response.Error(rsp, response.DbUpdateFail)
		}
		return response.Success(rsp, nil)
	}

	user := auth.User(ctx).GetUser()

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	var amount int64 = workOrder.Amount // 当前维修总金额 单位分
	// 存备件
	if len(q.Device) > 0 {
		corp := rpc.GetCorporationById(ctx, auth.User(ctx).GetCorporationId())
		if corp == nil {
			log.Error("corp == nil")
			return response.Error(rsp, response.Forbidden)
		}

		// 报修工单备件
		spareParts, err := (&workOrderModel.WorkOrderRepairSparePart{}).GetByWorkOrderId(q.Id)
		if err != nil {
			log.Error("GetByWorkOrderId err =", err)
			return response.Error(rsp, response.DbQueryFail)
		}

		var idxs []int // 记录重复参数(q.Device)的索引

		// 去除历史中设备物料
		for _, part := range spareParts {
			if part.FkDevicesId > 0 {
				for i2, d := range q.Device {
					if d.FkDevicesId == part.FkDevicesId {
						idxs = append(idxs, i2)
					}
				}
			} else {
				for i2, d := range q.Device {
					if d.Name == part.Name && d.Price == part.Price && d.Num == part.Num {
						idxs = append(idxs, i2)
						break // 相同的物料只去除一个
					}
				}
			}
		}

		var result []workOrderModel.WorkOrderRepairSparePart

		for i, part := range q.Device {
			var flag bool
			for _, idx := range idxs {
				if i == idx {
					flag = true
					break
				}
			}

			if flag {
				continue
			}

			// !--------------------------------
			//  >>>>>> 修改循环局部变量 part <<<<<<
			// !--------------------------------

			// compatible with bigint
			if part.DeviceCategoryDictIdStr != "" {
				part.DeviceCategoryDictId, _ = strconv.ParseInt(part.DeviceCategoryDictIdStr, 10, 64)
			}
			// compatible with bigint
			if part.FkDevicesIdStr != "" {
				part.FkDevicesId, _ = strconv.ParseInt(part.FkDevicesIdStr, 10, 64)
			}

			if part.Name == "" {
				log.Error("part.Name = ''")
				return response.Error(rsp, response.ParamsInvalid)
			}
			q.Device[i].Id = model.Id()
			q.Device[i].GroupId = user.TopCorporationId
			q.Device[i].FkWorkOrdersId = q.Id
			q.Device[i].CurrentUserId = user.Id
			q.Device[i].CurrentUserName = user.Name
			q.Device[i].CurrentCorporationId = corp.Id
			q.Device[i].CurrentCorporation = corp.Name

			q.Device[i].FkDevicesId = part.FkDevicesId
			q.Device[i].Name = part.Name
			q.Device[i].Code = part.Code
			q.Device[i].Price = part.Price
			q.Device[i].Num = part.Num
			q.Device[i].DeviceCategoryDictId = part.DeviceCategoryDictId
			q.Device[i].DeviceCategoryDictKey = part.DeviceCategoryDictKey

			amount += part.Num * part.Price
			result = append(result, q.Device[i])

		}

		if len(result) > 0 {
			err = (&workOrderModel.WorkOrderRepairSparePart{}).Add(tx, result)
			if err != nil {
				log.Error("WorkOrderRepairSparePart Add err =", err)
				return response.Error(rsp, response.DbSaveFail)
			}
		}

	}

	if len(q.ReplaceChildDevices) > 0 {
		var oldIds, newIds []int64
		for i := range q.ReplaceChildDevices {
			q.ReplaceChildDevices[i].WorkOrderId = workOrder.Id
			amount += q.ReplaceChildDevices[i].Price
			oldIds = append(oldIds, q.ReplaceChildDevices[i].OldChildDeviceId)
			newIds = append(newIds, q.ReplaceChildDevices[i].NewChildDeviceId)
		}
		err = (&workOrderModel.WorkOrderReplaceChildDevice{}).TransactionBatchCreate(tx, q.ReplaceChildDevices)
		if err != nil {
			log.Error("WorkOrderReplaceChildDevice.TransactionBatchCreate err =", err)
			return response.Error(rsp, response.DbSaveFail)
		}

		err = (&workOrderModel.ChildDevice{}).TransactionUpdateFreezeStatusByIds(tx, oldIds, workOrderModel.ChildDeviceFreezeStatusForUnBinding)
		if err != nil {
			log.Error("ChildDevice.TransactionUpdateFreezeStatusByIds old err =", err)
			return response.Error(rsp, response.DbSaveFail)
		}

		err = (&workOrderModel.ChildDevice{}).TransactionUpdateFreezeStatusByIds(tx, newIds, workOrderModel.ChildDeviceFreezeStatusForBinding)
		if err != nil {
			log.Error("ChildDevice.TransactionUpdateFreezeStatusByIds new err =", err)
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	log.ErrorFields("amount amount amount============", map[string]interface{}{"amount": amount})

	q.WorkOrder.Amount = amount

	if q.WorkOrder.MaintainerFactoryId == 0 {
		q.WorkOrder.MaintainerFactoryId = workOrder.MaintainerFactoryId
	}
	if q.WorkOrder.MaintainerFactoryKey == "" {
		q.WorkOrder.MaintainerFactoryKey = workOrder.MaintainerFactoryKey
	}
	if q.WorkOrder.ActualDevicesId == 0 {
		q.WorkOrder.ActualDevicesId = workOrder.ActualDevicesId
	}
	q.WorkOrder.FormStep = workOrder.FormStep + 1
	// 修改参数 更改状态
	err = (&q.WorkOrder).EditMaintenance(tx)
	if err != nil {
		log.Error("EditMaintenance err =", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	if q.WorkOrder.MaintenanceType == util.MaintenanceTypeForReplace {
		//更新流程参数
		err = processService.ResetProcessFormFieldValue(workOrder.Id, workOrder.TableName(), map[string]interface{}{"MaintenanceType": util.MaintenanceTypeForReplace})
	}

	return response.Success(rsp, nil)
}

// SetDeviceStatus 车队判断设备状态
func (w *WorkOrder) SetDeviceStatus(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param WorkOrder
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	var workOrder workOrderModel.WorkOrder
	err = workOrder.GetDetail(param.Id)
	if err != nil {
		log.ErrorFields("workOrder.GetDetail error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if workOrder.FormStep != util.ProcessFormStepTwo {
		return response.Error(rsp, response.FAIL)
	}

	//更新流程参数
	err = processService.ResetProcessFormFieldValue(workOrder.Id, workOrder.TableName(), map[string]interface{}{"DeviceStatus": param.DeviceStatus})

	if err != nil {
		log.ErrorFields("processService.ResetProcessFormFieldValue error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	err = workOrder.UpdateColumns(workOrder.Id, map[string]interface{}{
		"FormStep":     workOrder.FormStep + 1,
		"DeviceStatus": param.DeviceStatus,
	})

	if err != nil {
		log.ErrorFields("workOrder.EditDeviceStatus error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

//type RepairUserInfo struct {
//	UserId   int64  `json:"UserId"`
//	UserName string `json:"UserName"`
//	NickName string `json:"NickName"`
//}

type AssignRepairUserRequest struct {
	Id                int64      `json:"Id"`
	AssignRepairUsers model.JSON `json:"AssignRepairUsers"`
	Grade             int64      `json:"Grade"`
}

// AssignRepairUser 指派工单的维修人
func (w *WorkOrder) AssignRepairUser(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param AssignRepairUserRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	var repairUsers []auth.UserInfo
	err = json.Unmarshal(param.AssignRepairUsers, &repairUsers)
	if err != nil {
		log.Error("AssignRepairUsers json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(repairUsers) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var workOrder workOrderModel.WorkOrder
	err = workOrder.GetDetail(param.Id)
	if err != nil {
		log.ErrorFields("workOrder.GetDetail error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if workOrder.FormStep != util.ProcessFormStepFour && workOrder.FormStep != util.ProcessFormStepSix && workOrder.FormStep != util.ProcessFormStepEight {
		return response.Error(rsp, response.FAIL)
	}

	//查询当前被指派的人
	var sceneTypes []int64
	if workOrder.IsInWarranty == util.StatusForTrue {
		sceneTypes = append(sceneTypes, util.DeviceFactoryUserSceneTypeForHandler)
	} else {
		sceneTypes = append(sceneTypes, util.DeviceFactoryUserSceneTypeForRepairer)
	}
	factoryUser := (&setting.DeviceFactoryHasUser{}).GetByUser(workOrder.MaintainerFactoryId, repairUsers[0].UserId, sceneTypes)
	if factoryUser.Id == 0 {
		return response.Error(rsp, response.Forbidden)
	}

	//当前被指派的人是否有下级
	factoryUsers := (&setting.DeviceFactoryHasUser{}).GetBy(workOrder.MaintainerFactoryId, factoryUser.Id, sceneTypes)

	var userAccounts []string
	for i := range repairUsers {
		userInfo := rpc.GetUserInfoById(ctx, repairUsers[i].UserId)
		if userInfo != nil {
			userAccounts = append(userAccounts, userInfo.Username)
		}
	}

	if len(userAccounts) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var updateColumn = map[string]interface{}{
		"FormStep": workOrder.FormStep + 1,
	}

	var processFormDataColumn = map[string]interface{}{"RepairUserAccounts": strings.Join(userAccounts, ",,")}

	//负责人指派节点
	if workOrder.FormStep == util.ProcessFormStepFour {
		updateColumn["AssignFirstStepUsers"] = param.AssignRepairUsers

		//当前被指派人有下级
		if len(factoryUsers) > 0 {
			processFormDataColumn = map[string]interface{}{"AssignFirstStepUserAccounts": strings.Join(userAccounts, ",,")}
		} else {
			updateColumn["AssignRepairUsers"] = param.AssignRepairUsers
		}
	}
	//一级指派节点
	if workOrder.FormStep == util.ProcessFormStepSix {
		updateColumn["AssignSecondStepUsers"] = param.AssignRepairUsers
		//当前被指派人有下级
		if len(factoryUsers) > 0 {
			processFormDataColumn = map[string]interface{}{"AssignSecondStepUserAccounts": strings.Join(userAccounts, ",,")}
		} else {
			updateColumn["AssignRepairUsers"] = param.AssignRepairUsers
		}
	}

	//二级指派节点
	if workOrder.FormStep == util.ProcessFormStepEight {
		updateColumn["AssignThirdStepUsers"] = param.AssignRepairUsers
		updateColumn["AssignRepairUsers"] = param.AssignRepairUsers
	}

	//更新流程参数
	err = processService.ResetProcessFormFieldValue(workOrder.Id, workOrder.TableName(), processFormDataColumn)

	if err != nil {
		log.ErrorFields("processService.ResetProcessFormFieldValue error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	err = workOrder.UpdateColumns(workOrder.Id, updateColumn)

	if err != nil {
		log.ErrorFields("workOrder.UpdateColumns error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}
