package workOrder

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	erpProto "app/org/scs/erpv2/api/proto/rpc/erp"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"bufio"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
)

type RpcWorkOrder struct{}

func (rs *RpcWorkOrder) GetDeviceList(ctx context.Context, req *erpProto.GetDeviceListRequest, rsp *erpProto.GetDeviceListResponse) error {
	log.PrintFields("GetErpDriverViolationRecord Req", map[string]interface{}{"req": req})
	if req.DeviceCategoryDictId == 0 || req.DeviceClassDictId == 0 || req.VehicleId == 0 {
		rsp.Code = response.ParamsMissing
		rsp.Msg = response.MsgMap[response.ParamsMissing].Message
		return nil
	}
	vehicle := rpc.GetVehicleWithId(ctx, req.VehicleId)
	if vehicle == nil {
		rsp.Code = response.ParamsInvalid
		rsp.Msg = response.MsgMap[response.ParamsInvalid].Message
		return nil
	}

	list, _, err := (&workOrderModel.DeviceDetail{}).List(vehicle.CorporationId, req.VehicleId, util.StatusForTrue, "", "", "", "",
		[]int64{req.DeviceClassDictId}, []int64{req.DeviceCategoryDictId}, nil, "",
		"", 0, 0, time.Time{}, time.Time{}, model.Paginator{})
	if err != nil {
		log.Error("List err =", err)
		rsp.Code = response.FAIL
		rsp.Msg = response.MsgMap[response.FAIL].Message
		return nil
	}

	var results []*erpProto.DeviceItem

	for _, detail := range list {
		//isUsed, _ := (&workOrderModel.WorkOrder{}).IsUsed(detail.Id)
		//if isUsed {
		//	continue
		//}

		var result = erpProto.DeviceItem{
			Id:          detail.Id,
			Code:        detail.Code,
			FactoryCode: detail.FactoryCode,
		}
		//查询型号
		deviceModel := (&workOrderModel.DeviceModel{}).FirstBy(detail.DeviceModelId)
		result.HandlerFactoryId = deviceModel.HandlerFactoryId
		result.HandlerFactoryKey = deviceModel.HandlerFactoryKey

		//查询批次信息
		batchCode := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(detail.DeviceModelBatchCodeId)
		result.MaintainerFactoryId = batchCode.MaintainerFactoryId
		result.MaintainerFactoryKey = batchCode.MaintainerFactoryKey
		result.ExpireAt = time.Time(batchCode.ExpireAt).Local().Unix()

		results = append(results, &result)
	}

	rsp.Items = results
	rsp.Code = response.SUCCESS
	rsp.Msg = response.MsgMap[response.SUCCESS].Message

	return nil
}

func (rs *RpcWorkOrder) ReportDeviceWorkOrder(ctx context.Context, req *erpProto.ReportDeviceWorkOrderRequest, rsp *erpProto.ReportDeviceWorkOrderResponse) error {
	log.PrintFields("GetErpDriverViolationRecord Req", map[string]interface{}{"req": req})
	var param WorkOrder

	param.WorkOrder.FkRepairDeviceId = req.DeviceId
	//判断设备是否在报修中
	isUsed, _ := (&workOrderModel.WorkOrder{}).IsUsed(param.WorkOrder.FkRepairDeviceId)
	if isUsed {
		//查询工单
		workOrder := (&workOrderModel.WorkOrder{}).GetUsedDeviceWorkOrder(param.WorkOrder.FkRepairDeviceId)
		rsp.Code = response.SUCCESS
		rsp.Msg = response.MsgMap[response.SUCCESS].Message
		rsp.Item = &erpProto.WorkOrderInfo{
			Id:          workOrder.Id,
			Code:        workOrder.Code,
			ApplyStatus: workOrder.ApplyStatus,
		}
		return nil
	}

	user := auth.NewUserById(req.UserId)
	param.WorkOrder.Type = workOrderModel.DEVICE_REPAIR_2
	param.WorkOrder.GroupId = user.TopCorporationId
	param.WorkOrder.Code = util.GenerateIdentifier()

	param.WorkOrder.CreatorUserId = user.Id
	param.WorkOrder.CreatorUserName = user.Name
	param.WorkOrder.CreatorCorporationId = auth.User(ctx).GetCorporationId()

	detailById := rpc.GetCorporationDetailById(ctx, param.WorkOrder.CreatorCorporationId)
	if detailById == nil || detailById.Item == nil {
		log.Error("detailById == nil || detailById.Item == nil corpId == ", param.WorkOrder.CreatorCorporationId)
		rsp.Code = response.FAIL
		rsp.Msg = response.MsgMap[response.FAIL].Message
		return nil
	}

	param.WorkOrder.CreatorCorporation = detailById.Item.Name
	param.CreatorDepartmentId = detailById.DepartmentId
	param.CreatorDepartment = detailById.DepartmentName
	param.TopCorporationName = detailById.GroupName
	param.WorkOrder.ApplyStatus = util.ApplyStatusForDoing
	param.WorkOrder.RepairMethod = req.RepairMethod
	if req.RepairMethod == util.WorkOrderRepairMethodForNone {
		param.WorkOrder.RepairMethod = util.WorkOrderRepairMethodForFix
	}

	//查询设备
	device := (&workOrderModel.DeviceDetail{}).FirstById(req.DeviceId)
	if device.Id == 0 {
		rsp.Code = response.DbNotFoundRecord
		rsp.Msg = response.MsgMap[response.DbNotFoundRecord].Message
		return nil
	}

	//查询型号
	deviceModel := (&workOrderModel.DeviceModel{}).FirstBy(device.DeviceModelId)
	//查询批次信息
	batchCode := (&workOrderModel.DeviceModelBatchCode{}).FirstBy(device.DeviceModelBatchCodeId)

	param.WorkOrder.RepairClassDictId = deviceModel.DeviceClassDictId
	param.WorkOrder.RepairClassDictKey = deviceModel.DeviceClassDictKey
	param.WorkOrder.RepairDevice = device.Code

	param.WorkOrder.Content = req.Content

	// 判断是否保
	now := time.Now()
	nowDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	expireAt := time.Time(batchCode.ExpireAt)
	expireDay := time.Date(expireAt.Year(), expireAt.Month(), expireAt.Day(), 0, 0, 0, 0, time.Local)
	if expireDay.Sub(nowDay) >= 0 {
		// 过保时间大于现在时间 => 在保
		param.WorkOrder.IsInWarranty = util.StatusForTrue
		param.WorkOrder.MaintainerFactoryId = deviceModel.HandlerFactoryId
		param.WorkOrder.MaintainerFactoryKey = deviceModel.HandlerFactoryKey
	} else {
		param.WorkOrder.IsInWarranty = util.StatusForFalse
		param.WorkOrder.MaintainerFactoryId = batchCode.MaintainerFactoryId
		param.WorkOrder.MaintainerFactoryKey = batchCode.MaintainerFactoryKey
	}

	vehicleItem := rpc.GetVehicleWithId(ctx, req.VehicleId)
	if vehicleItem != nil {
		param.WorkOrder.License = vehicleItem.License
	}

	// 路径前缀 /mnt/www/
	prefixPath := config.Config.AbsDirPath

	// 相对路径 webroot/erp/...
	relativePath := fmt.Sprintf(`%s/erp/%v/%v`, config.Config.WebRoot, param.CreatorCorporationId, "files_upload/")

	// 完整路径 /mnt/www/webroot/erp/...
	fullPath := fmt.Sprintf("%s%s", prefixPath, relativePath)
	err := util.VerifyMkdirExistAndCreate(fullPath)
	if err != nil {
		rsp.Code = response.FAIL
		rsp.Msg = response.MsgMap[response.FAIL].Message
		return nil
	}

	var files []map[string]interface{}
	//存储文件
	if len(req.Files) > 0 {
		for i := range req.Files {
			suffixArr := strings.Split(req.Files[i].FileName, ".")
			suffix := suffixArr[len(suffixArr)-1]
			newUuid := uuid.NewV4()
			uuidStr := newUuid.String()
			newFileName := fmt.Sprintf("%s.%s", uuidStr, suffix)

			fileFullPath := fmt.Sprintf("%s%s", fullPath, newFileName)

			httpClient := &http.Client{
				Transport: &http.Transport{
					TLSClientConfig: &tls.Config{
						InsecureSkipVerify: true,
					},
				},
			}
			res, err := httpClient.Get(req.Files[i].FileUrl)
			if err != nil {
				log.Error("Http.Get fileUrl error:", err)
				continue
			}

			defer res.Body.Close()
			reader := bufio.NewReader(res.Body)

			file, err := os.Create(fileFullPath)
			if err != nil {
				log.Error("os.Create(fileFullPath) error:", err)
				continue
			}

			writer := bufio.NewWriter(file)
			_, err = io.Copy(writer, reader)
			if err != nil {
				log.Error("io.Copy(file, reader) error:", err)
				continue
			}

			files = append(files, map[string]interface{}{
				"Url":    config.Config.StaticFileHttpPrefix + relativePath + newFileName,
				"Path":   relativePath + newFileName,
				"Name":   req.Files[i].FileName,
				"Suffix": suffix,
			})
		}

		fileByte, _ := json.Marshal(files)

		param.WorkOrder.Files = fileByte
	}

	errCode := AddDeviceWorkOrder(ctx, &param, user)
	if errCode != response.SUCCESS {
		rsp.Code = errCode
		rsp.Msg = response.MsgMap[errCode].Message
		return nil
	}
	rsp.Code = response.SUCCESS
	rsp.Msg = response.MsgMap[response.SUCCESS].Message
	rsp.Item = &erpProto.WorkOrderInfo{
		Id:          param.WorkOrder.Id,
		Code:        param.WorkOrder.Code,
		ApplyStatus: param.WorkOrder.ApplyStatus,
	}
	return nil
}

func (rs *RpcWorkOrder) GetWorkOrderApplyStatus(ctx context.Context, req *erpProto.GetWorkOrderApplyStatusRequest, rsp *erpProto.GetWorkOrderApplyStatusResponse) error {
	log.PrintFields("GetErpDriverViolationRecord Req", map[string]interface{}{"req": req})
	workOrder := (&workOrderModel.WorkOrder{}).FirstBy(req.Id)

	if workOrder.Id == 0 {
		rsp.Code = response.FAIL
		rsp.Msg = response.MsgMap[response.FAIL].Message
		return nil
	}

	rsp.Item = &erpProto.WorkOrderInfo{
		Id:          workOrder.Id,
		Code:        workOrder.Code,
		ApplyStatus: workOrder.ApplyStatus,
	}
	rsp.Code = response.SUCCESS
	rsp.Msg = response.MsgMap[response.SUCCESS].Message
	return nil
}
