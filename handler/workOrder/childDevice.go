package workOrder

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	go_api "github.com/micro/go-micro/v2/api/proto"
	uuid "github.com/satori/go.uuid"
	"github.com/tealeg/xlsx"
	"time"
)

type ChildDevice struct {
	workOrderModel.ChildDevice
	BatchAddType          int64    `json:"BatchAddType"` //1按数量 2按厂家编号
	BatchAddCount         int      `json:"BatchAddCount"`
	BatchAddFileData      string   `json:"BatchAddFileData"`
	FactoryCodes          []string `json:"FactoryCodes"`
	AssociationDeviceCode string   `json:"AssociationDeviceCode"`
	RepairParty           string   `json:"RepairParty"`
	IsEnableUse           int64    `json:"IsEnableUse"` //0所有  1可使用（未绑定且不在报修中的设备）
	model.Paginator
	DeviceIds []int64 `json:"DeviceIds"`
}
type CateParam struct {
	Records []workOrderModel.ChildDeviceCate
}

func (cd *ChildDevice) AddCate(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var pp CateParam

	err := json.Unmarshal([]byte(req.Body), &pp)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	params := pp.Records
	tx := model.DB().Begin()
	for i := range params {
		if params[i].ActionType == "delete" {
			var hasChild bool
			for j := range params[i].Children {
				if params[i].Children[j].ActionType != "delete" {
					hasChild = true
				}
			}
			if hasChild {
				tx.Rollback()
				log.ErrorFields("ChildDeviceCate has children,dont delete", nil)
				return response.Error(rsp, response.DbDeleteFail)
			}
			//删除
			err := params[i].TransactionDelete(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("ChildDeviceCate.TransactionDelete error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbDeleteFail)
			}
		} else {
			if params[i].Id > 0 {
				//更新
				err := params[i].TransactionUpdate(tx)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("ChildDeviceCate.TransactionUpdate error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbUpdateFail)
				}
			} else {
				//新增
				params[i].TopCorporationId = auth.User(ctx).GetTopCorporationId()
				err := params[i].TransactionCreate(tx)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("ChildDeviceCate.TransactionCreate error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbSaveFail)
				}
			}
		}

		if len(params[i].Children) > 0 {
			for j := range params[i].Children {
				if params[i].Children[j].Id > 0 && params[i].Children[j].ActionType == "delete" {
					if (&workOrderModel.ChildDevice{}).ExistModelId(params[i].Children[j].Id) {
						tx.Rollback()
						log.ErrorFields("ChildDeviceCate has device,dont delete", nil)
						return response.Error(rsp, response.DbDeleteFail)
					} else {
						//删除
						err := params[i].Children[j].TransactionDelete(tx)
						if err != nil {
							tx.Rollback()
							log.ErrorFields("ChildDeviceCate.children.TransactionDelete error", map[string]interface{}{"err": err})
							return response.Error(rsp, response.DbUpdateFail)
						}
					}
				} else {
					//查询设备简称是否存在
					cate := (&workOrderModel.ChildDeviceCate{}).FirstByShotName(params[i].Children[j].ShotName)
					if cate.Id > 0 && cate.Id != params[i].Children[j].Id {
						tx.Rollback()
						log.ErrorFields("ChildDeviceCate shotName repeat", map[string]interface{}{"shotName": params[i].Children[j].ShotName})
						return response.Error(rsp, response.DbSaveFail)
					}

					params[i].Children[j].ParentId = params[i].Id
					if params[i].Children[j].Id > 0 {
						//更新
						err := params[i].Children[j].TransactionUpdate(tx)
						if err != nil {
							tx.Rollback()
							log.ErrorFields("ChildDeviceCate.children.TransactionUpdate error", map[string]interface{}{"err": err})
							return response.Error(rsp, response.DbUpdateFail)
						}
					} else {
						//新增
						params[i].Children[j].TopCorporationId = auth.User(ctx).GetTopCorporationId()
						err := params[i].Children[j].TransactionCreate(tx)
						if err != nil {
							tx.Rollback()
							log.ErrorFields("ChildDeviceCate.children.TransactionCreate error", map[string]interface{}{"err": err})
							return response.Error(rsp, response.DbSaveFail)
						}
					}
				}
			}
		}
	}
	tx.Commit()
	return response.Success(rsp, nil)
}

func (cd *ChildDevice) ListCate(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	cates := (&workOrderModel.ChildDeviceCate{}).ListAll(auth.User(ctx).GetTopCorporationId())
	var records []workOrderModel.ChildDeviceCate
	for i := range cates {
		if cates[i].ParentId == 0 {
			for j := range cates {
				if cates[j].ParentId == cates[i].Id {
					cates[j].IsHasDevice = (&workOrderModel.ChildDevice{}).ExistModelId(cates[j].Id)
					cates[i].Children = append(cates[i].Children, cates[j])
				}
			}
			records = append(records, cates[i])
		}

	}
	return response.Success(rsp, records)
}

func (cd *ChildDevice) AddDevice(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param ChildDevice
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()

	//获取型号、批次下最大的设备编号序号
	seq := (&workOrderModel.ChildDevice{}).GetMaxCodeSeq(param.ModelId, param.BatchCode)

	//查询设备型号简称
	cate := (&workOrderModel.ChildDeviceCate{}).FirstById(param.ModelId)

	var childDevices []workOrderModel.ChildDevice
	if param.BatchAddType == 0 {
		param.BatchAddType = 1
		param.BatchAddCount = 1
	}

	if param.BatchAddType == 1 && param.BatchAddCount > 0 {
		for i := 0; i < param.BatchAddCount; i++ {
			seq++
			param.ChildDevice.CodeSeq = seq
			param.ChildDevice.Code = fmt.Sprintf("%s%s%05d", cate.ShotName, param.BatchCode, seq)
			qrcodePath, err := generateDeviceQrcode(param.ChildDevice.Code, true)
			if err != nil {
				return response.Error(rsp, response.FAIL)
			}
			param.ChildDevice.QrCodePath = qrcodePath
			childDevices = append(childDevices, param.ChildDevice)
		}
	}

	if param.BatchAddType == 2 && len(param.FactoryCodes) > 0 {
		for i := range param.FactoryCodes {
			seq++
			param.ChildDevice.CodeSeq = seq
			param.ChildDevice.Code = fmt.Sprintf("%s%s%05d", cate.ShotName, param.BatchCode, seq)
			param.FactoryCode = param.FactoryCodes[i]
			qrcodePath, err := generateDeviceQrcode(param.ChildDevice.Code, true)
			if err != nil {
				return response.Error(rsp, response.FAIL)
			}
			param.ChildDevice.QrCodePath = qrcodePath
			childDevices = append(childDevices, param.ChildDevice)
		}
	}

	err = (&workOrderModel.ChildDevice{}).BatchCreate(childDevices)
	if err != nil {
		log.ErrorFields("ChildDevice.BatchCreate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)

}

func (cd *ChildDevice) ListDevice(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param ChildDevice
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()
	return response.Success(rsp, listChildDevice(param))
}

func (cd *ChildDevice) ExportDevice(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param ChildDevice
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()
	return response.Success(rsp, listChildDevice(param))
}

func listChildDevice(param ChildDevice) map[string]interface{} {
	devices, count := (&workOrderModel.ChildDevice{}).GetBy(param.TopCorporationId, param.CateId, param.ModelId, param.IsEnableUse, param.Code, param.BatchCode, param.AssociationDeviceCode,
		param.FactoryCode, param.RepairParty, time.Time(param.PurchaseAt), time.Time(param.ExpireAt), param.Paginator)

	for i := range devices {
		//查询种类
		cate := (&workOrderModel.ChildDeviceCate{}).FirstById(devices[i].CateId)
		devices[i].CateName = cate.Name

		//查询型号
		modelCate := (&workOrderModel.ChildDeviceCate{}).FirstById(devices[i].ModelId)
		devices[i].ModelName = modelCate.Name
		devices[i].RepairParty = modelCate.RepairParty

		if devices[i].AssociationDeviceId > 0 {
			var deviceDetail workOrderModel.DeviceDetail
			_ = deviceDetail.FindBy(devices[i].AssociationDeviceId)
			devices[i].AssociationDeviceCode = deviceDetail.Code
			devices[i].AssociationDeviceFactoryCode = deviceDetail.FactoryCode
		}
		if devices[i].QrCodePath != "" {
			devices[i].QrCodePath = config.Config.StaticFileHttpPrefix + devices[i].QrCodePath
		}
	}

	return map[string]interface{}{"Items": devices, "TotalCount": count}
}

func (cd *ChildDevice) ShowDevice(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param ChildDevice
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var device workOrderModel.ChildDevice
	if param.Code != "" {
		device = (&workOrderModel.ChildDevice{}).FirstByCode(param.Code)
	} else {
		device = (&workOrderModel.ChildDevice{}).FirstById(param.Id)
	}

	if device.Id == 0 {
		log.ErrorFields("ChildDevice.FirstById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	//查询种类
	cate := (&workOrderModel.ChildDeviceCate{}).FirstById(device.CateId)
	device.CateName = cate.Name

	//查询型号
	modelCate := (&workOrderModel.ChildDeviceCate{}).FirstById(device.ModelId)
	device.ModelName = modelCate.Name
	device.RepairParty = modelCate.RepairParty

	if device.AssociationDeviceId > 0 {
		var deviceDetail workOrderModel.DeviceDetail
		_ = deviceDetail.FindBy(device.AssociationDeviceId)
		device.AssociationDeviceCode = deviceDetail.Code
		device.AssociationDeviceFactoryCode = deviceDetail.FactoryCode
	}
	if device.QrCodePath != "" {
		device.QrCodePath = config.Config.StaticFileHttpPrefix + device.QrCodePath
	}
	return response.Success(rsp, device)
}

func (cd *ChildDevice) EditDevice(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param ChildDevice
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	device := (&workOrderModel.ChildDevice{}).FirstById(param.Id)
	if device.Id == 0 {
		log.ErrorFields("ChildDevice.FirstById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	err = param.ChildDevice.Update()
	if err != nil {
		log.ErrorFields("ChildDevice.Update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

func (cd *ChildDevice) ImportUpdateDevice(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param ImportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			response.Error(rsp, response.FAIL)
		}
		return
	}()

	// 0设备编号 1厂家编号 2过保时间 3设备单价
	sheet := excelFile.Sheets[0]
	var importFailDevices []string
	var successCount int64

	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 4 {
			continue
		}

		if row.Cells[0].String() == "" || row.Cells[2].String() == "" || row.Cells[3].String() == "" {
			importFailDevices = append(importFailDevices, row.Cells[0].String())
			continue
		}

		//根据设备编号查询设备详情
		childDevice := (&workOrderModel.ChildDevice{}).FirstByCode(row.Cells[0].String())
		if childDevice.Id == 0 {
			importFailDevices = append(importFailDevices, row.Cells[0].String())
			log.ErrorFields("ChildDevice.FirstByCode not found", nil)
			continue
		}

		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 0:
				childDevice.Code = row.Cells[0].String()
			case 1:
				childDevice.FactoryCode = row.Cells[1].String()
			case 2:
				at, err := row.Cells[2].GetTime(false)
				if err != nil {
					importFailDevices = append(importFailDevices, row.Cells[0].String())
					log.ErrorFields("row.Cells[2].GetTime err", map[string]interface{}{"err": err})
					continue
				}
				childDevice.ExpireAt = model.LocalTime(at)
			case 3:
				price, err := row.Cells[3].Float()
				if err != nil {
					importFailDevices = append(importFailDevices, row.Cells[0].String())
					log.ErrorFields("row.Cells[3].Float err", map[string]interface{}{"err": err})
					continue
				}
				childDevice.Price = int64(price * 100)
			}
		}

		//更新设备详情
		if err := childDevice.Update(); err != nil {
			importFailDevices = append(importFailDevices, childDevice.Code)
			log.ErrorFields("deviceDetail.Edit err", map[string]interface{}{"err": err})
			continue
		}
		successCount++
	}

	return response.Success(rsp, map[string]interface{}{
		"SuccessCount": successCount,
		"FailCount":    len(importFailDevices),
		"FailItems":    importFailDevices,
	})
}

func (cd *ChildDevice) DeleteDevice(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param ChildDevice
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if len(param.DeviceIds) == 0 {
		log.ErrorFields("id is empty", nil)
		return response.Error(rsp, response.ParamsMissing)
	}
	tx := model.DB().Begin()
	for i := range param.DeviceIds {
		device := (&workOrderModel.ChildDevice{}).FirstById(param.DeviceIds[i])
		if device.Id == 0 {
			tx.Rollback()
			log.ErrorFields("ChildDevice.FirstById error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		if device.AssociationDeviceId > 0 {
			tx.Rollback()
			log.ErrorFields("device has AssociationDevice", map[string]interface{}{"id": param.DeviceIds[i]})
			return response.Error(rsp, response.DbDeleteFail)
		}

		err := device.TransactionDelete(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("device.TransactionDelete error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbDeleteFail)
		}
	}
	tx.Commit()
	return response.Success(rsp, nil)
}

func (cd *ChildDevice) ExportQrcode(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param ChildDevice
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(param.DeviceIds) == 0 {
		log.Error("len(param.DeviceIds) == 0", err.Error())
		return response.Error(rsp, response.ParamsMissing)
	}

	// 找出所有的设备二维码的磁盘地址 /mnt/www/webroot/.../xxx.png
	// 将所有二维码打包压缩
	// 返回该压缩包url https://xxxxxx/webroot/.../xxx.zip

	// 相对路径 webroot/erp/...
	relativePath := fmt.Sprintf(`%s/erp/%v`, config.Config.WebRoot, "device_qrcodes/")

	// 完整路径 /mnt/www/webroot/erp/...
	fullPath := fmt.Sprintf("%s%s", config.Config.AbsDirPath, relativePath)

	err = util.VerifyMkdirExistAndCreate(fullPath)
	if err != nil {
		fmt.Println("VerifyMkdirExistAndCreate error ==", err)
		return response.Error(rsp, response.DbSaveFail)
	}

	newFileName := fmt.Sprintf("%s%s", uuid.NewV4().String(), ".zip")

	zipFilePath := fmt.Sprintf("%s%s", fullPath, newFileName)

	var files []string   // 待打包压缩的图片地址 /mnt/www/webroot/.../xxx.png
	var asNames []string // 图片重命名

	for i, id := range param.DeviceIds {
		device := (&workOrderModel.ChildDevice{}).FirstById(id)
		if device.Id == 0 {
			log.Error("ChildDevice.FirstById not found device", err, "Id ==", id)
			continue
		}

		if device.QrCodePath != "" {
			files = append(files, fmt.Sprintf("%s%s", config.Config.AbsDirPath, device.QrCodePath))

			// 序号_设备编号_关联对象
			asNames = append(asNames, fmt.Sprintf("%d_%s_%s", i, device.Code, util.Default_Suffix))
		}
	}

	err = util.FilesToZip(files, zipFilePath, asNames)
	if err != nil {
		fmt.Println("VerifyMkdirExistAndCreate error ==", err)
		return response.Error(rsp, response.DbSaveFail)
	}

	// 压缩包url
	zipUrl := fmt.Sprintf("%s%s%s", config.Config.StaticFileHttpPrefix, relativePath, newFileName)

	return response.Success(rsp, zipUrl)
}
