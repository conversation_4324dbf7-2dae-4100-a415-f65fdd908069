package scheduler

import (
	"app/org/scs/erpv2/api/log"
	hrModel "app/org/scs/erpv2/api/model/hr"
	schedulerModel "app/org/scs/erpv2/api/model/scheduler"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

type Scheduler struct {
	Schedulers []schedulerModel.Scheduler
}

func (s *Scheduler) GetLaborContractExpireNotify(ctx context.Context, req *api.Request, rsp *api.Response) error {
	schedulers := s.List(ctx, (&hrModel.StaffLaborContract{}).ExpireMessageType())
	return response.Success(rsp, map[string]interface{}{"Items": schedulers})
}
func (s *Scheduler) GetProbationExpireNotify(ctx context.Context, req *api.Request, rsp *api.Response) error {
	schedulers := s.List(ctx, (&hrModel.StaffArchive{}).ProbationExpireMessageType())
	return response.Success(rsp, map[string]interface{}{"Items": schedulers})
}

func (s *Scheduler) AddLaborContractExpireNotify(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Scheduler
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = s.Create(ctx, param.Schedulers, (&hrModel.StaffLaborContract{}).ExpireMessageType())
	if err != nil {
		log.ErrorFields("scheduler.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (s *Scheduler) AddProbationExpireNotify(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Scheduler
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = s.Create(ctx, param.Schedulers, (&hrModel.StaffArchive{}).ProbationExpireMessageType())
	if err != nil {
		log.ErrorFields("scheduler.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (s *Scheduler) EditLaborContractExpireNotify(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Scheduler
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = s.Edit(ctx, param.Schedulers, (&hrModel.StaffLaborContract{}).ExpireMessageType())
	if err != nil {
		log.ErrorFields("scheduler.Edit error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}
func (s *Scheduler) EditProbationExpireNotify(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Scheduler
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = s.Edit(ctx, param.Schedulers, (&hrModel.StaffArchive{}).ProbationExpireMessageType())
	if err != nil {
		log.ErrorFields("scheduler.Edit error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (s *Scheduler) List(ctx context.Context, scene string) interface{} {

	var scheduler schedulerModel.Scheduler
	return scheduler.GetByGroupAndScene(auth.User(ctx).GetTopCorporationId(), scene)
}

func (s *Scheduler) Create(ctx context.Context, schedulers []schedulerModel.Scheduler, scene string) error {
	user := auth.User(ctx).GetUser()
	for i := range schedulers {
		schedulers[i].GroupId = auth.User(ctx).GetTopCorporationId()
		schedulers[i].Scene = scene
		schedulers[i].OpUserId = user.Id
		schedulers[i].OpUserName = user.Name

	}

	var scheduler schedulerModel.Scheduler
	return scheduler.Create(schedulers)
}

func (s *Scheduler) Edit(ctx context.Context, schedulers []schedulerModel.Scheduler, scene string) error {
	user := auth.User(ctx).GetUser()
	for i := range schedulers {
		schedulers[i].GroupId = user.TopCorporationId
		schedulers[i].Scene = scene
		schedulers[i].OpUserId = user.Id
		schedulers[i].OpUserName = user.Name
	}

	var scheduler schedulerModel.Scheduler
	return scheduler.Update(schedulers, user.TopCorporationId, scene)
}
