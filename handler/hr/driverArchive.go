package hr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"github.com/micro/go-micro/v2/api/proto"
	"time"
)

type DriverArchive struct {
	StaffId   int64
	StartTime string `json:"StartTime"` // 开始时间 yyyy-mm-dd
	EndTime   string `json:"EndTime"`   // 结束时间 yyyy-mm-dd
}

type DriverOperate struct {
	OperateHours OperateHours // 运营工时
	MileageTimes MileageTimes // 公里趟次
}

type OperateHours struct {
	TotalOffPostWorking int64 // 总岗下工时
	TotalWorking        int64 // 总岗上工时
	Total               int64 // 总工时
}

type MileageTimes struct {
	Total                  int64 // 总公里
	TotalOperate           int64 // 运营总公里
	MileageUtilizationRate int64 // 里程利用率 1.23% 记作 123

	TotalPlanTimes     int64 // 总计划趟次
	TotalActualTimes   int64 // 总实际趟次
	PlanCompletionRate int64 // 计划完成率 1.23% 记作 123
}

func (d *DriverArchive) DriverOperate(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q DriverArchive
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.StaffId == 0 {
		log.ErrorFields("q.StaffId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var s, e time.Time // 左闭右闭

	// 计算时间
	now := time.Now()
	if now.Day() >= 26 {
		s = time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)
		e = time.Date(now.Year(), now.Month(), 25, 23, 59, 59, 999999, time.Local)
	} else {
		s = time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local).AddDate(0, -2, 0)
		e = time.Date(now.Year(), now.Month(), 25, 23, 59, 59, 999999, time.Local).AddDate(0, -1, 0)
	}

	data := rpc.DriverRunFormSum(ctx, q.StaffId, s.Unix(), e.Unix())
	if data == nil {
		return response.Error(rsp, response.FAIL)
	}
	rspD := DriverOperate{
		OperateHours: OperateHours{
			Total:               data.WorkTime + data.LaidOffTime,
			TotalWorking:        data.WorkTime,
			TotalOffPostWorking: data.LaidOffTime,
		},
		MileageTimes: MileageTimes{
			Total:                  data.ActualGpsMileage,
			TotalOperate:           data.ActualRunMileage,
			MileageUtilizationRate: 0,
			TotalPlanTimes:         data.PlanCount,
			TotalActualTimes:       data.ActualCount,
			PlanCompletionRate:     0,
		},
	}

	if data.ActualGpsMileage != 0 {
		rspD.MileageTimes.MileageUtilizationRate = data.ActualRunMileage * 100 / data.ActualGpsMileage
	}
	if data.PlanCount != 0 {
		rspD.MileageTimes.PlanCompletionRate = data.ActualCount * 100 / data.PlanCount
	}

	return response.Success(rsp, rspD)
}

type DriverSafetyRsp struct {
	Violation DriverSafetyRspVI
	Accident  DriverSafetyRspA
}

type DriverSafetyRspVI struct {
	ViolationCount  int64 // 违规次数
	ViolationDeduct int64 // 违规总扣分
	IllegalCount    int64 // 违法次数
	IllegalDeduct   int64 // 违法总扣分
	ThreeClassCount int64 // 三类以上次数
	TalkCount       int64 // 被约谈次数
	HelpCount       int64 // 被重点帮扶次数
	TotalDeduct     int64 // 总扣分
}

type DriverSafetyRspA struct {
	Count                 int64 // 事故次数
	PrimaryLiabilityCount int64 // 主责事故数量
	NoLiabilityCount      int64 // 无责事故数量
}

func (d *DriverArchive) DriverSafety(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q DriverArchive
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.StaffId == 0 {
		log.ErrorFields("q.StaffId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var s, e time.Time // 左闭右开

	// 计算时间
	now := time.Now()
	if now.Day() >= 26 {
		s = time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)
		e = time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local)
	} else {
		s = time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local).AddDate(0, -2, 0)
		e = time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)
	}

	var rspD DriverSafetyRsp

	// 违规违法数据
	violation, err := (&safety.TrafficViolation{}).GetCountWithOption(q.StaffId, "", s, e)
	if err != nil {
		log.ErrorFields("TrafficViolation GetCountWithOption == err", map[string]interface{}{"err": err})
		//return response.Error(rsp, response.DbQueryFail)
	}
	for _, vi := range violation {
		rspD.Violation.ViolationCount++
		rspD.Violation.ViolationDeduct += vi.DeductScore
		rspD.Violation.TotalDeduct += vi.DeductScore

		// 三类以上扣分 大于o.6分
		if vi.DeductScore > 60 {
			rspD.Violation.ThreeClassCount++
		}

	}
	// 约谈数据
	talkCount := (&safety.TrafficViolationRectification{}).DriverRectificationCount(q.StaffId, s, e)
	if err != nil {
		log.Error("DriverTalk err=", err.Error())
		//return response.Error(rsp, response.DbQueryFail)
	}
	rspD.Violation.TalkCount = talkCount

	// 重点帮扶数据
	count, err := (&safety.SafeEmphasisHelpDrivers{}).AddCount(q.StaffId, s, e)
	if err != nil {
		log.ErrorFields("AddCount == err", map[string]interface{}{"err": err})
		//return response.Error(rsp, response.DbQueryFail)
	}
	rspD.Violation.HelpCount = count

	// 事故数据
	accidents, err := (&safety.TrafficAccident{}).GetCountWithOption(q.StaffId, "", s, e)
	if err != nil {
		log.ErrorFields("TrafficAccident GetCountWithOption == err", map[string]interface{}{"err": err})
		//return response.Error(rsp, response.DbQueryFail)
	}
	for _, ac := range accidents {
		rspD.Accident.Count++
		if ac.LiabilityType == 1 {
			rspD.Accident.PrimaryLiabilityCount++
		} else if ac.LiabilityType == 5 {
			rspD.Accident.NoLiabilityCount++
		}
	}

	return response.Success(rsp, rspD)
}

func (d *DriverArchive) ViolationIllegalList(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var form DriverArchive
	err := json.Unmarshal([]byte(req.Body), &form)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if form.StaffId == 0 {
		log.ErrorFields("q.StaffId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsInvalid)
	}
	stime, etime := getStimeAndEtime(form.StartTime, form.EndTime)
	if stime.Unix() > etime.Unix() {
		log.ErrorFields("stime > etime", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsInvalid)
	}
	violation, err := (&safety.TrafficViolation{}).GetCountWithOption(form.StaffId, "", stime, etime)
	if err != nil {
		log.ErrorFields("查询数据库失败", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	if violation != nil {
		for i := range violation {
			qualityAssessmentCate := (&safety.QualityAssessmentCate{}).FindBy(violation[i].QualityAssessmentCateId)
			violation[i].QualityAssessmentCateName = qualityAssessmentCate.Name
		}
	}

	return response.Success(rsp, violation)
}

type AccidentResp struct {
	Items            []safety.TrafficAccident `json:"Items"`
	DriverSafetyRspA DriverSafetyRspA         `json:"DriverSafetyRspA"`
}

func (d *DriverArchive) AccidentList(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var form DriverArchive
	err := json.Unmarshal([]byte(req.Body), &form)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if form.StaffId == 0 {
		log.ErrorFields("q.StaffId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsInvalid)
	}
	stime, etime := getStimeAndEtime(form.StartTime, form.EndTime)
	if stime.Unix() > etime.Unix() {
		log.ErrorFields("stime > etime", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsInvalid)
	}
	// 事故数据
	accidents, err := (&safety.TrafficAccident{}).GetCountWithOption(form.StaffId, "", stime, etime)
	if err != nil {
		log.ErrorFields("TrafficAccident GetCountWithOption == err", map[string]interface{}{"err": err})
		//return response.Error(rsp, response.DbQueryFail)
	}
	var accidentResp AccidentResp
	accidentResp.Items = accidents
	for _, ac := range accidents {
		accidentResp.DriverSafetyRspA.Count++
		if ac.LiabilityType == 1 || ac.LiabilityType == 2 {
			accidentResp.DriverSafetyRspA.PrimaryLiabilityCount++
		}
		if ac.LiabilityType == 5 {
			accidentResp.DriverSafetyRspA.NoLiabilityCount++
		}
	}

	return response.Success(rsp, accidentResp)
}

func getStimeAndEtime(stimeStr, etimeStr string) (stime, etime time.Time) {
	if stimeStr == "" && etimeStr == "" {
		// 默认查询为今年
		now := time.Now()
		stime = time.Date(now.Year(), 1, 1, 0, 0, 0, 0, time.Local)
		etime = time.Date(now.Year()+1, 1, 1, 0, 0, 0, 0, time.Local)
	} else {
		if stimeStr != "" {
			stime, _ = time.ParseInLocation("2006-01-02", stimeStr, time.Local)
		}
		if etimeStr == "" {
			etime = etime.AddDate(9999, 0, 0)
		} else {
			etime, _ = time.ParseInLocation("2006-01-02", etimeStr, time.Local)
		}
	}
	return
}
