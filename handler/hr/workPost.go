package hr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

const WorkPostIsValidStatus = 1

type WorkPost struct {
	hrModel.WorkPost
	CorporationId  int64   `json:"CorporationId" validate:"required"`
	CorporationIds []int64 `json:"CorporationIds"`
	Keyword        string  `json:"Keyword"`
	model.Paginator
}

// List 岗位管理列表
func (wp *WorkPost) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkPost
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	//if len(param.CorporationIds) == 0 {
	//	param.CorporationIds = append(param.CorporationIds, util.GetUserCorporationId(ctx))
	//}

	posts, count := param.WorkPost.GetBy(corporationIds, param.Keyword, param.Type, param.Status, param.Paginator)

	for i := range posts {
		corporationId := posts[i].WorkPost.ReturnCorporationId()
		corporation := rpc.GetCorporationById(ctx, corporationId)
		if corporation != nil {
			posts[i].CorporationName = corporation.Name
			posts[i].CorporationId = corporation.Id
		}
	}

	return response.Success(rsp, map[string]interface{}{"Item": posts, "TotalCount": count})
}

// SelectList 用于岗位选择的下拉列表
func (wp *WorkPost) SelectList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkPost
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	posts, _ := param.WorkPost.GetBy(corporationIds, param.Keyword, param.Type, WorkPostIsValidStatus, param.Paginator)

	for i := range posts {
		posts[i].CorporationId = posts[i].WorkPost.GetCorporationId()
	}
	return response.Success(rsp, posts)
}

// Create 新增岗位数据
func (wp *WorkPost) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkPost
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	work := param.WorkPost.FirstByCode(param.Code)
	if work.Id > 0 {
		return response.Error(rsp, response.DbObjectDuplicate)
	}

	corporation := rpc.GetCorporationDetailById(ctx, param.CorporationId)

	if corporation != nil {
		param.GroupId = corporation.GroupId
		param.CompanyId = corporation.CompanyId
		param.BranchId = corporation.BranchId
		param.DepartmentId = corporation.DepartmentId
		param.FleetId = corporation.FleetId
	}

	param.ParseOpUser(ctx)
	err = param.WorkPost.Create()
	if err != nil {
		log.ErrorFields("workPost create fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

// Edit 编辑岗位数据
func (wp *WorkPost) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkPost
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	var workPost hrModel.WorkPost
	err = workPost.FirstById(param.Id)

	if err != nil {
		log.ErrorFields("workPost not found", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	work := param.WorkPost.FirstByCode(param.Code)
	if work.Id > 0 && work.Id != workPost.Id {
		return response.Error(rsp, response.DbObjectDuplicate)
	}

	corporation := rpc.GetCorporationDetailById(ctx, param.CorporationId)

	if corporation != nil {
		param.GroupId = corporation.GroupId
		param.CompanyId = corporation.CompanyId
		param.BranchId = corporation.BranchId
		param.DepartmentId = corporation.DepartmentId
		param.FleetId = corporation.FleetId
	}

	param.Id = workPost.Id

	param.ParseOpUser(ctx)
	err = param.WorkPost.Edit()
	if err != nil {
		log.ErrorFields("workPost create fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

// SwitchStatus 设置岗位状态 停用->启用
func (wp *WorkPost) SwitchStatus(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkPost
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 || param.Status == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	param.ParseOpUser(ctx)
	err = param.WorkPost.UpdateStatus()

	if err != nil {
		log.ErrorFields("UpdateStatus fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}
