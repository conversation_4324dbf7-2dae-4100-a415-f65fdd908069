package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	protoCorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	protoStaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/scheduler/command"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"sync"
	"time"
)

type StaffReport struct {
	StartTime model.LocalTime
	EndTime   model.LocalTime
	model.Paginator
}

type InfoDistributeRsp struct {
	Level  []int64 // 技术职称 固定长度数组 初级，中级，副高级，正高级，未知
	Degree []int64 // 文化程度 固定长度数组 1-博士,2-博士在读,3-硕士,4-硕士在读,5-大学本科,6-大学专科,7-中等专科,8-职业高中,9-技工学校,10-普通高中,11-初中,12-小学,13-在职大专,14-在职本科,15-在职硕士,16-在职博士,17-其他，未知
	Gender []int64 // 性别 固定长度数组 男，女，未知
	Age    []int64 // 年龄 固定长度数组 30及以下，31-35，36-40，40-45，46-50，51-55，56-60，60以上，未知
}

// InfoDistribute 人员信息分布
func (s *StaffReport) InfoDistribute(ctx context.Context, req *api.Request, rsp *api.Response) error {

	staffs, _, _ := service.SelectTopCorporationOetStaffByStaffName(ctx, "")

	rspD := InfoDistributeRsp{
		Level:  make([]int64, 5),
		Degree: make([]int64, 18),
		Gender: make([]int64, 3),
		Age:    make([]int64, 9),
	}

	for _, staff := range staffs {

		if staff.WorkingState == 2 || staff.WorkingState == 4 {
			// 不统计退休、离职人员
			continue
		}

		var arc StaffArchive
		err := arc.FindByStaffId(staff.Id)
		if err != nil {
			log.ErrorFields("FindByStaffId error", map[string]interface{}{"err": err})
			//return response.Success(rsp, rspD)
			continue
		}

		if len(arc.PositionalTitles) > 0 {
			for _, title := range arc.PositionalTitles {
				switch title.Level {
				case 1:
					rspD.Level[3]++
				case 2:
					rspD.Level[2]++
				case 3:
					rspD.Level[1]++
				case 4:
					rspD.Level[0]++
				case 5:
					rspD.Level[4]++
				}
			}
		}

		if arc.ErpDataForBasic.HighestEdu > 0 {
			rspD.Degree[arc.ErpDataForBasic.HighestEdu-1]++
		} else {
			rspD.Degree[17]++ // 学历未知+1
		}

		if staff.Sex {
			rspD.Gender[0]++
		} else {
			rspD.Gender[1]++
		}

		now := time.Now()
		var age int64
		if staff.IdentifyId != "" {
			parse, err2 := time.Parse("20060102", string([]rune(staff.IdentifyId)[6:14]))
			if err2 == nil {
				age = int64((now.Sub(parse).Hours()) / (24 * 365))
			}
		}

		switch {
		case age <= 30 && age > 0:
			rspD.Age[0]++
		case age <= 35:
			rspD.Age[1]++
		case age <= 40:
			rspD.Age[2]++
		case age <= 45:
			rspD.Age[3]++
		case age <= 50:
			rspD.Age[4]++
		case age <= 55:
			rspD.Age[5]++
		case age <= 60:
			rspD.Age[6]++
		case age > 60:
			rspD.Age[7]++
		default:
			rspD.Age[8]++
		}

	}

	var levelTotal int64
	for i := range rspD.Level {
		levelTotal += rspD.Level[i]
	}

	rspD.Level[4] = rspD.Level[4] + (int64(len(staffs)) - levelTotal)

	return response.Success(rsp, rspD)

}

type ListRsp struct {
	StaffId             int64  //人员ID
	StaffName           string //人员姓名
	JobNumber           string //人员工号
	CorporationName     string //机构
	Gender              int64  // 性别 1男 2女
	Age                 int64  //年龄
	WorkPostType        int64  //岗位类型 1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他
	WorkPostName        string //岗位名称
	Contact             string // 联系方式
	IdentityId          string // 身份证
	BirthDate           string //出生日期 例:2006-01-02 00:00:00
	JoinAt              string // 进现单位时间 格式：1970-01-01 00:00:00
	InsuranceAdjustDate int64  //社保调整日期 格式例：202102
	FundAdjustDate      int64  //公积金调整日期 格式例：202102
	PositionLevel       string //职位等级
	Address             string // 家庭住址
	HighestEdu          int64  // 最高学历 1-博士,2-博士在读,3-硕士,4-硕士在读,5-大学本科,6-大学专科,7-中等专科,8-职业高中,9-技工学校,10-普通高中,11-初中,12-小学,13-在职大专,14-在职本科,15-在职硕士,16-在职博士,17-其他
	School              string //毕业学校
	PoliticalIdentity   int64  // 政治面貌 1-中国共产党员,2-中国共产党预备党员,3-中国共产主义青年团团员,4-群众,5-其他党派人士
}

func (s *StaffReport) ListJoin(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffReport
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	entTime := time.Time(param.EndTime).AddDate(0, 0, 1)

	res := rpc.GetStaffsWithOpt(ctx, &protoStaff.GetStaffsWithOptionsRequest{
		TopCorporationId:      auth.User(ctx).GetTopCorporationId(),
		Name:                  "",
		Jobs:                  "",
		Sex:                   3,
		StaffCategory:         "",
		Occupation:            999,
		Offset:                int64(param.Offset),
		Limit:                 int64(param.Limit),
		Order:                 "asc",
		IdentifyId:            "",
		RegisterTimeStartTime: time.Time(param.StartTime).Unix(),
		RegisterTimeEndTime:   entTime.Unix(),
	})

	var rspDs []ListRsp
	totalCount := res.TotalCount

	now := time.Now()

	rpcTmpCorporation := make(map[int64]*protoCorporation.CorporationItem) // map[corporationId]机构信息
	tmpPostName := make(map[int64]hrModel.WorkPost)                        // map[岗位id]

	// 获取所有岗位
	pws, _ := (&hrModel.WorkPost{}).GetBy(nil, "", 0, WorkPostIsValidStatus, model.Paginator{}) //

	for _, pw := range pws {
		tmpPostName[pw.WorkPost.Id] = pw.WorkPost
	}

	for _, item := range res.Items {

		rspD := ListRsp{
			StaffId:             item.Id,
			StaffName:           item.Name,
			JobNumber:           item.StaffId,
			CorporationName:     "", //
			Gender:              0,  //
			Age:                 0,  //
			WorkPostType:        item.Occupation,
			WorkPostName:        "", //
			Contact:             item.Phone,
			IdentityId:          item.IdentifyId,
			BirthDate:           "", //
			JoinAt:              time.Unix(item.RegisterTime, 0).Format("2006-01-02"),
			InsuranceAdjustDate: 0,  //
			FundAdjustDate:      0,  //
			PositionLevel:       "", //
			Address:             "", //
			HighestEdu:          0,  //
			School:              item.School,
			PoliticalIdentity:   item.PoliticsStatus,
		}

		// 所属机构
		if oetCorporationItem, ok := rpcTmpCorporation[item.CorporationId]; ok {
			if oetCorporationItem != nil {
				rspD.CorporationName = oetCorporationItem.Name
			}
		} else {
			corporationItem := rpc.GetCorporationById(ctx, item.CorporationId)
			rpcTmpCorporation[item.CorporationId] = corporationItem
			if corporationItem != nil {
				rspD.CorporationName = corporationItem.Name
			}
		}

		// 计算性别
		if item.Sex {
			rspD.Gender = 1
		} else {
			rspD.Gender = 2
		}

		// 根据身份证号计算年龄 出生日期
		if item.IdentifyId != "" {
			parse, err2 := time.Parse("20060102", string([]rune(item.IdentifyId)[6:14]))
			if err2 == nil {
				rspD.BirthDate = parse.Format("2006-01-02")
				rspD.Age = int64((now.Sub(parse).Hours()) / (24 * 365))
			}
		}

		var arc StaffArchive
		err := arc.FindByStaffId(item.Id)
		if err != nil {
			log.ErrorFields("FindByStaffIdOrder error", map[string]interface{}{"err": err})

		} else {

			// 岗位 职位等级
			if len(arc.WorkPosts) > 0 {
				rspD.WorkPostName = tmpPostName[arc.WorkPosts[0].WorkPostId].Name
				rspD.PositionLevel = arc.WorkPosts[0].PositionLevel
				rspD.WorkPostType = tmpPostName[arc.WorkPosts[0].WorkPostId].Type
			}

			// 住址
			rspD.Address = arc.ErpDataForBasic.Address

			// 最高学历
			rspD.HighestEdu = arc.ErpDataForBasic.HighestEdu

			// 社保调整日期
			rspD.InsuranceAdjustDate = (&hrModel.InsuranceRecord{}).GetLatestFirstRecord(arc.Id).AdjustDate

			// 公积金调整日期
			rspD.FundAdjustDate = (&hrModel.FundRecord{}).GetLatestFirstRecord(arc.Id).AdjustDate
		}

		rspDs = append(rspDs, rspD)
	}

	data := map[string]interface{}{
		"Items":      rspDs,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)

}

func (s *StaffReport) ListQuit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffReport
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	entTime := time.Time(param.EndTime).AddDate(0, 0, 1)

	var quit hrModel.StaffQuitRecord

	quitList, totalCount, err := (&quit).List(auth.User(ctx).GetTopCorporationId(), time.Time(param.StartTime), entTime, param.Paginator)

	var rspDs []ListRsp

	now := time.Now()

	rpcTmpCorporation := make(map[int64]*protoCorporation.CorporationItem) // map[corporationId]机构信息

	tmpPostName := make(map[int64]hrModel.WorkPost) // map[岗位id]

	// 获取所有岗位
	pws, _ := (&hrModel.WorkPost{}).GetBy(nil, "", 0, WorkPostIsValidStatus, model.Paginator{}) //

	for _, pw := range pws {
		tmpPostName[pw.WorkPost.Id] = pw.WorkPost
	}

	for _, qu := range quitList {

		item := rpc.GetStaffWithId(ctx, qu.StaffId)

		rspD := ListRsp{
			StaffId:             item.Id,
			StaffName:           item.Name,
			JobNumber:           item.StaffId,
			CorporationName:     "", //
			Gender:              0,  //
			Age:                 0,  //
			WorkPostType:        item.Occupation,
			WorkPostName:        "", //
			Contact:             item.Phone,
			IdentityId:          item.IdentifyId,
			BirthDate:           "", //
			JoinAt:              time.Unix(item.RegisterTime, 0).Format("2006-01-02"),
			InsuranceAdjustDate: 0,  //
			FundAdjustDate:      0,  //
			PositionLevel:       "", //
			Address:             "", //
			HighestEdu:          0,  //
			School:              item.School,
			PoliticalIdentity:   item.PoliticsStatus,
		}

		// 所属机构
		if oetCorporationItem, ok := rpcTmpCorporation[item.CorporationId]; ok {
			if oetCorporationItem != nil {
				rspD.CorporationName = oetCorporationItem.Name
			}
		} else {
			corporationItem := rpc.GetCorporationById(ctx, item.CorporationId)
			rpcTmpCorporation[item.CorporationId] = corporationItem
			if corporationItem != nil {
				rspD.CorporationName = corporationItem.Name
			}
		}

		// 计算性别
		if item.Sex {
			rspD.Gender = 1
		} else {
			rspD.Gender = 2
		}

		// 根据身份证号计算年龄 出生日期
		if item.IdentifyId != "" {
			parse, err2 := time.Parse("20060102", string([]rune(item.IdentifyId)[6:14]))
			if err2 == nil {
				rspD.BirthDate = parse.Format("2006-01-02")
				rspD.Age = int64((now.Sub(parse).Hours()) / (24 * 365))
			}
		}

		var arc StaffArchive
		err := arc.FindByStaffId(item.Id)
		if err != nil {
			log.ErrorFields("FindByStaffIdOrder error", map[string]interface{}{"err": err})

		} else {

			// 岗位 职位等级
			if len(arc.WorkPosts) > 0 {
				rspD.WorkPostName = tmpPostName[arc.WorkPosts[0].WorkPostId].Name
				rspD.PositionLevel = arc.WorkPosts[0].PositionLevel
				rspD.WorkPostType = tmpPostName[arc.WorkPosts[0].WorkPostId].Type
			}

			// 住址
			rspD.Address = arc.ErpDataForBasic.Address

			// 最高学历
			rspD.HighestEdu = arc.ErpDataForBasic.HighestEdu

			// 社保调整日期
			rspD.InsuranceAdjustDate = (&hrModel.InsuranceRecord{}).GetLatestFirstRecord(arc.Id).AdjustDate

			// 公积金调整日期
			rspD.FundAdjustDate = (&hrModel.FundRecord{}).GetLatestFirstRecord(arc.Id).AdjustDate
		}

		rspDs = append(rspDs, rspD)
	}

	data := map[string]interface{}{
		"Items":      rspDs,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)
}

func (s *StaffReport) ListInsurance(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffReport
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	entTime := time.Time(param.EndTime).AddDate(0, 0, 1)

	var inr hrModel.InsuranceRecord

	inrList, totalCount, err := (&inr).List(auth.User(ctx).GetTopCorporationId(), time.Time(param.StartTime), entTime, param.Paginator)

	var rspDs []ListRsp
	now := time.Now()

	rpcTmpCorporation := make(map[int64]*protoCorporation.CorporationItem) // map[corporationId]机构信息
	tmpPostName := make(map[int64]hrModel.WorkPost)                        // map[岗位id]

	// 获取所有岗位
	pws, _ := (&hrModel.WorkPost{}).GetBy(nil, "", 0, WorkPostIsValidStatus, model.Paginator{}) //

	for _, pw := range pws {
		tmpPostName[pw.WorkPost.Id] = pw.WorkPost
	}

	for _, ir := range inrList {

		item := rpc.GetStaffWithId(ctx, ir.StaffId)

		rspD := ListRsp{
			StaffId:             item.Id,
			StaffName:           item.Name,
			JobNumber:           item.StaffId,
			CorporationName:     "", //
			Gender:              0,  //
			Age:                 0,  //
			WorkPostType:        item.Occupation,
			WorkPostName:        "", //
			Contact:             item.Phone,
			IdentityId:          item.IdentifyId,
			BirthDate:           "", //
			JoinAt:              time.Unix(item.RegisterTime, 0).Format("2006-01-02"),
			InsuranceAdjustDate: 0,  //
			FundAdjustDate:      0,  //
			PositionLevel:       "", //
			Address:             "", //
			HighestEdu:          0,  //
			School:              item.School,
			PoliticalIdentity:   item.PoliticsStatus,
		}

		// 所属机构
		if oetCorporationItem, ok := rpcTmpCorporation[item.CorporationId]; ok {
			if oetCorporationItem != nil {
				rspD.CorporationName = oetCorporationItem.Name
			}
		} else {
			corporationItem := rpc.GetCorporationById(ctx, item.CorporationId)
			rpcTmpCorporation[item.CorporationId] = corporationItem
			if corporationItem != nil {
				rspD.CorporationName = corporationItem.Name
			}
		}

		// 计算性别
		if item.Sex {
			rspD.Gender = 1
		} else {
			rspD.Gender = 2
		}

		// 根据身份证号计算年龄 出生日期
		if item.IdentifyId != "" {
			parse, err2 := time.Parse("20060102", string([]rune(item.IdentifyId)[6:14]))
			if err2 == nil {
				rspD.BirthDate = parse.Format("2006-01-02")
				rspD.Age = int64((now.Sub(parse).Hours()) / (24 * 365))
			}
		}

		var arc StaffArchive
		err := arc.FindByStaffId(item.Id)
		if err != nil {
			log.ErrorFields("FindByStaffIdOrder error", map[string]interface{}{"err": err})

		} else {

			// 岗位 职位等级
			if len(arc.WorkPosts) > 0 {
				rspD.WorkPostName = tmpPostName[arc.WorkPosts[0].WorkPostId].Name
				rspD.PositionLevel = arc.WorkPosts[0].PositionLevel
				rspD.WorkPostType = tmpPostName[arc.WorkPosts[0].WorkPostId].Type
			}

			// 住址
			rspD.Address = arc.ErpDataForBasic.Address

			// 最高学历
			rspD.HighestEdu = arc.ErpDataForBasic.HighestEdu

			// 社保调整日期
			rspD.InsuranceAdjustDate = (&hrModel.InsuranceRecord{}).GetLatestFirstRecord(arc.Id).AdjustDate

			// 公积金调整日期
			rspD.FundAdjustDate = (&hrModel.FundRecord{}).GetLatestFirstRecord(arc.Id).AdjustDate
		}

		rspDs = append(rspDs, rspD)
	}

	data := map[string]interface{}{
		"Items":      rspDs,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)
}

func (s *StaffReport) ListFund(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffReport
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	entTime := time.Time(param.EndTime).AddDate(0, 0, 1)

	var fundRecord hrModel.FundRecord

	fundRecordList, totalCount, err := fundRecord.List(auth.User(ctx).GetTopCorporationId(), time.Time(param.StartTime), entTime, param.Paginator)

	var rspDs []ListRsp
	now := time.Now()

	rpcTmpCorporation := make(map[int64]*protoCorporation.CorporationItem) // map[corporationId]机构信息
	tmpPostName := make(map[int64]hrModel.WorkPost)                        // map[岗位id]

	// 获取所有岗位
	pws, _ := (&hrModel.WorkPost{}).GetBy(nil, "", 0, WorkPostIsValidStatus, model.Paginator{}) //

	for _, pw := range pws {
		tmpPostName[pw.WorkPost.Id] = pw.WorkPost
	}

	for _, fr := range fundRecordList {

		item := rpc.GetStaffWithId(ctx, fr.StaffId)

		rspD := ListRsp{
			StaffId:             item.Id,
			StaffName:           item.Name,
			JobNumber:           item.StaffId,
			CorporationName:     "", //
			Gender:              0,  //
			Age:                 0,  //
			WorkPostType:        item.Occupation,
			WorkPostName:        "", //
			Contact:             item.Phone,
			IdentityId:          item.IdentifyId,
			BirthDate:           "", //
			JoinAt:              time.Unix(item.RegisterTime, 0).Format("2006-01-02"),
			InsuranceAdjustDate: 0,  //
			FundAdjustDate:      0,  //
			PositionLevel:       "", //
			Address:             "", //
			HighestEdu:          0,  //
			School:              item.School,
			PoliticalIdentity:   item.PoliticsStatus,
		}

		// 所属机构
		if oetCorporationItem, ok := rpcTmpCorporation[item.CorporationId]; ok {
			if oetCorporationItem != nil {
				rspD.CorporationName = oetCorporationItem.Name
			}
		} else {
			corporationItem := rpc.GetCorporationById(ctx, item.CorporationId)
			rpcTmpCorporation[item.CorporationId] = corporationItem
			if corporationItem != nil {
				rspD.CorporationName = corporationItem.Name
			}
		}

		// 计算性别
		if item.Sex {
			rspD.Gender = 1
		} else {
			rspD.Gender = 2
		}

		// 根据身份证号计算年龄 出生日期
		if item.IdentifyId != "" {
			parse, err2 := time.Parse("20060102", string([]rune(item.IdentifyId)[6:14]))
			if err2 == nil {
				rspD.BirthDate = parse.Format("2006-01-02")
				rspD.Age = int64((now.Sub(parse).Hours()) / (24 * 365))
			}
		}

		var arc StaffArchive
		err := arc.FindByStaffId(item.Id)
		if err != nil {
			log.ErrorFields("FindByStaffIdOrder error", map[string]interface{}{"err": err})

		} else {

			// 岗位 职位等级
			if len(arc.WorkPosts) > 0 {
				rspD.WorkPostName = tmpPostName[arc.WorkPosts[0].WorkPostId].Name
				rspD.PositionLevel = arc.WorkPosts[0].PositionLevel
				rspD.WorkPostType = tmpPostName[arc.WorkPosts[0].WorkPostId].Type
			}

			// 住址
			rspD.Address = arc.ErpDataForBasic.Address

			// 最高学历
			rspD.HighestEdu = arc.ErpDataForBasic.HighestEdu

			// 社保调整日期
			rspD.InsuranceAdjustDate = (&hrModel.InsuranceRecord{}).GetLatestFirstRecord(arc.Id).AdjustDate

			// 公积金调整日期
			rspD.FundAdjustDate = (&hrModel.FundRecord{}).GetLatestFirstRecord(arc.Id).AdjustDate
		}

		rspDs = append(rspDs, rspD)
	}

	data := map[string]interface{}{
		"Items":      rspDs,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)
}

type StaffArchiveReportParam struct {
	CorporationId int64           `json:"CorporationId"`
	DateType      string          `json:"DateType"`
	StartAt       model.LocalTime `json:"StartAt"`
	EndAt         model.LocalTime `json:"EndAt"`
	IsCalc        int64           `json:"IsCalc"`
}
type StaffArchiveReportResponseItem struct {
	CorporationName    string          `json:"CorporationName"`
	CorporationId      int64           `json:"CorporationId"`
	Total              int64           `json:"Total"`
	GroupTotal         map[int64]int64 `json:"GroupTotal"`
	GroupAddTotal      map[int64]int64 `json:"GroupAddTotal"`
	GroupSubTotal      map[int64]int64 `json:"GroupSubTotal"`
	LinkCorporationIds []int64         `json:"-"`
}
type StaffArchiveReportResponse struct {
	ReportAt string                           `json:"ReportAt"`
	Start    time.Time                        `json:"-"`
	End      time.Time                        `json:"-"`
	Items    []StaffArchiveReportResponseItem `json:"Items"`
}

func (s *StaffReport) StaffArchiveReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return staffArchiveReport(ctx, req, rsp)
}
func (s *StaffReport) StaffArchiveReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return staffArchiveReport(ctx, req, rsp)
}

func staffArchiveReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffArchiveReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//没传机构  用默认机构
	var branches []service.CorporationSimpleInfo
	if param.CorporationId == 0 {
		branches, _ = service.BuildStaffArchiveReportBranch()
	} else {
		branches, _ = service.BuildStaffArchiveReportDepartment(param.CorporationId)
	}

	var wg sync.WaitGroup
	dateParts := BuildReportDate(time.Time(param.StartAt), time.Time(param.EndAt), param.DateType)
	for i := range dateParts {
		var reportItems []StaffArchiveReportResponseItem
		for i := range branches {
			reportItems = append(reportItems, StaffArchiveReportResponseItem{
				CorporationId:      branches[i].Id,
				CorporationName:    branches[i].Name,
				LinkCorporationIds: branches[i].LinkCorporationIds,
				GroupTotal:         map[int64]int64{},
				GroupAddTotal:      map[int64]int64{},
				GroupSubTotal:      map[int64]int64{},
			})
		}
		dateParts[i].Items = reportItems
		wg.Add(1)
		go PartReport(&wg, dateParts[i].Start, dateParts[i].End, param.DateType, &(dateParts[i]))
	}
	wg.Wait()

	var responseItems []StaffArchiveReportResponse
	for i := range dateParts {
		if dateParts[i].Items != nil {
			responseItems = append(responseItems, dateParts[i])
		}
	}

	return response.Success(rsp, responseItems)
}
func PartReport(wg *sync.WaitGroup, start, end time.Time, dateType string, responseItem *StaffArchiveReportResponse) {
	latestDate := end.AddDate(0, 0, -1)
	y, m, d := time.Now().Date()
	if latestDate.Unix() >= time.Date(y, m, d, 0, 0, 0, 0, time.Local).Unix() {
		latestDate = time.Date(y, m, d, 0, 0, 0, 0, time.Local).AddDate(0, 0, -1)
	}

	var isExist bool
	reportItems := responseItem.Items

	var corporationIds []int64
	for i := range reportItems {
		corporationIds = append(corporationIds, reportItems[i].LinkCorporationIds...)
	}

	reports := (&hrModel.StaffArchiveDayReport{}).GetBy(corporationIds, start, end)
	var corporationReportMap = make(map[int64][]hrModel.StaffArchiveDayReport)
	for i := range reportItems {
		for j := range reports {
			if util.IncludeInt64(reportItems[i].LinkCorporationIds, reports[j].BranchId) ||
				util.IncludeInt64(reportItems[i].LinkCorporationIds, reports[j].DepartmentId) ||
				util.IncludeInt64(reportItems[i].LinkCorporationIds, reports[j].FleetId) {
				corporationReportMap[reportItems[i].CorporationId] = append(corporationReportMap[reportItems[i].CorporationId], reports[j])
			}
		}
	}

	for i := range reportItems {
		corporationReports := corporationReportMap[reportItems[i].CorporationId]
		if len(corporationReports) > 0 {
			isExist = true
		}
		for j := range corporationReports {
			reportItems[i].Total += corporationReports[j].Total

			var groupTotal = make(map[int64]int64)
			_ = json.Unmarshal(corporationReports[j].GroupTotal, &groupTotal)

			var groupAddTotal = make(map[int64]int64)
			_ = json.Unmarshal(corporationReports[j].GroupAddTotal, &groupAddTotal)

			var groupSubTotal = make(map[int64]int64)
			_ = json.Unmarshal(corporationReports[j].GroupSubTotal, &groupSubTotal)

			for k := range groupTotal {
				if _, ok := reportItems[i].GroupTotal[k]; ok {
					reportItems[i].GroupTotal[k] += groupTotal[k]
				} else {
					reportItems[i].GroupTotal[k] = groupTotal[k]
				}
			}
			for k := range groupAddTotal {
				if _, ok := reportItems[i].GroupAddTotal[k]; ok {
					reportItems[i].GroupAddTotal[k] += groupAddTotal[k]
				} else {
					reportItems[i].GroupAddTotal[k] = groupAddTotal[k]
				}
			}
			for k := range groupSubTotal {
				if _, ok := reportItems[i].GroupSubTotal[k]; ok {
					reportItems[i].GroupSubTotal[k] += groupSubTotal[k]
				} else {
					reportItems[i].GroupSubTotal[k] = groupSubTotal[k]
				}
			}
		}

		if dateType == "month" || dateType == "year" && len(corporationReports) > 0 {
			latestReports := (&hrModel.StaffArchiveDayReport{}).GetByDay(reportItems[i].LinkCorporationIds, latestDate)
			var latestTotal int64
			var latestGroupTotal = make(map[int64]int64)
			for j := range latestReports {
				latestTotal += latestReports[j].Total
				var groupTotal = make(map[int64]int64)
				_ = json.Unmarshal(latestReports[j].GroupTotal, &groupTotal)
				for k := range groupTotal {
					if _, ok := latestGroupTotal[k]; ok {
						latestGroupTotal[k] += groupTotal[k]
					} else {
						latestGroupTotal[k] = groupTotal[k]
					}
				}
			}
			reportItems[i].Total = latestTotal
			reportItems[i].GroupTotal = latestGroupTotal
		}
	}
	log.ErrorFields("", map[string]interface{}{"isExist": isExist})
	responseItem.Items = reportItems
	if !isExist {
		responseItem.Items = nil
	}
	wg.Done()
}

func BuildReportDate(start, end time.Time, dateType string) []StaffArchiveReportResponse {
	var responseItems []StaffArchiveReportResponse
	if start.Unix() > end.Unix() {
		return responseItems
	}
	for {
		if start.Unix() > end.Unix() {
			break
		}
		next := start.AddDate(0, 0, 1)
		format := "2006-01-02"
		if dateType == "month" {
			next = start.AddDate(0, 1, 0)
			format = "2006-01"
		}
		if dateType == "year" {
			next = start.AddDate(1, 0, 0)
			format = "2006"
		}
		responseItems = append(responseItems, StaffArchiveReportResponse{
			ReportAt: start.Format(format),
			Start:    start,
			End:      next,
		})
		start = next
	}

	return responseItems
}

func (s *StaffReport) StaffArchiveReportCalc(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffArchiveReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.IsCalc == util.StatusForFalse {
		return response.Success(rsp, map[string]interface{}{
			"Status": config.Global.IsCalculatingStaffArchiveReportData,
		})
	}

	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)

	if startAt.IsZero() || endAt.IsZero() {
		return response.Error(rsp, response.ParamsMissing)
	}

	config.Global.IsCalculatingStaffArchiveReportData = true
	go startReportCalcJob(startAt, endAt)

	return response.Success(rsp, map[string]interface{}{
		"Status": config.Global.IsCalculatingStaffArchiveReportData,
	})
}

func startReportCalcJob(startAt, endAt time.Time) {
	var corporations []command.CorporationItem
	command.GetAllCorporationId(config.Config.StaffArchiveReportCorpId, &corporations)
	var wg sync.WaitGroup
	for {
		if startAt.Unix() > endAt.Unix() {
			break
		}
		wg.Add(1)
		go command.DayStaffArchiveReport(&wg, corporations, startAt)

		startAt = startAt.AddDate(0, 0, 1)
	}

	wg.Wait()
	config.Global.IsCalculatingStaffArchiveReportData = false
}
