package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"strconv"
	"time"
)

type JoinCompanyApply struct {
	hrModel.JoinCompanyApply
	Keyword        string          `json:"Keyword"`
	StartAt        model.LocalTime `json:"StartAt"`
	EndAt          model.LocalTime `json:"EndAt"`
	CorporationIds []int64         `json:"CorporationIds"`
	model.Paginator
	FileHttpPrefix string `json:"FileHttpPrefix"`
}
type CreateStaffParams struct {
	Records []hrModel.ApplyUser `json:"Records"`
}

var (
	HasApply int64 = 1 //已申请
	HasEntry int64 = 2 //已入职
)

// BatchCreateStaff 新增入职员工
func (jca *JoinCompanyApply) BatchCreateStaff(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CreateStaffParams
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var apply hrModel.JoinCompanyApply
	for i := range param.Records {
		if param.Records[i].CorporationId == 0 || param.Records[i].Name == "" || param.Records[i].Contact == "" || param.Records[i].WorkPostType == 0 {
			return response.Error(rsp, response.ParamsMissing)
		}

		apply, _ := apply.FirstByContact(param.Records[i].Contact)
		if apply.Id > 0 {
			return response.Error(rsp, response.DbObjectDuplicate)
		}
	}

	var records []hrModel.JoinCompanyApply
	for i := range param.Records {
		topCorporation := rpc.GetTopCorporationById(ctx, param.Records[i].CorporationId)
		if topCorporation != nil {
			param.Records[i].TopCorporationId = topCorporation.Id
		}
		param.Records[i].ParseOpUser(ctx)
		records = append(records, hrModel.JoinCompanyApply{ApplyUser: param.Records[i]})

	}

	err = apply.BatchCreate(records)
	if err != nil {
		log.ErrorFields("BatchCreate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

// Delete 删除入职员工
func (jca *JoinCompanyApply) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param JoinCompanyApply
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	err = param.JoinCompanyApply.FindById(param.Id)
	if err != nil {
		log.ErrorFields("FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	err = param.JoinCompanyApply.Delete(param.Id)
	if err != nil {
		log.ErrorFields("Delete fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

// List 入职员工列表
func (jca *JoinCompanyApply) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param JoinCompanyApply
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.CorporationIds = service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	applies, count := param.JoinCompanyApply.GetBy(param.CorporationIds, param.Keyword, param.WorkPostType, param.IsApply, time.Time(param.StartAt), time.Time(param.EndAt), param.Paginator)
	for i := range applies {
		if applies[i].CorporationId > 0 {
			corporation := rpc.GetCorporationById(ctx, applies[i].CorporationId)
			if corporation != nil {
				applies[i].CorporationName = corporation.Name
			}
		}
	}

	return response.Success(rsp, map[string]interface{}{"Items": applies, "TotalCount": count})
}

// CheckUser 检查小程序端用户是否在申请列表
func (jca *JoinCompanyApply) CheckUser(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param JoinCompanyApply
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Contact == "" {
		return response.Error(rsp, response.ParamsMissing)
	}
	apply, err := param.JoinCompanyApply.FirstByContact(param.Contact)
	if err != nil {
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	apply.IdStr = strconv.FormatInt(apply.Id, 10)
	return response.Success(rsp, apply)
}

// Apply 小程序端提交入职申请
func (jca *JoinCompanyApply) Apply(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param JoinCompanyApply
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	apply, err := param.JoinCompanyApply.FirstByContact(param.Contact)
	if err != nil || strconv.FormatInt(apply.Id, 10) != param.IdStr {
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	param.IsApply = HasApply
	param.JoinCompanyApply.Id = apply.Id
	err = param.JoinCompanyApply.UpdateAll()
	if err != nil {
		log.ErrorFields("update fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

// ApplyInfo 获取入职申请详情
func (jca *JoinCompanyApply) ApplyInfo(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param JoinCompanyApply
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	err = param.JoinCompanyApply.FindById(param.Id)
	if err != nil {
		log.ErrorFields("FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	param.JoinCompanyApply.FileHttpPrefix = config.Config.StaticFileHttpPrefix

	return response.Success(rsp, param.JoinCompanyApply)
}
