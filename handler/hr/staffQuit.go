package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/process/dingTalkBpm"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	api "github.com/micro/go-micro/v2/api/proto"
)

type StaffQuit struct {
	hrModel.StaffQuit
	CorporationIds    []int64 `json:"CorporationIds"`
	Keyword           string  `json:"Keyword"`
	StaffQuitRecordId int64   `json:"StaffQuitRecordId"`
	model.Paginator
}

// List 离职管理列表
func (stf *StaffQuit) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffQuit
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)
	quitRecords, totalCount := (&hrModel.StaffQuitRecord{}).GetBy(corporationIds, param.Keyword, param.Paginator)

	for i := range quitRecords {
		oetStaff := rpc.GetStaffWithId(ctx, quitRecords[i].StaffId)
		if oetStaff != nil {
			quitRecords[i].IdentityId = oetStaff.IdentifyId
		}

		//获取流程
		var process processModel.LbpmApplyProcess
		err = process.GetProcessByItemId(quitRecords[i].Id, quitRecords[i].TableName())
		if err == nil && process.FormInstanceId > 0 {
			quitRecords[i].CurrentHandler = process.CurrentHandlerUserName
			quitRecords[i].HasProcess = true
		}
	}

	return response.Success(rsp, map[string]interface{}{"Items": quitRecords, "TotalCount": totalCount, "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

// Create 新增离职员工
func (stf *StaffQuit) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffQuit
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param.StaffQuit); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}
	authUser := auth.User(ctx).GetUser()
	if len(param.Records) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	for i := range param.Records {
		if err := util.Validator().Struct(param.Records[i]); err != nil {
			log.ErrorFields("records validate fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsMissing)
		}
	}

	param.StaffQuit.ParseOpUser(ctx)

	for i := range param.Records {
		//判断是否已经在离职中
		if (&hrModel.StaffQuitRecord{}).IsExistDoingQuit(param.Records[i].StaffId) {
			return response.Error(rsp, "存在正在离职的审批，无法再次离职")
		}

		staff := rpc.GetStaffWithId(ctx, param.Records[i].StaffId)
		if staff == nil {
			return response.Error(rsp, response.ParamsMissing)
		}
		param.Records[i].WorkPostType = util.MasterToErp[staff.Occupation]
		param.Records[i].CorporationId = staff.CorporationId
		param.Records[i].Corporations.Build(staff.CorporationId)
		param.Records[i].StaffName = staff.Name
		param.Records[i].JobNumber = staff.StaffId
		if util.MasterToErp[staff.Occupation] == util.WorkPostType_3 || util.MasterToErp[staff.Occupation] == util.WorkPostType_6 {
			//走流程时发起人必须是车队发起
			detailById := rpc.GetCorporationDetailById(ctx, authUser.CorporationId)
			if detailById == nil {
				log.ErrorFields("rpc.GetCorporationDetailById is nil", map[string]interface{}{"CorporationId": auth.User(ctx).GetCorporationId()})
				return response.Error(rsp, response.NotFleetAccount)
			}
			if detailById.FleetId == 0 {
				return response.Error(rsp, response.NotFleetAccount)
			}
			param.Records[i].ApplyStatus = util.ApplyStatusForDoing
		} else {
			param.Records[i].ApplyStatus = util.ApplyStatusForDone
		}

		var archive hrModel.StaffArchive
		err = archive.FindByStaffId(param.Records[i].StaffId)
		if err != nil {
			log.ErrorFields("archive.FindByStaffId error", map[string]interface{}{"err": err, "staffId": param.Records[i].StaffId})
			return response.Error(rsp, response.DbNotFoundRecord)
		}

		param.Records[i].StaffArchiveId = archive.Id
		var workPost hrModel.WorkPost
		_ = workPost.FirstById(param.Records[i].WorkPostId)
		param.Records[i].WorkPostName = workPost.Name
		param.Records[i].OpUserId = authUser.Id
		param.Records[i].OpUserName = authUser.Name
	}

	tx := model.DB().Begin()
	err = param.StaffQuit.TransactionCreate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("StaffQuit create fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	if config.Config.Lbpm.Enable {
		for _, record := range param.Records {
			//司机和辅工退休需要走流程  其他不走流程；
			if record.WorkPostType == util.WorkPostType_3 || record.WorkPostType == util.WorkPostType_6 {
				//查询当前被退休人的分公司编号
				corporation := rpc.GetCorporationDetailById(ctx, record.CorporationId)
				if corporation == nil {
					tx.Rollback()
					log.ErrorFields("rpc.GetCorporationDetailById is nil", map[string]interface{}{"CorporationId": record.CorporationId})
					return response.Error(rsp, response.FAIL)
				}
				branch := rpc.GetCorporationById(ctx, corporation.BranchId)
				if branch == nil {
					tx.Rollback()
					log.ErrorFields("rpc.GetCorporationById is nil", map[string]interface{}{"CorporationId": corporation.BranchId})
					return response.Error(rsp, response.FAIL)
				}

				byteParam, _ := json.Marshal(record)
				formData := map[string]interface{}{
					"WorkPostType": record.WorkPostType,
					"BranchCode":   branch.Virtual,
				}
				processTitle := fmt.Sprintf("%s提交的离职审批", authUser.Name)
				_, err = processService.NewDispatchProcess(authUser, config.StaffQuitLbpmApplyFormTemplate, processTitle, record.Id, record.TableName(), record.ApplyStatusFieldName(), string(byteParam), formData)

				if err != nil {
					tx.Rollback()
					log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.FAIL)
				}
			} else {
				err = service.StaffQuitAfterUpdateStaffArchive(tx, record)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("service.StaffQuitAfterUpdateStaffArchive error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbSaveFail)
				}
			}
		}
	} else {
		if !config.Config.DingTalkBpm.Enable {
			for i := range param.Records {
				err = service.StaffQuitAfterUpdateStaffArchive(tx, param.Records[i])
				if err != nil {
					tx.Rollback()
					log.ErrorFields("service.StaffQuitAfterUpdateStaffArchive error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbSaveFail)
				}
			}
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

// Show 离职详情
func (stf *StaffQuit) Show(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffQuit
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	record := (&hrModel.StaffQuitRecord{}).FindBy(param.StaffQuitRecordId)
	if record.Id == 0 {
		log.ErrorFields("staffQuitRecord.FindBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	record.FileHttpPrefix = config.Config.StaticFileHttpPrefix

	return response.Success(rsp, record)
}

type FileJson struct {
	Id     int64  `json:"Id"`
	Url    string `json:"Url"`
	Path   string `json:"Path"`
	Name   string `json:"Name"`
	Suffix string `json:"Suffix"`
}

func (stf *StaffQuit) DispatchProcess(ctx context.Context, req *api.Request, rsp *api.Response) error {
	if !config.Config.DingTalkBpm.Enable {
		return response.Error(rsp, response.BadRequest)
	}

	var param hrModel.StaffQuitRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	record := (&hrModel.StaffQuitRecord{}).FindBy(param.Id)

	if record.Id == 0 {
		log.ErrorFields("StaffQuitRecord.FindBy is nil", map[string]interface{}{"id": param.Id})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	record.OpIp = auth.User(ctx).GetClientIp()

	var archive hrModel.StaffArchive
	_ = archive.FindByStaffId(record.StaffId)
	var workPostName []string
	for j := range archive.WorkPosts {
		if archive.WorkPosts[j].IsNowJob == util.NotIsNowJob {
			continue
		}
		var workPost hrModel.WorkPost
		err := workPost.FirstById(archive.WorkPosts[j].WorkPostId)
		if err != nil {
			continue
		}
		workPostName = append(workPostName, workPost.Name)
	}

	oetStaff := rpc.GetStaffWithId(ctx, record.StaffId)

	if oetStaff == nil {
		log.ErrorFields(" rpc.GetStaffWithId[2] is nil", nil)
		return response.Error(rsp, response.FAIL)
	}

	dingUserId, err := dingTalkBpm.GetUserId(oetStaff.Phone, "")
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	dingUserInfo, err := dingTalkBpm.GetUserInfo(dingUserId, "")

	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	if len(dingUserInfo.DeptIdList) == 0 {
		return response.Error(rsp, response.FAIL)
	}

	dingDeptInfo, err := dingTalkBpm.GetDepartmentInfo(dingUserInfo.DeptIdList[0], "")
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	extValue, _ := json.Marshal([]map[string]string{{"name": dingDeptInfo.Name, "id": fmt.Sprintf("%v", dingDeptInfo.DeptId)}})

	contractEndAt := ""
	if record.ContractEndAt != nil && !time.Time(*record.ContractEndAt).IsZero() {
		contractEndAt = time.Time(*record.ContractEndAt).Format(model.DateFormat)
	}
	joinCompanyAt := ""
	if record.JoinCompanyAt != nil && !time.Time(*record.JoinCompanyAt).IsZero() {
		joinCompanyAt = time.Time(*record.JoinCompanyAt).Format(model.DateFormat)
	}
	var images []byte
	var files []FileJson
	err = json.Unmarshal(record.FilePath, &files)

	if err == nil && len(files) > 0 {
		var imgs []string
		for f := range files {
			imgs = append(imgs, config.Config.StaticFileHttpPrefix+files[f].Path)
		}

		images, _ = json.Marshal(imgs)
	}
	formFieldValue := []map[string]string{
		{"name": "姓名", "value": oetStaff.Name},
		{"name": "所属部门", "value": dingDeptInfo.Name, "extValue": string(extValue), "componentType": "DepartmentField"},
		{"name": "工号", "value": oetStaff.StaffId},
		{"name": "岗位名称", "value": strings.Join(workPostName, ",")},
		{"name": "劳动合同终止时间", "value": contractEndAt},
		{"name": "劳动合同解除类型", "value": util.RelieveContractTypeMap[record.RelieveContractType]},
		{"name": "参加工作时间", "value": joinCompanyAt},
		{"name": "年休假剩余天数", "value": fmt.Sprintf("%v", record.YearHolidayRemain)},
		{"name": "调休剩余天数", "value": fmt.Sprintf("%v", record.ExchangeHolidayRemain)},
		{"name": "附件", "value": string(images)},
	}

	processParam, _ := json.Marshal(record)
	user := auth.User(ctx).GetUser()
	err = processService.DispatchDingTalkProcess(user.TopCorporationId, record.Id, record.TableName(), string(processParam), config.StaffQuitApplyFormTemplate,
		processService.HandlerUser{
			Id:     user.Id,
			Name:   user.Name,
			Mobile: user.Phone,
		}, formFieldValue)

	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

// Rollback 离职回档
func (stf *StaffQuit) Rollback(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffQuit
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	record := (&hrModel.StaffQuitRecord{}).FindBy(param.Id)
	if record.Id == 0 {
		log.ErrorFields("StaffQuitRecord.FindBy is nil", map[string]interface{}{"id": param.Id})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	tx := model.DB().Begin()
	//删除离职数据
	err = record.TransactionDelete(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("StaffQuitRecord.TransactionDelete is error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.FAIL)
	}

	//合同变为正常状态
	laborContract := (&hrModel.StaffLaborContract{}).GetLatestByStaffArchiveId(record.StaffArchiveId)
	if laborContract.Id > 0 {
		laborContract.RelieveContractAt = nil
		laborContract.Status = util.LaborContractDefault
		err = laborContract.RollBackStatus(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("laborContract.RollBackStatus is error", map[string]interface{}{"error": err})
			return response.Error(rsp, response.FAIL)
		}
	}
	//人员变为在职
	oetStaff := rpc.GetStaffWithId(ctx, record.StaffId)
	if oetStaff == nil {
		tx.Rollback()
		log.ErrorFields("rpc.GetStaffWithId is nil", map[string]interface{}{"id": record.StaffId})
		return response.Error(rsp, response.FAIL)
	}
	oetStaff.WorkingState = util.JobStatusWorking
	err = rpc.EditOetStaff(ctx, auth.User(ctx).GetUserId(), oetStaff)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("rpc.EditOetStaff error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.FAIL)
	}
	tx.Commit()

	return response.Success(rsp, nil)
}

func SyncHistoryQuitRecordInfo() {
	time.Sleep(60 * time.Second)
	//查询所有离职人员
	staffQuitRecords := (&hrModel.StaffQuitRecord{}).GetAll()
	for _, record := range staffQuitRecords {
		oetStaff := rpc.GetStaffWithId(context.Background(), record.StaffId)
		if oetStaff == nil {
			continue
		}
		record.StaffName = oetStaff.Name
		record.JobNumber = oetStaff.StaffId
		record.Corporations.Build(oetStaff.CorporationId)
		record.WorkPostType = util.MasterToErp[oetStaff.Occupation]
		model.DB().Select("StaffName", "JobNumber", "GroupId", "CompanyId", "BranchId", "DepartmentId", "FleetId", "WorkPostType").Save(&record)
	}
}
