package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/process/dingTalkBpm"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"strings"
	"time"
)

type StaffQuit struct {
	hrModel.StaffQuit
	CorporationIds []int64 `json:"CorporationIds"`
	Keyword        string  `json:"Keyword"`
	model.Paginator
}

// List 离职管理列表
func (stf *StaffQuit) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffQuit
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)
	var oetWhere = service.OetWhere{Keyword: param.Keyword, JobStatusArr: []int64{util.JobStatusQuit}}
	oetStaffMaps, staffIds, _ := service.SelectOetStaffByMultiWhere(ctx, corporationIds, oetWhere, false, 0, 0)

	quitRecords, totalCount := (&hrModel.StaffQuitRecord{}).GetBy(staffIds, param.Paginator)

	for i := range quitRecords {
		quitRecords[i].StaffName = oetStaffMaps[quitRecords[i].StaffId].Name
		quitRecords[i].JobNumber = oetStaffMaps[quitRecords[i].StaffId].StaffId
		quitRecords[i].IdentityId = oetStaffMaps[quitRecords[i].StaffId].IdentifyId
	}

	return response.Success(rsp, map[string]interface{}{"Items": quitRecords, "TotalCount": totalCount})
}

// Create 新增离职员工
func (stf *StaffQuit) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffQuit
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param.StaffQuit); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	if len(param.Records) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	for i := range param.Records {
		if err := util.Validator().Struct(param.Records[i]); err != nil {
			log.ErrorFields("records validate fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsMissing)
		}
	}

	param.StaffQuit.ParseOpUser(ctx)

	//var beforeData = make(map[int64][]byte)

	for i := range param.Records {
		staff := rpc.GetStaffWithId(ctx, param.Records[i].StaffId)
		if staff == nil {
			return response.Error(rsp, response.ParamsMissing)
		}
		//corporation := rpc.GetCorporationDetailById(ctx, staff.CorporationId)
		//if corporation == nil {
		//	return response.Error(rsp, response.ParamsMissing)
		//}
		//
		//param.Records[i].GroupId = corporation.GroupId
		//param.Records[i].CompanyId = corporation.CompanyId
		//param.Records[i].BranchId = corporation.BranchId
		//param.Records[i].DepartmentId = corporation.DepartmentId
		//param.Records[i].FleetId = corporation.FleetId

		var archive hrModel.StaffArchive
		err := archive.FindByStaffId(param.Records[i].StaffId)
		if err != nil {
			log.ErrorFields("archive.FindByStaffId error", map[string]interface{}{"err": err, "staffId": param.Records[i].StaffId})
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		param.Records[i].StaffArchiveId = archive.Id
		var workPost hrModel.WorkPost
		_ = workPost.FirstById(param.Records[i].WorkPostId)
		param.Records[i].WorkPostName = workPost.Name
	}

	tx := model.DB().Begin()
	err = param.StaffQuit.TransactionCreate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("StaffQuit create fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	if !config.Config.DingTalkBpm.Enable {
		for i := range param.Records {
			err := service.StaffQuitAfterUpdateStaffArchive(tx, param.Records[i])
			if err != nil {
				tx.Rollback()
				log.ErrorFields("service.StaffQuitAfterUpdateStaffArchive error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

type FileJson struct {
	Id     int64  `json:"Id"`
	Url    string `json:"Url"`
	Path   string `json:"Path"`
	Name   string `json:"Name"`
	Suffix string `json:"Suffix"`
}

func (stf *StaffQuit) DispatchProcess(ctx context.Context, req *api.Request, rsp *api.Response) error {
	if !config.Config.DingTalkBpm.Enable {
		return response.Error(rsp, response.BadRequest)
	}

	var param hrModel.StaffQuitRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	record := (&hrModel.StaffQuitRecord{}).FindBy(param.Id)

	if record.Id == 0 {
		log.ErrorFields("StaffQuitRecord.FindBy is nil", map[string]interface{}{"id": param.Id})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	record.OpIp = auth.User(ctx).GetClientIp()

	var archive hrModel.StaffArchive
	_ = archive.FindByStaffId(record.StaffId)
	var workPostName []string
	for j := range archive.WorkPosts {
		if archive.WorkPosts[j].IsNowJob == util.NotIsNowJob {
			continue
		}
		var workPost hrModel.WorkPost
		err := workPost.FirstById(archive.WorkPosts[j].WorkPostId)
		if err != nil {
			continue
		}
		workPostName = append(workPostName, workPost.Name)
	}

	oetStaff := rpc.GetStaffWithId(ctx, record.StaffId)

	if oetStaff == nil {
		log.ErrorFields(" rpc.GetStaffWithId[2] is nil", nil)
		return response.Error(rsp, response.FAIL)
	}

	dingUserId, err := dingTalkBpm.GetUserId(oetStaff.Phone, "")
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	dingUserInfo, err := dingTalkBpm.GetUserInfo(dingUserId, "")

	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	if len(dingUserInfo.DeptIdList) == 0 {
		return response.Error(rsp, response.FAIL)
	}

	dingDeptInfo, err := dingTalkBpm.GetDepartmentInfo(dingUserInfo.DeptIdList[0], "")
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	extValue, _ := json.Marshal([]map[string]string{{"name": dingDeptInfo.Name, "id": fmt.Sprintf("%v", dingDeptInfo.DeptId)}})

	contractEndAt := ""
	if record.ContractEndAt != nil && !time.Time(*record.ContractEndAt).IsZero() {
		contractEndAt = time.Time(*record.ContractEndAt).Format(model.DateFormat)
	}
	joinCompanyAt := ""
	if record.JoinCompanyAt != nil && !time.Time(*record.JoinCompanyAt).IsZero() {
		joinCompanyAt = time.Time(*record.JoinCompanyAt).Format(model.DateFormat)
	}
	var images []byte
	var files []FileJson
	err = json.Unmarshal(record.FilePath, &files)

	if err == nil && len(files) > 0 {
		var imgs []string
		for f := range files {
			imgs = append(imgs, config.Config.StaticFileHttpPrefix+files[f].Path)
		}

		images, _ = json.Marshal(imgs)
	}
	formFieldValue := []map[string]string{
		{"name": "姓名", "value": oetStaff.Name},
		{"name": "所属部门", "value": dingDeptInfo.Name, "extValue": string(extValue), "componentType": "DepartmentField"},
		{"name": "工号", "value": oetStaff.StaffId},
		{"name": "岗位名称", "value": strings.Join(workPostName, ",")},
		{"name": "劳动合同终止时间", "value": contractEndAt},
		{"name": "劳动合同解除类型", "value": util.RelieveContractTypeMap[record.RelieveContractType]},
		{"name": "参加工作时间", "value": joinCompanyAt},
		{"name": "年休假剩余天数", "value": fmt.Sprintf("%v", record.YearHolidayRemain)},
		{"name": "调休剩余天数", "value": fmt.Sprintf("%v", record.ExchangeHolidayRemain)},
		{"name": "附件", "value": string(images)},
	}

	processParam, _ := json.Marshal(record)
	user := auth.User(ctx).GetUser()
	err = processService.DispatchDingTalkProcess(user.TopCorporationId, record.Id, record.TableName(), string(processParam), config.StaffQuitApplyFormTemplate,
		processService.HandlerUser{
			Id:     user.Id,
			Name:   user.Name,
			Mobile: user.Phone,
		}, formFieldValue)

	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

// Rollback 离职回档
func (stf *StaffQuit) Rollback(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffQuit
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	record := (&hrModel.StaffQuitRecord{}).FindBy(param.Id)
	if record.Id == 0 {
		log.ErrorFields("StaffQuitRecord.FindBy is nil", map[string]interface{}{"id": param.Id})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	tx := model.DB().Begin()
	//删除离职数据
	err = record.TransactionDelete(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("StaffQuitRecord.TransactionDelete is error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.FAIL)
	}

	//合同变为正常状态
	laborContract := (&hrModel.StaffLaborContract{}).GetLatestByStaffArchiveId(record.StaffArchiveId)
	if laborContract.Id > 0 {
		laborContract.RelieveContractAt = nil
		laborContract.Status = util.LaborContractDefault
		err = laborContract.RollBackStatus(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("laborContract.RollBackStatus is error", map[string]interface{}{"error": err})
			return response.Error(rsp, response.FAIL)
		}
	}
	//人员变为在职
	oetStaff := rpc.GetStaffWithId(ctx, record.StaffId)
	if oetStaff == nil {
		tx.Rollback()
		log.ErrorFields("rpc.GetStaffWithId is nil", map[string]interface{}{"id": record.StaffId})
		return response.Error(rsp, response.FAIL)
	}
	oetStaff.WorkingState = util.JobStatusWorking
	err = rpc.EditOetStaff(ctx, auth.User(ctx).GetUserId(), oetStaff)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("rpc.EditOetStaff error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.FAIL)
	}
	tx.Commit()

	return response.Success(rsp, nil)
}
