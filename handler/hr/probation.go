package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type Probation struct {
	StaffArchiveId int64           `json:"StaffArchiveId"`
	ProbationEndAt model.LocalTime `json:"ProbationEndAt"`
}

// List 试用期管理列表
func (stp *Probation) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffArchiveFilter
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	data := ArchiveList(ctx, param, "List", true)

	return response.Success(rsp, data)
}

// BecomeWorker 试用期转正
func (stp *Probation) BecomeWorker(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Probation
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StaffArchiveId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	var archive hrModel.StaffArchive
	err = archive.FindById(param.StaffArchiveId)
	if err != nil {
		log.ErrorFields("archive.FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	oetStaff := rpc.GetStaffWithId(ctx, archive.StaffId)
	if oetStaff == nil {
		log.ErrorFields("rpc GetStaffWithId is nil", map[string]interface{}{"staffId": archive.StaffId})
		return response.Error(rsp, response.FAIL)
	}
	beforeData, _ := json.Marshal(map[string]interface{}{"JobStatus": oetStaff.WorkingState})

	oetStaff.WorkingState = util.JobStatusWorking

	err = rpc.EditOetStaff(ctx, auth.User(ctx).GetUserId(), oetStaff)
	if err != nil {
		log.ErrorFields("rpc EditOetStaff error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	//插入修改记录
	afterData, _ := json.Marshal(map[string]interface{}{"JobStatus": util.JobStatusWorking})
	logger := hrModel.StaffArchiveLogger{
		StaffArchiveId: archive.Id,
		Scene:          hrModel.ArchiveLoggerSceneUpdate,
		Modular:        hrModel.ArchiveLoggerModularForProbation,
		BeforeData:     model.JSON(beforeData),
		AfterData:      model.JSON(afterData),
		Ip:             auth.User(ctx).GetClientIp(),
	}

	logger.ParseOpUser(ctx)

	err = logger.Create()
	if err != nil {
		log.ErrorFields("logger.Create", map[string]interface{}{"err": err})
	}

	return response.Success(rsp, err)
}

// ExtendProbation 延长试用期
func (stp *Probation) ExtendProbation(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Probation
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StaffArchiveId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	var archive hrModel.StaffArchive
	err = archive.FindById(param.StaffArchiveId)
	if err != nil {
		log.ErrorFields("archive.FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	beforeData, _ := json.Marshal(map[string]interface{}{"ProbationEndAt": archive.ProbationEndAt})

	err = archive.UpdateProbationEndAt(time.Time(param.ProbationEndAt))
	if err != nil {
		log.ErrorFields("archive.UpdateProbationEndAt error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	//插入修改记录
	afterData, _ := json.Marshal(map[string]interface{}{"ProbationEndAt": param.ProbationEndAt})
	logger := hrModel.StaffArchiveLogger{
		StaffArchiveId: archive.Id,
		Scene:          hrModel.ArchiveLoggerSceneUpdate,
		Modular:        hrModel.ArchiveLoggerModularForProbation,
		BeforeData:     model.JSON(beforeData),
		AfterData:      model.JSON(afterData),
		Ip:             auth.User(ctx).GetClientIp(),
	}

	logger.ParseOpUser(ctx)

	err = logger.Create()
	if err != nil {
		log.ErrorFields("logger.Create", map[string]interface{}{"err": err})
	}

	return response.Success(rsp, nil)
}

func (stp *Probation) BecomeWorkerProcess(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param hrModel.DriverBecomeWorkerRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validator.Validate error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	user := auth.User(ctx).GetUser()

	//发起人必须是车队
	corporation := rpc.GetCorporationDetailById(ctx, user.CorporationId)
	if corporation == nil || corporation.Item.Type != util.CorporationTypeForFleet {
		return response.Error(rsp, response.NotFleetAccount)
	}

	var archive hrModel.StaffArchive
	err = archive.FindById(param.StaffArchiveId)
	if err != nil {
		log.ErrorFields("archive.FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	oetStaff := rpc.GetStaffWithId(ctx, archive.StaffId)
	if oetStaff == nil {
		log.ErrorFields("rpc GetStaffWithId is nil", map[string]interface{}{"staffId": archive.StaffId})
		return response.Error(rsp, response.FAIL)
	}
	param.StaffId = oetStaff.Id
	param.StaffName = oetStaff.Name
	param.OpUserName = user.Name
	param.OpUserId = user.Id

	param.FleetCheckStatus = util.BecomeWorkerCheckStatusForUnknown
	param.FormStep = util.ProcessFormStepStart
	param.ApplyStatus = util.ApplyStatusForDoing

	tx := model.DB().Begin()

	if param.Id > 0 {
		err = param.TransactionUpdate(tx)
	} else {
		err = param.TransactionCreate(tx)
	}
	if err != nil {
		log.ErrorFields("BecomeWorkerProcess TransactionCreate || Update fail", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbSaveFail)
	}

	//有流程时 发起流程审批
	if config.Config.Lbpm.Enable {
		byteParam, _ := json.Marshal(param)

		formData := map[string]interface{}{}
		if param.IsRestart && param.ProcessId != "" {
			//重新发起流程
			err = processService.RestartProcess(user, param.ProcessId, string(byteParam), formData)
		} else {
			//发起新的流程
			processTitle := fmt.Sprintf("%s/%s", oetStaff.Name, oetStaff.StaffId)
			_, err = processService.NewDispatchProcess(user, config.DriverBecomeWorkerApplyFormTemplate, processTitle, param.Id, param.TableName(), param.ApplyStatusFieldName(), string(byteParam), formData)
		}

		if err != nil {
			tx.Rollback()
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	tx.Commit()
	return response.Success(rsp, nil)
}

func (stp *Probation) ShowBecomeWorkerProcess(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param hrModel.DriverBecomeWorkerRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	record := (&hrModel.DriverBecomeWorkerRecord{}).FirstBy(param.Id)

	return response.Success(rsp, record)
}

func (stp *Probation) BecomeWorkerProcessList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param hrModel.DriverBecomeWorkerRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	records := (&hrModel.DriverBecomeWorkerRecord{}).GetRecords(param.StaffArchiveId)
	for i := range records {
		var process processModel.LbpmApplyProcess
		_ = process.GetProcessByItemId(records[i].Id, records[i].TableName())
		records[i].CurrentHandler = process.CurrentHandlerUserName
		records[i].ProcessId = process.ProcessId
		if process.DoneAt != nil {
			records[i].DoneAt = *process.DoneAt
		}
	}
	return response.Success(rsp, records)
}

func (stp *Probation) BecomeWorkerProcessUpdate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param hrModel.DriverBecomeWorkerRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	record := (&hrModel.DriverBecomeWorkerRecord{}).FirstBy(param.Id)
	if record.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if record.FormStep != util.ProcessFormStepTwo {
		return response.Error(rsp, response.FAIL)
	}

	record.FleetCheckStatus = param.FleetCheckStatus
	record.FormStep = record.FormStep + 1
	record.AssessResult = param.AssessResult
	err = record.Update()
	if err != nil {
		log.ErrorFields("BecomeWorkerProcess Update fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, nil)
}
