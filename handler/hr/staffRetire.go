package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"sort"
	"time"
)

type StaffRetire struct {
	//hrModel.StaffRetire
	CorporationIds []int64          `json:"CorporationIds"`
	Keyword        string           `json:"Keyword"`      //姓名、工号
	RetireStatus   int64            `json:"RetireStatus"` //退休状态  1退休待审 2退休  3退休返聘
	StartRetireAt  *model.LocalTime `json:"StartRetireAt"`
	EndRetireAt    *model.LocalTime `json:"EndRetireAt"`
	Sex            int64            `json:"Sex"`
	model.Paginator
	StaffArchiveIds []int64         `json:"StaffArchiveIds"`
	StaffArchiveId  int64           `json:"StaffArchiveId"`
	RetireMore      string          `json:"RetireMore"`
	RetireAt        model.LocalTime `json:"RetireAt"`
	RetireType      int64           `json:"RetireType"`
}

// List 员工退休列表
func (stf *StaffRetire) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffRetire
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)
	var oetWhere service.OetWhere
	if param.EndRetireAt == nil || time.Time(*param.EndRetireAt).Unix() <= 0 || time.Time(*param.EndRetireAt).Unix() > time.Now().Unix() {
		now := model.LocalTime(time.Now())
		oetWhere.EndRetireAt = &now
	} else {
		oetWhere.EndRetireAt = param.EndRetireAt
	}

	//退休审批中
	if param.RetireStatus == util.RetireStatusForDoing {
		oetWhere.JobStatusArr = []int64{util.JobStatusWorking, util.JobStatusProbation}
	} else if param.RetireStatus == util.RetireStatusForDone {
		oetWhere.JobStatusArr = []int64{util.JobStatusRetire}
	} else if param.RetireStatus == util.RetireStatusForWorking {
		oetWhere.JobStatusArr = []int64{util.JobStatusRetireWork}
	} else {
		oetWhere.JobStatusArr = []int64{util.JobStatusWorking, util.JobStatusProbation, util.JobStatusRetire, util.JobStatusRetireWork}
	}
	oetWhere.Keyword = param.Keyword
	oetWhere.StartRetireAt = param.StartRetireAt
	oetWhere.SortOrder = "DESC"
	oetWhere.SortField = "RetireDateAt"

	records, count := retireList(ctx, corporationIds, oetWhere, param.Paginator, true)

	return response.Success(rsp, map[string]interface{}{"Items": records, "TotalCount": count, "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

func (stf *StaffRetire) WorkingList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffRetire
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)
	var oetWhere service.OetWhere
	if param.StartRetireAt == nil || time.Time(*param.StartRetireAt).Unix() < time.Now().Unix() {
		year, month, day := time.Now().AddDate(0, 0, 1).Date()
		now := model.LocalTime(time.Date(year, month, day, 0, 0, 0, 0, time.Local))
		oetWhere.StartRetireAt = &now
	} else {
		oetWhere.StartRetireAt = param.StartRetireAt
	}
	oetWhere.EndRetireAt = param.EndRetireAt

	oetWhere.JobStatusArr = []int64{util.JobStatusWorking, util.JobStatusProbation}
	oetWhere.Keyword = param.Keyword
	oetWhere.SortField = "RetireDateAt"
	oetWhere.SortOrder = "ASC"
	oetWhere.Gender = param.Sex

	records, count := retireList(ctx, corporationIds, oetWhere, param.Paginator, false)

	return response.Success(rsp, map[string]interface{}{"Items": records, "TotalCount": count, "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

type RetireListItem struct {
	StaffArchiveId    int64                           `json:"StaffArchiveId"`
	StaffId           int64                           `json:"StaffId"`
	StaffName         string                          `json:"StaffName"`
	JobNumber         string                          `json:"JobNumber"`
	Sex               int64                           `json:"Sex"`
	BirthDate         string                          `json:"BirthDate"`
	CorporationId     int64                           `json:"CorporationId"`
	CorporationName   string                          `json:"CorporationName"`
	WorkPostName      string                          `json:"WorkPostName"`
	RetireAt          string                          `json:"RetireAt"`
	JoinCompanyAt     string                          `json:"JoinCompanyAt"`
	RetireStatus      int64                           `json:"RetireStatus"`
	More              string                          `json:"More"`
	RetireWorkRecords []hrModel.StaffRetireWorkRecord `json:"RetireWorkRecords"`
	RetireAtUnix      int64                           `json:"-"`
}

func retireList(ctx context.Context, corporationIds []int64, oetWhere service.OetWhere, paginator model.Paginator, isRetired bool) ([]RetireListItem, int64) {
	oetStaffMaps, _, count := service.SelectOetStaffByMultiWhere(ctx, corporationIds, oetWhere, true, int64(paginator.Offset), int64(paginator.Limit))

	var retireLists []RetireListItem
	for staffId := range oetStaffMaps {
		var staffArchive hrModel.StaffArchive
		_ = staffArchive.FindByStaffId(oetStaffMaps[staffId].Id)

		var workPostName string
		if len(staffArchive.WorkPosts) > 0 {
			var workPost hrModel.WorkPost
			_ = workPost.FirstById(staffArchive.WorkPosts[0].WorkPostId)
			workPostName = workPost.Name
		}

		var sex int64
		if oetStaffMaps[staffId].Sex {
			sex = util.Male
		} else {
			sex = util.Female
		}
		var birthDate string
		if oetStaffMaps[staffId].IdentifyId != "" {
			date := []byte(oetStaffMaps[staffId].IdentifyId)[6:14]
			birthTime, _ := time.ParseInLocation("20060102", string(date), time.Local)
			birthDate = birthTime.Format(model.DateFormat)
		}
		var corporationName string
		corporation := rpc.GetCorporationById(ctx, oetStaffMaps[staffId].CorporationId)
		if corporation != nil {
			corporationName = corporation.Name
		}
		var retireStatus int64
		if oetStaffMaps[staffId].WorkingState == util.JobStatusRetire {
			retireStatus = util.RetireStatusForDone
		} else if oetStaffMaps[staffId].WorkingState == util.JobStatusRetireWork {
			retireStatus = util.RetireStatusForWorking
		} else {
			retireStatus = util.RetireStatusForDoing
		}

		//查询返聘记录
		var retireWorkRecords []hrModel.StaffRetireWorkRecord
		if isRetired {
			retireWorkRecords = (&hrModel.StaffRetireWorkRecord{}).GetBy(staffId)
			for i := range retireWorkRecords {
				var workPost hrModel.WorkPost
				_ = workPost.FirstById(retireWorkRecords[i].WorkPostId)
				retireWorkRecords[i].WorkPostName = workPost.Name

				corporation := rpc.GetCorporationDetailById(ctx, retireWorkRecords[i].CorporationId)
				if corporation != nil {
					retireWorkRecords[i].CorporationName = corporation.Item.Name
				}
			}
		}

		var item = RetireListItem{
			StaffArchiveId:    staffArchive.Id,
			StaffId:           staffId,
			StaffName:         oetStaffMaps[staffId].Name,
			JobNumber:         oetStaffMaps[staffId].StaffId,
			Sex:               sex,
			BirthDate:         birthDate,
			CorporationId:     oetStaffMaps[staffId].CorporationId,
			CorporationName:   corporationName,
			RetireAt:          time.Unix(oetStaffMaps[staffId].RetireDate, 64).Format(model.DateFormat),
			RetireAtUnix:      oetStaffMaps[staffId].RetireDate,
			JoinCompanyAt:     time.Unix(oetStaffMaps[staffId].RegisterTime, 64).Format(model.DateFormat),
			WorkPostName:      workPostName,
			RetireStatus:      retireStatus,
			More:              staffArchive.RetireMore,
			RetireWorkRecords: retireWorkRecords,
		}

		retireLists = append(retireLists, item)
	}

	if oetWhere.SortOrder == "DESC" {
		sort.SliceStable(retireLists, func(i, j int) bool {
			if retireLists[i].RetireAtUnix > retireLists[j].RetireAtUnix {
				return true
			}
			if retireLists[i].RetireAtUnix == retireLists[j].RetireAtUnix {
				if retireLists[i].StaffId > retireLists[j].StaffId {
					return true
				}
				return false
			}
			return false
		})
	} else {
		sort.SliceStable(retireLists, func(i, j int) bool {
			if retireLists[i].RetireAtUnix < retireLists[j].RetireAtUnix {
				return true
			}
			if retireLists[i].RetireAtUnix == retireLists[j].RetireAtUnix {
				if retireLists[i].StaffId > retireLists[j].StaffId {
					return true
				}
				return false
			}
			return false
		})
	}

	return retireLists, count
}

// Create 新增退休员工记录
//func (stf *StaffRetire) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
//	var param StaffRetire
//	err := json.Unmarshal([]byte(req.Body), &param)
//	if err != nil {
//		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
//		return response.Error(rsp, response.ParamsInvalid)
//	}
//
//	if err := util.Validator().Struct(param.StaffRetire); err != nil {
//		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
//		return response.Error(rsp, response.ParamsMissing)
//	}
//	if len(param.Records) == 0 {
//		return response.Error(rsp, response.ParamsMissing)
//	}
//	for i := range param.Records {
//		if err := util.Validator().Struct(param.Records[i]); err != nil {
//			log.ErrorFields("records validate fail", map[string]interface{}{"err": err})
//			return response.Error(rsp, response.ParamsMissing)
//		}
//	}
//	//当前操作人
//	var opStaffName string
//	if auth.User(ctx).HasStaff() {
//		opStaff := rpc.GetStaffWithId(ctx, auth.User(ctx).GetStaffId())
//		if opStaff != nil {
//			opStaffName = opStaff.Name
//		}
//	}
//
//	param.StaffRetire.OpStaffId = auth.User(ctx).GetStaffId()
//
//	var loggers []hrModel.StaffArchiveLogger
//	for i := range param.Records {
//		staff := rpc.GetStaffWithId(ctx, param.Records[i].StaffId)
//		if staff == nil {
//			return response.Error(rsp, response.ParamsMissing)
//		}
//		corporation := rpc.GetCorporationDetailById(ctx, staff.CorporationId)
//		if corporation == nil {
//			return response.Error(rsp, response.ParamsMissing)
//		}
//
//		param.Records[i].GroupId = corporation.GroupId
//		param.Records[i].CompanyId = corporation.CompanyId
//		param.Records[i].BranchId = corporation.BranchId
//		param.Records[i].DepartmentId = corporation.DepartmentId
//		param.Records[i].FleetId = corporation.FleetId
//		param.Records[i].Status = util.JobStatusRetire
//
//		var archive hrModel.StaffArchive
//		err := archive.FindByStaffId(param.Records[i].StaffId)
//		if err != nil {
//			log.ErrorFields("archive.FindByStaffId error", map[string]interface{}{"err": err, "staffId": param.Records[i].StaffId})
//			return response.Error(rsp, response.DbNotFoundRecord)
//		}
//		beforeData, _ := json.Marshal(map[string]interface{}{"JobStatus": staff.WorkingState})
//		afterData, _ := json.Marshal(map[string]interface{}{"JobStatus": param.Records[i].Status})
//		loggers = append(loggers, hrModel.StaffArchiveLogger{
//			StaffArchiveId: archive.Id,
//			Scene:          hrModel.ArchiveLoggerSceneUpdate,
//			Modular:        hrModel.ArchiveLoggerModularForRetire,
//			BeforeData:     beforeData,
//			AfterData:      afterData,
//			Ip:             auth.User(ctx).GetClientIp(),
//			OpStaffName:    opStaffName,
//			OpUser: model.OpUser{
//				OpStaffId: auth.User(ctx).GetStaffId(),
//			},
//		})
//	}
//
//	err = param.StaffRetire.Create()
//	if err != nil {
//		log.ErrorFields("StaffRetire create fail", map[string]interface{}{"err": err})
//		return response.Error(rsp, response.DbSaveFail)
//	}
//
//	//新增操作日志
//	var logger hrModel.StaffArchiveLogger
//	err = logger.Insert(loggers)
//	if err != nil {
//		log.ErrorFields("logger.Insert", map[string]interface{}{"err": err})
//	}
//
//	return response.Success(rsp, nil)
//}

// SwitchStatus 更改退休状态 退休->退休返聘
/*func (stf *StaffRetire) SwitchStatus(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffRetire
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	//当前操作人
	//var opStaffName string
	//if auth.User(ctx).HasStaff() {
	//	opStaff := rpc.GetStaffWithId(ctx, auth.User(ctx).GetStaffId())
	//	if opStaff != nil {
	//		opStaffName = opStaff.Name
	//	}
	//}

	var record hrModel.StaffRetireRecord
	err = record.FindBy(param.RecordId)
	if err != nil {
		log.ErrorFields("record.FindBy error", map[string]interface{}{"err": err, "id": param.RecordId})
		return response.Error(rsp, response.ParamsInvalid)
	}
	oetStaff := rpc.GetStaffWithId(ctx, record.StaffId)
	if oetStaff == nil {
		return response.Error(rsp, response.ParamsInvalid)
	}
	record.Status = util.JobStatusRetireWork
	err = record.SwitchStatus(param.RecordId)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	//新增操作日志
	var archive hrModel.StaffArchive
	err = archive.FindByStaffId(record.StaffId)
	if err != nil {
		log.ErrorFields("archive.FindByStaffId error", map[string]interface{}{"err": err, "staffId": record.StaffId})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//beforeData, _ := json.Marshal(map[string]interface{}{"JobStatus": oetStaff.WorkingState})
	//afterData, _ := json.Marshal(map[string]interface{}{"JobStatus": param.})
	//logger := hrModel.StaffArchiveLogger{
	//	StaffArchiveId: archive.Id,
	//	Scene:          hrModel.ArchiveLoggerSceneUpdate,
	//	Modular:        hrModel.ArchiveLoggerModularForRetire,
	//	BeforeData:     model.JSON(beforeData),
	//	AfterData:      model.JSON(afterData),
	//	Ip:             auth.User(ctx).GetClientIp(),
	//	OpStaffName:    opStaffName,
	//	OpUser: model.OpUser{
	//		OpStaffId: auth.User(ctx).GetStaffId(),
	//	},
	//}

	//err = logger.Create()
	//if err != nil {
	//	log.ErrorFields("logger.Create", map[string]interface{}{"err": err})
	//}

	return response.Success(rsp, nil)
}
*/
// ListRehireRecord 返聘记录
//func (stf *StaffRetire) ListRehireRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
//	var param StaffRetire
//	err := json.Unmarshal([]byte(req.Body), &param)
//	if err != nil {
//		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
//		return response.Error(rsp, response.ParamsInvalid)
//	}
//	//当前操作人
//	var opStaffName string
//	if auth.User(ctx).HasStaff() {
//		opStaff := rpc.GetStaffWithId(ctx, auth.User(ctx).GetStaffId())
//		if opStaff != nil {
//			opStaffName = opStaff.Name
//		}
//	}
//
//	var record hrModel.StaffRetireRehireRecord
//	list, totalCount, err := record.GetBy(param.RecordId)
//	if err != nil {
//		log.ErrorFields("record.FindBy error", map[string]interface{}{"err": err, "id": param.RecordId})
//		return response.Error(rsp, response.ParamsInvalid)
//	}
//	oetStaff := rpc.GetStaffWithId(ctx, record.StaffId)
//	if oetStaff == nil {
//		return response.Error(rsp, response.ParamsInvalid)
//	}
//
//	//新增操作日志
//	var archive hrModel.StaffArchive
//	err = archive.FindByStaffId(record.StaffId)
//	if err != nil {
//		log.ErrorFields("archive.FindByStaffId error", map[string]interface{}{"err": err, "staffId": record.StaffId})
//		return response.Error(rsp, response.ParamsInvalid)
//	}
//
//	beforeData, _ := json.Marshal(map[string]interface{}{"JobStatus": oetStaff.WorkingState})
//	afterData, _ := json.Marshal(map[string]interface{}{"JobStatus": param.Status})
//	logger := hrModel.StaffArchiveLogger{
//		StaffArchiveId: archive.Id,
//		Scene:          hrModel.ArchiveLoggerSceneUpdate,
//		Modular:        hrModel.ArchiveLoggerModularForRetire,
//		BeforeData:     model.JSON(beforeData),
//		AfterData:      model.JSON(afterData),
//		Ip:             auth.User(ctx).GetClientIp(),
//		OpStaffName:    opStaffName,
//		OpUser: model.OpUser{
//			OpStaffId: auth.User(ctx).GetStaffId(),
//		},
//	}
//
//	err = logger.Create()
//	if err != nil {
//		log.ErrorFields("logger.Create", map[string]interface{}{"Items": list, "TotalCount": totalCount})
//	}
//
//	return response.Success(rsp, nil)
//}

// BatchRetireSubmit 批量退休审批
func (stf *StaffRetire) BatchRetireSubmit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffRetire
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(param.StaffArchiveIds) == 0 || param.RetireMore == "" {
		return response.Error(rsp, response.ParamsMissing)
	}

	for i := range param.StaffArchiveIds {
		var staffArchive hrModel.StaffArchive
		err = staffArchive.FindById(param.StaffArchiveIds[i])
		if err != nil {
			log.ErrorFields("staffArchive.FindById error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}

		oetStaff := rpc.GetStaffWithId(ctx, staffArchive.StaffId)
		if oetStaff != nil {
			oetStaff.WorkingState = util.JobStatusRetire
			err := rpc.EditOetStaff(context.TODO(), auth.User(ctx).GetUserId(), oetStaff)
			if err != nil {
				log.ErrorFields("rpc EditOetStaff err", map[string]interface{}{"err": err})
				return response.Error(rsp, response.FAIL)
			}
		}
	}
	err = (&hrModel.StaffArchive{}).UpdateRetireMoreByIds(param.StaffArchiveIds, param.RetireMore)
	if err != nil {
		log.ErrorFields("StaffArchive UpdateRetireMoreByIds error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Error(rsp, response.SUCCESS)
}

// RetireSubmit 退休审批
func (stf *StaffRetire) RetireSubmit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffRetire
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StaffArchiveId == 0 || param.RetireMore == "" || param.RetireType == 0 || time.Time(param.RetireAt).IsZero() {
		return response.Error(rsp, response.ParamsMissing)
	}

	if param.RetireType == util.RetireTypeForNow && time.Time(param.RetireAt).Unix() > time.Now().Unix() {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.RetireType == util.RetireTypeForDelay && time.Time(param.RetireAt).Unix() < time.Now().Unix() {
		return response.Error(rsp, response.ParamsInvalid)
	}

	var staffArchive hrModel.StaffArchive
	err = staffArchive.FindById(param.StaffArchiveId)
	if err != nil {
		log.ErrorFields("staffArchive.FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	oetStaff := rpc.GetStaffWithId(ctx, staffArchive.StaffId)
	if oetStaff == nil {
		log.ErrorFields("rpc GetStaffWithId is nil", nil)
		return response.Error(rsp, response.FAIL)
	}

	//立即退休：更改状态为退休  更改退休时间
	if param.RetireType == util.RetireTypeForNow {
		oetStaff.WorkingState = util.JobStatusRetire
		oetStaff.RetireDate = time.Time(param.RetireAt).Unix()
	}
	//延迟退休：更改退休时间
	if param.RetireType == util.RetireTypeForDelay {
		oetStaff.RetireDate = time.Time(param.RetireAt).Unix()
	}

	err = rpc.EditOetStaff(ctx, auth.User(ctx).GetUserId(), oetStaff)
	if err != nil {
		log.ErrorFields("rpc EditOetStaff err", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	err = (&hrModel.StaffArchive{}).UpdateRetireMoreByIds([]int64{param.StaffArchiveId}, param.RetireMore)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Error(rsp, response.SUCCESS)
}

// EarlyRetireSubmit 提前退休
func (stf *StaffRetire) EarlyRetireSubmit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffRetire
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StaffArchiveId == 0 || param.RetireMore == "" || time.Time(param.RetireAt).IsZero() {
		return response.Error(rsp, response.ParamsMissing)
	}

	if time.Time(param.RetireAt).Unix() > time.Now().Unix() {
		return response.Error(rsp, response.ParamsInvalid)
	}

	var staffArchive hrModel.StaffArchive
	err = staffArchive.FindById(param.StaffArchiveId)
	if err != nil {
		log.ErrorFields("staffArchive.FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	oetStaff := rpc.GetStaffWithId(ctx, staffArchive.StaffId)
	if oetStaff == nil {
		log.ErrorFields("rpc GetStaffWithId is nil", nil)
		return response.Error(rsp, response.FAIL)
	}

	oetStaff.WorkingState = util.JobStatusRetire
	oetStaff.RetireDate = time.Time(param.RetireAt).Unix()

	err = rpc.EditOetStaff(ctx, auth.User(ctx).GetUserId(), oetStaff)
	if err != nil {
		log.ErrorFields("rpc EditOetStaff err", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	//解除劳动合同
	err = (&hrModel.StaffLaborContract{}).UpdateStatusToRelieveByStaffArchiveId(staffArchive.Id, util.LaborContractRelieve, time.Time(param.RetireAt))
	if err != nil {
		log.ErrorFields("StaffLaborContract UpdateStatus error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	err = (&hrModel.StaffArchive{}).UpdateRetireMoreByIds([]int64{param.StaffArchiveId}, param.RetireMore)
	if err != nil {
		log.ErrorFields("StaffArchive UpdateRetireMoreByIds error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Error(rsp, response.SUCCESS)
}

// AddRetireWork 退休返聘
func (stf *StaffRetire) AddRetireWork(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param hrModel.StaffRetireWorkRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	var staffArchive hrModel.StaffArchive
	err = staffArchive.FindById(param.StaffArchiveId)
	if err != nil {
		log.ErrorFields("staffArchive.FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	oetStaff := rpc.GetStaffWithId(ctx, staffArchive.StaffId)
	if oetStaff == nil {
		log.ErrorFields("rpc GetStaffWithId is nil", nil)
		return response.Error(rsp, response.FAIL)
	}

	param.StaffId = staffArchive.StaffId
	param.ParseOpUser(ctx)
	tx := model.DB().Begin()
	if err := param.TransactionCreate(tx); err != nil {
		tx.Rollback()
		log.ErrorFields("StaffRetireWorkRecord.TransactionCreate error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	oetStaff.WorkingState = util.JobStatusRetireWork
	err = rpc.EditOetStaff(ctx, auth.User(ctx).GetUserId(), oetStaff)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("rpc EditOetStaff err", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	tx.Commit()
	return response.Success(rsp, response.SUCCESS)
}

// RelieveRetireWork 解除返聘
func (stf *StaffRetire) RelieveRetireWork(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param hrModel.StaffRetireWorkRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.RetireWorkEndAt == nil || param.StaffArchiveId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var staffArchive hrModel.StaffArchive
	err = staffArchive.FindById(param.StaffArchiveId)
	if err != nil {
		log.ErrorFields("staffArchive.FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	oetStaff := rpc.GetStaffWithId(ctx, staffArchive.StaffId)
	if oetStaff == nil {
		log.ErrorFields("rpc GetStaffWithId is nil", nil)
		return response.Error(rsp, response.FAIL)
	}

	record := (&hrModel.StaffRetireWorkRecord{}).LatestRecord(oetStaff.Id)
	if record.Id == 0 {
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	record.ParseOpUser(ctx)
	record.RetireWorkEndAt = param.RetireWorkEndAt

	tx := model.DB().Begin()
	if err := record.TransactionUpdateEndAt(tx); err != nil {
		tx.Rollback()
		log.ErrorFields("StaffRetireWorkRecord.TransactionUpdateEndAt error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	oetStaff.WorkingState = util.JobStatusRetire
	err = rpc.EditOetStaff(ctx, auth.User(ctx).GetUserId(), oetStaff)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("rpc EditOetStaff err", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	tx.Commit()
	return response.Success(rsp, response.SUCCESS)
}

// Rollback 退休回档
func (stf *StaffRetire) Rollback(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffRetire
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var staffArchive hrModel.StaffArchive
	err = staffArchive.FindById(param.StaffArchiveId)
	if err != nil {
		log.ErrorFields("staffArchive.FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	oetStaff := rpc.GetStaffWithId(ctx, staffArchive.StaffId)
	if oetStaff == nil {
		log.ErrorFields("rpc GetStaffWithId is nil", nil)
		return response.Error(rsp, response.FAIL)
	}

	tx := model.DB().Begin()
	//合同变为正常状态
	laborContract := (&hrModel.StaffLaborContract{}).GetLatestByStaffArchiveId(staffArchive.Id)
	if laborContract.Id > 0 {
		laborContract.RelieveContractAt = nil
		laborContract.Status = util.LaborContractDefault
		err = laborContract.RollBackStatus(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("laborContract.RollBackStatus is error", map[string]interface{}{"error": err})
			return response.Error(rsp, response.FAIL)
		}
	}
	//人员变为在职
	oetStaff.WorkingState = util.JobStatusWorking
	//计算退休时间
	date := []byte(oetStaff.IdentifyId)[6:14]
	birthDate, _ := time.ParseInLocation("20060102", string(date), time.Local)
	var retireAt = oetStaff.RetireDate
	if oetStaff.Sex {
		retireAt = birthDate.AddDate(60, 0, 0).Unix()
	} else {
		retireAt = birthDate.AddDate(55, 0, 0).Unix()
	}

	oetStaff.RetireDate = retireAt
	err = rpc.EditOetStaff(ctx, auth.User(ctx).GetUserId(), oetStaff)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("rpc.EditOetStaff error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.FAIL)
	}
	tx.Commit()

	return response.Success(rsp, nil)
}
