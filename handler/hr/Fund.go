package hr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	protoStaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"strconv"
)

// Fund 公积金
type Fund struct {
	hrModel.FundRecord
	StaffIds       []int64 `json:"StaffIds"`
	CorporationIds []int64 `json:"CorporationIds"`
	Keyword        string  `json:"Keyword"`
	StartYear      int     `json:"StartYear"`
	EndYear        int     `json:"EndYear"`
	model.Paginator
}

// Setting 公积金缴纳比例设置
func (fun *Fund) Setting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param SettingParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	for i := range param.Records {
		if err := util.Validator().Struct(param.Records[i]); err != nil {
			log.ErrorFields("records validate fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsMissing)
		}
		param.Records[i].Scene = util.SceneForFund
		param.Records[i].ParseOpUser(ctx)
		param.Records[i].TopCorporationId = topCorporationId
	}

	var setting hrModel.InsuranceFundSetting
	err = setting.Create(param.Records, util.SceneForFund)

	if err != nil {
		log.ErrorFields("setting.Create fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

// SettingInfo 公积金设置的比例信息
func (fun *Fund) SettingInfo(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var setting hrModel.InsuranceFundSetting
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	settings := setting.Get(topCorporationId, util.SceneForFund)
	return response.Success(rsp, settings)
}

// Import 导入公积金记录
func (fun *Fund) Import(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ImportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			response.Error(rsp, response.FAIL)
		}
		return
	}()

	// 通过根机构获取所有员工数据
	oetStaffs := rpc.GetStaffsWithOption(ctx, auth.User(ctx).GetTopCorporationId())
	oetStaffMap := make(map[string]*protoStaff.OetStaffItem)
	for i := range oetStaffs {
		if oetStaffs[i].IdentifyId != "" {
			oetStaffMap[oetStaffs[i].IdentifyId] = oetStaffs[i]
		}
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()

	// 0姓名* 1身份证 2月度 3基数  4单位比例  5个人比例
	sheet := excelFile.Sheets[0]
	var importFailStaffs []string
	var successCount int64
	var funds []hrModel.FundRecord

	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 4 {
			continue
		}

		if row.Cells[0].String() == "" || row.Cells[1].String() == "" || row.Cells[2].String() == "" || row.Cells[3].String() == "" {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}

		identityId := row.Cells[1].String()
		oetStaff, ok := oetStaffMap[identityId]
		if !ok {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}
		var archive hrModel.StaffArchive
		err := archive.FindByStaffId(oetStaff.Id)

		if err != nil {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}

		var fund hrModel.FundRecord
		fund.StaffArchiveId = archive.Id
		fund.StaffId = oetStaff.Id
		fund.TopCorporationId = topCorporationId
		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 2:
				fund.AdjustDate, _ = row.Cells[2].Int64()
			case 3:
				base, err := row.Cells[3].Float()
				if err == nil {
					fund.FundBase = int64(base * 100) //转成单位：分
				}
			case 4:
				percent, err := row.Cells[4].Float()
				if err == nil {
					fund.FundCompanyAmount = int64((float64(fund.FundBase) * percent) / 100)
					fund.FundCompanyRatio = int64(percent * 100)
				}
			case 5:
				percent, err := row.Cells[5].Float()
				if err == nil {
					fund.FundPersonAmount = int64((float64(fund.FundBase) * percent) / 100)
					fund.FundPersonRatio = int64(percent * 100)
				}
			}
		}
		fund.SumAmount = fund.FundCompanyAmount + fund.FundPersonAmount
		successCount++
		funds = append(funds, fund)
	}
	if len(funds) > 0 {
		var fund hrModel.FundRecord
		err = fund.Create(funds)
		if err != nil {
			log.ErrorFields("fund.Create error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"SuccessCount": successCount,
		"FailCount":    len(importFailStaffs),
		"FailStaffs":   importFailStaffs,
	})
}

// Export 导出公积金记录
func (fun *Fund) Export(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Fund
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	result := FundList(ctx, param)

	return response.Success(rsp, result)
}

// List 公积金缴纳记录列表
func (fun *Fund) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Fund
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	result := FundList(ctx, param)

	return response.Success(rsp, result)
}

func FundList(ctx context.Context, param Fund) map[string]interface{} {

	param.CorporationIds = service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	var oetWhere service.OetWhere
	if param.Keyword != "" {
		oetWhere.Keyword = param.Keyword
	}

	oetStaffMap, staffIds, _ := service.SelectOetStaffByMultiWhere(ctx, param.CorporationIds, oetWhere, false, 0, 0)

	var startYear, endYear int64
	if param.StartYear > 0 {
		startYear, _ = strconv.ParseInt(fmt.Sprintf("%v01", param.StartYear), 10, 64)
	}
	if param.EndYear > 0 {
		endYear, _ = strconv.ParseInt(fmt.Sprintf("%v12", param.EndYear), 10, 64)
	}
	records, count := param.FundRecord.GetBy(staffIds, startYear, endYear, param.Paginator)

	for i := range records {
		oetStaff := oetStaffMap[records[i].StaffId]
		records[i].JobNumber = oetStaff.StaffId
		records[i].StaffName = oetStaff.Name
		corporation := rpc.GetCorporationById(context.Background(), oetStaff.CorporationId)

		if corporation != nil {
			records[i].CorporationName = corporation.Name
		}
	}

	return map[string]interface{}{"Items": records, "TotalCount": count}
}

// Create 新增公积金缴纳记录
func (fun *Fund) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Fund
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("param validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	if len(param.StaffIds) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()

	//获取公积金设置
	//var setting hrModel.InsuranceFundSetting
	//settings := setting.Get(topCorporationId, util.SceneForFund)
	//if len(settings) == 0 {
	//	return response.Error(rsp, response.FundSettingNotFund)
	//}
	//settingMap := buildSettingMap(settings)
	//
	//calcFundAmount(settingMap, &param.FundRecord)

	var records []hrModel.FundRecord
	for i := range param.StaffIds {
		var archive StaffArchive
		_ = archive.FindByStaffId(param.StaffIds[i])
		param.FundRecord.StaffArchiveId = archive.Id
		param.FundRecord.StaffId = param.StaffIds[i]
		param.FundRecord.ParseOpUser(ctx)
		param.FundRecord.SumAmount = param.FundPersonAmount + param.FundCompanyAmount
		param.FundRecord.TopCorporationId = topCorporationId
		records = append(records, param.FundRecord)
	}

	var fund hrModel.FundRecord
	err = fund.Create(records)
	if err != nil {
		log.ErrorFields("fund.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

//根据基数计算公积金缴纳
func calcFundAmount(setting map[string]hrModel.InsuranceFundSetting, record *hrModel.FundRecord) {
	//公积金
	if fund, ok := setting[util.ModularForFund]; ok {
		//公司
		if fund.CompanyPayType == util.TypeForPayRadio {
			record.FundCompanyAmount = int64((float64(fund.CompanyPayAmount) / 100 / 100) * float64(record.FundBase))
			record.FundCompanyRatio = fund.CompanyPayAmount
		}
		if fund.CompanyPayType == util.TypeForPayFixMoney {
			record.FundCompanyAmount = fund.CompanyPayAmount
			record.FundCompanyRatio = -1
		}
		//个人
		if fund.PersonalPayType == util.TypeForPayRadio {
			record.FundPersonAmount = int64((float64(fund.PersonalPayAmount) / 100 / 100) * float64(record.FundBase))
			record.FundPersonRatio = fund.PersonalPayAmount
		}
		if fund.PersonalPayType == util.TypeForPayFixMoney {
			record.FundPersonAmount = fund.PersonalPayAmount
			record.FundPersonRatio = -1
		}
	}
}

// Edit 公积金编辑
func (fun *Fund) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return EditFund(ctx, req, rsp)
}

// BatchEdit 批量编辑公积金
func (fun *Fund) BatchEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return EditFund(ctx, req, rsp)
}

type FundEditParam struct {
	Records []hrModel.FundRecord
}

func EditFund(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param FundEditParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	for i := range param.Records {
		if err := util.Validator().Struct(param.Records[i]); err != nil {
			log.ErrorFields("param validate fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsMissing)
		}
	}
	//topCorporationId := auth.User(ctx).GetTopCorporationId()
	//获取公积金设置
	//var setting hrModel.InsuranceFundSetting
	//settings := setting.Get(topCorporationId, util.SceneForFund)
	//if len(settings) == 0 {
	//	return response.Error(rsp, response.FundSettingNotFund)
	//}
	//settingMap := buildSettingMap(settings)

	var records []hrModel.FundRecord
	for i := range param.Records {
		//calcFundAmount(settingMap, &param.Records[i])
		param.Records[i].ParseOpUser(ctx)
		param.Records[i].SumAmount = param.Records[i].FundPersonAmount + param.Records[i].FundCompanyAmount
		records = append(records, param.Records[i])
	}

	var fund hrModel.FundRecord
	err = fund.BatchUpdate(records)
	if err != nil {
		log.ErrorFields("fund batchEdit error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

// Delete 删除公积金记录
func (fun *Fund) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Fund
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = param.FundRecord.Destroy()
	if err != nil {
		log.ErrorFields("FundRecord.Destroy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}
	return response.Success(rsp, nil)
}
