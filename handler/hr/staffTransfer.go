package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"strings"
	"time"
)

type StaffTransfer struct {
	hrModel.StaffTransfer
	hrModel.StaffTransferRecord
	Keyword           string  `json:"Keyword"`
	OutCorporationIds []int64 `json:"OutCorporationIds"`
	InCorporationIds  []int64 `json:"InCorporationIds"`
	//CorporationType int64   `json:"CorporationType"`
	ProcessId string `json:"ProcessId"`
	LbpmParam string `json:"LbpmParam"`
}

// List 人员调岗管理列表
func (stf *StaffTransfer) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffTransfer
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var startAt time.Time
	if param.StartAt != nil {
		startAt = time.Time(*param.StartAt)
	}

	param.OutCorporationIds = service.AuthCorporationIdProvider(ctx, param.OutCorporationIds)
	param.InCorporationIds = service.AuthCorporationIdProvider(ctx, param.InCorporationIds)

	records := param.StaffTransfer.GetBy(param.OutCorporationIds, param.InCorporationIds, param.Type, param.Keyword, startAt)
	var workPost hrModel.WorkPost
	for i := range records {

		var process processModel.LbpmApplyProcess
		_ = process.GetProcessByItemId(records[i].Id, records[i].TableName())
		records[i].CurrentHandlerUserName = process.CurrentHandlerUserName
		for j := range records[i].Records {
			inCorporation := rpc.GetCorporationById(ctx, records[i].Records[j].InCorporationId)
			if inCorporation != nil {
				records[i].Records[j].InCorporationName = inCorporation.Name
			}

			outCorporation := rpc.GetCorporationById(ctx, records[i].Records[j].OutCorporationId)
			if outCorporation != nil {
				records[i].Records[j].OutCorporationName = outCorporation.Name
			}

			inWorkPost, err := workPost.GetById(records[i].Records[j].InWorkPostId)
			if err == nil && inWorkPost.Id > 0 {
				records[i].Records[j].InWorkPostName = inWorkPost.Name
			}

			outWorkPost, err := workPost.GetById(records[i].Records[j].OutWorkPostId)
			if err == nil && outWorkPost.Id > 0 {
				records[i].Records[j].OutWorkPostName = outWorkPost.Name
			}
		}
	}

	return response.Success(rsp, map[string]interface{}{"Items": records, "TotalCount": len(records), "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

// Create 新增人员调岗记录
func (stf *StaffTransfer) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffTransfer
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param.StaffTransfer); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	if len(param.Records) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	for i := range param.Records {
		if err := util.Validator().Struct(param.Records[i]); err != nil {
			log.ErrorFields("records validate fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsMissing)
		}

		//借用、借调、挂职、调动、任免、任职:调入必填，任职类型和任职时间必填
		if util.IncludeInt64([]int64{hrModel.TransferTypeBorrow, hrModel.TransferTypeLoan, hrModel.TransferTypeSecondment, hrModel.TransferTypeTransfer, hrModel.TransferTypeAppointmentRemoval, hrModel.TransferTypeAppointment}, param.Records[i].Type) &&
			(param.Records[i].InWorkPostId == 0 || param.Records[i].InCorporationId == 0 || param.Records[i].PositionType == 0 || param.Records[i].StartAt == nil) {
			return response.Error(rsp, response.ParamsMissing)
		}

		//免职、借调结束、借用结束、挂职结束、调动、任免：调出必填
		if util.IncludeInt64([]int64{hrModel.TransferTypeRemoval, hrModel.TransferTypeLoanEnd, hrModel.TransferTypeBorrowEnd, hrModel.TransferTypeSecondmentEnd, hrModel.TransferTypeTransfer, hrModel.TransferTypeAppointmentRemoval}, param.Records[i].Type) &&
			(param.Records[i].OutWorkPostId == 0 || param.Records[i].OutCorporationId == 0) {
			return response.Error(rsp, response.ParamsMissing)
		}

	}

	var beforeData = make(map[int64][]byte)

	for i := range param.Records {
		var archive hrModel.StaffArchive
		err = archive.FindByStaffId(param.Records[i].StaffId)
		if err != nil {
			log.ErrorFields("archive.FindByStaffId error", map[string]interface{}{"err": err, "staffId": param.Records[i].StaffId})
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		param.Records[i].StaffArchiveId = archive.Id
		beforeData[archive.StaffId], _ = json.Marshal(archive.WorkPosts)
	}

	//当前操作人
	param.StaffTransfer.ParseOpUser(ctx)
	param.StaffTransfer.OpIp = auth.User(ctx).GetClientIp()

	tx := model.DB().Begin()
	if config.Config.Lbpm.Enable {
		param.StaffTransfer.ApplyStatus = util.ApplyStatusForDoing
	}

	err = param.StaffTransfer.TransactionCreate(tx)
	if err != nil {
		log.ErrorFields("StaffTransfer create fail", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbSaveFail)
	}

	//没有流程时直接更新数据  记录日志
	if !config.Config.Lbpm.Enable {
		for i := range param.Records {
			err = service.StaffTransferAfterUpdateStaffArchive(tx, param.Records[i])
			if err != nil {
				tx.Rollback()
				log.ErrorFields("StaffTransferAfterUpdateStaffArchive fail", map[string]interface{}{"err": err})
				return response.Error(rsp, response.FAIL)
			}
		}
		tx.Commit()

		for i := range param.Records {
			var staffWork hrModel.StaffHasWorkPost
			posts := staffWork.GetByArchiveId(param.Records[i].StaffArchiveId)
			afterData, _ := json.Marshal(posts)
			go service.CreateStaffArchiveLogger(param.Records[i].StaffArchiveId, param.OpUserId, param.OpUserName, hrModel.ArchiveLoggerSceneUpdate, auth.User(ctx).GetClientIp(),
				hrModel.ArchiveLoggerModularForTransfer, beforeData[param.Records[i].StaffId], afterData, "")
		}

		return response.Success(rsp, nil)
	}

	//有流程时 发起流程审批
	if config.Config.Lbpm.Enable {
		byteParam, _ := json.Marshal(param.StaffTransfer)
		var noticeUserPhones []string
		var noticeUserIds []int64
		err = json.Unmarshal(param.NoticeUserIds, &noticeUserIds)
		if err == nil {
			for i := range noticeUserIds {
				user := rpc.GetUserInfoById(context.Background(), noticeUserIds[i])
				if user != nil {
					noticeUserPhones = append(noticeUserPhones, user.Phone)
				}
			}
		}
		formData := map[string]interface{}{"WorkPostType": param.StaffTransfer.WorkPostType, "NoticeStaffPhones": strings.Join(noticeUserPhones, ",")}

		//创建的流程标题：【调动主题】的调动审批流
		var processId string
		processTitle := fmt.Sprintf("【%s】的调动审批流程", param.StaffTransfer.Topic)
		processId, err = processService.NewDispatchProcess(auth.User(ctx).GetUser(), config.StaffTransferApplyFormTemplate, processTitle, param.StaffTransfer.Id, param.StaffTransfer.TableName(), param.StaffTransfer.ApplyStatusFieldName(), string(byteParam), formData)

		if err != nil {
			tx.Rollback()
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}

		for i := range param.Records {
			var staffWork hrModel.StaffHasWorkPost
			posts := staffWork.GetByArchiveId(param.Records[i].StaffArchiveId)
			afterData, _ := json.Marshal(posts)
			go service.CreateStaffArchiveLogger(param.Records[i].StaffArchiveId, param.OpUserId, param.OpUserName, hrModel.ArchiveLoggerSceneUpdate, auth.User(ctx).GetClientIp(),
				hrModel.ArchiveLoggerModularForTransfer, beforeData[param.Records[i].StaffId], afterData, processId)
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

type StaffTransferApi struct {
	hrModel.StaffTransfer
	FileHttpPrefix string `json:"FileHttpPrefix"`
}

func (stf *StaffTransfer) Show(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param hrModel.StaffTransfer
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var staffTransfer hrModel.StaffTransfer
	err = staffTransfer.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("staffTransfer.FindBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	staffTransfer.IsProcessHandler = processService.CheckIsProcessRelater(staffTransfer.Id, config.StaffTransferApplyFormTemplate, auth.User(ctx).GetUserId())

	var workPost hrModel.WorkPost
	for i := range staffTransfer.Records {
		inCorporation := rpc.GetCorporationById(ctx, staffTransfer.Records[i].InCorporationId)
		if inCorporation != nil {
			staffTransfer.Records[i].InCorporationName = inCorporation.Name
		}

		outCorporation := rpc.GetCorporationById(ctx, staffTransfer.Records[i].OutCorporationId)
		if outCorporation != nil {
			staffTransfer.Records[i].OutCorporationName = outCorporation.Name
		}

		inWorkPost, err := workPost.GetById(staffTransfer.Records[i].InWorkPostId)
		if err == nil && inWorkPost.Id > 0 {
			staffTransfer.Records[i].InWorkPostName = inWorkPost.Name
		}

		outWorkPost, err := workPost.GetById(staffTransfer.Records[i].OutWorkPostId)
		if err == nil && outWorkPost.Id > 0 {
			staffTransfer.Records[i].OutWorkPostName = outWorkPost.Name
		}
	}

	return response.Success(rsp, StaffTransferApi{StaffTransfer: staffTransfer, FileHttpPrefix: config.Config.StaticFileHttpPrefix})
}

func (stf *StaffTransfer) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param hrModel.StaffTransfer
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var staffTransfer hrModel.StaffTransfer
	err = staffTransfer.Delete(param.Id)
	if err != nil {
		log.ErrorFields("staffTransfer.Delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, nil)
}
