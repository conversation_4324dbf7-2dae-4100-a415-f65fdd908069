package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
)

type DriverMigration struct {
	hrModel.DriverMigration
	StartAt    model.LocalTime `json:"StartAt"`
	EndAt      model.LocalTime `json:"EndAt"`
	StartUseAt model.LocalTime `json:"StartUseAt"`
	EndUseAt   model.LocalTime `json:"EndUseAt"`
	ProcessId  string          `json:"ProcessId"`
	IsRestart  bool            `json:"IsRestart"`
	DriverName string          `json:"DriverName"`
	model.Paginator
}

func (h *DriverMigration) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverMigration
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	provider := service.AuthCorporationIdProvider(ctx, nil)

	records, totalCount := param.GetBy(provider, param.DriverName, param.Code, param.ProcessHandler,
		param.OutCorporationId, param.InCorporationId, param.ApplyStatus, param.StartUseAt.ToTime(), param.EndUseAt.ToTime(),
		param.StartAt.ToTime(), param.EndAt.ToTime(), param.Paginator)

	for i := range records {
		inCorporation := rpc.GetCorporationById(ctx, records[i].InCorporationId)
		if inCorporation != nil {
			records[i].InCorporationName = inCorporation.Name
		}

		outCorporation := rpc.GetCorporationById(ctx, records[i].OutCorporationId)
		if outCorporation != nil {
			records[i].OutCorporationName = outCorporation.Name
		}

		var process processModel.LbpmApplyProcess
		err := process.GetProcessByItemId(records[i].Id, records[i].TableName())
		if err == nil {
			records[i].ProcessHandler = process.CurrentHandlerUserName
		}

	}

	data := map[string]interface{}{
		"Items":      records,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)

}

func (h *DriverMigration) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	if !config.Config.Lbpm.Enable {
		return response.Error(rsp, response.Forbidden)
	}

	var param DriverMigration
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param.DriverMigration); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}
	if len(param.Records) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	//调入和调出机构必须是车队
	outCorporation := rpc.GetCorporationDetailById(ctx, param.OutCorporationId)
	if outCorporation == nil || outCorporation.Item.Type != util.CorporationTypeForFleet {
		return response.Error(rsp, response.CorporationParamNotFleet)
	}
	inCorporation := rpc.GetCorporationDetailById(ctx, param.InCorporationId)
	if inCorporation == nil || inCorporation.Item.Type != util.CorporationTypeForFleet {
		return response.Error(rsp, response.CorporationParamNotFleet)
	}
	fleetUser := rpc.GetUserInfoById(ctx, param.InFleetUserId)
	if fleetUser == nil {
		return response.Error(rsp, response.ParamsInvalid)
	}
	hrUser := rpc.GetUserInfoById(ctx, param.InHrUserId)
	if hrUser == nil {
		return response.Error(rsp, response.ParamsInvalid)
	}
	//if param.InManageUserId > 0 {
	//	manageUser := rpc.GetUserInfoById(ctx, param.InManageUserId)
	//	if manageUser == nil {
	//		return response.Error(rsp, response.ParamsInvalid)
	//	}
	//}

	for i := range param.Records {
		if err := util.Validator().Struct(param.Records[i]); err != nil {
			log.ErrorFields("records validate fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsMissing)
		}
		driver := rpc.GetStaffWithId(ctx, param.Records[i].DriverId)
		if driver == nil {
			return response.Error(rsp, response.ParamsInvalid)
		}
		//查询司机是否有正在调动的审批
		if (&hrModel.DriverMigration{}).IsExistDoingMigration(driver.Id) {
			return response.Error(rsp, driver.Name+"存在正在调动的审批，无法再次调动")
		}

		param.Records[i].OutLineId = driver.LineId
		if driver.LineId > 0 {
			line, _ := rpc.GetLineWithId(ctx, driver.LineId)
			param.Records[i].OutLine = line.Name
		}
	}
	authUser := auth.User(ctx).GetUser()
	param.TopCorporationId = authUser.TopCorporationId
	param.IsFleetApply = util.StatusForTrue
	param.OpUserId = authUser.Id
	param.OpUserName = authUser.Name
	//判断发起人时否是车队
	userCorporation := rpc.GetCorporationById(ctx, authUser.CorporationId)
	if userCorporation.Type != util.CorporationTypeForFleet {
		param.IsFleetApply = util.StatusForFalse
	}

	tx := model.DB().Begin()

	param.ApplyStatus = util.ApplyStatusForDoing

	if param.Id > 0 {
		err = param.TransactionUpdate(tx)
	} else {
		err = param.TransactionCreate(tx)
	}
	if err != nil {
		log.ErrorFields("DriverMigration TransactionCreate || Update fail", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbSaveFail)
	}

	//有流程时 发起流程审批
	if config.Config.Lbpm.Enable {
		//获取调出和调入机构
		if outCorporation.GroupId > 0 {
			param.OutCorporationInfo = append(param.OutCorporationInfo, outCorporation.GroupName)
		}
		if outCorporation.CompanyId > 0 {
			param.OutCorporationInfo = append(param.OutCorporationInfo, outCorporation.CompanyName)
		}
		if outCorporation.BranchId > 0 {
			param.OutCorporationInfo = append(param.OutCorporationInfo, outCorporation.BranchName)
		}
		if outCorporation.DepartmentId > 0 {
			param.OutCorporationInfo = append(param.OutCorporationInfo, outCorporation.DepartmentName)
		}
		if outCorporation.FleetId > 0 {
			param.OutCorporationInfo = append(param.OutCorporationInfo, outCorporation.FleetName)
		}

		if inCorporation.GroupId > 0 {
			param.InCorporationInfo = append(param.InCorporationInfo, inCorporation.GroupName)
		}
		if inCorporation.CompanyId > 0 {
			param.InCorporationInfo = append(param.InCorporationInfo, inCorporation.CompanyName)
		}
		if inCorporation.BranchId > 0 {
			param.InCorporationInfo = append(param.InCorporationInfo, inCorporation.BranchName)
		}
		if inCorporation.DepartmentId > 0 {
			param.InCorporationInfo = append(param.InCorporationInfo, inCorporation.DepartmentName)
		}
		if inCorporation.FleetId > 0 {
			param.InCorporationInfo = append(param.InCorporationInfo, inCorporation.FleetName)
		}
		byteParam, _ := json.Marshal(param.DriverMigration)

		formData := map[string]interface{}{
			"FleetUserMobile":  fleetUser.Phone,
			"HrUserMobile":     hrUser.Phone,
			"ManageUserMobile": "",
			"IsFleetApply":     param.IsFleetApply,
		}

		var notifyUserMobiles []string
		//知会人
		if len(param.NotifyUsers) > 0 {
			var notifyUsers []auth.UserInfo
			_ = json.Unmarshal(param.NotifyUsers, &notifyUsers)
			for _, u := range notifyUsers {
				if u.UserId > 0 {
					user := rpc.GetUserInfoById(ctx, int64(u.UserId))
					if user != nil {
						notifyUserMobiles = append(notifyUserMobiles, user.Phone)
					}
				}
			}
		}
		for i := range notifyUserMobiles {
			if i > 3 {
				break
			}
			formData[fmt.Sprintf("NotifyUserMobile%v", i+1)] = notifyUserMobiles[i]
		}

		user := auth.User(ctx).GetUser()
		if param.IsRestart && param.ProcessId != "" {
			//重新发起流程
			err = processService.RestartProcess(user, param.ProcessId, string(byteParam), formData)
		} else {
			//发起新的流程
			processTitle := fmt.Sprintf("%s/%s", param.DriverMigration.Code, param.OpUserName)
			_, err = processService.NewDispatchProcess(user, config.DriverMigrationApplyFormTemplate, processTitle, param.Id, param.TableName(), param.ApplyStatusFieldName(), string(byteParam), formData)
		}

		if err != nil {
			tx.Rollback()
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	tx.Commit()
	go service.SendDriverMigrationProcessStatusChangeMsg(param.DriverMigration.Id)
	return response.Success(rsp, nil)
}

func (h *DriverMigration) Show(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverMigration
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	driverMigration := (&hrModel.DriverMigration{}).FirstBy(param.Id)
	if driverMigration.Id == 0 {
		log.ErrorFields("DriverMigration.FirstBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	inCorporation := rpc.GetCorporationById(ctx, driverMigration.InCorporationId)
	if inCorporation != nil {
		driverMigration.InCorporationName = inCorporation.Name
	}

	outCorporation := rpc.GetCorporationById(ctx, driverMigration.OutCorporationId)
	if outCorporation != nil {
		driverMigration.OutCorporationName = outCorporation.Name
	}

	for i := range driverMigration.Records {
		staff := rpc.GetStaffWithId(ctx, driverMigration.Records[i].DriverId)
		if staff != nil {
			driverMigration.Records[i].IdentifyId = staff.IdentifyId
			driverMigration.Records[i].Phone = staff.Phone
		}
	}

	return response.Success(rsp, driverMigration)
}

func (h *DriverMigration) UpdateInfo(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverMigration
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	driverMigration := (&hrModel.DriverMigration{}).FirstBy(param.Id)
	if driverMigration.Id == 0 {
		log.ErrorFields("DriverMigration.FirstBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	//获取车队的机动线路
	var motorLineId int64
	var motorLineName string
	setting := (&settingModel.GlobalSetting{}).GetBy(auth.User(ctx).GetTopCorporationId(), 2)
	if setting.Id != 0 {
		var motorLines []settingModel.GlobalSettingItemForMotorLine
		_ = json.Unmarshal(setting.SettingItem, &motorLines)
		for _, motorLine := range motorLines {
			if motorLine.CorporationId == driverMigration.InCorporationId {
				motorLineId = motorLine.LineId
				line, _ := rpc.GetLineWithId(ctx, motorLineId)
				if line != nil {
					motorLineName = line.Name
				}
			}
		}
	}
	//接收人关联车辆和线路
	if driverMigration.FormStep == util.ProcessFormStepTwo {
		for _, record := range param.Records {
			record.IsMotor = util.StatusForFalse
			if record.InLineId == 0 {
				record.InLineId = motorLineId
				record.InLine = motorLineName
				record.IsMotor = util.StatusForTrue
			}
			err = record.UpdateInLine()
			if err != nil {
				log.ErrorFields("UpdateInLine error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.FAIL)
			}
		}
		_ = driverMigration.UpdateFormStep(driverMigration.Id, driverMigration.FormStep+1)
	}

	return response.Success(rsp, nil)
}
