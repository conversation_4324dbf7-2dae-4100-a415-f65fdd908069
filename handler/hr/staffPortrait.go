package hr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/model/safety"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
)

type StaffPortraitRequest struct {
	StaffId   int64           `json:"StaffId"`
	StartAt   model.LocalTime `json:"StartAt"`
	EndAt     model.LocalTime `json:"EndAt"`
	SceneType int64           `json:"SceneType"`
}

type violationStandardItem struct {
	Id   int64  `json:"Id"`
	Code string `json:"Code"`
}

func (s *StaffReport) StaffPortrait(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffPortraitRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	staff := rpc.GetStaffWithId(ctx, param.StaffId)
	if staff == nil {
		log.ErrorFields("GetStaffWithId error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var result = make(map[string]interface{})
	result["StaffId"] = staff.Id
	result["StaffName"] = staff.Name
	result["DrvLicenseTypeStr"] = staff.DrvLicenseTypeStr
	corporation := rpc.GetCorporationById(ctx, staff.CorporationId)
	if corporation != nil {
		result["CorporationName"] = corporation.Name
	}
	result["Age"], _ = util.GetAgeFromIDCard(staff.IdentifyId)
	result["Avatar"] = staff.ExterHeadPortrait

	var hasLiabilityCount int64
	//有责事故
	model.DB().Model(&safety.TrafficAccident{}).
		Where("DriverId = ? AND IsClosed = ? AND OpenStatus = ? AND HappenAt >= ? AND HappenAt <= ?", staff.Id, util.StatusForTrue, util.StatusForTrue, param.StartAt.ToTime().Format(model.TimeFormat), param.EndAt.ToTime().Format(model.TimeFormat)).
		Where("LiabilityType IN ?", []int64{util.AccidentLiabilityTypeForAll, util.AccidentLiabilityTypeForMain, util.AccidentLiabilityTypeForEqual, util.AccidentLiabilityTypeForSub}).Count(&hasLiabilityCount)
	result["HasLiabilityCount"] = hasLiabilityCount

	var hasNotLiabilityCount int64
	//无责事故
	model.DB().Model(&safety.TrafficAccident{}).
		Where("DriverId = ? AND IsClosed = ? AND OpenStatus = ? AND HappenAt >= ? AND HappenAt <= ?", staff.Id, util.StatusForTrue, util.StatusForTrue, param.StartAt.ToTime().Format(model.TimeFormat), param.EndAt.ToTime().Format(model.TimeFormat)).
		Where("LiabilityType IN ?", []int64{util.AccidentLiabilityTypeForNull}).Count(&hasNotLiabilityCount)
	result["HasNotLiabilityCount"] = hasNotLiabilityCount

	//查询所有违规记录
	var violationRecords []safety.TrafficViolation
	model.DB().Model(&safety.TrafficViolation{}).Where("DriverStaffId = ? AND ReportAt >= ? AND ReportAt <= ?", staff.Id, param.StartAt.ToTime().Format(model.TimeFormat), param.EndAt.ToTime().Format(model.TimeFormat)).Find(&violationRecords)

	var safeCount, notSafeCount, violationCount int64

	//查询所有安全类违规号
	var standardMap = make(map[string]int64)
	var standardCode = make(map[string]bool)
	var safeStandards []safety.QualityAssessmentStandards
	model.DB().Model(&safety.QualityAssessmentStandards{}).Where("AttrType = ?", util.QualityAssessmentStandardsAttrTypeForSafe).Find(&safeStandards)
	for _, standard := range safeStandards {
		key := fmt.Sprintf("%v_%v", standard.Id, standard.QualityAssessmentCateId)
		standardMap[key] = util.QualityAssessmentStandardsAttrTypeForSafe
	}

	var notSafeStandards []safety.QualityAssessmentStandards
	model.DB().Model(&safety.QualityAssessmentStandards{}).Where("AttrType = ?", util.QualityAssessmentStandardsAttrTypeForNotSafe).Find(&notSafeStandards)
	for _, standard := range notSafeStandards {
		key := fmt.Sprintf("%v_%v", standard.Id, standard.QualityAssessmentCateId)
		standardMap[key] = util.QualityAssessmentStandardsAttrTypeForNotSafe
	}

	var violationStandards []safety.QualityAssessmentStandards
	model.DB().Model(&safety.QualityAssessmentStandards{}).Where("AttrType = ?", util.QualityAssessmentStandardsAttrTypeForViolation).Find(&violationStandards)
	for _, standard := range violationStandards {
		key := fmt.Sprintf("%v_%v", standard.Id, standard.QualityAssessmentCateId)
		standardMap[key] = util.QualityAssessmentStandardsAttrTypeForViolation
	}

	for _, violation := range violationRecords {
		if len(violation.Standards) == 0 {
			continue
		}

		var standards []violationStandardItem
		_ = json.Unmarshal(violation.Standards, &standards)
		var isSafe, isNotSate, isViolation bool
		for _, standard := range standards {
			standardCode[standard.Code] = true
			st := (&safety.QualityAssessmentStandards{}).GetById(standard.Id)
			if st.Id > 0 {
				key := fmt.Sprintf("%v_%v", st.Id, st.QualityAssessmentCateId)
				if _, ok := standardMap[key]; ok {
					if standardMap[key] == util.QualityAssessmentStandardsAttrTypeForSafe {
						isSafe = true
					}
					if standardMap[key] == util.QualityAssessmentStandardsAttrTypeForNotSafe {
						isNotSate = true
					}
					if standardMap[key] == util.QualityAssessmentStandardsAttrTypeForViolation {
						isViolation = true
					}
				}
			}
		}
		if isSafe {
			safeCount++
		}

		if isNotSate {
			notSafeCount++
		}

		if isViolation {
			violationCount++
		}
	}

	var standardCodes []string
	for k := range standardCode {
		standardCodes = append(standardCodes, k)
	}

	result["SafeViolationCount"] = safeCount
	result["NotSafeViolationCount"] = notSafeCount
	result["ViolationCount"] = violationCount
	result["StandardCodes"] = standardCodes

	var workOrderCount int64
	model.DB().Model(&workOrderModel.PetitionWorkOrder{}).Where("Id IN (?)", model.DB().Model(&workOrderModel.PetitionWorkOrderHandleResult{}).Select("PetitionWorkOrderId").Where("RelatedStaffId = ?", staff.Id)).
		Where("ReportAt >= ? AND ReportAt <= ? AND ApplyStatus = ?", param.StartAt.ToTime().Format(model.TimeFormat), param.EndAt.ToTime().Format(model.TimeFormat), util.ApplyStatusForDone).Count(&workOrderCount)

	result["WorkOrderCount"] = workOrderCount
	return response.Success(rsp, result)
}

func (s *StaffReport) StaffPortraitAccidentList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffPortraitRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	staff := rpc.GetStaffWithId(ctx, param.StaffId)
	if staff == nil {
		log.ErrorFields("GetStaffWithId error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var accidents []safety.TrafficAccident
	//有责事故
	if param.SceneType == 1 {
		model.DB().Model(&safety.TrafficAccident{}).
			Where("DriverId = ? AND IsClosed = ? AND OpenStatus = ? AND HappenAt >= ? AND HappenAt <= ?", staff.Id, util.StatusForTrue, util.StatusForTrue, param.StartAt.ToTime().Format(model.TimeFormat), param.EndAt.ToTime().Format(model.TimeFormat)).
			Where("LiabilityType NOT IN ?", []int64{util.AccidentLiabilityTypeForAll, util.AccidentLiabilityTypeForMain, util.AccidentLiabilityTypeForEqual, util.AccidentLiabilityTypeForSub}).Find(&accidents)
	}

	//无责事故
	if param.SceneType == 2 {
		model.DB().Model(&safety.TrafficAccident{}).
			Where("DriverId = ? AND IsClosed = ? AND OpenStatus = ? AND HappenAt >= ? AND HappenAt <= ?", staff.Id, util.StatusForTrue, util.StatusForTrue, param.StartAt.ToTime().Format(model.TimeFormat), param.EndAt.ToTime().Format(model.TimeFormat)).
			Where("LiabilityType IN ?", []int64{util.AccidentLiabilityTypeForNull}).Find(&accidents)
	}

	var results []map[string]interface{}
	for _, accident := range accidents {
		results = append(results, map[string]interface{}{
			"Id":                accident.Id,
			"Code":              accident.Code,
			"License":           accident.License,
			"HappenAt":          accident.HappenAt,
			"VehicleBrokenCate": accident.VehicleBrokenCate,
			"PeopleHurtCate":    accident.PeopleHurtCate,
		})
	}

	return response.Success(rsp, results)
}

func (s *StaffReport) StaffPortraitViolationList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffPortraitRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	staff := rpc.GetStaffWithId(ctx, param.StaffId)
	if staff == nil {
		log.ErrorFields("GetStaffWithId error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	//查询所有违规记录
	var violationRecords []safety.TrafficViolation
	model.DB().Model(&safety.TrafficViolation{}).Where("DriverStaffId = ? AND ReportAt >= ? AND ReportAt <= ?", staff.Id, param.StartAt.ToTime().Format(model.TimeFormat), param.EndAt.ToTime().Format(model.TimeFormat)).Find(&violationRecords)

	//查询所有安全类违规号
	var standardMap = make(map[string]int64)
	if param.SceneType == 1 {
		var safeStandards []safety.QualityAssessmentStandards
		model.DB().Model(&safety.QualityAssessmentStandards{}).Where("AttrType = ?", util.QualityAssessmentStandardsAttrTypeForSafe).Find(&safeStandards)
		for _, standard := range safeStandards {
			key := fmt.Sprintf("%v_%v", standard.Id, standard.QualityAssessmentCateId)
			standardMap[key] = util.QualityAssessmentStandardsAttrTypeForSafe
		}
	}

	if param.SceneType == 2 {
		var notSafeStandards []safety.QualityAssessmentStandards
		model.DB().Model(&safety.QualityAssessmentStandards{}).Where("AttrType = ?", util.QualityAssessmentStandardsAttrTypeForNotSafe).Find(&notSafeStandards)
		for _, standard := range notSafeStandards {
			key := fmt.Sprintf("%v_%v", standard.Id, standard.QualityAssessmentCateId)
			standardMap[key] = util.QualityAssessmentStandardsAttrTypeForNotSafe
		}
	}

	if param.SceneType == 3 {
		var violationStandards []safety.QualityAssessmentStandards
		model.DB().Model(&safety.QualityAssessmentStandards{}).Where("AttrType = ?", util.QualityAssessmentStandardsAttrTypeForViolation).Find(&violationStandards)
		for _, standard := range violationStandards {
			key := fmt.Sprintf("%v_%v", standard.Id, standard.QualityAssessmentCateId)
			standardMap[key] = util.QualityAssessmentStandardsAttrTypeForViolation
		}
	}

	var results []map[string]interface{}

	for _, violation := range violationRecords {
		if len(violation.Standards) == 0 {
			continue
		}

		var standards []violationStandardItem
		_ = json.Unmarshal(violation.Standards, &standards)

		var is bool
		for _, standard := range standards {
			st := (&safety.QualityAssessmentStandards{}).GetById(standard.Id)
			if st.Id > 0 {
				key := fmt.Sprintf("%v_%v", st.Id, st.QualityAssessmentCateId)
				if _, ok := standardMap[key]; ok {
					is = true
				}
			}
		}

		if is {
			results = append(results, map[string]interface{}{
				"Id":          violation.Id,
				"ReportAt":    violation.ReportAt,
				"License":     violation.License,
				"Standards":   violation.Standards,
				"DeductScore": violation.DeductScore,
				"LineName":    violation.LineName,
			})
		}
	}

	return response.Success(rsp, results)
}

func (s *StaffReport) StaffPortraitWorkOrderList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffPortraitRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	staff := rpc.GetStaffWithId(ctx, param.StaffId)
	if staff == nil {
		log.ErrorFields("GetStaffWithId error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var workOrders []workOrderModel.PetitionWorkOrder
	model.DB().Model(&workOrderModel.PetitionWorkOrder{}).Where("Id IN (?)", model.DB().Model(&workOrderModel.PetitionWorkOrderHandleResult{}).Select("PetitionWorkOrderId").Where("RelatedStaffId = ?", staff.Id)).
		Where("ReportAt >= ? AND ReportAt <= ? AND ApplyStatus = ?", param.StartAt.ToTime().Format(model.TimeFormat), param.EndAt.ToTime().Format(model.TimeFormat), util.ApplyStatusForDone).Find(&workOrders)

	var results []map[string]interface{}
	for _, workOrder := range workOrders {
		results = append(results, map[string]interface{}{
			"Id":          workOrder.Id,
			"Code":        workOrder.Code,
			"LineName":    workOrder.LineName,
			"ReportAt":    workOrder.ReportAt,
			"CateDictKey": workOrder.CateDictKey,
			"FromDictKey": workOrder.FromDictKey,
		})
	}

	return response.Success(rsp, results)
}

func (s *StaffReport) StaffPortraitVetoCreate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param hr.StaffVetoRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.Status = util.StatusForTrue
	param.SceneType = 1

	staff := rpc.GetStaffWithId(ctx, param.StaffId)
	if staff == nil {
		log.ErrorFields("GetStaffWithId error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.StaffName = staff.Name
	param.TopCorporationId = staff.TopCorporationId
	param.ParseOpUser(ctx)
	err = param.Create()
	if err != nil {
		log.ErrorFields("StaffVetoRecord Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	return response.Success(rsp, nil)
}

func (s *StaffReport) StaffPortraitVetoChange(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param hr.StaffVetoRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	old := (&hr.StaffVetoRecord{}).FirstBy(param.Id)
	if old.Id == 0 {
		log.ErrorFields("GetStaffVetoRecord error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = old.UpdateStatus(util.StatusForFalse)
	if err != nil {
		log.ErrorFields("StaffVetoRecord UpdateStatus error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	//生成变更记录
	var record = &hr.StaffVetoRecord{
		TopCorporationId: old.TopCorporationId,
		StaffId:          old.StaffId,
		StaffName:        old.StaffName,
		ReportAt:         old.ReportAt,
		Reason:           param.Reason,
		RecordId:         old.Id,
		SceneType:        2,
	}
	record.ParseOpUser(ctx)
	err = record.Create()
	if err != nil {
		log.ErrorFields("StaffVetoRecord Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (s *StaffReport) StaffPortraitVetoList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param hr.StaffVetoRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	records := (&hr.StaffVetoRecord{}).GetByStaffId(param.StaffId)

	var recordList []hr.StaffVetoRecord
	var changeList []hr.StaffVetoRecord
	for _, record := range records {
		if record.SceneType == 1 {
			recordList = append(recordList, record)
		}
		if record.SceneType == 2 {
			changeList = append(changeList, record)
		}
	}
	return response.Success(rsp, map[string]interface{}{
		"Items":       recordList,
		"ChangeItems": changeList,
	})
}
