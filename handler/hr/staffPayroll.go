package hr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"sync"
)

type StaffPayrollHandler struct {
	CorporationIds []int64 `json:"CorporationIds"` // 机构id
	Month          string  `json:"Month"`          //月份
	StaffId        int64   `json:"StaffId"`        // 驾驶员
	JobNumber      string  `json:"JobNumber"`      // 工号
	LineId         int64   `json:"LineId"`         // 线路id
	FileData       string  `json:"FileData"`       // 导入时的base64文件
	model.Paginator
}

func (sa *StaffPayrollHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form StaffPayrollHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	corporationIds := service.AuthCorporationIdProvider(ctx, form.CorporationIds)
	data, totalCount, err := (&hrModel.StaffPayrollReport{}).List(corporationIds, form.Month, form.StaffId, form.JobNumber, form.LineId, form.Paginator)
	if err != nil {
		log.ErrorFields("SafeProductionReport List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if data != nil {
		for index := range data {
			data[index].CorporationId, data[index].CorporationName = data[index].GetCorporation()
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": data, "TotalCount": totalCount})
}

func (sa *StaffPayrollHandler) Update(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form hrModel.StaffPayrollReport
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	if !form.IsModifiable() {
		return response.Error(rsp, "OP9010")
	}
	form.CalculateActualPayment()
	err := form.UpdateEditInfo()
	if err != nil {
		log.ErrorFields("StaffPayrollReport UpdateEditInfo error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

type ExcelForm struct {
	RowIndex int    `json:"RowIndex"`
	Error    string `json:"Error"`
}

func (sa *StaffPayrollHandler) Import(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form StaffPayrollHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.FileData == "" {
		return response.Error(rsp, response.ParamsInvalid)
	}
	decodeString, err := base64.StdEncoding.DecodeString(form.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.ParamsInvalid)
	}
	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("excelize.OpenBinary[err]:", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if len(excelFile.Sheets) == 0 {
		log.Error("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}
	rows := excelFile.Sheets[0].Rows
	var errRows []ExcelForm
	if rows != nil {
		wg := sync.WaitGroup{}
		var lock sync.Mutex
		for index, r := range rows {
			if index == 0 {
				continue
			}
			wg.Add(1)
			go func(row *xlsx.Row, i int) {
				defer wg.Done()
				jobNumber := row.Cells[2].String()
				month := row.Cells[5].String()
				data, err := (&hrModel.StaffPayrollReport{}).GetByJobNumberAndMonth(jobNumber, month)
				if err != nil {
					log.ErrorFields("StaffPayrollReport GetByJobNumberAndMonth error", map[string]interface{}{"error": err.Error()})
					lock.Lock()
					errRows = append(errRows, ExcelForm{RowIndex: i, Error: err.Error()})
					lock.Unlock()
					return
				}
				if data.Id == 0 {
					lock.Lock()
					errRows = append(errRows, ExcelForm{RowIndex: i, Error: "未找到该员工数据"})
					lock.Unlock()
					return
				}
				LastMonthTax, _ := row.Cells[6].Float()
				data.LastMonthTax = int64(LastMonthTax * 100)
				IndividualIncomeTax, _ := row.Cells[7].Float()
				data.IndividualIncomeTax = int64(IndividualIncomeTax * 100)

				DormitoryElectricityFee, _ := row.Cells[8].Float()
				data.DormitoryElectricityFee = int64(DormitoryElectricityFee * 100)

				PensionInsuranceWithholding, _ := row.Cells[9].Float()
				data.PensionInsuranceWithholding = int64(PensionInsuranceWithholding * 100)

				UnemploymentInsuranceWithholding, _ := row.Cells[10].Float()
				data.UnemploymentInsuranceWithholding = int64(UnemploymentInsuranceWithholding * 100)
				data.CalculateActualPayment()
				err = data.UpdateImportInfo()
				if err != nil {
					log.ErrorFields("StaffPayrollReport UpdateImportInfo error", map[string]interface{}{"error": err.Error()})
					lock.Lock()
					errRows = append(errRows, ExcelForm{RowIndex: i, Error: "更新失败"})
					lock.Unlock()
				}
			}(r, index+1)
		}
		wg.Wait()
	}
	return response.Success(rsp, errRows)
}

func (sa *StaffPayrollHandler) Calculate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	user := auth.User(ctx).GetUser()
	staffs := rpc.GetDriversWithCorporationId(context.TODO(), user.TopCorporationId, "")
	(&setting.StaffPayrollConfig{}).GetAllStaffPayrollConfigs("稳定排班")
	if len(staffs) != 0 {
		for _, staff := range staffs {
			if staff.CorporationId == 1519712307444188180 {
				service.CalcDrivePayroll(staff)
			}
		}
	}
	(&setting.StaffPayrollConfig{}).ClearStaffPayrollConfigCache()
	return response.Success(rsp, nil)
}
