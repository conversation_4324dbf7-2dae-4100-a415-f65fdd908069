package hr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	protoStaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
)

// StaffAssessment 人员考核等级评定
type StaffAssessment struct {
	hrModel.StaffAssessment
	StaffIds       []int64 `json:"StaffIds"`
	StartYear      int64   `json:"StartYear"`
	EndYear        int64   `json:"EndYear"`
	Degrees        []int64 `json:"degrees"`
	CorporationIds []int64 `json:"CorporationIds"`
	Keyword        string  `json:"Keyword"`
	model.Paginator
}

// Import 导入考核记录
func (sa *StaffAssessment) Import(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ImportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			response.Error(rsp, response.FAIL)
		}
		return
	}()

	//获取机构的层级关系
	//corporation := rpc.GetCorporationDetailById(ctx, util.GetUserCorporationId(ctx))
	//if corporation == nil {
	//	log.ErrorFields("rpc GetCorporationDetailById is nil", map[string]interface{}{"corporationId": util.GetUserCorporationId(ctx)})
	//	return response.Error(rsp, response.ParamsInvalid)
	//}

	// 通过根机构获取所有员工数据
	oetStaffs := rpc.GetStaffsWithOption(ctx, auth.User(ctx).GetTopCorporationId())
	oetStaffMap := make(map[string]*protoStaff.OetStaffItem)
	for i := range oetStaffs {
		if oetStaffs[i].Phone != "" {
			oetStaffMap[oetStaffs[i].Phone] = oetStaffs[i]
		}
	}

	// 0姓名* 1联系电话 2考核等级 3考核年度
	sheet := excelFile.Sheets[0]
	var importFailStaffs []string
	var successCount int64
	var assessments []hrModel.StaffAssessment
	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 4 {
			continue
		}

		if row.Cells[0].String() == "" || row.Cells[1].String() == "" || row.Cells[2].String() == "" || row.Cells[3].String() == "" {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}

		phone := row.Cells[1].String()
		oetStaff, ok := oetStaffMap[phone]
		if !ok {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}
		var archive hrModel.StaffArchive
		err := archive.FindByStaffId(oetStaff.Id)

		if err != nil {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}
		var assessment hrModel.StaffAssessment
		assessment.StaffArchiveId = archive.Id
		assessment.StaffId = oetStaff.Id
		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 2:
				assessment.Degree, _ = util.AssessmentDegree[row.Cells[2].String()]
			case 3:
				assessment.Year, _ = row.Cells[3].Int64()
			}
		}
		successCount++
		assessments = append(assessments, assessment)
	}

	var assessment hrModel.StaffAssessment
	err = assessment.Create(assessments)
	if err != nil {
		log.ErrorFields("assessment.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, map[string]interface{}{
		"SuccessCount": successCount,
		"FailCount":    len(importFailStaffs),
		"FailStaffs":   importFailStaffs,
	})
}

// Export 导出人员考核记录
func (sa *StaffAssessment) Export(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffAssessment
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.CorporationIds = service.AuthCorporationIdProvider(ctx, param.CorporationIds)
	result := StaffAssessmentList(ctx, param)

	return response.Success(rsp, result)
}

// List 人员考核列表
func (sa *StaffAssessment) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffAssessment
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	result := StaffAssessmentList(ctx, param)

	return response.Success(rsp, result)
}

func StaffAssessmentList(ctx context.Context, param StaffAssessment) map[string]interface{} {

	param.CorporationIds = service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	var oetWhere service.OetWhere

	if param.Keyword != "" {
		oetWhere.Keyword = param.Keyword
	}

	oetStaffMap, staffIds, _ := service.SelectOetStaffByMultiWhere(ctx, param.CorporationIds, oetWhere, false, 0, 0)

	records, count := param.StaffAssessment.GetBy(staffIds, param.Degrees, param.StartYear, param.EndYear, param.Paginator)

	for i := range records {
		oetStaff := oetStaffMap[records[i].StaffId]
		records[i].JobNumber = oetStaff.StaffId
		records[i].StaffName = oetStaff.Name
		corporation := rpc.GetCorporationById(context.Background(), oetStaff.CorporationId)

		if corporation != nil {
			records[i].CorporationName = corporation.Name
		}
	}

	return map[string]interface{}{"Items": records, "TotalCount": count}
}

// Create 新增人员考核记录
func (sa *StaffAssessment) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffAssessment
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("param validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	if len(param.StaffIds) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	param.StaffAssessment.ParseOpUser(ctx)

	var records []hrModel.StaffAssessment
	for i := range param.StaffIds {
		var archive StaffArchive
		_ = archive.FindByStaffId(param.StaffIds[i])
		param.StaffAssessment.StaffArchiveId = archive.Id
		param.StaffAssessment.StaffId = param.StaffIds[i]
		records = append(records, param.StaffAssessment)
	}

	var assessment hrModel.StaffAssessment
	err = assessment.Create(records)
	if err != nil {
		log.ErrorFields("assessment.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)

}
