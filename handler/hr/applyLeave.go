package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	go_api "github.com/micro/go-micro/v2/api/proto"
	"sort"
	"time"
)

type StaffLeave struct {
	hrModel.ApplyLeaveRecord
	CorporationId  int64   `json:"CorporationId"`
	CorporationIds []int64 `json:"CorporationIds"`
	WorkPostType   int64   `json:"WorkPostType"`
	Year           int64   `json:"Year"`
	ScrapType      int64   `json:"ScrapType"` // 报废查询标志 1为报废
	model.Paginator
}

func (sl *StaffLeave) ApplyList(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StaffLeave
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	records, count, CateCounts := (&hrModel.ApplyLeaveRecord{}).GetBy(corporationIds, param.Code, param.StaffName, param.JobNumber, param.LeaveType, time.Time(param.StartAt), time.Time(param.EndAt), param.ScrapType, param.Paginator)

	for i := range records {
		var staffInfo = rpc.GetStaffWithId(ctx, records[i].StaffId)
		if staffInfo == nil {
			continue
		}
		records[i].Dates = (&hrModel.ApplyLeaveRecordDate{}).GetByLeaveRecordId(records[i].Id)
		sort.SliceStable(records[i].Dates, func(m, n int) bool {
			return records[i].Dates[m].Date.ToTime().Unix() < records[i].Dates[n].Date.ToTime().Unix()
		})

		records[i].CorporationId, records[i].CorporationName = records[i].GetCorporation()
		if len(records[i].Dates) > 0 {
			records[i].StartAt = records[i].Dates[0].Date
			records[i].EndAt = records[i].Dates[len(records[i].Dates)-1].Date
		}

		var process processModel.LbpmApplyProcess
		err = process.GetProcessByItemId(records[i].Id, records[i].TableName())
		records[i].CurrentHandler = process.CurrentHandlerUserName
	}

	return response.Success(rsp, map[string]interface{}{"Items": records, "TotalCount": count, "FileHttpPrefix": config.Config.StaticFileHttpPrefix, "CateCounts": CateCounts})

}

func (sl *StaffLeave) Create(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StaffLeave
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if (param.LeaveDateType == util.LeaveDateTypeLinkDay && time.Time(param.StartAt).Unix() > time.Time(param.EndAt).Unix()) || (param.LeaveDateType == util.LeaveDateTypeMultiDay && len(param.LeaveDates) == 0) {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StaffId == 0 ||
		!(param.LeaveType >= util.LeaveTypeForAnnualLeave && param.LeaveType <= util.LeaveTypeForStopWork) {
		return response.Error(rsp, response.ParamsInvalid)
	}
	// 当请假类型为【病假】时，请假凭证必传，其余情况非必传
	if param.LeaveType == util.LeaveTypeForSickLeave && param.LeaveProof == nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	staffInfo := rpc.GetStaffWithId(ctx, param.StaffId)
	if staffInfo == nil {
		log.ErrorFields("rpc.GetStaffWithId error", map[string]interface{}{"id": param.StaffId})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.JobNumber = staffInfo.StaffId
	param.StaffName = staffInfo.Name

	//如果请假类型为年休假  则需要判断年休假天数是否够用
	if param.LeaveType == util.LeaveTypeForAnnualLeave {
		leaveManagement := (&hrModel.LeaveManagement{}).GetStaffLeaveByStatus(param.StaffId, util.StatusForTrue)
		var dayCount int64
		if param.LeaveDateType == util.LeaveDateTypeLinkDay {
			dayCount = int64(time.Time(param.EndAt).Sub(time.Time(param.StartAt)).Hours()/24) + 1
		} else {
			dayCount = int64(len(param.LeaveDates))
		}

		if leaveManagement.TotalDay-leaveManagement.UsedDay-dayCount < 0 {
			return response.Error(rsp, response.LeaveDayCountOverEnableDay)
		}
	}

	if param.CorporationId == 0 {
		param.CorporationId = staffInfo.CorporationId
	}

	user := auth.User(ctx).GetUser()
	_, err = StaffLeaveCreate(user, param.ApplyLeaveRecord, param.CorporationId, "erp")

	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func StaffLeaveCreate(user *auth.AuthUser, record hrModel.ApplyLeaveRecord, corporationId int64, from string) (int64, error) {
	record.Corporations.Build(corporationId)
	record.ApplyStatus = util.ApplyStatusForDoing
	record.OpUserId = user.Id
	record.OpUserName = user.Name
	record.From = from
	if record.LeaveDateType == 0 {
		record.LeaveDateType = util.LeaveDateTypeLinkDay
	}

	if record.LeaveDateType == util.LeaveDateTypeMultiDay {
		record.DayCount = int64(len(record.LeaveDates)) * 10
	} else {
		record.DayCount = (int64(time.Time(record.EndAt).Sub(time.Time(record.StartAt)).Hours()/24) + 1) * 10
	}

	var staffArchive hrModel.StaffArchive
	_ = staffArchive.FindByStaffId(record.StaffId)
	record.StaffArchiveId = staffArchive.Id

	tx := model.DB().Begin()
	err := record.TransactionCreate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("ApplyLeaveRecord.Create error", map[string]interface{}{"err": err})
		return 0, err
	}

	//添加请假日期
	var leaveRecordDates []hrModel.ApplyLeaveRecordDate
	if record.LeaveDateType == util.LeaveDateTypeLinkDay {
		record.LeaveDates = util.GetDateFromRangeTime(time.Time(record.StartAt), time.Time(record.EndAt))
	}

	for i := range record.LeaveDates {
		leaveDate, err := time.ParseInLocation(model.DateFormat, record.LeaveDates[i], time.Local)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("LeaveDates time.ParseInLocation error", map[string]interface{}{"err": err})
			return 0, err
		}

		leaveRecordDates = append(leaveRecordDates, hrModel.ApplyLeaveRecordDate{
			ApplyLeaveRecordId: record.Id,
			Date:               model.LocalTime(leaveDate),
		})
	}

	err = (&hrModel.ApplyLeaveRecordDate{}).TransactionCreate(tx, leaveRecordDates)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("ApplyLeaveRecordDate.TransactionCreate error", map[string]interface{}{"err": err})
		return 0, err
	}

	//发起流程审批
	if config.Config.Lbpm.Enable {
		byteParam, _ := json.Marshal(record)
		formData := map[string]interface{}{
			"ApplyLeaveDay": record.DayCount / 10,
		}
		processTitle := fmt.Sprintf("%s的请假审批", record.StaffName)
		_, err = processService.NewDispatchProcess(user, config.StaffLeaveFormTemplate, processTitle, record.Id, record.TableName(), record.ApplyStatusFieldName(), string(byteParam), formData)

		if err != nil {
			tx.Rollback()
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			return 0, err
		}
	}

	tx.Commit()

	return record.Id, nil
}

func (sl *StaffLeave) Show(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StaffLeave
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	applyRecord := (&hrModel.ApplyLeaveRecord{}).FirstBy(param.Id)

	var staffInfo = rpc.GetStaffWithId(ctx, applyRecord.StaffId)
	if staffInfo == nil {
		return response.Error(rsp, response.DbQueryFail)
	}

	applyRecord.CorporationId, applyRecord.CorporationName = applyRecord.GetCorporation()

	applyRecord.Dates = (&hrModel.ApplyLeaveRecordDate{}).GetByLeaveRecordId(applyRecord.Id)

	var process processModel.LbpmApplyProcess
	err = process.GetProcessByItemId(applyRecord.Id, applyRecord.TableName())
	applyRecord.CurrentHandler = process.CurrentHandlerUserName

	return response.Success(rsp, map[string]interface{}{"Item": applyRecord, "FileHttpPrefix": config.Config.StaticFileHttpPrefix})

}

type StaffLeaveLeaveList struct {
	CorporationId  int64   `json:"CorporationId"`
	WorkPostType   int64   `json:"WorkPostType"`
	StaffName      string  `json:"StaffName"`
	JobNumber      string  `json:"JobNumber"`
	Year           int64   `json:"Year"`
	CorporationIds []int64 `json:"CorporationIds"`
	model.Paginator
}

func (sl *StaffLeave) LeaveList(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StaffLeave
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	results, count := (&hrModel.LeaveManagement{}).GetBy(corporationIds, param.Code, param.StaffName, param.JobNumber, util.LeaveTypeForAnnualLeave, param.WorkPostType, param.Year, param.Paginator)

	for i := range results {
		var staffInfo = rpc.GetStaffWithId(ctx, results[i].StaffId)
		if staffInfo == nil {
			log.ErrorFields("staffInfo.GetStaffWithId error", map[string]interface{}{"info": results[i]})
			continue
		}

		results[i].StaffName = staffInfo.Name
		results[i].CorporationName = rpc.GetCorporationNameById(ctx, staffInfo.CorporationId)

		results[i].JoinAt = model.LocalTime(time.Unix(staffInfo.RegisterTime, 0))
		results[i].StartJobAt = model.LocalTime(time.Unix(staffInfo.WorkTime, 0))

		//不同岗位计算方式不同
		workPostType := util.MasterToErp[staffInfo.Occupation]
		if workPostType == util.WorkPostType_3 {
			results[i].WorkAge = (time.Now().Unix() - staffInfo.RegisterTime) / (365 * 24 * 60 * 60)
		} else {
			results[i].WorkAge = (time.Now().Unix() - staffInfo.WorkTime) / (365 * 24 * 60 * 60)
		}
	}

	return response.Success(rsp, map[string]interface{}{"Items": results, "TotalCount": count})

}

func (sl *StaffLeave) LeaveListExport(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return sl.LeaveList(ctx, req, rsp)
}

func (sl *StaffLeave) LeaveEdit(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param hrModel.LeaveManagement
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = param.UpdateColumn("TotalDay", param.TotalDay)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (sl *StaffLeave) ShowLeaveRule(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	user := auth.User(ctx).GetUser()
	leaveSetting := (&hrModel.LeaveRuleSetting{}).FirstBy(user.TopCorporationId, util.LeaveTypeForAnnualLeave)
	if leaveSetting.Id == 0 {
		return response.Success(rsp, map[string]interface{}{"Item": leaveSetting})
	}
	return response.Success(rsp, leaveSetting)
}

func (sl *StaffLeave) SetLeaveRule(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param hrModel.AnnualLeaveSettingItem
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	user := auth.User(ctx).GetUser()

	var leaveSetting hrModel.LeaveRuleSetting

	bytes, _ := json.Marshal(&param)

	leaveSetting.SettingItem = bytes

	leaveSetting.TopCorporationId = user.TopCorporationId
	leaveSetting.LeaveType = util.LeaveTypeForAnnualLeave
	leaveSetting.OpUser.ParseOpUser(ctx)

	err = leaveSetting.Update()
	if err != nil {
		log.ErrorFields("leaveSetting.Update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func SyncHistoryApplyLeaveTime() {
	var records []hrModel.ApplyLeaveRecord
	model.DB().Model(&hrModel.ApplyLeaveRecord{}).Find(&records)

	for i := 0; i < len(records); i++ {
		record := records[i]
		record.LeaveDates = util.GetDateFromRangeTime(time.Time(record.StartAt), time.Time(record.EndAt))

		var leaveRecordDates []hrModel.ApplyLeaveRecordDate
		for i := range record.LeaveDates {
			leaveDate, err := time.ParseInLocation(model.DateFormat, record.LeaveDates[i], time.Local)
			if err != nil {
				log.ErrorFields("LeaveDates time.ParseInLocation error", map[string]interface{}{"err": err})
				continue
			}

			leaveRecordDates = append(leaveRecordDates, hrModel.ApplyLeaveRecordDate{
				ApplyLeaveRecordId: record.Id,
				Date:               model.LocalTime(leaveDate),
			})
		}

		err := (&hrModel.ApplyLeaveRecordDate{}).TransactionCreate(model.DB(), leaveRecordDates)
		if err != nil {
			log.ErrorFields("ApplyLeaveRecordDate.TransactionCreate error", map[string]interface{}{"err": err})
			continue
		}
	}
}

type StaffLeaveScrap struct {
	Id          int64  `json:"Id"`
	ScrapReason string `json:"ScrapReason"`
}

func (sl *StaffLeave) Scrap(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var form StaffLeaveScrap
	err := json.Unmarshal([]byte(req.Body), &form)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if form.Id == 0 || form.ScrapReason == "" {
		log.ErrorFields("params invalid", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	applyLeaveRecord := (&hrModel.ApplyLeaveRecord{}).FirstBy(form.Id)
	if applyLeaveRecord.ApplyStatus != 2 {
		return response.Error(rsp, "OP7515")
	}
	applyLeaveRecord.ScrapDate = model.LocalTime(time.Now())
	applyLeaveRecord.ScrapReason = form.ScrapReason
	user := auth.User(ctx).GetUser()
	applyLeaveRecord.ScrapUserName = user.Name
	applyLeaveRecord.ScrapUserId = user.Id
	tx := model.DB().Begin()
	// 修改状态
	err = tx.Updates(&applyLeaveRecord).Error
	if err != nil {
		log.ErrorFields("save error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbUpdateFail)
	}
	//请假管理这边【年休假】的请假类型作废后，要把请假的天数返还给对应年度的年休假
	if applyLeaveRecord.LeaveType == 1 {
		year := time.Time(applyLeaveRecord.StartAt).Year()
		leaveManagement := (&hrModel.LeaveManagement{}).GetStaffLeaveByYear(applyLeaveRecord.StaffId, int64(year))
		if leaveManagement.Id != 0 {
			leaveManagement.UsedDay -= applyLeaveRecord.DayCount / 10
			err = tx.Model(&hrModel.LeaveManagement{}).Where("Id=?", leaveManagement.Id).Updates(map[string]interface{}{
				"UsedDay": leaveManagement.UsedDay,
			}).Error
			if err != nil {
				log.ErrorFields("save error", map[string]interface{}{"err": err})
				tx.Rollback()
				return response.Error(rsp, response.DbUpdateFail)
			}
		}
	}
	tx.Commit()
	return response.Success(rsp, nil)

}

// 请假汇总表 展示一个人员在一个时间段内不同请假类型下的合并天数
type StaffLeaveCount struct {
	StaffId        int64           `json:"StaffId"`
	StaffName      string          `json:"StaffName"`
	CorporationIds []int64         `json:"CorporationIds"`
	StartAt        model.LocalTime `json:"StartAt"`
	EndAt          model.LocalTime `json:"EndAt"`
	model.Paginator
}

func (sl *StaffLeave) LeaveCountReport(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StaffLeaveCount
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	records := (&hrModel.ApplyLeaveRecord{}).StaffLeaveCount(param.StaffId, time.Time(param.StartAt), time.Time(param.EndAt))

	return response.Success(rsp, records)

