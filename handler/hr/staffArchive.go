package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/handler/base"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	protoStaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"sort"
	"strconv"
	"time"
)

type StaffArchive struct {
	hrModel.StaffArchive
	JoinCompanyApplyId int64  `json:"JoinCompanyApplyId"`
	ProcessId          string `json:"ProcessId"`
	LbpmParam          string `json:"LbpmParam"` //蓝凌嵌入页面参数
	model.Paginator

	IsMore int64 `json:"IsMore"` // 0 不包含敏感信息  1 包含敏感信息
}

// SubmitApply 通过员工入职申请
func (sta *StaffArchive) SubmitApply(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return updateOrCreateArchive(ctx, req, rsp, "")
}

// Create 新增员工档案
func (sta *StaffArchive) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return updateOrCreateArchive(ctx, req, rsp, "Create")
}

// Edit 编辑员工档案
func (sta *StaffArchive) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	if !config.Config.Lbpm.Enable {
		return updateOrCreateArchive(ctx, req, rsp, "Edit")
	}

	var param StaffArchive
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}
	if param.StaffArchive.Id == 0 {
		log.ErrorFields("missing StaffArchive.Id", map[string]interface{}{"StaffArchiveId": param.StaffArchive.Id})
		return response.Error(rsp, response.ParamsMissing)
	}
	var staffArchive hrModel.StaffArchive
	err = staffArchive.FindById(param.Id)

	if err != nil {
		log.ErrorFields("staffArchive.FindById error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	//如果当前人员信息正在审批中  无法再次编辑发起流程
	//if staffArchive.EditApplyStatus == util.ApplyStatusForDoing {
	//	log.ErrorFields("EditMineInfo dont dispatch process repeat!!", nil)
	//	return response.Error(rsp, response.RepeatApplyProcess)
	//}

	//loginStaff := rpc.GetStaffWithId(ctx, auth.User(ctx).GetStaffId())
	param.StaffArchive.ParseOpUser(ctx)
	param.StaffArchive.OpIp = auth.User(ctx).GetClientIp()

	oetStaff := rpc.GetStaffWithId(ctx, staffArchive.StaffId)

	if oetStaff == nil {
		log.ErrorFields("rpc.GetStaffWithId[2] is nil", map[string]interface{}{"staffId": staffArchive.StaffId})
		return response.Error(rsp, response.FAIL)
	}

	//发起流程审批
	/*if config.Config.Lbpm.Enable {

		corporation := rpc.GetCorporationDetailById(ctx, param.StaffArchive.CorporationId)
		if corporation == nil {
			log.ErrorFields("rpc.GetCorporationDetailById is nil", map[string]interface{}{"corporationId": param.StaffArchive.CorporationId})
			return response.Error(rsp, response.FAIL)
		}
		var departmentCode string
		if corporation.BranchId != 0 {
			corporation = rpc.GetCorporationDetailById(ctx, corporation.BranchId)
			if corporation != nil {
				departmentCode = corporation.Item.Virtual
			}
		} else if corporation.CompanyId != 0 {
			corporation = rpc.GetCorporationDetailById(ctx, corporation.CompanyId)
			if corporation != nil {
				departmentCode = corporation.Item.Virtual
			}
		} else if corporation.GroupId != 0 {
			corporation = rpc.GetCorporationDetailById(ctx, corporation.GroupId)
			if corporation != nil {
				departmentCode = corporation.Item.Virtual
			}
		}

		var tx = model.DB().Begin()

		//更新员工档案状态为正在审批中
		err = staffArchive.UpdateEditApplyStatus(tx, util.ApplyStatusForDoing)
		if err != nil {
			log.ErrorFields("staffArchive.UpdateEditApplyStatus error", map[string]interface{}{"err": err})
			tx.Rollback()
			return response.Error(rsp, response.FAIL)
		}

		param.StaffArchive.StaffName = oetStaff.Name
		byteParam, _ := json.Marshal(param.StaffArchive)
		formData := map[string]interface{}{
			"WorkPostType":   param.StaffArchive.WorkPostType,
			"StaffPhone":     oetStaff.Phone,
			"StaffName":      oetStaff.Name,
			"DepartmentName": corporation.Item.Name,
			"DepartmentCode": departmentCode,
		}
		processTitle := fmt.Sprintf("%s提交的审批", oetStaff.Name)
		_, err = processService.NewDispatchProcess(oetStaff, config.StaffArchiveEditApplyFormTemplate, processTitle, param.StaffArchive.Id, param.StaffArchive.TableName(), param.StaffArchive.EditApplyStatusFieldName(), string(byteParam), formData)
		if err != nil {
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			tx.Rollback()
			return response.Error(rsp, response.FAIL)
		}
		tx.Commit()
	}*/

	err = service.UpdateStaffArchiveInfo(param.StaffArchive.Id, param.StaffArchive)
	if err != nil {
		log.ErrorFields("service.UpdateStaffArchiveInfo error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.FAIL)
	}

	service.OetStaffFieldToErpStaffField(oetStaff, &staffArchive)
	//添加历史记录
	go service.BuildStaffArchiveLogger(&staffArchive, &(param.StaffArchive), "")

	return response.Success(rsp, nil)
}

func updateOrCreateArchive(ctx context.Context, req *api.Request, rsp *api.Response, from string) error {
	var param StaffArchive
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	corporation := rpc.GetCorporationDetailById(ctx, param.CorporationId)

	if corporation != nil {
		param.GroupId = corporation.GroupId
		param.CompanyId = corporation.CompanyId
		param.BranchId = corporation.BranchId
		param.DepartmentId = corporation.DepartmentId
		param.FleetId = corporation.FleetId
	}

	var oldInfo hrModel.StaffArchive
	if param.Id > 0 {
		err := oldInfo.FindById(param.Id)
		if err != nil {
			return response.Error(rsp, response.DbNotFoundRecord)
		}
	}
	//获取当前登录人的权限
	user := auth.User(ctx).GetUser()

	var oetStaff *protoStaff.OetStaffItem
	staffId := oldInfo.StaffId
	staffCode := ""
	if staffId > 0 {
		oetStaff = rpc.GetStaffWithId(ctx, staffId)
		if oetStaff == nil {
			oetStaff = rpc.GetStaffWithPhone(ctx, param.GroupId, param.Contact)
			if oetStaff != nil {
				staffId = oetStaff.Id
				staffCode = oetStaff.Code
			} else {
				staffId = 0
			}
		} else {
			staffId = oetStaff.Id
			staffCode = oetStaff.Code
		}
	} else {
		oetStaff = rpc.GetStaffWithPhone(ctx, param.GroupId, param.Contact)
		if oetStaff != nil {
			staffId = oetStaff.Id
			staffCode = oetStaff.Code
		} else {
			staffId = 0
		}
	}

	if staffId > 0 && from == "Create" {
		if param.StaffArchive.ExistByStaffId(staffId) {
			return response.Error(rsp, response.DbObjectDuplicate)
		}
	}

	param.StaffId = staffId
	if oetStaff == nil {
		oetStaff = &protoStaff.OetStaffItem{Code: staffCode}
	}
	service.ErpStaffFieldToOetStaffField(param.StaffArchive, oetStaff)

	//数据同步主数据
	if staffId > 0 {
		//根据权限控制无权限字段不被更新
		if from == "Edit" {
			service.UpdateOetByPermission(oetStaff, user)
		}
		//更新主数据数据
		err = rpc.EditOetStaff(ctx, user.Id, oetStaff)
		if err != nil {
			log.ErrorFields("rpc EditOetStaff err", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	} else {
		//新增主数据数据
		oetStaff.CreatedAt = time.Now().Unix()
		newOetStaff, err := rpc.CreateOetStaff(ctx, oetStaff)
		if err != nil || newOetStaff.Id == 0 {
			log.ErrorFields("rpc CreateOetStaff err", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
		param.StaffId = newOetStaff.Id
	}

	//是否是人事直接录入
	var isHrCreate bool
	if param.Id == 0 && param.JoinCompanyApplyId == 0 {
		isHrCreate = true
	}

	//新增ERP数据
	param.ParseOpUser(ctx)
	param.OpIp = auth.User(ctx).GetClientIp()

	//年龄和出生日期
	if param.IdentityId != "" && len(param.IdentityId) == 18 {
		date := []byte(param.IdentityId)[6:14]
		birthDate, _ := time.ParseInLocation("20060102", string(date), time.Local)
		localTime := model.LocalTime(birthDate)
		param.BirthDate = &localTime
		param.Age = (time.Now().Unix() - birthDate.Unix()) / (365 * 24 * 3600)
	}

	var permission = make(map[string]bool)
	if from == "Edit" {
		permission = service.ReturnEditPermission(user)
	}

	err = param.StaffArchive.UpdateOrCreate(permission)
	if err != nil {
		log.ErrorFields("StaffArchive.Create fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	//更新申请状态为 已入职
	var apply hrModel.JoinCompanyApply
	if param.JoinCompanyApplyId > 0 {
		_ = apply.UpdateIsApply(param.JoinCompanyApplyId, HasEntry)
	} else {
		if isHrCreate {
			//如果是人事直接录入  需要在申请表添加一条记录
			err := util.StructAConvertStructB(param.StaffArchive, &apply)
			if err != nil {
				log.ErrorFields("StructAConvertStructB fail", map[string]interface{}{"err": err})
			} else {
				apply.IsApply = HasEntry
				err := apply.Create()
				if err != nil {
					log.ErrorFields("JoinCompanyApply Create fail", map[string]interface{}{"err": err})
				}
			}
		}
	}

	//添加历史记录
	service.BuildStaffArchiveLogger(&oldInfo, &(param.StaffArchive), "")

	return response.Success(rsp, nil)
}

type ImportParam struct {
	CorporationId int64  `json:"CorporationId"`
	FileData      string `json:"FileData"`
}

// Import 导入在职员工数据
func (sta *StaffArchive) Import(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ImportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			response.Error(rsp, response.FAIL)
		}
		return
	}()

	//获取机构的层级关系
	corporation := rpc.GetCorporationDetailById(ctx, param.CorporationId)
	if corporation == nil {
		log.ErrorFields("rpc GetCorporationDetailById is nil", map[string]interface{}{"corporationId": param.CorporationId})
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 通过根机构获取所有员工数据
	oetStaffs := rpc.GetStaffsWithOption(ctx, corporation.GroupId)
	oetStaffMap := make(map[string]*protoStaff.OetStaffItem)
	for i := range oetStaffs {
		if oetStaffs[i].Phone != "" {
			oetStaffMap[oetStaffs[i].Phone] = oetStaffs[i]
		}
	}

	//①sheet 0=>员工档案
	// 0姓名* 1身份证 2联系电话 3工号 4在职状态 5性别 6籍贯 7户口性质 8民族 9政治面貌 10家庭住址 11健康状况 12是否复退转军人 13复退转时间 14婚姻状况 15生育信息 16驾驶证号
	// 17准驾车型 18入党时间 19最高学历 20进入公司途径 21进现单位时间 22工龄起算时间 23退休时间 24岗位类型

	sheet := excelFile.Sheets[0]
	var importFailStaffs []string
	var successCount int64
	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}

		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 5 {
			continue
		}

		if row.Cells[0].String() == "" || row.Cells[1].String() == "" || row.Cells[2].String() == "" || row.Cells[3].String() == "" || row.Cells[4].String() == "" {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}

		if len([]byte(row.Cells[1].String())) != 18 {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}

		var archive hrModel.StaffArchive
		//archive.GroupId = corporation.GroupId
		//archive.CompanyId = corporation.CompanyId
		//archive.BranchId = corporation.BranchId
		//archive.DepartmentId = corporation.DepartmentId
		//archive.FleetId = corporation.FleetId

		var educations = make([]hrModel.StaffEducation, 2)
		var positionalTitles = make([]hrModel.StaffPositionalTitle, 2)
		var skills = make([]hrModel.StaffSkill, 2)
		var familyMembers = make([]hrModel.StaffFamilyMember, 3)
		var workPosts = make([]hrModel.StaffHasWorkPost, 2)
		var corporationId int64
		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 0:
				archive.Name = row.Cells[0].String()
			case 1:
				archive.IdentityId = row.Cells[1].String()
				//年龄和出生日期
				if archive.IdentityId != "" && len(archive.IdentityId) == 18 {
					date := []byte(archive.IdentityId)[6:14]
					birthDate, _ := time.ParseInLocation("20060102", string(date), time.Local)
					localTime := model.LocalTime(birthDate)
					archive.BirthDate = &localTime
					archive.Age = (time.Now().Unix() - birthDate.Unix()) / (365 * 24 * 3600)
				}
			case 2:
				archive.Contact = row.Cells[2].String()
			case 3:
				archive.JobNumber = row.Cells[3].String()
			case 4:
				if row.Cells[4].String() != "" {
					archive.JobStatus, _ = util.JobStatusMap[row.Cells[4].String()]
				}
			case 5:
				if row.Cells[5].String() != "" {
					archive.Gender, _ = util.GenderMap[row.Cells[5].String()]
				}
			case 6:
				archive.NativePlace = row.Cells[6].String()
			case 7:
				if row.Cells[7].String() != "" {
					archive.ResidenceAttr, _ = util.ResidenceAttrMap[row.Cells[7].String()]
				}
			case 8:
				archive.Nation = row.Cells[8].String()
			case 9:
				if row.Cells[9].String() != "" {
					archive.PoliticalIdentity, _ = util.PoliticalIdentityMap[row.Cells[9].String()]
				}
			case 10:
				archive.Address = row.Cells[10].String()
			case 11:
				if row.Cells[11].String() != "" {
					archive.HealthStatus, _ = util.HealthStatusMap[row.Cells[11].String()]
				}
			case 12:
				if row.Cells[12].String() != "" {
					archive.IsReversionSoldier, _ = util.StatusMap[row.Cells[12].String()]
				}
			case 13:
				reversionAt, err := row.Cells[13].GetTime(false)
				if err == nil && !reversionAt.IsZero() {
					local := model.LocalTime(reversionAt)
					archive.ReversionAt = &local
				}
			case 14:
				if row.Cells[14].String() != "" {
					archive.MarriageStatus, _ = util.MarriageStatusMap[row.Cells[14].String()]
				}
			case 15:
				if row.Cells[15].String() != "" {
					archive.BearStatus, _ = util.BearStatusMap[row.Cells[15].String()]
				}
			case 16:
				archive.DrivingCode = row.Cells[16].String()
			case 17:
				archive.DrivingModel = row.Cells[17].String()
			case 18:
				joinPartyAt, err := row.Cells[18].GetTime(false)
				if err == nil && !joinPartyAt.IsZero() {
					local := model.LocalTime(joinPartyAt)
					archive.JoinPartyAt = &local
				}
			case 19:
				if row.Cells[19].String() != "" {
					archive.HighestEdu, _ = util.HighestEduMap[row.Cells[19].String()]
				}
			case 20:
				if row.Cells[20].String() != "" {
					archive.JoinCompanyWay, _ = util.JoinCompanyWayMap[row.Cells[20].String()]
				}
			case 21:
				joinAt, err := row.Cells[21].GetTime(false)
				if err == nil && !joinAt.IsZero() {
					local := model.LocalTime(joinAt)
					archive.JoinAt = &local
				}
			case 22:
				jobAt, err := row.Cells[22].GetTime(false)
				if err == nil && !jobAt.IsZero() {
					local := model.LocalTime(jobAt)
					archive.StartJobAt = &local
				}
			case 23:
				retireAt, err := row.Cells[23].GetTime(false)
				if err == nil && !retireAt.IsZero() {
					local := model.LocalTime(retireAt)
					archive.RetireAt = &local
				}
			case 24:
				if row.Cells[24].String() != "" {
					archive.WorkPostType, _ = util.WorkPostTypeMap[row.Cells[24].String()]
				}
			case 25:
				//学历1：25学历 26毕业院校 27专业 28开始时间 29结束时间
				//学历2：30学历 31毕业院校 32专业 33开始时间 34结束时间
				if row.Cells[25].String() != "" {
					educations[0].Edu, _ = util.HighestEduMap[row.Cells[25].String()]
				}
			case 26:
				educations[0].School = row.Cells[26].String()
			case 27:
				educations[0].Major = row.Cells[27].String()
			case 28:
				startTime, err := row.Cells[28].GetTime(false)
				var startAt model.LocalTime
				if err == nil && !startTime.IsZero() {
					startAt = model.LocalTime(startTime)
				}
				educations[0].StartAt = &startAt
			case 29:
				endTime, err := row.Cells[29].GetTime(false)
				var endAt model.LocalTime
				if err == nil && !endTime.IsZero() {
					endAt = model.LocalTime(endTime)
				}
				educations[0].EndAt = &endAt
			case 30:
				if row.Cells[30].String() != "" {
					educations[1].Edu, _ = util.HighestEduMap[row.Cells[30].String()]
				}
			case 31:
				educations[1].School = row.Cells[31].String()
			case 32:
				educations[1].Major = row.Cells[32].String()
			case 33:
				startTime, err := row.Cells[33].GetTime(false)
				var startAt model.LocalTime
				if err == nil && !startTime.IsZero() {
					startAt = model.LocalTime(startTime)
				}
				educations[1].StartAt = &startAt
			case 34:
				endTime, err := row.Cells[34].GetTime(false)
				var endAt model.LocalTime
				if err == nil && !endTime.IsZero() {
					endAt = model.LocalTime(endTime)
				}
				educations[1].EndAt = &endAt
			case 35:
				//职称1：35职称名称
				//职称2：36职称名称
				if row.Cells[35].String() != "" {
					positionalTitles[0].Name = row.Cells[35].String()
				}
			case 36:
				if row.Cells[36].String() != "" {
					positionalTitles[1].Name = row.Cells[36].String()
				}
			case 37:
				//技能级别1：37技能级别名称
				//技能级别2：38技能级别名称
				if row.Cells[37].String() != "" {
					skills[0].Name = row.Cells[37].String()
				}
			case 38:
				if row.Cells[38].String() != "" {
					skills[1].Name = row.Cells[38].String()
				}
			case 39:
				//紧急联系人1：39姓名 40关系 41联系方式
				//家庭成员2：42姓名 43关系 44联系方式
				//家庭成员3：45姓名 46关系 47联系方式
				if row.Cells[39].String() != "" {
					familyMembers[0].Name = row.Cells[39].String()
					familyMembers[0].IsMainContact = 1
				}
			case 40:
				familyMembers[0].Relationship = row.Cells[40].String()
			case 41:
				familyMembers[0].Contact = row.Cells[41].String()
			case 42:
				if row.Cells[42].String() != "" {
					familyMembers[1].Name = row.Cells[42].String()
				}
			case 43:
				familyMembers[1].Relationship = row.Cells[43].String()
			case 44:
				familyMembers[1].Contact = row.Cells[44].String()
			case 45:
				if row.Cells[45].String() != "" {
					familyMembers[2].Name = row.Cells[45].String()
				}
			case 46:
				familyMembers[2].Relationship = row.Cells[46].String()
			case 47:
				familyMembers[2].Contact = row.Cells[47].String()
			case 48:
				//职务1：48任职时间 49岗位名称 50任职类型 51任职部门 52工资等级 53现任职务有效性
				//职务2：54任职时间 55岗位名称 56任职类型 57任职部门 58工资等级 59现任职务有效性
				startTime, err := row.Cells[48].GetTime(false)
				var startAt model.LocalTime
				if err == nil && !startTime.IsZero() {
					startAt = model.LocalTime(startTime)
				}
				workPosts[0].StartAt = &startAt
			case 49:
				if row.Cells[49].String() != "" {
					var workPost hrModel.WorkPost
					post := workPost.FirstByCode(row.Cells[49].String())
					workPosts[0].WorkPostType = post.Type
					workPosts[0].WorkPostId = post.Id
					if post.Id > 0 {
						archive.GroupId = post.GroupId
						archive.CompanyId = post.CompanyId
						archive.BranchId = post.BranchId
						archive.DepartmentId = post.DepartmentId
						archive.FleetId = post.FleetId
					} else {
						archive.GroupId = corporation.GroupId
						archive.CompanyId = corporation.CompanyId
						archive.BranchId = corporation.BranchId
						archive.DepartmentId = corporation.DepartmentId
						archive.FleetId = corporation.FleetId
					}
					corporationId, _ = archive.Corporations.GetCorporation()
				}
			case 50:
				workPosts[0].PositionType = util.PositionTypeMap[row.Cells[50].String()]
			case 51:
				//corporation := rpc.GetCorporationByName(ctx, row.Cells[51].String())
				//if corporation != nil {
				workPosts[0].CorporationId = corporationId
				//}
			case 52:
				workPosts[0].PositionLevel = row.Cells[52].String()
			case 53:
				workPosts[0].IsNowJob, _ = util.IsNowJobMap[row.Cells[53].String()]
			case 54:
				startTime, err := row.Cells[54].GetTime(false)
				var startAt model.LocalTime
				if err == nil && !startTime.IsZero() {
					startAt = model.LocalTime(startTime)
				}
				workPosts[1].StartAt = &startAt
			case 55:
				if row.Cells[55].String() != "" {
					var workPost hrModel.WorkPost
					post, _ := workPost.GetByName(row.Cells[55].String())
					workPosts[1].WorkPostType = post.Type
					workPosts[1].WorkPostId = post.Id
				}
			case 56:
				workPosts[1].PositionType = util.PositionTypeMap[row.Cells[56].String()]
			case 57:
				corporation := rpc.GetCorporationByName(ctx, row.Cells[57].String())
				if corporation != nil {
					workPosts[1].CorporationId = corporation.Id
				}
			case 58:
				workPosts[1].PositionLevel = row.Cells[58].String()
			case 59:
				if row.Cells[59].String() != "" {
					workPosts[1].IsNowJob, _ = util.IsNowJobMap[row.Cells[59].String()]
				}
			case 60:
				if row.Cells[60].String() != "" {
					archive.EmploymentCate = util.EmploymentCateMap[row.Cells[60].String()]
				}
				//case 61:
				//	archive.LineId, _ = row.Cells[61].Int64()
				//case 62:
				//	archive.AttendanceCard = row.Cells[62].String()
			}
		}

		archive.Educations = educations
		archive.PositionalTitles = positionalTitles
		archive.Skills = skills
		archive.FamilyMembers = familyMembers
		archive.WorkPosts = workPosts

		//手机号在主数据已经存在=>更新  不存在=>新增
		if _, ok := oetStaffMap[archive.Contact]; ok {
			var oetStaff = oetStaffMap[archive.Contact]
			archive.StaffId = oetStaff.Id
			archive.CorporationId = corporationId

			service.ErpStaffFieldToOetStaffField(archive, oetStaff)
			fmt.Printf("import edit oetStaff: %+v \n", oetStaff)
			err := rpc.EditOetStaff(ctx, auth.User(ctx).GetUserId(), oetStaff)

			if err != nil {
				log.ErrorFields("rpc EditOetStaff error", map[string]interface{}{"err": err})
				importFailStaffs = append(importFailStaffs, archive.Name)
				continue
			}
			var erpStaff hrModel.StaffArchive
			_ = erpStaff.FindByStaffId(archive.StaffId)

			archive.Id = erpStaff.Id

		} else {
			//新增主数据
			archive.CorporationId = param.CorporationId
			var oetStaff protoStaff.OetStaffItem
			service.ErpStaffFieldToOetStaffField(archive, &oetStaff)

			oetStaff.CreatedAt = time.Now().Unix()
			newOetStaff, err := rpc.CreateOetStaff(ctx, &oetStaff)
			if err != nil {
				importFailStaffs = append(importFailStaffs, archive.Name)
				log.ErrorFields("rpc CreateOetStaff error", map[string]interface{}{"err": err})
				continue
			}
			archive.StaffId = newOetStaff.Id
		}

		fmt.Printf("archive============= %+v \r\n", archive)
		err = archive.UpdateOrCreate(map[string]bool{})
		if err != nil {
			log.ErrorFields(" archive.UpdateOrCreate error", map[string]interface{}{"err": err})
			importFailStaffs = append(importFailStaffs, archive.Name)
			continue
		}
		successCount++

	}

	return response.Success(rsp, map[string]interface{}{
		"SuccessCount": successCount,
		"FailCount":    len(importFailStaffs),
		"FailStaffs":   importFailStaffs,
	})
}

// Export 导出员工数据
func (sta *StaffArchive) Export(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffArchiveFilter
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	data := ArchiveList(ctx, param, "List", false)

	return response.Success(rsp, data)
}

// List 在职员工数据管理列表
func (sta *StaffArchive) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffArchiveFilter
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	fmt.Printf("List param======================%+v \n", param)

	// 在职列表去除离职、退休状态的人员,如果筛选姓名则可以包含所有状态人员
	if len(param.JobStatusArr) == 0 && param.Keyword == "" {
		// 传空查全部， 重新赋值
		// 在职状态 主数据|多选 1-在职,2-离职,3-试用期,4-退休,5-退休返聘
		param.JobStatusArr = []int64{util.JobStatusWorking, util.JobStatusProbation, util.JobStatusRetireWork}
	}

	data := ArchiveList(ctx, param, "List", false)
	return response.Success(rsp, data)
}

type StaffArchiveFilter struct {
	CorporationIds []int64 `json:"CorporationIds"`
	service.OetWhere
	hrModel.ErpWhere
	model.Paginator
}

func ArchiveList(ctx context.Context, param StaffArchiveFilter, from string, isRelateBecomeWorkerProcess bool) map[string]interface{} {
	//if len(param.CorporationIds) == 0 {
	//	corporationId := util.GetUserCorporationId(ctx)
	//	param.CorporationIds = append(param.CorporationIds, corporationId)
	//}
	param.CorporationIds = service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	var oetIsPaginator bool
	//根据职位ID模糊搜索所有职位ID
	if param.WorkPost != "" {
		var workPost hrModel.WorkPost
		param.WorkPostIds = workPost.PluckIdByName(param.WorkPost)
	}

	if param.ResidenceAttr == 0 && len(param.HighestEduArr) == 0 && len(param.WorkPostIds) == 0 && param.PositionalTitle == "" && param.Skill == "" &&
		len(param.MarriageStatusArr) == 0 && len(param.BearStatusArr) == 0 && param.Major == "" && param.School == "" && param.Address == "" && param.CertificateName == "" &&
		(param.StartCertificateAt == nil || time.Time(*param.StartCertificateAt).IsZero()) && (param.EndCertificateAt == nil || time.Time(*param.EndCertificateAt).IsZero()) &&
		len(param.HumanRelationIds) == 0 && len(param.EmploymentCateArr) == 0 && (param.StartWorkAt == nil || time.Time(*param.StartWorkAt).IsZero()) &&
		(param.EndWorkAt == nil || time.Time(*param.EndWorkAt).IsZero()) && param.PositionLevel == "" {
		oetIsPaginator = true
	}

	//根据条件过滤主数据人员
	oetStaffMaps, oetStaffIds, count := service.SelectOetStaffByMultiWhere(ctx, param.CorporationIds, param.OetWhere, oetIsPaginator, int64(param.Offset), int64(param.Limit))

	if len(oetStaffIds) == 0 {
		return map[string]interface{}{"Items": []interface{}{}, "TotalCount": 0}
	}

	if oetIsPaginator {
		param.Limit = 0
	}

	var staffArchive hrModel.StaffArchive
	//根据条件过滤ERP人员
	archives, totalCount := staffArchive.GetBy(oetStaffIds, param.ErpWhere, param.Paginator)

	if oetIsPaginator {
		totalCount = count
		for i := range archives {
			archives[i].Sort = int64(util.SliceIntIndexOf(oetStaffIds, archives[i].StaffId))
		}
		sort.SliceStable(archives, func(i, j int) bool {
			return archives[i].Sort < archives[j].Sort
		})
	}

	//获取当前登录人的权限
	user := auth.User(ctx).GetUser()

	for i := range archives {
		archive := archives[i]
		oetStaff := oetStaffMaps[archive.StaffId]
		service.OetStaffFieldToErpStaffField(oetStaff, &archive)

		service.HiddenFieldByPermission(&archive, user, from)
		for j := range archive.WorkPosts {
			if archive.WorkPosts[j].WorkPostId > 0 {
				var workPost hrModel.WorkPost
				_ = workPost.FirstById(archive.WorkPosts[j].WorkPostId)
				archive.WorkPosts[j].WorkPostName = workPost.Name
			}
			if archive.WorkPosts[j].CorporationId > 0 {
				corporation := rpc.GetCorporationById(ctx, archive.WorkPosts[j].CorporationId)
				if corporation != nil {
					archive.WorkPosts[j].CorporationName = corporation.Name
				}
			}
			if archive.WorkPosts[j].CorporationId == archive.WorkPosts[j].HumanRelationId {
				archive.WorkPosts[j].HumanRelationName = archive.WorkPosts[j].CorporationName
			} else {
				corporation := rpc.GetCorporationById(ctx, archive.WorkPosts[j].HumanRelationId)
				if corporation != nil {
					archive.WorkPosts[j].HumanRelationName = corporation.Name
				}
			}
		}
		if oetStaff.CorporationId > 0 {
			corporation := rpc.GetCorporationById(ctx, oetStaff.CorporationId)
			if corporation != nil {
				archive.CorporationName = corporation.Name
			}
		}
		archive.InsuranceRecord = (&hrModel.InsuranceRecord{}).GetLatestFirstRecord(archive.Id)
		archive.FundRecord = (&hrModel.FundRecord{}).GetLatestFirstRecord(archive.Id)

		if len(archives[i].LaborContracts) > 0 {
			laborContract := archives[i].LaborContracts[0]
			if laborContract.Status < util.LaborContractRelieve && laborContract.EndAt != nil {
				if time.Time(*laborContract.EndAt).Unix() >= time.Now().Unix() {
					archives[i].LaborContractStatus = util.LaborContractValid
				} else {
					archives[i].LaborContractStatus = util.LaborContractExpiration
				}
			} else {
				archives[i].LaborContractStatus = laborContract.Status
			}
		}

		//查询最近一条转正流程记录
		if isRelateBecomeWorkerProcess {
			record := (&hrModel.DriverBecomeWorkerRecord{}).FirstLatestRecord(archive.Id)
			if record.Id > 0 {
				var process processModel.LbpmApplyProcess
				_ = process.GetProcessByItemId(record.Id, record.TableName())
				record.CurrentHandler = process.CurrentHandlerUserName
				if process.DoneAt != nil {
					record.DoneAt = *process.DoneAt
				}
				record.ProcessId = process.ProcessId
				archive.BecomeWorkerRecord = record
			}
		}
		archives[i] = archive
	}

	return map[string]interface{}{"Items": archives, "TotalCount": totalCount}
}

// Info 员工档案详情
func (sta *StaffArchive) Info(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffArchive
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	//获取当前登录人的权限
	user := auth.User(ctx).GetUser()

	//获取ERP数据
	err = param.StaffArchive.FindById(param.Id)
	if err != nil {
		log.ErrorFields("StaffArchive.FindById fail", map[string]interface{}{"err": err, "id": param.Id})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	//获取主数据数据
	oetStaff := rpc.GetStaffWithId(ctx, param.StaffId)
	if oetStaff == nil {
		log.ErrorFields("GetStaffWithId fail", map[string]interface{}{"err": err, "staffId": param.StaffId})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	service.OetStaffFieldToErpStaffField(oetStaff, &param.StaffArchive)

	//根据权限隐藏无权限看到的数据
	service.HiddenFieldByPermission(&param.StaffArchive, user, "Info")

	for i := range param.WorkPosts {
		if param.WorkPosts[i].WorkPostId > 0 {
			var workPost hrModel.WorkPost
			_ = workPost.FirstById(param.WorkPosts[i].WorkPostId)
			param.WorkPosts[i].WorkPostName = workPost.Name
		}
		if param.WorkPosts[i].CorporationId > 0 {
			corporation := rpc.GetCorporationById(ctx, param.WorkPosts[i].CorporationId)
			if corporation != nil {
				param.WorkPosts[i].CorporationName = corporation.Name
			}
		}
		if param.WorkPosts[i].CorporationId == param.WorkPosts[i].HumanRelationId {
			param.WorkPosts[i].HumanRelationName = param.WorkPosts[i].CorporationName
		} else {
			corporation := rpc.GetCorporationById(ctx, param.WorkPosts[i].HumanRelationId)
			if corporation != nil {
				param.WorkPosts[i].HumanRelationName = corporation.Name
			}
		}
	}
	//for i := range param.LaborContracts {
	//	//if param.LaborContracts[i].HandleStaffWorkPostId > 0 {
	//	//	var workPost hrModel.WorkPost
	//	//	_ = workPost.FirstById(param.LaborContracts[i].HandleStaffWorkPostId)
	//	//	param.LaborContracts[i].HandleStaffWorkPostName = workPost.Name
	//	//}
	//}

	var workTrain hrModel.WorkTrain

	param.StaffArchive.WorkTrains = workTrain.GetWorkTrainByArchiveId(param.StaffArchive.Id)
	param.StaffArchive.Assessments = (&hrModel.StaffAssessment{}).GetLatestRecord(param.StaffArchive.Id, 3)

	if oetStaff.CorporationId > 0 {
		corporation := rpc.GetCorporationById(ctx, oetStaff.CorporationId)
		if corporation != nil {
			param.CorporationName = corporation.Name
		}
	}

	if param.HumanRelationId == oetStaff.CorporationId {
		param.HumanRelationName = param.CorporationName
	} else {
		corporation := rpc.GetCorporationById(ctx, param.HumanRelationId)
		if corporation != nil {
			param.HumanRelationName = corporation.Name
		}
	}

	param.StaffArchive.FileHttpPrefix = config.Config.StaticFileHttpPrefix
	param.StaffArchive.IsProcessHandler = processService.CheckIsProcessRelater(param.StaffArchive.Id, config.StaffArchiveEditApplyFormTemplate, user.Id)

	return response.Success(rsp, param.StaffArchive)
}

// InsuranceRecord 社保记录
func (sta *StaffArchive) InsuranceRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffArchive
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var record hrModel.InsuranceRecord
	records, count := record.GetStaffRecord(param.Id, param.Paginator)

	return response.Success(rsp, map[string]interface{}{"Items": records, "TotalCount": count})

}

// FundRecord 公积金记录
func (sta *StaffArchive) FundRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffArchive
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var record hrModel.FundRecord
	records, count := record.GetStaffRecord(param.Id, param.Paginator)

	return response.Success(rsp, map[string]interface{}{"Items": records, "TotalCount": count})
}

type StaffArchiveLogger struct {
	hrModel.StaffArchiveLogger
	Keyword string          `json:"Keyword"`
	StartAt model.LocalTime `json:"StartAt"`
	EndAt   model.LocalTime `json:"EndAt"`
	model.Paginator
}
type LoggerRsp struct {
	hrModel.StaffArchiveLogger
	CurrentHandlerUserName string // 当前处理人
	LbpmProcessStatus      int64  // 流程状态
}

// Logger 员工档案操作日志记录
func (sta *StaffArchive) Logger(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffArchiveLogger
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	loggers, count := param.GetBy(param.StaffArchiveId, param.Keyword, time.Time(param.StartAt), time.Time(param.EndAt), param.Paginator)
	var rspD []LoggerRsp
	var status = []int64{util.ProcessStatusForDoing, util.ProcessStatusForDone, util.ProcessStatusForRefuse, util.ProcessStatusForTerminate, util.ProcessStatusForAbandon}
	for _, logger := range loggers {
		item := LoggerRsp{
			StaffArchiveLogger: logger,
			LbpmProcessStatus:  0,
		}

		var process processModel.LbpmApplyProcess

		err := process.GetProcess(logger.ProcessId, status)
		if err == nil {
			item.LbpmProcessStatus = process.Status
			item.CurrentHandlerUserName = process.CurrentHandlerUserName
		}

		rspD = append(rspD, item)
	}
	return response.Success(rsp, map[string]interface{}{"Items": rspD, "TotalCount": count})
}

// StaffInfo 通过主数据ID获取员工档案信息
func (sta *StaffArchive) StaffInfo(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffArchive
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.StaffId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	//获取ERP数据
	err = param.StaffArchive.FindByStaffId(param.StaffId)
	if err != nil {
		log.ErrorFields("StaffArchive.FindById fail", map[string]interface{}{"err": err, "id": param.Id})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	//获取主数据数据
	oetStaff := rpc.GetStaffWithId(ctx, param.StaffId)
	if oetStaff == nil {
		log.ErrorFields("GetStaffWithId fail", map[string]interface{}{"err": err, "staffId": param.StaffId})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	service.OetStaffFieldToErpStaffField(oetStaff, &param.StaffArchive)

	for i := range param.WorkPosts {
		if param.WorkPosts[i].WorkPostId > 0 {
			var workPost hrModel.WorkPost
			_ = workPost.FirstById(param.WorkPosts[i].WorkPostId)
			param.WorkPosts[i].WorkPostName = workPost.Name
		}
		if param.WorkPosts[i].CorporationId > 0 {
			corporation := rpc.GetCorporationById(ctx, param.WorkPosts[i].CorporationId)
			if corporation != nil {
				param.WorkPosts[i].CorporationName = corporation.Name
			}
		}
	}
	var staffInfo = make(map[string]interface{})
	staffInfo["Id"] = param.Id
	staffInfo["StaffId"] = param.StaffId
	staffInfo["Name"] = oetStaff.Name
	staffInfo["CorporationId"] = oetStaff.CorporationId
	if oetStaff.CorporationId > 0 {
		corporation := rpc.GetCorporationById(ctx, oetStaff.CorporationId)
		if corporation != nil {
			staffInfo["CorporationName"] = corporation.Name
		}
	}
	staffInfo["JoinAt"] = param.JoinAt
	staffInfo["RetireAt"] = param.RetireAt
	staffInfo["WorkPosts"] = param.WorkPosts
	staffInfo["LaborContracts"] = param.LaborContracts

	staffInfo["JobNumber"] = param.JobNumber
	staffInfo["WorkPostType"] = param.WorkPostType

	staffAnnualLeave := (&hrModel.LeaveManagement{}).GetStaffLeaveByStatus(param.StaffId, util.StatusForTrue)
	staffInfo["HasAnnualLeaveDay"] = staffAnnualLeave.TotalDay - staffAnnualLeave.UsedDay

	staffInfo["DrivingModel"] = param.DrivingModel
	staffInfo["Age"] = param.Age

	if param.IsMore == base.MoreOption {
		staffInfo["IdentityId"] = param.IdentityId
	}

	staffInfo["Gender"] = param.Gender

	return response.Success(rsp, staffInfo)
}

// MineInfo 员工档案详情
func (sta *StaffArchive) MineInfo(ctx context.Context, req *api.Request, rsp *api.Response) error {
	user := auth.User(ctx).GetUser()

	var staffArchive hrModel.StaffArchive

	oetStaff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	if oetStaff == nil {
		log.ErrorFields("GetStaffWithPhone fail", map[string]interface{}{"phone": user.Phone})
		return response.Success(rsp, staffArchive)
	}

	//获取ERP数据
	err := staffArchive.FindMineInfoByStaffId(oetStaff.Id)
	if err != nil {
		log.ErrorFields("StaffArchive.FindMineInfoByStaffId fail", map[string]interface{}{"err": err, "id": oetStaff.Id})
		//return response.Error(rsp, response.DbNotFoundRecord)
	}

	service.OetStaffFieldToErpStaffField(oetStaff, &staffArchive)

	staffArchive.FileHttpPrefix = config.Config.StaticFileHttpPrefix
	staffArchive.IdStr = fmt.Sprintf("%v", staffArchive.Id)
	staffArchive.IsProcessHandler = processService.CheckIsProcessRelater(staffArchive.Id, config.StaffArchiveEditApplyFormTemplate, user.Id)

	corporationItem := rpc.GetCorporationById(ctx, staffArchive.MasterDataFieldForJob.CorporationId)
	if corporationItem != nil {
		staffArchive.CorporationName = corporationItem.Name
		staffArchive.CorporationType = corporationItem.Type
	}
	return response.Success(rsp, staffArchive)
}

// EditMineInfo 编辑我的档案
func (sta *StaffArchive) EditMineInfo(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffArchive
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StaffArchive.IdStr != "" {
		param.StaffArchive.Id, _ = strconv.ParseInt(param.StaffArchive.IdStr, 10, 64)
	}

	user := auth.User(ctx).GetUser()

	oetStaff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	if oetStaff == nil {
		log.ErrorFields("GetStaffWithPhone fail", map[string]interface{}{"staffId": user.Phone})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	if param.StaffArchive.StaffId != oetStaff.Id || param.StaffArchive.Id == 0 {
		return response.Error(rsp, response.BadRequest)
	}

	if err := util.Validator().Struct(param.StaffArchive.MasterDataFieldForBasic); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	if !config.Config.Lbpm.Enable {
		param.StaffArchive.ParseOpUser(ctx)
		param.StaffArchive.OpIp = auth.User(ctx).GetClientIp()
		param.StaffArchive.IsMineEdit = true
		err = service.UpdateMineArchive(param.StaffArchive)
		if err != nil {
			log.ErrorFields("service.UpdateMineArchive", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbUpdateFail)
		}
		return response.Success(rsp, nil)
	}

	var staffArchive hrModel.StaffArchive
	err = staffArchive.FindById(param.Id)

	if err != nil {
		log.ErrorFields("staffArchive.FindById error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	//如果当前人员信息正在审批中  无法再次编辑发起流程
	if staffArchive.EditApplyStatus == util.ApplyStatusForDoing {
		log.ErrorFields("EditMineInfo dont dispatch process repeat!!", nil)
		return response.Error(rsp, response.RepeatApplyProcess)
	}

	param.StaffArchive.ParseOpUser(ctx)
	param.StaffArchive.OpIp = auth.User(ctx).GetClientIp()
	param.StaffArchive.IsMineEdit = true

	//发起流程审批
	var processId string
	if config.Config.Lbpm.Enable {
		//自己编辑时，不能修改所属部门，所以所属部门依然获取历史的所属部门
		corporation := rpc.GetCorporationDetailById(ctx, oetStaff.CorporationId)
		if corporation == nil {
			log.ErrorFields("rpc.GetCorporationDetailById is nil", map[string]interface{}{"corporationId": param.StaffArchive.CorporationId})
			return response.Error(rsp, response.FAIL)
		}
		var departmentCode string
		if corporation.BranchId != 0 {
			corporation = rpc.GetCorporationDetailById(ctx, corporation.BranchId)
			if corporation != nil {
				departmentCode = corporation.Item.Virtual
			}
		} else if corporation.CompanyId != 0 {
			corporation = rpc.GetCorporationDetailById(ctx, corporation.CompanyId)
			if corporation != nil {
				departmentCode = corporation.Item.Virtual
			}
		} else if corporation.GroupId != 0 {
			corporation = rpc.GetCorporationDetailById(ctx, corporation.GroupId)
			if corporation != nil {
				departmentCode = corporation.Item.Virtual
			}
		}

		var tx = model.DB().Begin()

		//更新员工档案状态为正在审批中
		err = param.StaffArchive.UpdateEditApplyStatus(tx, util.ApplyStatusForDoing)
		if err != nil {
			log.ErrorFields("staffArchive.UpdateEditApplyStatus error", map[string]interface{}{"err": err})
			tx.Rollback()
			return response.Error(rsp, response.FAIL)
		}
		param.StaffArchive.StaffName = oetStaff.Name
		byteParam, _ := json.Marshal(param.StaffArchive)
		formData := map[string]interface{}{
			"WorkPostType":   param.StaffArchive.WorkPostType,
			"StaffPhone":     oetStaff.Phone,
			"StaffName":      oetStaff.Name,
			"DepartmentName": corporation.Item.Name,
			"DepartmentCode": departmentCode,
		}
		processTitle := fmt.Sprintf("%s提交的审批", oetStaff.Name)

		processId, err = processService.NewDispatchProcess(user, config.StaffArchiveEditApplyFormTemplate, processTitle, param.StaffArchive.Id, param.StaffArchive.TableName(), param.StaffArchive.EditApplyStatusFieldName(), string(byteParam), formData)
		if err != nil {
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			tx.Rollback()
			return response.Error(rsp, response.FAIL)
		}
		tx.Commit()
	}

	service.OetStaffFieldToErpStaffField(oetStaff, &staffArchive)

	//添加历史记录
	go service.BuildStaffArchiveLogger(&staffArchive, &(param.StaffArchive), processId)

	return response.Success(rsp, nil)
}
