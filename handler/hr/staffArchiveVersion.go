package hr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

type StaffArchiveVersion struct {
	hrModel.StaffArchiveVersion
	model.Paginator
}

// Create 保存员工档案历史版本
func (sav *StaffArchiveVersion) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffArchiveVersion
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.ParseOpUser(ctx)

	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()
	param.CorporationId = auth.User(ctx).GetCorporationId()

	var archive hrModel.StaffArchive
	archives := archive.GetAll()
	param.StaffCount = int64(len(archives))

	var histories []hrModel.StaffArchiveHistory
	for i := range archives {
		oetStaff := rpc.GetStaffWithId(ctx, archives[i].StaffId)
		if oetStaff != nil {
			service.OetStaffFieldToErpStaffField(oetStaff, &archives[i])
		}

		for j := range archives[i].WorkPosts {
			if archives[i].WorkPosts[j].WorkPostId > 0 {
				var workPost hrModel.WorkPost
				_ = workPost.FirstById(archives[i].WorkPosts[j].WorkPostId)
				archives[i].WorkPosts[j].WorkPostName = workPost.Name
			}
			if archives[i].WorkPosts[j].CorporationId > 0 {
				corporation := rpc.GetCorporationById(ctx, archives[i].WorkPosts[j].CorporationId)
				if corporation != nil {
					archives[i].WorkPosts[j].CorporationName = corporation.Name
				}
			}
		}
		if oetStaff != nil && oetStaff.CorporationId > 0 {
			corporation := rpc.GetCorporationById(ctx, oetStaff.CorporationId)
			if corporation != nil {
				archives[i].CorporationName = corporation.Name
			}
		}

		var history hrModel.StaffArchiveHistory
		err := util.StructAConvertStructB(archives[i], &history)
		if err != nil {
			return response.Error(rsp, response.FAIL)
		}
		histories = append(histories, history)
	}

	param.Histories = histories

	err = param.StaffArchiveVersion.Create()
	if err != nil {
		log.ErrorFields("StaffArchiveVersion Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)

}

// List 员工档案历史版本列表
func (sav *StaffArchiveVersion) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var version hrModel.StaffArchiveVersion

	records := version.GetAll()

	return response.Success(rsp, map[string]interface{}{"Items": records})

}

func VersionRecordData(req *api.Request, rsp *api.Response) error {
	var param StaffArchiveVersion
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	err = param.FirstBy(param.Id)
	if err != nil {
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	records, count := param.GetHistory(param.Paginator)

	return response.Success(rsp, map[string]interface{}{"Items": records, "TotalCount": count})

}

// StaffRecord 员工档案历史版本对应的员工档案记录
func (sav *StaffArchiveVersion) StaffRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return VersionRecordData(req, rsp)
}

func (sav *StaffArchiveVersion) Export(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return VersionRecordData(req, rsp)
}
