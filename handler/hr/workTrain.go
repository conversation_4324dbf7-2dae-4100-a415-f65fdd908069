package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"strings"
	"time"
)

type WorkTrain struct {
	hrModel.WorkTrain
	StaffIds       []int64         `json:"StaffIds"`
	CorporationIds []int64         `json:"CorporationIds"`
	Keyword        string          `json:"Keyword"`
	StartAt        model.LocalTime `json:"StartAt"`
	EndAt          model.LocalTime `json:"EndAt"`
	Result         int64           `json:"Result"`
	IsAttend       int64           `json:"IsAttend"`
	model.Paginator
}

// List 培训列表
func (wt *WorkTrain) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkTrain
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	param.CorporationIds = service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	workTrains, count := param.WorkTrain.GetBy(topCorporationId, param.CorporationIds, param.Keyword, time.Time(param.StartAt), time.Time(param.EndAt), param.Paginator)

	return response.Success(rsp, map[string]interface{}{"Items": workTrains, "TotalCount": count, "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

type StaffHasWorkTrain struct {
	StaffId         int64  `json:"StaffId"`
	StaffArchiveId  int64  `json:"StaffArchiveId"`
	StaffName       string `json:"StaffName"`
	JobNumber       string `json:"JobNumber"`
	CorporationName string `json:"CorporationName"`
	JobStatus       int64  `json:"JobStatus"`
	WorkTrainCount  int64  `json:"WorkTrainCount"`
	AttendCount     int64  `json:"AttendCount"`
}

// StaffTrainList 人员培训列表
func (wt *WorkTrain) StaffTrainList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkTrain
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.CorporationIds = service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	var oetWhere service.OetWhere
	if param.Keyword != "" {
		oetWhere.Keyword = param.Keyword
	}

	oetStaffMap, staffIds, _ := service.SelectOetStaffByMultiWhere(ctx, param.CorporationIds, oetWhere, false, 0, 0)

	var workTrainStaff hrModel.WorkTrainHasStaff
	workTrainHasStaffs, count := workTrainStaff.GetStaffId(staffIds, param.Paginator)

	var staffWorkTrains []StaffHasWorkTrain
	for i := range workTrainHasStaffs {
		staff := oetStaffMap[workTrainHasStaffs[i].StaffId]
		var staffWorkTrain StaffHasWorkTrain

		staffWorkTrain.StaffArchiveId = workTrainHasStaffs[i].StaffArchiveId
		corporation := rpc.GetCorporationById(ctx, staff.CorporationId)
		if corporation != nil {
			staffWorkTrain.CorporationName = corporation.Name
		}
		staffWorkTrain.StaffId = staff.Id
		staffWorkTrain.StaffName = staff.Name
		staffWorkTrain.JobNumber = staff.StaffId
		staffWorkTrain.JobStatus = staff.WorkingState

		staffWorkTrain.WorkTrainCount = workTrainStaff.GetWorkTrainCount(staff.Id)
		staffWorkTrain.AttendCount = workTrainStaff.GetAttendCount(staff.Id)

		staffWorkTrains = append(staffWorkTrains, staffWorkTrain)
	}

	return response.Success(rsp, map[string]interface{}{"Items": staffWorkTrains, "TotalCount": count})
}

// AttendStaff 培训参加的人员管理
func (wt *WorkTrain) AttendStaff(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkTrain
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	var workTrainStaff hrModel.WorkTrainHasStaff
	workTrainStaffs := workTrainStaff.GetWorkTrainStaff(param.Id, param.Result)

	var records []hrModel.WorkTrainStaffApi
	for i := range workTrainStaffs {
		staff := rpc.GetStaffWithId(ctx, workTrainStaffs[i].StaffId)
		if staff == nil {
			continue
		}
		if param.Keyword != "" && !strings.Contains(staff.Name, param.Keyword) && !strings.Contains(staff.StaffId, param.Keyword) {
			continue
		}

		workTrainStaffs[i].StaffName = staff.Name
		workTrainStaffs[i].JobNumber = staff.StaffId
		workTrainStaffs[i].JobStatus = staff.WorkingState

		corporation := rpc.GetCorporationById(ctx, staff.CorporationId)
		if corporation != nil {
			workTrainStaffs[i].CorporationName = corporation.Name
		}

		records = append(records, workTrainStaffs[i])
	}

	return response.Success(rsp, records)

}

// Create 新增培训
func (wt *WorkTrain) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkTrain
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	if len(param.CorporationIds) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	for i := range param.StaffIds {
		var archive hrModel.StaffArchive
		err := archive.FindByStaffId(param.StaffIds[i])
		if err != nil {
			continue
		}
		param.WorkTrainStaffs = append(param.WorkTrainStaffs, hrModel.WorkTrainHasStaff{
			StaffId:        param.StaffIds[i],
			StaffArchiveId: archive.Id,
		})
	}
	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()
	for i := range param.CorporationIds {
		corporation := rpc.GetCorporationById(ctx, param.CorporationIds[i])
		if corporation != nil {
			param.WorkTrainCorporations = append(param.WorkTrainCorporations, hrModel.WorkTrainHasCorporation{
				CorporationId:   corporation.Id,
				CorporationName: corporation.Name,
			})
		}
	}

	err = param.WorkTrain.Create()
	if err != nil {
		log.ErrorFields("WorkTrain.Create fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

// Edit 编辑培训
func (wt *WorkTrain) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkTrain
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	if len(param.CorporationIds) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	for i := range param.CorporationIds {
		corporation := rpc.GetCorporationById(ctx, param.CorporationIds[i])
		if corporation != nil {
			param.WorkTrainCorporations = append(param.WorkTrainCorporations, hrModel.WorkTrainHasCorporation{
				CorporationId:   corporation.Id,
				CorporationName: corporation.Name,
			})
		}
	}

	err = param.WorkTrain.Update()
	if err != nil {
		log.ErrorFields("WorkTrain.Update fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

// AddAttendStaff 添加培训人员
func (wt *WorkTrain) AddAttendStaff(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkTrain
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	err = param.WorkTrain.FindById(param.Id)
	if err != nil {
		log.ErrorFields("WorkTrain.FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	for i := range param.StaffIds {
		var archive hrModel.StaffArchive
		err := archive.FindByStaffId(param.StaffIds[i])
		if err != nil {
			continue
		}
		param.WorkTrainStaffs = append(param.WorkTrainStaffs, hrModel.WorkTrainHasStaff{
			WorkTrainId:    param.WorkTrain.Id,
			StaffId:        param.StaffIds[i],
			StaffArchiveId: archive.Id,
		})
	}

	var workTrainStaff hrModel.WorkTrainHasStaff
	err = workTrainStaff.Create(param.WorkTrainStaffs)
	if err != nil {
		log.ErrorFields("workTrainStaff.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

// EditAttendStaff 批量编辑培训人员
func (wt *WorkTrain) EditAttendStaff(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkTrain
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if len(param.StaffIds) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var workTrainStaff hrModel.WorkTrainHasStaff
	err = workTrainStaff.UpdateResult(param.Id, param.StaffIds, param.Result, param.IsAttend)

	if err != nil {
		log.ErrorFields("workTrainStaff.UpdateResult error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

// DeleteAttendStaff 删除培训人员
func (wt *WorkTrain) DeleteAttendStaff(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkTrain
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if len(param.StaffIds) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var workTrainStaff hrModel.WorkTrainHasStaff
	err = workTrainStaff.DeleteStaff(param.Id, param.StaffIds)

	if err != nil {
		log.ErrorFields("workTrainStaff.DeleteStaff error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, nil)
}
