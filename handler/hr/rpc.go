package hr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	erpProto "app/org/scs/erpv2/api/proto/rpc/erp"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"errors"
	"time"
)

type RpcWorkPost struct {
}

func (rwp *RpcWorkPost) ListByCorporationIds(ctx context.Context, req *erpProto.ListByCorporationIdsRequest, rsp *erpProto.ListByCorporationIdsResponse) error {
	log.PrintFields("RpcWorkPostList Req", map[string]interface{}{"req": req})
	if len(req.CorporationIds) == 0 {
		return errors.New("PARAMS MISSING")
	}

	var workPost hrModel.WorkPost
	workPosts, _ := workPost.GetBy(req.CorporationIds, "", 0, WorkPostIsValidStatus, model.Paginator{Limit: 0})

	for i := range workPosts {
		corporationId, _ := workPosts[i].GetCorporation()
		if util.IncludeInt64(req.CorporationIds, corporationId) {
			rsp.Items = append(rsp.Items, &erpProto.WorkPostItem{
				Id:            workPosts[i].Id,
				Name:          workPosts[i].Name,
				Type:          workPosts[i].Type,
				Attr:          workPosts[i].Attr,
				Status:        workPosts[i].Status,
				Code:          workPosts[i].Code,
				CorporationId: corporationId,
			})
		}
	}
	rsp.TotalCount = int64(len(rsp.Items))

	return nil
}

func (rwp *RpcWorkPost) GetErpWorkPostsByStaffIds(ctx context.Context, req *erpProto.GetErpWorkPostsByStaffIdsRequest, rsp *erpProto.GetErpWorkPostsByStaffIdResponse) error {
	var staffIds = req.StaffIds

	if len(staffIds) == 0 {
		return nil
	}

	tmp := make(map[int64][]int64)       // map[staffId]{workPostIds}
	tmpNames := make(map[int64][]string) // map[staffId]{workPostNames}

	tmpPostName := make(map[int64]hrModel.WorkPost) // map[岗位id]

	// 获取所有岗位
	pws, _ := (&hrModel.WorkPost{}).GetBy(nil, "", 0, WorkPostIsValidStatus, model.Paginator{}) //

	for _, pw := range pws {
		tmpPostName[pw.WorkPost.Id] = pw.WorkPost
	}

	// sql IN staffIds
	posts := (&hrModel.StaffHasWorkPost{}).GetByStaffIds(staffIds)

	for _, post := range posts {

		//
		if post.WorkPostId == 0 {
			continue
		}

		// 过滤失效岗位
		if wp, ok := tmpPostName[post.WorkPostId]; ok {

			tmp[post.StaffId] = append(tmp[post.StaffId], post.WorkPostId)
			tmpNames[post.StaffId] = append(tmpNames[post.StaffId], wp.Name)
		}

	}

	for _, staffId := range staffIds {

		erpWorkPost := &erpProto.ErpWorkPost{
			StaffId:       staffId,
			WorkPostIds:   tmp[staffId],
			WorkPostNames: tmpNames[staffId],
		}

		rsp.Items = append(rsp.Items, erpWorkPost)
	}

	return nil
}

//func RpcWorkPostList(ctx context.Context, req *erpProto.ListByCorporationIdsRequest, rsp *erpProto.ListByCorporationIdsResponse) error {
//	log.PrintFields("RpcWorkPostList Req", map[string]interface{}{"req": req})
//
//	if len(req.CorporationIds) == 0 {
//		return errors.New("PARAMS MISSING")
//	}
//
//	var workPost hr.WorkPost
//	workPosts, _ := workPost.GetBy(req.CorporationIds, "", 0, 0, model.Paginator{Limit: 0})
//
//	for i := range workPosts {
//		corporationId, _ := workPosts[i].GetCorporation()
//		if util.IncludeInt64(req.CorporationIds, corporationId) {
//			rsp.Items = append(rsp.Items, &erpProto.WorkPostItem{
//				Id:            workPosts[i].Id,
//				Name:          workPosts[i].Name,
//				Type:          workPosts[i].Type,
//				Attr:          workPosts[i].Attr,
//				Status:        workPosts[i].Status,
//				Code:          workPosts[i].Code,
//				CorporationId: corporationId,
//			})
//		}
//	}
//	rsp.TotalCount = int64(len(rsp.Items))
//
//	return nil
//}

type RpcLeave struct {
}

func (rl *RpcLeave) AddErpStaffLeave(ctx context.Context, req *erpProto.AddStaffLeaveRequest, rsp *erpProto.AddStaffLeaveResponse) error {
	if req.StaffId == 0 || req.StartAt <= 0 || req.EndAt <= 0 {
		return errors.New("PARAMS MISSING")
	}

	staffInfo := rpc.GetStaffWithId(ctx, req.StaffId)
	if staffInfo == nil {
		rsp.Code = response.DbQueryFail
		return nil
	}

	var record = hrModel.ApplyLeaveRecord{
		StaffId:     req.StaffId,
		StartAt:     model.LocalTime(time.Unix(req.StartAt, 0)),
		EndAt:       model.LocalTime(time.Unix(req.EndAt, 0)),
		LeaveReason: req.LeaveReason,
		LeaveType:   req.LeaveType,
		JobNumber:   staffInfo.StaffId,
		StaffName:   staffInfo.Name,
	}

	//如果请假类型为年休假  则需要判断年休假天数是否够用
	if record.LeaveType == util.LeaveTypeForAnnualLeave {
		leaveManagement := (&hrModel.LeaveManagement{}).GetStaffLeaveByStatus(record.StaffId, util.StatusForTrue)
		dayCount := int64(time.Time(record.EndAt).Sub(time.Time(record.StartAt)).Hours()/24) + 1

		if leaveManagement.TotalDay-leaveManagement.UsedDay-dayCount < 0 {
			rsp.Code = response.LeaveDayCountOverEnableDay
			rsp.Msg = response.MsgMap[response.LeaveDayCountOverEnableDay].Message
			return nil
		}
	}

	authUser := auth.NewUserByPhone(staffInfo.TopCorporationId, staffInfo.Phone)
	if authUser.Id == 0 {
		rsp.Code = response.DbQueryFail
		return nil
	}

	_, err := StaffLeaveCreate(authUser, record, staffInfo.CorporationId, "ipoc")
	if err != nil {
		rsp.Code = response.FAIL
		return nil
	}

	rsp.Code = "0"
	rsp.Msg = "成功"
	return nil
}

func (rl *RpcLeave) GetErpStaffLeaveInfo(ctx context.Context, req *erpProto.GetErpStaffLeaveInfoRequest, rsp *erpProto.GetErpStaffLeaveInfoResponse) error {
	log.PrintFields("GetErpStaffLeaveInfo Req", map[string]interface{}{"req": req})

	if req.StaffId == 0 {
		return errors.New("PARAMS MISSING")
	}

	var (
		now = time.Now()
		s   = time.Date(now.Year()-1, 12, 26, 0, 0, 0, 0, time.Local)
		e   = time.Date(now.Year(), 12, 25, 0, 0, 0, 0, time.Local)
	)

	var applyLeaveRecord hrModel.ApplyLeaveRecord
	records := applyLeaveRecord.GetAllBy([]int64{req.StaffId}, 0, s, e)

	var results = make([]*erpProto.ApplyLeaveRecord, len(records))
	for k, v := range records {
		var result = &erpProto.ApplyLeaveRecord{
			Id:          v.Id,
			StartAt:     time.Time(v.StartAt).Unix(),
			EndAt:       time.Time(v.EndAt).Unix(),
			CreatedAt:   time.Time(v.CreatedAt).Format(model.TimeFormat),
			LeaveType:   v.LeaveType,
			ApplyStatus: v.ApplyStatus,
			DayCount:    v.DayCount,
		}
		results[k] = result
	}

	staffAnnualLeave := (&hrModel.LeaveManagement{}).GetStaffLeaveByStatus(req.StaffId, util.StatusForTrue)

	rsp.HasAnnualLeaveDay = staffAnnualLeave.TotalDay - staffAnnualLeave.UsedDay

	rsp.StaffId = req.StaffId
	rsp.StaffName = rpc.GetStaffNameWithId(ctx, req.StaffId)

	rsp.Items = results

	return nil
}

type RpcHr struct {
}

func (rpc *RpcHr) GetErpDriverMigrationRecord(ctx context.Context, req *erpProto.GetErpDriverMigrationRecordRequest, rsp *erpProto.GetErpDriverMigrationRecordResponse) error {
	var startAt = time.Time{}
	var endAt = time.Time{}
	if req.StartUseAt > 0 {
		startAt = time.Unix(req.StartUseAt, 0)
	}
	if req.EndUseAt > 0 {
		endAt = time.Unix(req.EndUseAt, 0)
	}

	records := (&hrModel.DriverMigration{}).GetByAllForRpc(req.CorporationId, req.DriverIds, req.ApplyStatuses, startAt, endAt)
	var items []*erpProto.DriverMigrationRecord
	for i := range records {
		var item = erpProto.DriverMigrationRecord{
			Id:               records[i].Id,
			Code:             records[i].Code,
			OutCorporationId: records[i].OutCorporationId,
			InCorporationId:  records[i].InCorporationId,
			CreatedAtUnix:    records[i].CreatedAt.ToTime().Unix(),
			UseAtUnix:        records[i].UseAt.ToTime().Unix(),
			ApplyStatus:      records[i].ApplyStatus,
			InFleetUserId:    records[i].InFleetUserId,
			Reason:           records[i].Reason,
		}

		//查询流程
		var process processModel.LbpmApplyProcess
		err := process.GetProcessByItemId(records[i].Id, records[i].TableName())
		if err == nil {
			if process.DoneAt != nil {
				item.HandleAtUnix = process.DoneAt.ToTime().Unix()
			} else {
				item.HandleAtUnix = process.UpdatedAt.ToTime().Unix()
			}
		}

		var drivers []*erpProto.MigrationDriverItem
		for j := range records[i].Records {
			drivers = append(drivers, &erpProto.MigrationDriverItem{
				DriverId: records[i].Records[j].DriverId,
				InLineId: records[i].Records[j].InLineId,
			})
		}
		item.Drivers = drivers
		items = append(items, &item)

	}
	rsp.Items = items
	rsp.Code = response.SUCCESS
	rsp.Msg = response.MsgMap[rsp.Code].Message
	return nil
}

func (rpc *RpcHr) GetErpDriverMigrationInfo(ctx context.Context, req *erpProto.GetErpDriverMigrationInfoRequest, rsp *erpProto.GetErpDriverMigrationInfoResponse) error {
	record := (&hrModel.DriverMigration{}).FirstBy(req.Id)
	var item = &erpProto.DriverMigrationRecord{
		Id:               record.Id,
		Code:             record.Code,
		OutCorporationId: record.OutCorporationId,
		InCorporationId:  record.InCorporationId,
		CreatedAtUnix:    record.CreatedAt.ToTime().Unix(),
		UseAtUnix:        record.UseAt.ToTime().Unix(),
		ApplyStatus:      record.ApplyStatus,
		InFleetUserId:    record.InFleetUserId,
		Reason:           record.Reason,
	}

	//查询流程
	var process processModel.LbpmApplyProcess
	err := process.GetProcessByItemId(record.Id, record.TableName())
	if err == nil {
		if process.DoneAt != nil {
			item.HandleAtUnix = process.DoneAt.ToTime().Unix()
		} else {
			item.HandleAtUnix = process.UpdatedAt.ToTime().Unix()
		}
	}

	var drivers []*erpProto.MigrationDriverItem
	for j := range record.Records {
		drivers = append(drivers, &erpProto.MigrationDriverItem{
			DriverId: record.Records[j].DriverId,
			InLineId: record.Records[j].InLineId,
		})
	}

	item.Drivers = drivers

	rsp.Item = item
	rsp.Code = response.SUCCESS
	rsp.Msg = response.MsgMap[rsp.Code].Message
	return nil
}
