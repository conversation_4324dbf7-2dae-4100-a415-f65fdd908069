package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"gorm.io/gorm"
	"strconv"
	"time"
)

type RefererReward struct {
	hrModel.RefererReward

	RefererRewardId  int64   `json:"RefererRewardId"`
	RefererRewardIds []int64 `json:"RefererRewardIds"`
	model.Paginator
	CorporationIds []int64 `json:"CorporationIds"`
	OpUserName     string  `json:"OpUserName"`

	StartAt model.LocalTime `json:"StartAt"`
	EndAt   model.LocalTime `json:"EndAt"`
}

// 推荐奖励数据新增
func (rr *RefererReward) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param RefererReward
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if len(param.Topic) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	var now = time.Now()

	var rrd hrModel.RefererReward
	err = rrd.SelectLatest(now)
	if err != nil {
		log.ErrorFields("rrd.SelectLatest error", map[string]interface{}{"err": err})
	}

	param.SerialNumber = GetSerialNumber(now, rrd.SerialNumber)

	corporationItem := rpc.GetCorporationDetailById(ctx, auth.User(ctx).GetCorporationId())
	if corporationItem == nil {
		log.ErrorFields("rpc.GetCorporationDetailById is nil", map[string]interface{}{"CorporationId": auth.User(ctx).GetCorporationId()})
		return response.Error(rsp, response.FAIL)
	}

	param.OpFleetId = corporationItem.FleetId
	param.OpDepartmentId = corporationItem.DepartmentId
	param.OpBranchId = corporationItem.BranchId
	param.OpCompanyId = corporationItem.CompanyId
	param.OpGroupId = corporationItem.GroupId
	param.ApplyStatus = util.ApplyStatusForDoing

	param.ParseOpUser(ctx)
	param.RefererReward.SumPeople = int64(len(param.RefererReward.Records))

	if len(param.RefererReward.Records) > 0 {
		// 第一,判断是否重复
		for indexA, valueA := range param.RefererReward.Records {
			for indexB, valueB := range param.RefererReward.Records {
				if indexA == indexB {
					continue
				}

				if valueA.StaffId == valueB.StaffId {
					oetStaffR := rpc.GetStaffWithId(ctx, valueA.StaffId)
					return response.ErrorWithMsg(rsp, "OP8003", fmt.Sprintf("\"%v\"，引荐名单重复", oetStaffR.Name))
				}
			}
		}

		for index, _ := range param.RefererReward.Records {
			var record = &param.RefererReward.Records[index]

			param.RefererReward.SumMoney += record.RewardMoney

			// 查询提交人
			oetStaffR := rpc.GetStaffWithId(ctx, record.StaffId)
			if oetStaffR == nil {
				log.ErrorFields("rpc.GetStaffWithId is nil", map[string]interface{}{"staffId": record.StaffId})
				return response.Error(rsp, response.FAIL)
			}

			// 入职时间必须大于6个月
			if oetStaffR.RegisterTime == 0 || now.Unix()-oetStaffR.RegisterTime < 182*24*60*60 {
				log.ErrorFields("rpc.GetStaffWithId is nil", map[string]interface{}{"staffId": record.StaffId})
				return response.ErrorWithMsg(rsp, "OP8001", fmt.Sprintf("\"%v\"，入职未满6个月，无法提交引荐奖励", oetStaffR.Name))
			}

			var rri hrModel.RefererRewardItem
			err = rri.GetByStaffId(record.StaffId)
			if err != nil {
				if err == gorm.ErrRecordNotFound {

				} else {
					return response.ErrorWithMsg(rsp, "OP8002", fmt.Sprintf("\"%v\"，已提交引荐人奖励，请勿重复提交", oetStaffR.Name))
				}
			}

			if rri.Id > 0 {
				return response.ErrorWithMsg(rsp, "OP8002", fmt.Sprintf("\"%v\"，已提交引荐人奖励，请勿重复提交", oetStaffR.Name))
			}

			record.JoinCompanyAt = model.LocalTime(time.Unix(oetStaffR.RegisterTime, 0))
			record.StaffWorkPostId = util.MasterToErp[oetStaffR.Occupation]

			record.DrivingModel = oetStaffR.DrvLicenseTypeStr

			corporationRItem := rpc.GetCorporationDetailById(ctx, oetStaffR.CorporationId)
			if corporationRItem == nil {
				log.ErrorFields("rpc.GetCorporationDetailById is nil", map[string]interface{}{"CorporationId": oetStaffR.CorporationId})
				return response.Error(rsp, response.FAIL)
			}

			record.FleetId = corporationRItem.FleetId
			record.DepartmentId = corporationRItem.DepartmentId
			record.BranchId = corporationRItem.BranchId
			record.CompanyId = corporationRItem.CompanyId
			record.GroupId = corporationRItem.GroupId
		}
	}

	tx := model.DB().Begin()
	err = param.RefererReward.Create(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("param.RefererReward.Create is error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	var processTitle = fmt.Sprintf("[%v]的审批", param.Topic)

	if config.Config.Lbpm.Enable {
		byteParam, _ := json.Marshal(param.RefererReward)
		var formData = make(map[string]interface{}, 0)
		processId, err := processService.NewDispatchProcess(auth.User(ctx).GetUser(), config.RefererRewardFormTemplate, processTitle, param.RefererReward.Id, param.RefererReward.TableName(), param.RefererReward.ApplyStatusFieldName(), string(byteParam), formData)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("processService.NewDispatchProcess is error", map[string]interface{}{"err": err})
			return err
		}
		log.ErrorFields("processId: ", map[string]interface{}{"processId": processId})
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

func GetSerialNumber(now time.Time, serialNumber int64) int64 {
	var (
		newSerialNumber int64
		numberStr       string
	)
	if serialNumber == 0 {
		numberStr = "0001"
	} else {
		var oldNumberStr = fmt.Sprintf("%v", serialNumber)[8:]
		number, _ := strconv.ParseInt(oldNumberStr, 10, 64)
		numberStr = StringMakeUpPosition(fmt.Sprintf("%v", number+1), 4)
	}

	var newSerialNumberStr = fmt.Sprintf("%v%v", now.Format("20060102"), numberStr)
	newSerialNumber, _ = strconv.ParseInt(newSerialNumberStr, 10, 64)

	return newSerialNumber

}

func StringMakeUpPosition(str string, n int) string {
	var lessLength = n - len(str)
	for i := 0; i < lessLength; i++ {
		str = fmt.Sprintf("%v%v", "0", str)
	}
	return str
}

func (rr *RefererReward) Update(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param RefererReward
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	return response.Success(rsp, nil)
}

type RefererRewardDetail struct {
	hrModel.RefererReward
	CorporationName        string `json:"CorporationName" gorm:"-"`        // 发起人机构
	CurrentHandlerUserName string `json:"CurrentHandlerUserName" gorm:"-"` //当前处理人
}

// 推荐奖励列表
func (rr *RefererReward) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param RefererReward
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)
	items, totalCount, err := param.RefererReward.List(param.Topic, param.OpUserName, corporationIds, time.Time(param.StartAt), time.Time(param.EndAt), param.Paginator)
	if err != nil {
		log.ErrorFields("param.RefererReward.List error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var retItems = make([]RefererRewardDetail, len(items))

	for i := 0; i < len(items); i++ {
		retItems[i].RefererReward = items[i]
		if items[i].OpFleetId > 0 {
			retItems[i].CorporationName = rpc.GetCorporationNameById(ctx, items[i].OpFleetId)
		} else if items[i].OpDepartmentId > 0 {
			retItems[i].CorporationName = rpc.GetCorporationNameById(ctx, items[i].OpDepartmentId)
		} else if items[i].OpBranchId > 0 {
			retItems[i].CorporationName = rpc.GetCorporationNameById(ctx, items[i].OpBranchId)
		} else if items[i].OpCompanyId > 0 {
			retItems[i].CorporationName = rpc.GetCorporationNameById(ctx, items[i].OpCompanyId)
		} else if items[i].OpGroupId > 0 {
			retItems[i].CorporationName = rpc.GetCorporationNameById(ctx, items[i].OpGroupId)
		}

		if items[i].ApplyStatus != util.ApplyStatusForDone {
			var p processModel.LbpmApplyProcess
			p.GetProcessByItemId(items[i].Id, rr.RefererReward.TableName())

			retItems[i].CurrentHandlerUserName = p.CurrentHandlerUserName
		}

		retItems[i].SumPeople, retItems[i].SumMoney = (&hrModel.RefererRewardItem{}).Aggs(items[i].Id)

	}

	data := map[string]interface{}{
		"Items":      retItems,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)
}

type RefererRewardRecordItem struct {
	hrModel.RefererRewardItem
	CaptainStaffName  string `json:"CaptainStaffName"`
	Age               int64  `json:"Age"`
	StaffWorkPostName string `json:"StaffWorkPostName"`
	StaffName         string `json:"StaffName"`
	StaffIdentityId   string `json:"StaffIdentityId"`
	RefererStaffName  string `json:"RefererStaffName"`
	RefererIdentityId string `json:"RefererIdentityId"`

	Gender int64 `json:"Gender" gorm:"-"`
}

// 推荐人列表
func (rr *RefererReward) Records(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param RefererReward
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.RefererRewardId == 0 {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var (
		rri hrModel.RefererRewardItem
	)

	var rrT hrModel.RefererReward
	err = rrT.GetById(param.RefererRewardId)
	if err != nil {
		log.ErrorFields("GetById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	items, totalCount, err := rri.ListByRefererRewardId(param.RefererRewardId, param.Paginator)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	var (
		retItems = make([]RefererRewardRecordItem, len(items))
	)

	for i := 0; i < len(items); i++ {
		retItems[i].RefererRewardItem = items[i]

		var staffItem = rpc.GetStaffWithId(ctx, items[i].StaffId)
		if staffItem != nil {
			retItems[i].StaffName = staffItem.Name
			if len(staffItem.IdentifyId) > 0 {
				parse, err2 := time.Parse("20060102", string([]rune(staffItem.IdentifyId)[6:14]))
				if err2 == nil {
					retItems[i].Age = int64((time.Now().Sub(parse).Hours()) / (24 * 365))
				}
			}
			if staffItem.Sex {
				retItems[i].Gender = util.Male
			} else {
				retItems[i].Gender = util.Female
			}
			retItems[i].DrivingModel = staffItem.DrvLicenseTypeStr
			retItems[i].StaffIdentityId = staffItem.IdentifyId
		}

		retItems[i].Fleet = rpc.GetCorporationNameById(ctx, items[i].FleetId)
		retItems[i].CaptainStaffName = rpc.GetStaffNameWithId(ctx, items[i].CaptainStaffId)
		retItems[i].StaffWorkPostName = util.ErpWorkPostTypeMap[retItems[i].StaffWorkPostId]

		var refererStaffItem = rpc.GetStaffWithId(ctx, items[i].RefererStaffId)
		if refererStaffItem != nil {
			retItems[i].RefererStaffName = refererStaffItem.Name
			retItems[i].RefererIdentityId = refererStaffItem.IdentifyId
		}

	}

	data := map[string]interface{}{
		"Item":       rrT,
		"Items":      retItems,
		"TotalCount": totalCount,
	}
	return response.Success(rsp, data)
}

func (rr *RefererReward) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param RefererReward
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	return response.Success(rsp, nil)
}

func (rr *RefererReward) Export(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param RefererReward
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)
	refererRewardItems, _, err := param.RefererReward.List(param.Topic, param.OpUserName, corporationIds, time.Time(param.StartAt), time.Time(param.EndAt), model.Paginator{
		Offset: 0,
		Limit:  99999,
	})
	if err != nil {
		log.ErrorFields("param.RefererReward.List error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	for i := 0; i < len(refererRewardItems); i++ {
		param.RefererRewardIds = append(param.RefererRewardIds, refererRewardItems[i].Id)
	}

	var (
		rri hrModel.RefererRewardItem
	)

	items, totalCount, err := rri.ListByRefererRewardIds(param.RefererRewardIds, param.Paginator)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	var (
		retItems = make([]RefererRewardRecordItem, len(items))
	)

	for i := 0; i < len(items); i++ {
		retItems[i].RefererRewardItem = items[i]
		var staffItem = rpc.GetStaffWithId(ctx, items[i].StaffId)
		if staffItem != nil {
			retItems[i].StaffName = staffItem.Name
			if len(staffItem.IdentifyId) > 0 {
				parse, err2 := time.Parse("20060102", string([]rune(staffItem.IdentifyId)[6:14]))
				if err2 == nil {
					retItems[i].Age = int64((time.Now().Sub(parse).Hours()) / (24 * 365))
				}
			}
			if staffItem.Sex {
				retItems[i].Gender = util.Male
			} else {
				retItems[i].Gender = util.Female
			}

			retItems[i].DrivingModel = staffItem.DrvLicenseTypeStr
			retItems[i].StaffIdentityId = staffItem.IdentifyId
		}

		retItems[i].Fleet = rpc.GetCorporationNameById(ctx, items[i].FleetId)
		retItems[i].CaptainStaffName = rpc.GetStaffNameWithId(ctx, items[i].CaptainStaffId)
		retItems[i].StaffWorkPostName = util.ErpWorkPostTypeMap[retItems[i].StaffWorkPostId]

		var refererStaffItem = rpc.GetStaffWithId(ctx, items[i].RefererStaffId)
		if refererStaffItem != nil {
			retItems[i].RefererStaffName = refererStaffItem.Name
			retItems[i].RefererIdentityId = refererStaffItem.IdentifyId
		}
	}

	data := map[string]interface{}{
		"Items":      retItems,
		"TotalCount": totalCount,
	}
	return response.Success(rsp, data)
}

type StaffRecordParam struct {
	StaffId int64
}

type StaffRecordItem struct {
	RefererRewardRecordItem
	Topic string `json:"Topic"`
}

// 查询被推荐员工是否存在
func (rr *RefererReward) StaffRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffRecordParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.StaffId == 0 {
		log.ErrorFields("param.StaffId is 0", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var rri hrModel.RefererRewardItem

	err = rri.GetByStaffId(param.StaffId)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			data := map[string]interface{}{
				"Item": nil,
			}
			return response.Success(rsp, data)
		} else {
			log.ErrorFields("rri.GetByStaffId error ", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	var sri StaffRecordItem
	sri.RefererRewardItem = rri

	var staffItem = rpc.GetStaffWithId(ctx, sri.StaffId)
	if staffItem != nil {
		sri.StaffName = staffItem.Name
		if len(staffItem.IdentifyId) > 0 {
			parse, err2 := time.Parse("20060102", string([]rune(staffItem.IdentifyId)[6:14]))
			if err2 == nil {
				sri.Age = int64((time.Now().Sub(parse).Hours()) / (24 * 365))
			}
		}
		if staffItem.Sex {
			sri.Gender = util.Male
		} else {
			sri.Gender = util.Female
		}
		sri.DrivingModel = staffItem.DrvLicenseTypeStr
		sri.StaffIdentityId = staffItem.IdentifyId
	}

	sri.Fleet = rpc.GetCorporationNameById(ctx, rri.FleetId)
	sri.CaptainStaffName = rpc.GetStaffNameWithId(ctx, rri.CaptainStaffId)
	sri.StaffWorkPostName = util.ErpWorkPostTypeMap[rri.StaffWorkPostId]

	var refererStaffItem = rpc.GetStaffWithId(ctx, rri.RefererStaffId)
	if refererStaffItem != nil {
		sri.RefererStaffName = refererStaffItem.Name
		sri.RefererIdentityId = refererStaffItem.IdentifyId
	}

	err = rr.GetById(rri.RefererRewardId)
	if err != nil {
		log.ErrorFields("rri.GetByStaffId error ", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	sri.Topic = rr.Topic

	data := map[string]interface{}{
		"Item": sri,
	}
	return response.Success(rsp, data)

}
