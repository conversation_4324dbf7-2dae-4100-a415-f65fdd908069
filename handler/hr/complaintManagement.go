package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	workOrder "app/org/scs/erpv2/api/model/common"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	go_api "github.com/micro/go-micro/v2/api/proto"
)

type ComplaintManagementHandler struct {
	CorporationIds []int64 `json:"CorporationIds"`
	Line           []int64 `json:"Line" qsField:"LineId" qs:"IN"`
	DriverId       []int64 `json:"DriverId" qs:"IN"`
	License        string  `json:"License" qs:"LIKE"`                    //车牌
	OrderType      int64   `json:"OrderType" qs:"="`                     //投诉分类
	LevelOne       []int64 `json:"LevelOne" qs:"IN"`                     // 一级分类dddd
	LevelTwo       []int64 `json:"LevelTwo" qs:"IN"`                     // 二级分类
	FromDictId     int64   `json:"FromDictId" qs:"="`                    // 数据来源id
	IssueStatus    int64   `json:"IssueStatus" qs:"="`                   // 下发状态
	Status         int64   `json:"Status" qs:"="`                        // 处理情况
	StartTime      string  `json:"StartTime" qsField:"CallTime" qs:">="` // 日期YYYY-MM-DD
	EndTime        string  `json:"EndTime" qsField:"CallTime" qs:"<="`   // 日期YYYY-MM-DD
	model.Paginator
}

func (m *ComplaintManagementHandler) List(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var form ComplaintManagementHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	qs := model.NewQs(form)
	//corporationIds := service.AuthCorporationIdProvider(ctx, form.CorporationIds)
	list, totalCount, err := (&hrModel.ComplaintManagement{}).List(qs, form.CorporationIds, form.Paginator)
	if err != nil {
		log.ErrorFields("ComplaintManagement List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if list != nil {
		for index := range list {
			var process processModel.LbpmApplyProcess
			err = process.GetProcessByItemId(list[index].PetitionWorkOrderId, (&workOrderModel.PetitionWorkOrder{}).TableName())
			list[index].CurrentHandler = process.CurrentHandlerUserName
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (m *ComplaintManagementHandler) Add(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var form hrModel.ComplaintManagement
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	fromDict := (&workOrder.Dict{}).FirstById(form.FromDictId)
	form.FromDictKey = fromDict.DictKey

	LevelOneDict := (&workOrder.Dict{}).FirstById(form.LevelOne)
	form.LevelOneKey = LevelOneDict.DictKey

	LevelTwoDict := (&workOrder.Dict{}).FirstById(form.LevelTwo)
	form.LevelTwoKey = LevelTwoDict.DictKey

	user := auth.User(ctx).GetUser()
	form.OpUserId = user.Id
	form.OpUserName = user.Name
	form.IssueStatus = 1
	form.Build(form.FleetId)
	err := form.Create()
	if err != nil {
		log.ErrorFields("ComplaintManagement ADD error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

func (m *ComplaintManagementHandler) Edit(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var form hrModel.ComplaintManagement
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	fromDict := (&workOrder.Dict{}).FirstById(form.FromDictId)
	form.FromDictKey = fromDict.DictKey

	LevelOneDict := (&workOrder.Dict{}).FirstById(form.LevelOne)
	form.LevelOneKey = LevelOneDict.DictKey

	LevelTwoDict := (&workOrder.Dict{}).FirstById(form.LevelTwo)
	form.LevelTwoKey = LevelTwoDict.DictKey
	form.Build(form.FleetId)
	err := form.Updates()
	if err != nil {
		log.ErrorFields("ComplaintManagement Updates error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

func (m *ComplaintManagementHandler) Delete(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var form hrModel.ComplaintManagement
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	var data hrModel.ComplaintManagement
	_ = data.GetById(form.Id)
	if data.IssueStatus == 2 {
		return response.Error(rsp, "数据已下发无法删除")
	}
	err := (&hrModel.ComplaintManagement{}).Delete(form.Id)
	if err != nil {
		log.ErrorFields("ComplaintManagement Delete error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbDeleteFail)
	}
	return response.Success(rsp, nil)
}

type IssueForm struct {
	ComplaintManagementIds []int64                          `json:"ComplaintManagementIds"`
	PetitionOrder          workOrderModel.PetitionWorkOrder `json:"PetitionOrder"`
}

func (m *ComplaintManagementHandler) Issue(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var form IssueForm
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if len(form.ComplaintManagementIds) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	var errRows []model.ExcelForm
	authUser := auth.User(ctx).GetUser()
	for index, complaintManagementId := range form.ComplaintManagementIds {
		var data hrModel.ComplaintManagement
		_ = data.GetById(complaintManagementId)
		if data.Id == 0 {
			errRows = append(errRows, model.ExcelForm{
				RowIndex: index + 1,
				Error:    "数据不存在",
			})
			continue
		}
		if data.IssueStatus == 2 {
			errRows = append(errRows, model.ExcelForm{
				RowIndex: index + 1,
				Error:    "数据已下发",
			})
			continue
		}
		var param workOrderModel.PetitionWorkOrder
		param.Corporations.Build(data.FleetId)
		param.OpUserName = authUser.Name
		param.OpUserId = authUser.Id
		param.FromDictId = data.FromDictId
		param.FromDictKey = data.FromDictKey
		param.LevenOneCateDictId = data.LevelOne
		param.LevenOneCateDictKey = data.LevelOneKey
		param.CateDictId = data.LevelTwo
		param.CateDictKey = data.LevelTwoKey
		param.LineId = data.LineId
		param.LineName = data.LineName
		param.UniqueNo = data.Code
		param.Petitioner = data.CallPeople
		param.PetitionerPhone = data.TelephoneNumber
		param.ReportAt = data.CallTime
		param.Content = data.Requirement
		param.ToCorporationId = form.PetitionOrder.ToCorporationId
		param.ApplyStatus = util.ApplyStatusForDoing
		toCorporation := rpc.GetCorporationById(ctx, param.ToCorporationId)
		if toCorporation != nil {
			param.ToCorporationName = toCorporation.Name
		}
		param.ToApplyUserId = form.PetitionOrder.ToApplyUserId
		ToUser := rpc.GetUserInfoById(ctx, param.ToApplyUserId)
		var applyUserAccount string
		var fleetCode string
		if ToUser != nil {
			param.ToApplyUserName = ToUser.Nickname
			applyUserAccount = ToUser.Username

			corporation := rpc.GetCorporationDetailById(ctx, ToUser.CorporationId)
			if corporation != nil {
				fleetCode = corporation.Item.Virtual
			}
		}
		tx := model.DB().Begin()
		err := param.Create(tx)
		if err != nil {
			tx.Rollback()
			errRows = append(errRows, model.ExcelForm{
				RowIndex: index + 1,
				Error:    "下发失败",
			})
			continue
		}
		data.IssueStatus = 2
		data.Status = 2
		data.PetitionWorkOrderId = param.Id
		data.PetitionWorkOrderCode = param.Code
		err = data.TxUpdates(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("complaint management.TxUpdates error", map[string]interface{}{"err": err})
			errRows = append(errRows, model.ExcelForm{
				RowIndex: index + 1,
				Error:    "下发失败",
			})
			continue
		}
		//发起流程审批
		if config.Config.Lbpm.Enable {
			byteParam, _ := json.Marshal(param)
			formData := map[string]interface{}{"ToApplyUserAccount": applyUserAccount, "IsAccept": util.StatusForTrue, "FleetCode": fleetCode}
			//发起新的流程
			processTitle := fmt.Sprintf("%s提交的审批/%s", authUser.Name, param.Code)
			_, err = processService.NewDispatchProcess(authUser, config.PetitionWorkOrderReportFormTemplate, processTitle, param.Id, param.TableName(), param.ApplyStatusFieldName(), string(byteParam), formData)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
				errRows = append(errRows, model.ExcelForm{
					RowIndex: index + 1,
					Error:    "发起流程失败",
				})
				continue
			}
		}
		tx.Commit()
	}
	return response.Success(rsp, errRows)
}
