package hr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	protoStaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"strconv"
)

// Insurance 社会保险
type Insurance struct {
	hrModel.InsuranceRecord
	StaffIds       []int64 `json:"StaffIds"`
	CorporationIds []int64 `json:"CorporationIds"`
	Keyword        string  `json:"Keyword"`
	StartYear      int     `json:"StartYear"`
	EndYear        int     `json:"EndYear"`
	model.Paginator
}

type SettingParam struct {
	Records []hrModel.InsuranceFundSetting
}

// Setting 社保缴纳比例设置
func (ins *Insurance) Setting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param SettingParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()

	for i := range param.Records {
		if err := util.Validator().Struct(param.Records[i]); err != nil {
			log.ErrorFields("records validate fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsMissing)
		}
		param.Records[i].Scene = util.SceneForInsurance
		param.Records[i].ParseOpUser(ctx)
		param.Records[i].TopCorporationId = topCorporationId
	}

	var setting hrModel.InsuranceFundSetting
	err = setting.Create(param.Records, util.SceneForInsurance)

	if err != nil {
		log.ErrorFields("setting.Create fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

// SettingInfo 社保缴纳比例设置的信息
func (ins *Insurance) SettingInfo(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var setting hrModel.InsuranceFundSetting
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	settings := setting.Get(topCorporationId, util.SceneForInsurance)
	return response.Success(rsp, settings)
}

// Import 社保导入
func (ins *Insurance) Import(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ImportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			response.Error(rsp, response.FAIL)
		}
		return
	}()

	// 通过根机构获取所有员工数据
	oetStaffs := rpc.GetStaffsWithOption(ctx, auth.User(ctx).GetTopCorporationId())
	oetStaffMap := make(map[string]*protoStaff.OetStaffItem)
	for i := range oetStaffs {
		if oetStaffs[i].IdentifyId != "" {
			oetStaffMap[oetStaffs[i].IdentifyId] = oetStaffs[i]
		}
	}

	// 0姓名* 1身份证 2月度 3养老基数  4单位比例  5个人比例  6医疗基数  7单位比例  8个人比例  9失业基数  10单位比例  11个人比例  12工伤基数  13单位比例  14个人比例
	// 15企业年金基数  16单位比例  17个人比例  18补充医疗基数  19单位比例  20个人比例
	sheet := excelFile.Sheets[0]
	var importFailStaffs []string
	var successCount int64
	var insurances []hrModel.InsuranceRecord

	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 4 {
			continue
		}

		if row.Cells[0].String() == "" || row.Cells[1].String() == "" || row.Cells[2].String() == "" || row.Cells[3].String() == "" {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}

		oetStaff, ok := oetStaffMap[row.Cells[1].String()]
		if !ok {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}
		var archive hrModel.StaffArchive
		err := archive.FindByStaffId(oetStaff.Id)

		if err != nil {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}
		var insurance hrModel.InsuranceRecord
		insurance.StaffArchiveId = archive.Id
		insurance.StaffId = oetStaff.Id
		insurance.TopCorporationId = topCorporationId
		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 2:
				insurance.AdjustDate, _ = row.Cells[2].Int64()
			case 3:
				base, err := row.Cells[3].Float()
				if err == nil {
					insurance.EndowmentBase = int64(base * 100) //转成单位：分
				}
			case 4:
				percent, err := row.Cells[4].Float()
				if err == nil {
					insurance.EndowmentCompanyRatio = int64(percent * 100)
					insurance.EndowmentCompanyAmount = int64((float64(insurance.EndowmentBase) * percent) / 100)
				}
			case 5:
				percent, err := row.Cells[5].Float()
				if err == nil {
					insurance.EndowmentPersonRatio = int64(percent * 100)
					insurance.EndowmentPersonAmount = int64((float64(insurance.EndowmentBase) * percent) / 100)
				}
			case 6:
				base, err := row.Cells[6].Float()
				if err == nil {
					insurance.MedicalBase = int64(base * 100) //转成单位：分
				}
			case 7:
				percent, err := row.Cells[7].Float()
				if err == nil {
					insurance.MedicalCompanyRatio = int64(percent * 100)
					insurance.MedicalCompanyAmount = int64((float64(insurance.MedicalBase) * percent) / 100)
				}
			case 8:
				percent, err := row.Cells[8].Float()
				if err == nil {
					insurance.MedicalPersonRatio = int64(percent * 100)
					insurance.MedicalPersonAmount = int64((float64(insurance.MedicalBase) * percent) / 100)
				}
			case 9:
				base, err := row.Cells[9].Float()
				if err == nil {
					insurance.UnemploymentBase = int64(base * 100) //转成单位：分
				}
			case 10:
				percent, err := row.Cells[10].Float()
				if err == nil {
					insurance.UnemploymentCompanyRatio = int64(percent * 100)
					insurance.UnemploymentCompanyAmount = int64((float64(insurance.UnemploymentBase) * percent) / 100)
				}
			case 11:
				percent, err := row.Cells[11].Float()
				if err == nil {
					insurance.UnemploymentPersonRatio = int64(percent * 100)
					insurance.UnemploymentPersonAmount = int64((float64(insurance.UnemploymentBase) * percent) / 100)
				}
			case 12:
				base, err := row.Cells[12].Float()
				if err == nil {
					insurance.InjuryBase = int64(base * 100) //转成单位：分
				}
			case 13:
				percent, err := row.Cells[13].Float()
				if err == nil {
					insurance.InjuryCompanyRatio = int64(percent * 100)
					insurance.InjuryCompanyAmount = int64((float64(insurance.InjuryBase) * percent) / 100)
				}
			case 14:
				percent, err := row.Cells[14].Float()
				if err == nil {
					insurance.InjuryPersonRatio = int64(percent * 100)
					insurance.InjuryPersonAmount = int64((float64(insurance.InjuryBase) * percent) / 100)
				}
			case 15:
				base, err := row.Cells[15].Float()
				if err == nil {
					insurance.BirthBase = int64(base * 100) //转成单位：分
				}
			case 16:
				percent, err := row.Cells[16].Float()
				if err == nil {
					insurance.BirthCompanyRatio = int64(percent * 100)
					insurance.BirthCompanyAmount = int64((float64(insurance.BirthBase) * percent) / 100)
				}
			case 17:
				percent, err := row.Cells[17].Float()
				if err == nil {
					insurance.BirthPersonRatio = int64(percent * 100)
					insurance.BirthPersonAmount = int64((float64(insurance.BirthBase) * percent) / 100)
				}
			case 18:
				base, err := row.Cells[18].Float()
				if err == nil {
					insurance.AnnuityBase = int64(base * 100) //转成单位：分
				}
				if insurance.AnnuityBase > 0 {
					insurance.IsAnnuity = util.StatusForTrue
				}
			case 19:
				percent, err := row.Cells[19].Float()
				if err == nil {
					insurance.AnnuityCompanyRatio = int64(percent * 100)
					insurance.AnnuityCompanyAmount = int64((float64(insurance.AnnuityBase) * percent) / 100)
				}
			case 20:
				percent, err := row.Cells[20].Float()
				if err == nil {
					insurance.AnnuityPersonRatio = int64(percent * 100)
					insurance.AnnuityPersonAmount = int64((float64(insurance.AnnuityBase) * percent) / 100)
				}
			case 21:
				base, err := row.Cells[21].Float()
				if err == nil {
					insurance.ExtraMedicalBase = int64(base * 100) //转成单位：分
				}
				if insurance.ExtraMedicalBase > 0 {
					insurance.IsExtraMedical = util.StatusForTrue
				}
			case 22:
				percent, err := row.Cells[22].Float()
				if err == nil {
					insurance.ExtraMedicalCompanyRatio = int64(percent * 100)
					insurance.ExtraMedicalCompanyAmount = int64((float64(insurance.ExtraMedicalBase) * percent) / 100)
				}
			case 23:
				percent, err := row.Cells[23].Float()
				if err == nil {
					insurance.ExtraMedicalPersonRatio = int64(percent * 100)
					insurance.ExtraMedicalPersonAmount = int64((float64(insurance.ExtraMedicalBase) * percent) / 100)
				}
			}
		}
		successCount++
		insurances = append(insurances, insurance)
	}

	if len(insurances) > 0 {
		var insurance hrModel.InsuranceRecord
		err = insurance.Create(insurances)
		if err != nil {
			log.ErrorFields("insurance.Create error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"SuccessCount": successCount,
		"FailCount":    len(importFailStaffs),
		"FailStaffs":   importFailStaffs,
	})
}

// Export 社保导出
func (ins *Insurance) Export(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Insurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	result := InsuranceList(ctx, param, "")

	return response.Success(rsp, result)
}

// ExtraMedialList 补充医疗列表
func (ins *Insurance) ExtraMedialList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Insurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	result := InsuranceList(ctx, param, util.ModularForExtraMedical)

	return response.Success(rsp, result)
}

// AnnuityList 企业年金列表
func (ins *Insurance) AnnuityList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Insurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	result := InsuranceList(ctx, param, util.ModularForAnnuity)

	return response.Success(rsp, result)
}

// List 社保列表
func (ins *Insurance) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Insurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	result := InsuranceList(ctx, param, "")

	return response.Success(rsp, result)
}

func InsuranceList(ctx context.Context, param Insurance, scene string) map[string]interface{} {
	param.CorporationIds = service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	var oetWhere service.OetWhere
	if param.Keyword != "" {
		oetWhere.Keyword = param.Keyword
	}

	oetStaffMap, staffIds, _ := service.SelectOetStaffByMultiWhere(ctx, param.CorporationIds, oetWhere, false, 0, 0)

	var startYear, endYear int64
	if param.StartYear > 0 {
		startYear, _ = strconv.ParseInt(fmt.Sprintf("%v01", param.StartYear), 10, 64)
	}
	if param.EndYear > 0 {
		endYear, _ = strconv.ParseInt(fmt.Sprintf("%v12", param.EndYear), 10, 64)
	}

	records, count := param.InsuranceRecord.GetBy(scene, staffIds, startYear, endYear, param.Paginator)

	for i := range records {
		oetStaff := oetStaffMap[records[i].StaffId]
		records[i].JobNumber = oetStaff.StaffId
		records[i].StaffName = oetStaff.Name
		corporation := rpc.GetCorporationById(context.Background(), oetStaff.CorporationId)

		if corporation != nil {
			records[i].CorporationName = corporation.Name
		}
	}

	return map[string]interface{}{"Items": records, "TotalCount": count}
}

// Create 社保新增
func (ins *Insurance) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Insurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("param validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}
	if len(param.StaffIds) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	//获取社保设置
	var setting hrModel.InsuranceFundSetting
	settings := setting.Get(topCorporationId, util.SceneForInsurance)
	if len(settings) == 0 {
		return response.Error(rsp, response.InsuranceSettingNotFund)
	}
	settingMap := buildSettingMap(settings)

	calcInsuranceAmount(settingMap, &param.InsuranceRecord)

	var records []hrModel.InsuranceRecord
	for i := range param.StaffIds {
		var archive StaffArchive
		_ = archive.FindByStaffId(param.StaffIds[i])
		param.InsuranceRecord.StaffArchiveId = archive.Id
		param.InsuranceRecord.StaffId = param.StaffIds[i]
		param.InsuranceRecord.ParseOpUser(ctx)
		param.InsuranceRecord.TopCorporationId = topCorporationId
		records = append(records, param.InsuranceRecord)
	}

	var insurance hrModel.InsuranceRecord
	err = insurance.Create(records)
	if err != nil {
		log.ErrorFields("insurance.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)

}

func buildSettingMap(settings []hrModel.InsuranceFundSetting) map[string]hrModel.InsuranceFundSetting {
	var settingMap = make(map[string]hrModel.InsuranceFundSetting)
	for i := range settings {
		settingMap[settings[i].Modular] = settings[i]
	}

	return settingMap
}

//根据基数计算社保缴纳数额
func calcInsuranceAmount(setting map[string]hrModel.InsuranceFundSetting, record *hrModel.InsuranceRecord) {
	//养老
	if endowment, ok := setting[util.ModularForEndowment]; ok {
		//公司
		if endowment.CompanyPayType == util.TypeForPayRadio {
			record.EndowmentCompanyAmount = int64((float64(endowment.CompanyPayAmount) / 100 / 100) * float64(record.EndowmentBase))
			record.EndowmentCompanyRatio = endowment.CompanyPayAmount
		}
		if endowment.CompanyPayType == util.TypeForPayFixMoney {
			record.EndowmentCompanyAmount = endowment.CompanyPayAmount
			record.EndowmentCompanyRatio = -1
		}
		//个人
		if endowment.PersonalPayType == util.TypeForPayRadio {
			record.EndowmentPersonAmount = int64((float64(endowment.PersonalPayAmount) / 100 / 100) * float64(record.EndowmentBase))
			record.EndowmentPersonRatio = endowment.PersonalPayAmount
		}
		if endowment.PersonalPayType == util.TypeForPayFixMoney {
			record.EndowmentPersonAmount = endowment.PersonalPayAmount
			record.EndowmentPersonRatio = -1
		}
	}

	//医疗
	if medical, ok := setting[util.ModularForMedical]; ok {
		//公司
		if medical.CompanyPayType == util.TypeForPayRadio {
			record.MedicalCompanyAmount = int64((float64(medical.CompanyPayAmount) / 100 / 100) * float64(record.MedicalBase))
			record.MedicalCompanyRatio = medical.CompanyPayAmount
		}
		if medical.CompanyPayType == util.TypeForPayFixMoney {
			record.MedicalCompanyAmount = medical.CompanyPayAmount
			record.MedicalCompanyRatio = -1
		}
		//个人
		if medical.PersonalPayType == util.TypeForPayRadio {
			record.MedicalPersonAmount = int64((float64(medical.PersonalPayAmount) / 100 / 100) * float64(record.MedicalBase))
			record.MedicalPersonRatio = medical.PersonalPayAmount
		}
		if medical.PersonalPayType == util.TypeForPayFixMoney {
			record.MedicalPersonAmount = medical.PersonalPayAmount
			record.MedicalPersonRatio = -1
		}
	}

	//失业
	if unemployment, ok := setting[util.ModularForUnemployment]; ok {
		//公司
		if unemployment.CompanyPayType == util.TypeForPayRadio {
			record.UnemploymentCompanyAmount = int64((float64(unemployment.CompanyPayAmount) / 100 / 100) * float64(record.UnemploymentBase))
			record.UnemploymentCompanyRatio = unemployment.CompanyPayAmount
		}
		if unemployment.CompanyPayType == util.TypeForPayFixMoney {
			record.UnemploymentCompanyAmount = unemployment.CompanyPayAmount
			record.UnemploymentCompanyRatio = -1
		}
		//个人
		if unemployment.PersonalPayType == util.TypeForPayRadio {
			record.UnemploymentPersonAmount = int64((float64(unemployment.PersonalPayAmount) / 100 / 100) * float64(record.UnemploymentBase))
			record.UnemploymentPersonRatio = unemployment.PersonalPayAmount
		}
		if unemployment.PersonalPayType == util.TypeForPayFixMoney {
			record.UnemploymentPersonAmount = unemployment.PersonalPayAmount
			record.UnemploymentPersonRatio = -1
		}
	}

	//工伤
	if injury, ok := setting[util.ModularForInjury]; ok {
		//公司
		if injury.CompanyPayType == util.TypeForPayRadio {
			record.InjuryCompanyAmount = int64((float64(injury.CompanyPayAmount) / 100 / 100) * float64(record.InjuryBase))
			record.InjuryCompanyRatio = injury.CompanyPayAmount
		}
		if injury.CompanyPayType == util.TypeForPayFixMoney {
			record.InjuryCompanyAmount = injury.CompanyPayAmount
			record.InjuryCompanyRatio = -1
		}
		//个人
		if injury.PersonalPayType == util.TypeForPayRadio {
			record.InjuryPersonAmount = int64((float64(injury.PersonalPayAmount) / 100 / 100) * float64(record.InjuryBase))
			record.InjuryPersonRatio = injury.PersonalPayAmount
		}
		if injury.PersonalPayType == util.TypeForPayFixMoney {
			record.InjuryPersonAmount = injury.PersonalPayAmount
			record.InjuryPersonRatio = -1
		}
	}

	//生育
	if birth, ok := setting[util.ModularForBirth]; ok {
		//公司
		if birth.CompanyPayType == util.TypeForPayRadio {
			record.BirthCompanyAmount = int64((float64(birth.CompanyPayAmount) / 100 / 100) * float64(record.BirthBase))
			record.BirthCompanyRatio = birth.CompanyPayAmount
		}
		if birth.CompanyPayType == util.TypeForPayFixMoney {
			record.BirthCompanyAmount = birth.CompanyPayAmount
			record.BirthCompanyRatio = -1
		}
		//个人
		if birth.PersonalPayType == util.TypeForPayRadio {
			record.BirthPersonAmount = int64((float64(birth.PersonalPayAmount) / 100 / 100) * float64(record.BirthBase))
			record.BirthPersonRatio = birth.PersonalPayAmount
		}
		if birth.PersonalPayType == util.TypeForPayFixMoney {
			record.BirthPersonAmount = birth.PersonalPayAmount
			record.BirthPersonRatio = -1
		}
	}

	//企业年金
	if annuity, ok := setting[util.ModularForAnnuity]; ok {
		//公司
		if annuity.CompanyPayType == util.TypeForPayRadio {
			record.AnnuityCompanyAmount = int64((float64(annuity.CompanyPayAmount) / 100 / 100) * float64(record.AnnuityBase))
			record.AnnuityCompanyRatio = annuity.CompanyPayAmount
		}
		if annuity.CompanyPayType == util.TypeForPayFixMoney {
			record.AnnuityCompanyAmount = annuity.CompanyPayAmount
			record.AnnuityCompanyRatio = -1

		}
		//个人
		if annuity.PersonalPayType == util.TypeForPayRadio {
			record.AnnuityPersonAmount = int64((float64(annuity.PersonalPayAmount) / 100 / 100) * float64(record.AnnuityBase))
			record.AnnuityPersonRatio = annuity.PersonalPayAmount
		}
		if annuity.PersonalPayType == util.TypeForPayFixMoney {
			record.AnnuityPersonAmount = annuity.PersonalPayAmount
			record.AnnuityPersonRatio = -1
		}
	}

	//补充医疗
	if extraMedical, ok := setting[util.ModularForExtraMedical]; ok {
		//公司
		if extraMedical.CompanyPayType == util.TypeForPayRadio {
			record.ExtraMedicalCompanyAmount = int64((float64(extraMedical.CompanyPayAmount) / 100 / 100) * float64(record.ExtraMedicalBase))
			record.ExtraMedicalCompanyRatio = extraMedical.CompanyPayAmount
		}
		if extraMedical.CompanyPayType == util.TypeForPayFixMoney {
			record.ExtraMedicalCompanyAmount = extraMedical.CompanyPayAmount
			record.ExtraMedicalCompanyRatio = -1
		}
		//个人
		if extraMedical.PersonalPayType == util.TypeForPayRadio {
			record.ExtraMedicalPersonAmount = int64((float64(extraMedical.PersonalPayAmount) / 100 / 100) * float64(record.ExtraMedicalBase))
			record.ExtraMedicalPersonRatio = extraMedical.PersonalPayAmount
		}
		if extraMedical.PersonalPayType == util.TypeForPayFixMoney {
			record.ExtraMedicalPersonAmount = extraMedical.PersonalPayAmount
			record.ExtraMedicalPersonRatio = -1
		}
	}
}

// InsuranceEdit 社保编辑
func (ins *Insurance) InsuranceEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return EditInsurance(ctx, req, rsp, "")
}

// InsuranceBatchEdit 批量编辑社保
func (ins *Insurance) InsuranceBatchEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return EditInsurance(ctx, req, rsp, "")
}

// AnnuityEdit 企业年金编辑
func (ins *Insurance) AnnuityEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return EditInsurance(ctx, req, rsp, util.ModularForAnnuity)
}

// AnnuityBatchEdit 批量编辑企业年金
func (ins *Insurance) AnnuityBatchEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return EditInsurance(ctx, req, rsp, util.ModularForAnnuity)
}

// ExtraMedicalEdit 补充医疗编辑
func (ins *Insurance) ExtraMedicalEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return EditInsurance(ctx, req, rsp, util.ModularForExtraMedical)
}

// ExtraMedicalBatchEdit 批量编辑补充医疗
func (ins *Insurance) ExtraMedicalBatchEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return EditInsurance(ctx, req, rsp, util.ModularForExtraMedical)
}

type InsuranceEditParam struct {
	Records []hrModel.InsuranceRecord
}

func EditInsurance(ctx context.Context, req *api.Request, rsp *api.Response, scene string) error {
	var param InsuranceEditParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	for i := range param.Records {
		if param.Records[i].Id == 0 {
			log.ErrorFields("param validate fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsMissing)
		}
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	//获取社保设置
	var setting hrModel.InsuranceFundSetting
	settings := setting.Get(topCorporationId, util.SceneForInsurance)
	if len(settings) == 0 {
		return response.Error(rsp, response.InsuranceSettingNotFund)
	}
	settingMap := buildSettingMap(settings)

	var records []hrModel.InsuranceRecord
	for i := range param.Records {
		calcInsuranceAmount(settingMap, &param.Records[i])
		param.Records[i].ParseOpUser(ctx)
		records = append(records, param.Records[i])
	}

	var insurance hrModel.InsuranceRecord
	err = insurance.BatchUpdate(records, scene)
	if err != nil {
		log.ErrorFields("insurance batchEdit error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

// Delete 删除社保
func (ins *Insurance) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Insurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = param.InsuranceRecord.Destroy()
	if err != nil {
		log.ErrorFields("InsuranceRecord.Destroy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}
	return response.Success(rsp, nil)
}

// AnnuityDelete 删除企业年金
func (ins *Insurance) AnnuityDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Insurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = param.InsuranceRecord.DeleteAnnuityAndExtraMedial(param.Id, util.ModularForAnnuity)
	if err != nil {
		log.ErrorFields("InsuranceRecord.DeleteAnnuityAndExtraMedial error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}
	return response.Success(rsp, nil)
}

// ExtraMedialDelete 删除补充医疗
func (ins *Insurance) ExtraMedialDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param Insurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = param.InsuranceRecord.DeleteAnnuityAndExtraMedial(param.Id, util.ModularForExtraMedical)
	if err != nil {
		log.ErrorFields("InsuranceRecord.DeleteAnnuityAndExtraMedial error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}
	return response.Success(rsp, nil)
}
