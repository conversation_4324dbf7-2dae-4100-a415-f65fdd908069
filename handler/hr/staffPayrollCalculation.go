package hr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
)

type StaffPayrollCalculationHandler struct {
	FleetId int64  `json:"FleetId"`
	LineId  int64  `json:"LineId"`
	Name    string `json:"Name"`
	Month   string `json:"Month"`
	Type    int64  `json:"Type"`
	IsFiled int64  `json:"IsFiled"`
	model.Paginator
}

func (sa *StaffPayrollCalculationHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form StaffPayrollCalculationHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	corporationIds := service.AuthCorporationIdProvider(ctx, []int64{})
	data, totalCount, err := (&hrModel.StaffPayrollCalculation{}).List(corporationIds, form.FleetId, form.LineId, form.Type, form.IsFiled, form.Name, form.Month, form.Paginator)
	if err != nil {
		log.ErrorFields("SafeProductionReport List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if data != nil {
		for index := range data {
			data[index].CorporationId, data[index].CorporationName = data[index].GetCorporation()
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": data, "TotalCount": totalCount})
}

func (sa *StaffPayrollCalculationHandler) Import(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		FileData string `json:"FileData" validate:"required"`
		Type     int64  `json:"Type" validate:"required"`
	}
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	// 1.先判断这个人是不是车队的
	user := auth.User(ctx).GetUser()
	corporation := rpc.GetCorporationDetailById(context.Background(), user.CorporationId)
	if corporation != nil {
		if corporation.FleetId == 0 {
			return response.Error(rsp, "您不是车队管理员，无法操作")
		}
	} else {
		return response.Error(rsp, "未找到组织信息")
	}
	fleetId := corporation.FleetId              //车队id
	month, _, _ := util.GetBusCompanyNowMonth() // 月份
	// 2.判断此月份是否归档
	var calcFileInfo hrModel.StaffPayrollCalculationFileInfo
	_ = calcFileInfo.Find(month, fleetId)
	if calcFileInfo.Id != 0 {
		if calcFileInfo.IsFiled == 2 {
			return response.Error(rsp, fmt.Sprintf("%s月份您车队的信息已归档，无法导入", month))
		}
	} else {
		calcFileInfo.Month = month
		calcFileInfo.FleetId = fleetId
		calcFileInfo.IsFiled = 1
		_ = calcFileInfo.Create()
	}
	// 解析excel文件
	decodeString, err := base64.StdEncoding.DecodeString(form.FileData)
	if err != nil {
		log.Error("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.Error("excelize.OpenBinary[err]:", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	if len(excelFile.Sheets) == 0 {
		log.Error("excelFile.Sheets为空")
		return response.Error(rsp, response.FAIL)
	}
	// 开始导入
	err = (&hrModel.StaffPayrollCalculation{}).ClearMonth(model.DB(), month, fleetId, form.Type)
	if err != nil {
		log.Error("StaffPayrollCalculation  ClearMonth[err]:", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	rows := excelFile.Sheets[0].Rows
	var errRows []ExcelForm
	if rows != nil {
		for index, row := range rows {
			if index == 0 {
				continue
			}
			var data hrModel.StaffPayrollCalculation
			data.Build(fleetId)
			data.Month = month
			data.Type = form.Type
			data.IsFiled = 1
			SetInfoMap[form.Type](&data, row.Cells) // 设置数据
			staff := rpc.GetStaffWithIdentifyId(ctx, user.TopCorporationId, data.IDCard)
			if staff != nil {
				data.StaffId = staff.Id
				data.JobNumber = staff.Code
				var staffArchive hrModel.StaffArchive
				_ = staffArchive.FindByStaffId(data.StaffId)
				data.StaffArchiveId = staffArchive.Id
			} else {
				errRows = append(errRows, ExcelForm{RowIndex: index + 1, Error: data.StaffName + "-" + data.IDCard + ",没有在组织中找到此员工"})
				continue
			}
			line := rpc.GetLineWithName(ctx, user.TopCorporationId, data.LineName)
			if line != nil {
				staff.LineId = line.Id
			}
			err = data.Create()
			if err != nil {
				errRows = append(errRows, ExcelForm{RowIndex: index + 1, Error: "插入失败"})
			}
		}
	}

	return response.Success(rsp, errRows)
}

var SetInfoMap = map[int64]func(data *hrModel.StaffPayrollCalculation, cells []*xlsx.Cell){
	hrModel.DriverBlueBus:      SetDriverBlueBus,
	hrModel.DriverCustomerLine: SetDriverCustomerLine,
	hrModel.DriverVillage:      SetDriverVillage,
}

func SetDriverBlueBus(data *hrModel.StaffPayrollCalculation, cells []*xlsx.Cell) {
	for i, cell := range cells {
		switch i {
		case 2:
			data.LineName = cell.String()
		case 3:
			data.StaffName = cell.String()
		case 4:
			data.IDCard = cell.String()
		case 6:
			data.PlanWorkDay, _ = cell.Int64()
			data.PlanWorkDay *= 10
		case 7:
			data.WorkDay, _ = cell.Int64()
			data.WorkDay *= 10
		case 8:
			data.HalfDayFrequency, _ = cell.Int64()
		case 9:
			data.AllDayFrequency, _ = cell.Int64()
		case 10:
			data.Holiday, _ = cell.Int64()
			data.Holiday *= 10
		case 11:
			hour, _ := cell.Float()
			data.NightWorkTime = int64(hour * 3600)
		case 12:
			vehicle, _ := cell.Float()
			data.VehicleLength = int64(vehicle * 1000)
		case 13:
			data.PassengerNumber, _ = cell.Int64()
		case 14:
			data.OverWorkHalfDay, _ = cell.Int64()
			data.OverWorkHalfDay *= 10
		case 15:
			data.OverWorkAllDay, _ = cell.Int64()
			data.OverWorkAllDay *= 10
		case 16:
			data.ManeuverDay, _ = cell.Int64()
			data.ManeuverDay *= 10
		case 17:
			data.Remark = cell.String()
		}
	}
}

func SetDriverCustomerLine(data *hrModel.StaffPayrollCalculation, cells []*xlsx.Cell) {
	for i, cell := range cells {
		switch i {
		case 2:
			data.LineName = cell.String()
		case 3:
			data.StaffName = cell.String()
		case 4:
			data.IDCard = cell.String()
		case 6:
			data.WorkDay, _ = cell.Int64()
			data.WorkDay *= 10
		case 7:
			data.Holiday, _ = cell.Int64()
			data.Holiday *= 10
		case 8:
			data.CommutingShiftDay, _ = cell.Int64()
			data.CommutingShiftDay *= 10
		case 9:
			data.BusShiftDay, _ = cell.Int64()
			data.BusShiftDay *= 10
		case 10:
			data.TempShiftDay, _ = cell.Int64()
			data.TempShiftDay *= 10
		case 11:
			mileage, _ := cell.Float()
			data.Mileage = int64(mileage * 1000)
		case 12:
			data.OverWorkOn5To6, _ = cell.Int64()
		case 13:
			data.OverWorkOn6To18, _ = cell.Int64()
		case 14:
			data.OverWorkOn18To23, _ = cell.Int64()
		case 15:
			data.OverWorkOn23To5, _ = cell.Int64()
		case 16:
			data.OverWorkOn18To24, _ = cell.Int64()
		case 17:
			data.OverWorkOn24To5, _ = cell.Int64()
		case 18:
			data.Remark = cell.String()
		}
	}
}

func SetDriverVillage(data *hrModel.StaffPayrollCalculation, cells []*xlsx.Cell) {
	for i, cell := range cells {
		switch i {
		case 2:
			data.LineName = cell.String()
		case 3:
			data.StaffName = cell.String()
		case 4:
			data.IDCard = cell.String()
		case 6:
			data.WorkDay, _ = cell.Int64()
			data.WorkDay *= 10
		case 7:
			data.ManeuverDay, _ = cell.Int64()
			data.ManeuverDay *= 10
		case 8:
			data.Holiday, _ = cell.Int64()
			data.Holiday *= 10
		case 9:
			data.Remark = cell.String()
		}
	}
}

func (sa *StaffPayrollCalculationHandler) File(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form StaffPayrollCalculationHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Month == "" {
		form.Month, _, _ = util.GetBusCompanyNowMonth()
	}
	// 1.先判断这个人是不是车队的
	user := auth.User(ctx).GetUser()
	corporation := rpc.GetCorporationDetailById(context.Background(), user.CorporationId)
	if corporation != nil {
		if corporation.FleetId == 0 {
			return response.Error(rsp, "您不是车队管理员，无法操作")
		}
	} else {
		return response.Error(rsp, "未找到组织信息")
	}
	fleetId := corporation.FleetId //车队id
	fmt.Println("==========================fleetId", fleetId)
	tx := model.DB().Begin()
	var calcFileInfo hrModel.StaffPayrollCalculationFileInfo
	_ = calcFileInfo.Find(form.Month, fleetId)
	if calcFileInfo.Id == 0 {
		calcFileInfo.Month = form.Month
		calcFileInfo.FleetId = fleetId
		calcFileInfo.IsFiled = 2
		err := calcFileInfo.TxCreate(tx)
		if err != nil {
			tx.Rollback()
			return response.Error(rsp, "归档失败")
		}
	} else {
		calcFileInfo.IsFiled = 2
		err := calcFileInfo.TxUpdate(tx)
		if err != nil {
			tx.Rollback()
			return response.Error(rsp, "归档失败")
		}
	}

	err := (&hrModel.StaffPayrollCalculation{}).File(tx, form.Month, fleetId)
	if err != nil {
		tx.Rollback()
		return response.Error(rsp, "归档失败")
	}
	tx.Commit()
	return response.Success(rsp, nil)
}
