package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	protoStaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"time"
)

// LaborContract 劳动合同管理
type LaborContract struct {
	hrModel.StaffLaborContract
	CorporationIds  []int64          `json:"CorporationIds"`
	Keyword         string           `json:"Keyword"`
	JobStatusArr    []int64          `json:"JobStatusArr"`
	WorkPostTypeArr []int64          `json:"WorkPostTypeArr"`
	EndAtStart      *model.LocalTime `json:"EndAtStart"`
	EndAtEnd        *model.LocalTime `json:"EndAtEnd"`
	IsDistinct      int64            `json:"IsDistinct"` //只显示一个人最新的一个合同 1是 0否
	model.Paginator
}

type LaborContractApi struct {
	hrModel.StaffLaborContract
	CorporationName string `json:"CorporationName"`
	JobNumber       string `json:"JobNumber"`
	JobStatus       int64  `json:"JobStatus"`
}

// Import 劳动合同导入
func (stf *LaborContract) Import(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ImportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			response.Error(rsp, response.FAIL)
		}
		return
	}()

	//获取机构的层级关系
	//corporation := rpc.GetCorporationDetailById(ctx, util.GetUserCorporationId(ctx))
	//if corporation == nil {
	//	log.ErrorFields("rpc GetCorporationDetailById is nil", map[string]interface{}{"corporationId": util.GetUserCorporationId(ctx)})
	//	return response.Error(rsp, response.ParamsInvalid)
	//}

	// 通过根机构获取所有员工数据
	oetStaffs := rpc.GetStaffsWithOption(ctx, auth.User(ctx).GetTopCorporationId())
	oetStaffMap := make(map[string]*protoStaff.OetStaffItem)
	for i := range oetStaffs {
		if oetStaffs[i].IdentifyId != "" {
			oetStaffMap[oetStaffs[i].IdentifyId] = oetStaffs[i]
		}
	}

	// 0姓名* 1联系电话 2合同编号 3合同类型 4签订时间 5到期时间
	sheet := excelFile.Sheets[0]
	var importFailStaffs []string
	var successCount int64
	var contracts []hrModel.StaffLaborContract

	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 6 {
			continue
		}

		if row.Cells[0].String() == "" || row.Cells[1].String() == "" || row.Cells[3].String() == "" || row.Cells[4].String() == "" {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}
		identifyId := row.Cells[1].String()
		oetStaff, ok := oetStaffMap[identifyId]
		if !ok {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}
		var archive hrModel.StaffArchive
		err := archive.FindByStaffId(oetStaff.Id)

		if err != nil {
			importFailStaffs = append(importFailStaffs, row.Cells[0].String())
			continue
		}

		var contract hrModel.StaffLaborContract
		contract.StaffArchiveId = archive.Id
		contract.StaffId = oetStaff.Id
		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 0:
				contract.StaffName = row.Cells[0].String()
			case 2:
				contract.Code = row.Cells[2].String()
			case 3:
				contract.Type, _ = util.LaborContractType[row.Cells[3].String()]
			case 4:
				startAt, err := row.Cells[4].GetTime(false)
				if err == nil && !startAt.IsZero() {
					local := model.LocalTime(startAt)
					contract.StartAt = &local
				}
			case 5:
				endAt, err := row.Cells[5].GetTime(false)
				if err == nil && !endAt.IsZero() {
					if endAt.Unix() < time.Now().Unix() {
						contract.Status = util.LaborContractExpiration
					} else {
						contract.Status = util.LaborContractValid
					}

					local := model.LocalTime(endAt)
					contract.EndAt = &local
				}
			}
		}
		successCount++
		contracts = append(contracts, contract)
	}

	var laborContract hrModel.StaffLaborContract
	err = laborContract.Create(contracts)
	if err != nil {
		log.ErrorFields("laborContract.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, map[string]interface{}{
		"SuccessCount": successCount,
		"FailCount":    len(importFailStaffs),
		"FailStaffs":   importFailStaffs,
	})
}

// List 劳动合同列表
func (stf *LaborContract) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LaborContract
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)
	var oetWhere service.OetWhere
	if param.Keyword != "" {
		oetWhere.Keyword = param.Keyword
	}
	if len(param.JobStatusArr) > 0 {
		oetWhere.JobStatusArr = param.JobStatusArr
	}

	oetStaffMap, staffIds, _ := service.SelectOetStaffByMultiWhere(ctx, corporationIds, oetWhere, false, 0, 0)

	var endAtStart, endAtEnd time.Time
	if param.EndAtStart != nil {
		endAtStart = time.Time(*param.EndAtStart)
	}

	if param.EndAtEnd != nil {
		endAtEnd = time.Time(*param.EndAtEnd)
	}
	laborContracts, count := param.StaffLaborContract.GetBy(staffIds, param.Type, param.Status, param.IsDistinct, endAtStart, endAtEnd, param.Paginator)

	var results []LaborContractApi
	for i := range laborContracts {
		var laborContract LaborContractApi
		laborContract.StaffLaborContract = laborContracts[i]
		oetStaff := oetStaffMap[laborContracts[i].StaffId]
		corporation := rpc.GetCorporationById(ctx, oetStaff.CorporationId)
		if corporation != nil {
			laborContract.CorporationName = corporation.Name
		}
		laborContract.JobNumber = oetStaff.StaffId
		laborContract.StaffName = oetStaff.Name
		laborContract.JobStatus = oetStaff.WorkingState
		if laborContracts[i].Status != util.LaborContractRelieve {
			if laborContracts[i].EndAt != nil && time.Time(*laborContracts[i].EndAt).Unix() < time.Now().Unix() {
				laborContract.Status = util.LaborContractExpiration
			} else {
				laborContract.Status = util.LaborContractValid
			}
		}
		results = append(results, laborContract)
	}

	return response.Success(rsp, map[string]interface{}{"Items": results, "TotalCount": count, "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

type StaffLaborContract struct {
	Records []hrModel.StaffLaborContract
}

// Create 合同添加
func (stf *LaborContract) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffLaborContract
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(param.Records) == 0 {
		log.ErrorFields("Records field value is empty", nil)
		return response.Error(rsp, response.ParamsMissing)
	}

	for i := range param.Records {
		if err := util.Validator().Struct(param.Records[i]); err != nil {
			log.ErrorFields("records validate fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsMissing)
		}
		var archive StaffArchive
		err := archive.FindByStaffId(param.Records[i].StaffId)
		if err != nil {
			log.ErrorFields("archive.FindByStaffId error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		param.Records[i].StaffArchiveId = archive.Id
	}

	var laborContract hrModel.StaffLaborContract
	err = laborContract.Create(param.Records)
	if err != nil {
		log.ErrorFields("laborContract.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)

}

// Edit 合同编辑
func (stf *LaborContract) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LaborContract
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param.StaffLaborContract); err != nil {
		log.ErrorFields("param validator error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	var laborContract hrModel.StaffLaborContract
	err = laborContract.FindById(param.Id)
	if err != nil {
		log.ErrorFields("laborContract.FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	err = param.StaffLaborContract.Update()
	if err != nil {
		log.ErrorFields("StaffLaborContract update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)

}

type RelieveContractForm struct {
	Id                    int64           `json:"Id"`
	JoinCompanyAt         model.LocalTime `json:"JoinCompanyAt"`
	RelieveContractAt     model.LocalTime `json:"RelieveContractAt"`
	RelieveContractType   int64           `json:"RelieveContractType"`
	YearHolidayRemain     int64           `json:"YearHolidayRemain"`
	ExchangeHolidayRemain int64           `json:"ExchangeHolidayRemain"`
	Files                 model.JSON      `json:"Files"`
}

// Relieve 合同解除
func (stf *LaborContract) Relieve(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param RelieveContractForm
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var laborContract hrModel.StaffLaborContract
	err = laborContract.FindById(param.Id)
	if err != nil {
		log.ErrorFields("laborContract.FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	//更新合同状态、解除合同时间
	err = laborContract.UpdateStatusToRelieveById(param.Id, util.LaborContractRelieve, time.Time(param.RelieveContractAt))
	if err != nil {
		log.ErrorFields("laborContract.UpdateStatusToRelieveById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	//新增离职数据
	var quitRecord = hrModel.StaffQuitRecord{
		StaffArchiveId:        laborContract.StaffArchiveId,
		StaffId:               laborContract.StaffId,
		ContractEndAt:         laborContract.EndAt,
		RelieveContractAt:     &(param.RelieveContractAt),
		RelieveContractType:   param.RelieveContractType,
		YearHolidayRemain:     param.YearHolidayRemain,
		ExchangeHolidayRemain: param.ExchangeHolidayRemain,
		FilePath:              param.Files,
	}
	quitRecord.ParseOpUser(ctx)
	err = quitRecord.Create()
	if err != nil {
		log.ErrorFields("quitRecord.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	//更新主数据
	oetStaff := rpc.GetStaffWithId(ctx, laborContract.StaffId)
	if oetStaff != nil {
		oetStaff.WorkingState = util.JobStatusQuit
		oetStaff.RegisterTime = time.Time(param.JoinCompanyAt).Unix()
		err = rpc.EditOetStaff(ctx, auth.User(ctx).GetUserId(), oetStaff)
		if err != nil {
			log.ErrorFields("rpc.EditOetStaff error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	return response.Success(rsp, nil)

}
