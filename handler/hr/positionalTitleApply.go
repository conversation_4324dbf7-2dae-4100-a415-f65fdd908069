package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"strings"
	"time"
)

// PositionalTitleApply 职称补贴申请管理
type PositionalTitleApply struct {
	hrModel.PositionalTitleApply
	CorporationIds []int64 `json:"CorporationIds"`
	Keyword        string  `json:"Keyword"`
	ProcessId      string  `json:"ProcessId"`
	LbpmParam      string  `json:"LbpmParam"`
	model.Paginator
}

// List 职称补贴申请列表
func (pta *PositionalTitleApply) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param PositionalTitleApply
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.CorporationIds = service.AuthCorporationIdProvider(ctx, param.CorporationIds)
	var oetWhere service.OetWhere
	if param.Keyword != "" {
		oetWhere.Keyword = param.Keyword
	}
	oetStaffMap, staffIds, _ := service.SelectOetStaffByMultiWhere(ctx, param.CorporationIds, oetWhere, false, 0, 0)

	applies, count := param.GetBy(staffIds, param.Name, param.Level, param.ApplyStatus, param.Paginator)

	for i := range applies {
		oetStaff := oetStaffMap[applies[i].StaffId]
		applies[i].StaffName = oetStaff.Name
		applies[i].JobNumber = oetStaff.StaffId

		corporation := rpc.GetCorporationById(ctx, oetStaff.CorporationId)
		if corporation != nil {
			applies[i].CorporationName = corporation.Name
		}

		applies[i].WorkPostType = oetStaff.Occupation

		var archiveWorkPost hrModel.StaffHasWorkPost
		workPostIds := archiveWorkPost.GetNowWorkPostIdByArchiveId(applies[i].StaffArchiveId)
		var workPost hrModel.WorkPost
		workPostNames := workPost.PluckNameByIds(workPostIds)
		applies[i].WorkPostName = strings.Join(workPostNames, "、")

		applies[i].JobStatus = oetStaff.WorkingState
		if oetStaff.RegisterTime > 0 {
			at := model.LocalTime(time.Unix(oetStaff.RegisterTime, 0))
			applies[i].JoinCompanyAt = &at
		}

		if oetStaff.WorkTime > 0 {
			diff := time.Now().Unix() - oetStaff.WorkTime
			if diff > 0 {
				applies[i].WorkingAge = diff / 3600 / 24 / 365
			}
		}
	}

	return response.Success(rsp, map[string]interface{}{"Items": applies, "TotalCount": count, "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

// Create 新增职称补贴申请
func (pta *PositionalTitleApply) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param PositionalTitleApply
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("param validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	//当前操作人
	user := auth.User(ctx).GetUser()

	tx := model.DB().Begin()
	param.PositionalTitleApply.UserId = user.Id
	param.PositionalTitleApply.OpUserId = user.Id
	param.PositionalTitleApply.OpUserName = user.Name

	staff := rpc.GetStaffWithPhone(ctx, auth.User(ctx).GetCorporationId(), user.Phone)
	if staff != nil {
		param.PositionalTitleApply.StaffId = staff.Id

		var archive hrModel.StaffArchive
		err = archive.FindByStaffId(staff.Id)
		if err != nil {
			return response.Error(rsp, response.FAIL)
		}
		param.PositionalTitleApply.StaffArchiveId = archive.Id
	}

	param.PositionalTitleApply.TopCorporationId = user.TopCorporationId
	err = param.PositionalTitleApply.TransactionCreate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("PositionTitleApply.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	//有流程时 发起流程审批
	if param.ProcessId != "" {
		param.PositionalTitleApply.UserName = user.Name
		byteParam, _ := json.Marshal(param.PositionalTitleApply)
		formData := map[string]interface{}{}
		processTitle := fmt.Sprintf("%s提交的审批", user.Name)
		_, err = processService.NewDispatchProcess(user, config.PositionalTitleApplyFormTemplate, processTitle, param.PositionalTitleApply.Id, param.PositionalTitleApply.TableName(), param.PositionalTitleApply.ApplyStatusFieldName(), string(byteParam), formData)

		if err != nil {
			tx.Rollback()
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

// Approval 职称补贴审批
func (pta *PositionalTitleApply) Approval(ctx context.Context, req *api.Request, rsp *api.Response) error {
	if config.Config.Lbpm.Enable {
		return response.Error(rsp, response.BadRequest)
	}

	var param PositionalTitleApply
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var apply hrModel.PositionalTitleApply
	err = apply.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("apply.FindBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	apply.ApplyStatus = param.ApplyStatus
	apply.ParseOpUser(ctx)

	err = apply.UpdateStatus()
	if err != nil {
		log.ErrorFields("apply.UpdateStatus error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	//审批通过，需要同步到员工档案
	if param.ApplyStatus == util.ApplyStatusForDone {
		var pt hrModel.StaffPositionalTitle
		pt.Name = apply.Name
		pt.StaffArchiveId = apply.StaffArchiveId
		pt.StaffId = apply.StaffId
		pt.More = apply.More
		pt.Level = apply.Level
		pt.FilePath = apply.FilePath
		_ = pt.Create()
	}

	return response.Success(rsp, nil)

}

// Show 职称补贴申请详情
func (pta *PositionalTitleApply) Show(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param PositionalTitleApply
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var apply hrModel.PositionalTitleApply
	err = apply.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("apply.FindBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	var archive hrModel.StaffArchive
	err = archive.FindById(apply.StaffArchiveId)
	if err != nil {
		log.ErrorFields("archive.FindById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	var applyApi hrModel.PositionalTitleApplyApi
	applyApi.PositionalTitleApply = apply

	oetStaff := rpc.GetStaffWithId(ctx, apply.StaffId)

	applyApi.StaffName = oetStaff.Name
	applyApi.JobNumber = oetStaff.StaffId
	applyApi.StaffMobile = oetStaff.Phone
	applyApi.ProbationEndAt = archive.ProbationEndAt

	corporation := rpc.GetCorporationById(ctx, oetStaff.CorporationId)
	if corporation != nil {
		applyApi.CorporationName = corporation.Name
	}

	applyApi.WorkPostType = oetStaff.Occupation

	var archiveWorkPost hrModel.StaffHasWorkPost
	workPostIds := archiveWorkPost.GetNowWorkPostIdByArchiveId(applyApi.StaffArchiveId)
	var workPost hrModel.WorkPost
	workPostNames := workPost.PluckNameByIds(workPostIds)
	applyApi.WorkPostName = strings.Join(workPostNames, "、")

	applyApi.JobStatus = oetStaff.WorkingState
	if oetStaff.RegisterTime > 0 {
		at := model.LocalTime(time.Unix(oetStaff.RegisterTime, 0))
		applyApi.JoinCompanyAt = &at
	}

	if oetStaff.WorkTime > 0 {
		diff := time.Now().Unix() - oetStaff.WorkTime
		if diff > 0 {
			applyApi.WorkingAge = diff / 3600 / 24 / 365
		}
	}

	applyApi.FileHttpPrefix = config.Config.StaticFileHttpPrefix
	applyApi.IsProcessHandler = processService.CheckIsProcessRelater(applyApi.Id, config.PositionalTitleApplyFormTemplate, auth.User(ctx).GetUserId())

	return response.Success(rsp, applyApi)
}
