package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/model/process"
	protoStaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	"time"

	api "github.com/micro/go-micro/v2/api/proto"
)

type HeadImgApproval struct {
	CorporationIds []int64 `json:"CorporationIds"`
	StaffName      string  `json:"StaffName"`
	JobNumber      string  `json:"JobNumber"`
	model.Paginator
}

func (fun *HeadImgApproval) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param HeadImgApproval
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)
	data, totalCount := (&hrModel.HeadImgApproval{}).List(corporationIds, param.StaffName, param.JobNumber, param.Paginator)
	if data != nil {
		for index := range data {
			data[index].CorporationId, data[index].CorporationName = data[index].GetCorporation()
			var processInfo process.LbpmApplyProcess
			_ = processInfo.GetProcessByItemId(data[index].Id, data[index].TableName())
			data[index].ApproveUserName = processInfo.CurrentHandlerUserName
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": data, "TotalCount": totalCount})
}

func (fun *HeadImgApproval) Show(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param hrModel.HeadImgApproval
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = param.FindById()
	if err != nil {
		log.ErrorFields("find err", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	if param.Id == 0 || param.StaffId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.CorporationId, param.CorporationName = param.GetCorporation()

	return response.Success(rsp, param)
}

func (fun *HeadImgApproval) Pass(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form hrModel.HeadImgApproval
	err := json.Unmarshal([]byte(req.Body), &form)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	err = form.FindById()
	if err != nil {
		log.ErrorFields("find error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	user := auth.User(ctx).GetUser()
	tx := model.DB().Begin()
	form.HeadImgState = 1
	form.ApproveUserId = user.Id
	form.ApproveUserName = user.Name
	form.ApproveTime = model.LocalTime(time.Now())
	err = form.TxUpdate(tx)
	if err != nil {
		log.ErrorFields("form.Reject error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbUpdateFail)
	}
	// 同意过后同步到主数据
	if form.HeadImg != nil && len(form.HeadImg) > 0 {
		var HeadImg []hrModel.HeadImg
		err = json.Unmarshal(form.HeadImg, &HeadImg)
		if err != nil {
			log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
			tx.Rollback()
			return response.Error(rsp, response.FAIL)
		}
		headImg := HeadImg[0]
		fullPath := fmt.Sprintf("%s%s", config.Config.AbsDirPath, headImg.Path)
		base64Data, err := util.FileToBase64(fullPath)
		if err != nil {
			log.ErrorFields("FileToBase64 is err", map[string]interface{}{"err": err})
			tx.Rollback()
			return response.Error(rsp, "OP9009")
		}
		err = rpc.EditOetStaffAvatar(ctx, &protoStaff.UploadStaffProfileRequest{
			Id:               form.StaffId,
			TopCorporationId: user.TopCorporationId,
			FileName:         headImg.Name,
			FileData:         base64Data,
		})
		if err != nil {
			log.ErrorFields("rpc.EditOetStaffAvatar is err", map[string]interface{}{"err": err})
			tx.Rollback()
			return response.Error(rsp, "OP9009")
		}
	}
	tx.Commit()
	return response.Success(rsp, nil)
}

func (fun *HeadImgApproval) Reject(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form hrModel.HeadImgApproval
	err := json.Unmarshal([]byte(req.Body), &form)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if form.Id == 0 || form.RejectReason == "" {
		return response.Error(rsp, response.ParamsMissing)
	}
	RejectReason := form.RejectReason
	err = form.FindById()
	if err != nil {
		log.ErrorFields("find error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	user := auth.User(ctx).GetUser()
	form.HeadImgState = 2
	form.ApproveUserId = user.Id
	form.ApproveUserName = user.Name
	form.ApproveTime = model.LocalTime(time.Now())
	form.RejectReason = RejectReason
	err = form.Update()
	if err != nil {
		log.ErrorFields("form.Reject error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)
}
