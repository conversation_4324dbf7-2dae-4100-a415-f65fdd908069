package base

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"github.com/micro/go-micro/v2/api/proto"
)

// 场站
type Parking struct {
	Name string // 场站名
	Type int64
}

func (p *Parking) List(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param Parking

	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()

	parkingItems := rpc.GetListWithCorpId(ctx, topCorporationId, param.Name, param.Type)
	data := map[string]interface{}{
		"Items": parkingItems,
	}

	return response.Success(rsp, data)
}
