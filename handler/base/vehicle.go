package base

import (
	"app/org/scs/erpv2/api/config"
	log "app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	commonModel "app/org/scs/erpv2/api/model/common"
	fileModel "app/org/scs/erpv2/api/model/file"
	"app/org/scs/erpv2/api/model/maintenance"
	operationModel "app/org/scs/erpv2/api/model/operation"
	protooetcorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	protooetline "app/org/scs/erpv2/api/proto/rpc/oetline"
	protooetvehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	protooetparking "app/org/scs/erpv2/api/proto/rpc/parking"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	"github.com/micro/go-micro/v2/api/proto"
	"strconv"
	"strings"
	"time"
)

// 查车辆
type Vehicle struct {
	AuthId         string
	Offset         int64
	Limit          int64
	Order          string
	License        string
	VehicleCode    string  `json:"VehicleCode"`
	Code           string  `json:"Code"`
	VehicleId      int64   `json:"VehicleId"`
	CorporationIds []int64 `json:"CorporationIds"`
}

type VehicleItemRsp struct {
	//*protooetvehicle.OetVehicleItem
	Id               int64
	License          string
	VehicleType      string
	Beacom           int64
	BranchId         int64
	Branch           string
	LineId           int64
	Line             string
	SonCorporationId int64  // 车属机构
	SonCorporation   string // 车属机构
	UpParkingId      int64  // 停车场（线路上行场区）id
	UpParking        string // 停车场（线路上行场区）
	Color            string `json:"Color"`
	CreatedAt        model.LocalTime
	UseStatus        int64
	Code             string
}

// 需要userid 移动端勿用
func (v *Vehicle) List(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	rspD, totalCount, errCode := VehicleList(ctx, req)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	data := map[string]interface{}{
		"Items":      rspD,
		"TotalCount": totalCount,
	}
	return response.Success(rsp, data)
}

func VehicleList(ctx context.Context, req *go_api.Request) ([]VehicleItemRsp, int64, string) {
	var param Vehicle
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return nil, 0, response.ParamsInvalid
	}
	err = util.Validator().Struct(param)
	if err != nil {
		log.ErrorFields("util.Validator().Struct error", map[string]interface{}{"err": err})
		return nil, 0, response.ParamsInvalid
	}

	if strings.ToLower(param.Order) != "asc" || strings.ToLower(param.Order) != "desc" {
		param.Order = "desc"
	}

	// 获取userid
	if len(param.CorporationIds) == 0 {
		param.CorporationIds = service.AuthCorporationIdProvider(ctx, nil)
	}

	userId := auth.User(ctx).GetUserId()

	vehicleItems, totalCount := rpc.GetVehiclesWithOptionCorpIds(ctx, &protooetvehicle.GetVehiclesWithOptionCorpIdsRequest{
		CorporationIds:   param.CorporationIds,
		License:          param.License,
		VehicleType:      "",
		LicenseStartDate: 0,
		LicenseEndDate:   0,
		Offset:           param.Offset,
		Limit:            param.Limit,
		Order:            param.Order,
		UserId:           userId,
	})

	vehicleModelColors := (&maintenance.VehicleModelColor{}).GetAll(auth.User(ctx).GetTopCorporationId())
	var vehicleModelColorMap = make(map[string]string)
	for i := range vehicleModelColors {
		vehicleModelColorMap[vehicleModelColors[i].ModelCode] = vehicleModelColors[i].Color
	}
	//标台
	vehicleModels := rpc.GetVehicleModelList(ctx, auth.User(ctx).GetTopCorporationId())
	var vehicleModelBeacomMap = make(map[string]int64)
	for i := range vehicleModels {
		vehicleModelBeacomMap[vehicleModels[i].Model] = vehicleModels[i].Beacom
	}
	var rspD []VehicleItemRsp

	// 已请求响应的机构详情
	tmp := make(map[int64]*protooetcorporation.GetCorporationDetailByIdResponse) // map[请求机构id]机构详情数据
	rpcTmpLine := make(map[int64]*protooetline.OetLineItem)                      // map[lineId]
	rpcTmpUpParking := make(map[int64]*protooetparking.OetParkingItem)           // map[UpParkingId]

	for i := range vehicleItems {
		var vehicle = VehicleItemRsp{
			Id:               vehicleItems[i].Id,
			License:          vehicleItems[i].License,
			LineId:           vehicleItems[i].LineId,
			Line:             vehicleItems[i].Line,
			VehicleType:      vehicleItems[i].VehicleType,
			SonCorporationId: vehicleItems[i].SonCorporationId,
			UseStatus:        vehicleItems[i].UseStatus,
			Code:             vehicleItems[i].Code,
			CreatedAt:        model.LocalTime(time.Unix(vehicleItems[i].CreatedAt, 0)),
		}

		if color, ok := vehicleModelColorMap[vehicle.VehicleType]; ok {
			vehicle.Color = color
		}

		if beacom, ok := vehicleModelBeacomMap[vehicle.VehicleType]; ok {
			vehicle.Beacom = beacom
		}

		if detail, ok := tmp[vehicle.SonCorporationId]; ok {
			if detail == nil {
				continue
			}
			vehicle.BranchId = detail.BranchId
			vehicle.Branch = detail.BranchName
			vehicle.SonCorporation = detail.Item.Name
		} else {
			dt := rpc.GetCorporationDetailById(ctx, vehicle.SonCorporationId)
			tmp[vehicle.SonCorporationId] = dt

			if dt == nil {
				continue
			}
			vehicle.BranchId = dt.BranchId
			vehicle.Branch = dt.BranchName
			vehicle.SonCorporation = dt.Item.Name
		}

		if lineItem, ok := rpcTmpLine[vehicle.LineId]; ok {
			if lineItem != nil {
				vehicle.Line = lineItem.Name

				if p, ok2 := rpcTmpUpParking[lineItem.UpParkingId]; ok2 {
					if p != nil {
						vehicle.UpParkingId = lineItem.UpParkingId
						vehicle.UpParking = p.Name

					}
				} else {
					oetParking := rpc.GetParkingWithId(ctx, lineItem.UpParkingId)
					rpcTmpUpParking[lineItem.UpParkingId] = oetParking
					if oetParking != nil {
						vehicle.UpParkingId = lineItem.UpParkingId
						vehicle.UpParking = oetParking.Name
					}
				}

			}
		} else {
			lineWithId, _ := rpc.GetLineWithId(ctx, vehicle.LineId)
			rpcTmpLine[vehicle.LineId] = lineWithId
			if lineWithId != nil {
				vehicle.Line = lineWithId.Name

				if p, ok2 := rpcTmpUpParking[lineWithId.UpParkingId]; ok2 {
					if p != nil {
						vehicle.UpParkingId = lineWithId.UpParkingId
						vehicle.UpParking = p.Name

					}
				} else {
					oetParking := rpc.GetParkingWithId(ctx, lineWithId.UpParkingId)
					rpcTmpUpParking[lineWithId.UpParkingId] = oetParking
					if oetParking != nil {
						vehicle.UpParkingId = lineWithId.UpParkingId
						vehicle.UpParking = oetParking.Name
					}
				}
			}
		}
		rspD = append(rspD, vehicle)
	}
	return rspD, totalCount, ""
}

func (v *Vehicle) GetLicenseByCode(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	fmt.Printf("GetLicenseByCode running \n")
	var q Vehicle
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Code == "" {
		q.Code = q.VehicleCode
	}

	// rpc 通过车辆编号获取车辆信息
	vehicleWithCode := rpc.GetVehicleWithCode(ctx, auth.User(ctx).GetTopCorporationId(), q.Code)
	if vehicleWithCode == nil {
		log.Error("vehicleWithCode.Item == nil || vehicleWithCode == nil", err)
		return response.Error(rsp, response.FileExtInvalid)
	}

	data := map[string]interface{}{
		"License":          vehicleWithCode.License,
		"CorporationId":    vehicleWithCode.SonCorporationId,
		"CorporationIdStr": strconv.FormatInt(vehicleWithCode.SonCorporationId, 10),
	}
	return util.SendResp(rsp, "", "0", data)

}

func (v *Vehicle) ListAll(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return VehicleListAll(ctx, req, rsp)
}

func VehicleListAll(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Vehicle
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()

	vehicleItems, totalCount := rpc.GetVehiclesWithLicenseOption(ctx, &protooetvehicle.GetVehiclesWithLicenseOptionRequest{
		TopCorporationId: topCorporationId,
		License:          q.License,
		Offset:           q.Offset,
		Limit:            q.Limit,
		Order:            q.Order,
	})

	vehicleModelColors := (&maintenance.VehicleModelColor{}).GetAll(topCorporationId)
	var vehicleModelColorMap = make(map[string]string)
	for i := range vehicleModelColors {
		vehicleModelColorMap[vehicleModelColors[i].ModelCode] = vehicleModelColors[i].Color
	}

	//标台
	vehicleModels := rpc.GetVehicleModelList(ctx, topCorporationId)
	var vehicleModelBeacomMap = make(map[string]int64)
	for i := range vehicleModels {
		vehicleModelBeacomMap[vehicleModels[i].Model] = vehicleModels[i].Beacom
	}
	var rspD []VehicleItemRsp

	// 已请求响应的机构详情
	tmp := make(map[int64]*protooetcorporation.GetCorporationDetailByIdResponse) // map[请求机构id]机构详情数据
	rpcTmpLine := make(map[int64]*protooetline.OetLineItem)                      // map[lineId]
	rpcTmpUpParking := make(map[int64]*protooetparking.OetParkingItem)           // map[UpParkingId]

	for i := range vehicleItems {
		var vehicle = VehicleItemRsp{
			Id:               vehicleItems[i].Id,
			License:          vehicleItems[i].License,
			LineId:           vehicleItems[i].LineId,
			Line:             vehicleItems[i].Line,
			VehicleType:      vehicleItems[i].VehicleType,
			SonCorporationId: vehicleItems[i].SonCorporationId,
			UseStatus:        vehicleItems[i].UseStatus,
			Code:             vehicleItems[i].Code,
			CreatedAt:        model.LocalTime(time.Unix(vehicleItems[i].CreatedAt, 0)),
		}

		if color, ok := vehicleModelColorMap[vehicle.VehicleType]; ok {
			vehicle.Color = color
		}

		if beacom, ok := vehicleModelBeacomMap[vehicle.VehicleType]; ok {
			vehicle.Beacom = beacom
		}

		if detail, ok := tmp[vehicle.SonCorporationId]; ok {
			if detail == nil {
				continue
			}
			vehicle.BranchId = detail.BranchId
			vehicle.Branch = detail.BranchName
			vehicle.SonCorporation = detail.Item.Name
		} else {
			dt := rpc.GetCorporationDetailById(ctx, vehicle.SonCorporationId)
			tmp[vehicle.SonCorporationId] = dt

			if dt == nil {
				continue
			}
			vehicle.BranchId = dt.BranchId
			vehicle.Branch = dt.BranchName
			vehicle.SonCorporation = dt.Item.Name
		}

		if lineItem, ok := rpcTmpLine[vehicle.LineId]; ok {
			if lineItem != nil {
				vehicle.Line = lineItem.Name

				if p, ok2 := rpcTmpUpParking[lineItem.UpParkingId]; ok2 {
					if p != nil {
						vehicle.UpParkingId = lineItem.UpParkingId
						vehicle.UpParking = p.Name

					}
				} else {
					oetParking := rpc.GetParkingWithId(ctx, lineItem.UpParkingId)
					rpcTmpUpParking[lineItem.UpParkingId] = oetParking
					if oetParking != nil {
						vehicle.UpParkingId = lineItem.UpParkingId
						vehicle.UpParking = oetParking.Name
					}
				}

			}
		} else {
			lineWithId, _ := rpc.GetLineWithId(ctx, vehicle.LineId)
			rpcTmpLine[vehicle.LineId] = lineWithId
			if lineWithId != nil {
				vehicle.Line = lineWithId.Name

				if p, ok2 := rpcTmpUpParking[lineWithId.UpParkingId]; ok2 {
					if p != nil {
						vehicle.UpParkingId = lineWithId.UpParkingId
						vehicle.UpParking = p.Name

					}
				} else {
					oetParking := rpc.GetParkingWithId(ctx, lineWithId.UpParkingId)
					rpcTmpUpParking[lineWithId.UpParkingId] = oetParking
					if oetParking != nil {
						vehicle.UpParkingId = lineWithId.UpParkingId
						vehicle.UpParking = oetParking.Name
					}
				}
			}
		}

		rspD = append(rspD, vehicle)
	}

	data := map[string]interface{}{
		"Items":      rspD,
		"TotalCount": totalCount,
	}
	return response.Success(rsp, data)
}

type VehicleItem struct {
	*protooetvehicle.OetVehicleItem
	AdsItems    []AssociationRecordItem `json:"AdsItems"`
	Corporation string                  `json:"Corporation"`
}

type AssociationRecordItem struct {
	AssociationId        int64  `json:"AssociationId"`
	Dict                 string `json:"Dict"`
	DictId               int64  `json:"DictId"`
	Material             string `json:"Material"`
	MaterialId           int64  `json:"MaterialId"`
	Specification        string `json:"Specification"`
	SpecificationId      int64  `json:"SpecificationId"`
	SpecificationFileUrl string `json:"SpecificationFileUrl"`
}

func (v *Vehicle) Detail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param Vehicle
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.VehicleId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	vehicleItem := rpc.GetVehicleWithId(ctx, param.VehicleId)
	if vehicleItem == nil {
		return response.Error(rsp, response.DbQueryFail)
	}
	var retItem = VehicleItem{
		OetVehicleItem: vehicleItem,
		Corporation:    rpc.GetCorporationNameById(ctx, vehicleItem.SonCorporationId),
	}

	rItems, _ := (&operationModel.OperationAssociationSettingRecord{}).GetByAssociationObjectId(param.VehicleId, operationModel.AssociationObjectForVehicle)
	for _, rItem := range rItems {
		var dictName string
		dict, _ := (&commonModel.Dict{PkId: model.PkId{rItem.DictId}}).Get()
		dictName = dict.DictKey

		var material string
		var ms = &operationModel.MaterialSetting{PkId: model.PkId{Id: rItem.MaterialSettingId}}
		ms.GetBy()
		material = ms.Name

		var specification string
		var ss = &operationModel.SpecificationSetting{PkId: model.PkId{Id: rItem.SpecificationSettingId}}
		ss.GetBy()
		specification = ss.Name

		retItem.AdsItems = append(retItem.AdsItems, AssociationRecordItem{
			AssociationId:        rItem.Id,
			Dict:                 dictName,
			DictId:               rItem.DictId,
			Material:             material,
			MaterialId:           rItem.MaterialSettingId,
			Specification:        specification,
			SpecificationId:      rItem.SpecificationSettingId,
			SpecificationFileUrl: config.Config.StaticFileHttpPrefix + (&fileModel.File{}).FindBy(ss.FileId).Path,
		})
	}

	data := map[string]interface{}{
		"Item": retItem,
	}
	return response.Success(rsp, data)
}
