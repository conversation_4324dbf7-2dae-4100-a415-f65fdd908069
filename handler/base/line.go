package base

import (
	"app/org/scs/erpv2/api/log"
	protooetline "app/org/scs/erpv2/api/proto/rpc/oetline"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"github.com/micro/go-micro/v2/api/proto"
)

// 查线路
type Line struct {
	AuthId string `validate:"required"`
	Offset int64
	Limit  int64
	Order  string `validate:"required,oneof=asc desc"`
	Line   string
}

type LineListResponseItem struct {
	protooetline.OetLineItem
	Fleet    string
	BranchId int64
	Branch   string
}

func LineList(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param Line

	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(param)
	if err != nil {
		log.ErrorFields("util.Validator().Struct error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 获取用户userid
	userId := auth.User(ctx).GetUserId()
	if userId == 0 {
		log.ErrorFields("md.GetWebUserId, userId = 0", nil)
		return response.Error(rsp, "403")
	}

	lineItems, totalCount := rpc.GetLinesWithUserId(ctx, &protooetline.GetLinesWithUserIdRequest{
		UserId:          userId,
		Name:            param.Line,
		Offset:          param.Offset,
		Limit:           param.Limit,
		Order:           param.Order,
		IsLineAttribute: 1,
	})

	var rspD []LineListResponseItem

	for _, item := range lineItems {

		var r LineListResponseItem

		r.OetLineItem = *item
		detailById := rpc.GetCorporationDetailById(ctx, item.SubCorporationId)
		if detailById != nil {
			r.Fleet = detailById.FleetName
			r.BranchId = detailById.BranchId
			r.Branch = detailById.BranchName
			if detailById.Item != nil {
				r.SubCorporation = detailById.Item.Name
			}
		}

		rspD = append(rspD, r)
	}

	data := map[string]interface{}{
		"Items":      rspD,
		"TotalCount": totalCount,
	}
	return response.Success(rsp, data)
}

func (l Line) List(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return LineList(ctx, req, rsp)
}
