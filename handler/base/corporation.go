package base

import (
	protostaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	log "github.com/micro/go-micro/v2/logger"
	"strconv"
)

type Corporation struct{}

type ListRequestParams struct {
	AuthId      string `validate:"required"` // *用户唯一标识
	ParentId    int64
	ParentIdStr string // 兼容小程序无法使用大整数
	Order       string `validate:"required,oneof=desc asc"`
}

type ListResponse struct {
	Id          int64  `json:"Id"`          // 机构id
	IdStr       string `json:"IdStr"`       // 机构id string
	Name        string `json:"Name"`        // 机构名
	ParentId    int64  `json:"ParentId"`    // 父级机构id
	ParentIdStr string `json:"ParentIdStr"` // 父级机构id string
	Type        int64  `json:"Type"`        //类型
}

func (c *Corporation) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q ListRequestParams
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsMissing)
	}
	if q.ParentIdStr != "" {
		parseInt, err := strconv.ParseInt(q.ParentIdStr, 10, 64)
		if err != nil {
			log.Error("strconv.ParseInt err=", err.Error())
			return response.Error(rsp, response.ParamsInvalid)
		}

		q.ParentId = parseInt
	}

	if q.ParentId == 0 {
		q.ParentId = auth.User(ctx).GetTopCorporationId()
	}

	treeItems, treeTotalCount, err := rpc.CorporationTree(ctx, q.ParentId, q.Order)

	if err != nil || treeItems == nil {
		return response.Success(rsp, nil)
	}

	var rspD []ListResponse
	for _, item := range treeItems {
		rspD = append(rspD, ListResponse{
			Id:          item.Id,
			IdStr:       strconv.FormatInt(item.Id, 10),
			Name:        item.Name,
			ParentId:    item.ParentId,
			ParentIdStr: strconv.FormatInt(item.ParentId, 10),
			Type:        item.Type,
		})
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      rspD,
		"TotalCount": treeTotalCount,
	})
}

type ListStaffRequestParams struct {
	AuthId           string `validate:"required"`
	Offset           int64
	Limit            int64  `validate:"required"`
	Order            string `validate:"required,oneof=desc asc"`
	TopCorporationId int64  // 不传获取当前人员所在
	Name             string // 司机姓名
}

// ListStaff 获取顶级机构下所有司机
func (c *Corporation) ListStaff(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q ListStaffRequestParams
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsMissing)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, nil)

	staffs, totalCount := rpc.GetStaffsWithMultiOptions(ctx, protostaff.GetStaffsWithMultiOptionsRequest{
		CorporationIds: corporationIds,
		NameOrStaffId:  q.Name,
		Sex:            3,
		Offset:         q.Offset,
		Limit:          q.Limit,
		Order:          q.Order,
	})

	return response.Success(rsp, map[string]interface{}{
		"Items":      staffs,
		"TotalCount": totalCount,
	})
}
