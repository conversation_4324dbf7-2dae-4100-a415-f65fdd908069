package base

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	protocorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	oet_scs_srv_app "app/org/scs/erpv2/api/proto/rpc/oetline"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	go_api "github.com/micro/go-micro/v2/api/proto"
	"github.com/micro/go-micro/v2/client"
	"strings"
)

type Staff struct {
	Client         client.Client
	StaffIdStr     string  // 工号 模糊搜索 空搜索全部
	StaffName      string  // 姓名 模糊搜索 空搜索全部
	CorporationIds []int64 // 指定机构下的人员 空搜索全部
	IsMore         int64   // 0 不包含敏感信息  1 包含敏感信息

	model.Paginator
}

const (
	MoreOption    = 1
	NonMoreOption = 0
)

type OetStaff struct {
	Id         int64 // staffId
	Name       string
	StaffIdStr string // 工号
}

// GetStaffByStaffIdStr 根据工号查找员工
func (s *Staff) GetStaffByStaffIdStr(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return GetStaffByStaffIdStr(ctx, req, rsp)
}

// GetStaffByStaffIdStr 移动端 查询顶级机构下全部人员 不根据权限
func (s *Staff) AppGetStaffByStaffIdStr(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return AppGetStaffByStaffIdStr(ctx, req, rsp)
}

func GetStaffByStaffIdStr(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Staff
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.ErrorFields("json.Unmarshal err", map[string]interface{}{"err": err.Error()})
		return response.Error(rsp, "OP1001")
	}

	// 获取当前用户授权的机构
	corporationIds := service.AuthCorporationIdProvider(ctx, q.CorporationIds)
	// 获取所有人员
	results, err := GetStaffList(ctx, corporationIds, q.StaffName, q.StaffIdStr, q.IsMore)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	var totalCount = len(results)

	if q.Limit > 0 {
		// 分页
		if q.Limit+q.Offset >= totalCount {
			if q.Offset < totalCount {
				results = results[q.Offset:]
			} else {
			}
		} else {
			results = results[q.Offset:(q.Offset + q.Limit)]
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": totalCount,
	})
}

func AppGetStaffByStaffIdStr(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Staff
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.ErrorFields("json.Unmarshal err", map[string]interface{}{"err": err.Error()})
		return response.Error(rsp, "OP1001")
	}

	// 获取当前用户授权的机构
	corporationIds := service.AuthCorporationIdProvider(ctx, nil)
	// 获取所有人员
	results, err := GetStaffList(ctx, corporationIds, q.StaffName, q.StaffIdStr, q.IsMore)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(results),
	})
}

type RequestParam struct {
	JobCode string `json:"JobCode"`
	Name    string `json:"Name"`
	Phone   string `json:"Phone"`
}

// StaffList 获取数据权限机构下的人员
func (s *Staff) StaffList(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param RequestParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 获取当前用户授权的机构
	corporationIds := service.AuthCorporationIdProvider(ctx, nil)
	results, err := GetStaffList(ctx, corporationIds, param.Name, param.JobCode, NonMoreOption)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(results),
	})
}

func GetStaffList(ctx context.Context, corporationIds []int64, name, jobCode string, isMore int64) ([]map[string]interface{}, error) {
	oetStaffs := rpc.GetStaffsWithCorporationIds(ctx, corporationIds, name)
	if oetStaffs == nil {
		log.ErrorFields("rpc GetStaffsWithCorporationIds is nil", map[string]interface{}{"corporationIds": corporationIds})
		return nil, nil
	}

	var results []map[string]interface{}

	rpcTmpCorporation := make(map[int64]*protocorporation.CorporationItem) // map[corporationId]机构信息
	rpcTmpLine := make(map[int64]*oet_scs_srv_app.OetLineItem)             // map[lineId]

	for i := range oetStaffs {
		if jobCode != "" && !strings.Contains(oetStaffs[i].StaffId, jobCode) {
			continue
		}

		var corpName string
		var lineName string

		// 获取所属机构
		if oetCorporationItem, ok := rpcTmpCorporation[oetStaffs[i].CorporationId]; ok {
			if oetCorporationItem != nil {
				corpName = oetCorporationItem.Name
			}
		} else {
			corporationItem := rpc.GetCorporationById(ctx, oetStaffs[i].CorporationId)
			rpcTmpCorporation[oetStaffs[i].CorporationId] = corporationItem
			if corporationItem != nil {
				corpName = corporationItem.Name
			}
		}

		// 如果是司机获取所属线路
		if oetStaffs[i].LineId > 0 {
			if lineItem, ok := rpcTmpLine[oetStaffs[i].LineId]; ok {
				lineName = lineItem.Name
			} else {
				oetLineItem, _ := rpc.GetLineWithId(ctx, oetStaffs[i].LineId)
				if oetLineItem != nil {
					rpcTmpLine[oetStaffs[i].LineId] = oetLineItem
					lineName = oetLineItem.Name
				}
			}
		}

		var result = map[string]interface{}{
			"Id":                oetStaffs[i].Id,
			"Name":              oetStaffs[i].Name,
			"StaffIdStr":        oetStaffs[i].StaffId,
			"JobStatus":         oetStaffs[i].WorkingState,
			"CorporationId":     oetStaffs[i].CorporationId,
			"Corporation":       corpName,
			"LineId":            oetStaffs[i].LineId,
			"Line":              lineName,
			"JoinCompanyAt":     oetStaffs[i].RegisterTime,
			"DrvLicenseTypeStr": oetStaffs[i].DrvLicenseTypeStr,
			"Sex":               oetStaffs[i].Sex,
			"WorkPostType":      util.MasterToErp[oetStaffs[i].Occupation],
			"Phone":             oetStaffs[i].Phone,
		}

		if isMore == MoreOption {
			result["IdentifyId"] = oetStaffs[i].IdentifyId // 身份证
		}

		results = append(results, result)
	}
	return results, nil
}

//func GetStaffIds(ctx context.Context, corporationIds []int64, name, jobCode string, isMore int64) ([]int64, error) {
//	var (
//		items, _ = GetStaffList(ctx, corporationIds, name, jobCode, isMore)
//		staffIds []int64
//	)
//	for _, v := range items {
//		staffIds = append(staffIds, v["Id"].(int64))
//	}
//	return staffIds, nil
//}

type ListTreeReq struct {
	CorporationId int64 // 机构 Id
}

type ListTreeRsp struct {
	Id       int64 // 机构 Id
	ParentId int64
	Type     int64
	Name     string // 机构名
	Drivers  []Driver
}

type Driver struct {
	Id            int64  // 司机id
	CorporationId int64  // 机构 Id
	Name          string // 司机名
}

func (s *Staff) ListTree(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param ListTreeReq
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var rspD []ListTreeRsp
	briefItems, _, err := rpc.CorporationTree(ctx, param.CorporationId, "ASC")
	if err != nil {
		log.ErrorFields("rpc.CorporationTree error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	for _, item := range briefItems {
		i := ListTreeRsp{
			Id:       item.Id,
			ParentId: item.ParentId,
			Type:     item.Type,
			Name:     item.Name,
			Drivers:  nil,
		}

		//
		corporationIdFalse := rpc.GetStaffsWithCorporationIdFalse(ctx, item.Id)

		for _, staffItem := range corporationIdFalse {
			driver := Driver{
				Id:            staffItem.Id,
				CorporationId: staffItem.CorporationId,
				Name:          staffItem.Name,
			}
			i.Drivers = append(i.Drivers, driver)
		}

		rspD = append(rspD, i)
	}

	return response.Success(rsp, map[string]interface{}{
		"Items": rspD,
	})
}
