package file

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	fileModel "app/org/scs/erpv2/api/model/file"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	proto1 "github.com/micro/go-micro/v2/api/proto"
	uuid "github.com/satori/go.uuid"
	"path"
	"runtime/debug"
)

type File struct {
	AuthId        string `validate:"required"`
	CorporationId int64
	FileName      string `validate:"required"` // 文件名(带后缀名)
	FileData      string `validate:"required"` // 文件流, base64 encode
	FileId        int64  // 文件Id,第1包时,可以为0
	FileSize      int64  `validate:"required"` // 文件大小，单位Byte
	TotalNum      int64  `validate:"required"` // 总包数
	SeqNum        int64  `validate:"required"` // 第几包, 1MB/包 start 1
}

func (f *File) Upload(ctx context.Context, req *proto1.Request, rsp *proto1.Response) error {
	var q File
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.ErrorFields("json.Unmarshal err ", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.ErrorFields("Validator err ", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()

	if q.CorporationId == 0 {
		q.CorporationId = auth.User(ctx).GetCorporationId()
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("recover() error", map[string]interface{}{"err": err, "stack": string(debug.Stack())})
			response.Error(rsp, response.FAIL)
		}
		return
	}()

	fileName := q.FileName // 文件名(带后缀名)
	fileData := q.FileData // 文件流, base64 encode
	fileId := q.FileId     // 上传升级包 Id，第1包时，可以为0
	fileSize := q.FileSize // 文件大小，单位Byte
	totalNum := q.TotalNum // 总包数
	seqNum := q.SeqNum     // 第几包, 1MB/包

	suffix := path.Ext(q.FileName)

	if "" == suffix {
		log.ErrorFields("suffix is empty", nil)
		return response.Error(rsp, response.FileExtInvalid)
	}

	// 限制单文件 15Mb
	if fileSize > (250 * 1024 * 1024) {
		log.ErrorFields("file too big", nil)
		return response.Error(rsp, response.FileSizeOverLimit)
	}

	// 计算此次成功上传后的进度 %
	curSize := seqNum * (fileSize / totalNum)
	progress := int64((float64(curSize) / float64(fileSize)) * 100)

	// 路径前缀 /mnt/www/
	prefixPath := config.Config.AbsDirPath

	// 相对路径 webroot/erp/...
	relativePath := fmt.Sprintf(`%s/erp/%v/%v`, config.Config.WebRoot, q.CorporationId, "files_upload/")

	// 完整路径 /mnt/www/webroot/erp/...
	fullPath := fmt.Sprintf("%s%s", prefixPath, relativePath)

	// 完整路径 + 新文件名(uuid.png)
	var fileFullPath string

	// 文件相对路径 + 新文件名
	var fileNamePath string
	// 首包
	if q.FileId == 0 {
		newUuid := uuid.NewV4()
		uuidStr := newUuid.String()
		newFileName := fmt.Sprintf("%s%s", uuidStr, suffix)

		fileFullPath = fmt.Sprintf("%s%s", fullPath, newFileName)

		// 1 新建文件
		_, _, err = util.CreateFile(newFileName, fileData, fullPath)
		if err != nil {
			log.ErrorFields("util.CreateFile error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}

		var file = fileModel.File{
			CorporationId:    q.CorporationId,
			TopCorporationId: topCorporationId,
			Name:             fileName,
			Suffix:           suffix,
			Path:             fmt.Sprintf(`%s%s`, relativePath, newFileName),
			Size:             fileSize,
			Progress:         progress,
		}

		// 2 存库  将返回的fileId 写入变量
		fileId, err = file.Create()
		if err != nil {
			log.ErrorFields("file.Create error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
		fileNamePath = fmt.Sprintf(`%s%s`, relativePath, newFileName)
	} else { // 非首包
		// 找出之前的文件
		fileInfo := (&fileModel.File{}).FindBy(fileId)
		if err != nil {
			log.ErrorFields("fileModel.FindBy error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbNotFoundRecord)
		}

		// 继续写文件
		//文件完整路径 /mnt/...xx.zip
		fileFullPath = fmt.Sprintf(`%s%s`, prefixPath, fileInfo.Path)
		err = util.OpenFileWriteData(fileFullPath, fileData)
		if err != nil {
			log.ErrorFields("OpenFileWriteData error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}

		fileNamePath = fileInfo.Path

		// 存库
		err = (&fileModel.File{}).UpdateProgress(fileId, progress)
		if err != nil {
			log.ErrorFields("fileModel UpdateProgress error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbUpdateFail)
		}
	}

	var (
		rspUrl  string
		rspPath string
	)

	// 最后一包成功写入返回文件地址
	if seqNum == totalNum {
		rspUrl = fmt.Sprintf(`%s%s`, config.Config.StaticFileHttpPrefix, fileNamePath)
		rspPath = fileNamePath
	}

	return response.Success(rsp, map[string]interface{}{
		"FileId":       fileId,
		"Url":          rspUrl,
		"RelativePath": rspPath,
	})
}
