package setting

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"strings"
)

type DeviceFactory struct {
	settingModel.DeviceFactory
}

func (df *DeviceFactory) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DeviceFactory
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	factories := (&settingModel.DeviceFactory{}).GetAll(auth.User(ctx).GetTopCorporationId(), param.Name, 0, 0, 0)

	for i := range factories {
		users := (&settingModel.DeviceFactoryHasUser{}).GetBy(factories[i].Id, 0, nil)
		for j := range users {
			children := (&settingModel.DeviceFactoryHasUser{}).GetBy(factories[i].Id, users[j].Id, nil)
			if children != nil {
				for k := range children {
					children[k].Children = (&settingModel.DeviceFactoryHasUser{}).GetBy(factories[i].Id, children[k].Id, nil)
				}
			}
			users[j].Children = children
		}
		factories[i].Users = users
	}

	return response.Success(rsp, factories)
}

type ImportParam struct {
	FileData string `json:"FileData"`
}

func (df *DeviceFactory) Import(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ImportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			response.Error(rsp, response.FAIL)
		}
		return
	}()
	user := auth.User(ctx).GetUser()
	// 0全称* 1简称* 2缩写* 3厂家性质* 4联系人  5联系方式
	sheet := excelFile.Sheets[0]
	var failRows []int
	var successCount int64

	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)
		if len(row.Cells) < 4 {
			continue
		}
		if row.Cells[0].String() == "" || row.Cells[1].String() == "" || row.Cells[2].String() == "" || row.Cells[3].String() == "" {
			failRows = append(failRows, i+1)
			continue
		}

		//是否存在
		fac := (&settingModel.DeviceFactory{}).FirstByName(user.TopCorporationId, row.Cells[0].String(), row.Cells[1].String(), row.Cells[2].String())
		if fac.Id > 0 {
			failRows = append(failRows, i+1)
			continue
		}

		var factory settingModel.DeviceFactory
		factory.OpUserId = user.Id
		factory.OpUserName = user.Name
		factory.TopCorporationId = user.TopCorporationId

		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 0:
				factory.Name = row.Cells[0].String()
			case 1:
				factory.SimpleName = row.Cells[1].String()
			case 2:
				factory.ShotName = row.Cells[2].String()
			case 3:
				attrs := strings.Split(row.Cells[3].String(), ",")
				//1品牌方  2供货方  3保修方  4过保维修放
				for i := range attrs {
					if attrs[i] == "1" {
						factory.IsBrandSide = util.StatusForTrue
					}
					if attrs[i] == "2" {
						factory.IsSupplier = util.StatusForTrue
					}
					if attrs[i] == "3" {
						factory.IsHandler = util.StatusForTrue
					}
					if attrs[i] == "4" {
						factory.IsRepairer = util.StatusForTrue
					}
				}

			case 4:
				factory.Contactor = row.Cells[4].String()
			case 5:
				factory.Contact = row.Cells[5].String()
			}
		}
		err = factory.Create()
		if err != nil {
			failRows = append(failRows, i+1)
			continue
		}

		successCount++
	}

	return response.Success(rsp, map[string]interface{}{
		"SuccessCount": successCount,
		"FailCount":    len(failRows),
		"FailRows":     failRows,
	})
}

func (df *DeviceFactory) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DeviceFactory
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if err = util.Validator().Struct(&param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	user := auth.User(ctx).GetUser()
	//是否存在
	fac := (&settingModel.DeviceFactory{}).FirstByName(user.GetTopCorporationId(), param.Name, param.SimpleName, param.ShotName)
	if fac.Id > 0 {
		return response.Error(rsp, response.DbObjectDuplicate)
	}

	param.DeviceFactory.TopCorporationId = user.TopCorporationId
	param.DeviceFactory.OpUserId = user.Id
	param.DeviceFactory.OpUserName = user.Name

	err = param.DeviceFactory.Create()
	if err != nil {
		log.ErrorFields("DeviceFactory.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func (df *DeviceFactory) Update(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DeviceFactory
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if err = util.Validator().Struct(&param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	factory := (&settingModel.DeviceFactory{}).FirstBy(param.Id)

	if factory.Id == 0 {
		log.ErrorFields("FirstBy not fund data", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//是否存在
	fac := (&settingModel.DeviceFactory{}).FirstByName(auth.User(ctx).GetTopCorporationId(), param.Name, param.SimpleName, param.ShotName)
	if fac.Id > 0 && fac.Id != factory.Id {
		return response.Error(rsp, response.DbObjectDuplicate)
	}

	err = param.DeviceFactory.Update()
	if err != nil {
		log.ErrorFields("DeviceFactory.Update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func (df *DeviceFactory) Del(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DeviceFactory
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	factory := (&settingModel.DeviceFactory{}).FirstBy(param.Id)

	if factory.Id == 0 {
		log.ErrorFields("FirstBy not fund data", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = param.DeviceFactory.Delete()
	if err != nil {
		log.ErrorFields("DeviceFactory.Delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, nil)
}

type DeviceFactoryUser struct {
	DeviceFactoryId int64
	Users           []settingModel.DeviceFactoryHasUser
}

func (df *DeviceFactory) BindUser(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DeviceFactoryUser
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	authUser := auth.User(ctx).GetUser()

	tx := model.DB().Begin()
	err = (&settingModel.DeviceFactoryHasUser{}).DeleteBy(tx, param.DeviceFactoryId)
	for _, user := range param.Users {
		if user.UserId == 0 {
			continue
		}

		user.DeviceFactoryId = param.DeviceFactoryId
		user.OpUserId = authUser.Id
		user.OpUserName = authUser.Name
		user.Pid = 0
		err = user.TransactionCreate(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("DeviceFactoryHasUser.TransactionCreate", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}

		if len(user.Children) > 0 {
			for _, child := range user.Children {
				child.DeviceFactoryId = param.DeviceFactoryId
				child.OpUserId = authUser.Id
				child.OpUserName = authUser.Name
				child.Pid = user.Id
				err = child.TransactionCreate(tx)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("DeviceFactoryHasUser.TransactionCreate", map[string]interface{}{"err": err})
					return response.Error(rsp, response.FAIL)
				}

				if len(child.Children) > 0 {
					for _, child2 := range child.Children {
						child2.DeviceFactoryId = param.DeviceFactoryId
						child2.OpUserId = authUser.Id
						child2.OpUserName = authUser.Name
						child2.Pid = child.Id
						err = child2.TransactionCreate(tx)
						if err != nil {
							tx.Rollback()
							log.ErrorFields("DeviceFactoryHasUser.TransactionCreate", map[string]interface{}{"err": err})
							return response.Error(rsp, response.FAIL)
						}
					}
				}
			}
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}
