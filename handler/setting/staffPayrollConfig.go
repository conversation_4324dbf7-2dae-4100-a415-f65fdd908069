package setting

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/util/response"
	"context"
	api "github.com/micro/go-micro/v2/api/proto"
)

type StaffPayrollConfigHandler struct {
	setting.StaffPayrollConfig
	Data        []setting.StaffPayrollConfig `json:"Data"`
	ConfigTypes []int64                      `json:"ConfigTypes"` // 配置类型
	model.Paginator
}

func (sa *StaffPayrollConfigHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form StaffPayrollConfigHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	list, totalCount, err := (&setting.StaffPayrollConfig{}).List(form.ConfigTypes, form.PayRollType, form.Paginator)
	if err != nil {
		log.ErrorFields("StaffPayrollConfig List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (sa *StaffPayrollConfigHandler) Save(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form StaffPayrollConfigHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if len(form.Data) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	for _, config := range form.Data {
		if config.Id == 0 {
			_ = config.Create()
		} else {
			_ = config.Update()
		}
	}
	return response.Success(rsp, nil)
}

func (sa *StaffPayrollConfigHandler) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form StaffPayrollConfigHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	err := (&setting.StaffPayrollConfig{}).Delete(form.Id)
	if err != nil {
		log.ErrorFields("StaffPayrollConfig Create error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}
