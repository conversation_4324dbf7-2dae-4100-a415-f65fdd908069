package setting

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type LineAllowancePrice struct {
	settingModel.LineAllowancePriceSetting
	model.Paginator
}

func (lap *LineAllowancePrice) Update(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineAllowancePrice
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	setting := (&settingModel.LineAllowancePriceSetting{}).FirstById(param.Id)
	if setting.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	setting.ParseOpUser(ctx)
	setting.Price = param.Price
	setting.SalaryPrice = param.SalaryPrice
	setting.WashPrice = param.WashPrice
	setting.UseMonth = param.UseMonth

	startMonth := param.UseMonth.ToTime()
	startAt := time.Date(startMonth.Year(), startMonth.Month()-1, 26, 0, 0, 0, 0, time.Local)
	setting.UseStartAt = model.LocalTime(startAt)
	setting.CreatedAt = model.LocalTime(time.Now())
	setting.UpdatedAt = model.LocalTime(time.Now())

	err = setting.Create()
	if err != nil {
		log.ErrorFields("Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, nil)
}

func (lap *LineAllowancePrice) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineAllowancePrice
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	settings, count := (&settingModel.LineAllowancePriceSetting{}).GetBy(param.CorporationId, param.LineId, param.Paginator)

	for i := range settings {
		settings[i].CorporationId, settings[i].CorporationName = settings[i].Corporations.GetCorporation()
	}
	return response.Success(rsp, map[string]interface{}{"TotalCount": count, "Items": settings})
}

func (lap *LineAllowancePrice) LineRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineAllowancePrice
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	settings := (&settingModel.LineAllowancePriceSetting{}).GetByLineId(param.CorporationId, param.LineId)

	for i := range settings {
		settings[i].CorporationId, settings[i].CorporationName = settings[i].Corporations.GetCorporation()
	}
	return response.Success(rsp, map[string]interface{}{"Items": settings})
}
