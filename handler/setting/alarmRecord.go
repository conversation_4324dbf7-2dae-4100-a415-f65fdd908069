package setting

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/model/safety"
	settingModel "app/org/scs/erpv2/api/model/setting"
	accidentSettingModel "app/org/scs/erpv2/api/model/setting/accident"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type AlarmRecord struct {
	ModelId         string           `json:"ModelId"`   //模块  safety_model:事故管理
	AlarmType       int64            `json:"AlarmType"` //告警类型  1预警 2告警
	StartDay        int64            `json:"StartDay"`
	EndDay          int64            `json:"EndDay"`
	CreatedStartAt  *model.LocalTime `json:"CreatedStartAt"`
	CreatedEndAt    *model.LocalTime `json:"CreatedEndAt"`
	AlarmCategoryId int64            `json:"AlarmCategoryId"`
	ApproveUserName string           `json:"ApproveUserName"`
	FinishStatus    int64            `json:"FinishStatus"`
	StartMoney      int64            `json:"StartMoney"`
	EndMoney        int64            `json:"EndMoney"`
	VehicleId       int64            `json:"VehicleId"`
	DriverId        int64            `json:"DriverId"`
	AccidentCode    string           `json:"AccidentCode"`
	AccidentCateId  int64            `json:"AccidentCateId"`
	CorporationId   int64            `json:"CorporationId"`
	RecordId        int64            `json:"RecordId"`
	model.Paginator
}

func (ar *AlarmRecord) ProcessTimeout(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AlarmRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var processStart, processEnd time.Time
	if param.CreatedStartAt != nil {
		processStart = time.Time(*param.CreatedStartAt)
	}
	if param.CreatedEndAt != nil {
		processEnd = time.Time(*param.CreatedEndAt)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()

	records, count := (&settingModel.ProcessTimeoutAlarmRecord{}).GetBy(topCorporationId, param.ModelId, param.AlarmType, param.StartDay, param.EndDay,
		param.AlarmCategoryId, param.FinishStatus, param.ApproveUserName, processStart, processEnd, param.Paginator)

	return response.Success(rsp, map[string]interface{}{
		"Items":      records,
		"TotalCount": count,
	})
}

func (ar *AlarmRecord) ProcessTimeoutExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AlarmRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var processStart, processEnd time.Time
	if param.CreatedStartAt != nil {
		processStart = time.Time(*param.CreatedStartAt)
	}
	if param.CreatedEndAt != nil {
		processEnd = time.Time(*param.CreatedEndAt)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()

	records, count := (&settingModel.ProcessTimeoutAlarmRecord{}).GetBy(topCorporationId, param.ModelId, param.AlarmType, param.StartDay, param.EndDay,
		param.AlarmCategoryId, param.FinishStatus, param.ApproveUserName, processStart, processEnd, param.Paginator)

	return response.Success(rsp, map[string]interface{}{
		"Items":      records,
		"TotalCount": count,
	})
}

func (ar *AlarmRecord) AccidentTimeout(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AlarmRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var createdStartAt, createdEndAt time.Time
	if param.CreatedStartAt != nil {
		createdStartAt = time.Time(*param.CreatedStartAt)
	}
	if param.CreatedEndAt != nil {
		createdEndAt = time.Time(*param.CreatedEndAt)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()

	records, count, sumMoney := (&accidentSettingModel.AccidentTimeoutAlarmRecord{}).GetBy(topCorporationId, param.CorporationId, param.AccidentCateId, param.AlarmType, param.StartDay, param.EndDay,
		param.StartMoney, param.EndMoney, param.VehicleId, param.DriverId, param.AlarmCategoryId, param.FinishStatus, param.AccidentCode, createdStartAt, createdEndAt, param.Paginator)

	for i := range records {
		var accident safety.TrafficAccident
		_ = accident.FindBy(records[i].TrafficAccidentId)
		records[i].CorporationId, records[i].CorporationName = accident.GetCorporation()
	}
	return response.Success(rsp, map[string]interface{}{
		"Items":      records,
		"TotalCount": count,
		"SumMoney":   sumMoney,
	})
}

func (ar *AlarmRecord) AccidentTimeoutExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AlarmRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var createdStartAt, createdEndAt time.Time
	if param.CreatedStartAt != nil {
		createdStartAt = time.Time(*param.CreatedStartAt)
	}
	if param.CreatedEndAt != nil {
		createdEndAt = time.Time(*param.CreatedEndAt)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()

	records, count, sumMoney := (&accidentSettingModel.AccidentTimeoutAlarmRecord{}).GetBy(topCorporationId, param.CorporationId, param.AccidentCateId, param.AlarmType, param.StartDay, param.EndDay,
		param.StartMoney, param.EndMoney, param.VehicleId, param.DriverId, param.AlarmCategoryId, param.FinishStatus, param.AccidentCode, createdStartAt, createdEndAt, param.Paginator)
	for i := range records {
		var accident safety.TrafficAccident
		_ = accident.FindBy(records[i].TrafficAccidentId)
		records[i].CorporationId, records[i].CorporationName = accident.GetCorporation()
	}
	return response.Success(rsp, map[string]interface{}{
		"Items":      records,
		"TotalCount": count,
		"SumMoney":   sumMoney,
	})
}

func (ar *AlarmRecord) ProcessTimeoutAlarmIgnore(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AlarmRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	record := (&settingModel.ProcessTimeoutAlarmRecord{}).FirstBy(param.RecordId)
	if record.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = record.UpdateColumn("IsPushWarningNotify", util.StatusForFalse)
	if err != nil {
		log.ErrorFields("UpdateColumn error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (ar *AlarmRecord) ProcessTimeoutAlarmDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AlarmRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	record := (&settingModel.ProcessTimeoutAlarmRecord{}).FirstBy(param.RecordId)
	if record.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = record.Delete()
	if err != nil {
		log.ErrorFields("delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	var process processModel.LbpmApplyProcess
	err = process.FindBy(record.FormInstanceId)
	if err != nil {
		log.ErrorFields("process.FindBy error", map[string]interface{}{"err": err})
		return response.Success(rsp, nil)
	}

	err = process.UpdateColumn("IsPushWarningNotify", util.StatusForFalse)
	if err != nil {
		log.ErrorFields("process.UpdateColumn error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (ar *AlarmRecord) AccidentTimeoutAlarmIgnore(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AlarmRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	record := (&accidentSettingModel.AccidentTimeoutAlarmRecord{}).FirstBy(param.RecordId)
	if record.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = record.UpdateColumn("IsPushWarningNotify", util.StatusForFalse)
	if err != nil {
		log.ErrorFields("UpdateColumn error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	err = (&safety.TrafficAccident{}).UpdateColumn(record.TrafficAccidentId, "IsPushWarningNotify", util.StatusForFalse)
	if err != nil {
		log.ErrorFields("TrafficAccident.UpdateColumn error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (ar *AlarmRecord) AccidentTimeoutAlarmDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AlarmRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	record := (&accidentSettingModel.AccidentTimeoutAlarmRecord{}).FirstBy(param.RecordId)
	if record.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = record.Delete()
	if err != nil {
		log.ErrorFields("delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	err = (&safety.TrafficAccident{}).UpdateColumn(record.TrafficAccidentId, "IsPushWarningNotify", util.StatusForFalse)
	if err != nil {
		log.ErrorFields("TrafficAccident.UpdateColumn error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}
