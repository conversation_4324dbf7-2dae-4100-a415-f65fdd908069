package setting

import (
	"app/org/scs/erpv2/api/log"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

type GlobalSetting struct {
	Type    int64 `json:"Type"`
	FleetId int64 `json:"FleetId"`
}

func (h *GlobalSetting) Setting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param GlobalSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	setting := (&settingModel.GlobalSetting{}).GetBy(auth.User(ctx).GetTopCorporationId(), param.Type)

	return response.Success(rsp, setting)
}

func (h *GlobalSetting) SaveSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param settingModel.GlobalSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.ParseOpUser(ctx)
	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()
	err = param.UpdateOrCreate()
	if err != nil {
		log.ErrorFields("SaveSetting error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}
