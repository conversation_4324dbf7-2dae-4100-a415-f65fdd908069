package setting

import (
	"app/org/scs/erpv2/api/log"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

type ListSetting struct {
	settingModel.ColumnSetting
}

func (ls *ListSetting) CustomColumn(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	user := auth.User(ctx).GetUser()

	param.UserId = user.Id
	param.UserName = user.Name
	param.TopCorporationId = user.TopCorporationId

	oldSetting := (&settingModel.ColumnSetting{}).FirstByUserId(user.Id, param.TableName)

	if oldSetting.Id > 0 {
		param.Id = oldSetting.Id
		err = param.Update()
	} else {
		err = param.Create()
	}

	if err != nil {
		log.ErrorFields("param.Create(Update) error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func (ls *ListSetting) SettingInfo(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	setting := (&settingModel.ColumnSetting{}).FirstByUserId(auth.User(ctx).GetUserId(), param.TableName)

	return response.Success(rsp, setting)
}
