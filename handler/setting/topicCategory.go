package setting

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/util/response"
	"context"
	api "github.com/micro/go-micro/v2/api/proto"
)

type TopicCategoryHandler struct {
	Id       int64                   `json:"Id"`
	Data     []setting.TopicCategory `json:"Data"`
	Category string                  `json:"Category"`
	model.Paginator
}

func (sa *TopicCategoryHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form TopicCategoryHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	list, totalCount, err := (&setting.TopicCategory{}).List(form.Category, form.Paginator)
	if err != nil {
		log.ErrorFields("TopicCategory List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (sa *TopicCategoryHandler) Save(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form TopicCategoryHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Data == nil || len(form.Data) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	for _, v := range form.Data {
		var err error
		if v.Id == 0 {
			err = v.Create()
		} else {
			err = v.Update()
		}
		if err != nil {
			log.ErrorFields("TopicCategory Save error", map[string]interface{}{"error": err.Error()})
			return response.Error(rsp, response.DbQueryFail)
		}
	}
	return response.Success(rsp, nil)
}

func (sa *TopicCategoryHandler) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form TopicCategoryHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	err := (&setting.TopicCategory{}).Delete(form.Id)
	if err != nil {
		log.ErrorFields("TopicCategory Delete error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}
