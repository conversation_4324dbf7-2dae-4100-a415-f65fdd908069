package accident

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	accidentSettingModel "app/org/scs/erpv2/api/model/setting/accident"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

type AccidentProcessTimeout struct {
	Records      []accidentSettingModel.AccidentProcessTimeoutSetting `json:"Records"`
	WarningValue int64                                                `json:"WarningValue"`
	AlarmValue   int64                                                `json:"AlarmValue"`
}

func (as *AccidentSetting) ProcessTimeoutSettingSave(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AccidentProcessTimeout
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	user := auth.User(ctx).GetUser()
	if len(param.Records) == 0 {
		param.Records = append(param.Records, accidentSettingModel.AccidentProcessTimeoutSetting{TemplateFormId: config.TrafficAccidentLendMoneyFormTemplate})
	}

	tx := model.DB().Begin()
	for i := range param.Records {
		param.Records[i].WarningValue = param.WarningValue
		param.Records[i].AlarmValue = param.AlarmValue
		param.Records[i].OpUserId = user.Id
		param.Records[i].OpUserName = user.Name
		param.Records[i].TopCorporationId = user.TopCorporationId
		if param.Records[i].Id > 0 {
			err := param.Records[i].TransactionUpdate(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("AccidentProcessTimeoutSetting TransactionUpdate error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbUpdateFail)
			}
		} else {
			if (&accidentSettingModel.AccidentProcessTimeoutSetting{}).ExistBy(user.TopCorporationId, param.Records[i].TemplateFormId) {
				return response.Error(rsp, response.DbObjectDuplicate)
			}

			err := param.Records[i].TransactionCreate(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("AccidentProcessTimeoutSetting TransactionCreate error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}
		}
		for j := range param.Records[i].Details {
			detail := param.Records[i].Details[j]
			detail.AccidentProcessTimeoutSettingId = param.Records[i].Id
			if detail.ActionType == "create" {
				err = detail.TransactionCreate(tx)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("AccidentProcessTimeoutSettingDetail TransactionCreate error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbSaveFail)
				}
			}

			if detail.ActionType == "update" {
				err = detail.TransactionUpdate(tx)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("AccidentProcessTimeoutSettingDetail TransactionUpdate error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbUpdateFail)
				}
			}

			if detail.ActionType == "delete" {
				err = detail.TransactionDelete(tx)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("AccidentProcessTimeoutSettingDetail TransactionDelete error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbDeleteFail)
				}
			}
		}
	}

	tx.Commit()

	return response.Success(rsp, response.SUCCESS)
}

func (as *AccidentSetting) ProcessTimeoutSettingAll(ctx context.Context, req *api.Request, rsp *api.Response) error {
	topCorporationId := auth.User(ctx).GetTopCorporationId()

	settings := (&accidentSettingModel.AccidentProcessTimeoutSetting{}).GetAll(topCorporationId)
	for i := range settings {
		settings[i].Details = (&accidentSettingModel.AccidentProcessTimeoutSettingDetail{}).GetBy(settings[i].Id)
	}

	var warningValue, alarmValue int64
	if len(settings) > 0 {
		warningValue = settings[0].WarningValue
		alarmValue = settings[0].AlarmValue
	}

	return response.Success(rsp, map[string]interface{}{
		"Records":      settings,
		"WarningValue": warningValue,
		"AlarmValue":   alarmValue,
	})
}
