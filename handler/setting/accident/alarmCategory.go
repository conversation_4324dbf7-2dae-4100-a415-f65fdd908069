package accident

import (
	"app/org/scs/erpv2/api/log"
	accidentSettingModel "app/org/scs/erpv2/api/model/setting/accident"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

type AlarmCategory struct {
	accidentSettingModel.AccidentAlarmCategorySetting
}

func (as *AccidentSetting) AlarmCategoryAll(ctx context.Context, req *api.Request, rsp *api.Response) error {
	topCorporationId := auth.User(ctx).GetTopCorporationId()

	alarms := (&accidentSettingModel.AccidentAlarmCategorySetting{}).GetAll(topCorporationId)

	for i := range alarms {
		if alarms[i].Type == util.AlarmCategoryTypeFormProcess {
			alarms[i].IsUsed = (&accidentSettingModel.AccidentProcessTimeoutSettingDetail{}).IsExistAlarmCategory(alarms[i].Id)
		} else {
			alarms[i].IsUsed = (&accidentSettingModel.AccidentTimeoutSettingDetail{}).IsExistAlarmCategory(alarms[i].Id)
		}
	}

	return response.Success(rsp, alarms)
}

func (as *AccidentSetting) AlarmCategoryCreate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AlarmCategory
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.AccidentAlarmCategorySetting.Title == "" {
		return response.Error(rsp, response.ParamsMissing)
	}

	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()
	param.ParseOpUser(ctx)

	err = param.AccidentAlarmCategorySetting.Create()
	if err != nil {
		log.ErrorFields("AccidentAlarmCategorySetting Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, response.SUCCESS)
}

func (as *AccidentSetting) AlarmCategoryUpdate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AlarmCategory
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.AccidentAlarmCategorySetting.Title == "" || param.AccidentAlarmCategorySetting.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	alarm := (&accidentSettingModel.AccidentAlarmCategorySetting{}).FindBy(param.Id)
	if alarm.Id == 0 {
		log.ErrorFields("AccidentAlarmCategorySetting FindBy not record", nil)
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()
	param.ParseOpUser(ctx)

	err = param.AccidentAlarmCategorySetting.Update()
	if err != nil {
		log.ErrorFields("AccidentAlarmCategorySetting Update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, response.SUCCESS)
}

func (as *AccidentSetting) AlarmCategoryDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AlarmCategory
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.AccidentAlarmCategorySetting.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	alarm := (&accidentSettingModel.AccidentAlarmCategorySetting{}).FindBy(param.Id)
	if alarm.Id == 0 {
		log.ErrorFields("AccidentAlarmCategorySetting FindBy not record", nil)
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	if alarm.Type == util.AlarmCategoryTypeFormProcess {
		alarm.IsUsed = (&accidentSettingModel.AccidentProcessTimeoutSettingDetail{}).IsExistAlarmCategory(alarm.Id)
	} else {
		alarm.IsUsed = (&accidentSettingModel.AccidentTimeoutSettingDetail{}).IsExistAlarmCategory(alarm.Id)
	}

	if alarm.IsUsed {
		return response.Error(rsp, response.DbDeleteFail)
	}

	err = param.AccidentAlarmCategorySetting.Delete()
	if err != nil {
		log.ErrorFields("AccidentAlarmCategorySetting Delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, response.SUCCESS)
}
