package accident

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	accidentSettingModel "app/org/scs/erpv2/api/model/setting/accident"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

type AccidentSetting struct{}

type AccidentTimeout struct {
	Records      []accidentSettingModel.AccidentTimeoutSetting `json:"Records"`
	WarningValue int64                                         `json:"WarningValue"`
	AlarmValue   int64                                         `json:"AlarmValue"`
}

func (as *AccidentSetting) AccidentTimeoutSettingSave(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AccidentTimeout
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	user := auth.User(ctx).GetUser()
	topCorporationId := user.GetTopCorporationId()

	if len(param.Records) == 0 {
		param.Records = append(param.Records, accidentSettingModel.AccidentTimeoutSetting{AccidentCate: util.TrafficAccidentCateForOne})
	}

	tx := model.DB().Begin()
	for i := range param.Records {
		param.Records[i].WarningValue = param.WarningValue
		param.Records[i].AlarmValue = param.AlarmValue
		param.Records[i].OpUserId = user.Id
		param.Records[i].OpUserName = user.Name
		param.Records[i].TopCorporationId = topCorporationId
		if param.Records[i].Id > 0 {
			err := param.Records[i].TransactionUpdate(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("AccidentTimeoutSetting TransactionUpdate error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbUpdateFail)
			}
		} else {
			if (&accidentSettingModel.AccidentTimeoutSetting{}).ExistBy(topCorporationId, param.Records[i].AccidentCate) {
				return response.Error(rsp, response.DbObjectDuplicate)
			}
			err := param.Records[i].TransactionCreate(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("AccidentTimeoutSetting TransactionCreate error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}
		}
		for j := range param.Records[i].Details {
			detail := param.Records[i].Details[j]
			detail.AccidentTimeoutSettingId = param.Records[i].Id
			if detail.ActionType == "create" {
				err = detail.TransactionCreate(tx)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("AccidentTimeoutSettingDetail TransactionCreate error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbSaveFail)
				}
			}

			if detail.ActionType == "update" {
				err = detail.TransactionUpdate(tx)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("AccidentTimeoutSettingDetail TransactionUpdate error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbUpdateFail)
				}
			}

			if detail.ActionType == "delete" {
				err = detail.TransactionDelete(tx)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("AccidentTimeoutSettingDetail TransactionDelete error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbDeleteFail)
				}
			}
		}
	}

	tx.Commit()

	return response.Success(rsp, response.SUCCESS)
}

func (as *AccidentSetting) AccidentTimeoutSettingAll(ctx context.Context, req *api.Request, rsp *api.Response) error {
	topCorporationId := auth.User(ctx).GetTopCorporationId()

	settings := (&accidentSettingModel.AccidentTimeoutSetting{}).GetAll(topCorporationId)
	for i := range settings {
		settings[i].Details = (&accidentSettingModel.AccidentTimeoutSettingDetail{}).GetBy(settings[i].Id)
	}

	var warningValue, alarmValue int64
	if len(settings) > 0 {
		warningValue = settings[0].WarningValue
		alarmValue = settings[0].AlarmValue
	}

	return response.Success(rsp, map[string]interface{}{
		"Records":      settings,
		"WarningValue": warningValue,
		"AlarmValue":   alarmValue,
	})
}
