package stock

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	stockModel "app/org/scs/erpv2/api/model/stock"
	oet_scs_srv_public "app/org/scs/erpv2/api/proto/rpc/corporation"
	oet_scs_srv_app "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/process/dingTalkBpm"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/micro/go-micro/v2/api/proto"
	"github.com/micro/go-micro/v2/client"
	"time"
)

type Stock struct {
	client.Client
}

type StockParam struct {
	Data []stockModel.Stock
	stockModel.Stock

	Name                        string // 库存物料名
	Supplier                    string // 供货方
	StockMaterialTypeClassId    int64  // 库存物料类型id 大类
	StockMaterialTypeCategoryId int64  // 库存物料类型id 种类
	LiableStaffId               int64  // 责任人人员id
	model.Paginator

	ApplyStaffId int64
	StartAt      model.LocalTime `json:"StartAt"`
	EndAt        model.LocalTime `json:"EndAt"`
}

func (st *Stock) BranchAddStock(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StockParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.ErrorFields("GetTopCorporationId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.Forbidden)
	}

	//tx := model.DB().Begin()
	//defer func() {
	//	if err == nil {
	//		tx.Commit()
	//	} else {
	//		tx.Rollback()
	//	}
	//}()

	for i, stock := range param.Data {
		if stock.StockMaterial.StockMaterialTypeId == 0 || stock.StockMaterial.Name == "" || len(stock.StockMaterial.LiableStaffs) == 0 ||
			stock.StockMaterial.SupplierPresetId == 0 || stock.StockMaterial.Unit == "" {
			log.ErrorFields(`create param missing`, map[string]interface{}{"err": nil})
			//err = errors.New(`create param missing`)
			return response.Error(rsp, response.ParamsMissing)
		}

		// 物料
		param.Data[i].StockMaterial.Id = model.Id()
		param.Data[i].StockMaterial.GroupId = topCorporationId

		// 物料责任人
		for j, staff := range stock.StockMaterial.LiableStaffs {
			if staff.LiablePresetId == 0 {
				log.ErrorFields(`staff.LiablePresetId == 0`, map[string]interface{}{"err": nil})
				return response.Error(rsp, response.ParamsMissing)
			}
			param.Data[i].StockMaterial.LiableStaffs[j].GroupId = topCorporationId
			param.Data[i].StockMaterial.LiableStaffs[j].StockMaterialId = param.Data[i].StockMaterial.Id
		}

		// 库存
		param.Data[i].GroupId = topCorporationId
		param.Data[i].StockMaterialId = param.Data[i].StockMaterial.Id

	}

	err = param.Stock.BranchCreate(param.Data)
	if err != nil {
		log.ErrorFields(`Stock.BranchCreate error`, map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	return response.Success(rsp, nil)

}

type MaterialParam struct {
	stockModel.StockMaterial
}

func (st *Stock) EditStockMaterial(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param MaterialParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.ErrorFields("GetTopCorporationId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.Forbidden)
	}

	if len(param.LiableStaffs) == 0 {
		log.ErrorFields("len(param.LiableStaffs) == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsMissing)
	}

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	// 更新物料表信息
	err = param.TransactionUpdateInfo(tx, param.Id)
	if err != nil {
		log.ErrorFields(`Stock.BranchCreate error`, map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}
	// 更新物料责任人表信息
	// 删除所有责任人
	// 新增此次上传的责任人

	err = (&stockModel.StockMaterialLiable{}).TransactionDeleteByStockMaterialId(tx, param.Id)
	if err != nil {
		log.ErrorFields(`TransactionDeleteByStockMaterialId error`, map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	for _, staff := range param.LiableStaffs {
		if staff.LiablePresetId == 0 {
			log.ErrorFields(`staff.LiablePresetId == 0`, map[string]interface{}{"err": nil})
			err = errors.New("params invalid")
			return response.Error(rsp, response.ParamsInvalid)
		}

		staff.GroupId = topCorporationId
		staff.StockMaterialId = param.Id

		err = staff.TransactionCreate(tx)
		if err != nil {
			log.ErrorFields(`TransactionCreate error`, map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	return response.Success(rsp, nil)

}
func (st *Stock) DeleteStockMaterial(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param MaterialParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = param.Recycle()
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)

}

func (st *Stock) ListStock(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StockParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.ErrorFields("GetTopCorporationId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.Forbidden)
	}

	stocks, totalCount, err := param.Stock.GetBy(false, param.Name, param.Supplier, param.StockMaterialTypeClassId, param.StockMaterialTypeCategoryId, param.LiableStaffId, param.Paginator)
	if err != nil {
		log.ErrorFields("Stock GetBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	mapPreset := make(map[int64]stockModel.StockMaterialTypePreset) // map[预设表id]

	mapStaff := make(map[int64]*oet_scs_srv_app.OetStaffItem) // map[staffId]

	mapCorp := make(map[int64]*oet_scs_srv_public.CorporationItem) // map[机构id]

	mapType := make(map[int64]stockModel.StockMaterialType) // map[库存物料类型id]

	for i, stock := range stocks {

		for j, liableStaff := range stock.StockMaterial.LiableStaffs {
			var staffId int64

			if preset, ok := mapPreset[liableStaff.LiablePresetId]; ok {
				staffId = preset.LiableStaffId

			} else {
				var ps stockModel.StockMaterialTypePreset
				err = ps.Find(liableStaff.LiablePresetId)
				if err != nil {
					log.ErrorFields("StockMaterialTypePreset Find error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbQueryFail)
				}

				mapPreset[liableStaff.LiablePresetId] = ps

				staffId = ps.LiableStaffId

			}

			if staff, ok := mapStaff[staffId]; ok {
				stocks[i].StockMaterial.LiableStaffs[j].LiableStaffId = staffId
				stocks[i].StockMaterial.LiableStaffs[j].LiableStaffName = staff.Name
				stocks[i].StockMaterial.LiableStaffs[j].LiableCorporationId = staff.CorporationId

			} else {
				staffItem := rpc.GetStaffWithId(ctx, staffId)
				if staffItem != nil {
					stocks[i].StockMaterial.LiableStaffs[j].LiableStaffId = staffItem.Id
					stocks[i].StockMaterial.LiableStaffs[j].LiableStaffName = staffItem.Name
					stocks[i].StockMaterial.LiableStaffs[j].LiableCorporationId = staffItem.CorporationId
				}
			}

			if corp, ok := mapCorp[stocks[i].StockMaterial.LiableStaffs[j].LiableCorporationId]; ok {
				stocks[i].StockMaterial.LiableStaffs[j].LiableCorporation = corp.Name
			} else {
				corporationItem := rpc.GetCorporationById(ctx, stocks[i].StockMaterial.LiableStaffs[j].LiableCorporationId)
				if corporationItem != nil {
					stocks[i].StockMaterial.LiableStaffs[j].LiableCorporation = corporationItem.Name

					mapCorp[stocks[i].StockMaterial.LiableStaffs[j].LiableCorporationId] = corporationItem
				}
			}
		}

		if stock.StockMaterial.SupplierPresetId != 0 {

			if preset, ok := mapPreset[stock.StockMaterial.SupplierPresetId]; ok {

				stocks[i].StockMaterial.Supplier = preset.Supplier
				stocks[i].StockMaterial.SupplierAbbr = preset.SupplierAbbr

			} else {
				var ps stockModel.StockMaterialTypePreset
				err = ps.Find(stock.StockMaterial.SupplierPresetId)
				if err != nil {
					log.ErrorFields("StockMaterialTypePreset Find error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbQueryFail)
				}

				mapPreset[stock.StockMaterial.SupplierPresetId] = ps

				stocks[i].StockMaterial.Supplier = ps.Supplier
				stocks[i].StockMaterial.SupplierAbbr = ps.SupplierAbbr

			}

		}
		if stock.StockMaterial.StockMaterialTypeId != 0 {

			var category stockModel.StockMaterialType

			if smtCategory, ok := mapType[stock.StockMaterial.StockMaterialTypeId]; ok {
				stocks[i].StockMaterial.StockMaterialTypeName = smtCategory.Name
				category = smtCategory
			} else {
				smtCategory_, err := (&StockMaterialType{}).FindBy(stock.StockMaterial.StockMaterialTypeId)
				if err != nil {
					log.ErrorFields("StockMaterialType FindBy error", map[string]interface{}{"err": err})
					//return response.Error(rsp, response.DbQueryFail)
				}
				stocks[i].StockMaterial.StockMaterialTypeName = smtCategory_.Name
				mapType[stock.StockMaterial.StockMaterialTypeId] = smtCategory_
				category = smtCategory_
			}

			if category.ParentId > 0 {
				if smtClass, ok := mapType[category.ParentId]; ok {
					stocks[i].StockMaterial.StockMaterialTypeClassId = smtClass.Id
					stocks[i].StockMaterial.StockMaterialTypeClassName = smtClass.Name
				} else {
					smtClass_, err := (&StockMaterialType{}).FindBy(category.ParentId)
					if err != nil {
						log.ErrorFields("StockMaterialType smtclass_ FindBy error", map[string]interface{}{"err": err})
						//return response.Error(rsp, response.DbQueryFail)
					}
					stocks[i].StockMaterial.StockMaterialTypeClassId = smtClass_.Id
					stocks[i].StockMaterial.StockMaterialTypeClassName = smtClass_.Name
					mapType[category.ParentId] = smtClass_
				}
			}

		}
	}

	data := map[string]interface{}{
		"Items":      stocks,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)

}

type StockRecordParam struct {
	stockModel.StockRecord
	StartAt model.LocalTime
	EndAt   model.LocalTime
	model.Paginator
}

func (st *Stock) ListStockRecord(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StockRecordParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	records, totalCount, err := param.StockRecord.GetBy(param.StockId, param.ApplyStaffId, time.Time(param.StartAt), time.Time(param.EndAt), param.Paginator)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var stockMap = make(map[int64]string, 0)
	for i := 0; i < len(records); i++ {
		if records[i].ProcessMode == util.StockProcessMode_DingTalk {
			var dingProcessModel processModel.DingTalkApplyProcess
			records[i].ApplyProcessBusinessId = dingProcessModel.GetProcessesByItemId(param.StockRecord.TableName(), records[i].Id).ProcessBusinessId
		}
		if value, ok := stockMap[records[i].StockId]; ok {
			records[i].Unit = value
		} else {
			var stock stockModel.Stock
			stockItem := stock.GetStockById(records[i].StockId)
			if stockItem != nil {
				records[i].Unit = stockItem.StockMaterial.Unit
				stockMap[records[i].StockId] = records[i].Unit
			}
		}
	}

	data := map[string]interface{}{
		"Items":      records,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)
}

func (st *Stock) OperateStock(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StockRecordParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StockRecord.StockId == 0 || param.StockRecord.Type == 0 {
		log.ErrorFields(`param.StockRecord.StockId == 0 || param.StockRecord.Type == 0`, map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsMissing)
	}

	param.ParseOpUser(ctx)

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	var stock stockModel.Stock

	err = stock.Find(tx, param.StockRecord.StockId)
	if err != nil {
		log.ErrorFields(`stock.Find error`, map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	if param.StockRecord.Type == stockModel.InStock {
		stock.Num += param.StockRecord.OperateNum
		err = stock.TransactionUpdateNum(tx, stock.Id, stock.Num)
		if err != nil {
			log.ErrorFields(`stock.TransactionUpdateNum error`, map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	} else if param.StockRecord.Type == stockModel.OutStock {

		if stock.Num >= param.StockRecord.OperateNum {
			stock.Num -= param.StockRecord.OperateNum
			err = stock.TransactionUpdateNum(tx, stock.Id, stock.Num)
			if err != nil {
				log.ErrorFields(`stock.TransactionUpdateNum error`, map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbUpdateFail)
			}
		} else {
			err = errors.New("库存不足")
			log.ErrorFields(`库存不足`, map[string]interface{}{"err": nil})
			return response.Error(rsp, response.DbUpdateFail)
		}

	}

	param.StockRecord.GroupId = stock.GroupId
	param.StockRecord.CurrentNum = stock.Num
	param.StockRecord.ApplyStatus = util.ApplyStatusForDone
	param.StockRecord.ProcessMode = util.StockProcessMode_ManualOperation

	param.StockRecord.ApplyStaffName = rpc.GetStaffNameWithId(ctx, param.StockRecord.ApplyStaffId)

	err = param.StockRecord.TransactionCreate(tx)
	if err != nil {
		log.ErrorFields(`StockRecord.TransactionCreate error`, map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)

}

func (st *Stock) ListRecycleStock(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StockParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.ErrorFields("GetTopCorporationId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.Forbidden)
	}

	stocks, totalCount, err := param.Stock.GetBy(true, param.Name, param.Supplier, param.StockMaterialTypeClassId, param.StockMaterialTypeCategoryId, param.LiableStaffId, param.Paginator)
	if err != nil {
		log.ErrorFields("Stock GetBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	mapPreset := make(map[int64]stockModel.StockMaterialTypePreset) // map[预设表id]

	mapStaff := make(map[int64]*oet_scs_srv_app.OetStaffItem) // map[staffId]

	mapCorp := make(map[int64]*oet_scs_srv_public.CorporationItem) // map[机构id]

	mapType := make(map[int64]stockModel.StockMaterialType) // map[库存物料类型id]

	for i, stock := range stocks {

		for j, liableStaff := range stock.StockMaterial.LiableStaffs {
			var staffId int64

			if preset, ok := mapPreset[liableStaff.LiablePresetId]; ok {
				staffId = preset.LiableStaffId

			} else {
				var ps stockModel.StockMaterialTypePreset
				err = ps.Find(liableStaff.LiablePresetId)
				if err != nil {
					log.ErrorFields("StockMaterialTypePreset Find error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbQueryFail)
				}

				mapPreset[liableStaff.LiablePresetId] = ps

				staffId = ps.LiableStaffId

			}

			if staff, ok := mapStaff[staffId]; ok {
				stocks[i].StockMaterial.LiableStaffs[j].LiableStaffId = staffId
				stocks[i].StockMaterial.LiableStaffs[j].LiableStaffName = staff.Name
				stocks[i].StockMaterial.LiableStaffs[j].LiableCorporationId = staff.CorporationId

			} else {
				staffItem := rpc.GetStaffWithId(ctx, staffId)
				if staffItem != nil {
					stocks[i].StockMaterial.LiableStaffs[j].LiableStaffId = staffItem.Id
					stocks[i].StockMaterial.LiableStaffs[j].LiableStaffName = staffItem.Name
					stocks[i].StockMaterial.LiableStaffs[j].LiableCorporationId = staffItem.CorporationId
				}
			}

			if corp, ok := mapCorp[stocks[i].StockMaterial.LiableStaffs[j].LiableCorporationId]; ok {
				stocks[i].StockMaterial.LiableStaffs[j].LiableCorporation = corp.Name
			} else {
				corporationItem := rpc.GetCorporationById(ctx, stocks[i].StockMaterial.LiableStaffs[j].LiableCorporationId)
				if corporationItem != nil {
					stocks[i].StockMaterial.LiableStaffs[j].LiableCorporation = corporationItem.Name

					mapCorp[stocks[i].StockMaterial.LiableStaffs[j].LiableCorporationId] = corporationItem
				}
			}
		}

		if stock.StockMaterial.SupplierPresetId != 0 {

			if preset, ok := mapPreset[stock.StockMaterial.SupplierPresetId]; ok {

				stocks[i].StockMaterial.Supplier = preset.Supplier
				stocks[i].StockMaterial.SupplierAbbr = preset.SupplierAbbr

			} else {
				var ps stockModel.StockMaterialTypePreset
				err = ps.Find(stock.StockMaterial.SupplierPresetId)
				if err != nil {
					log.ErrorFields("StockMaterialTypePreset Find error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbQueryFail)
				}

				mapPreset[stock.StockMaterial.SupplierPresetId] = ps

				stocks[i].StockMaterial.Supplier = ps.Supplier
				stocks[i].StockMaterial.SupplierAbbr = ps.SupplierAbbr

			}

		}
		if stock.StockMaterial.StockMaterialTypeId != 0 {

			var category stockModel.StockMaterialType

			if smtCategory, ok := mapType[stock.StockMaterial.StockMaterialTypeId]; ok {
				stocks[i].StockMaterial.StockMaterialTypeName = smtCategory.Name
				category = smtCategory
			} else {
				smtCategory_, err := (&StockMaterialType{}).FindBy(stock.StockMaterial.StockMaterialTypeId)
				if err != nil {
					log.ErrorFields("StockMaterialType FindBy error", map[string]interface{}{"err": err})
					//return response.Error(rsp, response.DbQueryFail)
				}
				stocks[i].StockMaterial.StockMaterialTypeName = smtCategory_.Name
				mapType[stock.StockMaterial.StockMaterialTypeId] = smtCategory_
				category = smtCategory_
			}

			if category.ParentId > 0 {
				if smtClass, ok := mapType[category.ParentId]; ok {
					stocks[i].StockMaterial.StockMaterialTypeClassId = smtClass.Id
					stocks[i].StockMaterial.StockMaterialTypeClassName = smtClass.Name
				} else {
					smtClass_, err := (&StockMaterialType{}).FindBy(category.ParentId)
					if err != nil {
						log.ErrorFields("StockMaterialType smtclass_ FindBy error", map[string]interface{}{"err": err})
						//return response.Error(rsp, response.DbQueryFail)
					}
					stocks[i].StockMaterial.StockMaterialTypeClassId = smtClass_.Id
					stocks[i].StockMaterial.StockMaterialTypeClassName = smtClass_.Name
					mapType[category.ParentId] = smtClass_
				}
			}

		}
	}

	data := map[string]interface{}{
		"Items":      stocks,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)

}

func (st *Stock) ListRecycleStockRecord(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return st.ListStockRecord(ctx, req, rsp)
}

type MaterialRequisitionParam struct {
	VehicleId int64 `json:"VehicleId"`
	StaffId   int64 `json:"StaffId"`

	StockId     int64  `json:"StockId"`
	ApplyNumber int64  `json:"ApplyNumber"`
	Remark      string `json:"Remark"`

	StaffName  string `json:"StaffName"`
	OpIp       string `json:"OpIp"`
	OpUserName string `json:"opUserName"`
	License    string `json:"License"`
}

func (st *Stock) MaterialRequisition(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {

	var param MaterialRequisitionParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.VehicleId == 0 || param.StaffId == 0 || param.StockId == 0 || param.ApplyNumber == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	vehicleItem := rpc.GetVehicleWithId(ctx, param.VehicleId)
	if vehicleItem == nil {
		return response.Error(rsp, response.DbQueryFail)
	}
	applyStaffItem := rpc.GetStaffWithId(ctx, param.StaffId)
	if applyStaffItem == nil {
		return response.Error(rsp, response.DbQueryFail)
	}

	// 查询物料信息
	var smt stockModel.Stock
	stockItem := smt.GetStockById(param.StockId)
	if stockItem == nil {
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	if stockItem.Num < param.ApplyNumber {
		return response.Error(rsp, "BPM1002")
	}

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	var stockRecord stockModel.StockRecord

	stockRecord.AssociationObjectType = stockModel.VEHICLE_1
	stockRecord.AssociationObjectId = param.VehicleId
	stockRecord.AssociationObjectName = vehicleItem.License

	stockRecord.GroupId = auth.User(ctx).GetTopCorporationId()
	stockRecord.StockId = param.StockId
	stockRecord.Type = stockModel.OutStock
	stockRecord.ProcessMode = util.StockProcessMode_DingTalk
	stockRecord.ApplyStatus = util.ApplyStatusForNone
	stockRecord.ApplyStaffId = param.StaffId // 申领人与申请人可能是不一致的
	stockRecord.ApplyStaffName = applyStaffItem.Name
	stockRecord.Remark = param.Remark

	err = stockRecord.TransactionCreate(tx)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	user := auth.User(ctx).GetUser()
	dingUserId, err := dingTalkBpm.GetUserId(user.Phone, "")
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	dingUserInfo, err := dingTalkBpm.GetUserInfo(dingUserId, "")
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	if len(dingUserInfo.DeptIdList) == 0 {
		return response.Error(rsp, response.FAIL)
	}

	dingDeptInfo, err := dingTalkBpm.GetDepartmentInfo(dingUserInfo.DeptIdList[0], "")
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	extValue, _ := json.Marshal([]map[string]string{{"name": dingDeptInfo.Name, "id": fmt.Sprintf("%v", dingDeptInfo.DeptId)}})

	param.OpIp = auth.User(ctx).GetClientIp()
	param.OpUserName = auth.User(ctx).GetUser().GetName()
	param.License = vehicleItem.License
	param.StaffName = applyStaffItem.Name

	parentMaterialType, err := (&StockMaterialType{}).FindBy(stockItem.StockMaterial.StockMaterialTypeId)
	pParentMaterialType, err := (&StockMaterialType{}).FindBy(parentMaterialType.ParentId)

	formFieldValue := []map[string]string{
		{"name": "所属部门", "value": dingDeptInfo.Name, "extValue": string(extValue), "componentType": "DepartmentField"},
		{"name": "申领车辆", "value": vehicleItem.License},
		{"name": "申领司机", "value": applyStaffItem.Name},
		{"name": "设备大类", "value": pParentMaterialType.Name},
		{"name": "设备种类", "value": parentMaterialType.Name},
		{"name": "物料名称", "value": stockItem.StockMaterial.Name},
		{"name": "预领数量", "value": fmt.Sprintf("%v", param.ApplyNumber)},
		{"name": util.DingDingFormNameLabel_ActualNumber, "value": fmt.Sprintf("%v", param.ApplyNumber)}, // "*实领数量"
		{"name": "单位", "value": stockItem.StockMaterial.Unit},
		{"name": "备注", "value": stockRecord.Remark},
	}

	processParam, _ := json.Marshal(param)

	err = processService.DispatchDingTalkProcess(user.TopCorporationId, stockRecord.Id, stockRecord.TableName(), string(processParam), config.MaterialRequisitionApplyFormTemplate,
		processService.HandlerUser{
			Id:     user.Id,
			Name:   user.Name,
			Mobile: user.Phone,
		}, formFieldValue)

	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, nil)
}

type StockFormItem struct {
	Name                       string `json:"Name"`                       // 物料名称
	StockMaterialTypeName      string `json:"StockMaterialTypeName"`      // 设备种类
	StockMaterialTypeClassName string `json:"StockMaterialTypeClassName"` // 设备大类
	StockId                    int64  `json:"StockId"`                    // 库存管理id
	Degree                     int64  `json:"Degree"`                     // 次数
	Number                     int64  `json:"Number"`                     // 数量
	ApplyStaffName             string `json:"ApplyStaffName"`             // 申请人名称
	ApplyStaffId               int64  `json:"ApplyStaffId"`               // 申请人ID
}

// 库存申请报表
func (st *Stock) OutStockForm(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StockParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var stockRecord stockModel.StockRecord

	items, totalCount, retItem, err := stockRecord.AggSum(int64(stockModel.OutStock), &stockModel.AggSumParam{
		StartAt:                     time.Time(param.StartAt),
		EndAt:                       time.Time(param.EndAt),
		Name:                        param.Name,
		StockMaterialTypeCategoryId: param.StockMaterialTypeCategoryId, // 库存物料类型id 种类

		ApplyStaffId: param.ApplyStaffId,

		Paginator: param.Paginator,
	})

	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	log.ErrorFields("StockMaterialType AggSum", map[string]interface{}{"records": items})
	var retItems = make([]StockFormItem, 0)

	for i := 0; i < len(items); i++ {
		var stockItem = (&stockModel.Stock{}).GetStockById(items[i].Id)
		if stockItem == nil {
			continue
		}

		smtCategory, err := (&StockMaterialType{}).FindBy(stockItem.StockMaterial.StockMaterialTypeId)
		if err != nil {
			log.ErrorFields("StockMaterialType FindBy error", map[string]interface{}{"err": err})
		}
		parentCategory, err := (&StockMaterialType{}).FindBy(smtCategory.ParentId)
		if err != nil {
			log.ErrorFields("StockMaterialType FindBy error", map[string]interface{}{"err": err})
		}
		retItems = append(retItems, StockFormItem{
			Name:                       stockItem.StockMaterial.Name,                       // 物料名称
			StockMaterialTypeName:      smtCategory.Name,                                   // 设备种类
			StockMaterialTypeClassName: parentCategory.Name,                                // 设备大类
			StockId:                    stockItem.Id,                                       // 库存管理id
			Degree:                     items[i].Degree,                                    // 次数
			Number:                     items[i].Number,                                    // 数量
			ApplyStaffName:             rpc.GetStaffNameWithId(ctx, items[i].ApplyStaffId), // 申请人名称
			ApplyStaffId:               items[i].ApplyStaffId,
		})
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      retItems,
		"TotalCount": totalCount,
		"Item":       retItem,
	})
}

func (st *Stock) OutStockFormDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return st.ListStockRecord(ctx, req, rsp)
}

func (st *Stock) ExportOutStockForm(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return st.OutStockForm(ctx, req, rsp)
}
func (st *Stock) ExportOutStockFormDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return st.OutStockFormDetail(ctx, req, rsp)
}
