package stock

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	stockModel "app/org/scs/erpv2/api/model/stock"
	oet_scs_srv_public "app/org/scs/erpv2/api/proto/rpc/corporation"
	staffProto "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"errors"
	"github.com/micro/go-micro/v2/api/proto"
	"strings"
)

type StockMaterialType struct {
	stockModel.StockMaterialType
}

func (st *Stock) ListStockMaterialType(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StockMaterialType
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.ErrorFields("GetTopCorporationId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.Forbidden)
	}

	smts, totalCount, err := param.GetBy(param.StockMaterialType.Kind, param.StockMaterialType.ParentId, param.StockMaterialType.Name)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	data := map[string]interface{}{
		"Items":      smts,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)

}

func (st *Stock) AddStockMaterialType(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StockMaterialType
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StockMaterialType.Kind == 0 || param.StockMaterialType.Name == "" {
		log.ErrorFields(`param.StockMaterialType.Kind == 0 || param.StockMaterialType.Name == "" || param.StockMaterialType.NameAbbr == ""`, map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsMissing)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.ErrorFields("GetTopCorporationId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.Forbidden)
	}

	param.StockMaterialType.GroupId = topCorporationId

	for i, preset := range param.StockMaterialType.StockMaterialTypePresets {
		if preset.LiableStaffId == 0 {
			log.ErrorFields("StockMaterialTypePresets.LiableStaffId == 0", map[string]interface{}{"err": nil})
			return response.Error(rsp, response.ParamsMissing)
		}
		param.StockMaterialType.StockMaterialTypePresets[i].GroupId = topCorporationId
		param.StockMaterialType.StockMaterialTypePresets[i].Type = stockModel.StockMaterialTypePresetTypeLiable

	}

	err = param.StockMaterialType.Create()
	if err != nil {
		log.ErrorFields("StockMaterialType.Create error", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func (st *Stock) EditStockMaterialType(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StockMaterialType
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StockMaterialType.Id == 0 || param.StockMaterialType.Name == "" || param.StockMaterialType.NameAbbr == "" {
		log.ErrorFields(`param.StockMaterialType.Id == 0 || param.StockMaterialType.Name == "" || param.StockMaterialType.NameAbbr == ""`, map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsMissing)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.ErrorFields("GetTopCorporationId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.Forbidden)
	}

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	err = param.StockMaterialType.TransactionUpdates(tx, param.StockMaterialType.Id,
		map[string]interface{}{"Name": param.StockMaterialType.Name, "NameAbbr": param.StockMaterialType.NameAbbr})
	if err != nil {
		log.ErrorFields(`StockMaterialType.TransactionUpdates error`, map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	for _, preset := range param.StockMaterialType.StockMaterialTypePresets {
		switch strings.ToLower(preset.Option) {
		case "create":
			var smtp stockModel.StockMaterialTypePreset
			//smtp.Id = model.Id()
			smtp.GroupId = topCorporationId
			smtp.StockMaterialTypeId = param.StockMaterialType.Id
			smtp.Type = stockModel.StockMaterialTypePresetTypeLiable

			if preset.LiableStaffId == 0 {
				log.Error("create LiableStaffId missing")
				err = errors.New("liableStaffId missing")
				return response.Error(rsp, response.ParamsInvalid)
			}
			smtp.LiableStaffId = preset.LiableStaffId
			err = smtp.TransactionCreate(tx)
			if err != nil {
				log.Error("TransactionCreate err", err)
				return response.Error(rsp, response.DbSaveFail)
			}
		case "edit":
			if preset.Id == 0 {
				log.Error("q.DevicePresets edit Id missing")
				err = errors.New("id missing")
				return response.Error(rsp, response.ParamsInvalid)
			}
			if preset.LiableStaffId == 0 {
				log.Error("edit LiableStaffId missing")
				err = errors.New("liableStaffId missing")
				return response.Error(rsp, response.ParamsInvalid)
			}
			err = (&stockModel.StockMaterialTypePreset{}).TransactionUpdates(tx, preset.Id, map[string]interface{}{"LiableStaffId": preset.LiableStaffId})
			if err != nil {
				log.Error("TransactionUpdates err", err)
				return response.Error(rsp, response.DbUpdateFail)
			}
		case "delete":
			if preset.Id == 0 {
				log.Error("delete Id missing")
				err = errors.New("id missing")
				return response.Error(rsp, response.ParamsInvalid)
			}
			err = (&stockModel.StockMaterialTypePreset{}).TransactionDelete(tx, preset.Id)
			if err != nil {
				log.Error("TransactionDelete err", err)
				return response.Error(rsp, response.DbDeleteFail)
			}

		default:
			log.Error("Option missing")
			err = errors.New("option missing")
			return response.Error(rsp, response.ParamsInvalid)

		}
	}
	// 查询该设备种类下的责任人数量
	// 如果为0 则修改失败
	countLiables := (&stockModel.StockMaterialTypePreset{}).CountLiables(tx, param.StockMaterialType.Id)
	if countLiables == 0 {
		log.Error("liable count = 0")
		err = errors.New("liable count = 0")
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}
func (st *Stock) DeleteStockMaterialType(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StockMaterialType
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StockMaterialType.Id == 0 {
		log.ErrorFields(`param.StockMaterialType.Id == 0`, map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsMissing)
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = param.StockMaterialType.TransactionDelete(tx, param.StockMaterialType.Id)
	if err != nil {
		log.Error("TransactionDelete err", err)
		return response.Error(rsp, response.DbDeleteFail)
	}

	err = (&stockModel.StockMaterialTypePreset{}).TransactionDeleteStockMaterialTypeId(tx, param.StockMaterialType.Id)
	if err != nil {
		log.Error("TransactionDelete err", err)
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, nil)
}

func (st *Stock) ListStockMaterialTypePreset(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StockMaterialType
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//if param.StockMaterialType.ParentId == 0 {
	//	log.ErrorFields(`param.StockMaterialType.ParentId == 0`, map[string]interface{}{"err": nil})
	//	return response.Error(rsp, response.ParamsMissing)
	//}

	smts, totalCount, err := param.StockMaterialType.GetPreset(param.StockMaterialType.ParentId)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	mapCorp := make(map[int64]*oet_scs_srv_public.CorporationItem) //map[机构id]机构信息
	mapStaff := make(map[int64]*staffProto.OetStaffItem)           //map[staffid]人员信息

	for i, smt := range smts {
		for j, preset := range smt.StockMaterialTypePresets {
			if preset.Type == stockModel.StockMaterialTypePresetTypeLiable {
				if staffItem, ok := mapStaff[preset.LiableStaffId]; ok {
					smts[i].StockMaterialTypePresets[j].LiableStaffName = staffItem.Name
				} else {
					oetStaffItem := rpc.GetStaffWithId(ctx, preset.LiableStaffId)
					if oetStaffItem != nil {
						smts[i].StockMaterialTypePresets[j].LiableStaffName = oetStaffItem.Name
						mapStaff[preset.LiableStaffId] = oetStaffItem
					}
				}

				if corp, ok := mapCorp[mapStaff[preset.LiableStaffId].CorporationId]; ok {
					smts[i].StockMaterialTypePresets[j].LiableCorporation = corp.Name
				} else {
					oetCorp := rpc.GetCorporationById(ctx, mapStaff[preset.LiableStaffId].CorporationId)
					if oetCorp != nil {
						smts[i].StockMaterialTypePresets[j].LiableCorporation = oetCorp.Name
						mapCorp[mapStaff[preset.LiableStaffId].CorporationId] = oetCorp
					}
				}
			}

		}
	}

	data := map[string]interface{}{
		"Items":      smts,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)

}

func (st *Stock) EditStockMaterialTypePreset(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StockMaterialType
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StockMaterialType.Id == 0 {
		log.ErrorFields(`param.StockMaterialType.Id == 0`, map[string]interface{}{"err": nil})
		return response.Error(rsp, response.ParamsMissing)
	}

	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId <= 0 {
		log.ErrorFields("GetTopCorporationId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.Forbidden)
	}

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	for _, preset := range param.StockMaterialType.StockMaterialTypePresets {
		switch strings.ToLower(preset.Option) {
		case "create":
			var smtp stockModel.StockMaterialTypePreset
			//smtp.Id = model.Id()
			smtp.GroupId = topCorporationId
			smtp.Type = preset.Type
			smtp.StockMaterialTypeId = param.StockMaterialType.Id

			switch preset.Type {
			case stockModel.StockMaterialTypePresetTypeLiable:
				if preset.LiableStaffId == 0 {
					log.Error("create LiableStaffId missing")
					err = errors.New("liableStaffId missing")
					return response.Error(rsp, response.ParamsInvalid)
				}
				smtp.LiableStaffId = preset.LiableStaffId
				err = smtp.TransactionCreate(tx)
				if err != nil {
					log.Error("TransactionCreate err", err)
					return response.Error(rsp, response.DbSaveFail)
				}
			case stockModel.StockMaterialTypePresetTypeSupplier:
				if preset.SupplierAbbr == "" || preset.Supplier == "" {
					log.Error("create SupplierAbbr || Supplier missing")
					err = errors.New("SupplierAbbr || Supplier missing")
					return response.Error(rsp, response.ParamsInvalid)
				}
				smtp.SupplierAbbr = preset.SupplierAbbr
				smtp.Supplier = preset.Supplier
				err = smtp.TransactionCreate(tx)
				if err != nil {
					log.Error("TransactionCreate err", err)
					return response.Error(rsp, response.DbSaveFail)
				}
			default:
				log.Error("create type missing")
				err = errors.New("create type missing")
				return response.Error(rsp, response.ParamsInvalid)
			}
		case "edit":
			if preset.Id == 0 {
				log.Error("q.DevicePresets edit Id missing")
				err = errors.New("id missing")
				return response.Error(rsp, response.ParamsInvalid)
			}
			switch preset.Type {
			case stockModel.StockMaterialTypePresetTypeLiable:
				if preset.LiableStaffId == 0 {
					log.Error("edit LiableStaffId missing")
					err = errors.New("liableStaffId missing")
					return response.Error(rsp, response.ParamsInvalid)
				}
				err = (&stockModel.StockMaterialTypePreset{}).TransactionUpdates(tx, preset.Id, map[string]interface{}{"LiableStaffId": preset.LiableStaffId})
				if err != nil {
					log.Error("TransactionUpdates err", err)
					return response.Error(rsp, response.DbUpdateFail)
				}
			case stockModel.StockMaterialTypePresetTypeSupplier:
				if preset.SupplierAbbr == "" || preset.Supplier == "" {
					log.Error("edit SupplierAbbr || Supplier missing")
					err = errors.New("SupplierAbbr || Supplier missing")
					return response.Error(rsp, response.ParamsInvalid)
				}
				err = (&stockModel.StockMaterialTypePreset{}).TransactionUpdates(tx, preset.Id, map[string]interface{}{"SupplierAbbr": preset.SupplierAbbr, "Supplier": preset.Supplier})
				if err != nil {
					log.Error("TransactionUpdates err", err)
					return response.Error(rsp, response.DbUpdateFail)
				}
			default:
				log.Error("edit type missing")
				err = errors.New("edit type missing")
				return response.Error(rsp, response.ParamsInvalid)
			}
		case "delete":
			if preset.Id == 0 {
				log.Error("delete Id missing")
				err = errors.New("id missing")
				return response.Error(rsp, response.ParamsInvalid)
			}
			err = (&stockModel.StockMaterialTypePreset{}).TransactionDelete(tx, preset.Id)
			if err != nil {
				log.Error("TransactionDelete err", err)
				return response.Error(rsp, response.DbDeleteFail)
			}

		default:
			log.Error("Option missing")
			err = errors.New("option missing")
			return response.Error(rsp, response.ParamsInvalid)

		}
	}
	return response.Success(rsp, nil)
}

func (st *Stock) ListStockMaterialTypePresetLiable(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param StockMaterialType
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	liables, err := (&stockModel.StockMaterialTypePreset{}).ListLiables()
	if err != nil {
		log.ErrorFields("ListLiables error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, liables)

}
