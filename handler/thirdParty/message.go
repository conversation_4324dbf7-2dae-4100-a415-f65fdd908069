package thirdParty

import (
	"app/org/scs/erpv2/api/log"
	processModel "app/org/scs/erpv2/api/model/process"
	messageService "app/org/scs/erpv2/api/service/message"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"strconv"
)

type PushMessage struct {
	FormInstanceId string
	TemplateFormId string
	ProcessId      string
	Handler        string
	Title          string
	MessageType    int64 // 消息类型 1=>需处理的消息（审批） 2=>待查看的消息（抄送）
}
type LbpmPushMessageParam struct {
	Params PushMessage
}

func (tp *ThirdParty) LbpmPushMessage(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LbpmPushMessageParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	fmt.Printf("LbpmPushMessage param====%+v \n", param)

	formInstanceId, err := strconv.ParseInt(param.Params.FormInstanceId, 10, 64)

	var process processModel.LbpmApplyProcess
	err = process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("process.FindBy error", map[string]interface{}{"err": err, "FormInstanceId": formInstanceId})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	user := rpc.GetUserInfoByPhone(ctx, process.TopCorporationId, param.Params.Handler)
	if user == nil {
		log.ErrorFields("rpc.GetUserInfoByPhone error", map[string]interface{}{"handler": param.Params.Handler, "topCorporationId": process.TopCorporationId})
		return response.Error(rsp, response.NotFundUserInfo)
	}
	//如果流程是被驳回到发起节点，但当前消息不是推送给发起人的，则推测为错误
	if process.Status == util.ProcessStatusForRefuse && process.ApplyUserId != user.Id && param.Params.MessageType == util.ProcessNodeTypeForApprove {
		log.ErrorFields("process is refuse && process.ApplyStaffId!=staff.Id", map[string]interface{}{"processStatus": process.Status, "ApplyStaffId": process.ApplyUserId, "userId": user.Id})
		return response.Error(rsp, response.BadRequest)
	}

	//发消息
	go messageService.BuildMessage(process, processService.HandlerUser{Id: user.Id, Name: user.Nickname, Mobile: user.Phone}, param.Params.MessageType)

	//流程关联处理人
	go processService.BuildProcessHandler(process, processService.HandlerUser{Id: user.Id, Name: user.Nickname, Mobile: user.Phone}, param.Params.MessageType)

	return response.Success(rsp, nil)
}
