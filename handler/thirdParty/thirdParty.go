package thirdParty

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/handler/thirdParty/method"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/process/dingTalkBpm"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	lbpmListener "app/org/scs/erpv2/api/service/process/lbpm/listener"
	webservice "app/org/scs/erpv2/api/service/process/lbpm/webservice/server"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/aes"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

type ThirdParty struct {
}

func (tp *ThirdParty) LbpmWebServiceInterface(ctx context.Context, req *api.Request, rsp *api.Response) error {
	fmt.Printf("LbpmWebServiceInterface request=========%+v \r\n", req)

	var Namespace = config.Config.Lbpm.ServicePrefix + "LbpmWebServiceInterface"
	//注册方法
	s := webservice.NewServer("ErpLbpmService", Namespace)
	_ = s.RegisterMethod(method.GetTemplateFormList{NameSpace: Namespace})
	_ = s.RegisterMethod(method.GetFormFieldList{NameSpace: Namespace})
	_ = s.RegisterMethod(method.GetFormFieldValueList{NameSpace: Namespace})
	_ = s.RegisterMethod(method.GetMethodInfo{NameSpace: Namespace})
	_ = s.RegisterMethod(method.DoMethodProcess{NameSpace: Namespace})
	_ = s.RegisterMethod(method.SynchronizeTemplate{NameSpace: Namespace})

	//注册监听器
	lbpmListener.RegisterListener()

	s.Handler(rsp, req)

	//go lbpmApi.CallLbpmGetAuditOptionListService(lbpmApi.Creator{LoginName: "***********"}, "1884b58d9107fdad7b36ddf473a86aa9", lbpmApi.FormId{
	//	SysId:          "erp-tz",
	//	ModelId:        "hr_model",
	//	TemplateFormId: "staff_transfer_apply_process",
	//	FormInstanceId: "7066946395852529664",
	//}, 1, 100)
	return nil
}

func (tp *ThirdParty) LbpmWebServiceTest(ctx context.Context, req *api.Request, rsp *api.Response) error {
	fmt.Printf("req=================%+v \r\n", req)

	var account = fmt.Sprintf("oet:%v", time.Now().UnixNano()/1e6)
	encrypted1 := aes.ECBEncrypt([]byte(account), []byte(config.LbpmAESKey))
	encrypted2 := base64.StdEncoding.EncodeToString(encrypted1)
	encrypted3 := base64.StdEncoding.EncodeToString([]byte(encrypted2))
	fmt.Printf("encrypted===========%+v \r\n", encrypted3)

	//将token进行2次base64解码
	token, err := base64.StdEncoding.DecodeString(encrypted3)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error[1]", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	token, err = base64.StdEncoding.DecodeString(string(token))
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error[2]", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	decrypted := aes.ECBDecrypt(token, []byte(config.LbpmAESKey))
	fmt.Printf("decrypted===========%+v \r\n", string(decrypted))

	client := http.Client{}
	url := fmt.Sprintf("%sapi/km-addin/kmAddinErpSsoService/ssoLogin?token=%s", config.Config.Lbpm.LbpmDomain, encrypted3)
	fmt.Printf("url============%+v \r\n", url)
	resp, err := client.Get(url)

	if err != nil {
		fmt.Printf("client.Get err===========%+v \r\n", err)
		return err
	}
	defer resp.Body.Close()
	bd, _ := ioutil.ReadAll(resp.Body)
	var result = make(map[string]interface{})
	_ = json.Unmarshal(bd, &result)
	fmt.Printf("result=====%+v", result)

	return nil

}

type StaffArchive struct {
	hrModel.StaffArchive
	FilterWhere
}

type FilterWhere struct {
	CorporationIds       []int64          `json:"CorporationIds,omitempty"`
	JoinCompanyApplyId   int64            `json:"JoinCompanyApplyId,omitempty"`
	Keyword              string           `json:"Keyword,omitempty"`              //员工姓名、工号 主数据
	JobStatusArr         []int64          `json:"JobStatusArr,omitempty"`         //在职状态  主数据|多选
	WorkPostTypeArr      []int64          `json:"WorkPostTypeArr,omitempty"`      //岗位类型 主数据|多选
	PoliticalIdentityArr []int64          `json:"PoliticalIdentityArr,omitempty"` //政治身份 主数据|多选
	JoinCompanyWayArr    []int64          `json:"JoinCompanyWayArr,omitempty"`    //加入公司途径 主数据|多选
	HighestEduArr        []int64          `json:"HighestEduArr,omitempty"`        //最高学历 多选
	PositionalTitle      string           `json:"PositionalTitle,omitempty"`      //职称名称
	Skill                string           `json:"Skill,omitempty"`                //技能
	WorkPost             string           `json:"WorkPost,omitempty"`             //岗位
	StartBirthAt         *model.LocalTime `json:"StartBirthAt,omitempty"`         //开始出生日期 主数据
	EndBirthAt           *model.LocalTime `json:"EndBirthAt,omitempty"`           //结束出生日期 主数据
	HealthStatusArr      []int64          `json:"HealthStatusArr,omitempty"`      //健康状况 主数据|多选
	StartReversionAt     *model.LocalTime `json:"StartReversionAt,omitempty"`     //开始复转退时间 主数据
	EndReversionAt       *model.LocalTime `json:"EndReversionAt,omitempty"`       //结束复转退时间 主数据
	MarriageStatusArr    []int64          `json:"MarriageStatusArr,omitempty"`    //婚姻状态 多选
	BearStatusArr        []int64          `json:"BearStatusArr,omitempty"`        //生育情况 多选
	Major                string           `json:"Major,omitempty"`                //专业
	School               string           `json:"School,omitempty"`               //学校
	CertificateName      string           `json:"CertificateName,omitempty"`      //资格证名称
	StartCertificateAt   *model.LocalTime `json:"StartCertificateAt,omitempty"`   //开始资格证时间
	EndCertificateAt     *model.LocalTime `json:"EndCertificateAt,omitempty"`     //结束资格证时间
	HumanRelationIds     []int64          `json:"HumanRelationIds,omitempty"`     //人事关系 多选
	EmploymentCateArr    []int64          `json:"EmploymentCateArr,omitempty"`    //用工类型 多选
	StartJoinAt          *model.LocalTime `json:"StartJoinAt,omitempty"`          //开始入职时间 主数据
	EndJoinAt            *model.LocalTime `json:"EndJoinAt,omitempty"`            //结束入职时间 主数据
	StartJobAt           *model.LocalTime `json:"StartJobAt,omitempty"`           //开始参加工作时间 主数据
	EndJobAt             *model.LocalTime `json:"EndJobAt,omitempty"`             //结束参加工作时间 主数据
	StartWorkAt          *model.LocalTime `json:"StartWorkAt,omitempty"`          //开始任职时间
	EndWorkAt            *model.LocalTime `json:"EndWorkAt,omitempty"`            //结束任职时间
	PositionLevel        string           `json:"PositionLevel,omitempty"`        //职位等级
}

func (tp *ThirdParty) TestGetParams(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param FilterWhere
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	oet, _ := util.StructToMapByReflect(param, "json")
	fmt.Printf("cccccc===%+v \r\n", oet)
	fmt.Printf("dddddd1===%+v \r\n", oet["StartBirthAt"].(string))
	fmt.Printf("dddddd2===%+v \r\n", oet["CorporationIds"].([]int64))
	fmt.Printf("dddddd3===%+v \r\n", oet["HighestEduArr"].([]int64))
	return nil

	templateFormId := "personal_title_apply_process"
	var template map[string]string
	for i := range config.LbpmFormTemplates {
		if config.LbpmFormTemplates[i]["TemplateFormId"] == templateFormId {
			template = config.LbpmFormTemplates[i]
		}
	}

	fmt.Printf("template====================%+v \r\n", template)

	formId := lbpmApi.FormId{
		SysId:          config.Config.Lbpm.SysId,
		ModelId:        template["ModelId"],
		TemplateFormId: template["TemplateFormId"],
		FormInstanceId: "275659104455129634",
	}

	fmt.Printf("formid====================%+v \r\n", formId)
	//processId := "1833f2ccbc147072f8ac5d7496b9e45d"
	processId, err := lbpmApi.CallLbpmCreateProcessService(formId, lbpmApi.Creator{LoginName: "***********"}, lbpmApi.ExParam{DocSubject: "***********的" + template["TemplateFormName"]})

	if err != nil {
		log.ErrorFields("lbpmService.CallLbpmCreateProcessService error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	//登录token
	var account = fmt.Sprintf("***********:%v", time.Now().UnixNano()/1e6)
	token := aes.ECBEncrypt([]byte(account), []byte(config.LbpmAESKey))

	//process.GetApprovingList(process.HandlerStaff{Mobile: "***********"}, "")

	return response.Success(rsp, map[string]interface{}{"FormId": formId, "ProcessId": processId, "Token": base64.StdEncoding.EncodeToString([]byte(base64.StdEncoding.EncodeToString(token)))})
}

type Form struct {
	ApplyAccount string `json:"ApplyAccount"`
	LendMoney    int64  `json:"LendMoney"`
	CompanyName  string `json:"CompanyName"`
	BranchName   string `json:"BranchName"`
}

type ProcessParam struct {
	FormId    lbpmApi.FormId
	ProcessId string
	FormData  Form
	LbpmParam string
}

func (tp *ThirdParty) TestApproveProcess(ctx context.Context, req *api.Request, rsp *api.Response) error {
	fmt.Printf("ApproveProcess====================%+v \r\n", req)

	var param ProcessParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	fmt.Printf("param===========%+v \r\n", param)
	formData, _ := json.Marshal(param.FormData)
	err = lbpmApi.CallLbpmApproveProcessService(param.ProcessId, string(formData), param.FormId, lbpmApi.Creator{LoginName: "***********"}, param.LbpmParam)

	if err != nil {
		log.ErrorFields("lbpmService.CallLbpmApproveProcessService", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	//formId := lbpmService.FormId{
	//	SysId:          "erp-tz",
	//	ModelId:        "safety_model",
	//	TemplateFormId: "traffic_accident_lend_money_process",
	//	FormInstanceId: "277222493520790784",
	//}
	//process.NextProcessNode(param.ProcessId, param.FormId)

	return response.Success(rsp, nil)

}

type LineSafetyReportParam struct {
	Params LineSafetyReportParams `json:"Params"`
}
type LineSafetyReportParams struct {
	GroupId int64
	StartAt model.LocalTime
	EndAt   model.LocalTime
}

func (tp *ThirdParty) LineSafetyReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	fmt.Printf("LineSafetyReport====================%+v \r\n", req)

	var param LineSafetyReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	fmt.Printf("param===========%+v \r\n", param)

	if param.Params.GroupId == 0 {
		log.Error("q.GroupId == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	tmpLineCount := make(map[int64]safetyModel.LineSafetyCount) // map[LineId]

	// 获取违规违法 数量
	viCount, err := (&safetyModel.TrafficViolation{}).GetCount(param.Params.GroupId, time.Time(param.Params.StartAt), time.Time(param.Params.EndAt))
	if err != nil {
		log.Error("TrafficViolation GetCount == 0")
		return response.Error(rsp, response.DbQueryFail)
	}

	// 获取事故数量
	accCount, err := (&safetyModel.TrafficAccident{}).GetCount(param.Params.GroupId, time.Time(param.Params.StartAt), time.Time(param.Params.EndAt))
	if err != nil {
		log.Error("TrafficViolation GetCount == 0")
		return response.Error(rsp, response.DbQueryFail)
	}

	viCount = append(viCount, accCount...)

	for _, count := range viCount {
		if safetyCount, ok := tmpLineCount[count.LineId]; ok {

			safetyCount.Accident += count.Accident
			safetyCount.Violation += count.Violation
			safetyCount.Illegal += count.Illegal

			tmpLineCount[count.LineId] = safetyCount
		} else {
			tmpLineCount[count.LineId] = count
		}
	}

	var rspD []safetyModel.LineSafetyCount

	for _, count := range tmpLineCount {
		rspD = append(rspD, count)
	}

	data := map[string]interface{}{
		"Items": rspD,
	}

	return response.Success(rsp, data)
}

type LbpmSSO struct {
	Params LbpmSSOParam `json:"Params"`
}

type LbpmSSOParam struct {
	UserToken string `json:"UserToken"`
}

func (tp *ThirdParty) LoginForLbpmSSO(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LbpmSSO
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Params.UserToken == "" {
		log.ErrorFields("token is empty", nil)
		return response.Error(rsp, response.ParamsMissing)
	}

	//将token进行2次base64解码
	token, err := base64.StdEncoding.DecodeString(param.Params.UserToken)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error[1]", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	token, err = base64.StdEncoding.DecodeString(string(token))
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error[2]", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	decrypted := aes.ECBDecrypt(token, []byte(config.LbpmAESKey))
	fmt.Printf("decrypted== %s \n", string(decrypted))
	data := strings.Split(string(decrypted), ":")
	if len(data) < 2 || data[0] == "" || data[1] == "" {
		log.ErrorFields("decrypted param not invalid", map[string]interface{}{"data": data})
		return response.Error(rsp, response.LoginFail)
	}
	fmt.Printf("decrypted value %+v", data)

	//获取用户登录信息
	user := rpc.LoginForMobile(ctx, data[0])
	if user == nil {
		log.ErrorFields("rpc.LoginForMobile is nil", map[string]interface{}{"mobile": data[0]})
		return response.Error(rsp, response.LoginFail)
	}

	return response.Success(rsp, map[string]interface{}{"AuthId": user.AuthId, "UserName": user.Username, "Phone": user.Phone})

}

type DingDingSSO struct {
	Params DingDingSSOParam `json:"Params"`
}

type DingDingSSOParam struct {
	Code string `json:"Code"`
}

// 钉钉code换取
func (tp *ThirdParty) LoginForDdSSO(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DingDingSSO
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Params.Code == "" {
		log.ErrorFields("code is empty", nil)
		return response.Error(rsp, response.ParamsMissing)
	}

	dingUserId, err := dingTalkBpm.GetUserIdByCode(param.Params.Code, "")
	if err != nil {
		log.ErrorFields("code is empty", nil)
		return response.Error(rsp, response.PasswordNotMatch)
	}

	dingUserInfo, err := dingTalkBpm.GetUserInfo(dingUserId, "")
	if err != nil {
		log.ErrorFields("code is empty", nil)
		return response.Error(rsp, response.PasswordNotMatch)
	}

	//获取用户登录信息
	user := rpc.LoginForMobile(ctx, dingUserInfo.Mobile)
	if user == nil {
		log.ErrorFields("rpc.LoginForMobile is nil", map[string]interface{}{"mobile": dingUserInfo.Mobile})
		return response.Error(rsp, response.LoginFail)
	}

	apiPermissions := auth.NewUserById(user.UserId).GetPermissions()
	var apiPermissionMap = make(map[string]bool, 0)
	for _, v := range config.Global.H5Permissions {
		for _, vv := range apiPermissions {
			if v == vv {
				apiPermissionMap[v] = true
			}
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"Item": map[string]interface{}{
			"AuthId":      user.AuthId,
			"UserName":    user.Username,
			"Phone":       user.Phone,
			"Permissions": apiPermissionMap,
		},
	})
}
