package method

import (
	"app/org/scs/erpv2/api/config"
	"encoding/json"
	"encoding/xml"
	"errors"
	"fmt"
)

type GetFormFieldList struct {
	NameSpace string
}

type GetFormFieldListRequest struct {
	SysId          string `xml:"sysId" wsdl:"sysId"`
	ModelId        string `xml:"modelId" wsdl:"modelId"`
	TemplateFormId string `xml:"templateFormId" wsdl:"templateFormId"`
	Language       string `xml:"language" wsdl:"language"`
}

type GetFormFieldListResponse struct {
	XMLName xml.Name `xml:"ns2:getFormFieldListResponse" wsdl:"xmlName"`
	Ns      string   `xml:"xmlns:ns2,attr" wsdl:"ns"`
	Return  []byte   `xml:"return" wsdl:"return"`
}

type GetFormFieldListValue struct {
	FieldId   string `json:"fieldId"`
	FieldName string `json:"fieldName"`
	Type      string `json:"type"`
}

func (d GetFormFieldList) Do(req interface{}, resp interface{}) error {
	fmt.Printf("GetFormFieldList.req==========%+v \r\n", req)
	res := resp.(*GetFormFieldListResponse)
	res.Ns = d.NameSpace

	request := req.(*GetFormFieldListRequest)
	if request.TemplateFormId == "" {
		res.Return = []byte("缺少 TemplateFormId 参数")
		return errors.New("缺少 TemplateFormId 参数")
	}

	fields := config.LbpmFormFields[request.TemplateFormId]

	var returns []GetFormFieldListValue
	for i := range fields {
		returns = append(returns, GetFormFieldListValue{
			FieldId:   fields[i]["FieldId"],
			FieldName: fields[i]["FieldName"],
			Type:      fields[i]["FieldType"],
		})
	}

	res.Return, _ = json.Marshal(returns)
	return nil
}

func (d GetFormFieldList) Name() string {
	return "getFormFieldList"
}

func (d GetFormFieldList) ReqStruct() interface{} {
	return GetFormFieldListRequest{}
}

func (d GetFormFieldList) RespStruct() interface{} {
	return GetFormFieldListResponse{}
}
