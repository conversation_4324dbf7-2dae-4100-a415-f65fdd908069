package method

import (
	lbpmListener "app/org/scs/erpv2/api/service/process/lbpm/listener"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"strings"
)

type GetMethodInfo struct {
	NameSpace string
}

type GetMethodInfoRequest struct {
	SysId          string `xml:"sysId" wsdl:"sysId"`
	ModelId        string `xml:"modelId" wsdl:"modelId"`
	TemplateFormId string `xml:"templateFormId" wsdl:"templateFormId"`
	Language       string `xml:"language" wsdl:"language"`
}

type GetMethodInfoResponse struct {
	XMLName xml.Name `xml:"ns2:getMethodInfoResponse" wsdl:"xmlName"`
	Ns      string   `xml:"xmlns:ns2,attr" wsdl:"ns"`
	Return  []byte   `json:"return" xml:"return" wsdl:"return"`
}

type GetMethodInfoValue struct {
	FunctionId   string `json:"functionId"`
	FunctionName string `json:"functionName"`
	FunctionDes  string `json:"functionDes"`
}

func (d GetMethodInfo) Do(req interface{}, resp interface{}) error {
	fmt.Printf("GetMethodInfo.req==========%+v \r\n", req)
	res := resp.(*GetMethodInfoResponse)
	res.Ns = d.NameSpace
	var returns []GetMethodInfoValue

	request := req.(*GetMethodInfoRequest)

	var methods = lbpmListener.EventListenerMethod.Methods()

	for id := range methods {
		if strings.Contains(id, request.TemplateFormId) || strings.Contains(id, "public") {
			method, _ := lbpmListener.EventListenerMethod.Read(id)
			returns = append(returns, GetMethodInfoValue{
				FunctionId:   method.Id(),
				FunctionName: method.Name(),
				FunctionDes:  method.Desc(),
			})
		}
	}
	b, _ := json.Marshal(returns)
	res.Return = b
	return nil
}

func (d GetMethodInfo) Name() string {
	return "getMethodInfo"
}

func (d GetMethodInfo) ReqStruct() interface{} {
	return GetMethodInfoRequest{}
}

func (d GetMethodInfo) RespStruct() interface{} {
	return GetMethodInfoResponse{}
}
