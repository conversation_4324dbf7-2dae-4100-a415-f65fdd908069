package method

import (
	"encoding/xml"
	"fmt"
)

type SynchronizeTemplate struct {
	NameSpace string
}

type SynchronizeTemplateRequest struct {
	SysId            string `xml:"sysId" wsdl:"sysId"`
	FlowDefinitionId string `xml:"flowDefinitionId" wsdl:"flowDefinitionId"`
	FlowTemplateId   string `xml:"flowTemplateId" wsdl:"flowTemplateId"`
	OperationType    string `xml:"operationType" wsdl:"operationType"`
	Language         string `xml:"language" wsdl:"language"`
}

type SynchronizeTemplateResponse struct {
	XMLName xml.Name `xml:"ns2:synchronizeTemplateResponse" wsdl:"xmlName"`
	Ns      string   `xml:"xmlns:ns2,attr" wsdl:"ns"`
	Return  []byte   `json:"return" xml:"return" wsdl:"return"`
}

func (d SynchronizeTemplate) Do(req interface{}, resp interface{}) error {
	fmt.Printf("SynchronizeTemplate.req==========%+v \r\n", req)
	res := resp.(*SynchronizeTemplateResponse)
	res.Ns = d.NameSpace
	res.Return = []byte("T")
	return nil
}

func (d SynchronizeTemplate) Name() string {
	return "synchronizeTemplate"
}

func (d SynchronizeTemplate) ReqStruct() interface{} {
	return SynchronizeTemplateRequest{}
}

func (d SynchronizeTemplate) RespStruct() interface{} {
	return SynchronizeTemplateResponse{}
}
