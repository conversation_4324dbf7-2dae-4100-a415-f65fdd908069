package method

import (
	"app/org/scs/erpv2/api/config"
	processModel "app/org/scs/erpv2/api/model/process"
	"encoding/json"
	"encoding/xml"
	"errors"
	"fmt"
	"strconv"
)

type GetFormFieldValueList struct {
	NameSpace string
}

type GetFormFieldValueListRequest struct {
	SysId          string `xml:"sysId" wsdl:"sysId"`
	ModelId        string `xml:"modelId" wsdl:"modelId"`
	TemplateFormId string `xml:"templateFormId" wsdl:"templateFormId"`
	FormInstanceId string `xml:"formInstanceId" wsdl:"formInstanceId"`
	FieldIds       string `xml:"fieldIds" wsdl:"fieldIds"`
	Language       string `xml:"language" wsdl:"language"`
}

type GetFormFieldValueListResponse struct {
	XMLName xml.Name `xml:"ns2:getFormFieldValueListResponse" wsdl:"xmlName"`
	Ns      string   `xml:"xmlns:ns2,attr" wsdl:"ns"`
	Return  []byte   `xml:"return" wsdl:"return"`
}

type GetFormFieldValueListValue struct {
	FieldId    string `json:"fieldId"`
	FieldValue string `json:"fieldValue"`
}

// Do 审批时如果没有传流程需要的字段值，LBPM会主动请求此接口获取对应数据
func (d GetFormFieldValueList) Do(req interface{}, resp interface{}) error {
	fmt.Printf("GetFormFieldValueList.req==========%+v \r\n", req)
	res := resp.(*GetFormFieldValueListResponse)
	res.Ns = d.NameSpace
	request := req.(*GetFormFieldValueListRequest)

	formInstanceId, _ := strconv.ParseInt(request.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		res.Return = []byte("参数formInstanceId 没找到")
		return errors.New("参数formInstanceId 没找到")
	}
	fmt.Printf("GetFormFieldValueList process === %+v \n", process)
	fmt.Printf("GetFormFieldValueList process.ProcessFieldValue==================%+v \r\n", string(process.ProcessFieldValue))

	if process.ProcessFieldValue == nil || string(process.ProcessFieldValue) == "" {
		var returns []GetFormFieldValueListValue
		fields, ok := config.LbpmFormFields[process.TemplateFormId]
		if ok {
			for j := range fields {
				var value = GetFormFieldValueListValue{
					FieldId: fields[j]["FieldId"],
				}
				if fields[j]["FieldType"] == "String" {
					value.FieldValue = ""
				}
				if fields[j]["FieldType"] == "Number" {
					value.FieldValue = "0"
				}
				returns = append(returns, value)
			}
		}

		fmt.Printf("GetFormFieldValueList returns============%+v \r\n", returns)
		res.Return, _ = json.Marshal(returns)
		return nil
	}

	var formData map[string]interface{}
	_ = json.Unmarshal(process.ProcessFieldValue, &formData)

	//如果新增了字段 但历史流程表单中没有新增的字段 也需要一并加进去
	fields, ok := config.LbpmFormFields[process.TemplateFormId]
	if ok {
		for i := range fields {
			if _, ok = formData[fields[i]["FieldId"]]; !ok {
				if fields[i]["FieldType"] == "String" || fields[i]["FieldType"] == "Array" {
					formData[fields[i]["FieldId"]] = ""
				}
				if fields[i]["FieldType"] == "Number" {
					formData[fields[i]["FieldId"]] = "0"
				}
			}
		}
	}

	var keyValues = make([]map[string]interface{}, 0)
	for key := range formData {
		keyValues = append(keyValues, map[string]interface{}{"fieldId": key, "fieldValue": fmt.Sprintf("%v", formData[key])})
	}
	res.Return, _ = json.Marshal(keyValues)

	fmt.Printf("GetFormFieldValueList res.Return==================%+v \r\n", string(res.Return))
	return nil
}

func (d GetFormFieldValueList) Name() string {
	return "getFormFieldValueList"
}

func (d GetFormFieldValueList) ReqStruct() interface{} {
	return GetFormFieldValueListRequest{}
}

func (d GetFormFieldValueList) RespStruct() interface{} {
	return GetFormFieldValueListResponse{}
}
