package method

import (
	lbpmListener "app/org/scs/erpv2/api/service/process/lbpm/listener"
	"encoding/xml"
	"fmt"
)

type DoMethodProcess struct {
	NameSpace string
}

type DoMethodProcessRequest struct {
	FormId      string `xml:"formId" wsdl:"formId"`
	FunctionId  string `xml:"functionId" wsdl:"functionId"`
	ProcessData string `xml:"processData" wsdl:"processData"`
	Language    string `xml:"language" wsdl:"language"`
}

type DoMethodProcessResponse struct {
	XMLName xml.Name `xml:"ns2:doMethodProcessResponse" wsdl:"xmlName"`
	Ns      string   `xml:"xmlns:ns2,attr" wsdl:"ns"`
	Return  []byte   `json:"return" xml:"return" wsdl:"return"`
}

type ProcessData struct {
	ProcessId  string `json:"processId"`
	NodeFactId string `json:"nodeFactId"`
	DocStatus  string `json:"docStatus"`
}

func (d DoMethodProcess) Do(req interface{}, resp interface{}) error {
	fmt.Printf("DoMethodProcess.req==========%+v \r\n", req)
	res := resp.(*DoMethodProcessResponse)
	res.Ns = d.NameSpace
	res.Return = []byte("T")

	request := req.(*DoMethodProcessRequest)

	lbpmListener.DoListener(request.FunctionId, request.FormId, request.ProcessData)

	return nil
}

func (d DoMethodProcess) Name() string {
	return "doMethodProcess"
}

func (d DoMethodProcess) ReqStruct() interface{} {
	return DoMethodProcessRequest{}
}

func (d DoMethodProcess) RespStruct() interface{} {
	return DoMethodProcessResponse{}
}
