package method

import (
	"app/org/scs/erpv2/api/config"
	"encoding/json"
	"encoding/xml"
	"errors"
	"fmt"
)

type GetTemplateFormList struct {
	NameSpace string
}
type GetTemplateFormListRequest struct {
	SysId    string `json:"sysId" xml:"sysId" wsdl:"sysId"`
	Language string `json:"language" xml:"language" wsdl:"language"`
}
type GetTemplateFormListResponse struct {
	XMLName xml.Name `xml:"ns2:getTemplateFormListResponse" wsdl:"xmlName"`
	Ns      string   `xml:"xmlns:ns2,attr" wsdl:"ns"`
	Return  []byte   `json:"return" xml:"return" wsdl:"return"`
}

type GetTemplateFormListValue struct {
	ModelId          string `json:"modelId"`
	ModelName        string `json:"modelName"`
	TemplateFormId   string `json:"templateFormId"`
	TemplateFormName string `json:"templateFormName"`
	FormUrl          string `json:"formUrl"`
}

func (d GetTemplateFormList) Do(req interface{}, resp interface{}) error {
	fmt.Printf("getTemplateFormList.req==========%+v \r\n", req)
	res := resp.(*GetTemplateFormListResponse)
	res.Ns = d.NameSpace

	request := req.(*GetTemplateFormListRequest)
	if request.SysId == "" {
		res.Return = []byte("缺少SysId参数")
		return errors.New("缺少SysId参数")
	}

	var returns []GetTemplateFormListValue
	for i := range config.LbpmFormTemplates {
		returns = append(returns, GetTemplateFormListValue{
			ModelId:          config.LbpmFormTemplates[i]["ModelId"],
			ModelName:        config.LbpmFormTemplates[i]["ModelName"],
			TemplateFormId:   config.LbpmFormTemplates[i]["TemplateFormId"],
			TemplateFormName: config.LbpmFormTemplates[i]["TemplateFormName"],
			FormUrl:          config.Config.Lbpm.ToDoPrefixDomain + config.LbpmFormTemplates[i]["FormUrl"],
		})
	}
	fmt.Printf("GetTemplateFormList %+v \n", returns)
	res.Return, _ = json.Marshal(returns)
	return nil
}

func (d GetTemplateFormList) Name() string {
	return "getTemplateFormList"
}

func (d GetTemplateFormList) ReqStruct() interface{} {
	return GetTemplateFormListRequest{}
}

func (d GetTemplateFormList) RespStruct() interface{} {
	return GetTemplateFormListResponse{}
}
