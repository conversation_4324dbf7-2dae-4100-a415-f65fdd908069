package thirdParty

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	stockModel "app/org/scs/erpv2/api/model/stock"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/process/dingTalkBpm"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/aes"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"gorm.io/gorm"
	"strconv"
	"time"
)

type DingTalkEncrypt struct {
	Encrypt string `json:"encrypt"`
}

type DingCallBackMsg struct {
	EventType         string `json:"EventType"`
	ProcessInstanceId string `json:"processInstanceId"`
	CorpId            string `json:"corpId"`
	CreateTime        int64  `json:"createTime"`
	FinishTime        int64  `json:"finishTime"`
	Title             string `json:"title"`
	Type              string `json:"type"`
	Result            string `json:"result"`
	Remark            string `json:"remark"`
	StaffId           string `json:"staffId"`
	Url               string `json:"url"`
	ProcessCode       string `json:"processCode"`
}

func (tp *ThirdParty) DingCallBack(ctx context.Context, req *api.Request, rsp *api.Response) error {
	fmt.Printf("DingCallBack  req: %+v======================= \n", req)
	queryParam := req.GetGet()
	signature := queryParam["signature"].Values[0]
	timestamp := queryParam["timestamp"].Values[0]
	nonce := queryParam["nonce"].Values[0]

	var param DingTalkEncrypt
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	crypto := aes.NewDingTalkCrypto(config.Config.DingTalkBpm.Token, config.Config.DingTalkBpm.AesKey, config.Config.DingTalkBpm.AppKey)
	msg, err := crypto.GetDecryptMsg(signature, timestamp, nonce, param.Encrypt)
	if err != nil {
		log.ErrorFields("crypto.GetDecryptMsg error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	log.ErrorFields("DingCallBack msg", map[string]interface{}{"msg": msg})

	var callbackMsg DingCallBackMsg
	_ = json.Unmarshal([]byte(msg), &callbackMsg)
	var result = make(map[string]interface{})
	switch callbackMsg.EventType {
	case "bpms_task_change": //审批任务开始、结束、转交
		err = BpmTaskChange(callbackMsg)
		if err != nil {
			log.ErrorFields("BpmTaskChange error", map[string]interface{}{"err": err})
			return err
		}

	case "bpms_instance_change": //审批任务开始、结束、转交
		err = BpmInstanceChange(callbackMsg)
		if err != nil {
			log.ErrorFields("BpmInstanceChange error", map[string]interface{}{"err": err})
			return err
		}
	}

	nonceStr := util.RandomString(9)
	timestampStr := fmt.Sprintf("%v", time.Now().Unix())
	encryptedData, signatureMsg, err := crypto.GetEncryptMsg("success", timestampStr, nonceStr)
	if err != nil {
		log.ErrorFields("crypto.GetEncryptMsg error", map[string]interface{}{"err": err})
		return nil
	}
	result = map[string]interface{}{
		"msg_signature": signatureMsg,
		"timeStamp":     timestampStr,
		"nonce":         nonceStr,
		"encrypt":       encryptedData,
	}
	fmt.Printf("result==========%+v \n", result)
	respByte, _ := json.Marshal(result)
	rsp.Body = string(respByte)
	return nil
}

func BpmTaskChange(message DingCallBackMsg) error {
	//根据流程实例ID查询流程
	processInstance := (&processModel.DingTalkApplyProcess{}).FirstByInstanceId(message.ProcessInstanceId)
	if processInstance.Id == 0 {
		log.ErrorFields("FirstByInstanceId error", map[string]interface{}{"processInstanceId": message.ProcessInstanceId})
		return errors.New("NOT FUND PROCESS")
	}

	//根据审批人ID 获取钉钉审批人信息
	dingUserInfo, err := dingTalkBpm.GetUserInfo(message.StaffId, "")
	if err != nil {
		log.ErrorFields("dingTalkBpm.GetUserInfo error", map[string]interface{}{"StaffId": message.StaffId})
		return err
	}
	//根据用户手机号获取主数据员工
	user := rpc.GetUserInfoByPhone(context.Background(), processInstance.TopCorporationId, dingUserInfo.Mobile)
	if user == nil {
		log.ErrorFields("rpc.GetUserInfoByPhone error", map[string]interface{}{"mobile": dingUserInfo.Mobile})
		return errors.New("oet user not fund")
	}
	msgByte, _ := json.Marshal(message)
	at := model.LocalTime(time.Now())

	//审批任务开始
	if message.Type == "start" {
		//将审批人存入数据库
		var handler = processModel.DingTalkApplyProcessHasHandler{
			DingTalkApplyProcessId: processInstance.Id,
			ProcessInstanceId:      processInstance.ProcessInstanceId,
			NodeType:               util.ProcessNodeTypeForApprove,
			UserId:                 user.Id,
			UserName:               user.Nickname,
			UserMobile:             user.Phone,
			UserDingTalkUserId:     dingUserInfo.UserId,
			Status:                 util.ApplyStatusForDoing,
			ProcessParam:           msgByte,
			StartAt:                &at,
		}

		err = handler.Create()
		if err != nil {
			log.ErrorFields("handler.Create error", map[string]interface{}{"err": err})
			return err
		}
	}

	//审批任务结束
	if message.Type == "finish" || message.Type == "cancel" {
		//根据流程实例和审批人查询正在审批的记录
		handlingNote := (&processModel.DingTalkApplyProcessHasHandler{}).FindHandlingBy(processInstance.Id, user.Id)
		if handlingNote.Id == 0 {
			log.ErrorFields("handler.FindHandlingBy is error", nil)
			return errors.New("handler not fund")
		}

		var result string
		if message.Result == "agree" {
			result = util.ProcessNodeHandleResultForPass
		}
		if message.Result == "refuse" {
			result = util.ProcessNodeHandleResultForRefuse
		}
		if message.Result == "redirect" {
			result = util.ProcessNodeHandleResultForRedirect
		}

		var status int64
		if message.Type == "finish" {
			status = util.ProcessNodeHandleStatusForDone
		}
		if message.Type == "cancel" {
			status = util.ProcessNodeHandleStatusForOver
		}

		//将审批状态更新到数据库
		var handler = processModel.DingTalkApplyProcessHasHandler{
			Status:       status,
			Result:       result,
			ProcessParam: msgByte,
			EndAt:        &at,
		}
		handler.Id = handlingNote.Id

		err = handler.UpdateStatus()
		if err != nil {
			log.ErrorFields("handler.UpdateStatus error", map[string]interface{}{"err": err})
			return err
		}
	}

	return nil
}

func BpmInstanceChange(message DingCallBackMsg) error {
	//根据流程实例ID查询流程
	processInstance := (&processModel.DingTalkApplyProcess{}).FirstByInstanceId(message.ProcessInstanceId)
	if processInstance.Id == 0 {
		log.ErrorFields("BpmInstanceChange FirstByInstanceId error", map[string]interface{}{"processInstanceId": message.ProcessInstanceId})
		return errors.New("BpmInstanceChange NOT FUND PROCESS")
	}

	at := model.LocalTime(time.Now())
	processInstance.DoneAt = &at

	if message.Type == "start" {
		model.DB().Table(processInstance.ItemTableName).Where("Id = ?", processInstance.ItemId).Update("applystatus", util.ApplyStatusForDoing)
	}
	//审批实例结束
	if message.Type == "finish" || message.Type == "terminate" {
		if message.Result == "agree" {
			processInstance.Result = util.ProcessResultForPass
		}
		if message.Result == "refuse" {
			processInstance.Result = util.ProcessResultForRefuse
		}

		if message.Type == "finish" {
			processInstance.Status = util.ProcessStatusForDone
		}
		if message.Type == "terminate" {
			processInstance.Status = util.ProcessStatusForTerminate
		}

		var err error
		tx := model.DB().Begin()
		defer func() {
			if err != nil {
				tx.Rollback()
			} else {
				tx.Commit()
			}
		}()

		//将审批状态更新到流程数据库
		err = processInstance.UpdateStatus(tx)
		if err != nil {
			log.ErrorFields("BpmInstanceChange processInstance.UpdateStatus error", map[string]interface{}{"err": err})
			return err
		}

		switch processInstance.TemplateFormId {
		case config.StaffQuitApplyFormTemplate:
			err = StaffQuitApply(tx, processInstance)
			if err != nil {
				return err
			}
		case config.MaterialRequisitionApplyFormTemplate:
			if processInstance.Status == util.ApplyStatusForDone && processInstance.Result == util.ProcessResultForPass {
				err = ApproveSuccessOutStock(tx, processInstance)
				if err != nil {
					return err
				}
			}
			// 终止
			if processInstance.Status == util.ProcessStatusForTerminate {
				var stockR stockModel.StockRecord
				stockR.Id = processInstance.ItemId
				stockR.ApplyStatus = util.ApplyStatusForTerminate
				err = stockR.TransactionUpdateApplyStatus(tx)
				if err != nil {
					log.ErrorFields(`StockRecord.TransactionUpdateApplyStatus error`, map[string]interface{}{"err": err})
					return err
				}
			}
		}
	}

	return nil
}

func StaffQuitApply(tx *gorm.DB, processInstance processModel.DingTalkApplyProcess) (err error) {
	//将审批状态更新到业务数据库
	err = (&hrModel.StaffQuitRecord{}).UpdateApplyStatus(tx, processInstance.ItemId, processInstance.Status, processInstance.Result)
	if err != nil {
		log.ErrorFields("BpmInstanceChange StaffQuitRecord.UpdateApplyStatus error", map[string]interface{}{"err": err})
		return err
	}

	//如果流程结束并且审批结果是通过  需更新档案数据+生成修改记录日志
	if processInstance.Status == util.ApplyStatusForDone && processInstance.Result == util.ProcessResultForPass {
		var record hrModel.StaffQuitRecord
		err = json.Unmarshal(processInstance.Param, &record)
		if err != nil {
			log.ErrorFields("BpmInstanceChange StaffQuitRecord json.Unmarshal error", map[string]interface{}{"err": err, "param": processInstance.Param})
			return err
		}

		staff := rpc.GetStaffWithId(context.Background(), record.StaffId)
		if staff == nil {
			log.ErrorFields("BpmInstanceChange rpc.GetStaffWithId nil", map[string]interface{}{"staffId": record.StaffId})
			return errors.New("BpmInstanceChange rpc.GetStaffWithId nil")
		}

		beforeData, _ := json.Marshal(map[string]interface{}{"JobStatus": staff.WorkingState})

		err = service.StaffQuitAfterUpdateStaffArchive(tx, record)
		if err != nil {
			log.ErrorFields("BpmInstanceChange service.StaffQuitAfterUpdateStaffArchive error", map[string]interface{}{"err": err})
			return err
		}

		afterData, _ := json.Marshal(map[string]interface{}{"JobStatus": util.JobStatusQuit})
		go service.CreateStaffArchiveLogger(record.StaffArchiveId, record.OpUserId, record.OpUserName, hrModel.ArchiveLoggerSceneUpdate, record.OpIp, hrModel.ArchiveLoggerModularForQuit,
			beforeData, afterData, "")
	}
	return
}

func ApproveSuccessOutStock(tx *gorm.DB, processInstance processModel.DingTalkApplyProcess) (err error) {

	var outStockNum int64
	// 解析实例数据
	dingProcessInfo, err := dingTalkBpm.GetProcessInstance(processInstance.ProcessInstanceId, "")
	if err != nil {
		log.ErrorFields("dingTalkBpm.GetProcessInstance error", map[string]interface{}{"err": err})
	}

	if !IsApproveSuccess(dingProcessInfo.Status, dingProcessInfo.Result) {
		log.ErrorFields("dingTalkBpm.GetProcessInstance nonterminal state", map[string]interface{}{"dingProcessInfo": dingProcessInfo})
		return nil
	}

	// 实际领取数量
	for _, v := range dingProcessInfo.FormComponentValues {
		if v.Name == util.DingDingFormNameLabel_ActualNumber {
			outStockNum, _ = strconv.ParseInt(v.Value, 10, 64)
		}
	}

	// 查询钉钉用户对应的手机号
	var (
		opUserId       int64
		opUserName     string
		dingDingUserId string
	)

	for _, v := range dingProcessInfo.OperationRecords {
		if v.Result == dingTalkBpm.ProcessInstance_OperationRecordResult_Agree && v.Type == dingTalkBpm.ProcessInstance_OperationRecordType_Task_Normal {
			dingDingUserId = v.UserId
		}
	}
	if len(dingDingUserId) > 0 {
		dingUserInfo, err := dingTalkBpm.GetUserInfo(dingDingUserId, "")
		if err != nil {
			log.ErrorFields("dingTalkBpm.GetUserInfo error", map[string]interface{}{"err": err})
		}
		if len(dingUserInfo.Mobile) > 0 {
			userInfo := rpc.GetUserInfoByPhone(context.TODO(), config.Config.TopCorporationId, dingUserInfo.Mobile)
			if userInfo != nil {
				opUserId = userInfo.Id
				opUserName = userInfo.Nickname
			}
		}
	}

	var stockR stockModel.StockRecord
	err = stockR.GetById(processInstance.ItemId)
	if err != nil {
		log.ErrorFields("stockR.GetById error", map[string]interface{}{"err": err})
	}

	if stockR.ApplyStatus == util.ApplyStatusForDone {
		log.ErrorFields("Have been approved", map[string]interface{}{"StockRecordId": stockR.Id})
		return nil
	}

	var stock stockModel.Stock

	err = stock.Find(tx, stockR.StockId)
	if err != nil {
		log.ErrorFields(`stock.Find error`, map[string]interface{}{"err": err})
		return err
	}

	// 库存数量可能小于0
	stock.Num -= outStockNum
	err = stock.TransactionUpdateNum(tx, stock.Id, stock.Num)
	if err != nil {
		log.ErrorFields("stock.TransactionUpdateNum error", map[string]interface{}{"err": err})
		return err
	}

	stockR.Id = processInstance.ItemId
	stockR.OperateNum = outStockNum
	stockR.CurrentNum = stock.Num
	stockR.OpUserId = opUserId
	stockR.OpUserName = opUserName
	stockR.ApplyStatus = util.ApplyStatusForDone

	err = stockR.TransactionUpdate(tx)
	if err != nil {
		log.ErrorFields(`StockRecord.TransactionUpdate error`, map[string]interface{}{"err": err})
		return err
	}

	return err

}

func IsApproveSuccess(status, result string) bool {
	if status == "COMPLETED" && result == "agree" {
		return true
	}
	return false
}
