package learnHr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	learnModel "app/org/scs/erpv2/api/model/learn"
	"app/org/scs/erpv2/api/util/response"
	"context"
	api "github.com/micro/go-micro/v2/api/proto"
)

type TopicGroupHandler struct {
	Id         int64  `json:"Id"`
	CategoryId int64  `json:"CategoryId"`
	Title      string `json:"Title" qs:"LIKE"`
	TopicType  int64  `json:"TopicType"`
	model.Paginator
}

func (sa *TopicGroupHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form TopicGroupHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	qs := model.NewQs(form)
	//list, totalCount, err := (&learnModel.TopicGroup{}).List(form.Title, form.CategoryId, form.TopicType, form.Paginator)
	list, totalCount, err := (&learnModel.TopicGroup{}).List2(qs, form.Paginator)
	if err != nil {
		log.ErrorFields("TopicGroup List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (sa *TopicGroupHandler) Add(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.TopicGroup
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Topics == nil || len(form.Topics) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	err := form.Create()
	if err != nil {
		log.ErrorFields("TopicGroup ADD error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *TopicGroupHandler) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.TopicGroup
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Topics == nil || len(form.Topics) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	err := form.Update()
	if err != nil {
		log.ErrorFields("TopicGroup Update error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *TopicGroupHandler) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form TopicGroupHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	err := (&learnModel.TopicGroup{}).Delete(form.Id)
	if err != nil {
		log.ErrorFields("TopicGroup Delete error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}
