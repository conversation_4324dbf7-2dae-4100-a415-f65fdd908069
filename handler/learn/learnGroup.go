package learnHr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	learnModel "app/org/scs/erpv2/api/model/learn"
	"app/org/scs/erpv2/api/util/response"
	"context"
	api "github.com/micro/go-micro/v2/api/proto"
	"strings"
)

type LearnGroupHandler struct {
	Name      string `json:"Name"`
	StaffName string `json:"StaffName"`
	learnModel.LearnGroup
	model.Paginator
}

func (sa *LearnGroupHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LearnGroupHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	list, totalCount, err := (&learnModel.LearnGroup{}).List(form.Name, form.StaffName, form.Paginator)
	if err != nil {
		log.ErrorFields("learnModel List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (sa *LearnGroupHandler) Add(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.LearnGroup
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Name == "" || form.StaffIds == "" || form.StaffNames == "" {
		return response.Error(rsp, response.ParamsMissing)
	}
	err := form.Create()
	if err != nil {
		log.ErrorFields("LearnGroup Create error", map[string]interface{}{"error": err.Error()})
		if strings.Index(err.Error(), "duplicate key value violates unique constraint") != -1 {
			return response.Error(rsp, "当前群组名称已存在")
		} else {
			return response.Error(rsp, response.DbSaveFail)
		}
	}
	return response.Success(rsp, nil)
}

func (sa *LearnGroupHandler) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.LearnGroup
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 || form.Name == "" || form.StaffIds == "" || form.StaffNames == "" {
		return response.Error(rsp, response.ParamsMissing)
	}
	err := form.Update()
	if err != nil {
		log.ErrorFields("LearnGroup Create error", map[string]interface{}{"error": err.Error()})
		if strings.Index(err.Error(), "duplicate key value violates unique constraint") != -1 {
			return response.Error(rsp, "当前群组名称已存在")
		} else {
			return response.Error(rsp, response.DbSaveFail)
		}
	}
	return response.Success(rsp, nil)
}

func (sa *LearnGroupHandler) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LearnGroupHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.LearnGroup.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	err := form.LearnGroup.Delete(form.Id)
	if err != nil {
		log.ErrorFields("LearnGroup Delete error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}
