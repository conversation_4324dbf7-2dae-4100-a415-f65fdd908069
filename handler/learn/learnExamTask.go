package learnHr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	learnModel "app/org/scs/erpv2/api/model/learn"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"context"
	api "github.com/micro/go-micro/v2/api/proto"
)

type LearnExamTaskHandler struct {
	Id             int64  `json:"Id"`
	CourseTitle    string `json:"CourseTitle"`
	TestPaperTitle string `json:"TestPaperTitle"`
	CategoryID     int64  `json:"CategoryID"`
	CourseType     int64  `json:"CourseType"`
	StartTime      string `json:"StartTime"`
	EndTime        string `json:"EndTime"`
	TaskType       int64  `json:"TaskType"`
	model.Paginator
}

func (m *LearnExamTaskHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LearnExamTaskHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	list, totalCount, err := (&learnModel.LearnExamTask{}).List(form.CourseTitle, form.TestPaperTitle, form.StartTime, form.EndTime, form.CategoryID, form.CourseType, form.Paginator)
	if err != nil {
		log.ErrorFields("LearnExamTask List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	for index := range list {
		if list[index].NeedSummary == 2 {
			list[index].Summary()
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (m *LearnExamTaskHandler) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LearnExamTaskHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	var data learnModel.LearnExamTask
	_ = data.FindById(form.Id)
	if !data.IsModified() {
		return response.Error(rsp, "此指派已有人开始学习或者考试，无法删除")
	}
	err := data.Delete(form.Id)
	if err != nil {
		log.ErrorFields("LearnExamTask Delete error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (m *LearnExamTaskHandler) StaffSummaryRecordList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		TaskId         int64   `json:"TaskId"`
		StaffInfo      string  `json:"StaffInfo"`
		CorporationIds []int64 `json:"CorporationIds"` // 机构id
		model.Paginator
	}
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	corporationIds := service.AuthCorporationIdProvider(ctx, form.CorporationIds)
	list, totalCount, err := (&learnModel.StaffSummaryReport{}).List(corporationIds, 0, form.TaskId, form.StaffInfo, form.Paginator)
	if err != nil {
		log.ErrorFields("StaffSummaryReport List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if list != nil {
		for index, v := range list {
			list[index].StaffExamRecords, _ = (&learnModel.StaffExamRecord{}).List(v.StaffId, v.TaskId)
			list[index].StaffLearnRecords, _ = (&learnModel.StaffLearnRecord{}).List(v.StaffId, v.TaskId)
			list[index].CorporationId, list[index].CorporationName = list[index].Corporations.GetCorporation()
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (m *LearnExamTaskHandler) ExamTaskList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LearnExamTaskHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	user := auth.User(ctx).GetUser()
	list, totalCount, err := (&learnModel.LearnExamTask{}).ExamTaskList(form.TestPaperTitle, form.CategoryID, user.Id, form.Paginator)
	if err != nil {
		log.ErrorFields("LearnExamTask List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if list != nil {
		for index := range list {
			_ = list[index].MySummaryReport.Find(user.Id, list[index].Id)
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (m *LearnExamTaskHandler) LearnTaskList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LearnExamTaskHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	user := auth.User(ctx).GetUser()
	list, totalCount, err := (&learnModel.LearnExamTask{}).ExamTaskList(form.TestPaperTitle, form.CategoryID, user.Id, form.Paginator)
	if err != nil {
		log.ErrorFields("LearnExamTask List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if list != nil {
		for index := range list {
			_ = list[index].MySummaryReport.Find(user.Id, list[index].Id)
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (m *LearnExamTaskHandler) StartExam(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LearnExamTaskHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	user := auth.User(ctx).GetUser()
	staffExamRecordId, err := (&learnModel.LearnExamTask{}).StartExam(form.Id, user.Id)
	if err != nil {
		return response.Error(rsp, err.Error())
	}
	return response.Success(rsp, staffExamRecordId)
}

func (m *LearnExamTaskHandler) ShowTestPaper(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LearnExamTaskHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	var task learnModel.LearnExamTask
	err := task.FindById(form.Id)
	if err != nil {
		return response.Error(rsp, response.DbQueryFail)
	}
	var testPaper learnModel.TestPaper
	err = testPaper.FindForExam(form.Id)
	if err != nil {
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, map[string]interface{}{"Paper": testPaper, "StaticFileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

func (m *LearnExamTaskHandler) SubmitTestPaper(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		Paper             learnModel.TestPaper `json:"Topic"`             // 试卷
		TaskId            int64                `json:"TaskId"`            // 考试任务id
		StaffExamRecordId int64                `json:"StaffExamRecordId"` // 考试记录id
	}
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	finalScore, finalPass, err := form.Paper.Scoring(form.StaffExamRecordId)
	if err != nil {
		return response.Error(rsp, err.Error())
	}
	user := auth.User(ctx).GetUser()
	//
	var mySummaryReport learnModel.StaffSummaryReport
	_ = mySummaryReport.Find(user.Id, form.TaskId)
	_ = mySummaryReport.ExamSummary(form.Paper.ScoringType)
	_ = (&learnModel.LearnExamTask{}).ChangeSummaryStatus(form.TaskId)
	return response.Success(rsp, map[string]interface{}{"Score": finalScore, "Pass": finalPass})
}

func (m *LearnExamTaskHandler) StartLearn(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LearnExamTaskHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	//user := auth.User(ctx).GetUser()
	//err := (&learnModel.LearnExamTask{}).StartExam(form.Id, user.Id)
	//if err != nil {
	//	return response.Error(rsp, err.Error())
	//}
	return response.Success(rsp, nil)
}
