package learnHr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	learnModel "app/org/scs/erpv2/api/model/learn"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"strconv"
)

type LearnExamTaskHandler struct {
	Id             int64  `json:"Id"`
	CourseTitle    string `json:"CourseTitle"`
	TestPaperTitle string `json:"TestPaperTitle"`
	CategoryID     int64  `json:"CategoryID"`
	CourseType     int64  `json:"CourseType"`
	StartTime      string `json:"StartTime"`
	EndTime        string `json:"EndTime"`
	TaskType       int64  `json:"TaskType"`
	model.Paginator
}

func (m *LearnExamTaskHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LearnExamTaskHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	list, totalCount, err := (&learnModel.LearnExamTask{}).List(form.CourseTitle, form.TestPaperTitle, form.StartTime, form.EndTime, form.CategoryID, form.CourseType, form.Paginator)
	if err != nil {
		log.ErrorFields("LearnExamTask List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	for index := range list {
		if list[index].NeedSummary == 2 {
			list[index].Summary()
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (m *LearnExamTaskHandler) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LearnExamTaskHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	var data learnModel.LearnExamTask
	_ = data.FindById(form.Id)
	if !data.IsModified() {
		return response.Error(rsp, "此指派已有人开始学习或者考试，无法删除")
	}
	err := data.Delete(form.Id)
	if err != nil {
		log.ErrorFields("LearnExamTask Delete error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (m *LearnExamTaskHandler) StaffSummaryRecordList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		TaskId         int64   `json:"TaskId"`
		TaskType       int64   `json:"TaskType"`   // 1学习任务 2考试任务
		CourseType     int64   `json:"CourseType"` // 1必修 2选修
		StaffInfo      string  `json:"StaffInfo"`
		StaffId        int64   `json:"StaffId"`        // 学习人
		CorporationIds []int64 `json:"CorporationIds"` // 机构id
		StartTime      string  `json:"StartTime"`      // 开始时间
		EndTime        string  `json:"EndTime"`        // 结束时间
		CourseTitle    string  `json:"CourseTitle"`    // 课程名称
		TestPaperTitle string  `json:"TestPaperTitle"` // 考试名称
		IsPass         int64   `json:"IsPass"`         // 是否通过
		CategoryId     int64   `json:"CategoryId"`     // 分类id
		model.Paginator
	}
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	corporationIds := service.AuthCorporationIdProvider(ctx, form.CorporationIds)
	list, totalCount, err := (&learnModel.StaffSummaryReport{}).List(corporationIds, form.CategoryId, form.StaffId,
		form.TaskId, form.TaskType, form.CourseType, form.IsPass, form.StaffInfo, form.StartTime, form.EndTime, form.CourseTitle, form.TestPaperTitle, form.Paginator)
	if err != nil {
		log.ErrorFields("StaffSummaryReport List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if list != nil {
		for index, v := range list {
			if form.TaskType == 0 {
				list[index].StaffExamRecords, _ = (&learnModel.StaffExamRecord{}).List(v.StaffId, v.TaskId)
				list[index].StaffLearnRecords, _ = (&learnModel.StaffLearnRecord{}).List(v.StaffId, v.TaskId)
			}
			if form.TaskType == 1 {
				list[index].StaffLearnRecords, _ = (&learnModel.StaffLearnRecord{}).List(v.StaffId, v.TaskId)

			}
			if form.TaskType == 2 {
				list[index].StaffExamRecords, _ = (&learnModel.StaffExamRecord{}).List(v.StaffId, v.TaskId)
			}
			list[index].CorporationId, list[index].CorporationName = list[index].Corporations.GetCorporation()
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

// MyLearnList 小程序端课程列表
func (m *LearnExamTaskHandler) MyLearnList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		Year               string   `json:"Year"` // 年份
		CourseTitle        string   `json:"CourseTitle"`
		TestPaperTitle     string   `json:"TestPaperTitle"`
		CategoryIds        []string `json:"CategoryIds"`        // 分类ids
		CourseTypes        []int64  `json:"CourseTypes"`        // 1必修2选修
		CourseStatus       []int64  `json:"CourseStatus"`       // 1已完成2未完成3已过期
		ExpirationDateSort string   `json:"ExpirationDateSort"` // 截止时间， ASC,DESC
		LearnSort          string   `json:"LearnSort"`          // 完成进度 ASC,DESC
		model.Paginator
	}
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	categoryIds := util.StringArrToInt64(form.CategoryIds)
	user := auth.User(ctx).GetUser()
	staff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	var staffId int64
	if staff != nil {
		staffId = staff.Id
	}
	list, totalCount, err := (&learnModel.StaffSummaryReport{}).LearnList(staffId, form.Year, categoryIds, form.CourseTypes, form.CourseStatus, form.ExpirationDateSort, form.LearnSort, form.Paginator)
	if err != nil {
		log.ErrorFields("StaffSummaryReport List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount, "StaticFileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

// ShowTaskCourse 学习课程详情
func (m *LearnExamTaskHandler) ShowTaskCourse(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		TaskId string `json:"TaskId" validate:"required"` // 任务id
	}
	errCode := response.BindForm(req, &form)

	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	user := auth.User(ctx).GetUser()
	staff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	var staffId int64
	if staff != nil {
		staffId = staff.Id
	}
	var staffSummaryReport learnModel.StaffSummaryReport
	taskId, _ := strconv.ParseInt(form.TaskId, 10, 64)
	_ = staffSummaryReport.Find(staffId, taskId)
	var course learnModel.LearnCourse
	err := course.FindById(staffSummaryReport.CourseId)
	if err != nil {
		log.ErrorFields("course FindById error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, "未找到该课程详情")
	}
	if course.EducationalResources != nil {
		// 找到学习记录
		for index := range course.EducationalResources {
			var learnRecord learnModel.StaffLearnRecord
			_ = learnRecord.Find(staffSummaryReport.TaskId, staffId, staffSummaryReport.CourseId, course.EducationalResources[index].Id)
			if learnRecord.Id == 0 {
				learnRecord.StaffId = staffId
				learnRecord.TaskId = staffSummaryReport.TaskId
				learnRecord.CourseId = staffSummaryReport.CourseId
				learnRecord.EducationalResourceId = course.EducationalResources[index].Id
				learnRecord.EducationalResourceName = course.EducationalResources[index].Title
				learnRecord.TotalLearnTime = course.EducationalResources[index].FinishTime
				learnRecord.LearnTime = 0
				learnRecord.Status = 1
				_ = learnRecord.Create()
			}
			course.EducationalResources[index].LearnRecord = learnRecord
		}
	}
	return response.Success(rsp, map[string]interface{}{"Course": course, "info": staffSummaryReport, "StaticFileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

// ReportLearningProgress 上报学习进度
func (m *LearnExamTaskHandler) ReportLearningProgress(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		TaskId                string `json:"TaskId" validate:"required"`                // 任务id
		CourseId              string `json:"CourseId" validate:"required"`              // 课程id
		EducationalResourceId string `json:"EducationalResourceId" validate:"required"` // 学习资料id
		LearnTime             int64  `json:"LearnTime" validate:"required"`             // 学习时间 秒
	}
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	user := auth.User(ctx).GetUser()
	staff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	var staffId int64
	if staff != nil {
		staffId = staff.Id
	}
	var learnRecord learnModel.StaffLearnRecord
	taskId, _ := strconv.ParseInt(form.TaskId, 10, 64)
	courseId, _ := strconv.ParseInt(form.CourseId, 10, 64)
	educationalResourceId, _ := strconv.ParseInt(form.EducationalResourceId, 10, 64)
	_ = learnRecord.Find(taskId, staffId, courseId, educationalResourceId)
	learnRecord.LearnTime = form.LearnTime
	if learnRecord.LearnTime >= learnRecord.TotalLearnTime {
		learnRecord.Status = 2 // 已完成
	}
	err := learnRecord.Update()
	if err != nil {
		log.ErrorFields("learnRecord Update error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, "学习记录上报失败")
	}
	if learnRecord.Status == 2 {
		// 学习考试进度汇总
		var staffSummaryReport learnModel.StaffSummaryReport
		_ = staffSummaryReport.Find(staffId, taskId)
		err := staffSummaryReport.LearnSummary()
		if err != nil {
			log.ErrorFields("staffSummaryReport LearnSummary error", map[string]interface{}{"error": err.Error()})
		}
		_ = (&learnModel.LearnExamTask{}).ChangeSummaryStatus(taskId)
	}

	return response.Success(rsp, learnRecord.Status)
}

// MyExamList 小程序考试列表
func (m *LearnExamTaskHandler) MyExamList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		Year        string   `json:"Year"`        // 年份
		CategoryIds []string `json:"CategoryIds"` // 分类ids
		ExamStatus  []int64  `json:"CourseTypes"` // 当前考试状态 1补考 2正常考试 3可刷分 4已完成
		model.Paginator
	}
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	CategoryIds := util.StringArrToInt64(form.CategoryIds)
	user := auth.User(ctx).GetUser()
	staff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	var staffId int64
	if staff != nil {
		staffId = staff.Id
	}
	list, totalCount, err := (&learnModel.StaffSummaryReport{}).ExamList(staffId, form.Year, CategoryIds, form.ExamStatus, form.Paginator)
	if err != nil {
		log.ErrorFields("StaffSummaryReport ExamList error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

// StartExam 开始考试接口
func (m *LearnExamTaskHandler) StartExam(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		TaskId string `json:"TaskId"`
	}
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.TaskId == "" {
		return response.Error(rsp, response.ParamsMissing)
	}
	taskId, _ := strconv.ParseInt(form.TaskId, 10, 64)
	user := auth.User(ctx).GetUser()
	staff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	var staffId int64
	if staff != nil {
		staffId = staff.Id
	}
	staffExamRecordId, err := (&learnModel.LearnExamTask{}).StartExam(taskId, staffId)
	if err != nil {
		return response.Error(rsp, err.Error())
	}
	return response.Success(rsp, staffExamRecordId)
}

// ShowTestPaper 试卷展示
func (m *LearnExamTaskHandler) ShowTestPaper(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		TaskId string `json:"TaskId"`
	}
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.TaskId == "" {
		return response.Error(rsp, response.ParamsMissing)
	}
	taskId, _ := strconv.ParseInt(form.TaskId, 10, 64)
	var task learnModel.LearnExamTask
	err := task.FindById(taskId)
	if err != nil {
		return response.Error(rsp, response.DbQueryFail)
	}
	var testPaper learnModel.TestPaper
	err = testPaper.FindForExam(task.TestPaperId)
	if err != nil {
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, map[string]interface{}{"Paper": testPaper, "StaticFileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

// SubmitTestPaper 试卷交卷
func (m *LearnExamTaskHandler) SubmitTestPaper(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		Paper             learnModel.TestPaperForMini `json:"Paper"`             // 试卷
		TaskId            string                      `json:"TaskId"`            // 考试任务id
		StaffExamRecordId string                      `json:"StaffExamRecordId"` // 考试记录id
	}
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	staffExamRecordId, _ := strconv.ParseInt(form.StaffExamRecordId, 10, 64)
	taskId, _ := strconv.ParseInt(form.TaskId, 10, 64)
	fmt.Println("form.Paper:", form.Paper)
	finalScore, finalPass, err := form.Paper.Scoring(staffExamRecordId)
	if err != nil {
		return response.Error(rsp, err.Error())
	}
	user := auth.User(ctx).GetUser()
	staff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	var staffId int64
	if staff != nil {
		staffId = staff.Id
	}
	//
	var mySummaryReport learnModel.StaffSummaryReport
	_ = mySummaryReport.Find(staffId, taskId)
	_ = mySummaryReport.ExamSummary(form.Paper.ScoringType, form.Paper.RepeatExam)
	_ = (&learnModel.LearnExamTask{}).ChangeSummaryStatus(taskId)
	return response.Success(rsp, map[string]interface{}{"Score": finalScore, "Pass": finalPass})
}

// ExamDetail 考试详情
func (m *LearnExamTaskHandler) ExamDetail(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		TaskId string `json:"TaskId"`
	}
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	taskId, _ := strconv.ParseInt(form.TaskId, 10, 64)
	user := auth.User(ctx).GetUser()
	staff := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, user.Phone)
	var staffId int64
	if staff != nil {
		staffId = staff.Id
	}
	var mySummaryReport learnModel.StaffSummaryReport
	_ = mySummaryReport.Find(staffId, taskId)
	// 找到最新的考试记录
	var staffExamRecord learnModel.StaffExamRecord
	_ = staffExamRecord.FindLatest(staffId, taskId)
	// 找到详情试卷
	var testPaper learnModel.TestPaper
	_ = testPaper.FindForExamDetail(mySummaryReport.TestPaperId, staffExamRecord.Id)
	return response.Success(rsp, map[string]interface{}{"Paper": testPaper, "mySummaryReport": mySummaryReport, "staffExamRecord": staffExamRecord, "StaticFileHttpPrefix": config.Config.StaticFileHttpPrefix})
}
