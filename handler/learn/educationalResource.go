package learnHr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	learnModel "app/org/scs/erpv2/api/model/learn"
	"app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/util/response"
	"context"
	api "github.com/micro/go-micro/v2/api/proto"
	"strings"
)

type EducationalResourceHandler struct {
	Id           int64  `json:"Id"`
	CategoryId   int64  `json:"CategoryId"`
	ResourceType int64  `json:"ResourceType"`
	Title        string `json:"Title"`
	model.Paginator
}

func (sa *EducationalResourceHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form EducationalResourceHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	list, totalCount, err := (&learnModel.EducationalResource{}).List(form.Title, form.CategoryId, form.ResourceType, form.Paginator)
	if err != nil {
		log.ErrorFields("EducationalResource List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if list != nil {
		for index := range list {
			categoryArr := (&setting.TopicCategory{}).FindCategoryNames(list[index].CategoryIds)
			list[index].CategoryNames = strings.Join(categoryArr, ";")
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (sa *EducationalResourceHandler) Add(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.EducationalResource
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	err := form.Create()
	if err != nil {
		log.ErrorFields("EducationalResource Create error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *EducationalResourceHandler) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.EducationalResource
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	err := form.Update()
	if err != nil {
		log.ErrorFields("EducationalResource Update error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *EducationalResourceHandler) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form EducationalResourceHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	err := (&learnModel.EducationalResource{}).Delete(form.Id)
	if err != nil {
		log.ErrorFields("EducationalResource Delete error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}
