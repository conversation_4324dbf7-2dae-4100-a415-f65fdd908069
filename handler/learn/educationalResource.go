package learnHr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	learnModel "app/org/scs/erpv2/api/model/learn"
	"app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"archive/zip"
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"io"
	"path/filepath"
	"strings"
	"time"
)

type EducationalResourceHandler struct {
	Id           int64  `json:"Id"`
	CategoryId   int64  `json:"CategoryId"`
	ResourceType int64  `json:"ResourceType"`
	Title        string `json:"Title"`
	model.Paginator
}

func (sa *EducationalResourceHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form EducationalResourceHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	list, totalCount, err := (&learnModel.EducationalResource{}).List(form.Title, form.CategoryId, form.ResourceType, form.Paginator)
	if err != nil {
		log.ErrorFields("EducationalResource List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if list != nil {
		for index := range list {
			categoryArr := (&setting.TopicCategory{}).FindCategoryNames(list[index].CategoryIds)
			list[index].CategoryNames = strings.Join(categoryArr, ";")
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (sa *EducationalResourceHandler) Add(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.EducationalResource
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	err := form.Create()
	if err != nil {
		log.ErrorFields("EducationalResource Create error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *EducationalResourceHandler) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.EducationalResource
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	err := form.Update()
	if err != nil {
		log.ErrorFields("EducationalResource Update error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *EducationalResourceHandler) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form EducationalResourceHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	err := (&learnModel.EducationalResource{}).Delete(form.Id)
	if err != nil {
		log.ErrorFields("EducationalResource Delete error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *EducationalResourceHandler) Import(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		FinishTime  int64  `json:"FinishTime" validate:"required"`  // 学习完成时间 秒
		FileData    string `json:"FileData" validate:"required"`    // zip文件base64
		CategoryIds string `json:"CategoryIds" validate:"required"` // 资料分类ids ","拼接
	}
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	zipData, err := base64.StdEncoding.DecodeString(form.FileData)
	if err != nil {
		return response.Error(rsp, "请上传正确的文件")
	}
	// 判断是否是zip文件
	if !bytes.HasPrefix(zipData, []byte("PK\x03\x04")) {
		return response.Error(rsp, "不是有效的 zip 文件")
	}
	MaxZipSize := 50 * 1024 * 1024
	if len(zipData) > MaxZipSize {
		return response.Error(rsp, "zip 文件大小不能超过 50MB")
	}
	reader, err := zip.NewReader(bytes.NewReader(zipData), int64(len(zipData)))
	if err != nil {
		return response.Error(rsp, "解析 zip 失败:"+err.Error())
	}
	if reader.File == nil || len(reader.File) == 0 {
		return response.Error(rsp, "zip包中没有任何文件")
	}
	var errorForm []model.ExcelForm
	for _, f := range reader.File {
		if f.FileInfo().IsDir() {
			continue // 📁 跳过文件夹
		}
		filename := filepath.Base(f.Name)
		fmt.Println("==============================fileName:", filename)
		if !strings.HasSuffix(strings.ToLower(filename), ".pdf") {
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 不是pdf文件", filename),
			})
			continue
		}
		rc, err := f.Open()
		if err != nil {
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 打开文件失败:%v", filename, err),
			})
			continue
		}
		fileBytes, _ := io.ReadAll(rc)
		base64Str := base64.StdEncoding.EncodeToString(fileBytes)
		newFileName := fmt.Sprintf("%s%s", time.Now().Format("20060102150405"), filename)
		newFile, _, err := util.CreateFile(newFileName, base64Str, config.Config.AbsDirPath, config.Config.WebRoot, "erp", "learn_pdf", time.Now().Format("20060102"))
		relativeFilePath := fmt.Sprintf(`%s/erp/%v/%s/%s`, config.Config.WebRoot, "learn_pdf", time.Now().Format("20060102"), newFileName)
		if err != nil {
			_ = rc.Close()
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 创建文件失败:%s", filename, err.Error()),
			})
			continue
		}
		var data learnModel.EducationalResource
		data.Title = strings.ReplaceAll(filename, ".pdf", "")
		data.FinishTime = form.FinishTime
		data.CategoryIds = form.CategoryIds
		data.ResourceType = 2
		var jsonObj model.JSON
		m := make(map[string]interface{})
		m["Id"] = model.Id()
		m["Name"] = filename
		m["Path"] = relativeFilePath
		m["Suffix"] = "pdf"
		m["Type"] = 1
		m["Url"] = fmt.Sprintf("%s%s", config.Config.StaticFileHttpPrefix, relativeFilePath)
		b, _ := json.Marshal(&m)
		_ = jsonObj.UnmarshalJSON(b)
		data.Content = jsonObj
		_ = newFile.Close()
		_ = rc.Close()
		err = data.Create()
		if err != nil {
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 数据插入失败", filename),
			})
			continue
		}

	}
	return response.Success(rsp, errorForm)
}
