package learnHr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	learnModel "app/org/scs/erpv2/api/model/learn"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"context"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type CourseHandler struct {
	Id         int64  `json:"Id"`
	CourseType int64  `json:"CourseType"`
	CategoryId int64  `json:"CategoryId"`
	StartTime  string `json:"StartTime"`
	EndTime    string `json:"EndTime"`
	Title      string `json:"Title"`
	model.Paginator
}

func (sa *CourseHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form CourseHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	list, totalCount, err := (&learnModel.LearnCourse{}).List(form.Title, form.StartTime, form.EndTime, form.CourseType, form.CategoryId, form.Paginator)
	if err != nil {
		log.ErrorFields("LearnCourse List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (sa *CourseHandler) Add(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.LearnCourse
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.EducationalResources == nil || len(form.EducationalResources) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	if form.NeedExamination == 1 {
		var testPaper learnModel.TestPaper
		_ = testPaper.FindById(form.TestPaperId)
		if time.Now().Unix() >= time.Time(testPaper.ExpirationDate).Unix() {
			return response.Error(rsp, "LA1001")
		}
	}
	err := form.Create()
	if err != nil {
		log.ErrorFields("LearnCourse Create error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *CourseHandler) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.LearnCourse
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.EducationalResources == nil || len(form.EducationalResources) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	if form.NeedExamination == 1 {
		var testPaper learnModel.TestPaper
		_ = testPaper.FindById(form.TestPaperId)
		if time.Now().Unix() >= time.Time(testPaper.ExpirationDate).Unix() {
			return response.Error(rsp, "LA1001")
		}
	}
	if !form.IsModified() {
		return response.Error(rsp, "此课程已有指派任务,无法修改")
	}
	err := form.Update()
	if err != nil {
		log.ErrorFields("TestPaper Update error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *CourseHandler) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form CourseHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	var data learnModel.LearnCourse
	data.Id = form.Id
	if !data.IsModified() {
		return response.Error(rsp, "此课程已有指派任务,无法修改")
	}
	err := (&learnModel.LearnCourse{}).Delete(form.Id)
	if err != nil {
		log.ErrorFields("LearnCourse Delete error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *CourseHandler) AddExaminationTask(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.AllocationForm
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	//
	if form.CourseId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	if form.AllocationType == 1 && (form.StaffIds == nil || len(form.StaffIds) == 0) {
		return response.Error(rsp, response.ParamsMissing)
	}
	if form.AllocationType == 2 && (form.GroupIds == nil || len(form.GroupIds) == 0) {
		return response.Error(rsp, response.ParamsMissing)
	}
	user := auth.User(ctx).GetUser()
	err := (&learnModel.LearnExamTask{}).PublishLearnTask(form, user)
	if err != nil {
		return response.Error(rsp, err.Error())
	}
	return response.Success(rsp, nil)
}
