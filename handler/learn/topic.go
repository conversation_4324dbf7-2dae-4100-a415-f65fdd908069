package learnHr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	learnModel "app/org/scs/erpv2/api/model/learn"
	"app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"strings"
)

type TopicHandler struct {
	Code       string  `json:"Code"`
	TopicType  int64   `json:"TopicType"`
	CategoryId int64   `json:"CategoryId"`
	DeleteIds  []int64 `json:"DeleteIds"`
	CreatedDay string  `json:"CreatedDay"` // 创建时间 YYYY-MM-DD
	model.Paginator
}

func (sa *TopicHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form TopicHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	list, totalCount, err := (&learnModel.Topic{}).List(form.Code, form.TopicType, form.CategoryId, form.CreatedDay, form.Paginator)
	if err != nil {
		log.ErrorFields("Topic List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	//if list != nil {
	//	for index := range list {
	//		if list[index].Answers == nil {
	//			continue
	//		}
	//		for j := range list[index].Answers {
	//			if list[index].Answers[j].AnswerType == 2 {
	//				list[index].Answers[j].PictureContent = fmt.Sprintf("%s%s", config.Config.StaticFileHttpPrefix, list[index].Answers[j].PictureContent)
	//			}
	//		}
	//	}
	//}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (sa *TopicHandler) Add(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.Topic
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if err := form.IsLegalAnswer(); err != nil {
		return response.Error(rsp, err.Error())
	}
	err := form.Create()
	if err != nil {
		log.ErrorFields("Topic Create error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *TopicHandler) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.Topic
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	if err := form.IsLegalAnswer(); err != nil {
		return response.Error(rsp, err.Error())
	}
	err := form.Update()
	if err != nil {
		log.ErrorFields("Topic Update error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

func (sa *TopicHandler) BatchDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form TopicHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.DeleteIds == nil || len(form.DeleteIds) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	err := (&learnModel.Topic{}).Delete(form.DeleteIds)
	if err != nil {
		log.ErrorFields("Topic Delete error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

type ExcelForm struct {
	RowIndex int `json:"RowIndex"`
	//Info  *xlsx.Row `json:"Info"`
	Error string `json:"Error"`
}

func (sa *TopicHandler) Import(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		FileData string `json:"FileData" validate:"required"`
	}
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	decodeString, err := base64.StdEncoding.DecodeString(form.FileData)
	if err != nil {
		log.Error("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.Error("excelize.OpenBinary[err]:", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	if len(excelFile.Sheets) == 0 {
		log.Error("excelFile.Sheets为空")
		return response.Error(rsp, response.FAIL)
	}
	rows := excelFile.Sheets[0].Rows
	var errRows []ExcelForm
	if rows != nil {
		for index, row := range rows {
			if index == 0 {
				continue
			}
			var topic learnModel.Topic
			var answers string
			rightAnswer := ""
			for i, cell := range row.Cells {
				switch i {
				case 0:
					topic.Content = cell.String()
				case 1:
					topicType := cell.String()
					if topicType == "单选" {
						topic.TopicType = 1
					} else if topicType == "多选" {
						topic.TopicType = 2
					} else if topicType == "判断" {
						topic.TopicType = 3
					}
				case 2:
					categoryName := cell.String()
					var topicCategory setting.TopicCategory
					_ = topicCategory.FindByCategory(categoryName)
					topic.CategoryId = topicCategory.Id
				case 3:
					answerRandom := cell.String()
					if answerRandom == "是" {
						topic.AnswerRandom = 1
					} else {
						topic.AnswerRandom = 2
					}
				case 4:
					answers = cell.String()
				case 5:
					rightAnswer = cell.String()
				case 6:
					topic.Analysis = cell.String()
				}

			}
			tx := model.DB().Begin()
			err = topic.TxCreate(tx)
			if err != nil {
				tx.Rollback()
				errRows = append(errRows, ExcelForm{
					RowIndex: index + 1,
					Error:    "题目数据保存失败",
				})
				continue
			}
			answerArr := strings.Split(answers, "\n")
			if answerArr == nil || len(answerArr) == 0 {
				tx.Rollback()
				errRows = append(errRows, ExcelForm{
					RowIndex: index + 1,
					Error:    fmt.Sprintf("未设置答案"),
				})
				continue
			}
			saveFlag := true
			haveRightAnswer := false
			for i, answerStr := range answerArr {
				arr := strings.Split(answerStr, "、")
				if len(arr) < 2 {
					tx.Rollback()
					errRows = append(errRows, ExcelForm{
						RowIndex: index + 1,
						Error:    fmt.Sprintf("第%d个答案格式不正确", i+1),
					})
					saveFlag = false
					break
				}
				var topicAnswer learnModel.TopicAnswer
				topicAnswer.TopicID = topic.Id
				topicAnswer.AnswerType = 1
				topicAnswer.TextContent = arr[1]
				if strings.Index(rightAnswer, arr[0]) != -1 {
					haveRightAnswer = true
					topicAnswer.IsRight = 1
				} else {
					topicAnswer.IsRight = 2
				}
				err = topicAnswer.TxCreate(tx)
				if err != nil {
					tx.Rollback()
					errRows = append(errRows, ExcelForm{
						RowIndex: index + 1,
						Error:    fmt.Sprintf("第%d个答案保存错误,错误原因:%s", i+1, "答案数据保存失败"),
					})
					saveFlag = false
					break
				}
			}
			if !saveFlag {
				continue
			}
			if !haveRightAnswer {
				tx.Rollback()
				errRows = append(errRows, ExcelForm{
					RowIndex: index + 1,
					Error:    fmt.Sprintf("未设置正确答案"),
				})
				continue
			}
			tx.Commit()
		}
	}
	return response.Success(rsp, errRows)
}
