package learnHr

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	learnModel "app/org/scs/erpv2/api/model/learn"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"context"
	api "github.com/micro/go-micro/v2/api/proto"
)

type TestPaperHandler struct {
	Id         int64  `json:"Id"`
	CategoryId int64  `json:"CategoryId"`
	StartTime  string `json:"StartTime"`
	EndTime    string `json:"EndTime"`
	Title      string `json:"Title"`
	model.Paginator
}

func (sa *TestPaperHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form TestPaperHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	list, totalCount, err := (&learnModel.TestPaper{}).List(form.Title, form.StartTime, form.EndTime, form.CategoryId, form.Paginator)
	if err != nil {
		log.ErrorFields("TestPaper List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (sa *TestPaperHandler) Add(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.TestPaper
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Topics == nil || len(form.Topics) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	err := form.Create()
	if err != nil {
		log.ErrorFields("TestPaper Create error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *TestPaperHandler) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.TestPaper
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Topics == nil || len(form.Topics) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	if !form.IsModified() {
		return response.Error(rsp, "此试卷已有指派任务,无法修改")
	}
	err := form.Update()
	if err != nil {
		log.ErrorFields("TestPaper Update error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *TestPaperHandler) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form TestPaperHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	var data learnModel.TestPaper
	data.Id = form.Id
	if !data.IsModified() {
		return response.Error(rsp, "此试卷已有指派任务,无法修改")
	}
	err := (&learnModel.TestPaper{}).Delete(form.Id)
	if err != nil {
		log.ErrorFields("TestPaper Delete error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, nil)
}

func (sa *TestPaperHandler) AddExaminationTask(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form learnModel.AllocationForm
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	//
	if form.TestPaperId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	if form.AllocationType == 1 && (form.StaffIds == nil || len(form.StaffIds) == 0) {
		return response.Error(rsp, response.ParamsMissing)
	}
	if form.AllocationType == 2 && (form.GroupIds == nil || len(form.GroupIds) == 0) {
		return response.Error(rsp, response.ParamsMissing)
	}
	user := auth.User(ctx).GetUser()
	err := (&learnModel.LearnExamTask{}).PublishExamTask(form, user)
	if err != nil {
		return response.Error(rsp, err.Error())
	}
	return response.Success(rsp, nil)
}
