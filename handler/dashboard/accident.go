package dashboard

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/process/lbpm/listener"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"strconv"
	"strings"
)

func (*Dashboard) UpdateAccidentForm(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param safetyModel.TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var trafficAccident safetyModel.TrafficAccident
	err = trafficAccident.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("TrafficAccident not found", map[string]interface{}{"id": param.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//机构信息
	param.Corporations.Build(param.CorporationId)

	tx := model.DB().Begin()
	//更新事故
	err = param.TransactionUpdate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("accident.Update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}
	tx.Commit()

	//当事故车辆需要维修，并且没有车损分支时，系统需自动创建当事人和车损分支
	var relaterId int64
	if param.IsMustFix == util.StatusForTrue && !param.ExistSelfVehicleBrokenBranch(param.Id) {
		relaterId, err = listener.CreateAccidentRelater(&param)
		if err != nil {
			return response.Error(rsp, response.DbUpdateFail)
		}

		err = listener.CreateAccidentSelfVehicleBrokenBranch(&param, relaterId)
		if err != nil {
			return response.Error(rsp, response.DbUpdateFail)
		}
	}

	var hasSelfVehicleBrokenBranchProcess bool
	//查询事故的己方车损分支
	branch := (&safetyModel.TrafficAccidentRelaterBranch{}).FindSelfVehicleBrokenBranchByAccidentId(param.Id)
	hasSelfVehicleBrokenBranch := branch.Id > 0
	if hasSelfVehicleBrokenBranch {
		//判断己方车损分支是否有流程
		hasSelfVehicleBrokenBranchProcess = branch.IsExistNormalProcess(branch.Id)
	}

	//事故是否维修状态为否，并且有己方车损分支，并且己方车损分支未有相关流程  则删除己方车损分支
	if param.IsMustFix == util.StatusForFalse && hasSelfVehicleBrokenBranch && !hasSelfVehicleBrokenBranchProcess {
		err = branch.Delete(branch.Id)
		if err != nil {
			log.ErrorFields("TrafficAccidentEditProcessEndEvent branch.Delete error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbUpdateFail)
		}
	}

	hasSelfPeopleHurt := strings.Contains(param.OwnStatus, strconv.FormatInt(util.AccidentBranchTypeForSelfPeopleHurt, 10))
	//如果己方人伤被勾选 需创建己方人伤分支
	if hasSelfPeopleHurt {
		if relaterId == 0 {
			relaterId, err = listener.CreateAccidentRelater(&param)
			if err != nil {
				return response.Error(rsp, response.DbUpdateFail)
			}
		}

		err = listener.CreateAccidentSelfPeopleHurtBranch(&param, relaterId)
		if err != nil {
			return response.Error(rsp, response.DbUpdateFail)
		}
	}

	//如果己方人伤没有勾选，则需删除己方人伤分支以及分支上的无效流程
	if !hasSelfPeopleHurt {
		err = listener.DeleteSelfPeopleHurtBranchAndProcess(param.Id)
		if err != nil {
			log.ErrorFields("TrafficAccidentEditProcessEndEvent DeleteSelfPeopleHurtBranchAndProcess error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbUpdateFail)
		}
	}

	// 将机构信息存入日志
	param.CorporationId, param.CorporationName = param.Corporations.GetCorporation()

	user := auth.User(ctx).GetUser()
	//新增操作日志
	go service.BuildAccidentLogger(user.Id, user.Name, auth.User(ctx).GetClientIp(), &trafficAccident, &param, "")

	return response.Success(rsp, nil)
}

func (*Dashboard) UpdateAccidentClosedForm(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param safetyModel.TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var accident safetyModel.TrafficAccident
	err = accident.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("accident.FindBy error", map[string]interface{}{"id": param.Id})
		return response.Error(rsp, response.FAIL)
	}

	accident.PersonalPayRatio = param.PersonalPayRatio
	accident.LossMoney = param.LossMoney
	accident.CloseDesc = param.CloseDesc
	accident.InsuranceCompanyPayMoney = param.InsuranceCompanyPayMoney
	accident.InsurancePayMoney = param.InsurancePayMoney
	accident.CloseFilePath = param.CloseFilePath
	accident.PayOrigin = param.PayOrigin
	accident.IsMustPay = param.IsMustPay
	accident.PayMoneyAt = param.PayMoneyAt
	accident.PayMoneyFile = param.PayMoneyFile

	err = accident.DashboardUpdateCloseInfo()
	if err != nil {
		log.ErrorFields("accident.DashboardUpdateCloseInfo error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (*Dashboard) UpdateAccidentBranchClosedForm(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param safetyModel.TrafficAccidentRelaterBranch
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var branch safetyModel.TrafficAccidentRelaterBranch
	err = branch.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("branch.FindBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	branch.SolutionType = param.SolutionType
	branch.SolutionFilePath = param.SolutionFilePath
	branch.InsuranceCompanyPayMoney = param.InsuranceCompanyPayMoney
	branch.InsurancePayMoney = param.InsurancePayMoney
	branch.LossMoney = param.LossMoney
	branch.SolutionDesc = param.SolutionDesc
	branch.PayOrigin = param.PayOrigin
	branch.IsMustPay = param.IsMustPay
	branch.PayMoneyAt = param.PayMoneyAt
	branch.PayMoneyFile = param.PayMoneyFile

	err = branch.DashboardUpdateCloseInfo()
	if err != nil {
		log.ErrorFields("branch.DashboardUpdateCloseInfo error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

func (*Dashboard) UpdateAccidentLendMoneyForm(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param safetyModel.TrafficAccidentLendMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var lendMoneyRecord safetyModel.TrafficAccidentLendMoneyRecord
	if err := lendMoneyRecord.FindBy(param.Id); err != nil {
		log.ErrorFields("TrafficAccidentLendMoneyRecord FindBy fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	err = param.DashboardUpdate()

	if err != nil {
		log.ErrorFields("TrafficAccidentLendMoneyRecord.DashboardUpdate error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func (*Dashboard) UpdateAccidentDrawbackMoneyForm(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param safetyModel.TrafficAccidentDrawbackMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var drawbackMoneyRecord safetyModel.TrafficAccidentDrawbackMoneyRecord
	if err := drawbackMoneyRecord.FindBy(param.Id); err != nil {
		log.ErrorFields("TrafficAccidentDrawbackMoneyRecord FindBy fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	err = param.DashboardUpdate()

	return response.Success(rsp, nil)
}

func (*Dashboard) UpdateAccidentPaymentMoneyForm(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param safetyModel.TrafficAccidentPaymentMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var paymentMoneyRecord safetyModel.TrafficAccidentPaymentMoneyRecord
	if err := paymentMoneyRecord.FindBy(param.Id); err != nil {
		log.ErrorFields("TrafficAccidentPaymentMoneyRecord FindBy fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	err = param.DashboardUpdate()

	return response.Success(rsp, nil)
}

type AccidentRequest struct {
	Code        string `json:"Code"`
	Status      int64  `json:"Status"`
	BranchCount int64  `json:"BranchCount"`
	model.Paginator
}

func (*Dashboard) AccidentList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AccidentRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	accidents, count := (&safetyModel.TrafficAccident{}).DashboardGetAll(param.Code, param.Status, param.BranchCount, param.Paginator)
	for i := range accidents {
		accidents[i].TotalBranchCount = (&safetyModel.TrafficAccidentRelaterBranch{}).GetBranchCount(accidents[i].Id, 0)
		accidents[i].ClosedBranchCount = (&safetyModel.TrafficAccidentRelaterBranch{}).GetBranchCount(accidents[i].Id, util.StatusForTrue)
		accidents[i].HasApplyingPaymentMoneyProcess = (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).IsExistDoingPaymentRecord(accidents[i].Id)
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      accidents,
		"TotalCount": count,
	})
}

type AccidentStatusEditForm struct {
	Id       int64                                 `json:"Id"`
	IsClosed int64                                 `json:"IsClosed"`
	Branches []AccidentRelaterBranchStatusEditItem `json:"Branches"`
	Relaters []AccidentRelaterBranchStatusEditItem `json:"Relaters"`
}

type AccidentRelaterBranchStatusEditItem struct {
	Id       int64 `json:"Id"`
	IsClosed int64 `json:"IsClosed"`
	IsDelete int64 `json:"IsDelete"`
}

func (*Dashboard) AccidentStatusEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AccidentStatusEditForm
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()
	if param.IsClosed == util.StatusForTrue {
		var accident safetyModel.TrafficAccident
		err = accident.FindBy(param.Id)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("accident.FindBy error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}

		err = accident.TransactionUpdateColumn(tx, accident.Id, "IsClosed", param.IsClosed)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("accident.FindBy error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	if len(param.Branches) > 0 {
		for i := range param.Branches {
			var branch safetyModel.TrafficAccidentRelaterBranch
			err = branch.FindBy(param.Branches[i].Id)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("branch.FindBy error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.FAIL)
			}

			if param.Branches[i].IsDelete == util.StatusForTrue {
				err = service.DeleteBranchAndRelationData(tx, []int64{param.Branches[i].Id})
				if err != nil {
					tx.Rollback()
					log.ErrorFields("DeleteBranchAndRelationData error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.FAIL)
				}
			} else {
				err = branch.TransactionDashboardUpdateClose(tx, branch.Id, "IsClosed", param.Branches[i].IsClosed)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("branch.TransactionDashboardUpdateClose error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.FAIL)
				}
			}
		}
	}

	if len(param.Relaters) > 0 {
		for i := range param.Relaters {
			var relater safetyModel.TrafficAccidentRelater
			err = relater.FindBy(param.Relaters[i].Id)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("relater.FindBy error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.FAIL)
			}

			if param.Relaters[i].IsDelete == util.StatusForTrue {
				//查询当事人下的所有分支Id
				branchIds := (&safetyModel.TrafficAccidentRelaterBranch{}).GetRelaterBranchIds(param.Relaters[i].Id)
				if len(branchIds) > 0 {
					err = service.DeleteBranchAndRelationData(tx, []int64{param.Branches[i].Id})
					if err != nil {
						tx.Rollback()
						log.ErrorFields("DeleteBranchAndRelationData error", map[string]interface{}{"err": err})
						return response.Error(rsp, response.FAIL)
					}
				}

				//删除当事人
				err = (&safetyModel.TrafficAccidentRelater{}).TransactionDelete(tx, param.Relaters[i].Id)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("TrafficAccidentRelaterBranch.TransactionDelete error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.FAIL)
				}
			}
		}
	}

	tx.Commit()
	return response.Success(rsp, nil)
}
