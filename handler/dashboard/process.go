package dashboard

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/message"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"strings"
)

type Dashboard struct{}

type ProcessRequest struct {
	FormInstanceId  int64   `json:"FormInstanceId"`
	FormInstanceIds []int64 `json:"FormInstanceIds"`
	Title           string  `json:"Title"`
	ApplyUserName   string  `json:"ApplyUserName"`
	ProcessId       string  `json:"ProcessId"`
	CorporationId   int64   `json:"CorporationId"`
	CurrentHandler  string  `json:"CurrentHandler"`
	Status          []int64 `json:"Status"`
	TemplateFormId  string  `json:"TemplateFormId"`
	ModelId         string  `json:"ModelId"`
	AccidentCode    string  `json:"AccidentCode"`
	Ids             []int64 `json:"Ids"`
	FormStep        int64   `json:"FormStep"`
	ApplyStatus     int64   `json:"ApplyStatus"`
	model.Paginator
}

func (*Dashboard) AllProcess(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ProcessRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	processes, count := (&processModel.LbpmApplyProcess{}).GetBy(param.ProcessId, param.Title, param.ApplyUserName, param.CurrentHandler, param.TemplateFormId, param.ModelId, param.CorporationId, param.Status, param.Paginator)

	return response.Success(rsp, map[string]interface{}{
		"Items":      processes,
		"TotalCount": count,
	})
}

type AccidentProcessResponse struct {
	processModel.LbpmApplyProcess
	FormStep int64 `json:"FormStep"`
}

func (*Dashboard) AccidentProcess(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ProcessRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	processes, count := (&safetyModel.TrafficAccident{}).GetAccidentProcess(param.CorporationId, param.FormStep, param.Title, param.AccidentCode, param.ApplyUserName, param.TemplateFormId, param.CurrentHandler, param.Status, param.Paginator)

	var results []AccidentProcessResponse
	for i := range processes {
		var result = AccidentProcessResponse{
			LbpmApplyProcess: processes[i],
		}
		//事故结案、事故借款、事故付款、事故分支结案
		if processes[i].TemplateFormId == config.TrafficAccidentCloseFormTemplate ||
			processes[i].TemplateFormId == config.TrafficAccidentLendMoneyFormTemplate ||
			processes[i].TemplateFormId == config.TrafficAccidentPaymentMoneyFormTemplate ||
			processes[i].TemplateFormId == config.TrafficAccidentBranchCloseFormTemplate {
			result.FormStep = processes[i].GetAccidentProcessFormStep()
		}

		results = append(results, result)
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": count,
	})
}

func (*Dashboard) DeleteProcess(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ProcessRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	tx := model.DB().Begin()

	for _, formInstanceId := range param.FormInstanceIds {
		var process processModel.LbpmApplyProcess
		err = process.FindBy(formInstanceId)
		if err != nil || process.FormInstanceId == 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}

		var logData = make(map[string]interface{})
		logData["process_data"] = process

		err = process.TransactionDelete(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("process.TransactionDelete error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbDeleteFail)
		}

		if process.TemplateFormId == config.TrafficAccidentLendMoneyFormTemplate ||
			process.TemplateFormId == config.TrafficAccidentDrawbackMoneyFormTemplate ||
			process.TemplateFormId == config.TrafficAccidentPaymentMoneyFormTemplate {

			logData["item_data"] = process.GetRelationItem()

			//删除关联数据
			err = process.TransactionDeleteRelationItem(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("process.TransactionDeleteRelationItem error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbDeleteFail)
			}
		}

		//事故结案
		if process.TemplateFormId == config.TrafficAccidentCloseFormTemplate {
			var accident safetyModel.TrafficAccident
			_ = accident.FindBy(process.ItemId)

			if accident.IsClosed != util.StatusForTrue && accident.ClosedApplyStatus != util.ApplyStatusForDone && process.Status == accident.ClosedApplyStatus {
				err = accident.TransactionUpdateColumn(tx, accident.Id, "ClosedApplyStatus", util.ApplyStatusForNone)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("accident.TransactionUpdateColumn error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbDeleteFail)
				}
			}
		}

		//分支结案
		if process.TemplateFormId == config.TrafficAccidentBranchCloseFormTemplate {
			var branch safetyModel.TrafficAccidentRelaterBranch
			_ = branch.FindBy(process.ItemId)

			if branch.IsClosed != util.StatusForTrue && branch.ClosedApplyStatus != util.ApplyStatusForDone && process.Status == branch.ClosedApplyStatus {
				err = branch.TransactionUpdateColumns(tx, branch.Id, map[string]interface{}{
					"ClosedApplyStatus": util.ApplyStatusForNone,
				})
				if err != nil {
					tx.Rollback()
					log.ErrorFields("branch.TransactionUpdateColumns error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.DbDeleteFail)
				}
			}
		}

		data, _ := json.Marshal(logData)
		var processDeleteLog = processModel.LbpmApplyProcessDeleteLog{
			FormInstanceId:   process.FormInstanceId,
			ItemId:           process.ItemId,
			ProcessId:        process.ProcessId,
			TemplateFormId:   process.TemplateFormId,
			TemplateFormName: process.TemplateFormName,
			Title:            process.Title,
			Data:             data,
		}
		processDeleteLog.ParseOpUser(ctx)

		_ = processDeleteLog.Create()
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

func (*Dashboard) ProcessMessage(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ProcessRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	messages := (&message.Message{}).GetMessagesBy(param.FormInstanceId, (&processModel.LbpmApplyProcess{}).TableName())

	return response.Success(rsp, messages)
}

func (*Dashboard) ProcessMessageDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ProcessRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()
	for _, id := range param.Ids {
		msg := (&message.Message{}).GetDetail(id)
		err = (&message.Message{}).TransactionDelete(tx, id)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("Message.Delete error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbDeleteFail)
		}
		//删除消息对应的审批人列表的数据
		if msg.RelationTableName == (&processModel.LbpmApplyProcess{}).TableName() {
			//读类型对应抄送消息
			var nodeType int64
			var isHandle int64
			if msg.ReadType == message.MESSAGE_READ_1 {
				nodeType = util.ProcessNodeTypeForNotice
			}
			if msg.ReadType == message.MESSAGE_PROCESS_2 {
				nodeType = util.ProcessNodeTypeForApprove
				//待处理
				if msg.ProcessStatus == message.PENDING_1 {
					isHandle = util.StatusForFalse
				}
				//已处理
				if msg.ProcessStatus == message.RESOLVE_2 {
					isHandle = util.StatusForTrue
				}
			}
			err = (&processModel.LbpmApplyProcessHasHandler{}).TransactionDeleteByUser(tx, msg.RelationId, msg.RecvUserId, nodeType, isHandle)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("LbpmApplyProcessHasHandler.TransactionDeleteByUser error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbDeleteFail)
			}
		}
	}

	if param.FormInstanceId > 0 {
		var process processModel.LbpmApplyProcess
		_ = process.FindBy(param.FormInstanceId)
		if process.Status == util.ProcessStatusForDoing || process.Status == util.ProcessStatusForRefuse {
			//获取流程当前审批人
			handlers := service.GeProcessCurrentApprovingHandler(param.FormInstanceId)
			var currenHandlerName []string
			for i := range handlers {
				currenHandlerName = append(currenHandlerName, handlers[i].UserName)
			}
			_ = process.TransactionUpdateColumn(tx, "CurrentHandlerUserName", strings.Join(currenHandlerName, ","))
		}

		if process.Status == util.ProcessStatusForDone || process.Status == util.ProcessStatusForTerminate || process.Status == util.ProcessStatusForAbandon {
			if process.CurrentHandlerUserName != "" {
				_ = process.TransactionUpdateColumn(tx, "CurrentHandlerUserName", "")
			}
		}
	}

	tx.Commit()
	return response.Success(rsp, nil)
}

func (*Dashboard) UpdateProcessFormStep(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ProcessRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var process processModel.LbpmApplyProcess
	err = process.FindBy(param.FormInstanceId)

	if err != nil {
		log.ErrorFields("process.FindBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	settings := (&settingModel.ProcessFormStepSetting{}).GetBy(param.TemplateFormId, param.FormStep)
	if len(settings) == 0 {
		return response.Error(rsp, response.SetProcessFormStepFail)
	}

	tx := model.DB().Begin()
	err = process.TransactionUpdates(tx, map[string]interface{}{
		"status":                 param.ApplyStatus,
		"currenthandlerusername": param.CurrentHandler,
	})
	if err != nil {
		tx.Rollback()
		log.ErrorFields("process.TransactionUpdates error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	var updateColumn = make(map[string]interface{})
	if param.FormStep > 0 {
		updateColumn["formstep"] = param.FormStep
	}

	updateColumn[process.ItemTableStatusField] = param.ApplyStatus

	err = tx.Table(process.ItemTableName).Where("Id = ?", process.ItemId).Updates(updateColumn).Error

	if err != nil {
		tx.Rollback()
		log.ErrorFields("table.updates error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	tx.Commit()

	return response.Success(rsp, nil)
}
