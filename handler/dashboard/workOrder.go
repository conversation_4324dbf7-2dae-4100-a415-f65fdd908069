package dashboard

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

type WorkOrderRequest struct {
	Id             int64   `json:"Id"`
	Ids            []int64 `json:"Ids"`
	ProcessTitle   string  `json:"ProcessTitle"`
	Code           string  `json:"Code"`
	DeviceCode     string  `json:"DeviceCode"`
	ApplyUser      string  `json:"ApplyUser"`
	ApplyStatus    int64   `json:"applyStatus"`
	CurrentHandler string  `json:"CurrentHandler"`
	FormStep       int64   `json:"FormStep"`
	model.Paginator
}

func (*Dashboard) WorkOrderList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkOrderRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	workOrders, count := (&workOrderModel.WorkOrder{}).DashboardList(param.ProcessTitle, param.Code, param.DeviceCode, param.ApplyUser, param.CurrentHandler, param.ApplyStatus, param.FormStep, param.Paginator)
	for i := range workOrders {
		var process processModel.LbpmApplyProcess
		_ = process.GetProcessByItemId(workOrders[i].Id, (&workOrderModel.WorkOrder{}).TableName())
		workOrders[i].FormInstanceId = process.FormInstanceId
		workOrders[i].ProcessId = process.ProcessId
		workOrders[i].ProcessTitle = process.Title
		workOrders[i].CurrentHandler = process.CurrentHandlerUserName
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      workOrders,
		"TotalCount": count,
	})
}

func (*Dashboard) WorkOrderDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkOrderRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	tx := model.DB().Begin()

	if len(param.Ids) > 0 {
		err = service.DeleteWorkOrderAndRelation(tx, param.Ids)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("service.DeleteWorkOrderAndRelation error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

type PetitionWorkOrderListParam struct {
	ProcessTitle   string `json:"ProcessTitle"`
	Code           string `json:"Code"`
	UserName       string `json:"UserName"`
	ToUserName     string `json:"ToUserName"`
	ApplyStatus    int64  `json:"applyStatus"`
	CurrentHandler string `json:"CurrentHandler"`
	FormStep       int64  `json:"FormStep"`
	model.Paginator
}

func (*Dashboard) PetitionWorkOrderList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param PetitionWorkOrderListParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	workOrders, count := (&workOrderModel.PetitionWorkOrder{}).DashboardGetBy(param.ProcessTitle, param.Code, param.UserName, param.ToUserName, param.CurrentHandler, param.ApplyStatus, param.FormStep, param.Paginator)
	for i := range workOrders {
		var process processModel.LbpmApplyProcess
		_ = process.GetProcessByItemId(workOrders[i].Id, (&workOrderModel.PetitionWorkOrder{}).TableName())
		workOrders[i].FormInstanceId = process.FormInstanceId
		workOrders[i].ProcessId = process.ProcessId
		workOrders[i].ProcessTitle = process.Title
		workOrders[i].CurrentHandler = process.CurrentHandlerUserName
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      workOrders,
		"TotalCount": count,
	})
}

func (*Dashboard) UpdatePetitionWorkOrderForm(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param workOrderModel.PetitionWorkOrder
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	workOrder := (&workOrderModel.PetitionWorkOrder{}).FirstBy(param.Id)
	if workOrder.Id == 0 {
		log.ErrorFields("PetitionWorkOrder FirstBy fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	err = param.DashboardUpdate()

	return response.Success(rsp, nil)
}

func (*Dashboard) PetitionWorkOrderDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param WorkOrderRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	tx := model.DB().Begin()

	if len(param.Ids) > 0 {
		err = service.DeletePetitionWorkOrderAndRelation(tx, param.Ids)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("service.DeletePetitionWorkOrderAndRelation error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}
