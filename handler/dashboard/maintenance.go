package dashboard

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	maintenanceModel "app/org/scs/erpv2/api/model/maintenance"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

// 司机调动流程
type VehicleMigrationParam struct {
	Ids            []int64 `json:"Ids"`
	License        string  `json:"License"`
	ProcessTitle   string  `json:"ProcessTitle"`
	Code           string  `json:"Code"`
	UserName       string  `json:"UserName"`
	ApplyStatus    int64   `json:"applyStatus"`
	CurrentHandler string  `json:"CurrentHandler"`
	FormStep       int64   `json:"FormStep"`
	model.Paginator
}

func (*Dashboard) VehicleMigrationList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleMigrationParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	migrations, count := (&maintenanceModel.VehicleMigration{}).DashboardGetBy(param.License, param.ProcessTitle, param.Code, param.UserName, param.CurrentHandler, param.ApplyStatus, param.FormStep, param.Paginator)
	for i := range migrations {
		var process processModel.LbpmApplyProcess
		_ = process.GetProcessByItemId(migrations[i].Id, (&maintenanceModel.VehicleMigration{}).TableName())
		migrations[i].FormInstanceId = process.FormInstanceId
		migrations[i].ProcessId = process.ProcessId
		migrations[i].ProcessTitle = process.Title
		migrations[i].CurrentHandler = process.CurrentHandlerUserName
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      migrations,
		"TotalCount": count,
	})
}

func (*Dashboard) VehicleMigrationDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleMigrationParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	tx := model.DB().Begin()

	if len(param.Ids) > 0 {
		err = service.DeleteVehicleMigrationAndRelation(tx, param.Ids)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("service.DeleteVehicleMigrationAndRelation error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}
