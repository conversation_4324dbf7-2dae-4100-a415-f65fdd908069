package dashboard

import (
	"app/org/scs/erpv2/api/log"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

type ProcessFormStepSetting struct {
	Items          []settingModel.ProcessFormStepSetting `json:"Items"`
	TemplateFormId string                                `json:"TemplateFormId"`
}

func (*Dashboard) CreateFormStep(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ProcessFormStepSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	for i := range param.Items {
		if err := util.Validator().Struct(param.Items[i]); err != nil {
			log.ErrorFields("validate fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsMissing)
		}
		param.Items[i].TemplateFormId = param.TemplateFormId
	}

	err = (&settingModel.ProcessFormStepSetting{}).Delete(param.TemplateFormId)

	if err != nil {
		log.ErrorFields("ProcessFormStepSetting Delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	if len(param.Items) > 0 {
		err = (&settingModel.ProcessFormStepSetting{}).Create(param.Items)
		if err != nil {
			log.ErrorFields("ProcessFormStepSetting Create error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	return response.Success(rsp, nil)
}

func (*Dashboard) ListFormStep(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ProcessFormStepSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	settings := (&settingModel.ProcessFormStepSetting{}).GetBy(param.TemplateFormId, 0)

	return response.Success(rsp, settings)
}
