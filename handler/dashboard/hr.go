package dashboard

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

// 司机调动流程
type DriverMigrationParam struct {
	Ids            []int64 `json:"Ids"`
	ProcessTitle   string  `json:"ProcessTitle"`
	Code           string  `json:"Code"`
	UserName       string  `json:"UserName"`
	ApplyStatus    int64   `json:"applyStatus"`
	CurrentHandler string  `json:"CurrentHandler"`
	FormStep       int64   `json:"FormStep"`
	model.Paginator
}

func (*Dashboard) DriverMigrationList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverMigrationParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	migrations, count := (&hrModel.DriverMigration{}).DashboardGetBy(param.ProcessTitle, param.Code, param.UserName, param.CurrentHandler, param.ApplyStatus, param.FormStep, param.Paginator)
	for i := range migrations {
		var process processModel.LbpmApplyProcess
		_ = process.GetProcessByItemId(migrations[i].Id, (&hrModel.DriverMigration{}).TableName())
		migrations[i].FormInstanceId = process.FormInstanceId
		migrations[i].ProcessId = process.ProcessId
		migrations[i].ProcessTitle = process.Title
		migrations[i].CurrentHandler = process.CurrentHandlerUserName
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      migrations,
		"TotalCount": count,
	})
}

func (*Dashboard) DriverMigrationDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverMigrationParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	tx := model.DB().Begin()

	if len(param.Ids) > 0 {
		err = service.DeleteDriverMigrationAndRelation(tx, param.Ids)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("service.DeleteDriverMigrationAndRelation error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

//func (*Dashboard) UpdateDriverMigrationForm(ctx context.Context, req *api.Request, rsp *api.Response) error {
//	var param hrModel.DriverMigration
//	err := json.Unmarshal([]byte(req.Body), &param)
//	if err != nil {
//		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
//		return response.Error(rsp, response.ParamsInvalid)
//	}
//
//	migration := (&hrModel.DriverMigration{}).FirstBy(param.Id)
//	if migration.Id == 0 {
//		log.ErrorFields("DriverMigration FirstBy fail", map[string]interface{}{"err": err})
//		return response.Error(rsp, response.DbNotFoundRecord)
//	}
//	err = param.TransactionUpdate(model.DB())
//
//	return response.Success(rsp, nil)
//}

// 请假
type StaffLeaveRecordParam struct {
	Ids            []int64 `json:"Ids"`
	ProcessTitle   string  `json:"ProcessTitle"`
	Code           string  `json:"Code"`
	UserName       string  `json:"UserName"`
	ApplyStatus    int64   `json:"applyStatus"`
	CurrentHandler string  `json:"CurrentHandler"`
	FormStep       int64   `json:"FormStep"`
	model.Paginator
}

func (*Dashboard) StaffLeaveRecordList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffLeaveRecordParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	migrations, count := (&hrModel.ApplyLeaveRecord{}).DashboardGetBy(param.ProcessTitle, param.Code, param.UserName, param.CurrentHandler, param.ApplyStatus, param.FormStep, param.Paginator)
	for i := range migrations {
		var process processModel.LbpmApplyProcess
		_ = process.GetProcessByItemId(migrations[i].Id, (&hrModel.ApplyLeaveRecord{}).TableName())
		migrations[i].FormInstanceId = process.FormInstanceId
		migrations[i].ProcessId = process.ProcessId
		migrations[i].ProcessTitle = process.Title
		migrations[i].CurrentHandler = process.CurrentHandlerUserName
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      migrations,
		"TotalCount": count,
	})
}

func (*Dashboard) StaffLeaveRecordDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StaffLeaveRecordParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	tx := model.DB().Begin()

	if len(param.Ids) > 0 {
		err = service.DeleteStaffLeaveRecordAndRelation(tx, param.Ids)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("service.DeleteStaffLeaveRecordAndRelation error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

// 转正
type BecomeWorkerRecordParam struct {
	Ids            []int64 `json:"Ids"`
	ProcessTitle   string  `json:"ProcessTitle"`
	UserName       string  `json:"UserName"`
	ApplyStatus    int64   `json:"applyStatus"`
	CurrentHandler string  `json:"CurrentHandler"`
	FormStep       int64   `json:"FormStep"`
	model.Paginator
}

func (*Dashboard) BecomeWorkerRecordList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param BecomeWorkerRecordParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	records, count := (&hrModel.DriverBecomeWorkerRecord{}).DashboardGetBy(param.ProcessTitle, param.UserName, param.CurrentHandler, param.ApplyStatus, param.FormStep, param.Paginator)
	for i := range records {
		var process processModel.LbpmApplyProcess
		_ = process.GetProcessByItemId(records[i].Id, (&hrModel.DriverBecomeWorkerRecord{}).TableName())
		records[i].FormInstanceId = process.FormInstanceId
		records[i].ProcessId = process.ProcessId
		records[i].ProcessTitle = process.Title
		records[i].CurrentHandler = process.CurrentHandlerUserName
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      records,
		"TotalCount": count,
	})
}

func (*Dashboard) BecomeWorkerRecordDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param BecomeWorkerRecordParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	tx := model.DB().Begin()

	if len(param.Ids) > 0 {
		err = service.DeleteBecomeWorkerRecordAndRelation(tx, param.Ids)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("service.DeleteBecomeWorkerRecordAndRelation error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}
