package maintenance

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	maintenanceModel "app/org/scs/erpv2/api/model/maintenance"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
)

type MaintenanceHandler struct {
}

type MaterialParam struct {
	maintenanceModel.MaintenanceMaterial
	Ids      []int64 `json:"Ids"`
	FileData string  `json:"FileData"`
	model.Paginator
}

func (h *MaintenanceHandler) MaterialImport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param MaterialParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			response.Error(rsp, response.FAIL)
		}
		return
	}()

	authUser := auth.User(ctx).GetUser()

	// 0序号 1车牌* 2保养日期* 3保养类型  4维修厂
	sheet := excelFile.Sheets[0]
	var records []maintenanceModel.MaintenanceMaterial
	topCorporationId := authUser.TopCorporationId
	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 3 {
			continue
		}

		if row.Cells[1].String() == "" {
			continue
		}

		var record maintenanceModel.MaintenanceMaterial
		record.TopCorporationId = topCorporationId
		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 0:
				record.Name = row.Cells[0].String()
			case 1:
				record.Unit = row.Cells[1].String()
			case 2:
				price, _ := row.Cells[2].Float()
				record.Price = int64(price * 100)
			case 3:
				record.TimeLen, _ = row.Cells[3].Float()
			case 4:
				record.MileageLen, _ = row.Cells[4].Float()
			}
		}

		record.OpUserName = authUser.Name
		record.OpUserId = authUser.Id

		records = append(records, record)
	}

	err = (&maintenanceModel.MaintenanceMaterial{}).CreateMany(records)
	if err != nil {
		log.ErrorFields("CreateMany error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, nil)
}

func (h *MaintenanceHandler) MaterialCreate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param maintenanceModel.MaintenanceMaterial
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Name == "" {
		return response.Error(rsp, response.ParamsMissing)
	}

	authUser := auth.User(ctx).GetUser()
	param.TopCorporationId = authUser.TopCorporationId
	param.OpUserName = authUser.Name
	param.OpUserId = authUser.Id

	err = param.Create()

	if err != nil {
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func (h *MaintenanceHandler) MaterialUpdate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param maintenanceModel.MaintenanceMaterial
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Name == "" {
		return response.Error(rsp, response.ParamsMissing)
	}

	material := (&maintenanceModel.MaintenanceMaterial{}).FirstBy(param.Id)
	if material.Id == 0 {
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	authUser := auth.User(ctx).GetUser()
	param.TopCorporationId = authUser.TopCorporationId
	param.OpUserName = authUser.Name
	param.OpUserId = authUser.Id
	err = param.Update()

	if err != nil {
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

func (h *MaintenanceHandler) MaterialList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param MaterialParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	topCorpId := auth.User(ctx).GetTopCorporationId()
	materials, count := (&maintenanceModel.MaintenanceMaterial{}).GetBy(topCorpId, param.Name, param.Paginator)
	return response.Success(rsp, map[string]interface{}{
		"Items":      materials,
		"TotalCount": count,
	})
}

func (h *MaintenanceHandler) MaterialDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param MaterialParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(param.Ids) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	err = (&maintenanceModel.MaintenanceMaterial{}).Delete(param.Ids)

	if err != nil {
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, nil)
}
