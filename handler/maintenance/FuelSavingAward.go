package maintenance

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/model/maintenance"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"sync"
)

type FuelSavingAwardHandler struct {
	CorporationIds []int64 `json:"CorporationIds"` // 机构id
	Month          string  `json:"Month"`          //月份
	StaffId        int64   `json:"StaffId"`        // 驾驶员
	JobNumber      string  `json:"JobNumber"`      // 工号
	LineId         int64   `json:"LineId"`         // 线路id
	FileData       string  `json:"FileData"`       // 导入时的base64文件
	model.Paginator
}

func (sa *FuelSavingAwardHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form FuelSavingAwardHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	corporationIds := service.AuthCorporationIdProvider(ctx, form.CorporationIds)
	data, totalCount, err := (&maintenance.FuelSavingAwardReport{}).List(corporationIds, form.Month, form.StaffId, form.JobNumber, form.LineId, form.Paginator)
	if err != nil {
		log.ErrorFields("SafeProductionReport List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if data != nil {
		for index := range data {
			data[index].CorporationId, data[index].CorporationName = data[index].GetCorporation()
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": data, "TotalCount": totalCount})
}

type ExcelForm struct {
	RowIndex int `json:"RowIndex"`
	//Info  *xlsx.Row `json:"Info"`
	Error string `json:"Error"`
}

func (sa *FuelSavingAwardHandler) Import(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form FuelSavingAwardHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.FileData == "" {
		return response.Error(rsp, response.ParamsInvalid)
	}
	decodeString, err := base64.StdEncoding.DecodeString(form.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.ParamsInvalid)
	}
	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("excelize.OpenBinary[err]:", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if len(excelFile.Sheets) == 0 {
		log.Error("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}
	user := auth.User(ctx).GetUser()
	rows := excelFile.Sheets[0].Rows
	var errRows []ExcelForm
	if rows != nil {
		wg := sync.WaitGroup{}
		var lock sync.Mutex
		for index, r := range rows {
			if index == 0 {
				continue
			}
			wg.Add(1)
			go func(row *xlsx.Row, i int) {
				defer wg.Done()
				isError := false
				month := row.Cells[5].String() // 月份
				// 判断当前时间能否更新这个月的数据
				if !(&maintenance.FuelSavingAwardReport{}).IsModified(month) {
					lock.Lock()
					errRows = append(errRows, ExcelForm{RowIndex: i, Error: "此月份数据无法修改"})
					lock.Unlock()
					//return
					isError = true
				}
				lineName := row.Cells[1].String() // 线路名称
				lineItem := rpc.GetLineWithName(ctx, user.TopCorporationId, lineName)
				if lineItem == nil {
					log.ErrorFields("lineItem is nil", map[string]interface{}{"error": lineName})
					lock.Lock()
					errRows = append(errRows, ExcelForm{RowIndex: i, Error: "未找到该路线"})
					lock.Unlock()
					isError = true
				}
				idCard := row.Cells[4].String() // 身份证
				staff := rpc.GetStaffWithIdentifyId(ctx, user.TopCorporationId, idCard)
				if staff == nil {
					log.ErrorFields("staff is nil", map[string]interface{}{"error": idCard})
					lock.Lock()
					errRows = append(errRows, ExcelForm{RowIndex: i, Error: "未找到该员工"})
					lock.Unlock()
					isError = true
				}
				if isError {
					return
				}
				var staffArchive hrModel.StaffArchive
				_ = staffArchive.FindByStaffId(staff.Id)
				data, _ := (&maintenance.FuelSavingAwardReport{}).FindOnly(staff.Id, lineItem.Id, month)
				if data.Id == 0 {
					data.StaffId = staff.Id
					data.LineId = lineItem.Id
					data.Month = month
				}
				data.Build(staff.CorporationId)
				data.StaffArchiveId = staffArchive.Id
				data.StaffName = staff.Name
				data.LineName = lineName
				data.IDCard = idCard
				data.License = row.Cells[2].String()          // 车牌
				data.RegistrationDate = row.Cells[6].String() // 上牌日期
				data.CarModel = row.Cells[7].String()         // 车型
				CarLength, _ := row.Cells[8].Int64()          // 车长
				data.CarLength = CarLength * 1000
				data.FuelType = row.Cells[9].String() // 燃料类型
				cash, _ := row.Cells[10].Int64()      // 现金
				data.Cash = cash * 100
				icCard, _ := row.Cells[11].Int64() // ic card
				data.ICCard = icCard * 100
				revenue, _ := row.Cells[12].Int64() // 营收
				data.Revenue = revenue * 100
				Mileage, _ := row.Cells[13].Int64() // 总共里
				data.Mileage = Mileage * 1000
				Fuel, _ := row.Cells[14].Int64() // 单车油料升
				data.Fuel = Fuel * 1000
				PersonMileage, _ := row.Cells[15].Int64() // 个人公里
				data.PersonMileage = PersonMileage * 1000
				FuelConsumption, _ := row.Cells[16].Int64() // 单车油耗
				data.FuelConsumption = FuelConsumption * 1000
				BaseQuota, _ := row.Cells[17].Int64() // 基础定额
				data.BaseQuota = BaseQuota * 100
				RevenueAllowance, _ := row.Cells[18].Int64() // 营收补贴
				data.RevenueAllowance = RevenueAllowance * 100
				RoadAllowance, _ := row.Cells[19].Int64() // 道路补贴
				data.RoadAllowance = RoadAllowance * 100
				VehicleAgeAllowance, _ := row.Cells[20].Int64() // 车龄补贴
				data.VehicleAgeAllowance = VehicleAgeAllowance * 100
				ApprovedAmount, _ := row.Cells[21].Int64() // 核定金额
				data.ApprovedAmount = ApprovedAmount * 100
				AirConditioningAllowance, _ := row.Cells[22].Int64() // 空调补贴
				data.AirConditioningAllowance = AirConditioningAllowance * 100
				SaveFuel, _ := row.Cells[23].Int64() // 节省
				data.SaveFuel = SaveFuel * 1000
				Reward, _ := row.Cells[24].Int64() // 奖金
				data.Reward = Reward * 100
				bt, _ := json.Marshal(&data)
				fmt.Println(string(bt))
				if data.Id == 0 {
					err = data.Create()
					if err != nil {
						log.ErrorFields("FuelSavingAwardReport created error", map[string]interface{}{"error": err.Error()})
						lock.Lock()
						errRows = append(errRows, ExcelForm{RowIndex: i, Error: "新增失败"})
						lock.Unlock()
					}
				} else {
					err = data.Update()
					if err != nil {
						log.ErrorFields("FuelSavingAwardReport update error", map[string]interface{}{"error": err.Error()})
						lock.Lock()
						errRows = append(errRows, ExcelForm{RowIndex: i, Error: "更新失败"})
						lock.Unlock()
					}
				}
			}(r, index+1)
		}
		wg.Wait()
	}
	return response.Success(rsp, errRows)
}
