package maintenance

import (
	maintenanceModel "app/org/scs/erpv2/api/model/maintenance"
	processModel "app/org/scs/erpv2/api/model/process"
	erpProto "app/org/scs/erpv2/api/proto/rpc/erp"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"time"
)

type RpcMaintenance struct {
}

func (rpc *RpcMaintenance) GetErpVehicleMigrationRecord(ctx context.Context, req *erpProto.GetErpVehicleMigrationRecordRequest, rsp *erpProto.GetErpVehicleMigrationRecordResponse) error {
	var startAt = time.Time{}
	var endAt = time.Time{}
	if req.StartUseAt > 0 {
		startAt = time.Unix(req.StartUseAt, 0)
	}
	if req.EndUseAt > 0 {
		endAt = time.Unix(req.EndUseAt, 0)
	}

	records := (&maintenanceModel.VehicleMigration{}).GetByAllForRpc(req.CorporationId, req.VehicleIds, req.ApplyStatuses, startAt, endAt)
	var items []*erpProto.VehicleMigrationRecord
	for i := range records {
		var item = erpProto.VehicleMigrationRecord{
			Id:               records[i].Id,
			Code:             records[i].Code,
			OutCorporationId: records[i].OutCorporationId,
			InCorporationId:  records[i].InCorporationId,
			CreatedAtUnix:    records[i].CreatedAt.ToTime().Unix(),
			UseAtUnix:        records[i].UseAt.ToTime().Unix(),
			ApplyStatus:      records[i].ApplyStatus,
			AcceptUserId:     records[i].AcceptUserId,
		}

		//查询流程
		var process processModel.LbpmApplyProcess
		err := process.GetProcessByItemId(records[i].Id, records[i].TableName())
		if err == nil {
			if process.DoneAt != nil {
				item.HandleAtUnix = process.DoneAt.ToTime().Unix()
			} else {
				item.HandleAtUnix = process.UpdatedAt.ToTime().Unix()
			}
		}

		var vehicles []*erpProto.MigrationVehicleItem
		for j := range records[i].Records {
			vehicles = append(vehicles, &erpProto.MigrationVehicleItem{
				VehicleId: records[i].Records[j].VehicleId,
				InLineId:  records[i].Records[j].InLineId,
			})
		}
		item.Vehicles = vehicles
		items = append(items, &item)

	}
	rsp.Items = items
	rsp.Code = response.SUCCESS
	rsp.Msg = response.MsgMap[rsp.Code].Message
	return nil
}

func (rpc *RpcMaintenance) GetErpVehicleMigrationInfo(ctx context.Context, req *erpProto.GetErpVehicleMigrationInfoRequest, rsp *erpProto.GetErpVehicleMigrationInfoResponse) error {
	record := (&maintenanceModel.VehicleMigration{}).FirstBy(req.Id)
	var item = &erpProto.VehicleMigrationRecord{
		Id:               record.Id,
		Code:             record.Code,
		OutCorporationId: record.OutCorporationId,
		InCorporationId:  record.InCorporationId,
		CreatedAtUnix:    record.CreatedAt.ToTime().Unix(),
		UseAtUnix:        record.UseAt.ToTime().Unix(),
		ApplyStatus:      record.ApplyStatus,
		AcceptUserId:     record.AcceptUserId,
	}

	//查询流程
	var process processModel.LbpmApplyProcess
	err := process.GetProcessByItemId(record.Id, record.TableName())
	if err == nil {
		if process.DoneAt != nil {
			item.HandleAtUnix = process.DoneAt.ToTime().Unix()
		} else {
			item.HandleAtUnix = process.UpdatedAt.ToTime().Unix()
		}
	}

	var vehicles []*erpProto.MigrationVehicleItem
	for j := range record.Records {
		vehicles = append(vehicles, &erpProto.MigrationVehicleItem{
			VehicleId: record.Records[j].VehicleId,
			InLineId:  record.Records[j].InLineId,
		})
	}

	item.Vehicles = vehicles

	rsp.Item = item
	rsp.Code = response.SUCCESS
	rsp.Msg = response.MsgMap[rsp.Code].Message
	return nil
}
