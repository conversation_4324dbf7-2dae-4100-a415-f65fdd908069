package maintenance

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	safetyModel "app/org/scs/erpv2/api/model/maintenance"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	go_api "github.com/micro/go-micro/v2/api/proto"
	"strconv"
	"time"
)

type GlassRepair struct {
	ProcessId string // 蓝凌返回的流程id
	LbpmParam string // 前端嵌入页面获取数据
	safetyModel.GlassRepair
	IdStr string // 玻璃维修id 兼容微信
	//CorporationId              int64  // 提交人所属机构
	CorporationIdStr           string  // 提交人所属机构 兼容微信
	RepairShopCorporationIdStr string  // 维修厂机构id 兼容wx小程序大整型
	CorporationIds             []int64 // 提交人所属机构
	model.Paginator
	StartAt, EndAt model.LocalTime
}

func (gr *GlassRepair) AddDraft(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q GlassRepair
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	q.GlassRepair.SubmitUserId = auth.User(ctx).GetUserId()
	q.GlassRepair.SubmitUserName = auth.User(ctx).GetUser().Name

	detail := rpc.GetCorporationDetailById(ctx, auth.User(ctx).GetCorporationId())
	if detail == nil {
		log.ErrorFields("rpc.GetCorporationDetailById detail == nil", map[string]interface{}{"corpId": auth.User(ctx).GetCorporationId()})
		return response.Error(rsp, response.ParamsInvalid)
	}

	q.GlassRepair.GroupId = detail.GroupId
	q.GlassRepair.CompanyId = detail.CompanyId
	q.GlassRepair.BranchId = detail.BranchId
	q.GlassRepair.DepartmentId = detail.DepartmentId
	q.GlassRepair.FleetId = detail.FleetId

	q.GlassRepair.Type = safetyModel.DRAFT_GR_2

	if q.GlassRepair.Id == 0 {
		q.GlassRepair.Id = model.Id()
		err = (&q.GlassRepair).Add()
		if err != nil {
			log.Error("GlassRepair Add err =", err)
			return response.Error(rsp, response.DbSaveFail)
		}
	} else {
		err = (&q.GlassRepair).Update()
		if err != nil {
			log.Error("GlassRepair Add err =", err)
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	return response.Success(rsp, q.GlassRepair.Id)
}

func (gr *GlassRepair) Add(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q GlassRepair
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.IdStr != "" {
		q.GlassRepair.Id, _ = strconv.ParseInt(q.IdStr, 10, 64)
	}

	if q.GlassRepair.LineId == 0 || q.GlassRepair.Line == "" || q.GlassRepair.License == "" ||
		q.GlassRepair.DamageType == 0 || q.GlassRepair.RepairShop == 0 ||
		q.GlassRepair.InWarranty == 0 {
		log.Error("add params missing")
		return response.Error(rsp, response.ParamsMissing)
	}

	// 获取提交人 所属机构信息
	detail := rpc.GetCorporationDetailById(ctx, auth.User(ctx).GetCorporationId())
	if detail == nil {
		log.ErrorFields("rpc.GetCorporationDetailById detail == nil", map[string]interface{}{"corpId": auth.User(ctx).GetCorporationId()})
		return response.Error(rsp, response.ParamsInvalid)
	}

	q.GlassRepair.GroupId = detail.GroupId
	q.GlassRepair.CompanyId = detail.CompanyId
	q.GlassRepair.BranchId = detail.BranchId
	q.GlassRepair.DepartmentId = detail.DepartmentId
	q.GlassRepair.FleetId = detail.FleetId
	q.GlassRepair.Type = safetyModel.GLASS_REPAIR_GR_1

	user := auth.User(ctx).GetUser()
	q.GlassRepair.SubmitUserName = user.Name
	q.GlassRepair.SubmitUserId = user.Id
	localNow := model.LocalTime(time.Now())
	q.GlassRepair.SubmitAt = &localNow

	tx := model.DB().Begin()

	if q.GlassRepair.Id == 0 {
		q.GlassRepair.Id = model.Id()
		err = (&q.GlassRepair).AddTx(tx)
		if err != nil {
			tx.Rollback()
			log.Error("GlassRepair Add err =", err)
			return response.Error(rsp, response.DbSaveFail)
		}
	} else {
		err = (&q.GlassRepair).UpdateTx(tx)
		if err != nil {
			tx.Rollback()
			log.Error("GlassRepair UpdateTx err =", err)
			return response.Error(rsp, response.DbUpdateFail)
		}
	}
	// 发流程
	if config.Config.Lbpm.Enable {
		param, err := json.Marshal(q.GlassRepair)
		if err != nil {
			tx.Rollback()
			log.Error("Marshal err =", err)
			return response.Error(rsp, response.FAIL)
		}

		formData := map[string]interface{}{
			"DamageType":        q.GlassRepair.DamageType,
			"FixOffice":         util.AccidentFixOfficeMap[q.GlassRepair.RepairShop],
			"InWarranty":        q.GlassRepair.InWarranty,
			"IsDirectIndemnity": 0,
		}
		processTitle := fmt.Sprintf("%s提交的审批", user.Name)
		_, err = processService.NewDispatchProcess(user, config.GlassRepairFormTemplate, processTitle, q.GlassRepair.Id, (&q.GlassRepair).TableName(), (&q.GlassRepair).ApplyStatusFieldName(), string(param), formData)

		if err != nil {
			tx.Rollback()
			log.Error("DispatchProcess err =", err)
			return response.Error(rsp, response.FAIL)
		}
	}
	tx.Commit()

	return response.Success(rsp, nil)
}

type GlassRepairListRsp struct {
	safetyModel.GlassRepair
	CorporationId    int64
	Corporation      string
	CorporationIdStr string // bigint is compatible with wx
	IdStr            string // bigint is compatible with wx
}

func (gr *GlassRepair) List(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q GlassRepair
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	data, err := glassRepairList(ctx, q)
	if err != nil {
		return response.Error(rsp, response.DbQueryFail)
	}

	return response.Success(rsp, data)
}

func (gr *GlassRepair) Export(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q GlassRepair
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	data, err := glassRepairList(ctx, q)
	if err != nil {
		return response.Error(rsp, response.DbQueryFail)
	}

	return response.Success(rsp, data)
}

func glassRepairList(ctx context.Context, q GlassRepair) (map[string]interface{}, error) {

	q.CorporationIds = service.AuthCorporationIdProvider(ctx, q.CorporationIds)

	list, totalCount, err := (&q.GlassRepair).List(auth.User(ctx).GetUserId(), q.CorporationIds, q.GlassRepair.LineId, q.GlassRepair.License, q.GlassRepair.DamageType, q.GlassRepair.ApplyStatus, time.Time(q.StartAt), time.Time(q.EndAt), q.Paginator)
	if err != nil {
		log.Error("List err =", err)
		return map[string]interface{}{}, err
	}

	var rspD []GlassRepairListRsp

	for i, _ := range list {
		item := GlassRepairListRsp{
			GlassRepair:   list[i],
			CorporationId: 0,
			Corporation:   "",
		}
		item.CorporationId, item.Corporation = list[i].Corporations.GetCorporation()

		item.CorporationIdStr = strconv.FormatInt(item.CorporationId, 10)
		item.IdStr = strconv.FormatInt(list[i].Id, 10)

		rspD = append(rspD, item)

		list[i].ParseCorporation()
	}

	return map[string]interface{}{
		"Items":      rspD,
		"TotalCount": totalCount,
	}, nil
}

type GetDetailRsp struct {
	safetyModel.GlassRepair

	IsProcessHandler bool   // 流程相关人
	CorporationId    int64  // 提交人所属机构
	Corporation      string // 提交人所属机构
	CorporationIdStr string // bigint is compatible with wx
	IdStr            string // bigint is compatible with wx
}

func (gr *GlassRepair) GetDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q GlassRepair
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	// bigint is compatible with wx
	//if q.CorporationIdStr != "" {
	//	q.CorporationId, _ = strconv.ParseInt(q.CorporationIdStr, 10, 64)
	//}

	if q.GlassRepair.Id == 0 {
		log.Error("q.GlassRepair.Id == 0")
		return response.Error(rsp, response.ParamsInvalid)
	}

	var glassRepair safetyModel.GlassRepair

	err = (&glassRepair).GetDetail(q.GlassRepair.Id)
	if err != nil {
		log.Error("GetDetail err =", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	var rspD GetDetailRsp
	rspD.GlassRepair = glassRepair
	rspD.IsProcessHandler = processService.CheckIsProcessRelater(rspD.GlassRepair.Id, rspD.GlassRepair.TemplateFormId(), auth.User(ctx).GetUserId())
	rspD.IdStr = strconv.FormatInt(glassRepair.Id, 10)

	staff := rpc.GetStaffWithId(ctx, auth.User(ctx).GetCorporationId())
	if staff != nil {
		corp := rpc.GetCorporationById(ctx, staff.CorporationId)
		if corp != nil {
			rspD.CorporationId = corp.Id
			rspD.Corporation = corp.Name
			rspD.CorporationIdStr = strconv.FormatInt(corp.Id, 10)
		}
	}

	return response.Success(rsp, rspD)
}

// AddForm 添加表单 包括 维修信息第一、第二阶段， 付款信息
func (gr *GlassRepair) AddForm(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q GlassRepair
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	// bigint is compatible with wx
	//if q.CorporationIdStr != "" {
	//	q.CorporationId, _ = strconv.ParseInt(q.CorporationIdStr, 10, 64)
	//}

	if q.GlassRepair.Id == 0 {
		log.Error("q.GlassRepair.Id == 0")
		return response.Error(rsp, response.ParamsInvalid)
	}

	switch q.GlassRepair.FormStatus {
	case safetyModel.REPAIR_GR_2:
		// 填写报修记录

		err = (&q.GlassRepair).EditWithFormStatusRepair()
		if err != nil {
			log.Error("q.EditWithFormStatusRepair err = ", err)
			return response.Error(rsp, response.ParamsInvalid)
		}
	case safetyModel.PAYMENT_GR_3:
		// 填写付款信息
		if q.GlassRepair.IsDirectIndemnity == 1 {
			// 跳过
		} else if q.GlassRepair.IsDirectIndemnity == 2 {
			if q.GlassRepair.PaymentFlow == "" || q.GlassRepair.Payee == "" ||
				q.GlassRepair.PaymentAmount == 0 {
				log.Error("safetyModel.PAYMENT_GR_3 param missing")
				return response.Error(rsp, response.ParamsMissing)
			}
		} else {
			log.Error("safetyModel.PAYMENT_GR_3 param invalid")
			return response.Error(rsp, response.ParamsInvalid)
		}

		err = (&q.GlassRepair).EditWithFormStatusPayment()
		if err != nil {
			log.Error("q.EditWithFormStatusPayment err = ", err)
			return response.Error(rsp, response.ParamsInvalid)
		}

	default:
		log.Error("q.GlassRepair.FormStatus < 2")
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 更新[直接赔付]字段
	if q.GlassRepair.FormStatus == safetyModel.PAYMENT_GR_3 {
		// 获取流程
		var process processModel.LbpmApplyProcess
		err = (&process).GetFormInstanceId((&q.GlassRepair).TemplateFormId(), (&q.GlassRepair).TableName(), q.GlassRepair.Id)
		if err != nil {
			log.Error("GetFormInstanceId err = ", err)
			return response.Error(rsp, response.DbQueryFail)
		}

		err = processService.ResetProcessFormFieldValue(q.GlassRepair.Id, (&q.GlassRepair).TableName(), map[string]interface{}{
			"IsDirectIndemnity": q.GlassRepair.IsDirectIndemnity,
		})
		if err != nil {
			log.Error("ResetProcessFormFieldValue err = ", err)
			return response.Error(rsp, response.DbUpdateFail)
		}
	}

	return response.Success(rsp, nil)
}

func (gr *GlassRepair) Delete(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param GlassRepair
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal err", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.GlassRepair.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	tx := model.DB().Begin()
	//删除玻璃维修
	var glassRepair safetyModel.GlassRepair
	err = glassRepair.GetDetail(param.GlassRepair.Id)
	if err != nil {
		return response.Error(rsp, response.DbDeleteFail)
	}
	err = glassRepair.TransactionDelete(tx)
	if err != nil {
		tx.Rollback()
		return response.Error(rsp, response.DbDeleteFail)
	}
	//删除相关流程
	var process processModel.LbpmApplyProcess
	err = process.GetProcessByItemId(param.GlassRepair.Id, glassRepair.TableName())
	if err != nil && process.FormInstanceId > 0 {
		err = process.TransactionDelete(tx)
		if err != nil {
			tx.Rollback()
			return response.Error(rsp, response.DbDeleteFail)
		}

		err = (&processModel.LbpmApplyProcessHasHandler{}).TransactionDelete(tx, process.FormInstanceId)
		if err != nil {
			tx.Rollback()
			return response.Error(rsp, response.DbDeleteFail)
		}
	}

	tx.Commit()
	return response.Success(rsp, nil)
}
