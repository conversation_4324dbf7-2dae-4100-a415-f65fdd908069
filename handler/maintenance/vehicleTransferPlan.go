package maintenance

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	maintenanceModel "app/org/scs/erpv2/api/model/maintenance"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"gorm.io/gorm"
	"time"
)

type VehicleTransferPlanRequest struct {
	maintenanceModel.VehicleTransferPlan
	DetailRecords []maintenanceModel.VehicleTransferPlanVersionDetail
	model.Paginator
}

func (h *VehicleMigration) TransferPlanList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleTransferPlanRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	plans, count := (&maintenanceModel.VehicleTransferPlan{}).GetBy(auth.User(ctx).GetTopCorporationId(), param.PublishStatus, param.ApplyStatus, param.Title, param.Code, param.Paginator)

	for i := range plans {
		version := (&maintenanceModel.VehicleTransferPlanVersion{}).FirstBy(plans[i].CurrentVersionId)
		plans[i].VersionNo = version.VersionNo
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      plans,
		"TotalCount": count,
	})
}

func (h *VehicleMigration) TransferPlanShow(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleTransferPlanRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	plan := (&maintenanceModel.VehicleTransferPlan{}).FirstBy(param.Id)
	if plan.Id == 0 {
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	version := (&maintenanceModel.VehicleTransferPlanVersion{}).FirstBy(plan.CurrentVersionId)
	plan.VersionNo = version.VersionNo

	plan.LatestVersionDetails = (&maintenanceModel.VehicleTransferPlanVersionDetail{}).GetBy(version.Id)

	return response.Success(rsp, plan)
}

func (h *VehicleMigration) TransferPlanCreate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleTransferPlanRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(param.DetailRecords) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	tx := model.DB().Begin()
	errCode := transferPlanCreate(ctx, tx, param)
	if errCode != response.SUCCESS {
		tx.Rollback()
		return response.Error(rsp, errCode)
	}

	tx.Commit()
	return response.Success(rsp, nil)

}

func transferPlanCreate(ctx context.Context, tx *gorm.DB, param VehicleTransferPlanRequest) string {
	authUser := auth.User(ctx).GetUser()
	param.OpUserId = authUser.Id
	param.OpUserName = authUser.Name
	param.TopCorporationId = authUser.TopCorporationId
	if param.VehicleTransferPlan.PublishStatus != util.VehicleTransferPlanPublishStatusForDraft {
		param.VehicleTransferPlan.PublishStatus = util.VehicleTransferPlanPublishStatusForDoing
	}
	//新建方案
	err := param.VehicleTransferPlan.TransactionCreate(tx)
	if err != nil {
		log.ErrorFields("VehicleTransferPlan.TransactionCreate error", map[string]interface{}{"err": err})
		return response.DbSaveFail
	}

	var version = maintenanceModel.VehicleTransferPlanVersion{
		VehicleTransferPlanId: param.VehicleTransferPlan.Id,
		IsDraft:               param.VehicleTransferPlan.PublishStatus == util.VehicleTransferPlanPublishStatusForDraft,
	}
	version.OpUser = param.OpUser

	//创建版本
	err = version.TransactionCreate(tx)
	if err != nil {
		log.ErrorFields("VehicleTransferPlanVersion.TransactionCreate error", map[string]interface{}{"err": err})
		return response.DbSaveFail
	}

	//更新方案的当前版本
	err = param.VehicleTransferPlan.TransactionUpdateColumns(tx, map[string]interface{}{
		"CurrentVersionId": version.Id,
	})
	if err != nil {
		log.ErrorFields("VehicleTransferPlan.TransactionUpdateColumns error", map[string]interface{}{"err": err})
		return response.DbSaveFail
	}

	for i, _ := range param.DetailRecords {
		param.DetailRecords[i].VehicleTransferPlanId = param.VehicleTransferPlan.Id
		param.DetailRecords[i].VehicleTransferPlanVersionId = version.Id
		inCorporation := rpc.GetCorporationById(ctx, param.DetailRecords[i].InCorporationId)
		if inCorporation != nil {
			param.DetailRecords[i].InCorporationName = inCorporation.Name
		}

		outCorporation := rpc.GetCorporationById(ctx, param.DetailRecords[i].OutCorporationId)
		if outCorporation != nil {
			param.DetailRecords[i].OutCorporationName = outCorporation.Name
		}

		inLine, _ := rpc.GetLineWithId(ctx, param.DetailRecords[i].InLineId)
		if inLine != nil {
			param.DetailRecords[i].InLineName = inLine.Name
		}

		outLine, _ := rpc.GetLineWithId(ctx, param.DetailRecords[i].OutLineId)
		if outLine != nil {
			param.DetailRecords[i].OutLineName = outLine.Name
		}

		param.DetailRecords[i].OpUser = param.OpUser
	}

	//创建版本具体的操作记录
	err = (&maintenanceModel.VehicleTransferPlanVersionDetail{}).TransactionCreate(tx, param.DetailRecords)
	if err != nil {
		log.ErrorFields("VehicleTransferPlanVersionDetail.TransactionCreate error", map[string]interface{}{"err": err})
		return response.DbSaveFail
	}

	return response.SUCCESS
}

func (h *VehicleMigration) TransferPlanEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleTransferPlanRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	plan := (&maintenanceModel.VehicleTransferPlan{}).FirstBy(param.Id)
	if plan.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	authUser := auth.User(ctx).GetUser()
	plan.Title = param.Title
	plan.PlanClassIdxVehicleCount = param.PlanClassIdxVehicleCount
	plan.OpUserId = authUser.Id
	plan.OpUserName = authUser.Name
	plan.UpdatedAt = model.LocalTime(time.Now())

	if len(param.DetailRecords) == 0 {
		err = plan.TransactionUpdate(model.DB())

		if err != nil {
			return response.Error(rsp, response.FAIL)
		}

		return response.Success(rsp, nil)
	}

	if plan.PublishStatus != util.VehicleTransferPlanPublishStatusForDraft &&
		(plan.ApplyStatus == util.ApplyStatusForDone || plan.ApplyStatus == util.ApplyStatusForDoing) {
		return response.Error(rsp, response.FAIL)
	}

	tx := model.DB().Begin()
	if plan.PublishStatus == util.VehicleTransferPlanPublishStatusForDraft {
		err = plan.TransactionDelete(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("VehicleTransferPlan.TransactionDelete error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
		errCode := transferPlanCreate(ctx, tx, param)
		if errCode != response.SUCCESS {
			tx.Rollback()
			return response.Error(rsp, errCode)
		}

		tx.Commit()
		return response.Success(rsp, nil)
	}

	var version = maintenanceModel.VehicleTransferPlanVersion{
		VehicleTransferPlanId: plan.Id,
	}
	version.OpUser = plan.OpUser

	//创建版本
	err = version.TransactionCreate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("VehicleTransferPlanVersion.TransactionCreate error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	//更新方案的当前版本和信息
	plan.CurrentVersionId = version.Id
	err = plan.TransactionUpdate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("VehicleTransferPlan.TransactionUpdate error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	for i, _ := range param.DetailRecords {
		param.DetailRecords[i].VehicleTransferPlanId = plan.Id
		param.DetailRecords[i].VehicleTransferPlanVersionId = version.Id
		inCorporation := rpc.GetCorporationById(ctx, param.DetailRecords[i].InCorporationId)
		if inCorporation != nil {
			param.DetailRecords[i].InCorporationName = inCorporation.Name
		}

		outCorporation := rpc.GetCorporationById(ctx, param.DetailRecords[i].OutCorporationId)
		if outCorporation != nil {
			param.DetailRecords[i].OutCorporationName = outCorporation.Name
		}

		inLine, _ := rpc.GetLineWithId(ctx, param.DetailRecords[i].InLineId)
		if inLine != nil {
			param.DetailRecords[i].InLineName = inLine.Name
		}

		outLine, _ := rpc.GetLineWithId(ctx, param.DetailRecords[i].OutLineId)
		if outLine != nil {
			param.DetailRecords[i].OutLineName = outLine.Name
		}

		param.DetailRecords[i].OpUser = param.OpUser
	}

	//创建版本具体的操作记录
	err = (&maintenanceModel.VehicleTransferPlanVersionDetail{}).TransactionCreate(tx, param.DetailRecords)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("VehicleTransferPlanVersionDetail.TransactionCreate error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	tx.Commit()
	return response.Success(rsp, nil)

}

func (h *VehicleMigration) TransferPlanDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleTransferPlanRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	plan := (&maintenanceModel.VehicleTransferPlan{}).FirstBy(param.Id)
	if plan.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if plan.ApplyStatus == util.ApplyStatusForDone || plan.ApplyStatus == util.ApplyStatusForDoing {
		return response.Error(rsp, response.FAIL)
	}
	tx := model.DB().Begin()
	err = plan.TransactionDelete(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("VehicleTransferPlan.TransactionDelete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	tx.Commit()
	return response.Success(rsp, nil)

}

func (h *VehicleMigration) TransferPlanVersionList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleTransferPlanRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	plan := (&maintenanceModel.VehicleTransferPlan{}).FirstBy(param.Id)
	if plan.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	versions := (&maintenanceModel.VehicleTransferPlanVersion{}).GetBy(plan.Id)
	for i := range versions {
		versions[i].Details = (&maintenanceModel.VehicleTransferPlanVersionDetail{}).GetBy(versions[i].Id)
	}

	return response.Success(rsp, map[string]interface{}{
		"Items": versions,
	})
}

type vehicleModelColorRequest struct {
	Colors []maintenanceModel.VehicleModelColor `json:"Colors"`
}

func (h *VehicleMigration) VehicleModelColor(ctx context.Context, req *api.Request, rsp *api.Response) error {
	//数据库已经保存的车型颜色
	colors := (&maintenanceModel.VehicleModelColor{}).GetAll(auth.User(ctx).GetTopCorporationId())
	var vehicleModelColorMap = make(map[string]string)

	for i := range colors {
		vehicleModelColorMap[colors[i].ModelCode] = colors[i].Color
	}

	var modelColors []maintenanceModel.VehicleModelColor
	//获取主数据所有车型
	oetVehicleModels := rpc.GetVehicleModelList(ctx, auth.User(ctx).GetTopCorporationId())
	for i := range oetVehicleModels {
		var color = maintenanceModel.VehicleModelColor{
			ModelCode: oetVehicleModels[i].Model,
		}
		if _, ok := vehicleModelColorMap[oetVehicleModels[i].Model]; ok {
			color.Color = vehicleModelColorMap[oetVehicleModels[i].Model]
		}
		modelColors = append(modelColors, color)
	}

	return response.Success(rsp, map[string]interface{}{
		"Items": modelColors,
	})
}

func (h *VehicleMigration) VehicleModelColorEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param vehicleModelColorRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	tx := model.DB().Begin()
	err = (&maintenanceModel.VehicleModelColor{}).Delete(tx, auth.User(ctx).GetTopCorporationId())
	if err != nil {
		tx.Rollback()
		log.ErrorFields("VehicleModelColor.Delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}
	authUser := auth.User(ctx).GetUser()
	for i := range param.Colors {
		param.Colors[i].OpUserId = authUser.Id
		param.Colors[i].OpUserName = authUser.Name
		param.Colors[i].TopCorporationId = authUser.TopCorporationId
	}

	err = (&maintenanceModel.VehicleModelColor{}).Create(tx, param.Colors)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("VehicleModelColor.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}
	tx.Commit()
	return response.Success(rsp, nil)
}
