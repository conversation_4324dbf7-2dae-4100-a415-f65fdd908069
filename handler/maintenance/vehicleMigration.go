package maintenance

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	maintenanceModel "app/org/scs/erpv2/api/model/maintenance"
	processModel "app/org/scs/erpv2/api/model/process"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	"time"

	api "github.com/micro/go-micro/v2/api/proto"
)

type VehicleMigration struct {
	maintenanceModel.VehicleMigration
	model.Paginator
	ProcessId  string          `json:"ProcessId"`
	IsRestart  bool            `json:"IsRestart"`
	License    string          `json:"License"`
	StartAt    model.LocalTime `json:"StartAt"`
	EndAt      model.LocalTime `json:"EndAt"`
	StartUseAt model.LocalTime `json:"StartUseAt"`
	EndUseAt   model.LocalTime `json:"EndUseAt"`
}

// List 车辆调动
func (h *VehicleMigration) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleMigration
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, []int64{})

	records, totalCount := param.VehicleMigration.GetBy(corporationIds, param.License, param.Code, param.ProcessHandler,
		param.OutCorporationId, param.InCorporationId, param.ApplyStatus, param.StartUseAt.ToTime(), param.EndUseAt.ToTime(),
		time.Time(param.StartAt), time.Time(param.EndAt), param.Paginator)
	for i := range records {
		inCorporation := rpc.GetCorporationById(ctx, records[i].InCorporationId)
		if inCorporation != nil {
			records[i].InCorporationName = inCorporation.Name
		}

		outCorporation := rpc.GetCorporationById(ctx, records[i].OutCorporationId)
		if outCorporation != nil {
			records[i].OutCorporationName = outCorporation.Name
		}

		var process processModel.LbpmApplyProcess

		err := process.GetProcessByItemId(records[i].Id, records[i].TableName())
		if err == nil {
			records[i].ProcessHandler = process.CurrentHandlerUserName
		}
	}

	return response.Success(rsp, map[string]interface{}{"Items": records, "TotalCount": totalCount})

}

// Create 新增车辆调动记录
func (h *VehicleMigration) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	if !config.Config.Lbpm.Enable {
		return response.Error(rsp, response.Forbidden)
	}

	var param VehicleMigration
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	authUser := auth.User(ctx).GetUser()
	param.TopCorporationId = authUser.TopCorporationId
	param.OpUserId = authUser.Id
	param.OpUserName = authUser.Name
	param.VehicleMigration.UserType = auth.User(ctx).UserType

	if err := util.Validator().Struct(param.VehicleMigration); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}
	if len(param.Records) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	//调入和调出机构必须是车队
	outCorporation := rpc.GetCorporationDetailById(ctx, param.OutCorporationId)
	if outCorporation == nil || outCorporation.Item.Type != util.CorporationTypeForFleet {
		return response.Error(rsp, response.CorporationParamNotFleet)
	}
	inCorporation := rpc.GetCorporationDetailById(ctx, param.InCorporationId)
	if inCorporation == nil || inCorporation.Item.Type != util.CorporationTypeForFleet {
		return response.Error(rsp, response.CorporationParamNotFleet)
	}

	acceptUser := rpc.GetUserInfoById(ctx, param.AcceptUserId)
	if acceptUser == nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	for i := range param.Records {
		if err := util.Validator().Struct(param.Records[i]); err != nil {
			log.ErrorFields("records validate fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsMissing)
		}
		vehicle := rpc.GetVehicleWithId(ctx, param.Records[i].VehicleId)
		if vehicle == nil {
			return response.Error(rsp, response.ParamsInvalid)
		}

		//查询车辆是否有正在调动的审批
		if (&maintenanceModel.VehicleMigration{}).IsExistDoingMigration(vehicle.Id) {
			return response.Error(rsp, vehicle.License+"存在正在调动的审批，无法再次调动")
		}

		param.Records[i].OutLineId = vehicle.LineId
		param.Records[i].OutLine = vehicle.Line
	}

	tx := model.DB().Begin()

	param.VehicleMigration.ApplyStatus = util.ApplyStatusForDoing

	if param.VehicleMigration.Id > 0 {
		err = param.VehicleMigration.TransactionUpdate(tx)
	} else {
		err = param.VehicleMigration.TransactionCreate(tx)
	}
	if err != nil {
		log.ErrorFields("VehicleMigration TransactionCreate || Update fail", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbSaveFail)
	}

	//有流程时 发起流程审批
	if config.Config.Lbpm.Enable {
		//获取调出和调入机构
		if outCorporation.GroupId > 0 {
			param.VehicleMigration.OutCorporationInfo = append(param.VehicleMigration.OutCorporationInfo, outCorporation.GroupName)
		}
		if outCorporation.CompanyId > 0 {
			param.VehicleMigration.OutCorporationInfo = append(param.VehicleMigration.OutCorporationInfo, outCorporation.CompanyName)
		}
		if outCorporation.BranchId > 0 {
			param.VehicleMigration.OutCorporationInfo = append(param.VehicleMigration.OutCorporationInfo, outCorporation.BranchName)
		}
		if outCorporation.DepartmentId > 0 {
			param.VehicleMigration.OutCorporationInfo = append(param.VehicleMigration.OutCorporationInfo, outCorporation.DepartmentName)
		}
		if outCorporation.FleetId > 0 {
			param.VehicleMigration.OutCorporationInfo = append(param.VehicleMigration.OutCorporationInfo, outCorporation.FleetName)
		}

		if inCorporation.GroupId > 0 {
			param.VehicleMigration.InCorporationInfo = append(param.VehicleMigration.InCorporationInfo, inCorporation.GroupName)
		}
		if inCorporation.CompanyId > 0 {
			param.VehicleMigration.InCorporationInfo = append(param.VehicleMigration.InCorporationInfo, inCorporation.CompanyName)
		}
		if inCorporation.BranchId > 0 {
			param.VehicleMigration.InCorporationInfo = append(param.VehicleMigration.InCorporationInfo, inCorporation.BranchName)
		}
		if inCorporation.DepartmentId > 0 {
			param.VehicleMigration.InCorporationInfo = append(param.VehicleMigration.InCorporationInfo, inCorporation.DepartmentName)
		}
		if inCorporation.FleetId > 0 {
			param.VehicleMigration.InCorporationInfo = append(param.VehicleMigration.InCorporationInfo, inCorporation.FleetName)
		}

		byteParam, _ := json.Marshal(param.VehicleMigration)

		formData := map[string]interface{}{"AcceptUserMobile": acceptUser.Phone}

		user := auth.User(ctx).GetUser()
		if param.IsRestart && param.ProcessId != "" {
			//重新发起流程
			err = processService.RestartProcess(user, param.ProcessId, string(byteParam), formData)
		} else {
			//发起新的流程
			processTitle := fmt.Sprintf("%s/%s", param.VehicleMigration.Code, param.OpUserName)
			_, err = processService.NewDispatchProcess(user, config.VehicleMigrationApplyFormTemplate, processTitle, param.VehicleMigration.Id, param.VehicleMigration.TableName(), param.VehicleMigration.ApplyStatusFieldName(), string(byteParam), formData)
		}

		if err != nil {
			tx.Rollback()
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	tx.Commit()
	go service.SendVehicleMigrationProcessStatusChangeMsg(param.VehicleMigration.Id)
	return response.Success(rsp, nil)
}

func (h *VehicleMigration) Show(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param maintenanceModel.VehicleMigration
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	vehicleMigration := (&maintenanceModel.VehicleMigration{}).FirstBy(param.Id)
	if vehicleMigration.Id == 0 {
		log.ErrorFields("VehicleMigration.FirstBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	inCorporation := rpc.GetCorporationById(ctx, vehicleMigration.InCorporationId)
	if inCorporation != nil {
		vehicleMigration.InCorporationName = inCorporation.Name
	}

	outCorporation := rpc.GetCorporationById(ctx, vehicleMigration.OutCorporationId)
	if outCorporation != nil {
		vehicleMigration.OutCorporationName = outCorporation.Name
	}

	return response.Success(rsp, vehicleMigration)
}

func (h *VehicleMigration) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param maintenanceModel.VehicleMigration
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	vehicleMigration := (&maintenanceModel.VehicleMigration{}).FirstBy(param.Id)
	if vehicleMigration.Id == 0 {
		log.ErrorFields("VehicleMigration.FirstBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	err = vehicleMigration.Delete()
	if err != nil {
		return response.Error(rsp, response.DbDeleteFail)
	}

	_ = (&maintenanceModel.VehicleMigrationRecord{}).Delete(vehicleMigration.Id)

	return response.Success(rsp, nil)
}

func (h *VehicleMigration) UpdateInfo(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param maintenanceModel.VehicleMigration
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	vehicleMigration := (&maintenanceModel.VehicleMigration{}).FirstBy(param.Id)
	if vehicleMigration.Id == 0 {
		log.ErrorFields("VehicleMigration.FirstBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	//获取车队的机动线路
	var motorLineId int64
	var motorLineName string
	setting := (&settingModel.GlobalSetting{}).GetBy(auth.User(ctx).GetTopCorporationId(), 2)
	if setting.Id != 0 {
		var motorLines []settingModel.GlobalSettingItemForMotorLine
		_ = json.Unmarshal(setting.SettingItem, &motorLines)
		for _, motorLine := range motorLines {
			if motorLine.CorporationId == vehicleMigration.InCorporationId {
				motorLineId = motorLine.LineId
				line, _ := rpc.GetLineWithId(ctx, motorLineId)
				if line != nil {
					motorLineName = line.Name
				}
			}
		}
	}
	//接收人关联车辆和线路
	if vehicleMigration.FormStep == util.ProcessFormStepTwo {
		for _, record := range param.Records {
			record.IsMotor = util.StatusForFalse
			if record.InLineId == 0 {
				record.InLineId = motorLineId
				record.InLine = motorLineName
				record.IsMotor = util.StatusForTrue
			}
			err = record.UpdateInLine()
			if err != nil {
				log.ErrorFields("UpdateInLine error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.FAIL)
			}
		}
		_ = vehicleMigration.UpdateFormStep(vehicleMigration.Id, vehicleMigration.FormStep+1)
	}

	return response.Success(rsp, vehicleMigration)
}

func (h *VehicleMigration) UpdateCancelStatus(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param maintenanceModel.VehicleMigration
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	vehicleMigration := (&maintenanceModel.VehicleMigration{}).FirstBy(param.Id)
	if vehicleMigration.Id == 0 {
		log.ErrorFields("VehicleMigration.FirstBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	authUser := auth.User(ctx).GetUser()
	err = vehicleMigration.UpdateCancelStatus(vehicleMigration.Id, map[string]interface{}{
		"IsCancel":         util.StatusForTrue,
		"CancelReason":     param.CancelReason,
		"CancelOpUserId":   authUser.Id,
		"CancelOpUserName": authUser.Name,
		"CancelOpAt":       time.Now(),
	})
	if err != nil {
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}
