package maintenance

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	maintenanceModel "app/org/scs/erpv2/api/model/maintenance"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/shopspring/decimal"
)

type VehicleMaintenance struct {
	Ids             []int64         `json:"Ids"`
	Id              int64           `json:"Id"`
	License         string          `json:"License"`
	StartAt         model.LocalTime `json:"StartAt"`
	EndAt           model.LocalTime `json:"EndAt"`
	MaintenanceType int64           `json:"MaintenanceType"`
	Status          int64           `json:"Status"`
	FileData        string          `json:"FileData"`
	FixOffice       int64           `json:"FixOffice"`
	ItemName        string          `json:"ItemName"`
	Month           int64           `json:"Month"`
	model.Paginator
}

func (h *VehicleMaintenance) CreateFix(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param maintenanceModel.VehicleFixRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	authUser := auth.User(ctx).GetUser()

	param.TopCorporationId = authUser.TopCorporationId
	vehicle := rpc.GetVehicleWithId(ctx, param.VehicleId)
	if vehicle == nil {
		log.ErrorFields("GetVehicleWithId error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.License = vehicle.License
	param.CorporationId = vehicle.SonCorporationId
	param.OpUserId = authUser.Id
	param.OpUserName = authUser.Name
	param.MaterialFee = 0
	param.LaborFee = 0
	for i := range param.Items {
		param.Items[i].LaborFee = 0
		param.Items[i].MaterialFee = 0
		for j := range param.Items[i].Materials {
			material := (&maintenanceModel.MaintenanceMaterial{}).FirstBy(param.Items[i].Materials[j].MaterialId)
			param.Items[i].Materials[j].MaterialName = material.Name
			param.Items[i].Materials[j].MaterialUnit = material.Unit
			param.Items[i].Materials[j].TimeLen = material.TimeLen
			param.Items[i].Materials[j].MileageLen = material.MileageLen
			param.Items[i].Materials[j].VehicleId = param.VehicleId
			param.Items[i].Materials[j].Price = material.Price
			param.Items[i].Materials[j].MaterialFee = decimal.NewFromInt(material.Price).Mul(decimal.NewFromFloat(param.Items[i].Materials[j].Count)).IntPart()
			param.Items[i].LaborFee += param.Items[i].Materials[j].LaborFee
			param.Items[i].MaterialFee += param.Items[i].Materials[j].MaterialFee
		}
		param.MaterialFee += param.Items[i].MaterialFee
		param.LaborFee += param.Items[i].LaborFee
	}

	err = param.Create()
	if err != nil {
		log.ErrorFields("Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (h *VehicleMaintenance) EditFix(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param maintenanceModel.VehicleFixRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	authUser := auth.User(ctx).GetUser()

	param.TopCorporationId = authUser.TopCorporationId
	vehicle := rpc.GetVehicleWithId(ctx, param.VehicleId)
	if vehicle == nil {
		log.ErrorFields("GetVehicleWithId error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.License = vehicle.License
	param.CorporationId = vehicle.SonCorporationId
	param.OpUserId = authUser.Id
	param.OpUserName = authUser.Name
	//param.MaterialFee =0
	//param.LaborFee =0
	for i := range param.Items {
		//param.Items[i].LaborFee =0
		//param.Items[i].MaterialFee =0
		for j := range param.Items[i].Materials {
			material := (&maintenanceModel.MaintenanceMaterial{}).FirstBy(param.Items[i].Materials[j].MaterialId)
			if param.Items[i].Materials[j].MaterialName == "" {
				param.Items[i].Materials[j].MaterialName = material.Name
			}
			if param.Items[i].Materials[j].MaterialUnit == "" {
				param.Items[i].Materials[j].MaterialUnit = material.Unit
			}
			if param.Items[i].Materials[j].Price == 0 {
				param.Items[i].Materials[j].Price = material.Price
			}
			param.Items[i].Materials[j].TimeLen = material.TimeLen
			param.Items[i].Materials[j].MileageLen = material.MileageLen
			param.Items[i].Materials[j].VehicleId = param.VehicleId
			//param.Items[i].Materials[j].MaterialFee = decimal.NewFromInt(material.Price).Mul(decimal.NewFromFloat(param.Items[i].Materials[j].Count)).IntPart()
			//param.Items[i].LaborFee += param.Items[i].Materials[j].LaborFee
			//param.Items[i].MaterialFee += param.Items[i].Materials[j].MaterialFee
		}
		//param.MaterialFee += param.Items[i].MaterialFee
		//param.LaborFee += param.Items[i].LaborFee
	}

	err = param.Update()
	if err != nil {
		log.ErrorFields("Update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (h *VehicleMaintenance) ListFix(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleMaintenance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	authUser := auth.User(ctx).GetUser()

	records, count := (&maintenanceModel.VehicleFixRecord{}).GetBy(authUser.TopCorporationId, param.License, param.ItemName, param.FixOffice, param.StartAt.ToTime(), param.EndAt.ToTime(), param.Paginator)

	for i := range records {
		corporation := rpc.GetCorporationById(ctx, records[i].CorporationId)
		if corporation != nil {
			records[i].CorporationName = corporation.Name
		}
		items := (&maintenanceModel.VehicleFixRecordItem{}).GetBy(records[i].Id)

		for j := range items {
			items[j].Materials = (&maintenanceModel.VehicleFixRecordItemMaterial{}).GetBy(items[j].Id)
		}

		records[i].Items = items
	}
	return response.Success(rsp, map[string]interface{}{"TotalCount": count, "Items": records})
}

func (h *VehicleMaintenance) ListFixExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return h.ListFix(ctx, req, rsp)
}

func (h *VehicleMaintenance) ShowFix(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleMaintenance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	record := (&maintenanceModel.VehicleFixRecord{}).FirstById(param.Id)

	corporation := rpc.GetCorporationById(ctx, record.CorporationId)
	if corporation != nil {
		record.CorporationName = corporation.Name
	}
	items := (&maintenanceModel.VehicleFixRecordItem{}).GetBy(record.Id)

	for j := range items {
		items[j].Materials = (&maintenanceModel.VehicleFixRecordItemMaterial{}).GetBy(items[j].Id)
	}

	record.Items = items
	return response.Success(rsp, record)
}

func (h *VehicleMaintenance) DeleteFix(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleMaintenance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = (&maintenanceModel.VehicleFixRecord{}).Delete(param.Id)
	if err != nil {
		log.ErrorFields("Delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}
