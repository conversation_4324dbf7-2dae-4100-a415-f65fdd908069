package maintenance

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/vehicle"
	protoVehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	uuid "github.com/satori/go.uuid"
	"strconv"
	"strings"
	"sync"
	"time"
)

type VehicleManage struct {
	CorporationId int64           `json:"CorporationId"`
	License       string          `json:"License"`
	VehicleType   string          `json:"VehicleType"`
	UseStatus     int64           `json:"UseStatus"`
	VehicleId     int64           `json:"VehicleId"`
	StartAt       model.LocalTime `json:"StartAt"`
	EndAt         model.LocalTime `json:"EndAt"`
	VehicleIds    string          `json:"VehicleIds"`
	Lines         []int64         `json:"Lines"`
	model.Paginator
}

type VehicleInfoItem struct {
	protoVehicle.OetVehicleItem
	RegisterAt         model.LocalTime                  `json:"RegisterAt"` // 注册日期
	LicenseAt          model.LocalTime                  `json:"LicenseAt"`  // 上牌日期
	PurchaseAt         model.LocalTime                  `json:"PurchaseAt"` //购置日期
	CreatedAt          model.LocalTime                  `json:"CreatedAt"`
	UpdatedAt          model.LocalTime                  `json:"UpdatedAt"`
	SonCorporationName string                           `json:"SonCorporationName"`
	Drivers            []protoVehicle.VehicleDriverItem `json:"Drivers"`
	QrcodePath         string                           `json:"QrcodePath"`
	ProfileInfo        vehicle.VehicleInfo              `json:"ProfileInfo"`
}

func (vm *VehicleManage) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	rspD, totalCount, errCode := VehicleList(ctx, req)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	data := map[string]interface{}{
		"Items":      rspD,
		"TotalCount": totalCount,
	}
	return response.Success(rsp, data)
}

func VehicleList(ctx context.Context, req *api.Request) ([]VehicleInfoItem, int64, string) {
	var param VehicleManage
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return nil, 0, response.ParamsInvalid
	}
	authUser := auth.User(ctx).GetUser()
	if param.CorporationId == 0 {
		param.CorporationId = authUser.CorporationId
	}

	vehicles, count := rpc.GetVehiclesWithOption(ctx, &protoVehicle.GetVehiclesWithOptionRequest{
		CorporationId:   param.CorporationId,
		License:         param.License,
		VehicleType:     param.VehicleType,
		UseStatus:       param.UseStatus,
		Offset:          int64(param.Offset),
		Limit:           int64(param.Limit),
		IsShowChildNode: true,
		UserId:          authUser.Id,
	})

	var results []VehicleInfoItem
	for i := range vehicles {
		var resultItem = VehicleInfoItem{
			OetVehicleItem: *vehicles[i],
			RegisterAt:     model.LocalTime(time.Unix(vehicles[i].RegisterDate, 0).Local()),
			LicenseAt:      model.LocalTime(time.Unix(vehicles[i].LicenseDate, 0).Local()),
			PurchaseAt:     model.LocalTime(time.Unix(vehicles[i].PurchaseDate, 0).Local()),
			CreatedAt:      model.LocalTime(time.Unix(vehicles[i].CreatedAt, 0).Local()),
			UpdatedAt:      model.LocalTime(time.Unix(vehicles[i].UpdatedAt, 0).Local()),
		}
		if vehicles[i].LineId > 0 {
			oetLine, _ := rpc.GetLineWithId(ctx, vehicles[i].LineId)
			if oetLine != nil {
				resultItem.Line = oetLine.Name
			}
		}
		if vehicles[i].SonCorporationId > 0 {
			corporation := rpc.GetCorporationById(ctx, vehicles[i].SonCorporationId)
			if corporation != nil {
				resultItem.SonCorporationName = corporation.Name
			}
		}

		//查询司机
		drivers := rpc.GetVehicleDriverByVehicleId(ctx, vehicles[i].Id)
		if drivers != nil {
			for _, driver := range drivers {
				resultItem.Drivers = append(resultItem.Drivers, *driver)
			}
		}
		// 设置车辆信息
		qrcodePath := setVehicleInfo(vehicles[i])
		resultItem.QrcodePath = fmt.Sprintf("%s%s", config.Config.StaticFileHttpPrefix, qrcodePath)
		results = append(results, resultItem)
	}

	return results, count, ""

}

func setVehicleInfo(oetVehicleItem *protoVehicle.OetVehicleItem) string {
	res := (&vehicle.VehicleInfo{}).FindVehicleInfoByVehicleId(oetVehicleItem.Id)
	if res.QrcodePath == "" {
		path, err := generateVehicleQrcode(oetVehicleItem.Code)
		if err != nil {
			log.ErrorFields("generate vehicle qrcode error", map[string]interface{}{"err": err})
			return ""
		}
		res.VehicleId = oetVehicleItem.Id
		res.License = oetVehicleItem.License
		res.QrcodePath = path

		if res.Id == 0 {
			err = res.Create()
		} else {
			err = res.Update()
		}
		if err != nil {
			log.ErrorFields("vehicleInfo save error", map[string]interface{}{"err": err})
		}
	}
	return res.QrcodePath
}

// 生成车辆二维码 https://ydd.tzgjjt.com:34443/vehicle?VehicleCode=xxxxx
func generateVehicleQrcode(vehicleCode string) (string, error) {
	// 相对路径 webroot/erp/...
	relativePath := fmt.Sprintf(`%s/erp/%v/%s/`, config.Config.WebRoot, "vehicle_qrcodes", time.Now().Format("20060102"))

	// 完整路径 /mnt/www/webroot/erp/...
	fullPath := fmt.Sprintf("%s%s", config.Config.AbsDirPath, relativePath)
	qrcodeLink := fmt.Sprintf("%s?VehicleCode=%s", config.Config.VehicleQrcodeUrl, vehicleCode)
	newFileName := fmt.Sprintf("%s%s", vehicleCode, util.Default_Suffix)

	err := util.SaveQrcode(fullPath, newFileName, qrcodeLink)
	if err != nil {
		log.ErrorFields("二维码生成错误", map[string]interface{}{"err": err})
		return "", err
	}

	return fmt.Sprintf(`%s%s`, relativePath, newFileName), nil
}

func (vm *VehicleManage) ListExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return vm.List(ctx, req, rsp)
}

type VehicleTransferRecordItem struct {
	protoVehicle.OetVehicleRecordItem
	TransferAt model.LocalTime `json:"TransferAt"`
}

func (vm *VehicleManage) VehicleTransferRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleManage
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	records, count := rpc.GetVehicleTransferRecordList(ctx, param.VehicleId, param.StartAt.ToTime(), param.EndAt.ToTime())
	var transferRecords []VehicleTransferRecordItem
	for i := range records {
		var resultItem = VehicleTransferRecordItem{
			OetVehicleRecordItem: *records[i],
			TransferAt:           model.LocalTime(time.Unix(records[i].TransferDateAt, 0).Local()),
		}
		transferRecords = append(transferRecords, resultItem)
	}
	return response.Success(rsp, map[string]interface{}{"TotalCount": count, "Items": transferRecords})
}

func (vm *VehicleManage) ExportQrcode(ctx context.Context, req *api.Request, rsp *api.Response) error {
	list, errCode := VehicleQrcodeList(ctx, req)
	if errCode != "" {
		return response.Error(rsp, "OP7518")
	}
	if list == nil {
		return response.Error(rsp, "OP7518")
	}
	//var filePaths []string
	for i := range list {
		list[i] = fmt.Sprintf("%s%s", config.Config.AbsDirPath, list[i])
	}
	fullFilePath, err := generateVehicleQrcodeZip(list)
	if fullFilePath == "" || err != nil {
		return response.Error(rsp, "OP7517")
	}
	m := make(map[string]string)
	m["FullFilePath"] = fmt.Sprintf("%s%s", config.Config.StaticFileHttpPrefix, fullFilePath)
	m["FilePath"] = fullFilePath
	return response.Success(rsp, m)
}

func VehicleQrcodeList(ctx context.Context, req *api.Request) ([]string, string) {
	var param VehicleManage
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return nil, response.ParamsInvalid
	}
	//param.VehicleIds = "25"
	if param.VehicleIds != "" {
		VehicleIdArr := strings.Split(param.VehicleIds, ",")
		var VehicleIdIntArr []int64
		for _, v := range VehicleIdArr {
			n, err := strconv.Atoi(v)
			if err != nil {
				continue
			}
			VehicleIdIntArr = append(VehicleIdIntArr, int64(n))
		}
		qrcodePaths := (&vehicle.VehicleInfo{}).FindQrcodePathsByVehicleIds(VehicleIdIntArr)
		return qrcodePaths, ""
	}

	authUser := auth.User(ctx).GetUser()
	if param.CorporationId == 0 {
		param.CorporationId = authUser.CorporationId
	}

	vehicles, _ := rpc.GetVehiclesWithOption(ctx, &protoVehicle.GetVehiclesWithOptionRequest{
		CorporationId:   param.CorporationId,
		License:         param.License,
		VehicleType:     param.VehicleType,
		UseStatus:       param.UseStatus,
		Offset:          int64(param.Offset),
		Limit:           int64(param.Limit),
		IsShowChildNode: true,
		UserId:          authUser.Id,
	})

	var qrcodePaths []string
	wg := sync.WaitGroup{}
	var mu sync.Mutex // 互斥锁
	for i := range vehicles {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			vehicleInfo := (&vehicle.VehicleInfo{}).FindVehicleInfoByVehicleId(vehicles[index].Id)
			if vehicleInfo.QrcodePath == "" {
				vehicleInfo.QrcodePath = setVehicleInfo(vehicles[index])
			}

			mu.Lock() // 加锁
			qrcodePaths = append(qrcodePaths, vehicleInfo.QrcodePath)
			mu.Unlock() // 解锁
		}(i)
	}
	wg.Wait()
	return qrcodePaths, ""

}

// 生成车辆二维码ZIP文件
func generateVehicleQrcodeZip(filePaths []string) (string, error) {
	// 相对路径 webroot/erp/...
	relativePath := fmt.Sprintf(`%s/erp/%v/%s/`, config.Config.WebRoot, "vehicle_qrcodes_zip", time.Now().Format("20060102"))

	// 完整路径 /mnt/www/webroot/erp/...
	fullPath := fmt.Sprintf("%s%s", config.Config.AbsDirPath, relativePath)
	//fullPath := "static/"
	newFileName := fmt.Sprintf("%s%s", uuid.NewV4().String(), ".zip")

	err := util.VerifyMkdirExistAndCreate(fullPath)
	if err != nil {
		log.ErrorFields("VerifyMkdirExistAndCreate error ==", map[string]interface{}{"err": err})
		return "", err
	}
	// 文件完成路径
	fullFilePath := fullPath + newFileName
	// 将多个文件写进zip文件中
	util.MultiFileCompression(fullFilePath, filePaths)
	fileRelativePath := relativePath + newFileName
	return fileRelativePath, nil
}

// VehicleProfile 车辆档案
func (vm *VehicleManage) VehicleProfile(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleManage
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	authUser := auth.User(ctx).GetUser()
	if param.CorporationId == 0 {
		param.CorporationId = authUser.CorporationId
	}
	vehicles, count := rpc.GetVehiclesWithOption(ctx, &protoVehicle.GetVehiclesWithOptionRequest{
		CorporationId:   param.CorporationId,
		License:         param.License,
		VehicleType:     param.VehicleType,
		UseStatus:       param.UseStatus,
		Offset:          int64(param.Offset),
		Limit:           int64(param.Limit),
		IsShowChildNode: true,
		UserId:          authUser.Id,
		LineIds:         param.Lines,
	})
	var results []VehicleInfoItem
	for i := range vehicles {
		var resultItem = VehicleInfoItem{
			OetVehicleItem: *vehicles[i],
			RegisterAt:     model.LocalTime(time.Unix(vehicles[i].RegisterDate, 0).Local()),
			LicenseAt:      model.LocalTime(time.Unix(vehicles[i].LicenseDate, 0).Local()),
			PurchaseAt:     model.LocalTime(time.Unix(vehicles[i].PurchaseDate, 0).Local()),
			CreatedAt:      model.LocalTime(time.Unix(vehicles[i].CreatedAt, 0).Local()),
			UpdatedAt:      model.LocalTime(time.Unix(vehicles[i].UpdatedAt, 0).Local()),
		}
		if vehicles[i].LineId > 0 {
			oetLine, _ := rpc.GetLineWithId(ctx, vehicles[i].LineId)
			if oetLine != nil {
				resultItem.Line = oetLine.Name
			}
		}
		if vehicles[i].SonCorporationId > 0 {
			corporation := rpc.GetCorporationById(ctx, vehicles[i].SonCorporationId)
			if corporation != nil {
				resultItem.SonCorporationName = corporation.Name
			}
		}
		// 车辆
		resultItem.ProfileInfo = (&vehicle.VehicleInfo{}).FindVehicleInfoByVehicleId(vehicles[i].Id)
		results = append(results, resultItem)
	}

	data := map[string]interface{}{
		"Items":      results,
		"TotalCount": count,
	}
	return response.Success(rsp, data)
}

func (vm *VehicleManage) VehicleProfileSave(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form vehicle.VehicleInfo
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.VehicleId == 0 || form.License == "" {
		return response.Error(rsp, response.ParamsMissing)
	}
	oldVehicleInfo := (&vehicle.VehicleInfo{}).FindVehicleInfoByVehicleId(form.VehicleId)
	form.Id = oldVehicleInfo.Id
	var err error
	if form.Id == 0 {
		err = form.Create()
	} else {
		err = form.Update()
	}
	if err != nil {
		log.Error("VehicleInfo save error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

func (vm *VehicleManage) VehicleProfileShow(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		VehicleId int64 `json:"VehicleId"  validate:"required"`
	}
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	vehicleItem := rpc.GetVehicleWithId(ctx, form.VehicleId)
	var License, SonCorporationName, Line string
	var UseStatus, Id int64
	if vehicleItem != nil {
		License = vehicleItem.License
		if vehicleItem.SonCorporationId > 0 {
			corporation := rpc.GetCorporationById(ctx, vehicleItem.SonCorporationId)
			if corporation != nil {
				SonCorporationName = corporation.Name
			}
		}
		if vehicleItem.LineId > 0 {
			oetLine, _ := rpc.GetLineWithId(ctx, vehicleItem.LineId)
			if oetLine != nil {
				Line = oetLine.Name
			}
		}
		UseStatus = vehicleItem.UseStatus
		Id = vehicleItem.Id
	}
	VehicleInfo := (&vehicle.VehicleInfo{}).FindVehicleInfoByVehicleId(form.VehicleId)
	return response.Success(rsp, map[string]interface{}{
		"VehicleItem": map[string]interface{}{
			"Id":                 Id,
			"License":            License,
			"SonCorporationName": SonCorporationName,
			"Line":               Line,
			"UseStatus":          UseStatus,
		},
		"ProfileInfo": VehicleInfo,
	})
}
