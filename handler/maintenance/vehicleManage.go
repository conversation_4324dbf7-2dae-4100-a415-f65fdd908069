package maintenance

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	protoVehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type VehicleManage struct {
	CorporationId int64           `json:"CorporationId"`
	License       string          `json:"License"`
	VehicleType   string          `json:"VehicleType"`
	UseStatus     int64           `json:"UseStatus"`
	VehicleId     int64           `json:"VehicleId"`
	StartAt       model.LocalTime `json:"StartAt"`
	EndAt         model.LocalTime `json:"EndAt"`
	model.Paginator
}

type VehicleInfoItem struct {
	protoVehicle.OetVehicleItem
	RegisterAt         model.LocalTime                  `json:"RegisterAt"` // 注册日期
	LicenseAt          model.LocalTime                  `json:"LicenseAt"`  // 上牌日期
	PurchaseAt         model.LocalTime                  `json:"PurchaseAt"` //购置日期
	CreatedAt          model.LocalTime                  `json:"CreatedAt"`
	UpdatedAt          model.LocalTime                  `json:"UpdatedAt"`
	SonCorporationName string                           `json:"SonCorporationName"`
	Drivers            []protoVehicle.VehicleDriverItem `json:"Drivers"`
}

func (vm *VehicleManage) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleManage
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	authUser := auth.User(ctx).GetUser()
	if param.CorporationId == 0 {
		param.CorporationId = authUser.CorporationId
	}

	vehicles, count := rpc.GetVehiclesWithOption(ctx, &protoVehicle.GetVehiclesWithOptionRequest{
		CorporationId:   param.CorporationId,
		License:         param.License,
		VehicleType:     param.VehicleType,
		UseStatus:       param.UseStatus,
		Offset:          int64(param.Offset),
		Limit:           int64(param.Limit),
		IsShowChildNode: true,
		UserId:          authUser.Id,
	})

	var results []VehicleInfoItem
	for i := range vehicles {
		var resultItem = VehicleInfoItem{
			OetVehicleItem: *vehicles[i],
			RegisterAt:     model.LocalTime(time.Unix(vehicles[i].RegisterDate, 0).Local()),
			LicenseAt:      model.LocalTime(time.Unix(vehicles[i].LicenseDate, 0).Local()),
			PurchaseAt:     model.LocalTime(time.Unix(vehicles[i].PurchaseDate, 0).Local()),
			CreatedAt:      model.LocalTime(time.Unix(vehicles[i].CreatedAt, 0).Local()),
			UpdatedAt:      model.LocalTime(time.Unix(vehicles[i].UpdatedAt, 0).Local()),
		}
		if vehicles[i].LineId > 0 {
			oetLine, _ := rpc.GetLineWithId(ctx, vehicles[i].LineId)
			if oetLine != nil {
				resultItem.Line = oetLine.Name
			}
		}
		if vehicles[i].SonCorporationId > 0 {
			corporation := rpc.GetCorporationById(ctx, vehicles[i].SonCorporationId)
			if corporation != nil {
				resultItem.SonCorporationName = corporation.Name
			}
		}

		//查询司机
		drivers := rpc.GetVehicleDriverByVehicleId(ctx, vehicles[i].Id)
		if drivers != nil {
			for _, driver := range drivers {
				resultItem.Drivers = append(resultItem.Drivers, *driver)
			}
		}

		results = append(results, resultItem)
	}

	return response.Success(rsp, map[string]interface{}{"TotalCount": count, "Items": results})

}

func (vm *VehicleManage) ListExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return vm.List(ctx, req, rsp)
}

type VehicleTransferRecordItem struct {
	protoVehicle.OetVehicleRecordItem
	TransferAt model.LocalTime `json:"TransferAt"`
}

func (vm *VehicleManage) VehicleTransferRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleManage
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	records, count := rpc.GetVehicleTransferRecordList(ctx, param.VehicleId, param.StartAt.ToTime(), param.EndAt.ToTime())
	var transferRecords []VehicleTransferRecordItem
	for i := range records {
		var resultItem = VehicleTransferRecordItem{
			OetVehicleRecordItem: *records[i],
			TransferAt:           model.LocalTime(time.Unix(records[i].TransferDateAt, 0).Local()),
		}
		transferRecords = append(transferRecords, resultItem)
	}
	return response.Success(rsp, map[string]interface{}{"TotalCount": count, "Items": transferRecords})
}
