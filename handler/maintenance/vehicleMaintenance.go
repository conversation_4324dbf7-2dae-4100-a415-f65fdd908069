package maintenance

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	maintenanceModel "app/org/scs/erpv2/api/model/maintenance"
	settingModel "app/org/scs/erpv2/api/model/setting"
	protoVehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
)

func (h *VehicleMaintenance) Import(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleMaintenance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			response.Error(rsp, response.FAIL)
		}
		return
	}()

	authUser := auth.User(ctx).GetUser()

	// 0序号 1车牌* 2保养日期* 3保养类型  4维修厂
	sheet := excelFile.Sheets[0]
	var records []maintenanceModel.VehicleMaintenanceRecord
	topCorporationId := authUser.TopCorporationId
	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 3 {
			continue
		}

		if row.Cells[1].String() == "" || row.Cells[2].String() == "" {
			return response.Error(rsp, response.ParamsMissing)
		}

		var record maintenanceModel.VehicleMaintenanceRecord
		record.TopCorporationId = topCorporationId
		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 1:
				record.License = row.Cells[1].String()
				vehicle := rpc.GetVehicleWithLicense(ctx, &protoVehicle.GetVehicleWithLicenseRequest{
					License:       record.License,
					CorporationId: topCorporationId,
				})
				if vehicle != nil {
					record.VehicleId = vehicle.Id
				}
			case 2:
				reportAt, _ := row.Cells[2].GetTime(false)
				record.ReportAt = model.LocalTime(reportAt)
			case 3:
				record.MaintenanceType, _ = row.Cells[3].Int64()
			case 4:
				record.FixOffice, _ = row.Cells[4].Int64()
			}
		}

		record.Status = util.StatusForFalse
		record.OpUserName = authUser.Name
		record.OpUserId = authUser.Id

		records = append(records, record)
	}

	err = (&maintenanceModel.VehicleMaintenanceRecord{}).CreateMany(records)
	if err != nil {
		log.ErrorFields("CreateMany error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, nil)
}

func (h *VehicleMaintenance) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleMaintenance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	authUser := auth.User(ctx).GetUser()

	records, count := (&maintenanceModel.VehicleMaintenanceRecord{}).GetBy(authUser.TopCorporationId, param.License, param.MaintenanceType, param.Status, param.StartAt.ToTime(), param.EndAt.ToTime(), param.Paginator)

	return response.Success(rsp, map[string]interface{}{"TotalCount": count, "Items": records})

}

func (h *VehicleMaintenance) ListExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return h.List(ctx, req, rsp)
}

func (h *VehicleMaintenance) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param maintenanceModel.VehicleMaintenanceRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	authUser := auth.User(ctx).GetUser()
	param.TopCorporationId = authUser.TopCorporationId
	vehicle := rpc.GetVehicleWithId(ctx, param.VehicleId)
	if vehicle == nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.License = vehicle.License
	param.OpUserName = authUser.Name
	param.OpUserId = authUser.Id
	param.Status = util.StatusForFalse

	//获取保养费
	setting := (&settingModel.GlobalSetting{}).GetBy(authUser.TopCorporationId, 3)
	var settingItem []settingModel.GlobalSettingItemForVehicleMaintenance
	err = json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	for _, item := range settingItem {
		if item.TypeId == param.MaintenanceType {
			param.MaintenanceFee = item.Fee
		}
	}

	err = param.Create()
	if err != nil {
		log.ErrorFields("Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (h *VehicleMaintenance) SwitchStatus(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleMaintenance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = (&maintenanceModel.VehicleMaintenanceRecord{}).UpdateStatus(param.Ids)
	if err != nil {
		log.ErrorFields("UpdateStatus error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (h *VehicleMaintenance) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleMaintenance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = (&maintenanceModel.VehicleMaintenanceRecord{}).Delete(param.Ids)
	if err != nil {
		log.ErrorFields("Delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func (h *VehicleMaintenance) VehicleModeList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	//获取主数据所有车型
	oetVehicleModels := rpc.GetVehicleModelList(ctx, auth.User(ctx).GetTopCorporationId())
	return response.Success(rsp, map[string]interface{}{"TotalCount": len(oetVehicleModels), "Items": oetVehicleModels})
}
