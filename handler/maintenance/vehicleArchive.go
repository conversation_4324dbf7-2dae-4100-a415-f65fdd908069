package maintenance

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	maintenanceModel "app/org/scs/erpv2/api/model/maintenance"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/shopspring/decimal"
	"time"
)

type ManualCalcVehicleArchiveParam struct {
	Month int64 `json:"Month"`
}

func (h *MaintenanceHandler) ManualCalcVehicleArchive(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ManualCalcVehicleArchiveParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	go calcVehicleArchive(auth.User(ctx).GetTopCorporationId(), param.Month)
	return response.Success(rsp, nil)
}

func calcVehicleArchive(topCorporationId int64, month int64) {
	//获取所有车辆
	vehicles, _ := rpc.GetVehiclesWithTopCorporationId(context.TODO(), topCorporationId)

	var reports []maintenanceModel.MaintenanceVehicleArchiveReport
	for _, vehicle := range vehicles {
		var report = maintenanceModel.MaintenanceVehicleArchiveReport{
			PkId:             model.PkId{},
			TopCorporationId: topCorporationId,
			VehicleId:        vehicle.Id,
			License:          vehicle.License,
			VehicleModel:     vehicle.VehicleType,
			PowerType:        vehicle.VehicleModal,
			ReportMonth:      month,
		}
		//开始里程
		report.StartMileage = (&maintenanceModel.MaintenanceVehicleArchiveReport{}).GetLastEndMileage(topCorporationId, vehicle.Id, month)

		//宁兴二保费用
		monthTime, _ := time.Parse("200601", fmt.Sprintf("%v", month))
		startAt := time.Date(monthTime.Year(), monthTime.Month(), 1, 0, 0, 0, 0, time.Local)
		endAt := startAt.AddDate(0, 1, 0)
		report.NingxingSecond = (&maintenanceModel.VehicleMaintenanceRecord{}).GetFeeBy(topCorporationId, vehicle.Id, maintenanceModel.SecondMaintenanceType, maintenanceModel.NingxingFixOffice, startAt, endAt)

		//宁兴一保费用
		report.NingxingFirst = (&maintenanceModel.VehicleMaintenanceRecord{}).GetFeeBy(topCorporationId, vehicle.Id, maintenanceModel.FirstMaintenanceType, maintenanceModel.NingxingFixOffice, startAt, endAt)

		//宁兴维修费用
		report.NingxingRepair = (&maintenanceModel.VehicleFixRecord{}).GetFeeBy(topCorporationId, vehicle.Id, maintenanceModel.NingxingFixOffice, startAt, endAt)

		//强华二保费用
		report.QianghuaSecond = (&maintenanceModel.VehicleMaintenanceRecord{}).GetFeeBy(topCorporationId, vehicle.Id, maintenanceModel.SecondMaintenanceType, maintenanceModel.QianghuaFixOffice, startAt, endAt)

		//强华一保费用
		report.QianghuaFirst = (&maintenanceModel.VehicleMaintenanceRecord{}).GetFeeBy(topCorporationId, vehicle.Id, maintenanceModel.FirstMaintenanceType, maintenanceModel.QianghuaFixOffice, startAt, endAt)

		//强华维修费用
		report.QianghuaRepair = (&maintenanceModel.VehicleFixRecord{}).GetFeeBy(topCorporationId, vehicle.Id, maintenanceModel.QianghuaFixOffice, startAt, endAt)

		//查询车辆当月的记录
		oldReport := (&maintenanceModel.MaintenanceVehicleArchiveReport{}).FirstByVehicleIdAndMonth(topCorporationId, vehicle.Id, month)

		report.EndMileage = oldReport.EndMileage
		report.MonthlyEnergy = oldReport.MonthlyEnergy
		report.EnergyCost = oldReport.EnergyCost
		report.OtherRepair = oldReport.OtherRepair

		//年总里程只需要查询当年的里程，即当年1月到当前月的里程
		currentYear := month / 100
		startMonth := currentYear*100 + 1 // January of current year
		report.YearTotalMileage = (&maintenanceModel.MaintenanceVehicleArchiveReport{}).GetYearTotalMileage(topCorporationId, vehicle.Id, startMonth, month)
		//历史总里程
		report.HistoryTotalMileage = (&maintenanceModel.MaintenanceVehicleArchiveReport{}).GetHistoryTotalMileage(topCorporationId, vehicle.Id, month)
		if report.EndMileage > 0 {
			report.YearTotalMileage = report.YearTotalMileage + (report.EndMileage - report.StartMileage)
			report.HistoryTotalMileage = report.HistoryTotalMileage + (report.EndMileage - report.StartMileage)
		}

		//计算百公里能耗费用
		if report.EndMileage-report.StartMileage > 0 {
			report.EnergyPer100km = decimal.NewFromFloat(report.MonthlyEnergy).Div(decimal.NewFromFloat(report.EndMileage - report.StartMileage).Mul(decimal.NewFromFloat(100))).Round(2).InexactFloat64()
		}

		//月修理费合计
		report.MonthlyRepairCost = report.NingxingSecond + report.NingxingFirst + report.NingxingRepair + report.QianghuaSecond + report.QianghuaFirst + report.QianghuaRepair + report.OtherRepair
		//当年截止到目前的累计费用
		report.AnnualTotalCost = (&maintenanceModel.MaintenanceVehicleArchiveReport{}).GetAnnualTotalCost(topCorporationId, vehicle.Id, startMonth, month)
		report.AnnualTotalCost = report.AnnualTotalCost + report.MonthlyRepairCost

		// 调用CreateOrUpdate方法创建或更新记录
		if oldReport.Id > 0 {
			report.Id = oldReport.Id
			err := report.Update()
			if err != nil {
				log.ErrorFields("Update error", map[string]interface{}{"err": err, "license": vehicle.License})
			}
		} else {
			err := report.Create()
			if err != nil {
				log.ErrorFields("Create error", map[string]interface{}{"err": err, "license": vehicle.License})
			}
		}

		reports = append(reports, report)
	}
}

// ManualEditVehicleArchive 手动编辑记录中的月末里程、月能耗、月能耗费用、其他维修费用
func (h *MaintenanceHandler) ManualEditVehicleArchive(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param maintenanceModel.MaintenanceVehicleArchiveReport
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	//获取记录
	report := (&maintenanceModel.MaintenanceVehicleArchiveReport{}).FirstBy(param.Id)
	if report.Id == 0 {
		log.ErrorFields("MaintenanceVehicleArchiveReport.FirstBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	report.EndMileage = param.EndMileage
	report.MonthlyEnergy = param.MonthlyEnergy
	report.EnergyCost = param.EnergyCost
	report.OtherRepair = param.OtherRepair

	//年总里程只需要查询当年的里程，即当年1月到当前月的里程
	currentYear := report.ReportMonth / 100
	startMonth := currentYear*100 + 1 // January of current year
	report.YearTotalMileage = (&maintenanceModel.MaintenanceVehicleArchiveReport{}).GetYearTotalMileage(auth.User(ctx).GetTopCorporationId(), report.VehicleId, startMonth, report.ReportMonth)
	//历史总里程
	report.HistoryTotalMileage = (&maintenanceModel.MaintenanceVehicleArchiveReport{}).GetHistoryTotalMileage(auth.User(ctx).GetTopCorporationId(), report.VehicleId, report.ReportMonth)
	if report.EndMileage > 0 {
		report.YearTotalMileage = report.YearTotalMileage + (report.EndMileage - report.StartMileage)
		report.HistoryTotalMileage = report.HistoryTotalMileage + (report.EndMileage - report.StartMileage)
	}

	//计算百公里能耗费用
	if report.EndMileage-report.StartMileage > 0 {
		report.EnergyPer100km = decimal.NewFromFloat(report.MonthlyEnergy).Div(decimal.NewFromFloat(report.EndMileage - report.StartMileage).Mul(decimal.NewFromFloat(100))).Round(2).InexactFloat64()
	}

	//月修理费合计
	report.MonthlyRepairCost = report.NingxingSecond + report.NingxingFirst + report.NingxingRepair + report.QianghuaSecond + report.QianghuaFirst + report.QianghuaRepair + report.OtherRepair
	//当年截止到目前的累计费用
	report.AnnualTotalCost = (&maintenanceModel.MaintenanceVehicleArchiveReport{}).GetAnnualTotalCost(auth.User(ctx).GetTopCorporationId(), report.VehicleId, startMonth, report.ReportMonth)
	report.AnnualTotalCost = report.AnnualTotalCost + report.MonthlyRepairCost

	err = report.Update()
	if err != nil {
		log.ErrorFields("Update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)
}

type ListVehicleArchiveResponse struct {
	Items      []maintenanceModel.MaintenanceVehicleArchiveReport `json:"Items"`
	TotalCount int64                                              `json:"TotalCount"`
	//柴油能耗和费用
	DieselEnergyCost maintenanceModel.PowerTypeEnergyCost `json:"DieselEnergyCost" gorm:"-"`
	//汽油能耗和费用
	GasolineEnergyCost maintenanceModel.PowerTypeEnergyCost `json:"GasolineEnergyCost" gorm:"-"`
	//电能能耗和费用
	ElectricEnergyCost maintenanceModel.PowerTypeEnergyCost `json:"ElectricEnergyCost" gorm:"-"`
}

// ListVehicleArchive 查询车辆档案列表信息，根据车牌号和月份查询
func (h *MaintenanceHandler) ListVehicleArchive(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleMaintenance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var result ListVehicleArchiveResponse
	reports, count := (&maintenanceModel.MaintenanceVehicleArchiveReport{}).GetBy(auth.User(ctx).GetTopCorporationId(), param.License, param.Month, param.Paginator)
	for i := range reports {
		//计算同车型下，当月车辆的能耗均值
		reports[i].VehicleModelEnergyAverage = (&maintenanceModel.MaintenanceVehicleArchiveReport{}).GetBrandAverage(auth.User(ctx).GetTopCorporationId(), reports[i].VehicleModel, reports[i].ReportMonth)
	}

	result.Items = reports
	result.TotalCount = count

	//根据车辆档案报表中动力类型不同，查询柴油月累计能耗和费用，汽油月累计能耗和费用，电能月累计能耗和费用
	//1:柴油车(传统);2:汽油车(传统);3:混合动力车(新能源);4:纯电动车(新能源);5:CNG(新能源);6:LNG;7:纯电动(双路BMS)
	result.DieselEnergyCost = (&maintenanceModel.MaintenanceVehicleArchiveReport{}).GetEnergyAndCost(auth.User(ctx).GetTopCorporationId(), []int64{1}, param.Month)
	result.GasolineEnergyCost = (&maintenanceModel.MaintenanceVehicleArchiveReport{}).GetEnergyAndCost(auth.User(ctx).GetTopCorporationId(), []int64{2}, param.Month)
	result.ElectricEnergyCost = (&maintenanceModel.MaintenanceVehicleArchiveReport{}).GetEnergyAndCost(auth.User(ctx).GetTopCorporationId(), []int64{4, 5, 7}, param.Month)

	return response.Success(rsp, result)
}
