package common

import (
	"app/org/scs/erpv2/api/log"
	commonModel "app/org/scs/erpv2/api/model/common"
	erpProto "app/org/scs/erpv2/api/proto/rpc/erp"
	"context"
)

type RpcDict struct {
}

func (d *RpcDict) GetDictList(ctx context.Context, req *erpProto.GetDictListRequest, rsp *erpProto.GetDictListResponse) error {
	if req.TopCorporationId == 0 {
		rsp.Code = "OP1001"
		return nil
	}
	var dict = &commonModel.Dict{
		GroupId:  req.TopCorporationId,
		DictType: commonModel.DictType(req.DictKey),
		ParentId: req.ParentId,
	}
	dicts, err := dict.GetWithOptions()
	if err != nil {
		rsp.Code = "OP1004"
		log.Error("GetWithOptions err: ", err)
		return nil
	}

	var items = make([]*erpProto.DictItem, 0)
	for _, dc := range dicts {
		items = append(items, &erpProto.DictItem{
			Id:        dc.Id,
			DictType:  int64(dc.DictType),
			DictKey:   dc.DictKey,
			DictValue: dc.DictValue,
			ParentId:  dc.ParentId,
		})
	}

	rsp.Code = "0"
	rsp.Items = items
	return nil
}
