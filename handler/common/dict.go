package common

import (
	"app/org/scs/erpv2/api/handler/operation/management"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	commonModel "app/org/scs/erpv2/api/model/common"
	operationModel "app/org/scs/erpv2/api/model/operation"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"github.com/micro/go-micro/v2/api/proto"
	"strconv"
)

type Dict struct {
	commonModel.Dict
	ParentIdStr    string // *上一级id， 第一级ParentId=0 兼容wx小程序大整型
	IdStr          string // *字典Id 兼容wx小程序大整型
	LiableStaffIds []int64
}

func (d *Dict) Add(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Dict
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	if q.DictType == 0 || q.DictKey == "" {
		log.Error("q.DictType == 0 || q.DictKey == '' ")
		return response.Error(rsp, response.ParamsInvalid)
	}

	// compatible with bigint
	if q.ParentIdStr != "" {
		q.ParentId, _ = strconv.ParseInt(q.ParentIdStr, 10, 64)
	}

	groupId := auth.User(ctx).GetTopCorporationId()

	q.Dict.Id = model.Id()
	q.Dict.GroupId = groupId
	//q.WorkOrderDict.DictCode = 查

	if q.ParentId == 0 {
		q.Dict.ParentIdPath = strconv.FormatInt(q.Dict.Id, 10)
	}

	q.Dict.IsDeleted = commonModel.NO_0

	switch q.DictType {
	case commonModel.REPAIR_CORPORATION_11, commonModel.REPAIR_CLASS_13, commonModel.REPAIR_CATEGORY_14:
		// 供货方、设备大类、设备种类 code start with 1
		code, err := (q.Dict).GetCodeWithOptions()
		if err != nil {
			log.Error("GetCodeWithOptions err == ", err)
			return response.Error(rsp, response.DbQueryFail)
		}
		if code == "" {
			q.Dict.DictCode = "1"
		} else {
			parseInt, err := strconv.ParseInt(code, 10, 64)
			if err != nil {
				log.Error("ParseInt err == ", err)
				return response.Error(rsp, response.FAIL)
			}

			q.Dict.DictCode = strconv.FormatInt(parseInt+1, 10)

		}
	default:
		// code default equal id
		q.Dict.DictCode = strconv.FormatInt(q.Dict.Id, 10)
	}
	if q.Dict.ChildrenCate != nil {
		for index := range q.Dict.ChildrenCate {
			q.Dict.ChildrenCate[index].DictNumber = q.Dict.DictNumber
		}
	}

	err = (&q.Dict).Add()
	if err != nil {
		log.Error("Add err == ", err)
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

type DictListRsp struct {
	commonModel.Dict

	IdStr       string // compatible with bigint
	ParentIdStr string // compatible with bigint
}

func (d *Dict) List(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Dict
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	groupId := auth.User(ctx).GetTopCorporationId()

	q.Dict.GroupId = groupId

	var rspD []DictListRsp

	dicts, err := (&q.Dict).GetWithOptions()
	if err != nil {
		log.Error("GetWithOptions err =", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	for _, dict := range dicts {
		i := DictListRsp{
			Dict:        dict,
			IdStr:       strconv.FormatInt(dict.Id, 10),
			ParentIdStr: strconv.FormatInt(dict.ParentId, 10),
		}
		rspD = append(rspD, i)
	}
	return response.Success(rsp, rspD)
}

// ListOperation 用于控制运营配置-广告牌类型权限
func (d *Dict) ListOperation(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return d.List(ctx, req, rsp)
}
func (d *Dict) Edit(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Dict
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	// compatible with bigint
	if q.IdStr != "" {
		q.Id, _ = strconv.ParseInt(q.IdStr, 10, 64)
	}
	// compatible with bigint
	if q.ParentIdStr != "" {
		q.ParentId, _ = strconv.ParseInt(q.ParentIdStr, 10, 64)
	}

	if q.Id == 0 || q.DictKey == "" {
		log.Error(" q.Id == 0 || q.DictKey == ''")
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 查询当前字典
	thisDict, err := (&q).Get()
	if err != nil {
		log.Error("Get err =", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	if thisDict.DictType == commonModel.REPAIR_CLASS_13 {
		// 设备大类有关联设备种类后，设备大类不可编辑；设备种类有关联设备后，设备种类不可编辑。

	}
	if q.Dict.ChildrenCate != nil {
		for index := range q.Dict.ChildrenCate {
			q.Dict.ChildrenCate[index].DictNumber = q.Dict.DictNumber
			if q.Dict.ChildrenCate[index].Id == 0 {
				q.Dict.ChildrenCate[index].Id = model.Id()
			}
			q.Dict.ChildrenCate[index].DictCode = strconv.FormatInt(q.Dict.ChildrenCate[index].Id, 10)
		}
	}
	if q.Dict.DictType == commonModel.PETITION_CLASS_2 {
		thisDict.ChildrenCate = q.Dict.ChildrenCate
		thisDict.DictKey = q.Dict.DictKey
		thisDict.DictValue = q.Dict.DictValue
		thisDict.DictNumber = q.Dict.DictNumber
		err = thisDict.EditPetitionClass2()
		if err != nil {
			log.Error("Edit err =", err.Error())
			return response.Error(rsp, response.DbUpdateFail)
		}
	} else {
		err = (&q.Dict).Edit()
		if err != nil {
			log.Error("Edit err =", err.Error())
			return response.Error(rsp, response.DbUpdateFail)
		}
	}

	return response.Success(rsp, nil)
}

func (d *Dict) Delete(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Dict
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	// compatible with bigint
	if q.IdStr != "" {
		q.Id, _ = strconv.ParseInt(q.IdStr, 10, 64)
	}
	if q.Id == 0 {
		log.Error(" q.Id == 0")
		return response.Error(rsp, response.ParamsInvalid)
	}

	var dq = commonModel.Dict{
		PkId: model.PkId{Id: q.Id},
	}
	dict, err := dq.Get()
	if err != nil {
		log.Error("Get err =", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	switch dict.DictType {
	case commonModel.AdvertisingBoardType:
		// 允许删除
		err = (&management.SpecificationSetting{}).DeleteByDictId(tx, q.Id)
		if err != nil {
			log.ErrorFields("SpecificationSetting.DeleteByDictId error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbDeleteFail)
		}

		err = (&management.MaterialSetting{}).DeleteByDictId(tx, q.Id)
		if err != nil {
			log.ErrorFields("MaterialSetting.DeleteByDictId error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbDeleteFail)
		}

		err = (&operationModel.OperationAssociationSettingRecord{}).DeleteByDictId(tx, q.Id)
		if err != nil {
			log.ErrorFields("DeleteByDictId.DeleteByDictId error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbDeleteFail)
		}
	}

	err = (&q.Dict).SoftDelete(tx)
	if err != nil {
		log.Error("SoftDelete err =", err.Error())
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, nil)
}

type TreeRspItem struct {
	commonModel.Dict
	IdStr       string // 字典Id 兼容wx小程序大整型
	ParentIdStr string // *上一级id， 第一级ParentId=0 兼容wx小程序大整型
	Children    []TreeRspItem
}

type TreeRsp struct {
	Data []TreeRspItem
}

func (r *TreeRsp) getChildren(pid int64) []TreeRspItem {
	var rsp []TreeRspItem

	for _, datum := range r.Data {
		if datum.ParentId == pid {
			item := TreeRspItem{
				Dict:        datum.Dict,
				IdStr:       strconv.FormatInt(datum.Dict.Id, 10),
				ParentIdStr: strconv.FormatInt(datum.Dict.ParentId, 10),
				Children:    nil,
			}
			item.Children = r.getChildren(item.Dict.Id)

			rsp = append(rsp, item)
		}
	}
	return rsp
}

func (r *TreeRsp) buildTree() {
	r.Data = r.getChildren(0)
}

func (d *Dict) Tree(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q Dict
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	groupId := auth.User(ctx).GetTopCorporationId()

	var tr TreeRsp
	var rspD []TreeRspItem
	var dict commonModel.Dict
	dict.GroupId = groupId
	dicts, err := (&dict).GetWithOptions()
	if err != nil {
		log.Error("GetWithOptions err =", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	for _, dict := range dicts {
		i := TreeRspItem{
			Dict:     dict,
			Children: nil,
		}
		tr.Data = append(tr.Data, i)
	}

	tr.buildTree()

	// 过滤
	if q.Dict.DictType == 0 {
		rspD = tr.Data
	} else {
		for _, item := range tr.Data {
			if item.DictType == q.Dict.DictType {
				rspD = append(rspD, item)
			}
		}
	}

	return response.Success(rsp, rspD)
}

func (d *Dict) ComplaintTree(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	list, err := (&commonModel.Dict{}).GetComplaintCateList()
	if err != nil {
		log.Error("GetComplaintCateList err =", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, list)
}
