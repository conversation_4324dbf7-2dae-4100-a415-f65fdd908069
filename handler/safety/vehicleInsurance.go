package safety

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/safety"
	protoVehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"archive/zip"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"io"
	"os"
	"path/filepath"
	"strings"
)

type VehicleInsurance struct {
	safety.VehicleInsurance
	model.Paginator
	InsuranceStatus int64   `json:"InsuranceStatus"`
	CorporationIds  []int64 `json:"CorporationIds"`
}

type ImportParam struct {
	FileData    string `json:"FileData"`
	Scene       string `json:"Scene"`
	ZipFilePath string `json:"ZipFilePath"`
}

// Import 车险导导入
func (vi *VehicleInsurance) Import(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ImportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			response.Error(rsp, response.FAIL)
		}
		return
	}()
	// 0车牌号* 1起保日期* 2终保日期* 3保险公司  4险种*  5保单号*
	sheet := excelFile.Sheets[0]
	var vehicleInsurances = make(map[string][]safety.VehicleInsurance)

	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 5 {
			continue
		}

		if row.Cells[0].String() == "" || row.Cells[1].String() == "" || row.Cells[3].String() == "" || row.Cells[4].String() == "" {
			return response.Error(rsp, response.ParamsMissing)
		}

		var insurance safety.VehicleInsurance
		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 0:
				insurance.License = row.Cells[0].String()
			case 1:
				start, err := row.Cells[1].GetTime(false)
				if err == nil {
					insurance.StartDate = model.LocalTime(start)
					insurance.EndDate = model.LocalTime(start.AddDate(1, 0, -1))
				}
			case 2:
				insurance.InsuranceCompany = row.Cells[2].String()
			case 3:
				insurance.InsuranceType = util.InsuranceTypeMap[row.Cells[3].String()]
			case 4:
				insurance.InsuranceCode = row.Cells[4].String()
			}
		}

		if (&safety.VehicleInsurance{}).IsExistWithInsuranceCode(insurance.VehicleId, insurance.InsuranceCode) {
			log.ErrorFields("insurance code repeat", nil)
			return response.Error(rsp, response.DbObjectDuplicate)
		}

		vehicleInsurances[insurance.License] = append(vehicleInsurances[insurance.License], insurance)
	}
	var successCount, failCount, totalCount int64

	for license, insurances := range vehicleInsurances {
		totalCount = totalCount + int64(len(insurances))
		vehicleInfo := rpc.GetVehicleWithLicense(ctx, &protoVehicle.GetVehicleWithLicenseRequest{
			License:       license,
			CorporationId: auth.User(ctx).GetTopCorporationId(),
		})
		if vehicleInfo == nil {
			failCount = failCount + int64(len(insurances))
			continue
		}

		for i := range insurances {
			insurances[i].VehicleId = vehicleInfo.Id
			insurances[i].Corporations.Build(vehicleInfo.SonCorporationId)
		}

		tx := model.DB().Begin()
		if param.Scene == "import" {
			err := (&safety.VehicleInsurance{}).DeleteByVehicleId(tx, vehicleInfo.Id)
			if err != nil {
				tx.Rollback()
				failCount = failCount + int64(len(insurances))
				continue
			}
		}

		err = (&safety.VehicleInsurance{}).TransactionBatchCreate(tx, insurances)
		if err != nil {
			tx.Rollback()
			failCount = failCount + int64(len(insurances))
			continue
		}
		successCount = successCount + int64(len(insurances))
		tx.Commit()
	}

	return response.Success(rsp, map[string]interface{}{
		"FailCount":    failCount,
		"SuccessCount": successCount,
		"TotalCount":   totalCount,
	})
}

// ImportPDF ImportPdf 导入表单的PDF文件
func (vi *VehicleInsurance) ImportPDF(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ImportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.ZipFilePath == "" {
		return response.Error(rsp, response.ParamsMissing)
	}

	param.ZipFilePath = config.Config.AbsDirPath + param.ZipFilePath
	zipReader, err := zip.OpenReader(param.ZipFilePath)
	if err != nil {
		log.ErrorFields("zip.OpenReader error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	defer zipReader.Close()

	relativePath := config.Config.WebRoot + "/erp/vehicle_insurances"
	var pdfFilePath = config.Config.AbsDirPath + relativePath
	err = os.MkdirAll(pdfFilePath, 0755)
	if err != nil {
		log.ErrorFields("os.MkdirAll error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	var failCount int64
	// 读取 ZIP 文件中的内容
	for _, file := range zipReader.File {
		fmt.Println("文件名:", file.Name)
		fmt.Println("文件大小:", file.UncompressedSize64)
		//根据保单号查询保单信息
		code := strings.Split(file.Name, ".")[0]
		insurance := (&safety.VehicleInsurance{}).GetByInsuranceCode(code)
		if insurance.Id == 0 {
			failCount++
			continue
		}

		dstPath := filepath.Join(pdfFilePath, file.Name)
		dstFile, err := os.OpenFile(dstPath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.Mode())
		if err != nil {
			log.ErrorFields("os.OpenFile error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}

		srcFile, err := file.Open()
		if err != nil {
			dstFile.Close()
			log.ErrorFields("file.Open error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}

		_, err = io.Copy(dstFile, srcFile)
		dstFile.Close()
		srcFile.Close()

		if err != nil {
			log.ErrorFields("io.Copy error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}

		//更新保险单地址
		err = insurance.UpdateColumn("InsuranceFilePath", filepath.Join(relativePath, file.Name))
		if err != nil {
			log.ErrorFields("insurance.UpdateColumn error", map[string]interface{}{"err": err, "path": filepath.Join(relativePath, file.Name)})
			return response.Error(rsp, response.FAIL)
		}
	}
	return response.Success(rsp, map[string]interface{}{"FailCount": failCount, "TotalCount": len(zipReader.Reader.File)})
}

// List 车险列表
func (vi *VehicleInsurance) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleInsurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	authUser := auth.User(ctx).GetUser()
	//corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	var scrapVehicleIds []int64
	var scrapVehicleMap = make(map[int64]bool)
	var allVehicleIds []int64

	if param.VehicleUseStatus == util.VehicleStatusForUnknown {
		var oetVehicles []*protoVehicle.OetVehicleItem
		if len(param.CorporationIds) > 0 {
			oetVehicles, _ = rpc.GetVehiclesWithOptionCorpIds(ctx, &protoVehicle.GetVehiclesWithOptionCorpIdsRequest{
				CorporationIds: param.CorporationIds,
				UserId:         authUser.Id,
			})
		} else {
			oetVehicles, _ = rpc.GetVehiclesWithTopCorporationId(ctx, authUser.TopCorporationId)
		}

		for i := range oetVehicles {
			allVehicleIds = append(allVehicleIds, oetVehicles[i].Id)
		}
	} else {
		if len(param.CorporationIds) > 0 {
			oetVehicles, _ := rpc.GetVehiclesWithOptionCorpIds(ctx, &protoVehicle.GetVehiclesWithOptionCorpIdsRequest{
				CorporationIds: param.CorporationIds,
				UserId:         authUser.Id,
			})
			for i := range oetVehicles {
				if oetVehicles[i].UseStatus == util.VehicleStatusForScrap {
					scrapVehicleIds = append(scrapVehicleIds, oetVehicles[i].Id)
					scrapVehicleMap[oetVehicles[i].Id] = true
				}
			}
		} else {
			// 获取权限下所有报废车辆
			scrapVehicleItems, _ := rpc.GetVehiclesWithOption(ctx, &protoVehicle.GetVehiclesWithOptionRequest{
				CorporationId:   auth.User(ctx).GetTopCorporationId(),
				Limit:           99999,
				IsShowChildNode: true,
				UserId:          auth.User(ctx).GetUserId(),
				UseStatus:       2,
			})
			for i := range scrapVehicleItems {
				scrapVehicleIds = append(scrapVehicleIds, scrapVehicleItems[i].Id)
				scrapVehicleMap[scrapVehicleItems[i].Id] = true
			}
		}
	}

	if param.VehicleUseStatus == util.VehicleStatusForScrap && len(scrapVehicleIds) == 0 {
		return response.Success(rsp, map[string]interface{}{"Items": nil, "TotalCount": 0})
	}

	insurances, count := (&safety.VehicleInsurance{}).GetBy(scrapVehicleIds, allVehicleIds, param.License, param.InsuranceCode, param.InsuranceType, param.InsuranceStatus, param.VehicleUseStatus, param.Paginator)
	for i := range insurances {
		oetVehicle := rpc.GetVehicleWithId(ctx, insurances[i].VehicleId)
		//查询车辆是否被删除
		if oetVehicle == nil {
			insurances[i].VehicleUseStatus = util.VehicleStatusForUnknown
			insurances[i].CorporationId, insurances[i].CorporationName = insurances[i].Corporations.GetCorporation()
		} else {
			insurances[i].VehicleUseStatus = util.VehicleStatusForNormal
			corporation := rpc.GetCorporationById(ctx, oetVehicle.SonCorporationId)
			insurances[i].CorporationId = corporation.Id
			insurances[i].CorporationName = corporation.Name
		}

		if insurances[i].InsuranceFilePath != "" {
			insurances[i].InsuranceFileUrl = config.Config.StaticFileHttpPrefix + insurances[i].InsuranceFilePath
		}

		if _, ok := scrapVehicleMap[insurances[i].VehicleId]; ok {
			insurances[i].VehicleUseStatus = util.VehicleStatusForScrap
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": insurances, "TotalCount": count})
}

// Export 车险导出
func (vi *VehicleInsurance) Export(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return vi.List(ctx, req, rsp)
}

// Create 新增车险
func (vi *VehicleInsurance) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleInsurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	vehicleInfo := rpc.GetVehicleWithId(ctx, param.VehicleId)
	if vehicleInfo == nil {
		log.ErrorFields("rpc vehicleInfo not fund", nil)
		return response.Error(rsp, response.ParamsMissing)
	}

	param.VehicleInsurance.Corporations.Build(vehicleInfo.SonCorporationId)

	if (&safety.VehicleInsurance{}).IsExistWithInsuranceCode(param.VehicleId, param.InsuranceCode) {
		log.ErrorFields("insurance code repeat", nil)
		return response.Error(rsp, response.DbObjectDuplicate)
	}

	err = param.VehicleInsurance.Create()
	if err != nil {
		log.ErrorFields("VehicleInsurance.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

// Update 车险更新
func (vi *VehicleInsurance) Update(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleInsurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}
	oldInsurance := (&safety.VehicleInsurance{}).FindBy(param.VehicleInsurance.Id)
	if oldInsurance.Id == 0 {
		log.ErrorFields("insurance not found", nil)
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	insurance := (&safety.VehicleInsurance{}).GetByInsuranceCode(param.InsuranceCode)
	if insurance.Id > 0 && insurance.Id != oldInsurance.Id {
		log.ErrorFields("insurance code repeat", nil)
		return response.Error(rsp, response.ParamsMissing)
	}

	vehicleInfo := rpc.GetVehicleWithId(ctx, param.VehicleId)
	if vehicleInfo == nil {
		log.ErrorFields("rpc vehicleInfo not fund", nil)
		return response.Error(rsp, response.ParamsMissing)
	}

	param.VehicleInsurance.Corporations.Build(vehicleInfo.SonCorporationId)

	err = param.VehicleInsurance.Update()
	if err != nil {
		log.ErrorFields("VehicleInsurance.update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

// Show 车险详情
func (vi *VehicleInsurance) Show(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleInsurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	oldInsurance := (&safety.VehicleInsurance{}).FindBy(param.VehicleInsurance.Id)
	if oldInsurance.Id == 0 {
		log.ErrorFields("insurance not found", nil)
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	//查询车辆的所有保险记录
	insurances := (&safety.VehicleInsurance{}).GetByVehicleId(oldInsurance.VehicleId)
	for i := range insurances {
		if insurances[i].InsuranceFilePath != "" {
			insurances[i].InsuranceFileUrl = config.Config.StaticFileHttpPrefix + insurances[i].InsuranceFilePath
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      insurances,
		"TotalCount": len(insurances),
	})
}

// Delete 删除车险
func (vi *VehicleInsurance) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleInsurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	oldInsurance := (&safety.VehicleInsurance{}).FindBy(param.VehicleInsurance.Id)
	if oldInsurance.Id == 0 {
		log.ErrorFields("insurance not found", nil)
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	err = oldInsurance.Delete(oldInsurance.Id)
	if err != nil {
		log.ErrorFields("insurance delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, nil)
}

// IsExist 车险是否存在
func (vi *VehicleInsurance) IsExist(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleInsurance
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	insurance := (&safety.VehicleInsurance{}).GetByInsuranceCodeAndLicense(param.VehicleInsurance.License, param.VehicleInsurance.InsuranceCode)

	return response.Success(rsp, map[string]interface{}{"IsExist": insurance.Id != 0, "Id": insurance.Id})
}
