package safety

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	oet_scs_srv_public "app/org/scs/erpv2/api/proto/rpc/corporation"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"errors"
	api "github.com/micro/go-micro/v2/api/proto"
	"strings"
	"time"
)

type TrafficAccidentSetting struct {
	safetyModel.TrafficAccidentSetting
	Data []safetyModel.TrafficAccidentSetting
}

func (ta *TrafficAccident) ListSettingCorporationCode(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId == 0 {
		log.ErrorFields("topCorporationId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.Forbidden)
	}

	accidentSettings, err := param.TrafficAccidentSetting.GetBy(topCorporationId)
	if err != nil {
		log.ErrorFields("TrafficAccidentSetting.GetBy err", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	mapCorp := make(map[int64]*oet_scs_srv_public.CorporationItem) // map[corpId]

	for i, setting := range accidentSettings {
		if corp, ok := mapCorp[setting.CorporationId]; ok {
			accidentSettings[i].CorporationName = corp.Name
		} else {
			corporationItem := rpc.GetCorporationById(ctx, setting.CorporationId)
			if corporationItem != nil {
				accidentSettings[i].CorporationName = corporationItem.Name
				mapCorp[setting.CorporationId] = corporationItem
			}
		}
	}

	return response.Success(rsp, accidentSettings)

}

func (ta *TrafficAccident) EditSettingCorporationCode(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	if topCorporationId == 0 {
		log.ErrorFields("topCorporationId == 0", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.Forbidden)
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	for _, setting := range param.Data {
		switch strings.ToLower(setting.Option) {
		case util.OptionCreate:
			setting.Id = model.Id()
			setting.GroupId = topCorporationId
			if setting.Code == "" || setting.CorporationId == 0 {
				err = errors.New(`setting.Code == "" || setting.CorporationId == 0`)
				log.ErrorFields(`setting.Code == "" || setting.CorporationId == 0`, map[string]interface{}{"err": nil})
				return response.Error(rsp, response.ParamsMissing)
			}
			// check code's unique
			// check corporationId's unique

			if setting.IsExistsCode(topCorporationId, setting.Code) {
				err = errors.New(`setting.Code is exists`)
				log.ErrorFields(`setting.Code is exists`, map[string]interface{}{"err": nil})
				return response.Error(rsp, response.DbObjectDuplicate)
			}
			if setting.IsExistsCorporationId(topCorporationId, setting.CorporationId) {
				err = errors.New(`setting.CorporationId is exists`)
				log.ErrorFields(`setting.CorporationId is exists`, map[string]interface{}{"err": nil})
				return response.Error(rsp, response.DbObjectDuplicate)
			}
			err = setting.TransactionCreate(tx)
			if err != nil {
				log.ErrorFields(`setting.TransactionCreate err`, map[string]interface{}{"err": nil})
				return response.Error(rsp, response.DbSaveFail)
			}

		case util.OptionEdit:
			if setting.Id == 0 || setting.Code == "" {
				err = errors.New(`setting.id == 0 || setting.Code == ""`)
				log.ErrorFields(`setting.id == 0 || setting.Code == ""`, map[string]interface{}{"err": nil})
				return response.Error(rsp, response.ParamsMissing)
			}
			err = setting.TransactionUpdate(tx, map[string]interface{}{"Code": setting.Code})
			if err != nil {
				log.ErrorFields(`setting.TransactionUpdate err`, map[string]interface{}{"err": nil})
				return response.Error(rsp, response.DbUpdateFail)
			}
		case util.OptionDelete:
			if setting.Id == 0 {
				err = errors.New(`setting.id == 0`)
				log.ErrorFields(`setting.id == 0`, map[string]interface{}{"err": nil})
				return response.Error(rsp, response.ParamsMissing)
			}
			err = setting.TransactionDelete(tx)
			if err != nil {
				log.ErrorFields(`setting.TransactionDelete err`, map[string]interface{}{"err": nil})
				return response.Error(rsp, response.DbDeleteFail)
			}
		default:
			log.ErrorFields(`option not find`, map[string]interface{}{"err": nil})
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	return response.Success(rsp, nil)
}

func (ta *TrafficAccident) IsAccidentLicenseDateDriver(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	trafficAccident, count, err := param.TrafficAccident.IsExistsDriverNameLicenseHappenAt()
	if err != nil {
		log.ErrorFields("IsExistsDriverNameLicenseHappenAt error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	return response.Success(rsp, map[string]interface{}{
		"IsExists": count > 0,
		"HappenAt": trafficAccident.HappenAt,
	})
}

func (ta *TrafficAccident) IsLendBranchMoney(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentLendMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	trafficAccidentLendMoneyRecord, count, err := param.TrafficAccidentLendMoneyRecord.IsExistsBatchMoney()
	if err != nil {
		log.ErrorFields("IsExistsBatchMoney error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	return response.Success(rsp, map[string]interface{}{
		"IsExists": count > 0,
		"LendAt":   trafficAccidentLendMoneyRecord.LendAt,
	})
}

func (ta *TrafficAccident) ListRecycle(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Order == "" {
		param.Order = "desc"
	}
	if param.OrderBy == "" {
		param.OrderBy = "CreatedAt"
	}

	param.CorporationIds = service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	var where = map[string]interface{}{
		"Code":           param.Code,
		"CorporationIds": param.CorporationIds,
		//"DriverId":                param.DriverId,
		"DriverName":              param.DriverName,
		"LineId":                  param.LineId,
		"License":                 param.License,
		"HappenLocation":          param.HappenLocation,
		"Grade":                   param.Grade,
		"PeopleHurtCate":          param.PeopleHurtCate,
		"VehicleBrokenCate":       param.VehicleBrokenCate,
		"AccidentScene":           param.AccidentScene,
		"RelaterName":             param.RelaterName,
		"RelaterLicense":          param.RelaterLicense,
		"RelaterContact":          param.RelaterContact,
		"RelaterInsuranceCompany": param.RelaterInsuranceCompany,
		"IsRecycle":               int64(util.StatusForTrue),
	}
	if param.StartHappenAt != nil {
		where["StartHappenAt"] = time.Time(*param.StartHappenAt).Format(model.TimeFormat)
	}

	if param.EndHappenAt != nil {
		where["EndHappenAt"] = time.Time(*param.EndHappenAt).Format(model.TimeFormat)
	}

	accidents, count := param.TrafficAccident.GetBy(auth.User(ctx).GetUserId(), where, param.Paginator, param.OrderBy, param.Order)
	for i := range accidents {
		accidents[i].Corporations.ParseCorporation()
		accidents[i].CorporationId, accidents[i].CorporationName = accidents[i].Corporations.GetCorporation()
		accidents[i].HasRelater = accidents[i].IsHasRelater(accidents[i].Id)
		accidents[i].TotalBranchCount = (&safetyModel.TrafficAccidentRelaterBranch{}).GetBranchCount(accidents[i].Id, 0)
		accidents[i].ClosedBranchCount = (&safetyModel.TrafficAccidentRelaterBranch{}).GetBranchCount(accidents[i].Id, util.StatusForTrue)

		accidents[i].IsEnableClosed = accidents[i].CheckIsEnableClosed() && accidents[i].EditApplyStatus != util.ApplyStatusForDoing && accidents[i].IsClosed == util.StatusForTrue

	}
	return response.Success(rsp, map[string]interface{}{"Items": accidents, "TotalCount": count, "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}
