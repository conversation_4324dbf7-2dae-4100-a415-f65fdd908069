package safety

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	messageService "app/org/scs/erpv2/api/service/message"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

// TrafficAccidentRelater 事故当事人
type TrafficAccidentRelater struct {
	safetyModel.TrafficAccidentRelater
}

// ListRelater 事故当事人列表
func (ta *TrafficAccident) ListRelater(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelater
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.TrafficAccidentId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	relaters := param.TrafficAccidentRelater.GetBy(param.TrafficAccidentId, param.Name)

	var lendMoneyRecord safetyModel.TrafficAccidentLendMoneyRecord
	//计算每个当事人的借款总金额
	for idx := range relaters {
		relaters[idx].TotalLendMoney = lendMoneyRecord.GetTotalMoneyByRelaterId(relaters[idx].Id)
		relaters[idx].HasBranch = (&safetyModel.TrafficAccidentRelaterBranch{}).GetRelaterBranchCount(relaters[idx].Id, 0) > 0

		// 查找当事人分支信息
		branches := (&safetyModel.TrafficAccidentRelaterBranch{}).GetByRelaterId(relaters[idx].Id)
		//var relaterBranches = make(map[int64][]map[string]interface{})

		for i := range branches {
			var branch = map[string]interface{}{
				"Id":                       branches[i].Id,
				"BranchType":               branches[i].BranchType,
				"BranchDetail":             branches[i].BranchDetail,
				"ClosedApplyStatus":        branches[i].ClosedApplyStatus,
				"IsClosed":                 branches[i].IsClosed,
				"FilePath":                 branches[i].FilePath,
				"SolutionType":             branches[i].SolutionType,
				"SolutionDesc":             branches[i].SolutionDesc,
				"InsuranceCompanyPayMoney": branches[i].InsuranceCompanyPayMoney,
				"InsurancePayMoney":        branches[i].InsurancePayMoney,
				"LossMoney":                branches[i].LossMoney,
				"OpUserName":               branches[i].OpUserName,
				"CreatedAt":                branches[i].CreatedAt,
				"ApproveUserName":          branches[i].ApproveUserName,
			}
			if branches[i].ApproveUserId > 0 {
				user := rpc.GetUserInfoById(ctx, branches[i].ApproveUserId)
				if user != nil {
					branch["ApproveUserName"] = user.Nickname
				}
			}
			//查询事故分支的借款记录
			var lmr safetyModel.TrafficAccidentLendMoneyRecord
			records := lmr.GetByBranchId(branches[i].Id)

			var totalLendMoney int64
			for j := range records {
				records[j].DrawbackMoneyRecords = (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).GetRecordForLendMoneyRecord(records[j].Id)
				totalLendMoney += records[j].LendMoney
			}
			branch["LendMoneyRecords"] = records
			branch["TotalLendMoney"] = totalLendMoney

			// 当事人付款记录
			branch["PaymentRecords"] = (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).GetByBranchId(branches[i].Id)

			branch["TotalDrawbackMoney"] = (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).TotalMoneyForBranch(branches[i].Id)
			relaters[idx].BranchesMap = append(relaters[idx].BranchesMap, branch)

		}

	}

	return response.Success(rsp, map[string]interface{}{"Items": relaters, "TotalCount": len(relaters), "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

// ListRelaterForSelect 事故当事人（包含当事人分支）用于下拉选择
func (ta *TrafficAccident) ListRelaterForSelect(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelater
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.TrafficAccidentId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	relaters := param.TrafficAccidentRelater.GetBy(param.TrafficAccidentId, "")
	var results []map[string]interface{}
	for i := range relaters {
		var res = map[string]interface{}{
			"Id":            relaters[i].Id,
			"Name":          relaters[i].Name,
			"LiabilityType": relaters[i].LiabilityType,
		}
		//查询当事人分支
		var branch safetyModel.TrafficAccidentRelaterBranch
		branches := branch.GetByRelaterId(relaters[i].Id)
		var relaterBranches []map[string]interface{}

		for j := range branches {
			relaterBranches = append(relaterBranches, map[string]interface{}{
				"Id":         branches[j].Id,
				"BranchType": branches[j].BranchType,
			})
		}
		res["Branches"] = relaterBranches

		results = append(results, res)
	}

	return response.Success(rsp, results)

}

// CreateRelater 添加事故当事人
func (ta *TrafficAccident) CreateRelater(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelater
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}
	var accident TrafficAccident
	if !accident.ExistBy(param.TrafficAccidentId) {
		log.ErrorFields("accident not found", map[string]interface{}{"TrafficAccidentId": param.TrafficAccidentId})
		return response.Error(rsp, response.ParamsInvalid)
	}

	for i := range param.Branches {
		param.Branches[i].TrafficAccidentId = param.TrafficAccidentId
	}

	if err := param.TrafficAccidentRelater.Create(); err != nil {
		log.ErrorFields("TrafficAccidentRelater.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	//保存日志
	go BuildRelaterLogger(ctx, safetyModel.TrafficAccidentRelater{}, param.TrafficAccidentRelater)

	return response.Success(rsp, nil)
}

// ShowRelater 事故当事人详情
func (ta *TrafficAccident) ShowRelater(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelater
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	var relater safetyModel.TrafficAccidentRelater
	err = relater.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("relater.FindBy error", map[string]interface{}{"err": err, "id": param.Id})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	return response.Success(rsp, relater)
}

// EditRelater 编辑事故当事人
func (ta *TrafficAccident) EditRelater(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelater
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	var relater safetyModel.TrafficAccidentRelater
	err = relater.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("TrafficAccidentRelater not found", map[string]interface{}{"id": param.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := param.TrafficAccidentRelater.Update(); err != nil {
		log.ErrorFields("TrafficAccidentRelater.Update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	//保存日志
	go BuildRelaterLogger(ctx, relater, param.TrafficAccidentRelater)
	return response.Success(rsp, nil)
}

// DeleteRelater 删除事故当事人
func (ta *TrafficAccident) DeleteRelater(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelater
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if !param.TrafficAccidentRelater.ExistBy(param.Id) {
		log.ErrorFields("TrafficAccidentRelater not found", map[string]interface{}{"id": param.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//当事人有分支  不能删除
	var branch safetyModel.TrafficAccidentRelaterBranch
	branches := branch.GetByRelaterId(param.Id)
	if len(branches) > 0 {
		log.ErrorFields("TrafficAccidentRelater Has branch, dont delete", map[string]interface{}{"branchCount": len(branches)})
		return response.Error(rsp, response.DbDeleteFail)
	}

	if err := param.TrafficAccidentRelater.Delete(param.Id); err != nil {
		log.ErrorFields("TrafficAccidentRelater.Delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, nil)
}

// TrafficAccidentRelaterBranch 事故当事人分支
type TrafficAccidentRelaterBranch struct {
	safetyModel.TrafficAccidentRelaterBranch
	TrafficAccidentRelaterBranchId int64  `json:"TrafficAccidentRelaterBranchId"`
	ProcessId                      string `json:"ProcessId"`
	IsRestart                      bool   `json:"IsRestart"`
}

// CreateBranch 创建事故当事人分支
func (ta *TrafficAccident) CreateBranch(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelaterBranch
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}
	if !(&safetyModel.TrafficAccident{}).ExistBy(param.TrafficAccidentId) {
		log.ErrorFields("TrafficAccident not found", nil)
		return response.Error(rsp, response.ParamsInvalid)
	}
	var accident safetyModel.TrafficAccident
	err = accident.FindBy(param.TrafficAccidentId)
	if err != nil {
		log.ErrorFields("TrafficAccident not found", nil)
		return response.Error(rsp, response.ParamsInvalid)
	}

	if !(&safetyModel.TrafficAccidentRelater{}).ExistBy(param.TrafficAccidentRelaterId) {
		log.ErrorFields("TrafficAccidentRelater not found", nil)
		return response.Error(rsp, response.ParamsInvalid)
	}

	//一个事故只能有一个己方车损分支
	if param.BranchType == util.AccidentBranchTypeForSelfVehicleBroken && (&safetyModel.TrafficAccident{}).ExistSelfVehicleBrokenBranch(param.TrafficAccidentId) {
		log.ErrorFields("TrafficAccidentRelaterBranch has self vehicle broken", nil)
		return response.Error(rsp, response.FAIL)
	}

	tx := model.DB().Begin()
	//如果是己方人伤分支，且己方人伤分支已经存在，无需再创建分支
	if param.BranchType != util.AccidentBranchTypeForSelfPeopleHurt ||
		!(&safetyModel.TrafficAccidentRelaterBranch{}).IsExistBranchType(param.TrafficAccidentRelaterId, util.AccidentBranchTypeForSelfPeopleHurt) {
		err = param.TrafficAccidentRelaterBranch.TransactionCreate(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("TrafficAccidentRelaterBranch.Create error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)

}

// DeleteBranch 删除事故当事人分支
func (ta *TrafficAccident) DeleteBranch(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelaterBranch
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	//分支有借款 不能删除
	//var lendMoneyRecord safetyModel.TrafficAccidentLendMoneyRecord
	//records := lendMoneyRecord.GetByBranchId(param.Id)
	//if len(records) > 0 {
	//	log.ErrorFields("TrafficAccidentRelaterBranch Has Lend Money Record, dont delete", map[string]interface{}{"record": len(records)})
	//	return response.Error(rsp, response.DbDeleteFail)
	//}

	//分支有流程  无法删除
	if (&safetyModel.TrafficAccidentRelaterBranch{}).IsExistNormalProcess(param.Id) {
		log.ErrorFields("TrafficAccidentRelaterBranch Has process Record, dont delete", nil)
		return response.Error(rsp, response.DbDeleteFail)
	}

	err = param.TrafficAccidentRelaterBranch.Delete(param.Id)
	if err != nil {
		log.ErrorFields("TrafficAccidentRelaterBranch.Delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, nil)

}

type PaymentRecordsAndProcess struct {
	safetyModel.TrafficAccidentPaymentMoneyRecord
	CurrentHandlerUserName string
	CreateUserName         string
}
type DrawbackMoneyRecordAndProcess struct {
	safetyModel.TrafficAccidentDrawbackMoneyRecord
	CurrentHandlerUserName string
	CreateUserName         string
}

// ListBranch 事故当事人分支列表
func (ta *TrafficAccident) ListBranch(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelaterBranch
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.TrafficAccidentId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var accident safetyModel.TrafficAccident
	err = accident.FindBy(param.TrafficAccidentId)
	if err != nil {
		log.ErrorFields("accident not found", map[string]interface{}{"err": err, "id": param.TrafficAccidentId})
		return response.Error(rsp, response.ParamsInvalid)
	}

	branches := param.TrafficAccidentRelaterBranch.GetByAccidentId(param.TrafficAccidentId)
	var relaterBranches = make(map[int64][]map[string]interface{})

	for i := range branches {
		var branch = map[string]interface{}{
			"Id":                       branches[i].Id,
			"BranchType":               branches[i].BranchType,
			"BranchDetail":             branches[i].BranchDetail,
			"ClosedApplyStatus":        branches[i].ClosedApplyStatus,
			"IsClosed":                 branches[i].IsClosed,
			"FilePath":                 branches[i].FilePath,
			"SolutionType":             branches[i].SolutionType,
			"SolutionDesc":             branches[i].SolutionDesc,
			"InsuranceCompanyPayMoney": branches[i].InsuranceCompanyPayMoney,
			"InsurancePayMoney":        branches[i].InsurancePayMoney,
			"LossMoney":                branches[i].LossMoney,
			"OpUserName":               branches[i].OpUserName,
			"CreatedAt":                branches[i].CreatedAt,
			"ApproveUserName":          branches[i].ApproveUserName,
		}
		if branches[i].ApproveUserId > 0 {
			user := rpc.GetUserInfoById(ctx, branches[i].ApproveUserId)
			if user != nil {
				branch["ApproveUserName"] = user.Nickname
			}
		}

		//查询事故分支的借款记录
		var lmr safetyModel.TrafficAccidentLendMoneyRecord
		records := lmr.GetByBranchId(branches[i].Id)
		var totalLendMoney int64
		for j := range records {
			records[j].DrawbackMoneyRecords = (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).GetRecordForLendMoneyRecord(records[j].Id)
			totalLendMoney += records[j].LendMoney
		}
		branch["LendMoneyRecords"] = records
		branch["TotalLendMoney"] = totalLendMoney

		// 当事人付款记录
		branch["PaymentRecords"] = (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).GetByBranchId(branches[i].Id)

		branch["TotalDrawbackMoney"] = (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).TotalMoneyForBranch(branches[i].Id)

		relaterBranches[branches[i].TrafficAccidentRelaterId] = append(relaterBranches[branches[i].TrafficAccidentRelaterId], branch)
	}

	var results []map[string]interface{}

	for i := range relaterBranches {
		var relater safetyModel.TrafficAccidentRelater
		err := relater.FindBy(i)
		if err != nil {
			continue
		}
		results = append(results, map[string]interface{}{
			"Id":                  i,
			"TrafficAccidentId":   accident.Id,
			"TrafficAccidentCode": accident.Code,
			"Name":                relater.Name,
			"IdentifyId":          relater.IdentifyId,
			"Contact":             relater.Contact,
			"LiabilityType":       relater.LiabilityType,
			"InjuryType":          relater.InjuryType,
			"License":             relater.License,
			"InsuranceCompany":    relater.InsuranceCompany,
			"More":                relater.More,
			"FilePath":            relater.FilePath,
			"Branches":            relaterBranches[i],
		})
	}

	return response.Success(rsp, map[string]interface{}{"Items": results, "TotalCount": len(results), "FileHttpPrefix": config.Config.StaticFileHttpPrefix})

}

// ShowBranch 事故当事人分支详情
func (ta *TrafficAccident) ShowBranch(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelater
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Id == 0 || param.TrafficAccidentId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	var accident safetyModel.TrafficAccident
	err = accident.FindBy(param.TrafficAccidentId)
	if err != nil {
		log.ErrorFields("accident not found", map[string]interface{}{"err": err, "id": param.TrafficAccidentId})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var relater safetyModel.TrafficAccidentRelater
	err = relater.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("TrafficAccidentRelater not found", map[string]interface{}{"err": err, "id": param.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var branch TrafficAccidentRelaterBranch
	branches := branch.GetByRelaterId(param.Id)

	var branchMaps []map[string]interface{}
	for i := range branches {
		var branch = map[string]interface{}{
			"Id":                       branches[i].Id,
			"BranchType":               branches[i].BranchType,
			"BranchDetail":             branches[i].BranchDetail,
			"ClosedApplyStatus":        branches[i].ClosedApplyStatus,
			"IsClosed":                 branches[i].IsClosed,
			"FilePath":                 branches[i].FilePath,
			"SolutionType":             branches[i].SolutionType,
			"SolutionDesc":             branches[i].SolutionDesc,
			"InsuranceCompanyPayMoney": branches[i].InsuranceCompanyPayMoney,
			"InsurancePayMoney":        branches[i].InsurancePayMoney,
			"LossMoney":                branches[i].LossMoney,
		}
		//查询事故分支的借款记录
		var lmr safetyModel.TrafficAccidentLendMoneyRecord
		records := lmr.GetByBranchId(branches[i].Id)
		var lendMoneyRecords []map[string]interface{}
		var totalLendMoney int64
		for j := range records {
			drawbackMoneyRecords := (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).GetRecordForLendMoneyRecord(records[j].Id)
			lendMoneyRecords = append(lendMoneyRecords, map[string]interface{}{
				"Id":                   records[j].Id,
				"ApplyUserId":          records[j].ApplyUserId,
				"ApplyUserName":        records[j].ApplyUserName,
				"Desc":                 records[j].Desc,
				"FilePath":             records[j].FilePath,
				"LendMoney":            records[j].LendMoney,
				"ApplyStatus":          records[j].ApplyStatus,
				"DrawbackMoneyRecords": drawbackMoneyRecords,
				"CreatedAt":            records[j].CreatedAt,
			})
			totalLendMoney += records[j].LendMoney
		}
		branch["LendMoneyRecords"] = lendMoneyRecords
		branch["TotalLendMoney"] = totalLendMoney

		branch["PaymentRecords"] = (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).GetByBranchId(branches[i].Id)

		branch["TotalDrawbackMoney"] = (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).TotalMoneyForBranch(branches[i].Id)
		branchMaps = append(branchMaps, branch)
	}

	var result = map[string]interface{}{
		"Id":                  relater.Id,
		"TrafficAccidentId":   accident.Id,
		"TrafficAccidentCode": accident.Code,
		"Name":                relater.Name,
		"IdentifyId":          relater.IdentifyId,
		"Contact":             relater.Contact,
		"LiabilityType":       relater.LiabilityType,
		"InjuryType":          relater.InjuryType,
		"License":             relater.License,
		"InsuranceCompany":    relater.InsuranceCompany,
		"More":                relater.More,
		"FilePath":            relater.FilePath,
		"Branches":            branchMaps,
	}

	return response.Success(rsp, result)
}

// ShowBranchDetail 分支的详情
func (ta *TrafficAccident) ShowBranchDetail(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelaterBranch
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var branch safetyModel.TrafficAccidentRelaterBranch
	err = branch.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("TrafficAccidentRelaterBranch not found", map[string]interface{}{"err": err, "id": param.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var relater safetyModel.TrafficAccidentRelater
	err = relater.FindBy(branch.TrafficAccidentRelaterId)
	if err != nil {
		log.ErrorFields("TrafficAccidentRelater not found", map[string]interface{}{"err": err, "id": branch.TrafficAccidentRelaterId})
		return response.Error(rsp, response.FAIL)
	}

	branch.TrafficAccidentRelaterName = relater.Name
	branch.LiabilityType = relater.LiabilityType
	if branch.ApproveUserId == auth.User(ctx).GetUserId() {
		branch.IsApproveUser = true
	}
	return response.Success(rsp, branch)
}

// ShowBranchPaymentRecord 事故分支的付款记录
func (ta *TrafficAccident) ShowBranchPaymentRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelaterBranch
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var branch safetyModel.TrafficAccidentRelaterBranch
	err = branch.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("TrafficAccidentRelaterBranch not found", map[string]interface{}{"err": err, "id": param.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}

	paymentRecord := (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).GetDoneByBranchId(branch.Id)

	// 实际只会有一条完成的付款记录
	for i := range paymentRecord {
		// 查询流程表中的数据
		var process processModel.LbpmApplyProcess
		err = process.GetProcessByItemId(paymentRecord[i].Id, paymentRecord[i].TableName())
		if err != nil {
			log.ErrorFields("GetProcessByItemId err", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		var processParam safetyModel.TrafficAccidentPaymentMoneyRecord
		_ = json.Unmarshal(process.Param, &processParam)
	}

	return response.Success(rsp, paymentRecord)
}

// ApplyBranchClose 发起事故当事人分支结案申请
func (ta *TrafficAccident) ApplyBranchClose(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelaterBranch
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	user := auth.User(ctx).GetUser()

	var branch safetyModel.TrafficAccidentRelaterBranch
	err = branch.FindBy(param.TrafficAccidentRelaterBranchId)
	if err != nil {
		log.ErrorFields("branch.FindBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.TrafficAccidentRelaterBranch.Id = branch.Id

	//事故是已结案的状态  无法重复申请结案
	if branch.IsClosed == util.StatusForTrue || branch.ClosedApplyStatus == util.ApplyStatusForDoing {
		log.ErrorFields("process repeat", map[string]interface{}{"id": param.TrafficAccidentRelaterId})
		return response.Error(rsp, response.RepeatApplyProcess)
	}

	//事故分支有正在审批中的借款申请、退款申请、付款申请  不能结案
	if branch.IsExistHandlingLendMoneyRecord() || branch.IsExistHandlingDrawbackMoneyRecord() || branch.IsExistHandlingPaymentMoneyRecord() {
		log.ErrorFields("branch has handling process", nil)
		return response.Error(rsp, response.FAIL)
	}

	// 查询事故编号 存入param 消息中心使用
	var accident safetyModel.TrafficAccident
	err = accident.FindBy(param.TrafficAccidentId)
	if err != nil {
		log.ErrorFields("TrafficAccident FindBy fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	param.TrafficAccidentCode = accident.Code

	param.TrafficAccidentRelaterBranch.CorporationId, param.TrafficAccidentRelaterBranch.CorporationName = accident.Corporations.GetCorporation()
	param.TrafficAccidentRelaterBranch.License = accident.License
	param.TrafficAccidentRelaterBranch.LineId = accident.LineId
	param.TrafficAccidentRelaterBranch.LineName = accident.LineName
	param.TrafficAccidentRelaterBranch.DriverName = accident.DriverName

	param.ClosedApplyStatus = util.ApplyStatusForDoing
	param.FormStep = util.ProcessFormStepStart
	param.ClosedApplyUserName = user.Name
	param.ClosedApplyUserId = user.Id

	tx := model.DB().Begin()
	err = param.TrafficAccidentRelaterBranch.TransactionUpdateCloseInfo(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("TrafficAccidentRelaterBranch.TransactionUpdateCloseInfo error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	param.BranchType = branch.BranchType

	var relater safetyModel.TrafficAccidentRelater
	relater.FindBy(branch.TrafficAccidentRelaterId)
	param.TrafficAccidentRelaterName = relater.Name
	tx.Commit()

	go messageService.BuildAccidentMessage(messageService.AccidentMessageForm{
		TrafficAccidentId: accident.Id,
		RelationId:        branch.Id,
		DriverName:        accident.DriverName,
		License:           accident.License,
		ApplyUserId:       user.Id,
		ReceiveUserId:     param.TrafficAccidentRelaterBranch.ApproveUserId,
	}, util.ProcessMessageTypeForApprove, (&safetyModel.TrafficAccidentRelaterBranch{}).TableName())

	go messageService.BuildAccidentMessage(messageService.AccidentMessageForm{
		TrafficAccidentId: accident.Id,
		RelationId:        branch.Id,
		DriverName:        accident.DriverName,
		License:           accident.License,
		ApplyUserId:       user.Id,
		ReceiveUserId:     param.TrafficAccidentRelaterBranch.NotifyUserId,
	}, util.ProcessMessageTypeForNotice, (&safetyModel.TrafficAccidentRelaterBranch{}).TableName())

	return response.Success(rsp, nil)
}

// EditApplyBranchClose 编辑事故当事人分支结案表单数据
func (ta *TrafficAccident) EditApplyBranchClose(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelaterBranch
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var branch safetyModel.TrafficAccidentRelaterBranch
	err = branch.FindBy(param.TrafficAccidentRelaterBranchId)
	if err != nil {
		log.ErrorFields("branch.FindBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	branch.SolutionType = param.TrafficAccidentRelaterBranch.SolutionType
	branch.SolutionFilePath = param.TrafficAccidentRelaterBranch.SolutionFilePath
	branch.InsuranceCompanyPayMoney = param.TrafficAccidentRelaterBranch.InsuranceCompanyPayMoney
	branch.InsurancePayMoney = param.TrafficAccidentRelaterBranch.InsurancePayMoney
	branch.LossMoney = param.TrafficAccidentRelaterBranch.LossMoney
	branch.SolutionDesc = param.TrafficAccidentRelaterBranch.SolutionDesc
	branch.PayOrigin = param.TrafficAccidentRelaterBranch.PayOrigin

	tx := model.DB().Begin()

	err = branch.TransactionUpdateCloseInfo(tx)
	if err != nil {
		log.ErrorFields("branch.TransactionUpdateCloseInfo error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbUpdateFail)
	}

	var process processModel.LbpmApplyProcess
	err = (&process).GetProcessByItemId(branch.Id, branch.TableName())
	if err != nil {
		log.ErrorFields("branch.GetProcessByItemId error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbQueryFail)
	}

	var processBranch safetyModel.TrafficAccidentRelaterBranch
	err = json.Unmarshal(process.Param, &processBranch)
	if err != nil {
		log.ErrorFields("Unmarshal error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbQueryFail)
	}
	processBranch.SolutionType = param.TrafficAccidentRelaterBranch.SolutionType
	processBranch.SolutionFilePath = param.TrafficAccidentRelaterBranch.SolutionFilePath
	processBranch.InsuranceCompanyPayMoney = param.TrafficAccidentRelaterBranch.InsuranceCompanyPayMoney
	processBranch.InsurancePayMoney = param.TrafficAccidentRelaterBranch.InsurancePayMoney
	processBranch.LossMoney = param.TrafficAccidentRelaterBranch.LossMoney
	processBranch.SolutionDesc = param.TrafficAccidentRelaterBranch.SolutionDesc
	processBranch.PayOrigin = param.TrafficAccidentRelaterBranch.PayOrigin

	marshal, err := json.Marshal(processBranch)
	if err != nil {
		log.ErrorFields("Marshal error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbQueryFail)
	}

	//process.Param = marshal

	err = process.TransactionUpdateColumn(tx, "Param", marshal)
	if err != nil {
		log.ErrorFields("TransactionUpdateColumn error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbUpdateFail)
	}

	err = branch.TransactionUpdateColumns(tx, branch.Id, map[string]interface{}{"IsUpdateForm": util.StatusForFalse})
	if err != nil {
		log.ErrorFields("branch TransactionUpdateColumns error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbUpdateFail)
	}

	tx.Commit()
	return response.Success(rsp, nil)
}

// IsExistsOtherBranch 查询事故是否存在其他分支，返回前端是否应弹框选择
func (ta *TrafficAccident) IsExistsOtherBranch(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelaterBranch
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var branch safetyModel.TrafficAccidentRelaterBranch
	err = branch.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("TrafficAccidentRelaterBranch not found", map[string]interface{}{"err": err, "id": param.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}

	branches := (&safetyModel.TrafficAccidentRelaterBranch{}).GetByAccidentId(branch.TrafficAccidentId)

	var isExists bool

	for _, relaterBranch := range branches {
		if relaterBranch.Id != branch.Id {
			isExists = true
			break
		}
	}

	return response.Success(rsp, !isExists)
}

// AutoBranchClose 自动分支结案
func (ta *TrafficAccident) AutoBranchClose(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentRelaterBranch
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var branch safetyModel.TrafficAccidentRelaterBranch
	err = branch.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("TrafficAccidentRelaterBranch not found", map[string]interface{}{"err": err, "id": param.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}
	tx := model.DB().Begin()

	if branch.BranchType == util.AccidentBranchTypeForSelfVehicleBroken {
		paymentRecord := (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).GetDoneByBranchId(branch.Id)

		if len(paymentRecord) == 0 {
			log.ErrorFields("TrafficAccidentPaymentMoneyRecord not found", map[string]interface{}{"err": err, "id": param.Id})
			return response.Error(rsp, response.DbNotFoundRecord)
		}

		// 查询流程表中的数据
		var process processModel.LbpmApplyProcess
		err = process.GetProcessByItemId(paymentRecord[0].Id, paymentRecord[0].TableName())
		if err != nil {
			log.ErrorFields("GetProcessByItemId err", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		var processParam safetyModel.TrafficAccidentPaymentMoneyRecord
		err = json.Unmarshal(process.Param, &processParam)
		if err != nil {
			log.ErrorFields("TrafficAccidentPaymentMoneyRecord accident json Unmarshal error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	err = branch.TransactionUpdateClose(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("TransactionUpdateClose error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

func BuildRelaterLogger(ctx context.Context, oldData, newData safetyModel.TrafficAccidentRelater) {
	scene := safetyModel.TrafficLoggerSceneRelater
	var beforeData, afterData []byte
	if oldData.Id == 0 {
		_, newDiff := service.FindDifferentField(nil, newData, safetyModel.TrafficAccidentRelaterLoggerExceptField)
		afterData, _ = json.Marshal(newDiff)
	} else {
		oldDiff, newDiff := service.FindDifferentField(oldData, newData, safetyModel.TrafficAccidentRelaterLoggerExceptField)
		beforeData, _ = json.Marshal(oldDiff)
		afterData, _ = json.Marshal(newDiff)
	}

	user := auth.User(ctx).GetUser()

	service.CreateLogger(user.Id, user.Name, newData.Id, scene, auth.User(ctx).GetClientIp(), safetyModel.TrafficLoggerModularForAccidentRelater, beforeData, afterData, nil, "")
}
