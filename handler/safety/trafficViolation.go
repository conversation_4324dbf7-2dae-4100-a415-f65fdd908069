package safety

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/message"
	"app/org/scs/erpv2/api/model/safety"
	protocorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	protoStaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	protooetvehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"time"
)

type TrafficViolation struct {
	safety.TrafficViolation
	//TimeAt               int64   // 违规违法时间
	StartAt        model.LocalTime // 违规违法时间
	EndAt          model.LocalTime // 违规违法时间
	CreateStartAt  model.LocalTime // 违规录入时间
	CreateEndAt    model.LocalTime // 违规录入时间
	Driver         string          // 司机名+司机工号
	CorporationIds []int64         // 司机机构
	FkQASIds       []int64         // 质量考核标准id
	model.Paginator
	FileData          string
	IsRectification   bool // t: 发起整改通知 f: 不发起整改通知
	RectificationData RectificationData
	CheckStatus       int64           `json:"CheckStatus"`
	CheckMore         string          `json:"CheckMore"`
	ViolationId       int64           `json:"ViolationId"`
	FinishAt          model.LocalTime // *整改期限
	OrderField        string          `json:"OrderField"`
	Order             string          `json:"Order"`
}

type RectificationData struct {
	HandleCorporationId int64           // *整改机构id
	HeadUserId          int64           // *整改负责人id
	FinishAt            model.LocalTime // *整改期限
	Require             string          // *整改要求
}

func (t *TrafficViolation) AddBatchViolation(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {

	var q TrafficViolation
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	decodeString, err := base64.StdEncoding.DecodeString(q.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			response.Error(rsp, response.FAIL)
		}
		return
	}()

	//获取机构的层级关系
	corporation := rpc.GetCorporationDetailById(ctx, auth.User(ctx).GetCorporationId())
	if corporation == nil {
		log.ErrorFields("rpc GetCorporationDetailById is nil", map[string]interface{}{"corporationId": auth.User(ctx).GetCorporationId()})
		return response.Error(rsp, response.ParamsInvalid)
	}
	// 通过根机构获取所有员工数据
	oetStaffs := rpc.GetStaffsWithOption(ctx, corporation.GroupId)
	oetStaffMap := make(map[string]*protoStaff.OetStaffItem) // map[工号]
	for i := range oetStaffs {
		if oetStaffs[i].StaffId != "" {
			oetStaffMap[oetStaffs[i].StaffId] = oetStaffs[i]
		}
	}

	rpcTmpDetail := make(map[int64]*protocorporation.GetCorporationDetailByIdResponse) // map[corpId]
	rpcTmpVehicle := make(map[string]*protooetvehicle.OetVehicleItem)                  // map[License]

	//tmpQAS := make(map[string]safety.QualityAssessmentStandards) // 同一个CompanyId 下 map[执行标准code]

	// 0司机姓名 1司机工号 2车牌号 3违规时间 4执违规地点 5执行标准 6提交单位 7信息来源 8违规事件描述
	sheet := excelFile.Sheets[0]

	var failedRow []int // 失败行号
	var successCount int64
	var vis []safety.TrafficViolation

	//nowTime := time.Now()
	//nowDay := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, time.Local)

sheetRow:
	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 4 {
			continue
		}

		if row.Cells[0].String() == "" || row.Cells[1].String() == "" || row.Cells[2].String() == "" || row.Cells[3].String() == "" {
			failedRow = append(failedRow, i)
			continue
		}

		var vi safety.TrafficViolation
		vi.Id = model.Id()

		for cellIndex, cell := range row.Cells {
			switch cellIndex {
			case 0:
				vi.DriverStaffName = cell.String()
			case 1:
				vi.DriverStaffNo = cell.String()

				if vi.DriverStaffName == oetStaffMap[vi.DriverStaffNo].Name {
					vi.DriverStaffId = oetStaffMap[vi.DriverStaffNo].Id
				} else {
					log.Error("vi.DriverStaffName == oetStaffMap[vi.DriverStaffIdStr].Name", vi.DriverStaffName, vi.DriverStaffId, oetStaffMap[vi.DriverStaffNo].Name)
					failedRow = append(failedRow, i)
					continue sheetRow // 下一行
				}

				corporationId := oetStaffMap[vi.DriverStaffNo].CorporationId
				if detail, ok := rpcTmpDetail[corporationId]; ok {
					vi.GroupId = detail.GroupId
					vi.CompanyId = detail.CompanyId
					vi.BranchId = detail.BranchId
					vi.DepartmentId = detail.DepartmentId
					vi.FleetId = detail.FleetId
				} else {
					d := rpc.GetCorporationDetailById(ctx, corporationId)
					if d == nil {
						log.Error("GetCorporationDetailById == nil")
						failedRow = append(failedRow, i)
						continue sheetRow // 下一行
					}
					vi.GroupId = d.GroupId
					vi.CompanyId = d.CompanyId
					vi.BranchId = d.BranchId
					vi.DepartmentId = d.DepartmentId
					vi.FleetId = d.FleetId
				}

			case 2:
				vi.License = cell.String()

				// 车辆信息
				if vehicleItem, ok := rpcTmpVehicle[vi.License]; ok {
					vi.VehicleId = vehicleItem.Id
					vi.LineId = vehicleItem.LineId
				}

			case 3:
				parse, err := time.Parse("2006-1-2 15:04:05", cell.String())
				if err != nil {
					log.Error("time.Parse err", err, cell.String())
					failedRow = append(failedRow, i)
					continue sheetRow // 下一行
				}

				vi.ReportAt = model.LocalTime(parse)
			case 4:
				vi.Place = cell.String()
			case 5:

			case 6:
				vi.SubmitCorporationId = auth.User(ctx).GetCorporationId()
			case 7:
				if _, ok := util.ViolationOrigin[cell.Value]; ok {
					vi.Origin = util.ViolationOrigin[cell.Value]
				}
			case 8:
				vi.Content = cell.String()
			}
		}

		successCount++
		vis = append(vis, vi)
	}

	if len(vis) > 0 {
		err := (&safety.TrafficViolation{}).AddBatch(vis)
		if err != nil {
			log.ErrorFields("AddBatch error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"SuccessCount": successCount,
		"FailedCount":  len(failedRow),
		"FailedRow":    failedRow,
	})
}

func (t *TrafficViolation) AddViolation(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q TrafficViolation
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	q.TrafficViolation.Corporations.Build(q.CorporationId)
	q.SubmitCorporationId = auth.User(ctx).GetCorporationId()

	if q.Origin == 0 || time.Time(q.ReportAt).IsZero() || q.Content == "" ||
		q.License == "" || q.DriverStaffId == 0 {
		log.Error("q.CompanyId == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	var rec safety.TrafficViolationRectification

	if q.IsRectification {
		if q.RectificationData.HandleCorporationId == 0 || q.RectificationData.HeadUserId == 0 ||
			time.Time(q.RectificationData.FinishAt).IsZero() || q.RectificationData.Require == "" {
			log.Error("q.RectificationData.Type == 0")
			return response.Error(rsp, response.ParamsMissing)
		}
	}

	lineInfo, _ := rpc.GetLineWithId(ctx, q.LineId)
	if lineInfo != nil {
		q.TrafficViolation.LineName = lineInfo.Name
	}

	driver := rpc.GetStaffWithId(ctx, q.DriverStaffId)
	if driver != nil {
		q.TrafficViolation.DriverStaffName = driver.Name
		q.TrafficViolation.DriverStaffNo = driver.StaffId
	}

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()
	if q.IsRectification {
		q.TrafficViolation.Status = util.TrafficViolationStatusForSend
	}

	err = (&q.TrafficViolation).Add(tx)
	if err != nil {
		log.Error("Add err = ", err)
		return response.Error(rsp, response.DbSaveFail)
	}

	if q.IsRectification {
		rec.Id = model.Id()
		rec.Corporations.Build(q.RectificationData.HandleCorporationId)

		rec.TrafficViolationId = q.TrafficViolation.Id
		rec.SubmitCorporationId = q.TrafficViolation.SubmitCorporationId
		rec.ObjectStaffId = q.TrafficViolation.DriverStaffId
		rec.ObjectStaffName = q.TrafficViolation.DriverStaffName

		rec.HeadUserId = q.RectificationData.HeadUserId
		user := rpc.GetUserInfoById(ctx, rec.HeadUserId)
		if user != nil {
			rec.HeadUserName = user.Nickname
		}

		rec.FinishAt = q.RectificationData.FinishAt
		rec.Require = q.RectificationData.Require

		err = (&rec).Create()
		if err != nil {
			log.Error("RectificationData Create err == ", err)
			return response.Error(rsp, response.DbSaveFail)
		}
		go sendRectificationMessage(ctx, &rec)
	}

	return response.Error(rsp, response.SUCCESS)
}

func (t *TrafficViolation) ListViolation(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TrafficViolation
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)
	param.IsRecycle = util.StatusForFalse
	result := violationList(ctx, corporationIds, param)

	return response.Success(rsp, result)
}

func (t *TrafficViolation) RecycleList(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TrafficViolation
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)
	param.IsRecycle = util.StatusForTrue
	param.DeductScore = -1
	param.Status = -1
	result := violationList(ctx, corporationIds, param)

	return response.Success(rsp, result)
}

func violationList(ctx context.Context, corporationIds []int64, param TrafficViolation) map[string]interface{} {

	violations, totalCount := (&safety.TrafficViolation{}).List(corporationIds, param.Driver, param.FkQASIds, param.IsRecycle,
		param.LineId, param.VehicleId, param.Origin, param.DeductScore, param.Status, param.License, param.Content,
		time.Time(param.StartAt), time.Time(param.EndAt), time.Time(param.CreateStartAt), time.Time(param.CreateEndAt),
		param.OrderField, param.Order, param.Paginator)

	for i := range violations {
		violations[i].CorporationId, violations[i].CorporationName = violations[i].GetCorporation()

		submitCorporation := rpc.GetCorporationById(ctx, violations[i].SubmitCorporationId)
		if submitCorporation != nil {
			violations[i].SubmitCorporationName = submitCorporation.Name
		}
	}

	return map[string]interface{}{
		"Items":          violations,
		"TotalCount":     totalCount,
		"FileHttpPrefix": config.Config.StaticFileHttpPrefix,
	}
}

func (t *TrafficViolation) GetViolationById(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q TrafficViolation
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsMissing)
	}
	violation, err := q.TrafficViolation.GetDetail()
	if err != nil {
		log.Error("GetDetail err == ", err)
		return response.Error(rsp, response.DbQueryFail)
	}
	violation.CorporationId, violation.CorporationName = violation.GetCorporation()

	submitCorporation := rpc.GetCorporationById(ctx, violation.SubmitCorporationId)
	if submitCorporation != nil {
		violation.SubmitCorporationName = submitCorporation.Name
	}
	violation.FileHttpPrefix = config.Config.StaticFileHttpPrefix

	return response.Success(rsp, violation)
}

func (t *TrafficViolation) EditViolation(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q TrafficViolation
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 || q.Origin == 0 || time.Time(q.ReportAt).IsZero() || q.Content == "" ||
		q.License == "" || q.LineId == 0 ||
		q.DriverStaffId == 0 || q.CorporationId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	trafficViolation := (&safety.TrafficViolation{}).FindBy(q.Id)
	if trafficViolation.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	lineInfo, _ := rpc.GetLineWithId(ctx, q.LineId)
	if lineInfo != nil {
		q.TrafficViolation.LineName = lineInfo.Name
	}
	q.Corporations.Build(q.CorporationId)

	//
	driver := rpc.GetStaffWithId(ctx, q.DriverStaffId)
	if driver != nil {
		q.TrafficViolation.DriverStaffName = driver.Name
		q.TrafficViolation.DriverStaffNo = driver.StaffId
	}
	q.TrafficViolation.SubmitCorporationId = auth.User(ctx).GetCorporationId()

	err = (&q.TrafficViolation).Edit()
	if err != nil {
		log.Error("Edit err = ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}
	if trafficViolation.Status > util.TrafficViolationStatusForNotSend {
		_, trafficViolation.CorporationName = trafficViolation.GetCorporation()
		_, q.TrafficViolation.CorporationName = q.TrafficViolation.GetCorporation()
		//记录修改日志
		oldDiff, newDiff := service.FindDifferentField(trafficViolation, q.TrafficViolation, []string{
			"IsRecycle", "Status", "RecycleMore", "RecycleFile", "CreatedAt", "UpdatedAt",
		})

		beforeData, _ := json.Marshal(oldDiff)
		afterData, _ := json.Marshal(newDiff)

		var logger = safety.TrafficViolationLogger{
			TrafficViolationId: trafficViolation.Id,
			BeforeData:         beforeData,
			AfterData:          afterData,
		}

		logger.ParseOpUser(ctx)
		_ = logger.Create()
	}

	return response.Error(rsp, response.SUCCESS)
}

func (t *TrafficViolation) Check(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TrafficViolation
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 || !util.IncludeInt64([]int64{util.TrafficViolationStatusForRefuse, util.TrafficViolationStatusForChecked}, param.CheckStatus) {
		return response.Error(rsp, response.ParamsMissing)
	}

	if param.CheckStatus == util.TrafficViolationStatusForRefuse && param.CheckMore == "" {
		return response.Error(rsp, response.ParamsMissing)
	}

	violation := (&safety.TrafficViolation{}).FindBy(param.Id)

	if violation.Id == 0 {
		log.ErrorFields("TrafficViolation FindBy not fund", map[string]interface{}{"violationId": param.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()

	err = violation.TransactionUpdateStatus(tx, param.CheckStatus)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("TrafficViolation.TransactionUpdateStatus error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	//更新整改状态
	rectification := (&safety.TrafficViolationRectification{}).FirstByViolationId(param.Id)
	if rectification.Id == 0 {
		tx.Rollback()
		log.ErrorFields("TrafficViolationRectification FirstByViolationId not fund", map[string]interface{}{"violationId": param.Id})
		return response.Error(rsp, response.DbSaveFail)
	}
	if param.CheckStatus == util.TrafficViolationStatusForRefuse {
		rectification.FinishAt = param.FinishAt
		rectification.HandleStatus = util.RectificationHandleStatusForRefuse
	}
	if param.CheckStatus == util.TrafficViolationStatusForChecked {
		rectification.HandleStatus = util.RectificationHandleStatusForChecked
	}

	err = rectification.TransactionUpdateHandleStatus(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("rectification.TransactionUpdateHandleStatus error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	//创建日志
	var checkLog = safety.TrafficViolationCheckLog{
		TrafficViolationId: violation.Id,
		Status:             param.CheckStatus,
		More:               param.CheckMore,
	}
	checkLog.ParseOpUser(ctx)

	err = checkLog.TransactionCreate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("checkLog.TransactionCreate error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	tx.Commit()

	//司机帮扶
	if param.CheckStatus == util.TrafficViolationStatusForChecked {
		go addDriverHelp(&rectification)
	}

	return response.Success(rsp, nil)

}

func (t *TrafficViolation) CheckLog(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TrafficViolation
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	violation := (&safety.TrafficViolation{}).FindBy(param.ViolationId)

	if violation.Id == 0 {
		log.ErrorFields("TrafficViolation FindBy not fund", map[string]interface{}{"violationId": param.ViolationId})
		return response.Error(rsp, response.ParamsInvalid)
	}

	logs := (&safety.TrafficViolationCheckLog{}).GetByViolationId(violation.Id)

	return response.Success(rsp, map[string]interface{}{"Items": logs, "TotalCount": len(logs)})
}

func (t *TrafficViolation) EditLog(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TrafficViolation
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	violation := (&safety.TrafficViolation{}).FindBy(param.ViolationId)

	if violation.Id == 0 {
		log.ErrorFields("TrafficViolation FindBy not fund", map[string]interface{}{"violationId": param.ViolationId})
		return response.Error(rsp, response.ParamsInvalid)
	}

	logs := (&safety.TrafficViolationLogger{}).GetBy(violation.Id)

	return response.Success(rsp, map[string]interface{}{"Items": logs, "TotalCount": len(logs)})
}

func (t *TrafficViolation) AddRecycle(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TrafficViolation
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	violation := (&safety.TrafficViolation{}).FindBy(param.Id)

	if violation.Id == 0 {
		log.ErrorFields("TrafficViolation FindBy not fund", map[string]interface{}{"violationId": param.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.RecycleMore == "" {
		return response.Error(rsp, response.ParamsMissing)
	}

	violation.IsRecycle = util.StatusForTrue
	violation.RecycleMore = param.RecycleMore
	violation.RecycleFile = param.RecycleFile

	tx := model.DB().Begin()
	err = violation.TransactionUpdateRecycle(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("violation.UpdateRecycle error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	rectification := (&safety.TrafficViolationRectification{}).FirstByViolationId(violation.Id)
	if rectification.Id > 0 {
		err = (&message.Message{}).TransactionDeleteByRelationId(tx, rectification.Id, rectification.TableName())
		if err != nil {
			tx.Rollback()
			log.ErrorFields("Message.TransactionDeleteByRelationId error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbDeleteFail)
		}

		err = rectification.TransactionDelete(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("rectification.TransactionDelete error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbDeleteFail)
		}
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

func (t *TrafficViolation) RecycleDelete(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TrafficViolation
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	violation := (&safety.TrafficViolation{}).FindBy(param.Id)

	if violation.Id == 0 {
		log.ErrorFields("TrafficViolation FindBy not fund", map[string]interface{}{"violationId": param.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if violation.IsRecycle != util.StatusForTrue {
		return response.Error(rsp, response.FAIL)
	}

	err = violation.Delete()
	if err != nil {
		log.ErrorFields("violation.Delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, nil)
}

type HistoryViolation struct {
	safety.TrafficViolationHistory
	StartAt model.LocalTime `json:"StartAt"`
	EndAt   model.LocalTime `json:"EndAt"`
	model.Paginator
}

func (t *TrafficViolation) HistoryViolation(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param HistoryViolation
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	records, count := (&safety.TrafficViolationHistory{}).GetBy(param.LineName, param.CorporationName, param.Driver, param.Attendant, param.License,
		time.Time(param.StartAt), time.Time(param.EndAt), param.Paginator)

	return response.Success(rsp, map[string]interface{}{
		"Items":      records,
		"TotalCount": count,
	})
}
