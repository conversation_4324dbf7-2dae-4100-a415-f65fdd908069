package safety

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"time"

	api "github.com/micro/go-micro/v2/api/proto"
)

type VehicleAbnormalStopRecordRequest struct {
	Id         int64           `json:"Id"`
	VehicleId  int64           `json:"VehicleId"`
	License    string          `json:"License"`
	DriverName string          `json:"DriverName"`
	LineName   string          `json:"LineName"`
	StartAt    model.LocalTime `json:"StartAt"`
	EndAt      model.LocalTime `json:"EndAt"`
	model.Paginator
}

func (ta *TrafficAccident) VehicleAbnormalStopRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleAbnormalStopRecordRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	authCorporationIds := service.AuthCorporationIdProvider(ctx, nil)

	records, count := (&safetyModel.VehicleAbnormalStopRecord{}).GetBy(auth.User(ctx).GetTopCorporationId(), authCorporationIds, param.License, param.DriverName, param.LineName, time.Time(param.StartAt), time.Time(param.EndAt), param.Paginator)
	return response.Success(rsp, map[string]interface{}{"Items": records, "TotalCount": count})
}

// 查询车辆的异常停车记录
func (ta *TrafficAccident) VehicleAbnormalStopRecordByVehicleId(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleAbnormalStopRecordRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	records := (&safetyModel.VehicleAbnormalStopRecord{}).GetByVehicleId(param.VehicleId)
	return response.Success(rsp, map[string]interface{}{"Items": records})
}

// 设置异常停车的状态为忽略
func (ta *TrafficAccident) VehicleAbnormalStopRecordIgnore(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleAbnormalStopRecordRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	record := (&safetyModel.VehicleAbnormalStopRecord{}).FirstBy(param.Id)
	if record.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	record.Status = util.VehicleAbnormalStopHandleStatusForIgnored
	err = record.Update()
	if err != nil {
		log.ErrorFields("record.Update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)
}
