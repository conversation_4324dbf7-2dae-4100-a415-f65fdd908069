package safety

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"strconv"
)

type QualityAssessmentStandard struct {
	safetyModel.QualityAssessmentStandards
	ClassIds    []int64
	ValueIds    []int64
	CategoryIds []int64
	model.Paginator
	CorporationId int64
	FileData      string
}

type Category struct {
	safetyModel.ViolationCategory
	CorporationId int64
}

// 添加 违规类别
func (qas *QualityAssessmentStandard) AddCategory(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Category
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.ItemType > safetyModel.CLASS_1 && q.ParentId == 0 {
		log.Error("创建子项未传parentId")
		return response.Error(rsp, response.ParamsMissing)
	}

	if q.ItemType == safetyModel.CLASS_1 {
		q.ParentIdPath = strconv.FormatInt(q.Id, 10)
		err := (&q).Add()
		if err != nil {
			log.Error("Add err = ", err)
			return response.Error(rsp, response.DbSaveFail)
		}
	} else {
		// 获取 父项信息 填充parentIdPath
		violationCategory, err := (&q).GetByParentId()
		if err != nil {
			log.Error("GetByParentId err = ", err)
			return response.Error(rsp, response.DbQueryFail)
		}

		q.ParentIdPath = fmt.Sprintf(`%s,%d`, violationCategory.ParentIdPath, q.Id)
		err = (&q).Add()
		if err != nil {
			log.Error("Add err = ", err)
			return response.Error(rsp, response.DbSaveFail)
		}

	}

	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) ListCategory(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Category
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	categories, err := (&q).List(q.QualityAssessmentCateId)
	if err != nil {
		log.Error("List err = ", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	return response.Success(rsp, categories)
}

func (qas *QualityAssessmentStandard) EditCategory(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Category
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 || q.ItemType == 0 || q.Name == "" {
		log.Error("q.Id == 0 || q.Name == ''")
		return response.Error(rsp, response.ParamsMissing)
	}

	if q.ItemType == safetyModel.VALUE_2 {
		parseFloat, err := strconv.ParseFloat(q.Name, 64)
		if err != nil {
			log.Error("ParseFloat err =", err)
			return response.Error(rsp, response.ParamsInvalid)
		}
		q.DeductScore = int64(parseFloat * 100)
	}

	err = (&q).Edit()
	if err != nil {
		log.Error("Edit err = ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) DeleteCategory(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Category
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0 || q.Name == ''")
		return response.Error(rsp, response.ParamsMissing)
	}

	// 检查删除项 是否关联质量考核标准 如果关联的质量考核标准未删除，则不允许违规类别删除

	count, err := (&safetyModel.QualityAssessmentStandards{}).GetCountByViolationCategoriesId(q.Id)
	if err != nil {
		log.Error("GetCountByViolationCategoriesId err = ", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	if count > 0 {
		log.Error("count > 0, 违规类别已绑定质量考核标准，不允许删除", err)
		return response.Error(rsp, "OP1001")
	}

	err = (&q).Delete()
	if err != nil {
		log.Error("Delete err = ", err)
		return response.Error(rsp, response.DbDeleteFail)
	}
	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) AddBatch(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.QualityAssessmentCateId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	decodeString, err := base64.StdEncoding.DecodeString(q.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			response.Error(rsp, response.FAIL)
		}
		return
	}()

	// 0类别规范 1扣分 2规类别 3执行标准号 4执行标准描述  5标准属性
	sheet := excelFile.Sheets[0]

	var failedRow []int // 失败行号
	var successCount int64
	var QASItems []safetyModel.QualityAssessmentStandards

	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 4 {
			continue
		}

		if row.Cells[0].String() == "" || row.Cells[1].String() == "" || row.Cells[2].String() == "" || row.Cells[3].String() == "" {
			failedRow = append(failedRow, i)
			continue
		}

		var QASItem safetyModel.QualityAssessmentStandards
		QASItem.QualityAssessmentCateId = q.QualityAssessmentCateId

		for cellIndex, cell := range row.Cells {
			switch cellIndex {
			case 0:
				QASItem.ClassName = cell.String()
			case 1:
				QASItem.ValueName = cell.String()
			case 2:
				QASItem.CategoryName = cell.String()
			case 3:
				QASItem.Code = cell.String()
			case 4:
				QASItem.Describe = cell.String()
			case 5:
				QASItem.AttrType, _ = cell.Int64()
			}
		}
		successCount++
		QASItems = append(QASItems, QASItem)
	}

	if len(QASItems) > 0 {
		err := (&safetyModel.QualityAssessmentStandards{}).AddBatch(q.QualityAssessmentCateId, QASItems)
		if err != nil {
			log.ErrorFields("AddBatch error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"SuccessCount": successCount,
		"FailedCount":  len(failedRow),
		"FailedRow":    failedRow,
	})
}

func (qas *QualityAssessmentStandard) Add(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	//if (time.Time(q.ValidAt).Unix() - time.Now().Unix()) > 0 {
	//	q.QualityAssessmentStandards.Status = util.StatusForTrue
	//} else {
	//	q.QualityAssessmentStandards.Status = util.StatusForFalse
	//}

	err = (&q.QualityAssessmentStandards).Add()
	if err != nil {
		log.Error("Add err = ", err)
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return list(ctx, req, rsp)
}

func (qas *QualityAssessmentStandard) SelectList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return list(ctx, req, rsp)
}

func list(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	standards, totalCount, err := (&param.QualityAssessmentStandards).List(param.QualityAssessmentCateId, param.Status, param.Describe, param.Paginator)
	if err != nil {
		log.Error("List err ==", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	for i := range standards {
		standards[i].ViolationCount = (&safetyModel.TrafficViolation{}).CountByStandardId(standards[i].Id)
	}

	data := map[string]interface{}{
		"Items":      standards,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)
}

func (qas *QualityAssessmentStandard) Tree(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	//detailById := rpc.GetCorporationDetailById(ctx, auth.User(ctx).GetCorporationId())
	//if detailById == nil {
	//	log.Error("detailById == nil")
	//	return response.Error(rsp, response.Forbidden)
	//}
	//
	//if q.CorporationId == 0 {
	//	if detailById.FleetId > 0 {
	//		q.CorporationId = detailById.FleetId
	//	} else if detailById.DepartmentId > 0 {
	//		q.CorporationId = detailById.DepartmentId
	//	} else if detailById.BranchId > 0 {
	//		q.CorporationId = detailById.BranchId
	//	} else if detailById.CompanyId > 0 {
	//		q.CorporationId = detailById.CompanyId
	//	} else if detailById.GroupId > 0 {
	//		q.CorporationId = detailById.GroupId
	//	}
	//}

	list, err := (&q.QualityAssessmentStandards).Tree(q.QualityAssessmentCateId)
	if err != nil {
		log.Error("List err ==", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	// 赋值Name
	for i := range list {
		for i2 := range list[i].SubItems {
			for i3 := range list[i].SubItems[i2].SubItems {
				for i4 := range list[i].SubItems[i2].SubItems[i3].SubItems {
					list[i].SubItems[i2].SubItems[i3].SubItems[i4].Name = list[i].SubItems[i2].SubItems[i3].SubItems[i4].Code
				}
			}
		}
	}

	return response.Success(rsp, list)
}

func (qas *QualityAssessmentStandard) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = (&q.QualityAssessmentStandards).Edit()
	if err != nil {
		log.Error("Edit err = ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	if (&safetyModel.TrafficViolation{}).CountByStandardId(q.Id) > 0 {
		return response.Error(rsp, response.DeleteParentDataFail)
	}

	err = (&q.QualityAssessmentStandards).Delete()
	if err != nil {
		log.Error("Delete err = ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) Enable(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	// 启用
	q.QualityAssessmentStandards.Status = util.StatusForTrue
	err = (&q.QualityAssessmentStandards).EditStatus()
	if err != nil {
		log.Error("Delete err = ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) Disable(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	// 禁用
	q.QualityAssessmentStandards.Status = util.StatusForFalse
	err = (&q.QualityAssessmentStandards).EditStatus()
	if err != nil {
		log.Error("Delete err = ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}
