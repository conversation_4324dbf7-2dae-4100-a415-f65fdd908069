package safety

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
)

type QualityAssessmentStandard struct {
	safetyModel.QualityAssessmentStandards
	ClassIds    []int64
	ValueIds    []int64
	CategoryIds []int64
	model.Paginator
	CorporationId int64
	FileData      string
}

type Category struct {
	safetyModel.ViolationCategory
	CorporationId int64
}

// 添加 违规类别
func (qas *QualityAssessmentStandard) AddCategory(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Category
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = (&q).Add()
	if err != nil {
		log.Error("Add err = ", err)
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) ListCategory(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Category
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	categories, err := (&q).List(q.QualityAssessmentCateId)
	if err != nil {
		log.Error("List err = ", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	for i := range categories {
		categories[i].PunishSetting = (&safetyModel.ViolationCategoryPunishSetting{}).FirstBy(categories[i].Id)
		if len(categories[i].SubItems) > 0 {
			for j := range categories[i].SubItems {
				categories[i].SubItems[j].PunishSetting = (&safetyModel.ViolationCategoryPunishSetting{}).FirstBy(categories[i].SubItems[j].Id)
				if len(categories[i].SubItems[j].SubItems) > 0 {
					for m := range categories[i].SubItems[j].SubItems {
						categories[i].SubItems[j].SubItems[m].PunishSetting = (&safetyModel.ViolationCategoryPunishSetting{}).FirstBy(categories[i].SubItems[j].SubItems[m].Id)
					}
				}
			}
		}
	}

	return response.Success(rsp, categories)
}

func (qas *QualityAssessmentStandard) EditCategory(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Category
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 || q.Name == "" {
		log.Error("q.Id == 0 || q.Name == ''")
		return response.Error(rsp, response.ParamsMissing)
	}

	err = (&q).Edit()
	if err != nil {
		log.Error("Edit err = ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) DeleteCategory(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Category
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0 || q.Name == ''")
		return response.Error(rsp, response.ParamsMissing)
	}

	// 检查删除项 是否关联质量考核标准 如果关联的质量考核标准未删除，则不允许违规类别删除

	count, err := (&safetyModel.QualityAssessmentStandards{}).GetCountByViolationCategoriesId(q.Id)
	if err != nil {
		log.Error("GetCountByViolationCategoriesId err = ", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	if count > 0 {
		log.Error("count > 0, 违规类别已绑定质量考核标准，不允许删除", err)
		return response.Error(rsp, "OP1001")
	}

	err = (&q).Delete()
	if err != nil {
		log.Error("Delete err = ", err)
		return response.Error(rsp, response.DbDeleteFail)
	}
	return response.Success(rsp, nil)
}

type CategoryPunishSetting struct {
	CategoryId   int64                                            `json:"CategoryId"`
	SettingItems []safetyModel.ViolationCategoryPunishSettingItem `json:"SettingItems"`
	DevoteStatus int64                                            `json:"DevoteStatus"`
}

func (qas *QualityAssessmentStandard) SaveCategoryPunishSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CategoryPunishSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.CategoryId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	category := (&safetyModel.ViolationCategory{}).FirstById(param.CategoryId)
	if category.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	children := category.GetChildren()
	if len(children) > 0 {
		return response.Error(rsp, "存在下级，无法添加处罚配置")
	}
	var settingItem []byte
	if len(param.SettingItems) > 0 {
		settingItem, err = json.Marshal(param.SettingItems)
		if err != nil {
			log.Error("json.Marshal err=", err.Error())
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	var setting = safetyModel.ViolationCategoryPunishSetting{
		SettingItem:         settingItem,
		ViolationCategoryId: category.Id,
	}

	err = setting.Create()
	if err != nil {
		log.Error("Create err=", err.Error())
		return response.Error(rsp, response.DbSaveFail)
	}
	if param.DevoteStatus != category.DevoteStatus {
		category.DevoteStatus = param.DevoteStatus
		_ = category.UpdateDevoteStatus()
	}

	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) AddBatch(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.QualityAssessmentCateId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	decodeString, err := base64.StdEncoding.DecodeString(q.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	defer func() {
		// 发生宕机时，获取panic传递的上下文并打印
		if err := recover(); err != nil {
			log.ErrorFields("panic error", map[string]interface{}{"err": err})
			response.Error(rsp, response.FAIL)
		}
		return
	}()

	// 0类别规范 1扣分 2规类别 3执行标准号 4执行标准描述  5标准属性
	sheet := excelFile.Sheets[0]

	var failedRow []int // 失败行号
	var successCount int64
	var QASItems []safetyModel.QualityAssessmentStandards

	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		if len(row.Cells) < 4 {
			continue
		}

		if row.Cells[0].String() == "" || row.Cells[3].String() == "" {
			failedRow = append(failedRow, i)
			continue
		}

		var QASItem safetyModel.QualityAssessmentStandards
		QASItem.QualityAssessmentCateId = q.QualityAssessmentCateId

		for cellIndex, cell := range row.Cells {
			switch cellIndex {
			case 0:
				QASItem.FirstCateName = cell.String()
			case 1:
				QASItem.SecondCateName = cell.String()
			case 2:
				QASItem.ThreeCateName = cell.String()
			case 3:
				QASItem.Code = cell.String()
			case 4:
				QASItem.Describe = cell.String()
			case 5:
				QASItem.AttrType, _ = cell.Int64()
			}
		}
		err = buildCategoryId(&QASItem)
		if err != nil {
			failedRow = append(failedRow, i)
		}

		successCount++
		QASItems = append(QASItems, QASItem)
	}

	if len(QASItems) > 0 {
		err := (&safetyModel.QualityAssessmentStandards{}).AddBatch(QASItems)
		if err != nil {
			log.ErrorFields("AddBatch error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"SuccessCount": successCount,
		"FailedCount":  len(failedRow),
		"FailedRow":    failedRow,
	})
}
func buildCategoryId(item *safetyModel.QualityAssessmentStandards) error {
	//获取一级
	first := (&safetyModel.ViolationCategory{}).FirstByName(item.QualityAssessmentCateId, item.FirstCateName, 0)
	if first.Id == 0 {
		first.QualityAssessmentCateId = item.QualityAssessmentCateId
		first.ParentId = 0
		first.Name = item.FirstCateName
		err := first.Add()
		if err != nil {
			log.ErrorFields("AddCategory error", map[string]interface{}{"err": err})
			return err
		}
	}

	if item.SecondCateName == "" {
		item.CategoryId = first.Id
		item.CategoryName = first.Name
		return nil
	}

	second := (&safetyModel.ViolationCategory{}).FirstByName(item.QualityAssessmentCateId, item.SecondCateName, first.Id)
	if second.Id == 0 {
		second.QualityAssessmentCateId = item.QualityAssessmentCateId
		second.ParentId = first.Id
		second.Name = item.SecondCateName
		err := second.Add()
		if err != nil {
			log.ErrorFields("AddCategory error", map[string]interface{}{"err": err})
			return err
		}
	}

	if item.ThreeCateName == "" {
		item.CategoryId = second.Id
		item.CategoryName = second.Name
		return nil
	}

	three := (&safetyModel.ViolationCategory{}).FirstByName(item.QualityAssessmentCateId, item.ThreeCateName, second.Id)
	if three.Id == 0 {
		three.QualityAssessmentCateId = item.QualityAssessmentCateId
		three.ParentId = second.Id
		three.Name = item.ThreeCateName
		err := three.Add()
		if err != nil {
			log.ErrorFields("AddCategory error", map[string]interface{}{"err": err})
			return err
		}
	}

	item.CategoryId = three.Id
	item.CategoryName = three.Name
	return nil
}
func (qas *QualityAssessmentStandard) Add(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	category := (&safetyModel.ViolationCategory{}).FirstById(q.CategoryId)
	if category.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	q.QualityAssessmentStandards.DeductScore = category.DeductScore
	q.QualityAssessmentStandards.DeductMoney = category.DeductMoney

	err = (&q.QualityAssessmentStandards).Add()
	if err != nil {
		log.Error("Add err = ", err)
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return list(ctx, req, rsp)
}

func (qas *QualityAssessmentStandard) SelectList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return list(ctx, req, rsp)
}

func list(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	standards, totalCount, err := (&param.QualityAssessmentStandards).List(param.QualityAssessmentCateId, param.Status, param.Describe, param.Paginator)
	if err != nil {
		log.Error("List err ==", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	for i := range standards {
		category := (&safetyModel.ViolationCategory{}).FirstById(standards[i].CategoryId)
		standards[i].CategoryNames = category.ParseCategoryNames()

		cate := (&safetyModel.QualityAssessmentCate{}).FindBy(category.QualityAssessmentCateId)
		standards[i].DeductScore = category.DeductScore
		standards[i].DeductMoney = category.DeductMoney
		standards[i].ViolationCount = (&safetyModel.TrafficViolation{}).CountByStandardId(standards[i].Id)
		standards[i].CateAttr = cate.Attr
		standards[i].QualityAssessmentCateName = cate.Name
		standards[i].QualityAssessmentCateAttr = cate.Attr
	}

	data := map[string]interface{}{
		"Items":      standards,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)
}

//func (qas *QualityAssessmentStandard) Tree(ctx context.Context, req *api.Request, rsp *api.Response) error {
//	var q QualityAssessmentStandard
//	err := json.Unmarshal([]byte(req.Body), &q)
//	if err != nil {
//		log.Error("json.Unmarshal err=", err.Error())
//		return response.Error(rsp, response.ParamsInvalid)
//	}
//
//	err = util.Validator().Struct(q)
//	if err != nil {
//		log.Error("validate.Struct err=", err.Error())
//		return response.Error(rsp, response.ParamsInvalid)
//	}
//
//	list, err := (&q.QualityAssessmentStandards).Tree(q.QualityAssessmentCateId)
//	if err != nil {
//		log.Error("List err ==", err.Error())
//		return response.Error(rsp, response.DbQueryFail)
//	}
//
//	// 赋值Name
//	for i := range list {
//		for i2 := range list[i].SubItems {
//			for i3 := range list[i].SubItems[i2].SubItems {
//				for i4 := range list[i].SubItems[i2].SubItems[i3].SubItems {
//					list[i].SubItems[i2].SubItems[i3].SubItems[i4].Name = list[i].SubItems[i2].SubItems[i3].SubItems[i4].Code
//				}
//			}
//		}
//	}
//
//	return response.Success(rsp, list)
//}

func (qas *QualityAssessmentStandard) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	err = (&q.QualityAssessmentStandards).Edit()
	if err != nil {
		log.Error("Edit err = ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	if (&safetyModel.TrafficViolation{}).CountByStandardId(q.Id) > 0 {
		return response.Error(rsp, response.DeleteParentDataFail)
	}

	err = (&q.QualityAssessmentStandards).Delete()
	if err != nil {
		log.Error("Delete err = ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) Enable(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	// 启用
	q.QualityAssessmentStandards.Status = util.StatusForTrue
	err = (&q.QualityAssessmentStandards).EditStatus()
	if err != nil {
		log.Error("Delete err = ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) Disable(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q QualityAssessmentStandard
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	// 禁用
	q.QualityAssessmentStandards.Status = util.StatusForFalse
	err = (&q.QualityAssessmentStandards).EditStatus()
	if err != nil {
		log.Error("Delete err = ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}
