package safety

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type SafeProduction struct {
	safety.SafeProductionReport
	CorporationId  int64            `json:"CorporationId"`
	CorporationIds []int64          `json:"CorporationIds"` // 机构id
	Month          *model.LocalTime `json:"Month"`          //月份
	ProcessId      string           `json:"ProcessId"`
	IsRestart      bool             `json:"IsRestart"`
	model.Paginator
}

func (qas *SafeProduction) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form SafeProduction
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	corporationIds := service.AuthCorporationIdProvider(ctx, form.CorporationIds)
	data, totalCount, err := (&safety.SafeProductionReport{}).List(corporationIds, form.Month, form.Paginator)
	if err != nil {
		log.ErrorFields("SafeProductionReport List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if data != nil {
		for index := range data {
			data[index].CorporationId, data[index].CorporationName = data[index].GetCorporation()
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": data, "TotalCount": totalCount})
}

func (qas *SafeProduction) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form SafeProduction
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	// 判断提交的表单状态
	if !form.SafeProductionReport.IsAddable() {
		return response.Error(rsp, response.ParamsInvalid)
	}
	// 草稿不需要校验
	if form.SafeProductionReport.ApplyStatus != util.Safe_Production_Report_Draft {
		err := util.Validator().Struct(&form.SafeProductionReport)
		if err != nil {
			return response.Error(rsp, response.ParamsInvalid)
		}
	}
	//
	user := auth.User(ctx).GetUser()
	form.SafeProductionReport.Corporations.Build(user.CorporationId)
	// 一车队一月一报表
	if form.SafeProductionReport.IsExist() {
		return response.Error(rsp, "OP7523")
	}
	form.SafeProductionReport.OpUserId = user.Id
	form.SafeProductionReport.OpUserName = user.Name
	// 审批中 需要走lbpm流
	if form.SafeProductionReport.ApplyStatus == util.Safe_Production_Report_Approval && config.Config.Lbpm.Enable {
		var tx = model.DB().Begin()
		err := form.SafeProductionReport.TxCreate(tx)
		if err != nil {
			log.ErrorFields("SafeProductionReport created error", map[string]interface{}{"err": err})
			tx.Rollback()
			return response.Error(rsp, response.DbSaveFail)
		}
		byteParam, _ := json.Marshal(form)
		processTitle := fmt.Sprintf("%s提交的安全生产审批", user.Name)
		_, err = processService.NewDispatchProcess(user, config.SafeProductionReportFormTemplate, processTitle, form.SafeProductionReport.Id, form.TableName(), form.ApplyStatusFieldName(), string(byteParam), nil)
		if err != nil {
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			tx.Rollback()
			return response.Error(rsp, response.FAIL)
		}
		tx.Commit()
	} else {
		err := form.SafeProductionReport.Create()
		if err != nil {
			return response.Error(rsp, response.DbSaveFail)
		}
	}
	return response.Success(rsp, form.Id)
}

func (qas *SafeProduction) Update(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form SafeProduction
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	oldReport := (&safety.SafeProductionReport{}).FirstBy(form.Id)
	if !oldReport.IsModifiable() {
		return response.Error(rsp, "OP7521")
	}
	user := auth.User(ctx).GetUser()
	form.SafeProductionReport.Corporations.Build(user.CorporationId)

	// 重新提交走lbpm流
	if form.SafeProductionReport.ApplyStatus == util.Safe_Production_Report_Approval && config.Config.Lbpm.Enable {
		user := auth.User(ctx).GetUser()
		var tx = model.DB().Begin()
		err := form.SafeProductionReport.TxUpdate(tx)
		if err != nil {
			log.ErrorFields("SafeProductionReport created error", map[string]interface{}{"err": err})
			tx.Rollback()
			return response.Error(rsp, response.DbSaveFail)
		}
		byteParam, _ := json.Marshal(form)
		processTitle := fmt.Sprintf("%s提交的安全生产审批", user.Name)
		if form.IsRestart {
			if form.ProcessId == "" {
				log.ErrorFields("ProcessId is missing", map[string]interface{}{"processId": form.ProcessId})
				return response.Error(rsp, response.ParamsMissing)
			}
			//重新发起流程
			err = processService.RestartProcess(user, form.ProcessId, string(byteParam), nil)
			if err != nil {
				log.ErrorFields("processService.RestartProcess error", map[string]interface{}{"err": err})
				tx.Rollback()
				return response.Error(rsp, response.FAIL)
			}
			tx.Commit()
		} else {
			//发起新的流程
			_, err = processService.NewDispatchProcess(user, config.SafeProductionReportFormTemplate, processTitle, form.Id, form.TableName(), form.ApplyStatusFieldName(), string(byteParam), nil)
			if err != nil {
				log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
				tx.Rollback()
				return response.Error(rsp, response.FAIL)
			}
			tx.Commit()
		}
	} else {
		err := form.SafeProductionReport.Update()
		if err != nil {
			return response.Error(rsp, response.DbSaveFail)
		}
	}
	return response.Success(rsp, form.Id)
}

func (qas *SafeProduction) Scrap(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form SafeProduction
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	err := (&safety.SafeProductionReport{}).UpdateState(form.Id, 5)
	if err != nil {
		return response.Error(rsp, response.DbUpdateFail)
	}
	return nil
}

func (qas *SafeProduction) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form safety.SafeProductionReport
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	data := (&safety.SafeProductionReport{}).FirstBy(form.Id)
	if data.Id == 0 {
		log.ErrorFields("SafeProductionReport.FirstBy error", map[string]interface{}{"err": nil})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	// 只有草稿和废弃的报表才能删除
	if !data.IsDeletable() {
		return response.Error(rsp, "OP7521")
	}
	err := (&form).Delete()
	if err != nil {
		return response.Error(rsp, response.DbDeleteFail)
	}
	return response.Success(rsp, nil)
}

func (qas *SafeProduction) Summary(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form SafeProduction
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Month == nil {
		return response.Error(rsp, response.ParamsInvalid)
	}
	corporationIds := service.AuthCorporationIdProvider(ctx, form.CorporationIds)
	data, err := (&safety.SafeProductionReport{}).Summary(corporationIds, form.Month)
	if err != nil {
		log.ErrorFields("SafeProductionReport Summary error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, data)
}

// IsExistThisMonth 26 - 25
func (qas *SafeProduction) IsExistThisMonth(ctx context.Context, req *api.Request, rsp *api.Response) error {
	user := auth.User(ctx).GetUser()
	var form safety.SafeProductionReport
	now := time.Now()
	//  判断边界26号是边界
	limit26 := time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local)
	var month time.Time
	if now.Unix() < limit26.Unix() {
		month = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)
	} else {
		month = time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, time.Local)
	}
	form.ReportMonth = model.LocalTime(month)
	form.Corporations.Build(user.CorporationId)
	isExist := (&form).IsExist()
	return response.Success(rsp, isExist)
}
