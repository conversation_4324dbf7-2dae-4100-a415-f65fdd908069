package safety

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	settingModel "app/org/scs/erpv2/api/model/setting"
	accidentSettingModel "app/org/scs/erpv2/api/model/setting/accident"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"sort"
	"strconv"
	"strings"
	"time"
)

type TrafficAccidentReport struct {
	StartTime model.LocalTime `json:"StartTime"`
	EndTime   model.LocalTime `json:"EndTime"`
	//CateType          int64           `json:"CateType"` //事故类别 1-车内伤,2-车外伤,3-单方事故,4-倒车,5-刮擦,6-追尾,7-侧翻,8-其他
	VehicleBrokenCate int64 `json:"VehicleBrokenCate"` //车损类别 1-刮擦车损、2-倒车车损、3-追尾车损、4-侧翻车损、5-玻璃自爆与破损、6-场站刮擦（物业）、9-无车损
	PeopleHurtCate    int64 `json:"PeopleHurtCate"`    //人伤类别 1-无人伤、2-车内伤、3-车外伤、4-刮擦导致人伤、5-倒车导致人伤、6-追尾导致人伤、7-侧翻导致人伤
	CorporationId     int64 `json:"CorporationId"`
	AlarmType         int64 `json:"AlarmType"`
	LineId            int64 `json:"LineId"`
	model.Paginator
}

func (ta *TrafficAccident) YearReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentReport
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	startTime := time.Time(param.StartTime)
	endTime := time.Time(param.EndTime)
	if startTime.IsZero() {
		startTime = time.Date(time.Now().Year(), 1, 1, 0, 0, 0, 0, time.Local)
		endTime = startTime.AddDate(1, 0, 0)
	} else {
		endTime = time.Time(param.EndTime).AddDate(0, 0, 1)
	}
	if param.CorporationId == 0 {
		param.CorporationId = auth.User(ctx).GetTopCorporationId()
	}

	//所有事故数
	totalCount := (&safetyModel.TrafficAccident{}).AccidentCount(param.CorporationId, param.LineId, 0, 0, startTime, endTime)
	//结案事故数
	closedTotalCount := (&safetyModel.TrafficAccident{}).AccidentCount(param.CorporationId, param.LineId, util.StatusForTrue, 0, startTime, endTime)
	//结案率
	var closedRate float64
	if totalCount > 0 {
		closedRate, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", float64(closedTotalCount)/float64(totalCount)), 64)
	}

	//车内伤总数
	vehicleInnerBrokenCount := (&safetyModel.TrafficAccident{}).AccidentCountForCateIds(param.CorporationId, param.LineId, []int64{util.TrafficAccidentCateForOne}, nil, startTime, endTime)

	//非车内伤 且有人伤分支事故数
	cateIds := []int64{util.TrafficAccidentCateForTwo, util.TrafficAccidentCateForThree, util.TrafficAccidentCateForFour, util.TrafficAccidentCateForFive, util.TrafficAccidentCateForSix, util.TrafficAccidentCateForSeven, util.TrafficAccidentCateForEight}
	branchTypes := []int64{util.AccidentBranchTypeForSidePeopleHurt, util.AccidentBranchTypeForSelfPeopleHurt}
	vehicleInnerNotBrokenCount := (&safetyModel.TrafficAccident{}).AccidentCountForCateIds(param.CorporationId, param.LineId, cateIds, branchTypes, startTime, endTime)

	//人伤事故总数
	peopleHurtCount := vehicleInnerBrokenCount + vehicleInnerNotBrokenCount

	//无责事故总数
	notLiabilityCount := (&safetyModel.TrafficAccident{}).AccidentCount(param.CorporationId, param.LineId, 0, util.AccidentLiabilityTypeForNull, startTime, endTime)

	//维修总金额
	fixMoney := (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).SumAccidentFixMoney(param.CorporationId, param.LineId, startTime, endTime)

	//借款总金额
	lendMoney := (&safetyModel.TrafficAccidentLendMoneyRecord{}).SumAccidentLendMoney(param.CorporationId, param.LineId, startTime, endTime)

	//退款总金额
	drawbackMoney := (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).SumAccidentDrawbackMoney(param.CorporationId, param.LineId, startTime, endTime)

	//损失金额
	lossMoney := fixMoney + lendMoney - drawbackMoney

	closedMoney := (&safetyModel.TrafficAccident{}).AccidentClosedMoney(param.CorporationId, param.LineId, startTime, endTime)

	return response.Success(rsp, map[string]interface{}{
		"TotalCount":                 totalCount,
		"ClosedRate":                 closedRate,
		"VehicleInnerBrokenCount":    vehicleInnerBrokenCount,
		"VehicleInnerNotBrokenCount": vehicleInnerNotBrokenCount,
		"PeopleHurtCount":            peopleHurtCount,
		"NotLiabilityCount":          notLiabilityCount,
		"LossMoney":                  lossMoney,
		"FixMoney":                   fixMoney,
		"LendMoney":                  lendMoney,
		"ClosedMoney":                closedMoney,
	})
}

func buildReportTime(ctx context.Context, req *api.Request) (time.Time, time.Time, int64, int64, error) {
	var param TrafficAccidentReport
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return time.Time{}, time.Time{}, 0, 0, err
	}

	startTime := time.Time(param.StartTime)
	endTime := time.Time(param.EndTime)
	if startTime.IsZero() || endTime.IsZero() {
		return time.Time{}, time.Time{}, 0, 0, errors.New("time param is missing")
	}
	endTime = endTime.AddDate(0, 0, 1)

	if param.CorporationId == 0 {
		param.CorporationId = auth.User(ctx).GetTopCorporationId()
	}

	return startTime, endTime, param.CorporationId, param.LineId, nil
}

func (ta *TrafficAccident) CateReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, lineId, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	countItems := (&safetyModel.TrafficAccident{}).AccidentCountGroupByCate(corporationId, lineId, startTime, endTime)
	var countMap = make(map[int64]int64)
	for i := range countItems {
		countMap[countItems[i].GroupItem] = countItems[i].Count
	}

	for cate := 1; cate <= util.TrafficAccidentCateForNine; cate++ {
		if _, ok := countMap[int64(cate)]; !ok {
			countItems = append(countItems, safetyModel.AccidentCountGroupItem{GroupItem: int64(cate)})
		}
	}

	return response.Success(rsp, countItems)
}

func (ta *TrafficAccident) FleetReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, _, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	countItems := (&safetyModel.TrafficAccident{}).AccidentCountGroupByFleetId(corporationId, 0, startTime, endTime)
	var countMap = make(map[int64]int64)
	for i := range countItems {
		countMap[countItems[i].GroupItem] = countItems[i].Count
	}

	var results []safetyModel.AccidentCountGroupItem
	//获取所有车队
	fleets := service.GetFleet(config.Config.StaffArchiveReportCorpId)

	for i := range fleets {
		if _, ok := countMap[fleets[i].Id]; ok {
			results = append(results, safetyModel.AccidentCountGroupItem{
				GroupItem: fleets[i].Id,
				ItemName:  fleets[i].Name,
				Count:     countMap[fleets[i].Id],
			})
		} else {
			results = append(results, safetyModel.AccidentCountGroupItem{
				GroupItem: fleets[i].Id,
				ItemName:  fleets[i].Name,
			})
		}
	}

	return response.Success(rsp, results)
}

func (ta *TrafficAccident) BranchCompanyReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, _, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	countItems := (&safetyModel.TrafficAccident{}).AccidentCountGroupByBranchId(corporationId, startTime, endTime)
	var countMap = make(map[int64]int64)
	for i := range countItems {
		countMap[countItems[i].GroupItem] = countItems[i].Count
	}

	var results []safetyModel.AccidentCountGroupItem
	//获取所有分公司
	branches := service.GetBranchCompany(config.Config.StaffArchiveReportCorpId)

	for i := range branches {
		if _, ok := countMap[branches[i].Id]; ok {
			results = append(results, safetyModel.AccidentCountGroupItem{
				GroupItem: branches[i].Id,
				ItemName:  branches[i].Name,
				Count:     countMap[branches[i].Id],
			})
		} else {
			var hasFleet bool
			//判断是否有车队
			corporations := rpc.CorporationList(context.Background(), branches[i].Id)
			for j := range corporations {
				if corporations[j].Type == util.CorporationTypeForFleet {
					hasFleet = true
					break
				}
			}
			if hasFleet {
				results = append(results, safetyModel.AccidentCountGroupItem{
					GroupItem: branches[i].Id,
					ItemName:  branches[i].Name,
				})
			}
		}
	}

	return response.Success(rsp, results)
}

func (ta *TrafficAccident) HourReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, lineId, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	countItems := (&safetyModel.TrafficAccident{}).AccidentCountGroupByHour(corporationId, lineId, startTime, endTime)
	var countMap = make(map[int64]int64)
	for i := range countItems {
		countMap[countItems[i].GroupItem] = countItems[i].Count
	}

	for hour := 0; hour < 24; hour++ {
		if _, ok := countMap[int64(hour)]; !ok {
			countItems = append(countItems, safetyModel.AccidentCountGroupItem{
				GroupItem: int64(hour),
			})
		}
	}
	sort.SliceStable(countItems, func(i, j int) bool {
		return countItems[i].GroupItem < countItems[j].GroupItem
	})

	return response.Success(rsp, countItems)
}

func (ta *TrafficAccident) WeatherReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, lineId, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	countItems := (&safetyModel.TrafficAccident{}).AccidentCountGroupByWeatherCondition(corporationId, lineId, startTime, endTime)
	var countMap = make(map[int64]int64)
	for i := range countItems {
		countMap[countItems[i].GroupItem] = countItems[i].Count
	}

	for weather := 1; weather <= 8; weather++ {
		if _, ok := countMap[int64(weather)]; !ok {
			countItems = append(countItems, safetyModel.AccidentCountGroupItem{
				GroupItem: int64(weather),
			})
		}
	}
	sort.SliceStable(countItems, func(i, j int) bool {
		return countItems[i].GroupItem < countItems[j].GroupItem
	})

	return response.Success(rsp, countItems)
}

func (ta *TrafficAccident) HappenLocationReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, lineId, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}
	var param TrafficAccidentReport
	_ = json.Unmarshal([]byte(req.Body), &param)

	items := (&safetyModel.TrafficAccident{}).AccidentHappenLocation(corporationId, lineId, param.VehicleBrokenCate, param.PeopleHurtCate, startTime, endTime)
	return response.Success(rsp, items)
}

func (ta *TrafficAccident) HappenLocationReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, lineId, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}
	var param TrafficAccidentReport
	_ = json.Unmarshal([]byte(req.Body), &param)

	items := (&safetyModel.TrafficAccident{}).AccidentHappenLocation(corporationId, lineId, param.VehicleBrokenCate, param.PeopleHurtCate, startTime, endTime)
	for i := range items {
		_, items[i].CorporationName = items[i].Corporations.GetCorporation()
	}

	return response.Success(rsp, items)
}

type BranchFleetReportItem struct {
	CorporationId        int64                   `json:"CorporationId"`        //机构ID
	CorporationName      string                  `json:"CorporationName"`      //机构名称
	TotalCount           int64                   `json:"TotalCount"`           //事故总数
	ClosedTotalCount     int64                   `json:"ClosedTotalCount"`     //结案事故总数
	LossMoney            int64                   `json:"LossMoney"`            //损失金额
	FixMoney             int64                   `json:"FixMoney"`             //维修总费用
	LendMoney            int64                   `json:"LendMoney"`            //借款总金额
	DrawbackMoney        int64                   `json:"DrawbackMoney"`        //退款总金额
	ProcessWarningCount  int64                   `json:"ProcessWarningCount"`  //流程预警数
	ProcessAlarmCount    int64                   `json:"ProcessAlarmCount"`    //流程告警数
	AccidentWarningCount int64                   `json:"AccidentWarningCount"` //事故预警数
	AccidentAlarmCount   int64                   `json:"AccidentAlarmCount"`   //事故告警数
	Children             []BranchFleetReportItem `json:"Children"`
}

func (ta *TrafficAccident) BranchFleetReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return branchFleetReport(ctx, req, rsp)
}

func (ta *TrafficAccident) BranchFleetReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return branchFleetReport(ctx, req, rsp)
}

func branchFleetReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, _, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}
	//事故数量
	countItems := (&safetyModel.TrafficAccident{}).AccidentCountGroupByFleetId(corporationId, 0, startTime, endTime)
	var countMap = make(map[int64]int64)
	for i := range countItems {
		countMap[countItems[i].GroupItem] = countItems[i].Count
	}

	//结案事故数量
	closedCountItems := (&safetyModel.TrafficAccident{}).AccidentCountGroupByFleetId(corporationId, util.StatusForTrue, startTime, endTime)
	var closedCountMap = make(map[int64]int64)
	for i := range closedCountItems {
		closedCountMap[closedCountItems[i].GroupItem] = closedCountItems[i].Count
	}

	//维修金额
	fixMoneyItems := (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).SumAccidentFixMoneyGroupByFleet(corporationId, startTime, endTime)
	var fixMoneyMap = make(map[int64]int64)
	for i := range fixMoneyItems {
		fixMoneyMap[fixMoneyItems[i].CorporationId] = fixMoneyItems[i].TotalMoney
	}

	//借款金额
	lendMoneyItems := (&safetyModel.TrafficAccidentLendMoneyRecord{}).SumAccidentLendMoneyGroupByFleet(corporationId, startTime, endTime)
	var lendMoneyMap = make(map[int64]int64)
	for i := range lendMoneyItems {
		lendMoneyMap[lendMoneyItems[i].CorporationId] = lendMoneyItems[i].TotalMoney
	}

	//退款金额
	drawbackMoneyItems := (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).SumAccidentDrawbackMoneyGroupByFleet(corporationId, startTime, endTime)
	var drawbackMoneyMap = make(map[int64]int64)
	for i := range drawbackMoneyItems {
		drawbackMoneyMap[drawbackMoneyItems[i].CorporationId] = drawbackMoneyItems[i].TotalMoney
	}

	var branchFleetReportItems []BranchFleetReportItem
	branchFleets := service.BuildBranchAndFleet(config.Config.StaffArchiveReportCorpId)
	for i := range branchFleets {
		var branchItem = BranchFleetReportItem{
			CorporationId:   branchFleets[i].Id,
			CorporationName: branchFleets[i].Name,
		}

		for j := range branchFleets[i].Fleets {
			var fleetItem = BranchFleetReportItem{
				CorporationId:   branchFleets[i].Fleets[j].Id,
				CorporationName: branchFleets[i].Fleets[j].Name,
			}
			//事故数
			if _, ok := countMap[branchFleets[i].Fleets[j].Id]; ok {
				fleetItem.TotalCount = countMap[branchFleets[i].Fleets[j].Id]
				branchItem.TotalCount += fleetItem.TotalCount
			}
			//结案事故数
			if _, ok := closedCountMap[branchFleets[i].Fleets[j].Id]; ok {
				fleetItem.ClosedTotalCount = closedCountMap[branchFleets[i].Fleets[j].Id]
				branchItem.ClosedTotalCount += fleetItem.ClosedTotalCount
			}

			//维修金额
			if _, ok := fixMoneyMap[branchFleets[i].Fleets[j].Id]; ok {
				fleetItem.FixMoney = fixMoneyMap[branchFleets[i].Fleets[j].Id]
				branchItem.FixMoney += fleetItem.FixMoney
			}

			//借款金额
			if _, ok := lendMoneyMap[branchFleets[i].Fleets[j].Id]; ok {
				fleetItem.LendMoney = lendMoneyMap[branchFleets[i].Fleets[j].Id]
				branchItem.LendMoney += fleetItem.LendMoney
			}

			//退款金额
			if _, ok := drawbackMoneyMap[branchFleets[i].Fleets[j].Id]; ok {
				fleetItem.DrawbackMoney = drawbackMoneyMap[branchFleets[i].Fleets[j].Id]
				branchItem.DrawbackMoney += fleetItem.DrawbackMoney
			}

			fleetItem.LossMoney = fleetItem.FixMoney + fleetItem.LendMoney - fleetItem.DrawbackMoney
			branchItem.LossMoney += fleetItem.LossMoney

			//查询流程预警（事故结案、分支结案、事故付款、事故借款、事故退款）
			processIds := (&safetyModel.TrafficAccident{}).GetProcessIdWithAccidentRelate(fleetItem.CorporationId, startTime, endTime)
			fleetItem.ProcessWarningCount = (&settingModel.ProcessTimeoutAlarmRecord{}).CountByFormInstanceIds(processIds, util.AlarmTypeForWarning)
			fleetItem.ProcessAlarmCount = (&settingModel.ProcessTimeoutAlarmRecord{}).CountByFormInstanceIds(processIds, util.AlarmTypeForAlarm)

			branchItem.ProcessWarningCount += fleetItem.ProcessWarningCount
			branchItem.ProcessAlarmCount += fleetItem.ProcessAlarmCount

			accidentIds := (&safetyModel.TrafficAccident{}).GetAccidentId("FleetId", fleetItem.CorporationId, startTime, endTime)
			fleetItem.AccidentWarningCount = (&accidentSettingModel.AccidentTimeoutAlarmRecord{}).CountByAccidentIds(accidentIds, util.AlarmTypeForWarning)
			fleetItem.AccidentAlarmCount = (&accidentSettingModel.AccidentTimeoutAlarmRecord{}).CountByAccidentIds(accidentIds, util.AlarmTypeForAlarm)

			branchItem.AccidentWarningCount += fleetItem.AccidentWarningCount
			branchItem.AccidentAlarmCount += fleetItem.AccidentAlarmCount

			branchItem.Children = append(branchItem.Children, fleetItem)
		}
		branchFleetReportItems = append(branchFleetReportItems, branchItem)
	}

	return response.Success(rsp, branchFleetReportItems)
}

func (ta *TrafficAccident) CorporationAccidentList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, _, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if corporationId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var param TrafficAccidentReport
	_ = json.Unmarshal([]byte(req.Body), &param)

	result := accidentReportList(ctx, param, startTime, endTime, false)

	return response.Success(rsp, result)
}

func (ta *TrafficAccident) CorporationAccidentListExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, _, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if corporationId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var param TrafficAccidentReport
	_ = json.Unmarshal([]byte(req.Body), &param)

	result := accidentReportList(ctx, param, startTime, endTime, false)

	return response.Success(rsp, result)
}

func (ta *TrafficAccident) CorporationAccidentMoneyList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, _, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if corporationId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var param TrafficAccidentReport
	_ = json.Unmarshal([]byte(req.Body), &param)

	result := accidentReportList(ctx, param, startTime, endTime, true)

	return response.Success(rsp, result)
}

func (ta *TrafficAccident) CorporationAccidentMoneyListExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, _, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if corporationId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var param TrafficAccidentReport
	_ = json.Unmarshal([]byte(req.Body), &param)

	result := accidentReportList(ctx, param, startTime, endTime, true)

	return response.Success(rsp, result)
}

type AccidentReportListItem struct {
	safetyModel.TrafficAccident
	LossTotalMoney     int64 `json:"LossTotalMoney"`
	FixTotalMoney      int64 `json:"FixTotalMoney"`
	LendTotalMoney     int64 `json:"LendTotalMoney"`
	DrawbackTotalMoney int64 `json:"DrawbackTotalMoney"`
}

func accidentReportList(ctx context.Context, param TrafficAccidentReport, startTime, endTime time.Time, isReturnMoney bool) map[string]interface{} {
	corporation := rpc.GetCorporationById(ctx, param.CorporationId)
	if corporation == nil {
		return map[string]interface{}{}
	}

	var column = "FleetId"
	if corporation.Type == util.CorporationTypeForBranch {
		column = "BranchId"
	}
	accidents, count := (&safetyModel.TrafficAccident{}).GetAccidentsForReport(column, param.CorporationId, startTime, endTime, param.Paginator)

	var items []AccidentReportListItem
	for i := range accidents {
		var item = AccidentReportListItem{
			TrafficAccident: accidents[i],
		}
		if corporation.Type == util.CorporationTypeForFleet {
			item.CorporationName = corporation.Name
			item.CorporationId = corporation.Id
		} else {
			fleet := rpc.GetCorporationById(ctx, accidents[i].FleetId)
			if fleet != nil {
				item.CorporationName = fleet.Name
				item.CorporationId = fleet.Id
			}
		}

		if isReturnMoney {
			item.FixTotalMoney = (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).SumFixMoney(accidents[i].Id)
			item.LendTotalMoney = (&safetyModel.TrafficAccidentLendMoneyRecord{}).GetTotalMoneyByAccidentId(accidents[i].Id)
			item.DrawbackTotalMoney = (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).DoneTotalMoneyByAccidentId(accidents[i].Id)
			item.LossTotalMoney = item.FixTotalMoney + item.LendTotalMoney - item.DrawbackTotalMoney
		}

		items = append(items, item)
	}

	return map[string]interface{}{
		"Items":      items,
		"TotalCount": count,
	}
}

func (ta *TrafficAccident) CorporationAccidentAlarmList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, _, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if corporationId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var param TrafficAccidentReport
	_ = json.Unmarshal([]byte(req.Body), &param)

	result := accidentWarningAndAlarmList(ctx, param, startTime, endTime)

	return response.Success(rsp, result)
}

func (ta *TrafficAccident) CorporationAccidentAlarmListExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, _, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if corporationId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var param TrafficAccidentReport
	_ = json.Unmarshal([]byte(req.Body), &param)

	result := accidentWarningAndAlarmList(ctx, param, startTime, endTime)

	return response.Success(rsp, result)
}

func accidentWarningAndAlarmList(ctx context.Context, param TrafficAccidentReport, startTime, endTime time.Time) map[string]interface{} {
	corporation := rpc.GetCorporationById(ctx, param.CorporationId)
	if corporation == nil {
		return map[string]interface{}{}
	}

	var column = "FleetId"
	if corporation.Type == util.CorporationTypeForBranch {
		column = "BranchId"
	}
	accidentIds := (&safetyModel.TrafficAccident{}).GetAccidentId(column, param.CorporationId, startTime, endTime)

	items, count := (&accidentSettingModel.AccidentTimeoutAlarmRecord{}).GetByAccidentIds(accidentIds, param.AlarmType, param.Paginator)

	return map[string]interface{}{
		"Items":      items,
		"TotalCount": count,
	}
}

func (ta *TrafficAccident) CorporationProcessAlarmList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, _, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if corporationId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var param TrafficAccidentReport
	_ = json.Unmarshal([]byte(req.Body), &param)

	result := processWarningAndAlarmList(ctx, param, startTime, endTime)

	return response.Success(rsp, result)
}

func (ta *TrafficAccident) CorporationProcessAlarmListExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, _, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if corporationId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var param TrafficAccidentReport
	_ = json.Unmarshal([]byte(req.Body), &param)

	result := processWarningAndAlarmList(ctx, param, startTime, endTime)

	return response.Success(rsp, result)
}

func processWarningAndAlarmList(ctx context.Context, param TrafficAccidentReport, startTime, endTime time.Time) map[string]interface{} {
	corporation := rpc.GetCorporationById(ctx, param.CorporationId)
	if corporation == nil {
		return map[string]interface{}{}
	}

	//var column = "FleetId"
	//if corporation.Type == util.CorporationTypeForBranch {
	//	column = "BranchId"
	//}
	processIds := (&safetyModel.TrafficAccident{}).GetProcessIdWithAccidentRelate(param.CorporationId, startTime, endTime)

	items, count := (&settingModel.ProcessTimeoutAlarmRecord{}).GetByFormInstanceIds(processIds, param.AlarmType, param.Paginator)

	return map[string]interface{}{
		"Items":      items,
		"TotalCount": count,
	}
}

type NotHasDiffMoneyReportItem struct {
	Id                int64           `json:"Id"`
	CorporationName   string          `json:"CorporationName"`
	HappenAt          model.LocalTime `json:"HappenAt"`
	LineId            int64           `json:"LineId"`
	LineName          string          `json:"LineName"`
	License           string          `json:"License"`
	Code              string          `json:"Code"`
	VehicleBrokenCate int64           `json:"VehicleBrokenCate"`
	PeopleHurtCate    int64           `json:"PeopleHurtCate"`
	GetMoney          int64           `json:"GetMoney"`
	PaymentFlow       string          `json:"PaymentFlow"`
	PaymentMoney      int64           `json:"PaymentMoney"`
	ClosedAt          model.LocalTime `json:"ClosedAt"`
}

// NotHasDiffMoneyReport 无差额报表
func (ta *TrafficAccident) NotHasDiffMoneyReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	startTime, endTime, corporationId, _, err := buildReportTime(ctx, req)
	if err != nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	var param TrafficAccidentReport
	_ = json.Unmarshal([]byte(req.Body), &param)
	accidents, count := (&safetyModel.TrafficAccident{}).GetNotHasDiffMoneyAccident(corporationId, startTime, endTime, param.Paginator)

	var items []NotHasDiffMoneyReportItem
	for _, accident := range accidents {
		_, corporationName := accident.GetCorporation()
		var item = NotHasDiffMoneyReportItem{
			Id:                accident.Id,
			CorporationName:   corporationName,
			HappenAt:          accident.HappenAt,
			LineId:            accident.LineId,
			LineName:          accident.LineName,
			License:           accident.License,
			Code:              accident.Code,
			PeopleHurtCate:    accident.PeopleHurtCate,
			VehicleBrokenCate: accident.VehicleBrokenCate,
			ClosedAt:          accident.ClosedAt,
			GetMoney:          accident.InsuranceCompanyPayMoney,
			PaymentMoney:      accident.LossMoney,
		}
		var payFlows []string
		paymentRecords := (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).GetDoneByAccidentId(accident.Id)
		if len(paymentRecords) > 0 {
			if paymentRecords[0].IsFull == util.StatusForTrue {
				item.GetMoney += paymentRecords[0].PaymentMoney
			} else {
				//查询分支
				var branch safetyModel.TrafficAccidentRelaterBranch
				_ = branch.FindBy(paymentRecords[0].TrafficAccidentRelaterBranchId)
				item.GetMoney += branch.InsuranceCompanyPayMoney
			}
			payFlows = append(payFlows, paymentRecords[0].FlowTo)
			item.PaymentMoney += paymentRecords[0].FixMoney
		}

		lendMoneyRecords := (&safetyModel.TrafficAccidentLendMoneyRecord{}).GetDoneByAccidentId(accident.Id)
		for i := range lendMoneyRecords {
			payFlows = append(payFlows, lendMoneyRecords[i].FlowTo)
		}

		item.PaymentFlow = strings.Join(payFlows, "；")

		items = append(items, item)
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      items,
		"TotalCount": count,
	})
}

func (ta *TrafficAccident) NotHasDiffMoneyReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return ta.NotHasDiffMoneyReport(ctx, req, rsp)
}
