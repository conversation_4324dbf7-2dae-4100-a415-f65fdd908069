package safety

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/export"
	protoDoorCheck "app/org/scs/erpv2/api/proto/rpc/doorcheck"
	"app/org/scs/erpv2/api/service/auth"
	exportService "app/org/scs/erpv2/api/service/export"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"strings"
	"time"
)

type VehicleCheckHandler struct {
	Id               int64
	Code             string          `json:"Code"`
	VehicleIds       []int64         `json:"VehicleIds"`
	TopCorporationId int64           `json:"TopCorporationId"`
	CorporationId    int64           `json:"CorporationId"`
	StaffIds         []int64         `json:"StaffIds"`
	StartAt          model.LocalTime `json:"StartAt"`
	EndAt            model.LocalTime `json:"EndAt"`
	CheckType        int64           `json:"CheckType"`   // 提交类型: 0:全部; 1:进场; 2:出场
	Occupation       int64           `json:"Occupation"`  // 岗位类型：0-司机; 1-乘务员;2-管理员; 3-辅工;  4-辅岗; 5-干部; 6-仓管人员;7-安保人员; 8-修理工 10-其他
	DealStatus       int64           `json:"DealStatus"`  // 处理状态: 1:未处理;2:处理中;3:已处理
	CheckStatus      int64           `json:"CheckStatus"` // 检查状态: 0:全部;1:正常;2:异常
	model.Paginator
}

type VehicleCheckDetailItem struct {
	Id                     int64           `json:"Id"`                     // 主键ID
	Code                   string          `json:"Code"`                   // 编号
	License                string          `json:"License"`                // 车牌号
	Corporation            string          `json:"Corporation"`            // 车辆所属机构
	PlanScheduleStaffNames []string        `json:"PlanScheduleStaffNames"` // 排班司机
	StaffName              string          `json:"StaffName"`              // 提交人
	Occupation             int64           `json:"Occupation"`             // 岗位类型：0-司机; 1-乘务员;2-管理员; 3-辅工;  4-辅岗; 5-干部; 6-仓管人员;7-安保人员; 8-修理工 10-其他
	CheckType              int64           `json:"CheckType"`              // 门检类型: 1:进场; 2:出场
	AbLabelCount           int64           `json:"AbLabelCount"`           // 异常标签数
	DealLabelCount         int64           `json:"DealLabelCount"`         // 已处理的标签数
	DealStatus             int64           `json:"DealStatus"`             // 处理状态: 1:未处理;2:处理中;3:已处理
	CheckStatus            int64           `json:"CheckStatus"`            // 检查状态: 1:正常;2:异常
	AllowOperation         int64           `json:"AllowOperation"`         // 是否允许运营: 1:允许;2:不允许
	CloseStatus            int64           `json:"CloseStatus"`            // 关闭状态: 1:未关闭;2:已关闭
	CreatedAtTime          model.LocalTime `json:"CreatedAtTime"`
}

func (h *VehicleCheckHandler) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleCheckHandler
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()
	results, errCode := vehicleCheckList(ctx, param)
	if errCode != response.SUCCESS {
		return response.Error(rsp, errCode)
	}

	return response.Success(rsp, results)
}

func (h *VehicleCheckHandler) ListExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleCheckHandler
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()
	var startAt = time.Time(param.StartAt)
	var endAt = time.Time(param.EndAt)
	var fileName = fmt.Sprintf("门检记录%s-%s.xlsx", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))
	paramByte, _ := json.Marshal(param)
	exportFileRecord, err := exportService.CreateExportFileRecord(auth.User(ctx).GetUserId(), fileName, exportService.VehicleCheckResultExportScene, paramByte, startAt, endAt)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	go ExportVehicleCheckResult(exportFileRecord, param)
	return response.Success(rsp, nil)
}

// AbnormalList 异常列表
func (h *VehicleCheckHandler) AbnormalList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleCheckHandler
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()
	param.CheckStatus = 2

	results, errCode := vehicleCheckList(ctx, param)
	if errCode != response.SUCCESS {
		return response.Error(rsp, errCode)
	}

	return response.Success(rsp, results)
}

func (h *VehicleCheckHandler) AbnormalListExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleCheckHandler
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()
	param.CheckStatus = 2

	var startAt = time.Time(param.StartAt)
	var endAt = time.Time(param.EndAt)
	var fileName = fmt.Sprintf("门检异常记录%s-%s.xlsx", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))
	paramByte, _ := json.Marshal(param)
	exportFileRecord, err := exportService.CreateExportFileRecord(auth.User(ctx).GetUserId(), fileName, exportService.VehicleCheckAbnormalResultExportScene, paramByte, startAt, endAt)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	go ExportVehicleCheckResult(exportFileRecord, param)
	return response.Success(rsp, nil)
}

type VehicleCheckListResult struct {
	Items      []VehicleCheckDetailItem `json:"Items"`
	TotalCount int64                    `json:"TotalCount"`
}

func vehicleCheckList(ctx context.Context, param VehicleCheckHandler) (VehicleCheckListResult, string) {
	var requestParam = protoDoorCheck.GetResultsRequest{
		TopCorporationId: param.TopCorporationId,
		Code:             param.Code,
		CorporationId:    param.CorporationId,
		StaffIds:         param.StaffIds,
		SubmitStartAt:    param.StartAt.ToTime().Unix(),
		SubmitEndAt:      param.EndAt.ToTime().Unix(),
		VehicleIds:       param.VehicleIds,
		CheckType:        param.CheckType,
		Occupation:       param.Occupation,
		DealStatus:       param.DealStatus,
		CheckStatus:      param.CheckStatus,
		Offset:           int64(param.Offset),
		Limit:            int64(param.Limit),
		Order:            "DESC",
	}

	results := rpc.GetDoorCheckResults(ctx, requestParam)
	if results == nil {
		return VehicleCheckListResult{}, response.FAIL
	}
	var items []VehicleCheckDetailItem
	for i := range results.Items {
		at := model.LocalTime(time.Unix(results.Items[i].CreatedAt, 0))
		items = append(items, VehicleCheckDetailItem{
			Id:                     results.Items[i].Id,
			Code:                   results.Items[i].Code,
			License:                results.Items[i].License,
			Corporation:            results.Items[i].Corporation,
			PlanScheduleStaffNames: results.Items[i].PlanScheduleStaffNames,
			StaffName:              results.Items[i].StaffName,
			Occupation:             results.Items[i].Occupation,
			CheckType:              results.Items[i].CheckType,
			AbLabelCount:           results.Items[i].AbLabelCount,
			DealLabelCount:         results.Items[i].DealLabelCount,
			DealStatus:             results.Items[i].DealStatus,
			CheckStatus:            results.Items[i].CheckStatus,
			AllowOperation:         results.Items[i].AllowOperation,
			CloseStatus:            results.Items[i].CloseStatus,
			CreatedAtTime:          at,
		})
	}

	return VehicleCheckListResult{
		Items:      items,
		TotalCount: results.TotalCount,
	}, response.SUCCESS
}

type VehicleCheckResult struct {
	Id            int64                       `json:"Id"`   // 主键ID 注:Type=1 or 2时为0
	Type          int64                       `json:"Type"` // 1 车辆 2 设备 99 其他(支持增删改查)
	Name          string                      `json:"Name"` // 检查类型名称
	Items         []VehicleCheckResultItem    `json:"Items"`
	WorkPostItems []VehicleCheckWorkOrderItem `json:"WorkPostItems"`
}

type VehicleCheckResultItem struct {
	Id          int64  `json:"Id"`          // 门检记录详情ID
	ProjectName string `json:"ProjectName"` // 检查项目名称
	LabelName   string `json:"LabelName"`   // 检查项目标签名称
	Desc        string `json:"Desc"`        // 描述
	CheckStatus int64  `json:"CheckStatus"` // 检查状态: 1:正常;2:异常
	DealStatus  int64  `json:"DealStatus"`  // 处理状态: 1:未处理;2:已处理
	OpUserName  string `json:"OpUserName"`  // 处理人员
}

type VehicleCheckWorkOrderItem struct {
	Id             int64                              `json:"Id"`             // 工单ID
	Code           string                             `json:"Code"`           // 工单编号
	DeliveryStatus int64                              `json:"DeliveryStatus"` // 交车状态: 1:确认收车;2:再次维修
	Items          []VehicleCheckWorkOrderProcessItem `json:"Items"`          // 工单流程
	ConfirmAt      model.LocalTime                    `json:"ConfirmAt"`
}

type VehicleCheckWorkOrderProcessItem struct {
	Id       int64           `json:"Id"`       // 工单ID
	Status   int64           `json:"Status"`   // 工单状态: 1:未受理;2:已受理;3:维修中;4:已竣工;5:已交车
	ReportAt model.LocalTime `json:"ReportAt"` // 上报时间
	WorkShop string          `json:"WorkShop"` // 维修车间
}

func (h *VehicleCheckHandler) Show(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param VehicleCheckHandler
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var requestParam = protoDoorCheck.GetResultDetailsRequest{
		TopCorporationId:  auth.User(ctx).GetTopCorporationId(),
		DoorCheckResultId: param.Id,
		CheckStatus:       param.CheckStatus,
	}

	result := rpc.GetDoorCheckResultDetail(ctx, requestParam)
	if result == nil {
		return response.Error(rsp, response.FAIL)
	}

	var vehicleCheckDetail = VehicleCheckDetailItem{
		Id:                     result.Item.Id,
		Code:                   result.Item.Code,
		License:                result.Item.License,
		Corporation:            result.Item.Corporation,
		PlanScheduleStaffNames: result.Item.PlanScheduleStaffNames,
		StaffName:              result.Item.StaffName,
		Occupation:             result.Item.Occupation,
		CheckType:              result.Item.CheckType,
		AbLabelCount:           result.Item.AbLabelCount,
		DealLabelCount:         result.Item.DealLabelCount,
		DealStatus:             result.Item.DealStatus,
		CheckStatus:            result.Item.CheckStatus,
		AllowOperation:         result.Item.AllowOperation,
		CloseStatus:            result.Item.CloseStatus,
		CreatedAtTime:          model.LocalTime(time.Unix(result.Item.CreatedAt, 0)),
	}

	var vehicleCheckResults []VehicleCheckResult
	for _, resultItem := range result.Items {
		vehicleResult := VehicleCheckResult{
			Id:   resultItem.Id,
			Type: resultItem.Type,
			Name: resultItem.Name,
		}
		var resultItems []VehicleCheckResultItem
		for _, item := range resultItem.Items {
			resultItems = append(resultItems, VehicleCheckResultItem{
				Id:          item.Id,
				ProjectName: item.ProjectName,
				LabelName:   item.LabelName,
				Desc:        item.Desc,
				CheckStatus: item.CheckStatus,
				DealStatus:  item.DealStatus,
				OpUserName:  item.OpUserName,
			})
		}
		vehicleResult.Items = resultItems

		var workOrderItems []VehicleCheckWorkOrderItem
		for _, item := range resultItem.WorkPostItems {
			var workOrderItem = VehicleCheckWorkOrderItem{
				Id:             item.Id,
				Code:           item.Code,
				DeliveryStatus: item.DeliveryStatus,
				ConfirmAt:      model.LocalTime(time.Unix(item.ConfirmAt, 0)),
			}

			var workOrderProcessItems []VehicleCheckWorkOrderProcessItem
			for _, item2 := range item.Items {
				workOrderProcessItems = append(workOrderProcessItems, VehicleCheckWorkOrderProcessItem{
					Id:       item2.Id,
					Status:   item2.Status,
					WorkShop: item2.WorkShop,
					ReportAt: model.LocalTime(time.Unix(item2.ReportAt, 0)),
				})
			}
			workOrderItem.Items = workOrderProcessItems
			workOrderItems = append(workOrderItems, workOrderItem)
		}

		vehicleResult.WorkPostItems = workOrderItems

		vehicleCheckResults = append(vehicleCheckResults, vehicleResult)
	}

	return response.Success(rsp, map[string]interface{}{
		"Item":  vehicleCheckDetail,
		"Items": vehicleCheckResults,
	})
}

func ExportVehicleCheckResult(exportFile export.ExportFile, param VehicleCheckHandler) {
	result, errorCode := vehicleCheckList(context.TODO(), param)

	if errorCode != response.SUCCESS {
		exportFile.ErrorCode = errorCode
		err := exportFile.UpdateFail()
		log.ErrorFields("ExportVehicleCheckResult exportFile.UpdateStatus error", map[string]interface{}{"err": err})
		return
	}

	var resultMap []map[string]interface{}
	for _, item := range result.Items {
		resultByte, err := json.Marshal(item)
		if err != nil {
			return
		}
		var dataMap map[string]interface{}
		err = json.Unmarshal(resultByte, &dataMap)
		if err != nil {
			return
		}

		dataMap["CreatedAt"] = time.Time(item.CreatedAtTime).Format("2006-01-02 15:04")
		dataMap["PlanScheduleStaffNames"] = strings.Join(item.PlanScheduleStaffNames, ",")
		dataMap["Occupation"] = exportService.StaffOccupationMap[item.Occupation]
		dataMap["CheckType"] = exportService.VehicleCheckTypeMap[item.CheckType]
		dataMap["CheckStatus"] = exportService.VehicleCheckStatusMap[item.CheckStatus]
		dataMap["DealStatus"] = exportService.VehicleCheckDealStatusMap[item.DealStatus]
		dataMap["AllowOperation"] = exportService.VehicleCheckAllowOperationMap[item.AllowOperation]

		resultMap = append(resultMap, dataMap)
	}

	var err error
	if exportFile.Scene == exportService.VehicleCheckResultExportScene {
		err = exportService.ExportVehicleCheckResult(resultMap, exportFile)
	}

	if exportFile.Scene == exportService.VehicleCheckAbnormalResultExportScene {
		err = exportService.ExportVehicleCheckAbnormalResult(resultMap, exportFile)
	}

	if err != nil {
		exportFile.ErrorCode = err.Error()
		err = exportFile.UpdateFail()
	} else {
		err = exportFile.UpdateSuccess()
	}
	log.ErrorFields("ExportVehicleCheckResult exportFile.UpdateStatus error", map[string]interface{}{"err": err})
}
