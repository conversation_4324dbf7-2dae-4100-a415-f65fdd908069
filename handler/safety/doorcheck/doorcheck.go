package doorcheck

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/safety"
	protocorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	protoschedule "app/org/scs/erpv2/api/proto/rpc/iss"
	protoMini "app/org/scs/erpv2/api/proto/rpc/mini"
	protooetvehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	protoUser "app/org/scs/erpv2/api/proto/rpc/user"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/message"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	log "github.com/micro/go-micro/v2/logger"
	"strconv"
	"strings"
	"time"
)

type NoticeType int64 // 消息通知类型

const (
	ABNORMAL_1         NoticeType = 1 // 司机发起异常给管理员
	REPORT_2           NoticeType = 2 // 管理员上报
	ABNORMAL_RESOLVE_4 NoticeType = 4 // 管理员整改发给司机
	RESOLVE_8          NoticeType = 8 // 解决上报 发给上报人和司机
)

type DoorCheck struct {
	safety.DoorCheckItems
	CompanyIdStr         string // 公司id 兼容小程序
	IconFileId           int64
	NormalLabels         []string // 创建正常标签
	AbnormalLabels       []string // 创建异常标签
	AddNormalLabels      []string // 新增正常标签
	DeleteNormalLabels   []string // 删除正常标签
	AddAbnormalLabels    []string // 新增异常标签
	DeleteAbnormalLabels []string // 删除异常标签
}

type Records struct {
	safety.DoorCheckRecords
	Items             []safety.EditResultParamsItems
	IdStr             string // 门检记录id 兼容微信
	StartAt           int64
	EndAt             int64
	CorporationIds    []int64
	CorporationIdStrs []string
	LineId            int64
	License           string
	VehicleCode       string
	StaffId           int64
	Keyword           string
	CheckForms        []int64
	Results           []int64
	Statuses          []int64
	model.Paginator
}

type ItemResult struct {
	safety.DoorCheckItemResult
	safety.EditResultParamsItems
	ItemResult []ItemResultRequest
	CheckForm  int64  // 检测形式 1司机出场自检 2司机回场自检 4管理员抽检
	IdStr      string // *记录id 兼容微信
}

// 每一项门检结果
type ItemResultRequest struct {
	FkItemId    int64  // 门检项目id
	FkItemIdStr string // 门检项目id 兼容微信
	ItemName    string // 门检项目名

	NoticeUserId   int64  // 上报通知人员id
	NoticeUserName string // 上报通知人员 //前端不传
	NoticeText     string // 上报通知文本
	NoticeId       int64  // 后端生成 前端不传

	Status     int64      // 该门检项目状态 1正常 2待整改 4待解决 8已解决 16已关闭
	ResultJson ResultJson // 如果没有可以为空
}

type ResultJson struct {
	NotRectified NotRectified       // (异常)未整改的内容 | (正常)提交的内容
	Rectified    []safety.Rectified // (异常)已整改的内容 | (正常)补充提交的内容->暂无
}

type NotRectified struct {
	Labels []ResultJsonLabel
	Files  []safety.ResultJsonFile
	Remark string // 评论 留言
}

type ResultJsonLabel struct {
	FkLabelId    int64  // 标签id
	FkLabelIdStr string // 标签id 兼容微信
	Name         string // 标签名
}

type Inspector struct {
	safety.DoorCheckInspectors
	AddItems  []AddItem
	DeleteIds []int64
	StartAt   int64
	EndAt     int64
}

type AddItem struct {
	FleetId   int64
	UserItems []UserItem
}

type UserItem struct {
	DutyAt  int64
	UserIds []int64
}

// AddInspector 新增检查人员
func (d *DoorCheck) AddInspector(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Inspector
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	rpcTmpDetail := make(map[int64]*protocorporation.GetCorporationDetailByIdResponse) // map[fleetId]
	rpcTmpUser := make(map[int64]*protoUser.MainUser)                                  // map[userId]

	for _, item := range q.AddItems {
		if item.FleetId == 0 {
			log.Error("item.FleetId == 0 item == ", item)
			return response.Error(rsp, response.ParamsInvalid)
		}
		var add safety.DoorCheckInspectors
		if detail, ok := rpcTmpDetail[item.FleetId]; ok {

			if detail != nil {
				add.GroupId = detail.GroupId
				add.CompanyId = detail.CompanyId
				add.BranchId = detail.BranchId
				add.DepartmentId = detail.DepartmentId
				add.FleetId = detail.FleetId
			}
		} else {
			detailById := rpc.GetCorporationDetailById(ctx, item.FleetId)
			rpcTmpDetail[item.FleetId] = detailById
			if detailById != nil {
				add.GroupId = detailById.GroupId
				add.CompanyId = detailById.CompanyId
				add.BranchId = detailById.BranchId
				add.DepartmentId = detailById.DepartmentId
				add.FleetId = detailById.FleetId
			}
		}

		for _, userItem := range item.UserItems {
			add.DutyAt = time.Unix(userItem.DutyAt, 0)

			for _, userId := range userItem.UserIds {
				add.UserId = userId

				if user, ok := rpcTmpUser[userId]; ok {

					if user != nil {
						add.Name = user.Nickname
					}
				} else {
					user := rpc.GetUserInfoById(ctx, userId)
					rpcTmpUser[userId] = user
					if user != nil {
						add.Name = user.Nickname

					}
				}

				// 生成、重置id
				add.Id = model.Id()

				// 创建
				err = (&add).Add()
				if err != nil {
					log.Error("Add err=", err.Error())
					return response.Error(rsp, response.DbSaveFail)
				}

			}
		}
	}

	return response.Success(rsp, nil)
}

type ListInspectorResponse struct {
	DutyAt int64
	Users  []ListInspectorResponseUser
}

type ListInspectorResponseUser struct {
	UserId int64  // 人员id
	Name   string // 检查人员姓名
	Id     int64  // 检查值班id
}

// 列表检查人员
func (d *DoorCheck) ListInspector(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Inspector
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.FleetId == 0 {
		log.Error("q.FleetId == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	s := time.Unix(q.StartAt, 0)
	e := time.Unix(q.EndAt, 0)

	list, err := (&q).List(s, e)
	if err != nil {
		log.Error("List err=", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	var rspD []ListInspectorResponse

	tmpDateUsers := make(map[int64][]ListInspectorResponseUser) // map["2022-07-29 00:00:00" -> unix timestamp]
	var tmpDateUsersIdx []int64

	for _, inspectors := range list {

		dutyAt := time.Date(inspectors.DutyAt.Year(), inspectors.DutyAt.Month(), inspectors.DutyAt.Day(), 0, 0, 0, 0, time.Local).Unix()

		user := ListInspectorResponseUser{
			UserId: inspectors.UserId,
			Name:   inspectors.Name,
			Id:     inspectors.Id,
		}

		if _, ok := tmpDateUsers[dutyAt]; !ok {
			tmpDateUsersIdx = append(tmpDateUsersIdx, dutyAt)
		}

		tmpDateUsers[dutyAt] = append(tmpDateUsers[dutyAt], user)
	}

	for _, dutyAt := range tmpDateUsersIdx {
		rspD = append(rspD, ListInspectorResponse{
			DutyAt: dutyAt,
			Users:  tmpDateUsers[dutyAt],
		})
	}

	data := map[string]interface{}{
		"Items": rspD,
	}

	return response.Success(rsp, data)
}

// 编辑检查人员
func (d *DoorCheck) EditInspector(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Inspector
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	rpcTmpDetail := make(map[int64]*protocorporation.GetCorporationDetailByIdResponse) // map[fleetId]
	rpcTmpUser := make(map[int64]*protoUser.MainUser)                                  // map[userId]

	for _, item := range q.AddItems {
		if item.FleetId == 0 {
			log.Error("item.FleetId == 0 item == ", item)
			return response.Error(rsp, response.ParamsInvalid)
		}
		var add safety.DoorCheckInspectors
		if detail, ok := rpcTmpDetail[item.FleetId]; ok {

			if detail != nil {
				add.GroupId = detail.GroupId
				add.CompanyId = detail.CompanyId
				add.BranchId = detail.BranchId
				add.DepartmentId = detail.DepartmentId
				add.FleetId = detail.FleetId
			}
		} else {
			detailById := rpc.GetCorporationDetailById(ctx, item.FleetId)
			rpcTmpDetail[item.FleetId] = detailById
			if detailById != nil {
				add.GroupId = detailById.GroupId
				add.CompanyId = detailById.CompanyId
				add.BranchId = detailById.BranchId
				add.DepartmentId = detailById.DepartmentId
				add.FleetId = detailById.FleetId
			}
		}

		for _, userItem := range item.UserItems {
			add.DutyAt = time.Unix(userItem.DutyAt, 0)

			for _, userId := range userItem.UserIds {
				add.UserId = userId

				if user, ok := rpcTmpUser[userId]; ok {
					if user != nil {
						add.Name = user.Nickname
					}
				} else {
					user := rpc.GetUserInfoById(ctx, userId)
					rpcTmpUser[userId] = user
					if user != nil {
						add.Name = user.Nickname
					}
				}

				// 生成、重置id
				add.Id = model.Id()

				// 创建
				err = (&add).Add()
				if err != nil {
					log.Error("Add err=", err.Error())
					return response.Error(rsp, response.DbSaveFail)
				}

			}
		}
	}

	err = (&q).DeleteByIds(q.DeleteIds)
	if err != nil {
		log.Error("DeleteByIds err=", err.Error())
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

// 新增门检类别
func (d *DoorCheck) AddItemType(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q DoorCheck
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.CompanyId == 0 || (q.WorkPostType != 1 && q.WorkPostType != 3) {
		log.Error("q.CompanyId == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	corporationType := rpc.GetCorporationDetailById(ctx, q.BranchId)
	if corporationType != nil {
		q.GroupId = corporationType.GroupId
		//q.CompanyId = corporationType.CompanyId
		q.BranchId = corporationType.BranchId
		q.DepartmentId = corporationType.DepartmentId
		q.FleetId = corporationType.FleetId
	}

	// 设置门检类别
	q.ItemType = safety.CHECK_TYPE_1

	// 设置pid path
	id := model.Id()
	q.Id = id
	q.ParentIdPath = util.SliceItoA([]int64{id})

	err = (&q.DoorCheckItems).Add()
	if err != nil {
		log.Error("DoorCheckItems.Add()", err.Error())
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

// EditItemType 编辑门检类别  暂定不允许编辑
func (d *DoorCheck) EditItemType(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q DoorCheck
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	return response.Success(rsp, nil)

}

// DeleteItemType 删除门检类别
func (d *DoorCheck) DeleteItemType(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q DoorCheck
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = (&q.DoorCheckItems).Delete()
	if err != nil {
		log.Error("DoorCheckItems.Delete()", err.Error())
		return response.Error(rsp, response.DbDeleteFail)
	}
	return response.Success(rsp, nil)
}

// Add 自定义门检项目创建
func (d *DoorCheck) Add(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q DoorCheck
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.CompanyId == 0 || (q.WorkPostType != 1 && q.WorkPostType != 3) || q.ParentId == 0 {
		log.Error("q.CompanyId == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	corporationType := rpc.GetCorporationDetailById(ctx, q.BranchId)
	if corporationType != nil {
		q.GroupId = corporationType.GroupId
		//q.CompanyId = corporationType.CompanyId
		q.BranchId = corporationType.BranchId
		q.DepartmentId = corporationType.DepartmentId
		q.FleetId = corporationType.FleetId
	}

	q.DoorCheckItems.FkIconFileId = q.IconFileId

	err = (&q.DoorCheckItems).AddItemsAndLabels(q.NormalLabels, q.AbnormalLabels)
	if err != nil {
		log.Error("DoorCheckItems.AddItemsAndLabels", err.Error())
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

// List 自定义门检项目列表
func (d *DoorCheck) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q DoorCheck
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	list, err := (&q.DoorCheckItems).List()
	if err != nil {
		log.Error("DoorCheckItems.List()", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	data := map[string]interface{}{
		"Items": list,
	}

	return response.Success(rsp, data)

}

// 自定义门检项目编辑
func (d *DoorCheck) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q DoorCheck
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	// 去重
	q.AddAbnormalLabels = util.RemoveRepeatByMap(q.AddAbnormalLabels)
	q.AddNormalLabels = util.RemoveRepeatByMap(q.AddNormalLabels)
	q.DeleteAbnormalLabels = util.RemoveRepeatByMap(q.DeleteAbnormalLabels)
	q.DeleteNormalLabels = util.RemoveRepeatByMap(q.DeleteNormalLabels)

	// 新增标签数据库查重
	for _, label := range q.AddNormalLabels {
		if (&q.DoorCheckItems).FindName(1, label) {
			log.Error("已存在")
			return response.Error(rsp, "OP7501")
		}
	}

	for _, label := range q.AddAbnormalLabels {
		if (&q.DoorCheckItems).FindName(2, label) {
			log.Error("已存在")
			return response.Error(rsp, "OP7501")
		}
	}

	err = (&q.DoorCheckItems).Edit(q.DeleteNormalLabels, q.DeleteAbnormalLabels, q.AddNormalLabels, q.AddAbnormalLabels)
	if err != nil {
		log.Error("DoorCheckItems.Edit err=", err.Error())
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)
}

// 自定义门检项目删除
func (d *DoorCheck) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q DoorCheck
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsMissing)
	}
	err = (q.DoorCheckItems).DeleteItemAndLabelWithId()
	if err != nil {
		log.Error("doorcheck.DeleteRequest err=", err.Error())
		return response.Error(rsp, response.DbDeleteFail)
	}
	return response.Success(rsp, nil)
}

// TodoList 门检项目(待检查)列表 根据当前 该接口目前只在wx小程序上使用
func (d *DoorCheck) TodoList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q DoorCheck
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 获取配置门检机构
	//y := config.GetConfig()
	//level := y.DoorCheckCorporationLevel

	var occupation int64 = -1
	// 获取当前用户的StaffId

	staffItem := rpc.GetStaffWithPhone(ctx, auth.User(ctx).GetTopCorporationId(), auth.User(ctx).GetPhone())
	if staffItem != nil {
		occupation = staffItem.Occupation
	}

	if occupation == 0 { // 司机
		q.WorkPostType = 3
	} else if occupation == 2 || occupation == 5 { // 管理员、干部
		q.WorkPostType = 1
	} else {
		log.Error("身份不是司机或管理员无法门检")
		return response.Error(rsp, "OP7507")
	}

	if q.CompanyId == 0 && q.CompanyIdStr == "" {
		detailById := rpc.GetCorporationDetailById(ctx, auth.User(ctx).GetCorporationId())
		if detailById == nil {
			log.Error("rpc.GetCorporationDetailById == nil, CorporationId=", auth.User(ctx).GetCorporationId())
			return response.Error(rsp, "OP7006")
		}

		q.CompanyId = detailById.CompanyId
	}

	if q.CompanyIdStr != "" {
		q.CompanyId, err = strconv.ParseInt(q.CompanyIdStr, 10, 64)
		if err != nil {
			log.Error("ParseInt err=", err.Error())
			return response.Error(rsp, response.ParamsInvalid)
		}

	}

	list, err := (&q.DoorCheckItems).TodoList()
	if err != nil {
		log.Error("DoorCheckItems.TodoList", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	data := map[string]interface{}{
		"Items": list,
	}

	return response.Success(rsp, data)
}

// TodoListSubstitute 管理员代录入门检-门检项目(待检查)列表
func (d *DoorCheck) TodoListSubstitute(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q DoorCheck
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	var occupation int64 = -1
	// 获取当前用户的StaffId
	staffItem := rpc.GetStaffWithPhone(ctx, auth.User(ctx).GetTopCorporationId(), auth.User(ctx).GetPhone())
	if staffItem != nil {
		occupation = staffItem.Occupation
	}

	if occupation != 2 && occupation != 5 { // 管理员 干部
		log.Error("身份不是管理员,干部无法代门检")
		return response.Error(rsp, "OP7508")
	}

	// 代录入获取司机检查模板
	q.WorkPostType = 3

	if q.CompanyId == 0 && q.CompanyIdStr == "" {
		detailById := rpc.GetCorporationDetailById(ctx, auth.User(ctx).GetCorporationId())
		if detailById == nil {
			log.Error("rpc.GetCorporationDetailById == nil, CorporationId=", auth.User(ctx).GetCorporationId())
			return response.Error(rsp, "OP7006")
		}

		q.CompanyId = detailById.CompanyId
	}

	if q.CompanyIdStr != "" {
		q.CompanyId, err = strconv.ParseInt(q.CompanyIdStr, 10, 64)
		if err != nil {
			log.Error("ParseInt err=", err.Error())
			return response.Error(rsp, response.ParamsInvalid)
		}

	}

	list, err := (&q.DoorCheckItems).TodoList()
	if err != nil {
		log.Error("DoorCheckItems.TodoList", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	data := map[string]interface{}{
		"Items": list,
	}

	return response.Success(rsp, data)
}

type InspectorInfo struct { // staffId name fleetId
	safety.DoorCheckInspectors
	Phone string // 手机号
}

// IsInsert 此次门检是否应该新增一条记录 (而不是更新cron预生成的门检记录)
func IsInsert(groupId int64, license string, checkForm int64, startAt, endAt time.Time) (safety.DoorCheckRecords, bool) {
	var rsp safety.DoorCheckRecords
	if checkForm == 4 {
		return rsp, true
	}

	record, err := (&safety.DoorCheckRecords{}).VehicleRecord(groupId, license, checkForm, startAt, endAt)
	if record == nil || err != nil {
		log.Error("IsInsert record == nil || err != nil, err=", err)
		return rsp, true
	} else {
		if len(record) == 1 {
			if record[0].HandleStatus == safety.PRE_GENERATE_1 && record[0].Id != 0 {
				return record[0], false
			}
		}
		return rsp, true
	}
}

// 门检结果保存 保存在车属机构
func (d *DoorCheck) AddResult(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q ItemResult
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 获取当前用户的StaffId
	userStaffItem := rpc.GetStaffWithPhone(ctx, auth.User(ctx).GetTopCorporationId(), auth.User(ctx).GetPhone())
	if userStaffItem == nil {
		log.Info("GetStaffWithPhone == nil")
		return response.Error(rsp, "OP7004")
	}

	// 过滤非司机、管理员提交数据
	if userStaffItem.Occupation != 0 && userStaffItem.Occupation != 2 && userStaffItem.Occupation != 5 {
		log.Error("只有司机和管理员能提交门检结果")
		return response.Error(rsp, "OP7502")
	}

	var Record safety.DoorCheckRecords
	// 通知司机信息 staffId, name, phone
	var driverNotice InspectorInfo
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	todayEnd := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 1)

	// 获取车辆code
	vehicleItem := rpc.GetVehicleWithLicense(ctx, &protooetvehicle.GetVehicleWithLicenseRequest{
		License:       q.License,
		CorporationId: auth.User(ctx).GetTopCorporationId(),
	})
	if vehicleItem == nil {
		log.Error("rpc.GetVehicleWithLicense vehicleItem == nil", err)
		return response.Error(rsp, "OP7005")
	}

	detailById := rpc.GetCorporationDetailById(ctx, vehicleItem.SonCorporationId)
	if detailById == nil {
		log.Error("rpc.GetCorporationDetailById detailById == nil, corpId=", vehicleItem.SonCorporationId)
		return response.Error(rsp, "OP7006")
	}

	Record.CompanyId = detailById.CompanyId
	Record.BranchId = detailById.BranchId
	Record.DepartmentId = detailById.DepartmentId
	Record.FleetId = detailById.FleetId

	user := auth.User(ctx).GetUser()
	// 1自检 2抽检
	if q.Type == 1 {
		//自检
		Record.StaffId = userStaffItem.Id
		Record.StaffName = userStaffItem.Name
		driverUser := rpc.GetUserInfoByPhone(ctx, userStaffItem.CorporationId, userStaffItem.Phone)
		if driverUser != nil {
			driverNotice.UserId = driverUser.Id
			driverNotice.Name = driverUser.Nickname
			driverNotice.Phone = driverUser.Phone
		}
	} else if q.Type == 2 {
		//抽检
		if q.StaffId <= 0 {
			log.Error("抽检司机staffId必传 q.StaffId <=0")
			return response.Error(rsp, response.ParamsMissing)
		}

		driver := rpc.GetStaffWithId(ctx, q.StaffId)
		if driver == nil {
			log.Info("driver == nil")
			return response.Error(rsp, "OP7004")
		}
		Record.StaffId = driver.Id
		Record.StaffName = driver.Name

		Record.AdminUserId = user.Id
		Record.AdminUserName = user.Name

		driverUser := rpc.GetUserInfoByPhone(ctx, driver.CorporationId, driver.Phone)
		if driverUser != nil {
			driverNotice.UserId = driverUser.Id
			driverNotice.Name = driverUser.Nickname
			driverNotice.Phone = driverUser.Phone
		}
	}

	// 解析参数获取 正常 异常状态
	// 默认正常
	var recordResult int64 = 1                 // 该条记录的状态
	recordStatusCountSlice := make([]int64, 5) // [正常数量，待整改数量，待解决数量，已解决数量，已关闭数量]
	for _, ir := range q.ItemResult {
		switch ir.Status {
		case 1:
			recordStatusCountSlice[0]++
		case 2:
			recordStatusCountSlice[1]++
			recordResult = 2 // 异常
		case 4:
			recordStatusCountSlice[2]++
			recordResult = 2 // 异常
		case 8:
			recordStatusCountSlice[3]++
			recordResult = 2 // 异常
		case 16:
			recordStatusCountSlice[4]++
			recordResult = 2 // 异常
		}
	}
	recordStatusCount := util.SliceItoA(recordStatusCountSlice) //记录的门检项目状态统计

	Record.Result = recordResult
	Record.StatusCount = recordStatusCount
	Record.HandleStatus = safety.CHECKED_2

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	// 根据 groupId 车辆 checkForm 时间 查询是否有预生成的数据
	if rd, is := IsInsert(userStaffItem.TopCorporationId, q.License, q.CheckForm, todayStart, todayEnd); is {
		// 抽查、多次门检、无预生成门检记录 需要插入一条数据

		log.Info("Insert Record ###########################")

		Record.Id = model.Id()
		Record.GroupId = detailById.GroupId
		Record.License = vehicleItem.License
		Record.VehicleCode = vehicleItem.Code

		// 根据出场和回场 传不同的时间
		now := time.Now()
		var s, e time.Time
		var scheduleType int64
		if q.CheckForm == 1 || q.CheckForm == 4 {
			s = time.Unix(now.Unix()-1800, 0) // 查询出场提前半小时
			e = time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 9999, time.Local)
			scheduleType = 1 // 调度早班
		} else if q.CheckForm == 2 {
			s = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
			e = now
			scheduleType = 2 // 调度晚班
		}

		// 根据排班查询线路
		scheduleWithVehicleIdItems := rpc.GetFirstLastPlanScheduleWithVehicleId(ctx, &protoschedule.GetFirstLastPlanScheduleWithVehicleIdRequest{
			VehicleId:         vehicleItem.Id,
			ExpectDepartAtGte: s.Unix(),
			ExpectDepartAtLte: e.Unix(),
			Status:            0,
			PlanSchType:       -1,
		})
		if scheduleWithVehicleIdItems == nil {
			// 排班没有查到 使用车辆归属线路
			Record.LineId = vehicleItem.LineId
			Record.Schedule = safety.NO_SCHEDULE_2

		} else {
			for _, item := range scheduleWithVehicleIdItems {
				if item.Id == 0 {
					// 排班没有查到 使用车辆归属线路
					Record.LineId = vehicleItem.LineId
					Record.Schedule = safety.NO_SCHEDULE_2
				} else {
					if item.Type == scheduleType {
						Record.LineId = item.LineId
						Record.Schedule = safety.YES_SCHEDULE_1
					}
				}
			}
		}

		lineWithId, _ := rpc.GetLineWithId(ctx, q.LineId)
		if lineWithId != nil {
			Record.Line = lineWithId.Name
		}

		err = (&Record).AddTx(tx)
		if err != nil {
			log.Error("AddTx err=", err)
			return response.Error(rsp, response.DbSaveFail)
		}

	} else {
		// 有预生成数据应该执行更新操作

		log.Info("Update Record ###########################")

		Record.Id = rd.Id

		err = (&Record).UpdatePreGenerateRecord(tx)
		if err != nil {
			log.Error("UpdatePreGenerateRecord err=", err)
			return response.Error(rsp, response.DbUpdateFail)
		}
	}

	//
	//
	var ir []safety.ItemResult

	// 转换id
	for i, request := range q.ItemResult {
		if request.FkItemIdStr != "" {
			q.ItemResult[i].FkItemId, _ = strconv.ParseInt(request.FkItemIdStr, 10, 64)
		}

		for i2, label := range request.ResultJson.NotRectified.Labels {
			if label.FkLabelIdStr != "" {
				q.ItemResult[i].ResultJson.NotRectified.Labels[i2].FkLabelId, _ = strconv.ParseInt(label.FkLabelIdStr, 10, 64)
			}
		}

	}

	// 结构体赋值
	for i, request := range q.ItemResult {
		item := safety.ItemResult{
			FkItemId: request.FkItemId,
			ItemName: request.ItemName,
			Status:   request.Status,
			ResultJson: safety.ResultJson{
				NotRectified: safety.NotRectified{
					Labels: nil,
					Files:  request.ResultJson.NotRectified.Files,
					Remark: request.ResultJson.NotRectified.Remark,
				},
				Rectified: request.ResultJson.Rectified,
			},
		}
		if request.Status == 4 { // 上报
			item.NoticeId = model.Id()
			q.ItemResult[i].NoticeId = item.NoticeId
			item.ReportUserId = request.NoticeUserId
			item.ReportUserName = request.NoticeUserName

			item.NoticeText = request.NoticeText

		} else if request.Status == 8 {
			item.ResolveUserId = request.NoticeUserId
			item.ResolveUserName = request.NoticeUserName
		}

		var labels []safety.ResultJsonLabel

		for _, label := range request.ResultJson.NotRectified.Labels {
			l := safety.ResultJsonLabel{
				FkLabelId: label.FkLabelId,
				Name:      label.Name,
			}

			labels = append(labels, l)
		}

		item.ResultJson.NotRectified.Labels = labels

		ir = append(ir, item)
	}

	// 存每一项门检的结果

	for _, item := range ir {

		itemResult := safety.DoorCheckItemResult{
			PkId:                  model.PkId{Id: model.Id()},
			GroupCorporation:      model.GroupCorporation{GroupId: Record.GroupId},
			CompanyCorporation:    model.CompanyCorporation{CompanyId: Record.CompanyId},
			BranchCorporation:     model.BranchCorporation{BranchId: Record.BranchId},
			DepartmentCorporation: model.DepartmentCorporation{DepartmentId: Record.DepartmentId},
			FleetCorporation:      model.FleetCorporation{FleetId: Record.FleetId},
			License:               Record.License,
			VehicleCode:           Record.VehicleCode,
			LineId:                Record.LineId,
			Line:                  Record.Line,
			StaffId:               Record.StaffId,
			StaffName:             Record.StaffName,
			Type:                  Record.Type,
			AdminUserId:           Record.AdminUserId,
			AdminUserName:         Record.AdminUserName,
			ReportUserId:          item.ReportUserId,
			ReportUserName:        item.ReportUserName,
			ResolveUserId:         item.ResolveUserId,
			ResolveUserName:       item.ResolveUserName,
			FkRecordId:            Record.Id,
			FkItemId:              item.FkItemId,
			ItemName:              item.ItemName,
			Status:                item.Status,
			ResultJson:            nil,
			Timestamp:             model.Timestamp{},
		}

		bytes, _ := json.Marshal(item.ResultJson)
		itemResult.ResultJson = model.JSON(bytes)

		err = (&itemResult).AddTx(tx)
		if err != nil {
			log.Error("itemResult AddTx err=", err)
			return response.Error(rsp, response.DbSaveFail)
		}

		if item.Status == 4 {
			// 上报
			// 存消息

			notice := safety.DoorCheckNotices{
				PkId:         model.PkId{Id: item.NoticeId},
				FkRecordId:   Record.Id,
				FkItemId:     item.FkItemId,
				ItemName:     item.ItemName,
				SendUserId:   item.ReportUserId,
				SendUserName: item.ReportUserName,
				ContentJson:  nil,
				Status:       1,
				NoticeType:   1,
				Timestamp:    model.Timestamp{},
			}

			contentJson := safety.ContentJson{
				Files:  nil,
				Remark: item.NoticeText,
			}
			bytes, _ := json.Marshal(contentJson)

			notice.ContentJson = model.JSON(bytes)

			err = (&notice).AddTx(tx, []safety.DoorCheckNoticeReceives{{UserId: item.ReportUserId, UserName: item.ReportUserName}})
			if err != nil {
				return err
			}
		}

	}

	// 通知
	//

	mini := config.Config.Mini

	// 获取值班车队长

	var inspector safety.DoorCheckInspectors
	inspector.FleetId = q.FleetId
	list, err := (&inspector).List(todayStart, todayEnd)
	if err != nil {
		log.Error("List err=", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}
	var inspectorList []InspectorInfo
	var nameList []string

	// 过滤重复人员
	tmpUser := make(map[int64]byte) // map[人员id]
	for _, i := range list {

		if _, ok := tmpUser[i.UserId]; ok {
			continue
		} else {
			tmpUser[i.UserId] = 0
		}

		// 获取车队长手机号
		user := rpc.GetUserInfoById(ctx, i.UserId)
		if user == nil || user.Phone == "" {
			log.Error("user == nil || user.Phone == '' user == ", user)
			continue
		}

		inspectorList = append(inspectorList, InspectorInfo{
			DoorCheckInspectors: i,
			Phone:               user.Phone,
		})

		nameList = append(nameList, i.Name)
	}

	// 异常通知 抽检不发送 异常通知
	if q.CheckForm < 4 {
		for _, item := range q.ItemResult {

			pagePath := fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%d&Type=%d`, mini.Page, Record.Id, item.FkItemId, 0, ABNORMAL_1)

			if item.Status == 2 {
				// 待整改 发给值班车队长+司机
				// 直接发外部消息 不存储

				for _, i := range inspectorList {

					go message.NewOfficialAccountMsg(
						"台州公交车辆检查通知",
						[]string{fmt.Sprintf(`[异常处理] %s，%s，%s`, Record.Line, Record.License, driverNotice.Name), fmt.Sprintf(`%s`, strings.Join(nameList, ","))},
						"请尽快进行处理",
						pagePath,
						i.Phone,
					).Send()

				}

			}
		}
	} else {
		for _, item := range q.ItemResult {

			if item.Status == 4 {
				pagePath := fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%d&Type=%d`, mini.Page, Record.Id, item.FkItemId, item.NoticeId, REPORT_2)
				// 上报 发给上报接收人员 + 司机

				// rpc
				recv := rpc.GetUserInfoById(ctx, item.NoticeUserId)
				if recv != nil && recv.Phone != "" {
					go message.NewOfficialAccountMsg(
						"台州公交车辆检查通知",
						[]string{fmt.Sprintf(`[异常处理] %s，%s，%s`, Record.Line, Record.License, driverNotice.Name), fmt.Sprintf(`%s`, item.NoticeUserName)},
						"请尽快进行处理",
						pagePath,
						recv.Phone,
					).Send()
				}

				// send to driver
				pagePath = fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%d&Type=%d`, mini.Page, Record.Id, item.FkItemId, item.NoticeId, ABNORMAL_RESOLVE_4)

				go message.NewOfficialAccountMsg(
					"台州公交车辆检查通知",
					[]string{fmt.Sprintf(`[整改进展] %s，%s，%s`, Record.Line, Record.License, driverNotice.Name), fmt.Sprintf(`%s`, item.NoticeUserName)},
					"请尽快进行处理",
					pagePath,
					driverNotice.Phone,
				).Send()

			}
		}
	}

	return response.Success(rsp, nil)

}

// 管理员代录入 - 门检结果保存 保存在车属机构
func (d *DoorCheck) AddResultSubstitute(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q ItemResult
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.CheckForm == 4 {
		log.Infof("q.CheckForm == 4, 代录入不能抽检")
		return response.Error(rsp, "OP7509")
	}

	if q.StaffId == 0 {
		log.Infof("q.StaffId == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	staffItem := rpc.GetStaffWithPhone(ctx, auth.User(ctx).GetTopCorporationId(), auth.User(ctx).GetPhone())
	if staffItem == nil {
		log.Info("GetStaffWithPhone == nil")
		return response.Error(rsp, "OP7004")
	}

	// 过滤非管理员提交数据
	if staffItem.Occupation != 2 && staffItem.Occupation != 5 { // 管理员 干部
		log.Error("身份不是管理员,干部无法代门检", staffItem.Id)
		return response.Error(rsp, "OP7508")
	}

	var Record safety.DoorCheckRecords
	// 通知司机信息 staffId, name, phone
	var driverNotice InspectorInfo
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	todayEnd := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 1)

	// 获取车辆code
	vehicleItem := rpc.GetVehicleWithLicense(ctx, &protooetvehicle.GetVehicleWithLicenseRequest{
		License:       q.License,
		CorporationId: staffItem.TopCorporationId,
	})
	if vehicleItem == nil {
		log.Error("rpc.GetVehicleWithLicense vehicleItem == nil", err)
		return response.Error(rsp, "OP7005")
	}

	detailById := rpc.GetCorporationDetailById(ctx, vehicleItem.SonCorporationId)
	if detailById == nil {
		log.Error("rpc.GetCorporationDetailById detailById == nil, corpId=", vehicleItem.SonCorporationId)
		return response.Error(rsp, "OP7006")
	}

	Record.CompanyId = detailById.CompanyId
	Record.BranchId = detailById.BranchId
	Record.DepartmentId = detailById.DepartmentId
	Record.FleetId = detailById.FleetId

	driver := rpc.GetStaffWithId(ctx, q.StaffId)
	if driver == nil {
		log.Info("driver == nil")
		return response.Error(rsp, "OP7004")
	}
	Record.StaffId = driver.Id
	Record.StaffName = driver.Name

	user := auth.User(ctx).GetUser()

	Record.AdminUserId = user.Id
	Record.AdminUserName = user.Name

	driverUser := rpc.GetUserInfoByPhone(ctx, driver.CorporationId, driver.Phone)
	if driverUser != nil {
		driverNotice.UserId = driverUser.Id
		driverNotice.Name = driverUser.Nickname
		driverNotice.Phone = driverUser.Phone
	}

	// 解析参数获取 正常 异常状态
	// 默认正常
	var recordResult int64 = 1                 // 该条记录的状态
	recordStatusCountSlice := make([]int64, 5) // [正常数量，待整改数量，待解决数量，已解决数量，已关闭数量]
	for _, ir := range q.ItemResult {
		switch ir.Status {
		case 1:
			recordStatusCountSlice[0]++
		case 2:
			recordStatusCountSlice[1]++
			recordResult = 2 // 异常
		case 4:
			recordStatusCountSlice[2]++
			recordResult = 2 // 异常
		case 8:
			recordStatusCountSlice[3]++
			recordResult = 2 // 异常
		case 16:
			recordStatusCountSlice[4]++
			recordResult = 2 // 异常
		}
	}
	recordStatusCount := util.SliceItoA(recordStatusCountSlice) //记录的门检项目状态统计

	Record.Result = recordResult
	Record.StatusCount = recordStatusCount
	Record.HandleStatus = safety.CHECKED_2

	tx := model.DB().Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	// 根据 groupId 车辆 checkForm 时间 查询是否有预生成的数据
	if rd, is := IsInsert(staffItem.TopCorporationId, q.License, q.CheckForm, todayStart, todayEnd); is {
		// 抽查、多次门检、无预生成门检记录 需要插入一条数据

		log.Info("Insert Record ###########################")

		Record.Id = model.Id()
		Record.GroupId = detailById.GroupId
		Record.License = vehicleItem.License
		Record.VehicleCode = vehicleItem.Code

		// 根据出场和回场 传不同的时间
		now := time.Now()
		var s, e time.Time
		var scheduleType int64
		if q.CheckForm == 1 || q.CheckForm == 4 {
			s = time.Unix(now.Unix()-1800, 0) // 查询出场提前半小时
			e = time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 9999, time.Local)
			scheduleType = 1 // 调度早班
		} else if q.CheckForm == 2 {
			s = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
			e = now
			scheduleType = 2 // 调度晚班
		}

		// 根据排班查询线路
		scheduleWithVehicleIdItems := rpc.GetFirstLastPlanScheduleWithVehicleId(ctx, &protoschedule.GetFirstLastPlanScheduleWithVehicleIdRequest{
			VehicleId:         vehicleItem.Id,
			ExpectDepartAtGte: s.Unix(),
			ExpectDepartAtLte: e.Unix(),
			Status:            0,
			PlanSchType:       -1,
		})
		if scheduleWithVehicleIdItems == nil {
			// 排班没有查到 使用车辆归属线路
			Record.LineId = vehicleItem.LineId
			Record.Schedule = safety.NO_SCHEDULE_2

		} else {
			for _, item := range scheduleWithVehicleIdItems {
				if item.Id == 0 {
					// 排班没有查到 使用车辆归属线路
					Record.LineId = vehicleItem.LineId
					Record.Schedule = safety.NO_SCHEDULE_2
				} else {
					if item.Type == scheduleType {
						Record.LineId = item.LineId
						Record.Schedule = safety.YES_SCHEDULE_1
					}
				}
			}
		}

		lineWithId, _ := rpc.GetLineWithId(ctx, q.LineId)
		if lineWithId != nil {
			Record.Line = lineWithId.Name
		}

		err = (&Record).AddTx(tx)
		if err != nil {
			log.Error("AddTx err=", err)
			return response.Error(rsp, response.DbSaveFail)
		}

	} else {
		// 有预生成数据应该执行更新操作

		log.Info("Update Record ###########################")

		Record.Id = rd.Id

		err = (&Record).UpdatePreGenerateRecord(tx)
		if err != nil {
			log.Error("UpdatePreGenerateRecord err=", err)
			return response.Error(rsp, response.DbUpdateFail)
		}
	}

	var ir []safety.ItemResult

	// 转换id
	for i, request := range q.ItemResult {
		if request.FkItemIdStr != "" {
			q.ItemResult[i].FkItemId, _ = strconv.ParseInt(request.FkItemIdStr, 10, 64)
		}

		for i2, label := range request.ResultJson.NotRectified.Labels {
			if label.FkLabelIdStr != "" {
				q.ItemResult[i].ResultJson.NotRectified.Labels[i2].FkLabelId, _ = strconv.ParseInt(label.FkLabelIdStr, 10, 64)
			}
		}

	}
	// 结构体赋值
	for i, request := range q.ItemResult {
		item := safety.ItemResult{
			FkItemId: request.FkItemId,
			ItemName: request.ItemName,
			Status:   request.Status,
			ResultJson: safety.ResultJson{
				NotRectified: safety.NotRectified{
					Labels: nil,
					Files:  request.ResultJson.NotRectified.Files,
					Remark: request.ResultJson.NotRectified.Remark,
				},
				Rectified: request.ResultJson.Rectified,
			},
		}
		if request.Status == 4 { // 上报
			item.NoticeId = model.Id()
			q.ItemResult[i].NoticeId = item.NoticeId
			item.ReportUserId = request.NoticeUserId
			item.ReportUserName = request.NoticeUserName

			item.NoticeText = request.NoticeText

		} else if request.Status == 8 {
			item.NoticeId = model.Id()
			q.ItemResult[i].NoticeId = item.NoticeId
			item.ResolveUserId = request.NoticeUserId
			item.ResolveUserName = request.NoticeUserName
		}

		var labels []safety.ResultJsonLabel

		for _, label := range request.ResultJson.NotRectified.Labels {
			l := safety.ResultJsonLabel{
				FkLabelId: label.FkLabelId,
				Name:      label.Name,
			}

			labels = append(labels, l)
		}

		item.ResultJson.NotRectified.Labels = labels

		ir = append(ir, item)
	}

	// 存每一项门检内容
	for _, item := range ir {

		itemResult := safety.DoorCheckItemResult{
			PkId:                  model.PkId{Id: model.Id()},
			GroupCorporation:      model.GroupCorporation{GroupId: Record.GroupId},
			CompanyCorporation:    model.CompanyCorporation{CompanyId: Record.CompanyId},
			BranchCorporation:     model.BranchCorporation{BranchId: Record.BranchId},
			DepartmentCorporation: model.DepartmentCorporation{DepartmentId: Record.DepartmentId},
			FleetCorporation:      model.FleetCorporation{FleetId: Record.FleetId},
			License:               Record.License,
			VehicleCode:           Record.VehicleCode,
			LineId:                Record.LineId,
			Line:                  Record.Line,
			StaffId:               Record.StaffId,
			StaffName:             Record.StaffName,
			Type:                  Record.Type,
			AdminUserId:           Record.AdminUserId,
			AdminUserName:         Record.AdminUserName,
			ReportUserId:          item.ReportUserId,
			ReportUserName:        item.ReportUserName,
			ResolveUserId:         item.ResolveUserId,
			ResolveUserName:       item.ResolveUserName,
			FkRecordId:            Record.Id,
			FkItemId:              item.FkItemId,
			ItemName:              item.ItemName,
			Status:                item.Status,
			ResultJson:            nil,
			Timestamp:             model.Timestamp{},
		}

		bytes, _ := json.Marshal(item.ResultJson)
		itemResult.ResultJson = model.JSON(bytes)

		err = (&itemResult).AddTx(tx)
		if err != nil {
			log.Error("itemResult AddTx err=", err)
			return response.Error(rsp, response.DbSaveFail)
		}

		if item.Status == 4 {
			// 上报
			// 存消息

			notice := safety.DoorCheckNotices{
				PkId:         model.PkId{Id: item.NoticeId},
				FkRecordId:   Record.Id,
				FkItemId:     item.FkItemId,
				ItemName:     item.ItemName,
				SendUserId:   item.ReportUserId,
				SendUserName: item.ReportUserName,
				ContentJson:  nil,
				Status:       1,
				NoticeType:   1,
				Timestamp:    model.Timestamp{},
			}

			contentJson := safety.ContentJson{
				Files:  nil,
				Remark: item.NoticeText,
			}
			bytes, _ := json.Marshal(contentJson)

			notice.ContentJson = model.JSON(bytes)

			err = (&notice).AddTx(tx, []safety.DoorCheckNoticeReceives{{UserId: item.ReportUserId, UserName: item.ReportUserName}})
			if err != nil {
				return err
			}
		}

	}

	mini := config.Config.Mini

	// 获取值班车队长

	var inspector safety.DoorCheckInspectors
	inspector.FleetId = q.FleetId
	list, err := (&inspector).List(todayStart, todayEnd)
	if err != nil {
		log.Error("List err=", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}
	var inspectorList []InspectorInfo
	var nameList []string

	// 过滤重复人员
	tmpUser := make(map[int64]byte) // map[userId]

	for _, i := range list {

		if _, ok := tmpUser[i.UserId]; ok {
			continue
		} else {
			tmpUser[i.UserId] = 0
		}

		// 获取车队长手机号
		user := rpc.GetUserInfoById(ctx, i.UserId)
		if user == nil || user.Phone == "" {
			log.Error("user == nil || user.Phone == '' user == ", user)
			continue
		}

		inspectorList = append(inspectorList, InspectorInfo{
			DoorCheckInspectors: i,
			Phone:               user.Phone,
		})

		nameList = append(nameList, i.Name)
	}

	// 异常通知 抽检不发送 异常通知  代录入只有司机提交异常
	for _, item := range q.ItemResult {

		if item.Status == 2 {
			pagePath := fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%d&Type=%d`, mini.Page, Record.Id, item.FkItemId, 0, ABNORMAL_1)
			// 待整改 发给值班车队长
			// 直接发外部消息 不存储

			for _, i := range inspectorList {

				go message.NewOfficialAccountMsg(
					"台州公交车辆检查通知",
					[]string{fmt.Sprintf(`[异常处理] %s，%s，%s`, Record.Line, Record.License, driverNotice.Name), fmt.Sprintf(`%s`, strings.Join(nameList, ","))},
					"请尽快进行处理",
					pagePath,
					i.Phone,
				).Send()
			}

		} else if item.Status == 4 {
			pagePath := fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%d&Type=%d`, mini.Page, Record.Id, item.FkItemId, item.NoticeId, REPORT_2)
			// 上报 发给上报接收人员 + 司机

			// rpc
			recv := rpc.GetUserInfoById(ctx, item.NoticeUserId)
			if recv != nil && recv.Phone != "" {
				go message.NewOfficialAccountMsg(
					"台州公交车辆检查通知",
					[]string{fmt.Sprintf(`[异常处理] %s，%s，%s`, Record.Line, Record.License, driverNotice.Name), fmt.Sprintf(`%s`, item.NoticeUserName)},
					"请尽快进行处理",
					pagePath,
					recv.Phone,
				).Send()
			}

			// send to driver
			pagePath = fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%d&Type=%d`, mini.Page, Record.Id, item.FkItemId, item.NoticeId, ABNORMAL_RESOLVE_4)
			go message.NewOfficialAccountMsg(
				"台州公交车辆检查通知",
				[]string{fmt.Sprintf(`[整改进展] %s，%s，%s`, Record.Line, Record.License, driverNotice.Name), fmt.Sprintf(`%s`, item.NoticeUserName)},
				"请尽快进行处理",
				pagePath,
				driverNotice.Phone,
			).Send()

		} else if item.Status == 8 {
			// 解决后 发送给司机 此处为整改
			pagePath := fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%d&Type=%d`, mini.Page, Record.Id, item.FkItemId, item.NoticeId, RESOLVE_8)

			recv2driver := rpc.GetUserInfoById(ctx, driverNotice.UserId)
			if recv2driver != nil && recv2driver.Phone != "" {

				go message.NewOfficialAccountMsg(
					"台州公交车辆检查通知",
					[]string{fmt.Sprintf(`[整改结果] %s，%s，%s`, Record.Line, Record.License, driverNotice.Name), ""},
					"请尽快进行处理",
					pagePath,
					recv2driver.Phone,
				).Send()
			}
		}
	}

	return response.Success(rsp, nil)

}

type Total struct {
	Count         int64 // 出/回场车次
	NoCheck       int64 // 未门检记录数
	AbNormal      int64 // 异常记录数
	ToBeRectified int64 // 待整改
	ToBeProcess   int64 // 待处理
	Resolved      int64 // 已解决
}

type SpotCheck struct {
	Count       int64 // 抽检数记录数
	AbNormal    int64 // 异常记录数
	ToBeProcess int64 // 待处理
	Resolved    int64 // 已解决
}

// 门检结果web列表 前端选择机构
func (d *DoorCheck) ListWebResult(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Records
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.StartAt <= 0 || q.EndAt <= 0 {
		log.Error("q.StartAt <= 0 || q.EndAt <= 0")
		return response.Error(rsp, response.ParamsInvalid)
	}

	t := time.Unix(q.StartAt, 0)
	e := time.Unix(q.EndAt, 0)

	// 查询今天数据

	// 获取用户userid
	if !auth.User(ctx).HasUser() {
		log.Error("md.GetWebUserId, userId <= 0")
		return response.Error(rsp, "403")
	}

	// 过滤机构
	if len(q.CorporationIds) == 0 {

		// 获取当前用户的userid 所属机构

		userCorporationId := auth.User(ctx).GetCorporationId()
		detailById := rpc.GetCorporationDetailById(ctx, userCorporationId)
		if detailById == nil {
			log.Error("GetCorporationDetailById detailById == nil")
			return response.Error(rsp, "OP7004")
		}
		if detailById.GroupId > 0 {
			q.CorporationIds = append(q.CorporationIds, detailById.GroupId)
		}
		if detailById.CompanyId > 0 {
			q.CorporationIds = append(q.CorporationIds, detailById.CompanyId)
		}
		if detailById.BranchId > 0 {
			q.CorporationIds = append(q.CorporationIds, detailById.BranchId)
		}
		if detailById.DepartmentId > 0 {
			q.CorporationIds = append(q.CorporationIds, detailById.DepartmentId)
		}
		if detailById.FleetId > 0 {
			q.CorporationIds = append(q.CorporationIds, detailById.FleetId)
		}

	}

	list, err := (&safety.DoorCheckRecords{}).List(q.CorporationIds, q.License, q.LineId, q.StaffId, q.CheckForms, q.Results, q.Statuses, t, e)
	if err != nil {
		log.Error("DoorCheckRecords List err=", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	all, err := (&safety.DoorCheckRecords{}).All(q.CorporationIds, t, e)
	if err != nil {
		log.Error("DoorCheckRecords All err=", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	// 根据车辆分组
	tmp := make(map[string][]safety.DoorCheckRecords) //map[车牌]{ 门检记录 }

	// map index
	var (
		tmpIndex  []string // []车牌
		out, back Total
		spotCheck SpotCheck
	)

	for _, record := range list {
		if _, ok := tmp[record.License]; !ok {
			tmpIndex = append(tmpIndex, record.License)
		}

		tmp[record.License] = append(tmp[record.License], record)
	}

	// 统计
	for _, record := range all {
		if record.CheckForm == 1 {
			//	出场
			if record.Id > 0 && record.HandleStatus == safety.CHECKED_2 {
				out.Count++

				split := strings.Split(record.StatusCount, ",")
				if len(split) != 5 {
					log.Error("len(split) != 5 record.id=", record.Id)
					continue
				}
				sc := util.StringSliceToInt64Slice(split)
				if sc[1] > 0 {
					out.ToBeRectified++
				}
				if sc[2] > 0 {
					out.ToBeProcess++
				}
				if sc[3] > 0 && sc[1] == 0 && sc[2] == 0 {
					out.Resolved++
				}
				if record.Result > 1 {
					out.AbNormal++
				}

			} else {
				out.NoCheck += 1
			}

		} else if record.CheckForm == 2 {
			// 回场
			if record.Id > 0 && record.HandleStatus == safety.CHECKED_2 {
				back.Count++

				split := strings.Split(record.StatusCount, ",")
				if len(split) != 5 {
					log.Error("len(split) != 5 record.id=", record.Id)
					continue
				}
				sc := util.StringSliceToInt64Slice(split)
				if sc[1] > 0 {
					back.ToBeRectified++
				}
				if sc[2] > 0 {
					back.ToBeProcess++
				}
				if sc[3] > 0 && sc[1] == 0 && sc[2] == 0 {
					back.Resolved++
				}
				if record.Result > 1 {
					back.AbNormal++
				}

			} else {
				back.NoCheck += 1
			}
		} else if record.CheckForm == 4 {
			// 抽检
			if record.Id > 0 && record.HandleStatus == safety.CHECKED_2 {
				spotCheck.Count++

				split := strings.Split(record.StatusCount, ",")
				if len(split) != 5 {
					log.Error("len(split) != 5 record.id=", record.Id)
					continue
				}
				sc := util.StringSliceToInt64Slice(split)

				if sc[2] > 0 {
					spotCheck.ToBeProcess++
				}
				if sc[3] > 0 && sc[1] == 0 && sc[2] == 0 {
					spotCheck.Resolved++
				}
				if record.Result > 1 {
					spotCheck.AbNormal++
				}

			}
		}
	}

	// 列表分页
	var totalCount = len(tmp)
	var pageVehicle []string
	if q.Limit+q.Offset >= totalCount {
		if q.Offset < totalCount {
			pageVehicle = tmpIndex[q.Offset:]
		} else {
		} //	偏移大于总数 返回空

	} else {
		pageVehicle = tmpIndex[q.Offset:(q.Offset + q.Limit)]
	}

	var rspData [][]safety.DoorCheckRecords // [[{浙A12345}, {浙A12345}, {浙A12345}...],[{...}],[{...}],]
	for _, license := range pageVehicle {
		rspData = append(rspData, tmp[license])
	}

	data := map[string]interface{}{
		//"DynamicColumns":           listWebResult.DynamicColumns,
		"Items":      rspData,
		"TotalCount": totalCount,
		"Out":        out,
		"Back":       back,
		"SpotCheck":  spotCheck,
	}

	return response.Success(rsp, data)
}

type ListResponse struct {
	safety.DoorCheckRecords
	AbNormalLabels []string
	IdStr          string // id 转换 string 兼容微信
}

// ListAppResult 门检结果app列表
func (d *DoorCheck) ListAppResult(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Records
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.StartAt <= 0 || q.EndAt <= 0 {
		log.Error("q.StartAt <= 0 || q.EndAt <= 0")
		return response.Error(rsp, response.ParamsInvalid)
	}

	t := time.Unix(q.StartAt, 0)
	e := time.Unix(q.EndAt, 0)

	// 获取用户userid
	if !auth.User(ctx).HasUser() {
		log.Error("md.GetWebUserId, userId <= 0")
		return response.Error(rsp, "403")
	}

	// 转换corporationid
	for _, idStr := range q.CorporationIdStrs {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			log.Error("strconv.ParseInt CorporationIdStrs err", err)
			return response.Error(rsp, response.ParamsInvalid)
		}
		q.CorporationIds = append(q.CorporationIds, id)
	}

	// 过滤机构
	if len(q.CorporationIds) == 0 {
		// 获取录入员信息
		// 录入员使用当前操作人
		// 获取当前用户的StaffId
		detailById := rpc.GetCorporationDetailById(ctx, auth.User(ctx).GetCorporationId())
		if detailById == nil {
			log.Error("GetCorporationDetailById detailById == nil")
			return response.Error(rsp, "OP7004")
		}
		if detailById.GroupId > 0 {
			q.CorporationIds = append(q.CorporationIds, detailById.GroupId)
		}
		if detailById.CompanyId > 0 {
			q.CorporationIds = append(q.CorporationIds, detailById.CompanyId)
		}
		if detailById.BranchId > 0 {
			q.CorporationIds = append(q.CorporationIds, detailById.BranchId)
		}
		if detailById.DepartmentId > 0 {
			q.CorporationIds = append(q.CorporationIds, detailById.DepartmentId)
		}
		if detailById.FleetId > 0 {
			q.CorporationIds = append(q.CorporationIds, detailById.FleetId)
		}

	}

	list, err := (&safety.DoorCheckRecords{}).List(q.CorporationIds, q.License, q.LineId, q.StaffId, q.CheckForms, q.Results, q.Statuses, t, e)
	if err != nil {
		log.Error("DoorCheckRecords List err=", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	all, err := (&safety.DoorCheckRecords{}).All(q.CorporationIds, t, e)
	if err != nil {
		log.Error("DoorCheckRecords All err=", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	// 根据车辆分组
	tmp := make(map[string][]safety.DoorCheckRecords) //map[车牌]{ 门检记录 }

	// map index
	var (
		tmpIndex  []string // []车牌
		out, back Total
		spotCheck SpotCheck
	)

	for _, record := range list {
		if _, ok := tmp[record.License]; !ok {
			tmpIndex = append(tmpIndex, record.License)
		}

		tmp[record.License] = append(tmp[record.License], record)
	}

	// 统计
	for _, record := range all {
		if record.CheckForm == 1 {
			//	出场
			if record.Id > 0 && record.HandleStatus == safety.CHECKED_2 {
				out.Count++

				split := strings.Split(record.StatusCount, ",")
				if len(split) != 5 {
					log.Error("len(split) != 5 record.id=", record.Id)
					continue
				}
				sc := util.StringSliceToInt64Slice(split)
				if sc[1] > 0 {
					out.ToBeRectified++
				}
				if sc[2] > 0 {
					out.ToBeProcess++
				}
				if sc[3] > 0 && sc[1] == 0 && sc[2] == 0 {
					out.Resolved++
				}
				if record.Result > 1 {
					out.AbNormal++
				}

			} else {
				out.NoCheck += 1
			}

		} else if record.CheckForm == 2 {
			// 回场
			if record.Id > 0 && record.HandleStatus == safety.CHECKED_2 {
				back.Count++

				split := strings.Split(record.StatusCount, ",")
				if len(split) != 5 {
					log.Error("len(split) != 5 record.id=", record.Id)
					continue
				}
				sc := util.StringSliceToInt64Slice(split)
				if sc[1] > 0 {
					back.ToBeRectified++
				}
				if sc[2] > 0 {
					back.ToBeProcess++
				}
				if sc[3] > 0 && sc[1] == 0 && sc[2] == 0 {
					back.Resolved++
				}
				if record.Result > 1 {
					back.AbNormal++
				}

			} else {
				back.NoCheck += 1
			}
		} else if record.CheckForm == 4 {
			// 抽检
			if record.Id > 0 && record.HandleStatus == safety.CHECKED_2 {
				spotCheck.Count++

				split := strings.Split(record.StatusCount, ",")
				if len(split) != 5 {
					log.Error("len(split) != 5 record.id=", record.Id)
					continue
				}
				sc := util.StringSliceToInt64Slice(split)

				if sc[2] > 0 {
					spotCheck.ToBeProcess++
				}
				if sc[3] > 0 && sc[1] == 0 && sc[2] == 0 {
					spotCheck.Resolved++
				}
				if record.Result > 1 {
					spotCheck.AbNormal++
				}

			}
		}
	}

	// 列表分页
	var totalCount = len(tmp)
	var pageVehicle []string
	if q.Limit+q.Offset >= totalCount {
		if q.Offset < totalCount {
			pageVehicle = tmpIndex[q.Offset:]
		} else {
		} //	偏移大于总数 返回空

	} else {
		pageVehicle = tmpIndex[q.Offset:(q.Offset + q.Limit)]
	}

	var rspData [][]safety.DoorCheckRecords // [[{浙A12345}, {浙A12345}, {浙A12345}...],[{...}],[{...}],]
	for _, license := range pageVehicle {

		rspData = append(rspData, tmp[license])
	}

	// 处理 idStr
	var rspDataWx [][]ListResponse
	for _, v := range rspData {
		var appRsp []ListResponse
		for _, records := range v {
			var appRspItems ListResponse
			appRspItems.DoorCheckRecords = records
			if records.Id > 0 {
				appRspItems.IdStr = strconv.FormatInt(records.Id, 10)

				labels, err := (&records).GetAbNormalLabels()
				if err != nil {
					log.Error("GetAbNormalLabels = ", err)
				}
				appRspItems.AbNormalLabels = labels

			}
			appRsp = append(appRsp, appRspItems)
		}
		rspDataWx = append(rspDataWx, appRsp)
	}

	data := map[string]interface{}{
		"Items":      rspDataWx,
		"TotalCount": totalCount,
		"Out":        out,
		"Back":       back,
		"SpotCheck":  spotCheck,
	}

	return response.Success(rsp, data)
}

type WebDetailResponse struct {
	safety.DoorCheckItemResult
	CheckTypeId   int64
	CheckTypeName string
	IconFileUrl   string // 门检项目icon
	Report        safety.Notice
	Resolve       safety.Notice
}

// 门检结果web详情
func (d *DoorCheck) GetResultDetail(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q ItemResult
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	var record safety.DoorCheckRecords
	record.Id = q.Id
	checkRecords, err := (&record).GetById()
	if err != nil {
		log.Error("GetByIdl err= ", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	// id 接收的参数实际为 DoorCheckRecords.Id // 门检记录的id
	resultDetail, err := (&q.DoorCheckItemResult).GetDetailWeb()
	if err != nil {
		log.Error("doorcheck.GetResultDetail err= ", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	var rspD []WebDetailResponse

	for _, result := range resultDetail {
		var (
			item    safety.DoorCheckItems
			rspItem WebDetailResponse
		)

		rspItem.DoorCheckItemResult = result

		item.Id = result.FkItemId

		doorCheckItems, err := (&item).GetCheckTypeById()
		if err != nil {
			log.Error("GetCheckTypeById err= ", err)
		} else {
			rspItem.CheckTypeId = doorCheckItems.Id
			rspItem.CheckTypeName = doorCheckItems.Name
		}

		doorCheckItemGetById, err := (&item).GetById()
		if err != nil {
			log.Error("GetById err= ", err)
		} else {
			rspItem.IconFileUrl = doorCheckItemGetById.IconFileUrl
		}

		// 待处理 已处理 才查询消息
		if result.Status > 2 && result.Status < 16 {
			noticeList, err := (&safety.DoorCheckNotices{}).GetByItemId(q.Id, result.FkItemId)
			if err != nil {
				log.Error("GetByItemId err= ", err)
			} else {
				if noticeList != nil {
					rspItem.Report = noticeList[0]
					rspItem.Resolve = noticeList[1]
				}
			}
		}

		rspD = append(rspD, rspItem)

	}

	// 过滤没有上传记录的结果
	//for _, i := range resultDetail {
	//	if len(i.ResultJson.NotRectified.Labels) == 0 && len(i.ResultJson.NotRectified.Files) == 0 && i.ResultJson.NotRectified.Remark == "" {
	//		continue
	//	} else {
	//		if len(i.ResultJson.Rectified) > 1 {
	//			i.ResultJson.Rectified = i.ResultJson.Rectified[len(i.ResultJson.Rectified)-1:]
	//		}
	//		rspData = append(rspData, i)
	//	}
	//}

	data := map[string]interface{}{
		"Items":  rspD,
		"Record": checkRecords,
	}

	return response.Success(rsp, data)
}

// EditResult 门检结果整改
func (d *DoorCheck) EditResult(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Records
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.IdStr != "" {

		q.Id, _ = strconv.ParseInt(q.IdStr, 10, 64)

	}

	if q.Id == 0 {
		log.Error("q.Id == 0", err)
		return response.Error(rsp, response.ParamsMissing)
	}

	for i, item := range q.Items {
		q.Items[i].NewNoticeId = strconv.FormatInt(model.Id(), 10)
		if item.ItemResultIdStr == "" {
			continue
		}
		q.Items[i].ItemResultId, _ = strconv.ParseInt(item.ItemResultIdStr, 10, 64)
	}

	staffItem := rpc.GetStaffWithPhone(ctx, auth.User(ctx).GetTopCorporationId(), auth.User(ctx).GetPhone())
	if staffItem == nil {
		log.Info("staffWithId == nil")
		return response.Error(rsp, "OP7004")
	}

	// 查询整改之前的门检
	dr := &safety.DoorCheckItemResult{}
	dr.Id = q.Id
	itemResults, err := dr.GetDetailWeb()
	if err != nil {
		log.Error("GetDetailWeb err=", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	var editIndex int
	// 修改状态
	for i, result := range itemResults {
		for i2, item := range q.Items {
			if result.Id == item.ItemResultId {
				q.Items[i2].ItemStatus = 8
				itemResults[i].Status = 8
				editIndex = i
				q.Items[i2].ResolveUserId = staffItem.Id
				q.Items[i2].ResolveUserName = staffItem.Name

			}
		}
	}

	recordStatusCountSlice := make([]int64, 5) // [正常数量，待整改数量，待解决数量，已解决数量，已关闭数量]
	for _, i := range itemResults {
		switch i.Status {
		case 1:
			recordStatusCountSlice[0]++
		case 2:
			recordStatusCountSlice[1]++
		case 4:
			recordStatusCountSlice[2]++
		case 8:
			recordStatusCountSlice[3]++
		case 16:
			recordStatusCountSlice[4]++
		}
	}
	q.StatusCount = util.SliceItoA(recordStatusCountSlice) //记录的门检项目状态统计

	err = (&q.DoorCheckRecords).Edit(q.Items)
	if err != nil {
		log.Error("doorcheck.EditResult err=", err.Error())
		return response.Error(rsp, response.DbUpdateFail)
	}

	mini := config.Config.Mini

	for _, item := range q.Items {
		pagePath := fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%s&Type=%d`, mini.Page, q.Id, itemResults[editIndex].FkItemId, item.NewNoticeId, ABNORMAL_RESOLVE_4)

		sendData := []*protoMini.UniformMsgKeyValue{
			{
				Key:   "first",
				Value: "台州公交车辆检查通知",
				Color: "#000000",
			},
			{
				Key:   "keyword1",
				Value: fmt.Sprintf(`[整改结果] %s，%s，%s`, itemResults[editIndex].Line, itemResults[editIndex].License, itemResults[editIndex].StaffName),
				Color: "#000000",
			},
			{
				Key:   "keyword2",
				Value: fmt.Sprintf(`%s`, itemResults[editIndex].StaffName),
				Color: "#000000",
			},
			{
				Key:   "remark",
				Value: "查看整改结果",
				Color: "#000000",
			},
		}
		// rpc
		recv := rpc.GetStaffWithId(ctx, itemResults[editIndex].StaffId)
		if recv != nil && recv.Phone != "" {
			err := rpc.SendUniformMsg(ctx, mini.AppAccountId, recv.Phone, mini.DoorCheckTemplateId, pagePath, sendData)
			if err != nil {
				log.Error("rpc.SendUniformMsg err = ", err)
			}
		}

	}

	return response.Success(rsp, nil)

}

// 门检结果关闭重复
func (d *DoorCheck) RepeatResult(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Records
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.IdStr != "" {

		q.Id, _ = strconv.ParseInt(q.IdStr, 10, 64)

	}

	if q.Id == 0 {
		log.Error("q.Id == 0", err)
		return response.Error(rsp, response.ParamsMissing)
	}

	for i, item := range q.Items {
		if item.ItemResultIdStr == "" {
			continue
		}
		q.Items[i].ItemResultId, _ = strconv.ParseInt(item.ItemResultIdStr, 10, 64)
	}

	// 查询之前的门检
	dr := &safety.DoorCheckItemResult{}
	dr.Id = q.Id
	itemResults, err := dr.GetDetailWeb()
	if err != nil {
		log.Error("GetDetailWeb err=", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	// 修改状态
	for i, result := range itemResults {
		for i2, item := range q.Items {
			if result.Id == item.ItemResultId {
				q.Items[i2].ItemStatus = 16
				itemResults[i].Status = 16
			}
		}
	}

	recordStatusCountSlice := make([]int64, 5) // [正常数量，待整改数量，待解决数量，已解决数量，已关闭数量]
	for _, i := range itemResults {
		switch i.Status {
		case 1:
			recordStatusCountSlice[0]++
		case 2:
			recordStatusCountSlice[1]++
		case 4:
			recordStatusCountSlice[2]++
		case 8:
			recordStatusCountSlice[3]++
		case 16:
			recordStatusCountSlice[4]++
		}
	}
	q.StatusCount = util.SliceItoA(recordStatusCountSlice) //记录的门检项目状态统计

	err = (&q.DoorCheckRecords).Edit(q.Items)
	if err != nil {
		log.Error("doorcheck.EditResult err=", err.Error())
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)

}

// 门检结果激活关闭的重复
func (d *DoorCheck) ActiveResult(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Records
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.IdStr != "" {

		q.Id, _ = strconv.ParseInt(q.IdStr, 10, 64)

	}

	if q.Id == 0 {
		log.Error("q.Id == 0", err)
		return response.Error(rsp, response.ParamsMissing)
	}

	for i, item := range q.Items {
		if item.ItemResultIdStr == "" {
			continue
		}
		q.Items[i].ItemResultId, _ = strconv.ParseInt(item.ItemResultIdStr, 10, 64)
	}

	// 查询整改之前的门检
	dr := &safety.DoorCheckItemResult{}
	dr.Id = q.Id
	itemResults, err := dr.GetDetailWeb()
	if err != nil {
		log.Error("GetDetailWeb err=", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	// 修改状态
	for i, result := range itemResults {
		for i2, item := range q.Items {
			if result.Id == item.ItemResultId {
				q.Items[i2].ItemStatus = 2
				itemResults[i].Status = 2
			}
		}
	}

	recordStatusCountSlice := make([]int64, 5) // [正常数量，待整改数量，待解决数量，已解决数量，已关闭数量]
	for _, i := range itemResults {
		switch i.Status {
		case 1:
			recordStatusCountSlice[0]++
		case 2:
			recordStatusCountSlice[1]++
		case 4:
			recordStatusCountSlice[2]++
		case 8:
			recordStatusCountSlice[3]++
		case 16:
			recordStatusCountSlice[4]++
		}
	}
	q.StatusCount = util.SliceItoA(recordStatusCountSlice) //记录的门检项目状态统计

	err = (&q.DoorCheckRecords).Edit(q.Items)
	if err != nil {
		log.Error("doorcheck.EditResult err=", err.Error())
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)

}

// ReportResult 门检结果上报
func (d *DoorCheck) ReportResult(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Records
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	user := auth.User(ctx).GetUser()
	// 通知司机信息 staffId, name, phone
	var driverNotice InspectorInfo

	if q.IdStr != "" {

		q.Id, _ = strconv.ParseInt(q.IdStr, 10, 64)

	}

	if q.Id == 0 {
		log.Error("q.Id == 0", err)
		return response.Error(rsp, response.ParamsMissing)
	}

	for i, item := range q.Items {

		q.Items[i].NewNoticeId = strconv.FormatInt(model.Id(), 10)

		if item.ItemResultIdStr != "" {
			q.Items[i].ItemResultId, _ = strconv.ParseInt(item.ItemResultIdStr, 10, 64)
		}

	}

	// 查询整改之前的门检
	dr := &safety.DoorCheckItemResult{}
	dr.Id = q.Id
	itemResults, err := dr.GetDetailWeb()
	if err != nil {
		log.Error("GetDetailWeb err=", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	var editIndex int

	// 修改状态 q.items 其实只有一个
	for i, result := range itemResults {
		for i2, item := range q.Items {
			if result.Id == item.ItemResultId {
				q.Items[i2].ItemStatus = 4
				itemResults[i].Status = 4
				editIndex = i

				oetStaffItem := rpc.GetStaffWithId(ctx, itemResults[i].StaffId)
				if oetStaffItem == nil {
					continue
				}
				driverUser := rpc.GetUserInfoByPhone(ctx, oetStaffItem.CorporationId, oetStaffItem.Phone)
				if driverUser != nil {
					driverNotice.UserId = driverUser.Id
					driverNotice.Name = driverUser.Nickname
					driverNotice.Phone = driverUser.Phone
				}

				q.Items[i2].ReportUserId = user.Id
				q.Items[i2].ReportUserName = user.Name
			}
		}
	}

	recordStatusCountSlice := make([]int64, 5) // [正常数量，待整改数量，待解决数量，已解决数量，已关闭数量]
	for _, i := range itemResults {
		switch i.Status {
		case 1:
			recordStatusCountSlice[0]++
		case 2:
			recordStatusCountSlice[1]++
		case 4:
			recordStatusCountSlice[2]++
		case 8:
			recordStatusCountSlice[3]++
		case 16:
			recordStatusCountSlice[4]++
		}
	}
	q.StatusCount = util.SliceItoA(recordStatusCountSlice) //记录的门检项目状态统计

	if len(itemResults) == 0 {
		log.Error("len(itemResults) == 0")
		return response.Success(rsp, nil)
	}

	err = (&q.DoorCheckRecords).Report(q.Items, user.Id, itemResults[editIndex].FkItemId, user.Name, itemResults[editIndex].ItemName)

	if err != nil {
		log.Error("doorcheck.ReportResultEdit err=", err.Error())
		return response.Error(rsp, response.DbUpdateFail)
	}

	// 发送通知 上报
	mini := config.Config.Mini
	for _, item := range q.Items {
		pagePath := fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%s&Type=%d`, mini.Page, q.Id, itemResults[editIndex].FkItemId, item.NewNoticeId, REPORT_2)

		sendData := []*protoMini.UniformMsgKeyValue{
			{
				Key:   "first",
				Value: "台州公交车辆检查通知",
				Color: "#000000",
			},
			{
				Key:   "keyword1",
				Value: fmt.Sprintf(`[异常上报] %s，%s，%s`, itemResults[editIndex].Line, itemResults[editIndex].License, driverNotice.Name),
				Color: "#000000",
			},
			{
				Key:   "keyword2",
				Value: fmt.Sprintf(`%s`, item.NoticeUserName),
				Color: "#000000",
			},
			{
				Key:   "remark",
				Value: "请尽快进行处理",
				Color: "#000000",
			},
		}
		// rpc
		recv := rpc.GetUserInfoById(ctx, item.NoticeUserId)
		if recv != nil && recv.Phone != "" {
			err := rpc.SendUniformMsg(ctx, mini.AppAccountId, recv.Phone, mini.DoorCheckTemplateId, pagePath, sendData)
			if err != nil {
				log.Error("rpc.SendUniformMsg err = ", err)
			}
		}

		// send to driver
		pagePath = fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%s&Type=%d`, mini.Page, q.Id, itemResults[editIndex].FkItemId, item.NewNoticeId, ABNORMAL_RESOLVE_4)
		sendData[1].Value = fmt.Sprintf(`[整改进展] %s，%s，%s`, itemResults[editIndex].Line, itemResults[editIndex].License, driverNotice.Name)
		err := rpc.SendUniformMsg(ctx, mini.AppAccountId, driverNotice.Phone, mini.DoorCheckTemplateId, pagePath, sendData)
		if err != nil {
			log.Error("rpc.SendUniformMsg err = ", err)
		}

	}

	return response.Success(rsp, nil)
}

// 修改门检结果上报消息
func (d *DoorCheck) EditReportResult(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Records
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	user := auth.User(ctx).GetUser()

	// 通知司机信息 staffId, name, phone
	var driverNotice InspectorInfo

	if q.IdStr != "" {

		q.Id, _ = strconv.ParseInt(q.IdStr, 10, 64)

	}

	if q.Id == 0 {
		log.Error("q.Id == 0", err)
		return response.Error(rsp, response.ParamsMissing)
	}

	for i, item := range q.Items {

		q.Items[i].NewNoticeId = strconv.FormatInt(model.Id(), 10)

		if item.ItemResultIdStr != "" {
			q.Items[i].ItemResultId, _ = strconv.ParseInt(item.ItemResultIdStr, 10, 64)
		}

	}

	// 查询整改之前的门检
	dr := &safety.DoorCheckItemResult{}
	dr.Id = q.Id
	itemResults, err := dr.GetDetailWeb()
	if err != nil {
		log.Error("GetDetailWeb err=", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	var editIndex int

	// 修改状态 q.items 其实只有一个
	for i, result := range itemResults {
		for i2, item := range q.Items {
			if result.Id == item.ItemResultId {
				editIndex = i
				oetStaffItem := rpc.GetStaffWithId(ctx, itemResults[i].StaffId)
				if oetStaffItem == nil {
					continue
				}
				driverUser := rpc.GetUserInfoByPhone(ctx, oetStaffItem.CorporationId, oetStaffItem.Phone)
				if driverUser != nil {
					driverNotice.UserId = driverUser.Id
					driverNotice.Name = driverUser.Nickname
					driverNotice.Phone = driverUser.Phone
				}

				q.Items[i2].ReportUserId = user.Id
				q.Items[i2].ReportUserName = user.Name
			}
		}
	}

	if len(itemResults) == 0 {
		log.Error("len(itemResults) == 0")
		return response.Success(rsp, nil)
	}

	err = (&q.DoorCheckRecords).EditReport(q.Items, user.Id, itemResults[editIndex].FkItemId, user.Name, itemResults[editIndex].ItemName)

	if err != nil {
		log.Error("doorcheck.ReportResultEdit err=", err.Error())
		return response.Error(rsp, response.DbUpdateFail)
	}

	// 发送通知
	mini := config.Config.Mini

	for _, item := range q.Items {
		pagePath := fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%s&Type=%d`, mini.Page, q.Id, itemResults[editIndex].FkItemId, item.NewNoticeId, REPORT_2)
		sendData := []*protoMini.UniformMsgKeyValue{
			{
				Key:   "first",
				Value: "台州公交车辆检查通知",
				Color: "#000000",
			},
			{
				Key:   "keyword1",
				Value: fmt.Sprintf(`[异常上报] %s，%s，%s`, itemResults[editIndex].Line, itemResults[editIndex].License, driverNotice.Name),
				Color: "#000000",
			},
			{
				Key:   "keyword2",
				Value: fmt.Sprintf(`%s`, item.NoticeUserName),
				Color: "#000000",
			},
			{
				Key:   "remark",
				Value: "请尽快进行处理",
				Color: "#000000",
			},
		}
		// rpc
		recv := rpc.GetUserInfoById(ctx, item.NoticeUserId)
		if recv != nil && recv.Phone != "" {
			err := rpc.SendUniformMsg(ctx, mini.AppAccountId, recv.Phone, mini.DoorCheckTemplateId, pagePath, sendData)
			if err != nil {
				log.Error("rpc.SendUniformMsg err = ", err)
			}
		}

		// send to driver
		pagePath = fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%s&Type=%d`, mini.Page, q.Id, itemResults[editIndex].FkItemId, item.NewNoticeId, ABNORMAL_RESOLVE_4)
		sendData[1].Value = fmt.Sprintf(`[整改进展] %s，%s，%s`, itemResults[editIndex].Line, itemResults[editIndex].License, driverNotice.Name)
		err := rpc.SendUniformMsg(ctx, mini.AppAccountId, driverNotice.Phone, mini.DoorCheckTemplateId, pagePath, sendData)
		if err != nil {
			log.Error("rpc.SendUniformMsg err = ", err)
		}

	}

	return response.Success(rsp, nil)
}

// 司机 管理员 扫一扫过渡页
func (d *DoorCheck) IsChecked(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Records
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	q.DoorCheckRecords.VehicleCode = q.VehicleCode

	checked, err := (&q.DoorCheckRecords).IsVehicleChecked(time.Unix(q.StartAt, 0), time.Unix(q.EndAt, 0))
	if err != nil {
		log.Error("doorcheck.IsChecked err=", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	data := map[string]interface{}{
		"Item": checked,
	}

	return response.Success(rsp, data)
}

type DetailResponse struct {
	safety.DoorCheckItemResult
	IdStr       string // id 转 string 项目id 兼容微信
	IconFileUrl string // 门检项目icon
	FkItemIdStr string // 兼容微信
	Report      safety.Notice
	Resolve     safety.Notice
}

// app结果列表获取详情
func (d *DoorCheck) GetAppResultDetail(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q ItemResult
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.IdStr != "" {

		q.Id, _ = strconv.ParseInt(q.IdStr, 10, 64)

	}

	if q.Id == 0 {
		log.Error("q.Id == 0", err)
		return response.Error(rsp, response.ParamsMissing)
	}

	var record safety.DoorCheckRecords
	record.Id = q.Id
	checkRecords, err := (&record).GetById()
	if err != nil {
		log.Error("GetByIdl err= ", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	// id 接收的参数实际为 DoorCheckRecords.Id // 门检记录的id
	resultDetail, err := (&q.DoorCheckItemResult).GetDetailWeb()
	if err != nil {
		log.Error("doorcheck.GetResultDetail err= ", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	// 过滤没有上传记录的结果
	//for _, i := range resultDetail {
	//	if len(i.ResultJson.NotRectified.Labels) == 0 && len(i.ResultJson.NotRectified.Files) == 0 && i.ResultJson.NotRectified.Remark == "" {
	//		continue
	//	} else {
	//		if len(i.ResultJson.Rectified) > 1 {
	//			i.ResultJson.Rectified = i.ResultJson.Rectified[len(i.ResultJson.Rectified)-1:]
	//		}
	//		rspData = append(rspData, i)
	//	}
	//}

	var rspD []DetailResponse
	for _, result := range resultDetail {
		var item safety.DoorCheckItems
		item.Id = result.FkItemId
		var iconUrl string

		doorCheckItemGetById, err := (&item).GetById()
		if err != nil {
			log.Error("GetById err= ", err)
		} else {
			iconUrl = doorCheckItemGetById.IconFileUrl
		}

		rspItem := DetailResponse{
			DoorCheckItemResult: result,
			IdStr:               strconv.FormatInt(result.Id, 10),
			FkItemIdStr:         strconv.FormatInt(result.FkItemId, 10),
			IconFileUrl:         iconUrl,
		}

		// 待处理 已处理 才查询消息
		if result.Status > 2 && result.Status < 16 {
			noticeList, err := (&safety.DoorCheckNotices{}).GetByItemId(q.Id, result.FkItemId)
			if err != nil {
				log.Error("GetByItemId err= ", err)
			} else {
				if noticeList != nil {
					rspItem.Report = noticeList[0]
					rspItem.Resolve = noticeList[1]

					userInfo := rpc.GetUserInfoById(ctx, rspItem.Report.RecvUserId)
					if userInfo != nil {
						rspItem.Report.RecvUserCorporation = userInfo.Corporation
					}
				}
			}
		}

		rspD = append(rspD, rspItem)
	}

	data := map[string]interface{}{
		"Items":  rspD,
		"Record": checkRecords,
	}

	return response.Success(rsp, data)
}

// ProcessResult 门检结果处理
func (d *DoorCheck) ProcessResult(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Records
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.IdStr != "" {
		q.Id, _ = strconv.ParseInt(q.IdStr, 10, 64)

	}

	if q.Id == 0 {
		log.Error("q.Id == 0", err)
		return response.Error(rsp, response.ParamsMissing)
	}

	for i, item := range q.Items {
		q.Items[i].NewNoticeId = strconv.FormatInt(model.Id(), 10)

		if item.ItemResultIdStr != "" {
			q.Items[i].ItemResultId, _ = strconv.ParseInt(item.ItemResultIdStr, 10, 64)
		}

	}
	user := auth.User(ctx).GetUser()

	// 查询整改之前的门检
	dr := &safety.DoorCheckItemResult{}
	dr.Id = q.Id
	itemResults, err := dr.GetDetailWeb()
	if err != nil {
		log.Error("GetDetailWeb err=", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	var editIndex int
	// 修改状态
	for i, result := range itemResults {
		for i2, item := range q.Items {
			if result.Id == item.ItemResultId {
				q.Items[i2].ItemStatus = 8
				itemResults[i].Status = 8
				editIndex = i
				q.Items[i2].ResolveUserId = user.Id
				q.Items[i2].ResolveUserName = user.Name
			}
		}
	}

	recordStatusCountSlice := make([]int64, 5) // [正常数量，待整改数量，待解决数量，已解决数量，已关闭数量]
	for _, i := range itemResults {
		switch i.Status {
		case 1:
			recordStatusCountSlice[0]++
		case 2:
			recordStatusCountSlice[1]++
		case 4:
			recordStatusCountSlice[2]++
		case 8:
			recordStatusCountSlice[3]++
		case 16:
			recordStatusCountSlice[4]++
		}
	}
	q.StatusCount = util.SliceItoA(recordStatusCountSlice) //记录的门检项目状态统计

	err = (&q.DoorCheckRecords).Process(q.Items, user.Id, itemResults[editIndex].FkItemId, user.Name, itemResults[editIndex].ItemName)

	if err != nil {
		log.Error("doorcheck.ReportResultEdit err=", err.Error())
		return response.Error(rsp, response.DbUpdateFail)
	}
	if len(itemResults) == 0 {
		log.Error("len(itemResults) == 0")
		return response.Success(rsp, nil)
	}

	// 发送通知
	mini := config.Config.Mini
	for _, item := range q.Items {
		pagePath := fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%s&Type=%d`, mini.Page, q.Id, itemResults[editIndex].FkItemId, item.NewNoticeId, RESOLVE_8)

		sendData := []*protoMini.UniformMsgKeyValue{
			{
				Key:   "first",
				Value: "台州公交车辆检查通知",
				Color: "#000000",
			},
			{
				Key:   "keyword1",
				Value: fmt.Sprintf(`[整改结果] %s，%s，%s`, itemResults[editIndex].Line, itemResults[editIndex].License, itemResults[editIndex].StaffName),
				Color: "#000000",
			},
			{
				Key:   "keyword2",
				Value: "",
				Color: "#000000",
			},
			{
				Key:   "remark",
				Value: "请尽快进行处理",
				Color: "#000000",
			},
		}
		// 发送给上报人
		notices, err := (&safety.DoorCheckNotices{}).GetByItemId(q.Id, itemResults[editIndex].FkItemId)
		if err != nil {
			log.Error("GetByItemId err=", err.Error())
		} else {
			recv := rpc.GetUserInfoById(ctx, notices[0].SendUserId)
			sendData[2].Value = fmt.Sprintf(`%s`, recv.Nickname)
			if recv != nil && recv.Phone != "" {
				err := rpc.SendUniformMsg(ctx, mini.AppAccountId, recv.Phone, mini.DoorCheckTemplateId, pagePath, sendData)
				if err != nil {
					log.Error("rpc.SendUniformMsg err = ", err)
				}
			}
		}
		sendData[2].Value = fmt.Sprintf(`%s`, itemResults[editIndex].StaffName)
		recv2driver := rpc.GetStaffWithId(ctx, itemResults[editIndex].StaffId)
		if recv2driver != nil && recv2driver.Phone != "" {
			err := rpc.SendUniformMsg(ctx, mini.AppAccountId, recv2driver.Phone, mini.DoorCheckTemplateId, pagePath, sendData)
			if err != nil {
				log.Error("rpc.SendUniformMsg err = ", err)
			}
		}

	}

	return response.Success(rsp, nil)

}

// 修改门检结果处理
func (d *DoorCheck) EditProcessResult(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var q Records
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.IdStr != "" {
		q.Id, _ = strconv.ParseInt(q.IdStr, 10, 64)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0", err)
		return response.Error(rsp, response.ParamsMissing)
	}

	for i, item := range q.Items {
		q.Items[i].NewNoticeId = strconv.FormatInt(model.Id(), 10)

		if item.ItemResultIdStr != "" {
			q.Items[i].ItemResultId, _ = strconv.ParseInt(item.ItemResultIdStr, 10, 64)
		}

	}

	// 查询整改之前的门检
	dr := &safety.DoorCheckItemResult{}
	dr.Id = q.Id
	itemResults, err := dr.GetDetailWeb()
	if err != nil {
		log.Error("GetDetailWeb err=", err.Error())
		return response.Error(rsp, response.DbQueryFail)
	}

	user := auth.User(ctx).GetUser()

	var editIndex int
	// 修改状态 q.items 其实只有一个
	for i, result := range itemResults {
		for i2, item := range q.Items {
			if result.Id == item.ItemResultId {

				editIndex = i
				q.Items[i2].ResolveUserId = user.Id
				q.Items[i2].ResolveUserName = user.Name
			}
		}
	}

	if len(itemResults) == 0 {
		log.Error("len(itemResults) == 0")
		return response.Success(rsp, nil)
	}

	err = (&q.DoorCheckRecords).EditProcess(q.Items, user.Id, itemResults[editIndex].FkItemId, user.Name, itemResults[editIndex].ItemName)

	if err != nil {
		log.Error("doorcheck.ReportResultEdit err=", err.Error())
		return response.Error(rsp, response.DbUpdateFail)
	}

	// 发送通知
	mini := config.Config.Mini
	for _, item := range q.Items {
		pagePath := fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%s&Type=%d`, mini.Page, q.Id, itemResults[editIndex].FkItemId, item.NewNoticeId, RESOLVE_8)

		sendData := []*protoMini.UniformMsgKeyValue{
			{
				Key:   "first",
				Value: "台州公交车辆检查通知",
				Color: "#000000",
			},
			{
				Key:   "keyword1",
				Value: fmt.Sprintf(`[整改结果] %s，%s，%s`, itemResults[editIndex].Line, itemResults[editIndex].License, itemResults[editIndex].StaffName),
				Color: "#000000",
			},
			{
				Key:   "keyword2",
				Value: "",
				Color: "#000000",
			},
			{
				Key:   "remark",
				Value: "请尽快进行处理",
				Color: "#000000",
			},
		}
		// 发送给上报人
		notices, err := (&safety.DoorCheckNotices{}).GetByItemId(q.Id, itemResults[editIndex].FkItemId)
		if err != nil {
			log.Error("GetByItemId err=", err.Error())
		} else {
			recv := rpc.GetUserInfoById(ctx, notices[0].SendUserId)
			if recv != nil && recv.Phone != "" {
				sendData[2].Value = fmt.Sprintf(`%s`, recv.Nickname)
				err := rpc.SendUniformMsg(ctx, mini.AppAccountId, recv.Phone, mini.DoorCheckTemplateId, pagePath, sendData)
				if err != nil {
					log.Error("rpc.SendUniformMsg err = ", err)
				}
			}
		}
		recv2driver := rpc.GetStaffWithId(ctx, itemResults[editIndex].StaffId)
		if recv2driver != nil && recv2driver.Phone != "" {
			sendData[2].Value = fmt.Sprintf(`%s`, itemResults[editIndex].StaffName)
			err := rpc.SendUniformMsg(ctx, mini.AppAccountId, recv2driver.Phone, mini.DoorCheckTemplateId, pagePath, sendData)
			if err != nil {
				log.Error("rpc.SendUniformMsg err = ", err)
			}
		}

	}

	return response.Success(rsp, nil)

}

// SyncPreGenerateRecord 定时任务失败时 同步门检预生成记录
func (d *DoorCheck) SyncPreGenerateRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	// 查询目前所有车辆
	vehicleItems, _ := rpc.GetVehiclesWithTopCorporationId(context.Background(), config.Config.TopCorporationId)

	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	todayEnd := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 1)

	s := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	e := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 9999, time.Local)

	// 查询数据库
	records, err := (&safety.DoorCheckRecords{}).FindRecord(config.Config.TopCorporationId, todayStart, todayEnd)
	if err != nil {
		log.Error("FindRecord err=", err.Error())
		return response.Error(rsp, response.DbUpdateFail)
	}

	topCorpId := config.Config.TopCorporationId
	if topCorpId == 0 {
		log.Error("topCorpId == 0")
		return response.Error(rsp, response.Forbidden)
	}

	iss := config.Config.IsRelationIss

	// 对比
	for _, item := range vehicleItems {
		//
		var isOut, isBack bool // 是否存在出场、回场门检
		var addData []safety.DoorCheckRecords

		for _, record := range records {
			if item.License == record.License {
				if record.CheckForm == 1 {
					isOut = true
				} else if record.CheckForm == 2 {
					isBack = true
				}
			}
		}

		// 出场门检记录
		outRecord := safety.DoorCheckRecords{
			PkId:                  model.PkId{Id: model.Id()},
			GroupCorporation:      model.GroupCorporation{GroupId: topCorpId},
			CompanyCorporation:    model.CompanyCorporation{},
			BranchCorporation:     model.BranchCorporation{},
			DepartmentCorporation: model.DepartmentCorporation{},
			FleetCorporation:      model.FleetCorporation{},
			License:               item.License,
			VehicleCode:           item.Code,
			LineId:                0,
			Line:                  "",
			StaffId:               0,
			StaffName:             "",
			Type:                  1,
			CheckForm:             1,
			Result:                0,
			StatusCount:           "0,0,0,0,0",
			HandleStatus:          safety.PRE_GENERATE_1,
			Schedule:              safety.NO_SCHEDULE_2,
			Timestamp:             model.Timestamp{},
		}

		// 回场门检记录
		backRecord := safety.DoorCheckRecords{
			PkId:                  model.PkId{Id: model.Id()},
			GroupCorporation:      model.GroupCorporation{GroupId: topCorpId},
			CompanyCorporation:    model.CompanyCorporation{},
			BranchCorporation:     model.BranchCorporation{},
			DepartmentCorporation: model.DepartmentCorporation{},
			FleetCorporation:      model.FleetCorporation{},
			License:               item.License,
			VehicleCode:           item.Code,
			LineId:                0,
			Line:                  "",
			StaffId:               0,
			StaffName:             "",
			Type:                  1,
			CheckForm:             2,
			Result:                0,
			StatusCount:           "0,0,0,0,0",
			HandleStatus:          safety.PRE_GENERATE_1,
			Schedule:              safety.NO_SCHEDULE_2,
			Timestamp:             model.Timestamp{},
		}

		if iss {
			firstAndLast := rpc.GetFirstLastPlanScheduleWithVehicleId(context.Background(), &protoschedule.GetFirstLastPlanScheduleWithVehicleIdRequest{
				VehicleId:         item.Id,
				ExpectDepartAtGte: s.Unix(),
				ExpectDepartAtLte: e.Unix(),
				Status:            0,
				PlanSchType:       -1,
			})

			if firstAndLast != nil {
				outRecord.Schedule = safety.YES_SCHEDULE_1
				backRecord.Schedule = safety.YES_SCHEDULE_1
			}

			for _, idItem := range firstAndLast {
				if idItem.Type == 1 { // 早班
					outRecord.StaffId = idItem.PlanDriverId // 计划司机

					staff := rpc.GetStaffWithId(context.Background(), outRecord.StaffId)
					if staff != nil {
						outRecord.StaffName = staff.Name
					}

					outRecord.LineId = idItem.LineId

				} else if idItem.Type == 2 { // 晚班
					backRecord.StaffId = idItem.PlanDriverId

					staff := rpc.GetStaffWithId(context.Background(), backRecord.StaffId)
					if staff != nil {
						backRecord.StaffName = staff.Name
					}

					backRecord.LineId = idItem.LineId

				}
			}

		}

		if !isOut {
			addData = append(addData, outRecord)
		}

		if !isBack {
			addData = append(addData, backRecord)
		}

		if len(addData) > 0 {
			// 存数据库
			err := (&safety.DoorCheckRecords{}).Adds(addData)
			if err != nil {
				log.Error("DoorCheckRecords Adds err ==", err)
				//errLicenses = append(errLicenses, item.License)
				continue
			}
		}
	}

	// 插入预生成数据

	return nil
}
