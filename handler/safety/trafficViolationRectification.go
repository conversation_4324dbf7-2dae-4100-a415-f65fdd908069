package safety

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/message"
	"app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	messageService "app/org/scs/erpv2/api/service/message"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	"github.com/micro/go-micro/v2/api/proto"
	"time"
)

type TrafficViolationRectification struct {
	safety.TrafficViolationRectification
	SubmitCorporationIds, CorporationIds                   []int64
	SubmitStartAt, SubmitEndAt, HandleStartAt, HandleEndAt model.LocalTime
	QualityAssessmentCateAttr                              int64 `json:"QualityAssessmentCateAttr"`
	model.Paginator
}

type DriverHelpParam struct {
	HelpDriverId int64           `json:"HelpDriverId"`
	HelpStartAt  model.LocalTime `json:"HelpStartAt"`
	HelpFinishAt model.LocalTime `json:"HelpFinishAt"`
	HelpReason   string          `json:"HelpReason"`
}

func (tvr *TrafficViolationRectification) Add(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TrafficViolationRectification
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(param)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	violation := (&safety.TrafficViolation{}).FindBy(param.TrafficViolationId)

	if violation.Id == 0 {
		log.ErrorFields("TrafficViolation FindBy not fund", map[string]interface{}{"violationId": param.TrafficViolationId})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.ObjectStaffId = violation.DriverStaffId
	param.ObjectStaffName = violation.DriverStaffName

	param.Corporations.Build(param.CorporationId)
	param.SubmitCorporationId = auth.User(ctx).GetCorporationId()

	userItem := rpc.GetUserInfoById(ctx, param.HeadUserId)
	if userItem != nil {
		param.HeadUserName = userItem.Nickname
	}

	tx := model.DB().Begin()

	err = param.TrafficViolationRectification.TransactionCreate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("TrafficViolationRectification.TransactionCreate error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	//更新违规状态=>已下发
	err = violation.TransactionUpdateStatus(tx, util.TrafficViolationStatusForSend)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("TrafficViolation.TransactionUpdateStatus error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}
	tx.Commit()

	go sendRectificationMessage(ctx, &param.TrafficViolationRectification)

	return response.Success(rsp, nil)
}

func sendRectificationMessage(ctx context.Context, rectification *safety.TrafficViolationRectification) {
	paramBytes, _ := json.Marshal(rectification)
	var msg message.Message
	msg.Id = model.Id()
	msg.GroupId = rectification.GroupId
	msg.Type = rectification.MessageType()
	msg.RelationId = rectification.Id
	msg.RelationTableName = rectification.TableName()
	msg.RelationParam = paramBytes

	user := auth.User(ctx).GetUser()
	msg.SendUserId = user.Id
	msg.SendUserName = user.Name

	msg.RecvUserId = rectification.HeadUserId
	msg.RecvUserName = rectification.HeadUserName
	msg.ReadStatus = message.UNREAD_1
	msg.ReadType = message.MESSAGE_READ_1
	msg.Origin = msg.Type
	msg.Kind = message.NOTIFICATION_3
	msg.Title = "违规通知"
	msg.Content = ""
	receiver := rpc.GetUserInfoById(ctx, msg.RecvUserId)
	if receiver == nil {
		log.ErrorFields("GetUserInfoById receiver is nil", map[string]interface{}{"staffId": msg.RecvUserId})
		return
	}

	messageService.NewSendMessage(
		msg,
		messageService.WithWxMessage(
			"违规通知",
			[]string{msg.Title, fmt.Sprintf("发送人：%s", msg.SendUserName)},
			"请尽快到ERP后台管理系统进行处理",
			"",
			receiver.Phone,
		),
	).Send()
}

func (tvr *TrafficViolationRectification) List(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TrafficViolationRectification
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	corporationIds := service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	var cateAttrs []int64
	if param.QualityAssessmentCateAttr > 0 {
		cateAttrs = []int64{param.QualityAssessmentCateAttr}
	}
	rectifications, totalCount := (&safety.TrafficViolationRectification{}).List(param.ObjectStaffName, param.License, param.HandleStatus, cateAttrs, param.SubmitCorporationIds, corporationIds,
		time.Time(param.SubmitStartAt), time.Time(param.SubmitEndAt), time.Time(param.HandleStartAt), time.Time(param.HandleEndAt), param.Paginator)

	for i := range rectifications {
		rectifications[i].CorporationId, rectifications[i].CorporationName = rectifications[i].Corporations.GetCorporation()

		submitCorporation := rpc.GetCorporationById(ctx, rectifications[i].SubmitCorporationId)
		if submitCorporation != nil {
			rectifications[i].SubmitCorporationName = submitCorporation.Name
		}

		violation := (&safety.TrafficViolation{}).FindBy(rectifications[i].TrafficViolationId)
		rectifications[i].License = violation.License
		rectifications[i].ViolationStandards = violation.Standards
	}

	return response.Success(rsp, map[string]interface{}{"Items": rectifications, "TotalCount": totalCount, "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

func (tvr *TrafficViolationRectification) GetDetail(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TrafficViolationRectification
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	var rectification safety.TrafficViolationRectification
	if param.TrafficViolationId > 0 {
		rectification = (&safety.TrafficViolationRectification{}).FirstByViolationId(param.TrafficViolationId)
	} else {
		rectification = (&safety.TrafficViolationRectification{}).FirstById(param.Id)
	}

	if rectification.Id > 0 {
		rectification.CorporationId, rectification.CorporationName = rectification.Corporations.GetCorporation()

		submitCorporation := rpc.GetCorporationById(ctx, rectification.SubmitCorporationId)
		if submitCorporation != nil {
			rectification.SubmitCorporationName = submitCorporation.Name
		}
	}
	rectification.FileHttpPrefix = config.Config.StaticFileHttpPrefix

	return response.Success(rsp, rectification)
}

func (tvr *TrafficViolationRectification) Handle(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TrafficViolationRectification
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 || param.HandleUserId == 0 || time.Time(param.HandleAt).IsZero() {
		return response.Error(rsp, response.ParamsMissing)
	}

	rectification := (&safety.TrafficViolationRectification{}).FirstById(param.Id)
	if rectification.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	handleUser := rpc.GetUserInfoById(ctx, param.HandleUserId)
	if handleUser == nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.HandleUserName = handleUser.Nickname
	param.TrafficViolationRectification.HandleStatus = util.RectificationHandleStatusForChecking

	tx := model.DB().Begin()
	err = param.TrafficViolationRectification.TransactionUpdateHandle(tx)

	if err != nil {
		tx.Rollback()
		log.ErrorFields("TrafficViolationRectification.TransactionUpdateHandle error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	//更新违规状态为待审核
	violation := (&safety.TrafficViolation{}).FindBy(rectification.TrafficViolationId)

	if violation.Id == 0 {
		tx.Rollback()
		log.ErrorFields("TrafficViolation FindBy not fund", map[string]interface{}{"violationId": param.TrafficViolationId})
		return response.Error(rsp, response.ParamsInvalid)
	}

	//更新违规状态=>待审核
	err = violation.TransactionUpdateStatus(tx, util.TrafficViolationStatusForChecking)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("TrafficViolation.TransactionUpdateStatus error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}
	tx.Commit()

	//加入司机帮扶
	//if param.IsHelp == util.StatusForTrue {
	//	go addDriverHelp(&rectification)
	//}

	//更新消息
	go updateMessage(auth.User(ctx).GetUserId(), &rectification)

	return response.Success(rsp, nil)
}

func addDriverHelp(rectification *safety.TrafficViolationRectification) {
	if rectification.IsDriverHelp == util.StatusForFalse {
		log.ErrorFields("addDriverHelp no need!", nil)
		return
	}
	var help safety.SafeEmphasisHelpDrivers
	help.Id = model.Id()
	help.ViolationRectificationId = rectification.Id
	var driverHelpParam DriverHelpParam
	err := json.Unmarshal(rectification.DriverHelpParam, &driverHelpParam)
	if err != nil {
		log.ErrorFields("driverHelpParam json.Unmarshal error", map[string]interface{}{"err": err})
		return
	}

	driver := rpc.GetStaffWithId(context.Background(), driverHelpParam.HelpDriverId)
	if driver == nil {
		log.ErrorFields("rpc.GetStaffWithId is nil", map[string]interface{}{"staffId": rectification.ObjectStaffId})
		return
	}
	help.Corporations.Build(driver.CorporationId)

	help.StaffId = driver.Id
	help.StaffName = driver.Name
	help.StaffIdStr = driver.StaffId
	help.CorporationId, help.Corporation = help.Corporations.GetCorporation()
	help.AddAt = time.Time(driverHelpParam.HelpStartAt)
	help.FinishAt = time.Time(driverHelpParam.HelpFinishAt)
	help.CauseContent = driverHelpParam.HelpReason

	help.Terminate = safety.TERMINATE_NO_1

	err = help.Add()
	if err != nil {
		log.ErrorFields("help.Add error", map[string]interface{}{"err": err})
	}
}

func updateMessage(userId int64, rectification *safety.TrafficViolationRectification) {
	var msg message.Message
	msg.Type = rectification.MessageType()
	msg.RelationId = rectification.Id
	msg.ReadStatus = message.READ_2
	msg.ProcessStatus = message.RESOLVE_2

	err := msg.EditProcessStatus(model.DB(), userId)
	if err != nil {
		log.ErrorFields("msg.EditProcessStatus error", map[string]interface{}{"err": err})
	}
}

//func (TrafficViolationRectification) Process(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
//	var q Rectification
//	err := json.Unmarshal([]byte(req.Body), &q)
//	if err != nil {
//		log.Error("json.Unmarshal err=", err.Error())
//		return response.Error(rsp, response.ParamsInvalid)
//	}
//
//	err = util.Validator().Struct(q)
//	if err != nil {
//		log.Error("validate.Struct err=", err.Error())
//		return response.Error(rsp, response.ParamsInvalid)
//	}
//
//	if q.Id == 0 || q.SafeRectifications.Process == 0 || q.ProcessAt == 0 || q.ProcessContent == "" {
//		log.Error("q.Type == 0")
//		return response.Error(rsp, response.ParamsMissing)
//	}
//
//	if q.SafeRectifications.Process == safety.TALK_HELP_12 {
//		if q.StaffId == 0 || q.CauseContent == "" || q.AddAt == 0 || q.FinishAt == 0 {
//			log.Error("q.Type == 0")
//			return response.Error(rsp, response.ParamsMissing)
//		}
//	}
//
//	tx := model.DB().Begin()
//	defer func() {
//		if err == nil {
//			tx.Commit()
//		} else {
//			tx.Rollback()
//		}
//	}()
//
//	if q.SafeRectifications.Process == safety.TALK_HELP_12 {
//		var help safety.SafeEmphasisHelpDrivers
//		help.Id = model.Id()
//
//		driver := rpc.GetStaffWithId(ctx, q.StaffId)
//		if driver == nil {
//			log.Error("driver == nil", q.StaffId)
//			return response.Error(rsp, response.Forbidden)
//		}
//
//		detailById := rpc.GetCorporationDetailById(ctx, driver.CorporationId)
//		if detailById != nil {
//			help.GroupId = detailById.GroupId
//			help.CompanyId = detailById.CompanyId
//			help.BranchId = detailById.BranchId
//			help.DepartmentId = detailById.DepartmentId
//			help.FleetId = detailById.FleetId
//			help.Corporation = detailById.Item.Name
//		}
//
//		help.StaffId = q.StaffId
//		help.StaffName = driver.Name
//		help.StaffIdStr = driver.StaffId
//		help.CorporationId = driver.CorporationId
//
//		help.CauseContent = q.CauseContent
//		help.AddAt = time.Unix(q.AddAt, 0)
//		help.FinishAt = time.Unix(q.FinishAt, 0)
//		help.Terminate = safety.TERMINATE_NO_1
//
//		err = (&help).AddTx(tx)
//		if err != nil {
//
//			log.Error("AddTx err == ", err)
//			return response.Error(rsp, response.DbSaveFail)
//		}
//		q.SafeRectifications.FkHelpId = help.Id
//	}
//
//	q.SafeRectifications.ReadStatus = util.RectificationReadStatusForDone
//	q.SafeRectifications.ProcessStatus = util.RectificationProcessStatusForChecked
//	q.SafeRectifications.ReplyAt = time.Now()
//	q.SafeRectifications.ProcessAt = time.Unix(q.ProcessAt, 0)
//
//	err = (&q.SafeRectifications).EditProcess(tx)
//	if err != nil {
//		log.Error("EditProcess err == ", err)
//		return response.Error(rsp, response.DbUpdateFail)
//	}
//
//	// message
//	if auth.User(ctx).HasStaff() {
//		var msg message.Message
//		msg.Type = q.SafeRectifications.MessageType()
//		msg.RelationId = q.SafeRectifications.Id
//		msg.ReadStatus = message.READ_2
//		msg.ProcessStatus = message.RESOLVE_2
//
//		err = (&msg).EditProcessStatus(tx, auth.User(ctx).GetStaffId())
//		if err != nil {
//			log.Error("EditProcessStatus err == ", err)
//			return response.Error(rsp, response.DbUpdateFail)
//		}
//	}
//
//	return response.Success(rsp, nil)
//
//}
//
//func (r *Rectification) ListTalk(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
//	var q Rectification
//	err := json.Unmarshal([]byte(req.Body), &q)
//	if err != nil {
//		log.Error("json.Unmarshal err=", err.Error())
//		return response.Error(rsp, response.ParamsInvalid)
//	}
//
//	err = util.Validator().Struct(q)
//	if err != nil {
//		log.Error("validate.Struct err=", err.Error())
//		return response.Error(rsp, response.ParamsInvalid)
//	}
//
//	//var searchCorpIds []int64
//	//if len(q.SubmitCorporationIds) == 0 && len(q.RecvCorporationIds) == 0 {
//	//	corpId := util.GetUserCorporationId(ctx)
//	//	id := rpc.GetCorporationDetailById(ctx, corpId)
//	//}
//	submitCorporationIds := service.AuthCorporationIdProvider(ctx, q.SubmitCorporationIds)
//	recvCorporationIds := service.AuthCorporationIdProvider(ctx, q.RecvCorporationIds)
//
//	// 处理时间
//	var ss, se, rs, re time.Time
//
//	if q.SubmitStartAt == 0 {
//		ss = time.Time{}
//	} else {
//		ss = time.Unix(q.SubmitStartAt, 0)
//	}
//
//	if q.SubmitEndAt == 0 {
//		se = time.Time{}
//	} else {
//		se = time.Unix(q.SubmitEndAt, 0)
//	}
//
//	if q.ReplyStartAt == 0 {
//		rs = time.Time{}
//	} else {
//		rs = time.Unix(q.ReplyStartAt, 0)
//	}
//
//	if q.ReplyEndAt == 0 {
//		re = time.Time{}
//	} else {
//		re = time.Unix(q.ReplyEndAt, 0)
//	}
//
//	list, totalCount, err := (q.SafeRectifications).ListTalk(q.ReadStatus, q.ProcessStatus, q.SafeRectifications.Process, q.Types, submitCorporationIds, recvCorporationIds,
//		ss, se, rs, re, q.Paginator)
//	if err != nil {
//		log.Error("List err =", err)
//		return response.Error(rsp, response.DbQueryFail)
//	}
//
//	var rspD []RectificationListRsp
//
//	for _, item := range list {
//
//		obj, err := (&safety.SafeRectificationsObject{}).GetByFkId(item.Id)
//		if err != nil {
//			log.Error("GetByFkId err =", err)
//			return response.Error(rsp, response.DbQueryFail)
//		}
//
//		rspD = append(rspD, RectificationListRsp{
//			Object:             obj,
//			SafeRectifications: item,
//		})
//	}
//
//	data := map[string]interface{}{
//		"Items":      rspD,
//		"TotalCount": totalCount,
//	}
//
//	return response.Success(rsp, data)
//}
//
//func (r *Rectification) ReadTalk(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
//	return response.Error(rsp, response.FAIL)
//}
//
//func (r *Rectification) ProcessTalk(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
//	return r.Process(ctx, req, rsp)
//}
//

func (tvr *TrafficViolationRectification) Times(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TrafficViolationRectification
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.ObjectStaffId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	now := time.Now()
	start := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, -1, 0)
	end := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 1)

	times := (&safety.TrafficViolationRectification{}).DriverRectificationCount(param.ObjectStaffId, start, end)
	rectification := (&safety.TrafficViolationRectification{}).DriverLatestRectification(param.ObjectStaffId)

	return response.Success(rsp, map[string]interface{}{
		"Times":                 times,                   // 30天内约谈次数
		"LatestRectificationAt": rectification.CreatedAt, // 30天内最近整改时间
	})
}

func (tvr *TrafficViolationRectification) Revoke(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var param TrafficViolationRectification
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	data := (&safety.TrafficViolationRectification{}).FirstById(param.Id)
	// 只有待审批可以撤回
	if data.HandleStatus != 1 {
		return response.Error(rsp, "OP7522")
	}
	data.HandleStatus = 4 // 设置状态重新回复
	err = (&data).Update()
	if err != nil {
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)
}
