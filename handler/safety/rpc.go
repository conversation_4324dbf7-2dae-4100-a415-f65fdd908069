package safety

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/safety"
	erpProto "app/org/scs/erpv2/api/proto/rpc/erp"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"errors"
	"time"
)

type RpcSafety struct {
}

func (rs *RpcSafety) GetErpDriverViolationRecord(ctx context.Context, req *erpProto.GetErpDriverViolationRecordRequest, rsp *erpProto.GetErpDriverViolationRecordResponse) error {
	log.PrintFields("GetErpDriverViolationRecord Req", map[string]interface{}{"req": req})
	if req.StaffId == 0 || req.StartAt == 0 || req.EndAt == 0 {
		return errors.New("PARAMS MISSING")
	}

	//获取司机违规数据
	records := (&safety.TrafficViolation{}).GetDriverViolation(req.StaffId, time.Unix(req.StartAt, 0), time.Unix(req.EndAt, 0))
	for i := range records {
		var standards []*erpProto.ViolationStandard
		_ = json.Unmarshal(records[i].Standards, &standards)
		rsp.Items = append(rsp.Items, &erpProto.DriverViolationRecord{
			Id:        records[i].Id,
			ReportAt:  time.Time(records[i].ReportAt).Unix(),
			PlaceText: records[i].PlaceText,
			Standards: standards,
			Score:     records[i].DeductScore,
			Content:   records[i].Content,
		})
	}
	return nil
}

func (rs *RpcSafety) GetErpDriverAccidentRecord(ctx context.Context, req *erpProto.GetErpDriverAccidentRecordRequest, rsp *erpProto.GetErpDriverAccidentRecordResponse) error {
	log.PrintFields("GetErpDriverAccidentRecord Req", map[string]interface{}{"req": req})
	if req.StaffId == 0 || req.StartAt == 0 || req.EndAt == 0 {
		return errors.New("PARAMS MISSING")
	}

	//获取司机违规数据
	records := (&safety.TrafficAccident{}).GetDriverAccident(req.StaffId, time.Unix(req.StartAt, 0), time.Unix(req.EndAt, 0))
	for i := range records {
		rsp.Items = append(rsp.Items, &erpProto.DriverAccidentRecord{
			Id:                records[i].Id,
			HappenAt:          time.Time(records[i].HappenAt).Unix(),
			License:           records[i].License,
			LiabilityType:     records[i].LiabilityType,
			PeopleHurtCate:    records[i].PeopleHurtCate,
			VehicleBrokenCate: records[i].VehicleBrokenCate,
			Grade:             records[i].Grade,
		})
	}
	return nil
}

func (rs *RpcSafety) SubmitTrafficAccidentAbnormalStop(ctx context.Context, req *erpProto.SubmitTrafficAccidentAbnormalStopRequest, rsp *erpProto.SubmitTrafficAccidentAbnormalStopResponse) error {
	log.PrintFields("SubmitTrafficAccidentAbnormalStop Req", map[string]interface{}{"req": req})
	rsp.Msg = "OK"
	rsp.Code = response.SUCCESS
	if req.TopCorporationId == 0 || req.CorporationId == 0 || req.VehicleId == 0 || req.ReportAtUnix == 0 {
		rsp.Code = response.ParamsMissing
		rsp.Msg = "PARAMS MISSING"
		return errors.New("PARAMS MISSING")
	}
	record := &safety.VehicleAbnormalStopRecord{
		TopCorporationId: req.TopCorporationId,
		CorporationId:    req.CorporationId,
		VehicleId:        req.VehicleId,
		LineId:           req.LineId,
		DriverId:         req.DriverId,
		ReportAt:         model.LocalTime(time.Unix(req.ReportAtUnix, 0)),
		Reason:           "事故停车",
	}
	corporation := rpc.GetCorporationById(ctx, req.CorporationId)
	if corporation != nil {
		record.CorporationName = corporation.Name
	}
	vehicle := rpc.GetVehicleWithId(ctx, req.VehicleId)
	if vehicle != nil {
		record.License = vehicle.License
	}
	line, _ := rpc.GetLineWithId(ctx, req.LineId)
	if line != nil {
		record.LineName = line.Name
	}

	driver := rpc.GetStaffWithId(ctx, req.DriverId)
	if driver != nil {
		record.DriverName = driver.Name
	}

	err := record.Create()
	if err != nil {
		rsp.Code = response.DbSaveFail
		rsp.Msg = "DB SAVE FAIL"
		return errors.New("DB SAVE FAIL")
	}
	return nil
}
