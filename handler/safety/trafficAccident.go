package safety

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/process/lbpm/listener"
	"app/org/scs/erpv2/api/service/rpc"
	util "app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"strconv"
	"strings"
	"time"
)

// TrafficAccident 事故
type TrafficAccident struct {
	safetyModel.TrafficAccident
	StartHappenAt           *model.LocalTime `json:"StartHappenAt"`
	EndHappenAt             *model.LocalTime `json:"EndHappenAt"`
	CorporationIds          []int64          `json:"CorporationIds"`
	RelaterName             string           `json:"RelaterName"`
	RelaterLicense          string           `json:"RelaterLicense"`
	RelaterContact          string           `json:"RelaterContact"`
	RelaterInsuranceCompany string           `json:"RelaterInsuranceCompany"`
	//ProcessId               string           `json:"ProcessId"`
	//LbpmParam               string           `json:"LbpmParam"`
	TrafficAccidentId int64  `json:"TrafficAccidentId"`
	CorporationIdStr  string `json:"CorporationIdStr"`
	TemplateFormId    string `json:"TemplateFormId"`
	AccidentScene     int64  `json:"AccidentScene"`
	model.Paginator
	Order   string // asc desc
	OrderBy string // 排序字段 ApplyAt(事故提交时间)/HappenAt(事故发生时间)

	FixOffice                int64      //维修车间  1-黄岩客运西站 2-椒江景元路 3-椒江客运总站 4-椒江工人路 5-路桥桐屿 6-路桥金清 7-综合场站 8-回浦  9-洪家
	IsFull                   int64      //赔付方式 1保险赔付 2我方司机赔付 3对方赔付
	SolutionType             int64      //解决方式 1-协商处理 2-交警简易程序 3-法院判决 4-保险公司 5-其他方式
	SolutionFilePath         model.JSON //解决方式对应的附件
	InsuranceCompanyPayMoney int64      //保险公司赔付金额 单位：分
	InsurancePayMoney        int64      //保险直赔金额 单位：分
	LossMoney                int64      //经济损失 单位：分
	SolutionDesc             string     //结案说明
	PayOrigin                int64      //赔付来源 1保险赔付 2我方司机赔付 3对方赔付
	PersonalPayRatio         int64      //损失金额个人承担比例  1028=>10.28%

	ProcessId string `json:"ProcessId"`
	IsRestart bool   `json:"IsRestart"`
}

// List 事故列表
func (ta *TrafficAccident) List(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	accidents, count := accidentList(ctx, param)

	return response.Success(rsp, map[string]interface{}{"Items": accidents, "TotalCount": count, "FileHttpPrefix": config.Config.StaticFileHttpPrefix})

}

// Export 导出事故列表
func (ta *TrafficAccident) Export(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	accidents, count := accidentList(ctx, param)

	return response.Success(rsp, map[string]interface{}{"Items": accidents, "TotalCount": count, "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

func accidentList(ctx context.Context, param TrafficAccident) ([]safetyModel.TrafficAccident, int64) {
	if param.Order == "" {
		param.Order = "desc"
	}
	if param.OrderBy == "" {
		param.OrderBy = "CreatedAt"
	}

	param.CorporationIds = service.AuthCorporationIdProvider(ctx, param.CorporationIds)

	var where = map[string]interface{}{
		"Code":                    param.Code,
		"CorporationIds":          param.CorporationIds,
		"LiabilityType":           param.LiabilityType,
		"DriverName":              param.DriverName,
		"LineId":                  param.LineId,
		"License":                 param.License,
		"HappenLocation":          param.HappenLocation,
		"Grade":                   param.Grade,
		"PeopleHurtCate":          param.PeopleHurtCate,
		"VehicleBrokenCate":       param.VehicleBrokenCate,
		"AccidentScene":           param.AccidentScene,
		"RelaterName":             param.RelaterName,
		"RelaterLicense":          param.RelaterLicense,
		"RelaterContact":          param.RelaterContact,
		"RelaterInsuranceCompany": param.RelaterInsuranceCompany,
		"IsRecycle":               int64(util.StatusForFalse),
		"IsMustFix":               param.IsMustFix,
		"IsFull":                  param.IsFull,
	}
	if param.StartHappenAt != nil {
		where["StartHappenAt"] = time.Time(*param.StartHappenAt).Format(model.TimeFormat)
	}

	if param.EndHappenAt != nil {
		where["EndHappenAt"] = time.Time(*param.EndHappenAt).Format(model.TimeFormat)
	}

	accidents, count := param.TrafficAccident.GetBy(auth.User(ctx).GetUserId(), where, param.Paginator, param.OrderBy, param.Order)
	for i := range accidents {
		accidents[i].Corporations.ParseCorporation()
		accidents[i].CorporationId, accidents[i].CorporationName = accidents[i].Corporations.GetCorporation()
		accidents[i].HasRelater = accidents[i].IsHasRelater(accidents[i].Id)
		accidents[i].TotalBranchCount = (&safetyModel.TrafficAccidentRelaterBranch{}).GetBranchCount(accidents[i].Id, 0)
		accidents[i].ClosedBranchCount = (&safetyModel.TrafficAccidentRelaterBranch{}).GetBranchCount(accidents[i].Id, util.StatusForTrue)

		accidents[i].HasApplyingPaymentMoneyProcess = (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).IsExistDoingPaymentRecord(accidents[i].Id)

		accidents[i].IsEnableClosed = accidents[i].CheckIsEnableClosed() && accidents[i].EditApplyStatus != util.ApplyStatusForDoing && accidents[i].IsClosed != util.StatusForFalse

		for j := range accidents[i].Relaters {
			accidents[i].Relaters[j].IsSelfVehicleBroken = (&safetyModel.TrafficAccidentRelaterBranch{}).IsExistBranchType(accidents[i].Relaters[j].Id, util.AccidentBranchTypeForSelfVehicleBroken)
			accidents[i].Relaters[j].IsSelfHurt = (&safetyModel.TrafficAccidentRelaterBranch{}).IsExistBranchType(accidents[i].Relaters[j].Id, util.AccidentBranchTypeForSelfPeopleHurt)
			accidents[i].Relaters[j].IsSideVehicleBroken = (&safetyModel.TrafficAccidentRelaterBranch{}).IsExistBranchType(accidents[i].Relaters[j].Id, util.AccidentBranchTypeForSideVehicleBroken)
			accidents[i].Relaters[j].IsSideHurt = (&safetyModel.TrafficAccidentRelaterBranch{}).IsExistBranchType(accidents[i].Relaters[j].Id, util.AccidentBranchTypeForSidePeopleHurt)
		}

		if accidents[i].InsuranceFilePath != "" {
			accidents[i].InsuranceFileUrl = config.Config.StaticFileHttpPrefix + accidents[i].InsuranceFilePath
		}

		if accidents[i].CommercialInsuranceFilePath != "" {
			accidents[i].CommercialInsuranceFileUrl = config.Config.StaticFileHttpPrefix + accidents[i].CommercialInsuranceFilePath
		}
	}

	return accidents, count
}

// Create 事故上报
func (ta *TrafficAccident) Create(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.ParseOpUser(ctx)

	//保存草稿
	if param.OpenStatus == util.AccidentOpenStatusForDraft {
		if param.CorporationIdStr != "" {
			param.CorporationId, _ = strconv.ParseInt(param.CorporationIdStr, 10, 64)
		}

		if param.CorporationId > 0 {
			//机构信息
			param.TrafficAccident.Corporations.Build(param.CorporationId)
			if param.TrafficAccident.FleetId == 0 {
				log.ErrorFields("Corporation is not fleet", map[string]interface{}{"corporationId": param.CorporationId})
				return response.Error(rsp, response.CorporationParamNotFleet)
			}

			fleet := rpc.GetCorporationById(ctx, param.TrafficAccident.FleetId)
			branchCompany := rpc.GetCorporationById(ctx, param.TrafficAccident.BranchId)
			if fleet == nil || branchCompany == nil {
				log.ErrorFields("Corporation is not fund", map[string]interface{}{"corporationId": param.CorporationId})
				return response.Error(rsp, response.FAIL)
			}
		}
		if param.TrafficAccident.Id > 0 {
			param.TrafficAccident.CreatedAt = model.LocalTime(time.Now())
			err = param.TrafficAccident.UpdateDraftAll()
		} else {
			err = param.TrafficAccident.Create()
		}
		if err != nil {
			log.ErrorFields("TrafficAccident.Create error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}

		return response.Success(rsp, map[string]interface{}{"Id": param.TrafficAccident.Id, "BranchId": 0})
	}

	if param.IsCreatePayment == util.StatusForTrue {
		// 检查参数
		if param.TmpFixOffice == 0 {
			log.ErrorFields("[TmpFixOffice] is required", nil)
			return response.Error(rsp, response.ParamsMissing)
		}
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	if len(param.Relaters) > 0 {
		for i := range param.Relaters {
			if param.Relaters[i].ActionType == "delete" {
				continue
			}
			if err := util.Validator().Struct(param.Relaters[i]); err != nil {
				log.ErrorFields("relater validate fail", map[string]interface{}{"err": err})
				return response.Error(rsp, response.ParamsMissing)
			}

			if param.Relaters[i].ActionType != "Delete" {
				if param.Relaters[i].License == "" && param.Relaters[i].IdentifyId == "" && param.Relaters[i].Contact == "" {
					log.ErrorFields("relater validate fail", map[string]interface{}{"err": err})
					return response.Error(rsp, response.ParamsMissing)
				}
			}
		}
	}

	if param.CorporationIdStr != "" {
		param.CorporationId, _ = strconv.ParseInt(param.CorporationIdStr, 10, 64)
	}

	//机构信息
	param.TrafficAccident.Corporations.Build(param.CorporationId)
	if param.TrafficAccident.FleetId == 0 {
		log.ErrorFields("Corporation is not fleet", map[string]interface{}{"corporationId": param.CorporationId})
		return response.Error(rsp, response.CorporationParamNotFleet)
	}

	fleet := rpc.GetCorporationById(ctx, param.TrafficAccident.FleetId)
	branchCompany := rpc.GetCorporationById(ctx, param.TrafficAccident.BranchId)
	if fleet == nil || branchCompany == nil {
		log.ErrorFields("Corporation is not fund", map[string]interface{}{"corporationId": param.CorporationId})
		return response.Error(rsp, response.FAIL)
	}

	//生成事故编号 事故发生日期-分公司序号-车队号-车号-今年第几个事故（公司下所有的事故）
	//param.TrafficAccident.Code = fmt.Sprintf("%s-%v-%v", time.Time(param.TrafficAccident.HappenAt).Format("20060102"), branchCompany.Virtual, fleet.Virtual)

	setting := (&safetyModel.TrafficAccidentSetting{}).FindByCorporationId(param.TrafficAccident.BranchId)

	if setting.Id == 0 {
		log.ErrorFields("not find corporation code at config file", map[string]interface{}{"BranchId": param.TrafficAccident.BranchId})
		return response.Error(rsp, "OP7510")
	}

	//生成事故编号 分公司机构编号-事故发生日期-今年第几个事故（公司下所有的事故）
	param.TrafficAccident.Code = fmt.Sprintf("%s-%s", setting.Code, time.Time(param.TrafficAccident.HappenAt).Format("20060102"))

	//事故上报没有审批环节，所以流程自动审批通过
	param.OpenStatus = util.AccidentOpenStatusForOpen //是否开放
	param.ApplyStatus = util.ApplyStatusForDone
	user := auth.User(ctx).GetUser()
	tx := model.DB().Begin()
	if param.TrafficAccident.Id > 0 {
		//licenseByte := []rune(param.TrafficAccident.License)
		start := time.Date(time.Time(param.TrafficAccident.HappenAt).Year(), 1, 1, 0, 0, 0, 0, time.Local)
		end := start.AddDate(1, 0, 0)
		sequence := param.TrafficAccident.GetCountByTime(param.TrafficAccident.BranchId, start, end)
		param.TrafficAccident.Code = fmt.Sprintf("%s-%04d", param.TrafficAccident.Code, sequence+1)
		param.TrafficAccident.CreatedAt = model.LocalTime(time.Now())
		param.TrafficAccident.CodeSequence = sequence + 1
		err = param.TrafficAccident.TransactionUpdateAll(tx)
	} else {
		err = param.TrafficAccident.TransactionCreate(tx)
	}

	if err != nil {
		tx.Rollback()
		log.ErrorFields("TrafficAccident.Update or Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}
	var processId string
	//发起流程审批
	if config.Config.Lbpm.Enable {
		corporation := rpc.GetCorporationById(ctx, param.CorporationId)
		if corporation != nil {
			param.TrafficAccident.CorporationName = corporation.Name
		}
		byteParam, _ := json.Marshal(param.TrafficAccident)
		formData := map[string]interface{}{"AccidentGrade": util.TrafficAccidentGradeMap[param.TrafficAccident.Grade]}

		processTitle := service.BuildAccidentProcessTitle(&param.TrafficAccident)
		//err = processService.DispatchProcess(param.TrafficAccident.Id, param.TrafficAccident.TableName(), param.TrafficAccident.ApplyStatusFieldName(), param.ProcessId, string(byteParam), oetStaff.Phone, param.LbpmParam, formData)
		processId, err = processService.NewDispatchProcess(user, config.TrafficAccidentReportFormTemplate, processTitle, param.TrafficAccident.Id, param.TrafficAccident.TableName(), param.TrafficAccident.ApplyStatusFieldName(), string(byteParam), formData)

		if err != nil {
			tx.Rollback()
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}
	tx.Commit()

	var bid int64 // 车损分支id
	for _, status := range strings.Split(param.OwnStatus, "|") {
		statusInt, _ := strconv.Atoi(status)
		if statusInt == util.AccidentBranchTypeForSelfVehicleBroken && param.IsMustFix == util.StatusForTrue {
			relaterId, err := listener.CreateAccidentRelater(&param.TrafficAccident)
			if err != nil {
				return err
			}

			bid, err = listener.CreateAccidentSelfVehicleBrokenBranchReturnId(&param.TrafficAccident, relaterId)
			if err != nil {
				return err
			}
		}
		if statusInt == util.AccidentBranchTypeForSelfPeopleHurt {
			relaterId, err := listener.CreateAccidentRelater(&param.TrafficAccident)
			if err != nil {
				return err
			}

			err = listener.CreateAccidentSelfPeopleHurtBranch(&param.TrafficAccident, relaterId)
			if err != nil {
				return err
			}
		}
	}

	go service.BuildAccidentLogger(user.Id, user.Name, auth.User(ctx).GetClientIp(), &safetyModel.TrafficAccident{}, &param.TrafficAccident, processId)

	return response.Success(rsp, map[string]interface{}{"BranchId": bid, "Id": param.TrafficAccident.Id})

}

// Show 事故基本信息
func (ta *TrafficAccident) Show(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	var accident safetyModel.TrafficAccident
	err = accident.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("accident.FindBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	//查询分公司和车队
	accident.Corporations.ParseCorporation()
	accident.CorporationId, accident.CorporationName = accident.Corporations.GetCorporation()

	accident.FileHttpPrefix = config.Config.StaticFileHttpPrefix
	accident.IsProcessHandler = processService.CheckIsProcessRelater(accident.Id, param.TemplateFormId, auth.User(ctx).GetUserId())
	//accident.Relaters = (&safetyModel.TrafficAccidentRelater{}).GetBy(accident.Id, "")
	accident.Relaters = (&safetyModel.TrafficAccidentRelater{}).GetRelaterBranchesBy(accident.Id, "")

	accident.HasRelater = accident.IsHasRelater(accident.Id)
	accident.TotalBranchCount = (&safetyModel.TrafficAccidentRelaterBranch{}).GetBranchCount(accident.Id, 0)
	accident.ClosedBranchCount = (&safetyModel.TrafficAccidentRelaterBranch{}).GetBranchCount(accident.Id, util.StatusForTrue)
	accident.IsEnableClosed = accident.CheckIsEnableClosed() && accident.EditApplyStatus != util.ApplyStatusForDoing && accident.IsClosed == util.StatusForFalse

	//查询事故的己方车损分支
	branch := (&safetyModel.TrafficAccidentRelaterBranch{}).FindSelfVehicleBrokenBranchByAccidentId(accident.Id)
	accident.HasSelfVehicleBrokenBranch = branch.Id > 0
	if branch.Id > 0 {
		//判断己方车损分支是否有流程
		accident.HasSelfVehicleBrokenBranchProcess = branch.IsExistNormalProcess(branch.Id)
	}

	accident.HasPaymentProcess = (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).ExistPaymentRecord(accident.Id)

	//正在审批的付款流程
	accident.HasApplyingPaymentMoneyProcess = (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).IsExistDoingPaymentRecord(accident.Id)

	lendRecords := (&safetyModel.TrafficAccidentLendMoneyRecord{}).GetByAccidentIdByApplyStatus(accident.Id, []int64{util.ApplyStatusForDoing, util.ApplyStatusForTerminate, util.ApplyStatusForReject})
	accident.HasApplyingLendMoneyProcess = len(lendRecords) > 0

	//有己方人伤分支，但未发起流程或者发起的流程都已废弃，则推断为”未发起任何流程“
	selfPeopleHurtBranch := (&safetyModel.TrafficAccidentRelaterBranch{}).FindSelfPeopleHurtBranchByAccidentId(accident.Id)
	if selfPeopleHurtBranch.Id > 0 {
		accident.HasSelfPeopleHurtProcess = (&safetyModel.TrafficAccidentRelaterBranch{}).IsExistNormalProcess(selfPeopleHurtBranch.Id)
	}

	//否有进行中、已通过、撤回、驳回的流程 包括付款、借款、退款、分支结案
	accident.HasDoingProcess = service.IsExistDoingProcess(&accident)
	// 查询事故提交时间
	var process processModel.LbpmApplyProcess
	err = (&process).GetProcessByItemId(accident.Id, accident.TableName())
	if err == nil {
		accident.ApplyAt = process.ApplyAt
	}

	if accident.InsuranceFilePath != "" {
		accident.InsuranceFileUrl = config.Config.StaticFileHttpPrefix + accident.InsuranceFilePath
	} else {
		accident.InsuranceFileUrl = config.Config.StaticFileHttpPrefix + "webroot/erp/vehicle_insurances/" + accident.InsuranceCode + ".pdf"
	}

	if accident.CommercialInsuranceFilePath != "" {
		accident.CommercialInsuranceFileUrl = config.Config.StaticFileHttpPrefix + accident.CommercialInsuranceFilePath
	} else {
		accident.CommercialInsuranceFileUrl = config.Config.StaticFileHttpPrefix + "webroot/erp/vehicle_insurances/" + accident.CommercialInsuranceCode + ".pdf"
	}

	return response.Success(rsp, accident)

}

// Edit 事故编辑
func (ta *TrafficAccident) Edit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	var trafficAccident safetyModel.TrafficAccident
	err = trafficAccident.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("TrafficAccident not found", map[string]interface{}{"id": param.TrafficAccident.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if trafficAccident.OpenStatus == util.AccidentOpenStatusForDraft {
		log.ErrorFields("TrafficAccident is draft", nil)
		return response.Error(rsp, response.ParamsInvalid)
	}

	//事故变更中、事故结案申请中、事故已结案 不允许再做事故信息变更
	if trafficAccident.ClosedApplyStatus == util.ApplyStatusForDoing ||
		trafficAccident.ClosedApplyStatus == util.ApplyStatusForDone ||
		trafficAccident.EditApplyStatus == util.ApplyStatusForDoing {
		return response.Error(rsp, response.FAIL)
	}

	//如果事故已经有分支，不能设置损伤情况为无车损无人伤
	if (&safetyModel.TrafficAccidentRelaterBranch{}).GetBranchCount(trafficAccident.Id, 0) > 0 && param.TrafficAccident.HurtStatus == util.AccidentHurtStatus_1 {
		log.ErrorFields("有分支的事故不能设置损伤情况为无车损无人伤", nil)
		return response.Error(rsp, response.FAIL)
	}

	// 限制事故列表编辑功能使用权限，只有【车队】中的人才能对事故进行编辑
	detailById := rpc.GetCorporationDetailById(ctx, auth.User(ctx).GetCorporationId())
	if detailById == nil {
		log.ErrorFields("rpc.GetCorporationDetailById is nil", map[string]interface{}{"CorporationId": auth.User(ctx).GetCorporationId()})
		return response.Error(rsp, response.NotFleetAccount)
	}
	if detailById.FleetId == 0 {
		log.ErrorFields("detailById.FleetId ==0, 限制事故列表编辑功能使用权限，只有【车队】中的人才能对事故进行编辑", nil)
		return response.Error(rsp, response.NotFleetAccount)
	}

	//机构信息
	param.TrafficAccident.Corporations.Build(param.CorporationId)
	param.ParseOpUser(ctx)
	user := auth.User(ctx).GetUser()
	tx := model.DB().Begin()
	//更新事故变更流程的状态为审批中
	err = (&safetyModel.TrafficAccident{}).TransactionUpdateColumn(tx, trafficAccident.Id, trafficAccident.EditApplyStatusFieldName(), util.ApplyStatusForDoing)
	if err != nil {
		log.ErrorFields("TrafficAccident TransactionUpdateColumn error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.FAIL)
	}
	var processId string
	//发起事故变更流程审批
	if config.Config.Lbpm.Enable {
		param.TrafficAccident.OpIp = auth.User(ctx).GetClientIp()
		corporation := rpc.GetCorporationById(ctx, param.CorporationId)
		if corporation != nil {
			param.TrafficAccident.CorporationName = corporation.Name
		}
		byteParam, _ := json.Marshal(param.TrafficAccident)
		formData := map[string]interface{}{"AccidentGrade": util.TrafficAccidentGradeMap[param.TrafficAccident.Grade]}
		processTitle := service.BuildAccidentProcessTitle(&param.TrafficAccident)

		processId, err = processService.NewDispatchProcess(user, config.TrafficAccidentEditFormTemplate, processTitle, param.TrafficAccident.Id, param.TrafficAccident.TableName(), param.TrafficAccident.EditApplyStatusFieldName(), string(byteParam), formData)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	tx.Commit()

	// 将机构信息存入日志
	param.TrafficAccident.CorporationId, param.TrafficAccident.CorporationName = param.TrafficAccident.Corporations.GetCorporation()

	//新增操作日志
	go service.BuildAccidentLogger(user.Id, user.Name, param.TrafficAccident.OpIp, &trafficAccident, &param.TrafficAccident, processId)

	return response.Success(rsp, nil)
}

// Delete 事故删除
func (ta *TrafficAccident) Delete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	var accident safetyModel.TrafficAccident
	err = accident.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("accident.FindBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if accident.IsClosed == util.StatusForTrue {
		return response.Error(rsp, response.Forbidden)
	}

	err = param.TrafficAccident.Delete(param.Id)
	if err != nil {
		log.ErrorFields("TrafficAccident.Delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}
	return response.Success(rsp, nil)

}

// UploadConfirmFile 事故责任认定文件更新
func (ta *TrafficAccident) UploadConfirmFile(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var trafficAccident safetyModel.TrafficAccident
	err = trafficAccident.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("TrafficAccident not found", map[string]interface{}{"id": param.TrafficAccident.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if trafficAccident.IsClosed == util.StatusForTrue {
		return response.Error(rsp, response.Forbidden)
	}

	err = param.TrafficAccident.UpdateColumn(param.Id, "ConfirmFilePath", param.ConfirmFilePath)
	if err != nil {
		log.ErrorFields("TrafficAccident.UpdateColumn error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	//保存日志
	beforeData, _ := json.Marshal(map[string]interface{}{"ConfirmFilePath": trafficAccident.ConfirmFilePath})
	afterData, _ := json.Marshal(map[string]interface{}{"ConfirmFilePath": param.ConfirmFilePath})

	user := auth.User(ctx).GetUser()
	go service.CreateLogger(user.Id, user.Name, param.Id, safetyModel.TrafficLoggerSceneUpdate, auth.User(ctx).GetClientIp(), safetyModel.TrafficLoggerModularForAccidentConfirm, beforeData, afterData, nil, "")

	return response.Success(rsp, nil)
}

// ShowPaymentRecord 事故的付款记录
func (ta *TrafficAccident) ShowPaymentRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var accident safetyModel.TrafficAccident
	err = accident.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("TrafficAccident not found", map[string]interface{}{"err": err, "id": param.Id})
		return response.Error(rsp, response.ParamsInvalid)
	}

	paymentRecord := (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).GetDoneByAccidentId(accident.Id)
	// 实际只会有一条完成的付款记录
	for i := range paymentRecord {
		// 查询流程表中的数据
		var process processModel.LbpmApplyProcess
		err = process.GetProcessByItemId(paymentRecord[i].Id, paymentRecord[i].TableName())
		if err != nil {
			log.ErrorFields("GetProcessByItemId err", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		var processParam safetyModel.TrafficAccidentPaymentMoneyRecord
		_ = json.Unmarshal(process.Param, &processParam)

		paymentRecord[i].SolutionType = processParam.SolutionType                         //解决方式 1-协商处理 2-交警简易程序 3-法院判决 4-保险公司 5-其他方式
		paymentRecord[i].SolutionFilePath = processParam.SolutionFilePath                 //解决方式对应的附件
		paymentRecord[i].InsuranceCompanyPayMoney = processParam.InsuranceCompanyPayMoney //保险公司赔付金额 单位：分
		paymentRecord[i].InsurancePayMoney = processParam.InsurancePayMoney               //保险直赔金额 单位：分
		paymentRecord[i].LossMoney = processParam.LossMoney                               //经济损失 单位：分
		paymentRecord[i].SolutionDesc = processParam.SolutionDesc                         //结案说明
		paymentRecord[i].PayOrigin = processParam.PayOrigin                               //赔付来源
	}

	return response.Success(rsp, paymentRecord)
}

// ApplyClose 事故结案
func (ta *TrafficAccident) ApplyClose(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var accident safetyModel.TrafficAccident
	err = accident.FindBy(param.TrafficAccidentId)
	if err != nil {
		log.ErrorFields("accident.FindBy error", map[string]interface{}{"id": param.TrafficAccidentId})
		return response.Error(rsp, response.FAIL)
	}

	//事故是已结案的状态  无法重复申请结案
	if accident.IsClosed == util.StatusForTrue {
		log.ErrorFields("process repeat", map[string]interface{}{"id": param.TrafficAccidentId})
		return response.Error(rsp, response.RepeatApplyProcess)
	}

	//事故不允许结案
	if !accident.CheckIsEnableClosed() || accident.ApplyStatus != util.ApplyStatusForDone || accident.EditApplyStatus == util.ApplyStatusForDoing || accident.ClosedApplyStatus == util.ApplyStatusForDoing {
		log.ErrorFields("accident cant closed", map[string]interface{}{"id": param.TrafficAccidentId})
		return response.Error(rsp, response.FAIL)
	}

	if param.IsRestart && param.ProcessId == "" {
		log.ErrorFields("ProcessId is missing", map[string]interface{}{"processId": param.ProcessId})
		return response.Error(rsp, response.ParamsMissing)
	}

	accident.PersonalPayRatio = param.PersonalPayRatio
	accident.LossMoney = param.LossMoney
	accident.CloseDesc = param.CloseDesc
	accident.InsuranceCompanyPayMoney = param.InsuranceCompanyPayMoney
	accident.InsurancePayMoney = param.InsurancePayMoney
	accident.CloseFilePath = param.CloseFilePath
	accident.PayOrigin = param.PayOrigin

	//保存日志
	afterData, _ := json.Marshal(map[string]interface{}{"PersonalPayRatio": param.PersonalPayRatio, "LossMoney": param.LossMoney})

	// 查询事故借款总金额 存入param 消息中心使用
	accident.TrafficAccidentLendMoneyRecordTotal = (&safetyModel.TrafficAccidentLendMoneyRecord{}).GetTotalMoneyByAccidentId(accident.Id)

	accident.ClosedApplyStatus = util.ApplyStatusForDoing
	accident.FormStep = util.ProcessFormStepStart

	tx := model.DB()
	err = accident.TransactionUpdateCloseInfo(tx)
	if err != nil {
		log.ErrorFields("accident.TransactionUpdateCloseInfo error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	accident.CorporationId, accident.CorporationName = accident.Corporations.GetCorporation()
	byteParam, _ := json.Marshal(accident)
	user := auth.User(ctx).GetUser()
	var processId string
	//发起流程审批
	if config.Config.Lbpm.Enable {
		lendTotalMoney := (&safetyModel.TrafficAccidentLendMoneyRecord{}).GetTotalMoneyByAccidentId(accident.Id)
		drawbackTotalMoney := (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).DoneTotalMoneyByAccidentId(accident.Id)

		//var branch safetyModel.TrafficAccidentRelaterBranch
		//insuranceTotalMoney := branch.GetTotalInsuranceCompanyPayMoneyByAccidentId(accident.Id)

		diffAmount, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", float64(accident.LossMoney-accident.InsuranceCompanyPayMoney)/100), 64)
		payAmount, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", float64(lendTotalMoney-drawbackTotalMoney)/100), 64)
		formData := map[string]interface{}{"DiffAmount": diffAmount, "PayAmount": payAmount, "IsMustFix": accident.IsMustFix}

		if param.IsRestart {
			//重新发起流程
			err = processService.RestartProcess(user, param.ProcessId, string(byteParam), formData)
		} else {
			//发起新的流程
			processTitle := service.BuildAccidentProcessTitle(&accident)
			processId, err = processService.NewDispatchProcess(user, config.TrafficAccidentCloseFormTemplate, processTitle, accident.Id, accident.TableName(), accident.ClosedApplyStatusFieldName(), string(byteParam), formData)
		}

		if err != nil {
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	if !param.IsRestart {
		go service.CreateLogger(user.Id, user.Name, param.TrafficAccidentId, safetyModel.TrafficLoggerSceneClosed, auth.User(ctx).GetClientIp(), safetyModel.TrafficLoggerModularForCloseApply, nil, afterData, byteParam, processId)
	}

	return response.Success(rsp, nil)

}

func (ta *TrafficAccident) SafeCheckHandle(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param safetyModel.TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var accident safetyModel.TrafficAccident
	err = accident.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("accident.FindBy error", map[string]interface{}{"id": param.Id})
		return response.Error(rsp, response.FAIL)
	}

	if param.CheckHandleResult == 0 || param.CheckHandleMore == "" {
		return response.Error(rsp, response.ParamsMissing)
	}

	err = param.UpdateCheckHandle()
	if err != nil {
		log.ErrorFields("param.UpdateCheckHandle error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	return response.Success(rsp, nil)
}

// EditApplyClose 编辑事故结案表单数据
func (ta *TrafficAccident) EditApplyClose(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var acc safetyModel.TrafficAccident
	err = acc.FindBy(param.TrafficAccidentId)
	if err != nil {
		log.ErrorFields("acc.FindBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	acc.PersonalPayRatio = param.PersonalPayRatio
	acc.LossMoney = param.LossMoney
	acc.CloseDesc = param.CloseDesc
	acc.CloseFilePath = param.CloseFilePath
	acc.InsuranceCompanyPayMoney = param.InsuranceCompanyPayMoney
	acc.InsurancePayMoney = param.InsurancePayMoney
	acc.PayOrigin = param.PayOrigin

	tx := model.DB().Begin()

	err = acc.TransactionUpdateCloseInfo(tx)
	if err != nil {
		log.ErrorFields("acc.TransactionUpdateCloseInfo error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbUpdateFail)
	}

	var process processModel.LbpmApplyProcess
	err = (&process).GetApproveProcess(config.TrafficAccidentCloseFormTemplate, acc.Id, nil)
	if err != nil {
		log.ErrorFields("branch.GetProcessByItemId error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbQueryFail)
	}

	var processAcc safetyModel.TrafficAccident
	err = json.Unmarshal(process.Param, &processAcc)
	if err != nil {
		log.ErrorFields("Unmarshal error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbQueryFail)
	}
	processAcc.PersonalPayRatio = param.PersonalPayRatio
	processAcc.LossMoney = param.LossMoney
	processAcc.CloseDesc = param.CloseDesc
	processAcc.CloseFilePath = param.CloseFilePath
	processAcc.InsuranceCompanyPayMoney = param.InsuranceCompanyPayMoney
	processAcc.InsurancePayMoney = param.InsurancePayMoney
	processAcc.PayOrigin = param.PayOrigin

	marshal, err := json.Marshal(processAcc)
	if err != nil {
		log.ErrorFields("Marshal error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbQueryFail)
	}

	//process.Param = marshal

	err = process.TransactionUpdateColumn(tx, "Param", marshal)
	if err != nil {
		log.ErrorFields("TransactionUpdateColumn error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbUpdateFail)
	}

	err = acc.TransactionUpdateColumn(tx, acc.Id, "IsUpdateForm", util.StatusForFalse)
	if err != nil {
		log.ErrorFields("acc TransactionUpdateColumn error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.DbUpdateFail)
	}

	tx.Commit()
	return response.Success(rsp, nil)
}

// ClosePrinted 结案事故已打印
func (ta *TrafficAccident) ClosePrinted(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var accident safetyModel.TrafficAccident
	err = accident.FindBy(param.TrafficAccidentId)
	if err != nil {
		log.ErrorFields("accident.FindBy error", map[string]interface{}{"id": param.TrafficAccidentId})
		return response.Error(rsp, response.FAIL)
	}

	//事故是未结案的状态  不可打印
	if accident.IsClosed == util.StatusForFalse {
		log.ErrorFields("the accident is not closed.", map[string]interface{}{"id": param.TrafficAccidentId})
		return response.Error(rsp, response.AccidentUnCloseDoNotPrint)
	}

	err = (&safetyModel.TrafficAccident{}).UpdateColumn(param.TrafficAccidentId, "IsPrinted", util.StatusForTrue)
	if err != nil {
		log.ErrorFields("TrafficAccident.UpdateColumn error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)

}

// TrafficAccidentLogger 事故基本信息操作日志
type TrafficAccidentLogger struct {
	safetyModel.TrafficAccidentLogger
	Keyword string          `json:"Keyword"`
	StartAt model.LocalTime `json:"StartAt"`
	EndAt   model.LocalTime `json:"EndAt"`
	model.Paginator
}

type LoggerRsp struct {
	safetyModel.TrafficAccidentLogger
	CurrentHandlerUserName string // 当前处理人
	LbpmProcessStatus      int64  // 流程状态
}

// Logger 事故基本信息操作日志
func (ta *TrafficAccident) Logger(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentLogger
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	loggers, count := param.GetBy(param.TrafficAccidentId, param.Keyword, time.Time(param.StartAt), time.Time(param.EndAt), param.Paginator)

	var rspD []LoggerRsp
	var status = []int64{util.ProcessStatusForDoing, util.ProcessStatusForDone, util.ProcessStatusForRefuse, util.ProcessStatusForTerminate, util.ProcessStatusForAbandon}
	for _, logger := range loggers {
		item := LoggerRsp{
			TrafficAccidentLogger:  logger,
			CurrentHandlerUserName: "",
			LbpmProcessStatus:      0,
		}

		// 事故提交 事故变更 事故结案
		// 如果是事故变更，所有变更展示最新的那个流程（为同一个流程）

		var process processModel.LbpmApplyProcess

		err := process.GetProcess(logger.ProcessId, status)
		if err == nil {
			item.LbpmProcessStatus = process.Status
			item.CurrentHandlerUserName = process.CurrentHandlerUserName
		}

		rspD = append(rspD, item)
	}

	return response.Success(rsp, map[string]interface{}{"Items": rspD, "TotalCount": count})
}

// AddRecycle 加入回收站
func (ta *TrafficAccident) AddRecycle(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccident
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	// 更新事故 IsRecycle 状态
	err = param.TrafficAccident.TransactionUpdateColumn(tx, param.TrafficAccident.Id, "IsRecycle", util.StatusForTrue)
	if err != nil {
		log.ErrorFields("TransactionUpdateColumn error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}
	err = param.TrafficAccident.TransactionUpdateColumn(tx, param.TrafficAccident.Id, "RecycleAt", time.Now())
	if err != nil {
		log.ErrorFields("TransactionUpdateColumn error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}

	// 删除相关消息
	// 事故结案 更新事故预警告警
	go service.CheckAccidentAlarm(param.TrafficAccident.Id)
	return response.Success(rsp, nil)
}

/*func FixHistoryLogger() {
	time.Sleep(1 * time.Minute)
	fmt.Printf("====FixHistoryLogger start \n")

	//查询未关联流程的历史记录
	loggers := (&TrafficAccidentLogger{}).GetNotProcessId()

	for i := range loggers {
		if loggers[i].Scene == 1 { //事故创建
			var accident TrafficAccident
			err := accident.FindBy(loggers[i].TrafficAccidentId)
			if err != nil {
				continue
			}
			s := time.Time(loggers[i].CreatedAt)
			fmt.Printf("======createdat: %s,%s \n", loggers[i].CreatedAt, s)
			startUnix := time.Date(s.Year(), s.Month(), s.Day(), s.Hour(), s.Minute(), s.Second(), 0, time.Local).Unix() - 3
			fmt.Printf("======startUnix: %v \n", startUnix)
			startAt := time.Unix(startUnix, 0)
			fmt.Printf("======startAt: %s \n", startAt)
			endUnix := startUnix + 6
			endAt := time.Unix(endUnix, 0)
			process := (&processModel.LbpmApplyProcess{}).GetProcessByAt(loggers[i].TrafficAccidentId, config.TrafficAccidentReportFormTemplate, startAt, endAt)
			if process.FormInstanceId == 0 {
				continue
			}
			(&TrafficAccidentLogger{}).Update(loggers[i].Id, process.ProcessId, loggers[i].AfterData)

			fmt.Printf("====TrafficAccidentLogger update success: %+v \n", loggers[i])
		}

		if loggers[i].Scene == 2 && loggers[i].Modular == "事故编辑" {
			var accident TrafficAccident
			err := accident.FindBy(loggers[i].TrafficAccidentId)
			if err != nil {
				continue
			}
			s := time.Time(loggers[i].CreatedAt)
			fmt.Printf("======createdat: %s,%s \n", loggers[i].CreatedAt, s)
			startUnix := time.Date(s.Year(), s.Month(), s.Day(), s.Hour(), s.Minute(), s.Second(), 0, time.Local).Unix() - 3
			fmt.Printf("======startUnix: %v \n", startUnix)
			startAt := time.Unix(startUnix, 0)
			fmt.Printf("======startAt: %s \n", startAt)
			endUnix := startUnix + 6
			endAt := time.Unix(endUnix, 0)
			process := (&processModel.LbpmApplyProcess{}).GetProcessByAt1(loggers[i].TrafficAccidentId, config.TrafficAccidentEditFormTemplate, startAt, endAt)
			if process.FormInstanceId == 0 {
				continue
			}

			accident.CorporationId, accident.CorporationName = accident.Corporations.GetCorporation()
			b, _ := json.Marshal(accident)
			(&TrafficAccidentLogger{}).Update(loggers[i].Id, process.ProcessId, b)

			fmt.Printf("====TrafficAccidentLogger update success: %+v \n", loggers[i])
		}

		if loggers[i].Scene == 2 && loggers[i].Modular == "申请事故结案" {
			var accident TrafficAccident
			err := accident.FindBy(loggers[i].TrafficAccidentId)
			if err != nil {
				continue
			}
			s := time.Time(loggers[i].CreatedAt)
			fmt.Printf("======createdat: %s,%s \n", loggers[i].CreatedAt, s)
			startUnix := time.Date(s.Year(), s.Month(), s.Day(), s.Hour(), s.Minute(), s.Second(), 0, time.Local).Unix() - 3
			fmt.Printf("======startUnix: %v \n", startUnix)
			startAt := time.Unix(startUnix, 0)
			fmt.Printf("======startAt: %s \n", startAt)
			endUnix := startUnix + 6
			endAt := time.Unix(endUnix, 0)
			process := (&processModel.LbpmApplyProcess{}).GetProcessByAt(loggers[i].TrafficAccidentId, config.TrafficAccidentCloseFormTemplate, startAt, endAt)
			if process.FormInstanceId == 0 {
				continue
			}

			accident.CorporationId, accident.CorporationName = accident.Corporations.GetCorporation()
			b, _ := json.Marshal(accident)
			(&TrafficAccidentLogger{}).Update(loggers[i].Id, process.ProcessId, b)

			fmt.Printf("====TrafficAccidentLogger update success: %+v \n", loggers[i])
		}
	}
}*/
