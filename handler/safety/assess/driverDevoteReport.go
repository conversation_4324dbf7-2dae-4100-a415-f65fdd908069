package assess

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	assessModel "app/org/scs/erpv2/api/model/safety/assess"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/scheduler/command"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"strconv"
	"time"
)

func (h *DriverAssess) DriverSafeDevoteReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverAssess
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	reports, count := (&assessModel.DriverDevoteReport{}).GetBy(param.CorporationIds, param.LineId, param.StaffId, param.Paginator)
	for i := range reports {
		reports[i].CorporationId, reports[i].CorporationName = reports[i].Corporations.GetCorporation()
		reports[i].Items = (&assessModel.DriverDevoteLogger{}).GetByDevoteId(reports[i].Id, "safe")
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      reports,
		"TotalCount": count,
	})
}

func (h *DriverAssess) DriverServiceDevoteReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverAssess
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	reports, count := (&assessModel.DriverDevoteReport{}).GetBy(param.CorporationIds, param.LineId, param.StaffId, param.Paginator)
	for i := range reports {
		reports[i].CorporationId, reports[i].CorporationName = reports[i].Corporations.GetCorporation()
		reports[i].Items = (&assessModel.DriverDevoteLogger{}).GetByDevoteId(reports[i].Id, "service")
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      reports,
		"TotalCount": count,
	})
}

type DriverDevoteUpdateParam struct {
	ReportId int64 `json:"ReportId"`
	Level    int64 `json:"Level"`
}

func (h *DriverAssess) DriverSafeDevoteEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverDevoteUpdateParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Level < 1 || param.Level > 7 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	report := (&assessModel.DriverDevoteReport{}).FindById(param.ReportId)
	if report.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Level == report.SafeCurrentLevel {
		return response.Success(rsp, nil)
	}

	//查询安全贡献奖金基数配置
	setting := (&settingModel.GlobalSetting{}).GetBy(auth.User(ctx).GetTopCorporationId(), util.GlobalSettingTypeForSafeContribution)
	var settingItem settingModel.GlobalSettingItemForViolationSafeContributionAssess
	err = json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.FAIL)
	}

	var settingMap = make(map[int64]settingModel.GlobalSettingItemForContributionAssessItem)
	for _, st := range settingItem.Settings {
		settingMap[st.Level] = st
	}

	err = report.UpdateColumns(map[string]interface{}{
		"SafeCurrentLevel":    param.Level,
		"SafeRewardBaseMoney": settingMap[param.Level].Money,
	})

	if err != nil {
		log.ErrorFields("UpdateColumn error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	//写入日志
	var logger = assessModel.DriverDevoteLogger{
		DriverDevoteReportId: report.Id,
		ReportMonth:          report.SafeReportMonth,
		BeforeLevel:          report.SafeCurrentLevel,
		AfterLevel:           param.Level,
		Action:               "edit",
		Scene:                "safe",
	}
	user := auth.User(ctx).GetUser()
	var detail = map[string]interface{}{
		"CreatedAt":  model.LocalTime(time.Now()),
		"OpUserId":   user.Id,
		"OpUserName": user.Name,
	}

	logger.Detail, _ = json.Marshal(detail)
	err = logger.Create(model.DB())
	if err != nil {
		log.ErrorFields("logger.Create error", map[string]interface{}{"error": err, "logger": logger})
	}

	return response.Success(rsp, nil)
}

func (h *DriverAssess) DriverServiceDevoteEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverDevoteUpdateParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Level < 1 || param.Level > 7 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	report := (&assessModel.DriverDevoteReport{}).FindById(param.ReportId)
	if report.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Level == report.ServiceCurrentLevel {
		return response.Success(rsp, nil)
	}

	//查询安全贡献奖金基数配置
	setting := (&settingModel.GlobalSetting{}).GetBy(auth.User(ctx).GetTopCorporationId(), util.GlobalSettingTypeForServiceContribution)
	var settingItem settingModel.GlobalSettingItemForServiceSafeContributionAssess
	err = json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.FAIL)
	}

	var settingMap = make(map[int64]settingModel.GlobalSettingItemForContributionAssessItem)
	for _, st := range settingItem.Settings {
		settingMap[st.Level] = st
	}

	err = report.UpdateColumns(map[string]interface{}{
		"ServiceCurrentLevel":    param.Level,
		"ServiceRewardBaseMoney": settingMap[param.Level].Money,
	})

	if err != nil {
		log.ErrorFields("UpdateColumn error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	//写入日志
	var logger = assessModel.DriverDevoteLogger{
		DriverDevoteReportId: report.Id,
		ReportMonth:          report.ServiceReportMonth,
		BeforeLevel:          report.ServiceCurrentLevel,
		AfterLevel:           param.Level,
		Action:               "edit",
		Scene:                "service",
	}
	user := auth.User(ctx).GetUser()
	var detail = map[string]interface{}{
		"CreatedAt":  model.LocalTime(time.Now()),
		"OpUserId":   user.Id,
		"OpUserName": user.Name,
	}

	logger.Detail, _ = json.Marshal(detail)
	err = logger.Create(model.DB())
	if err != nil {
		log.ErrorFields("logger.Create error", map[string]interface{}{"error": err, "logger": logger})
	}

	return response.Success(rsp, nil)
}

// 重新计算司机贡献奖金
func (h *DriverAssess) RecalcDriverDevoteReward(topCorporationId int64, devoteReport assessModel.DriverDevoteReport) {
	//查询贡献考核奖金时间配置
	setting := (&settingModel.GlobalSetting{}).GetBy(topCorporationId, util.GlobalSettingTypeForDriverDevoteCalcDayDot)
	var settingItem settingModel.GlobalSettingItemForDriverDevoteCalcDayDot
	err := json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
		return
	}

	var month string
	nowDay := int64(time.Now().Day())
	if nowDay >= settingItem.DayDot {
		month = time.Now().Format("200601")
	} else {
		month = time.Now().AddDate(0, -1, 0).Format("200601")
	}
	monthInt, _ := strconv.ParseInt(month, 10, 64)
	startAt := time.Date(time.Now().Year(), time.Now().Month()-1, 26, 0, 0, 0, 0, time.Local)
	endAt := time.Date(time.Now().Year(), time.Now().Month(), 25, 0, 0, 0, 0, time.Local)
	var report = command.DriverSafeAssessReport{
		Staff: command.StaffArchiveInfo{
			StaffId:   devoteReport.StaffId,
			StaffName: devoteReport.StaffName,
			LineId:    devoteReport.LineId,
			LineName:  devoteReport.LineName,
		},
		Month:   monthInt,
		StartAt: startAt,
		EndAt:   endAt,
	}
	report.Staff.CorporationId = devoteReport.Corporations.GetCorporationId()
	report.SaveDriverDevoteMoneyForMonth()
}
