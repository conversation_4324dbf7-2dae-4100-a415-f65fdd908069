package assess

import (
	"app/org/scs/erpv2/api/log"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	assessModel "app/org/scs/erpv2/api/model/safety/assess"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/scheduler/command"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

func (h *DriverAssess) ManualCalcYearReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverAssess
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Year == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	at, err := time.ParseInLocation("2006", fmt.Sprintf("%v", param.Year), time.Local)
	if err != nil {
		log.ErrorFields("time.ParseInLocation error", map[string]interface{}{"err": err, "year": param.Year})
		return response.Error(rsp, response.ParamsInvalid)
	}

	go command.ManualCalcDriverSafeAssessReportForYear(auth.User(ctx).GetTopCorporationId(), at)

	return response.Success(rsp, nil)
}

func (h *DriverAssess) DriverSafeYearReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverAssess
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	reports, count := (&assessModel.DriverYearAssessReport{}).GetBy(param.CorporationIds, param.LineId, param.StaffId, param.Year, param.Paginator)
	for i := range reports {
		reports[i].CorporationId, reports[i].CorporationName = reports[i].Corporations.GetCorporation()
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      reports,
		"TotalCount": count,
		"CalcTime":   (&assessModel.DriverYearAssessReport{}).LatestCalcTime(),
	})
}

func (h *DriverAssess) DriverSafeYearReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return h.DriverSafeYearReport(ctx, req, rsp)
}
func (h *DriverAssess) DriverServiceYearReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return h.DriverSafeYearReport(ctx, req, rsp)
}
func (h *DriverAssess) DriverServiceYearReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return h.DriverSafeYearReport(ctx, req, rsp)
}

type YearAssessDetail struct {
	WorkItem        []assessModel.DriverMonthWorkRecord           `json:"WorkItem"`
	LeaveItem       []assessModel.DriverCasualLeaveRecord         `json:"LeaveItem"`
	ViolationItem   []assessModel.DriverViolationYearAssessRecord `json:"ViolationItem"`
	AccidentItem    []assessModel.DriverAccidentYearAssessRecord  `json:"AccidentItem"`
	ThirdAssessItem []assessModel.DriverViolationYearAssessRecord `json:"ThirdAssessItem"`

	LeaveRate       int64 `json:"LeaveRate"`
	SafeYearBase    int64 `json:"SafeBase"`
	ServiceYearBase int64 `json:"ServiceBase"`
}

func (h *DriverAssess) DriverSafeYearReportDetail(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverAssess
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	report := (&assessModel.DriverYearAssessReport{}).FirstBy(param.Id)
	if report.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	//查询安全年考核配置
	setting := (&settingModel.GlobalSetting{}).GetBy(auth.User(ctx).GetTopCorporationId(), util.GlobalSettingTypeForSafeYearAssess)
	var settingItem settingModel.GlobalSettingItemForViolationYearAssess
	err = json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
		return response.Error(rsp, response.FAIL)
	}

	safeSt := settingItem.Line
	if report.IsCctDriver == util.StatusForTrue {
		safeSt = settingItem.CctLine
	}

	var detail YearAssessDetail

	//查询出勤记录
	detail.WorkItem = (&assessModel.DriverMonthWorkRecord{}).GetBy(param.Id)

	//查询事假记录
	detail.LeaveItem = (&assessModel.DriverCasualLeaveRecord{}).GetBy(param.Id)

	//查询违规记录
	violationDetails := (&assessModel.DriverViolationYearAssessRecord{}).GetBy(param.Id)
	for _, v := range violationDetails {
		violation := (&safetyModel.TrafficViolation{}).FindBy(v.ViolationId)
		v.License = violation.License
		v.LineName = violation.LineName
		v.Standard = (&safetyModel.QualityAssessmentStandards{}).GetById(v.StandardId)
		if v.ViolationCateAttr == util.TrafficViolationCateAttrForSafe {
			detail.ViolationItem = append(detail.ViolationItem, v)
		}
	}

	//查询事故
	accidentDetails := (&assessModel.DriverAccidentYearAssessRecord{}).GetBy(param.Id)
	for _, v := range accidentDetails {
		var accident safetyModel.TrafficAccident
		_ = accident.FindBy(v.AccidentId)
		v.License = accident.License
		v.LiabilityType = accident.LiabilityType
		v.LineName = accident.LineName
		v.AccidentCheckResult = accident.CheckHandleResult
		detail.AccidentItem = append(detail.AccidentItem, v)
	}

	detail.SafeYearBase = safeSt.BaseMoney
	detail.LeaveRate = report.SafeCasualLeaveRate

	return response.Success(rsp, detail)
}

func (h *DriverAssess) DriverServiceYearReportDetail(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverAssess
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	report := (&assessModel.DriverYearAssessReport{}).FirstBy(param.Id)
	if report.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	//查询服务年考核配置
	setting := (&settingModel.GlobalSetting{}).GetBy(auth.User(ctx).GetTopCorporationId(), util.GlobalSettingTypeForServiceYearAssess)
	var serviceSettingItem settingModel.GlobalSettingItemForServiceYearAssess
	err = json.Unmarshal(setting.SettingItem, &serviceSettingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
	}
	serviceSt := serviceSettingItem.Line
	if report.IsCctDriver == util.StatusForTrue {
		serviceSt = serviceSettingItem.CctLine
	}

	var detail YearAssessDetail

	//查询出勤记录
	detail.WorkItem = (&assessModel.DriverMonthWorkRecord{}).GetBy(param.Id)

	//查询事假记录
	detail.LeaveItem = (&assessModel.DriverCasualLeaveRecord{}).GetBy(param.Id)

	//查询违规记录
	violationDetails := (&assessModel.DriverViolationYearAssessRecord{}).GetBy(param.Id)
	for _, v := range violationDetails {
		violation := (&safetyModel.TrafficViolation{}).FindBy(v.ViolationId)
		v.License = violation.License
		v.LineName = violation.LineName
		v.Standard = (&safetyModel.QualityAssessmentStandards{}).GetById(v.StandardId)
		if v.ViolationCateAttr == util.TrafficViolationCateAttrForService {
			detail.ViolationItem = append(detail.ViolationItem, v)
		}

		if v.ViolationOrigin == util.TrafficViolationOriginForThirdAssess {
			detail.ThirdAssessItem = append(detail.ThirdAssessItem, v)
		}
	}

	detail.ServiceYearBase = serviceSt.BaseMoney
	detail.LeaveRate = report.ServiceCasualLeaveRate

	return response.Success(rsp, detail)
}
