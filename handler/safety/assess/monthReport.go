package assess

import (
	"app/org/scs/erpv2/api/log"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	assessModel "app/org/scs/erpv2/api/model/safety/assess"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/scheduler/command"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

func (h *DriverAssess) ManualCalcMonthReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverAssess
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Month == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	at, err := time.ParseInLocation("200601", fmt.Sprintf("%v", param.Month), time.Local)
	if err != nil {
		log.ErrorFields("time.ParseInLocation error", map[string]interface{}{"err": err, "month": param.Month})
		return response.Error(rsp, response.ParamsInvalid)
	}

	go command.ManualCalcDriverSafeAssessReportForMonth(auth.User(ctx).GetTopCorporationId(), at)

	return response.Success(rsp, nil)
}

func (h *DriverAssess) DriverSafeMonthReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverAssess
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	reports, count := (&assessModel.DriverMonthAssessReport{}).GetBy(param.CorporationIds, param.LineId, param.StaffId, param.Month, param.Paginator)
	for i := range reports {
		reports[i].CorporationId, reports[i].CorporationName = reports[i].Corporations.GetCorporation()
		devoteReport := (&assessModel.DriverDevoteMonthMoneyReport{}).GetDriverMonthReport(reports[i].StaffId, reports[i].ReportMonth)
		reports[i].SafeDevoteMoney = devoteReport.SafeDevoteMoney
		reports[i].ServiceDevoteMoney = devoteReport.ServiceDevoteMoney
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      reports,
		"TotalCount": count,
		"CalcTime":   (&assessModel.DriverMonthAssessReport{}).LatestCalcTime(),
	})
}

func (h *DriverAssess) DriverSafeMonthReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return h.DriverSafeMonthReport(ctx, req, rsp)
}
func (h *DriverAssess) DriverServiceMonthReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return h.DriverSafeMonthReport(ctx, req, rsp)
}
func (h *DriverAssess) DriverServiceMonthReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return h.DriverSafeMonthReport(ctx, req, rsp)
}

type AssessDetail struct {
	SafeItem     []assessModel.DriverViolationMonthAssessReportItem `json:"SafeItem"`
	SpecialItem  []assessModel.DriverViolationMonthAssessReportItem `json:"SpecialItem"`
	ServiceItem  []assessModel.DriverViolationMonthAssessReportItem `json:"ServiceItem"`
	AccidentItem []assessModel.DriverAccidentMonthAssessReportItem  `json:"AccidentItem"`
	SafeBase     int64                                              `json:"SafeBase"`
	SpecialBase  int64                                              `json:"SpecialBase"`
	ServiceBase  int64                                              `json:"ServiceBase"`
	AccidentBase int64                                              `json:"AccidentBase"`
}

func (h *DriverAssess) DriverSafeMonthReportDetail(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverAssess
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	report := (&assessModel.DriverMonthAssessReport{}).FirstBy(param.Id)
	if report.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	violationDetails := (&assessModel.DriverViolationMonthAssessReportItem{}).GetBy(param.Id)

	var detail AssessDetail
	for _, v := range violationDetails {
		violation := (&safetyModel.TrafficViolation{}).FindBy(v.ViolationId)
		v.License = violation.License
		v.LineName = violation.LineName
		v.Standard = (&safetyModel.QualityAssessmentStandards{}).GetById(v.StandardId)
		if v.QualityAssessmentCateAttr == util.QualityAssessmentCateAttrForSafeAction {
			detail.SafeItem = append(detail.SafeItem, v)
		}
		if v.QualityAssessmentCateAttr == util.QualityAssessmentCateAttrForSpecial {
			detail.SpecialItem = append(detail.SpecialItem, v)
		}
	}
	accidentDetails := (&assessModel.DriverAccidentMonthAssessReportItem{}).GetBy(param.Id)
	for _, v := range accidentDetails {
		var accident safetyModel.TrafficAccident
		_ = accident.FindBy(v.AccidentId)
		v.License = accident.License
		v.LiabilityType = accident.LiabilityType
		v.LineName = accident.LineName
		detail.AccidentItem = append(detail.AccidentItem, v)
	}

	safeCate := (&safetyModel.QualityAssessmentCate{}).GetAttrBaseReward(util.QualityAssessmentCateAttrForSafeAction)
	if report.IsCctDriver == util.StatusForTrue {
		detail.SafeBase = safeCate.CctLineBaseReward
	} else {
		detail.SafeBase = safeCate.LineBaseReward
	}
	specialCate := (&safetyModel.QualityAssessmentCate{}).GetAttrBaseReward(util.QualityAssessmentCateAttrForSpecial)
	if report.IsCctDriver == util.StatusForTrue {
		detail.SpecialBase = specialCate.CctLineBaseReward
	} else {
		detail.SpecialBase = specialCate.LineBaseReward
	}

	//获取事故考核配置
	setting := (&settingModel.GlobalSetting{}).GetBy(auth.User(ctx).GetTopCorporationId(), util.GlobalSettingTypeForAccidentAssess)

	var settingItem settingModel.GlobalSettingItemForAccidentAssess
	err = json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
	}
	if report.IsCctDriver == util.StatusForTrue {
		detail.AccidentBase = settingItem.CctLine.BaseMoney
	} else {
		detail.AccidentBase = settingItem.Line.BaseMoney
	}

	return response.Success(rsp, detail)
}

func (h *DriverAssess) DriverServiceMonthReportDetail(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverAssess
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	report := (&assessModel.DriverMonthAssessReport{}).FirstBy(param.Id)
	if report.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	violationDetails := (&assessModel.DriverViolationMonthAssessReportItem{}).GetBy(param.Id)

	var detail AssessDetail
	for _, v := range violationDetails {
		violation := (&safetyModel.TrafficViolation{}).FindBy(v.ViolationId)
		v.License = violation.License
		v.LineName = violation.LineName
		v.Standard = (&safetyModel.QualityAssessmentStandards{}).GetById(v.StandardId)
		if v.QualityAssessmentCateAttr == util.QualityAssessmentCateAttrForService {
			detail.ServiceItem = append(detail.ServiceItem, v)
		}
	}
	serviceCate := (&safetyModel.QualityAssessmentCate{}).GetAttrBaseReward(util.QualityAssessmentCateAttrForService)
	if report.IsCctDriver == util.StatusForTrue {
		detail.ServiceBase = serviceCate.CctLineBaseReward
	} else {
		detail.ServiceBase = serviceCate.LineBaseReward
	}

	return response.Success(rsp, detail)
}

type AdditionalMoneyForm struct {
	Type                         int64  `json:"Type" validate:"required"`     // 1安全 2服务
	Id                           int64  `json:"Id" validate:"required"`       // 月报ID
	AdditionalSafeMoney          int64  `json:"AdditionalSafeMoney" `         // 安全额外奖金或者补扣	 单位：分
	AdditionalSafeMoneyRemark    string `json:"AdditionalSafeMoneyRemark"`    // 安全备注
	AdditionalServiceMoney       int64  `json:"AdditionalServiceMoney"`       // 服务质量额外奖金或者补扣 单位：分
	AdditionalServiceMoneyRemark string `json:"AdditionalServiceMoneyRemark"` // 服务质量备注
}

func (h *DriverAssess) AdditionalMoneySet(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form AdditionalMoneyForm
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	report := (&assessModel.DriverMonthAssessReport{}).FirstBy(form.Id)
	if report.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	if form.Type == 1 {
		report.AdditionalSafeMoney = form.AdditionalSafeMoney
		report.AdditionalSafeMoneyRemark = form.AdditionalSafeMoneyRemark
	} else if form.Type == 2 {
		report.AdditionalServiceMoney = form.AdditionalServiceMoney
		report.AdditionalServiceMoneyRemark = form.AdditionalServiceMoneyRemark
	}
	err := report.Updates()
	if err != nil {
		log.ErrorFields("DriverMonthAssessReport.Updates error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)
}
