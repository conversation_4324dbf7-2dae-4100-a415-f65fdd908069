package assess

import (
	hrHandler "app/org/scs/erpv2/api/handler/hr"
	"app/org/scs/erpv2/api/log"
	hrModel "app/org/scs/erpv2/api/model/hr"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	assessModel "app/org/scs/erpv2/api/model/safety/assess"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

func (h *DriverAssess) DriverWaitWorkReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverAssess
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	reports, count := (&assessModel.DriverWaitWorkReport{}).GetBy(param.CorporationIds, param.LineId, param.StaffId, param.IsDone, param.Paginator)
	for i := range reports {
		reports[i].CorporationId, reports[i].CorporationName = reports[i].Corporations.GetCorporation()
		var itemIds []int64
		err = json.Unmarshal(reports[i].DriverWaitWorkViolationItemIds, &itemIds)
		if err != nil {
			log.ErrorFields("DriverWaitWorkViolationItemIds json.Unmarshal error", map[string]interface{}{"err": err})
			continue
		}
		items := (&assessModel.DriverWaitWorkViolationItem{}).GetByIds(itemIds)
		for j := range items {
			category := (&safetyModel.ViolationCategory{}).FirstById(items[j].CategoryId)
			items[j].CategoryName = category.Name
			violation := (&safetyModel.TrafficViolation{}).FindBy(items[j].ViolationId)
			items[j].LineName = violation.LineName
			items[j].Standard = (&safetyModel.QualityAssessmentStandards{}).GetById(items[j].StandardId)

		}
		reports[i].Items = items

		if reports[i].ApplyLeaveRecordId > 0 {
			leaveDates := (&hrModel.ApplyLeaveRecordDate{}).GetByLeaveRecordId(reports[i].ApplyLeaveRecordId)
			for _, date := range leaveDates {
				reports[i].LeaveDates = append(reports[i].LeaveDates, date.Date)
			}
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      reports,
		"TotalCount": count,
		"CalcTime":   (&assessModel.DriverMonthAssessReport{}).LatestCalcTime(),
	})
}

type DriverWaitWorkApply struct {
	ReportId int64 `json:"ReportId"`
	hrModel.ApplyLeaveRecord
}

func (h *DriverAssess) DriverWaitWorkApplyLeave(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverWaitWorkApply
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if (param.LeaveDateType == util.LeaveDateTypeLinkDay && time.Time(param.StartAt).Unix() > time.Time(param.EndAt).Unix()) || (param.LeaveDateType == util.LeaveDateTypeMultiDay && len(param.LeaveDates) == 0) {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StaffId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.LeaveType = util.LeaveTypeForWaitWork

	staffInfo := rpc.GetStaffWithId(ctx, param.StaffId)
	if staffInfo == nil {
		log.ErrorFields("rpc.GetStaffWithId error", map[string]interface{}{"id": param.StaffId})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.JobNumber = staffInfo.StaffId
	param.StaffName = staffInfo.Name

	if param.CorporationId == 0 {
		param.CorporationId = staffInfo.CorporationId
	}

	user := auth.User(ctx).GetUser()
	leaveId, err := hrHandler.StaffLeaveCreate(user, param.ApplyLeaveRecord, param.CorporationId, "erp")
	if err != nil {
		log.ErrorFields("hrHandler.StaffLeaveCreate error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	report := (&assessModel.DriverWaitWorkReport{}).FindById(param.ReportId)
	report.ApplyLeaveRecordId = leaveId
	_ = report.UpdateApplyLeaveRecordId()
	return response.Success(rsp, nil)
}
