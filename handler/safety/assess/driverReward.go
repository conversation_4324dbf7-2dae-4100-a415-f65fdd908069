package assess

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	assessModel "app/org/scs/erpv2/api/model/safety/assess"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
)

type DriverAssess struct {
	Id             int64   `json:"Id"`
	CorporationId  int64   `json:"CorporationId"`
	CorporationIds []int64 `json:"CorporationIds"`
	LineId         int64   `json:"LineId"`
	StaffId        int64   `json:"StaffId"`
	Month          int64   `json:"Month"`
	Year           int64   `json:"Year"`
	IsDone         int64   `json:"IsDone"`
	ApplyStatus    []int64 `json:"ApplyStatus"`
	model.Paginator
}

func (h *DriverAssess) AddDriverReward(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param assessModel.DriverAssessRewardReport
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.StaffId == 0 || param.RewardMoney == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	staff := rpc.GetStaffWithId(ctx, param.StaffId)
	if staff == nil {
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.StaffName = staff.Name
	param.CorporationId = staff.CorporationId

	corporation := rpc.GetCorporationDetailById(ctx, param.CorporationId)
	if corporation == nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	user := auth.User(ctx).GetUser()
	param.CorporationName = corporation.Item.Name
	param.Corporations.Build(param.CorporationId)
	param.OpUserName = user.Name
	param.OpUserId = user.Id

	tx := model.DB().Begin()
	err = param.Create(tx)
	if err != nil {
		tx.Rollback()
		return response.Error(rsp, response.DbSaveFail)
	}

	//发起流程审批
	if config.Config.Lbpm.Enable {
		byteParam, _ := json.Marshal(param)
		formData := map[string]interface{}{
			"DepartmentCode": corporation.Item.Virtual,
		}
		processTitle := fmt.Sprintf("%s的司机奖励审批", param.StaffName)
		_, err = processService.NewDispatchProcess(user, config.DriverAssessRewardApplyFormTemplate, processTitle, param.Id, param.TableName(), param.ApplyStatusFieldName(), string(byteParam), formData)

		if err != nil {
			tx.Rollback()
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.CreateProcessFail)
		}
	}
	tx.Commit()

	return response.Success(rsp, nil)
}

func (h *DriverAssess) EditDriverReward(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param assessModel.DriverAssessRewardReport
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Id == 0 || param.StaffId == 0 || param.RewardMoney == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	report := (&assessModel.DriverAssessRewardReport{}).FirstBy(param.Id)
	if report.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StaffName == "" {
		staff := rpc.GetStaffWithId(ctx, param.StaffId)
		if staff != nil {
			param.StaffName = staff.Name
			if param.CorporationId == 0 {
				param.CorporationId = staff.CorporationId
			}
		}
	}

	param.Corporations.Build(param.CorporationId)
	param.ParseOpUser(ctx)

	err = param.Update()
	if err != nil {
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)

}

// DriverRewardDetail 司机奖励详情
func (h *DriverAssess) DriverRewardDetail(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverAssess
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	report := (&assessModel.DriverAssessRewardReport{}).FirstBy(param.Id)
	if report.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	report.CorporationId, report.CorporationName = report.Corporations.GetCorporation()
	staffArchive := (&hrModel.StaffArchive{}).FirstByStaffId(report.StaffId)
	if staffArchive.Id > 0 {
		report.IsCctDriver = staffArchive.IsCctDriver
	}

	return response.Success(rsp, report)
}

func (h *DriverAssess) DeleteDriverReward(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param assessModel.DriverAssessRewardReport
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	report := (&assessModel.DriverAssessRewardReport{}).FirstBy(param.Id)
	if report.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = param.Delete()
	if err != nil {
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, nil)
}

func (h *DriverAssess) ListDriverReward(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DriverAssess
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	reports, count := (&assessModel.DriverAssessRewardReport{}).GetBy(param.CorporationIds, param.LineId, param.StaffId, param.Month, param.ApplyStatus, param.Paginator)

	for i := range reports {
		reports[i].CorporationId, reports[i].CorporationName = reports[i].Corporations.GetCorporation()
		staffArchive := (&hrModel.StaffArchive{}).FirstByStaffId(reports[i].StaffId)
		if staffArchive.Id > 0 {
			reports[i].IsCctDriver = staffArchive.IsCctDriver
		}
		var process processModel.LbpmApplyProcess
		_ = process.GetProcessByItemId(reports[i].Id, reports[i].TableName())
		reports[i].CurrentHandler = process.CurrentHandlerUserName
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      reports,
		"TotalCount": count,
	})
}

func (h *DriverAssess) ListDriverRewardExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return h.ListDriverReward(ctx, req, rsp)
}
