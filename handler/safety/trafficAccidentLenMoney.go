package safety

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service/auth"
	messageService "app/org/scs/erpv2/api/service/message"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
)

// TrafficAccidentLendMoneyRecord 事故借款
type TrafficAccidentLendMoneyRecord struct {
	safetyModel.TrafficAccidentLendMoneyRecord
	IsRestart bool   `json:"IsRestart"`
	ProcessId string `json:"ProcessId"`
}

// ApplyLendMoney 发起事故当事人分支借款申请
func (ta *TrafficAccident) ApplyLendMoney(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentLendMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	user := auth.User(ctx).GetUser()
	param.ApplyUserId = user.Id
	param.ApplyUserName = user.Name

	param.OpUserId = user.Id
	param.OpUserName = user.Name

	// 查询事故编号 存入param 消息中心使用
	var accident safetyModel.TrafficAccident
	err = accident.FindBy(param.TrafficAccidentLendMoneyRecord.TrafficAccidentId)
	if err != nil {
		log.ErrorFields("TrafficAccident FindBy fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	param.TrafficAccidentCode = accident.Code

	param.TrafficAccidentLendMoneyRecord.CorporationId, param.TrafficAccidentLendMoneyRecord.CorporationName = accident.Corporations.GetCorporation()
	param.TrafficAccidentLendMoneyRecord.License = accident.License
	param.TrafficAccidentLendMoneyRecord.LineId = accident.LineId
	param.TrafficAccidentLendMoneyRecord.LineName = accident.LineName
	param.TrafficAccidentLendMoneyRecord.DriverName = accident.DriverName

	var relater TrafficAccidentRelater
	err = relater.FindBy(param.TrafficAccidentLendMoneyRecord.TrafficAccidentRelaterId)
	if err != nil {
		log.ErrorFields("TrafficAccidentRelater FindBy fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	param.TrafficAccidentRelaterName = relater.Name

	var tx = model.DB().Begin()
	if param.IsRestart {
		var lendMoneyRecord safetyModel.TrafficAccidentLendMoneyRecord
		if err := lendMoneyRecord.FindBy(param.Id); err != nil {
			log.ErrorFields("TrafficAccidentLendMoneyRecord FindBy fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbNotFoundRecord)
		}

		param.TrafficAccidentLendMoneyRecord.ApplyStatus = util.ApplyStatusForDoing
		param.TrafficAccidentLendMoneyRecord.FormStep = util.ProcessFormStepStart
		err = param.TrafficAccidentLendMoneyRecord.TransactionUpdate(tx)
	} else {
		err = param.TrafficAccidentLendMoneyRecord.TransactionCreate(tx)
	}

	if err != nil {
		tx.Rollback()
		log.ErrorFields("TrafficAccidentLendMoneyRecord.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	tx.Commit()

	go messageService.BuildAccidentMessage(messageService.AccidentMessageForm{
		TrafficAccidentId: accident.Id,
		RelationId:        param.TrafficAccidentLendMoneyRecord.Id,
		DriverName:        accident.DriverName,
		License:           accident.License,
		ApplyUserId:       user.Id,
		ReceiveUserId:     param.TrafficAccidentLendMoneyRecord.ApproveUserId,
	}, util.ProcessMessageTypeForApprove, (&safetyModel.TrafficAccidentLendMoneyRecord{}).TableName())

	go messageService.BuildAccidentMessage(messageService.AccidentMessageForm{
		TrafficAccidentId: accident.Id,
		RelationId:        param.TrafficAccidentLendMoneyRecord.Id,
		DriverName:        accident.DriverName,
		License:           accident.License,
		ApplyUserId:       user.Id,
		ReceiveUserId:     param.TrafficAccidentLendMoneyRecord.NotifyUserId,
	}, util.ProcessMessageTypeForNotice, (&safetyModel.TrafficAccidentLendMoneyRecord{}).TableName())

	return response.Success(rsp, map[string]interface{}{
		"Id": param.TrafficAccidentLendMoneyRecord.Id,
	})
}

// LendMoneyRecord 事故借款记录
func (ta *TrafficAccident) LendMoneyRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentLendMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.TrafficAccidentId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	records := param.TrafficAccidentLendMoneyRecord.GetDoneByAccidentId(param.TrafficAccidentId)

	for i := range records {
		var relater safetyModel.TrafficAccidentRelater
		_ = relater.FindBy(records[i].TrafficAccidentRelaterId)
		records[i].TrafficAccidentRelaterName = relater.Name
		var branch safetyModel.TrafficAccidentRelaterBranch
		_ = branch.FindBy(records[i].TrafficAccidentRelaterBranchId)
		records[i].TrafficAccidentRelaterBranchType = branch.BranchType
		records[i].TotalDrawbackMoney = (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).DoneTotalMoneyForLendMoneyRecord(records[i].Id)

		approveUser := rpc.GetUserInfoById(ctx, records[i].ApproveUserId)
		if approveUser != nil {
			records[i].ApproveUserName = approveUser.Nickname
		}
	}

	return response.Success(rsp, map[string]interface{}{"Items": records, "TotalCount": len(records), "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

// RelaterLendMoneyRecord 当事人的事故借款记录列表
func (ta *TrafficAccident) RelaterLendMoneyRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentLendMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.TrafficAccidentRelaterId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	records := param.TrafficAccidentLendMoneyRecord.GetByRelaterId(param.TrafficAccidentRelaterId)

	for i := range records {
		var relater safetyModel.TrafficAccidentRelater
		_ = relater.FindBy(records[i].TrafficAccidentRelaterId)
		records[i].TrafficAccidentRelaterName = relater.Name
		var branch safetyModel.TrafficAccidentRelaterBranch
		_ = branch.FindBy(records[i].TrafficAccidentRelaterBranchId)
		records[i].TrafficAccidentRelaterBranchType = branch.BranchType
		approveUser := rpc.GetUserInfoById(ctx, records[i].ApproveUserId)
		if approveUser != nil {
			records[i].ApproveUserName = approveUser.Nickname
		}
	}

	return response.Success(rsp, map[string]interface{}{"Items": records, "TotalCount": len(records), "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

// BranchLendMoneyRecord 事故分支的借款记录列表
func (ta *TrafficAccident) BranchLendMoneyRecord(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentLendMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.TrafficAccidentRelaterBranchId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	records := param.TrafficAccidentLendMoneyRecord.GetByBranchId(param.TrafficAccidentRelaterBranchId)

	for i := range records {
		var relater safetyModel.TrafficAccidentRelater
		_ = relater.FindBy(records[i].TrafficAccidentRelaterId)
		records[i].TrafficAccidentRelaterName = relater.Name
		var branch safetyModel.TrafficAccidentRelaterBranch
		_ = branch.FindBy(records[i].TrafficAccidentRelaterBranchId)
		records[i].TrafficAccidentRelaterBranchType = branch.BranchType
		records[i].TotalDrawbackMoney = (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).TotalMoneyForLendMoneyRecord(records[i].Id)
		approveUser := rpc.GetUserInfoById(ctx, records[i].ApproveUserId)
		if approveUser != nil {
			records[i].ApproveUserName = approveUser.Nickname
		}
	}

	return response.Success(rsp, map[string]interface{}{"Items": records, "TotalCount": len(records), "FileHttpPrefix": config.Config.StaticFileHttpPrefix})
}

// ShowLendMoneyApply 事故当事人分支借款记录的详情
func (ta *TrafficAccident) ShowLendMoneyApply(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentLendMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var lendMoneyRecord safetyModel.TrafficAccidentLendMoneyRecord
	err = lendMoneyRecord.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("lendMoneyRecord.FindBy", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	var relater safetyModel.TrafficAccidentRelater
	_ = relater.FindBy(lendMoneyRecord.TrafficAccidentRelaterId)
	lendMoneyRecord.TrafficAccidentRelaterName = relater.Name
	lendMoneyRecord.LiabilityType = relater.LiabilityType

	var branch safetyModel.TrafficAccidentRelaterBranch
	_ = branch.FindBy(lendMoneyRecord.TrafficAccidentRelaterBranchId)
	lendMoneyRecord.TrafficAccidentRelaterBranchType = branch.BranchType
	lendMoneyRecord.IsProcessRelater = processService.CheckIsProcessRelater(lendMoneyRecord.Id, lendMoneyRecord.TemplateFormId(), auth.User(ctx).GetUserId())

	approveUser := rpc.GetUserInfoById(ctx, lendMoneyRecord.ApproveUserId)
	if approveUser != nil {
		lendMoneyRecord.ApproveUserName = approveUser.Nickname
	}

	if lendMoneyRecord.ApplyStatus == util.ApplyStatusForDoing && auth.User(ctx).GetUserId() == lendMoneyRecord.ApproveUserId {
		lendMoneyRecord.IsApproveUser = true
	}

	return response.Success(rsp, lendMoneyRecord)
}

// TrafficAccidentDrawbackMoneyRecord 事故退款
type TrafficAccidentDrawbackMoneyRecord struct {
	safetyModel.TrafficAccidentDrawbackMoneyRecord
	ProcessId string `json:"ProcessId"`
	IsRestart bool   `json:"IsRestart"`
}

// ApplyDrawbackMoney 发起事故退款申请
func (ta *TrafficAccident) ApplyDrawbackMoney(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentDrawbackMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	user := auth.User(ctx).GetUser()
	param.OpUserId = user.Id
	param.OpUserName = user.Name

	//查询借款记录
	var lendMoney safetyModel.TrafficAccidentLendMoneyRecord
	err = lendMoney.FindBy(param.TrafficAccidentLendMoneyRecordId)
	if err != nil {
		log.ErrorFields("lendMoney.FindBy is error", map[string]interface{}{"err": err, "id": param.TrafficAccidentLendMoneyRecordId})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	param.TrafficAccidentRelaterBranchId = lendMoney.TrafficAccidentRelaterBranchId

	param.TrafficAccidentRelaterId = lendMoney.TrafficAccidentRelaterId
	param.TrafficAccidentId = lendMoney.TrafficAccidentId

	// 查询事故编号 存入param 消息中心使用
	var accident safetyModel.TrafficAccident
	err = accident.FindBy(lendMoney.TrafficAccidentId)
	if err != nil {
		log.ErrorFields("TrafficAccident FindBy fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	param.TrafficAccidentCode = accident.Code

	param.TrafficAccidentDrawbackMoneyRecord.CorporationId, param.TrafficAccidentDrawbackMoneyRecord.CorporationName = accident.Corporations.GetCorporation()
	param.TrafficAccidentDrawbackMoneyRecord.License = accident.License
	param.TrafficAccidentDrawbackMoneyRecord.LineId = accident.LineId
	param.TrafficAccidentDrawbackMoneyRecord.LineName = accident.LineName
	param.TrafficAccidentDrawbackMoneyRecord.DriverName = accident.DriverName

	var relater safetyModel.TrafficAccidentRelater
	err = relater.FindBy(lendMoney.TrafficAccidentRelaterId)
	if err != nil {
		log.ErrorFields("TrafficAccidentRelater FindBy is error", map[string]interface{}{"err": err})
	}
	param.TrafficAccidentRelaterName = relater.Name

	var tx = model.DB().Begin()
	if param.IsRestart {
		var drawbackMoneyRecord safetyModel.TrafficAccidentDrawbackMoneyRecord
		if err := drawbackMoneyRecord.FindBy(param.Id); err != nil {
			log.ErrorFields("TrafficAccidentDrawbackMoneyRecord FindBy fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		param.TrafficAccidentDrawbackMoneyRecord.ApplyStatus = util.ApplyStatusForDoing
		err = param.TrafficAccidentDrawbackMoneyRecord.TransactionUpdate(tx)
	} else {
		err = param.TrafficAccidentDrawbackMoneyRecord.TransactionCreate(tx)
	}
	if err != nil {
		tx.Rollback()
		log.ErrorFields("TrafficAccidentDrawbackMoneyRecord.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	tx.Commit()

	//发系统消息
	return response.Success(rsp, map[string]interface{}{
		"Id": param.TrafficAccidentDrawbackMoneyRecord.Id,
	})
}

// ShowDrawbackMoneyApply 事故退款记录的详情
func (ta *TrafficAccident) ShowDrawbackMoneyApply(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentDrawbackMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var drawbackMoneyRecord safetyModel.TrafficAccidentDrawbackMoneyRecord
	err = drawbackMoneyRecord.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("drawbackMoneyRecord.FindBy", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	return response.Success(rsp, drawbackMoneyRecord)

}

// TrafficAccidentPaymentMoneyRecord 事故付款
type TrafficAccidentPaymentMoneyRecord struct {
	safetyModel.TrafficAccidentPaymentMoneyRecord
	ProcessId string `json:"ProcessId"`
	IsRestart bool   `json:"IsRestart"`
}

// ApplyPaymentMoney 发起事故分支付款申请
func (ta *TrafficAccident) ApplyPaymentMoney(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentPaymentMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.FixOffice == 0 || param.TrafficAccidentRelaterBranchId == 0 {
		log.ErrorFields("[FixOffice,TrafficAccidentRelaterBranchId] is required", nil)
		return response.Error(rsp, response.ParamsMissing)
	}

	user := auth.User(ctx).GetUser()
	param.OpUserId = user.Id
	param.OpUserName = user.Name

	var branch safetyModel.TrafficAccidentRelaterBranch
	err = branch.FindBy(param.TrafficAccidentRelaterBranchId)
	if err != nil {
		log.ErrorFields("TrafficAccidentRelaterBranch FindBy is error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	if branch.BranchType != util.AccidentBranchTypeForSelfVehicleBroken {
		return response.Error(rsp, response.FAIL)
	}

	param.TrafficAccidentRelaterId = branch.TrafficAccidentRelaterId
	param.TrafficAccidentId = branch.TrafficAccidentId

	var relater safetyModel.TrafficAccidentRelater
	err = relater.FindBy(branch.TrafficAccidentRelaterId)
	if err != nil {
		log.ErrorFields("TrafficAccidentRelater FindBy is error", map[string]interface{}{"err": err})
	}

	//一个事故只能有一条通过或在审批中的付款申请
	if (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).ExistPaymentRecord(branch.TrafficAccidentId) {
		return response.Error(rsp, response.FAIL)
	}

	// 查询事故编号 存入param 消息中心使用
	var accident safetyModel.TrafficAccident
	err = accident.FindBy(branch.TrafficAccidentId)
	if err != nil {
		log.ErrorFields("TrafficAccident FindBy fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	var tx = model.DB().Begin()
	if param.IsRestart {
		var paymentMoneyRecord safetyModel.TrafficAccidentPaymentMoneyRecord
		if err := paymentMoneyRecord.FindBy(param.Id); err != nil {
			log.ErrorFields("TrafficAccidentPaymentMoneyRecord FindBy fail", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbNotFoundRecord)
		}

		param.TrafficAccidentPaymentMoneyRecord.ApplyStatus = util.ApplyStatusForDoing
		param.TrafficAccidentPaymentMoneyRecord.FormStep = util.ProcessFormStepStart
		err = param.TrafficAccidentPaymentMoneyRecord.TransactionUpdate(tx)
	} else {
		err = param.TrafficAccidentPaymentMoneyRecord.TransactionCreate(tx)
	}

	if err != nil {
		tx.Rollback()
		log.ErrorFields("TrafficAccidentPaymentMoneyRecord.Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	tx.Commit()
	go messageService.BuildAccidentMessage(messageService.AccidentMessageForm{
		TrafficAccidentId: accident.Id,
		RelationId:        param.TrafficAccidentPaymentMoneyRecord.Id,
		DriverName:        accident.DriverName,
		License:           accident.License,
		ApplyUserId:       user.Id,
		ReceiveUserId:     param.TrafficAccidentPaymentMoneyRecord.ApproveUserId,
	}, util.ProcessMessageTypeForApprove, (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).TableName())

	go messageService.BuildAccidentMessage(messageService.AccidentMessageForm{
		TrafficAccidentId: accident.Id,
		RelationId:        param.TrafficAccidentPaymentMoneyRecord.Id,
		DriverName:        accident.DriverName,
		License:           accident.License,
		ApplyUserId:       user.Id,
		ReceiveUserId:     param.TrafficAccidentPaymentMoneyRecord.NotifyUserId,
	}, util.ProcessMessageTypeForNotice, (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).TableName())

	return response.Success(rsp, map[string]interface{}{
		"Id": param.TrafficAccidentPaymentMoneyRecord.Id,
	})
}

// ShowPaymentMoneyApply 事故付款记录的详情
func (ta *TrafficAccident) ShowPaymentMoneyApply(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentPaymentMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var paymentMoneyRecord safetyModel.TrafficAccidentPaymentMoneyRecord
	err = paymentMoneyRecord.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("paymentMoneyRecord.FindBy", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	// 查询流程表中的数据
	var process processModel.LbpmApplyProcess
	err = process.GetProcessByItemId(paymentMoneyRecord.Id, paymentMoneyRecord.TableName())
	if err != nil {
		log.ErrorFields("GetProcessByItemId err", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	var processParam safetyModel.TrafficAccidentPaymentMoneyRecord
	_ = json.Unmarshal(process.Param, &processParam)

	// 查询分支是否存在借款
	records := (&safetyModel.TrafficAccidentLendMoneyRecord{}).GetByBranchId(paymentMoneyRecord.TrafficAccidentRelaterBranchId)
	if len(records) > 0 {
		paymentMoneyRecord.IsExistsLend = 1
	} else {
		paymentMoneyRecord.IsExistsLend = 2
	}

	approveUser := rpc.GetUserInfoById(ctx, paymentMoneyRecord.ApproveUserId)
	if approveUser != nil {
		paymentMoneyRecord.ApproveUserName = approveUser.Nickname
	}

	if paymentMoneyRecord.ApplyStatus == util.ApplyStatusForDoing && auth.User(ctx).GetUserId() == paymentMoneyRecord.ApproveUserId {
		paymentMoneyRecord.IsApproveUser = true
	}

	return response.Success(rsp, paymentMoneyRecord)
}

// UpdatePaymentMoneyApply 事故付款记录更新
func (ta *TrafficAccident) UpdatePaymentMoneyApply(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TrafficAccidentPaymentMoneyRecord
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	var paymentMoneyRecord safetyModel.TrafficAccidentPaymentMoneyRecord
	err = paymentMoneyRecord.FindBy(param.Id)
	if err != nil {
		log.ErrorFields("paymentMoneyRecord.FindBy", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	//维修信息
	if paymentMoneyRecord.FormStep == util.ProcessFormStepTwo {
		if len(param.FixBillPath) == 0 || len(param.FixBeforePath) == 0 || len(param.FixAfterPath) == 0 {
			return response.Error(rsp, response.ParamsMissing)
		}

		paymentMoneyRecord.FixFile = param.FixFile
		paymentMoneyRecord.FormStep = util.ProcessFormStepThree

		err = paymentMoneyRecord.UpdateFixFile()
	} else if paymentMoneyRecord.FormStep == util.ProcessFormStepThree { //付款信息
		if param.FlowTo == "" || param.CollectorName == "" {
			return response.Error(rsp, response.ParamsMissing)
		}
		paymentMoneyRecord.IsLend = param.IsLend
		paymentMoneyRecord.ConfirmFilePath = param.ConfirmFilePath
		paymentMoneyRecord.PaymentInfo = param.PaymentInfo
		paymentMoneyRecord.FormStep = util.ProcessFormStepFour

		err = paymentMoneyRecord.UpdatePaymentInfo()
	}

	if err != nil {
		return response.Error(rsp, response.DbUpdateFail)
	}

	var relater safetyModel.TrafficAccidentRelater
	err = relater.FindBy(paymentMoneyRecord.TrafficAccidentRelaterId)
	if err != nil {
		log.ErrorFields("TrafficAccidentRelater FindBy is error", map[string]interface{}{"err": err})
	}

	var accident safetyModel.TrafficAccident
	err = accident.FindBy(paymentMoneyRecord.TrafficAccidentId)
	if err != nil {
		log.ErrorFields("TrafficAccident FindBy fail", map[string]interface{}{"err": err})
	}

	paymentMoneyRecord.ConfirmFilePath = param.ConfirmFilePath

	//更新param
	paramValue, _ := json.Marshal(paymentMoneyRecord)
	err = processService.ResetProcessColumnValue(paymentMoneyRecord.Id, paymentMoneyRecord.TableName(), "param", paramValue)
	if err != nil {
		log.ErrorFields("processService.ResetProcessColumnValue is error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	err = processService.ResetProcessFormFieldValue(paymentMoneyRecord.Id, paymentMoneyRecord.TableName(), map[string]interface{}{
		"PaymentMoney": float64(param.PaymentMoney) / 100,
	})

	if err != nil {
		log.Error("processService.ResetProcessFormFieldValue err = ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)

}
