package safety

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type QualityAssessmentCate struct {
	safety.QualityAssessmentCate
}

func (qas *QualityAssessmentStandard) AddAssessmentCate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param QualityAssessmentCate
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.ParentId > 0 && (param.Name == "" || time.Time(param.ExpireAt).IsZero()) {
		return response.Error(rsp, response.ParamsMissing)
	}

	if param.ParentId == 0 && param.Name == "" {
		return response.Error(rsp, response.ParamsMissing)
	}

	if param.Code != "" && (&safety.QualityAssessmentCate{}).IsExistByCode(auth.User(ctx).GetTopCorporationId(), param.Code) {
		return response.Error(rsp, response.CodeDontRepeat)
	}

	param.GroupId = auth.User(ctx).GetTopCorporationId()
	err = param.QualityAssessmentCate.Create()
	if err != nil {
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) EditAssessmentCate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param QualityAssessmentCate
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.ParentId > 0 && (param.Name == "" || time.Time(param.ExpireAt).IsZero()) {
		return response.Error(rsp, response.ParamsMissing)
	}

	if param.ParentId == 0 && param.Name == "" {
		return response.Error(rsp, response.ParamsMissing)
	}

	cate := (&safety.QualityAssessmentCate{}).FindBy(param.Id)
	if cate.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	if param.Code != "" {
		tmpCate := (&safety.QualityAssessmentCate{}).FindByCode(auth.User(ctx).GetTopCorporationId(), param.Code)
		if tmpCate.Id > 0 && tmpCate.Id != cate.Id {
			return response.Error(rsp, response.CodeDontRepeat)
		}
	}
	param.GroupId = auth.User(ctx).GetTopCorporationId()

	err = param.QualityAssessmentCate.Update()
	if err != nil {
		return response.Error(rsp, response.DbUpdateFail)
	}

	return response.Success(rsp, nil)
}

func (qas *QualityAssessmentStandard) ListAssessmentCate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	cates := (&safety.QualityAssessmentCate{}).GetAll(auth.User(ctx).GetTopCorporationId())
	var results []safety.QualityAssessmentCate
	for i := range cates {
		if cates[i].ParentId == 0 {
			for j := range cates {
				if cates[j].ParentId == cates[i].Id {
					cates[j].StandardCount = (&safety.QualityAssessmentStandards{}).GetCountByQualityAssessmentCateId(cates[j].Id)
					cates[i].Children = append(cates[i].Children, cates[j])
				}
			}
			results = append(results, cates[i])
		}
	}

	return response.Success(rsp, results)
}

func (qas *QualityAssessmentStandard) DeleteAssessmentCate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param QualityAssessmentCate
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	cate := (&safety.QualityAssessmentCate{}).FindBy(param.Id)
	if cate.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	if cate.ParentId == 0 && (&safety.QualityAssessmentCate{}).GetCountByParentId(cate.Id) > 0 {
		return response.Error(rsp, response.DeleteParentDataFail)
	}

	if cate.ParentId > 0 && (&safety.QualityAssessmentStandards{}).GetCountByQualityAssessmentCateId(cate.Id) > 0 {
		return response.Error(rsp, response.DeleteParentDataFail)
	}

	err = param.QualityAssessmentCate.Delete()
	if err != nil {
		return response.Error(rsp, response.DbDeleteFail)
	}

	return response.Success(rsp, nil)
}
