package safety

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"github.com/micro/go-micro/v2/api/proto"
	"gorm.io/gorm"
	"path"
	"time"
)

type EmphasisHelpDriver struct {
	safety.SafeEmphasisHelpDrivers
	Items           []string
	AddAt, FinishAt int64
	model.Paginator
	StartAt, EndAt int64
	Driver         string // 司机名 + 工号
	CorporationIds []int64
}

type EmphasisHelpDriverRecord struct {
	safety.SafeEmphasisHelpDriverRecords
	HelpId int64
	HelpAt int64 // 记录表 帮扶时间
}

func (e *EmphasisHelpDriver) AddCause(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	//var q EmphasisHelpDriver
	//err := json.Unmarshal([]byte(req.Body), &q)
	//if err != nil {
	//	log.Error("json.Unmarshal err=", err.Error())
	//	return response.Error(rsp, response.ParamsInvalid)
	//}
	//
	//err = util.Validator().Struct(q)
	//if err != nil {
	//	log.Error("validate.Struct err=", err.Error())
	//	return response.Error(rsp, response.ParamsInvalid)
	//}
	//
	//if len(q.Items) != 10  {
	//	log.Error("q.Items != 10")
	//	return response.Error(rsp, response.ParamsInvalid)
	//}
	//
	//// 获取当前用户的StaffId
	//staffId := util.GetStaffId(ctx)
	//if staffId <= 0 {
	//	log.Error("Staff not fount", map[string]interface{}{"staffId": staffId})
	//	return response.Error(rsp, response.Forbidden)
	//}
	//
	////
	//staffItem := rpc.GetStaffWithId(ctx, staffId)
	//if staffItem == nil {
	//	log.Error("rpc.GetStaffWithId Staff not fount", map[string]interface{}{"staffId": staffId})
	//	return response.Error(rsp, response.Forbidden)
	//}
	//
	//var causes []safety.SafeEmphasisHelpDriverCauses
	//for i, item := range q.Items {
	//	if item  == "" {
	//		continue
	//	}
	//	var cause safety.SafeEmphasisHelpDriverCauses
	//	cause.Id = model.Id()
	//	cause.GroupId = staffItem.TopCorporationId
	//	cause.Sort = int64(i)
	//	cause.Content = item
	//	causes = append(causes, cause)
	//}
	//
	//err = (&safety.SafeEmphasisHelpDriverCauses{}).Add(causes)
	//if err != nil {
	//	log.Error("Add err", map[string]interface{}{"causes": causes, "err": err})
	//	return response.Error(rsp, response.DbSaveFail)
	//}
	//
	//return response.Success(rsp, nil)

	return e.EditCause(ctx, req, rsp)
}

func (e *EmphasisHelpDriver) ListCause(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q EmphasisHelpDriver
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	rspD := make([]string, 10)

	var cause safety.SafeEmphasisHelpDriverCauses
	cause.GroupId = auth.User(ctx).GetTopCorporationId()
	list, err := (&cause).List()
	if err != nil {
		log.Error("List err", map[string]interface{}{"cause": cause, "err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	for _, c := range list {
		rspD[c.Sort] = c.Content
	}

	return response.Success(rsp, rspD)
}

func (e *EmphasisHelpDriver) EditCause(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q EmphasisHelpDriver
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(q.Items) != 10 {
		log.Error("q.Items != 10")
		return response.Error(rsp, response.ParamsInvalid)
	}
	topCorporationId := auth.User(ctx).GetTopCorporationId()
	var causes []safety.SafeEmphasisHelpDriverCauses
	for i, item := range q.Items {
		if item == "" {
			continue
		}

		var cause safety.SafeEmphasisHelpDriverCauses
		cause.Id = model.Id()
		cause.GroupId = topCorporationId
		cause.Sort = int64(i)
		cause.Content = item
		causes = append(causes, cause)
	}

	err = (&safety.SafeEmphasisHelpDriverCauses{}).Edit(topCorporationId, causes)
	if err != nil {
		log.Error("Add err", map[string]interface{}{"causes": causes, "err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

func (e *EmphasisHelpDriver) Add(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q EmphasisHelpDriver
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.StaffId == 0 || q.CauseContent == "" || q.AddAt == 0 || q.FinishAt == 0 {
		log.Error("q.Items != 10")
		return response.Error(rsp, response.ParamsInvalid)
	}

	//
	q.SafeEmphasisHelpDrivers.AddAt = time.Unix(q.AddAt, 0)
	q.SafeEmphasisHelpDrivers.FinishAt = time.Unix(q.FinishAt, 0)

	// 获取司机信息
	driver := rpc.GetStaffWithId(ctx, q.StaffId)
	if driver == nil {
		log.Error("rpc.GetStaffWithId Staff not fount", map[string]interface{}{"driver": driver})
		return response.Error(rsp, response.Forbidden)
	}

	q.SafeEmphasisHelpDrivers.Id = model.Id()
	q.SafeEmphasisHelpDrivers.StaffName = driver.Name
	q.SafeEmphasisHelpDrivers.StaffIdStr = driver.StaffId
	q.SafeEmphasisHelpDrivers.CorporationId = driver.CorporationId

	detail := rpc.GetCorporationDetailById(ctx, driver.CorporationId)
	if detail != nil {
		q.SafeEmphasisHelpDrivers.GroupId = detail.GroupId
		q.SafeEmphasisHelpDrivers.CompanyId = detail.CompanyId
		q.SafeEmphasisHelpDrivers.BranchId = detail.BranchId
		q.SafeEmphasisHelpDrivers.DepartmentId = detail.DepartmentId
		q.SafeEmphasisHelpDrivers.FleetId = detail.FleetId

		q.SafeEmphasisHelpDrivers.Corporation = detail.Item.Name
	}

	q.SafeEmphasisHelpDrivers.Terminate = safety.TERMINATE_NO_1

	err = (&q.SafeEmphasisHelpDrivers).Add()
	if err != nil {
		log.Error("Add err =", err)
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)

}

func (e *EmphasisHelpDriver) List(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q EmphasisHelpDriver
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	q.CorporationIds = service.AuthCorporationIdProvider(ctx, q.CorporationIds)

	var st, et time.Time

	if q.StartAt != 0 {
		st = time.Unix(q.StartAt, 0)
	}

	if q.EndAt != 0 {
		et = time.Unix(q.EndAt, 0)
	}

	base := path.Base(req.Path)

	if base == "List" {
		q.SafeEmphasisHelpDrivers.Terminate = safety.TERMINATE_NO_1
	} else if base == "ListHistory" {
		q.SafeEmphasisHelpDrivers.Terminate = safety.TERMINATE_YES_2
	} else {
		log.Error("req.Path == ", req.Path)
		return response.Error(rsp, response.FAIL)
	}

	list, totalCount, err := (&q.SafeEmphasisHelpDrivers).List(q.CorporationIds, q.Driver, st, et, q.Paginator)
	if err != nil {
		log.Error("List err =", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	data := map[string]interface{}{
		"Items":      list,
		"TotalCount": totalCount,
	}

	return response.Success(rsp, data)

}

func (e *EmphasisHelpDriver) GetById(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q EmphasisHelpDriver
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	getDetail, err := (&q.SafeEmphasisHelpDrivers).GetDetail()
	if err != nil {
		log.Error("GetDetail err = ", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	return response.Success(rsp, getDetail)

}

func (e *EmphasisHelpDriver) Terminate(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q EmphasisHelpDriver
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 {
		log.Error("q.Id == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	q.SafeEmphasisHelpDrivers.Terminate = safety.TERMINATE_YES_2
	q.SafeEmphasisHelpDrivers.TerminateAt = time.Now()

	err = (&q.SafeEmphasisHelpDrivers).EditTerm()
	if err != nil {
		log.Error("EditTerm err== ", err)
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)
}

func (e *EmphasisHelpDriver) AddRecord(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q EmphasisHelpDriverRecord
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.HelpId == 0 || q.Remind == 0 || q.HelpAt == 0 {
		log.Error("q.HelpId == 0 || q.Remind == 0 || q.HelpAt == 0")
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 获取帮扶表信息
	var help safety.SafeEmphasisHelpDrivers
	help.Id = q.HelpId
	getDetail, err := (&help).GetDetail()
	if err != nil {
		log.Error("GetDetail err = ", err)
		return response.Error(rsp, response.DbQueryFail)
	}

	q.SafeEmphasisHelpDriverRecords.Id = model.Id()
	q.SafeEmphasisHelpDriverRecords.GroupId = getDetail.GroupId
	q.SafeEmphasisHelpDriverRecords.CompanyId = getDetail.CompanyId
	q.SafeEmphasisHelpDriverRecords.BranchId = getDetail.BranchId
	q.SafeEmphasisHelpDriverRecords.DepartmentId = getDetail.DepartmentId
	q.SafeEmphasisHelpDriverRecords.FleetId = getDetail.FleetId
	q.SafeEmphasisHelpDriverRecords.FkHelpId = q.HelpId

	q.SafeEmphasisHelpDriverRecords.HelpAt = time.Unix(q.HelpAt, 0)

	err = (&q.SafeEmphasisHelpDriverRecords).Add()
	if err != nil {
		log.Error("Add err =", err)
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

func (e *EmphasisHelpDriver) ListRecord(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q EmphasisHelpDriverRecord
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.HelpId == 0 {
		log.Error("q.HelpId == 0")
		return response.Error(rsp, response.ParamsMissing)
	}

	list, err := (&q.SafeEmphasisHelpDriverRecords).ListByHelpId(q.HelpId)
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			log.Error("List err =", err)
			return response.Error(rsp, response.DbQueryFail)
		}
	}

	return response.Success(rsp, list)
}

func (e *EmphasisHelpDriver) EditRecord(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	var q EmphasisHelpDriverRecord
	err := json.Unmarshal([]byte(req.Body), &q)
	if err != nil {
		log.Error("json.Unmarshal err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = util.Validator().Struct(q)
	if err != nil {
		log.Error("validate.Struct err=", err.Error())
		return response.Error(rsp, response.ParamsInvalid)
	}

	if q.Id == 0 || q.Remind == 0 || q.HelpAt == 0 {
		log.Error("q.HelpId == 0 || q.Remind == 0 || q.HelpAt == 0")
		return response.Error(rsp, response.ParamsInvalid)
	}

	q.SafeEmphasisHelpDriverRecords.HelpAt = time.Unix(q.HelpAt, 0)

	err = (&q.SafeEmphasisHelpDriverRecords).Edit()
	if err != nil {
		log.Error("Edit err =", err)
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

func (e *EmphasisHelpDriver) ListHistory(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return e.List(ctx, req, rsp)
}

func (e *EmphasisHelpDriver) ListHistoryRecord(ctx context.Context, req *go_api.Request, rsp *go_api.Response) error {
	return e.ListRecord(ctx, req, rsp)
}
