package operation

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/operation"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type CharteredOrderHandler struct {
}

type CharteredOrderTripParam struct {
	OrderId string `json:"OrderId"`
	operation.CharteredOrderTrip
}

type CharteredOrderListItem struct {
	OrderId      string                      `json:"OrderId"`      //订单ID
	Name         string                      `json:"Name"`         //姓名
	Phone        string                      `json:"Phone"`        //手机号
	Count        int64                       `json:"Count"`        //人数
	CreatedAt    model.LocalTime             `json:"CreatedAt"`    //创建时间
	StartAt      model.LocalTime             `json:"StartAt"`      //开始时间
	EndAt        model.LocalTime             `json:"EndAt"`        //结束时间
	ParkingItems []CharteredOrderParkingItem `json:"ParkingItems"` //场站信息
}

type CharteredOrderParkingItem struct {
	ParkingId   int64  `json:"ParkingId"`   //场站ID
	ParkingName string `json:"ParkingName"` //场站名称
}

// ListCharteredOrder 获取用户的包车订单列表
func (h *CharteredOrderHandler) ListCharteredOrder(ctx context.Context, req *api.Request, rsp *api.Response) error {
	authUser := auth.User(ctx).GetUser()

	startAt := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local)
	endAt := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 23, 59, 59, 999, time.Local)
	//通过rpc获取订单列表
	orders := rpc.GetContractOrderWithPhone(ctx, authUser.TopCorporationId, authUser.Phone, []string{}, startAt, endAt)
	var items []CharteredOrderListItem
	for i := range orders {
		if orders[i].Type == 2 {
			var item = CharteredOrderListItem{
				OrderId:   orders[i].OrderId,
				Name:      orders[i].Name,
				Phone:     orders[i].Phone,
				Count:     orders[i].Count,
				CreatedAt: model.LocalTime(time.Unix(orders[i].CreatedTime, 0)),
				StartAt:   model.LocalTime(time.Unix(orders[i].StartDate, 0)),
				EndAt:     model.LocalTime(time.Unix(orders[i].EndDate, 0)),
			}

			for _, parking := range orders[i].AttractionItems {
				item.ParkingItems = append(item.ParkingItems, CharteredOrderParkingItem{
					ParkingId:   parking.ParkingId,
					ParkingName: parking.ParkingName,
				})
			}
			items = append(items, item)
		}
	}
	return response.Success(rsp, map[string]interface{}{
		"Items": items,
	})
}

// AddCharteredOrderTrip 新增行程
func (h *CharteredOrderHandler) AddCharteredOrderTrip(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CharteredOrderTripParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	//查询订单 判断订单是否有效

	//查询场站
	fromParking := rpc.GetParkingWithId(ctx, param.FromParkingId)
	if fromParking == nil {
		return response.Error(rsp, "选择的场站不存在")
	}
	toParking := rpc.GetParkingWithId(ctx, param.ToParkingId)
	if toParking == nil {
		return response.Error(rsp, "选择的场站不存在")
	}

	if param.FromParkingId == param.ToParkingId {
		return response.Error(rsp, "起点和终点不能相同")
	}

	tx := model.DB().Begin()
	//查询订单列表是否有当前订单
	order := (&operation.CharteredOrder{}).FirstById(param.OrderId)
	if order.Id == 0 {
		//订单不存在，需要创建订单
		order = operation.CharteredOrder{
			OrderId: param.OrderId,
			Status:  1,
		}

		err = order.Create(tx)
		if err != nil {
			tx.Rollback()
			log.Error("CharteredOrder.Create error", err)
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	var trip = operation.CharteredOrderTrip{
		CharteredOrderId: order.Id,
		FromParkingId:    param.FromParkingId,
		FromParkingName:  fromParking.Name,
		ToParkingId:      param.ToParkingId,
		ToParkingName:    toParking.Name,
		OrderType:        order.OrderType,
		Status:           1,
	}

	err = trip.Create(tx)
	if err != nil {
		tx.Rollback()
		log.Error("CharteredOrderTrip.Create error", err)
		return response.Error(rsp, response.DbSaveFail)
	}

	tx.Commit()

	return response.Success(rsp, map[string]interface{}{
		"Id": trip.Id,
	})

}
