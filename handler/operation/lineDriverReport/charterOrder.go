package lineDriverReport

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/export"
	"app/org/scs/erpv2/api/model/operation"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	exportService "app/org/scs/erpv2/api/service/export"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	xlsxUtil "app/org/scs/erpv2/api/util/xlsx"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"sort"
	"strconv"
	"time"
)

type CharterOrderBriefParam struct {
	operation.CharterOrderBrief
}

func (*OperationReport) AddCharterOrderBrief(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CharterOrderBriefParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal error", err)
		return response.Error(rsp, response.ParamsInvalid)
	}

	var user = auth.User(ctx)

	var timeAt = time.Now()
	startAt := time.Date(timeAt.Year(), timeAt.Month(), timeAt.Day(), 0, 0, 0, 0, time.Local)
	endAt := time.Date(timeAt.Year(), timeAt.Month(), timeAt.Day(), 23, 59, 59, 999, time.Local)

	if param.CustomerCompany == "" || param.CustomerName == "" || param.CustomerPhone == "" {
		return response.Error(rsp, response.ParamsInvalid)
	}
	if len(param.CharterOrderItems) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	for _, order := range param.CharterOrderItems {
		if order.CustomerName == "" || order.CustomerPhone == "" || order.DepartAddress == "" || order.ArriveAddress == "" || order.AssignCorporationId == 0 ||
			order.VehicleNum <= 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	for _, dOrder := range param.DispatchOrderItems {
		if dOrder.ExecCorporationId == 0 || dOrder.ExecVehicleId == 0 || dOrder.ExecDrivers == nil ||
			dOrder.TravelDates == "" {
			return response.Error(rsp, response.ParamsInvalid)
		}
		if !checkCharterOrderId(param.CharterOrderItems, dOrder.AssociationCharterOrderIndex) {
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	var topCorpId = user.GetTopCorporationId()

	param.Code = getCharterOrderBriefCode(startAt, endAt)

	param.TopCorporationId = topCorpId
	param.ParseOpUser(ctx)

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = param.Create(tx)
	if err != nil {
		log.Error("CharterOrderBrief.Create error", err)
		return response.Error(rsp, response.DbSaveFail)
	}

	for key, order := range param.CharterOrderItems {
		order.TopCorporationId = topCorpId
		order.CharterOrderBriefId = param.Id
		order.ParseOpUser(ctx)
		err = order.Create(tx)
		if err != nil {
			log.Error("CharterOrder.Create error", err)
			return response.Error(rsp, response.DbSaveFail)
		}
		param.CharterOrderItems[key] = order
	}

	for _, dOrder := range param.DispatchOrderItems {
		dOrder.CharterOrderBriefId = param.Id
		orderId, ok := getCharterOrderId(param.CharterOrderItems, dOrder.AssociationCharterOrderIndex)
		if !ok {
			err = errors.New(fmt.Sprintf("getCharterOrderId error"))
			log.Error("getCharterOrderId error", err)
			return response.Error(rsp, response.DbSaveFail)
		}
		dOrder.AssociationCharterOrderId = orderId
		dOrder.ParseOpUser(ctx)
		err = dOrder.Create(tx)
		if err != nil {
			log.Error("DispatchOrderItems.Create error", err)
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	return response.Success(rsp, nil)
}

func checkCharterOrderId(items []operation.CharterOrder, index int64) bool {
	for _, item := range items {
		if item.Sort == index {
			return true
		}
	}
	return false
}

func getCharterOrderId(items []operation.CharterOrder, index int64) (int64, bool) {
	for _, item := range items {
		if item.Sort == index {
			return item.Id, true
		}
	}
	return 0, false
}

func getCharterOrderBriefCode(startAt, endAt time.Time) string {
	// 编号规则 yyyymmdd + 顺序号[0001-9999]

	item := (&operation.CharterOrderBrief{}).GetLatest(startAt, endAt)
	var number int64
	if item.Id > 0 {
		number, _ = strconv.ParseInt(item.Code[8:], 10, 64)
	}
	var numberStr = StringMakeUpPosition(fmt.Sprintf("%v", number+1), 4)
	newCode := fmt.Sprintf("%v%v", startAt.Format(model.DateFormat_Merge), numberStr)
	log.DebugFields("编号:%v", map[string]interface{}{
		"Code": newCode,
	})
	return newCode
}

func StringMakeUpPosition(str string, n int) string {
	var lessLength = n - len(str)
	for i := 0; i < lessLength; i++ {
		str = fmt.Sprintf("%v%v", "0", str)
	}
	return str
}

type ListCharterOrderBriefParam struct {
	IsPrecision         bool   `json:"IsPrecision"`
	CustomerCompany     string `json:"CustomerCompany"`
	AssignCorporationId int64  `json:"AssignCorporationId"`
	StartAt             model.LocalTime
	EndAt               model.LocalTime
	model.Paginator
}

func (*OperationReport) ListCharterOrderBrief(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListCharterOrderBriefParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal error", err)
		return response.Error(rsp, response.ParamsInvalid)
	}
	items, totalCount, errCode := listCharterOrderBrief(ctx, req, rsp, param)
	if errCode != response.SUCCESS {
		return response.Error(rsp, errCode)
	}
	return response.Success(rsp, map[string]interface{}{
		"Items":      items,
		"TotalCount": totalCount,
	})
}

func listCharterOrderBrief(ctx context.Context, req *api.Request, rsp *api.Response, param ListCharterOrderBriefParam) ([]operation.CharterOrderBrief, int64, string) {
	var authCorpIds []int64
	if param.AssignCorporationId > 0 {
		authCorpIds = service.AuthCorporationIdProvider(ctx, []int64{param.AssignCorporationId})
	} else {
		authCorpIds = service.AuthCorporationIdProvider(ctx, nil)
	}

	if len(authCorpIds) == 0 {
		return nil, 0, response.SUCCESS
	}

	var p = operation.CharterOrderBriefParam{
		CustomerCompany:      param.CustomerCompany,
		AssignCorporationIds: authCorpIds,
		StartAt:              param.StartAt,
		EndAt:                param.EndAt,
		Paginator:            param.Paginator,
		IsPrecision:          param.IsPrecision,
	}

	items, totalCount, err := (&operation.CharterOrderBrief{}).GetAll(p)
	if err != nil {
		log.Error("json.Unmarshal error", err)
		return nil, 0, response.DbQueryFail
	}

	var corporationNameMap = make(map[int64]string, 0)

	for k, v := range items {
		for kk, vv := range v.DispatchOrderItems {
			if value, ok := corporationNameMap[vv.ExecCorporationId]; ok {
				items[k].DispatchOrderItems[kk].ExecCorporation = value
			} else {
				var corpName = rpc.GetCorporationNameById(context.Background(), vv.ExecCorporationId)
				corporationNameMap[vv.ExecCorporationId] = corpName
				items[k].DispatchOrderItems[kk].ExecCorporation = corpName
			}

			vehicleItem := rpc.GetVehicleWithId(context.Background(), vv.ExecVehicleId)
			if vehicleItem != nil {
				items[k].DispatchOrderItems[kk].ExecVehicleLicense = vehicleItem.License
			}
			v.DispatchOrderItems[kk].AssociationCharterOrderIndex = getCharterOrderSort(v.CharterOrderItems, vv.AssociationCharterOrderId)
		}

		for kk, vv := range v.CharterOrderItems {
			if value, ok := corporationNameMap[vv.AssignCorporationId]; ok {
				items[k].CharterOrderItems[kk].AssignCorporation = value
			} else {
				var corpName = rpc.GetCorporationNameById(context.Background(), vv.AssignCorporationId)
				items[k].CharterOrderItems[kk].AssignCorporation = corpName
				corporationNameMap[vv.AssignCorporationId] = corpName
			}
		}
	}

	for k, v := range items {
		// 指派机构唯一
		var (
			corpNameMap = make(map[string]bool, 0)
			corpNames   []string
			amount      int64
			vehicleNum  int64
		)
		for _, value := range v.CharterOrderItems {
			corpNameMap[value.AssignCorporation] = true
			amount += value.Amount
			vehicleNum += value.VehicleNum
		}
		for key, _ := range corpNameMap {
			corpNames = append(corpNames, key)
		}
		sort.Strings(corpNames)

		items[k].TotalAmount = amount
		items[k].TotalVehicleNum = vehicleNum
		items[k].TotalCorporations = corpNames
	}

	return items, totalCount, response.SUCCESS
}

func getCharterOrderSort(items []operation.CharterOrder, orderId int64) int64 {
	for _, item := range items {
		if item.Id == orderId {
			return item.Sort
		}
	}
	return 0
}

func (*OperationReport) CharterOrderBriefDetail(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param EditCharterOrderBriefParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal error", err)
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	var authCorpIds = service.AuthCorporationIdProvider(ctx, nil)
	item, err := (&operation.CharterOrderBrief{}).GetBy(param.Id, authCorpIds)
	if err != nil {
		log.Error("CharterOrderBrief.GetBy error", err)
		return response.Error(rsp, response.DbQueryFail)
	}
	var corporationNameMap = make(map[int64]string, 0)
	for kk, vv := range item.DispatchOrderItems {
		if value, ok := corporationNameMap[vv.ExecCorporationId]; ok {
			item.DispatchOrderItems[kk].ExecCorporation = value
		} else {
			var corpName = rpc.GetCorporationNameById(context.Background(), vv.ExecCorporationId)
			corporationNameMap[vv.ExecCorporationId] = corpName
			item.DispatchOrderItems[kk].ExecCorporation = corpName
		}

		vehicleItem := rpc.GetVehicleWithId(context.Background(), vv.ExecVehicleId)
		if vehicleItem != nil {
			item.DispatchOrderItems[kk].ExecVehicleLicense = vehicleItem.License
		}
		item.DispatchOrderItems[kk].AssociationCharterOrderIndex = getCharterOrderSort(item.CharterOrderItems, vv.AssociationCharterOrderId)
	}

	for kk, vv := range item.CharterOrderItems {
		if value, ok := corporationNameMap[vv.AssignCorporationId]; ok {
			item.CharterOrderItems[kk].AssignCorporation = value
		} else {
			var corpName = rpc.GetCorporationNameById(context.Background(), vv.AssignCorporationId)
			item.CharterOrderItems[kk].AssignCorporation = corpName
			corporationNameMap[vv.AssignCorporationId] = corpName
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"Item": item,
	})

}

type EditCharterOrderBriefParam struct {
	Id                 int64                           `json:"Id"`
	BriefItem          *operation.CharterOrderBrief    `json:"BriefItem"`
	DelCharterOrderId  int64                           `json:"DelCharterOrderId"`
	DelDispatchOrderId int64                           `json:"DelDispatchOrderId"`
	CharterOrderItem   *operation.CharterOrder         `json:"CharterOrderItem"`
	DispatchOrderItem  *operation.CharterDispatchOrder `json:"DispatchOrderItem"`
}

func (*OperationReport) EditCharterOrderBrief(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param EditCharterOrderBriefParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal error", err)
		return response.Error(rsp, response.ParamsInvalid)
	}

	var (
		user      = auth.User(ctx)
		topCorpId = user.GetTopCorporationId()
	)

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	var briefItem = param.BriefItem
	log.Error("1111111111111111111 error", map[string]interface{}{
		"Item": briefItem,
	})

	if briefItem != nil {
		if briefItem.CustomerCompany == "" || briefItem.CustomerName == "" || briefItem.CustomerPhone == "" {
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	// 更新属性
	if briefItem != nil {
		param.BriefItem.Id = param.Id
		param.BriefItem.TopCorporationId = topCorpId
		param.BriefItem.ParseOpUser(ctx)
		param.BriefItem.ParseEditOpUser(ctx)
		err = param.BriefItem.Updates(tx)
		if err != nil {
			log.Error("CharterOrder.Updates error", err)
			return response.Error(rsp, response.DbSaveFail)
		}
	} else if param.DelDispatchOrderId > 0 {
		err = (&operation.CharterDispatchOrder{}).BatchDeleteWithIds(tx, []int64{param.DelDispatchOrderId})
		if err != nil {
			log.Error("CharterDispatchOrder.BatchDeleteWithIds error", err)
			return response.Error(rsp, response.DbSaveFail)
		}
	} else if param.DelCharterOrderId > 0 {
		err = (&operation.CharterOrder{}).BatchDeleteWithIds(tx, []int64{param.DelCharterOrderId})
		if err != nil {
			log.Error("CharterOrder.BatchDeleteWithIds error", err)
			return response.Error(rsp, response.DbSaveFail)
		}
	} else if param.CharterOrderItem != nil {
		var order = param.CharterOrderItem
		if order.CustomerName == "" || order.CustomerPhone == "" || order.DepartAddress == "" || order.ArriveAddress == "" || order.AssignCorporationId == 0 ||
			order.VehicleNum <= 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}

		if order.Id == 0 {
			order.CharterOrderBriefId = param.Id
			order.TopCorporationId = topCorpId
			order.ParseOpUser(ctx)
			order.Sort = (&operation.CharterOrder{}).GetMaxSort(tx, param.Id) + 1
			err = order.Create(tx)
			if err != nil {
				log.Error("CharterOrder.Create error", err)
				return response.Error(rsp, response.DbSaveFail)
			}
		} else {
			// 查询归属是否相同
			item, err := (&operation.CharterOrder{}).GetBy(order.Id)
			if err != nil {
				log.Error("CharterOrder.Create error", err)
				return response.Error(rsp, response.DbQueryFail)
			}
			if item.CharterOrderBriefId != param.Id {
				return response.Error(rsp, response.ParamsInvalid)
			}
			// 归属机构不同，判断是否存在派遣单
			if item.AssignCorporationId != order.AssignCorporationId {
				totalCount, err := (&operation.CharterDispatchOrder{}).GetCountByCOId(item.CharterOrderBriefId)
				if err != nil {
					log.Error("CharterOrder.GetCountByCOId error", err)
					return response.Error(rsp, response.DbQueryFail)
				}
				if totalCount > 0 {
					return response.Error(rsp, "OP9909")
				}
			}

			err = order.Updates(tx)
			if err != nil {
				log.Error("CharterOrder.Update error", err)
				return response.Error(rsp, response.DbSaveFail)
			}
		}

	} else if param.DispatchOrderItem != nil {
		var dOrder = param.DispatchOrderItem
		if dOrder.ExecCorporationId == 0 || dOrder.ExecVehicleId == 0 || dOrder.ExecDrivers == nil || dOrder.TravelDates == "" {
			return response.Error(rsp, response.ParamsInvalid)
		}

		// 指派任务校准
		orderItem, err := (&operation.CharterOrder{}).GetBy(dOrder.AssociationCharterOrderId)
		if err != nil {
			log.Error("CharterOrder.GetBy error", err)
			return response.Error(rsp, "OP9910")
		}
		if orderItem.CharterOrderBriefId != param.Id || dOrder.ExecCorporationId != orderItem.AssignCorporationId {
			return response.Error(rsp, response.ParamsInvalid)
		}

		dOrder.CharterOrderBriefId = param.Id

		if dOrder.Id == 0 {
			dOrder.ParseOpUser(ctx)
			err = dOrder.Create(tx)
			if err != nil {
				log.Error("CharterDispatchOrder.Create error", err)
				return response.Error(rsp, response.DbSaveFail)
			}
		} else {
			// 派遣单自身校验
			item, err := (&operation.CharterDispatchOrder{}).GetById(dOrder.Id)
			if item.CharterOrderBriefId != param.Id {
				return response.Error(rsp, response.ParamsInvalid)
			}

			err = dOrder.Updates(tx)
			if err != nil {
				log.Error("CharterDispatchOrder.Updates error", err)
				return response.Error(rsp, response.DbSaveFail)
			}
		}
	}

	return response.Success(rsp, response.SUCCESS)
}

type DelCharterOrderBrief struct {
	Ids []int64 `json:"Ids"`
}

func (*OperationReport) DelCharterOrderBrief(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DelCharterOrderBrief
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal error", err)
		return response.Error(rsp, response.ParamsInvalid)
	}
	if len(param.Ids) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	for _, id := range param.Ids {
		if id == 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}
	}
	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = (&operation.CharterOrderBrief{}).BatchDelete(tx, param.Ids)
	if err != nil {
		log.Error("CharterOrderBrief.BatchDelete error", err)
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = (&operation.CharterOrder{}).BatchDeleteWithCOBriefId(tx, param.Ids)
	if err != nil {
		log.Error("CharterOrder.BatchDelete error", err)
		return response.Error(rsp, response.ParamsInvalid)
	}

	err = (&operation.CharterDispatchOrder{}).BatchDeleteWithCOBriefId(tx, param.Ids)
	if err != nil {
		log.Error("DispatchOrder.BatchDelete error", err)
		return response.Error(rsp, response.ParamsInvalid)
	}

	return response.Success(rsp, nil)
}
func (*OperationReport) ListCharterOrderBriefExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListCharterOrderBriefParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal error", err)
		return response.Error(rsp, response.ParamsInvalid)
	}
	var fileName = fmt.Sprintf("派车单管理_%v_%v.xlsx", param.StartAt.FormatIntDate(), param.EndAt.FormatIntDate())

	var user = auth.User(ctx)

	exportFileRecord, err := exportService.CreateExportFileRecord(user.GetUserId(), fileName, "ListCharterOrderBriefExport", []byte(req.Body), param.StartAt.ToTime(), param.EndAt.ToTime())
	if err != nil {
		return response.Error(rsp, response.DbSaveFail)
	}

	items, _, errCode := listCharterOrderBrief(ctx, req, rsp, param)
	if errCode != response.SUCCESS {
		return response.Error(rsp, errCode)
	}

	go exportCharterOrderBrief(exportFileRecord, items)

	return response.Success(rsp, response.SUCCESS)
}

func exportCharterOrderBrief(exportFileRecord export.ExportFile, items []operation.CharterOrderBrief) error {

	var (
		fileName    = exportFileRecord.Key
		basePath    = exportService.GetExcelFolder(exportFileRecord)
		style       = xlsxUtil.GetExcelPubStyle()
		stringStyle = xlsx.MakeStringStyle(&style.Font, &style.Fill, &style.Alignment, &style.Border)
	)

	var xlsxCellMetadataAndHeaders = []xlsxUtil.XlsxCellMetadataAndHeader{
		{HeaderName: "序号", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "派车单号", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "用车单位", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "客户联系人", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "客户联系人电话", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "总用车日期", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "指派机构", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "总车辆数", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "总金额", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "备注", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "更新时间", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "更新人", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "创建时间", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "创建人", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
	}

	streamFile, err := xlsxUtil.GetStreamFile(fileName, basePath, xlsxCellMetadataAndHeaders)
	if err != nil {
		log.ErrorFields("GetStreamFile error [%v]", map[string]interface{}{"err": err})
		return err
	}

	itemsLen := len(items)
	if 0 >= itemsLen {
		return nil
	}

	for idx := 0; idx < itemsLen; idx++ {
		var cells []string
		var index = idx + 1
		var item = items[idx]
		cells = append(cells, fmt.Sprintf("%v", index))
		cells = append(cells, item.Code)                                                                      // 派单工号
		cells = append(cells, item.CustomerCompany)                                                           // 用车单位
		cells = append(cells, item.CustomerName)                                                              // 客户联系人
		cells = append(cells, item.CustomerPhone)                                                             // 客户联系人电话
		cells = append(cells, fmt.Sprintf("%v-%v", item.StartAt.FormatIntDate(), item.EndAt.FormatIntDate())) // 总用车日期
		var corpNameStr string
		for kk, vv := range item.TotalCorporations {
			if kk == 0 {
				corpNameStr = vv
			} else {
				corpNameStr += "," + vv
			}
		}

		cells = append(cells, corpNameStr)                                                // 指派机构
		cells = append(cells, fmt.Sprintf("%v", item.TotalVehicleNum))                    // 总车辆数
		cells = append(cells, fmt.Sprintf("%v", util.IDecimal(item.TotalAmount, 100, 2))) // 总金额
		cells = append(cells, item.Remark)                                                // 备注
		cells = append(cells, item.UpdatedAt.String())                                    // 更新时间
		cells = append(cells, item.EditUserName)                                          // 更新人
		cells = append(cells, item.CreatedAt.String())                                    // 创建时间
		cells = append(cells, item.OpUserName)                                            // 创建人
		err = streamFile.WriteWithColumnDefaultMetadata(cells)
		if err != nil {
			log.ErrorFields("streamFile.WriteWithColumnDefaultMetadata error[%v]", map[string]interface{}{"err": err})
			return err
		}
	}

	// 写文件
	if err = streamFile.Close(); err != nil {
		return errors.New(fmt.Sprintf("写入文件异常,错误信息[%v]", err))
	}

	exportFileRecord.UpdateSuccess()
	return nil

}

type ListCharterOrderExportParam struct {
	BriefId int64 `json:"BriefId"`
}

func (*OperationReport) ListCharterOrderExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListCharterOrderExportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.Error("json.Unmarshal error", err)
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.BriefId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	charterOrderBriefItem, err := (&operation.CharterOrderBrief{}).GetById(param.BriefId)
	if err != nil {
		log.Error("operation.CharterOrderBrief.GetById error", err)
		return response.Error(rsp, response.ParamsInvalid)
	}
	charterDispatchOrderItems, err := (&operation.CharterDispatchOrder{}).GetAllByCOBriefId(param.BriefId)
	if err != nil {
		log.Error("operation.CharterDispatchOrder.GetAllByCOBriefId error", err)
		return response.Error(rsp, response.ParamsInvalid)
	}
	charterOrderItems, err := (&operation.CharterOrder{}).GetAllByCOBriefId(param.BriefId)
	if err != nil {
		log.Error("operation.CharterOrder.GetAllByCOBriefId error", err)
		return response.Error(rsp, response.ParamsInvalid)
	}

	var fileName = fmt.Sprintf("派车单详情导出.xlsx")
	var user = auth.User(ctx)

	exportFileRecord, err := exportService.CreateExportFileRecord(user.GetUserId(), fileName, "ListCharterOrderExport", []byte(req.Body), time.Now(), time.Now())
	if err != nil {
		return response.Error(rsp, response.DbSaveFail)
	}
	go exportCharterOrder(exportFileRecord, charterOrderBriefItem, charterDispatchOrderItems, charterOrderItems)

	return response.Success(rsp, response.SUCCESS)
}

func exportCharterOrder(exportFileRecord export.ExportFile, charterOrderBriefItem operation.CharterOrderBrief, charterDispatchOrderItems []operation.CharterDispatchOrder, charterOrderItems []operation.CharterOrder) error {

	var (
		fileName    = exportFileRecord.Key
		basePath    = exportService.GetExcelFolder(exportFileRecord)
		style       = xlsxUtil.GetExcelPubStyle()
		stringStyle = xlsx.MakeStringStyle(&style.Font, &style.Fill, &style.Alignment, &style.Border)
	)

	var xlsxCellMetadataAndHeaders = []xlsxUtil.XlsxCellMetadataAndHeader{
		{HeaderName: "用车单位", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "任务号", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "客户", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "联系电话", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "起点", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "讫点", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "用车日期", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "用车时段", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "总车辆数", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "指派机构", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "执行机构", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "执行车辆", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "执行司机", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "司机联系电话", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "行驶日期", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "趟次（趟）", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "运营公里（千米）", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "租赁金额（￥)", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
	}

	streamFile, err := xlsxUtil.GetStreamFile(fileName, basePath, xlsxCellMetadataAndHeaders)
	if err != nil {
		log.ErrorFields("GetStreamFile error [%v]", map[string]interface{}{"err": err})
		return err
	}

	var (
		totalTrip             int64
		totalOperationMileage int64
		totalAmount           int64
	)
	for _, charterOrderItem := range charterOrderItems {
		for _, charterDispatchOrderItem := range charterDispatchOrderItems {
			if charterOrderItem.Id == charterDispatchOrderItem.AssociationCharterOrderId {
				totalTrip += charterDispatchOrderItem.TripNum
				totalOperationMileage += charterDispatchOrderItem.OperationMileage
				totalAmount += charterDispatchOrderItem.Amount
				var cells []string
				cells = append(cells, charterOrderBriefItem.CustomerCompany)                                                                                              // 用车单位
				cells = append(cells, fmt.Sprintf("%v", charterOrderItem.Sort))                                                                                           // 任务号
				cells = append(cells, charterOrderItem.CustomerName)                                                                                                      // 客户
				cells = append(cells, charterOrderItem.CustomerPhone)                                                                                                     // 联系电话
				cells = append(cells, charterOrderItem.DepartAddress)                                                                                                     // 起点
				cells = append(cells, charterOrderItem.ArriveAddress)                                                                                                     // 讫点
				cells = append(cells, fmt.Sprintf("%v-%v", charterOrderItem.StartAt.ToTime().Format("2006/01/02"), charterOrderItem.EndAt.ToTime().Format("2006/01/02"))) // 用车日期
				cells = append(cells, fmt.Sprintf("%v-%v", util.GetSecondStr(charterOrderItem.StartTime), util.GetSecondStr(charterOrderItem.EndTime)))                   // 用车时段
				cells = append(cells, fmt.Sprintf("%v", charterOrderItem.VehicleNum))                                                                                     // 总车辆数
				cells = append(cells, rpc.GetCorporationNameById(context.Background(), charterOrderItem.AssignCorporationId))                                             // 指派机构
				cells = append(cells, rpc.GetCorporationNameById(context.Background(), charterDispatchOrderItem.ExecCorporationId))                                       // 执行机构
				oetVehicleItem := rpc.GetVehicleWithId(context.Background(), charterDispatchOrderItem.ExecVehicleId)
				if oetVehicleItem != nil {
					cells = append(cells, oetVehicleItem.License) // 执行车辆
				} else {
					cells = append(cells, "") // 执行车辆
				}
				var driverItems []operation.ExecDriver
				err = json.Unmarshal(charterDispatchOrderItem.ExecDrivers, &driverItems)
				if err != nil {
					log.ErrorFields("charterDispatchOrderItem.ExecDrivers error [%v]", map[string]interface{}{"err": err})
				}
				var (
					driverStr      string
					driverPhoneStr string
				)
				for key, driver := range driverItems {
					if key == 0 {
						driverStr = driver.DriverName
						driverPhoneStr = driver.DriverPhone
					} else {
						driverStr += "," + driver.DriverName
						driverPhoneStr += "," + driver.DriverPhone
					}
				}

				cells = append(cells, driverStr)                                                                            // 执行司机
				cells = append(cells, driverPhoneStr)                                                                       // 司机联系电话
				cells = append(cells, charterDispatchOrderItem.TravelDates)                                                 // 行驶日期
				cells = append(cells, fmt.Sprintf("%v", charterDispatchOrderItem.TripNum))                                  // 趟次（趟）
				cells = append(cells, fmt.Sprintf("%v", util.IDecimal(charterDispatchOrderItem.OperationMileage, 1000, 3))) // 运营公里（千米）
				cells = append(cells, fmt.Sprintf("%v", util.IDecimal(charterDispatchOrderItem.Amount, 100, 2)))            // 租赁金额（￥)
				err = streamFile.WriteWithColumnDefaultMetadata(cells)
				if err != nil {
					log.ErrorFields("streamFile.WriteWithColumnDefaultMetadata error[%v]", map[string]interface{}{"err": err})
					return err
				}
			}
		}

	}

	// 合计:
	var cells []string
	cells = append(cells, "")                                                               // 用车单位
	cells = append(cells, "合计")                                                             // 任务号
	cells = append(cells, "")                                                               // 客户
	cells = append(cells, "")                                                               // 联系电话
	cells = append(cells, "")                                                               // 起点
	cells = append(cells, "")                                                               // 讫点
	cells = append(cells, "")                                                               // 用车日期
	cells = append(cells, "")                                                               // 用车时段
	cells = append(cells, "")                                                               // 总车辆数
	cells = append(cells, "")                                                               // 指派机构
	cells = append(cells, "")                                                               // 执行机构
	cells = append(cells, "")                                                               // 执行车辆
	cells = append(cells, "")                                                               // 执行司机
	cells = append(cells, "")                                                               // 司机联系电话
	cells = append(cells, "")                                                               // 行驶日期
	cells = append(cells, fmt.Sprintf("%v", totalTrip))                                     // 趟次（趟）
	cells = append(cells, fmt.Sprintf("%v", util.IDecimal(totalOperationMileage, 1000, 3))) // 运营公里（千米）
	cells = append(cells, fmt.Sprintf("%v", util.IDecimal(totalAmount, 100, 2)))            // 租赁金额（￥)
	err = streamFile.WriteWithColumnDefaultMetadata(cells)
	if err != nil {
		log.ErrorFields("streamFile.WriteWithColumnDefaultMetadata error[%v]", map[string]interface{}{"err": err})
		return err
	}

	// 写文件
	if err = streamFile.Close(); err != nil {
		return errors.New(fmt.Sprintf("写入文件异常,错误信息[%v]", err))
	}
	exportFileRecord.UpdateSuccess()

	return nil

}
