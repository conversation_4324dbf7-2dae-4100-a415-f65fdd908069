package lineDriverReport

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	exportModel "app/org/scs/erpv2/api/model/export"
	holidayModel "app/org/scs/erpv2/api/model/holiday"
	lineDriverModel "app/org/scs/erpv2/api/model/lineDriver"
	operationModel "app/org/scs/erpv2/api/model/operation"
	protoSchedule "app/org/scs/erpv2/api/proto/rpc/iss"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	exportService "app/org/scs/erpv2/api/service/export"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
)

type OperationReport struct {
}

type LineReportParam struct {
	StartAt        model.LocalTime `json:"StartAt"`
	EndAt          model.LocalTime `json:"EndAt"`
	LineId         int64           `json:"LineId"`
	LineIds        []int64         `json:"LineIds"`
	CorporationId  int64           `json:"CorporationId"`
	CorporationIds []int64         `json:"CorporationIds"`
	DriverIds      []int64         `json:"DriverIds"`
	IsCheck        int64           `json:"IsCheck"`
	Id             int64           `json:"Id"`
	ReportAt       model.LocalTime `json:"ReportAt"`
	IsExportFile   int64           `json:"IsExportFile"`
	ExportGroup    string          `json:"ExportGroup"` //后端导出时是否需要分组  line=>线路分组
	model.Paginator
	model.Sortable
}

func (*OperationReport) CalcLineMileage(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)
	if param.LineId == 0 || param.CorporationId == 0 || startAt.IsZero() || endAt.IsZero() {
		return response.Error(rsp, response.ParamsMissing)
	}

	if startAt.Unix() != endAt.Unix() {
		return response.Error(rsp, response.ParamsInvalid)
	}

	isExistApproval := (&operationModel.OperationApproval{}).IsExistCrossApproval(param.CorporationId, []string{"line_vehicle_operation_report"}, []string{strconv.Itoa(int(param.LineId))}, startAt, startAt)
	if isExistApproval {
		return response.Error(rsp, "BPM1003")
	}

	//查询是否已经计算过
	isCalc := (&operationModel.LineVehicleMileageReport{}).IsExistRecord(param.CorporationId, param.LineId, startAt)

	if param.IsCheck == util.StatusForTrue {
		return response.Success(rsp, map[string]interface{}{
			"IsExist": isCalc,
		})
	}

	//查询线路信息
	var lineName, CorporationName string
	line, _ := rpc.GetLineWithId(ctx, param.LineId)
	if line != nil {
		lineName = line.Name
	}
	corporation := rpc.GetCorporationById(ctx, param.CorporationId)
	if corporation != nil {
		CorporationName = corporation.Name
	}

	//查询线路是否设置过配置项
	setting := (&operationModel.OperationLineSetting{}).FirstByLineId(param.CorporationId, param.LineId, startAt)

	if setting.Id == 0 {
		return response.Error(rsp, "OP9002")
	}

	var settingItem operationModel.OperationLineSettingSettingItem
	err = json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("CalcLineMileage setting.SettingItem json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	endDate := time.Date(startAt.Year(), startAt.Month(), startAt.Day(), 23, 59, 59, 0, time.Local)
	issItems := rpc.LineRunFormDaySum(ctx, param.CorporationId, param.LineId, startAt.Unix(), endDate.Unix())

	//是否为节假日
	var holidayDay int64
	if (&holidayModel.HolidayDate{}).IsHoliday(time.Time(param.StartAt)) {
		holidayDay = 1 * 10
	}

	var reportDataItems []operationModel.LineVehicleMileageReport
	var driverIds = make(map[int64]bool)
	var driverExistReports []map[string]interface{}
	for i := range issItems {
		var reportData = calcReportData(ctx, issItems[i], settingItem, time.Time(param.StartAt))
		reportData.CorporationId = param.CorporationId
		reportData.CorporationName = CorporationName
		reportData.LineId = param.LineId
		reportData.LineName = lineName
		reportData.ReportAt = param.StartAt
		reportData.TopCorporationId = auth.User(ctx).GetTopCorporationId()
		reportData.ParseOpUser(ctx)

		driverLine := (&lineDriverModel.LineHasDriver{}).DriverHasLine(reportData.DriverId, startAt)
		reportData.DriverHasLineId = driverLine.LineId
		reportData.DriverHasLineName = driverLine.LineName
		reportData.DriverCorporationId = driverLine.GetCorporationId()

		reportData.HolidayDay = holidayDay
		//同一个司机一天有多条数据，只有第一条的出勤天数为1 其他为0
		if _, ok := driverIds[reportData.DriverId]; ok {
			reportData.WorkDayCount = 0
			reportData.HolidayDay = 0
		} else {
			if reportData.WorkDayCount > 0 {
				driverIds[reportData.DriverId] = true
			}
		}

		if reportData.WorkDayCount > 0 && reportData.DriverId > 0 {
			//司机当天在其他线路存在司机出勤天数，则此次为0
			driverReport := (&operationModel.LineVehicleMileageReport{}).GetDriverOtherLineReport(reportData.DriverId, reportData.LineId, time.Time(reportData.ReportAt))
			if driverReport.Id > 0 && driverReport.WorkDayCount > 0 {
				reportData.WorkDayCount = 0
				driverExistReports = append(driverExistReports, map[string]interface{}{
					"DriverName":   reportData.DriverName,
					"LineName":     driverReport.LineName,
					"WorkDayCount": driverReport.WorkDayCount,
				})
			}
		}

		if reportData.WorkDayCount == 0 {
			reportData.HolidayDay = 0
		}

		switch reportData.FrequencyType {
		case util.MileageReportForFullWork:
			reportData.FullWorkDay = reportData.WorkDayCount
		case util.MileageReportForHalfWork:
			reportData.HalfWorkDay = reportData.WorkDayCount
		case util.MileageReportForMotorWork:
			reportData.MotorWorkDay = reportData.WorkDayCount
		case util.MileageReportForBigMotorWork:
			reportData.MotorBigWorkDay = reportData.WorkDayCount
		}

		reportDataItems = append(reportDataItems, reportData)
	}

	return response.Success(rsp, map[string]interface{}{
		"IsExist":            isCalc,
		"Items":              reportDataItems,
		"DriverExistReports": driverExistReports, //司机当天在其他线路存在的出勤天数
		//是否存在区间设置
		"ExistRangeSetting": (&operationModel.OperationLineSetting{}).IsExistRangeTrip([]int64{param.CorporationId}, param.LineId, startAt, endAt),
	})
}

func (*OperationReport) MultiDayCalcLineMileage(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)
	if param.LineId == 0 || param.CorporationId == 0 || startAt.IsZero() || endAt.IsZero() {
		return response.Error(rsp, response.ParamsMissing)
	}

	if startAt.AddDate(0, 0, 31).Unix() < endAt.Unix() {
		return response.Error(rsp, "OP9006")
	}

	//查询线路信息
	var lineName, CorporationName string
	line, _ := rpc.GetLineWithId(ctx, param.LineId)
	if line != nil {
		lineName = line.Name
	}
	corporation := rpc.GetCorporationById(ctx, param.CorporationId)
	if corporation != nil {
		CorporationName = corporation.Name
	}

	startAt = time.Date(startAt.Year(), startAt.Month(), startAt.Day(), 0, 0, 0, 0, time.Local)
	endAt = time.Date(endAt.Year(), endAt.Month(), endAt.Day(), 0, 0, 0, 0, time.Local)

	var notExistSettingDays []string
	var settings = make(map[int64]operationModel.OperationLineSetting)
	for {
		if startAt.Unix() > endAt.Unix() {
			break
		}

		//查询线路是否设置过配置项
		setting := (&operationModel.OperationLineSetting{}).FirstByLineId(param.CorporationId, param.LineId, startAt)

		if setting.Id == 0 {
			notExistSettingDays = append(notExistSettingDays, startAt.Format(model.DateFormat))
			startAt = startAt.AddDate(0, 0, 1)
			continue
		}
		settings[startAt.Unix()] = setting

		startAt = startAt.AddDate(0, 0, 1)
	}

	if len(notExistSettingDays) > 0 {
		return response.Success(rsp, map[string]interface{}{
			"NotExistSettingDays": notExistSettingDays,
		})
	}

	tx := model.DB().Begin()
	defer func() {
		if err1 := recover(); err1 != nil {
			tx.Rollback()
			return
		}

		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	for startUnix := range settings {
		var settingItem operationModel.OperationLineSettingSettingItem
		err = json.Unmarshal(settings[startUnix].SettingItem, &settingItem)
		if err != nil {
			log.ErrorFields("CalcLineMileage setting.SettingItem json.Unmarshal error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
		startDate := time.Unix(startUnix, 0)
		endDate := time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 23, 59, 59, 0, time.Local)
		issItems := rpc.LineRunFormDaySum(ctx, param.CorporationId, param.LineId, startDate.Unix(), endDate.Unix())

		//是否为节假日
		var holidayDay int64
		if (&holidayModel.HolidayDate{}).IsHoliday(startDate) {
			holidayDay = 1 * 10
		}

		var reportDataItems []operationModel.LineVehicleMileageReport
		var driverIds = make(map[int64]bool)
		for i := range issItems {
			var reportData = calcReportData(ctx, issItems[i], settingItem, startDate)
			reportData.CorporationId = param.CorporationId
			reportData.CorporationName = CorporationName
			reportData.LineId = param.LineId
			reportData.LineName = lineName
			reportData.ReportAt = model.LocalTime(startDate)
			reportData.TopCorporationId = auth.User(ctx).GetTopCorporationId()
			reportData.ParseOpUser(ctx)

			driverLine := (&lineDriverModel.LineHasDriver{}).DriverHasLine(reportData.DriverId, startDate)
			reportData.DriverHasLineId = driverLine.LineId
			reportData.DriverHasLineName = driverLine.LineName
			reportData.DriverCorporationId = driverLine.GetCorporationId()

			reportData.HolidayDay = holidayDay
			//同一个司机一天有多条数据，只有第一条的出勤天数为1 其他为0
			if _, ok := driverIds[reportData.DriverId]; ok {
				reportData.WorkDayCount = 0
				reportData.HolidayDay = 0
			} else {
				if reportData.WorkDayCount > 0 {
					driverIds[reportData.DriverId] = true
				}
			}

			if reportData.WorkDayCount == 0 {
				reportData.HolidayDay = 0
			}

			switch reportData.FrequencyType {
			case util.MileageReportForFullWork:
				reportData.FullWorkDay = reportData.WorkDayCount
			case util.MileageReportForHalfWork:
				reportData.HalfWorkDay = reportData.WorkDayCount
			case util.MileageReportForMotorWork:
				reportData.MotorWorkDay = reportData.WorkDayCount
			case util.MileageReportForBigMotorWork:
				reportData.MotorBigWorkDay = reportData.WorkDayCount
			}

			reportData.Id = model.Id()
			reportDataItems = append(reportDataItems, reportData)
		}

		err = (&operationModel.LineVehicleMileageReport{}).Delete(tx, param.CorporationId, param.LineId, startDate)
		if err != nil {
			log.ErrorFields("LineVehicleMileageReport.Delete error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}

		if len(reportDataItems) > 0 {
			err = (&operationModel.LineVehicleMileageReport{}).TransactionBatchCreate(model.DB(), reportDataItems, startDate)
			if err != nil {
				log.ErrorFields("LineVehicleMileageReport.TransactionBatchCreate error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.FAIL)
			}
			go buildLineVehicleMileageReportLog(nil, nil, &reportDataItems[0], "batchCreate", reportDataItems[0].OpUserName)
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"NotExistSettingDays": notExistSettingDays,
	})
}

func calcReportData(ctx context.Context, issData *protoSchedule.LineRunFormDaySumItem, setting operationModel.OperationLineSettingSettingItem, reportAt time.Time) operationModel.LineVehicleMileageReport {
	log.ErrorFields("issItem====", map[string]interface{}{"item": issData})
	log.ErrorFields("settingItem====", map[string]interface{}{"item": setting})
	if setting.HasFrequencyIndex == util.StatusForTrue {
		//找到当前班次对应的设置项
		var index int64
		frequencyArr := strings.Split(issData.ClassIdxStr, ",")
		if len(frequencyArr) > 0 {
			index, _ = strconv.ParseInt(frequencyArr[0], 10, 64)
		}
		setting.NightBefore22WorkTimeLength = 0
		setting.NightAfter22WorkTimeLength = 0

		var hasIndex bool
		for i := range setting.FrequencyItems {
			if setting.FrequencyItems[i].Index == index {
				hasIndex = true
				setting.FullInOutDepotMileage = setting.FrequencyItems[i].FullInOutDepotMileage
				setting.FullAssistantMileage = setting.FrequencyItems[i].FullAssistantMileage
				setting.FullInOutDepotTime = setting.FrequencyItems[i].FullInOutDepotTime
				setting.FullAssistantTime = setting.FrequencyItems[i].FullAssistantTime
				setting.RangeInOutDepotMileage = setting.FrequencyItems[i].RangeInOutDepotMileage
				setting.RangeAssistantMileage = setting.FrequencyItems[i].RangeAssistantMileage
				setting.RangeInOutDepotTime = setting.FrequencyItems[i].RangeInOutDepotTime
				setting.RangeAssistantTime = setting.FrequencyItems[i].RangeAssistantTime
				if issData.ActualWholeLastClassCount+issData.ActualPartLastClassCount > 0 {
					setting.NightBefore22WorkTimeLength = setting.FrequencyItems[i].NightBefore22WorkTimeLength
					setting.NightAfter22WorkTimeLength = setting.FrequencyItems[i].NightAfter22WorkTimeLength
				}
				setting.AttendanceType = setting.FrequencyItems[i].AttendanceType
				break
			}
		}

		if setting.NightBefore22WorkTimeLength == 0 && setting.NightAfter22WorkTimeLength == 0 && (issData.ActualWholeLastClassCount+issData.ActualPartLastClassCount > 0) {
		outLoop:
			for i := range frequencyArr {
				classIdx, _ := strconv.ParseInt(frequencyArr[i], 10, 64)
				for j := range setting.FrequencyItems {
					if classIdx == setting.FrequencyItems[j].Index && (setting.FrequencyItems[j].NightBefore22WorkTimeLength != 0 || setting.FrequencyItems[j].NightAfter22WorkTimeLength != 0) {
						setting.NightBefore22WorkTimeLength = setting.FrequencyItems[j].NightBefore22WorkTimeLength
						setting.NightAfter22WorkTimeLength = setting.FrequencyItems[j].NightAfter22WorkTimeLength
						break outLoop
					}
				}
			}
		}

		if !hasIndex {
			setting.FullAndRangeInOutDepotField = operationModel.FullAndRangeInOutDepotField{}
		}

		log.ErrorFields("HasFrequencyIndex setting====", map[string]interface{}{"item": setting})
	} else {
		setting.NightBefore22WorkTimeLength = issData.NightBefore22HourTime
		setting.NightAfter22WorkTimeLength = issData.NightAfter22HourTime
	}

	if issData.LineType == util.LineTypeDoubleCircle {
		if issData.PlanWholeFirstServiceType == util.LineSheetForDown {
			setting.FullRatedMileage = setting.DownFullRatedMileage
		} else {
			setting.FullRatedMileage = setting.UpFullRatedMileage
		}

		if issData.PlanPartFirstServiceType == util.LineSheetForDown {
			setting.RangeRatedMileage = setting.DownRangeRatedMileage
		} else {
			setting.RangeRatedMileage = setting.UpRangeRatedMileage
		}
	}

	var reportData = operationModel.LineVehicleMileageReport{
		VehicleId:                   issData.VehicleId,
		DriverId:                    issData.DriverId,
		FrequencyIndex:              issData.ClassIdxStr,
		WorkDayCount:                10, //单位：天数*10
		FrequencyType:               issData.ClassIdSys,
		FullRatedMileage:            setting.FullRatedMileage,
		RangeRatedMileage:           setting.RangeRatedMileage,
		NightBefore22WorkTimeLength: util.GetHourRound2BySecond(setting.NightBefore22WorkTimeLength),
		NightAfter22WorkTimeLength:  util.GetHourRound2BySecond(setting.NightAfter22WorkTimeLength),
		DelayInParkingTimeLength:    issData.ActualEnterParkingAt - issData.PlanEnterParkingAt,
		WorkType:                    setting.FrequencyType,
	}

	if reportData.DelayInParkingTimeLength < 1800 {
		reportData.DelayInParkingTimeLength = 0
	}

	if setting.AttendanceType > 0 {
		reportData.FrequencyType = setting.AttendanceType
	}

	reportData.NightTotalWorkTimeLength = reportData.NightBefore22WorkTimeLength + reportData.NightAfter22WorkTimeLength

	reportData.FullInOutDepotMileage = int64(float64(issData.ActualWholeFirstClassCount+issData.ActualWholeLastClassCount) * 0.5 * float64(setting.FullInOutDepotMileage))
	reportData.FullAssistantMileage = issData.ActualWholeFirstClassCount * setting.FullAssistantMileage
	reportData.RangeInOutDepotMileage = int64(float64(issData.ActualPartFirstClassCount+issData.ActualPartLastClassCount) * 0.5 * float64(setting.RangeInOutDepotMileage))
	reportData.RangeAssistantMileage = issData.ActualPartFirstClassCount * setting.RangeAssistantMileage

	reportData.FullInOutDepotTime = int64(float64(issData.ActualWholeFirstClassCount+issData.ActualWholeLastClassCount) * 0.5 * float64(setting.FullInOutDepotTime))
	reportData.FullInOutDepotTime = util.GetHourRound2BySecond(reportData.FullInOutDepotTime)

	reportData.FullAssistantTime = issData.ActualWholeFirstClassCount * setting.FullAssistantTime
	reportData.FullAssistantTime = util.GetHourRound2BySecond(reportData.FullAssistantTime)

	reportData.RangeInOutDepotTime = int64(float64(issData.ActualPartFirstClassCount+issData.ActualPartLastClassCount) * 0.5 * float64(setting.RangeInOutDepotTime))
	reportData.RangeInOutDepotTime = util.GetHourRound2BySecond(reportData.RangeInOutDepotTime)

	reportData.RangeAssistantTime = issData.ActualPartFirstClassCount * setting.RangeAssistantTime
	reportData.RangeAssistantTime = util.GetHourRound2BySecond(reportData.RangeAssistantTime)

	//全班1，半班、机动0.5
	if reportData.FrequencyType == util.MileageReportForFullWork {
		reportData.VehicleWorkDayCount = 1 * 10
	} else {
		reportData.VehicleWorkDayCount = 0.5 * 10
	}

	//查询车辆信息
	vehicle := rpc.GetVehicleWithId(ctx, issData.VehicleId)
	if vehicle != nil {
		reportData.License = vehicle.License
		vehicleSize := strings.Split(vehicle.CarBodySize, "*")
		if len(vehicleSize) > 0 {
			reportData.VehicleLength, _ = strconv.ParseInt(strings.Trim(vehicleSize[0], " "), 10, 64)
		}
		//reportData.VehicleLength = setting.VehicleLength
		//reportData.VehicleModel = setting.VehicleModel
	}

	//查询司机信息
	driver := rpc.GetStaffWithId(ctx, issData.DriverId)
	if driver != nil {
		reportData.DriverName = driver.Name
	}

	if issData.LineType == util.LineTypeSingleCircle || issData.LineType == util.LineTypeDoubleCircle {
		reportData.FullPlanCircle = issData.PlanWholeTrip * 10
		reportData.FullDoneCircle = issData.ActualWholeTrip * 10
		reportData.RangePlanCircle = issData.PlanPartTrip * 10
		reportData.RangeDoneCircle = issData.ActualPartTrip * 10

		reportData.FixVehicleCircle = issData.StopRepairTrip * 10
		reportData.CasualLeaveCircle = issData.StopPersonalAffairTrip * 10
		reportData.AccidentDisputeCircle = issData.StopAccidentTrip * 10
		reportData.AnnualReviewCircle = issData.StopAnnualAuditTrip * 10
		reportData.OfficialWorkCircle = issData.StopOfficialAttendanceTrip * 10
		reportData.CharterBusCircle = issData.StopContractVehicleTrip * 10
		reportData.SickLeaveCircle = issData.StopSickLeaveTrip * 10
		reportData.AnnualLeaveCircle = issData.StopAnnualLeaveTrip * 10
		reportData.TrafficJamCircle = issData.StopTrafficJamTrip * 10
		reportData.RestCircle = issData.StopRecuperateTrip * 10
	} else {
		reportData.FullPlanCircle = int64((float64(issData.PlanWholeTrip) / 2) * 10)
		reportData.FullDoneCircle = int64((float64(issData.ActualWholeTrip) / 2) * 10)
		reportData.RangePlanCircle = int64((float64(issData.PlanPartTrip) / 2) * 10)
		reportData.RangeDoneCircle = int64((float64(issData.ActualPartTrip) / 2) * 10)

		reportData.FixVehicleCircle = int64((float64(issData.StopRepairTrip) / 2) * 10)
		reportData.CasualLeaveCircle = int64((float64(issData.StopPersonalAffairTrip) / 2) * 10)
		reportData.AccidentDisputeCircle = int64((float64(issData.StopAccidentTrip) / 2) * 10)
		reportData.AnnualReviewCircle = int64((float64(issData.StopAnnualAuditTrip) / 2) * 10)
		reportData.OfficialWorkCircle = int64((float64(issData.StopOfficialAttendanceTrip) / 2) * 10)
		reportData.CharterBusCircle = int64((float64(issData.StopContractVehicleTrip) / 2) * 10)
		reportData.SickLeaveCircle = int64((float64(issData.StopSickLeaveTrip) / 2) * 10)
		reportData.AnnualLeaveCircle = int64((float64(issData.StopAnnualLeaveTrip) / 2) * 10)
		reportData.TrafficJamCircle = int64((float64(issData.StopTrafficJamTrip) / 2) * 10)
		reportData.RestCircle = int64((float64(issData.StopRecuperateTrip) / 2) * 10)
	}

	//当实际圈次为0时，司机出勤天数为0
	if reportData.FullDoneCircle+reportData.RangeDoneCircle == 0 {
		reportData.WorkDayCount = 0
	}

	reportData.CircleMileage = int64(((float64(reportData.FullDoneCircle) / 10) * float64(reportData.FullRatedMileage)) + ((float64(reportData.RangeDoneCircle) / 10) * float64(reportData.RangeRatedMileage)))

	reportData.TotalMileage = reportData.CircleMileage + reportData.StopWorkRatedMileage + reportData.FullInOutDepotMileage + reportData.FullAssistantMileage +
		reportData.RangeInOutDepotMileage + reportData.RangeAssistantMileage + reportData.CharterBusMileage + reportData.AnnualReviewMileage + reportData.TrafficJamMileage + reportData.FixVehicleMileage

	if issData.PlanWholeFirstServiceType == util.LineSheetForUp || issData.PlanWholeFirstServiceType == util.LineSheetForCircle {
		reportData.FullRatedWorkTimeLength = util.GetHourRound2BySecond(setting.UpFullRatedWorkTime)
		reportData.FullRatedNotWorkTimeLength = util.GetHourRound2BySecond(setting.UpFullRatedNotWorkTime)
	} else {
		reportData.FullRatedWorkTimeLength = util.GetHourRound2BySecond(setting.DownFullRatedWorkTime)
		reportData.FullRatedNotWorkTimeLength = util.GetHourRound2BySecond(setting.DownFullRatedNotWorkTime)
	}

	reportData.FullCircleWorkTimeLength = int64((float64(reportData.FullDoneCircle) / 10) * float64(reportData.FullRatedWorkTimeLength))
	reportData.FullCircleWorkTimeLength = util.GetHourRound2BySecond(reportData.FullCircleWorkTimeLength)

	reportData.FullCircleNotWorkTimeLength = int64((float64(reportData.FullDoneCircle) / 10) * float64(reportData.FullRatedNotWorkTimeLength))
	reportData.FullCircleNotWorkTimeLength = util.GetHourRound2BySecond(reportData.FullCircleWorkTimeLength)

	if issData.PlanPartFirstServiceType == util.LineSheetForUp || issData.PlanPartFirstServiceType == util.LineSheetForCircle {
		reportData.RangeRatedWorkTimeLength = util.GetHourRound2BySecond(setting.UpRangeRatedWorkTime)
		reportData.RangeRatedNotWorkTimeLength = util.GetHourRound2BySecond(setting.UpRangeRatedNotWorkTime)
	} else {
		reportData.RangeRatedWorkTimeLength = util.GetHourRound2BySecond(setting.DownRangeRatedWorkTime)
		reportData.RangeRatedNotWorkTimeLength = util.GetHourRound2BySecond(setting.DownRangeRatedNotWorkTime)
	}

	reportData.RangeCircleWorkTimeLength = int64((float64(reportData.RangeDoneCircle) / 10) * float64(reportData.RangeRatedWorkTimeLength))
	reportData.RangeCircleWorkTimeLength = util.GetHourRound2BySecond(reportData.RangeCircleWorkTimeLength)

	reportData.RangeCircleNotWorkTimeLength = int64((float64(reportData.RangeDoneCircle) / 10) * float64(reportData.RangeRatedNotWorkTimeLength))
	reportData.RangeCircleNotWorkTimeLength = util.GetHourRound2BySecond(reportData.RangeCircleNotWorkTimeLength)
	//if reportData.AddWorkTimeLength > 0 {
	//	reportData.AddWorkTimes = 1
	//}

	//时长计算
	var totalServiceTypeTimeLength int64
	if issData.ActualWholeFirstServiceType > 0 {
		if issData.ActualWholeFirstServiceType == util.LineSheetForUp || issData.ActualWholeFirstServiceType == util.LineSheetForCircle {
			totalServiceTypeTimeLength = setting.UpFullRatedWorkTime + setting.UpFullRatedNotWorkTime
		} else {
			totalServiceTypeTimeLength = setting.DownFullRatedWorkTime + setting.DownFullRatedNotWorkTime
		}
	} else {
		if issData.ActualPartFirstServiceType > 0 && setting.HasRangeTrip == util.StatusForTrue {
			if issData.ActualPartFirstServiceType == util.LineSheetForUp || issData.ActualPartFirstServiceType == util.LineSheetForCircle {
				totalServiceTypeTimeLength = setting.UpRangeRatedWorkTime + setting.UpRangeRatedNotWorkTime
			} else {
				totalServiceTypeTimeLength = setting.DownRangeRatedWorkTime + setting.DownRangeRatedNotWorkTime
			}
		}
	}

	reportData.FixVehicleTimeLength = util.GetHourRound2BySecond(totalServiceTypeTimeLength * (reportData.FixVehicleCircle / 10))
	reportData.CasualLeaveTimeLength = util.GetHourRound2BySecond(totalServiceTypeTimeLength * (reportData.CasualLeaveCircle / 10))
	reportData.AccidentDisputeTimeLength = util.GetHourRound2BySecond(totalServiceTypeTimeLength * (reportData.AccidentDisputeCircle / 10))
	reportData.AnnualReviewTimeLength = util.GetHourRound2BySecond(totalServiceTypeTimeLength * (reportData.AnnualReviewCircle / 10))
	reportData.OfficialWorkTimeLength = util.GetHourRound2BySecond(totalServiceTypeTimeLength * (reportData.OfficialWorkCircle / 10))
	reportData.CharterBusTimeLength = util.GetHourRound2BySecond(totalServiceTypeTimeLength * (reportData.CharterBusCircle / 10))
	reportData.SickLeaveTimeLength = util.GetHourRound2BySecond(totalServiceTypeTimeLength * (reportData.SickLeaveCircle / 10))
	reportData.AnnualLeaveTimeLength = util.GetHourRound2BySecond(totalServiceTypeTimeLength * (reportData.AnnualLeaveCircle / 10))
	reportData.TrafficJamTimeLength = util.GetHourRound2BySecond(totalServiceTypeTimeLength * (reportData.TrafficJamCircle / 10))
	reportData.RestTimeLength = util.GetHourRound2BySecond(totalServiceTypeTimeLength * (reportData.RestCircle / 10))

	reportData.TotalWorkTimeLength = reportData.FullCircleWorkTimeLength + reportData.FullInOutDepotTime + reportData.FullAssistantTime + reportData.FullStopWorkTimeLength +
		reportData.RangeCircleWorkTimeLength + reportData.RangeInOutDepotTime + reportData.RangeAssistantTime + reportData.RangeStopWorkTimeLength + reportData.FixVehicleWorkTimeLength + reportData.AddWorkWorkTimeLength +
		reportData.TrafficJamTimeLength

	if reportData.DelayInParkingTimeLength > 1800 {
		reportData.TotalWorkTimeLength += reportData.DelayInParkingTimeLength
	}

	reportData.TotalNotWorkTimeLength = reportData.FullCircleNotWorkTimeLength + reportData.FullStopNotWorkTimeLength + reportData.FixVehicleTimeLength +
		reportData.AccidentDisputeTimeLength + reportData.RangeCircleNotWorkTimeLength + reportData.RangeStopNotWorkTimeLength + reportData.AddWorkNotWorkTimeLength

	reportData.TotalTimeLength = reportData.TotalWorkTimeLength + reportData.TotalNotWorkTimeLength

	if len(issData.Items) > 0 {
		var more []string
		var ReplenishMore []string
		for i := range issData.Items {
			if issData.Items[i].Remark != "" {
				more = append(more, issData.Items[i].Remark)
			}

			if issData.Items[i].ReplenishRemark != "" {
				ReplenishMore = append(ReplenishMore, issData.Items[i].ReplenishRemark)
			}
		}

		moreData := map[string]string{
			"ReportAt":      time.Unix(issData.Items[0].ActualDepartAt, 0).Format(model.DateFormat),
			"More":          strings.Join(more, ";"),
			"ReplenishMore": strings.Join(ReplenishMore, ";"),
		}

		reportData.More, _ = json.Marshal(moreData)
	}

	//事假天数、次数
	if reportData.CasualLeaveCircle > 0 {
		if reportData.CasualLeaveCircle == (reportData.FullPlanCircle + reportData.RangePlanCircle) {
			reportData.CasualLeaveDay = 1 * 10
		} else {
			reportData.CasualLeaveTimes = 1
		}
	}

	if driver != nil {
		//事假天数
		reportData.CasualLeaveDay = service.GetDriverLeaveDay(driver.Id, util.LeaveTypeForCasualLeave, reportAt)
		if reportData.CasualLeaveDay > 0 {
			reportData.CasualLeaveTimes = 0
		} else {
			if reportData.CasualLeaveCircle > 0 {
				reportData.CasualLeaveTimes = 1
			} else {
				reportData.CasualLeaveTimes = 0
			}
		}

		//病假天数
		reportData.SickLeaveTimeDay = service.GetDriverLeaveDay(driver.Id, util.LeaveTypeForSickLeave, reportAt)
		reportData.AnnualLeaveDay = service.GetDriverLeaveDay(driver.Id, util.LeaveTypeForAnnualLeave, reportAt)
		reportData.RestTimeDay = service.GetDriverLeaveDay(driver.Id, util.LeaveTypeForRestLeave, reportAt)
	}

	if issData.ActualFirstPersonalAffairTrip > 0 || issData.ActualLastPersonalAffairTrip > 0 {
		reportData.CasualLeaveFirstLastPlanTimes = 1
	}

	return reportData
}

func (*OperationReport) LineMileageReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return lineMileageReport(ctx, req, rsp)
}

func (*OperationReport) LineMileageReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return lineMileageReport(ctx, req, rsp)
}

func lineMileageReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)

	if startAt.Unix() > endAt.Unix() {
		return response.Error(rsp, response.ParamsInvalid)
	}

	userSetting := service.GetUserSetting(auth.User(ctx).GetUserId())
	isApproval := userSetting.OperationReportDataIsApproval

	notExistDays := GetDontHaveDataDays(isApproval, param.CorporationId, param.LineId, startAt, endAt)

	if param.IsCheck == util.StatusForTrue {
		return response.Success(rsp, map[string]interface{}{
			"NotExistDays": notExistDays,
		})
	}

	if len(notExistDays) > 0 {
		return response.Error(rsp, "OP9003")
	}

	var reports []operationModel.LineVehicleMileageReport
	if time.Time(param.StartAt).Unix() == time.Time(param.EndAt).Unix() {
		reports = (&operationModel.LineVehicleMileageReport{}).GetByDay(isApproval, param.CorporationId, param.LineId, time.Time(param.StartAt))
	} else {
		reports, _ = (&operationModel.LineVehicleMileageReport{}).GetByRangeDay(isApproval, "", "", "", []int64{param.CorporationId}, []int64{param.LineId}, nil, nil, time.Time(param.StartAt), time.Time(param.EndAt), model.Sortable{}, model.Paginator{})

		for i := range reports {
			reports[i].Mores = (&operationModel.LineVehicleMileageReport{}).GetAllMore(isApproval, []int64{param.CorporationId}, param.LineId, reports[i].VehicleId, reports[i].DriverId, time.Time(param.StartAt), time.Time(param.EndAt))
		}
	}

	var approvalHandler service.ApprovalHandler
	if isApproval == util.StatusForTrue {
		approvalHandler = service.GetApprovalHandler([]int64{param.CorporationId}, []int64{param.LineId}, []string{"line_vehicle_operation_report"}, startAt, endAt)
	}

	return response.Success(rsp, map[string]interface{}{
		"StartAt":           param.StartAt,
		"EndAt":             param.EndAt,
		"Items":             reports,
		"ExistRangeSetting": (&operationModel.OperationLineSetting{}).IsExistRangeTrip([]int64{param.CorporationId}, param.LineId, startAt, endAt),
		"ApprovalHandler":   approvalHandler,
	})
}

func (*OperationReport) LineMileageRangeReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineMileageRangeReportRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	result, errorCode := lineMileageRangeReport(param)
	if errorCode != response.SUCCESS {
		return response.Error(rsp, errorCode)
	}
	return response.Success(rsp, result)
}

func (*OperationReport) LineMileageRangeReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineMileageRangeReportRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	//异步导出到下载中心
	if param.IsExportFile == util.StatusForTrue {
		var startAt = time.Time(param.StartAt)
		var endAt = time.Time(param.EndAt)
		var fileName = fmt.Sprintf("运营统计表%s-%s.xlsx", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))
		paramByte, _ := json.Marshal(param)
		exportFileRecord, err := exportService.CreateExportFileRecord(auth.User(ctx).GetUserId(), fileName, (&operationModel.LineVehicleMileageReport{}).LineVehicleMileageSumReportTableName(), paramByte, startAt, endAt)
		if err != nil {
			return response.Error(rsp, response.FAIL)
		}
		go ExportLineMileageRangeReportFile(exportFileRecord, param)
		return response.Success(rsp, nil)
	}

	result, errorCode := lineMileageRangeReport(param)
	if errorCode != response.SUCCESS {
		return response.Error(rsp, errorCode)
	}
	return response.Success(rsp, result)
}

func ExportLineMileageRangeReportFile(exportFile exportModel.ExportFile, param LineMileageRangeReportRequest) {
	result, errorCode := lineMileageRangeReport(param)

	if errorCode != response.SUCCESS {
		exportFile.ErrorCode = errorCode
		err := exportFile.UpdateFail()
		log.ErrorFields("ExportLineMileageRangeReportFile exportFile.UpdateStatus error", map[string]interface{}{"err": err})
		return
	}

	err := exportService.ExportLineMileageRangeReportFile(result.Items, exportFile, param.Scene, param.Tab)
	if err != nil {
		exportFile.ErrorCode = err.Error()
		err = exportFile.UpdateFail()
	} else {
		err = exportFile.UpdateSuccess()
	}
	log.ErrorFields("ExportLineMileageRangeReportFile exportFile.UpdateStatus error", map[string]interface{}{"err": err})
}

type LineMileageRangeReportRequest struct {
	StartAt                     model.LocalTime
	EndAt                       model.LocalTime
	CorporationIds              []int64
	LineIds                     []int64
	VehicleIds                  []int64
	DriverIds                   []int64
	Tab                         string //driverAndVehicle:人车  line：线路  driver：司机   vehicle：车辆
	Scene                       string //detail:明细  sum：汇总
	SumType                     string //汇总方式 line:按司机线路汇总 driver：按司机汇总
	IsRelateIrregularLineReport int64  `json:"IsRelateIrregularLineReport"` //是否关联定制线路报表 1是 2否
	IsExportFile                int64  `json:"IsExportFile"`
	model.Paginator
	model.Sortable
}
type LineMileageRangeReportResponse struct {
	StartAt    model.LocalTime                           `json:"StartAt"`
	EndAt      model.LocalTime                           `json:"EndAt"`
	Items      []operationModel.LineVehicleMileageReport `json:"Items"`
	SumItem    operationModel.LineVehicleMileageReport   `json:"SumItem"`
	TotalCount int64                                     `json:"TotalCount"`
}

func lineMileageRangeReport(param LineMileageRangeReportRequest) (LineMileageRangeReportResponse, string) {
	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)

	if startAt.Unix() > endAt.Unix() {
		return LineMileageRangeReportResponse{}, response.ParamsInvalid
	}

	//notExistDays := GetDontHaveDataDays(param.CorporationId, param.LineId, startAt, endAt)
	//
	//if param.IsCheck == util.StatusForTrue {
	//	return response.Success(rsp, map[string]interface{}{
	//		"NotExistDays": notExistDays,
	//	})
	//}
	//
	//if len(notExistDays) > 0 {
	//	return response.Error(rsp, "OP9003")
	//}
	var driverHasLine = make(map[string]lineDriverModel.LineHasDriver)
	var driverHasMainLine = make(map[int64]operationModel.DriverLineWorkDayCountItem)

	reports, count := (&operationModel.LineVehicleMileageReport{}).GetByRangeDay(0, param.Tab, param.Scene, param.SumType, param.CorporationIds, param.LineIds, param.VehicleIds, param.DriverIds, startAt, endAt, param.Sortable, param.Paginator)
	for i := range reports {
		var driverHasLineAt time.Time
		if param.Scene == "detail" {
			reports[i].Mores = (&operationModel.LineVehicleMileageReport{}).GetAllMore(0, []int64{reports[i].CorporationId}, reports[i].LineId, reports[i].VehicleId, reports[i].DriverId, time.Time(reports[i].ReportAt), time.Time(reports[i].ReportAt))
			if reports[i].LineId == 0 {
				lineNames, corporationNames := (&operationModel.LineVehicleMileageReport{}).GetAllLineName(reports[i].VehicleId, reports[i].DriverId, time.Time(reports[i].ReportAt), time.Time(reports[i].ReportAt))
				reports[i].LineName = strings.Join(lineNames, ";")
				reports[i].CorporationName = strings.Join(corporationNames, ";")
			}
			//reports[i].MotorWorkDay = (&operationModel.LineVehicleMileageReport{}).GetDriverRunCountDataGroupByReportAt(param.CorporationIds, reports[i].LineId, reports[i].VehicleId, reports[i].DriverId, util.MileageReportForMotorWork, time.Time(reports[i].ReportAt), time.Time(reports[i].ReportAt))
			//reports[i].HalfWorkDay = (&operationModel.LineVehicleMileageReport{}).GetDriverRunCountDataGroupByReportAt(param.CorporationIds, reports[i].LineId, reports[i].VehicleId, reports[i].DriverId, util.MileageReportForHalfWork, time.Time(reports[i].ReportAt), time.Time(reports[i].ReportAt))
			//reports[i].FullWorkDay = (&operationModel.LineVehicleMileageReport{}).GetDriverRunCountDataGroupByReportAt(param.CorporationIds, reports[i].LineId, reports[i].VehicleId, reports[i].DriverId, util.MileageReportForFullWork, time.Time(reports[i].ReportAt), time.Time(reports[i].ReportAt))

			driverHasLineAt = time.Time(reports[i].ReportAt)
		} else {
			//reports[i].Mores = (&operationModel.LineVehicleMileageReport{}).GetAllMore(param.CorporationIds, reports[i].LineId, reports[i].VehicleId, reports[i].DriverId, startAt, endAt)
			//reports[i].MotorWorkDay = (&operationModel.LineVehicleMileageReport{}).GetDriverRunCountDataGroupByReportAt(param.CorporationIds, reports[i].LineId, reports[i].VehicleId, reports[i].DriverId, util.MileageReportForMotorWork, startAt, endAt)
			//reports[i].HalfWorkDay = (&operationModel.LineVehicleMileageReport{}).GetDriverRunCountDataGroupByReportAt(param.CorporationIds, reports[i].LineId, reports[i].VehicleId, reports[i].DriverId, util.MileageReportForHalfWork, startAt, endAt)
			//reports[i].FullWorkDay = (&operationModel.LineVehicleMileageReport{}).GetDriverRunCountDataGroupByReportAt(param.CorporationIds, reports[i].LineId, reports[i].VehicleId, reports[i].DriverId, util.MileageReportForFullWork, startAt, endAt)

			if reports[i].LineId == 0 {
				lineNames, corporationNames := (&operationModel.LineVehicleMileageReport{}).GetAllLineName(reports[i].VehicleId, reports[i].DriverId, startAt, endAt)
				reports[i].LineName = strings.Join(lineNames, ";")
				reports[i].CorporationName = strings.Join(corporationNames, ";")
			}
			driverHasLineAt = endAt
		}

		if reports[i].VehicleId > 0 {
			vehicle := rpc.GetVehicleWithId(context.Background(), reports[i].VehicleId)
			if vehicle != nil {
				vehicleSize := strings.Split(vehicle.CarBodySize, "*")
				if len(vehicleSize) > 0 {
					reports[i].VehicleLength, _ = strconv.ParseInt(strings.Trim(vehicleSize[0], " "), 10, 64)
				}
			}
		}

		if reports[i].DriverId > 0 && !driverHasLineAt.IsZero() {
			mapKey := fmt.Sprintf("%v_%v", reports[i].DriverId, driverHasLineAt.Unix())
			if _, ok := driverHasLine[mapKey]; ok {
				reports[i].DriverHasLineId = driverHasLine[mapKey].LineId
				reports[i].DriverHasLineName = driverHasLine[mapKey].LineName
			} else {
				driverLine := (&lineDriverModel.LineHasDriver{}).DriverHasLine(reports[i].DriverId, driverHasLineAt)
				reports[i].DriverHasLineId = driverLine.LineId
				reports[i].DriverHasLineName = driverLine.LineName
				driverHasLine[mapKey] = driverLine
			}
		}

		//查询司机的主运营线路
		if reports[i].DriverId > 0 {
			if _, ok := driverHasMainLine[reports[i].DriverId]; ok {
				reports[i].MainRunLineId, reports[i].MainRunLineName = driverHasMainLine[reports[i].DriverId].LineId, driverHasMainLine[reports[i].DriverId].LineName
			} else {
				mainRunLine := service.GetDriverMainRunLineInfo(reports[i].DriverId, startAt, endAt)
				reports[i].MainRunLineId, reports[i].MainRunLineName = mainRunLine.LineId, mainRunLine.LineName
				driverHasMainLine[reports[i].DriverId] = mainRunLine
			}
		}
	}

	sumItem := (&operationModel.LineVehicleMileageReport{}).GetSumRangeDay(param.Tab, param.CorporationIds, param.LineIds, param.VehicleIds, param.DriverIds, startAt, endAt)

	return LineMileageRangeReportResponse{
		StartAt:    param.StartAt,
		EndAt:      param.EndAt,
		Items:      reports,
		SumItem:    sumItem,
		TotalCount: count,
	}, response.SUCCESS
}

type SaveLineMileageParam struct {
	Items         []operationModel.LineVehicleMileageReport `json:"Items"`
	ReportAt      model.LocalTime
	CorporationId int64  `json:"CorporationId"`
	LineId        int64  `json:"LineId"`
	Scene         string `json:"Scene"`
}

func (*OperationReport) SaveLineMileage(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param SaveLineMileageParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if time.Time(param.ReportAt).IsZero() {
		return response.Error(rsp, response.ParamsMissing)
	}

	isExistApproval := (&operationModel.OperationApproval{}).IsExistCrossApproval(param.CorporationId, []string{"line_vehicle_operation_report"}, []string{strconv.Itoa(int(param.LineId))}, time.Time(param.ReportAt), time.Time(param.ReportAt))
	if isExistApproval {
		return response.Error(rsp, "BPM1003")
	}

	user := auth.User(ctx).GetUser()

	tx := model.DB().Begin()
	var reportItems = param.Items

	if len(reportItems) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	//查询之前的数据 保存成版本
	oldVersionData := (&operationModel.LineVehicleMileageReport{}).GetByDay(0, param.CorporationId, param.LineId, time.Time(param.ReportAt))

	var hasId bool

	for i := range reportItems {
		switch reportItems[i].FrequencyType {
		case util.MileageReportForFullWork:
			reportItems[i].FullWorkDay = reportItems[i].WorkDayCount
			reportItems[i].HalfWorkDay = 0
			reportItems[i].MotorWorkDay = 0
			reportItems[i].MotorBigWorkDay = 0
		case util.MileageReportForHalfWork:
			reportItems[i].HalfWorkDay = reportItems[i].WorkDayCount
			reportItems[i].FullWorkDay = 0
			reportItems[i].MotorWorkDay = 0
			reportItems[i].MotorBigWorkDay = 0
		case util.MileageReportForMotorWork:
			reportItems[i].MotorWorkDay = reportItems[i].WorkDayCount
			reportItems[i].FullWorkDay = 0
			reportItems[i].HalfWorkDay = 0
			reportItems[i].MotorBigWorkDay = 0
		case util.MileageReportForBigMotorWork:
			reportItems[i].MotorBigWorkDay = reportItems[i].WorkDayCount
			reportItems[i].MotorWorkDay = 0
			reportItems[i].FullWorkDay = 0
			reportItems[i].HalfWorkDay = 0
		}

		//reportItems[i].CircleMileage = int64(((float64(reportItems[i].FullDoneCircle) / 10) * float64(reportItems[i].FullRatedMileage)) + ((float64(reportItems[i].RangeDoneCircle) / 10) * float64(reportItems[i].RangeRatedMileage)))
		//reportItems[i].TotalMileage = reportItems[i].CircleMileage + reportItems[i].StopWorkRatedMileage + reportItems[i].FullInOutDepotMileage + reportItems[i].FullAssistantMileage +
		//	reportItems[i].RangeInOutDepotMileage + reportItems[i].RangeAssistantMileage + reportItems[i].CharterBusMileage + reportItems[i].AnnualReviewMileage + reportItems[i].FixVehicleMileage
		//
		//reportItems[i].FullCircleWorkTimeLength = int64((float64(reportItems[i].FullDoneCircle) / 10) * float64(reportItems[i].FullRatedWorkTimeLength))
		//reportItems[i].FullCircleNotWorkTimeLength = int64((float64(reportItems[i].FullDoneCircle) / 10) * float64(reportItems[i].FullRatedNotWorkTimeLength))
		//
		//reportItems[i].RangeCircleWorkTimeLength = int64((float64(reportItems[i].RangeDoneCircle) / 10) * float64(reportItems[i].RangeRatedWorkTimeLength))
		//reportItems[i].RangeCircleNotWorkTimeLength = int64((float64(reportItems[i].RangeDoneCircle) / 10) * float64(reportItems[i].RangeRatedNotWorkTimeLength))

		//if reportItems[i].AddWorkTimeLength > 0 {
		//	reportItems[i].AddWorkTimes = 1
		//} else {
		//	reportItems[i].AddWorkTimes = 0
		//}

		//reportItems[i].TotalWorkTimeLength = reportItems[i].FullCircleWorkTimeLength + reportItems[i].FullInOutDepotTime + reportItems[i].FullAssistantTime + reportItems[i].FullStopWorkTimeLength +
		//	reportItems[i].RangeCircleWorkTimeLength + reportItems[i].RangeInOutDepotTime + reportItems[i].RangeAssistantTime + reportItems[i].RangeStopWorkTimeLength + reportItems[i].FixVehicleWorkTimeLength + reportItems[i].AddWorkWorkTimeLength +
		//	reportItems[i].TrafficJamTimeLength
		//
		//if reportItems[i].DelayInParkingTimeLength > 1800 {
		//	reportItems[i].TotalWorkTimeLength += reportItems[i].DelayInParkingTimeLength
		//}
		//
		//reportItems[i].TotalNotWorkTimeLength = reportItems[i].FullCircleNotWorkTimeLength + reportItems[i].FullStopNotWorkTimeLength + reportItems[i].FixVehicleTimeLength +
		//	reportItems[i].AccidentDisputeTimeLength + reportItems[i].RangeCircleNotWorkTimeLength + reportItems[i].RangeStopNotWorkTimeLength + reportItems[i].AddWorkNotWorkTimeLength
		//
		//reportItems[i].TotalTimeLength = reportItems[i].TotalWorkTimeLength + reportItems[i].TotalNotWorkTimeLength

		if reportItems[i].Id > 0 {
			hasId = true
		} else {
			reportItems[i].Id = model.Id()
		}

		//司机所属线路
		driverLine := (&lineDriverModel.LineHasDriver{}).DriverHasLine(reportItems[i].DriverId, time.Time(param.ReportAt))
		reportItems[i].DriverHasLineId = driverLine.LineId
		reportItems[i].DriverHasLineName = driverLine.LineName
		reportItems[i].DriverCorporationId = driverLine.GetCorporationId()

	}

	//计算的数据 需将之前的全部删除  然后在创建
	if param.Scene == "calc" && !hasId {
		err = (&operationModel.LineVehicleMileageReport{}).Delete(tx, param.CorporationId, param.LineId, time.Time(param.ReportAt))
		if err != nil {
			tx.Rollback()
			log.ErrorFields("LineVehicleMileageReport.Delete error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}

		if len(reportItems) > 0 {
			err = (&operationModel.LineVehicleMileageReport{}).BatchCreate(tx, reportItems)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("LineVehicleMileageReport.BatchCreate error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.FAIL)
			}
		}
		tx.Commit()

		go buildLineVehicleMileageReportLog(nil, nil, &reportItems[0], param.Scene, user.Name)
		if len(oldVersionData) > 0 {
			go createLineMileageReportVersion(user, oldVersionData, "save")
		}
		return response.Success(rsp, nil)
	}

	var oldAndNewReportData []map[string]operationModel.LineVehicleMileageReport
	for i := range reportItems {
		reportData := (&operationModel.LineVehicleMileageReport{}).FirstById(reportItems[i].Id, time.Time(param.ReportAt))

		if reportData.Id == 0 {
			err = reportItems[i].TransactionCreate(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("LineVehicleMileageReport.TransactionCreate error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.FAIL)
			}
		}
		err = reportItems[i].TransactionUpdate(tx)

		if err != nil {
			tx.Rollback()
			log.ErrorFields("LineVehicleMileageReport.TransactionUpdate error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}

		oldAndNewReportData = append(oldAndNewReportData, map[string]operationModel.LineVehicleMileageReport{
			"OldData": reportData,
			"NewData": reportItems[i],
		})
	}

	//生成操作日志
	for i := range oldAndNewReportData {
		oldData := oldAndNewReportData[i]["OldData"]
		newData := oldAndNewReportData[i]["NewData"]
		go buildLineVehicleMileageReportLog(nil, &oldData, &newData, param.Scene, user.Name)
	}

	tx.Commit()

	if len(oldVersionData) > 0 {
		go createLineMileageReportVersion(user, oldVersionData, "save")
	}

	return response.Success(rsp, nil)
}

func buildLineVehicleMileageReportLog(tx *gorm.DB, oldData, newData *operationModel.LineVehicleMileageReport, scene, userName string) {
	var data operationModel.LineVehicleMileageReport
	if newData != nil {
		data = *newData
	} else {
		if oldData != nil {
			data = *oldData
		}
	}

	var reportLog = operationModel.LineVehicleMileageReportLog{
		TopCorporationId: data.TopCorporationId,
		CorporationId:    data.CorporationId,
		CorporationName:  data.CorporationName,
		LineId:           data.LineId,
		LineName:         data.LineName,
		ReportAt:         data.ReportAt,
		Scene:            scene,
		OperateName:      util.LogSceneValMap[scene],
		OpUserName:       userName,
	}
	if scene != "calc" {
		reportLog.VehicleId = data.VehicleId
		reportLog.License = data.License
		reportLog.DriverId = data.DriverId
		reportLog.DriverName = data.DriverName
		reportLog.FrequencyIndex = data.FrequencyIndex
		if oldData == nil && newData != nil {
			reportLog.AfterData, _ = json.Marshal(newData)
		}

		if newData == nil && oldData != nil {
			reportLog.BeforeData, _ = json.Marshal(oldData)
		}

		if newData != nil && oldData != nil {
			oldDiff, newDiff := service.FindDifferentField(*oldData, *newData, []string{"Id", "More", "Mores", "OpUserId", "OpUserName", "CreatedAt", "UpdatedAt"})
			if len(oldDiff) == 0 && len(newDiff) == 0 {
				return
			}

			reportLog.BeforeData, _ = json.Marshal(oldDiff)
			reportLog.AfterData, _ = json.Marshal(newDiff)
		}
	}

	if tx == nil {
		err := reportLog.Create()
		if err != nil {
			log.ErrorFields("LineVehicleMileageReportLog.Create error", map[string]interface{}{"err": err})
		}
	} else {
		err := reportLog.CreateByTx(tx)
		if err != nil {
			log.ErrorFields("LineVehicleMileageReportLog.CreateTx error", map[string]interface{}{"err": err})
		}
	}

}

func (*OperationReport) DeleteLineMileage(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Id == 0 || time.Time(param.ReportAt).IsZero() {
		return response.Error(rsp, response.ParamsMissing)
	}

	reportData := (&operationModel.LineVehicleMileageReport{}).FirstById(param.Id, time.Time(param.ReportAt))

	if reportData.Id == 0 {
		log.ErrorFields("LineVehicleMileageReport.FirstById NOT FOUND", map[string]interface{}{"Id": param.Id})
		return response.Error(rsp, response.FAIL)
	}

	err = reportData.DeleteById()
	if err != nil {
		log.ErrorFields("LineVehicleMileageReport.DeleteById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbDeleteFail)
	}

	user := auth.User(ctx).GetUser()

	go buildLineVehicleMileageReportLog(nil, &reportData, nil, "delete", user.Name)

	return response.Success(rsp, nil)
}

func (*OperationReport) LineMileageLog(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)
	logs, count := (&operationModel.LineVehicleMileageReportLog{}).GetBy(param.CorporationId, param.LineId, startAt, endAt, param.Paginator)

	return response.Success(rsp, map[string]interface{}{
		"TotalCount": count,
		"Items":      logs,
	})
}

func (*OperationReport) LineMileageVersion(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	versions := (&operationModel.LineVehicleMileageReportVersion{}).GetBy(param.CorporationId, param.LineId, time.Time(param.ReportAt))

	return response.Success(rsp, map[string]interface{}{
		"Items": versions,
	})
}

func (*OperationReport) LineMileageVersionData(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	version := (&operationModel.LineVehicleMileageReportVersion{}).FirstBy(param.Id)
	if version.Id == 0 {
		log.ErrorFields("LineVehicleMileageReportVersion.FirstBy is empty", nil)
		return response.Error(rsp, response.DbNotFoundRecord)
	}
	version.VersionData = version.Data
	return response.Success(rsp, version)
}

func (*OperationReport) LineMileageVersionRecover(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	version := (&operationModel.LineVehicleMileageReportVersion{}).FirstBy(param.Id)
	if version.Id == 0 {
		log.ErrorFields("LineVehicleMileageReportVersion.FirstBy is empty", nil)
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	var recoverReports []operationModel.LineVehicleMileageReport
	err = json.Unmarshal(version.Data, &recoverReports)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	//查询之前的数据 保存成版本
	oldVersionData := (&operationModel.LineVehicleMileageReport{}).GetByDay(0, version.CorporationId, version.LineId, time.Time(version.ReportAt))

	tx := model.DB().Begin()
	err = (&operationModel.LineVehicleMileageReport{}).Delete(tx, version.CorporationId, version.LineId, time.Time(version.ReportAt))
	if err != nil {
		tx.Rollback()
		log.ErrorFields("LineVehicleMileageReport.Delete error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	if len(recoverReports) > 0 {
		err = (&operationModel.LineVehicleMileageReport{}).BatchCreate(tx, recoverReports)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("LineVehicleMileageReport.BatchCreate error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}
	tx.Commit()

	go createLineMileageReportVersion(auth.User(ctx).GetUser(), oldVersionData, "recover")

	return response.Success(rsp, nil)
}

func syncUpdateHistoryLineMileage(tx *gorm.DB, setting operationModel.OperationLineSetting, dates, columns []string, isUpdateManualData int64) error {
	var settingItem operationModel.OperationLineSettingSettingItem
	err := json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		return errors.New(fmt.Sprintf("syncUpdateHistoryLineMileage json.Unmarshal SettingItem error: %+v", err))
	}

	// 配置项关联字段变更
	// 查询日期内数据，一天一天变更
	for i := range dates {
		reportAt, _ := time.ParseInLocation(model.DateFormat, dates[i], time.Local)
		// 查询报表
		oldReports := (&operationModel.LineVehicleMileageReport{}).GetByDay(0, setting.CorporationId, setting.LineId, reportAt)
		if len(oldReports) == 0 {
			continue
		}

		endDate := time.Date(reportAt.Year(), reportAt.Month(), reportAt.Day(), 23, 59, 59, 0, time.Local)
		issItems := rpc.LineRunFormDaySum(context.TODO(), setting.CorporationId, setting.LineId, reportAt.Unix(), endDate.Unix())

		log.ErrorFields("calcHistoryReportData issItems====", map[string]interface{}{"item": issItems})
		for i := range oldReports {
			//是否需要更新手动添加的数据
			if oldReports[i].IsManualData == util.StatusForTrue && isUpdateManualData == util.StatusForFalse {
				continue
			}

			for _, issItem := range issItems {
				// 唯一性
				if oldReports[i].VehicleId == issItem.VehicleId && oldReports[i].DriverId == issItem.DriverId {
					var newReport = calcHistoryReportData(oldReports[i], issItem, settingItem, columns)
					err = newReport.TransactionUpdate(tx)
					if err != nil {
						log.ErrorFields("LineVehicleMileageReport.TransactionUpdate error", map[string]interface{}{"err": err})
						return err
					}
					buildLineVehicleMileageReportLog(tx, &oldReports[i], &newReport, "edit", "system")
					break
				}
			}
		}
		//新增版本
		go createLineMileageReportVersion(&auth.AuthUser{Id: setting.OpUserId, Name: setting.OpUserName}, oldReports, "history")
	}

	return nil
}

func calcHistoryReportData(report operationModel.LineVehicleMileageReport, issData *protoSchedule.LineRunFormDaySumItem, setting operationModel.OperationLineSettingSettingItem, columns []string) operationModel.LineVehicleMileageReport {
	log.ErrorFields("calcHistoryReportData issItem====", map[string]interface{}{"item": issData})
	log.ErrorFields("calcHistoryReportData settingItem====", map[string]interface{}{"item": setting})

	if setting.HasFrequencyIndex == util.StatusForTrue {
		//找到当前班次对应的设置项
		var index int64
		frequencyArr := strings.Split(issData.ClassIdxStr, ",")
		if len(frequencyArr) > 0 {
			index, _ = strconv.ParseInt(frequencyArr[0], 10, 64)
		}

		for i := range setting.FrequencyItems {
			if setting.FrequencyItems[i].Index == index {
				setting.FullInOutDepotMileage = setting.FrequencyItems[i].FullInOutDepotMileage
				setting.FullAssistantMileage = setting.FrequencyItems[i].FullAssistantMileage
				setting.FullInOutDepotTime = setting.FrequencyItems[i].FullInOutDepotTime
				setting.FullAssistantTime = setting.FrequencyItems[i].FullAssistantTime
				setting.RangeInOutDepotMileage = setting.FrequencyItems[i].RangeInOutDepotMileage
				setting.RangeAssistantMileage = setting.FrequencyItems[i].RangeAssistantMileage
				setting.RangeInOutDepotTime = setting.FrequencyItems[i].RangeInOutDepotTime
				setting.RangeAssistantTime = setting.FrequencyItems[i].RangeAssistantTime
				setting.AttendanceType = setting.FrequencyItems[i].AttendanceType
				if issData.ActualWholeLastClassCount+issData.ActualPartLastClassCount > 0 {
					setting.NightBefore22WorkTimeLength = setting.FrequencyItems[i].NightBefore22WorkTimeLength
					setting.NightAfter22WorkTimeLength = setting.FrequencyItems[i].NightAfter22WorkTimeLength
				}
				break
			}
		}
	} else {
		setting.NightBefore22WorkTimeLength = issData.NightBefore22HourTime
		setting.NightAfter22WorkTimeLength = issData.NightAfter22HourTime
	}

	if issData.LineType == util.LineTypeDoubleCircle {
		if issData.PlanWholeFirstServiceType == util.LineSheetForDown {
			setting.FullRatedMileage = setting.DownFullRatedMileage
		} else {
			setting.FullRatedMileage = setting.UpFullRatedMileage
		}

		if issData.PlanPartFirstServiceType == util.LineSheetForDown {
			setting.RangeRatedMileage = setting.DownRangeRatedMileage
		} else {
			setting.RangeRatedMileage = setting.UpRangeRatedMileage
		}
	}

	//if util.Include(columns, "VehicleModel") {
	//	report.VehicleModel = setting.VehicleModel
	//}
	//
	//if util.Include(columns, "VehicleLength") {
	//	report.VehicleLength = setting.VehicleLength
	//}

	if util.Include(columns, "WorkType") {
		report.WorkType = setting.FrequencyType
	}
	if util.Include(columns, "FrequencyType") {
		report.FrequencyType = setting.AttendanceType
		switch report.FrequencyType {
		case util.MileageReportForFullWork:
			report.FullWorkDay = report.WorkDayCount
			report.HalfWorkDay = 0
			report.MotorWorkDay = 0
			report.MotorBigWorkDay = 0
		case util.MileageReportForHalfWork:
			report.HalfWorkDay = report.WorkDayCount
			report.FullWorkDay = 0
			report.MotorWorkDay = 0
			report.MotorBigWorkDay = 0
		case util.MileageReportForMotorWork:
			report.MotorWorkDay = report.WorkDayCount
			report.FullWorkDay = 0
			report.HalfWorkDay = 0
			report.MotorBigWorkDay = 0
		case util.MileageReportForBigMotorWork:
			report.MotorBigWorkDay = report.WorkDayCount
			report.MotorWorkDay = 0
			report.FullWorkDay = 0
			report.HalfWorkDay = 0
		}
	}

	if util.Include(columns, "FullRatedMileage") {
		report.FullRatedMileage = setting.FullRatedMileage
	}
	if util.Include(columns, "RangeRatedMileage") {
		report.RangeRatedMileage = setting.RangeRatedMileage
	}

	if util.Include(columns, "FullInOutDepotMileage") {
		report.FullInOutDepotMileage = int64(float64(issData.ActualWholeFirstClassCount+issData.ActualWholeLastClassCount) * 0.5 * float64(setting.FullInOutDepotMileage))
	}
	if util.Include(columns, "FullAssistantMileage") {
		report.FullAssistantMileage = issData.ActualWholeFirstClassCount * setting.FullAssistantMileage
	}
	if util.Include(columns, "RangeInOutDepotMileage") {
		report.RangeInOutDepotMileage = int64(float64(issData.ActualPartFirstClassCount+issData.ActualPartLastClassCount) * 0.5 * float64(setting.RangeInOutDepotMileage))
	}
	if util.Include(columns, "RangeAssistantMileage") {
		report.RangeAssistantMileage = issData.ActualPartFirstClassCount * setting.RangeAssistantMileage
	}
	if util.Include(columns, "FullInOutDepotTime") {
		report.FullInOutDepotTime = int64(float64(issData.ActualWholeFirstClassCount+issData.ActualWholeLastClassCount) * 0.5 * float64(setting.FullInOutDepotTime))
		report.FullInOutDepotTime = util.GetHourRound2BySecond(report.FullInOutDepotTime)
	}
	if util.Include(columns, "FullAssistantTime") {
		report.FullAssistantTime = issData.ActualWholeFirstClassCount * setting.FullAssistantTime
		report.FullAssistantTime = util.GetHourRound2BySecond(report.FullAssistantTime)
	}

	if util.Include(columns, "RangeInOutDepotTime") {
		report.RangeInOutDepotTime = int64(float64(issData.ActualPartFirstClassCount+issData.ActualPartLastClassCount) * 0.5 * float64(setting.RangeInOutDepotTime))
		report.RangeInOutDepotTime = util.GetHourRound2BySecond(report.RangeInOutDepotTime)
	}
	if util.Include(columns, "RangeAssistantTime") {
		report.RangeAssistantTime = issData.ActualPartFirstClassCount * setting.RangeAssistantTime
		report.RangeAssistantTime = util.GetHourRound2BySecond(report.RangeAssistantTime)
	}

	if util.Include(columns, "FullRatedMileage") || util.Include(columns, "RangeRatedMileage") {
		report.CircleMileage = int64(((float64(report.FullDoneCircle) / 10) * float64(report.FullRatedMileage)) + ((float64(report.RangeDoneCircle) / 10) * float64(report.RangeRatedMileage)))
	}

	report.TotalMileage = report.CircleMileage + report.StopWorkRatedMileage + report.FullInOutDepotMileage + report.FullAssistantMileage +
		report.RangeInOutDepotMileage + report.RangeAssistantMileage + report.CharterBusMileage + report.AnnualReviewMileage + report.TrafficJamMileage + report.FixVehicleMileage

	if issData.PlanWholeFirstServiceType == util.LineSheetForUp || issData.PlanWholeFirstServiceType == util.LineSheetForCircle {
		if util.Include(columns, "FullRatedWorkTimeLength") {
			report.FullRatedWorkTimeLength = util.GetHourRound2BySecond(setting.UpFullRatedWorkTime)
		}
		if util.Include(columns, "FullRatedNotWorkTimeLength") {
			report.FullRatedNotWorkTimeLength = util.GetHourRound2BySecond(setting.UpFullRatedNotWorkTime)
		}
	} else {
		if util.Include(columns, "FullRatedWorkTimeLength") {
			report.FullRatedWorkTimeLength = util.GetHourRound2BySecond(setting.DownFullRatedWorkTime)
		}
		if util.Include(columns, "FullRatedNotWorkTimeLength") {
			report.FullRatedNotWorkTimeLength = util.GetHourRound2BySecond(setting.DownFullRatedNotWorkTime)
		}
	}

	if util.Include(columns, "FullRatedWorkTimeLength") {
		report.FullCircleWorkTimeLength = int64((float64(report.FullDoneCircle) / 10) * float64(report.FullRatedWorkTimeLength))
		report.FullCircleWorkTimeLength = util.GetHourRound2BySecond(report.FullCircleWorkTimeLength)
	}

	if util.Include(columns, "FullRatedNotWorkTimeLength") {
		report.FullCircleNotWorkTimeLength = int64((float64(report.FullDoneCircle) / 10) * float64(report.FullRatedNotWorkTimeLength))
		report.FullCircleNotWorkTimeLength = util.GetHourRound2BySecond(report.FullCircleNotWorkTimeLength)
	}

	if issData.PlanPartFirstServiceType == util.LineSheetForUp || issData.PlanPartFirstServiceType == util.LineSheetForCircle {
		if util.Include(columns, "RangeRatedWorkTimeLength") {
			report.RangeRatedWorkTimeLength = util.GetHourRound2BySecond(setting.UpRangeRatedWorkTime)
		}
		if util.Include(columns, "RangeRatedNotWorkTimeLength") {
			report.RangeRatedNotWorkTimeLength = util.GetHourRound2BySecond(setting.UpRangeRatedNotWorkTime)
		}
	} else {
		if util.Include(columns, "RangeRatedWorkTimeLength") {
			report.RangeRatedWorkTimeLength = util.GetHourRound2BySecond(setting.DownRangeRatedWorkTime)
		}
		if util.Include(columns, "RangeRatedNotWorkTimeLength") {
			report.RangeRatedNotWorkTimeLength = util.GetHourRound2BySecond(setting.DownRangeRatedNotWorkTime)
		}
	}

	if util.Include(columns, "RangeRatedWorkTimeLength") {
		report.RangeCircleWorkTimeLength = int64((float64(report.RangeDoneCircle) / 10) * float64(report.RangeRatedWorkTimeLength))
		report.RangeCircleWorkTimeLength = util.GetHourRound2BySecond(report.RangeCircleWorkTimeLength)
	}
	if util.Include(columns, "RangeCircleNotWorkTimeLength") {
		report.RangeCircleNotWorkTimeLength = int64((float64(report.RangeDoneCircle) / 10) * float64(report.RangeRatedNotWorkTimeLength))
		report.RangeCircleNotWorkTimeLength = util.GetHourRound2BySecond(report.RangeCircleNotWorkTimeLength)
	}

	report.TotalWorkTimeLength = report.FullCircleWorkTimeLength + report.FullInOutDepotTime + report.FullAssistantTime + report.FullStopWorkTimeLength +
		report.RangeCircleWorkTimeLength + report.RangeInOutDepotTime + report.RangeAssistantTime + report.RangeStopWorkTimeLength + report.FixVehicleWorkTimeLength + report.AddWorkWorkTimeLength +
		report.TrafficJamTimeLength
	report.TotalWorkTimeLength = util.GetHourRound2BySecond(report.TotalWorkTimeLength)

	report.TotalNotWorkTimeLength = report.FullCircleNotWorkTimeLength + report.FullStopNotWorkTimeLength + report.FixVehicleTimeLength +
		report.AccidentDisputeTimeLength + report.RangeCircleNotWorkTimeLength + report.RangeStopNotWorkTimeLength + report.AddWorkNotWorkTimeLength
	report.TotalNotWorkTimeLength = util.GetHourRound2BySecond(report.TotalNotWorkTimeLength)

	report.TotalTimeLength = report.TotalWorkTimeLength + report.TotalNotWorkTimeLength
	report.TotalTimeLength = util.GetHourRound2BySecond(report.TotalTimeLength)
	return report
}

func GetDontHaveDataDays(isApproval, corporationId, lineId int64, startAt, endAt time.Time) []string {
	hasReportAts := (&operationModel.LineVehicleMileageReport{}).HasRecordReportAt(isApproval, corporationId, lineId, startAt, endAt)
	var hasReportAtMap = make(map[string]bool)

	for i := range hasReportAts {
		hasReportAtMap[time.Time(hasReportAts[i]).Format(model.DateFormat)] = true
	}

	var notExistDays []string
	for {
		if startAt.Unix() > endAt.Unix() {
			break
		}

		if _, ok := hasReportAtMap[startAt.Format(model.DateFormat)]; !ok {
			notExistDays = append(notExistDays, startAt.Format(model.DateFormat))
		}

		startAt = startAt.AddDate(0, 0, 1)
	}

	return notExistDays
}

func createLineMileageReportVersion(user *auth.AuthUser, reports []operationModel.LineVehicleMileageReport, scene string) {
	var version = operationModel.LineVehicleMileageReportVersion{
		TopCorporationId: user.TopCorporationId,
		CorporationId:    reports[0].CorporationId,
		CorporationName:  reports[0].CorporationName,
		LineId:           reports[0].LineId,
		LineName:         reports[0].LineName,
		ReportAt:         reports[0].ReportAt,
		Scene:            scene,
	}
	version.Data, _ = json.Marshal(reports)
	version.OpUserId = user.Id
	version.OpUserName = user.Name

	_ = version.Create()
}

func UpdateHistoryWorkDay() {
	time.Sleep(30 * time.Second)
	var year = 2023
	for {
		if year > 2024 {
			break
		}
		var reports []operationModel.LineVehicleMileageReport
		model.DB().Table(fmt.Sprintf("line_vehicle_mileage_reports_%v", year)).Select("Id", "FrequencyType", "WorkDayCount").Find(&reports)
		for i := range reports {
			if reports[i].FrequencyType == util.MileageReportForFullWork {
				reports[i].FullWorkDay = reports[i].WorkDayCount
			}
			if reports[i].FrequencyType == util.MileageReportForHalfWork {
				reports[i].HalfWorkDay = reports[i].WorkDayCount
			}
			if reports[i].FrequencyType == util.MileageReportForMotorWork {
				reports[i].MotorWorkDay = reports[i].WorkDayCount
			}
			model.DB().Table(fmt.Sprintf("line_vehicle_mileage_reports_%v", year)).Where("id = ?", reports[i].Id).Select("FullWorkDay", "HalfWorkDay", "MotorWorkDay").Updates(&reports[i])
		}

		year += 1
	}

}

// LineMileageSumReport 累计公里统计报表-线路维度
func (op *OperationReport) LineMileageSumReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	result, errorCode := LineMileageSumReport(ctx, auth.User(ctx).GetTopCorporationId(), auth.User(ctx).GetUserId(), req)
	if errorCode != response.SUCCESS {
		return response.Error(rsp, errorCode)
	}

	return response.Success(rsp, result)
}

func (op *OperationReport) LineMileageSumReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineMileageRangeReportRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.IsExportFile == util.StatusForTrue {
		var startAt = time.Time(param.StartAt)
		var endAt = time.Time(param.EndAt)
		var fileName = fmt.Sprintf("累计公里统计（线路）%s-%s.xlsx", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))
		paramByte, _ := json.Marshal(param)
		exportFileRecord, err := exportService.CreateExportFileRecord(auth.User(ctx).GetUserId(), fileName, (&operationModel.LineVehicleMileageReport{}).LineMileageSumReportTableName(), paramByte, startAt, endAt)
		if err != nil {
			return response.Error(rsp, response.FAIL)
		}
		go ExportLineMileageSumReport(ctx, exportFileRecord, req)
		return response.Success(rsp, nil)
	}

	result, errorCode := LineMileageSumReport(ctx, auth.User(ctx).GetTopCorporationId(), auth.User(ctx).GetUserId(), req)
	if errorCode != response.SUCCESS {
		return response.Error(rsp, errorCode)
	}

	return response.Success(rsp, result)
}

type LineMileageSumReportItem struct {
	Items      []exportService.LineMileageSumReportRsp `json:"Items"`
	TotalCount int64                                   `json:"TotalCount"`
	SumItem    exportService.LineMileageSumReportRsp   `json:"SumItem"`
}

func LineMileageSumReport(ctx context.Context, topCorporationId, userId int64, req *api.Request) (LineMileageSumReportItem, string) {
	var param LineMileageRangeReportRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return LineMileageSumReportItem{}, response.ParamsInvalid
	}

	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)

	if startAt.Unix() > endAt.Unix() {
		return LineMileageSumReportItem{}, response.ParamsInvalid
	}
	userSetting := service.GetUserSetting(auth.User(ctx).GetUserId())
	isApproval := userSetting.OperationReportDataIsApproval

	var reportMap = make(map[string]operationModel.LineMileageAndCircleSumItem)
	//线路公里明细表
	lineMileageReports := (&operationModel.LineVehicleMileageReport{}).GetLineMileageAndCircleSum(isApproval, param.CorporationIds, param.LineIds, startAt, endAt)

	for i := range lineMileageReports {
		mapKey := fmt.Sprintf("%v_%v_%s", lineMileageReports[i].CorporationId, lineMileageReports[i].LineId, lineMileageReports[i].LineName)
		reportMap[mapKey] = lineMileageReports[i]
	}

	//班制外加班明细表
	outAddWorkMileageReports := (&operationModel.OutFrequencyAddWorkReport{}).GetLinesTotalMileage(isApproval, param.CorporationIds, param.LineIds, startAt, endAt)
	for i := range outAddWorkMileageReports {
		mapKey := fmt.Sprintf("%v_%v_%s", outAddWorkMileageReports[i].CorporationId, outAddWorkMileageReports[i].LineId, outAddWorkMileageReports[i].LineName)
		if data, ok := reportMap[mapKey]; ok {
			data.TotalMileage += outAddWorkMileageReports[i].TotalMileage
			reportMap[mapKey] = data
		} else {
			reportMap[mapKey] = outAddWorkMileageReports[i]
		}
	}

	if param.IsRelateIrregularLineReport == util.StatusForTrue {
		//定制线路表数据
		irregularLineReports := (&operationModel.IrregularLineReport{}).GetLinesTotalMileageAndCircle(isApproval, param.CorporationIds, param.LineIds, startAt, endAt)
		for i := range irregularLineReports {
			mapKey := fmt.Sprintf("%v_%v_%s", irregularLineReports[i].CorporationId, irregularLineReports[i].LineId, irregularLineReports[i].LineName)
			if data, ok := reportMap[mapKey]; ok {
				data.TotalMileage += irregularLineReports[i].TotalMileage
				data.TotalCircle += irregularLineReports[i].TotalCircle
				reportMap[mapKey] = data
			} else {
				reportMap[mapKey] = irregularLineReports[i]
			}
		}
	}

	var reports []exportService.LineMileageSumReportRsp
	var sumItem exportService.LineMileageSumReportRsp
	for key := range reportMap {
		report := exportService.LineMileageSumReportRsp{
			LineMileageAndCircleSumItem: reportMap[key],
		}

		//计算区域公里
		//计算各区域的里程
		lineSettings := (&operationModel.OperationLineSetting{}).GetTimeCrossSetting([]int64{report.CorporationId}, report.LineId, param.StartAt.ToTime(), param.EndAt.ToTime())

		if len(lineSettings) == 1 {
			var settingItem operationModel.OperationLineSettingSettingItem
			_ = json.Unmarshal(lineSettings[0].SettingItem, &settingItem)

			report.CityCenter = decimal.NewFromInt(report.TotalMileage).Mul(decimal.NewFromFloat(settingItem.CityCenter)).Div(decimal.NewFromInt(100)).IntPart()
			report.Jiaojiang = decimal.NewFromInt(report.TotalMileage).Mul(decimal.NewFromFloat(settingItem.Jiaojiang)).Div(decimal.NewFromInt(100)).IntPart()
			report.Taizhouwan = decimal.NewFromInt(report.TotalMileage).Mul(decimal.NewFromFloat(settingItem.Taizhouwan)).Div(decimal.NewFromInt(100)).IntPart()
			report.Luqiao = decimal.NewFromInt(report.TotalMileage).Mul(decimal.NewFromFloat(settingItem.Luqiao)).Div(decimal.NewFromInt(100)).IntPart()
			report.Linhai = decimal.NewFromInt(report.TotalMileage).Mul(decimal.NewFromFloat(settingItem.Linhai)).Div(decimal.NewFromInt(100)).IntPart()
			report.Huangyan = decimal.NewFromInt(report.TotalMileage).Mul(decimal.NewFromFloat(settingItem.Huangyan)).Div(decimal.NewFromInt(100)).IntPart()
		}

		if len(lineSettings) > 1 {
			//跨多个配置项时  需要计算线路每天的里程
			report.CityCenter, report.Jiaojiang, report.Taizhouwan, report.Huangyan, report.Luqiao, report.Linhai = service.CalcLineCityMileage(report.CorporationId, report.LineId, param.IsRelateIrregularLineReport, isApproval, param.StartAt.ToTime(), param.EndAt.ToTime())
		}

		sumItem.TotalMileage += report.TotalMileage
		sumItem.TotalCircle += report.TotalCircle
		sumItem.CityCenter += report.CityCenter
		sumItem.Taizhouwan += report.Taizhouwan
		sumItem.Jiaojiang += report.Jiaojiang
		sumItem.Huangyan += report.Huangyan
		sumItem.Luqiao += report.Luqiao
		sumItem.Linhai += report.Linhai

		reports = append(reports, report)
	}

	sort.SliceStable(reports, func(i, j int) bool {
		return reports[i].LineName < reports[j].LineName
	})

	var results []exportService.LineMileageSumReportRsp
	if param.Limit+param.Offset >= len(reports) {
		if param.Offset < len(reports) {
			results = reports[param.Offset:]
		}
	} else {
		results = reports[param.Offset:(param.Offset + param.Limit)]
	}

	for i := range results {
		oetLine, _ := rpc.GetLineWithId(context.TODO(), results[i].LineId)
		if oetLine != nil {
			results[i].LineAttr = oetLine.LineAttribute
		}
	}

	return LineMileageSumReportItem{
		Items:      results,
		TotalCount: int64(len(reports)),
		SumItem:    sumItem,
	}, response.SUCCESS
}

func ExportLineMileageSumReport(ctx context.Context, exportFile exportModel.ExportFile, req *api.Request) {
	result, errorCode := LineMileageSumReport(ctx, exportFile.TopCorporationId, exportFile.OpUserId, req)

	if errorCode != response.SUCCESS {
		exportFile.ErrorCode = errorCode
		err := exportFile.UpdateFail()
		log.ErrorFields("ExportIrregularLineReport exportFile.UpdateStatus error", map[string]interface{}{"err": err})
		return
	}

	err := exportService.LineMileageSumReport(result.Items, exportFile)
	if err != nil {
		exportFile.ErrorCode = err.Error()
		err = exportFile.UpdateFail()
	} else {
		err = exportFile.UpdateSuccess()
	}
	log.ErrorFields("ExportIrregularLineReport exportFile.UpdateStatus error", map[string]interface{}{"err": err})
}

// VehicleMileageSumReport 累计公里统计报表-车辆维度
func (op *OperationReport) VehicleMileageSumReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineMileageRangeReportRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)

	if startAt.Unix() > endAt.Unix() {
		return response.Error(rsp, response.ParamsInvalid)
	}
	userSetting := service.GetUserSetting(auth.User(ctx).GetUserId())
	isApproval := userSetting.OperationReportDataIsApproval

	//线路公里明细报表
	lineMileageReports := (&operationModel.LineVehicleMileageReport{}).GetVehicleMileageAndCircleSum(isApproval, param.CorporationIds, param.LineIds, param.VehicleIds, startAt, endAt)

	var reportMap = make(map[int64]operationModel.LineMileageAndCircleSumItem)
	for i := range lineMileageReports {
		reportMap[lineMileageReports[i].VehicleId] = lineMileageReports[i]
	}
	//班制外加班明细表
	outAddWorkMileageReports := (&operationModel.OutFrequencyAddWorkReport{}).GetVehiclesTotalMileage(isApproval, param.CorporationIds, param.LineIds, param.VehicleIds, startAt, endAt)

	for i := range outAddWorkMileageReports {
		if data, ok := reportMap[outAddWorkMileageReports[i].VehicleId]; ok {
			data.TotalMileage += outAddWorkMileageReports[i].TotalMileage
			reportMap[outAddWorkMileageReports[i].VehicleId] = data
		} else {
			reportMap[outAddWorkMileageReports[i].VehicleId] = outAddWorkMileageReports[i]
		}
	}

	if param.IsRelateIrregularLineReport == util.StatusForTrue {
		//定制线路表数据
		irregularLineReports := (&operationModel.IrregularLineReport{}).GetVehiclesTotalMileageAndCircle(isApproval, param.CorporationIds, param.LineIds, param.VehicleIds, startAt, endAt)
		for i := range irregularLineReports {
			if data, ok := reportMap[irregularLineReports[i].VehicleId]; ok {
				data.TotalMileage += irregularLineReports[i].TotalMileage
				reportMap[irregularLineReports[i].VehicleId] = data
			} else {
				reportMap[irregularLineReports[i].VehicleId] = irregularLineReports[i]
			}
		}
	}

	var reports []operationModel.LineMileageAndCircleSumItem
	var sumItem operationModel.LineMileageAndCircleSumItem
	for key := range reportMap {
		reports = append(reports, reportMap[key])
		sumItem.TotalMileage += reportMap[key].TotalMileage
	}

	sort.SliceStable(reports, func(i, j int) bool {
		return reports[i].License < reports[j].License
	})

	var results []operationModel.LineMileageAndCircleSumItem
	if param.Limit+param.Offset >= len(reports) {
		if param.Offset < len(reports) {
			results = reports[param.Offset:]
		}
	} else {
		results = reports[param.Offset:(param.Offset + param.Limit)]
	}

	for i := range results {
		vehicleInfo := rpc.GetVehicleWithId(ctx, results[i].VehicleId)
		if vehicleInfo != nil {
			results[i].LineName = vehicleInfo.Line
			if vehicleInfo.SonCorporationId > 0 {
				corporation := rpc.GetCorporationById(ctx, vehicleInfo.SonCorporationId)
				if corporation != nil {
					results[i].CorporationName = corporation.Name
				}
			}
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(reports),
		"SumItem":    sumItem,
	})
}

func (op *OperationReport) VehicleMileageSumReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return op.VehicleMileageSumReport(ctx, req, rsp)
}

type IssData struct {
	LineId   int64  `json:"LineId"`
	ReportAt string `json:"ReportAt"`
	Data     []*protoSchedule.LineRunFormDaySumItem
}

func TakeIssData() {
	time.Sleep(30 * time.Second)
	fmt.Printf("start TakeIssData.....")
	var lineIds = []map[string]int64{
		{"LineId": 173, "CorporationId": 1519706879830262790},
		{"LineId": 179, "CorporationId": 1519706879830262790},
		{"LineId": 198, "CorporationId": 1519707559844381705},
		{"LineId": 201, "CorporationId": 1519707559844381705},
	}
	startAt := time.Date(2024, 7, 26, 0, 0, 0, 0, time.Local)

	var data []IssData
	for {
		if startAt.Month() == 8 && startAt.Day() > 25 {
			break
		}
		endAt := time.Date(startAt.Year(), startAt.Month(), startAt.Day(), 23, 59, 59, 0, time.Local)
		for _, line := range lineIds {
			issItems := rpc.LineRunFormDaySum(context.TODO(), line["CorporationId"], line["LineId"], startAt.Unix(), endAt.Unix())
			data = append(data, IssData{
				LineId:   line["LineId"],
				ReportAt: startAt.Format("2006-01-02"),
				Data:     issItems,
			})
		}
		startAt = startAt.AddDate(0, 0, 1)
	}

	dataByte, _ := json.Marshal(data)

	file, err := os.Create("iss_run_data.json")
	if err != nil {
		// 如果文件创建失败，打印错误并退出
		fmt.Println("Error creating file:", err)
		return
	}
	defer file.Close() // 确保在函数退出时关闭文件

	// 写入数据到文件
	_, err = file.Write(dataByte)
	if err != nil {
		// 如果写入失败，打印错误并退出
		fmt.Println("Error writing to file:", err)
		return
	}

	// 刷新文件缓冲区到磁盘
	err = file.Sync()
	if err != nil {
		fmt.Println("Error syncing file to disk:", err)
		return
	}

	fmt.Printf("end TakeIssData.....")
}
