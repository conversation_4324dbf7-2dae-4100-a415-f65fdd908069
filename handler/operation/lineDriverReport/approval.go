package lineDriverReport

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	operationModel "app/org/scs/erpv2/api/model/operation"
	processModel "app/org/scs/erpv2/api/model/process"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"math/rand"
	"strconv"
	"time"
)

type OperationApprovalRequest struct {
	Id             int64           `json:"Id"`
	StartAt        model.LocalTime `json:"StartAt"`
	EndAt          model.LocalTime `json:"EndAt"`
	LineIds        []int64         `json:"LineIds"`
	CorporationIds []int64         `json:"CorporationIds"`
	LineId         int64           `json:"LineId"`
	CorporationId  int64           `json:"CorporationId"`
	RelateReports  []string        `json:"RelateReports"`
	ApplyStatus    []int64         `json:"ApplyStatus"`
	model.Paginator
}

func (*OperationReport) ApprovalCreate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param operationModel.OperationApproval
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	var relateReport []string
	err = json.Unmarshal(param.RelateReport, &relateReport)
	if err != nil {
		log.ErrorFields("Unmarshal RelateReport error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var lineIds []string
	err = json.Unmarshal(param.LineIds, &lineIds)
	if err != nil {
		log.ErrorFields("Unmarshal lineIds error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	//查询是否有正在审批或者审批通过的流程
	if (&operationModel.OperationApproval{}).IsExistCrossApproval(param.CorporationId, relateReport, lineIds, time.Time(param.StartAt), time.Time(param.EndAt)) {
		return response.Error(rsp, response.RepeatApplyProcess)
	}

	authUser := auth.User(ctx).GetUser()
	param.OpUserId = authUser.Id
	param.OpUserName = authUser.Name
	param.OpUserCorporationId = authUser.CorporationId
	param.ApplyStatus = util.ApplyStatusForDoing
	tx := model.DB().Begin()

	var processId string
	if param.Id > 0 {
		approval := (&operationModel.OperationApproval{}).FirstBy(param.Id)
		if approval.Id == 0 {
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		//获取之前的流程
		processes := (&processModel.LbpmApplyProcess{}).GetProcessesByItemId(config.OperationReportApplyFormTemplate, param.Id)

		var process processModel.LbpmApplyProcess
		if len(processes) > 0 {
			process = processes[0]
		}

		//没流程、废弃流程、已经审批通过的流程 再次发起审批时，表单数据更新，流程重新发起
		if process.FormInstanceId == 0 || process.Status == util.ProcessStatusForDone || process.Status == util.ProcessStatusForAbandon {
			rand.Seed(time.Now().UnixNano())
			num := rand.Intn(10000)
			param.Code = fmt.Sprintf("%s%04d", time.Now().Format("20060102"), num)
			param.CreatedAt = model.LocalTime(time.Now())
			err = param.UpdateAll(tx)
		} else {
			processId = process.ProcessId
			param.Code = approval.Code
			param.CreatedAt = approval.CreatedAt
			err = param.UpdateAll(tx)
		}
		if err != nil {
			tx.Rollback()
			log.ErrorFields("UpdateAll error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	} else {
		err = param.Create(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("Create error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}
	//发起流程审批
	if config.Config.Lbpm.Enable {
		byteParam, _ := json.Marshal(param)
		formData := map[string]interface{}{}

		if processId != "" {
			//重新发起流程
			err = processService.RestartProcess(authUser, processId, string(byteParam), formData)
		} else {
			//发起新的流程
			processTitle := fmt.Sprintf("营运报表审批")
			_, err = processService.NewDispatchProcess(authUser, config.OperationReportApplyFormTemplate, processTitle, param.Id, param.TableName(), param.ApplyStatusFieldName(), string(byteParam), formData)
		}

		if err != nil {
			tx.Rollback()
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.FAIL)
		}
	}

	tx.Commit()

	return response.Success(rsp, map[string]interface{}{
		"Id": param.Id,
	})
}

func (*OperationReport) ApprovalList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return ApprovalList(ctx, req, rsp)
}

func (*OperationReport) ApprovalListExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return ApprovalList(ctx, req, rsp)
}

func ApprovalList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param OperationApprovalRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)
	approvals, count := (&operationModel.OperationApproval{}).GetBy(param.CorporationIds, param.RelateReports, param.LineIds, param.ApplyStatus, startAt, endAt, param.Paginator)

	for i := range approvals {
		corporation := rpc.GetCorporationById(ctx, approvals[i].OpUserCorporationId)
		if corporation != nil {
			approvals[i].OpUserCorporationName = corporation.Name
		}

		process := (&processModel.LbpmApplyProcess{}).GetProcessesByItemId(config.OperationReportApplyFormTemplate, approvals[i].Id)
		handler, _ := (&processModel.LbpmApplyProcess{}).GetHandlerByItemId(approvals[i].TableName(), approvals[i].Id)
		approvals[i].CurrentHandler = process[0].CurrentHandlerUserName
		if handler.Id > 0 {
			approvals[i].CurrentHandlerAt = *handler.StartAt
		}

		approvals[i].EnableUpdate = true
		setting := (&settingModel.GlobalSetting{}).GetBy(auth.User(ctx).GetTopCorporationId(), 1)
		var settingItem settingModel.GlobalSettingItemForOperationReport
		_ = json.Unmarshal(setting.SettingItem, &settingItem)

		if settingItem.StartDay > 0 && settingItem.EndDay > 0 && settingItem.StartDay <= settingItem.EndDay {
			nowDay := int64(time.Now().Day())
			if nowDay >= settingItem.StartDay && nowDay <= settingItem.EndDay {
				if !util.IncludeInt64(settingItem.DisabledFleetIds, approvals[i].CorporationId) {
					approvals[i].EnableUpdate = false
				}
			}
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      approvals,
		"TotalCount": count,
	})
}

func (*OperationReport) ApprovalShow(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param OperationApprovalRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	approval := (&operationModel.OperationApproval{}).FirstBy(param.Id)
	if approval.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	corporation := rpc.GetCorporationById(ctx, approval.OpUserCorporationId)
	if corporation != nil {
		approval.OpUserCorporationName = corporation.Name
	}

	return response.Success(rsp, approval)
}

// ApprovalTerminate 已审批通过的流程撤回
func (*OperationReport) ApprovalTerminate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param OperationApprovalRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	approval := (&operationModel.OperationApproval{}).FirstBy(param.Id)
	if approval.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if approval.ApplyStatus != util.ApplyStatusForDone {
		return response.Error(rsp, response.Forbidden)
	}

	var relateReports []string
	err = json.Unmarshal(approval.RelateReport, &relateReports)
	if err != nil {
		log.ErrorFields("json.Unmarshal relateReports error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	var lineIdStr []string
	err = json.Unmarshal(approval.LineIds, &lineIdStr)
	if err != nil {
		log.ErrorFields("json.Unmarshal lineIds error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	reportAts := util.GetDateFromRangeTime(time.Time(approval.StartAt), time.Time(approval.EndAt))
	updateLineVehicleReport := util.Include(relateReports, "line_vehicle_operation_report") || util.Include(relateReports, "driver_work_report") || util.Include(relateReports, "line_salary_report")
	updateIrregularLineReport := util.Include(relateReports, "irregular_line_report")
	updateOutFrequencyAddWorkReport := util.Include(relateReports, "out_frequency_add_work_report")
	var lineIds []int64
	for i := range lineIdStr {
		lineId, _ := strconv.ParseInt(lineIdStr[i], 10, 64)
		lineIds = append(lineIds, lineId)
	}
	tx := model.DB().Begin()
	for i := range reportAts {
		reportAt, err := time.ParseInLocation(model.DateFormat, reportAts[i], time.Local)
		if err != nil {
			log.ErrorFields("time.ParseInLocation error", map[string]interface{}{"err": err})
			tx.Rollback()
			return response.Error(rsp, response.FAIL)
		}

		if updateLineVehicleReport {
			err = (&operationModel.LineVehicleMileageReport{}).TransactionUpdateApprovalStatus(tx, approval.CorporationId, lineIds, reportAt, util.StatusForFalse)
			if err != nil {
				log.ErrorFields("LineVehicleMileageReport UpdateApprovalStatus error", map[string]interface{}{"err": err})
				tx.Rollback()
				return response.Error(rsp, response.FAIL)
			}
		}

		if updateIrregularLineReport {
			err = (&operationModel.IrregularLineReport{}).TransactionUpdateApprovalStatus(tx, approval.CorporationId, lineIds, reportAt, util.StatusForFalse)
			if err != nil {
				log.ErrorFields("IrregularLineReport UpdateApprovalStatus error", map[string]interface{}{"err": err})
				tx.Rollback()
				return response.Error(rsp, response.FAIL)
			}
		}

		if updateOutFrequencyAddWorkReport {
			err = (&operationModel.OutFrequencyAddWorkReport{}).TransactionUpdateApprovalStatus(tx, approval.CorporationId, lineIds, reportAt, util.StatusForFalse)
			if err != nil {
				log.ErrorFields("OutFrequencyAddWorkReport UpdateApprovalStatus error", map[string]interface{}{"err": err})
				tx.Rollback()
				return response.Error(rsp, response.FAIL)
			}
		}
	}

	approval.ApplyStatus = util.ApplyStatusForTerminate
	err = approval.UpdateApplyStatus(tx)
	if err != nil {
		log.ErrorFields("UpdateApplyStatus error", map[string]interface{}{"err": err})
		tx.Rollback()
		return response.Error(rsp, response.FAIL)
	}

	tx.Commit()

	return response.Success(rsp, nil)
}

type CheckLineReportAtResult struct {
	LineId                             int64    `json:"LineId"`
	LineName                           string   `json:"LineName"`
	DontHaveDataReportAts              []string `json:"DontHaveDataReportAts"`              //线路公里明细表没有数据的日期
	DontApprovalReportAts              []string `json:"DontApprovalReportAts"`              //线路公里明细表数据没审批的日期
	DontHaveIrregularLineReportAts     []string `json:"DontHaveIrregularLineReportAts"`     //定制线路没有数据的日期
	DontApprovalIrregularLineReportAts []string `json:"DontApprovalIrregularLineReportAts"` //定制线路数据没审批的日期
	DontHaveAddWorkReportAts           []string `json:"DontHaveAddWorkReportAts"`           //班制外加班没有数据的日期
	DontApprovalAddWorkReportAts       []string `json:"DontApprovalAddWorkReportAts"`       //班制外加班数据没审批的日期
}

// CheckLineReportAtApproval 线路没有数据的日期和线路没有审批的日期的核验
func (*OperationReport) CheckLineReportAtApproval(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param OperationApprovalRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)
	if startAt.IsZero() || endAt.IsZero() {
		return response.Error(rsp, response.ParamsInvalid)
	}
	hasLineVehicleReport := util.Include(param.RelateReports, "line_vehicle_operation_report") || util.Include(param.RelateReports, "driver_work_report") || util.Include(param.RelateReports, "line_salary_report")
	hasIrregularLineReport := util.Include(param.RelateReports, "irregular_line_report")
	hasOutFrequencyAddWorkReport := util.Include(param.RelateReports, "out_frequency_add_work_report")

	allReportAts := util.GetDateFromRangeTime(startAt, endAt)
	var results []CheckLineReportAtResult

	for _, lineId := range param.LineIds {
		var dontHaveDataReportAts []string
		var dontApprovalReportAts []string
		var dontHaveIrregularLineReportAts []string
		var dontApprovalIrregularLineReportAts []string
		var dontHaveAddWorkReportAts []string
		var dontApprovalAddWorkReportAts []string
		for i := range allReportAts {
			if hasLineVehicleReport {
				hasDataReportAts := (&operationModel.LineVehicleMileageReport{}).GetAllReportAt(param.CorporationIds, lineId, startAt, endAt)
				dontApprovalReportAts = (&operationModel.LineVehicleMileageReport{}).GetAllReportAtByStatus(param.CorporationIds, lineId, util.StatusForFalse, startAt, endAt)

				if !util.Include(hasDataReportAts, allReportAts[i]) {
					dontHaveDataReportAts = append(dontHaveDataReportAts, allReportAts[i])
				}
			}

			if hasIrregularLineReport {
				hasIrregularLineDataReportAts := (&operationModel.IrregularLineReport{}).GetAllReportAt(param.CorporationIds, lineId, startAt, endAt)
				dontApprovalIrregularLineReportAts = (&operationModel.IrregularLineReport{}).GetAllReportAtByStatus(param.CorporationIds, lineId, util.StatusForFalse, startAt, endAt)

				if !util.Include(hasIrregularLineDataReportAts, allReportAts[i]) {
					dontHaveIrregularLineReportAts = append(dontHaveIrregularLineReportAts, allReportAts[i])
				}
			}

			if hasOutFrequencyAddWorkReport {
				hasAddWorkReportAts := (&operationModel.OutFrequencyAddWorkReport{}).GetAllReportAt(param.CorporationIds, lineId, startAt, endAt)
				dontApprovalAddWorkReportAts = (&operationModel.OutFrequencyAddWorkReport{}).GetAllReportAtByStatus(param.CorporationIds, lineId, util.StatusForFalse, startAt, endAt)

				if !util.Include(hasAddWorkReportAts, allReportAts[i]) {
					dontHaveAddWorkReportAts = append(dontHaveAddWorkReportAts, allReportAts[i])
				}
			}

		}

		oetLine, _ := rpc.GetLineWithId(ctx, lineId)
		var lineName string
		if oetLine != nil {
			lineName = oetLine.Name
		}

		if len(dontHaveDataReportAts) > 0 || len(dontApprovalReportAts) > 0 {
			results = append(results, CheckLineReportAtResult{
				LineId:                             lineId,
				LineName:                           lineName,
				DontHaveDataReportAts:              dontHaveDataReportAts,
				DontApprovalReportAts:              dontApprovalReportAts,
				DontHaveIrregularLineReportAts:     dontHaveIrregularLineReportAts,
				DontApprovalIrregularLineReportAts: dontApprovalIrregularLineReportAts,
				DontHaveAddWorkReportAts:           dontHaveAddWorkReportAts,
				DontApprovalAddWorkReportAts:       dontApprovalAddWorkReportAts,
			})
		}
	}

	return response.Success(rsp, results)
}

func (*OperationReport) CheckLineReportEditStatus(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param OperationApprovalRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)
	if startAt.IsZero() || endAt.IsZero() {
		return response.Error(rsp, response.ParamsInvalid)
	}
	if len(param.RelateReports) == 0 || param.LineId == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	isExist := (&operationModel.OperationApproval{}).IsExistCrossApproval(param.CorporationId, param.RelateReports, []string{strconv.Itoa(int(param.LineId))}, startAt, endAt)
	return response.Success(rsp, map[string]interface{}{
		"IsEnableEdit": !isExist,
	})
}

type NotApprovalCorporationItem struct {
	CorporationId   int64                 `json:"CorporationId"`
	CorporationName string                `json:"CorporationName"`
	Items           []NotApprovalLineItem `json:"Items"`
}

type NotApprovalLineItem struct {
	CorporationId   int64  `json:"CorporationId"`
	CorporationName string `json:"CorporationName"`
	LineId          int64  `json:"LineId"`
	LineName        string `json:"LineName"`
	Scene           string `json:"Scene"`
}

func (*OperationReport) CheckLineNotApproval(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param OperationApprovalRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	//线路公里明细表
	lineMileageReports := (&operationModel.LineVehicleMileageReport{}).GetNotApprovalLine(param.CorporationIds, param.StartAt.ToTime(), param.EndAt.ToTime())
	var corporationReportMap = make(map[int64][]NotApprovalLineItem)
	for _, item := range lineMileageReports {
		corporationReportMap[item.CorporationId] = append(corporationReportMap[item.CorporationId], NotApprovalLineItem{
			LineId:          item.LineId,
			LineName:        item.LineName,
			CorporationId:   item.CorporationId,
			CorporationName: item.CorporationName,
			Scene:           "line_vehicle_operation_report",
		})
	}
	//定制线路明细表
	irregularLineReports := (&operationModel.IrregularLineReport{}).GetNotApprovalLine(param.CorporationIds, param.StartAt.ToTime(), param.EndAt.ToTime())
	for _, item := range irregularLineReports {
		corporationReportMap[item.CorporationId] = append(corporationReportMap[item.CorporationId], NotApprovalLineItem{
			LineId:          item.LineId,
			LineName:        item.LineName,
			CorporationId:   item.CorporationId,
			CorporationName: item.CorporationName,
			Scene:           "irregular_line_report",
		})
	}
	//班制外加班明细表
	addWorkReports := (&operationModel.OutFrequencyAddWorkReport{}).GetNotApprovalLine(param.CorporationIds, param.StartAt.ToTime(), param.EndAt.ToTime())
	for _, item := range addWorkReports {
		corporationReportMap[item.CorporationId] = append(corporationReportMap[item.CorporationId], NotApprovalLineItem{
			LineId:          item.LineId,
			LineName:        item.LineName,
			CorporationId:   item.CorporationId,
			CorporationName: item.CorporationName,
			Scene:           "out_frequency_add_work_report",
		})
	}

	var corporationReports []NotApprovalCorporationItem
	for _, items := range corporationReportMap {
		corporationReports = append(corporationReports, NotApprovalCorporationItem{
			CorporationId:   items[0].CorporationId,
			CorporationName: items[0].CorporationName,
			Items:           items,
		})
	}

	return response.Success(rsp, corporationReports)
}
