package lineDriverReport

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	operationModel "app/org/scs/erpv2/api/model/operation"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/util/response"
	"context"
	api "github.com/micro/go-micro/v2/api/proto"
)

type LineRoadHandler struct {
	VehicleId      int64   `json:"VehicleId"`
	License        string  `json:"License"`
	StartAt        string  `json:"StartAt"`        // YYYYMMDD
	EndAt          string  `json:"EndAt"`          // YYYYMMDD
	IsMatch        int64   `json:"IsMatch"`        // 1无异常 2异常
	ReportType     int64   `json:"ReportType"`     // 1调度数据 2IC卡数据
	LineIds        []int64 `json:"LineIds"`        // 线路id
	UpLineIds      []int64 `json:"UpLineIds"`      // ic卡线路id
	LineName       string  `json:"LineName"`       // ic卡线路名称
	CorporationIds []int64 `json:"CorporationIds"` // 组织架构ids
	model.Paginator
}

func (*OperationReport) VehicleLineRoadReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LineRoadHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.StartAt == "" || form.EndAt == "" {
		return response.Error(rsp, response.ParamsInvalid)
	}

	list, totalCount, err := (&operationModel.VehicleLineIcReport{}).List(form.StartAt, form.EndAt, form.License, form.LineName, form.IsMatch, form.ReportType, form.LineIds, form.UpLineIds, form.CorporationIds, form.Paginator)
	if err != nil {
		log.ErrorFields("VehicleLineIcReport List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	for index := range list {
		if form.ReportType == 1 {
			list[index].Lines, _ = (&operationModel.VehicleLineIcReportLine{}).List(list[index].Id, list[index].TxnDate)
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (*OperationReport) VehicleLineRoadReportCalc(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LineRoadHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.StartAt == "" || form.EndAt == "" || form.ReportType == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	if service.VehicleLineRoadReportCalcFlag {
		return response.Error(rsp, "数据计算中，请稍等")
	}
	if form.ReportType == 1 {
		go service.VehicleLineRoadReportCalcTimeRangeReverse(context.Background(), form.StartAt, form.EndAt, config.Config.TopCorporationId)
	}
	if form.ReportType == 2 {
		go service.VehicleLineRoadReportCalcTimeRange(context.Background(), form.StartAt, form.EndAt, config.Config.TopCorporationId)
	}
	return response.Success(rsp, "数据已开始计算")
}
