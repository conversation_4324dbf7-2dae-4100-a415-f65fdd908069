package lineDriverReport

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	exportModel "app/org/scs/erpv2/api/model/export"
	"app/org/scs/erpv2/api/model/operation"
	protooetvehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	exportService "app/org/scs/erpv2/api/service/export"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/shopspring/decimal"
	"github.com/tealeg/xlsx"
	"strconv"
	"time"
)

type IrregularLineRequest struct {
	Ids            []int64
	CorporationIds []int64         `json:"CorporationIds"`
	LineIds        []int64         `json:"LineIds"`
	DriverName     string          `json:"DriverName"`
	License        string          `json:"License"`
	StartAt        model.LocalTime `json:"StartAt"`
	EndAt          model.LocalTime `json:"EndAt"`
	FileData       string          `json:"FileData"`
	IsExportFile   int64           `json:"IsExportFile"`
	LineAttrs      []int64         `json:"LineAttrs"`
}

type IrregularLineResponse struct {
	Items           []operation.IrregularLineReport
	ApprovalHandler service.ApprovalHandler
}

func (*OperationReport) IrregularLineReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	result, errorCode := irregularLineReport(ctx, auth.User(ctx).GetTopCorporationId(), auth.User(ctx).GetUserId(), req)
	if errorCode != response.SUCCESS {
		return response.Error(rsp, errorCode)
	}

	return response.Success(rsp, result)
}

func irregularLineReport(ctx context.Context, topCorporationId, userId int64, req *api.Request) (IrregularLineResponse, string) {
	var param IrregularLineRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return IrregularLineResponse{}, response.ParamsInvalid
	}
	userSetting := service.GetUserSetting(userId)
	isApproval := userSetting.OperationReportDataIsApproval

	if len(param.LineAttrs) > 0 {
		lineIds := service.GetLineIdsByLineAttr(topCorporationId, param.LineIds, param.LineAttrs, userId)
		if len(lineIds) == 0 {
			return IrregularLineResponse{}, response.SUCCESS
		}
		param.LineIds = lineIds
	}

	if len(param.CorporationIds) == 0 {
		param.CorporationIds = service.AuthCorporationIdProvider(ctx, []int64{})
	}

	reports := (&operation.IrregularLineReport{}).GetBy(isApproval, param.CorporationIds, param.LineIds, param.DriverName, param.License, time.Time(param.StartAt), time.Time(param.EndAt))

	var approvalHandler service.ApprovalHandler
	if isApproval == util.StatusForTrue {
		approvalHandler = service.GetApprovalHandler(param.CorporationIds, param.LineIds, []string{"irregular_line_report"}, time.Time(param.StartAt), time.Time(param.EndAt))
		approvalHandler.HasApproval = true
	}
	return IrregularLineResponse{
		Items:           reports,
		ApprovalHandler: approvalHandler,
	}, response.SUCCESS
}

func (op *OperationReport) IrregularLineReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param IrregularLineRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.IsExportFile == util.StatusForTrue {
		var startAt = time.Time(param.StartAt)
		var endAt = time.Time(param.EndAt)
		var fileName = fmt.Sprintf("定制线路明细表%s-%s.xlsx", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))
		paramByte, _ := json.Marshal(param)
		exportFileRecord, err := exportService.CreateExportFileRecord(auth.User(ctx).GetUserId(), fileName, (&operation.IrregularLineReport{}).TableName(), paramByte, startAt, endAt)
		if err != nil {
			return response.Error(rsp, response.FAIL)
		}
		go ExportIrregularLineReport(ctx, exportFileRecord, req)
		return response.Success(rsp, nil)
	}

	result, errorCode := irregularLineReport(ctx, auth.User(ctx).GetTopCorporationId(), auth.User(ctx).GetUserId(), req)
	if errorCode != response.SUCCESS {
		return response.Error(rsp, errorCode)
	}

	return response.Success(rsp, result)
}

func (*OperationReport) IrregularLineReportImport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param IrregularLineRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	user := auth.User(ctx).GetUser()
	corporation := rpc.GetCorporationById(ctx, user.CorporationId)
	if corporation == nil {
		return response.Error(rsp, response.NotFleetAccount)
	}
	if corporation.Type != util.CorporationTypeForFleet {
		return response.Error(rsp, response.NotFleetAccount)
	}
	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 0日期* 1线路 2线路编号 3车牌  4司机  5工号 6计划圈次  7实际圈次 8运营公里 9额外圈次 10自定义公里 11备注
	sheet := excelFile.Sheets[0]
	var failItems []map[string]interface{}
	var reports []operation.IrregularLineReport
	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)
		var report operation.IrregularLineReport
		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 0:
				reportAt, err := row.Cells[0].GetTime(false)
				if err != nil {
					failItems = append(failItems, map[string]interface{}{
						"RowIndex": i + 1,
						"Error":    "时间格式有误",
					})
				} else {
					report.ReportAt = model.LocalTime(reportAt)
				}
			case 1:
				report.LineName = row.Cells[1].String()
			case 2:
				report.LineCode = row.Cells[2].String()
			case 3:
				report.License = row.Cells[3].String()
			case 4:
				report.DriverName = row.Cells[4].String()
			case 5:
				report.DriverCode = row.Cells[5].String()
			case 6:
				if row.Cells[6].String() != "" {
					circle, err := row.Cells[6].Float()
					log.ErrorFields("row.Cells[6].Float() error", map[string]interface{}{"err": err})
					if err != nil {
						failItems = append(failItems, map[string]interface{}{
							"RowIndex": i + 1,
							"Error":    "计划圈次格式有误",
						})
					} else {
						report.PlanCircle = decimal.NewFromFloat(circle).Mul(decimal.NewFromFloat(10)).IntPart()
					}
				}
			case 7:
				if row.Cells[7].String() != "" {
					circle, err := row.Cells[7].Float()
					if err != nil {
						failItems = append(failItems, map[string]interface{}{
							"RowIndex": i + 1,
							"Error":    "实际圈次格式有误",
						})
					} else {
						report.ActualCircle = decimal.NewFromFloat(circle).Mul(decimal.NewFromFloat(10)).IntPart()
					}
				}
			case 8:
				if row.Cells[8].String() != "" {
					mileage, err := row.Cells[8].Float()
					if err != nil {
						failItems = append(failItems, map[string]interface{}{
							"RowIndex": i + 1,
							"Error":    "运营公里格式有误",
						})
					} else {
						if mileage == 0 {
							failItems = append(failItems, map[string]interface{}{
								"RowIndex": i + 1,
								"Error":    "运营公里不能为0",
							})
						} else {
							report.DoneMileage = decimal.NewFromFloat(mileage).Mul(decimal.NewFromFloat(1000)).IntPart()
						}
					}
				}
			case 9:
				if row.Cells[9].String() != "" {
					circle, err := row.Cells[9].Float()
					if err != nil {
						failItems = append(failItems, map[string]interface{}{
							"RowIndex": i + 1,
							"Error":    "额外圈次格式有误",
						})
					} else {
						report.ExtraCircle = decimal.NewFromFloat(circle).Mul(decimal.NewFromFloat(10)).IntPart()
					}
				}
			case 10:
				if row.Cells[10].String() != "" {
					mileage, err := row.Cells[10].Float()
					if err != nil {
						failItems = append(failItems, map[string]interface{}{
							"RowIndex": i + 1,
							"Error":    "自定义公里格式有误",
						})
					} else {
						report.CustomMileage = decimal.NewFromFloat(mileage).Mul(decimal.NewFromFloat(1000)).IntPart()
					}
				}
			case 11:
				report.More = row.Cells[11].String()
			}
		}

		reports = append(reports, report)
	}

	if len(failItems) > 0 {
		return response.Success(rsp, map[string]interface{}{
			"FailItems": failItems,
		})
	}

	for i := range reports {
		reports[i].TopCorporationId = user.TopCorporationId
		reports[i].OpUserId = user.Id
		reports[i].OpUserName = user.Name

		oetLine, corpIds := rpc.GetLineWithCode(ctx, user.TopCorporationId, reports[i].LineCode)
		if oetLine == nil {
			failItems = append(failItems, map[string]interface{}{
				"RowIndex": i + 2,
				"Error":    "线路不存在",
			})
		} else {
			if !util.IncludeInt64(corpIds, user.CorporationId) {
				failItems = append(failItems, map[string]interface{}{
					"RowIndex": i + 2,
					"Error":    "当前账号下无此线路，无权限导入",
				})
			}
			if oetLine.Name != reports[i].LineName {
				failItems = append(failItems, map[string]interface{}{
					"RowIndex": i + 2,
					"Error":    "线路与线路编号不一致",
				})
			} else {
				reports[i].LineId = oetLine.Id
				reports[i].LineName = oetLine.Name
				reports[i].LineCode = oetLine.Code
				reports[i].LineAttr = oetLine.LineAttribute
				reports[i].LineAttr = oetLine.LineAttribute

				reports[i].CorporationId = user.CorporationId
				if reports[i].CorporationId == 0 && len(corpIds) > 0 {
					reports[i].CorporationId = corpIds[0]
				}
				corp := rpc.GetCorporationDetailById(ctx, reports[i].CorporationId)
				if corp != nil {
					reports[i].CorporationName = corp.Item.Name
				}

				if reports[i].LineId > 0 && reports[i].CorporationId > 0 && !(time.Time(reports[i].ReportAt).IsZero()) {
					isExist := (&operation.OperationApproval{}).IsExistCrossApproval(reports[i].CorporationId, []string{"irregular_line_report"}, []string{strconv.Itoa(int(reports[i].LineId))}, time.Time(reports[i].ReportAt), time.Time(reports[i].ReportAt))

					if isExist {
						return response.Error(rsp, "BPM1003")
					}
				}
			}
		}

		if reports[i].License != "" {
			oetVehicle := rpc.GetVehicleWithLicense(ctx, &protooetvehicle.GetVehicleWithLicenseRequest{
				License:       reports[i].License,
				CorporationId: user.TopCorporationId,
			})
			if oetVehicle != nil {
				reports[i].VehicleId = oetVehicle.Id
			} else {
				failItems = append(failItems, map[string]interface{}{
					"RowIndex": i + 2,
					"Error":    "车辆不存在",
				})
			}
		}

		if reports[i].DriverCode != "" {
			oetStaff := rpc.GetStaffWithStaffId(ctx, user.TopCorporationId, reports[i].DriverCode)
			if oetStaff != nil {
				if oetStaff.Name != reports[i].DriverName {
					failItems = append(failItems, map[string]interface{}{
						"RowIndex": i + 2,
						"Error":    "司机名字和工号不一致",
					})
				} else {
					reports[i].DriverId = oetStaff.Id
					reports[i].DriverName = oetStaff.Name
				}
			} else {
				failItems = append(failItems, map[string]interface{}{
					"RowIndex": i + 2,
					"Error":    "司机不存在",
				})
			}
		}

		if reports[i].VehicleId == 0 && reports[i].DriverId == 0 {
			failItems = append(failItems, map[string]interface{}{
				"RowIndex": i + 2,
				"Error":    "车辆和司机至少有一项有值",
			})
		}
	}
	if len(failItems) > 0 {
		return response.Success(rsp, map[string]interface{}{
			"FailItems": failItems,
		})
	}

	for i := range reports {
		report := reports[i].FindByParam()
		if report.Id > 0 {
			reports[i].Id = report.Id
			err = reports[i].Update()
			if err != nil {
				failItems = append(failItems, map[string]interface{}{
					"RowIndex": i + 2,
					"Error":    "数据保存失败",
				})
			}
		} else {
			err = reports[i].Create()
			if err != nil {
				failItems = append(failItems, map[string]interface{}{
					"RowIndex": i + 2,
					"Error":    "数据保存失败",
				})
			}
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"FailItems": failItems,
	})
}

func (*OperationReport) IrregularLineReportDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param IrregularLineRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	err = (&operation.IrregularLineReport{}).Delete(param.Ids)
	if err != nil {
		return response.Error(rsp, response.DbDeleteFail)
	}
	return response.Success(rsp, nil)
}

func ExportIrregularLineReport(ctx context.Context, exportFile exportModel.ExportFile, req *api.Request) {
	result, errorCode := irregularLineReport(ctx, exportFile.TopCorporationId, exportFile.OpUserId, req)

	if errorCode != response.SUCCESS {
		exportFile.ErrorCode = errorCode
		err := exportFile.UpdateFail()
		log.ErrorFields("ExportIrregularLineReport exportFile.UpdateStatus error", map[string]interface{}{"err": err})
		return
	}

	err := exportService.ExportIrregularLine(result.Items, exportFile, result.ApprovalHandler)
	if err != nil {
		exportFile.ErrorCode = err.Error()
		err = exportFile.UpdateFail()
	} else {
		err = exportFile.UpdateSuccess()
	}
	log.ErrorFields("ExportIrregularLineReport exportFile.UpdateStatus error", map[string]interface{}{"err": err})
}
