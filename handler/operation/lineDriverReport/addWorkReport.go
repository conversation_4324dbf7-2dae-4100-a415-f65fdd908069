package lineDriverReport

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	exportModel "app/org/scs/erpv2/api/model/export"
	"app/org/scs/erpv2/api/model/operation"
	protooetline "app/org/scs/erpv2/api/proto/rpc/oetline"
	protostaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	protooetvehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	exportService "app/org/scs/erpv2/api/service/export"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/shopspring/decimal"
	"github.com/tealeg/xlsx"
	"strconv"
	"time"
)

type AddWorkRequest struct {
	FileData       string          `json:"FileData"`
	StartAt        model.LocalTime `json:"StartAt"`
	EndAt          model.LocalTime `json:"EndAt"`
	CorporationIds []int64         `json:"CorporationIds"`
	LineIds        []int64         `json:"LineIds"`
	VehicleIds     []int64         `json:"VehicleIds"`
	DriverIds      []int64         `json:"DriverIds"`
	Ids            []int64         `json:"Ids"`
	IsExportFile   int64           `json:"IsExportFile"`
	model.Paginator
}

type OutFrequencyAddWorkReportResponse struct {
	TotalCount      int64 `json:"TotalCount"`
	Items           []operation.OutFrequencyAddWorkReport
	Sum             operation.OutFrequencyAddWorkReportSumField
	ApprovalHandler service.ApprovalHandler
}

func (*OperationReport) OutFrequencyAddWorkReportImport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AddWorkRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	user := auth.User(ctx).GetUser()
	corporation := rpc.GetCorporationById(ctx, user.CorporationId)
	if corporation == nil {
		return response.Error(rsp, response.NotFleetAccount)
	}
	if corporation.Type != util.CorporationTypeForFleet {
		return response.Error(rsp, response.NotFleetAccount)
	}

	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var lineMap = make(map[string]*protooetline.OetLineItem)
	var staffMap = make(map[string]*protostaff.OetStaffItem)
	// 0日期* 1司机 2工号  3线路  4车牌 5加班类型  6区内外 7运营天数 8运营时长 9运营公里 10备注
	sheet := excelFile.Sheets[0]
	var failItems []map[string]interface{}
	var reports []operation.OutFrequencyAddWorkReport
	for i, row := range sheet.Rows {
		if i == 0 {
			continue
		}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)
		var report = operation.OutFrequencyAddWorkReport{
			TopCorporationId: user.TopCorporationId,
			CorporationId:    user.CorporationId,
			CorporationName:  user.CorporationName,
		}

		for cellIndex, _ := range row.Cells {
			switch cellIndex {
			case 0:
				reportAt, err := row.Cells[0].GetTime(false)
				log.ErrorFields("row.Cells[0].GetTime(false)", map[string]interface{}{"err": err})
				if err != nil {
					failItems = append(failItems, map[string]interface{}{
						"RowIndex": i + 1,
						"Error":    "时间格式有误",
					})
				} else {
					report.ReportAt = model.LocalTime(reportAt)
				}
			case 1:
				report.DriverName = row.Cells[1].String()
			case 2:
				report.JobNumber = row.Cells[2].String()
				//获取司机
				if report.JobNumber == "" {
					failItems = append(failItems, map[string]interface{}{
						"RowIndex": i + 1,
						"Error":    "司机工号不存在",
					})
				} else {
					if staff, ok := staffMap[report.JobNumber]; ok {
						if report.DriverName != staff.Name {
							failItems = append(failItems, map[string]interface{}{
								"RowIndex": i + 1,
								"Error":    "司机工号和司机姓名不一致",
							})
						} else {
							report.DriverId = staff.Id
						}
					} else {
						oetStaff := rpc.GetStaffWithStaffId(ctx, user.TopCorporationId, report.JobNumber)
						if oetStaff != nil {
							if report.DriverName != oetStaff.Name {
								failItems = append(failItems, map[string]interface{}{
									"RowIndex": i + 1,
									"Error":    "司机工号和司机姓名不一致",
								})
							} else {
								report.DriverId = oetStaff.Id
							}
							staffMap[report.JobNumber] = oetStaff
						} else {
							failItems = append(failItems, map[string]interface{}{
								"RowIndex": i + 1,
								"Error":    "司机工号对应的司机不存在",
							})
						}
					}
				}
			case 3:
				report.LineName = row.Cells[3].String()
				log.ErrorFields("row.Cells[3].String()", map[string]interface{}{"report": report})
				if line, ok := lineMap[report.LineName]; ok {
					report.LineId = line.Id
				} else {
					oetLine := rpc.GetLineWithName(ctx, user.TopCorporationId, report.LineName)
					if oetLine != nil {
						if !util.IncludeInt64(oetLine.SubCorporationIds, user.CorporationId) {
							failItems = append(failItems, map[string]interface{}{
								"RowIndex": i + 1,
								"Error":    "当前账号下无此线路，无权限导入",
							})
						}
						report.LineId = oetLine.Id
						lineMap[report.LineName] = oetLine
					} else {
						failItems = append(failItems, map[string]interface{}{
							"RowIndex": i + 1,
							"Error":    "线路不存在",
						})
					}
				}
				if report.LineId > 0 && report.CorporationId > 0 && !(time.Time(report.ReportAt).IsZero()) {
					isExist := (&operation.OperationApproval{}).IsExistCrossApproval(report.CorporationId, []string{"out_frequency_add_work_report"}, []string{strconv.Itoa(int(report.LineId))}, time.Time(report.ReportAt), time.Time(report.ReportAt))

					if isExist {
						return response.Error(rsp, "BPM1003")
					}
				}
			case 4:
				report.License = row.Cells[4].String()
				if report.License != "" {
					oetVehicle := rpc.GetVehicleWithLicense(ctx, &protooetvehicle.GetVehicleWithLicenseRequest{
						License:       report.License,
						CorporationId: user.TopCorporationId,
					})

					if oetVehicle == nil {
						failItems = append(failItems, map[string]interface{}{
							"RowIndex": i + 1,
							"Error":    "车牌号不存在",
						})
					} else {
						report.VehicleId = oetVehicle.Id
					}
				}
			case 5:
				addWorkType, exist := operation.AddWorkTypeMap[row.Cells[5].String()]
				if !exist {
					addWorkType = operation.OtherType
				}
				if row.Cells[5].String() == "普通场站年审" {
					report.AnnualReviewPark = 1
				}
				if row.Cells[5].String() == "特殊场站年审" {
					report.AnnualReviewPark = 2
				}
				report.AddWorkType = addWorkType
			case 6:
				report.AreaScene = row.Cells[6].String()
			case 7:
				if row.Cells[7].String() != "" {
					dayCount, err := row.Cells[7].Float()
					log.ErrorFields("row.Cells[7].Float()", map[string]interface{}{"err": err, "dayCount": dayCount})
					if err != nil {
						failItems = append(failItems, map[string]interface{}{
							"RowIndex": i + 1,
							"Error":    "运营天数格式有误",
						})
					} else {
						report.DayCount = decimal.NewFromFloat(dayCount).Mul(decimal.NewFromFloat(10)).IntPart()
					}
				}
			case 8:
				if row.Cells[8].String() != "" {
					timeLength, err := row.Cells[8].Float()
					log.ErrorFields("row.Cells[8].Float()", map[string]interface{}{"err": err})
					if err != nil {
						failItems = append(failItems, map[string]interface{}{
							"RowIndex": i + 1,
							"Error":    "运营时长格式有误",
						})
					} else {
						report.TimeLength = decimal.NewFromFloat(timeLength).Mul(decimal.NewFromFloat(3600)).IntPart()
					}
				}
			case 9:
				if row.Cells[9].String() != "" {
					mileage, err := row.Cells[9].Float()
					log.ErrorFields("row.Cells[9].Float()", map[string]interface{}{"err": err})
					if err != nil {
						failItems = append(failItems, map[string]interface{}{
							"RowIndex": i + 1,
							"Error":    "运营公里格式有误",
						})
					} else {
						report.Mileage = decimal.NewFromFloat(mileage).Mul(decimal.NewFromFloat(1000)).IntPart()
					}
				}
			case 10:
				report.More = row.Cells[10].String()
			}
		}
		if report.Mileage != 0 && (report.License == "" || report.VehicleId == 0) {
			failItems = append(failItems, map[string]interface{}{
				"RowIndex": i + 1,
				"Error":    "车牌号未录入",
			})
		}
		reports = append(reports, report)
	}

	if len(failItems) > 0 {
		return response.Success(rsp, map[string]interface{}{
			"FailItems": failItems,
		})
	}

	tx := model.DB().Begin()
	for i := range reports {
		oldReport := (&operation.OutFrequencyAddWorkReport{}).GetByLineDriverVehicleWorkType(reports[i].CorporationId, reports[i].LineId, reports[i].VehicleId, reports[i].DriverId, reports[i].AddWorkType, time.Time(reports[i].ReportAt))
		reports[i].OpUserName = user.Name
		reports[i].OpUserId = user.Id
		if oldReport.Id > 0 {
			oldReport.TimeLength = reports[i].TimeLength
			oldReport.Mileage = reports[i].Mileage
			oldReport.DayCount = reports[i].DayCount
			oldReport.More = reports[i].More
			err = oldReport.TransactionUpdate(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("TransactionUpdate err", map[string]interface{}{"err": err})
				return response.Error(rsp, response.FAIL)
			}
		} else {
			err = reports[i].Create(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("Create err", map[string]interface{}{"err": err})
				return response.Error(rsp, response.FAIL)
			}
		}
	}

	tx.Commit()

	return response.Success(rsp, map[string]interface{}{
		"FailItems": failItems,
	})
}

func (*OperationReport) OutFrequencyAddWorkReportList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	result, errorCode := OutFrequencyAddWorkReport(auth.User(ctx).GetUserId(), req)
	if errorCode != response.SUCCESS {
		return response.Error(rsp, errorCode)
	}

	return response.Success(rsp, result)
}

func OutFrequencyAddWorkReport(userId int64, req *api.Request) (OutFrequencyAddWorkReportResponse, string) {
	var param AddWorkRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return OutFrequencyAddWorkReportResponse{}, response.ParamsInvalid
	}
	userSetting := service.GetUserSetting(userId)
	isApproval := userSetting.OperationReportDataIsApproval

	reports, count := (&operation.OutFrequencyAddWorkReport{}).GetBy(isApproval, param.CorporationIds, param.LineIds, param.VehicleIds, param.DriverIds, time.Time(param.StartAt), time.Time(param.EndAt), param.Paginator)

	var approvalHandler service.ApprovalHandler
	if isApproval == util.StatusForTrue {
		approvalHandler = service.GetApprovalHandler(param.CorporationIds, param.LineIds, []string{"out_frequency_add_work_report"}, time.Time(param.StartAt), time.Time(param.EndAt))
		approvalHandler.HasApproval = true
	}
	return OutFrequencyAddWorkReportResponse{
		TotalCount:      count,
		Items:           reports,
		Sum:             (&operation.OutFrequencyAddWorkReport{}).GetSumBy(isApproval, param.CorporationIds, param.LineIds, param.VehicleIds, param.DriverIds, time.Time(param.StartAt), time.Time(param.EndAt)),
		ApprovalHandler: approvalHandler,
	}, response.SUCCESS
}

func (*OperationReport) OutFrequencyAddWorkReportListExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AddWorkRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.IsExportFile == util.StatusForTrue {
		var startAt = time.Time(param.StartAt)
		var endAt = time.Time(param.EndAt)
		var fileName = fmt.Sprintf("班制外加班明细表%s-%s.xlsx", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))
		paramByte, _ := json.Marshal(param)
		exportFileRecord, err := exportService.CreateExportFileRecord(auth.User(ctx).GetUserId(), fileName, (&operation.OutFrequencyAddWorkReport{}).TableName(), paramByte, startAt, endAt)
		if err != nil {
			return response.Error(rsp, response.FAIL)
		}
		go ExportOutFrequencyAddWorkReport(exportFileRecord, req)
		return response.Success(rsp, nil)
	}

	result, errorCode := OutFrequencyAddWorkReport(auth.User(ctx).GetUserId(), req)
	if errorCode != response.SUCCESS {
		return response.Error(rsp, errorCode)
	}

	return response.Success(rsp, result)
}

func (*OperationReport) OutFrequencyAddWorkSumReportList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	result, errorCode := OutFrequencyAddWorkSumReport(auth.User(ctx).GetUserId(), req)
	if errorCode != response.SUCCESS {
		return response.Error(rsp, errorCode)
	}

	return response.Success(rsp, result)
}

type OutFrequencyAddWorkSumReportResponse struct {
	TotalCount int64 `json:"TotalCount"`
	Items      []operation.OutFrequencyAddWorkSumReport
}

func OutFrequencyAddWorkSumReport(userId int64, req *api.Request) (OutFrequencyAddWorkSumReportResponse, string) {
	var param AddWorkRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return OutFrequencyAddWorkSumReportResponse{}, response.ParamsInvalid
	}
	userSetting := service.GetUserSetting(userId)
	isApproval := userSetting.OperationReportDataIsApproval
	reports, count := (&operation.OutFrequencyAddWorkReport{}).GetSumGroupBy(isApproval, param.CorporationIds, param.LineIds, param.VehicleIds, param.DriverIds, time.Time(param.StartAt), time.Time(param.EndAt), param.Paginator)
	for i := range reports {
		reports[i].LineName, reports[i].TimeLength, reports[i].Mileage, reports[i].More = (&operation.OutFrequencyAddWorkReport{}).GetAllLineTimeLengthMileageMore(isApproval, reports[i].DriverId, reports[i].AddWorkType, time.Time(param.StartAt), time.Time(param.EndAt))
	}

	return OutFrequencyAddWorkSumReportResponse{
		Items:      reports,
		TotalCount: count,
	}, response.SUCCESS
}

func (*OperationReport) OutFrequencyAddWorkSumReportListExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AddWorkRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.IsExportFile == util.StatusForTrue {
		var startAt = time.Time(param.StartAt)
		var endAt = time.Time(param.EndAt)
		var fileName = fmt.Sprintf("班制外加班汇总表%s-%s.xlsx", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))
		paramByte, _ := json.Marshal(param)
		exportFileRecord, err := exportService.CreateExportFileRecord(auth.User(ctx).GetUserId(), fileName, (&operation.OutFrequencyAddWorkReport{}).TableName(), paramByte, startAt, endAt)
		if err != nil {
			return response.Error(rsp, response.FAIL)
		}
		go ExportOutFrequencyAddWorkSumReport(exportFileRecord, req)
		return response.Success(rsp, nil)
	}

	result, errorCode := OutFrequencyAddWorkSumReport(auth.User(ctx).GetUserId(), req)
	if errorCode != response.SUCCESS {
		return response.Error(rsp, errorCode)
	}

	return response.Success(rsp, result)
}

func (*OperationReport) OutFrequencyAddWorkReportDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AddWorkRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(param.Ids) == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	err = (&operation.OutFrequencyAddWorkReport{}).BatchDelete(param.Ids)

	if err != nil {
		log.ErrorFields("BatchDelete err", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}

func ExportOutFrequencyAddWorkReport(exportFile exportModel.ExportFile, req *api.Request) {
	result, errorCode := OutFrequencyAddWorkReport(exportFile.OpUserId, req)

	if errorCode != response.SUCCESS {
		exportFile.ErrorCode = errorCode
		err := exportFile.UpdateFail()
		log.ErrorFields("ExportOutFrequencyAddWorkReport exportFile.UpdateStatus error", map[string]interface{}{"err": err})
		return
	}

	err := exportService.ExportOutFrequencyAddWorkReport(result.Items, exportFile, result.ApprovalHandler)
	if err != nil {
		exportFile.ErrorCode = err.Error()
		err = exportFile.UpdateFail()
	} else {
		err = exportFile.UpdateSuccess()
	}
	log.ErrorFields("ExportOutFrequencyAddWorkReport exportFile.UpdateStatus error", map[string]interface{}{"err": err})
}

func ExportOutFrequencyAddWorkSumReport(exportFile exportModel.ExportFile, req *api.Request) {
	result, errorCode := OutFrequencyAddWorkSumReport(exportFile.OpUserId, req)

	if errorCode != response.SUCCESS {
		exportFile.ErrorCode = errorCode
		err := exportFile.UpdateFail()
		log.ErrorFields("ExportOutFrequencyAddWorkSumReport exportFile.UpdateStatus error", map[string]interface{}{"err": err})
		return
	}

	err := exportService.ExportOutFrequencyAddWorkSumReport(result.Items, exportFile)
	if err != nil {
		exportFile.ErrorCode = err.Error()
		err = exportFile.UpdateFail()
	} else {
		err = exportFile.UpdateSuccess()
	}
	log.ErrorFields("ExportOutFrequencyAddWorkSumReport exportFile.UpdateStatus error", map[string]interface{}{"err": err})
}
