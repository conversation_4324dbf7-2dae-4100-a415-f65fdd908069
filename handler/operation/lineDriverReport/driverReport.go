package lineDriverReport

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	exportModel "app/org/scs/erpv2/api/model/export"
	operationModel "app/org/scs/erpv2/api/model/operation"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	exportService "app/org/scs/erpv2/api/service/export"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/shopspring/decimal"
	"strconv"
	"strings"
	"time"
)

type LineDriverWorkReportItem struct {
	operationModel.LineDriverWorkReportData
	LineWorkDay                             int64   `json:"LineWorkDay"`                             //汇总线路天数 单位：天数*10
	InFrequencyTypeCircleWorkTimeLength     int64   `json:"InFrequencyTypeCircleWorkTimeLength"`     //班制内圈次岗上时长 单位：秒
	InFrequencyTypeCircleNotWorkTimeLength  int64   `json:"InFrequencyTypeCircleNotWorkTimeLength"`  //班制内圈次岗下时长 单位：秒
	OutFrequencyTypeCircleWorkTimeLength    int64   `json:"OutFrequencyTypeCircleWorkTimeLength"`    //班制外圈次岗上时长 单位：秒
	OutFrequencyTypeCircleNotWorkTimeLength int64   `json:"OutFrequencyTypeCircleNotWorkTimeLength"` //班制外圈次岗下时长 单位：秒
	PlanWorkDay                             int64   `json:"PlanWorkDay"`                             //计划出勤天数
	PlanWorkRate                            float64 `json:"PlanWorkRate"`                            //班次执行率 单位：%
	PlanTotalCircle                         int64   `json:"PlanTotalCircle"`                         //计划排班总圈数 单位：圈数*10
	InPlanDoneCircle                        int64   `json:"InPlanDoneCircle"`                        //排班内完成圈次
	OutPlanDoneCircle                       int64   `json:"OutPlanDoneCircle"`                       //排班外完成圈次
	WorkAndNotWorkTimeLength
	JobNumber string `json:"JobNumber"` //司机工号
}

func (op *OperationReport) LineDriverWorkReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	result, errCode := lineDriverWorkReport(req, auth.User(ctx).GetUserId())
	if errCode != response.SUCCESS {
		return response.Error(rsp, errCode)
	}
	return response.Success(rsp, result)
}

type LineDriverWorkReportRsp struct {
	Items           []LineDriverWorkReportItem `json:"Items"`
	TotalCount      int64                      `json:"TotalCount"`
	SumItem         LineDriverWorkReportItem   `json:"SumItem"`
	ApprovalHandler service.ApprovalHandler    `json:"ApprovalHandler"`
}

func lineDriverWorkReport(req *api.Request, userId int64) (LineDriverWorkReportRsp, string) {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return LineDriverWorkReportRsp{}, response.ParamsInvalid
	}
	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)

	if startAt.Unix() > endAt.Unix() {
		return LineDriverWorkReportRsp{}, response.ParamsInvalid
	}

	userSetting := service.GetUserSetting(userId)
	isApproval := userSetting.OperationReportDataIsApproval

	//notExistDays := GetDontHaveDataDays(param.CorporationId, param.LineId, startAt, endAt)
	//获取线路运营过的司机汇总数据
	driverWorkReportItems, count, sumItem := (&operationModel.LineVehicleMileageReport{}).GetDriverRunReportDataGroupByLineDriver(isApproval, param.CorporationIds, param.LineIds, param.DriverIds, startAt, endAt, param.Sortable, param.Paginator)

	fmt.Printf("driverWorkReportItems=========%+v \n", driverWorkReportItems)
	var totalSumItem LineDriverWorkReportItem
	totalSumItem.LineDriverWorkReportData = sumItem

	driverVehicleLength := make(map[int64]int64)
	var results []LineDriverWorkReportItem
	for i := range driverWorkReportItems {
		var reportItem = LineDriverWorkReportItem{
			LineDriverWorkReportData: driverWorkReportItems[i],
		}
		oetDriver := rpc.GetStaffWithId(context.TODO(), reportItem.DriverId)
		if oetDriver != nil {
			reportItem.JobNumber = oetDriver.StaffId
		}

		//获取和所选时间有交叉的配置项
		settings := (&operationModel.OperationLineSetting{}).GetTimeCrossSetting([]int64{driverWorkReportItems[i].CorporationId}, driverWorkReportItems[i].LineId, startAt, endAt)
		reportItem.WorkAndNotWorkTimeLength = ParseTimeLengthByTimeRange(settings, startAt, endAt)

		reportItem.LineWorkDay = (&operationModel.LineVehicleMileageReport{}).GetDriverRunCountDataGroupByReportAt(isApproval, []int64{driverWorkReportItems[i].CorporationId}, 0, 0, driverWorkReportItems[i].DriverId, 0, startAt, endAt)
		totalSumItem.LineWorkDay += reportItem.LineWorkDay
		lineWorkDay := reportItem.LineWorkDay / 10

		if lineWorkDay <= reportItem.FrequencyDay {
			reportItem.OutFrequencyTypeCircleWorkTimeLength = reportItem.AddWorkWorkTimeLength
			reportItem.OutFrequencyTypeCircleNotWorkTimeLength = reportItem.AddWorkNotWorkTimeLength
			reportItem.InFrequencyTypeCircleWorkTimeLength = reportItem.TotalWorkTimeLength - reportItem.OutFrequencyTypeCircleWorkTimeLength
			reportItem.InFrequencyTypeCircleNotWorkTimeLength = reportItem.TotalNotWorkTimeLength - reportItem.OutFrequencyTypeCircleNotWorkTimeLength
		} else {
			reportItem.OutFrequencyTypeCircleWorkTimeLength = (((reportItem.TotalWorkTimeLength - reportItem.AddWorkWorkTimeLength) / lineWorkDay) * (lineWorkDay - reportItem.FrequencyDay)) + reportItem.AddWorkWorkTimeLength
			reportItem.OutFrequencyTypeCircleNotWorkTimeLength = (((reportItem.TotalNotWorkTimeLength - reportItem.AddWorkNotWorkTimeLength) / lineWorkDay) * (lineWorkDay - reportItem.FrequencyDay)) + reportItem.AddWorkNotWorkTimeLength
			reportItem.InFrequencyTypeCircleWorkTimeLength = reportItem.TotalWorkTimeLength - reportItem.OutFrequencyTypeCircleWorkTimeLength
			reportItem.InFrequencyTypeCircleNotWorkTimeLength = reportItem.TotalNotWorkTimeLength - reportItem.OutFrequencyTypeCircleNotWorkTimeLength
		}

		totalSumItem.InFrequencyTypeCircleWorkTimeLength += reportItem.InFrequencyTypeCircleWorkTimeLength
		totalSumItem.InFrequencyTypeCircleNotWorkTimeLength += reportItem.InFrequencyTypeCircleNotWorkTimeLength
		totalSumItem.OutFrequencyTypeCircleWorkTimeLength += reportItem.OutFrequencyTypeCircleWorkTimeLength
		totalSumItem.OutFrequencyTypeCircleNotWorkTimeLength += reportItem.OutFrequencyTypeCircleNotWorkTimeLength

		if _, ok := driverVehicleLength[driverWorkReportItems[i].DriverId]; ok {
			reportItem.VehicleLength = driverVehicleLength[driverWorkReportItems[i].DriverId]
		} else {
			driverRunVehicles := (&operationModel.LineVehicleMileageReport{}).GetLineDriverRunVehicleDay(driverWorkReportItems[i].DriverId, startAt, endAt)
			if len(driverRunVehicles) > 0 {
				if driverRunVehicles[0].VehicleLength == 0 {
					vehicle := rpc.GetVehicleWithId(context.TODO(), driverRunVehicles[0].VehicleId)
					if vehicle != nil {
						vehicleSize := strings.Split(vehicle.CarBodySize, "*")
						if len(vehicleSize) > 0 {
							reportItem.VehicleLength, _ = strconv.ParseInt(strings.Trim(vehicleSize[0], " "), 10, 64)
						}
					}
				} else {
					reportItem.VehicleLength = driverRunVehicles[0].VehicleLength
				}
				driverVehicleLength[driverWorkReportItems[i].DriverId] = reportItem.VehicleLength
			}
		}

		reportItem.PlanWorkDay = (&operationModel.PlanScheduleReport{}).GetDriverFrequencyIndexCount(driverWorkReportItems[i].CorporationId, driverWorkReportItems[i].LineId, driverWorkReportItems[i].DriverId, startAt, endAt)

		//年休假、疗休养、病假日期
		//notWorkDays := (&operationModel.LineVehicleMileageReport{}).GetLineDriverNotWorkReportAt(driverWorkReportItems[i].CorporationId, driverWorkReportItems[i].LineId, driverWorkReportItems[i].DriverId, startAt, endAt)
		//从请假列表里获取时间段内请假全天的日期和请假半天的日期
		exceptLeaveTypes := []int64{util.LeaveTypeForCasualLeave, util.LeaveTypeForWaitWork, util.LeaveTypeForStopWork}
		oneDayLeave, _ := service.GetDriverLeaveReportAtByRange(driverWorkReportItems[i].DriverId, startAt, endAt, exceptLeaveTypes)

		reportItem.PlanTotalCircle = (&operationModel.PlanScheduleReport{}).GetDriverSumCircleCount(driverWorkReportItems[i].CorporationId, driverWorkReportItems[i].LineId, driverWorkReportItems[i].DriverId, startAt, endAt, oneDayLeave)

		//查询日期范围内没有排班的日期
		reportAtCircleCount := (&operationModel.PlanScheduleReport{}).GetReportAtPlanCircleByDriver(driverWorkReportItems[i].DriverId, driverWorkReportItems[i].LineId, driverWorkReportItems[i].CorporationId, startAt, endAt)
		for i := range reportAtCircleCount {
			if reportAtCircleCount[i].PlanCircle == 0 {
				oneDayLeave = append(oneDayLeave, reportAtCircleCount[i].ReportAt.ToTime().Format(model.DateFormat))
			}
		}

		notWorkCircles := (&operationModel.LineVehicleMileageReport{}).GetLineDriverNotWorkCircle(driverWorkReportItems[i].CorporationId, driverWorkReportItems[i].LineId, driverWorkReportItems[i].DriverId, startAt, endAt, oneDayLeave)
		reportItem.PlanTotalCircle = reportItem.PlanTotalCircle - notWorkCircles
		if reportItem.PlanTotalCircle < 0 {
			reportItem.PlanTotalCircle = 0
		}
		//计算排班内完成圈次和排班外完成圈次
		reportItem.InPlanDoneCircle, reportItem.OutPlanDoneCircle = service.CalcInAndOutPlanDoneCircle(isApproval, startAt, endAt, driverWorkReportItems[i].DriverId, driverWorkReportItems[i].CorporationId, driverWorkReportItems[i].LineId)

		if reportItem.PlanTotalCircle > 0 {
			inPlanDoneCircle := float64(reportItem.InPlanDoneCircle) / 10
			planTotalCircle := float64(reportItem.PlanTotalCircle) / 10
			//reportItem.PlanWorkRate = int64((inPlanDoneCircle / planTotalCircle) * 100)
			reportItem.PlanWorkRate = decimal.NewFromFloat(inPlanDoneCircle).Div(decimal.NewFromFloat(planTotalCircle)).Mul(decimal.NewFromInt(100)).Round(2).InexactFloat64()
		}

		totalSumItem.PlanWorkDay += reportItem.PlanWorkDay
		totalSumItem.PlanTotalCircle += reportItem.PlanTotalCircle
		totalSumItem.InPlanDoneCircle += reportItem.InPlanDoneCircle
		totalSumItem.OutPlanDoneCircle += reportItem.OutPlanDoneCircle

		if reportItem.DriverId > 0 {
			//查询司机的主运营线路
			mainRunLine := service.GetDriverMainRunLineInfo(reportItem.DriverId, startAt, endAt)
			reportItem.MainRunLineId, reportItem.MainRunLineName = mainRunLine.LineId, mainRunLine.LineName
			//查询司机的归属线路
			hasLineNames := (&operationModel.LineVehicleMileageReport{}).GetDriverHasLines(reportItem.DriverId, startAt, endAt)
			if len(hasLineNames) > 0 {
				reportItem.DriverHasLineName = strings.Join(hasLineNames, ",")
			}
		}

		results = append(results, reportItem)
	}

	var approvalHandler service.ApprovalHandler
	if isApproval == util.StatusForTrue {
		approvalHandler = service.GetApprovalHandler(param.CorporationIds, param.LineIds, []string{"line_vehicle_operation_report", "driver_work_report"}, startAt, endAt)
		approvalHandler.HasApproval = true
	}

	return LineDriverWorkReportRsp{
		Items:           results,
		TotalCount:      count,
		SumItem:         totalSumItem,
		ApprovalHandler: approvalHandler,
	}, response.SUCCESS

}

func (*OperationReport) LineDriverWorkReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	//异步导出到下载中心
	if param.IsExportFile == util.StatusForTrue {
		var startAt = time.Time(param.StartAt)
		var endAt = time.Time(param.EndAt)
		paramByte, _ := json.Marshal(param)
		var str string
		if param.ExportGroup == "line" {
			str = "分线路导出"
		} else {
			str = "整表导出"
		}
		var fileName = fmt.Sprintf("司机出勤表（按线路）_%s_%s_%s.xlsx", str, startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))

		exportFileRecord, err := exportService.CreateExportFileRecord(auth.User(ctx).GetUserId(), fileName, (&operationModel.LineVehicleMileageReport{}).LineDriverWorkReportTableName(), paramByte, startAt, endAt)
		if err != nil {
			return response.Error(rsp, response.FAIL)
		}
		go ExportLineDriverWork(exportFileRecord, req, param, "line")
		return response.Success(rsp, nil)
	}

	result, errCode := lineDriverWorkReport(req, auth.User(ctx).GetUserId())
	if errCode != response.SUCCESS {
		return response.Error(rsp, errCode)
	}
	return response.Success(rsp, result)
}

func (*OperationReport) LineDriverWorkReportByDriver(ctx context.Context, req *api.Request, rsp *api.Response) error {
	result, errCode := lineDriverWorkReportByDriver(req, auth.User(ctx).GetUserId())
	if errCode != response.SUCCESS {
		return response.Error(rsp, errCode)
	}
	return response.Success(rsp, result)
}

func (*OperationReport) LineDriverWorkReportByDriverExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	//异步导出到下载中心
	if param.IsExportFile == util.StatusForTrue {
		var startAt = time.Time(param.StartAt)
		var endAt = time.Time(param.EndAt)
		paramByte, _ := json.Marshal(param)
		var str string
		if param.ExportGroup == "line" {
			str = "分线路导出"
		} else {
			str = "整表导出"
		}
		var fileName = fmt.Sprintf("司机出勤表（按司机）_%s_%s_%s.xlsx", str, startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))

		exportFileRecord, err := exportService.CreateExportFileRecord(auth.User(ctx).GetUserId(), fileName, (&operationModel.LineVehicleMileageReport{}).LineDriverWorkReportTableName(), paramByte, startAt, endAt)
		if err != nil {
			return response.Error(rsp, response.FAIL)
		}
		go ExportLineDriverWork(exportFileRecord, req, param, "driver")
		return response.Success(rsp, nil)
	}

	result, errCode := lineDriverWorkReport(req, auth.User(ctx).GetUserId())
	if errCode != response.SUCCESS {
		return response.Error(rsp, errCode)
	}
	return response.Success(rsp, result)
}

func lineDriverWorkReportByDriver(req *api.Request, userId int64) (LineDriverWorkReportRsp, string) {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return LineDriverWorkReportRsp{}, response.ParamsInvalid
	}
	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)

	if startAt.Unix() > endAt.Unix() {
		return LineDriverWorkReportRsp{}, response.ParamsInvalid
	}

	userSetting := service.GetUserSetting(userId)
	isApproval := userSetting.OperationReportDataIsApproval

	driverIds := param.DriverIds
	var driverMainRunLine = make(map[int64]operationModel.DriverLineWorkDayCountItem)
	//获取运营线路下主运营的司机
	if len(param.LineIds) > 0 {
		driverIds, driverMainRunLine = service.GetLinesMainRunDriverIds(param.CorporationIds, param.LineIds, startAt, endAt)
		if len(param.DriverIds) > 0 {
			var ids []int64
			for i := range param.DriverIds {
				if _, ok := driverMainRunLine[param.DriverIds[i]]; ok {
					ids = append(ids, param.DriverIds[i])
				}
			}
			driverIds = ids
		}
	}

	if len(driverIds) == 0 {
		return LineDriverWorkReportRsp{
			TotalCount:      0,
			Items:           nil,
			SumItem:         LineDriverWorkReportItem{},
			ApprovalHandler: service.ApprovalHandler{},
		}, response.SUCCESS
	}

	//获取线路运营过的司机汇总数据
	driverWorkReportItems, count, sumItem := (&operationModel.LineVehicleMileageReport{}).GetDriverRunReportDataGroupByDriver(isApproval, driverIds, startAt, endAt, param.Sortable, param.Paginator)

	fmt.Printf("driverWorkReportItems=========%+v \n", driverWorkReportItems)
	var totalSumItem LineDriverWorkReportItem
	totalSumItem.LineDriverWorkReportData = sumItem

	driverVehicleLength := make(map[int64]int64)
	var results []LineDriverWorkReportItem
	for i := range driverWorkReportItems {
		var reportItem = LineDriverWorkReportItem{
			LineDriverWorkReportData: driverWorkReportItems[i],
		}
		oetDriver := rpc.GetStaffWithId(context.TODO(), driverWorkReportItems[i].DriverId)
		if oetDriver != nil {
			reportItem.JobNumber = oetDriver.StaffId
		}

		//判断司机的主营线路
		if _, ok := driverMainRunLine[reportItem.DriverId]; ok {
			mainRunLine := driverMainRunLine[reportItem.DriverId]
			reportItem.MainRunLineId, reportItem.MainRunLineName = mainRunLine.LineId, mainRunLine.LineName
			reportItem.CorporationId, reportItem.CorporationName = mainRunLine.CorporationId, mainRunLine.CorporationName
		} else {
			mainRunLine := service.GetDriverMainRunLineInfo(reportItem.DriverId, startAt, endAt)
			reportItem.MainRunLineId, reportItem.MainRunLineName = mainRunLine.LineId, mainRunLine.LineName
			reportItem.CorporationId, reportItem.CorporationName = mainRunLine.CorporationId, mainRunLine.CorporationName
		}

		//获取和所选时间有交叉的配置项
		settings := (&operationModel.OperationLineSetting{}).GetTimeCrossSetting([]int64{reportItem.CorporationId}, reportItem.MainRunLineId, startAt, endAt)
		reportItem.WorkAndNotWorkTimeLength = ParseTimeLengthByTimeRange(settings, startAt, endAt)

		reportItem.LineWorkDay = (&operationModel.LineVehicleMileageReport{}).GetDriverRunCountDataGroupByReportAt(isApproval, nil, 0, 0, driverWorkReportItems[i].DriverId, 0, startAt, endAt)
		totalSumItem.LineWorkDay += reportItem.LineWorkDay
		lineWorkDay := reportItem.LineWorkDay / 10

		if lineWorkDay <= reportItem.FrequencyDay {
			reportItem.OutFrequencyTypeCircleWorkTimeLength = reportItem.AddWorkWorkTimeLength
			reportItem.OutFrequencyTypeCircleNotWorkTimeLength = reportItem.AddWorkNotWorkTimeLength
			reportItem.InFrequencyTypeCircleWorkTimeLength = reportItem.TotalWorkTimeLength - reportItem.OutFrequencyTypeCircleWorkTimeLength
			reportItem.InFrequencyTypeCircleNotWorkTimeLength = reportItem.TotalNotWorkTimeLength - reportItem.OutFrequencyTypeCircleNotWorkTimeLength
		} else {
			reportItem.OutFrequencyTypeCircleWorkTimeLength = (((reportItem.TotalWorkTimeLength - reportItem.AddWorkWorkTimeLength) / lineWorkDay) * (lineWorkDay - reportItem.FrequencyDay)) + reportItem.AddWorkWorkTimeLength
			reportItem.OutFrequencyTypeCircleNotWorkTimeLength = (((reportItem.TotalNotWorkTimeLength - reportItem.AddWorkNotWorkTimeLength) / lineWorkDay) * (lineWorkDay - reportItem.FrequencyDay)) + reportItem.AddWorkNotWorkTimeLength
			reportItem.InFrequencyTypeCircleWorkTimeLength = reportItem.TotalWorkTimeLength - reportItem.OutFrequencyTypeCircleWorkTimeLength
			reportItem.InFrequencyTypeCircleNotWorkTimeLength = reportItem.TotalNotWorkTimeLength - reportItem.OutFrequencyTypeCircleNotWorkTimeLength
		}

		totalSumItem.InFrequencyTypeCircleWorkTimeLength += reportItem.InFrequencyTypeCircleWorkTimeLength
		totalSumItem.InFrequencyTypeCircleNotWorkTimeLength += reportItem.InFrequencyTypeCircleNotWorkTimeLength
		totalSumItem.OutFrequencyTypeCircleWorkTimeLength += reportItem.OutFrequencyTypeCircleWorkTimeLength
		totalSumItem.OutFrequencyTypeCircleNotWorkTimeLength += reportItem.OutFrequencyTypeCircleNotWorkTimeLength

		if _, ok := driverVehicleLength[driverWorkReportItems[i].DriverId]; ok {
			reportItem.VehicleLength = driverVehicleLength[driverWorkReportItems[i].DriverId]
		} else {
			driverRunVehicles := (&operationModel.LineVehicleMileageReport{}).GetLineDriverRunVehicleDay(driverWorkReportItems[i].DriverId, startAt, endAt)
			if len(driverRunVehicles) > 0 {
				if driverRunVehicles[0].VehicleLength == 0 {
					vehicle := rpc.GetVehicleWithId(context.TODO(), driverRunVehicles[0].VehicleId)
					if vehicle != nil {
						vehicleSize := strings.Split(vehicle.CarBodySize, "*")
						if len(vehicleSize) > 0 {
							reportItem.VehicleLength, _ = strconv.ParseInt(strings.Trim(vehicleSize[0], " "), 10, 64)
						}
					}
				} else {
					reportItem.VehicleLength = driverRunVehicles[0].VehicleLength
				}
				driverVehicleLength[driverWorkReportItems[i].DriverId] = reportItem.VehicleLength
			}
		}

		reportItem.PlanWorkDay = (&operationModel.PlanScheduleReport{}).GetDriverFrequencyIndexCount(0, 0, driverWorkReportItems[i].DriverId, startAt, endAt)

		//年休假、疗休养、病假日期
		//notWorkDays := (&operationModel.LineVehicleMileageReport{}).GetLineDriverNotWorkReportAt(driverWorkReportItems[i].CorporationId, driverWorkReportItems[i].LineId, driverWorkReportItems[i].DriverId, startAt, endAt)
		//从请假列表里获取时间段内请假全天的日期和请假半天的日期
		exceptLeaveTypes := []int64{util.LeaveTypeForCasualLeave, util.LeaveTypeForWaitWork, util.LeaveTypeForStopWork}
		oneDayLeave, _ := service.GetDriverLeaveReportAtByRange(driverWorkReportItems[i].DriverId, startAt, endAt, exceptLeaveTypes)
		reportItem.PlanTotalCircle = (&operationModel.PlanScheduleReport{}).GetDriverSumCircleCount(0, 0, driverWorkReportItems[i].DriverId, startAt, endAt, oneDayLeave)
		//查询日期范围内没有排班的日期
		reportAtCircleCount := (&operationModel.PlanScheduleReport{}).GetReportAtPlanCircleByDriver(driverWorkReportItems[i].DriverId, 0, 0, startAt, endAt)
		for i := range reportAtCircleCount {
			if reportAtCircleCount[i].PlanCircle == 0 {
				oneDayLeave = append(oneDayLeave, reportAtCircleCount[i].ReportAt.ToTime().Format(model.DateFormat))
			}
		}
		notWorkCircles := (&operationModel.LineVehicleMileageReport{}).GetLineDriverNotWorkCircle(0, 0, driverWorkReportItems[i].DriverId, startAt, endAt, oneDayLeave)
		reportItem.PlanTotalCircle = reportItem.PlanTotalCircle - notWorkCircles
		if reportItem.PlanTotalCircle < 0 {
			reportItem.PlanTotalCircle = 0
		}

		//计算排班内完成圈次和排班外完成圈次
		reportItem.InPlanDoneCircle, reportItem.OutPlanDoneCircle = service.CalcInAndOutPlanDoneCircle(isApproval, startAt, endAt, driverWorkReportItems[i].DriverId, 0, 0)

		if reportItem.PlanTotalCircle > 0 {
			inPlanDoneCircle := float64(reportItem.InPlanDoneCircle) / 10
			planTotalCircle := float64(reportItem.PlanTotalCircle) / 10
			//reportItem.PlanWorkRate = int64((inPlanDoneCircle / planTotalCircle) * 100)
			reportItem.PlanWorkRate = decimal.NewFromFloat(inPlanDoneCircle).Div(decimal.NewFromFloat(planTotalCircle)).Mul(decimal.NewFromInt(100)).Round(2).InexactFloat64()

		}

		totalSumItem.PlanWorkDay += reportItem.PlanWorkDay
		totalSumItem.PlanTotalCircle += reportItem.PlanTotalCircle
		totalSumItem.InPlanDoneCircle += reportItem.InPlanDoneCircle
		totalSumItem.OutPlanDoneCircle += reportItem.OutPlanDoneCircle

		results = append(results, reportItem)

	}
	//if param.Paginator.Offset > len(results) {
	//	results = nil
	//} else {
	//	if param.Paginator.Offset+param.Paginator.Limit > len(results) {
	//		results = results[param.Paginator.Offset:]
	//	} else {
	//		results = results[param.Paginator.Offset:param.Paginator.Limit]
	//	}
	//}

	var approvalHandler service.ApprovalHandler
	if isApproval == util.StatusForTrue {
		approvalHandler = service.GetApprovalHandler(param.CorporationIds, param.LineIds, []string{"line_vehicle_operation_report", "driver_work_report"}, startAt, endAt)
		approvalHandler.HasApproval = true
	}

	return LineDriverWorkReportRsp{
		Items:           results,
		TotalCount:      count,
		SumItem:         totalSumItem,
		ApprovalHandler: approvalHandler,
	}, response.SUCCESS
}

type ExportLineDriverWorkItem struct {
	LineDriverWorkReportItem
	FullSinglePassWorkTimeLengthValue  int64  `json:"FullSinglePassWorkTimeLengthValue"`
	FullWorkTimeLengthValue            int64  `json:"FullWorkTimeLengthValue"`
	FullNotWorkTimeLengthValue         int64  `json:"FullNotWorkTimeLengthValue"`
	RangeSinglePassWorkTimeLengthValue int64  `json:"RangeSinglePassWorkTimeLengthValue"`
	RangeWorkTimeLengthValue           int64  `json:"RangeWorkTimeLengthValue"`
	RangeNotWorkTimeLengthValue        int64  `json:"RangeNotWorkTimeLengthValue"`
	More                               string `json:"More"`
	VehicleModelStr                    string `json:"VehicleModelStr"`
	FrequencyTypeStr                   string `json:"FrequencyTypeStr"`
}

func ExportLineDriverWork(exportFile exportModel.ExportFile, req *api.Request, param LineReportParam, from string) {
	var result LineDriverWorkReportRsp
	var errorCode = response.SUCCESS
	//获取所有数据
	if from == "line" {
		result, errorCode = lineDriverWorkReport(req, exportFile.OpUserId)
	}

	if from == "driver" {
		result, errorCode = lineDriverWorkReportByDriver(req, exportFile.OpUserId)
	}

	exportFile.ErrorCode = errorCode

	if errorCode != response.SUCCESS {
		err := exportFile.UpdateFail()
		log.ErrorFields("ExportLineDriverWorkByLine error", map[string]interface{}{"err": err})
		return
	}

	var exportExports []map[string]interface{}
	for i := range result.Items {
		var exportItem ExportLineDriverWorkItem
		exportItem.LineDriverWorkReportItem = result.Items[i]
		for j := range exportItem.FullWorkTimeLength {
			if j == 0 {
				exportItem.FullWorkTimeLengthValue = exportItem.FullWorkTimeLength[j].Value
				continue
			}
			exportItem.More += fmt.Sprintf("%s-%s全程岗上时长：%v；", time.Time(exportItem.FullWorkTimeLength[j].StartAt).Format("2006/01/02"), time.Time(exportItem.FullWorkTimeLength[j].EndAt).Format("2006/01/02"), exportItem.FullWorkTimeLength[j].Value)
		}

		for j := range exportItem.FullNotWorkTimeLength {
			if j == 0 {
				exportItem.FullNotWorkTimeLengthValue = exportItem.FullNotWorkTimeLength[j].Value
				continue
			}
			exportItem.More += fmt.Sprintf("%s-%s全程岗下时长：%v；", time.Time(exportItem.FullNotWorkTimeLength[j].StartAt).Format("2006/01/02"), time.Time(exportItem.FullNotWorkTimeLength[j].EndAt).Format("2006/01/02"), exportItem.FullNotWorkTimeLength[j].Value)
		}

		for j := range exportItem.FullSinglePassWorkTimeLength {
			if j == 0 {
				exportItem.FullSinglePassWorkTimeLengthValue = exportItem.FullSinglePassWorkTimeLength[j].Value
				continue
			}
			exportItem.More += fmt.Sprintf("%s-%s全程单圈工作时长：%v；", time.Time(exportItem.FullSinglePassWorkTimeLength[j].StartAt).Format("2006/01/02"), time.Time(exportItem.FullSinglePassWorkTimeLength[j].EndAt).Format("2006/01/02"), exportItem.FullSinglePassWorkTimeLength[j].Value)
		}

		for j := range exportItem.RangeWorkTimeLength {
			if j == 0 {
				exportItem.RangeWorkTimeLengthValue = exportItem.RangeWorkTimeLength[j].Value
				continue
			}
			exportItem.More += fmt.Sprintf("%s-%s区间岗上时长：%v；", time.Time(exportItem.RangeWorkTimeLength[j].StartAt).Format("2006/01/02"), time.Time(exportItem.RangeWorkTimeLength[j].EndAt).Format("2006/01/02"), exportItem.RangeWorkTimeLength[j].Value)
		}

		for j := range exportItem.RangeNotWorkTimeLength {
			if j == 0 {
				exportItem.RangeNotWorkTimeLengthValue = exportItem.RangeNotWorkTimeLength[j].Value
				continue
			}
			exportItem.More += fmt.Sprintf("%s-%s区间岗下时长：%v；", time.Time(exportItem.RangeNotWorkTimeLength[j].StartAt).Format("2006/01/02"), time.Time(exportItem.RangeNotWorkTimeLength[j].EndAt).Format("2006/01/02"), exportItem.RangeNotWorkTimeLength[j].Value)
		}

		for j := range exportItem.RangeSinglePassWorkTimeLength {
			if j == 0 {
				exportItem.RangeSinglePassWorkTimeLengthValue = exportItem.RangeSinglePassWorkTimeLength[j].Value
				continue
			}
			exportItem.More += fmt.Sprintf("%s-%s区间单圈工作时长：%v；", time.Time(exportItem.RangeSinglePassWorkTimeLength[j].StartAt).Format("2006/01/02"), time.Time(exportItem.RangeSinglePassWorkTimeLength[j].EndAt).Format("2006/01/02"), exportItem.RangeSinglePassWorkTimeLength[j].Value)
		}

		if exportItem.VehicleLength >= 6000 {
			exportItem.VehicleModelStr = util.VehicleModelMap[util.VehicleModelForBig]
		}
		if exportItem.VehicleLength < 6000 && exportItem.VehicleLength > 0 {
			exportItem.VehicleModelStr = util.VehicleModelMap[util.VehicleModelForMiddle]
		}

		exportItem.FrequencyTypeStr = util.FrequencyTypeMap[exportItem.LineDriverWorkReportItem.WorkAndNotWorkTimeLength.FrequencyType]

		mapItem, err := util.StructToMapByMarshal(exportItem)
		if err != nil {
			log.ErrorFields("ExportLineDriverWork util.StructToMapByMarshal error", map[string]interface{}{"err": err})
			return
		}

		mapItem["ReportAt"] = fmt.Sprintf("%s-%s", time.Time(param.StartAt).Format("2006/01/02"), time.Time(param.EndAt).Format("2006/01/02"))
		exportExports = append(exportExports, mapItem)
	}

	err := exportService.ExportLineDriverWork(exportExports, exportFile, param.ExportGroup, from, result.ApprovalHandler)
	if err != nil {
		exportFile.ErrorCode = err.Error()
		err = exportFile.UpdateFail()
	} else {
		err = exportFile.UpdateSuccess()
	}
	log.ErrorFields("ExportLineDriverWorkByLine exportFile.UpdateStatus", map[string]interface{}{"err": err})
}
