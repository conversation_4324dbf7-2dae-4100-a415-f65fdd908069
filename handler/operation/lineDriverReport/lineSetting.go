package lineDriverReport

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	operationModel "app/org/scs/erpv2/api/model/operation"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

var UpdateColumnSetting = map[string][]string{
	"FrequencyType":     {"WorkType"},
	"AttendanceType":    {"FrequencyType"},
	"RatedMileage":      {"FullRatedMileage", "UpFullRatedMileage", "DownFullRatedMileage", "RangeRatedMileage", "UpRangeRatedMileage", "DownRangeRatedMileage"},
	"InOutDepotMileage": {"FullInOutDepotMileage", "RangeInOutDepotMileage"},
	"AssistantMileage":  {"FullAssistantMileage", "RangeAssistantMileage"},
	"InOutDepotTime":    {"FullInOutDepotTime", "RangeInOutDepotTime"},
	"AssistantTime":     {"FullAssistantTime", "RangeAssistantTime"},
	"RatedWorkTime":     {"FullRatedWorkTimeLength", "RangeRatedWorkTimeLength"},
	"RatedNotWorkTime":  {"FullRatedNotWorkTimeLength", "RangeRatedNotWorkTimeLength"},
}

type LineSettingParam struct {
	CorporationId  int64           `json:"CorporationId"`
	LineId         int64           `json:"LineId"`
	DateAt         model.LocalTime `json:"DateAt"`
	StartAt        model.LocalTime `json:"StartAt"`
	EndAt          model.LocalTime `json:"EndAt"`
	CorporationIds []int64         `json:"CorporationIds"`
	LineIds        []int64         `json:"LineIds"`
	model.Paginator
}

func (op *OperationReport) LineSettingList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineSettingParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	settings := (&operationModel.OperationLineSetting{}).GetByLineId(param.CorporationId, param.LineId)

	return response.Success(rsp, settings)

}

func (op *OperationReport) LineSettingInfo(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineSettingParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	settings := (&operationModel.OperationLineSetting{}).FirstByLineId(param.CorporationId, param.LineId, time.Time(param.DateAt))

	return response.Success(rsp, settings)
}

func (op *OperationReport) LineSettingCreate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param operationModel.OperationLineSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	//获取线路
	line, _ := rpc.GetLineWithId(ctx, param.LineId)
	if line == nil {
		return response.Error(rsp, response.LineNotFund)
	}
	param.LineName = line.Name
	param.ParseOpUser(ctx)

	var dates []string
	if param.WorkRate == 0 {
		param.WorkRate = 1
	}

	var startAt = time.Time(param.StartAt)

	if time.Time(param.EndAt).Year()-startAt.Year() > 3 {
		return response.Error(rsp, "OP9005")
	}

	for {
		if startAt.Unix() > time.Time(param.EndAt).Unix() {
			break
		}
		dates = append(dates, startAt.Format(model.DateFormat))
		startAt = startAt.AddDate(0, 0, int(param.WorkRate))
	}

	//判断时间是否有交叉
	if (&operationModel.OperationLineSettingHasDate{}).IsExistDates(param.CorporationId, param.LineId, 0, dates) {
		return response.Error(rsp, "OP9004")
	}

	//if (&operationModel.OperationLineSetting{}).IsExistTimeCross(param.CorporationId, param.LineId, time.Time(param.StartAt), time.Time(param.EndAt)) {
	//	return response.Error(rsp, response.ParamsInvalid)
	//}

	tx := model.DB().Begin()
	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()
	err = param.TransactionCreate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("OperationLineSetting.TransactionCreate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	var settingDates []operationModel.OperationLineSettingHasDate
	for i := range dates {
		dateAt, _ := time.ParseInLocation(model.DateFormat, dates[i], time.Local)
		settingDates = append(settingDates, operationModel.OperationLineSettingHasDate{
			CorporationId:          param.CorporationId,
			OperationLineSettingId: param.Id,
			LineId:                 param.LineId,
			DateAt:                 model.LocalTime(dateAt),
		})
	}
	err = (&operationModel.OperationLineSettingHasDate{}).Create(tx, settingDates)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("OperationLineSettingHasDate.Create fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	tx.Commit()

	go CreateSettingLog(param.OpUser, "create", nil, &param)
	return response.Success(rsp, map[string]interface{}{
		"IsExistHistory": (&operationModel.LineVehicleMileageReport{}).IsExistRecordByRange(param.CorporationId, param.LineId, dates, time.Time(param.StartAt), time.Time(param.EndAt)),
	})
}

func (op *OperationReport) LineSettingUpdate(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param operationModel.OperationLineSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if err := util.Validator().Struct(param); err != nil {
		log.ErrorFields("validate fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsMissing)
	}

	if param.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}

	beforeData := (&operationModel.OperationLineSetting{}).FirstById(param.Id)
	//获取线路
	line, _ := rpc.GetLineWithId(ctx, param.LineId)
	if line == nil {
		return response.Error(rsp, response.LineNotFund)
	}

	param.LineName = line.Name

	param.ParseOpUser(ctx)

	var dates []string
	if param.WorkRate == 0 {
		param.WorkRate = 1
	}
	var startAt = time.Time(param.StartAt)
	if time.Time(param.EndAt).Year()-startAt.Year() > 3 {
		return response.Error(rsp, "OP9005")
	}
	for {
		if startAt.Unix() > time.Time(param.EndAt).Unix() {
			break
		}
		dates = append(dates, startAt.Format(model.DateFormat))
		startAt = startAt.AddDate(0, 0, int(param.WorkRate))
	}

	//判断时间是否有交叉
	if (&operationModel.OperationLineSettingHasDate{}).IsExistDates(param.CorporationId, param.LineId, param.Id, dates) {
		return response.Error(rsp, "OP9004")
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = param.Update(tx)
	if err != nil {
		log.ErrorFields("OperationLineSetting.Update fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	err = (&operationModel.OperationLineSettingHasDate{}).Delete(tx, param.Id)
	if err != nil {
		log.ErrorFields("OperationLineSettingHasDate.Delete fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	var settingDates []operationModel.OperationLineSettingHasDate
	for i := range dates {
		dateAt, _ := time.ParseInLocation(model.DateFormat, dates[i], time.Local)
		settingDates = append(settingDates, operationModel.OperationLineSettingHasDate{
			CorporationId:          param.CorporationId,
			OperationLineSettingId: param.Id,
			LineId:                 param.LineId,
			DateAt:                 model.LocalTime(dateAt),
		})
	}

	err = (&operationModel.OperationLineSettingHasDate{}).Create(tx, settingDates)
	if err != nil {
		log.ErrorFields("OperationLineSettingHasDate.Create fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	if param.OnlyUpdateDate == util.StatusForTrue {
		go CreateSettingLog(param.OpUser, "editDate", &beforeData, &param)
	} else {
		go CreateSettingLog(param.OpUser, "editSettingItem", &beforeData, &param)
	}
	if len(param.UpdateHistoryColumns) > 0 && param.IsUpdateHistory == util.StatusForTrue {
		var willUpdatedColumns []string
		for i := range param.UpdateHistoryColumns {
			if _, ok := UpdateColumnSetting[param.UpdateHistoryColumns[i]]; ok {
				willUpdatedColumns = append(willUpdatedColumns, UpdateColumnSetting[param.UpdateHistoryColumns[i]]...)
			}
		}

		if len(willUpdatedColumns) > 0 {
			// 更新历史数据
			var err = syncUpdateHistoryLineMileage(tx, param, dates, willUpdatedColumns, param.IsUpdateManualData)
			if err != nil {
				log.ErrorFields("OperationLineSetting syncUpdateHistoryLineMileage error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.FAIL)
			}
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"IsExistHistory": (&operationModel.LineVehicleMileageReport{}).IsExistRecordByRange(param.CorporationId, param.LineId, dates, time.Time(param.StartAt), time.Time(param.EndAt)),
	})
}

func (op *OperationReport) LineSettingDelete(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param operationModel.OperationLineSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	setting := (&operationModel.OperationLineSetting{}).FirstById(param.Id)
	if setting.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()
	err = setting.Delete(tx)

	if err != nil {
		tx.Rollback()
		log.ErrorFields("OperationLineSetting.Delete fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	err = (&operationModel.OperationLineSettingHasDate{}).Delete(tx, setting.Id)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("OperationLineSettingHasDate.Delete fail", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	tx.Commit()
	go CreateSettingLog(param.OpUser, "delete", &setting, &setting)
	return response.Success(rsp, nil)

}

func (op *OperationReport) LineSettingLog(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineSettingParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	settingLogs, count := (&operationModel.OperationLineSettingLog{}).GetBy(param.CorporationIds, param.LineIds, param.StartAt.ToTime(), param.EndAt.ToTime(), param.Paginator)

	return response.Success(rsp, map[string]interface{}{
		"Items":      settingLogs,
		"TotalCount": count,
	})
}

func (op *OperationReport) LineSettingLogExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return op.LineSettingLog(ctx, req, rsp)
}

func CreateSettingLog(opUser model.OpUser, scene string, beforeData, afterData *operationModel.OperationLineSetting) {
	log.ErrorFields("CreateSettingLog", map[string]interface{}{"before": beforeData, "after": afterData})

	var settingLog = operationModel.OperationLineSettingLog{
		OperationLineSettingId: afterData.Id,
		CorporationId:          afterData.CorporationId,
		CorporationName:        afterData.CorporationName,
		LineId:                 afterData.LineId,
		LineName:               afterData.LineName,
		Scene:                  scene,
		OpUser:                 opUser,
	}

	var isCreate = true
	//添加时段
	if scene == "create" {
		after := map[string]interface{}{
			"StartAt":  afterData.StartAt,
			"EndAt":    afterData.EndAt,
			"WorkRate": afterData.WorkRate,
		}
		settingLog.AfterData, _ = json.Marshal(after)
	}

	//修改时段
	if scene == "editDate" {
		after := map[string]interface{}{
			"StartAt":  afterData.StartAt,
			"EndAt":    afterData.EndAt,
			"WorkRate": afterData.WorkRate,
		}
		before := map[string]interface{}{
			"StartAt":  beforeData.StartAt,
			"EndAt":    beforeData.EndAt,
			"WorkRate": beforeData.WorkRate,
		}
		settingLog.AfterData, _ = json.Marshal(after)
		settingLog.BeforeData, _ = json.Marshal(before)
	}

	//删除时段
	if scene == "delete" {
		before := map[string]interface{}{
			"StartAt":  beforeData.StartAt,
			"EndAt":    beforeData.EndAt,
			"WorkRate": beforeData.WorkRate,
		}
		settingLog.BeforeData, _ = json.Marshal(before)
	}

	//修改配置
	if scene == "editSettingItem" {
		var beforeSetting = make(map[string]interface{})
		var afterSetting = make(map[string]interface{})
		_ = json.Unmarshal(beforeData.SettingItem, &beforeSetting)
		_ = json.Unmarshal(afterData.SettingItem, &afterSetting)

		var beforeMap = make(map[string]interface{})
		var afterMap = make(map[string]interface{})
		for k := range beforeSetting {
			if k == "FrequencyItems" {
				before, _ := json.Marshal(beforeSetting[k])
				after, _ := json.Marshal(afterSetting[k])
				if string(before) != string(after) {
					beforeMap[k] = beforeSetting[k]
					afterMap[k] = afterSetting[k]
				}
			} else {
				if beforeSetting[k] != afterSetting[k] {
					beforeMap[k] = beforeSetting[k]
					afterMap[k] = afterSetting[k]
				}
			}
		}

		if len(beforeMap) == 0 && len(afterMap) == 0 {
			isCreate = false
		}

		settingLog.BeforeData, _ = json.Marshal(beforeMap)
		settingLog.AfterData, _ = json.Marshal(afterMap)
	}

	if isCreate {
		_ = settingLog.Create()
	}

}

func SyncHistorySettingSplitDate() {
	time.Sleep(60 * time.Second)
	var settings []operationModel.OperationLineSetting
	model.DB().Model(&operationModel.OperationLineSetting{}).Find(&settings)

	for _, set := range settings {
		var startAt = time.Time(set.StartAt)
		var settingDates []operationModel.OperationLineSettingHasDate
		for {
			if startAt.Unix() > time.Time(set.EndAt).Unix() {
				break
			}
			settingDates = append(settingDates, operationModel.OperationLineSettingHasDate{
				CorporationId:          set.CorporationId,
				OperationLineSettingId: set.Id,
				LineId:                 set.LineId,
				DateAt:                 model.LocalTime(startAt),
			})
			startAt = startAt.AddDate(0, 0, 1)
		}

		_ = (&operationModel.OperationLineSettingHasDate{}).Create(model.DB(), settingDates)
	}
}
