package lineDriverReport

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	operationModel "app/org/scs/erpv2/api/model/operation"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"sort"
	"strings"
	"time"
)

type LineSalaryReportResponse struct {
	TopCorporationId         int64                                        `json:"TopCorporationId"`         //顶级机构ID
	CorporationId            int64                                        `json:"CorporationId"`            //线路所属的机构（车队）ID
	CorporationName          string                                       `json:"CorporationName"`          //线路所属的机构（车队）
	LineId                   int64                                        `json:"LineId"`                   //线路ID
	LineName                 string                                       `json:"LineName"`                 //线路名称
	WorkPostType             int64                                        `json:"WorkPostType"`             //岗位类型 1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他,9-修理工
	VehicleNum               int64                                        `json:"VehicleNum"`               //营运车辆数
	VehicleModel             string                                       `json:"VehicleModel"`             //车型
	NightTotalWorkTimeLength int64                                        `json:"NightTotalWorkTimeLength"` //夜班总时长  单位：秒
	More                     string                                       `json:"More"`                     //备注
	OperationItems           []operationModel.DriverRunLineReportDataItem `json:"OperationItems"`           //运营信息
	WorkAndNotWorkTimeLength
}

type WorkAndNotWorkTimeLength struct {
	FullWorkTimeLength            []operationModel.LineReportSettingTimeLength `json:"FullWorkTimeLength"`            //全程岗上时长 单位：秒
	FullNotWorkTimeLength         []operationModel.LineReportSettingTimeLength `json:"FullNotWorkTimeLength"`         //全程岗下时长 单位：秒
	FullSinglePassWorkTimeLength  []operationModel.LineReportSettingTimeLength `json:"FullSinglePassWorkTimeLength"`  //全程单趟工作时长 单位：秒
	RangeWorkTimeLength           []operationModel.LineReportSettingTimeLength `json:"RangeWorkTimeLength"`           //区间岗上时长 单位：秒
	RangeNotWorkTimeLength        []operationModel.LineReportSettingTimeLength `json:"RangeNotWorkTimeLength"`        //区间岗下时长 单位：秒
	RangeSinglePassWorkTimeLength []operationModel.LineReportSettingTimeLength `json:"RangeSinglePassWorkTimeLength"` //区间单趟工作时长 单位：秒
	HasRangeTrip                  int64                                        `json:"HasRangeTrip"`                  //是否有区间
	FrequencyType                 int64                                        `json:"FrequencyType"`                 //班制 1双班  2单班  3混合班
	FrequencyDay                  int64                                        `json:"FrequencyDay"`                  //班制对应的天数
}

func (*OperationReport) LineSalaryReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)

	if startAt.Unix() > endAt.Unix() {
		return response.Error(rsp, response.ParamsInvalid)
	}
	userSetting := service.GetUserSetting(auth.User(ctx).GetUserId())
	isApproval := userSetting.OperationReportDataIsApproval
	notExistDays := GetDontHaveDataDays(isApproval, param.CorporationId, param.LineId, startAt, endAt)

	var result LineSalaryReportResponse
	result.WorkPostType = util.WorkPostType_3
	result.LineId = param.LineId
	result.More = (&operationModel.LineSalaryReportSetting{}).GetMoreByLineIdStartEndAt(param.CorporationId, param.LineId, startAt, endAt)
	//获取和所选时间有交叉的配置项
	settings := (&operationModel.OperationLineSetting{}).GetTimeCrossSetting([]int64{param.CorporationId}, param.LineId, startAt, endAt)

	if len(settings) > 0 {
		newSetting := settings[len(settings)-1]
		result.LineName = newSetting.LineName
		result.TopCorporationId = newSetting.TopCorporationId
		result.CorporationId = newSetting.CorporationId
		result.CorporationName = newSetting.CorporationName
	}

	if result.LineName == "" {
		lineInfo, _ := rpc.GetLineWithId(ctx, result.LineId)
		if lineInfo != nil {
			result.LineName = lineInfo.Name
		}
	}

	vehicleIds := (&operationModel.LineVehicleMileageReport{}).GetLineVehiclesGroupByVehicleId(param.CorporationId, param.LineId, startAt, endAt)
	result.VehicleNum = int64(len(vehicleIds))

	timeLengthSetting := ParseTimeLengthByTimeRange(settings, startAt, endAt)

	//岗上、岗下工时配置项
	result.WorkAndNotWorkTimeLength = timeLengthSetting

	//查询线路归属的司机
	//driverIds := (&operationModel.LineVehicleMileageReport{}).GetLineHasDriversGroupByDriverId([]int64{param.CorporationId}, param.LineId, startAt, endAt)

	//查询线路下主运营的司机
	driverIds := service.GetLineMainRunDriverIds(param.CorporationId, param.LineId, startAt, endAt)

	if len(driverIds) > 0 {
		driverRunLineItems := (&operationModel.LineVehicleMileageReport{}).GetDriverRunLineReportDataGroupByLineId(isApproval, driverIds, startAt, endAt)
		for i := range driverRunLineItems {
			driverRunLineItems[i].Sort = int64(i)
			if driverRunLineItems[i].LineId == param.LineId {
				driverRunLineItems[i].Sort = -1
			}
			result.NightTotalWorkTimeLength += driverRunLineItems[i].NightTotalWorkTimeLength
		}

		sort.SliceStable(driverRunLineItems, func(i, j int) bool {
			return driverRunLineItems[i].Sort < driverRunLineItems[j].Sort
		})

		//加班超过2.5小时不足半天的和
		totalHalfDayAddWorkTimes := (&operationModel.LineVehicleMileageReport{}).GetDriverRunLineReportAddWorkTimeLength(isApproval, driverIds, "HalfDayAddWorkTimes", startAt, endAt)

		//加班超过半天不足一天的和
		totalFullDayAddWorkTimes := (&operationModel.LineVehicleMileageReport{}).GetDriverRunLineReportAddWorkTimeLength(isApproval, driverIds, "FullDayAddWorkTimes", startAt, endAt)

		//加班岗上时长之和
		totalAddWorkWorkTimeLength := (&operationModel.LineVehicleMileageReport{}).GetDriverRunLineReportAddWorkTimeLength(isApproval, driverIds, "AddWorkWorkTimeLength", startAt, endAt)

		//加班岗下时长之和
		totalAddWorkNotWorkTimeLength := (&operationModel.LineVehicleMileageReport{}).GetDriverRunLineReportAddWorkTimeLength(isApproval, driverIds, "AddWorkNotWorkTimeLength", startAt, endAt)

		//夜班加班时长之和
		totalNightAddWorkTimeLength := (&operationModel.LineVehicleMileageReport{}).GetDriverRunLineReportAddWorkTimeLength(isApproval, driverIds, "NightAddWorkTimeLength", startAt, endAt)

		driverRunLineItems = append(driverRunLineItems, operationModel.DriverRunLineReportDataItem{
			LineName:                 "加班",
			WorkDayCount:             (totalHalfDayAddWorkTimes + totalFullDayAddWorkTimes) * 10,
			TotalWorkTimeLength:      totalAddWorkWorkTimeLength,
			TotalNotWorkTimeLength:   totalAddWorkNotWorkTimeLength,
			NightTotalWorkTimeLength: totalNightAddWorkTimeLength,
			Scene:                    "add_work",
		})

		result.OperationItems = driverRunLineItems
	}

	vehicleLength := (&operationModel.LineVehicleMileageReport{}).GetLineVehicleLengthGroupBy(isApproval, []int64{param.CorporationId}, param.LineId, 0, 0, startAt, endAt)
	var vehicleLengthStr []string
	for i := range vehicleLength {
		if vehicleLength[i] > 0 {
			vehicleLengthStr = append(vehicleLengthStr, fmt.Sprintf("%.3f", float64(vehicleLength[i])/1000))
		}
	}
	result.VehicleModel = strings.Join(vehicleLengthStr, "/")

	var approvalHandler service.ApprovalHandler
	if isApproval == util.StatusForTrue {
		approvalHandler = service.GetApprovalHandler([]int64{param.CorporationId}, []int64{param.LineId}, []string{"line_vehicle_operation_report", "line_salary_report"}, startAt, endAt)
	}

	return response.Success(rsp, map[string]interface{}{
		"NotExistDays":    notExistDays,
		"Items":           result,
		"ApprovalHandler": approvalHandler,
	})
}

func ParseTimeLengthByTimeRange(settings []operationModel.OperationLineSetting, startAt, endAt time.Time) WorkAndNotWorkTimeLength {
	for i := range settings {
		if time.Time(settings[i].StartAt).Unix() < startAt.Unix() {
			settings[i].StartAt = model.LocalTime(startAt)
		}
		if time.Time(settings[i].EndAt).Unix() > endAt.Unix() {
			settings[i].EndAt = model.LocalTime(endAt)
		}
	}

	var timeLengthSetting WorkAndNotWorkTimeLength
	for i := range settings {
		var settingItem operationModel.OperationLineSettingSettingItem
		err := json.Unmarshal(settings[i].SettingItem, &settingItem)
		if err != nil {
			log.ErrorFields("LineSalaryReport setting.SettingItem json.Unmarshal error", map[string]interface{}{"err": err})
			return timeLengthSetting
		}

		if settingItem.HasRangeTrip == util.StatusForTrue {
			timeLengthSetting.HasRangeTrip = util.StatusForTrue
		}
		if i == len(settings)-1 {
			timeLengthSetting.FrequencyType = settingItem.FrequencyType
			timeLengthSetting.FrequencyDay = settingItem.FrequencyDay
		}

		timeLengthSetting.FullWorkTimeLength = append(timeLengthSetting.FullWorkTimeLength, operationModel.LineReportSettingTimeLength{
			StartAt:       settings[i].StartAt,
			EndAt:         settings[i].EndAt,
			Value:         settingItem.UpFullRatedWorkTime,
			FrequencyType: settingItem.FrequencyType,
			FrequencyDay:  settingItem.FrequencyDay,
		})
		timeLengthSetting.FullNotWorkTimeLength = append(timeLengthSetting.FullNotWorkTimeLength, operationModel.LineReportSettingTimeLength{
			StartAt:       settings[i].StartAt,
			EndAt:         settings[i].EndAt,
			Value:         settingItem.UpFullRatedNotWorkTime,
			FrequencyType: settingItem.FrequencyType,
			FrequencyDay:  settingItem.FrequencyDay,
		})
		timeLengthSetting.FullSinglePassWorkTimeLength = append(timeLengthSetting.FullSinglePassWorkTimeLength, operationModel.LineReportSettingTimeLength{
			StartAt:       settings[i].StartAt,
			EndAt:         settings[i].EndAt,
			Value:         settingItem.UpFullRatedWorkTime + settingItem.UpFullRatedNotWorkTime,
			FrequencyType: settingItem.FrequencyType,
			FrequencyDay:  settingItem.FrequencyDay,
		})
		timeLengthSetting.RangeWorkTimeLength = append(timeLengthSetting.RangeWorkTimeLength, operationModel.LineReportSettingTimeLength{
			StartAt:       settings[i].StartAt,
			EndAt:         settings[i].EndAt,
			Value:         settingItem.UpRangeRatedWorkTime,
			FrequencyType: settingItem.FrequencyType,
			FrequencyDay:  settingItem.FrequencyDay,
		})
		timeLengthSetting.RangeNotWorkTimeLength = append(timeLengthSetting.RangeNotWorkTimeLength, operationModel.LineReportSettingTimeLength{
			StartAt:       settings[i].StartAt,
			EndAt:         settings[i].EndAt,
			Value:         settingItem.UpRangeRatedNotWorkTime,
			FrequencyType: settingItem.FrequencyType,
			FrequencyDay:  settingItem.FrequencyDay,
		})
		timeLengthSetting.RangeSinglePassWorkTimeLength = append(timeLengthSetting.RangeSinglePassWorkTimeLength, operationModel.LineReportSettingTimeLength{
			StartAt:       settings[i].StartAt,
			EndAt:         settings[i].EndAt,
			Value:         settingItem.UpRangeRatedWorkTime + settingItem.UpRangeRatedNotWorkTime,
			FrequencyType: settingItem.FrequencyType,
			FrequencyDay:  settingItem.FrequencyDay,
		})
	}
	return timeLengthSetting
}

type LineSalarySumReportResponse struct {
	LineId                   int64                                   `json:"LineId"`       //线路ID
	LineName                 string                                  `json:"LineName"`     //线路名称
	VehicleModel             string                                  `json:"VehicleModel"` //车型
	WorkDayCount             int64                                   `json:"WorkDayCount"`
	TotalCircle              int64                                   `json:"TotalCircle"` //FullDoneCircle+RangeDoneCircle
	TotalWorkTimeLength      int64                                   `json:"TotalWorkTimeLength"`
	TotalNotWorkTimeLength   int64                                   `json:"TotalNotWorkTimeLength"`
	NightTotalWorkTimeLength int64                                   `json:"NightTotalWorkTimeLength"`
	WorkTimeLength           []operationModel.LineWorkTimeLengthItem `json:"WorkTimeLength"`
	NotWorkTimeLength        []operationModel.LineWorkTimeLengthItem `json:"NotWorkTimeLength"`
	WorkAndNotWorkTimeLength
}

func (*OperationReport) LineSalarySumReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	startAt := time.Time(param.StartAt)
	endAt := time.Time(param.EndAt)

	if startAt.Unix() > endAt.Unix() {
		return response.Error(rsp, response.ParamsInvalid)
	}
	userSetting := service.GetUserSetting(auth.User(ctx).GetUserId())
	isApproval := userSetting.OperationReportDataIsApproval

	var lineIds []int64
	if param.Limit+param.Offset >= len(param.LineIds) {
		if param.Offset < len(param.LineIds) {
			lineIds = param.LineIds[param.Offset:]
		}
	} else {
		lineIds = param.LineIds[param.Offset:(param.Offset + param.Limit)]
	}

	var results []LineSalarySumReportResponse
	for _, lineId := range lineIds {
		var result LineSalarySumReportResponse
		//获取和所选时间有交叉的配置项
		settings := (&operationModel.OperationLineSetting{}).GetTimeCrossSetting(param.CorporationIds, lineId, startAt, endAt)
		result.LineId = lineId
		if len(settings) > 0 {
			newSetting := settings[len(settings)-1]
			result.LineName = newSetting.LineName
		}
		lineInfo, _ := rpc.GetLineWithId(ctx, lineId)
		if lineInfo != nil {
			result.LineName = lineInfo.Name
		}

		timeLengthSetting := ParseTimeLengthByTimeRange(settings, startAt, endAt)

		//岗上、岗下工时配置项
		result.WorkAndNotWorkTimeLength = timeLengthSetting

		//查询线路归属的司机
		//driverIds := (&operationModel.LineVehicleMileageReport{}).GetLineHasDriversGroupByDriverId(param.CorporationIds, lineId, startAt, endAt)

		//查询线路主运营司机
		var driverIds []int64
		for i := range param.CorporationIds {
			ids := service.GetLineMainRunDriverIds(param.CorporationIds[i], lineId, startAt, endAt)
			if len(ids) > 0 {
				driverIds = append(driverIds, ids...)
			}
		}

		if len(driverIds) > 0 {
			driverRunSum := (&operationModel.LineVehicleMileageReport{}).GetDriverRunLineReportData(isApproval, driverIds, startAt, endAt)
			result.WorkDayCount = driverRunSum.WorkDayCount
			result.TotalCircle = driverRunSum.TotalCircle
			result.TotalWorkTimeLength = driverRunSum.TotalWorkTimeLength
			result.TotalNotWorkTimeLength = driverRunSum.TotalNotWorkTimeLength
			result.NightTotalWorkTimeLength = driverRunSum.NightTotalWorkTimeLength
		}

		vehicleLength := (&operationModel.LineVehicleMileageReport{}).GetLineVehicleLengthGroupBy(isApproval, param.CorporationIds, result.LineId, 0, 0, startAt, endAt)
		var vehicleLengthStr []string
		for i := range vehicleLength {
			if vehicleLength[i] > 0 {
				vehicleLengthStr = append(vehicleLengthStr, fmt.Sprintf("%.3f", float64(vehicleLength[i])/1000))
			}
		}
		result.VehicleModel = strings.Join(vehicleLengthStr, "/")

		//岗上 岗下时长
		result.WorkTimeLength = (&operationModel.LineVehicleMileageReport{}).GetLineAllWorkTimeGroupBy(isApproval, param.CorporationIds, result.LineId, startAt, endAt)
		result.NotWorkTimeLength = (&operationModel.LineVehicleMileageReport{}).GetLineAllNotWorkTimeGroupBy(isApproval, param.CorporationIds, result.LineId, startAt, endAt)

		results = append(results, result)
	}

	return response.Success(rsp, map[string]interface{}{
		"TotalCount": len(param.LineIds),
		"Items":      results,
	})

}

func (*OperationReport) SaveLineSalaryReportMore(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param operationModel.LineSalaryReportSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.LineId == 0 || param.CorporationId == 0 || param.StartAt.ToTime().IsZero() || param.EndAt.ToTime().IsZero() {
		return response.Error(rsp, response.ParamsMissing)
	}

	line, _ := rpc.GetLineWithId(ctx, param.LineId)
	if line != nil {
		param.LineName = line.Name
	}

	corporation := rpc.GetCorporationById(ctx, param.CorporationId)
	if corporation != nil {
		param.CorporationName = corporation.Name
	}

	param.TopCorporationId = auth.User(ctx).GetTopCorporationId()

	param.OpUser.ParseOpUser(ctx)

	err = param.UpdateOrCreate()
	if err != nil {
		log.ErrorFields("UpdateOrCreate error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}

	return response.Success(rsp, nil)
}
