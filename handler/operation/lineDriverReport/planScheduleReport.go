package lineDriverReport

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	operationModel "app/org/scs/erpv2/api/model/operation"
	protooetline "app/org/scs/erpv2/api/proto/rpc/oetline"
	protostaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"time"

	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/shopspring/decimal"
	"github.com/tealeg/xlsx"
)

type PlanScheduleReportRequest struct {
	FileData       string          `json:"FileData"`
	CorporationId  int64           `json:"CorporationId"`
	LineId         int64           `json:"LineId"`
	CorporationIds []int64         `json:"CorporationIds"`
	LineIds        []int64         `json:"LineIds"`
	StartAt        model.LocalTime `json:"StartAt"`
	EndAt          model.LocalTime `json:"EndAt"`
	model.Paginator
}
type PlanScheduleReportImportItem struct {
	operationModel.PlanScheduleReport
	RowIndex  int
	CellIndex int
}

func (*OperationReport) PlanScheduleReportImport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param PlanScheduleReportRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var lineMap = make(map[string]*protooetline.OetLineItem)
	var staffMap = make(map[string]*protostaff.OetStaffItem)
	user := auth.User(ctx).GetUser()

	corporation := rpc.GetCorporationById(ctx, user.CorporationId)
	if corporation == nil {
		return response.Error(rsp, "LG1006")
	}

	if corporation.Type != util.CorporationTypeForFleet {
		return response.Error(rsp, "PA1105")
	}
	var failItems []map[string]interface{}

	// 0线路* 1驾驶员 2工号 3部位/班次 4日期.......
	for _, sheet := range excelFile.Sheets {
		var items []PlanScheduleReportImportItem
		var rowCellCircle = make(map[string]float64)
		//将表头的日期获取到
		headers := sheet.Rows[0].Cells
		var datesCells = make(map[int]time.Time)
		for i, _ := range headers {
			if i < 4 {
				continue
			}
			datesCells[i], err = headers[i].GetTime(false)
			if err != nil {
				failItems = append(failItems, map[string]interface{}{
					"SheetName": sheet.Name,
					"RowIndex":  1,
					"Error":     "日期格式错误【要求为日期格式】",
				})
			}
		}
		var sheetLine string
		if len(failItems) > 0 {
			return response.Success(rsp, map[string]interface{}{"FailItems": failItems})
		}

		for i, row := range sheet.Rows {
			fmt.Printf("row.cells====== %+v \r\n", row.Cells)
			if i == 0 {
				continue
			}
			if len(row.Cells) < 3 {
				continue
			}
			//偶数行
			if i%2 == 0 {
				for cellIndex, _ := range row.Cells {
					if cellIndex < 4 {
						continue
					}
					rowCellCircle[fmt.Sprintf("%v_%v", i, cellIndex)], err = row.Cells[cellIndex].Float()
					if err != nil {
						failItems = append(failItems, map[string]interface{}{
							"SheetName": sheet.Name,
							"RowIndex":  i + 1,
							"Error":     "圈次格式错误【要求为数字格式】",
						})
					}
				}
			} else {
				var item PlanScheduleReportImportItem
				item.LineName = row.Cells[0].String()
				if sheetLine == "" {
					sheetLine = item.LineName
				} else {
					if sheetLine != item.LineName {
						failItems = append(failItems, map[string]interface{}{
							"SheetName": sheet.Name,
							"RowIndex":  i + 1,
							"Error":     "一个sheet表只允许存在一条线路",
						})
					}
				}
				item.DriverName = row.Cells[1].String()
				item.DriverCode = row.Cells[2].String()

				//获取线路
				if item.LineName == "" {
					failItems = append(failItems, map[string]interface{}{
						"SheetName": sheet.Name,
						"RowIndex":  i + 1,
						"Error":     "线路不存在",
					})
				} else {
					if line, ok := lineMap[item.LineName]; ok {
						item.LineId = line.Id
						item.LineCode = line.Code
					} else {
						oetLine := rpc.GetLineWithName(ctx, user.TopCorporationId, item.LineName)
						if oetLine != nil {
							item.LineId = oetLine.Id
							item.LineCode = oetLine.Code
							lineMap[item.LineName] = oetLine
							if !util.IncludeInt64(oetLine.SubCorporationIds, corporation.Id) {
								failItems = append(failItems, map[string]interface{}{
									"SheetName": sheet.Name,
									"RowIndex":  i + 1,
									"Error":     item.LineName + "线路不在账号机构下，无权导入此线路数据",
								})
							}
						} else {
							failItems = append(failItems, map[string]interface{}{
								"SheetName": sheet.Name,
								"RowIndex":  i + 1,
								"Error":     "线路不存在",
							})
						}
					}
				}

				//获取司机
				if item.DriverCode == "" {
					failItems = append(failItems, map[string]interface{}{
						"SheetName": sheet.Name,
						"RowIndex":  i + 1,
						"Error":     "司机工号不存在",
					})
				} else {
					if staff, ok := staffMap[item.DriverCode]; ok {
						if item.DriverName != staff.Name {
							failItems = append(failItems, map[string]interface{}{
								"SheetName": sheet.Name,
								"RowIndex":  i + 1,
								"Error":     "司机工号和司机姓名不一致",
							})
						} else {
							item.DriverId = staff.Id
							item.DriverName = staff.Name
						}

					} else {
						oetStaff := rpc.GetStaffWithStaffId(ctx, user.TopCorporationId, item.DriverCode)
						if oetStaff != nil {
							if item.DriverName != oetStaff.Name {
								failItems = append(failItems, map[string]interface{}{
									"SheetName": sheet.Name,
									"RowIndex":  i + 1,
									"Error":     "司机工号和司机姓名不一致",
								})
							} else {
								item.DriverId = oetStaff.Id
								item.DriverName = oetStaff.Name
							}
							staffMap[item.DriverCode] = oetStaff
						} else {
							failItems = append(failItems, map[string]interface{}{
								"SheetName": sheet.Name,
								"RowIndex":  i + 1,
								"Error":     "司机工号对应的司机不存在",
							})
						}
					}
				}

				item.RowIndex = i
				for cellIndex, _ := range row.Cells {
					if cellIndex < 4 {
						continue
					}
					item.CellIndex = cellIndex
					item.ReportAt = model.LocalTime(datesCells[cellIndex])
					item.FrequencyIndex, err = row.Cells[cellIndex].Int64()
					if err != nil {
						failItems = append(failItems, map[string]interface{}{
							"SheetName": sheet.Name,
							"RowIndex":  i + 1,
							"Error":     "班次格式错误【要求为数字格式】",
						})
					}
					items = append(items, item)
				}
			}
		}

		for i := range items {
			if items[i].LineId > 0 && corporation.Id > 0 && !(time.Time(items[i].ReportAt).IsZero()) {
				isExist := (&operationModel.OperationApproval{}).IsExistCrossApproval(corporation.Id, []string{"line_vehicle_operation_report"}, []string{strconv.Itoa(int(items[i].LineId))}, time.Time(items[i].ReportAt), time.Time(items[i].ReportAt))

				if isExist {
					return response.Error(rsp, "BPM1003")
				}
			}
			//找对应的圈次
			var circle = rowCellCircle[fmt.Sprintf("%v_%v", items[i].RowIndex+1, items[i].CellIndex)]
			items[i].CircleCount = decimal.NewFromFloat(circle).Mul(decimal.NewFromFloat(10)).IntPart()
			fmt.Printf("PlanScheduleReportImport=======row:%v,cell:%v,circle:%v,%T \n", items[i].RowIndex, items[i].CellIndex, circle, circle)
			items[i].TopCorporationId = user.TopCorporationId
			items[i].CorporationId = corporation.Id
			items[i].CorporationName = corporation.Name
			items[i].OpUserName = user.Name
			items[i].OpUserId = user.Id
		}

		if len(failItems) > 0 {
			return response.Success(rsp, map[string]interface{}{"FailItems": failItems})
		}

		var deletedDate = make(map[string]bool)
		fmt.Printf("deletedDate start=========lineName: %v,deletedDate:%+v \n", items[0].LineName, deletedDate)
		for i := range items {
			//根据线路日期删除历史数据
			reportAt := time.Time(items[i].ReportAt)
			if _, ok := deletedDate[reportAt.Format(model.DateFormat)]; !ok {
				err = items[i].PlanScheduleReport.DeleteByParam()
				if err != nil {
					log.ErrorFields("PlanScheduleReport.DeleteByParam error", map[string]interface{}{"err": err})
					return response.Error(rsp, response.FAIL)
				}
				deletedDate[reportAt.Format(model.DateFormat)] = true
			}

			err = items[i].PlanScheduleReport.Create()
			if err != nil {
				failItems = append(failItems, map[string]interface{}{
					"SheetName": sheet.Name,
					"RowIndex":  items[i].RowIndex + 1,
					"Error":     "数据保存失败",
				})
			}
		}

		fmt.Printf("deletedDate end=========lineName: %v,deletedDate:%+v \n", items[0].LineName, deletedDate)
	}

	return response.Success(rsp, map[string]interface{}{
		"FailItems": nil,
	})
}

func (op *OperationReport) PlanScheduleReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param PlanScheduleReportRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	reports := (&operationModel.PlanScheduleReport{}).GetBy(param.CorporationId, param.LineId, time.Time(param.StartAt), time.Time(param.EndAt))

	return response.Success(rsp, map[string]interface{}{
		"Items": reports,
	})
}

func (op *OperationReport) PlanScheduleReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return op.PlanScheduleReport(ctx, req, rsp)
}

type PlanScheduleAvgDayReportItem struct {
	CorporationId   int64   `json:"CorporationId"`
	CorporationName string  `json:"CorporationName"`
	LineId          int64   `json:"LineId"`
	LineName        string  `json:"LineName"`
	FrequencyType   int64   `json:"FrequencyType"`
	DriverCount     int64   `json:"DriverCount"`
	DayCount        int64   `json:"DayCount"`
	AvgDay          float64 `json:"AvgDay"`
}

func (op *OperationReport) PlanScheduleAvgDayReport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param PlanScheduleReportRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(param.CorporationIds) == 0 {
		param.CorporationIds = service.AuthCorporationIdProvider(ctx, param.CorporationIds)
	}

	reports := (&operationModel.PlanScheduleReport{}).GetDriverPlanDayCount(param.LineIds, param.CorporationIds, time.Time(param.StartAt), time.Time(param.EndAt))

	var reportMap = make(map[string][]PlanScheduleAvgDayReportItem)
	for i := range reports {
		var mpKey = fmt.Sprintf("%v_%v", reports[i].LineId, reports[i].CorporationId)
		reportMap[mpKey] = append(reportMap[mpKey], PlanScheduleAvgDayReportItem{
			CorporationId:   reports[i].CorporationId,
			CorporationName: reports[i].CorporationName,
			LineId:          reports[i].LineId,
			LineName:        reports[i].LineName,
			DayCount:        reports[i].DayCount,
		})
	}

	var reportItems []PlanScheduleAvgDayReportItem
	for _, items := range reportMap {
		var item PlanScheduleAvgDayReportItem
		item.CorporationId = items[0].CorporationId
		item.CorporationName = items[0].CorporationName
		item.LineId = items[0].LineId
		item.LineName = items[0].LineName
		for i := range items {
			item.DayCount += items[i].DayCount
		}
		lineSetting := (&operationModel.OperationLineSetting{}).FirstByLineId(item.CorporationId, item.LineId, time.Time(param.EndAt))
		var settingItem operationModel.OperationLineSettingSettingItem
		_ = json.Unmarshal(lineSetting.SettingItem, &settingItem)
		item.FrequencyType = settingItem.FrequencyType
		item.DriverCount = int64(len(items))
		item.AvgDay = decimal.NewFromInt(item.DayCount).Div(decimal.NewFromInt(int64(item.DriverCount))).Round(2).InexactFloat64()

		reportItems = append(reportItems, item)
	}

	sort.SliceStable(reportItems, func(i, j int) bool {
		return reportItems[i].LineName < reportItems[j].LineName
	})

	var results []PlanScheduleAvgDayReportItem
	if param.Limit+param.Offset >= len(reportItems) {
		if param.Offset < len(reportItems) {
			results = reportItems[param.Offset:]
		}
	} else {
		results = reportItems[param.Offset:(param.Offset + param.Limit)]
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      results,
		"TotalCount": len(reportItems),
	})
}

func (op *OperationReport) PlanScheduleAvgDayReportExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return op.PlanScheduleAvgDayReport(ctx, req, rsp)
}
