package lineDriverReport

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/operation"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"time"
)

type LineBaseDayConfigHandler struct {
	CorporationId int64  `json:"CorporationId"` // 组织机构
	LineId        int64  `json:"LineId" qs:"="` // 路线id
	Code          string `json:"Code" qs:"LIKE"`
	ApplyStatus   int64  `json:"ApplyStatus" qs:"="`
	model.Paginator
}

func (*OperationReport) LineBaseDayConfigAdd(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form operation.LineBaseDayConfig
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	user := auth.User(ctx).GetUser()
	form.OpUserId = user.Id
	form.OpUserName = user.Name
	form.Build(form.FleetId)
	tx := model.DB().Begin()
	form.ApplyStatus = util.ApplyStatusForDoing
	err := form.TxCreate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("ComplaintManagement ADD error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbSaveFail)
	}
	//发起流程审批
	if config.Config.Lbpm.Enable {
		byteParam, _ := json.Marshal(form)
		processTitle := fmt.Sprintf("%s提交的路线基准天数变更", form.OpUserName)
		_, err = processService.NewDispatchProcess(user, config.LineBaseDayApplyFormTemplate, processTitle, form.Id, form.TableName(), form.ApplyStatusFieldName(), string(byteParam), nil)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
			return response.Error(rsp, err.Error())
		}
	}

	tx.Commit()
	return response.Success(rsp, nil)
}

func (*OperationReport) LineBaseDayConfigList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form LineBaseDayConfigHandler
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	qs := model.NewQs(form)
	list, totalCount, err := (&operation.LineBaseDayConfig{}).Records(qs, form.CorporationId, form.Paginator)
	if err != nil {
		log.ErrorFields("LineBaseDayConfig Records error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if list != nil {
		for index := range list {
			_, list[index].CorporationName = list[index].Corporations.GetCorporation()
			var process processModel.LbpmApplyProcess
			err = process.GetProcessByItemId(list[index].Id, list[index].TableName())
			list[index].CurrentHandler = process.CurrentHandlerUserName
		}
	}

	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

type StaffLeaveScrap struct {
	Id          int64  `json:"Id" validate:"required"`
	ScrapReason string `json:"ScrapReason"`
}

func (*OperationReport) LineBaseDayConfigScrap(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form StaffLeaveScrap
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	var data operation.LineBaseDayConfig
	_ = data.GetById(form.Id)
	if data.ApplyStatus != util.ApplyStatusForDone {
		return response.Error(rsp, "未通过审核，无法作废")
	}
	user := auth.User(ctx).GetUser()
	data.ScrapDate = model.LocalTime(time.Now())
	data.ScrapReason = form.ScrapReason
	data.ScrapUserId = user.Id
	data.ScrapUserName = user.Name
	err := data.Updates()
	if err != nil {
		log.ErrorFields("LineBaseDayConfig Updates error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbUpdateFail)
	}
	return response.Success(rsp, nil)
}
