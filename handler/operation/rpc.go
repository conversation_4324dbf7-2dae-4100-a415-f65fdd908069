package operation

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model/operation"
	erpProto "app/org/scs/erpv2/api/proto/rpc/erp"
	"context"
	"errors"
	"time"
)

type RpcOperation struct {
}

func (ro *RpcOperation) GetErpDriverOperationInfo(ctx context.Context, req *erpProto.GetErpDriverOperationInfoRequest, rsp *erpProto.GetErpDriverOperationInfoResponse) error {
	log.PrintFields("GetErpDriverOperationInfo Req", map[string]interface{}{"req": req})
	if req.StaffId == 0 || req.StartAt == 0 || req.EndAt == 0 {
		return errors.New("PARAMS MISSING")
	}

	//获取司机运营数据
	reports := (&operation.LineVehicleMileageReport{}).GetDriverOperationInfo(req.StaffId, time.Unix(req.StartAt, 0), time.Unix(req.EndAt, 0))
	for i := range reports {
		rsp.Items = append(rsp.Items, &erpProto.DriverOperationReport{
			Id:                     reports[i].Id,
			WorkDayCount:           reports[i].WorkDayCount,
			FullDoneCircle:         reports[i].FullDoneCircle,
			CircleMileage:          reports[i].CircleMileage,
			StopWorkRatedMileage:   reports[i].StopWorkRatedMileage,
			FullInOutDepotMileage:  reports[i].FullInOutDepotMileage,
			FullAssistantMileage:   reports[i].FullAssistantMileage,
			CharterBusMileage:      reports[i].CharterBusMileage,
			TotalMileage:           reports[i].TotalMileage,
			TotalWorkTimeLength:    reports[i].TotalWorkTimeLength,
			TotalNotWorkTimeLength: reports[i].TotalNotWorkTimeLength,
			ReportAt:               time.Time(reports[i].ReportAt).Unix(),
		})
	}
	return nil
}

func (ro *RpcOperation) ConfirmCharteredOrderTrip(ctx context.Context, req *erpProto.ConfirmCharteredOrderTripRequest, rsp *erpProto.ConfirmCharteredOrderTripResponse) error {
	rsp.Code = "0"
	rsp.Msg = "success"
	if req.CharteredOrderTripId == 0 {
		rsp.Code = "OP1001"
		rsp.Msg = "行程单ID不存在"
		return nil
	}

	//查询行程单
	trip := (&operation.CharteredOrderTrip{}).FirstById(req.CharteredOrderTripId)
	if trip.Id == 0 {
		rsp.Code = "OP1001"
		rsp.Msg = "行程单不存在"
		return nil
	}

	return nil
}
