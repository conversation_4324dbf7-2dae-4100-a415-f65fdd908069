package management

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/export"
	"app/org/scs/erpv2/api/model/operation"
	protoStation "app/org/scs/erpv2/api/proto/rpc/station"
	"app/org/scs/erpv2/api/service/auth"
	exportService "app/org/scs/erpv2/api/service/export"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	xlsxUtil "app/org/scs/erpv2/api/util/xlsx"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"gorm.io/gorm"
	"math"
	"path/filepath"
	"strings"
	"time"
)

type CustomLineFolderParam struct {
	ParentFolderId int64  `json:"ParentFolderId"`
	FolderId       int64  `json:"FolderId"`
	Name           string `json:"Name"`

	// 删除
	FolderIds []int64 `json:"FolderIds"`
}

type CustomLineFolder struct {
	ParentFolderId int64 `json:"ParentFolderId"` // 父文件夹ID

	FolderId int64  `json:"FolderId"` // 文件夹ID
	Name     string `json:"Name"`
	Sort     int64  `json:"Sort"`

	// 线路
	CustomLineId int64 `json:"CustomLineId"`
	Sheet        int64 `json:"Sheet"`
	ColorValue   int64 `json:"ColorValue"`
}

// 获取文件夹
func (*DataManagement) ListCustomLineFolder(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CustomLineFolderParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var topCorpId = auth.User(ctx).GetTopCorporationId()
	if topCorpId == 0 {
		return response.Error(rsp, "PA1106")
	}

	// 查询所有线路列表
	var opc = operation.CustomLine{}
	lineItems := opc.GetAllBy(topCorpId)

	var opf = operation.Folder{}
	folderItems := opf.GetAllBy(topCorpId)

	var retItems = make([]CustomLineFolder, 0)
	for _, folderItem := range folderItems {
		retItems = append(retItems, CustomLineFolder{
			ParentFolderId: folderItem.ParentFolderId,
			FolderId:       folderItem.Id,
			Name:           folderItem.Name,
			Sort:           folderItem.Sort,
		})
	}

	for _, lineItem := range lineItems {
		retItems = append(retItems, CustomLineFolder{
			FolderId:     lineItem.FolderId,
			Name:         lineItem.Name,
			Sort:         lineItem.Sort,
			CustomLineId: lineItem.Id,
			Sheet:        lineItem.Sheet,
			ColorValue:   lineItem.ColorValue,
		})
	}

	return response.Success(rsp, map[string]interface{}{
		"Items": retItems,
	})
}

func getMaxSort() int64 {
	var cl operation.CustomLine
	maxSortL := cl.GetMaxSortBy()

	var ofl operation.Folder
	maxSortF := ofl.GetMaxSortBy()

	if maxSortL > maxSortF {
		return maxSortL + operation.SortInterval
	} else {
		return maxSortF + operation.SortInterval
	}
}

// 新增文件夹
func (*DataManagement) AddCustomLineFolder(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CustomLineFolderParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Name == "" {
		return response.Error(rsp, response.ParamsInvalid)
	}

	var opUserId = auth.User(ctx).GetUserId()
	var topCorpId = auth.User(ctx).GetTopCorporationId()
	if topCorpId == 0 {
		return response.Error(rsp, "PA1106")
	}

	var f = operation.Folder{
		TopCorporationId: topCorpId,
		Name:             param.Name,
		ParentFolderId:   param.ParentFolderId,
		OpUser: model.OpUser{
			OpUserId:   opUserId,
			OpUserName: auth.NewUserById(opUserId).Name,
		},
	}
	err = f.Create()
	if err != nil {
		log.ErrorFields("create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

// 编辑文件夹
func (*DataManagement) EditCustomLineFolder(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CustomLineFolderParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.Name == "" || param.FolderId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	var f = operation.Folder{
		PkId: model.PkId{Id: param.FolderId},
	}
	var as = map[string]interface{}{
		"ParentFolderId": param.ParentFolderId,
		"Name":           param.Name,
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = f.Update(tx, as)
	if err != nil {
		log.ErrorFields("Folder update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

// 删除文件夹
func (*DataManagement) DelCustomLineFolder(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CustomLineFolderParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	for _, folderId := range param.FolderIds {
		if folderId == 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}
		code := DelCustomLineFolderWithFolderId(folderId, ctx, req, rsp)
		if code != response.SUCCESS {
			return response.Error(rsp, code)
		}
	}

	return response.Success(rsp, nil)
}

func DelCustomLineFolderWithFolderId(folderId int64, ctx context.Context, req *api.Request, rsp *api.Response) string {

	var err error
	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	var f = operation.Folder{}
	err = f.Delete(tx, folderId)
	if err != nil {
		log.ErrorFields("Delete error", map[string]interface{}{"err": err})
		return response.DbSaveFail
	}
	var p = operation.CustomLine{}

	// 查询所有线路
	items := p.GetByFolderId(tx, folderId)
	for _, item := range items {
		err = DelCustomLineByCustomLineId(tx, item.Id)
		if err != nil {
			log.ErrorFields("DelCustomLineByCustomLineId error", map[string]interface{}{"err": err})
			return response.DbSaveFail
		}
	}
	return response.SUCCESS
}

type SortCustomLineFolderParam struct {
	Items []CustomLineFolderItem `json:"Items"`
}

type CustomLineFolderItem struct {
	ParentFolderId int64 `json:"ParentFolderId"`
	FolderId       int64 `json:"FolderId"`
	CustomLineId   int64 `json:"CustomLineId"`
	Sort           int64 `json:"Sort"`
}

// 文件夹排序
func (*DataManagement) SortCustomLineFolder(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param SortCustomLineFolderParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(param.Items) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	for _, v := range param.Items {
		if v.CustomLineId == 0 && v.FolderId == 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	for _, v := range param.Items {

		if v.CustomLineId > 0 {
			var fl = operation.CustomLine{
				PkId: model.PkId{Id: v.CustomLineId},
			}
			var as = map[string]interface{}{
				"FolderId": v.FolderId,
				"Sort":     v.Sort,
			}

			err = fl.Update(tx, as)
			if err != nil {
				log.ErrorFields("CustomLine update error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}
		}

		if v.CustomLineId == 0 && v.FolderId > 0 {

			var fl = operation.Folder{
				PkId: model.PkId{Id: v.FolderId},
			}
			var as = map[string]interface{}{
				"ParentFolderId": v.ParentFolderId,
				"Sort":           v.Sort,
			}

			err = fl.Update(tx, as)
			if err != nil {
				log.ErrorFields("Folder update error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}
		}

	}
	return response.Success(rsp, nil)
}

type AddCustomLineParam struct {
	Items []CustomLineParam `json:"Items"`
}

type CustomLineParam struct {
	CustomLineIds []int64             `json:"CustomLineIds"`
	CustomLineId  int64               `json:"CustomLineId"`
	Sheet         int64               `json:"Sheet"`
	Name          string              `json:"Name"`
	FolderId      int64               `json:"FolderId"`
	ColorValue    int64               `json:"ColorValue"`
	StationItems  []CustomStationItem `json:"StationItems"`
}

type CustomStationItem struct {
	Sequence  int64   `json:"Sequence"`  // 站序
	StationId int64   `json:"StationId"` // 站点编号
	Longitude float64 `json:"Longitude"` // 经度, 如 120.xxxxxx
	Latitude  float64 `json:"Latitude"`  // 纬度, 如 30.xxxxxx
}

// 新增线路
func (*DataManagement) AddCustomLine(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AddCustomLineParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	for _, p := range param.Items {
		p.Name = strings.TrimSpace(p.Name)
		if len(p.Name) == 0 || !(p.Sheet >= util.LineSheetForUp && p.Sheet <= util.LineSheetForCircle) {
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	var topCorpId = auth.User(ctx).GetTopCorporationId()
	if topCorpId == 0 {
		return response.Error(rsp, "PA1106")
	}

	tx := model.DB().Begin()

	for _, p := range param.Items {
		code := addSingleCustomLine(tx, topCorpId, p)
		if code != response.SUCCESS {
			tx.Rollback()
			return response.Error(rsp, code)
		}
	}
	tx.Commit()

	return response.Success(rsp, nil)
}

func addSingleCustomLine(tx *gorm.DB, topCorpId int64, param CustomLineParam) string {
	var p = operation.CustomLine{
		TopCorporationId: topCorpId,
		Name:             param.Name,
		Sheet:            param.Sheet,
		FolderId:         param.FolderId,
		ColorValue:       param.ColorValue,
		Sort:             getMaxSort(),
	}
	err := p.Create(tx)
	if err != nil {
		log.ErrorFields("CustomLine create error", map[string]interface{}{"err": err})
		return response.DbSaveFail
	}

	if len(param.StationItems) > 0 {
		for key, stationItem := range param.StationItems {

			var oetStationItem *protoStation.OetStationItem
			if stationItem.StationId > 0 {
				oetStationItem = rpc.GetStationWithId(context.Background(), stationItem.StationId)
				if oetStationItem == nil {
					return response.DbNotFoundRecord
				}
			}

			var ls = operation.CustomLineStation{
				TopCorporationId: topCorpId,
				LineId:           p.Id,
				StationId:        stationItem.StationId,
				Sequence:         stationItem.Sequence,
				Longitude:        int64(stationItem.Longitude * math.Pow(10, 6)),
				Latitude:         int64(stationItem.Latitude * math.Pow(10, 6)),
				Sort:             int64(key + 1),
			}

			if oetStationItem != nil {
				ls.Longitude = int64(oetStationItem.Longitude * math.Pow(10, 6))
				ls.Latitude = int64(oetStationItem.Latitude * math.Pow(10, 6))
			}
			err = ls.Create(tx)
			if err != nil {
				log.ErrorFields("CustomLine create error", map[string]interface{}{"err": err})
				return response.DbSaveFail
			}
		}
	}
	return response.SUCCESS
}

// 编辑线路
func (*DataManagement) EditCustomLine(ctx context.Context, req *api.Request, rsp *api.Response) error {

	var param CustomLineParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.Name = strings.TrimSpace(param.Name)
	if len(param.Name) == 0 || !(param.Sheet >= util.LineSheetForUp && param.Sheet <= util.LineSheetForCircle) || param.CustomLineId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	var p = operation.CustomLine{
		PkId: model.PkId{Id: param.CustomLineId},
	}

	var as = map[string]interface{}{
		"Sheet":      param.Sheet,
		"Name":       param.Name,
		"ColorValue": param.ColorValue,
		"FolderId":   param.FolderId,
	}

	err = p.Update(tx, as)
	if err != nil {
		log.ErrorFields("CustomLine Update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

// 删除线路
func (*DataManagement) DelCustomLine(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CustomLineParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if len(param.CustomLineIds) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	for _, customLineId := range param.CustomLineIds {
		if customLineId == 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	for _, customLineId := range param.CustomLineIds {
		err = DelCustomLineByCustomLineId(tx, customLineId)
		if err != nil {
			log.ErrorFields("DelCustomLineByCustomLineId error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	return response.Success(rsp, nil)
}

func DelCustomLineByCustomLineId(tx *gorm.DB, customLineId int64) error {
	var p = operation.CustomLine{
		PkId: model.PkId{Id: customLineId},
	}
	err := p.DeleteById(tx)
	if err != nil {
		log.ErrorFields("CustomLine DeleteById error", map[string]interface{}{"err": err})
		return err
	}
	// 删除线路站点
	var ps = operation.CustomLineStation{}
	err = ps.DeleteByLineId(tx, customLineId)
	if err != nil {
		log.ErrorFields("CustomLineStation DeleteByLineId error", map[string]interface{}{"err": err})
		return err
	}
	return nil
}

// 同步线路站点
func (*DataManagement) SyncCustomLine(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CustomLineParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.CustomLineId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	var pl = operation.CustomLine{}
	err = pl.GetById(param.CustomLineId)
	if err != nil {
		log.ErrorFields("pl.GetById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	var p = operation.CustomLineStation{}
	err = p.DeleteByLineId(tx, param.CustomLineId)
	if err != nil {
		log.ErrorFields("CustomLine DeleteById error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	if len(param.StationItems) > 0 {
		for key, stationItem := range param.StationItems {
			var oetStationItem *protoStation.OetStationItem
			if stationItem.StationId > 0 {
				oetStationItem = rpc.GetStationWithId(ctx, stationItem.StationId)
				if oetStationItem == nil {
					return response.Error(rsp, response.DbNotFoundRecord)
				}
			}

			var ls = operation.CustomLineStation{
				TopCorporationId: pl.TopCorporationId,
				LineId:           pl.Id,
				StationId:        stationItem.StationId,
				Sequence:         stationItem.Sequence,
				Longitude:        int64(stationItem.Longitude * math.Pow(10, 6)),
				Latitude:         int64(stationItem.Latitude * math.Pow(10, 6)),
				Sort:             int64(key + 1),
			}

			if oetStationItem != nil {
				ls.Longitude = int64(oetStationItem.Longitude * math.Pow(10, 6))
				ls.Latitude = int64(oetStationItem.Latitude * math.Pow(10, 6))
			}
			err = ls.Create(tx)
			if err != nil {
				log.ErrorFields("CustomLine create error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbSaveFail)
			}
		}
	}

	return response.Success(rsp, nil)
}

type LineStationItem struct {
	Id          int64   `json:"Id,omitempty"`          // 操作线路站点 Id
	LineName    string  `json:"LineName,omitempty"`    // 线路名
	StationId   int64   `json:"StationId,omitempty"`   // 站点 Id
	StationName string  `json:"StationName,omitempty"` // 站点名
	StationCode string  `json:"StationCode,omitempty"` // 站点编号
	Sequence    int64   `json:"Sequence,omitempty"`    // 站点序号
	Sheet       int64   `json:"Sheet,omitempty"`       // 站点方向, 1:上行, 2:下行, 3:环形
	Longitude   float64 `json:"Longitude,omitempty"`   // 经度, 原始坐标, 如 120.xxxxxx
	Latitude    float64 `json:"Latitude,omitempty"`    // 纬度, 原始坐标, 如 30.xxxxxx
	FirstLast   int64   `json:"FirstLast,omitempty"`   // 站点类型, 0-中途站；1-起点站；2-终点站；3-普通采样点；4-考核点
	IsNormal    int64   `json:"IsNormal,omitempty"`    // 是否大站, 1:普通站,2:大站

	Direction int64 `json:"Direction,omitempty"` // 站点方位, 1:东向西,2:西向东,3:南向北,4:北向南
	Capacity  int64 `json:"Capacity,omitempty"`  // 泊位数

	StationType int64 `json:"StationType,omitempty"` // 站点类型 1-起点站；2-中途站；3-终点站；
	BizStatus   int64 `json:"BizStatus,omitempty"`   // 运营状态 1:正常、2:停运、3:维修检测、4:试运行、
	// 5:临时使用、6:停用、7:临时停用、8:废弃、999:其他
	AdminIsTrAtIveDivision string `json:"AdminIsTrAtIveDivision,omitempty"` // 行政区划
	StreetName             string `json:"StreetName,omitempty"`             // 街道名称
	OwnedRoad              string `json:"OwnedRoad,omitempty"`              // 所在道路
	OwnerUnit              string `json:"OwnerUnit,omitempty"`              // 业主单位
	CoordinateSystem       int64  `json:"CoordinateSystem,omitempty"`       // 坐标系统,1-WGS84, 2-GCJ02
	AllAround              string `json:"AllAround,omitempty"`              // 周边景点

	PlatformType int64 `json:"PlatformType,omitempty"` // 站台类型，1-港湾式，2-直线式
	BusBoardType int64 `json:"BusBoardType,omitempty"` // 站牌类型，1-普通站牌，2-电子站牌，
	// 3-简易站牌
	ElecBusBoardType int64 `json:"ElecBusBoardType,omitempty"` // 电子站牌类型，1-立式，2-嵌入，3-吊装

	RegionName  string `json:"RegionName,omitempty"`
	StationSize string `json:"StationSize,omitempty"` // 站点尺寸
}

// 查询线路站点
func (*DataManagement) ListCustomLineStation(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CustomLineParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.CustomLineId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	retItems := listCustomLineStation(param)

	return response.Success(rsp, map[string]interface{}{
		"Items":      retItems,
		"TotalItems": len(retItems),
	})
}

func listCustomLineStation(param CustomLineParam) (retItems []LineStationItem) {
	// 查询线路详情
	var pcl = operation.CustomLine{}
	pcl.GetById(param.CustomLineId)

	// 查询所有站点
	var p = operation.CustomLineStation{}
	items := p.GetAllByLineId(param.CustomLineId)

	lineStationCount := p.GetCountByLineId(param.CustomLineId)

	var r = operation.CorpRegion{}
	regionItems := r.GetByTopCorpId(pcl.TopCorporationId)

	var (
		seq int64
	)
	for _, item := range items {
		if item.StationId > 0 {
			seq++
			var protoStationOpt = protoStation.GetStationsWithOption2Request{
				CorporationId: pcl.TopCorporationId,
				StationIds:    []int64{item.StationId},
				Offset:        0,
				Limit:         1,
				Order:         "asc",
			}

			stationItems, _ := rpc.GetStationsWithOption2(context.Background(), &protoStationOpt)
			if len(stationItems) > 0 {
				var oetStationItem = stationItems[0]
				if oetStationItem != nil {
					retItems = append(retItems, getLineStationItemWithStation(pcl.Name, pcl.Sheet, seq, item, oetStationItem, lineStationCount, regionItems))
				}
				continue
			}
		}
		retItems = append(retItems, LineStationItem{
			Id:        item.Id,
			Longitude: float64(item.Longitude) / math.Pow(10, 6), // 经度, 如 120.xxxxxx
			Latitude:  float64(item.Latitude) / math.Pow(10, 6),  // 纬度, 如 30.xxxxxx
			FirstLast: util.Station_Queue_Sample,
		})
	}
	return
}

func getLineStationItemWithStation(lineName string, sheet, seq int64, item operation.CustomLineStation, stationItem *protoStation.OetStationItem, lineStationCount int64, regionItems []operation.CorpRegion) LineStationItem {
	var firstLast int64
	if seq == 1 {
		firstLast = util.Station_Queue_First
	}

	if seq == lineStationCount {
		firstLast = util.Station_Queue_Last
	}

	var ssItem = (&operation.SpecificationSetting{}).GetByStationId(stationItem.Id)
	var stationSize string
	if ssItem.Id > 0 {
		stationSize = fmt.Sprintf("长:%vcm 高: %vcm", float64(ssItem.Length)/float64(10), float64(ssItem.Width)/float64(10))
	}

	return LineStationItem{
		Id:          item.Id,               // 操作线路站点 Id
		LineName:    lineName,              // 线路名
		StationId:   item.StationId,        // 站点 Id
		StationName: stationItem.Name,      // 站点名
		StationCode: stationItem.Code,      // 站点编号
		Sequence:    seq,                   // 站点序号
		Sheet:       sheet,                 // 站点方向, 1:上行, 2:下行, 3:环形
		Longitude:   stationItem.Longitude, // 经度, 原始坐标, 如 120.xxxxxx
		Latitude:    stationItem.Latitude,  // 纬度, 原始坐标, 如 30.xxxxxx
		FirstLast:   firstLast,             // 站点类型, 0-中途站；1-起点站；2-终点站；3-普通采样点；4-考核点
		IsNormal:    1,                     // 是否大站, 1:普通站,2:大站

		Direction: stationItem.Direction, // 站点方位, 1:东向西,2:西向东,3:南向北,4:北向南
		Capacity:  stationItem.Capacity,  // 泊位数

		StationType: stationItem.StationType, // 站点类型 1-起点站；2-中途站；3-终点站；
		BizStatus:   stationItem.BizStatus,   // 运营状态 1:正常、2:停运、3:维修检测、4:试运行、
		// 5:临时使用、6:停用、7:临时停用、8:废弃、999:其他
		AdminIsTrAtIveDivision: stationItem.AdminIsTrAtIveDivision, // 行政区划
		StreetName:             stationItem.StreetName,             // 街道名称
		OwnedRoad:              stationItem.OwnedRoad,              // 所在道路
		OwnerUnit:              stationItem.OwnerUnit,              // 业主单位
		CoordinateSystem:       stationItem.CoordinateSystem,       // 坐标系统,1-WGS84, 2-GCJ02
		AllAround:              stationItem.AllAround,              // 周边景点

		PlatformType: stationItem.PlatformType, // 站台类型，1-港湾式，2-直线式
		BusBoardType: stationItem.BusBoardType, // 站牌类型，1-普通站牌，2-电子站牌，
		// 3-简易站牌
		ElecBusBoardType: stationItem.ElecBusBoardType, // 电子站牌类型，1-立式，2-嵌入，3-吊装

		RegionName:  operation.GetRegionName(regionItems, stationItem.AdminIsTrAtIveDivision),
		StationSize: stationSize, // 站点尺寸
	}
}

// 导出线路站点
func (this *DataManagement) ListCustomLineStationExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param CustomLineParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.CustomLineId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 查询线路详情
	var pcl = operation.CustomLine{}
	pcl.GetById(param.CustomLineId)

	var (
		user     = auth.User(ctx)
		startAt  = time.Now()
		endAt    = time.Now()
		fileName = fmt.Sprintf("%v(%v)_线路仿真.xlsx", pcl.Name, util.LineSheetMap[pcl.Sheet])
	)

	exportFileRecord, err := exportService.CreateExportFileRecord(user.GetUserId(), fileName, "ListCustomLineStationExport", []byte(req.Body), startAt, endAt)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	go generatePosterByCustomLine(param, exportFileRecord)
	return response.Success(rsp, nil)

}

func generatePosterByCustomLine(param CustomLineParam, efr export.ExportFile) {
	stationItems := listCustomLineStation(param)
	// 生成excel文件
	err := generateCustomLineExcel(exportService.GetExcelFolder(efr), efr.Key, stationItems)
	if err != nil {
		efr.UpdateFail()
		log.ErrorFields("generateCustomLineExcel error", map[string]interface{}{"err": err})
		return
	}
	efr.UpdateSuccess()
	return

}

// 导出路牌-线路维度
func (*DataManagement) ListStreetPlateByCustomLine(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListStreetPlateByLineParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.OetLineItem == nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.LineId <= 0 || param.Sheet <= 0 || param.Price <= 0 || param.FirstTime <= 0 ||
		param.LastTime <= 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	if setFont() == nil {
		return response.Error(rsp, "OP9903")
	}

	var (
		user    = auth.User(ctx)
		startAt = time.Now()
		endAt   = time.Now()
	)

	var (
		lineInfo = param.OetLineItem
		lineItem operation.CustomLine
	)

	err = lineItem.GetById(param.LineId)
	if err != nil {
		log.ErrorFields("customLine  GetById is nil ", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	var (
		folder  = fmt.Sprintf("%v_%v", lineItem.Name, util.LineSheetMap[lineInfo.Sheet])
		zipName = fmt.Sprintf("%v.zip", folder)
	)

	exportFileRecord, err := exportService.CreateExportFileRecord(user.GetUserId(), zipName, "ListStreetPlateByCustomLine", []byte(req.Body), startAt, endAt)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	go streetPlateByCustomLine(param, exportFileRecord, lineItem)

	return response.Success(rsp, nil)
}

func streetPlateByCustomLine(param ListStreetPlateByLineParam, efr export.ExportFile, lineItem operation.CustomLine) {

	path, err := NewPosterTaskSvc().makePosterWithCustomLine(param, lineItem, efr.FileName, strings.TrimSuffix(efr.FileName, filepath.Ext(efr.FileName)))
	if err != nil {
		efr.UpdateFail()
		log.ErrorFields("makePosterWithCustomLine error", map[string]interface{}{"err": err})
		return
	}
	efr.Path = strings.Replace(path, config.Config.AbsDirPath, "", -1)
	efr.Status = util.ExportFileStatusForDone
	efr.UpdateStatus()
}

// 省级
func generateStationExcelWithProvincialDemand(basePath, fileName string, stationItems []OetStationItem) error {

	var (
		style       = xlsxUtil.GetExcelPubStyle()
		stringStyle = xlsx.MakeStringStyle(&style.Font, &style.Fill, &style.Alignment, &style.Border)
	)

	// 表头
	var xlsxCellMetadataAndHeaders = []xlsxUtil.XlsxCellMetadataAndHeader{
		{HeaderName: "序号", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点ID", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "县级名称", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "街道/镇名称", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点名称", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点类型", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点经度", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点纬度", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "坐标系", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点方向", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点设施", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站牌", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "电子站牌", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "二维码查询", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "备注", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
	}

	streamFile, err := xlsxUtil.GetStreamFile(fileName, basePath, xlsxCellMetadataAndHeaders)
	if err != nil {
		log.ErrorFields("GetStreamFile error [%v]", map[string]interface{}{"err": err})
		return err
	}

	// 第二行
	var (
		sCells []string
	)
	for i := 0; i < 15; i++ {
		switch i {
		case 2:
			sCells = append(sCells, "行政区划")
		case 10:
			sCells = append(sCells, "站台类型")
		case 11:
			sCells = append(sCells, "默认为空")
		case 13:
			sCells = append(sCells, "默认为有")
		case 14:
			sCells = append(sCells, "途经线路--方向")
		default:
			sCells = append(sCells, "")
		}
	}
	err = streamFile.WriteWithColumnDefaultMetadata(sCells)
	if err != nil {
		log.ErrorFields("streamFile.WriteWithColumnDefaultMetadata error[%v]", map[string]interface{}{"err": err})
		return err
	}

	itemsLen := len(stationItems)
	if 0 >= itemsLen {
		return nil
	}

	for idx := 0; idx < itemsLen; idx++ {
		var cells []string
		var index = idx + 1
		var item = stationItems[idx]
		cells = append(cells, fmt.Sprintf("%v", index))
		cells = append(cells, item.Code)                                       // 站点ID
		cells = append(cells, item.RegionName)                                 // 行政区划
		cells = append(cells, item.StreetName)                                 // 街道/镇名称
		cells = append(cells, item.Name)                                       // 站点名称
		cells = append(cells, util.OetStationTypeMap[item.StationType])        // 站点类型
		cells = append(cells, fmt.Sprintf("%v", item.Longitude))               // 站点经度
		cells = append(cells, fmt.Sprintf("%v", item.Latitude))                // 站点纬度
		cells = append(cells, util.CoordinateSystemMap[item.CoordinateSystem]) // 坐标系
		cells = append(cells, util.StationDirectionMap[item.Direction])        // 站点方位
		cells = append(cells, util.StationPlatformTypeMap[item.PlatformType])  // 站台类型
		cells = append(cells, util.StationBusBoardTypeMap[item.BusBoardType])  // 站牌类型
		if item.ElecBusBoardType > 0 {
			cells = append(cells, "有")
		} else {
			cells = append(cells, "无")
		}
		// 电子站牌
		cells = append(cells, "有")                    // 二维码查询
		cells = append(cells, GetLineStr(item.Lines)) // 途经线路--方向

		err = streamFile.WriteWithColumnDefaultMetadata(cells)
		if err != nil {
			log.ErrorFields("streamFile.WriteWithColumnDefaultMetadata error[%v]", map[string]interface{}{"err": err})
			return err
		}
	}

	// 写文件
	if err = streamFile.Close(); err != nil {
		return errors.New(fmt.Sprintf("写入文件异常,错误信息[%v]", err))
	}
	return nil
}

func generateCustomLineExcel(basePath string, fileName string, stationItems []LineStationItem) error {
	var (
		style       = xlsxUtil.GetExcelPubStyle()
		stringStyle = xlsx.MakeStringStyle(&style.Font, &style.Fill, &style.Alignment, &style.Border)
	)

	var xlsxCellMetadataAndHeaders = []xlsxUtil.XlsxCellMetadataAndHeader{
		{HeaderName: "序号", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeNumeric, stringStyle)},
		{HeaderName: "线路名", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "线路方向", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站序", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点名称", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点ID", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点类型", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "经度", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "纬度", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "行政区划", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "所属街道", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "所属道路", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点方位", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站台类型", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站牌类型", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站牌尺寸", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
	}

	streamFile, err := xlsxUtil.GetStreamFile(fileName, basePath, xlsxCellMetadataAndHeaders)
	if err != nil {
		log.ErrorFields("GetStreamFile error [%v]", map[string]interface{}{"err": err})
		return err
	}

	itemsLen := len(stationItems)
	if 0 >= itemsLen {
		return nil
	}
	var index int64
	for idx := 0; idx < itemsLen; idx++ {
		var cells []string

		var item = stationItems[idx]
		if item.StationId == 0 {
			continue
		}
		index++
		cells = append(cells, fmt.Sprintf("%v", index))
		cells = append(cells, item.LineName)                                  // 线路名
		cells = append(cells, util.LineSheetMap[item.Sheet])                  // 线路方向
		cells = append(cells, fmt.Sprintf("%v", item.Sequence))               // 站序
		cells = append(cells, fmt.Sprintf("%v", item.StationName))            // 站点名称
		cells = append(cells, fmt.Sprintf("%v", item.StationCode))            // 站点ID
		cells = append(cells, util.OetStationTypeMap[item.StationType])       // 站点类型
		cells = append(cells, fmt.Sprintf("%v", item.Longitude))              // 经度
		cells = append(cells, fmt.Sprintf("%v", item.Latitude))               // 纬度
		cells = append(cells, item.RegionName)                                // 行政区划
		cells = append(cells, item.StreetName)                                // 所属街道
		cells = append(cells, item.OwnedRoad)                                 // 所属道路
		cells = append(cells, util.StationDirectionMap[item.Direction])       // 站点方位
		cells = append(cells, util.StationPlatformTypeMap[item.PlatformType]) // 站台类型
		cells = append(cells, util.StationBusBoardTypeMap[item.BusBoardType]) // 站牌类型
		cells = append(cells, item.StationSize)                               // 站牌尺寸

		err = streamFile.WriteWithColumnDefaultMetadata(cells)
		if err != nil {
			log.ErrorFields("streamFile.WriteWithColumnDefaultMetadata error[%v]", map[string]interface{}{"err": err})
			return err
		}
	}

	// 写文件
	if err = streamFile.Close(); err != nil {
		return errors.New(fmt.Sprintf("写入文件异常,错误信息[%v]", err))
	}
	return nil
}

func GetLineStr(items []*protoStation.OetLineItem) string {
	var str string
	for _, item := range items {
		if str == "" {
			str = fmt.Sprintf("%s(%s)", item.LineName, util.LineSheetMap[item.Sheet])
		} else {
			str += fmt.Sprintf(",%s(%s)", item.LineName, util.LineSheetMap[item.Sheet])
		}
	}
	return str
}
