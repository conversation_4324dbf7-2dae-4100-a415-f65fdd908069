package management

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model/file"
	"app/org/scs/erpv2/api/model/operation"
	protoLine "app/org/scs/erpv2/api/proto/rpc/oetline"
	protoLineStation "app/org/scs/erpv2/api/proto/rpc/oetlinestation"
	protoStation "app/org/scs/erpv2/api/proto/rpc/station"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/golang/freetype/truetype"
	"github.com/nfnt/resize"
	"github.com/xujiangtao0207/gg"
	"golang.org/x/image/font"
	"io/ioutil"
	"math"
	"os"
	"sort"
	"strings"
	"sync"
	"time"
)

// 画布位置
const (
	P_LineNameArea        = "P1" // 线路名
	P_StartStationArea    = "P2" // 起点站
	P_FirstLastTimeArea   = "P3" // 首班时间 末班时间
	P_NextStationFlagArea = "P4" // 下站或终点站
	P_NextStationNameArea = "P5" // 下一站内容
	P_LineStationNameArea = "P6" // 站点列表
	P_LastStationArea     = "P7" // 目的地
	P_PriceArea           = "P8" // 票价
)

// 画布填充数据类型
const (
	Poster_Config_Type_Text    = 1 // 1:文字
	Poster_Config_Type_Image   = 2 // 2:图片
	Poster_Config_Type_Station = 3 //  3:站点列表
)

type PosterConfig struct {
	FileName string `json:"FileName"` // 文件名
	FilePath string `json:"FilePath"` // 文件路径
	Width    int    `json:"Width"`    // 画布宽度
	Height   int    `json:"Height"`   // 画布高度

	PaddingLeft   int `json:"PaddingLeft"`   // 画布左侧留白
	PaddingRight  int `json:"PaddingRight"`  // 画布右侧留白
	PaddingTop    int `json:"PaddingTop"`    // 画布顶部留白
	PaddingBottom int `json:"PaddingBottom"` // 画布底部留白
	Items         []PosterConfigItem
}
type PosterConfigItem struct {
	Type   int    `json:"Type"`   // 内容类型：1文字，2图片 3:站点列表
	Remark string `json:"Remark"` // 备注
	X      int    `json:"X"`      // 水平坐标，从左到右
	Y      int    `json:"Y"`      // 垂直坐标，从上到下
	Width  int    `json:"Width"`  // 内容宽度
	Height int    `json:"Height"` // 内容高度

	Content    string `json:"Content"`    // 文字内容
	FontSize   int    `json:"FontSize"`   // 文字大小
	FontFamily int    `json:"FontFamily"` // 字体 1宋体 2黑体 3楷体 4仿宋 5圆体
	TextAlign  int    `json:"TextAlign"`  // 文字定位：0居左（默认），1居中
	Color      string `json:"Color"`      // 文字颜色："#000"格式

	Url string `json:"Url"` // 图片地址：外部链接 或 本地文件

	LineStationItems []PosterLineStation
}
type PosterLineStation struct {
	StationId       int64    `json:"StationId"`
	Sequence        int64    `json:"Sequence"`
	Direction       int64    `json:"Direction"`
	Sheet           int64    `json:"Sheet"`
	RailInterchange string   `json:"RailInterchange"` // 是否换乘站
	Name            string   `json:"Name"`
	NameList        []string `json:"NameList"` // 站点名称分多列
}

type LineMidItem struct {
	Name             string
	Sheet            int64
	FirstStationName string
	LastStationName  string
	FirstTime        int64
	LastTime         int64
	Price            int64
}

type PosterTaskSvc struct {
	TopCorpId int64
	// 加载
	Font *truetype.Font
}

func PosterTaskServe() {
	time.Sleep(7 * time.Second)
	//NewPosterTaskSvc().makePosterWithStation(ListStreetPlateByStationParam{
	//	Lines: []*protoStation.OetLineItem{
	//		&protoStation.OetLineItem{LineId: 173, Sheet: 1},
	//	},
	//	StationId: 21185,
	//})
	//var svc = NewPosterTaskSvc()
	//setFont()
	//
	//svc.makePosterWithLine(ListStreetPlateByLineParam{
	//	OetLineItem: &protoStation.OetLineItem{LineId: 332, Sheet: 1},
	//	Price:       100,
	//	FirstTime:   3600*5 + 60*30,
	//	LastTime:    3600*20 + 60*30,
	//})
	//svc.Font = nil
	//svc = nil

	//var svc = NewPosterTaskSvc()
	//setFont()
	//
	//svc.makePosterWithCustomLine(ListStreetPlateByLineParam{
	//	OetLineItem: &protoStation.OetLineItem{LineId: 7203641828567638016, Sheet: 1},
	//	Price:       500,
	//	FirstTime:   41880,
	//	LastTime:    45480,
	//})
	//svc.Font = nil
	//svc = nil

}

func NewPosterTaskSvc() *PosterTaskSvc {
	return &PosterTaskSvc{}
}

func (this *PosterTaskSvc) getPosterLineStations(lineStations []*protoLineStation.OetLineStationItem) []PosterLineStation {
	var retItems = make([]PosterLineStation, 0)
	for _, item := range lineStations {
		if item.Sequence <= 0 {
			continue
		}
		retItems = append(retItems, PosterLineStation{
			StationId:       item.StationId,
			Sequence:        item.Sequence,
			Direction:       item.Direction,
			Sheet:           item.Sheet,
			RailInterchange: item.RailInterchange, // 是否换乘站
			Name:            item.StationName,
		})
	}

	sort.SliceStable(retItems, func(i, j int) bool {
		return retItems[i].Sequence < retItems[j].Sequence
	})

	return retItems
}

func (this *PosterTaskSvc) getPosterLineStationsWithCustomLine(sheet int64, lineStations []operation.CustomLineStation, stations []*protoStation.OetStationItem) ([]PosterLineStation, error) {
	var retItems = make([]PosterLineStation, 0)
	for _, item := range lineStations {
		if item.Sequence <= 0 {
			continue
		}

		var stationItem *protoStation.OetStationItem

		for _, station := range stations {
			if station.Id == item.StationId {
				stationItem = station
				break
			}
		}
		if stationItem != nil {
			retItems = append(retItems, PosterLineStation{
				StationId:       item.StationId,
				Sequence:        item.Sequence,
				Direction:       stationItem.Direction,
				Sheet:           sheet,
				RailInterchange: stationItem.RailInterchange, // 是否换乘站
				Name:            stationItem.Name,
			})
		} else {
			return nil, errors.New(fmt.Sprintf("站点ID:%v数据不存在", item.StationId))
		}
	}

	sort.SliceStable(retItems, func(i, j int) bool {
		return retItems[i].Sequence < retItems[j].Sequence
	})

	return retItems, nil
}

//func CreateExportFileRecord(userId int64, fileName string, param []byte, startAt,endAt time.Time){
//	exportFileRecord, err := exportService.CreateExportFileRecord(auth.User(ctx).GetUserId(), fileName, (&operationModel.LineVehicleMileageReport{}).LineDriverWorkReportTableName(), paramByte, startAt, endAt)
//	if err != nil {
//		return response.Error(rsp, response.FAIL)
//	}
//}

func (this *PosterTaskSvc) makePosterWithStation(p ListStreetPlateByStationParam, zipName, folder string) (string, error) {

	defer clear()
	lineItem, _ := rpc.GetLineWithId(context.Background(), p.Lines[0].LineId)
	if lineItem == nil {
		log.ErrorFields("rpc.GetLineWithId is nil ", nil)
		return "", errors.New("查询线路数据异常")
	}
	this.TopCorpId = lineItem.CorporationId

	baseFullPath, zipTmpFullPath, storageFullPath := getStoragePath("poster", this.TopCorpId, folder)
	for _, lineInfo := range p.Lines {
		oetLineItem, _ := rpc.GetLineWithId(context.Background(), lineInfo.LineId)
		if oetLineItem == nil {
			log.ErrorFields("rpc.GetLineWithId is nil ", nil)
			return "", errors.New("查询线路数据异常")
		}

		var (
			firstTime, lastTime int64
		)
		switch lineInfo.Sheet {
		case util.LineSheetForUp, util.LineSheetForCircle:
			firstTime = oetLineItem.UpFirstTime
			lastTime = oetLineItem.UpLastTime
		case util.LineSheetForDown:
			firstTime = oetLineItem.DownFirstTime
			lastTime = oetLineItem.DownLastTime
		}

		// 线路站点
		var opt = &protoLineStation.GetOetLineStationWithLineIdRequest{
			LineId:     lineInfo.LineId,
			Sheet:      lineInfo.Sheet,
			FilterType: util.Station_Queue_Sample,
		}

		resp := rpc.GetOetLineStationWithLineId(context.Background(), opt)
		if resp == nil {
			log.ErrorFields("rpc.GetOetLineStationWithLineId is nil ", nil)
			return "", errors.New("查询线路站点数据异常")
		}

		retItems := getAllLineStation(resp.Items, resp.SkipItems)
		lineStationItems := this.getPosterLineStations(retItems)

		var stationItems []PosterLineStation
		for _, Item := range lineStationItems {
			if Item.StationId == p.StationId {
				stationItems = append(stationItems, Item)
			}
		}

		var lineMidItem = LineMidItem{
			Name:             oetLineItem.Name,
			Sheet:            lineInfo.Sheet,
			FirstTime:        firstTime,
			LastTime:         lastTime,
			FirstStationName: this.getFirstStation(lineStationItems),
			LastStationName:  this.getLastStation(lineStationItems),
			Price:            oetLineItem.Price,
		}

		for _, stationItem := range stationItems {
			var fileName = fmt.Sprintf("%v_%v_%v_%v_%v", lineMidItem.Name, util.LineSheetMap[lineMidItem.Sheet], stationItem.Sequence, stationItem.Name, util.StationDirectionMap[stationItem.Direction]) + ".png"
			err := NewPostMakeTaskSvc().makeStationPoster(storageFullPath, fileName, lineMidItem, lineStationItems, stationItem)
			if err != nil {
				log.ErrorFields("makeStationPoster is nil ", map[string]interface{}{"err": err})
				return "", err
			}
		}

	}

	// 压缩文件
	util.ZipWriter(zipTmpFullPath, baseFullPath+"/"+zipName)

	////移除临时文件夹
	os.RemoveAll(zipTmpFullPath)

	return baseFullPath + "/" + zipName, nil
}

func (this *PosterTaskSvc) makePosterWithLine(p ListStreetPlateByLineParam, oetLineItem *protoLine.OetLineItem, zipName, folder string) (string, error) {
	defer clear()
	var (
		lineInfo = p.OetLineItem
	)
	this.TopCorpId = oetLineItem.CorporationId

	baseFullPath, zipTmpFullPath, storageFullPath := getStoragePath("poster", this.TopCorpId, folder)
	// 线路站点
	var opt = &protoLineStation.GetOetLineStationWithLineIdRequest{
		LineId:     lineInfo.LineId,
		Sheet:      lineInfo.Sheet,
		FilterType: util.Station_Queue_Sample,
	}

	resp := rpc.GetOetLineStationWithLineId(context.Background(), opt)
	if resp == nil {
		log.ErrorFields("rpc.GetOetLineStationWithLineId is nil ", nil)
		return "", errors.New("查询站点异常")
	}

	retItems := getAllLineStation(resp.Items, resp.SkipItems)
	lineStationItems := this.getPosterLineStations(retItems)

	var lineMidItem = LineMidItem{
		Name:             oetLineItem.Name,
		FirstTime:        p.FirstTime,
		LastTime:         p.LastTime,
		FirstStationName: this.getFirstStation(lineStationItems),
		LastStationName:  this.getLastStation(lineStationItems),
		Price:            p.Price,
	}
	//// 内存问题，并发线程池限制
	for _, stationItem := range lineStationItems {
		var fileName = fmt.Sprintf("%v_%v_%v", stationItem.Sequence, stationItem.Name, util.StationDirectionMap[stationItem.Direction]) + ".png"
		err := NewPostMakeTaskSvc().makeStationPoster(storageFullPath, fileName, lineMidItem, lineStationItems, stationItem)
		if err != nil {
			log.ErrorFields("makeStationPoster is nil ", map[string]interface{}{"err": err})
			return "", err
		}
	}

	// 压缩文件
	util.ZipWriter(zipTmpFullPath, baseFullPath+"/"+zipName)

	////移除临时文件夹
	os.RemoveAll(zipTmpFullPath)

	return baseFullPath + "/" + zipName, nil
}

// 自定义线路
func (this *PosterTaskSvc) makePosterWithCustomLine(p ListStreetPlateByLineParam, lineItem operation.CustomLine, zipName, folder string) (string, error) {
	defer clear()
	this.TopCorpId = lineItem.TopCorporationId

	baseFullPath, zipTmpFullPath, storageFullPath := getStoragePath("poster", this.TopCorpId, folder)
	var cls = operation.CustomLineStation{}
	lineStations := cls.GetByLineId(p.LineId)

	// 查询所有站点
	var stationIds = []int64{}
	for _, lineStationItem := range lineStations {
		stationIds = append(stationIds, lineStationItem.StationId)
	}
	var protoStationOpt = protoStation.GetStationsWithOption2Request{
		CorporationId: this.TopCorpId,
		StationIds:    stationIds,
		Offset:        0,
		Limit:         int64(len(stationIds)),
		Order:         "asc",
	}

	stationItems, _ := rpc.GetStationsWithOption2(context.Background(), &protoStationOpt)

	lineStationItems, err := this.getPosterLineStationsWithCustomLine(lineItem.Sheet, lineStations, stationItems)
	if err != nil {
		log.ErrorFields("getPosterLineStationsWithCustomLine is err  ", map[string]interface{}{"err": err})
		return "", err
	}

	var lineMidItem = LineMidItem{
		Name:             lineItem.Name,
		FirstTime:        p.FirstTime,
		LastTime:         p.LastTime,
		FirstStationName: this.getFirstStation(lineStationItems),
		LastStationName:  this.getLastStation(lineStationItems),
		Price:            p.Price,
	}
	// 内存问题，并发线程池限制
	for _, stationItem := range lineStationItems {
		var fileName = fmt.Sprintf("%v_%v_%v", stationItem.Sequence, stationItem.Name, util.StationDirectionMap[stationItem.Direction]) + ".png"
		err = NewPostMakeTaskSvc().makeStationPoster(storageFullPath, fileName, lineMidItem, lineStationItems, stationItem)
		if err != nil {
			log.ErrorFields("makeStationPoster is nil ", map[string]interface{}{"err": err})
			return "", err
		}
	}

	// 压缩文件
	util.ZipWriter(zipTmpFullPath, baseFullPath+"/"+zipName)

	////移除临时文件夹
	os.RemoveAll(zipTmpFullPath)

	return baseFullPath + "/" + zipName, nil
}

func (this *PosterTaskSvc) getFirstStation(items []PosterLineStation) string {
	if len(items) > 0 {
		return items[0].Name
	}
	return ""
}

func (this *PosterTaskSvc) getLastStation(items []PosterLineStation) string {
	if len(items) > 0 {
		return items[len(items)-1].Name
	}
	return ""
}

type PostMakeTaskSvc struct {
	// 加载
	Font *truetype.Font
}

func NewPostMakeTaskSvc() *PostMakeTaskSvc {
	var svc = &PostMakeTaskSvc{}
	svc.Font = getFont()
	return svc
	//return &PostMakeTaskSvc{}
}

func (this *PostMakeTaskSvc) makePosterParam(lineMidItem LineMidItem, lineStationItems []PosterLineStation, stationItem PosterLineStation) (PosterConfig, error) {
	// 从p1->p8处理信息

	pp := (&operation.SpecificationSetting{}).GetByStationId(stationItem.StationId)
	if pp.Id == 0 {
		return PosterConfig{}, errors.New("站点配置信息不存在")
	}

	var items []operation.PosterSettingItem
	err := json.Unmarshal(pp.SettingItem, &items)
	if err != nil || len(items) == 0 {
		return PosterConfig{}, errors.New("站点配置信息不存在")
	}
	var retItems = make([]PosterConfigItem, 0)
	for _, item := range items {
		var retItem = PosterConfigItem{
			X:      item.X,      // 水平坐标，从左到右
			Y:      item.Y,      // 垂直坐标，从上到下
			Width:  item.Width,  // 内容宽度
			Height: item.Height, // 内容高度

			Content:    item.Content,    // 文字内容
			FontSize:   item.FontSize,   // 文字大小
			FontFamily: item.FontFamily, // 字体
			TextAlign:  item.TextAlign,  // 文字定位：0居左（默认），1居中
			Color:      item.Color,      // 文字颜色："#000"格式

			Url: item.Url, // 图片地址：外部链接 或 本地文件
		}
		switch item.Id {
		case P_LineNameArea: // 线路名
			retItem.Type = Poster_Config_Type_Text
			retItem.Content = lineMidItem.Name
		case P_StartStationArea: // 起点站
			retItem.Type = Poster_Config_Type_Text
			retItem.Content = "起点站"
		case P_FirstLastTimeArea: // 首班时间 末班时间
			retItem.Type = Poster_Config_Type_Text
			retItem.Content = fmt.Sprintf("首班 %v 末班 %v", util.GetMinuteStr(lineMidItem.FirstTime), util.GetMinuteStr(lineMidItem.LastTime))
		case P_NextStationFlagArea: // 下站或终点站
			retItem.Type = Poster_Config_Type_Text
			retItem.Content = fmt.Sprintf("下站")
		case P_NextStationNameArea: // 下一站内容
			retItem.Type = Poster_Config_Type_Text
			retItem.Content = this.NextStationName(lineStationItems, stationItem)
		case P_LineStationNameArea: // 站点列表
			retItem.Type = Poster_Config_Type_Station
			retItem.LineStationItems = lineStationItems
		case P_LastStationArea: // 目的地
			retItem.Type = Poster_Config_Type_Text
			retItem.Content = lineMidItem.LastStationName
		case P_PriceArea: // 票价
			retItem.Type = Poster_Config_Type_Text
			retItem.Content = fmt.Sprintf("%v", util.IDecimal(lineMidItem.Price, 100, 1))
		}

		retItems = append(retItems, retItem)
	}

	// 再次增加背景图片
	if pp.FileId > 0 {
		var filePath = config.Config.AbsDirPath + (&file.File{}).FindBy(pp.FileId).Path
		retItems = append(retItems, PosterConfigItem{
			Type:   Poster_Config_Type_Image,
			X:      0,              // 水平坐标，从左到右
			Y:      0,              // 垂直坐标，从上到下
			Width:  int(pp.Width),  // 内容宽度
			Height: int(pp.Length), // 内容高度

			Url: filePath, // 图片地址：外部链接 或 本地文件
		})
	}
	return PosterConfig{
		Width:  int(pp.Width),  // 画布宽度
		Height: int(pp.Length), // 画布高度

		PaddingLeft:   int(pp.Left),  // 画布左侧留白
		PaddingRight:  int(pp.Right), // 画布右侧留白
		PaddingTop:    int(pp.Up),    // 画布顶部留白
		PaddingBottom: int(pp.Down),  // 画布底部留白
		Items:         retItems,
	}, nil

}

func (this *PostMakeTaskSvc) makeStationPoster(storageFullPath string, fileName string, lineMidItem LineMidItem, lineStationItems []PosterLineStation, stationItem PosterLineStation) error {
	defer func() {
		if r := recover(); r != nil {
			log.ErrorFields("makeStationPoster recover ", map[string]interface{}{"err": r})
		}
	}()

	pfItem, err := NewPostMakeTaskSvc().makePosterParam(lineMidItem, lineStationItems, stationItem)
	if err != nil {
		log.ErrorFields("makePosterParam is nil ", nil)
		return err
	}
	bytes, _ := json.Marshal(&pfItem)
	log.ErrorFields("makePosterParam is nil ", map[string]interface{}{"Item": string(bytes)})

	contextW := pfItem.Width + pfItem.PaddingLeft + pfItem.PaddingRight
	contextH := pfItem.Height + pfItem.PaddingTop + pfItem.PaddingBottom
	startX := pfItem.PaddingLeft
	startY := pfItem.PaddingTop

	//创建画布（像素值）
	dc := gg.NewContext(contextW, contextH)
	//默认白色背景铺满
	dc.SetHexColor("#fff")
	dc.DrawRectangle(0, 0, float64(contextW), float64(contextH))
	dc.Fill()

	//图片前置，优先绘制，否则会盖住文字内容
	sort.Slice(pfItem.Items, func(i, j int) bool {
		return pfItem.Items[i].Type == 2 && pfItem.Items[j].Type != 2
	})

	for key, item := range pfItem.Items {
		log.Error("++++++++++++++++[%v][%+v]", key, item)
		if item.Type == Poster_Config_Type_Text {
			//文字颜色
			dc.SetHexColor(item.Color)

			//文字内容
			//if face, ok := this.getPosterFaceCache(item.FontSize); !ok {
			//	return errors.New("加载字体异常")
			//} else {
			//	dc.SetFontFaceWithPoints(face, float64(item.FontSize))
			//}
			if face, ok := this.getPosterFaceCacheByFamilyCode(item.FontSize, item.FontFamily); !ok {
				return errors.New("加载字体异常")
			} else {
				dc.SetFontFaceWithPoints(face, float64(item.FontSize))
			}

			text := item.Content                        //文本内容
			wordLen := len([]rune(text))                //文字长度
			rowMaxWordNum := item.Width / item.FontSize //单行最大显示文字数
			rowNum := int(math.Ceil(float64(wordLen) / float64(rowMaxWordNum)))

			if item.Width > 0 {
				//设置了宽度，若内容超出Width需要自动换行

				fontSize := item.FontSize // 初始字体大小
				textList := []string{}    // 文字内容（行）

				if rowMaxWordNum < wordLen {
					//文字长度超出Width
					//内容需分割成多行展示

					if item.Height > 0 {
						//设置了高度，若多行内容超出Height需要自适应字体

						if (rowNum * item.FontSize) > item.Height {
							//检测到以初始字体大小分多行展示，会超出Height

							//获取合适的字体大小方案
							//假设每行字数从1遍历到全字数，计算对应的字体大小，检测是否会超出高度，取第一个适配的字体大小方案
							for idx := 1; idx <= wordLen; idx++ {
								size := math.Floor(float64(item.Width) / float64(idx))
								rowN := math.Ceil(float64(wordLen) / float64(idx))
								if rowN*size <= float64(item.Height) {
									fontSize = int(size)
									rowMaxWordNum = idx
									rowNum = int(rowN)
									break
								}
							}

							//重新设置字体
							//if face, ok := this.getPosterFaceCache(fontSize); !ok {
							//	return errors.New("加载字体异常")
							//} else {
							//	dc.SetFontFaceWithPoints(face, float64(fontSize))
							//}
							if face, ok := this.getPosterFaceCacheByFamilyCode(fontSize, item.FontFamily); !ok {
								return errors.New("加载字体异常")
							} else {
								dc.SetFontFaceWithPoints(face, float64(fontSize))
							}
						}

					}

					//分割内容成多行
					for idx := 0; idx < rowNum; idx++ {
						textSlice := ""
						if idx == rowNum-1 {
							textSlice = string([]rune(text)[(idx * rowMaxWordNum):])
						} else {
							textSlice = string([]rune(text)[(idx * rowMaxWordNum):((idx + 1) * rowMaxWordNum)])
						}
						textList = append(textList, textSlice)
					}
				} else {
					//文字长度未超出Width
					//单行展示全内容
					textList = append(textList, item.Content)
				}

				if item.TextAlign == 1 {
					//文本居中
					space := 0
					if item.Height > 0 {
						space = (item.Height - rowNum*fontSize) / 2 // 手动居中，计算上下多余空隙
					}
					for idx := 0; idx < rowNum; idx++ {
						textSlice := ""
						if idx == rowNum-1 {
							textSlice = string([]rune(text)[(idx * rowMaxWordNum):])
						} else {
							textSlice = string([]rune(text)[(idx * rowMaxWordNum):((idx + 1) * rowMaxWordNum)])
						}
						dc.DrawStringAnchored(textSlice, float64(startX+item.X+item.Width/2), float64(startY+item.Y+space+idx*fontSize), 0.5, 1)
					}
				} else {
					//文本居左
					for idx := 0; idx < rowNum; idx++ {
						textSlice := ""
						if idx == rowNum-1 {
							textSlice = string([]rune(text)[(idx * rowMaxWordNum):])
						} else {
							textSlice = string([]rune(text)[(idx * rowMaxWordNum):((idx + 1) * rowMaxWordNum)])
						}
						dc.DrawStringAnchored(textSlice, float64(startX+item.X), float64(startY+item.Y+idx*fontSize), 0, 1)
					}
				}
			} else {
				if item.TextAlign == 1 {
					//单行居中显示
					//XY坐标点为文本中心点
					dc.DrawStringAnchored(item.Content, float64(startX+item.X), float64(startY+item.Y), 0.5, 1)
				} else {
					//单行居左显示
					//XY坐标点为内容左上角点
					dc.DrawStringAnchored(item.Content, float64(startX+item.X), float64(startY+item.Y), 0, 1)
				}
			}
		} else if item.Type == Poster_Config_Type_Image {
			//绘制图片
			imageUrl := item.Url
			if image, err := gg.LoadImage(imageUrl); err == nil {
				image = resize.Resize(uint(item.Width), uint(item.Height), image, resize.Lanczos3)
				dc.DrawImage(image, startX+item.X, startY+item.Y)
			}
		} else if item.Type == Poster_Config_Type_Station {
			err = this.setStationAreaPoster(pfItem, lineStationItems, item, stationItem, dc)
			if err != nil {
				log.ErrorFields("setStationAreaPoster is nil ", nil)
				return err
			}
		}
	}

	//保存结果
	err = dc.SavePNG(storageFullPath + "/" + fileName)

	//检测结果
	if err == nil {
		log.DebugFields("SavePNG is nil ", map[string]interface{}{"SavePNG": "海报生成成功：" + storageFullPath + fileName + ".png"})
	} else {
		log.ErrorFields("setStationAreaPoster ", map[string]interface{}{"err": err})
	}
	return nil
}

func (this *PostMakeTaskSvc) calcLineStationColumn(stations []PosterLineStation, item PosterConfigItem) ([]PosterLineStation, int) {

	//提前计算站名显示列数，若需多列显示则分割站名，同时获取站名总宽度
	nameTotalWidth := 0
	for i, _ := range stations {
		station := &stations[i]

		//站点名称
		stationName := station.Name

		//站点名称（列）
		nameList := []string{}

		//计算是否多列显示
		wordLen := len([]rune(stationName))
		columeHeight := item.Height - item.FontSize //站名实际的显示区高度=站点列表高度-减去顶部预留的特殊标识位置
		if wordLen*item.FontSize > columeHeight {
			// 2025.4.3判断是否存在括号 一对括号不能分列
			bracketIndex := -1
			stationName = strings.ReplaceAll(stationName, "(", "（")
			for index, r := range []rune(stationName) {
				if string(r) == "（" {
					bracketIndex = index
				}
			}
			if bracketIndex != -1 {
				nameList = append(nameList, string([]rune(stationName)[0:bracketIndex]))
				nameList = append(nameList, string([]rune(stationName)[bracketIndex:wordLen]))
			} else {
				// 文字超出范围，分隔成多列
				columnMaxWordNum := columeHeight / item.FontSize                          //每列最大显示字数
				columnNum := int(math.Ceil(float64(wordLen) / float64(columnMaxWordNum))) //站名所需列数
				nameTotalWidth += int(float64(columnNum) * float64(item.FontSize))
				for idx := 0; idx < columnNum; idx++ {
					nameList = append(nameList, string([]rune(stationName)[(idx*columnMaxWordNum):((idx+1)*columnMaxWordNum)]))
				}
			}
		} else {
			nameTotalWidth += int(float64(item.FontSize))
			//文字未超范围，单列展示
			nameList = append(nameList, stationName)
		}

		station.NameList = nameList
	}
	return stations, nameTotalWidth
}

func (this *PostMakeTaskSvc) setStationAreaPoster(pfItem PosterConfig, stations []PosterLineStation, item PosterConfigItem, stationItem PosterLineStation, dc *gg.Context) error {

	var (
		startX = pfItem.PaddingLeft
		startY = pfItem.PaddingTop
	)

	//提前计算站名显示列数，若需多列显示则分割站名，同时获取站名总宽度
	nameTotalWidth := 0
	//提前计算站名显示列数，若需多列显示则分割站名，同时获取站名总宽度
	stations, nameTotalWidth = this.calcLineStationColumn(stations, item)
	//计算站名均匀分布时每列之间的
	//间距大小=（站点列表宽度-站名总宽度）/（站点数-1）
	//最后一列贴着最右侧，不需分配间距
	columnW := (item.Width - nameTotalWidth) / (len(stations) - 1)

	//绘制站点列表
	stationX := startX + item.X
	for _, station := range stations {
		//计算坐标
		x := stationX
		y := startY + item.Y + int(float64(item.FontSize)*1.12) //站点名称顶部预留特殊标识位置，大致为一个字的距离，“*1.12”是为了留底部空隙
		if len(station.RailInterchange) > 0 {
			//若为换乘站显示特殊标识
			dc.SetHexColor("#db0000")
			fontSize := int(float64(item.FontSize) * 0.88) //“*0.88”是为了缩小标识大小

			//重新设置字体
			//if face, ok := this.getPosterFaceCache(fontSize); !ok {
			//	return errors.New("加载字体异常")
			//} else {
			//	dc.SetFontFaceWithPoints(face, float64(fontSize))
			//}
			if face, ok := this.getPosterFaceCacheByFamilyCode(fontSize, item.FontFamily); !ok {
				return errors.New("加载字体异常")
			} else {
				dc.SetFontFaceWithPoints(face, float64(fontSize))
			}

			dc.DrawStringAnchored("@", float64(x)+float64(item.FontSize-fontSize)/2, float64(startY+item.Y), 0, 1) //“+float64(item.FontSize-fontSize)/2”是让标识居中
		} else if station.StationId == stationItem.StationId && station.Sequence == stationItem.Sequence {
			//若为当前站显示特殊标识
			if image, err := gg.LoadImage(CurrentStationIcon); err == nil {
				imageW := uint(float64(item.FontSize) * 0.77)
				imageH := uint(float64(item.FontSize) * 0.65)
				image = resize.Resize(imageW, imageH, image, resize.Lanczos3)                                                          //“*0.77，*0.65”是控制图片大小
				dc.DrawImage(image, x+int((float64(item.FontSize)-float64(imageW))/2), startY+item.Y+int(float64(item.FontSize)*0.11)) //“+int((float64(item.FontSize)-float64(imageW))/2)”是调整图片基于下方文字居中，“int(float64(item.FontSize)*0.11)”是为了留底部空隙

			}
		} else {
			//若为普通站点显示特殊标识
			dc.SetHexColor("#000")
			fontSize := int(float64(item.FontSize) * 1.5) //“*1.5”是为了放大标识大小
			//if face, ok := this.getPosterFaceCache(fontSize); !ok {
			//	return errors.New("加载字体异常")
			//} else {
			//	dc.SetFontFaceWithPoints(face, float64(fontSize))
			//}
			if face, ok := this.getPosterFaceCacheByFamilyCode(fontSize, item.FontFamily); !ok {
				return errors.New("加载字体异常")
			} else {
				dc.SetFontFaceWithPoints(face, float64(fontSize))
			}

			// 2025.4.2 需求取消海报上的.
			//dc.DrawStringAnchored("·", float64(x)+float64(fontSize)*0.05, float64(startY+item.Y), 0, 1) //“+float64(fontSize)*0.05”是让标识居中
		}

		//文字颜色
		if station.StationId == stationItem.StationId && station.Sequence == stationItem.Sequence {
			dc.SetHexColor("#db0000")
		} else {
			dc.SetHexColor("#000")
		}

		//加载字体
		//if face, ok := this.getPosterFaceCache(item.FontSize); !ok {
		//	return errors.New("加载字体异常")
		//} else {
		//	dc.SetFontFaceWithPoints(face, float64(item.FontSize))
		//}
		if face, ok := this.getPosterFaceCacheByFamilyCode(item.FontSize, item.FontFamily); !ok {
			return errors.New("加载字体异常")
		} else {
			dc.SetFontFaceWithPoints(face, float64(item.FontSize))
		}

		//绘制站名

		for ii, name := range station.NameList {
			// 2025.4.2需求 将左右括号换成上下括号 字段对齐
			name = strings.ReplaceAll(name, "(", "︵")
			name = strings.ReplaceAll(name, "（", "︵")
			name = strings.ReplaceAll(name, ")", "︶")
			name = strings.ReplaceAll(name, "）", "︶")
			//将文字之间用空格隔开，因为站名垂直显示，但组件仅在空格处进行自动换行
			nameSplit := strings.Split(name, "")    //例："东南西北"
			nameStr := strings.Join(nameSplit, " ") //例："东 南 西 北"
			textNum := float64(len(nameSplit))
			// 计算lineSpacing
			// 高度 = fontSize * 字数 + （lineSpacing - 1）*fontSize*（字数-1）
			lineSpacing := (float64(item.Height-item.FontSize)-float64(item.FontSize)*textNum)/float64(item.FontSize)/(textNum-1) + 1
			//因为站名垂直居中会导致左移半个字，x坐标需要右移半个字复位(item.FontSize/2)
			//多列站名需根据 i 值右移相应列位(i*item.FontSize)
			dc.DrawStringWrapped(nameStr, float64(x+(ii*item.FontSize)), float64(y), 0, 0, float64(item.FontSize), lineSpacing, gg.AlignCenter)
		}
		//将站名X移动到下一列位置
		stationX += len(station.NameList)*item.FontSize + columnW
	}
	return nil
}

func (this *PostMakeTaskSvc) NextStationName(lineStationItems []PosterLineStation, item PosterLineStation) string {
	for _, lineStationItem := range lineStationItems {
		if lineStationItem.Sequence == item.Sequence+1 {
			return lineStationItem.Name
		}
	}
	return ""
}

func (this *PostMakeTaskSvc) getPosterFaceCache(fontSize int) (font.Face, bool) {
	var key = getPosterFaceKey(fontSize)
	value, ok := posterFontSync.Load(key)
	if !ok {
		// faceT 不支持并发,不然可以全局共享
		// 内存飙升 ==> truetype.NewFace  --> image.NewAlpha --> pix := make([]uint8, 1*w*h)
		faceT := truetype.NewFace(this.Font, &truetype.Options{Size: float64(fontSize)})
		posterFontSync.Store(key, faceT)

		return faceT, true
	}
	return value.(font.Face), true
}

func (this *PostMakeTaskSvc) getPosterFaceCacheByFamilyCode(fontSize int, familyCode int) (font.Face, bool) {
	var key = getPosterFaceKeyByFamily(fontSize, familyCode)
	value, ok := posterFontSync.Load(key)
	if !ok {
		// faceT 不支持并发,不然可以全局共享
		// 内存飙升 ==> truetype.NewFace  --> image.NewAlpha --> pix := make([]uint8, 1*w*h)
		ft := getFontFamily(familyCode)

		faceT := truetype.NewFace(ft, &truetype.Options{Size: float64(fontSize)})
		posterFontSync.Store(key, faceT)
		return faceT, true
	}
	return value.(font.Face), true

}

// todo: 不支持并行，不然内存吃不消
var (
	posterSync     sync.Map //
	posterFontSync sync.Map // 字体库
)

func getFont() *truetype.Font {
	if value, ok := posterSync.Load(true); ok {
		return value.(*truetype.Font)
	}
	return nil
}

func setFont() *truetype.Font {
	if _, ok := posterSync.Load(true); !ok {
		fontBytes, err := ioutil.ReadFile(FontLibraryFile)
		if err != nil {
			log.Error("fontBytes:", err)
			return nil
		}
		f, err := truetype.Parse(fontBytes)
		if err != nil {
			log.Error("truetype.Parse:", err)
			return nil
		}
		posterSync.Store(true, f)
		return f
	}
	return nil
}

func getFontFamily(familyCode int) *truetype.Font {
	storeKey := getPosterFontKeyByFamily(familyCode)
	if value, ok := posterSync.Load(storeKey); ok {
		return value.(*truetype.Font)
	}
	return nil
}

// setAreaFont 设置字体缓存,根据站牌内容的对应不同的字体
func setFontFamily() bool {
	for key, value := range P_FontMap {
		fontBytes, err := ioutil.ReadFile(value)
		if err != nil {
			log.ErrorFields("fontBytes:", map[string]interface{}{"error": err.Error()})
			return false
		}
		f, err := truetype.Parse(fontBytes)
		if err != nil {
			log.ErrorFields("truetype.Parse:", map[string]interface{}{"error": err.Error()})
			return false
		}
		storeKey := fmt.Sprintf("%s%d", Poster_Face_Cache_Key_Prefix_FAMILY, key)
		posterSync.Store(storeKey, f)
	}
	return true
}

func clear() {
	posterFontSync.Range(func(key, value interface{}) bool {
		posterFontSync.Delete(key)
		return true // 返回true继续遍历，返回false停止遍历
	})
	posterSync.Delete(true)
}

func getPosterFaceKey(fontSize int) string {
	return fmt.Sprintf("%v%v", Poster_Face_Cache_Key_Prefix, fontSize)
}

func getPosterFontKeyByFamily(familyCode int) string {
	return fmt.Sprintf("%v%v", Poster_Face_Cache_Key_Prefix_FAMILY, familyCode)
}

func getPosterFaceKeyByFamily(fontSize, familyCode int) string {
	return fmt.Sprintf("%v%v_%v", Poster_Face_Cache_Key_Prefix_FAMILY, familyCode, fontSize)
}

// 缓存前缀，区分其他缓存
const (
	Poster_Face_Cache_Key_Prefix        = "poster_face_cache_key_"            // 海报字体缓存前缀
	Poster_Face_Cache_Key_Prefix_FAMILY = "poster_face_cache_key_family_"     // 海报字体缓存前缀
	FontLibraryFile                     = "./static/SourceHanSansCN-Bold.ttf" // 字体库文件
	CurrentStationIcon                  = "./static/redArrow.png"             // 当前站标识
)

var P_FontMap = map[int]string{
	0: "./static/SourceHanSansCN-Bold.ttf",
	1: "./static/STSONG.TTF",
	2: "./static/simhei.ttf",
	3: "./static/STKAITI.TTF",
	4: "./static/simfang.ttf",
	5: "./static/msyh.ttc",
}
