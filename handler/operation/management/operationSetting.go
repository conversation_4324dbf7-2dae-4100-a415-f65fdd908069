package management

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/handler/operation/lineDriverReport"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	commonModel "app/org/scs/erpv2/api/model/common"
	"app/org/scs/erpv2/api/model/file"
	"app/org/scs/erpv2/api/model/operation"
	protoVehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"strings"
)

type DataManagement struct{}
type ListSpecificationSettingParam struct {
	DictId            int64 `json:"DictId"`
	SpecificationType int64 `json:"SpecificationType"`
	Length            int64 `json:"Length"`
	Width             int64 `json:"Width" `
	Left              int64 `json:"Left"`
	Right             int64 `json:"Right"`
	model.Paginator
}
type SpecificationSetting struct {
	operation.SpecificationSetting
	FileUrl   string `json:"FileUrl"`
	AttachUrl string `json:"AttachUrl"`
}

func (*DataManagement) ListSpecificationSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListSpecificationSettingParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var topCorpId = auth.User(ctx).GetTopCorporationId()

	var ss = operation.SpecificationSetting{
		Length:            param.Length,
		Width:             param.Width,
		Left:              param.Left,
		Right:             param.Right,
		TopCorporationId:  topCorpId,
		SpecificationType: param.SpecificationType,
		DictId:            param.DictId,
	}
	items, totalCount, err := ss.GetAllBy(param.Paginator)
	if err != nil {
		log.ErrorFields("ListSpecificationSetting GetAllBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var retItems = make([]SpecificationSetting, 0)
	for _, item := range items {
		var (
			fileUrl   string
			attachUrl string
		)
		if item.FileId > 0 {
			var f file.File
			fileUrl = config.Config.StaticFileHttpPrefix + f.FindBy(item.FileId).Path
		}

		if item.AttachId > 0 {
			var f file.File
			attachUrl = config.Config.StaticFileHttpPrefix + f.FindBy(item.FileId).Path
		}

		retItems = append(retItems, SpecificationSetting{
			SpecificationSetting: item,
			FileUrl:              fileUrl,
			AttachUrl:            attachUrl,
		})
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      retItems,
		"TotalCount": totalCount,
	})
}

type AddSpecificationSettingParam struct {
	Items []SpecificationSettingItem `json:"Items"`
}

type SpecificationSettingItem struct {
	Id                int64                         `json:"Id"`
	Name              string                        `json:"Name"`
	Up                int64                         `json:"Up"`
	Down              int64                         `json:"Down"`
	Length            int64                         `json:"Length"`
	Width             int64                         `json:"Width"`
	Left              int64                         `json:"Left"`
	Right             int64                         `json:"Right"`
	FileId            int64                         `json:"FileId"`
	SettingItem       []operation.PosterSettingItem `json:"SettingItem"`
	SpecificationType int64                         `json:"SpecificationType"`
	DictId            int64                         `json:"DictId"`
	AttachId          int64                         `json:"AttachId"`
	AttachName        string                        `json:"AttachName"`
}

func (*DataManagement) AddSpecificationSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AddSpecificationSettingParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if len(param.Items) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	for _, item := range param.Items {
		item.Name = strings.TrimSpace(item.Name)
		if len(item.Name) == 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}

		if item.DictId > 0 {
			// 如果存在字典类
			var dict = commonModel.Dict{PkId: model.PkId{Id: item.DictId}}
			d, err := dict.Get()
			if err != nil {
				log.ErrorFields("dict.Get error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbQueryFail)
			}
			if !(d.DictType == commonModel.AdvertisingBoardType && item.SpecificationType == operation.SettingType_Ads) {
				return response.Error(rsp, response.ParamsInvalid)
			}
		}
	}

	var topCorpId = auth.User(ctx).GetTopCorporationId()
	if topCorpId == 0 {
		return response.Error(rsp, "PA1106")
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	for _, item := range param.Items {
		var bytes, _ = json.Marshal(&item.SettingItem)
		var ss = operation.SpecificationSetting{
			TopCorporationId:  topCorpId,
			Name:              strings.TrimSpace(item.Name),
			Length:            item.Length,
			Up:                item.Up,
			Down:              item.Down,
			Width:             item.Width,
			Left:              item.Left,
			Right:             item.Right,
			FileId:            item.FileId,
			SettingItem:       bytes,
			SpecificationType: item.SpecificationType,
			DictId:            item.DictId,
			AttachId:          item.AttachId,
			AttachName:        item.AttachName,
		}
		ss.ParseOpUser(ctx)

		err = ss.Create(tx)
		if err != nil {
			log.ErrorFields("AddSpecificationSetting.Create error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}
	return response.Success(rsp, nil)

}

func (*DataManagement) EditSpecificationSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param SpecificationSettingItem
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.Name = strings.TrimSpace(param.Name)
	if len(param.Name) == 0 || param.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	var bytes, _ = json.Marshal(&param.SettingItem)
	var ss = operation.SpecificationSetting{
		PkId: model.PkId{Id: param.Id},
	}
	var aa = map[string]interface{}{
		"Name":        strings.TrimSpace(param.Name),
		"Length":      param.Length,
		"Width":       param.Width,
		"Up":          param.Up,
		"Down":        param.Down,
		"Left":        param.Left,
		"Right":       param.Right,
		"FileId":      param.FileId,
		"SettingItem": bytes,
		"AttachId":    param.AttachId,
		"AttachName":  param.AttachName,
	}
	err = ss.Update(tx, aa)
	if err != nil {
		log.ErrorFields("EditSpecificationSetting.Update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

type DelSpecificationSettingParam struct {
	Ids []int64 `json:"Ids"`
}

func (*DataManagement) DelSpecificationSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DelSpecificationSettingParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if len(param.Ids) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	for _, id := range param.Ids {
		// 规格
		var oss = operation.SpecificationSetting{
			PkId: model.PkId{Id: id},
		}
		err = oss.GetByTx(tx)
		if err != nil {
			log.ErrorFields("GetByTx error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
		switch oss.SpecificationType {
		case operation.SettingType_StreetPlate: // 路牌类型
			// 查询是否有站点绑定记录
			var ss operation.OperationAssociationSetting
			items, err := ss.GetBySettingId(id, operation.StationSetting_SpecificationType)
			if err != nil {
				log.ErrorFields("GetBySettingId error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbQueryFail)
			}
			if len(items) > 0 {
				return response.Error(rsp, "OP9901")
			}
		case operation.SettingType_Ads: // 广告牌类型
			// 允许删除
			var ss operation.OperationAssociationSettingRecord
			err = ss.DeleteBySpecificationSettingId(tx, id)
			if err != nil {
				log.ErrorFields("DeleteBySpecificationSettingId error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbQueryFail)
			}
		}
		err = oss.Delete(tx, id)
		if err != nil {
			log.ErrorFields("EditSpecificationSetting.Delete error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}
	return response.Success(rsp, nil)
}

type ListMaterialSettingParam struct {
	DictId       int64 `json:"DictId"`
	MaterialType int64 `json:"MaterialType"`
	model.Paginator
}

type MaterialSetting struct {
	operation.MaterialSetting
	FileUrl string `json:"FileUrl"`
}

func (*DataManagement) ListMaterialSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListMaterialSettingParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var ss = operation.MaterialSetting{
		DictId:           param.DictId,
		MaterialType:     param.MaterialType,
		TopCorporationId: auth.User(ctx).GetTopCorporationId(),
	}
	items, totalCount, err := ss.GetAllBy(param.Paginator)
	if err != nil {
		log.ErrorFields("ListSpecificationSetting GetAllBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var retItems = make([]MaterialSetting, 0)
	for _, item := range items {
		var fileUrl string
		if item.FileId > 0 {
			var f file.File
			fileUrl = config.Config.StaticFileHttpPrefix + f.FindBy(item.FileId).Path
		}
		retItems = append(retItems, MaterialSetting{
			MaterialSetting: item,
			FileUrl:         fileUrl,
		})
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      retItems,
		"TotalCount": totalCount,
	})
}

type AddMaterialSettingParam struct {
	Items []operation.MaterialSetting `json:"Items"`
}

func (*DataManagement) AddMaterialSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AddMaterialSettingParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	for _, item := range param.Items {
		item.Name = strings.TrimSpace(item.Name)
		if len(item.Name) == 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}
		if item.DictId > 0 {
			// 如果存在字典类
			var dict = commonModel.Dict{PkId: model.PkId{Id: item.DictId}}
			d, err := dict.Get()
			if err != nil {
				log.ErrorFields("dict.Get error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbQueryFail)
			}
			if !(d.DictType == commonModel.AdvertisingBoardType && item.MaterialType == operation.SettingType_Ads) {
				return response.Error(rsp, response.ParamsInvalid)
			}
		}

	}

	var topCorpId = auth.User(ctx).GetTopCorporationId()
	if topCorpId == 0 {
		return response.Error(rsp, "PA1106")
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	for _, item := range param.Items {
		item.ParseOpUser(ctx)
		item.TopCorporationId = topCorpId
		err = item.Create(tx)
		if err != nil {
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	return response.Success(rsp, nil)
}

func (*DataManagement) EditMaterialSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param MaterialSetting
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	param.Name = strings.TrimSpace(param.Name)
	if len(param.Name) == 0 || param.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	var ss = operation.MaterialSetting{
		PkId: model.PkId{Id: param.Id},
	}
	var aa = map[string]interface{}{
		"Name":   strings.TrimSpace(param.Name),
		"FileId": param.FileId,
	}
	err = ss.Update(tx, aa)
	if err != nil {
		log.ErrorFields("EditMaterialSetting.Update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

func (*DataManagement) DelMaterialSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DelSpecificationSettingParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if len(param.Ids) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	for _, id := range param.Ids {
		var oms = operation.MaterialSetting{
			PkId: model.PkId{Id: id},
		}
		err = oms.GetByTx(tx)
		if err != nil {
			log.ErrorFields("GetBySettingId error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbQueryFail)
		}
		switch oms.MaterialType {
		case operation.SettingType_StreetPlate: // 路牌类型
			// 查询是否有站点绑定记录
			var ss operation.OperationAssociationSetting
			items, err := ss.GetBySettingId(id, operation.StationSetting_MaterialType)
			if err != nil {
				log.ErrorFields("GetBySettingId error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbQueryFail)
			}
			if len(items) > 0 {
				return response.Error(rsp, "OP9901")
			}
		case operation.SettingType_Ads: // 广告牌类型
			// 允许删除
			var ss operation.OperationAssociationSettingRecord
			err = ss.DeleteByMaterialSettingId(tx, id)
			if err != nil {
				log.ErrorFields("DeleteByMaterialSettingId error", map[string]interface{}{"err": err})
				return response.Error(rsp, response.DbQueryFail)
			}
		}

		var oss operation.MaterialSetting
		err = oss.Delete(tx, id)
		if err != nil {
			log.ErrorFields("DelMaterialSetting.Delete error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}
	return response.Success(rsp, nil)
}

type ListAssociationRecordParam struct {
	CorporationIds []int64
	LineIds        []int64
	VehicleIds     []int64
	model.Paginator
}

type ListVehicleAdScreenConfigItem struct {
	VehicleId   int64                   `json:"VehicleId"`
	License     string                  `json:"License"`
	Corporation string                  `json:"Corporation"`
	Line        string                  `json:"Line"`
	VehicleType string                  `json:"VehicleType"`
	SiteNum     int64                   `json:"SiteNum"`
	Items       []AssociationRecordItem `json:"Items"`
}

type AssociationRecordItem struct {
	AssociationId   int64  `json:"AssociationId,omitempty"`
	Dict            string `json:"Dict"`
	DictId          int64  `json:"DictId"`
	Material        string `json:"Material"`
	MaterialId      int64  `json:"MaterialId"`
	Specification   string `json:"Specification"`
	SpecificationId int64  `json:"SpecificationId"`
	VehicleId       int64  `json:"VehicleId,omitempty"`
}

func (this *DataManagement) ListVehicleAdScreenConfigExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return this.ListVehicleAdScreenConfig(ctx, req, rsp)
}

func (this *DataManagement) ListVehicleAdScreenConfig(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return this.ListAssociationRecord(ctx, req, rsp, operation.AssociationObjectForVehicle)
}

func (*DataManagement) ListAssociationRecord(ctx context.Context, req *api.Request, rsp *api.Response, associationObjectType int64) error {
	var param ListAssociationRecordParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	groupId := auth.User(ctx).GetTopCorporationId()

	userId := auth.User(ctx).GetUserId()

	switch associationObjectType {
	case operation.AssociationObjectForVehicle:
		var vehicleIds []int64
		if len(param.VehicleIds) > 0 {
			vehicleIds = param.VehicleIds
		} else if len(param.LineIds) > 0 || len(param.CorporationIds) > 0 {
			vehicleIds = rpc.GetVehicleIdsByLineIdsAndCorporationIds(ctx, groupId, param.LineIds, param.CorporationIds)
		}

		oetVehicleItems, totalCount := rpc.GetVehiclesWithOption(ctx, &protoVehicle.GetVehiclesWithOptionRequest{
			VehicleIds:      vehicleIds,
			Offset:          int64(param.Offset),
			Limit:           int64(param.Limit),
			IsShowChildNode: true,
			UserId:          userId,
		})

		var (
			retItems           = make([]ListVehicleAdScreenConfigItem, 0)
			corpMap            = make(map[int64]string, 0)
			lineMap            = make(map[int64]string, 0)
			dictMap            = make(map[int64]string, 0)
			materialSettingMap = make(map[int64]*operation.MaterialSetting, 0)
			specificationMap   = make(map[int64]*operation.SpecificationSetting, 0)
		)
		for _, oetVehicleItem := range oetVehicleItems {
			var corpName string
			if value, ok := corpMap[oetVehicleItem.SonCorporationId]; ok {
				corpName = value
			} else {
				corpName = rpc.GetCorporationNameById(ctx, oetVehicleItem.SonCorporationId)
				corpMap[oetVehicleItem.SonCorporationId] = corpName
			}
			var lineName string
			if value, ok := lineMap[oetVehicleItem.LineId]; ok {
				lineName = value
			} else {
				lineItem, _ := rpc.GetLineWithId(ctx, oetVehicleItem.LineId)
				if lineItem != nil {
					lineName = lineItem.Name
					lineMap[oetVehicleItem.LineId] = lineName
				}
			}

			var retItem = ListVehicleAdScreenConfigItem{
				VehicleId:   oetVehicleItem.Id,
				License:     oetVehicleItem.License,
				Corporation: corpName,
				Line:        lineName,
				VehicleType: oetVehicleItem.VehicleType,
				SiteNum:     oetVehicleItem.SiteNum,
			}

			rItems, _ := (&operation.OperationAssociationSettingRecord{}).GetByAssociationObjectId(oetVehicleItem.Id, associationObjectType)
			for _, rItem := range rItems {
				var dictName string
				if value, ok := dictMap[rItem.DictId]; ok {
					dictName = value
				} else {
					dict, _ := (&commonModel.Dict{PkId: model.PkId{rItem.DictId}}).Get()
					dictName = dict.DictKey
					dictMap[rItem.DictId] = dict.DictKey
				}

				var material string
				if value, ok := materialSettingMap[rItem.MaterialSettingId]; ok {
					material = value.Name
				} else {
					var ms = &operation.MaterialSetting{PkId: model.PkId{Id: rItem.MaterialSettingId}}
					ms.GetBy()
					materialSettingMap[rItem.MaterialSettingId] = ms
					material = ms.Name
				}

				var specification string
				if value, ok := specificationMap[rItem.SpecificationSettingId]; ok {
					specification = value.Name
				} else {
					var ms = &operation.SpecificationSetting{PkId: model.PkId{Id: rItem.SpecificationSettingId}}
					ms.GetBy()
					specificationMap[rItem.SpecificationSettingId] = ms
					specification = ms.Name
				}

				retItem.Items = append(retItem.Items, AssociationRecordItem{
					AssociationId:   rItem.Id,
					Dict:            dictName,
					DictId:          rItem.DictId,
					Material:        material,
					MaterialId:      rItem.MaterialSettingId,
					Specification:   specification,
					SpecificationId: rItem.SpecificationSettingId,
				})
			}
			retItems = append(retItems, retItem)
		}
		return response.Success(rsp, map[string]interface{}{
			"Items":      retItems,
			"TotalCount": totalCount,
		})

	}

	return nil
}

type ImportVehicleAdScreenConfigParam struct {
	FileData string `json:"FileData"`
}

type ImportVehicleAdScreenElement struct {
	License       string `json:"License"`
	Dict          string `json:"Dict"`
	Material      string `json:"Material"`
	Specification string `json:"Specification"`

	VehicleId       int64 `json:"-"`
	DictId          int64 `json:"-"`
	MaterialId      int64 `json:"-"`
	SpecificationId int64 `json:"-"`
}

/*
*

	运营车辆    广告牌类型    尺寸    材质
*/
func (*DataManagement) ImportVehicleAdScreenConfig(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param lineDriverReport.AddWorkRequest
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	decodeString, err := base64.StdEncoding.DecodeString(param.FileData)
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.ErrorFields("xlsx.OpenBinary error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	user := auth.User(ctx).GetUser()
	var topCorpId = user.TopCorporationId

	if len(excelFile.Sheets) == 0 {
		log.ErrorFields("xlsx sheets is 0", map[string]interface{}{})
		return response.Error(rsp, response.ParamsInvalid)
	}

	sheet := excelFile.Sheets[0]
	var failItems []map[string]interface{}
	var reports []ImportVehicleAdScreenElement
	for i, row := range sheet.Rows {
		if i == 0 || i == 1 {
			continue
		}
		var report = ImportVehicleAdScreenElement{}
		fmt.Printf("row.cells====== %+v \r\n", row.Cells)

		for cellIndex, cell := range row.Cells {
			switch cellIndex {
			case 0:
				report.License = cell.String()
				if report.License == "" {
					failItems = append(failItems, map[string]interface{}{"RowIndex": i + 1, "Error": "车牌号格式有误"})
					break
				} else {
					vehicleItem := rpc.GetVehicleWithLicense(ctx, &protoVehicle.GetVehicleWithLicenseRequest{
						License:       report.License,
						CorporationId: topCorpId,
					})
					if vehicleItem == nil {
						failItems = append(failItems, map[string]interface{}{"RowIndex": i + 1, "Error": "车牌号不存在"})
						break
					} else {
						report.VehicleId = vehicleItem.Id
					}
				}

			case 1:
				report.Dict = cell.String()
				var d = &commonModel.Dict{}
				err = d.GetByDictKey(report.Dict, int64(commonModel.AdvertisingBoardType))
				if err != nil || d.Id == 0 {
					failItems = append(failItems, map[string]interface{}{"RowIndex": i + 1, "Error": "广告牌类型不存在"})
					break
				} else {
					report.DictId = d.Id
				}

			case 2:
				report.Specification = cell.String()
				var ss = operation.SpecificationSetting{}
				err = ss.GetByName(report.Specification, report.DictId, operation.SettingType_Ads)
				if err != nil || ss.Id == 0 {
					failItems = append(failItems, map[string]interface{}{"RowIndex": i + 1, "Error": "尺寸不存在"})
					break
				} else {
					report.SpecificationId = ss.Id
				}

			case 3:
				report.Material = cell.String()
				var ss = operation.MaterialSetting{}
				err = ss.GetByName(report.Material, report.DictId, operation.SettingType_Ads)
				if err != nil || ss.Id == 0 {
					failItems = append(failItems, map[string]interface{}{"RowIndex": i + 1, "Error": "材质不存在"})
					break
				} else {
					report.MaterialId = ss.Id
				}
			}
		}
		reports = append(reports, report)
	}
	if len(failItems) > 0 {
		return response.Success(rsp, map[string]interface{}{"FailItems": failItems})
	}

	for _, item := range reports {
		if item.VehicleId == 0 || item.DictId == 0 || item.MaterialId == 0 || item.SpecificationId == 0 {
			return response.Success(rsp, map[string]interface{}{
				"FailItems": "",
			})
		}
	}
	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	for _, item := range reports {
		var op = &operation.OperationAssociationSettingRecord{
			TopCorporationId:       topCorpId,
			AssociationObjectType:  operation.AssociationObjectForVehicle,
			AssociationObjectId:    item.VehicleId,
			DictId:                 item.DictId,
			MaterialSettingId:      item.MaterialId,
			SpecificationSettingId: item.SpecificationId,
		}
		err = op.Create(tx)
		if err != nil {
			log.ErrorFields(" OperationAssociationSettingRecord Update error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	return response.Success(rsp, map[string]interface{}{
		"SuccessCount": len(reports),
		"FailCount":    0,
	})
}

type SetVehicleAdScreenConfigParam struct {
	AddItems  []AssociationRecordItem `json:"AddItems"`
	EditItems []AssociationRecordItem `json:"EditItems"`
	DelItems  []int64                 `json:"DelItems"`
}

func (*DataManagement) SetVehicleAdScreenConfig(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param SetVehicleAdScreenConfigParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	groupId := auth.User(ctx).GetTopCorporationId()

	// 数据校验
	if len(param.AddItems) == 0 && len(param.EditItems) == 0 && len(param.DelItems) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	for _, item := range param.AddItems {
		if item.VehicleId == 0 || item.DictId == 0 || item.MaterialId == 0 || item.SpecificationId == 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}
		var dd = commonModel.Dict{
			PkId: model.PkId{item.DictId},
		}
		var dict commonModel.Dict
		dict, err = dd.Get()
		fmt.Printf("%+v", dict)
		if err != nil || dict.Id == 0 {
			log.ErrorFields(" commonModel.Dict query error", map[string]interface{}{"err": err})
			return response.Error(rsp, "OP9906")
		}
		if dict.DictType != commonModel.AdvertisingBoardType {
			return response.Error(rsp, "OP9906")
		}

		// 先判断材料类、规格类是否属于dictId
		var ms = operation.MaterialSetting{
			PkId: model.PkId{item.MaterialId},
		}
		err = ms.GetBy()
		if err != nil {
			log.ErrorFields(" operation.MaterialSetting query error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		if ms.DictId != item.DictId {
			return response.Error(rsp, "OP9904")
		}

		// 先判断材料类、规格类是否属于dictId
		var ss = operation.SpecificationSetting{
			PkId: model.PkId{item.SpecificationId},
		}
		err = ss.GetBy()
		if err != nil {
			log.ErrorFields(" operation.SpecificationSetting query error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		if ss.DictId != item.DictId {
			return response.Error(rsp, "OP9905")
		}

	}
	for _, item := range param.EditItems {
		if item.AssociationId == 0 || item.DictId == 0 || item.MaterialId == 0 || item.SpecificationId == 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}

		var dd = commonModel.Dict{
			PkId: model.PkId{item.DictId},
		}
		var dict commonModel.Dict
		dict, err = dd.Get()
		if err != nil || dict.Id == 0 {
			log.ErrorFields(" commonModel.Dict query error", map[string]interface{}{"err": err})
			return response.Error(rsp, "OP9906")
		}
		if dict.DictType != commonModel.AdvertisingBoardType {
			return response.Error(rsp, "OP9906")
		}

		// 先判断材料类、规格类是否属于dictId
		var ms = operation.MaterialSetting{
			PkId: model.PkId{item.MaterialId},
		}
		err = ms.GetBy()
		if err != nil {
			log.ErrorFields(" operation.MaterialSetting query error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		if ms.DictId != item.DictId {
			return response.Error(rsp, "OP9904")
		}

		// 先判断材料类、规格类是否属于dictId
		var ss = operation.SpecificationSetting{
			PkId: model.PkId{item.SpecificationId},
		}
		err = ss.GetBy()
		if err != nil {
			log.ErrorFields(" operation.SpecificationSetting query error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbNotFoundRecord)
		}
		if ss.DictId != item.DictId {
			return response.Error(rsp, "OP9905")
		}

	}
	for _, item := range param.DelItems {
		if item == 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	for _, item := range param.DelItems {
		err = (&operation.OperationAssociationSettingRecord{}).Delete(tx, item)
		if err != nil {
			log.ErrorFields(" OperationAssociationSettingRecord delete error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsInvalid)
		}
	}
	for _, item := range param.EditItems {

		var m = make(map[string]interface{}, 0)
		m["dictid"] = item.DictId
		m["materialsettingid"] = item.MaterialId
		m["specificationsettingid"] = item.SpecificationId

		err = (&operation.OperationAssociationSettingRecord{PkId: model.PkId{item.AssociationId}}).Update(tx, m)
		if err != nil {
			log.ErrorFields(" OperationAssociationSettingRecord Update error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	for _, item := range param.AddItems {
		var op = &operation.OperationAssociationSettingRecord{
			TopCorporationId:       groupId,
			AssociationObjectType:  operation.AssociationObjectForVehicle,
			AssociationObjectId:    item.VehicleId,
			DictId:                 item.DictId,
			MaterialSettingId:      item.MaterialId,
			SpecificationSettingId: item.SpecificationId,
		}
		err = op.Create(tx)
		if err != nil {
			log.ErrorFields(" OperationAssociationSettingRecord Update error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.ParamsInvalid)
		}
	}
	return response.Success(rsp, nil)
}
