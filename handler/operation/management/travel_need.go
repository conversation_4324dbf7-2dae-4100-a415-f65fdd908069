package management

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/operation"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"strings"
)

type ListTravelNeedParam struct {
	IsNeedDetail int64  `json:"IsNeedDetail"` // 0:不需要 1:需要
	Name         string `json:"Name"`
	model.Paginator
}

type TravelNeedItem struct {
	Id         int64                  `json:"Id"`
	Name       string                 `json:"Name"`
	Code       string                 `json:"Code"`
	Remark     string                 `json:"Remark"`
	Status     int64                  `json:"Status"`
	OpUserName string                 `json:"OpUserName"`
	Url        string                 `json:"Url"` // 出行需求H5链接
	CreatedAt  model.LocalTime        `json:"CreatedAt"`
	Count      int64                  `json:"Count"`
	FormItems  []TravelNeedDetailItem `json:"FormItems"`
}

func (*DataManagement) ListTravelNeed(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListTravelNeedParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var topCorpId = auth.User(ctx).GetTopCorporationId()

	var p = operation.TravelNeed{
		TopCorporationId: topCorpId,
		Name:             strings.TrimSpace(param.Name),
	}
	items, totalCount, err := p.GetBy(param.Paginator)
	if err != nil {
		log.ErrorFields("ListTravelNeed.GetBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}
	var retItems = make([]TravelNeedItem, 0)
	for _, item := range items {
		var retItem = TravelNeedItem{
			Id:         item.Id,
			Name:       item.Name,
			Code:       item.Code,
			Remark:     item.Remark,
			Status:     item.Status,
			OpUserName: item.OpUserName,
			Url:        fmt.Sprintf("%v%v", config.Config.StaticFileHttpPrefix, item.QrCodePath), // 出行需求H5链接
			CreatedAt:  item.CreatedAt,
		}

		var pp operation.TravelNeedDetail
		count, err := pp.GetCountBy(item.Id)
		if err != nil {
			log.ErrorFields("GetCountBy error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbQueryFail)
		}
		retItem.Count = count

		retItems = append(retItems, retItem)
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      retItems,
		"TotalCount": totalCount,
	})
}

type AddTravelNeedParam struct {
	Id     int64  `json:"Id"`
	Name   string `json:"Name"`
	Remark string `json:"Remark"`
	Status int64  `json:"Status"`
}

func (*DataManagement) AddTravelNeed(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AddTravelNeedParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var topCorpId = auth.User(ctx).GetTopCorporationId()
	if topCorpId == 0 {
		return response.Error(rsp, "PA1106")
	}
	param.Name = strings.TrimSpace(param.Name)
	param.Remark = strings.TrimSpace(param.Remark)

	if len(param.Name) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	var p operation.TravelNeed

	var (
		user = auth.User(ctx).GetUser()
	)

	p.OpUserId = user.Id
	p.OpUserName = user.Name
	p.Name = param.Name
	p.Remark = param.Remark
	p.Status = operation.TravelNeed_Status_NoValid
	p.TopCorporationId = topCorpId

	// 生成二维码链接
	var code = util.GenerateRandomString(10)
	p.Code = code

	qrPath, err := generateTravelNeedQrcode(topCorpId, code)
	if err != nil {
		return response.Error(rsp, response.DbQueryFail)
	}
	p.QrCodePath = qrPath

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	err = p.Create(tx)
	if err != nil {
		log.ErrorFields("Create error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

// 生成设备二维码
func generateTravelNeedQrcode(topCorpId int64, code string) (string, error) {
	// 相对路径 webroot/erp/...
	relativePath := fmt.Sprintf(`%s/erp/%v/%v/`, config.Config.WebRoot, "travel_need_qrcodes", topCorpId)

	// 完整路径 /mnt/www/webroot/erp/...
	fullPath := fmt.Sprintf("%s%s", config.Config.AbsDirPath, relativePath)
	qrcodeLink := fmt.Sprintf("%s?TravelNeedCode=%s", config.Config.DeviceQrcodeUrl, code)

	newFileName := fmt.Sprintf("%s%s", code, util.Default_Suffix)

	err := util.SaveQrcode(fullPath, newFileName, qrcodeLink)
	if err != nil {
		log.ErrorFields("二维码生成错误", map[string]interface{}{"err": err})
		return "", err
	}

	return fmt.Sprintf(`%s%s`, relativePath, newFileName), nil
}

func (*DataManagement) EditTravelNeed(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param AddTravelNeedParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.Name = strings.TrimSpace(param.Name)
	param.Remark = strings.TrimSpace(param.Remark)

	if len(param.Name) == 0 || param.Id == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	var p = operation.TravelNeed{
		PkId: model.PkId{param.Id},
	}

	var pm = make(map[string]interface{}, 0)
	pm["name"] = param.Name
	pm["remark"] = param.Remark
	pm["status"] = param.Status

	err = p.Update(tx, pm)
	if err != nil {
		log.ErrorFields("Update error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbSaveFail)
	}

	return response.Success(rsp, nil)
}

type DelTravelNeedParam struct {
	Ids []int64 `json:"Ids"`
}

func (*DataManagement) DelTravelNeed(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DelTravelNeedParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(param.Ids) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	for _, id := range param.Ids {
		var p = operation.TravelNeed{}

		err = p.DeleteById(tx, id)
		if err != nil {
			log.ErrorFields("DelTravelNeed error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	return response.Success(rsp, nil)
}

type TravelNeedDetailItem struct {
	operation.TravelNeedDetail
	TravelDays     []int64 `json:"TravelDays"`
	TravelPurposes []int64 `json:"TravelPurposes"`
}

func getTravelNeedDetailItems(items []operation.TravelNeedDetail) []TravelNeedDetailItem {
	var retItems = make([]TravelNeedDetailItem, 0)
	for _, item := range items {
		retItems = append(retItems, TravelNeedDetailItem{
			TravelNeedDetail: item,
			TravelDays:       util.GetIntArrWithBits(item.TravelDay, operation.TravelDayArr),
			TravelPurposes:   util.GetIntArrWithBits(item.TravelPurpose, operation.TravelPurposeArr),
		})
	}
	return retItems
}

type ListTravelNeedDetailParam struct {
	TravelNeedId   int64   `json:"TravelNeedId"`
	TravelNeedIds  []int64 `json:"TravelNeedIds"`
	TravelPurposes []int64 `json:"TravelPurposes"`
	DepartAddress  string  `json:"DepartAddress"`
	ArriveAddress  string  `json:"ArriveAddress"`
	model.Paginator
}

func (*DataManagement) ListTravelNeedDetail(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListTravelNeedDetailParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var topCorpId = auth.User(ctx).GetTopCorporationId()

	var pp = operation.TravelNeedDetail{
		TopCorporationId: topCorpId,
		TravelNeedId:     param.TravelNeedId,
		DepartAddress:    param.DepartAddress,
		ArriveAddress:    param.ArriveAddress,
	}
	items, totalCount, err := pp.GetBy(param.Paginator, &operation.TravelNeedDetailParam{
		TravelPurposes: param.TravelPurposes,
		TravelNeedIds:  param.TravelNeedIds,
	})
	if err != nil {
		log.ErrorFields("ListTravelNeedDetail.GetBy error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	return response.Success(rsp, map[string]interface{}{
		"Items":      getTravelNeedDetailItems(items),
		"TotalCount": totalCount,
	})
}

func (this *DataManagement) ListTravelNeedDetailExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return this.ListTravelNeedDetail(ctx, req, rsp)
}

type AddTravelNeedDetailParam struct {
	TravelNeedCode string                 `json:"TravelNeedCode"`
	TravelNeedId   int64                  `json:"TravelNeedId"`
	Items          []TravelNeedDetailItem `json:"Items"`
}

func (*DataManagement) AddTravelNeedDetail(ctx context.Context, req *api.Request, rsp *api.Response) error {

	var param AddTravelNeedDetailParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	param.TravelNeedCode = strings.TrimSpace(param.TravelNeedCode)
	if param.TravelNeedCode == "" && param.TravelNeedId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	// 参数校验
	for _, item := range param.Items {
		if len(item.TravelDays) == 0 || len(item.TravelPurposes) == 0 || item.TravelMode == 0 ||
			item.DepartTime == 0 || item.DepartAddress == "" {
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	var p operation.TravelNeed

	if param.TravelNeedCode != "" {
		err = p.GetByCode(param.TravelNeedCode)
	} else {
		err = p.GetById(param.TravelNeedId)
	}
	if err != nil {
		log.ErrorFields("operation.TravelNeed error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	if p.Id == 0 {
		log.ErrorFields("operation.TravelNeed error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbNotFoundRecord)
	}

	if p.Status == operation.TravelNeed_Status_NoValid {
		return response.Error(rsp, "OP9907")
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	for _, item := range param.Items {

		var retItem = operation.TravelNeedDetail{}
		retItem = item.TravelNeedDetail
		retItem.TravelNeedId = p.Id
		retItem.TopCorporationId = p.TopCorporationId
		retItem.TravelPurpose = util.GetIntWithIntsBits(item.TravelPurposes, operation.TravelPurposeArr)
		retItem.TravelDay = util.GetIntWithIntsBits(item.TravelDays, operation.TravelDayArr)

		err = retItem.Create(tx)
		if err != nil {
			log.ErrorFields("operation.TravelNeedDetail.Create error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	return response.Success(rsp, response.SUCCESS)
}

func (*DataManagement) DelTravelNeedDetail(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param DelTravelNeedParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(param.Ids) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	for _, id := range param.Ids {
		var p = operation.TravelNeedDetail{}

		err = p.DeleteById(tx, id)
		if err != nil {
			log.ErrorFields("TravelNeedDetail error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	return response.Success(rsp, nil)
}
