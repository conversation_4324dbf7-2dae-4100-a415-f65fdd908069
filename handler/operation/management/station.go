package management

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/handler/operation/lineDriverReport"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/export"
	"app/org/scs/erpv2/api/model/operation"
	protoSchedule "app/org/scs/erpv2/api/proto/rpc/iss"
	protoMiniStation "app/org/scs/erpv2/api/proto/rpc/mini_station"
	protoLine "app/org/scs/erpv2/api/proto/rpc/oetline"
	protoLineStation "app/org/scs/erpv2/api/proto/rpc/oetlinestation"
	protoStation "app/org/scs/erpv2/api/proto/rpc/station"
	"app/org/scs/erpv2/api/service/auth"
	exportService "app/org/scs/erpv2/api/service/export"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/httpClient"
	"app/org/scs/erpv2/api/util/response"
	xlsxUtil "app/org/scs/erpv2/api/util/xlsx"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/paulmach/orb"
	"github.com/paulmach/orb/geo"
	"github.com/tealeg/xlsx"
	"io"
	"math"
	"os"
	"path"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"
)

type Region struct {
	CityCode string `json:"CityCode"`
	Name     string `json:"Name"`
}

// 获取区域列表
func (*DataManagement) ListRegion(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param lineDriverReport.LineReportParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var topCorpId = auth.User(ctx).GetTopCorporationId()
	if topCorpId == 0 {
		return response.Error(rsp, "PA1106")
	}

	var corpId = operation.CorpRegion{}

	var items = corpId.GetByTopCorpId(topCorpId)

	var retItems = make([]Region, 0)
	for _, item := range items {
		retItems = append(retItems, Region{
			CityCode: item.CityCode,
			Name:     item.Name,
		})
	}

	return response.Success(rsp, retItems)
}

type ListStationParam struct {
	CityCodes            []string                    `json:"CityCodes"`
	Lines                []*protoStation.OetLineItem `json:"Lines"`
	StreetName           string                      `json:"StreetName"`
	OwnedRoads           []string                    `json:"OwnedRoads"`
	Name                 string                      `json:"Name"` // 站点名(站点名或者站点ID)，模糊搜索
	ElecBusBoardTypes    []int64                     `json:"ElecBusBoardTypes"`
	CompletionTimeGteStr model.LocalTime             `json:"CompletionTimeGte"`
	CompletionTimeLteStr model.LocalTime             `json:"CompletionTimeLte"`
	StationIds           []int64                     `json:"StationIds"`

	Points [][]string `json:"Points"` // 二维数组[["a1,b1","a2,b2"],["aa1,bb1","aa1,bb1"]]

	IsExportStationExcel   bool
	IsExportStationReality bool
	IsMap                  bool //  true 地图展示(不需要站牌配置等信息)

	model.Paginator
	Order string `json:"Order"` // 排序

}

type OetStationItem struct {
	*protoStation.OetStationItem
	RegionName           string `json:"RegionName,omitempty"`           // 行政区域
	StationSize          string `json:"StationSize,omitempty"`          // 站牌尺寸
	Corporation          string `json:"Corporation,omitempty"`          // 所属机构
	StationSpecification string `json:"StationSpecification,omitempty"` // 站牌规格
	StationMaterial      string `json:"StationMaterial,omitempty"`      // 站牌材料
	Extension            string `json:"Extension,omitempty"`            // 左右预留空间
}

// 获取站点列表
func (op *DataManagement) ListStation(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var topCorpId = auth.User(ctx).GetTopCorporationId()
	retItems, totalCount, errCode := op.listStation(ctx, topCorpId, req)
	if errCode != response.SUCCESS {
		return response.Error(rsp, errCode)
	}
	return response.Success(rsp, map[string]interface{}{
		"Items":      retItems,
		"TotalCount": totalCount,
	})
}
func (op *DataManagement) listStation(ctx context.Context, topCorpId int64, req *api.Request) (retItems []OetStationItem, totalCount int64, errCode string) {
	var param ListStationParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return retItems, totalCount, response.ParamsInvalid
	}

	var protoStationOpt = protoStation.GetStationsWithOption2Request{
		CorporationId:           topCorpId,
		AdminIsTrAtIveDivisions: param.CityCodes,
		StreetName:              param.StreetName,
		Name:                    param.Name,
		Lines:                   param.Lines,
		OwnedRoads:              param.OwnedRoads,
		ElecBusBoardTypes:       param.ElecBusBoardTypes,
		StationIds:              param.StationIds,
		Offset:                  int64(param.Offset),
		Limit:                   int64(param.Limit),
		Order:                   param.Order,
	}

	var (
		completionTimeGte = time.Time(param.CompletionTimeGteStr).Unix()
		completionTimeLte = time.Time(param.CompletionTimeLteStr).Unix()
	)
	if completionTimeGte > 0 {
		protoStationOpt.CompletionStartTime = completionTimeGte
	}
	if completionTimeLte > 0 {
		protoStationOpt.CompletionEndTime = completionTimeLte + 24*60*60 - 1
	}

	var (
		r     = operation.CorpRegion{}
		osms  = operation.MaterialSetting{}
		oss   = operation.SpecificationSetting{}
		items = make([]*protoStation.OetStationItem, 0)
	)
	regionItems := r.GetByTopCorpId(topCorpId)

	if len(param.Points) > 0 {
		// 区域计算
		protoStationOpt.Limit = 999999
		items, totalCount = rpc.GetStationsWithOption2(ctx, &protoStationOpt)
		items, totalCount = rpc.SearchStationsWithMoreCustomPoints(items, param.Points)
		// 如果分页
		if param.Limit > 0 {
			// 分页
			if param.Limit+param.Offset >= int(totalCount) {
				if param.Offset < int(totalCount) {
					items = items[param.Offset:]
				}
			} else {
				items = items[param.Offset:(param.Offset + param.Limit)]
			}
		}

	} else {
		items, totalCount = rpc.GetStationsWithOption2(ctx, &protoStationOpt)
	}

	var (
		corporationMap = make(map[int64]string)
	)
	for _, item := range items {
		var (
			corpName string
			ok       bool
		)
		if corpName, ok = corporationMap[item.CorporationId]; !ok {
			corpName = rpc.GetCorporationNameById(ctx, item.CorporationId)
			corporationMap[item.CorporationId] = corpName
		}

		var (
			stationspecification string
			extension            string
			stationMaterial      string
		)

		if param.IsMap {

		} else {
			var (
				ssItem  = oss.GetByStationId(item.Id)
				ssmItem = osms.GetByStationId(item.Id)
			)

			if ssItem.Id > 0 {
				stationspecification = fmt.Sprintf("长:%v 高:%v", float64(ssItem.Length)/float64(10), float64(ssItem.Width)/float64(10))
				extension = fmt.Sprintf("%vmm", ssItem.Right)
			}
			stationMaterial = ssmItem.Name
		}

		var retItem = OetStationItem{
			OetStationItem:       item,
			RegionName:           operation.GetRegionName(regionItems, item.AdminIsTrAtIveDivision),
			StationSize:          "",
			Corporation:          corpName,
			StationSpecification: stationspecification,
			StationMaterial:      stationMaterial,
			Extension:            extension,
		}
		retItems = append(retItems, retItem)
	}
	return retItems, totalCount, response.SUCCESS
}

// 省级数据导出
func (op *DataManagement) ProvincialDemandStationExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListStationParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var (
		user      = auth.User(ctx)
		topCorpId = user.GetTopCorporationId()
		startAt   = time.Now()
		endAt     = time.Now()
	)

	exportFileRecord, err := exportService.CreateExportFileRecord(user.GetUserId(), "省级数据导出.xlsx", "ProvincialDemandStation", []byte(req.Body), startAt, endAt)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	go op.GenerateStationExcelWithProvincialDemand(req, topCorpId, exportFileRecord)
	return response.Error(rsp, response.SUCCESS)

}

func (op *DataManagement) GenerateStationExcelWithProvincialDemand(req *api.Request, topCorpId int64, efr export.ExportFile) {
	retItems, _, _ := op.listStation(context.Background(), topCorpId, req)
	var (
		baseFullPath = exportService.GetExcelFolder(efr)
		fileName     = efr.Key
	)

	// 生成excel文件
	err := generateStationExcelWithProvincialDemand(baseFullPath, fileName, retItems)
	if err != nil {
		efr.UpdateFail()
		log.ErrorFields("generateCustomLineStreetPlateExcel error", map[string]interface{}{"err": err})
		return
	}
	efr.UpdateSuccess()
	return
}
func (op *DataManagement) ListStationExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return op.ListStation(ctx, req, rsp)
}

func (op *DataManagement) ListStationRealityExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListStationParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var (
		user = auth.User(ctx)
	)

	var topCorpId = user.GetTopCorporationId()
	if topCorpId == 0 {
		return response.Error(rsp, "PA1106")
	}

	if !param.IsExportStationExcel && !param.IsExportStationReality {
		return response.Error(rsp, response.ParamsInvalid)
	}

	var protoStationOpt = protoStation.GetStationsWithOption2Request{
		CorporationId:           topCorpId,
		AdminIsTrAtIveDivisions: param.CityCodes,
		StreetName:              param.StreetName,
		Name:                    param.Name,
		Lines:                   param.Lines,
		OwnedRoads:              param.OwnedRoads,
		ElecBusBoardTypes:       param.ElecBusBoardTypes,
		StationIds:              param.StationIds,
		Offset:                  int64(param.Offset),
		Limit:                   99999,
		Order:                   param.Order,
	}

	var (
		completionTimeGte = time.Time(param.CompletionTimeGteStr).Unix()
		completionTimeLte = time.Time(param.CompletionTimeLteStr).Unix()
	)
	if completionTimeGte > 0 {
		protoStationOpt.CompletionStartTime = completionTimeGte
	}
	if completionTimeLte > 0 {
		protoStationOpt.CompletionEndTime = completionTimeLte + 24*60*60 - 1
	}
	var (
		startAt = time.Now()
		endAt   = time.Now()
	)
	if param.IsExportStationExcel {
		exportFileRecord, err := exportService.CreateExportFileRecord(user.GetUserId(), "站点列表.xlsx", "ListStationRealityExport", []byte(req.Body), startAt, endAt)
		if err != nil {
			return response.Error(rsp, response.DbSaveFail)
		}
		go stationRealityExport(topCorpId, true, false, protoStationOpt, exportFileRecord)
	}
	if param.IsExportStationReality {
		exportFileRecord, err := exportService.CreateExportFileRecord(user.GetUserId(), "线路站点分析(站点查询).zip", "ListStationRealityExport", []byte(req.Body), startAt, endAt)
		if err != nil {
			return response.Error(rsp, response.DbSaveFail)
		}
		go stationRealityExport(topCorpId, false, true, protoStationOpt, exportFileRecord)
	}

	return response.Success(rsp, nil)
}

func stationRealityExport(topCorpId int64, isExportStationExcel, isExportStationReality bool, protoStationOpt protoStation.GetStationsWithOption2Request, efr export.ExportFile) {
	items, _ := rpc.GetStationsWithOption2(context.Background(), &protoStationOpt)

	var r = operation.CorpRegion{}
	regionItems := r.GetByTopCorpId(topCorpId)

	// efr.FileName : 线路站点分析(站点查询).zip   strings.TrimSuffix(efr.FileName, filepath.Ext(efr.FileName)): 线路站点分析(站点查询)
	var svc = NewStationRealityExportSvc(efr.FileName, strings.TrimSuffix(efr.FileName, filepath.Ext(efr.FileName)), topCorpId, regionItems, items)
	if isExportStationExcel {
		err := svc.exportExcel(efr.Key, exportService.GetExcelFolder(efr))
		if err != nil {
			efr.UpdateFail()
			log.ErrorFields("exportExcel error", map[string]interface{}{"err": err})
			return
		}
		efr.UpdateSuccess()
	}
	if isExportStationReality {
		path, err := svc.export(isExportStationExcel)
		if err != nil {
			efr.UpdateFail()
			log.ErrorFields("stationRealityExport error", map[string]interface{}{"err": err})
			return
		}
		efr.Path = strings.Replace(path, config.Config.AbsDirPath, "", -1)
		efr.Status = util.ExportFileStatusForDone
		efr.UpdateStatus()
	}

	return
}

func (op *DataManagement) ListStationRealityByLineExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListStreetPlateByStationParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var (
		user    = auth.User(ctx)
		startAt = time.Now()
		endAt   = time.Now()
	)

	if len(param.Lines) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	var topCorpId = user.GetTopCorporationId()
	if topCorpId == 0 {
		return response.Error(rsp, "PA1106")
	}

	exportFileRecord, err := exportService.CreateExportFileRecord(user.GetUserId(), "线路站点分析(线路查询).zip", "ListStationRealityByLineExport", []byte(req.Body), startAt, endAt)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	go stationRealityByLineExport(topCorpId, param, exportFileRecord)
	return response.Success(rsp, nil)
}

func stationRealityByLineExport(topCorpId int64, param ListStreetPlateByStationParam, efr export.ExportFile) {
	var retItemss = getTwoDimOetLineStationItem(param)

	regionItems := (&operation.CorpRegion{}).GetByTopCorpId(topCorpId)

	path, err := NewLineStationRealityExportSvc(efr.FileName, strings.TrimSuffix(efr.FileName, filepath.Ext(efr.FileName)), topCorpId, regionItems, param.Lines, retItemss).export()
	if err != nil {
		efr.UpdateFail()
		log.ErrorFields("NewLineStationRealityExportSvc error", map[string]interface{}{"err": err})
		return
	}
	efr.Path = strings.Replace(path, config.Config.AbsDirPath, "", -1)
	efr.Status = util.ExportFileStatusForDone
	efr.UpdateStatus()
}
func lineStationExportExcel(topCorpId int64, param ListStreetPlateByStationParam, efr export.ExportFile) {
	var retItemss = getTwoDimOetLineStationItem(param)

	regionItems := (&operation.CorpRegion{}).GetByTopCorpId(topCorpId)

	err := NewLineStationRealityExportSvc(efr.FileName, strings.TrimSuffix(efr.FileName, filepath.Ext(efr.FileName)), topCorpId, regionItems, param.Lines, retItemss).
		exportExcel(efr.Key, exportService.GetExcelFolder(efr))
	if err != nil {
		efr.UpdateFail()
		log.ErrorFields("NewLineStationRealityExportSvc error", map[string]interface{}{"err": err})
		return
	}
	efr.UpdateSuccess()
}

func getTwoDimOetLineStationItem(param ListStreetPlateByStationParam) [][]OetLineStationItem {
	var (
		ctx       = context.Background()
		retItemss = make([][]OetLineStationItem, 0)
	)
	for _, line := range param.Lines {
		var retItems = make([]*protoLineStation.OetLineStationItem, 0)
		var opt = &protoLineStation.GetOetLineStationWithLineIdRequest{
			LineId:     line.LineId,
			Sheet:      line.Sheet,
			FilterType: -1,
		}

		resp := rpc.GetOetLineStationWithLineId(ctx, opt)
		if resp == nil {
			log.ErrorFields("rpc.GetOetLineStationWithLineId is nil ", nil)
		}
		retItems = getAllLineStation(resp.Items, resp.SkipItems)

		assessItemM := rpc.GetAssessLineStationByLineId(ctx, &protoSchedule.GetAssessLineStationRequest{
			LineId: line.LineId,
			Sheet:  line.Sheet,
		})

		fmt.Println("站点数量: ", len(retItems))
		retItemss = append(retItemss, getOetLineStationItem(false, assessItemM, retItems))
	}
	return retItemss
}

func getAllLineStation(items, skipItems []*protoLineStation.OetLineStationItem) []*protoLineStation.OetLineStationItem {
	var retItems = make([]*protoLineStation.OetLineStationItem, 0)
	for i := 0; i < len(items); i++ {
		if items[i].Sequence <= 0 {
			continue
		}

		retItems = append(retItems, items[i])
	}

	for i := 0; i < len(skipItems); i++ {
		if skipItems[i].Sequence <= 0 {
			continue
		}
		var isExist bool
		for j := 0; j < len(retItems); j++ {
			if retItems[j].Sequence == skipItems[i].Sequence {
				isExist = true
				break
			}
		}
		if !isExist {
			retItems = append(retItems, skipItems[i])
		}
	}

	sort.SliceStable(retItems, func(i, j int) bool {
		return retItems[i].Sequence < retItems[j].Sequence
	})
	return retItems
}

type ListStreetPlateByLineParam struct {
	*protoStation.OetLineItem
	Price     int64 `json:"Price"` // 分
	FirstTime int64 `json:"FirstTime"`
	LastTime  int64 `json:"LastTime"`
}

// 导出路牌-线路维度
func (*DataManagement) ListStreetPlateByLine(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListStreetPlateByLineParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.OetLineItem == nil {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.LineId <= 0 || param.Sheet <= 0 || param.Price <= 0 || param.FirstTime <= 0 ||
		param.LastTime <= 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if setFont() == nil {
		return response.Error(rsp, "OP9903")
	}
	if !setFontFamily() {
		return response.Error(rsp, "OP9903")
	}
	var (
		user    = auth.User(ctx)
		startAt = time.Now()
		endAt   = time.Now()
	)

	oetLineItem, _ := rpc.GetLineWithId(ctx, param.LineId)
	if oetLineItem == nil {
		return response.Error(rsp, response.DbQueryFail)
	}
	var (
		zipName = fmt.Sprintf("%v_%v.zip", oetLineItem.Name, util.LineSheetMap[param.Sheet])
	)
	exportFileRecord, err := exportService.CreateExportFileRecord(user.GetUserId(), zipName, "ListStreetPlateByLine", []byte(req.Body), startAt, endAt)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	go streetPlateByLineExport(param, exportFileRecord, oetLineItem)

	return response.Success(rsp, nil)
}

func streetPlateByLineExport(param ListStreetPlateByLineParam, efr export.ExportFile, oetLineItem *protoLine.OetLineItem) {
	path, err := NewPosterTaskSvc().makePosterWithLine(param, oetLineItem, efr.FileName, strings.TrimSuffix(efr.FileName, filepath.Ext(efr.FileName)))
	if err != nil {
		efr.UpdateFail()
		log.ErrorFields("makePosterWithLine error", map[string]interface{}{"err": err})
		return
	}
	efr.Path = strings.Replace(path, config.Config.AbsDirPath, "", -1)
	efr.Status = util.ExportFileStatusForDone
	efr.UpdateStatus()
}

type ListStreetPlateByStationParam struct {
	Lines     []*protoStation.OetLineItem `json:"Lines"`
	StationId int64                       `json:"StationId"`
}

// 导出路牌-站点维度
func (*DataManagement) ListStreetPlateByStation(ctx context.Context, req *api.Request, rsp *api.Response) error {

	var param ListStreetPlateByStationParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(param.Lines) == 0 || param.StationId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	if setFont() == nil {
		return response.Error(rsp, "OP9903")
	}
	fmt.Println("==========================即将执行setFontFamily========================")
	if !setFontFamily() {
		return response.Error(rsp, "OP9903")
	}
	var (
		user    = auth.User(ctx)
		startAt = time.Now()
		endAt   = time.Now()
	)

	exportFileRecord, err := exportService.CreateExportFileRecord(user.GetUserId(), "路牌导出(按站点).zip", "ListStreetPlateByStation", []byte(req.Body), startAt, endAt)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}
	go streetPlateByStationExport(param, exportFileRecord)

	return response.Success(rsp, nil)
}

func streetPlateByStationExport(param ListStreetPlateByStationParam, efr export.ExportFile) {
	path, err := NewPosterTaskSvc().makePosterWithStation(param, efr.FileName, strings.TrimSuffix(efr.FileName, filepath.Ext(efr.FileName)))
	if err != nil {
		efr.UpdateFail()
		log.ErrorFields("makePosterWithLine error", map[string]interface{}{"err": err})
		return
	}

	efr.Path = strings.Replace(path, config.Config.AbsDirPath, "", -1)
	efr.Status = util.ExportFileStatusForDone
	efr.UpdateStatus()
}

// 获取线路列表
func (*DataManagement) ListLine(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return nil
}

type StationSettingParam struct {
	StationIds            []int64 `json:"StationIds"`
	MaterialSettingIds    []int64 `json:"MaterialSettingIds"` // 材料ID
	SpecificationSettings []int64 `json:"SpecificationSettings"`
}

func (*DataManagement) StationSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param StationSettingParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if len(param.StationIds) == 0 || len(param.MaterialSettingIds) == 0 || len(param.SpecificationSettings) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	for _, stationId := range param.StationIds {
		if stationId == 0 {
			return response.Error(rsp, response.ParamsInvalid)
		}
	}

	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	for _, stationId := range param.StationIds {
		var items = make([]operation.OperationAssociationSetting, 0)
		for _, item := range param.MaterialSettingIds {
			items = append(items, operation.OperationAssociationSetting{
				AssociationObjectId:   stationId,
				AssociationObjectType: operation.AssociationObjectForStation,
				SettingId:             item,
				Type:                  operation.StationSetting_MaterialType,
			})
		}

		for _, item := range param.SpecificationSettings {
			items = append(items, operation.OperationAssociationSetting{
				AssociationObjectId:   stationId,
				AssociationObjectType: operation.AssociationObjectForStation,
				SettingId:             item,
				Type:                  operation.StationSetting_SpecificationType,
			})
		}

		var ss = operation.OperationAssociationSetting{
			AssociationObjectId:   stationId,
			AssociationObjectType: operation.AssociationObjectForStation,
		}

		err = ss.CreateOrUpdate(tx, items)
		if err != nil {
			log.ErrorFields("CreateOrUpdate error", map[string]interface{}{"err": err})
			return response.Error(rsp, response.DbSaveFail)
		}
	}

	return response.Success(rsp, nil)

}

type StationSettingItem struct {
	Id   int64  `json:"Id"`
	Name string `json:"Name"`
	Type int64  `json:"Type"`
}

type GetStationSettingParam struct {
	StationId int64 `json:"StationId"`
}

func (*DataManagement) GetStationSetting(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param GetStationSettingParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	if param.StationId == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	var ss operation.OperationAssociationSetting
	items, err := ss.GetByStationId(param.StationId)
	if err != nil {
		log.ErrorFields("GetByStationId error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.DbQueryFail)
	}

	var retItems = make([]StationSettingItem, 0)

	for _, item := range items {
		var retItem = StationSettingItem{
			Id:   item.SettingId,
			Type: item.Type,
		}
		switch item.Type {
		case operation.StationSetting_MaterialType:
			var oss = operation.MaterialSetting{
				PkId: model.PkId{Id: item.SettingId},
			}
			oss.GetBy()
			retItem.Name = oss.Name
		case operation.StationSetting_SpecificationType:
			var oss = operation.SpecificationSetting{
				PkId: model.PkId{Id: item.SettingId},
			}
			oss.GetBy()
			retItem.Name = oss.Name
		}
		retItems = append(retItems, retItem)
	}
	return response.Success(rsp, map[string]interface{}{
		"Items": retItems,
	})
}

type ListLineStationParam struct {
	LineId     int64 `json:"LineId"`     // 选择线路id
	Sheet      int64 `json:"Sheet"`      // 线路方向，0-全部，1-上行；2-下行；3-环行
	FilterType int64 `json:"FilterType"` // 过滤类型, 不过滤:-1；过滤：0-中途站，1-起点站，2-终点站，3-采样点
}

type OetLineStationItem struct {
	*protoLineStation.OetLineStationItem
	FirstDepartTime int64 `json:"FirstDepartTime,omitempty"`
	LastDepartTime  int64 `json:"LastDepartTime,omitempty"`
}

// 查询线路站点列表
func (*DataManagement) ListLineStation(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListLineStationParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	var opt = &protoLineStation.GetOetLineStationWithLineIdRequest{
		LineId:     param.LineId,
		Sheet:      param.Sheet,
		FilterType: param.FilterType,
	}

	assessItemM := rpc.GetAssessLineStationByLineId(ctx, &protoSchedule.GetAssessLineStationRequest{
		LineId: param.LineId,
		Sheet:  param.Sheet,
	})

	resp := rpc.GetOetLineStationWithLineId(ctx, opt)
	if resp == nil {
		log.ErrorFields("rpc.GetOetLineStationWithLineId is nil ", nil)
	}

	return response.Success(rsp, map[string]interface{}{
		"StationItems":     getOetLineStationItem(true, assessItemM, resp.Items),
		"SkipStationItems": getOetLineStationItem(true, assessItemM, resp.SkipItems),
	})
}

func (*DataManagement) ListLineStationExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param ListStreetPlateByStationParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var (
		user    = auth.User(ctx)
		startAt = time.Now()
		endAt   = time.Now()
	)

	if len(param.Lines) == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}

	var topCorpId = user.GetTopCorporationId()
	if topCorpId == 0 {
		return response.Error(rsp, "PA1106")
	}

	exportFileRecord, err := exportService.CreateExportFileRecord(user.GetUserId(), "线路查询导出.xlsx", "ListLineStationExport", []byte(req.Body), startAt, endAt)
	if err != nil {
		return response.Error(rsp, response.FAIL)
	}

	go lineStationExportExcel(topCorpId, param, exportFileRecord)
	return response.Success(rsp, nil)
}

func getOetLineStationItem(isPoint bool, assessItemM map[int64][]*protoSchedule.AssessLineStationItem, items []*protoLineStation.OetLineStationItem) []OetLineStationItem {
	var retItems = make([]OetLineStationItem, 0)

	for _, item := range items {
		var retItem = OetLineStationItem{
			OetLineStationItem: item,
		}

		if item.StationId == 0 && isPoint {
			retItems = append(retItems, retItem)
			continue
		}

		if item.StationId > 0 {
			if len(assessItemM) > 0 {
				firstItem := rpc.GetAssessLineStationByOpt(item.Sheet, item.StationId, item.Sequence, rpc.AssessLineStation_FirstLast_First, assessItemM)
				if firstItem != nil {
					retItem.FirstDepartTime = firstItem.PlanDepartTime
				}

				lastItem := rpc.GetAssessLineStationByOpt(item.Sheet, item.StationId, item.Sequence, rpc.AssessLineStation_FirstLast_Last, assessItemM)
				if lastItem != nil {
					retItem.LastDepartTime = lastItem.PlanDepartTime
				}
			}
			retItems = append(retItems, retItem)
		}

	}
	return retItems
}

type StationRealityExportSvc struct {
	TopCorpId int64
	ZipName   string
	Folder    string
	Regions   []operation.CorpRegion
	Items     []*protoStation.OetStationItem
}

func NewStationRealityExportSvc(zipName, folder string, topCorpId int64, regions []operation.CorpRegion, items []*protoStation.OetStationItem) *StationRealityExportSvc {
	return &StationRealityExportSvc{
		TopCorpId: topCorpId,
		ZipName:   zipName,
		Folder:    folder,
		Regions:   regions,
		Items:     items,
	}
}

func (svc *StationRealityExportSvc) export(isExportStationExcel bool) (string, error) {
	var (
		zipName   = svc.ZipName
		folder    = svc.Folder
		topCorpId = svc.TopCorpId
	)

	baseFullPath, zipTmpFullPath, storageFullPath := getStoragePath("reality", topCorpId, folder)

	var storageReFullPath = fmt.Sprintf("%s/站点实景", storageFullPath)

	err := util.VerifyMkdirExistAndCreate(storageReFullPath)
	if nil != err {
		return "", err
	}

	if isExportStationExcel {
		// 生成站点信息表格
		err := svc.exportExcel("站点信息.xlsx", storageFullPath)
		if err != nil {
			log.ErrorFields("exportExcel error ", map[string]interface{}{"err": err})
			return "", err
		}
	}

	svc.download(storageReFullPath)

	// 压缩文件
	err = util.ZipWriter(zipTmpFullPath, baseFullPath+"/"+zipName)
	if err != nil {
		log.ErrorFields("util.ZipWriter error ", map[string]interface{}{"err": err})
		return "", err
	}
	//移除临时文件夹
	err = os.RemoveAll(zipTmpFullPath)
	if err != nil {
		log.ErrorFields("os.RemoveAll error ", map[string]interface{}{"err": err})
		return "", err
	}
	return baseFullPath + "/" + zipName, nil
}

func (svc *StationRealityExportSvc) download(storageReFullPath string) error {

	var wg sync.WaitGroup

	for _, item := range svc.Items {
		wg.Add(1)
		go func(storageReFullPath string, item *protoStation.OetStationItem) {
			err := svc.downloadSingle(storageReFullPath, item)
			if err != nil {
				log.ErrorFields("downloadStationReality error ", map[string]interface{}{"err": err})
				return
			}
			wg.Done()
		}(storageReFullPath, item)
	}
	wg.Wait()
	return nil
}

func (svc *StationRealityExportSvc) downloadSingle(storageFullPath string, item *protoStation.OetStationItem) error {

	var newPath = fmt.Sprintf("%s_%s_%s", item.Code, item.Name, util.StationDirectionMap[item.Direction])

	var imagePath = storageFullPath + "/" + newPath
	if err := util.VerifyMkdirExistAndCreate(imagePath); nil != err {
		return err
	}

	for _, ad := range item.RealityImages {
		req := httpClient.Request{
			Url:      ad.ImageUrl,
			Method:   "GET",
			Insecure: true,
		}
		resp, err := req.Do()
		if err != nil {
			log.ErrorFields("item.RealityImages error ", map[string]interface{}{"err": err})
			continue
		}
		defer resp.Body.Close()

		ext := filepath.Ext(path.Base(ad.ImageUrl))

		var newFileName = fmt.Sprintf("%s_%s%s", newPath, util.StationRealityImageOrientationMap[ad.Orientation], ext)

		f, err := os.Create(imagePath + "/" + newFileName)
		if err != nil {
			log.ErrorFields("os.Create error ", map[string]interface{}{"err": err})
			continue
		}
		defer f.Close()

		io.Copy(f, resp.Body)
	}
	return nil
}

func (svc *StationRealityExportSvc) exportExcel(fileName string, basePath string) error {

	var (
		style       = xlsxUtil.GetExcelPubStyle()
		stringStyle = xlsx.MakeStringStyle(&style.Font, &style.Fill, &style.Alignment, &style.Border)
	)

	var xlsxCellMetadataAndHeaders = []xlsxUtil.XlsxCellMetadataAndHeader{
		{HeaderName: "序号", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeNumeric, stringStyle)},
		{HeaderName: "站点名称", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点ID", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "经度", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "纬度", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点方位", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "运营状态", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "行政区划", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "所属街道", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "所属道路", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "停靠线路", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点类型", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站牌类型", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
	}

	streamFile, err := xlsxUtil.GetStreamFile(fileName, basePath, xlsxCellMetadataAndHeaders)
	if err != nil {
		log.ErrorFields("GetStreamFile error [%v]", map[string]interface{}{"err": err})
		return err
	}

	itemsLen := len(svc.Items)
	if 0 >= itemsLen {
		return nil
	}

	for idx := 0; idx < itemsLen; idx++ {
		var cells []string
		var index = idx + 1
		var item = svc.Items[idx]
		cells = append(cells, fmt.Sprintf("%v", index))
		cells = append(cells, item.Name)                                                         // 名称
		cells = append(cells, item.Code)                                                         // Id
		cells = append(cells, fmt.Sprintf("%v", item.Longitude))                                 // 经度
		cells = append(cells, fmt.Sprintf("%v", item.Latitude))                                  // 纬度
		cells = append(cells, util.StationDirectionMap[item.Direction])                          // 站点方位
		cells = append(cells, "正常")                                                              // 运营状态
		cells = append(cells, operation.GetRegionName(svc.Regions, item.AdminIsTrAtIveDivision)) // 行政区划
		cells = append(cells, item.StreetName)                                                   // 所属街道
		cells = append(cells, item.OwnedRoad)                                                    // 所属道路
		cells = append(cells, GetLineStr(item.Lines))                                            // 停靠线路
		cells = append(cells, util.StationPlatformTypeMap[item.PlatformType])                    // 站台类型
		cells = append(cells, util.StationBusBoardTypeMap[item.BusBoardType])                    // 站牌类型

		err = streamFile.WriteWithColumnDefaultMetadata(cells)
		if err != nil {
			log.ErrorFields("streamFile.WriteWithColumnDefaultMetadata error[%v]", map[string]interface{}{"err": err})
			return err
		}
	}

	// 写文件
	if err = streamFile.Close(); err != nil {
		return errors.New(fmt.Sprintf("写入文件异常,错误信息[%v]", err))
	}
	return nil
}

func getBaseFullPath(action string, topCorpId int64) string {
	var now = time.Now()
	// 路径前缀 /mnt/www/
	prefixPath := config.Config.AbsDirPath

	// 相对路径 webroot/erp/...
	var baseRelativePath = fmt.Sprintf("%s/erp/%s/%v/%s/%s", config.Config.WebRoot, action, topCorpId, now.Format("20060102"), util.GenerateRandomString(6))

	// 完整路径 /mnt/www/webroot/erp/...
	baseFullPath := fmt.Sprintf("%s/%s", prefixPath, baseRelativePath)
	return baseFullPath
}

func getStoragePath(action string, topCorpId int64, folder string) (string, string, string) {
	var baseFullPath = getBaseFullPath(action, topCorpId)
	// 压缩目录
	zipTmpFullPath := fmt.Sprintf("%s/tmp", baseFullPath)
	// 存储目录
	storageFullPath := fmt.Sprintf("%s/%s", zipTmpFullPath, folder)

	if err := util.VerifyMkdirExistAndCreate(storageFullPath); nil != err {
		return "", "", ""
	}

	return baseFullPath, zipTmpFullPath, storageFullPath
}

type LineStationRealityExportSvc struct {
	TopCorpId int64
	ZipName   string
	Folder    string
	Regions   []operation.CorpRegion
	Items     [][]OetLineStationItem
	Lines     []*protoStation.OetLineItem
}

func NewLineStationRealityExportSvc(zipName, folder string, topCorpId int64, regions []operation.CorpRegion, lines []*protoStation.OetLineItem, items [][]OetLineStationItem) *LineStationRealityExportSvc {
	return &LineStationRealityExportSvc{
		TopCorpId: topCorpId,
		ZipName:   zipName,
		Folder:    folder,
		Regions:   regions,
		Items:     items,
		Lines:     lines,
	}
}

func (svc *LineStationRealityExportSvc) export() (string, error) {
	var (
		zipName   = svc.ZipName
		folder    = svc.Folder
		topCorpId = svc.TopCorpId
	)

	baseFullPath, zipTmpFullPath, storageFullPath := getStoragePath("reality", topCorpId, folder)

	if err := util.VerifyMkdirExistAndCreate(storageFullPath); nil != err {
		return "", err
	}
	// 生成站点信息表格
	err := svc.exportExcel("线路站点关系.xlsx", storageFullPath)
	if err != nil {
		log.ErrorFields("exportExcel error ", map[string]interface{}{"err": err})
		return "", err
	}

	svc.download(storageFullPath)

	// 压缩文件
	err = util.ZipWriter(zipTmpFullPath, baseFullPath+"/"+zipName)
	if err != nil {
		log.ErrorFields("util.ZipWriter error ", map[string]interface{}{"err": err})
		return "", err
	}
	//移除临时文件夹
	err = os.RemoveAll(zipTmpFullPath)
	if err != nil {
		log.ErrorFields("os.RemoveAll error ", map[string]interface{}{"err": err})
		return "", err
	}
	return baseFullPath + "/" + zipName, nil
}
func (svc *LineStationRealityExportSvc) download(storageFullPath string) error {
	var wg sync.WaitGroup

	for _, item := range svc.Items {
		wg.Add(1)
		go func(storageReFullPath string, item []OetLineStationItem) {
			err := svc.downloadSingle(storageReFullPath, item)
			if err != nil {
				log.ErrorFields("downloadStationReality error ", map[string]interface{}{"err": err})
				return
			}
			wg.Done()
		}(storageFullPath, item)
	}
	wg.Wait()
	return nil
}

func (svc *LineStationRealityExportSvc) downloadSingle(storageFullPath string, items []OetLineStationItem) error {
	var storageReFullPath = fmt.Sprintf("%s/%s_%s_站点实景", storageFullPath, items[0].LineName, util.LineSheetMap[items[0].Sheet])

	if err := util.VerifyMkdirExistAndCreate(storageReFullPath); nil != err {
		return err
	}

	for _, item := range items {
		var newPath = fmt.Sprintf("%s_%s_%s", item.StationCode, item.StationName, util.StationDirectionMap[item.Direction])
		for _, ad := range item.RealityImages {
			req := httpClient.Request{
				Url:      ad.ImageUrl,
				Method:   "GET",
				Insecure: true,
			}
			resp, err := req.Do()
			if err != nil {
				log.ErrorFields("item.RealityImages error ", map[string]interface{}{"err": err})
				continue
			}
			defer resp.Body.Close()

			ext := filepath.Ext(path.Base(ad.ImageUrl))

			var newFileName = fmt.Sprintf("%s_%s%s", newPath, util.StationRealityImageOrientationMap[ad.Orientation], ext)

			f, err := os.Create(storageReFullPath + "/" + newFileName)
			if err != nil {
				log.ErrorFields("os.Create error ", map[string]interface{}{"err": err})
				continue
			}
			defer f.Close()

			io.Copy(f, resp.Body)
		}
	}
	return nil

}

func (svc *LineStationRealityExportSvc) exportExcel(fileName string, basePath string) error {

	var (
		style       = xlsxUtil.GetExcelPubStyle()
		stringStyle = xlsx.MakeStringStyle(&style.Font, &style.Fill, &style.Alignment, &style.Border)
	)

	var xlsxCellMetadataAndHeaders = []xlsxUtil.XlsxCellMetadataAndHeader{
		{HeaderName: "序号", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "线路名", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "线路类型", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "线路方向", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站序", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点名称", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点ID", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点类型", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "首班发车", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "末班发车", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "经度", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "纬度", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "行政区划", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "所属街道", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "所属道路", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站点方位", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站台类型", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "站牌类型", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "建成区", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "轨交换乘站", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
		{HeaderName: "入口", CellMetadata: xlsx.MakeCellMetadata(xlsx.CellTypeString, stringStyle)},
	}

	streamFile, err := xlsxUtil.GetStreamFile(fileName, basePath, xlsxCellMetadataAndHeaders)
	if err != nil {
		log.ErrorFields("GetStreamFile error [%v]", map[string]interface{}{"err": err})
		return err
	}

	var seq int64
	for key, line := range svc.Lines {
		items := svc.Items[key]
		lineItem, _ := rpc.GetLineWithId(context.Background(), line.LineId)
		if lineItem == nil {
			return errors.New("查询线路详情异常")
		}
		if err = svc.writeExcelBySingleLine(streamFile, &seq, line.Sheet, lineItem, items); err != nil {
			return err
		}
	}
	// 写文件
	if err = streamFile.Close(); err != nil {
		return errors.New(fmt.Sprintf("写入文件异常,错误信息[%v]", err))
	}

	return nil
}

func (svc *LineStationRealityExportSvc) writeExcelBySingleLine(streamFile *xlsx.StreamFile, seq *int64, sheet int64, line *protoLine.OetLineItem, items []OetLineStationItem) error {
	// 线路详情

	var stationNameStr = ""
	for _, item := range items {
		if item.Sequence > 0 {
			if stationNameStr == "" {
				stationNameStr = item.StationName
			} else {
				stationNameStr += fmt.Sprintf(",%s", item.StationName)
			}
		}
	}
	var (
		lineTypeStr = util.OetLineTypeMap[line.LineType]
		sheetStr    = util.LineSheetMap[sheet]
	)

	*seq++
	var cells []string
	cells = append(cells, fmt.Sprintf("%v", *seq)) // 序号
	cells = append(cells, line.Name)               // 线路名称
	cells = append(cells, lineTypeStr)             // 线路类型

	cells = append(cells, sheetStr) // 线路方向
	cells = append(cells, "")
	cells = append(cells, "首班时间")
	switch sheet {
	case util.LineSheetForUp, util.LineSheetForCircle:
		cells = append(cells, util.GetSecondStr(line.UpFirstTime))
	case util.LineSheetForDown:
		cells = append(cells, util.GetSecondStr(line.DownFirstTime))
	default:
		cells = append(cells, "")
	}

	cells = append(cells, "末班时间")
	switch sheet {
	case util.LineSheetForUp, util.LineSheetForCircle:
		cells = append(cells, util.GetSecondStr(line.UpLastTime))
	case util.LineSheetForDown:
		cells = append(cells, util.GetSecondStr(line.DownLastTime))
	default:
		cells = append(cells, "")
	}
	cells = append(cells, "票价")
	cells = append(cells, fmt.Sprintf("%v", float64(line.Price)/float64(100)))
	cells = append(cells, "")
	cells = append(cells, "途径站点")
	cells = append(cells, stationNameStr)
	cells = append(cells, "")
	cells = append(cells, "")
	cells = append(cells, "")
	cells = append(cells, "")
	cells = append(cells, "")
	cells = append(cells, "")
	cells = append(cells, "")
	err := streamFile.WriteWithColumnDefaultMetadata(cells)
	if err != nil {
		log.ErrorFields("WriteWithColumnDefaultMetadata error [%v]", map[string]interface{}{"err": err})
		return err
	}

	// 线路站点
	for _, item := range items {
		*seq++
		var iCells []string
		iCells = append(iCells, fmt.Sprintf("%v", *seq))                                           // 序号
		iCells = append(iCells, line.Name)                                                         // 线路名称
		iCells = append(iCells, lineTypeStr)                                                       // 线路类型
		iCells = append(iCells, sheetStr)                                                          // 线路方向
		iCells = append(iCells, fmt.Sprintf("%v", item.Sequence))                                  // 站序
		iCells = append(iCells, item.StationName)                                                  // 站点名称
		iCells = append(iCells, item.StationCode)                                                  // 站点ID
		iCells = append(iCells, util.OetLineStationTypeMap[item.FirstLast])                        // 站点类型
		iCells = append(iCells, util.GetSecondStr(item.FirstDepartTime))                           // 首班发车
		iCells = append(iCells, util.GetSecondStr(item.LastDepartTime))                            // 末班发车
		iCells = append(iCells, fmt.Sprintf("%v", item.Longitude))                                 // 经度
		iCells = append(iCells, fmt.Sprintf("%v", item.Latitude))                                  // 纬度
		iCells = append(iCells, operation.GetRegionName(svc.Regions, item.AdminIsTrAtIveDivision)) // 行政区划
		iCells = append(iCells, item.StreetName)                                                   // 所属街道
		iCells = append(iCells, item.OwnedRoad)                                                    // 所属道路
		iCells = append(iCells, util.StationDirectionMap[item.Direction])                          // 站点方位
		iCells = append(iCells, util.StationPlatformTypeMap[item.PlatformType])                    // 站台类型
		iCells = append(iCells, util.StationBusBoardTypeMap[item.BusBoardType])                    // 站牌类型
		switch item.IsBuiltUpArea {
		case true:
			iCells = append(iCells, "是") // 是否建成区
		case false:
			iCells = append(iCells, "否") // 是否建成区
		}
		iCells = append(iCells, item.RailInterchange)     // 轨交换乘站
		iCells = append(iCells, item.InterchangeEntrance) // 入口
		err = streamFile.WriteWithColumnDefaultMetadata(iCells)
		if err != nil {
			log.ErrorFields("WriteWithColumnDefaultMetadata error [%v]", map[string]interface{}{"err": err})
			return err
		}
	}
	return nil

}

type TimeSlotStationVoteParam struct {
	StationId int64 `json:"StationId"`
	LineId    int64 `json:"LineId"`
	Sheet     int64 `json:"Sheet"`
	StartAt   int64 `json:"StartAt"`
	EndAt     int64 `json:"EndAt"`
	Scale     int64 `json:"Scale"`
}

func (*DataManagement) TimeSlotStationVote(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param TimeSlotStationVoteParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}

	if param.StationId == 0 || param.EndAt < param.StartAt || param.Scale == 0 {
		return response.Error(rsp, response.ParamsInvalid)
	}
	stationItem := rpc.GetStationWithId(ctx, param.StationId)
	if stationItem == nil {
		return response.Error(rsp, response.DbQueryFail)
	}

	items, _ := rpc.GetStationTimeSlotVotes(ctx, &protoMiniStation.TimeSlotVoteRequest{
		StationId: param.StationId,
		StartAt:   param.StartAt,
		EndAt:     param.EndAt,
		Scale:     param.Scale,
		LineId:    param.LineId,
		Sheet:     param.Sheet,
	})

	return response.Success(rsp, map[string]interface{}{
		"Item": map[string]interface{}{
			"Name":      stationItem.Name,
			"Direction": stationItem.Direction,
		},
		"Items": items,
	})

}

func (this *DataManagement) TimeSlotStationVoteExport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	return this.TimeSlotStationVote(ctx, req, rsp)
}

type PredictLineStationParam struct {
	Radius int64         `json:"Radius"`
	Items  []PredictItem `json:"Items"`
}

type PredictItem struct {
	Longitude float64 `json:"Longitude"` //  经度
	Latitude  float64 `json:"Latitude"`  //  纬度
}

func (*DataManagement) PredictLineStation(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var param PredictLineStationParam
	err := json.Unmarshal([]byte(req.Body), &param)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	var topCorpId = auth.User(ctx).GetTopCorporationId()
	if topCorpId == 0 {
		return response.Error(rsp, "PA1106")
	}
	var protoStationOpt = protoStation.GetStationsWithOption2Request{
		CorporationId: topCorpId,
		Offset:        0,
		Limit:         999999999999,
	}

	items, _ := rpc.GetStationsWithOption2(ctx, &protoStationOpt)

	var retItems = getLineStationTrack(Predict(param.Radius, items, param.Items))

	return response.Success(rsp, map[string]interface{}{
		"Items": retItems,
	})
}

type TrackPoint struct {
	Index int
	Lng   float64 //经度(180°W-180°E) -180 - 0 - 180
	Lat   float64 //纬度（90°S-90°N）-90 -0 -90
}

type PredictStationItem struct {
	*protoStation.OetStationItem
	P        TrackPoint
	Distance float64
}

type PredictLineStation struct {
	StationId   int64   `json:"StationId"`
	StationName string  `json:"StationName"`
	StationCode string  `json:"StationCode"`
	Sequence    int64   `json:"Sequence"`
	Direction   int64   `json:"Direction"`
	Longitude   float64 `json:"Longitude"`
	Latitude    float64 `json:"Latitude"`
	Distance    float64 `json:"Distance"`
}

func getLineStationTrack(items []PredictStationItem) []PredictLineStation {
	var retItems = make([]PredictLineStation, 0)
	for index, item := range items {
		retItems = append(retItems, PredictLineStation{
			StationId:   item.Id,
			StationName: item.Name,
			StationCode: item.Code,
			Sequence:    int64(index + 1),
			Direction:   item.Direction,
			Longitude:   item.Longitude,
			Latitude:    item.Latitude,
			Distance:    item.Distance,
		})
	}
	return retItems
}

func Predict(radius int64, sites []*protoStation.OetStationItem, items []PredictItem) []PredictStationItem {
	var trajectoryPoints = make([]TrackPoint, 0)

	for index, item := range items {
		trajectoryPoints = append(trajectoryPoints, TrackPoint{
			Index: index + 1,
			Lng:   item.Longitude,
			Lat:   item.Latitude,
		})
	}

	newTps := LineAddPoint(trajectoryPoints)
	fmt.Println("轨迹点数量: ", len(newTps))

	sites = FilterRadius(radius, sites, newTps)
	fmt.Println("站点数量: ", len(sites))

	var retSites = make([]PredictStationItem, 0)

	for i := 1; i < len(newTps); i++ {
		var (
			minDistance    = -1.0
			nearestSegment = &protoStation.OetStationItem{}

			s       = newTps[i-1]
			e       = newTps[i]
			start   = orb.Point{s.Lng, s.Lat}
			end     = orb.Point{e.Lng, e.Lat}
			bearing = geo.Bearing(start, end) // 方向角

		)
		for _, site := range sites {
			//var (
			//	sitePoint = orb.Point{site.Longitude, site.Latitude}
			//) -- 坐标轴距离，不是gps距离
			//distance := planar.DistanceFromSegment(start, end, sitePoint)
			//
			//if minDistance == -1.0 || distance < minDistance {
			//	minDistance = distance
			//	nearestSegment = site
			//}
			distance := util.CalcDistance(site.Longitude, site.Latitude, s.Lng, s.Lat)
			if minDistance == -1.0 || distance < minDistance {
				minDistance = distance
				nearestSegment = site
			}

		}

		if minDistance > float64(radius) || nearestSegment.Id == 0 {
			continue
		}

		//if nearestSegment.Code == "1083" {
		//	var str = fmt.Sprintf("轨迹点 {经度：%v, 纬度：%v, 经度：%v, 纬度：%v} 最近的轨迹段是 %v，距离为 %v 米,方向角: %v",
		//		start.Lon(), start.Lat(), end.Lon(), end.Lat(), nearestSegment.Name, minDistance, bearing)
		//	fmt.Println(str)
		//}

		directions := getDirections(bearing)
		if !util.IncludeInt64(directions, nearestSegment.Direction) {
			continue
		}
		retSites = append(retSites, PredictStationItem{
			OetStationItem: nearestSegment,
			P:              s,
			Distance:       minDistance,
		})
	}
	// 去重
	res := make([]PredictStationItem, 0)
	for _, s := range retSites {
		isRepeat := false // 是否重复
		for _, s2 := range res {
			if s.Id == s2.Id {
				isRepeat = true
				break
			}
		}
		if isRepeat {
			continue
		} else {
			res = append(res, s)
		}
	}

	sort.SliceStable(res, func(i, j int) bool {
		if res[i].P.Index == res[j].P.Index {
			return res[i].Distance < res[j].Distance
		}
		return res[i].P.Index < res[j].P.Index
	})
	return res

}

func FilterRadius(radius int64, sites []*protoStation.OetStationItem, newTps []TrackPoint) []*protoStation.OetStationItem {
	var (
		ch        = make(chan []*protoStation.OetStationItem, 4)
		retItemss = make([]*protoStation.OetStationItem, 0)
	)
	for _, newTp := range newTps {
		go getOetStationItemWithRadius(radius, sites, newTp, ch)
	}

	for i := 0; i < len(newTps); i++ {
		var retItems = <-ch
		if retItems != nil {
			retItemss = append(retItemss, retItems...)
		}
	}
	// 去重
	res := make([]*protoStation.OetStationItem, 0)
	for _, s := range retItemss {
		isRepeat := false // 是否重复
		for _, s2 := range res {
			if s.Id == s2.Id {
				isRepeat = true
				break
			}
		}
		if isRepeat {
			continue
		} else {
			res = append(res, s)
		}
	}
	return res
}

func getOetStationItemWithRadius(radius int64, sites []*protoStation.OetStationItem, tp TrackPoint, ch chan []*protoStation.OetStationItem) {
	var retItems = make([]*protoStation.OetStationItem, 0)
	for _, site := range sites {
		if util.CalcDistance(site.Longitude, site.Latitude, tp.Lng, tp.Lat) <= float64(radius) {
			retItems = append(retItems, site)
		}
	}
	ch <- retItems
}

func getDirections(bearing float64) []int64 {
	if bearing < 0 {
		bearing = 360 + bearing
	}
	/**
	  东向西: 180-360
	  西向东: 0-180
	  南向北: 270--360 0-90
	  北向南: 90-270
	  direction 站点方向，1:东向西,2:西向东,3:南向北,4:北向南
	*/
	var directions []int64
	// 东向西: 180-360
	if bearing >= 180 && bearing < 360 {
		directions = append(directions, 1)
	}
	// 西向东: 0-180
	if bearing >= 0 && bearing <= 180 {
		directions = append(directions, 2)
	}

	// 3:南向北
	if (bearing >= 0 && bearing <= 90) || (bearing >= 270 && bearing <= 360) {
		directions = append(directions, 3)
	}

	// 4:北向南
	if bearing >= 90 && bearing <= 270 {
		directions = append(directions, 4)
	}
	return directions
}

// LineAddPoint 对线路的采样点进行差值补点
func LineAddPoint(stations []TrackPoint) []TrackPoint {
	if len(stations) == 0 {
		return nil
	}
	var points []TrackPoint
	log.Error("stations %+v", stations[len(stations)-1])
	for i := 0; i < len(stations); i++ {

		if i == len(stations)-1 {
			points = append(points, stations[i])
		} else {

			startPoint := stations[i]
			endPoint := stations[i+1]

			fillPoints := FillPoints(startPoint, endPoint)

			points = append(points, startPoint)
			if len(fillPoints) > 0 {
				points = append(points, fillPoints...)
			}
		}
	}

	for i := 0; i < len(points); i++ {
		points[i].Index = i + 1
	}

	return points
}

const spacing = 10

// FillPoints 补点
func FillPoints(startPoint, endPoint TrackPoint) []TrackPoint {
	//计算两点之间的距离
	distance := util.CalcDistance(startPoint.Lng, startPoint.Lat, endPoint.Lng, endPoint.Lat)

	//补点的总数
	count := math.Ceil(distance / float64(spacing))

	//起点与终端经度差
	lngDiff := (endPoint.Lng * math.Pow(10, 6)) - (startPoint.Lng * math.Pow(10, 6))

	//起点与终端纬度差
	latDiff := (endPoint.Lat * math.Pow(10, 6)) - (startPoint.Lat * math.Pow(10, 6))

	//每步的经度差
	spacingLng := lngDiff / count

	//每步的纬度差
	spacingLat := latDiff / count

	var points []TrackPoint

	for i := 1; i < int(count); i++ {
		pointLng := ((startPoint.Lng * math.Pow(10, 6)) + (spacingLng * float64(i))) / math.Pow(10, 6)
		pointLat := ((startPoint.Lat * math.Pow(10, 6)) + (spacingLat * float64(i))) / math.Pow(10, 6)
		points = append(points, TrackPoint{Lng: pointLng, Lat: pointLat})
	}
	return points
}
