package identification

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/operation"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"archive/zip"
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"io"
	"path/filepath"
	"strings"
	"time"
)

type DriverSearchForm struct {
	CorporationId int64  `json:"CorporationId"`
	DriverName    string `json:"DriverName" qs:"LIKE"`
	CardType      int64  `json:"CardType" qs:"="`
	CardStatus    int64  `json:"CardStatus" qs:"="`
	CardNumber    string `json:"CardNumber" qs:"LIKE"`
	DriverStatus  int64  `json:"DriverStatus" qs:"="`
	model.Paginator
}

func (*Identification) DriverIdentificationList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form DriverSearchForm
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	qs := model.NewQs(form)
	list, totalCount, err := (&operation.DriverIdentification{}).List(qs, form.CorporationId, form.Paginator)
	if err != nil {
		log.ErrorFields("DriverIdentification List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if list != nil {
		for index := range list {
			list[index].CorporationId, list[index].CorporationName = list[index].Corporations.GetCorporation()
			list[index].SetCardStatus()
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (*Identification) DriverIdentificationAdd(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form operation.DriverIdentification
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	form.Build(form.CorporationId)
	err := form.Create()
	if err != nil {
		log.ErrorFields("DriverIdentification Create error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

func (*Identification) DriverIdentificationEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form operation.DriverIdentification
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	form.Build(form.CorporationId)
	err := form.Updates()
	if err != nil {
		log.ErrorFields("DriverIdentification Updates error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

func (*Identification) DriverIdentificationExcelImport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		FileData string `json:"FileData" validate:"required"`
	}
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	decodeString, err := base64.StdEncoding.DecodeString(form.FileData)
	if err != nil {
		log.Error("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.Error("excelize.OpenBinary[err]:", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	if len(excelFile.Sheets) == 0 {
		log.Error("excelFile.Sheets为空")
		return response.Error(rsp, response.FAIL)
	}
	user := auth.User(ctx).GetUser()
	rows := excelFile.Sheets[0].Rows
	var errRows []model.ExcelForm
	if rows != nil {
		for index, row := range rows {
			if index == 0 {
				continue
			}
			if len(row.Cells) < 6 {
				errRows = append(errRows, model.ExcelForm{
					RowIndex: index + 1,
					Error:    "数据格式错误",
				})
				continue
			}
			cardNumber := row.Cells[5].String()
			if cardNumber == "" {
				errRows = append(errRows, model.ExcelForm{
					RowIndex: index + 1,
					Error:    "证件编号为空",
				})
				continue
			}
			qs := model.NewQs()
			qs.Add("CardNumber = ?", cardNumber)
			data, _ := (&operation.DriverIdentification{}).QueryOne(qs, 0)
			if data.Id == 0 {
				data.ParseOpUser(ctx)
				data.CardNumber = cardNumber
				data.Build(user.CorporationId)
			}
			data.DriverName = row.Cells[0].String()
			data.Phone = row.Cells[1].String()
			t, _ := ParseExcelTime(row.Cells[2], time.Local)
			data.EffectiveDate = model.LocalTime(t)
			t, _ = ParseExcelTime(row.Cells[3], time.Local)
			data.EffectiveDate = model.LocalTime(t)
			cardType := row.Cells[4].String()
			switch cardType {
			case "驾驶证":
				data.CardType = 1
			case "从业资格证":
				data.CardType = 2
			}
			staffItem := rpc.GetStaffWithPhone(ctx, user.TopCorporationId, data.Phone)
			if staffItem == nil {
				errRows = append(errRows, model.ExcelForm{
					RowIndex: index + 1,
					Error:    fmt.Sprintf("此手机号:%s,未找到员工信息", data.Phone),
				})
				continue
			}
			data.DriverId = staffItem.Id
			data.DriverStatus = staffItem.WorkingState
			if data.Id == 0 {
				err = data.Create()
			} else {
				err = data.Updates()
			}
			if err != nil {
				errRows = append(errRows, model.ExcelForm{
					RowIndex: index + 1,
					Error:    "保存数据失败",
				})
				continue
			}
		}
	}
	return response.Success(rsp, errRows)
}

func (*Identification) DriverIdentificationFileImport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		FileData string `json:"FileData" validate:"required"` // zip文件base64
	}
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	zipData, err := base64.StdEncoding.DecodeString(form.FileData)
	if err != nil {
		return response.Error(rsp, "请上传正确的文件")
	}
	// 判断是否是zip文件
	if !bytes.HasPrefix(zipData, []byte("PK\x03\x04")) {
		return response.Error(rsp, "不是有效的 zip 文件")
	}
	MaxZipSize := 50 * 1024 * 1024
	if len(zipData) > MaxZipSize {
		return response.Error(rsp, "zip 文件大小不能超过 50MB")
	}
	reader, err := zip.NewReader(bytes.NewReader(zipData), int64(len(zipData)))
	if err != nil {
		return response.Error(rsp, "解析 zip 失败:"+err.Error())
	}
	if reader.File == nil || len(reader.File) == 0 {
		return response.Error(rsp, "zip包中没有任何文件")
	}
	var errorForm []model.ExcelForm
	for _, f := range reader.File {
		if f.FileInfo().IsDir() {
			continue // 📁 跳过文件夹
		}
		filename := filepath.Base(f.Name)
		fmt.Println("==============================fileName:", filename)
		fileArr := strings.Split(filename, ".")
		if len(fileArr) != 2 {
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 文件名格式错误", filename),
			})
			continue
		}
		fName := fileArr[0]
		suffix := fileArr[1]
		//if !strings.HasSuffix(strings.ToLower(filename), ".pdf") {
		//	errorForm = append(errorForm, model.ExcelForm{
		//		Error: fmt.Sprintf("%s 不是pdf文件", filename),
		//	})
		//	continue
		//}
		rc, err := f.Open()
		if err != nil {
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 打开文件失败:%v", filename, err),
			})
			continue
		}
		fileBytes, _ := io.ReadAll(rc)
		base64Str := base64.StdEncoding.EncodeToString(fileBytes)
		newFileName := fmt.Sprintf("%s%s", time.Now().Format("20060102150405"), filename)
		newFile, _, err := util.CreateFile(newFileName, base64Str, config.Config.AbsDirPath, config.Config.WebRoot, "erp", "learn_pdf", time.Now().Format("20060102"))
		relativeFilePath := fmt.Sprintf(`%s/erp/%v/%s/%s`, config.Config.WebRoot, "identification", time.Now().Format("20060102"), newFileName)
		if err != nil {
			_ = rc.Close()
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 创建文件失败:%s", filename, err.Error()),
			})
			continue
		}
		qs := model.NewQs()
		qs.Add("CardNumber = ?", fName)
		data, _ := (&operation.DriverIdentification{}).QueryOne(qs, 0)
		if data.Id == 0 {
			_ = newFile.Close()
			_ = rc.Close()
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("未找到该证件编号：%s", filename),
			})
			continue
		}
		var jsonObj model.JSON
		m := make(map[string]interface{})
		m["Id"] = model.Id()
		m["Name"] = filename
		m["Path"] = relativeFilePath
		m["Suffix"] = suffix
		m["Type"] = 1
		m["Url"] = fmt.Sprintf("%s%s", config.Config.StaticFileHttpPrefix, relativeFilePath)
		b, err := json.Marshal(&m)
		if err != nil {
			_ = newFile.Close()
			_ = rc.Close()
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 数据插入失败", filename),
			})
			continue
		}
		_ = jsonObj.UnmarshalJSON(b)
		data.Attachment = jsonObj
		_ = newFile.Close()
		_ = rc.Close()
		err = data.Updates()
		if err != nil {
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 数据插入失败", filename),
			})
			continue
		}
	}
	return response.Success(rsp, errorForm)
}
