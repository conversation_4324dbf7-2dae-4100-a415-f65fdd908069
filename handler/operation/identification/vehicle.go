package identification

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/operation"
	protoVehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/response"
	"archive/zip"
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/tealeg/xlsx"
	"io"
	"path/filepath"
	"strings"
	"time"
)

type Identification struct{}

type VehicleSearchForm struct {
	CorporationId int64  `json:"CorporationId"`
	License       string `json:"License" qs:"LIKE"`
	CardType      int64  `json:"CardType" qs:"="`
	CardStatus    int64  `json:"CardStatus" qs:"="`
	CardNumber    string `json:"CardNumber" qs:"LIKE"`
	VehicleStatus int64  `json:"VehicleStatus" qs:"="`
	model.Paginator
}

func (*Identification) VehicleIdentificationList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form VehicleSearchForm
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	qs := model.NewQs(form)
	list, totalCount, err := (&operation.VehicleIdentification{}).List(qs, form.CorporationId, form.Paginator)
	if err != nil {
		log.ErrorFields("VehicleIdentification List error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbQueryFail)
	}
	if list != nil {
		for index := range list {
			list[index].CorporationId, list[index].CorporationName = list[index].Corporations.GetCorporation()
			list[index].SetCardStatus()
		}
	}
	return response.Success(rsp, map[string]interface{}{"Items": list, "TotalCount": totalCount})
}

func (*Identification) VehicleIdentificationAdd(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form operation.VehicleIdentification
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	form.Build(form.CorporationId)
	err := form.Create()
	if err != nil {
		log.ErrorFields("VehicleIdentification Create error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

func (*Identification) VehicleIdentificationEdit(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form operation.VehicleIdentification
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	if form.Id == 0 {
		return response.Error(rsp, response.ParamsMissing)
	}
	form.Build(form.CorporationId)
	err := form.Updates()
	if err != nil {
		log.ErrorFields("VehicleIdentification Updates error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}

func (*Identification) VehicleIdentificationExcelImport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		FileData string `json:"FileData" validate:"required"`
	}
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	decodeString, err := base64.StdEncoding.DecodeString(form.FileData)
	if err != nil {
		log.Error("base64.StdEncoding.DecodeString error", map[string]interface{}{"err": err})
		return response.Error(rsp, response.ParamsInvalid)
	}
	excelFile, err := xlsx.OpenBinary(decodeString)
	if err != nil {
		log.Error("excelize.OpenBinary[err]:", map[string]interface{}{"err": err})
		return response.Error(rsp, response.FAIL)
	}
	if len(excelFile.Sheets) == 0 {
		log.Error("excelFile.Sheets为空")
		return response.Error(rsp, response.FAIL)
	}
	user := auth.User(ctx).GetUser()
	rows := excelFile.Sheets[0].Rows
	var errRows []model.ExcelForm
	if rows != nil {
		for index, row := range rows {
			if index == 0 {
				continue
			}
			if len(row.Cells) < 5 {
				errRows = append(errRows, model.ExcelForm{
					RowIndex: index + 1,
					Error:    "数据格式错误",
				})
				continue
			}
			cardNumber := row.Cells[4].String()
			if cardNumber == "" {
				errRows = append(errRows, model.ExcelForm{
					RowIndex: index + 1,
					Error:    "证件编号为空",
				})
				continue
			}
			qs := model.NewQs()
			qs.Add("CardNumber = ?", cardNumber)
			data, _ := (&operation.VehicleIdentification{}).QueryOne(qs, 0)
			if data.Id == 0 {
				data.ParseOpUser(ctx)
				data.CardNumber = cardNumber
				data.Build(user.CorporationId)
			}
			data.License = row.Cells[0].String()
			t, _ := ParseExcelTime(row.Cells[1], time.Local)
			data.EffectiveDate = model.LocalTime(t)
			t, _ = ParseExcelTime(row.Cells[2], time.Local)
			data.EffectiveDate = model.LocalTime(t)
			cardType := row.Cells[3].String()
			switch cardType {
			case "行驶证":
				data.CardType = 1
			case "营运证":
				data.CardType = 2
			case "车辆年检":
				data.CardType = 3
			}
			vehicles, _ := rpc.GetVehiclesWithLicenseOption(ctx, &protoVehicle.GetVehiclesWithLicenseOptionRequest{
				TopCorporationId: user.TopCorporationId,
				License:          data.License,
			})
			if vehicles == nil || len(vehicles) == 0 {
				errRows = append(errRows, model.ExcelForm{
					RowIndex: index + 1,
					Error:    fmt.Sprintf("此车牌:%s,未找到车辆信息", data.License),
				})
				continue
			}
			data.VehicleId = vehicles[0].Id
			data.VehicleStatus = vehicles[0].UseStatus
			if data.Id == 0 {
				err = data.Create()
			} else {
				err = data.Updates()
			}
			if err != nil {
				errRows = append(errRows, model.ExcelForm{
					RowIndex: index + 1,
					Error:    "保存数据失败",
				})
				continue
			}
		}
	}
	return response.Success(rsp, errRows)
}

func ParseExcelTime(cell *xlsx.Cell, location *time.Location) (time.Time, error) {
	if cell.Type() == xlsx.CellTypeNumeric {
		// Excel 日期数字（浮点数，表示从 1900-01-01 开始的天数）
		floatVal, err := cell.Float()
		if err != nil {
			return time.Time{}, err
		}
		// Excel 基准时间：1900-01-01，注意要减去2天（Excel认为1900是闰年且第一天从1开始）
		baseTime := time.Date(1899, 12, 30, 0, 0, 0, 0, location)
		return baseTime.Add(time.Duration(floatVal * float64(24*time.Hour))), nil
	}

	// 否则尝试按字符串解析
	strVal := strings.TrimSpace(cell.String())

	// 你可以根据需要支持多个时间格式
	formats := []string{
		"2006-01-02 15:04:05",
		"2006-01-02",
		"01/02/2006",        // 英文格式
		"2006/01/02",        // 常见格式
		"02-Jan-2006 15:04", // Excel 的其他格式
	}

	for _, layout := range formats {
		t, err := time.ParseInLocation(layout, strVal, location)
		if err == nil {
			return t, nil
		}
	}

	return time.Time{}, &time.ParseError{Layout: "multiple", Value: strVal, LayoutElem: "", ValueElem: ""}
}

func (*Identification) VehicleIdentificationFileImport(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		FileData string `json:"FileData" validate:"required"` // zip文件base64
	}
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	zipData, err := base64.StdEncoding.DecodeString(form.FileData)
	if err != nil {
		return response.Error(rsp, "请上传正确的文件")
	}
	// 判断是否是zip文件
	if !bytes.HasPrefix(zipData, []byte("PK\x03\x04")) {
		return response.Error(rsp, "不是有效的 zip 文件")
	}
	MaxZipSize := 50 * 1024 * 1024
	if len(zipData) > MaxZipSize {
		return response.Error(rsp, "zip 文件大小不能超过 50MB")
	}
	reader, err := zip.NewReader(bytes.NewReader(zipData), int64(len(zipData)))
	if err != nil {
		return response.Error(rsp, "解析 zip 失败:"+err.Error())
	}
	if reader.File == nil || len(reader.File) == 0 {
		return response.Error(rsp, "zip包中没有任何文件")
	}
	var errorForm []model.ExcelForm
	for _, f := range reader.File {
		if f.FileInfo().IsDir() {
			continue // 📁 跳过文件夹
		}
		filename := filepath.Base(f.Name)
		fmt.Println("==============================fileName:", filename)
		fileArr := strings.Split(filename, ".")
		if len(fileArr) != 2 {
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 文件名格式错误", filename),
			})
			continue
		}
		fName := fileArr[0]
		suffix := fileArr[1]
		//if !strings.HasSuffix(strings.ToLower(filename), ".pdf") {
		//	errorForm = append(errorForm, model.ExcelForm{
		//		Error: fmt.Sprintf("%s 不是pdf文件", filename),
		//	})
		//	continue
		//}
		rc, err := f.Open()
		if err != nil {
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 打开文件失败:%v", filename, err),
			})
			continue
		}
		fileBytes, _ := io.ReadAll(rc)
		base64Str := base64.StdEncoding.EncodeToString(fileBytes)
		newFileName := fmt.Sprintf("%s%s", time.Now().Format("20060102150405"), filename)
		newFile, _, err := util.CreateFile(newFileName, base64Str, config.Config.AbsDirPath, config.Config.WebRoot, "erp", "learn_pdf", time.Now().Format("20060102"))
		relativeFilePath := fmt.Sprintf(`%s/erp/%v/%s/%s`, config.Config.WebRoot, "identification", time.Now().Format("20060102"), newFileName)
		if err != nil {
			_ = rc.Close()
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 创建文件失败:%s", filename, err.Error()),
			})
			continue
		}
		qs := model.NewQs()
		qs.Add("CardNumber = ?", fName)
		data, _ := (&operation.VehicleIdentification{}).QueryOne(qs, 0)
		if data.Id == 0 {
			_ = newFile.Close()
			_ = rc.Close()
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("未找到该证件编号：%s", filename),
			})
			continue
		}
		var jsonObj model.JSON
		m := make(map[string]interface{})
		m["Id"] = model.Id()
		m["Name"] = filename
		m["Path"] = relativeFilePath
		m["Suffix"] = suffix
		m["Type"] = 1
		m["Url"] = fmt.Sprintf("%s%s", config.Config.StaticFileHttpPrefix, relativeFilePath)
		b, err := json.Marshal(&m)
		if err != nil {
			_ = newFile.Close()
			_ = rc.Close()
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 数据插入失败", filename),
			})
			continue
		}
		_ = jsonObj.UnmarshalJSON(b)
		data.Attachment = jsonObj
		_ = newFile.Close()
		_ = rc.Close()
		err = data.Updates()
		if err != nil {
			errorForm = append(errorForm, model.ExcelForm{
				Error: fmt.Sprintf("%s 数据插入失败", filename),
			})
			continue
		}
	}
	return response.Success(rsp, errorForm)
}
