package identification

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model/operation"
	"app/org/scs/erpv2/api/util/response"
	"context"
	api "github.com/micro/go-micro/v2/api/proto"
)

func (*Identification) RemindStaffConfigList(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		ConfigType int64 `json:"ConfigType" validate:"required"` // 配置类型 1车辆证件有效期 2司机证件有效期
	}
	errCode := response.BindForm(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	list, err := (&operation.RemindStaffConfig{}).Configs(form.ConfigType)
	if err != nil {
		log.ErrorFields("RemindStaffConfig List error", map[string]interface{}{"error": err.<PERSON>rror()})
		return response.Error(rsp, response.DbQueryFail)
	}
	return response.Success(rsp, list)
}

func (*Identification) RemindStaffConfigBatchSave(ctx context.Context, req *api.Request, rsp *api.Response) error {
	var form struct {
		Data []operation.RemindStaffConfig `json:"Data"`
	}
	errCode := response.BindFormWithoutValidator(req, &form)
	if errCode != "" {
		return response.Error(rsp, errCode)
	}
	err := (&operation.RemindStaffConfig{}).BatchSave(form.Data)
	if err != nil {
		log.ErrorFields("RemindStaffConfig BatchSave error", map[string]interface{}{"error": err.Error()})
		return response.Error(rsp, response.DbSaveFail)
	}
	return response.Success(rsp, nil)
}
