package middleware

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	log "github.com/micro/go-micro/v2/logger"
	"github.com/micro/go-micro/v2/server"
	"net/url"
	"strings"
	"time"
)

type System struct {
	AppId string
	Nonce int64
	Sign  string
	Time  int64
	Ver   string
}

type RequestParams struct {
	AuthId string
}

type requestBody struct {
	System System                 `json:"System"`
	Params map[string]interface{} `json:"Params"`
}

func Auth(fn server.HandlerFunc) server.HandlerFunc {
	return func(ctx context.Context, req server.Request, rsp interface{}) error {
		if strings.Contains(req.Endpoint(), "Thirdparty") { // 第三方外部调用
			log.Infof("Received 【%s】 API request with: %s", req.Endpoint(), req.Body())
			return fn(ctx, req, rsp)
		}

		if strings.Contains(req.Endpoint(), "Health") { // 健康检查
			return fn(ctx, req, rsp)
		}

		newReq, ok := req.Body().(*api.Request)
		if !ok || strings.Contains(req.Method(), "Dss.") { // rpc || dss
			log.Infof("Received 【%s】 API request with: %s", req.Endpoint(), req.Body())
			return fn(ctx, req, rsp)
		}

		reqBody := newReq.Body

		if "GET" == newReq.Method {
			u, _ := url.Parse(newReq.Url)
			log.Infof("Received 【%s】 API request with: %s", req.Endpoint(), u.RawQuery)
		} else {
			if req.Endpoint() == "File.Upload" {
				log.Infof("Received 【%s】 API request", req.Endpoint())
			} else {
				log.Infof("Received 【%s】 API request with: %s", req.Endpoint(), reqBody)
			}
		}

		var requestParam requestBody
		dec := json.NewDecoder(bytes.NewReader([]byte(newReq.Body)))
		dec.UseNumber()
		err := dec.Decode(&requestParam)
		if err != nil {
			log.Infof("Received 【%s】 API request with: %s, err:%+v", req.Endpoint(), req.Body(), err)
			return fn(ctx, req, rsp)
		}

		var token string
		if _, ok := requestParam.Params["AuthId"]; ok {
			token = fmt.Sprintf("%v", requestParam.Params["AuthId"])
		}
		newRsp, ok := rsp.(*api.Response)
		if !ok {
			return fn(ctx, req, rsp)
		}

		if token == "" && req.Endpoint() == "Operation.AddTravelNeedDetail" {
			//H5不需要认证
		} else if !auth.Check(ctx, token) {
			return response.Error(newRsp, response.Forbidden)
		}

		//重写请求体参数值
		newRequestParams, _ := json.Marshal(requestParam.Params)
		if req.Endpoint() != "File.Upload" {
			fmt.Printf("auth newRequestParams======= %+v \n", string(newRequestParams))
		}
		newReq.Body = string(newRequestParams)

		return fn(ctx, req, rsp)
	}
}

func CheckSystemParam(sys System) string {
	appId, appSecret := config.GetApp()
	if appId != sys.AppId {
		return response.UnknownApp
	}

	nowPts := time.Now().Unix()
	diffSecond := nowPts - sys.Time
	if diffSecond > 60*60 || diffSecond < -60*60 {
		return "SN1002"
	}

	//Sign=md5(Time:1404443389,Nonce:342099721252242433,AppSecret:yuc4dbb354sdsdfj77d76lkd86")
	rawSign := fmt.Sprintf("Time:%v,Nonce:%v,AppSecret:%v", sys.Time, sys.Nonce, appSecret)
	md5Sign := fmt.Sprintf("%x", md5.Sum([]byte(rawSign)))
	if sys.Sign != md5Sign {
		log.Infof(" ### sys.Sign: %v, md5Sign: %v, rawSign: %v", sys.Sign, md5Sign, rawSign)
		return "SN1001"
	}

	return "0"
}
