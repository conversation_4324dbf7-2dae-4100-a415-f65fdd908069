package middleware

import (
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util/response"
	"context"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/micro/go-micro/v2/server"
	"strings"
)

//var ForbiddenAccessEndPoint = []string{
//	"Freecheck.LbpmProcessParam",
//	"Freecheck.LbpmApproveProcess",
//	"Freecheck.LbpmApprovingProcess",
//	"Freecheck.LbpmProcessFormValue",
//	"Positionaltitleapply.Create",
//	"Staffarchive.MineInfo",
//	"Staffarchive.EditMineInfo",
//	"Staffquit.DispatchProcess",
//	"Trafficaccident.Create",
//	"Trafficaccident.Edit",
//	"Trafficaccident.ApplyClose",
//	"Trafficaccident.ApplyLendMoney",
//	"Trafficaccident.ApplyBranchClose",
//	"Countmoney.Add",
//	"Countmoney.Edit",
//	"Countticket.Add",
//	"Countticket.Edit",
//	"Workorder.Add",
//	"Workorder.RepairChoose",
//}

//func CheckIsStaff(fn server.HandlerFunc) server.HandlerFunc {
//return func(ctx context.Context, req server.Request, rsp interface{}) error {
//	if utils.Contains(ForbiddenAccessEndPoint, req.Endpoint()) && !auth.User(ctx).HasStaff() {
//		newRsp, ok := rsp.(*api.Response)
//		if !ok {
//			return errors.New("BAD REQUEST")
//		}
//		return response.Error(newRsp, response.Forbidden)
//	}
//	return fn(ctx, req, rsp)
//}
//}

func CheckHasAccount(fn server.HandlerFunc) server.HandlerFunc {
	return func(ctx context.Context, req server.Request, rsp interface{}) error {
		fmt.Println("-----------------------")
		fmt.Println(req.Endpoint())
		fmt.Println("-----------------------")

		if strings.Contains(req.Endpoint(), "Thirdparty") { // 第三方外部调用
			fmt.Printf("\n==============Thirdparty API================ \n")
			return fn(ctx, req, rsp)
		}

		if strings.Contains(req.Endpoint(), "Health") || req.Endpoint() == "Operation.AddTravelNeedDetail" { // 健康检查
			return fn(ctx, req, rsp)
		}

		if strings.Contains(req.Method(), "Dss.") { // rpc || dss
			fmt.Printf("\n==============Dss API================ \n")
			return fn(ctx, req, rsp)
		}
		if !auth.User(ctx).HasUser() && !auth.User(ctx).IsAdmin() {
			fmt.Printf("\n==============NO AUTH================ \n")
			newRsp, ok := rsp.(*api.Response)
			if !ok {
				return fn(ctx, req, rsp)
			}
			return response.Error(newRsp, response.Forbidden)
		}
		return fn(ctx, req, rsp)
	}
}
