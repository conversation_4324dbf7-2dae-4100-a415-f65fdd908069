package middleware

import (
	"bytes"
	"context"
	"fmt"
	"github.com/micro/go-micro/v2/server"
	"runtime"
)

// ExceptionHandler 异常处理
func ExceptionHandler() server.HandlerWrapper {
	return func(h server.HandlerFunc) server.HandlerFunc {
		return func(ctx context.Context, req server.Request, rsp interface{}) error {
			defer func() {
				// 如果是崩溃导致的信号，发送报警
				err := recover()
				if err != nil {
					// 捕获后处理
					errorMsg := PanicTrace(1)
					fmt.Printf("程序崩溃了-_-: %s \r\n", errorMsg)
				}
			}()
			return h(ctx, req, rsp)
		}
	}
}

// PanicTrace 获取当前堆栈信息
func PanicTrace(kb int) string {
	s := []byte("/src/runtime/panic.go")
	e := []byte("\ngoroutine ")
	line := []byte("\n")
	stack := make([]byte, kb<<10) //4KB
	length := runtime.Stack(stack, true)
	start := bytes.Index(stack, s)
	stack = stack[start:length]
	start = bytes.Index(stack, line) + 1
	stack = stack[start:]
	end := bytes.LastIndex(stack, line)
	if end != -1 {
		stack = stack[:end]
	}
	end = bytes.Index(stack, e)
	if end != -1 {
		stack = stack[:end]
	}
	stack = bytes.TrimRight(stack, "\n")
	return string(stack)
}
