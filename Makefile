GOPATH:=$(shell go env GOPATH)

.PHONY: proto
proto:
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/corporation/corporation.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/oetstaff/oetstaff.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/oetvehicle/oetvehicle.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/oetvehiclemodel/oetvehiclemodel.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/user/user.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/permission/permission.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/oetline/oetline.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/tree/tree.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/iss/schedule.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/iss/dataform.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/iss/assess_line_stations.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/iss/notify.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/iss/line_task.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/mini/mini.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/parking/oetparking.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/station/oetstation.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/erp/erp.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/oetlinestation/oetlinestation.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/mini_station/station.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/doorcheck/doorcheck.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/rpc/oetvehicle/oetvehicledriver.proto

	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/base/base.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/freeCheck/freeCheck.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/dss/dss.proto

	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/file/file.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/hr/hr.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/safety/safety.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/maintenance/maintenance.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/health/health.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/ticket/ticket.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/thirdParty/thirdParty.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/workOrder/workOrder.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/scheduler/scheduler.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/stock/stock.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/setting/setting.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/operation/operation.proto
	protoc --plugin=protoc-gen-micro=${GOPATH}/bin/protoc-gen-micro --proto_path=${GOPATH}/src --proto_path=. --micro_out=. --go_out=. proto/dashboard/dashboard.proto


.PHONY: build
build: proto
	GOOS=linux CGO_ENABLED=0 go build -a -ldflags '-extldflags "-static"' -o erp-api main.go

.PHONY: build-direct
build-direct:
	GOOS=linux CGO_ENABLED=0 go build -a -ldflags '-extldflags "-static"' -o erp-api main.go

win:
	go build -a -ldflags '-extldflags "-static"' -o api.exe main.go

.PHONY: test
test:
	go test -v ./... -cover

.PHONY: run
run:
	go run main.go --config=./config_local.json

.PHONY: clean
clean:
	rm erp-api

.PHONY: docker
docker:
	docker build . -t erp-api:latest
