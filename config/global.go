package config

import (
	"github.com/micro/go-micro/v2/client"
	"gorm.io/gorm"
	"net/http"
)

var Global GlobalVariable

type GlobalVariable struct {
	DbClient                            *gorm.DB
	MicroClient                         client.Client
	HttpClient                          *http.Client
	H5Permissions                       []string `json:"H5Permissions"` //H5页面查看对应权限
	IsCalculatingStaffArchiveReportData bool     //系统是否正在计算人资报表数据
}

const LbpmAESKey = "&$%&@#$@#^&*^<>%"
