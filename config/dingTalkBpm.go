package config

const (
	// 员工离职申请流程模板
	StaffQuitApplyFormTemplate = "staff_quit_apply_process"
	// 物料申请流程模板
	MaterialRequisitionApplyFormTemplate = "material_requisition_apply_process"
)

// 岱山
var DingTalkBpmProcessCodes = map[string]string{
	//StaffQuitApplyFormTemplate: "PROC-1684C18D-70C2-4E3C-B391-ED67F319063F", //测试
	StaffQuitApplyFormTemplate:           "PROC-07DDB382-17D6-4504-B98E-E4BC8C6CC5E3", //生产
	MaterialRequisitionApplyFormTemplate: "PROC-07839CA0-4CE7-4C89-AF5E-659DA888F3E6", // 测试
}

func ResetDingTalkBpmProcessCode() {
	for k, v := range Config.DingTalkBpm.DingTalkBpmProcessCode {
		DingTalkBpmProcessCodes[k] = v
	}
}
