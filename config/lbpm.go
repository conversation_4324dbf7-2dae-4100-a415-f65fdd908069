package config

const SafetyModelId = "safety_model"
const HrModelId = "hr_model"
const WorkOrderModelId = "work_order_model"
const MaintenanceModelId = "maintenance_model"
const OperationModelId = "operation_model"

const PublicFormTemplate = "public"

// TrafficAccidentReportFormTemplate 事故上报流程
const TrafficAccidentReportFormTemplate = "traffic_accident_report_process"

// TrafficAccidentEditFormTemplate 事故变更流程
const TrafficAccidentEditFormTemplate = "traffic_accident_edit_process"

// TrafficAccidentLendMoneyFormTemplate 事故借款流程
const TrafficAccidentLendMoneyFormTemplate = "traffic_accident_lend_money_process"

// TrafficAccidentDrawbackMoneyFormTemplate 事故退款流程
const TrafficAccidentDrawbackMoneyFormTemplate = "traffic_accident_drawback_money_process"

// TrafficAccidentPaymentMoneyFormTemplate 事故付款流程
const TrafficAccidentPaymentMoneyFormTemplate = "traffic_accident_payment_money_process"

// TrafficAccidentBranchCloseFormTemplate 事故分支结案流程
const TrafficAccidentBranchCloseFormTemplate = "traffic_accident_branch_close_process"

// TrafficAccidentCloseFormTemplate 事故结案流程
const TrafficAccidentCloseFormTemplate = "traffic_accident_close_process"

// StaffArchiveEditApplyFormTemplate 员工档案信息编辑申请流程
const StaffArchiveEditApplyFormTemplate = "staff_archive_edit_apply_process"

// StaffTransferApplyFormTemplate 员工调岗申请流程
const StaffTransferApplyFormTemplate = "staff_transfer_apply_process"

// PositionalTitleApplyFormTemplate 职称补贴申请流程
const PositionalTitleApplyFormTemplate = "positional_title_apply_process"

// PetitionWorkOrderReportFormTemplate 信访工单上报流程
const PetitionWorkOrderReportFormTemplate = "petition_work_order_report_process"

// DeviceWorkOrderReportFormTemplate 报修工单上报流程
const DeviceWorkOrderReportFormTemplate = "device_work_orders_report_process"

// GlassRepairFormTemplate 玻璃维修流程
const GlassRepairFormTemplate = "glass_repair_process"

// VehicleMigrationApplyFormTemplate 车辆调动申请流程
const VehicleMigrationApplyFormTemplate = "vehicle_migration_apply_process"

// DriverMigrationApplyFormTemplate 司机调动申请流程
const DriverMigrationApplyFormTemplate = "driver_migration_apply_process"

// RefererRewardFormTemplate 引荐奖励申请流程
const RefererRewardFormTemplate = "referer_reward_apply_process"

// StaffLeaveFormTemplate 请假流程
const StaffLeaveFormTemplate = "staff_leave_apply_new_process"

// OperationReportApplyFormTemplate 营运报表审批流程
const OperationReportApplyFormTemplate = "operation_report_apply_process"

// DriverBecomeWorkerApplyFormTemplate 司机转正流程
const DriverBecomeWorkerApplyFormTemplate = "driver_become_worker_apply_process"

var LbpmFormTemplates = []map[string]string{
	{"TemplateFormId": TrafficAccidentReportFormTemplate, "TemplateFormName": "事故提交", "ModelId": SafetyModelId, "ModelName": "安全模块", "FormUrl": "/oa/#/process/accidentreport"},
	{"TemplateFormId": TrafficAccidentEditFormTemplate, "TemplateFormName": "事故变更", "ModelId": SafetyModelId, "ModelName": "安全模块", "FormUrl": "/oa/#/process/accidentchange"},
	{"TemplateFormId": TrafficAccidentLendMoneyFormTemplate, "TemplateFormName": "事故借款", "ModelId": SafetyModelId, "ModelName": "安全模块", "FormUrl": "/oa/#/process/accidentborrow"},
	{"TemplateFormId": TrafficAccidentDrawbackMoneyFormTemplate, "TemplateFormName": "事故退款", "ModelId": SafetyModelId, "ModelName": "安全模块", "FormUrl": "/oa/#/process/accidentrefund"},
	{"TemplateFormId": TrafficAccidentPaymentMoneyFormTemplate, "TemplateFormName": "事故付款", "ModelId": SafetyModelId, "ModelName": "安全模块", "FormUrl": "/oa/#/process/accidentpayment"},
	{"TemplateFormId": TrafficAccidentBranchCloseFormTemplate, "TemplateFormName": "事故分支结案", "ModelId": SafetyModelId, "ModelName": "安全模块", "FormUrl": "/oa/#/process/branchClosed"},
	{"TemplateFormId": TrafficAccidentCloseFormTemplate, "TemplateFormName": "事故结案", "ModelId": SafetyModelId, "ModelName": "安全模块", "FormUrl": "/oa/#/process/close"},

	{"TemplateFormId": StaffArchiveEditApplyFormTemplate, "TemplateFormName": "员工信息编辑申请", "ModelId": HrModelId, "ModelName": "人资模块", "FormUrl": ""},
	{"TemplateFormId": StaffTransferApplyFormTemplate, "TemplateFormName": "员工调动申请", "ModelId": HrModelId, "ModelName": "人资模块", "FormUrl": "/oa/#/process/stafftransfer"},
	{"TemplateFormId": PositionalTitleApplyFormTemplate, "TemplateFormName": "职称补贴申请", "ModelId": HrModelId, "ModelName": "人资模块", "FormUrl": ""},
	{"TemplateFormId": RefererRewardFormTemplate, "TemplateFormName": "引荐奖励", "ModelId": HrModelId, "ModelName": "人资模块", "FormUrl": ""},
	{"TemplateFormId": StaffLeaveFormTemplate, "TemplateFormName": "员工请假", "ModelId": HrModelId, "ModelName": "人资模块", "FormUrl": "/oa/#/process/leaveApplicationDetail"},
	{"TemplateFormId": DriverMigrationApplyFormTemplate, "TemplateFormName": "司机调动申请", "ModelId": HrModelId, "ModelName": "人资模块", "FormUrl": "/oa/#/process/transferPersonnel"},
	{"TemplateFormId": DriverBecomeWorkerApplyFormTemplate, "TemplateFormName": "司机转正流程", "ModelId": HrModelId, "ModelName": "人资模块", "FormUrl": ""},

	{"TemplateFormId": PetitionWorkOrderReportFormTemplate, "TemplateFormName": "信访工单", "ModelId": WorkOrderModelId, "ModelName": "工单模块", "FormUrl": ""},
	{"TemplateFormId": DeviceWorkOrderReportFormTemplate, "TemplateFormName": "设备报修工单", "ModelId": WorkOrderModelId, "ModelName": "工单模块", "FormUrl": "/oa/#/process/workorder"},

	{"TemplateFormId": GlassRepairFormTemplate, "TemplateFormName": "玻璃维修", "ModelId": MaintenanceModelId, "ModelName": "机务模块", "FormUrl": "/oa/#/process/glassrepair"},
	{"TemplateFormId": VehicleMigrationApplyFormTemplate, "TemplateFormName": "车辆调动申请", "ModelId": MaintenanceModelId, "ModelName": "机务模块", "FormUrl": "/oa/#/process/transferVehicle"},

	{"TemplateFormId": OperationReportApplyFormTemplate, "TemplateFormName": "营运报表审批", "ModelId": OperationModelId, "ModelName": "运营模块", "FormUrl": ""},
}

var LbpmFormFields = map[string][]map[string]string{
	TrafficAccidentReportFormTemplate: []map[string]string{
		{"FieldId": "AccidentGrade", "FieldName": "事故级别", "FieldType": "String"},
	},
	TrafficAccidentEditFormTemplate: []map[string]string{
		{"FieldId": "AccidentGrade", "FieldName": "事故级别", "FieldType": "String"},
	},
	TrafficAccidentLendMoneyFormTemplate: []map[string]string{
		{"FieldId": "LendMoney", "FieldName": "借款金额", "FieldType": "Number"},
	},
	TrafficAccidentDrawbackMoneyFormTemplate: []map[string]string{
		{"FieldId": "DrawbackMoney", "FieldName": "退款金额", "FieldType": "Number"},
	},
	TrafficAccidentPaymentMoneyFormTemplate: []map[string]string{
		{"FieldId": "FixOffice", "FieldName": "维修车间", "FieldType": "String"},
		{"FieldId": "PaymentMoney", "FieldName": "付款金额", "FieldType": "Number"},
	},
	TrafficAccidentBranchCloseFormTemplate: []map[string]string{
		{"FieldId": "DiffAmount", "FieldName": "差额", "FieldType": "Number"},
		{"FieldId": "PayAmount", "FieldName": "赔付金额", "FieldType": "Number"},
		{"FieldId": "IsMustFix", "FieldName": "是否需要修车", "FieldType": "Number"},
	},
	TrafficAccidentCloseFormTemplate: []map[string]string{
		{"FieldId": "DiffAmount", "FieldName": "差额", "FieldType": "Number"},
		{"FieldId": "PayAmount", "FieldName": "赔付金额", "FieldType": "Number"},
		{"FieldId": "IsMustFix", "FieldName": "是否需要修车", "FieldType": "Number"},
	},

	StaffArchiveEditApplyFormTemplate: []map[string]string{
		{"FieldId": "WorkPostType", "FieldName": "岗位类型", "FieldType": "Number"},
		{"FieldId": "StaffPhone", "FieldName": "员工手机号", "FieldType": "String"},
		{"FieldId": "StaffName", "FieldName": "员工姓名", "FieldType": "String"},
		{"FieldId": "DepartmentName", "FieldName": "组织机构名称", "FieldType": "String"},
		{"FieldId": "DepartmentCode", "FieldName": "组织机构编号", "FieldType": "String"},
	},
	StaffTransferApplyFormTemplate: []map[string]string{
		{"FieldId": "WorkPostType", "FieldName": "人员性质", "FieldType": "Number"},
		{"FieldId": "NoticeStaffPhones", "FieldName": "知会人", "FieldType": "Array"},
	},

	PetitionWorkOrderReportFormTemplate: []map[string]string{
		{"FieldId": "ToApplyUserAccount", "FieldName": "交办人员", "FieldType": "String"},
		{"FieldId": "IsAccept", "FieldName": "是否认可", "FieldType": "Number"},
		{"FieldId": "FleetCode", "FieldName": "交办人车队编号", "FieldType": "string"},
	},

	DeviceWorkOrderReportFormTemplate: []map[string]string{
		{"FieldId": "RepairMethod", "FieldName": "报修方式", "FieldType": "Number"},
		{"FieldId": "FleetCode", "FieldName": "车队编号", "FieldType": "String"},
		{"FieldId": "DeviceStatus", "FieldName": "设备状态", "FieldType": "Number"},
		{"FieldId": "IsExpire", "FieldName": "设备是否在保", "FieldType": "Number"},
		{"FieldId": "HandlerName", "FieldName": "设备保修方", "FieldType": "String"},
		{"FieldId": "Maintainer", "FieldName": "设备过保维修方", "FieldType": "String"},
		{"FieldId": "IsMaterialMissing", "FieldName": "是否缺失维修物料", "FieldType": "Number"},
		{"FieldId": "AssignMainUserAccounts", "FieldName": "指派的负责人", "FieldType": "Array"},
		{"FieldId": "AssignFirstStepUserAccounts", "FieldName": "第一级的负责人", "FieldType": "Array"},
		{"FieldId": "AssignSecondStepUserAccounts", "FieldName": "第二级的负责人", "FieldType": "Array"},
		{"FieldId": "RepairUserAccounts", "FieldName": "指派的维修人员", "FieldType": "Array"},
	},
	GlassRepairFormTemplate: []map[string]string{
		{"FieldId": "DamageType", "FieldName": "损坏类型", "FieldType": "Number"},
		{"FieldId": "FixOffice", "FieldName": "维修车间", "FieldType": "String"},
		{"FieldId": "InWarranty", "FieldName": "是否在质保期内", "FieldType": "Number"},
		{"FieldId": "IsDirectIndemnity", "FieldName": "直接赔付", "FieldType": "Number"},
	},
	VehicleMigrationApplyFormTemplate: []map[string]string{
		{"FieldId": "AcceptUserMobile", "FieldName": "调动接收人", "FieldType": "String"},
	},

	StaffLeaveFormTemplate: []map[string]string{
		{"FieldId": "ApplyLeaveDay", "FieldName": "请假天数", "FieldType": "Number"},
	},

	DriverMigrationApplyFormTemplate: []map[string]string{
		{"FieldId": "FleetUserMobile", "FieldName": "调入车队车队长", "FieldType": "String"},
		{"FieldId": "HrUserMobile", "FieldName": "调入机构劳资", "FieldType": "String"},
		{"FieldId": "ManageUserMobile", "FieldName": "调入机构综合", "FieldType": "String"},
		{"FieldId": "IsFleetApply", "FieldName": "是否车队发起", "FieldType": "Number"},
		{"FieldId": "NotifyUserMobile1", "FieldName": "知会人1", "FieldType": "String"},
		{"FieldId": "NotifyUserMobile2", "FieldName": "知会人2", "FieldType": "String"},
		{"FieldId": "NotifyUserMobile3", "FieldName": "知会人3", "FieldType": "String"},
		{"FieldId": "NotifyUserMobile4", "FieldName": "知会人4", "FieldType": "String"},
	},
}

//func MigrateLbpmFormListField() {
//	for i := range FormTemplates {
//		var FormTemplate lbpmModel.LbpmFormTemplate
//		_ = FormTemplate.FindBy(FormTemplates[i]["Name"])
//		if FormTemplate.FormId > 0 {
//			FormTemplate.FormName = FormTemplates[i]["FormName"]
//			FormTemplate.ModelName = FormTemplates[i]["ModelName"]
//			FormTemplate.ModelId = FormTemplates[i]["ModelId"]
//			FormTemplate.FormUrl = FormTemplates[i]["FormUrl"]
//			FormTemplate.SysId = config.Config.Lbpm.SysId
//
//			err := FormTemplate.Update()
//			if err != nil {
//				log.ErrorFields("LbpmFormTemplate Update error", map[string]interface{}{"err": err})
//				continue
//			}
//		} else {
//			FormTemplate = lbpmModel.LbpmFormTemplate{
//				FormId:    model.Id(),
//				FormName:  FormTemplates[i]["FormName"],
//				Name:      FormTemplates[i]["Name"],
//				ModelId:   FormTemplates[i]["ModelId"],
//				ModelName: FormTemplates[i]["ModelName"],
//				SysId:     config.Config.Lbpm.SysId,
//				FormUrl:   FormTemplates[i]["FormUrl"],
//			}
//
//			err := FormTemplate.Create()
//			if err != nil {
//				log.ErrorFields("LbpmFormTemplate Create error", map[string]interface{}{"err": err})
//				continue
//			}
//		}
//
//		if _, ok := formFields[FormTemplates[i]["Name"]]; ok {
//			fields := formFields[FormTemplates[i]["Name"]]
//
//			for j := range fields {
//				var field lbpmModel.LbpmFormField
//				_ = field.FindBy(FormTemplate.FormId, fields[j]["FieldId"])
//
//				if field.Id > 0 {
//					field.FieldName = fields[j]["FieldName"]
//					field.FieldType = fields[j]["FieldType"]
//					err := field.Update()
//					if err != nil {
//						log.ErrorFields("LbpmFormField Update error", map[string]interface{}{"err": err})
//						continue
//					}
//				} else {
//					field.Id = model.Id()
//					field.FormId = FormTemplate.FormId
//					field.FieldId = fields[j]["FieldId"]
//					field.FieldName = fields[j]["FieldName"]
//					field.FieldType = fields[j]["FieldType"]
//					err := field.Create()
//					if err != nil {
//						log.ErrorFields("LbpmFormField Create error", map[string]interface{}{"err": err})
//						continue
//					}
//				}
//			}
//		}
//	}
//}
