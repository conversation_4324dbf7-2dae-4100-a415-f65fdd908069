package config

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
)

type database struct {
	Host     string `json:"Host"`
	Port     int64  `json:"Port"`
	DbName   string `json:"DbName"`
	User     string `json:"User"`
	Password string `json:"Password"`
}

type etcd struct {
	Host     string `json:"Host"`
	Port     int64  `json:"Port"`
	Interval int64  `json:"Interval"`
	TTL      int64  `json:"TTL"`
}

type micro struct {
	ServiceName string `json:"ServiceName"`
}

type mini struct {
	AppAccountId        int64  `json:"AppAccountId"`
	DoorCheckTemplateId string `json:"DoorCheckTemplateId"` // 门检-微信服务号通知
	Page                string `json:"Page"`                // 跳转小程序页面 后面自行添加参数
}

type lbpm struct {
	Enable              bool   `json:"Enable"`
	SysId               string `json:"SysId"`               //蓝凌流程引擎系统对接标识
	AuthAccount         string `json:"AuthAccount"`         //webservice请求鉴权账号
	AuthPassword        string `json:"AuthPassword"`        //webservice请求鉴权密码
	RestApiAuthAccount  string `json:"RestApiAuthAccount"`  //restful api请求鉴权账号
	RestApiAuthPassword string `json:"RestApiAuthPassword"` //restful api请求鉴权密码
	LbpmDomain          string `json:"LbpmDomain"`
	ServicePrefix       string `json:"ServicePrefix"`
	ToDoPrefixDomain    string `json:"ToDoPrefixDomain"` //待办审批跳转地址的域名前缀
}
type dingTalkBpm struct {
	Enable                 bool              `json:"Enable"`
	AgentId                int64             `json:"AgentId"`   //内部应用的ID
	AppKey                 string            `json:"AppKey"`    //内部应用的AppKey
	AppSecret              string            `json:"AppSecret"` //内部应用的AppSecret
	AesKey                 string            `json:"AesKey"`    //内部应用订阅消息加解密key
	Token                  string            `json:"Token"`     //内部应用订阅消息加解密token
	DingTalkBpmProcessCode map[string]string `json:"DingTalkBpmProcessCode"`
}

type schedulerJob struct {
	IsSendDoorCheckNotify             bool `json:"IsSendDoorCheckNotify"`             //是否定时发送门检未整改的通知
	IsSendLaborContractExpireNotify   bool `json:"IsSendLaborContractExpireNotify"`   //是否定时发送劳动合同即将到期提醒
	IsSendStaffProbationExpireNotify  bool `json:"IsSendStaffProbationExpireNotify"`  //是否定时发送试用期即将到期提醒
	IsPreGenerateDoorCheckRecord      bool `json:"IsPreGenerateDoorCheckRecord"`      //是否定时预生成门检记录
	IsCheckTicketDataPermission       bool `json:"IsCheckTicketDataPermission"`       //是否检查票务数据编辑权限
	IsProcessAlarm                    bool `json:"IsProcessAlarm"`                    // 是否开启流程预警告警 每天1点检查 8:45和14:15发送微信消息
	IsCalcStaffArchiveReport          bool `json:"IsCalcStaffArchiveReport"`          // 是否开启人员报表统计
	IsTakeOetLineHasDriverRecord      bool `json:"IsTakeOetLineHasDriverRecord"`      // 是否同步主数据线路人员归属关系数据
	IsCalcStaffAnnualLeave            bool `json:"IsCalcStaffAnnualLeave"`            //是否计算员工年假
	IsTakeTaizhouThirdIcCardTradeData bool `json:"IsTakeTaizhouThirdIcCardTradeData"` //是否定时获取台州IC卡交易结算数据
}

type syncMasterStaff struct {
	Enable         bool    `json:"Enable"`
	CorporationIds []int64 `json:"CorporationIds"`
}

type dingTalkAlarm struct {
	Enable bool   `json:"Enable"`
	Url    string `json:"Url"`
}

type iCMysqlDatabase struct {
	DbHost string `json:"DbHost"`
	DbUser string `json:"DbUser"`
	DbPass string `json:"DbPass"`
	DbName string `json:"DbName"`
}

var Config struct {
	Env                        string          `json:"Env"`
	DebugLevel                 string          `json:"DebugLevel"`
	AbsDirPath                 string          `json:"AbsDirPath"`
	WebRoot                    string          `json:"WebRoot"`
	StaticFileHttpPrefix       string          `json:"StaticFileHttpPrefix"`
	IsRelationIss              bool            `json:"IsRelationIss"` // 是否关联OET调度系统
	Database                   database        `json:"Database"`
	Etcd                       etcd            `json:"Etcd"`
	Micro                      micro           `json:"Micro"`
	Mini                       mini            `json:"Mini"`
	SchedulerJob               schedulerJob    `json:"SchedulerJob"`               // 定时任务控制
	Lbpm                       lbpm            `json:"Lbpm"`                       //蓝凌OA审批接入配置
	DingTalkBpm                dingTalkBpm     `json:"DingTalkBpm"`                //钉钉OA审批接入配置
	PermissionSys              string          `json:"PermissionSys"`              //适用的权限系统
	SyncMasterStaff            syncMasterStaff `json:"SyncMasterStaff"`            //同步主数据的人员到ERP员工档案
	TopCorporationId           int64           `json:"TopCorporationId"`           // 顶级机构id
	DingTalkAlarm              dingTalkAlarm   `json:"DingTalkAlarm"`              // 监控告警(目前包含：定时任务-门检预生成记录错误告警)
	DeviceQrcodeUrl            string          `json:"DeviceQrcodeUrl"`            // 设备二维码url 工单设备微信扫码报修功能
	VehicleQrcodeUrl           string          `json:"VehicleQrcodeUrl"`           // 车辆二维码url
	TicketBankCheckExcludeLine []string        `json:"TicketBankCheckExcludeLine"` //票务银行对账查询时需要排除的线路
	StaffArchiveReportCorpId   int64           `json:"StaffArchiveReportCorpId"`   //人员档案报表统计的机构ID
	ICMysqlDatabase            iCMysqlDatabase `json:"ICMysqlDatabase"`            //ic卡数据库连接配置
	EnableMigrateDatabase      bool            `json:"EnableMigrateDatabase"`      //是否迁移表
	TravelNeedQrcodeUrl        string          `json:"TravelNeedQrcodeUrl"`        // // 出行需求二维码url
}

// InitConfig :init app config
func InitConfig(filePath string) error {
	fileContents, err := ioutil.ReadFile(filePath)
	if err != nil {
		return err
	}

	err = json.Unmarshal(fileContents, &Config)
	if nil != err {
		return err
	}

	if Config.Database.Host == "" || Config.Database.User == "" || Config.Database.DbName == "" {
		return errors.New("please Input database info")
	}

	if "" == Config.AbsDirPath {
		return errors.New("please Input AbsDirPath")
	}

	// 重置全局变量
	ResetDingTalkBpmProcessCode()

	fmt.Printf("APP CONFIG %+v \r\n", Config)
	return nil
}

func GetApp() (string, string) {
	return "cf186f7dafdab3433ca875d6bd7d7b63", "b813ecf3577b379701db34a0149bc365"
}
