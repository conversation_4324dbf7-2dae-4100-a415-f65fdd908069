syntax = "proto3";

package oet.scs.srv.app;

service OetVehicleModel {
    rpc GetVehicleModelsWithOption (GetVehicleModelsWithOptionRequest) returns (GetVehicleModelsWithOptionResponse) {
    }
}

message OetVehicleModelItem {
    string Model = 1;               // 车辆型号 (key)
    string Brand = 2;               // 车辆品牌
    double Length = 3;              // 车辆长度（米）
    int64 Emission = 4;             // 排放标准：0-未知;1-国一;2-国二;3-国三;4-国四;5-国五;6-国六; 7-欧三；8-欧四；9-欧五；10-欧六；999-其他
    int64 VehicleClass = 5;         // 车辆类型,1-客车；2-大型客车；3-中型客车；4-小型客车；5-轿车；6-大型卧铺客车；7-中型卧铺客车；999-其他车辆
    int64 SiteNum = 6;              // 座位数    
    int64 MaxNum = 7;               // 最大载客数   
    int64 VehicleModal = 8;         // 燃料类型, 1:柴油车(传统);2:汽油车(传统); 3:混合动力车(新能源);4:纯电动车(新能源);5:CNG(新能源);6:LNG;7:纯电动(双路BMS);
    int64 Beacom = 9;               // 标台，标准车的单位。10米到13米车型折算为1.3标台，16米到18米车型折算为2.0标台
    string EngineModel = 10;        // 发动机型号   
    string MotorModel = 11;         // 电机型号   
    string ChassisModel = 12;       // 底盘型号   
    string EngineFacture = 13;      // 发动机厂家   
    string EngineType = 14;         // 发动机类型   
    int64 EngineCapacity = 15;      // 发动机排量（ml）   
    int64 WholeQuality = 16;        // 整车质备   
    int64 Width = 17;               // 车辆宽度（mm）   
    int64 Height = 18;              // 车辆高度（mm）   
    string OuterColor = 19;         // 外廓颜色   
    bool IsDoubleDecker = 20;       // 双层车， 0-否，1-是   
    int64 WarrantyPeriod = 21;      // 质保年限,（年）   
    int64 OilTankCapacity = 22;     // 邮箱容积（L）   
    string BatteryFuelType = 23;    // 电池燃料类型   
    int64 BatteryEnduranceMileage = 24;  // 电池续航里程（km）   
    string BatteryFacturer = 25;         // 电池生产厂商   
    int64 BatteryRatedVol = 26;          // 电池额定总电压 （V）   
    int64 BatteryRatedCapacity = 27;     // 电池额定容量 (kWh)   
    int64 RefEnergy = 28;                // 参考能耗, 百公里耗电量（kWh/100km）/百公里耗油量（L/100km）
    int64 UpdatedAt = 29;       // 更新时间
    int64 CreatedAt = 30;       // 创建时间
}


message GetVehicleModelsWithOptionRequest {
    int64 TopCorporationId = 1;             // 根机构id
    repeated string VehicleModels = 2;      // 车辆型号
}

message GetVehicleModelsWithOptionResponse {
    repeated OetVehicleModelItem Items = 1;     // 车辆型号列表
}