syntax = "proto3";

package oet.scs.srv.public;

service Corporation {
	rpc Add(AddCorporationRequest) returns (AddCorporationResponse) {}
	rpc Del(DelCorporationRequest) returns (DelCorporationResponse) {}
	rpc Edit(EditCorporationRequest) returns (EditCorporationResponse) {}
	rpc Get(GetCorporationByNameRequest) returns (CorporationItem) {}
	rpc GetById(GetCorporationByIdRequest) returns (CorporationItem) {}
	rpc List(ListCorporationRequest) returns (ListCorporationResponse) {}
	rpc Tree(TreeCorporationRequest) returns (TreeCorporationResponse) {}
	rpc Move(MoveCorporationRequest) returns (MoveCorporationResponse) {}
	rpc Sort(SortCorporationRequest) returns (SortCorporationResponse) {}
	rpc GetTopDetailById(GetTopDetailByIdRequest) returns (GetTopDetailByIdResponse) {}
	rpc GetCorporationDetailById(GetCorporationDetailByIdRequest) returns (GetCorporationDetailByIdResponse) {}
}

message AddCorporationRequest {
	string Name = 1;
	string Leader = 2;
	string Phone = 3;
	int64 CountryId = 4;
	int64 ProvinceId = 5;
	int64 CityId = 6;
	int64 ParentId = 7;
	int64 IssVendor = 8;
	int64 IssProtocol = 9;
	string IssTable = 10;
	string IssHost = 11;
	int64 Type = 12; //类型, 1:集团、2:公司、3:分公司、4:部门、5:车队
}

message AddCorporationResponse {
	int64 Id = 1;
}

message DelCorporationRequest {
	int64 Id = 1;
}

message DelCorporationResponse {
}

message EditCorporationRequest {
	int64 Id = 1;
	string Name = 2;
	string Leader = 3;
	string Phone = 4;
	int64 CountryId = 5;
	int64 ProvinceId = 6;
	int64 CityId = 7;
	int64 IssVendor = 8;
	int64 IssProtocol = 9;
	string IssTable = 10;
	string IssHost = 11;
}

message EditCorporationResponse {
}

message GetCorporationByNameRequest {
	string Name = 1;
}

message GetCorporationByIdRequest {
	int64 Id = 1;
}

message ListCorporationRequest {
	int64 ParentId = 1;
	string Name = 2;
	int64 Offset = 3;
	int64 Limit = 4;
	string Order = 5;
}

message ListCorporationResponse {
	int64 TotalCount = 1;
	repeated CorporationItem Items = 2;
}

message CorporationItem {
	int64 Id = 1;
	string Name = 2;
	string Virtual = 3;
	string Leader = 4;
	string Phone = 5;
	int64 CountryId = 6;
	int64 ProvinceId = 7;
	int64 CityId = 8;
	int64 AddedBy = 9;
	bool IsAdmin = 10;
	int64 ParentId = 11;
	int64 CreatedAt = 12;
	int64 UpdatedAt = 13;
	int64 SonNum = 14;
	string Province = 15;
	string City = 16;
	int64 IssVendor = 17;
	int64 IssProtocol = 18;
	string IssTable = 19;
	string IssHost = 20;
	int64 Type = 21; //类型, 1:集团、2:公司、3:分公司、4:部门、5:车队
}

message TreeCorporationRequest {
	int64 ParentId = 1;
	string Order = 2;
}

message TreeCorporationResponse {
	int64 TotalCount = 1;
	repeated CorporationBriefItem Items = 2;
}

message CorporationBriefItem {
	int64 Id = 1;
	string Name = 2;
	int64 ParentId = 3;
	int64 Type = 4; //类型, 1:集团、2:公司、3:分公司、4:部门、5:车队
}

message MoveCorporationRequest {
    int64 FromCorporationId = 1;
    int64 ToCorporationId = 2;
}

message MoveCorporationResponse {
    string Code = 1;
    string Msg = 2;
}

message SortCorporationRequest {
    int64 CorporationId = 1;// 需要排序机构 Id
    int64 AfterCorporationId = 2;//目的地排序位置后机构 Id， 如果没有为0
    int64 BeforeCorporationId = 3; // 目的地排序位置前机构 Id， 如果没有为0
}

message SortCorporationResponse {
    string Code = 1;
    string Msg = 2;
}

message GetTopDetailByIdRequest {
    int64 CorporationId = 1;
};

message GetTopDetailByIdResponse {
    CorporationItem Item = 1;
};

message GetCorporationDetailByIdRequest {
    int64 CorporationId = 1;
};

message GetCorporationDetailByIdResponse {
    CorporationItem Item = 1;
    int64 GroupId = 2;        //所属 集团 的机构Id
    string GroupName = 3;     //所属 集团 的机构名称
  
    int64 CompanyId = 4;      //所属 公司 的机构Id
    string CompanyName = 5;   //所属 公司 的机构名称
  
    int64 BranchId = 6;       //所属 分公司 的机构Id
    string BranchName = 7;    //所属 分公司 的机构名称
  
    int64 DepartmentId = 8;   //所属 部门 的机构Id
    string DepartmentName = 9;//所属 部门 的机构名称

    int64 FleetId = 10;       //所属 车队 的机构Id
    string FleetName = 11;    //所属 车队 的机构名称
};
