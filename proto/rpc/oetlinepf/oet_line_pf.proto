syntax = "proto3";

package oet.apc.api.app;

service OetLinePf {
  rpc GetLinePfWithLineIds (GetLinePfWithLineIdsRequest) returns (GetLinePfWithLineIdsResponse) {};
}

message LinePfItem {
  int64 LineId = 1;
  int64 InPeople = 2;
  string ReportAt = 3;
}

message GetLinePfWithLineIdsRequest {
  int64 TopCorporationId = 1;
  int64 LineId = 2;
  int64 StartAtUnix = 3;
  int64 EndAtUnix = 4;
}

message GetLinePfWithLineIdsResponse {
  repeated LinePfItem items = 1;
}