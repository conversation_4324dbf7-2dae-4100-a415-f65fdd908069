syntax = "proto3";

package oet.scs.api.mini;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

service Station {
  rpc Board(go.api.Request) returns (go.api.Response) {}
  rpc Vote(go.api.Request) returns (go.api.Response) {}
  rpc GetLineList(go.api.Request) returns (go.api.Response) {}
  rpc TimeSlotVote(TimeSlotVoteRequest) returns (TimeSlotVoteResponse){}
}

message TimeSlotVoteRequest {
  int64          StationId = 1;  // 站点ID
  int64          StartAt =   2;  // 开始时间
  int64          EndAt   =   3;  // 结束时间
  int64          Scale   =   4;  // 时间刻度,单位秒
  int64          LineId =   5;  // 线路ID
  int64           Sheet =   6;  // 1:上行 2:下行 3:环形
}

message TimeSlotVoteResponse{
  string Code = 1;
  string Msg = 2;
  repeated TimeSlotVoteItem Items = 3;
}

message  TimeSlotVoteItem {
  int64    StartTime =   1;  // 开始时段
  int64    EndTime   =   2;  // 结束时段
  int64    VoteCount =   3;  // 投票人数
}

