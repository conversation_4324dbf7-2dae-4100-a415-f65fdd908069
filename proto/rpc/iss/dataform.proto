syntax = "proto3";

package oet.scs.api.iss;

service Dataform {
  rpc VehicleRunFormSum(VehicleRunFormSumRequest) returns (VehicleRunFormSumResponse) {} // 车辆运营汇总
  rpc DriverRunFormSum(DriverRunFormSumRequest) returns (DriverRunFormSumResponse) {} // 司机运营汇总
  rpc LineRunFormDaySum(LineRunFormDaySumRequest) returns (LineRunFormDaySumResponse) {} // 司机运营汇总
  rpc LineCard(LineCardRequest) returns (LineCardResponse) {} // 查询线路计划详情

}

// 2022.8.26 00:00~2022.9.25 23:59  8.26~9.25(包含)内统计数据
message VehicleRunFormSumRequest {
  int64 VehicleId = 1;     // 车辆ID
  int64 StartAt = 2;     // 开始时间 8.26 00:00
  int64 EndAt = 3;     // 结束时间 9.25 23:59
}

// 核定总里程 = PlanActualRunMileage + UnloadMileage
// 空驶里程 = 空驶总里程
// GPS里程 = 实际GPS总里程
message VehicleRunFormSumResponse {
  int64  RunDay = 1; // 运营天数
  int64  PlanCount = 2; // 计划趟次
  int64  ActualCount = 3; // 实际趟次
  // gps里程
  int64 ActualGpsMileage = 4; // 实际里程(单位: 米)  当天所有里程
  int64 ActualUnLoadGpsMileage = 5; // 空驶里程(单位: 米)  当天所有里程-当天路单里程
  int64 ActualGpsTime = 6; // 实际时间(单位: 秒)

  // 执行的路单计划信息
  int64 PlanActualRunMileage = 7; // 实际运营的计划里程(单位:米)     已执行路单的计划里程
  int64 PlanActualRunUnloadMileage = 8; // 实际运营的计划空驶里程(单位:米)  已执行路单的计划空驶里程
  int64 PlanActualRunTime = 9; // 实际运营的计划时间(单位: 秒)    已执行路单的计划时间

  // 执行的路单实际信息
  int64 ActualRunMileage = 10; //  实际运营里程(单位:米)      路单里程 + 空驶
  int64 ActualRunUnloadMileage = 11; //  实际运营空驶里程(单位:米)   路单空驶里程
  int64 ActualRunTime = 12; // 实际运营时间(单位: 秒)     路单总时间
}

message DriverRunFormSumRequest {
  int64 DriverId = 1;   // 司机ID
  int64 StartAt = 2;   // 开始时间 00:00
  int64 EndAt = 3;   // 结束时间 23:59
}

// 运营总公里 = ActualRunMileage
// 总公里 = 实际GPS总里程
message DriverRunFormSumResponse {
  int64  RunDay = 1; // 运营天数 -- 暂无数据
  int64  PlanCount = 2; // 计划趟次
  int64  ActualCount = 3; // 实际趟次
  // gps里程
  int64 ActualGpsMileage = 4; // 实际里程(单位: 米)  当天所有里程
  int64 ActualUnLoadGpsMileage = 5; // 空驶里程(单位: 米)  当天所有里程-当天路单里程
  int64 ActualGpsTime = 6; // 实际时间(单位: 秒)

  // 执行的路单计划信息
  int64 PlanActualRunMileage = 7; // 实际运营的计划里程(单位:米)     已执行路单的计划里程
  int64 PlanActualRunUnloadMileage = 8; // 实际运营的计划空驶里程(单位:米)  已执行路单的计划空驶里程
  int64 PlanActualRunTime = 9; // 实际运营的计划时间(单位: 秒)    已执行路单的计划时间

  // 执行的路单实际信息
  int64 ActualRunMileage = 10; //  实际运营里程(单位:米)      路单里程 + 空驶
  int64 ActualRunUnloadMileage = 11; //  实际运营空驶里程(单位:米)   路单空驶里程
  int64 ActualRunTime = 12; // 实际运营时间(单位: 秒)     路单总时间

  int64  WorkTime = 13; // 岗上工时 (单位: 秒)
  int64  LaidOffTime = 14; // 岗下工时 (单位: 秒)
}


message LineRunFormDaySumRequest {
  int64 LineId = 1;          // 线路ID
  int64 CorporationId = 2;   // 机构ID
  int64 StartAt = 3;        // 开始时间 秒蹉
  int64 EndAt = 4;        // 结束时间 秒蹉
}

message LineRunFormDaySumResponse {
  string Code = 1;
  string Msg = 2;
  repeated LineRunFormDaySumItem Items = 3;
}

message LineRunFormDaySumItem  {
  int64  LineId = 1;        // 线路ID
  int64  CorporationId = 2;        // 机构ID
  int64  LineType = 3;        // 线路类型
  int64  VehicleId = 4;        // 车辆ID
  int64  DriverId = 5;        // 司机ID
  string ClassIdxStr = 6;        // 班次组
  int64  PlanWholeTrip = 7;        // 计划全程趟次
  int64  PlanPartTrip = 8;        // 计划区间趟次 -- 含停运趟次
  int64  ActualWholeTrip = 9;        // 实际全程趟次 -- 已完成、已补录
  int64  ActualPartTrip = 10;       // 实际区间趟次 -- 已完成、已补录
  int64  StopRepairTrip = 11;       // 修车趟次
  int64  StopPersonalAffairTrip = 12;       // 事假趟次
  int64  StopAccidentTrip = 13;       // 事故趟次
  int64  StopAnnualAuditTrip = 14;       // 年审趟次
  int64  StopOfficialAttendanceTrip = 15;       // 公务出勤趟次
  int64  StopContractVehicleTrip = 16;       // 包车趟次
  int64  StopSickLeaveTrip = 17;       // 病假趟次
  int64  StopAnnualLeaveTrip = 18;       // 年休假趟次
  int64  StopTrafficJamTrip = 19;       // 堵车趟次
  int64  StopRecuperateTrip = 20;       // 疗休养趟次
  int64  NightBefore22HourTime = 21;       // 22点前夜班时长 秒
  int64  NightAfter22HourTime = 22;       // 22点后夜班时长 秒
  int64  PlanEnterParkingAt = 23;       // 计划进场时间
  int64  ActualEnterParkingAt = 24;       // 实际进场时间
  int64  ClassIdSys = 25;       // 班制: 1 全班  2 半班  3 机动
  int64  PlanWholeFirstServiceType = 26;       // 计划全程首趟上下行类型 0 无  1 上行  2 下行  3 环形
  int64  ActualWholeFirstServiceType = 27;       // 实际全程首趟上下行类型 0 无  1 上行  2 下行  3 环形
  int64  PlanWholeFirstClassCount = 28;       // 计划全程首趟次数
  int64  ActualWholeFirstClassCount = 29;       // 实际全程首趟次数
  int64  PlanWholeLastClassCount = 30;       // 计划全程末趟次数
  int64  ActualWholeLastClassCount = 31;       // 实际全程末趟次数
  int64  PlanPartFirstServiceType = 32;       // 计划区间首趟上下行类型 0 无  1 上行  2 下行  3 环形
  int64  ActualPartFirstServiceType = 33;       // 实际区间首趟上下行类型 0 无  1 上行  2 下行  3 环形
  int64  PlanPartFirstClassCount = 34;       // 计划区间首趟次数
  int64  ActualPartFirstClassCount = 35;       // 实际区间首趟次数
  int64  PlanPartLastClassCount = 36;       // 计划区间末趟次数
  int64  ActualPartLastClassCount = 37;       // 实际区间末趟次数
  repeated  TripItem         Items = 38;       // 趟次信息
  repeated  int64         ClassIdxArr = 39;       // 班次组-数据处理
  int64  ActualFirstPersonalAffairTrip = 40;       // 实际首趟事假次数
  int64  ActualLastPersonalAffairTrip = 41;       // 实际末趟事假次数
}
message TripItem {
  int64 Id = 1; // 路单ID
  int64 ActualDepartAt = 2; // 发车时间
  int64 ActualArriveAt = 3; // 到达时间
  string Remark = 4; // 备注
  string ReplenishRemark = 5; // 补录备注
}


message LineCardRequest {
  repeated  LineCorpItem Items = 1 ;
}

message LineCorpItem {
  int64 LineId = 1;          // 线路ID
  int64 CorporationId = 2;   // 机构ID
}

message LineCardResponse {
  string Code = 1;
  string Msg = 2;
  repeated  LineCardItem Items = 3 ;
}

message LineCardItem {
  int64 LineId = 1;          // 线路ID
  int64 CorporationId = 2;   // 机构ID
  int64 ClassIdx = 3;          // 班次数
}