syntax = "proto3";

package oet.scs.api.iss;

service Schedule {

    // 门检系统使用
    rpc GetFirstLastPlanScheduleWithVehicleId(GetFirstLastPlanScheduleWithVehicleIdRequest) returns (GetFirstLastPlanScheduleWithVehicleIdResponse) {
    }
}


message GetFirstLastPlanScheduleWithVehicleIdRequest {
    int64 VehicleId=1;
    int64 ExpectDepartAtGte =2;  // 开始时间
    int64 ExpectDepartAtLte =3;  // 结束时间
    int64 Status =4; // 任务状态: 1:未执行, 2:进行中, 3:已完成, 4:停运  0 全部(去掉停运)
    int64 PlanSchType = 5; // 排班计划类型, 0: 未知, 1: 包车, 2: 班线  (默认值传-1)
}

message GetFirstLastPlanScheduleWithVehicleIdResponse {
    string Code = 1;
    string Msg = 2;
    repeated FirstLastPlanScheduleWithVehicleIdItem items = 3;
}

message FirstLastPlanScheduleWithVehicleIdItem {
    int64 Id =1;
    int64 Type =2; // 1 早 2 晚
    int64 LineId =3;        // 线路ID
    int64 CorporationId = 4; // 分机构 Id
    int64 PlanSchType = 5; // 排班计划类型, 0: 未知, 1: 包车, 2: 班线
    int64 ServiceType = 6; // 业务类型, 0: 未知, 0x01:上行, 0x02:下行, 0x03:环行, 0x04:停主站, 0x05:停副站, 0x06～0x1F:保留, 0x20～0x7F:自定义, 0x80:出场, 0x81:进场, 0x82:加油, 0x83:加气, 0x84:充电, 0x85:小修, 0x86:大修, 0x87:一保, 0x88:二保, 0x89:三保, 0x8A:放空, 0x8B:停场, 0x8C～0x9F:保留, 0xA0～0xFF:自定义
    int64 DispatchType = 7; // 调度类型, 0: 未知, 0x01:全程, 0x02:区间, 0x03:放站, 0x04～0x7F:保留, 0x80～0xFF:厂家自定义
    int64 Status   = 8;      // 任务状态: 1:未执行, 2:进行中, 3:已完成, 4:停运
    int64 ExpectDepartAt =9;  // 发车时间
    int64 ActualDepartAt =10;  // 发车时间
    int64 PlanDriverId = 11; // 计划司机 Id
    int64 ActualDriverId = 12; // 实际司机 Id
}