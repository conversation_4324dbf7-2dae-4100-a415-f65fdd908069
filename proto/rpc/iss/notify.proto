syntax = "proto3";

package oet.scs.api.iss;

service Notify {
  // erp车辆调度通知
  rpc VehicleMigrationStatusChange(VehicleMigrationStatusChangeRequest) returns (VehicleMigrationStatusChangeResponse) {};
  // erp司机调度通知
  rpc DriverMigrationStatusChange(DriverMigrationStatusChangeRequest) returns (DriverMigrationStatusChangeResponse) {};
}

message VehicleMigrationStatusChangeRequest {
  int64  WorkPostId = 1;
}

message VehicleMigrationStatusChangeResponse {
  string Code = 1;
  string Msg = 2;
}

message DriverMigrationStatusChangeRequest {
  int64  WorkPostId = 1;
}

message DriverMigrationStatusChangeResponse {
  string Code = 1;
  string Msg = 2;
}