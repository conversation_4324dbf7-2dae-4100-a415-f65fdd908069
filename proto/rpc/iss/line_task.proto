syntax = "proto3";

package oet.scs.api.iss;

service Linetask {
    rpc GetLineTaskWithId (GetLineTaskWithIdRequest) returns (GetLineTaskWithIdResponse) {} // 查详情时 LineTaskItem才返回
    rpc GetLineTaskWithLineId(GetLineTaskWithLineIdRequest) returns (GetLineTaskWithLineIdResponse){}  // 不返回 LineTaskItem
}

message LineTaskItem {
    int64 Id = 1;
    int64 TopCorporationId = 2;
    int64 CorporationId = 3;
    int64 LineId = 4;
    string Name = 5;
    int64 CreatedAt = 6;
    int64 UpdatedAt = 7;
    int64 IsDefault = 8; //是否默认: 0 非 1 是
    int64 IsArchived = 9; // 是否存档: 0 非 1 是
    repeated LineTaskDetailItem Items = 10;
}

message GetLineTaskWithIdRequest {
    int64 Id = 1;
}

message GetLineTaskWithIdResponse {
    string Code = 1;
    string Msg = 2;
    LineTaskItem Item = 3;
}


message GetLineTaskWithLineIdRequest {
    int64 CorporationId = 1;
    int64 LineId = 2;
}

message GetLineTaskWithLineIdResponse {
    string Code = 1;
    string Msg = 2;
    repeated LineTaskItem Items = 3;
}


service Linetaskdetail {
    // ---------------------------- rpc
    rpc DelLineTaskDetailWithLineTaskId (DelLineTaskDetailWithLineTaskIdRequest) returns (DelLineTaskDetailWithLineTaskIdResponse) {}
    rpc GetLineTaskDetailsWithLineTaskId (GetLineTaskDetailsWithLineTaskIdRequest) returns (GetLineTaskDetailsWithLineTaskIdResponse) {}
}

message LineTaskDetailItem {
    int64 Id = 1; // 行车计划详情 Id
    int64 TopCorporationId = 2;
    int64 LineTaskId = 3;

    string Line = 4; // 线路名, add
    int64 LineId = 5; // 线路 Id, add
    int64 LineCode = 6; // 线路唯一编号, add

    int64 ClassIdx = 7; // 班次，从1开始
    int64 ServiceType = 8; // 业务类型, 0: 未知, 0x01:上行, 0x02:下行, 0x03:环行, 0x04:停主站, 0x05:停副站, 0x06～0x1F:保留, 0x20～0x7F:自定义, 0x80:出场, 0x81:进场, 0x82:加油, 0x83:加气, 0x84:充电, 0x85:小修, 0x86:大修, 0x87:一保, 0x88:二保, 0x89:三保, 0x8A:放空, 0x8B:停场, 0x8C～0x9F:保留, 0xA0～0xFF:自定义
    int64 DispatchType = 9; // 调度类型, 0: 未知, 0x01:全程, 0x02:区间, 0x03:放站, 0x04～0x7F:保留, 0x80～0xFF:厂家自定义
    int64 TripType = 10; // 趟次类型, 0: 未知, 0x01:普通班, 0x02:上午班, 0x03:下午班, 0x04:延时班, 0x05:夜班, 0x06:加班, 0x07:日班
    int64 PlanMileage = 11; // 定额里程，单位:米

    int64 PlanDepartStopType = 12; // 计划起始站场类型，1-起点站，2-中途站，3-终点站，130-停车场,131-维修厂,132-加油站，160-固定包车点
    int64 PlanDepartStopId = 13; // 计划起点车站或场站数据库Id
    string PlanDepartStop = 14; // 计划起点车站或场站名称
    int64 PlanDepartStopCode = 15; // 计划起点车站或场站唯一编号

    int64 PlanArriveStopType = 16; // 计划终点站场类型，1-起点站，2-中途站，3-终点站，130-停车场,131-维修厂,132-加油站，160-固定包车点
    int64 PlanArriveStopId = 17; // 计划终点车站或场站数据库Id
    string PlanArriveStop = 18; // 计划终点车站或场站名称
    int64 PlanArriveStopCode = 19; // 计划终点车站或场站唯一编号

    int64 PlanDepartTime = 20; // 预计划发车时间, 秒
    int64 PlanArriveTime = 21; // 预计划到达时间, 秒

    int64 CreatedAt = 22;
    int64 UpdatedAt = 23;

    int64 TripSubType = 24;  // 趟次子类型, 1 小夜班  2 中夜班  3 大夜班
    int64 HandOverFlag = 25; // 交接标识: 0 否  1 是
    int64 ReturnOutType = 26; // 出回场类型: 0 无 1 回场 2 出场
    int64 PlanParkingId = 27; // 到达场区ID
    int64 PlanParkingArriveTime =28; // 计划场区到达时间,秒
    int64 PlanParkingMileage =29; // 计划末站到场站GPS里程
    int64 PlanType =30; //  计划类型, 0：线路计划，1：机构计划
}

message DelLineTaskDetailWithLineTaskIdRequest {
    int64 TopCorporationId = 1;
    int64 LineTaskId = 2;
}

message DelLineTaskDetailWithLineTaskIdResponse {
}

message GetLineTaskDetailsWithLineTaskIdRequest {
    int64 LineTaskId = 1;
    int64 FilterType = 2; // 筛选类型, 0: 显示全部, 1: 显示上行和下行, 2: 只显示上行, 3: 只显示下行, 4: 显示环形, 5: 只显示非运营
}

message GetLineTaskDetailsWithLineTaskIdResponse {
    string Code = 1;
    string Msg = 2;
    repeated LineTaskDetailItem Items = 3;
}