syntax = "proto3";

package oet.scs.api.iss;

service IpssRpc {
    // 根据场站ID查询到达车辆列表
    rpc GetVehiclesToArriveParking(GetVehiclesToArriveParkingRequest) returns (GetVehiclesToArriveParkingResponse) {};
    rpc Location(LocationRequest) returns (LocationResponse) {};
    // 场站配置变更
    rpc ParkingSettingEditNotify(ParkingSettingEditNotifyRequest) returns (ParkingSettingEditNotifyResponse) {};

    // 提交包车需求
    rpc SubmitContractDemand(SubmitContractDemandRequest)  returns (SubmitContractDemandResponse) {};
    // 取消包车需求
    rpc EditContractDemand(EditContractDemandRequest)  returns (EditContractDemandResponse) {};

}

message LocationRequest {
    int64 TopCorporationId = 1;
    int64 VehicleId        = 2;
}

message LocationResponse {
    string Code                     = 1;
    string Msg                      = 2;
    repeated IpssLocationItem Items = 3;
}

message IpssLocationItem {
    int64 VehicleId    = 1;
    string License     = 2;
    string OwnEncode   = 3;
    string Code        = 4;
    int64 SiteNum      = 5;
    int64 MaxNum       = 6;
    int64 Occupied     = 7;
    int64 UnOccupied   = 8;
    int64 ReportAt     = 9;
    int64 Longitude    = 10;
    int64 Latitude     = 11;
    int64 Online       = 12;
    int64 RunStatus    = 13;
    int64 Speed        = 14;
    int64 Direction    = 15;
    int64 DispatchType = 16;  // 调度类型:0:全部, 0x01:全程;0x20:包车;0xb1:机动
    int64 StopTime     = 17;  // 停车时长,单位:秒
    int64 TodayMileage = 18;
}

message GetVehiclesToArriveParkingRequest {
    int64 ParkingId = 1;
}

message GetVehiclesToArriveParkingResponse {
    string Code                               = 1;
    string Msg                                = 2;
    repeated VehicleToArriveParkingItem Items = 3;
}
// 占坐数+空坐数=总座位数 司机位不包含在内
message VehicleToArriveParkingItem {
    int64 VehicleId  = 1;
    string License   = 2;
    string OwnEncode = 3;
    string Code      = 4;
    int64 SiteNum    = 5;
    int64 Occupied   = 6;  // 占座数
    int64 UnOccupied = 7;  // 空座数
    int64 Longitude  = 8;
    int64 Latitude   = 9;
    int64 ReportAt   = 10;
}

message ParkingSettingEditNotifyRequest {
    int64 ParkingId = 1;  // 场站ID
    int64 Type      = 2;  // 1-场站廊道基础配置;2-场站阈值配置
}

message ParkingSettingEditNotifyResponse {
    string Code = 1;
    string Msg  = 2;
}


message SubmitContractDemandRequest {
    int64 TopCorporationId  = 1;
    string OrderId          = 2; // 订单号
    int64 MaxPeople         = 3;// 订单人数
    int64 DepartParkingId   = 4;
    int64 ArriveParkingId   = 5;
    string Phone            = 6; // 手机号
    string Uuid = 7;  // 记录唯一标识符
    int64 PublishAt =8;   // 发布时间
}

message SubmitContractDemandResponse {
    string Code = 1;
    string Msg  = 2;
}

message EditContractDemandRequest {
    string Uuid = 1;  // 记录唯一标识符
    int64 Status   = 2;  // 1-确认;2-取消
}

message EditContractDemandResponse {
    string Code = 1;
    string Msg  = 2;
}
