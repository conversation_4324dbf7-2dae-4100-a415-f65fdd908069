syntax = "proto3";

package oet.scs.api.iss;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

service Assesslinestations {
  rpc Submit (go.api.Request) returns (go.api.Response) {}
  rpc Edit (go.api.Request) returns (go.api.Response) {}
  rpc Del (go.api.Request) returns (go.api.Response) {}
  rpc List (go.api.Request) returns (go.api.Response) {}
  rpc Search (go.api.Request) returns (go.api.Response) {}
  rpc SearchExport (go.api.Request) returns (go.api.Response) {}
  rpc GetAssessLineStation(GetAssessLineStationRequest)returns (GetAssessLineStationResponse) {}
}

message GetAssessLineStationRequest {
  int64 LineId  = 1;            // 线路ID
  int64 CorporationId  = 2;     // 机构ID
  int64 Sheet   =3;             // 1:上行; 2:下行; 3:环形
}

message GetAssessLineStationResponse {
  string Code = 1;
  string Msg = 2;
  repeated AssessLineStationItem Items = 3;
}

message AssessLineStationItem {
  int64   Id               = 1 ;   // 主键
  int64   Sheet            = 2 ;   // 上行
  int64   StationId        = 3 ;   // 站点ID
  int64   StationSeq       = 4 ;   // 站序
  int64   ClassIdx         = 5 ;   // 第几次
  int64   FirstLast        = 6 ;   // 0 中途  1 首次  2 末次
  int64   PlanDepartTime   = 7 ;   // 计划发车时间
  int64   PlanArriveTime   = 8 ;   // 计划到达时间
  string  StationName      = 9 ;   // 站点名称
  string  StationCode      = 10 ;  // 站点编号
  int64   CreatedAt        = 11 ;  // 创建时间
  int64   UpdatedAt        = 12 ;  // 更新时间
}