syntax = "proto3";

package oet.scs.srv.public;

service User {
  rpc GetMainUserById (GetMainUserByIdRequest) returns (MainUser) {};
  rpc GetMainUserByName (GetMainUserByNameRequest) returns (MainUser) {};
  rpc GetMainUserByPhone (GetMainUserByPhoneRequest) returns (MainUser) {};
  rpc EncodeAuth (EncodeAuthRequest) returns (EncodeAuthResponse) {};
  rpc ListMainUser(ListMainUserRequest) returns (ListMainUserResponse) {}      //用户列表(基础信息、岗位、角色、权限)
  rpc ListMainUserByCorporationId(ListMainUserByCorporationIdRequest) returns (ListMainUserByCorporationIdResponse) {} //for left tree

}

message GetMainUserByIdRequest {
  int64 Id = 1;
  //v2.0
  int64 ModuleType = 2; //模块类型, 0x0:全部, 0x01:运力云, 0x02:安全云, 0x04:监管云, 0x08:场站云, 0x10:调度云, 0x20:小程序云
}

message GetMainUserByPhoneRequest {
  string Phone = 1;
  int64 CorporationId = 2;
}

message GetMainUserByNameRequest {
  string Username = 1;
}

message MainUser {
  int64 Id = 1;
  string Username = 2;
  string Password = 3;
  string Nickname = 4;
  int64 CorporationId = 5;
  bool Status = 6;
  string BindIp = 7;
  string Remark = 8;
  int64 AddedBy = 9;
  bool IsAdmin = 10;
  string LastLoginIp = 11;
  int64 LastLoginAt = 12;
  int64 ActiveAt = 13;
  int64 ExpiredAt = 14;
  int64 CreatedAt = 15;
  int64 UpdatedAt = 16;
  repeated int64 Roles = 17;
  repeated int64 Permissions = 18;
  repeated int64 AlarmTypes = 19;
  repeated string ApiPermissions = 20;
  repeated string RoleNames = 21;
  int64 UserType = 22;  //1单机构  2多机构
  repeated int64 CorporationIds = 23;
  repeated int64 ModuleTypes = 24;
  repeated string Corporations = 25;//对应repeated int64 CorporationIds = 23;
  string Corporation = 26; //对应int64 CorporationId =5;
  string Phone = 27;
  int64 StaffId = 28;
  int64 ModuleType = 29;
}

message EncodeAuthRequest {
  string Username = 1;      //用户账号密码登录时，Username不能为空, 其他模式登录时必须为空
  string Password = 2;      //用户账号密码登录时，Password不能为空, 其他模式登录时必须为空
  string ClientIp = 3;      //浏览器端用户的ip
  string WxSiteAppId = 4;   //登录应用，1)微信网站应用的AppId; 2)固定为QrScan，表示自定义扫码登录模式; 3)固定为DingTalk，表示钉钉授权登录; 4)固定为DingGov，表示浙政钉授权登录
  string WxSiteCode = 5;    //1)微信授权临时票据， 2)或者为自定义扫码登录对应的用户临时票据Uuid，3)或者为用户钉钉手机号，4)或者为用户浙政钉手机号
  int64  ModuleType = 6;    //客户端平台模块(运力云: 0x01, 安全云: 0x02, 监管云: 0x04, 场站云: 0x08, 调度云: 0x10, 小程序云: 0x20, 融合云: 0x40, 电子站牌云: 0x80, 会员管理云: 0x100, 有轨电车云: 0x200, ERP云: 0x800)
  int64 RepeatLogin = 7;    //是否支持重复登录，默认0: 不允许重复登陆, 1: 允许重复登陆
  int64 CorporationId = 8;  //WxSiteAppId=DingTalk, WxSiteAppId=DingGov时必填，归属的根机构Id
}

message EncodeAuthResponse {
  string AuthId = 1;
  int64 ExpireTime = 2;
  string Nickname = 3;
  int64 CorporationId = 4;
  bool Status = 5;
  string BindIp = 6;
  string Remark = 7;
  int64 AddedBy = 8;
  bool IsAdmin = 9;
  string LastLoginIp = 10;
  int64 LastLoginAt = 11;
  int64 ActiveAt = 12;
  int64 ExpiredAt = 13;
  int64 CreatedAt = 14;
  int64 UpdatedAt = 15;
  repeated int64 Roles = 16;
  repeated int64 Permissions = 17;
  repeated int64 AlarmTypes = 18;
  repeated string ApiPermissions = 19;
  int64 UserId = 20;
  string UnionId = 21;
  int64 ModuleType = 22; //该用户拥有的平台模块
  int64 UserType = 23;   //用户类型, 0x01为公交用户类型(归属唯一机构), 0x02为跨多个一级机构用户类型
  int64 AuthStatus = 24;
  string Phone = 25;
  string Username = 26; //用户账号
  bool IsUseOrg = 27;   //是否启用组织架构, 默认false: 不启用, true: 启用
  repeated int64 WorkPostIds = 28; //ERP系统中的岗位数据库Id，当IsUseOrg=true时，跟第16位的字段Roles，互相对应
}

message ListMainUserRequest {
  int64 CorporationId = 1;
  bool IsShowSonCorporationUser = 2;
  string Username = 3;
  int64 Status = 4;
  int64 Offset = 5;
  int64 Limit = 6;
  string Order = 7;
  string BindIp = 8;
  string Nickname = 9;
  string Phone = 10;
}

message ListMainUserResponse {
  int64 TotalCount = 1;
  repeated MainUser Items = 2;
}

message BriefMainUser {
  int64 Id                =1;
  string Username         =2;
  string Nickname         =3;
}

message ListMainUserByCorporationIdRequest {
  int64 CorporationId = 1;
}

message ListMainUserByCorporationIdResponse {
  repeated BriefMainUser Items = 2;
}