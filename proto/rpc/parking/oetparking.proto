syntax = "proto3";

package oet.scs.srv.app;

service OetParking {
    rpc GetListWithCorpId (GetListWithCorpIdRequest) returns (GetListWithCorpIdResponse) {
    }
    rpc GetParkingWithCode (GetParkingWithCodeRequest) returns (GetParkingWithCodeResponse) {
    }
    rpc GetParkingWithId (GetParkingWithIdRequest) returns (GetParkingWithIdResponse) {
    }
    rpc GetBestParkingInfoWithGps (GetBestParkingInfoWithGpsRequest) returns (GetBestParkingInfoWithGpsResponse) {
    }
}

message CenterPoint {
    double Longitude = 1;  // 经度, 原始坐标
    double Latitude = 2;   // 纬度, 原始坐标
}

message OetParkingGpsPoint{
    double Longitude = 1;  // 经度, 原始坐标
    double Latitude = 2;   // 纬度, 原始坐标
}

message OetParkingItem {
    int64 Id = 1;
    int64 CorporationId = 2;    // 机构id
    string Code = 3;            // 场站编号
    string Name = 4;            // 场站名
    int64 ParkingGroupId = 5;   // 场站分组id
    int64 ParkingType = 6;      // 场站类型, 1:停车场;2:加油站;3:维修场;4:包车点
    int64 ShapeType = 7;        // 图形类型，0:Multi, 1:Circle, 2:Rectange
    int64 Capacity = 8;         // 泊位数
    int64 Area = 9;             // 占地面积, 平方米
    int64 CreatedAt = 10;
    int64 UpdatedAt = 11;
    string ParkingGroup = 12;   // 分组场站名称
    CenterPoint Center = 13;    // 场站中心点
    string CityCode = 14;       // 城市代码,国家城市区域代码330101
    string District = 15;       // 区域名称
    repeated OetParkingGpsPoint Coordinates = 16; //  场站，图形类型，0:多边形，图形坐标集
    int64 ChargingBolt = 17;       // 充电栓数量
    int64 ChargingPlace = 18;      // 充电位数量
    int64 ParkingQulity = 29;      // 场站性质, 1-租用；2-划拨；3-占道
    CenterPoint Spots = 20;        // 图形类型为1:Circle(圆形)的中心点
    int64 Radius = 21;             // 图形类型为1:Circle(圆形)的半径，单位：米
}

message GetListWithCorpIdRequest {
    int64 CorporationId = 1;
    int64 ParkingType = 2;          // 场站类型, 1:停车场;2:加油站;3:维修场;4:包车点
    string Name = 3;                // 场站名,模糊搜索
    repeated int64 ParkingIds = 4;  // 场站id，空值全部
    int64 Offset = 5;               // 偏移
    int64 Limit = 6;                // 数量，数量为0读取偏移后的全部表
    string Order = 7;               // 排序，升序：asc；降序：desc
    repeated int64 NotParkingIds = 8;  // 场站id
}
message GetListWithCorpIdResponse {
    repeated OetParkingItem Items = 1;
    int64 TotalCount = 2;               // 列表总数
}

message GetParkingWithCodeRequest {
    int64 TopCorporationId = 1;
    string Code = 2;
}

message GetParkingWithCodeResponse {
    OetParkingItem Item = 1;
}

message GetParkingWithIdRequest {
    int64 ParkingId = 1;
}

message GetParkingWithIdResponse {
    OetParkingItem Item = 1;
}

message GetBestParkingInfoWithGpsRequest {
    int64 TopCorporationId = 1;
    int64 LineId = 2;
    int64 VehicleId = 3;
    int64 Longitude = 4; //GPS原始经纬度, 10的6次方
    int64 Latitude = 5;
    int64 ParkingIdA = 6; //ParkingIdA为GPS原始经纬度优先要匹配的场站, 当ParkingIdA > 0时，优先匹配此包车点ParkingIdA内; 匹配不到时／ParkingIdA＝0，再去任意包车点匹配得到ParkingIdA'
    int64 ParkingIdB = 7; //ParkingIdB为上一个场站, 当且仅当ParkingIdB > 0时， 需要匹配定额里程Distance(ParkingIdB, ParkingIdC) 或者 Distance(ParkingIdB, ParkingIdC'), 其他ParkingIdC/ParkingIdC'为经纬度匹配出的场站
}

message GetBestParkingInfoWithGpsResponse {
    string Code = 1; //if "0": success, else failed
    string Msg = 2;
    OetParkingItem Item = 3;
    int64 PlanMileage = 4; //定额里程, 单位: 米
}