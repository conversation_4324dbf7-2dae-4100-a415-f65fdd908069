syntax = "proto3";

package oet.scs.api.iss;

service Lineroadform {
    // rpc
    rpc GetLineLineRoadForms (GetLineLineRoadFormsRequest) returns (GetLineLineRoadFormsResponse) {
    }
    rpc GetVehicleLineRoadForms (GetVehicleLineRoadFormsRequest) returns (GetVehicleLineRoadFormsResponse) {
    }
    rpc GetDriverLineRoadForms (GetDriverLineRoadFormsRequest) returns (GetDriverLineRoadFormsResponse) {
    }
    rpc GetLineRoadForms (GetLineRoadFormsRequest) returns (GetLineRoadFormsResponse) {
    }
    // 查询车辆最早发车时间-最晚到达时间
    rpc GetVehicleRoadFormBriefWithTopCorpId(GetVehicleRoadFormBriefWithTopCorpIdRequest) returns (GetVehicleRoadFormBriefWithTopCorpIdResponse) {}

}

message LineRoadFormItem {
    int64 Id = 1;
    int64 TopCorporationId = 2; // 根机构 Id
    int64 CorporationId = 3; // 分机构 Id

    int64 LineId = 4; // 线路 Id
    string Line = 5; // 线路名称

    int64 DriverId = 6; // 司机名
    string Driver = 7;
    int64 VehicleId = 8; // 执行车辆 Id
    string License = 9; // 执行车辆车牌
    int64 ServiceType = 10; // 运营类型, 1:载客, 2:空驶

    int64 Status = 11; // 路单提交状态: 0x01:司机未提交, 0x02:司机已提交
    int64 RunStatus = 12; // 路单执行状态: 0x01:任务未开始, 0x02: 任务执行中(正常), 0x04: 任务执行中(异常), 0x08: 任务结束(正常, 终端自动匹配), 0x10: 任务结束(异常)
    int64 CheckStatus = 13; // 路单检查状态: 0x01:未处理, 0x02:已处理
    int64 DataFrom = 14; // 路单来源: 1:终端事件上报, 2:司机创建, 3:系统补录, 4:排班创建

    int64 PlanDepartAt = 15; // 预计划发车时间, 秒
    int64 PlanArriveAt = 16; // 预计划到达时间, 秒
    int64 ActualDepartAt = 17; // 实际划发车时间
    int64 ActualArriveAt = 18; // 实际划到达时间
    int64 PlanMileage = 19; // 定额里程，单位:米
    int64 ActualMileage = 20; // GPS 里程，单位:米
    int64 UnLoadMileage = 21;
    int64 Sheet = 22; //上下行 1上行 2下行  3环形
    int64 ClassIdx = 23; // 班次
}

message GetLineLineRoadFormsRequest {
    int64 LineId = 1; // 线路 Id
    int64 CorporationId = 2; // 分机构 Id
    int64 StartAt = 3; // 开始时间, unix timestamp
    int64 EndAt = 4; // 结束时间, unix timestamp
    int64 Offset = 5; // 从第几行开始查询
    int64 Limit = 6; // 查询数据的个数
    string Order = 7; // 查询结果的排序方式，desc(降序)/asc(升序)
}

message GetLineLineRoadFormsResponse {
    repeated LineRoadFormItem Items = 1;
    int64 TotalCount = 2;
}

message GetLineRoadFormsRequest {
    int64 DriverId = 1; // 司机 Id
    int64 VehicleId = 2;
    int64 StartAt = 3; // 开始时间, unix timestamp
    int64 EndAt = 4; // 结束时间, unix timestamp
    int64 Offset = 5; // 从第几行开始查询
    int64 Limit = 6; // 查询数据的个数
    string Order = 7; // 查询结果的排序方式，desc(降序)/asc(升序)
}

message GetLineRoadFormsResponse {
    repeated LineRoadFormItem Items = 1;
    int64 TotalCount = 2;
}

message GetVehicleLineRoadFormsRequest {
    int64 VehicleId = 1; // 车辆 Id
    int64 StartAt = 2; // 开始时间, unix timestamp
    int64 EndAt = 3; // 结束时间, unix timestamp
    int64 Offset = 4; // 从第几行开始查询
    int64 Limit = 5; // 查询数据的个数
    string Order = 6; // 查询结果的排序方式，desc(降序)/asc(升序)
}

message GetVehicleLineRoadFormsResponse {
    repeated LineRoadFormItem Items = 1;
    int64 TotalCount = 2;
}

message GetDriverLineRoadFormsRequest {
    int64 DriverId = 1; // 司机 Id
    int64 StartAt = 2; // 开始时间, unix timestamp
    int64 EndAt = 3; // 结束时间, unix timestamp
    int64 Offset = 4; // 从第几行开始查询
    int64 Limit = 5; // 查询数据的个数
    string Order = 6; // 查询结果的排序方式，desc(降序)/asc(升序)
}

message GetDriverLineRoadFormsResponse {
    repeated LineRoadFormItem Items = 1;
    int64 TotalCount = 2;
}

message GetVehicleRoadFormBriefWithTopCorpIdRequest {
    int64 TopCorporationId  = 1;  // 顶级机构
    int64 StartAt           = 2;  // 某天零点
    int64 EndAt             = 3;  // 某天23:59:59
}

message  GetVehicleRoadFormBriefWithTopCorpIdResponse {
    string Code = 1;
    string Msg = 2;
    repeated GetVehicleRoadFormBriefWithTopCorpIdItem items = 3;
}

message  GetVehicleRoadFormBriefWithTopCorpIdItem {
    int64 VehicleId      = 1; // 车辆ID
    int64 FirstDepartAt  = 2; // 首班发车时间
    int64 LastArrivedAt  = 3; // 末班到达时间
}