syntax = "proto3";

package oet_scs_api_erp_v2;

service ErpWorkpost {
  //机构岗位列表
  rpc ListByCorporationIds(ListByCorporationIdsRequest) returns (ListByCorporationIdsResponse) {}
  // 根据员工id获取erp岗位名
  rpc GetErpWorkPostsByStaffIds (GetErpWorkPostsByStaffIdsRequest) returns (GetErpWorkPostsByStaffIdResponse) {}
}

message ListByCorporationIdsRequest{
  repeated int64 CorporationIds = 1;       //机构ID数组
}

message ListByCorporationIdsResponse{
  repeated WorkPostItem Items = 1;         //岗位列表
  int64 TotalCount = 2;                    //岗位总数
}

message WorkPostItem {
  int64 Id = 1;                            //岗位ID
  int64 CorporationId = 2;                 //岗位所属机构
  int64 Type = 3;                          //岗位类型 1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他
  string Name = 4;                         //岗位名称
  int64 Attr = 5;                          //岗位性质 1-部门领导（正职）,2-部门领导（副职）,3-普通员工
  int64  Status = 6;                       //岗位状态 1-有效  2-无效
  string Code = 7;                         //岗位编号
}

message GetErpWorkPostsByStaffIdsRequest {
  repeated int64 StaffIds = 1; // 员工id 切片
}

// 岗位列表
message ErpWorkPost {
  int64 StaffId = 1;             //员工数据库id
  repeated int64 WorkPostIds = 2;         //岗位id
  repeated string WorkPostNames = 3;      //岗位名
}

message GetErpWorkPostsByStaffIdResponse {
  repeated ErpWorkPost Items = 1; // 每个 员工id 对应的岗位列表
}


service ErpLeave {
  //请假列表
  rpc GetErpStaffLeaveInfo(GetErpStaffLeaveInfoRequest) returns (GetErpStaffLeaveInfoResponse) {}
  rpc AddErpStaffLeave(AddStaffLeaveRequest) returns (AddStaffLeaveResponse) {}
}

message GetErpStaffLeaveInfoRequest{
  int64 StaffId = 1;       //人员ID
}

message GetErpStaffLeaveInfoResponse{
  int64 StaffId = 1;                    //人员ID
  string StaffName = 2;                 //人员姓名
  int64 HasAnnualLeaveDay = 3;          //剩余年休假天数
  repeated ApplyLeaveRecord Items = 4;  //历史请假记录
  string Code = 5; //错误码
  string Msg = 6;//消息
}

message AddStaffLeaveRequest {
  int64 StaffId = 1;                    //人员ID
  int64 LeaveType = 2;  //请假类型 1年休假 2调休假 3事假 4病假 5婚假 6产假 7陪产假 8丧假 9疗休养 10工伤 11车队值班（机动）12待岗 13停岗
  int64 StartAt = 3;   // 请假开始时间
  int64 EndAt = 4;  // 请假结束时间
  string LeaveReason = 5;  // 请假原因
}

message AddStaffLeaveResponse {
  string Code = 1; //错误码
  string Msg = 2;//消息
}

message ApplyLeaveRecord {
  int64 Id = 1;             //ID
  int64 StartAt = 2;        //开始时间
  int64 EndAt = 3;          //结束时间
  string CreatedAt = 4;     //提交时间
  int64 LeaveType = 5;      //请假类型 1年休假 2调休假 3事假 4病假 5婚假 6产假 7陪产假 8丧假 9疗休养 10工伤 11车队值班（机动）12待岗 13停岗
  int64 ApplyStatus = 6;   // 审批状态 1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃
  string LeaveReason = 7;  // 请假原因
  int64 DayCount = 8; //请假天数 单位：天数*10
}

service ErpOperation {
  //司机运营数据明细
  rpc GetErpDriverOperationInfo(GetErpDriverOperationInfoRequest) returns (GetErpDriverOperationInfoResponse) {}
  //通知行程状态
  rpc ConfirmCharteredOrderTrip(ConfirmCharteredOrderTripRequest) returns (ConfirmCharteredOrderTripResponse) {}
}

message ConfirmCharteredOrderTripRequest {
  int64 CharteredOrderTripId = 1; //行程ID
  int64 Status = 2; //状态 1-确认派车
}

message ConfirmCharteredOrderTripResponse {
  string Code = 1; //错误码
  string Msg = 2;//消息
}

message GetErpDriverOperationInfoRequest{
  int64 StaffId = 1;       //人员ID(司机ID)
  int64 StartAt = 2;  //开始时间
  int64 EndAt = 3; //结束时间
}

message GetErpDriverOperationInfoResponse{
  repeated DriverOperationReport Items = 1;  //司机运营记录
  string Code = 2; //错误码
  string Msg = 3;//消息
}

message DriverOperationReport {
  int64 Id = 1; //唯一标识
  int64 WorkDayCount = 2; //司机出勤天数 单位：天数*10
  int64 FullDoneCircle = 3; //全程实际完成圈次 单位：圈次*10
  int64 CircleMileage = 4; //圈次公里 单位：米
  int64 StopWorkRatedMileage = 5; //停运核定公里 单位：米
  int64 FullInOutDepotMileage = 6; //全程进出场公里 单位：米
  int64 FullAssistantMileage = 7; //全程辅助公里 单位：米
  int64 CharterBusMileage = 8; //包车公里 单位：米
  int64 TotalMileage = 9; //合计公里 单位：米
  int64 TotalWorkTimeLength = 10; //合计岗上时长 单位：秒
  int64 TotalNotWorkTimeLength = 11; //合计岗下时长 单位：秒
  int64 CheckStatus = 12; //是否确认  1是 2否
  int64 ReportAt = 13; // 时间蹉
}

service ErpSafety {
  //司机违章记录
  rpc GetErpDriverViolationRecord(GetErpDriverViolationRecordRequest) returns (GetErpDriverViolationRecordResponse) {}
  //司机事故记录
  rpc GetErpDriverAccidentRecord(GetErpDriverAccidentRecordRequest) returns (GetErpDriverAccidentRecordResponse) {}
}

message GetErpDriverViolationRecordRequest{
  int64 StaffId = 1;       //人员ID(司机ID)
  int64 StartAt = 2;  //开始时间
  int64 EndAt = 3; //结束时间
}

message GetErpDriverViolationRecordResponse{
  repeated DriverViolationRecord Items = 1;  //司机违章记录
  string Code = 2; //错误码
  string Msg = 3;//消息
}

message DriverViolationRecord {
  int64 Id = 1; //唯一标识
  int64 ReportAt = 2; //违规时间
  string PlaceText = 3; //违规地点
  repeated ViolationStandard Standards = 4; //执行标准
  int64  Score = 5; //扣分分值  1/100分 即 1分 记作 100
  string Content = 6; //描述
}

message ViolationStandard {
  string Code = 1; //执行标准号
  string ClassName = 2; //规范类别
  string CategoryName = 3; //违规类型
}

message GetErpDriverAccidentRecordRequest{
  int64 StaffId = 1;   //人员ID(司机ID)
  int64 StartAt = 2;  //开始时间
  int64 EndAt = 3; //结束时间
}

message GetErpDriverAccidentRecordResponse{
  repeated DriverAccidentRecord Items = 1;  //司机事故记录
  string Code = 2; //错误码
  string Msg = 3;//消息
}

//司机事故记录
message DriverAccidentRecord {
  int64 Id = 1; //唯一标识
  int64 HappenAt = 2; //事故发生时间
  string License = 3; //车牌号
  int64 LiabilityType = 4; //责任认定 0未知,1-全责,2-主责,3-同责,4-次责,5-无责 6待定
  int64 VehicleBrokenCate = 5;//车损类别 1-刮擦车损、2-倒车车损、3-追尾车损、4-侧翻车损、5-玻璃自爆与破损、6-场站刮擦（物业）、9-无车损
  int64 PeopleHurtCate = 6; //人伤类别 1-无人伤、2-车内伤、3-车外伤、4-刮擦导致人伤、5-倒车导致人伤、6-追尾导致人伤、7-侧翻导致人伤
  int64 Grade = 7; //事故级别 1-轻微事故,2-ICU以上事故,3-留院观察,4-住院治疗
}


service ErpDict {
  rpc GetDictList(GetDictListRequest) returns (GetDictListResponse) {}
}

message GetDictListRequest {
  int64 TopCorporationId = 1;  // 机构ID
  int64 DictKey = 2; // 字典类型 0:全部 1:信访工单-信访来源 2:信访工单-信访分类 11:设备报修工单-供货方/厂家/协办单位 13:设备报修工单-设备大类 14:设备报修工单-设备种类    101 广告牌类型
  int64 ParentId = 3;
}

message GetDictListResponse {
  repeated  DictItem Items = 1;
  string Code = 2; //错误码
  string Msg = 3;//消息
}
message DictItem {
  int64 Id = 1; // 字典Id
  int64 DictType = 2; // 字典类型 0:全部 1:信访工单-信访来源 2:信访工单-信访分类 11:设备报修工单-供货方/厂家/协办单位 13:设备报修工单-设备大类 14:设备报修工单-设备种类    101 广告牌类型
  string DictKey = 3;  // 字典内容
  string DictValue = 4; // 字典值
  int64 ParentId = 5;
}


service ErpWorkOrder {
  //获取设备列表
  rpc GetDeviceList(GetDeviceListRequest) returns (GetDeviceListResponse) {}
  //上报设备报修工单
  rpc ReportDeviceWorkOrder(ReportDeviceWorkOrderRequest) returns (ReportDeviceWorkOrderResponse) {}
  //获取工单审批状态
  rpc GetWorkOrderApplyStatus(GetWorkOrderApplyStatusRequest) returns (GetWorkOrderApplyStatusResponse) {}
}

message GetDeviceListRequest {
  int64 VehicleId = 1;  // 车辆ID
  int64 DeviceClassDictId = 2; //设备大类ID
  int64 DeviceCategoryDictId = 3; //设备种类ID
}

message GetDeviceListResponse {
  repeated  DeviceItem Items = 1;
  string Code = 2; //错误码
  string Msg = 3;//消息
}

message DeviceItem {
  int64 Id = 1; // 设备Id
  string Code = 2; //设备编号
  int64 ExpireAt = 3; //过保日期 秒时间戳
  int64 HandlerFactoryId = 4; //保修方ID
  string HandlerFactoryKey = 5;//保修方名称
  int64 MaintainerFactoryId = 6; //过保维修方ID
  string MaintainerFactoryKey = 7;//过保维修方名称
  string FactoryCode = 8; //厂家编号
}

message ReportDeviceWorkOrderRequest {
  int64 VehicleId = 1;  // 车辆ID
  int64 DeviceId = 2; //设备ID
  string Content = 3; //故障描述
  int64 RepairMethod = 4; //报修方式 0未知  1车队查看  2直接报修
  repeated WorkOrderFileItem Files = 5; //附件列表
  int64 UserId = 6; //登录账号ID
}

message WorkOrderFileItem {
  string FileName = 1; //源文件名（含后缀）
  string FileUrl = 2; //文件完整访问地址
}

message ReportDeviceWorkOrderResponse {
  WorkOrderInfo Item = 1;
  string Code = 2; //错误码
  string Msg = 3;//消息
}

message WorkOrderInfo {
  int64 Id = 1; //工单ID
  string Code = 2; //工单编号
  int64 ApplyStatus = 3; //审批状态 1进行中 2已完成 3驳回 4撤回  5废弃
}

message GetWorkOrderApplyStatusRequest {
  int64 Id = 1;//工单ID
}

message GetWorkOrderApplyStatusResponse {
  WorkOrderInfo Item = 1;
  string Code = 2; //错误码
  string Msg = 3;//消息
}

//机务
service ErpMaintenance {
  //车辆调动记录
  rpc GetErpVehicleMigrationRecord(GetErpVehicleMigrationRecordRequest) returns (GetErpVehicleMigrationRecordResponse) {}
  rpc GetErpVehicleMigrationInfo(GetErpVehicleMigrationInfoRequest) returns (GetErpVehicleMigrationInfoResponse) {}
}

message GetErpVehicleMigrationRecordRequest{
  repeated int64 VehicleIds = 1;
  int64 StartUseAt = 2; //调入生效时间开始  秒时间戳
  int64 EndUseAt = 3;  //调入生效时间结束  秒时间戳
  repeated int64 ApplyStatuses = 4; //审批状态 1-申请审批中, 2-申请审批通过, 3-驳回（拒绝）, 4-撤回, 5-废弃
  int64 CorporationId = 5;
}

message GetErpVehicleMigrationRecordResponse{
  repeated VehicleMigrationRecord Items = 1; //调动审批记录
  string Code = 2; //错误码
  string Msg = 3;//消息
}

message VehicleMigrationRecord {
  int64 Id = 1;  //记录ID
  string Code = 2; //调动编号
  int64 OutCorporationId = 3; //调出机构ID
  int64 InCorporationId = 4; //调入机构ID
  int64 CreatedAtUnix = 5; //发起时间 秒时间戳
  int64 UseAtUnix = 6; //调入生效时间 秒时间戳
  int64 ApplyStatus = 7; //审批状态 1-申请审批中, 2-申请审批通过, 3-驳回（拒绝）, 4-撤回, 5-废弃
  int64 HandleAtUnix = 8; //审批时间
  int64 AcceptUserId = 9;//调入机构车辆接收人
  repeated MigrationVehicleItem Vehicles = 10; //调动记录对应的车辆列表
}

message MigrationVehicleItem {
  int64 VehicleId = 1;
  int64 InLineId = 2;
}

message GetErpVehicleMigrationInfoRequest{
  int64 Id = 1;  //记录ID
}

message GetErpVehicleMigrationInfoResponse{
  VehicleMigrationRecord Item = 1; //调动审批详情
  string Code = 2; //错误码
  string Msg = 3;//消息
}

//人资
service ErpHr {
  //人员调动记录
  rpc GetErpDriverMigrationRecord(GetErpDriverMigrationRecordRequest) returns (GetErpDriverMigrationRecordResponse) {}
  rpc GetErpDriverMigrationInfo(GetErpDriverMigrationInfoRequest) returns (GetErpDriverMigrationInfoResponse) {}
}

message GetErpDriverMigrationRecordRequest{
  repeated int64 DriverIds = 1;
  int64 StartUseAt = 2; //调入生效时间开始  秒时间戳
  int64 EndUseAt = 3;  //调入生效时间结束  秒时间戳
  repeated int64 ApplyStatuses = 4; //审批状态 1-申请审批中, 2-申请审批通过, 3-驳回（拒绝）, 4-撤回, 5-废弃
  int64 CorporationId = 5;
}

message GetErpDriverMigrationRecordResponse{
  repeated DriverMigrationRecord Items = 1; //调动审批记录
  string Code = 2; //错误码
  string Msg = 3;//消息
}

message DriverMigrationRecord {
  int64 Id = 1;  //记录ID
  string Code = 2; //调动编号
  int64 OutCorporationId = 3; //调出机构ID
  int64 InCorporationId = 4; //调入机构ID
  int64 CreatedAtUnix = 5; //发起时间 秒时间戳
  int64 UseAtUnix = 6; //调入生效时间 秒时间戳
  int64 ApplyStatus = 7; //审批状态 1-申请审批中, 2-申请审批通过, 3-驳回（拒绝）, 4-撤回, 5-废弃
  int64 HandleAtUnix = 8; //审批时间
  int64 InFleetUserId = 9;//调入机构车队长
  string Reason = 10; //调动原因
  repeated MigrationDriverItem Drivers = 11; //调动记录对应的司机列表
}

message MigrationDriverItem {
  int64 DriverId = 1;
  int64 InLineId = 2;
}

message GetErpDriverMigrationInfoRequest{
  int64 Id = 1;  //记录ID
}

message GetErpDriverMigrationInfoResponse{
  DriverMigrationRecord Item = 1; //调动审批详情
  string Code = 2; //错误码
  string Msg = 3;//消息
}