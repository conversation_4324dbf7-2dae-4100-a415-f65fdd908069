syntax = "proto3";

package oet.scs.srv.app;

service Tree {
    rpc GetList (GetListRequest) returns (GetListResponse) {
    }
    // 获取用户授权机构id
    rpc GetUserCorpIds (GetUserCorpIdsRequest) returns (GetUserCorpIdsResponse) {
    }
}

message GetListRequest {
    int64 UserId = 1;
    int64 TreeType = 2; // 左侧树类型, 1:机构;2:机构->线路;3:机构->线路->车辆、机构->车辆;4:机构->司机、机构->线路->司机;5:机构->角色
    // 6:机构->用户,7:机构->线路->车辆->通道（默认显示通道下主码流\子码流）；8:机构->场站/场馆
    // 9:机构->场站/场馆->通道 ; 10:机构->场站/场馆->通道->出入口; 11:机构->线路->行车计划、机构->行车计划
    // 12:机构->站点
    int64 CorporationId = 3; // 选择的机构Id，默认用户所属机构
    int64 LineId = 4; // 线路 Id

}

message GetListResponse {
    repeated Corporation Corporations = 1;
    repeated Line Lines = 2;
    repeated Vehicle Vehicles = 3;
    repeated Driver Drivers = 4;
}

message Corporation {
    int64 TreeTypeCount = 1; // 左侧树类型下的总数
    int64 Id = 2; // 机构 Id
    string Name = 3; // 机构名
    int64 Type = 4; //类型, 1:集团、2:公司、3:分公司、4:部门、5:车队
}

message Line {
    int64 TreeTypeCount = 1; // 左侧树类型下的总数
    int64 Id = 2; // 线路 Id
    string Name = 3; // 线路名
}
message Vehicle {
    int64 TreeTypeCount = 1; // 左侧树类型下的总数
    int64 Id = 2; // 车辆 Id
    string Code = 3; // 车辆编号
    string License = 4; // 车牌号
    string OwnEncode = 5; // 车辆自编号
    int64 MaxNum = 6; // 最大载客数
}

message Driver {
    int64 TreeTypeCount = 1; // 左侧树类型下的总数
    int64 Id = 2; // 司机 Id
    string Code = 3; // 司机编号
    string Name = 4; // 司机名
    string Phone = 5; // 联系方式
}

message GetUserCorpIdsRequest {
    int64 UserId = 1; // 用户id
}

message GetUserCorpIdsResponse {
    repeated int64 CorpIds = 1; // 用户授权的机构id
}
