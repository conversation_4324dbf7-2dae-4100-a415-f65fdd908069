syntax = "proto3";

package oet.scs.srv.app;

// 员工表其实读取的是司机表，司机表后期作为员工表使用
service OetStaff {
    // 复选获取员工列表
    rpc GetStaffsWithMultiOptions (GetStaffsWithMultiOptionsRequest) returns (GetStaffsWithMultiOptionsResponse) {
    }

    // 获取员工列表
    rpc GetStaffsWithOptions (GetStaffsWithOptionsRequest) returns (GetStaffsWithOptionsResponse) {
    }

    // 钉钉UID获取员工信息
    rpc GetStaffWithDingDingUID (GetStaffWithDingDingUIDRequest) returns (GetStaffWithDingDingUIDResponse) {
    }

    // 手机号获取员工信息
    rpc GetStaffWithPhone (GetStaffWithPhoneRequest) returns (GetStaffWithPhoneResponse) {
    }

    // 手机号数组获取员工信息
    rpc GetStaffWithPhones (GetStaffWithPhonesRequest) returns (GetStaffWithPhonesResponse) {
    }

    // 身份证号获取员工信息
    rpc GetStaffWithIdentifyId (GetStaffWithIdentifyIdRequest) returns (GetStaffWithIdentifyIdResponse) {
    }

    // 员工Id获取员工信息
    rpc GetStaffWithId (GetStaffWithIdRequest) returns (GetStaffWithIdResponse) {
    }

    // 员工Ids获取员工信息
    rpc GetStaffWithIds (GetStaffWithIdsRequest) returns (GetStaffWithIdsResponse) {
    }

    // 批量导入员工
    rpc BatchAdd (BatchAddRequest) returns (BatchAddResponse) {
    }

    // 批量创建员工
    rpc BatchCreate (BatchCreateRequest) returns (BatchCreateResponse) {
    }

    // 编辑员工
    rpc Edit (EditRequest) returns (EditResponse) {
    }

    // 通过选择机构id获取员工列表
    rpc GetStaffsWithCorpId (GetStaffsWithCorpIdRequest) returns (GetStaffsWithCorpIdResponse) {
    }

    // 通过选择机构ids获取员工列表
    rpc GetStaffsWithCorpIds (GetStaffsWithCorpIdsRequest) returns (GetStaffsWithCorpIdResponse) {
    }

    // 工号获取人员
    rpc GetStaffWithStaffId (GetStaffWithStaffIdRequest) returns (GetStaffWithStaffIdResponse) {
    }

    // 查询用户授权机构下的历史全部人员列表(用户权限)
    rpc GetHistoricalStaffsWithUserId (GetHistoricalStaffsWithUserIdRequest) returns (GetHistoricalStaffsWithUserIdResponse) {
    }
}

// 员工 erp:rw可读写，r只读。没有标注的erp都是默认只读
message OetStaffItem {
    int64 Id = 1;               // id
    int64 TopCorporationId = 2; // 根机构Id
    int64 CorporationId = 3;    // 所属机构Id
    int64 LineId = 4;           // 线路Id
    string Code = 5;            // 员工编号
    string Name = 6;            // 员工姓名
    string StaffId = 7;         // 员工工号
    string HeadImg = 8;         // 员工头像
    bool Sex = 9;               // TRUE: male; FALSE: female
    string NativePlace = 10;    // 籍贯
    string IdentifyId = 11;     // 身份证号
    string Phone = 12;          // 联系方式
    string DrvAddress = 13;     // 联系地址
    int64 RegisterTime = 14;    // 入职日期, 2006-01-02 15:04:05 (UTC时间戳，单位：秒) (erp:rw)
    string AttendanceCard = 15; // 考勤卡号
    string DrvLicense = 16;     // 驾驶证号
    int64 DrvLicenseType = 17;  // 驾驶证类型(岱山专用), 0:未知; 1:A1-大型客车; 2:A2-牵引车;
    // 3:A3-城市公交车; 4:B1-中型货车; 5:B2-大型货车;
    // 6:C1-小型汽车; 7:C2-小型自动挡汽车; 8:C3-低速载货汽车;
    // 9:D-普通三轮摩托车; 10:E-普通两轮摩托
    // 11:F-轻便摩托车; 12:M-轮式自动机械车; 13:N-无轨电车;
    // 14:P-有轨电车;
    int64 WorkingState = 18;    // 在职状态,0:未知 1-在职,2-离职,3-试用期,4-退休,5-退休返聘; （center/erp:rw）
    double Sort = 19;           // 排序

    int64 Occupation = 20;      // 岗位类型：0-司机; 1-乘务员;2-管理员;
    // 3-辅工; 4-辅岗; 5-干部;
    // 6-仓管人员; 7-安保人员; 8-修理工; 10-其他; (erp:r）
    int64 DingDingUID = 21;     // 钉钉UID (erp:r）
    string Jobs = 22;           // 所在岗位:填写文字 (erp:rw）
    string StaffCategory = 23;  // 员工类别:填写文字 (erp:rw）
    int64 BirthDate = 24;       // 出生日期 格式：2006-01-02 (UTC时间戳，单位：秒) (erp:rw）
    int64 Nation = 25;          // 民族：0-未知, 1-汉族，2-少数民族 (岱山专用，erp:rw）
    int64 Degree = 26;          // 文化程度：0-未知, 1-初中及以下，2-高中，3-本科，4-研究生及以上 (erp:rw）
    int64 PoliticsStatus = 27;  // 政治面貌: 0-未知；1-中国共产党；2-中国共产党预备党员；3-中国共产主义青年团团员；
    // 4-其他党派人士；5-群众；6-中国国民党革命委员会；
    // 7-中国民主同盟盟员；8-中国民主建国会会员；9-中国民主促进会会员；
    // 10-中国工农民党党员；11-中国致公党党员；12-九三学社社员；
    // 13-台湾民主自治同盟盟员；14-无党派民主人士；(erp:rw）
    int64 MaritalStatus = 28;   // 婚姻情况:0-未知, 1-未婚，2-已婚，3-离异，4-丧偶，999-其他 (erp:rw）
    int64 Healthy = 29;         // 健康情况:0-未知, 1-健康，2-良好，3-一般，4-慢性病，5-残疾，(erp:rw）
    int64 Fertility = 30;       // 生育情况:0-未知, 1-未育，2-已育 (erp:rw）
    int64 BloodType = 31;       // 血型:0-未知, 1-A型，2-B型，3-O型，4-AB型，5-其他 (erp:rw）
    string Friends = 32;        // 亲友：填写文字 (erp:rw）

    int64 CreatedAt = 33;       // 创建时间
    int64 UpdatedAt = 34;       // 更新时间
    string School = 35;         // 毕业院校：school of graduation (erp:rw）
    int64 LaborUnionTime = 36;  // 工会加入时间，UTC时间戳，单位：秒 (erp:rw）
    int64 Level = 37;           // 职级: 0-未知, 1—初级，2—中级，3—高级(erp:rw）

    int64 WorkTime  = 38;        // 参加工作时间，UTC时间戳，单位：秒 (erp:r）
    int64 PartyTime = 39;        // 入党时间, UTC时间戳，单位：秒 (erp:r）
    bool IsOfflapSoldier = 40;   // 是否复退转军人 0-否，1-是 (erp:r）
    int64 OfflapTime = 41;       // 复退转时间, UTC时间戳，单位：秒 (erp:r）
    string WaysToEnter  = 42;    // 进公司途径：1-在编,2-公开招聘,3-派遣,4-军转安置,5-借用,
    // 6-自主录用,7-自主招聘,8-转岗,9-竞聘上岗,10-其他 (erp:r）
    int64 RetireDate = 43;       // 退休时间, UTC时间戳，单位：秒 (erp:r）
    int64 WorkingAge = 44;       // 工龄（年）(erp:r）
    string NationStr = 45;       // 民族: 字符串(台州) (台州专用，erp:rw）
    string ContactName    = 46;  // 紧急联系人  (erp:rw）
    string ContactPhone   = 47;  // 紧急联系方式  (erp:rw）
    string StaffRelations = 48;  // 与员工关系   (erp:rw）
    string DrvLicenseTypeStr = 49;  // 驾驶证类型（台州专用）,空:未知;A1:A1-大型客车;A2:A2-牵引车;
    // A3:A3-城市公交车;B1:B1-中型货车;B2:B2-大型货车;
    // C1:C1-小型汽车;C2:C2-小型自动挡汽车;C3:C3-低速载货汽车;
    // D:D-普通三轮摩托车;E:E-普通两轮摩托
    // F:F-轻便摩托车;M:M-轮式自动机械车;N:N-无轨电车;
    // P:P-有轨电车;（支持多个","逗号分隔，A1,A2,E）
    int64 SortSeq = 50;             // 排序号
    string ExterHeadPortrait = 51;   // 外网访问员工头像, http://xxxxx/xxx.jpg
    string InnerHeadPortrait = 52;   // 内网访问员工头像, http://xxxxx/xxx.jpg
}

// 历史人员信息
message HistoryStaffItem {
    int64 Id = 1;               // id (unique(CorporationId_Id_MigrationState))
    int64 TopCorporationId = 2; // 根机构Id
    int64 CorporationId = 3;    // 所属机构Id (unique(CorporationId_Id_MigrationState))
    int64 LineId = 4;           // 线路Id
    string Code = 5;            // 员工编号
    string Name = 6;            // 员工姓名
    int64 Occupation = 20;      // 岗位类型：0-司机; 1-乘务员;2-管理员;
    // 3-辅工; 4-辅岗; 5-干部;
    // 6-仓管人员; 7-安保人员; 8-修理工; 10-其他; (erp:r）
    string CorporationName = 98; // 所属机构名称
    string LineName = 99;       // 线路名称
    int64 MigrationState = 100; // 所属机构迁移状态:0-迁入，1-迁出 (unique(CorporationId_Id_MigrationState))
}

// 员工列表过滤选项
message GetStaffsWithMultiOptionsRequest {
    repeated int64 CorporationIds = 1;  // 选择的机构Ids
    string NameOrStaffId = 2;           // 员工姓名/工号，模糊搜索，空搜索全部
    string Jobs = 3;                    // 岗位，模糊搜索，空搜索全部
    int64 Sex = 4;                      // 性别，1:男；2:女；3:全部
    string StaffCategory = 5;           // 员工类别，模糊搜索，空搜索全部
    repeated int64 WorkingStateArray = 6;   // 在职状态,0:未知 1-在职,2-离职,3-试用期,4-退休,5-退休返聘;
    repeated int64 OccupationArray = 7;     // 岗位类型：0-司机; 1-乘务员;2-管理员;
    // 3-辅工; 4-辅岗; 5-干部;
    // 6-仓管人员; 7-安保人员; 8-修理工; 10-其他;
    int64 Offset = 8;                   // 偏移
    int64 Limit = 9;                    // 数量，数量为0读取偏移后的全部表项
    string Order = 10;                  // 排序，升序：asc；降序：desc
    string IdentifyId = 11;             // 身份证号，模糊搜索，空搜索全部
    int64 BirthDateStartTime = 12;      // 出生日期查询开始日期 格式：2006-01-02 (UTC时间戳，单位：秒) [2021-02-1
    int64 BirthDateEndTime = 13;        // 出生日期查询结束日期 格式：2006-01-02 (UTC时间戳，单位：秒) 2022-02-1) 开始时间和结束时间相等时搜索全部
    int64 RegisterTimeStartTime = 14;   // 入职查询开始日期,当前凌晨(UTC时间戳，单位：秒), [2021-02-1
    int64 RegisterTimeEndTime = 15;     // 入职查询结束日期，第二天凌晨(UTC时间戳，单位：秒),2021-02-2) 开始时间和结束时间相等时搜索全部
    repeated int64 PoliticsStatusArray = 16; // 政治面貌: 0-未知；1-中国共产党；2-中国共产党预备党员；3-中国共产主义青年团团员；
    // 4-其他党派人士；5-群众；6-中国国民党革命委员会；
    // 7-中国民主同盟盟员；8-中国民主建国会会员；9-中国民主促进会会员；
    // 10-中国工农民党党员；11-中国致公党党员；12-九三学社社员；
    // 13-台湾民主自治同盟盟员；14-无党派民主人士；
    string DrvLicenseTypeStr = 17;      // 驾驶证类型, 模糊搜索，空搜索全部
    string NativePlace = 18;            // 籍贯, 模糊搜索，空搜索全部
    repeated string WaysToEnterArray  = 19;  // 进公司途径：1-在编,2-公开招聘,3-派遣,4-军转安置,5-借用,
    // 6-自主录用,7-自主招聘,8-转岗,9-竞聘上岗,10-其他
    string NationStr = 20;              // 民族: 字符串(台州) 模糊搜索, 空搜索全部
    repeated int64 HealthyArray = 21;   // 健康情况:0-未知, 1-健康，2-良好，3-一般，4-慢性病，5-残疾,
    int64 IsOfflapSoldier = 22;         // 是否复退转军人: 0-否，1-是, 999-全部
    int64 OfflapTimeStartTime = 23;     // 复退转时间开始日期,当前凌晨(UTC时间戳，单位：秒), [2021-02-1
    int64 OfflapTimeEndTime = 24;       // 复退转时间结束日期，第二天凌晨(UTC时间戳，单位：秒),2021-02-2) 开始时间和结束时间相等时搜索全部
    int64 WorkTimeStartTime = 25;       // 参加工作时间开始日期,当前凌晨(UTC时间戳，单位：秒), [2021-02-1
    int64 WorkTimeEndTime = 26;         // 参加工作时间结束日期，第二天凌晨(UTC时间戳，单位：秒),2021-02-2) 开始时间和结束时间相等时搜索全部
    int64 RetireDateStartTime = 27;     // 退休时间开始日期,当前凌晨(UTC时间戳，单位：秒), [2021-02-1
    int64 RetireDateEndTime = 28;       // 退休时间结束日期，第二天凌晨(UTC时间戳，单位：秒),2021-02-2) 开始时间和结束时间相等时搜索全部
    string SortArg = 29;                // 排序参数（与Order配合排序）：RetireDateAt-退休时间，
    string Phone = 30;                  // 手机号（模糊），
}

message GetStaffsWithMultiOptionsResponse {
    repeated OetStaffItem Items = 1;    // 员工信息列表
    int64 TotalCount = 2;               // 列表总数
}

// 员工列表过滤选项
message GetStaffsWithOptionsRequest {
    int64 TopCorporationId = 1; // 根机构Id
    string Name = 2;            // 员工姓名，模糊搜索，空搜索全部
    string Jobs = 3;            // 岗位，模糊搜索，空搜索全部
    int64 Sex = 4;              // 性别，1:男；2:女；3:全部
    string StaffCategory = 5;   // 员工类别，模糊搜索，空搜索全部
    int64 Occupation = 6;       // 岗位类型：0-司机; 1-乘务员;2-管理员;
    // 3-辅工; 4-辅岗; 5-干部;
    // 6-仓管人员; 7-安保人员; 8-修理工; 10-其他; 999-全部
    int64 Offset = 7;           // 偏移
    int64 Limit = 8;            // 数量，数量为0读取偏移后的全部表项
    string Order = 9;           // 排序，升序：asc；降序：desc
    string IdentifyId = 10;     // 身份证号，模糊搜索，空搜索全部
    int64 RegisterTimeStartTime = 11;// 入职查询开始日期,当前凌晨(UTC时间戳，单位：秒), [2021-02-1
    int64 RegisterTimeEndTime = 12;  // 入职查询结束日期，第二天凌晨(UTC时间戳，单位：秒),2021-02-2) 开始时间和结束时间相等时搜索全部
}

message GetStaffsWithOptionsResponse {
    repeated OetStaffItem Items = 1;    // 员工信息列表
    int64 TotalCount = 2;               // 列表总数
}

message GetStaffWithDingDingUIDRequest {
    int64 TopCorporationId = 1; // 根机构Id
    int64 DingDingUID = 2;      // 钉钉UID
}

message GetStaffWithDingDingUIDResponse {
    OetStaffItem Item = 1;      // 员工信息条目
}

message GetStaffWithPhoneRequest {
    int64 TopCorporationId = 1; // 根机构Id
    string Phone = 2;           // 手机号
}

message GetStaffWithPhoneResponse {
    OetStaffItem Item = 1;      // 员工信息条目
}

message GetStaffWithPhonesRequest {
    int64 TopCorporationId = 1;         // 根机构Id
    repeated string Phones = 2;         // 手机号
}

message GetStaffWithPhonesResponse {
    repeated OetStaffItem Items = 1;      // 员工信息条目
}

message GetStaffWithIdentifyIdRequest {
    int64 TopCorporationId = 1; // 根机构Id
    string IdentifyId = 2;      // 身份证号，精确查找
}

message GetStaffWithIdentifyIdResponse {
    OetStaffItem Item = 1;      // 员工信息条目
}

message GetStaffWithIdRequest {
    int64 Id = 1;               // 员工Id ，数据库自增的Id号
}

message GetStaffWithIdResponse {
    OetStaffItem Item = 1;      // 员工信息条目
}

message GetStaffWithIdsRequest {
    repeated int64 Ids = 1;               // 员工Id ，数据库自增的Id号
}

message GetStaffWithIdsResponse {
    repeated OetStaffItem Items = 1;      // 员工信息条目列表
}

message BatchAddRequest {
    repeated OetStaffItem Items = 1;    // 员工信息列表
    int64 Version  = 2;                 // ****注意了***，接口调用版本： 0-岱山，1-台州
    int64 UserId = 3;                       // 用户Id
    bool IsAdmin = 4;                       // 用户是否是超级管理员
}

message BatchAddResponse {
    string Code = 1;            // 错误码 0：成功，其他错误
    string Msg = 2;             // 错误信息
    int64 TotalCount = 3;       // 导入总条数
    int64 SuccessCount = 4;     // 导入成功条数
    int64 FailedCount = 5;      // 导入错误条数
}

message BatchCreateRequest {
    repeated OetStaffItem Items = 1;    // 员工信息列表
}

message BatchCreateResponse {
    string Code = 1;                    // 错误码 0：成功，其他错误
    string Msg = 2;                     // 错误信息
    int64 TotalCount = 3;               // 导入总条数
    int64 SuccessCount = 4;             // 导入成功条数
    int64 FailedCount = 5;              // 导入错误条数
    repeated OetStaffItem Items = 6;    // 新增的员工信息列表
}

message EditRequest {
    OetStaffItem Item = 1;      // 员工信息条目
    int64 Version  = 2;         // ****注意了***，接口调用版本： 0-岱山，1-台州
    int64 UserId = 3;                       // 用户Id
    bool IsAdmin = 4;                       // 用户是否是超级管理员
}

message EditResponse {
    string Code = 1;            // 错误码 0-正确，其他错误
    string Msg = 2;             // 错误信息
}

message GetStaffsWithCorpIdRequest{
    int64 CorporationId = 1;    // 选择的机构Id
    int64 Offset = 2;           // 偏移
    int64 Limit  = 3;           // 数量，0-读取全部
    string Order = 4;           // 排序，升序：asc；降序：desc

    string Name = 5;            // 员工姓名/拼音首字母，模糊搜索，空搜索全部
    string Jobs = 6;            // 岗位，模糊搜索，空搜索全部
    int64 Sex = 7;              // 性别，1:男；2:女；3:全部
    string StaffCategory = 8;   // 员工类别，模糊搜索，空搜索全部
    int64 Occupation = 9;       // 岗位类型：0-司机; 1-乘务员;2-管理员;
    // 3-辅工; 4-辅岗; 5-干部;
    // 6-仓管人员; 7-安保人员; 8-修理工; 10-其他; 999-全部
    string IdentifyId = 10;     // 身份证号，模糊搜索，空搜索全部
    int64 RegisterTimeStartTime = 11; // 入职查询开始日期,当前凌晨(UTC时间戳，单位：秒), [2021-02-1
    int64 RegisterTimeEndTime = 12;   // 入职查询结束日期，第二天凌晨(UTC时间戳，单位：秒),2021-02-2) 开始时间和结束时间相等时搜索全部
    bool IsShowChildNode = 13;        // 是否查询选择的机构Id以及下面的子机构，否-false，是-true
}

message GetStaffsWithCorpIdResponse{
    repeated OetStaffItem Items = 1;    // 员工信息列表
    int64 TotalCount = 2;               // 列表总数
}

message GetStaffsWithCorpIdsRequest{
    repeated int64 CorporationIds = 1;  // 选择的机构Id
    int64 Offset = 2;                   // 偏移
    int64 Limit  = 3;                   // 数量，0-读取全部
    string Order = 4;                   // 排序，升序：asc；降序：desc

    string Name = 5;                    // 员工姓名，模糊搜索，空搜索全部
    string Jobs = 6;                    // 岗位，模糊搜索，空搜索全部
    int64 Sex = 7;                      // 性别，1:男；2:女；3:全部
    string StaffCategory = 8;           // 员工类别，模糊搜索，空搜索全部
    int64 Occupation = 9;               // 岗位类型：0-司机; 1-乘务员;2-管理员;
    // 3-辅工; 4-辅岗; 5-干部;
    // 6-仓管人员; 7-安保人员; 8-修理工; 10-其他; 999-全部
    string IdentifyId = 10;             // 身份证号，模糊搜索，空搜索全部
    int64 RegisterTimeStartTime = 11;   // 入职查询开始日期,当前凌晨(UTC时间戳，单位：秒), [2021-02-1
    int64 RegisterTimeEndTime = 12;     // 入职查询结束日期，第二天凌晨(UTC时间戳，单位：秒),2021-02-2) 开始时间和结束时间相等时搜索全部
}

message GetStaffWithStaffIdRequest {
    int64 TopCorporationId = 1;
    string StaffId = 2;       // 员工工号
}

message GetStaffWithStaffIdResponse {
    OetStaffItem Item = 1;      // 员工信息条目
}

message GetHistoricalStaffsWithUserIdRequest {
    int64 UserId = 1;   // 用户id
    bool IsAdmin = 2;   // 是否是管理员，false:普通用户，true:超级管理员
    int64 QueryDateStart = 8;   // 查询开始日期 格式：2006-01-02 (UTC时间戳，单位：秒) [2021-02-1
    int64 QueryDateEnd = 9;     // 查询结束日期 格式：2006-01-02 (UTC时间戳，单位：秒) 2022-02-1) 开始时间和结束时间相等时搜索全部
}

message GetHistoricalStaffsWithUserIdResponse {
    string Code = 1;
    string Msg = 2;
    repeated HistoryStaffItem Items = 3; // 注意同一个人员id可能归属多个所属机构
}