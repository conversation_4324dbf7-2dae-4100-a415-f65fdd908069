syntax = "proto3";

package oet.scs.api.mini;

service Message {
  rpc SendUniformMsg (SendUniformMsgRequest) returns (SendUniformMsgResponse) {
  } //下发小程序和公众号统一的服务消息

  rpc SendSubscribeMessage (SendSubscribeMessageRequest) returns (SendSubscribeMessageResponse) {
  }
}

message SendUniformMsgRequest {
  int64 AppAccountId = 1;
  int64 UserType = 2; //用户类型, 0:普通用户，2:司机
  string ToUser = 3; //推送对象openid或者手机号(推荐使用openid)
  UniformMpTmpMsg MpTemplate = 4;
}

message UniformMpTmpMsg {
  string TemplateId = 1; //公众号模板id
  string URL = 2; //公众号模板消息所要跳转的url
  string PagePath = 3; //所要跳转的小程序路径，小程序的必须与公众号具有绑定关系
  repeated UniformMsgKeyValue Items = 4; //公众号模板消息的数据
}

message UniformMsgKeyValue {
  string Key = 1;
  string Value = 2;
  string Color = 3;
}

message SendUniformMsgResponse {
  string Code = 1;
  string Msg = 2;
}

message SendSubscribeMessageRequest {
  int64 AppAccountId = 1;
  string AppId  = 2;
  int64 UserType = 3; //用户类型, 0:普通用户
  string ToUser = 4; //推送对象openid或者手机号(推荐使用openid)
  int64 ToUserType = 5;// 1：openid，2：phone，3：idCardNo，4：shortNo
  string TemplateId = 6;
  string Page = 7;
  string MiniProgramState = 8;
  string Lang = 9;
  repeated SubscribeMessageData Data = 10;
}

message SubscribeMessageData {
  string Key = 1;
  string Value = 2;
}

message SendSubscribeMessageResponse {
  string Code = 1;
  string Msg = 2;
}