syntax = "proto3";

package oet.scs.srv.app;

service OetLineStation {
    rpc GetLineStationInfoWithLineId (GetLineStationInfoWithLineIdRequest) returns (GetLineStationInfoWithLineIdResponse) {
    }
    rpc GetLineStationDetailWithStationId (GetLineStationDetailWithStationIdRequest) returns (GetLineStationDetailWithStationIdResponse) {
    }
    rpc GetLineStationsWithLineId (GetLineStationsWithLineIdRequest) returns (GetLineStationsWithLineIdResponse) {
    }
    rpc GetLineStationInfosWithTopCorpId (GetLineStationInfosWithTopCorpIdRequest) returns (GetLineStationInfosWithTopCorpIdResponse) {
    }
    rpc GetLineStationInfosWithLineIds (GetLineStationInfosWithLineIdsRequest) returns (GetLineStationInfosWithLineIdsResponse) {
    }
    rpc GetStationIdsWithLineIdAndSheet (GetStationIdsWithLineIdAndSheetRequest) returns (GetStationIdsWithLineIdAndSheetResponse) {
    }
    rpc GetFirstLastStationIdWithLineIdAndSheet (GetFirstLastStationIdWithLineIdAndSheetRequest) returns (GetFirstLastStationIdWithLineIdAndSheetResponse) {
    }
    //
    rpc GetStationStopLines (GetStationStopLinesRequest) returns (GetStationStopLinesResponse) {
    }
    // 获取选择线路站点
    rpc GetOetLineStationWithLineId (GetOetLineStationWithLineIdRequest) returns (GetOetLineStationWithLineIdResponse) {
    }
}

message LineStationInfo {
    int64 LineId = 1;
    repeated OetLineStationBriefItem UpLineStations = 2;        // 上行站点列表，已按照Sequence从小到大排序
    repeated OetLineStationBriefItem DownLineStations = 3;      // 下行站点列表，已排序
    repeated OetLineStationBriefItem CircleLineStations = 4;    // 环形站点列表，已排序
}

message RealityImage  {
    int64 FileId = 1;           // 图片文件id
    string Orientation = 2;     // 照片方位，1-正面，2-左面，3-右面，4-线路牌1，5-线路牌2
    string ImageUrl = 3;        // 图片url路径
    string FileName = 4;        // 图片文件名称
}

message OetLineStationItem {
    int64 Id     = 1;                       // 线路站点 Id,主键自增
    int64 LineId  = 2;                      // 线路 Id
    string LineName = 3;                    // 名称
    int64 StationId = 4;                    // 站点Id
    string StationCode = 5;                 // 站点code
    int64  Direction = 6;                   // 站点方向，1:东向西,2:西向东,3:南向北,4:北向南
    int64 Sequence = 7;                     // 站点序号
    string StationName = 8;                 // 站点名称
    int64 FirstLast = 9;                    // 站点类型，0-中途站；1-起点站；2-终点站；3-普通采样点；4-考核点
    int64 IsNormal = 10;                    // 是否大站，1:普通站,2:大站
    int64 NextStationLength = 11;           // 距离下一个站点距离，单位：米
    double Longitude = 12;                  // 经度, 原始坐标
    double Latitude = 13;                   // 纬度, 原始坐标
    int64 Radius = 14;                      // 站点半径，单位，米
    string AdminIsTrAtIveDivision = 15;     // 行政区划，城市代码
    int64  PlatformType = 16;               // 站台类型，1-港湾式，2-直线式
    int64  BusBoardType = 17;               // 站牌类型，1-普通站牌，2-电子站牌，3-简易站牌
    int64  ElecBusBoardType = 18;           // 电子站牌类型，1-立式，2-嵌入，3-吊装
    repeated RealityImage RealityImages = 19; // 站点实景
    string StreetName = 20;                 // 街道名称
    string OwnedRoad = 21;                  // 所属道路
    int64  CoordinateSystem = 22;           // 坐标系统,1-WGS84, 2-GCJ02
    bool   IsBuiltUpArea = 23;              // 是否建成区
    string RailInterchange = 24;            // 轨交换乘站
    string InterchangeEntrance = 25;        // 换乘站入口
    int64 Sheet = 26;                       // 线路方向，1-上行；2-下行；3-环行
    int64 IsCheckSkip = 27;                 // 检测越站，1-检测，0-不检测（硬件云检测使用）
    int64 DualSequence = 28;                // 双程号（海信）
}

message OetLineStationBriefItem {
    int64 StationId = 1;         // 站点Id
    string StationCode = 2;      // 站点code(暂时没值)
    int64 Sequence = 3;          // 站点序号
    string Name = 4;             // 站点名称
    int64 FirstLast = 5;         // 站点类型，0-中途站；1-起点站；2-终点站；3-普通采样点；4-考核点
    int64 IsNormal = 6;          // 是否大站，1:普通站,2:大站
    int64 NextStationLength = 7; // 距离下一个站点距离，单位：米
    double Longitude = 8;        // 经度, 原始坐标
    double Latitude = 9;         // 纬度, 原始坐标
    int64 Radius = 10;           // 站点半径，单位，米
    int64 Sheet = 11;            // 线路方向，1-上行；2-下行；3-环行
    int64 IsCheckSkip = 12;      // 检测越站，1-检测，0-不检测（硬件云检测使用）
    int64 DualSequence = 13;     // 双程号（海信）
}

message StationStopLineItem {
    int64 Id = 1;
    int64 TopCorporationId = 2;
    string Name = 3;
    int64 LineType = 4;
}

message GetLineStationInfoWithLineIdRequest {
    int64 LineId = 1;
    int64 Except = 2; // 需要过滤的站点类型,-1 不过滤, 0-中途站, 1-起点站, 2-终点站, 3-普通采样点
}

message GetLineStationInfoWithLineIdResponse {
    string LineName = 1;
    int64 LineType = 2;         // 线路类型，1:普通线路, 2:单向环行线路；3:双向环行线路
    int64 UpParkingId = 3;      // 副场站 Id
    string UpParking = 4;       // 副场站名称
    int64 DownParkingId = 5;    // 主场站Id
    string DownParking = 6;     // 主场站名称
    repeated OetLineStationBriefItem Line1 = 7; // 上行站点列表，已按照Sequence从小到大排序, 单向环行线路线路站点放在上行线路
    repeated OetLineStationBriefItem Line2 = 8; // 下行站点列表，已排序
}

message GetLineStationDetailWithStationIdRequest {
    int64 LineId = 1;
    int64 Sheet = 2;
    int64 StationId = 3;
    int64 Sequence = 4;
}

message GetLineStationDetailWithStationIdResponse {
    OetLineStationBriefItem Item = 1;
}

message GetStationIdsWithLineIdAndSheetRequest {
    int64 LineId = 1;
    int64 Sheet = 2;
}

message GetStationIdsWithLineIdAndSheetResponse {
    repeated int64 StationIds = 1;
}

message GetFirstLastStationIdWithLineIdAndSheetRequest {
    int64 LineId = 1;
    int64 Sheet = 2;
    int64 FirstLast = 3;
}

message GetFirstLastStationIdWithLineIdAndSheetResponse {
    OetLineStationBriefItem Item = 1;
}

message GetLineStationsWithLineIdRequest {
    int64 LineId = 1;
    int64 Sheet = 2;
    int64 FilterType = 3; // 过滤类型, 不过滤:-1；过滤：0-中途站，1-起点站，2-终点站，3-普通采样点
}

message GetLineStationsWithLineIdResponse {
    repeated OetLineStationBriefItem Items = 1;
}

message GetLineStationInfosWithTopCorpIdRequest {
    int64 TopCorpId = 1;
    int64 FilterType = 2; // 过滤类型, 0:线路站点-所有, 1:线路站点-去除采样点
}

message GetLineStationInfosWithTopCorpIdResponse {
    repeated LineStationInfo Items = 1;
}

message GetLineStationInfosWithLineIdsRequest {
    repeated int64 LineIds = 1;
    int64 FilterType = 2;
}

message GetLineStationInfosWithLineIdsResponse {
    repeated LineStationInfo Items = 1;
}

message GetStationStopLinesRequest {
    int64 StationId = 1;
}

message GetStationStopLinesResponse {
    repeated StationStopLineItem Items = 1;
}

message GetOetLineStationWithLineIdRequest {
    int64 LineId = 1;     // 选择线路id
    int64 Sheet = 2;      // 线路方向，0-全部，1-上行；2-下行；3-环行
    int64 FilterType = 3; // 过滤类型, 不过滤:-1；过滤：0-中途站，1-起点站，2-终点站，3-采样点
}

message GetOetLineStationWithLineIdResponse {
    repeated OetLineStationItem Items = 4;        // 线路站点列表，已按照Sequence从小到大排序
    repeated OetLineStationItem SkipItems = 5;
}
