syntax = "proto3";

package oet.scs.srv.app.station;

service OetStation {
    rpc GetStationWithId (GetStationWithIdRequest) returns (GetStationWithIdResponse) {
    }
    rpc GetStationsWithIds (GetStationsWithIdsRequest) returns (GetStationsWithIdsResponse) {
    }
    rpc GetStationWithCode (GetStationWithCodeRequest) returns (GetStationWithCodeResponse) {
    }
    rpc GetStationNameWithId (GetStationNameWithIdRequest) returns (GetStationNameWithIdResponse) {
    }
    rpc GetStationsWithCorporationId (GetStationsWithCorporationIdRequest) returns (GetStationsWithCorporationIdResponse) {
    }
    rpc GetStationCountWithCorporationId (GetStationCountWithCorporationIdRequest) returns (GetStationCountWithCorporationIdResponse) {
    }
    // 通过过滤选项获取站点信息
    rpc GetStationsWithOption (GetStationsWithOptionRequest) returns (GetStationsWithOptionResponse) {
    }
    // 查询站点途径的线路
    rpc GetOetLinesWithIds (GetOetLinesWithIdsRequest) returns (GetOetLinesWithIdsResponse) {
    }
    // 通过过滤选项获取站点信息
    rpc GetStationsWithOption2 (GetStationsWithOption2Request) returns (GetStationsWithOption2Response) {
    }
}

message OetStationItem {
    int64       Id = 1;                         // 站点ID
    int64       CorporationId = 2;              // 根机构Id
    string      Code = 3;                       // 站点Code
    string      Name = 4;                       // 站点名称
    int64       Direction = 5;                  // 站点方向，1:东向西,2:西向东,3:南向北,4:北向南
    double      Longitude = 6;                  // 经度
    double      Latitude = 7;                   // 纬度
    int64       Capacity = 8;                   // 泊位数
    int64       Radius = 9;                     // 半径
    string      Remark = 10;                    // 备注
    int64       CreatedAt = 11;                 // 创建时间
    int64       UpdatedAt = 12;                 // 更新时间
    string      NameEN = 13;                    // 站点英文名称
    string      QrPath = 15;                    // 二维码相对地址
    string      HttpPrefixQrPath = 16;          // 二维码url地址
    int64       StationType = 17;               // 站点类型 1-起点站；2-中途站；3-终点站；
    int64       BizStatus = 18;                 // 运营状态 1:正常、2:停运、3:维修检测、4:试运行、5:临时使用、6:停用、7:临时停用、8:废弃、999:其他
    string      AdminIsTrAtIveDivision = 19;    // 行政区划，城市代码
    string      AllAround = 20;                 // 周边景点
    int64       PlatformType = 21;              // 站台类型，1-港湾式，2-直线式
    int64       BusBoardType = 22;              // 站牌类型，1-普通站牌，2-电子站牌，3-简易站牌
    int64       ElecBusBoardType = 23;          // 电子站牌类型，1-立式，2-嵌入，3-吊装
    bool        IsMonitor = 24;                 // 是否有监控
    bool        IsBusHall  = 25;                // 是否有候站厅
    bool        IsBRT = 26;                     // 是否为BRT站
    int64       BuildTime = 27;                 // 建造时间，UTC时间戳，秒
    string      Address = 28;                   // 站点地址
    repeated    RealityImage RealityImages = 29; // 站点实景
    string      StreetName = 30;                // 街道名称
    string      OwnedRoad = 31;                 // 所属道路
    string      OwnerUnit = 32;                 // 业主单位
    int64       CoordinateSystem = 33;          // 坐标系统,1-WGS84, 2-GCJ02
    bool        IsBuiltUpArea = 34;             // 是否建成区
    int64       CompletionTime = 35;            // 电子站牌建成时间,UTC时间戳，秒
    string      RailInterchange = 36;           // 轨交换乘站
    string      InterchangeEntrance = 37;       // 换乘站入口

    repeated    OetLineItem Lines = 100;       // 停靠线路
}

message RealityImage  {
    int64 FileId = 1;           // 图片文件id
    string Orientation = 2;     // 照片方位，1-正面，2-左面，3-右面，4-线路牌1，5-线路牌2
    string ImageUrl = 3;        // 图片url路径
    string FileName = 4;        // 图片文件名称
}

message OetLineItem {
    int64 LineId = 1;       // 线路id
    string LineName = 2;    // 线路名称
    int64 Sheet = 3;        // 线路方向, 1-上行；2-下行; 3-环行(暂时给单向环行线路使用)
}

message OetLineWithIdItem {
    int64 Id = 1;                     // 站点id
    repeated OetLineItem Lines = 2;   // 途径线路
}

message GetStationWithIdRequest {
    int64 StationId = 1;
}

message GetStationWithIdResponse {
    OetStationItem Item = 1;
}

message GetStationsWithIdsRequest {
    repeated int64 StationIds = 1;
}

message GetStationsWithIdsResponse {
    repeated OetStationItem Items = 1;
}

message GetStationWithCodeRequest {
    int64 TopCorporationId = 1;
    string Code = 2;
    int64 LineId = 3;
    int64 Sheet = 4;        // 线路方向，1-上行；2-下行；3-环行
    int64 Sequence = 5;     // 站序
}

message GetStationWithCodeResponse {
    OetStationItem Item = 1;
    int64 Sheet = 2;        // 线路方向，1-上行；2-下行；3-环行
    int64 FirstLast = 3;    // 站点类型，0-中途站；1-起点站；2-终点站；3-普通采样点；4-考核点
    int64 IsCheckSkip = 4;  // 检测越站， 1-检测，0-不检测
}

message GetStationNameWithIdRequest {
    int64 StationId = 1;
}

message GetStationNameWithIdResponse {
    string StationName = 1;
}

message GetStationsWithCorporationIdRequest {
    int64 CorporationId = 1;
}

message GetStationsWithCorporationIdResponse {
    string Code = 1;
    string Msg = 2;
    repeated OetStationItem Items = 3;
}

message GetStationCountWithCorporationIdRequest {
    int64 CorporationId = 1;
}

message GetStationCountWithCorporationIdResponse {
    string Code = 1;
    string Msg = 2;
    int64 TotalCount = 3;
}

message GetStationsWithOptionRequest {
    int64 CorporationId = 1;        // 根机构
    string Name = 2;                // 站点名称，模糊搜索
    repeated int64 StationIds = 3;  // 站点id，空值全部
    int64 Offset = 4;               // 偏移
    int64 Limit = 5;                // 数量，数量为0读取偏移后的全部表
    string Order = 6;               // 排序，升序：asc；降序：desc
    repeated int64 NotStationIds = 7;  // 站点id
}

message GetStationsWithOptionResponse {
    string Code = 1;
    string Msg = 2;
    repeated OetStationItem Items = 3;
    int64 TotalCount = 4;               // 列表总数
}

message GetOetLinesWithIdsRequest {
    repeated int64 StationIds = 1;
}

message GetOetLinesWithIdsResponse {
    repeated OetLineWithIdItem Items = 1;
}

message GetStationsWithOption2Request {
    int64 CorporationId = 1;        // 根机构
    repeated string AdminIsTrAtIveDivisions = 2; // 行政区划，城市代码
    string StreetName = 3;                       // 街道名称
    string Name = 4;                             // 站点名称
    repeated OetLineItem Lines  = 5;             // 线路
    repeated string OwnedRoads = 6;              // 所属道路
    int64 CompletionStartTime = 7;               // 电子站牌建成开始日期 格式：2006-01-02 (UTC时间戳，单位：秒) [2021-02-1
    int64 CompletionEndTime = 8;                 // 电子站牌建成结束日期 格式：2006-01-02 (UTC时间戳，单位：秒) 2022-02-1) 开始时间和结束时间相等时搜索全部
    repeated int64 ElecBusBoardTypes = 9;        // 电子站牌类型，1-立式，2-嵌入，3-吊装
    int64 Offset = 10;                           // 偏移
    int64 Limit = 11;                            // 数量，数量为0读取偏移后的全部表
    string Order = 12;                           // 排序，升序：asc；降序：desc
    int64 IsSkipLineStation  = 13;               // 是否读取越站线路表，0-否，1-是
    repeated int64 StationIds = 14;              // 站点ids
}

message GetStationsWithOption2Response {
    string Code = 1;
    string Msg = 2;
    repeated OetStationItem Items = 3;
    int64 TotalCount = 4;               // 站点列表总数
}