syntax = "proto3";

package oet.scs.api.ipoc;

service doorcheck {
  rpc GetResults(GetResultsRequest) returns (GetResultsResponse) {};                    // 门检列表
  rpc GetResultDetails(GetResultDetailsRequest) returns (GetResultDetailsResponse) {};  // 门检详情列表
  rpc GetWorkPosts(GetWorkPostsRequest) returns (GetWorkPostsResponse) {};
}

message GetResultsRequest {
  int64 TopCorporationId    = 1;   // 顶级机构ID
  string Code               = 2;   // 编号
  int64 CorporationId       = 3;   // 机构ID
  repeated int64 StaffIds   = 4;   // 提交人
  int64 SubmitStartAt       = 5;   // 提交开始时间
  int64 SubmitEndAt         = 6;   // 提交结束时间
  repeated int64 VehicleIds = 7;   // 车牌号
  int64 CheckType           = 8;   // 门检类型: 0:全部; 1:进场; 2:出场
  int64 Occupation          = 9;   // 岗位类型：0-司机; 1-乘务员;2-管理员; 3-辅工;  4-辅岗; 5-干部; 6-仓管人员;7-安保人员; 8-修理工 10-其他
  int64 DealStatus          = 10;  // 处理状态: 1:未处理;2:处理中;3:已处理
  int64 Offset              = 11;
  int64 Limit               = 12;
  string Order              = 13;
  int64 CheckStatus         = 14;  // 检查状态: 0:全部;1:正常;2:异常
}

message GetResultsResponse {
  repeated DoorCheckResultItem Items = 1;
  int64 TotalCount                   = 2;  // 数量
  string Code                        = 3;  // 错误编码
}

message DoorCheckResultItem {
  int64 Id                               = 1;   // 主键ID
  string Code                            = 2;   // 编号
  string License                         = 3;   // 车牌号
  string Corporation                     = 4;   // 车辆所属机构
  repeated string PlanScheduleStaffNames = 5;   // 排班司机
  string StaffName                       = 6;   // 提交人
  int64 Occupation                       = 7;   // 岗位类型：0-司机; 1-乘务员;2-管理员; 3-辅工;  4-辅岗; 5-干部; 6-仓管人员;7-安保人员; 8-修理工 10-其他
  int64 CheckType                        = 8;   // 门检类型: 1:进场; 2:出场
  int64 AbLabelCount                     = 9;   // 异常标签数
  int64 DealLabelCount                   = 10;  // 已处理的标签数
  int64 DealStatus                       = 11;  // 处理状态: 1:未处理;2:处理中;3:已处理
  int64 CheckStatus                      = 12;  // 检查状态: 1:正常;2:异常
  int64 AllowOperation                   = 13;  // 是否允许运营: 1:允许;2:不允许
  int64 CloseStatus                      = 14;  // 关闭状态: 1:未关闭;2:已关闭
  int64 CreatedAt                        = 15;  // 提交时间,时间蹉
}
message GetResultDetailsRequest {
  int64 TopCorporationId  = 1;  // 顶级机构ID
  int64 DoorCheckResultId = 2;  // 门检记录ID
  int64 CheckStatus       = 3;  // 检查状态: 0:全部;1:正常;2:异常
}

message GetResultDetailsResponse {
  repeated DoorCheckProjectResultItem Items = 1;
  DoorCheckResultItem Item                  = 2;
  string Code                               = 3;  // 错误编码
}

message DoorCheckProjectResultItem {
  int64 Id                                 = 1;  // 主键ID 注:Type=1 or 2时为0
  int64 Type                               = 2;  // 1 车辆 2 设备 99 其他(支持增删改查)
  string Name                              = 3;  // 检查类型名称
  repeated DoorCheckResultDetailItem Items = 4;
  repeated WorkPostItem WorkPostItems      = 5;
}
message DoorCheckResultDetailItem {
  int64 Id                      = 1;   // 门检记录详情ID
  string ProjectName            = 2;   // 检查项目名称
  int64 CategoryId              = 3;   // 设备种类ID
  int64 LabelId                 = 4;   // 标签ID
  string LabelName              = 5;   // 检查项目标签名称
  string Desc                   = 6;   // 描述
  repeated AttachmentItem Items = 7;   // 附件
  int64 CheckStatus             = 8;   // 检查状态: 1:正常;2:异常
  int64 DealStatus              = 9;   // 处理状态: 1:未处理;2:已处理
  int64 AllowOperation          = 10;  // 是否允许运营: 1:允许;2:不允许
  int64 CloseStatus             = 11;  // 关闭状态: 1:未关闭;2:已关闭
  string OpUserName             = 12;  // 处理人员
  int64 OpUserId                = 13;
  int64 CreatedAt               = 14;  // 创建时间
}
message AttachmentItem {
  int64 FileId = 1;  // 文件ID
  string Url   = 2;  // 文件地址
  string Path  = 3;
}

message GetWorkPostsRequest {
  int64 TopCorporationId  = 1;  // 顶级机构ID
  int64 DoorCheckResultId = 2;  // 门检记录ID
}

message GetWorkPostsResponse {
  repeated WorkPostItem Items = 1;  // 工单
  string Code                 = 2;  // 错误编码
}
message WorkPostItem {
  int64 Id                           = 1;  // 工单ID
  string Code                        = 2;  // 工单编号
  int64 DeliveryStatus               = 3;  // 交车状态: 1:确认收车;2:再次维修
  repeated WorkPostProcessItem Items = 4;  // 工单流程
  int64 ConfirmAt                    = 5;  // 确认时间
}

message WorkPostProcessItem {
  int64 Id       = 1;  // 工单ID
  int64 Status   = 2;  // 工单状态: 1:未受理;2:已受理;3:维修中;4:已竣工;5:已交车
  int64 ReportAt = 3;  // 上报时间
  string WorkShop = 4;  // 维修车间
}