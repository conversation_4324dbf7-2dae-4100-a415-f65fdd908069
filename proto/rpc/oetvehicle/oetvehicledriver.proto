syntax = "proto3";

package oet.scs.srv.app;

service VehicleDriver {
  rpc GetVehicleDriverByVehicleId (GetVehicleDriverByVehicleIdReq) returns (GetVehicleDriverByVehicleIdResp) {
  }
}

message VehicleDriverItem {
  int64 Id             =1;    // id
  int64 LineId         =2;    // 线路ID
  int64 CorporationId  =3;    // 线路所在机构ID
  string License       =4;    // 车牌号
  int64  VehicleId     =5;    // 车辆id
  string DriverName    =6;    // 驾驶员姓名
  int64  DriverId      =7;    // 驾驶员id
}

message GetVehicleDriverByVehicleIdReq {
  int64 VehicleId = 1; // 车辆id
}

message GetVehicleDriverByVehicleIdResp {
  repeated VehicleDriverItem Items = 1;
}
