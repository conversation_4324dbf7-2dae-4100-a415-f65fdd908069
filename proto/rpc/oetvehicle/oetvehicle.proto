syntax = "proto3";

package oet.scs.srv.app;

service OetVehicle {
    rpc GetVehicles (GetVehiclesRequest) returns (GetVehiclesResponse) {
    }
    rpc GetVehicleWithId (GetVehicleWithIdRequest) returns (GetVehicleWithIdResponse) {
    }
    rpc GetVehicleWithLicense (GetVehicleWithLicenseRequest) returns (GetVehicleWithLicenseResponse) {
    }
    rpc GetVehicleWithDeviceSN (GetVehicleWithDeviceSNRequest) returns (GetVehicleWithDeviceSNResponse) {
    }
    rpc GetVehiclesWithDeviceSNs (GetVehiclesWithDeviceSNsRequest) returns (GetVehiclesWithDeviceSNsResponse) {
    }
    rpc GetVehiclesWithIds (GetVehiclesWithIdsRequest) returns (GetVehiclesWithIdsResponse) {
    }
    rpc GetVehicleBriefsWithLineId (GetVehicleBriefsWithLineIdRequest) returns (GetVehicleBriefsWithLineIdResponse) {
    }
    rpc GetVehicleBriefsWithVehicleIds (GetVehicleBriefsWithVehicleIdsRequest) returns (GetVehicleBriefsWithVehicleIdsResponse) {
    }
    rpc GetVehiclesWithTopCorporationId (GetVehiclesWithTopCorporationIdRequest) returns (GetVehiclesWithTopCorporationIdResponse) {
    }
    rpc GetVehicleCountWithTopCorporationId (GetVehicleCountWithTopCorporationIdRequest) returns (GetVehicleCountWithTopCorporationIdResponse) {
    }

    rpc GetVehiclesWithUserId (GetVehiclesWithUserIdRequest) returns (GetVehiclesWithUserIdResponse) {
    }

    // 根机构下车牌号模糊搜索车辆
    rpc GetVehiclesWithLicenseOption (GetVehiclesWithLicenseOptionRequest) returns (GetVehiclesWithLicenseOptionResponse) {
    }

    // 根机构下根据车辆ID查询车辆信息
    rpc GetVehicleWithCode (GetVehicleWithCodeRequest) returns (GetVehicleWithCodeResponse) {
    }

    // 查询机构下的车辆（用户权限）
    rpc GetVehiclesWithOption (GetVehiclesWithOptionRequest) returns (GetVehiclesWithOptionResponse) {
    }

    // 查询多子机构下的车辆（用户权限）
    rpc GetVehiclesWithOptionCorpIds (GetVehiclesWithOptionCorpIdsRequest) returns (GetVehiclesWithOptionResponse) {
    }

    // 查询车辆
    rpc GetVehiclesAndModelWithOption (GetVehiclesAndModelWithOptionRequest) returns (GetVehiclesAndModelWithOptionResponse) {
    }

    // 批量迁移车辆
    rpc Move (MoveRequest) returns (MoveResponse) {
    }

    // 获取车辆调动记录
    rpc GetVehicleRecords (GetVehicleRecordsRequest) returns (GetVehicleRecordsResponse) {
    }

    // 查询用户授权机构下的历史全部车辆列表(用户权限)
    rpc GetHistoricalVehiclesWithUserId (GetHistoricalVehiclesWithUserIdRequest) returns (GetHistoricalVehiclesWithUserIdResponse) {
    }
}

message GetVehiclesWithOptionRequest {
    int64 CorporationId = 1;            // 选中的机构id
    string License = 2;                 // 车牌号，模糊搜索
    string VehicleType = 3;             // 车辆型号 oet_vehicle_types(Model)，模糊搜索
    int64 LicenseStartDate = 4;         // 上牌日期开始，UTC时间戳，秒
    int64 LicenseEndDate = 5;           // 上牌日期结束，UTC时间戳，秒
    int64 Offset = 6;                   // 偏移
    int64 Limit = 7;                    // 数量，数量为0读取偏移后的全部表项
    string Order = 8;                   // 排序，升序：asc；降序：desc
    bool IsShowChildNode = 9;           // 是否查询子机构节点
    int64 UserId = 10;                  // 用户Id
    int64 UseStatus = 11;               // 使用状态, 0-全部，1-使用中；2-已报废
    repeated int64 LineIds = 12;        // 线路Id，空值全部
    repeated int64 VehicleIds = 13;     // 车辆Id，空值全部
    repeated int64 NotVehicleIds = 14;  // 车辆Id
}

message GetVehiclesWithOptionResponse {
    repeated OetVehicleItem Items = 1;  // 车辆信息
    int64 TotalCount = 2;               // 列表总数
}

message GetVehiclesWithOptionCorpIdsRequest {
    repeated int64 CorporationIds = 1;  // 选中的机构id
    string License = 2;                 // 车牌号，模糊搜索
    string VehicleType = 3;             // 车辆型号 oet_vehicle_types(Model)，模糊搜索
    int64 LicenseStartDate = 4;         // 上牌日期开始，UTC时间戳，秒
    int64 LicenseEndDate = 5;           // 上牌日期结束，UTC时间戳，秒
    int64 Offset = 6;                   // 偏移
    int64 Limit = 7;                    // 数量，数量为0读取偏移后的全部表项
    string Order = 8;                   // 排序，升序：asc；降序：desc
    int64 UserId = 9;                   // 用户Id
}

message GetVehiclesWithUserIdRequest {
    int64 UserId = 1;
    int64 CorporationId = 2;
    int64 LineId = 3;
    repeated int64 VehicleIds = 4;
    string License = 5;
    bool IsShowChildNode = 6;
}

message GetVehiclesWithUserIdResponse {
    repeated OetVehicleItem Items = 1;
}

// 车辆信息
message OetVehicleItem {
    int64 Id = 1;
    int64 CorporationId = 2;        // 根机构Id
    int64 SonCorporationId = 3;     // 上级机构Id, 如果未归属子机构, 则填写根机构Id, 便于查询
    string Line = 4;                // 线路名称，没值
    int64 LineId = 5;               // 所属线路Id
    string AuthLine = 6;            // 没值
    int64 AuthLineId = 7;           // 没值
    string License = 8;             // 车牌号
    string DeviceSN = 9;            // 设备号,逗号分隔
    string DeviceSNEx = 10;         // 过滤出DeviceSN中平台对应设备号
    string Code = 11;               // 车辆编号
    string OwnEncode = 12;          // 自编号
    string VehicleType = 13;        // 车辆型号 oet_vehicle_types(Model)
    int64 VehicleModal = 14;        // 燃料类型, 0:未知;1:柴油车(传统);2:汽油车(传统);3:混合动力车(新能源);
    // 4:纯电动车(新能源);5:CNG(新能源);6:LNG;7:纯电动(双路BMS);
    int64 SiteNum = 15;             // 座位数
    int64 MaxNum = 16;              // 最大载客数
    double StandArea = 17;          // 站立面积, 平方米
    int64 UpdatedAt = 18;           // 更新时间
    int64 CreatedAt = 19;           // 创建时间
    string EnduranceMiles = 20;     // 续航里程,公里，推送给支付宝,[{"start_date":"0101","end_date":"1231","miles":300}]
    int64  MaxNumGreaterRaito = 21; // 超过最大载客数百分比，大于等于0正整数，20代表20%
    bool IsWarnning = 22;           // 是否使能荷载报警

    int64 UseNature= 23;            // 使用性质*, 1-公交车；2-校车；3-公务用车；4-巴士公务车
    string VIN = 24;                // 车架号
    int64 VehicleColor = 25;        // 车牌颜色,1-黄色，2-蓝色，3-绿色
    int64 RegisterDate = 26;        // 注册日期，UTC时间戳，秒
    int64 LicenseDate = 27;         // 上牌日期，UTC时间戳，秒
    int64 PurchaseDate = 28;        // 购置日期，UTC时间戳，秒
    int64 LecenseMileage = 29;      // 上牌时里程数值，（m）
    string OperationCertNo = 30;    // 车辆运营证件号
    string EngineNo = 31;           // 发动机号
    string MotorNo = 32;            // 电机号
    bool IsBRT = 33;                // BRT线路，0-否；1-是
    bool IsAIScheduler = 34;        // 智能调度终端，0-无；1-有
    bool IsVehicleVideo = 35;       // 车载视频终端，0-无；1-有
    bool IsActiveSafety = 36;       // 主动安全设备，0-无；1-有
    bool IsAround360 = 37;          // 360环视系统，0-无；1-有
    bool IsPassenger = 38;          // 客流调查器，0-无；1-有
    bool IsICCard = 39;             // IC卡刷卡机，0-无；1-有
    bool IsDynamicDisplay = 40;     // 动态显示屏，0-无；1-有
    bool IsMobileTV = 41;           // 移动电视，0-无；1-有
    bool IsSurvival = 42;           // 逃生应急设备，0-无；1-有
    bool IsFirefighting = 43;       // 消防设备，0-无；1-有
    bool IsBarrierfree = 44;        // 无障碍设施，0-无；1-有
    int64 DiscardAge =45;           // 报废年限，整数（年）
    int64 LineSubordinate =46;      // 所属线路类型，1-城区，2-城乡，3-乡镇，4-山区
    string SerialCh = 47;           // 串口通道，232-1；232-2；232-3；485-1；485-2；
    int64 UseStatus = 48;           // 使用状态, 1-使用中；2-已报废
    string CarBodySize = 49;        // 车身尺寸,单位mm(8545*2550*3150)长*宽*高
}

// 历史车辆信息
message HistoryVehicleItem {
    int64 Id = 1;                   // id (unique(SubCorporationId_Id_MigrationState))
    int64 CorporationId = 2;        // 根机构Id
    int64 SubCorporationId = 3;     // 所属机构
    int64 LineId = 5;               // 所属线路Id
    string License = 8;             // 车牌号
    int64 UseStatus = 48;           // 使用状态, 1-使用中；2-已报废
    string SubCorporationName = 98;    // 所属机构名称
    string LineName = 99;           // 线路名称
    int64 MigrationState = 100;     // 所属机构迁移状态:0-迁入，1-迁出 (unique(SubCorporationId_Id_MigrationState))
}

message OetVehicleRecordItem {
    int64  Id                   = 1;  //  id
    int64  VehicleId            = 2;  // 车辆 Id
    string VehicleLicense       = 3;  // 车牌号
    int64  BeforeCorporationId  = 4;  // 改前机构Id
    string BeforeCorporation    = 5;  // 改前机构
    int64  BeforeLineId         = 6;  // 改前线路Id
    string BeforeLineName       = 7;  // 改前线路名称
    int64  AfterCorporationId   = 8;  // 改后机构Id
    string AfterCorporation     = 9;  // 改后机构
    int64  AfterLineId          = 10; // 改后线路Id
    string AfterLineName        = 11; // 改后线路名称
    int64  UserId               = 12; // 操作人id
    string UserName             = 13; // 操作人账号
    bool   IsAdmin              = 14; // 是否超级管理员
    int64  TransferMode         = 15; // 调动方式:1-执行方案，2-回退方案，3-车队调整，4-主数据
    string SchemeNo             = 16; // 方案编号
    int64  TransferDateAt       = 17; // 调动时间，UTC时间戳，秒
}

message OetVehicleBrief {
    int64 Id = 1;
    string License = 2; // 车牌号
    string DeviceSN = 3;
}

message GetVehiclesRequest {
    repeated int64 VehicleIds = 1;
    string License = 2;
    int64 Offset = 3;
    int64 Limit = 4;
    string Order = 5;
}

message GetVehiclesResponse {
    string Code = 1;
    string Msg = 2;
    repeated OetVehicleItem Items = 3;
}

message GetVehicleWithIdRequest {
    int64 VehicleId = 1;
}

message GetVehicleWithIdResponse {
    OetVehicleItem Item = 1;
}

message GetVehicleWithDeviceSNRequest {
    string DeviceSN = 1;
}

message GetVehicleWithDeviceSNResponse {
    OetVehicleItem Item = 1;
}

message GetVehiclesWithDeviceSNsRequest {
    repeated string DeviceSNs = 1;
    int64 ModuleType = 2;
}

message GetVehiclesWithDeviceSNsResponse {
    repeated OetVehicleItem Items = 1;
}

message GetVehiclesWithIdsRequest {
    repeated int64 Ids = 1;
}

message GetVehiclesWithIdsResponse {
    repeated OetVehicleItem Items = 1;
}

message GetVehicleBriefsWithLineIdRequest {
    int64 CorporationId = 1;
    int64 LineId = 2;
    int64 ModuleType = 3;
}

message GetVehicleBriefsWithLineIdResponse {
    repeated OetVehicleBrief Items = 1;
}

message GetVehicleBriefsWithVehicleIdsRequest {
    repeated int64 VehicleIds = 1;
    int64 ModuleType = 2;
}

message GetVehicleBriefsWithVehicleIdsResponse {
    repeated OetVehicleBrief Items = 1;
}

message GetVehiclesWithTopCorporationIdRequest {
    int64 TopCorporationId = 1;
}

message GetVehiclesWithTopCorporationIdResponse {
    string Code = 1;
    string Msg = 2;
    repeated OetVehicleItem Items = 3;
}

message GetVehicleCountWithTopCorporationIdRequest {
    int64 TopCorporationId = 1;
}

message GetVehicleCountWithTopCorporationIdResponse {
    string Code = 1;
    string Msg = 2;
    int64 TotalCount = 3;
}

message GetVehicleWithLicenseRequest {
    string License = 1;         // 车牌号，精确搜索
    int64 CorporationId = 2;    // 根机构
}

message GetVehicleWithLicenseResponse {
    string Code = 1;
    string Msg = 2;
    OetVehicleItem Item = 3;
}

message GetVehiclesWithLicenseOptionRequest {
    int64 TopCorporationId = 1; // 根机构
    string License = 2;         // 车牌号，模糊搜索
    int64 Offset = 3;           // 偏移
    int64 Limit  = 4;           // 数量，0-读取全部
    string Order = 5;           // 排序，升序：asc；降序：desc
}

message GetVehiclesWithLicenseOptionResponse {
    repeated OetVehicleItem Items = 1;
    int64 TotalCount = 2;               // 列表总数
}

message GetVehicleWithCodeRequest {
    int64 CorporationId = 1; // 根机构
    string Code  =2;
}

message GetVehicleWithCodeResponse {
    OetVehicleItem Item = 1;
}

message GetVehiclesAndModelWithOptionRequest {
    repeated int64 Ids = 1;             // 车辆Id
    string Brand = 2;                   // 车辆品牌，模糊搜索，空搜全部
    double Length = 3;                  // 车辆长度（米）
    int64 VehicleModal = 4;             // 燃料类型, 0:未知;1:柴油车(传统);2:汽油车(传统);3:混合动力车(新能源);
    // 4:纯电动车(新能源);5:CNG(新能源);6:LNG;7:纯电动(双路BMS);999-全部
    int64 UseNature = 5;                // 使用性质*, 1-公交车；2-校车；3-公务用车；4-巴士公务车;999-全部
    int64 LicenseStartDate = 6;         // 上牌日期，UTC时间戳，秒 [左闭
    int64 LicenseEndDate = 7;           // 上牌日期，UTC时间戳，秒  右开)
    int64 PurchaseStartDate = 8;        // 购置日期，UTC时间戳，秒 [左闭
    int64 PurchaseEndDate = 9;          // 购置日期，UTC时间戳，秒  右开)
    // int64 Offset = 10;                  // 偏移
    // int64 Limit = 11;                   // 数量，0-读取全部
    // string Order = 12;                  // 排序，升序：asc；降序：desc
}

message VehiclesAndModel {
    OetVehicleItem VehicleItem = 1;
    string Brand = 2;           // 车辆品牌，模糊搜索，空搜全部
    double Length = 4;          // 车辆长度（米）
}

message GetVehiclesAndModelWithOptionResponse {
    repeated VehiclesAndModel Items = 1;
    int64 TotalCount = 2;               // 列表总数
}

message SrcVehicle {
    int64 CorporationId = 1; // 所属机构
    string Corporation = 2;  // 所属机构名称
    int64 Id = 3;            // 车辆Id
    string License = 4;      // 车牌号
    int64 LineId = 5;        // 线路Id
    string LineName = 6;     // 线路名称
    int64 TransferMode = 7;  // 调动方式，1-执行方案，2-回退方案，3-车队调整，4-主数据
    string SchemeNo  = 8;    // 方案编号
}

message DstVehicle {
    int64  LineId = 1;          // 迁移目的地线路 Id
    string Line = 2;            // 迁移目的地线路名
    int64  CorporationId = 3;   // 迁移目的地机构 Id
    string Corporation = 4;     // 迁移目的地机构名
}

message MoveRequest {
    repeated SrcVehicle SrcVehicles = 1;    // 需要迁移的源车辆表
    DstVehicle DstVehicle = 2;              // 迁移的目的地
    int64 UserId = 3;                       // 用户Id
    bool IsAdmin = 4;                       // 用户是否是超级管理员
}

message MoveResponse {
    string Code = 1;            // 错误码 0-正确，其他错误
    string Msg = 2;             // 错误信息
}

message GetVehicleRecordsRequest{
    int64 VehicleId = 1;  // 车辆id
    int64 StartAt   = 2;  // 开始时间, unix timestamp（秒戳）
    int64 EndAt     = 3;  // 结束时间, unix timestamp（秒戳）
    int64 Offset    = 4;  // 从第几行开始查询
    int64 Limit     = 5;  // 查询数据的个数
    string Order    = 6;  // 查询结果的排序方式, desc(降序)/asc(升序)
}

message GetVehicleRecordsResponse{
    repeated OetVehicleRecordItem Items = 1;
    int64 TotalCount = 2;               // 列表总数
}

message GetHistoricalVehiclesWithUserIdRequest {
    int64 UserId = 1;   // 用户id
    bool IsAdmin = 2;   // 是否是管理员，false:普通用户，true:超级管理员
    int64 QueryDateStart = 8;   // 查询开始日期 格式：2006-01-02 (UTC时间戳，单位：秒) [2021-02-1
    int64 QueryDateEnd = 9;     // 查询结束日期 格式：2006-01-02 (UTC时间戳，单位：秒) 2022-02-1) 开始时间和结束时间相等时搜索全部
}

message GetHistoricalVehiclesWithUserIdResponse {
    string Code = 1;
    string Msg = 2;
    repeated HistoryVehicleItem Items = 3; // 注意同一个车辆id可能归属多个所属机构
}