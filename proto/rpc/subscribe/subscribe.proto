syntax = "proto3";

package oet.scs.api.device;

service RpcApiApc {
  rpc LineChangeEvent(LineChangeEventReport) returns (LineChangeEventResponse) {}
  rpc ArrLeavStationEvent(ArrLeavStationEventReport) returns (ArrLeavStationEventResponse) {}
  rpc GetVehiclePfWithVehicleId(GetVehiclePfWithVehicleIdRequest) returns (GetVehiclePfWithVehicleIdResponse) {};
  rpc GetLinePfWithLineId (GetLinePfWithLineIdRequest) returns (GetLinePfWithLineIdResponse) {};
}

service RpcApiIss {
  // 查询车辆运行线路
  rpc QueryVehicleRunLineInfo(QueryVehicleRunLineInfoRequest) returns (QueryVehicleRunLineInfoResponse) {}
  // 同步司机手动打卡记录
  rpc SyncDriverAttendanceRecord(SyncDriverAttendanceRecordRequest) returns (SyncDriverAttendanceRecordResponse) {}
  // 路单补录、路单修改通知
  rpc LineRoadFormModifyNotify(LineRoadFormModifyNotifyRequest) returns (LineRoadFormModifyNotifyResponse) {}
  // 主数据线路轨迹站点更改通知
  rpc LineStationModifyNotify(LineStationModifyNotifyRequest) returns (LineStationModifyNotifyResponse) {}
  // 线路重新排班通知
  rpc LinePlanReSchedulingNotify(LinePlanReSchedulingNotifyRequest) returns (LinePlanReSchedulingNotifyResponse) {}
  // 单个趟次重新计算
  rpc LinePlanScheduleReCalc(LinePlanScheduleReCalcRequest) returns (LinePlanScheduleReCalcResponse) {}
}

message LineChangeEventReport {
  string DeviceSN = 1;  // iss devicesn
  int64 VehicleId = 2;
  int64 LineId = 3;
  uint32 LineCode = 4;
  int64 LineType = 5;
  int64 ChangeAt = 6;  // 线路切换的时间, time unix
}

message LineChangeEventResponse {
  string Code = 1;  // if "0": success, else failed
  string Msg = 2;
}

message ArrLeavStationEventReport {
  string DeviceSN = 1;  // iss devicesn
  int64 TopCorporationId = 2;
  int64 CorporationId = 3;
  int64 LineId = 4;
  int64 DeviceId = 5;
  int64 VehicleId = 6;
  int64 DriverId = 7;
  int64 ArrLeavType = 8;
  int64 ServiceType = 9;
  int64 StationId = 10;
  int64 StationSeq = 11;
  int64 FirstLast = 12;
  int64 Flag = 13;
  int64 Longitude = 14;
  int64 Latitude = 15;
  int64 Altitude = 16;
  int64 Speed = 17;
  int64 Direction = 18;
  int64 ReportAt = 19;
  int64 PeopleIn = 20;
  int64 PeopleOut = 21;
  int64 People = 22;
}

message ArrLeavStationEventResponse {
  string Code = 1;  // if "0": success, else failed
  string Msg = 2;
}

message QueryVehicleRunLineInfoRequest {
  string DeviceSN = 1;  // apc devicesn
  int64 VehicleId = 2;
}

message QueryVehicleRunLineInfoResponse {
  string Code = 1;  // if "0": success, else failed
  string Msg = 2;
  int64 TopCorporationId = 3;
  int64 CorporationId = 4;
  int64 LineId = 5;
  uint32 LineCode = 6;
  int64 LineType = 7;
  int64 DriverId = 8;
}

message SyncDriverAttendanceRecordRequest {
  int64 TopCorporationId = 1;  // 机构Id
  int64 AttendanceId = 2;  // 打卡记录ID
  string DeviceSN = 3;  // 设备号
}

message SyncDriverAttendanceRecordResponse {
  string Code = 1;  // 1 成功  -1失败
  string Msg = 2;  // ok, error
}

message LineRoadFormModifyNotifyRequest {
  int64 ReportAt = 1;
  int64 LineRoadFormId = 2;
  int64 TopCorporationId = 3;
  int64 OriginVehicleId = 4;
}

message LineRoadFormModifyNotifyResponse {
  string Code = 1;  // if "0": success, else failed
  string Msg = 2;
}

message LineStationModifyNotifyRequest {
  int64 LineId = 1;
}

message LineStationModifyNotifyResponse {
  string Code = 1;  // if "0": success, else failed
  string Msg = 2;
}

message LinePlanReSchedulingNotifyRequest {
  int64 LineId = 1;
  int64 PlanDate = 2;  // 秒时间戳
  int64 TopCorporationId = 3;  // 顶级机构
  int64 CorporationId = 4;  // 线路所属的机构
}

message LinePlanReSchedulingNotifyResponse {
  string Code = 1;  // if "0": success, else failed
  string Msg = 2;
}

message LinePlanScheduleReCalcRequest {
  int64 TopCorporationId = 1;
  int64 DayAt = 2;  // 排班的日期 unix时间戳
  repeated int64 PlanScheduleIds = 3;  // 营运调度排班ID
}

message LinePlanScheduleReCalcResponse {
  string Code = 1;  // if "0": success, else failed
  string Msg = 2;
}

message GetVehiclePfWithVehicleIdRequest {
  int64 CorporationId = 1;
  int64 VehicleId = 2;
  int64 StartUnix = 3;
  int64 EndUnix = 4;
}

message GetVehiclePfWithVehicleIdResponse {
  VehiclePfItem item = 1;
}

message VehiclePfItem {
  int64 VehicleId = 1;
  int64 People = 2;
  int64 FrontIn = 3;
  int64 FrontOut = 4;
  int64 RearIn = 5;
  int64 RearOut = 6;
}

message LinePfItem {
  int64 LineId = 1;
  int64 InPeople = 2;
  string ReportAt = 3;
}

message GetLinePfWithLineIdRequest {
  int64 TopCorporationId = 1;
  int64 LineId = 2;
  int64 StartAtUnix = 3;
  int64 EndAtUnix = 4;
}

message GetLinePfWithLineIdResponse {
  string Code = 1;  // if "0": success, else failed
  string Msg = 2;
  repeated LinePfItem items = 3;
}