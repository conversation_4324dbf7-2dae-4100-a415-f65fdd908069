syntax = "proto3";

package oet.scs.srv.app;

service OetLine {
    rpc GetLines (GetLinesRequest) returns (GetLinesResponse) {
    }
    rpc GetLineWithId (GetLineWithIdRequest) returns (GetLineWithIdResponse) {
    }
    rpc GetLineWithCode (GetLineWithCodeRequest) returns (GetLineWithCodeResponse) {
    }
    rpc GetLineWithName (GetLineWithNameRequest) returns (GetLineWithNameResponse) {
    }
    rpc GetCorporationIdsWithLineType (GetCorporationIdsWithLineTypeRequest) returns (GetCorporationIdsWithLineTypeResponse) {
    }
    rpc GetLinesWithTopCorporationId (GetLinesWithTopCorporationIdRequest) returns (GetLinesWithTopCorporationIdResponse) {
    }
    rpc GetLineCountWithTopCorporationId (GetLineCountWithTopCorporationIdRequest) returns (GetLineCountWithTopCorporationIdResponse) {
    }
    rpc GetLineLengthWithTopCorporationId (GetLineLengthWithTopCorporationIdRequest) returns (GetLineLengthWithTopCorporationIdResponse) {
    }
    // 查询用户授权的线路列表(用户权限)
    rpc GetLinesWithUserId (GetLinesWithUserIdRequest) returns (GetLinesWithUserIdResponse) {
    }
    // 查询子机构下线路
    rpc GetLinesWithSubCorporationId (GetLinesWithSubCorporationIdRequest) returns (GetLinesWithSubCorporationIdResponse) {
    }
    // 查询所属机构线路(用户权限)
    rpc GetLinesWithCorporationId (GetLinesWithCorporationIdRequest) returns (GetLinesWithCorporationIdResponse) {
    }
    // 查询用户授权机构下的历史全部线路列表(用户权限)
    rpc GetHistoricalLinesWithUserId (GetHistoricalLinesWithUserIdRequest) returns (GetHistoricalLinesWithUserIdResponse) {
    }
    // 根据线路id查询该线路的历史所属机构
    rpc GetHistoricalCorpsWithLineId (GetHistoricalCorpsWithLineIdRequest) returns (GetHistoricalCorpsWithLineIdResponse) {
    }
    // 根据线路id查询该线路的历史列表
    rpc GetHistoricalLinesWithLineId (GetHistoricalLinesWithLineIdRequest) returns (GetHistoricalLinesWithLineIdResponse) {
    }
}

message OetLineItem {
    int64 Id = 1;               // 线路id (unique(SubCorporationId_Id_MigrationState))
    int64 CorporationId = 2;    // 根机构
    string Corporation = 3;     // 根机构名称
    string Name = 4;            // 线路名称
    int64 LineType = 5;         // 线路类型, 1-普通线路；2-单向环行线路；
    // 3:双向环行线路；4:机动型 (old:0:普通线路, 1:单向环行线路；2:双向环行线路)
    string Code = 6;            // 线路编号
    int64 UpParkingId = 7;      // 上行场站 Id
    string UpParking = 8;       // 上行场站名
    int64 UpTime = 9;           // 上行单程时间，分钟
    int64 UpLength = 10;        // 上行单程距离(米)
    int64 UpFirstTime = 11;     // 上行/环线首班发车时间,秒
    int64 UpLastTime = 12;      // 上行/环线末班发车时间,秒
    int64 DownParkingId = 13;   // 下行场站 Id
    string DownParking = 14;    // 下行场站名
    int64 DownTime = 15;        // 下行单程时间，分钟
    int64 DownLength = 16;      // 下行单程距离(米)
    int64 DownFirstTime = 17;   // 下行/环线首班发车时间,秒
    int64 DownLastTime = 18;    // 下行/环线末班发车时间,秒
    int64 MinInterval = 19;     // 串车(米)
    int64 LargeInterval = 20;   // 大间隔(米)
    int64 VehicleNumber = 21;   // 备车数
    int64 SaleType = 22;        // 售票方式, 1:无人售票,1:有人售票,9:其他
    int64 NewLineType = 23;     // 运营类型,1:干线, 2:支线, 3:快速, 4:高峰, 5:夜间线路,9:其他
    int64 Status = 24;          // 是否正式路线, 1:正式线路,2:临时线路(客户端不显示),3:停用(客户端不显示)
    int64 CreatedAt = 25;       // 创建时间
    int64 UpdatedAt = 26;       // 跟新时间
    string CityCode = 27;       // 国家城市区域代码
    int64 Price = 28;           // 起始票价, 分, -1未设置票价
    int64 PriceEx = 29;         // 全程票价, 分, -1未设置票价
    int64 UpFirstParkDistance = 30;      // 上行(环行)首站到场站距离，单位：米
    int64 UpLastParkDistance = 31;       // 上行(环行)末站到场站距离，单位：米
    int64 DownFirstParkDistance = 32;    // 下行首站到场站距离，单位：米
    int64 DownLastParkDistance = 33;     // 下行末站到场站距离，单位：米
    int64 InspectionType = 34;           // 考核类型: 0-全部, 1-上行, 2-下行, 9-不考核
    int64 SubCorporationId = 35;         // 线路归属的子机构Id(unique(SubCorporationId_Id_MigrationState))
    string SubCorporation = 36;          // 子机构名称
    int64 IsJudgeCarDistance = 37;       // 判断前后车距, 0-否；1-是
    string PosLineNo = 38;               // POS机线路号
    int64 LineAttribute  = 39;           // 线路属性，1-常规公交；2-定制公交；3-公务车；4-校车；5-旅游专线; 6-村村通
    int64 RunStatus = 40;                // 营运状态，0-停运；1-营运
    int64 SchedulingClass = 41;          // 调度类型，1-单边调度；2-双边调度
    int64 MorningPeakStartTime = 42;     // 早高峰时段开始时间，秒 6:00
    int64 MorningPeakEndTime = 43;       // 早高峰时段结束时间，秒 7:00
    int64 EveningPeakStartTime = 44;     // 晚高峰时段开始时间，秒 18:00
    int64 EveningPeakEndTime = 45;       // 晚高峰时段结束时间，秒 19:00
    int64 LineOpeningTime = 46;          // 线路开通时间，UTC时间戳，秒
    int64 CityType = 47;                 // 城区类型（台州），0-非中心城区，1-中心城区,
    int64 TrunkLineId = 48;              // 主线ID（海信）

    repeated int64 SubCorporationIds = 100; // 线路归属的机构Id集合,兼容线路归属多个所属机构
    repeated string SubCorporations = 101;  // 所属机构名称
}

message OetSubLineItem {
    int64 Id = 1;               // 线路id (unique(SubCorporationId_Id))
    int64 SubCorporationId = 2; // 所属机构id (unique(SubCorporationId_Id))
    string SubCorporation = 3;  // 所属机构名称
    string Name = 4;            // 线路名称
    int64 LineType = 5;         // 线路类型, 1-普通线路；2-单向环行线路；
    // 3:双向环行线路；4:机动型 (old: 0:普通线路, 1:单向环行线路；2:双向环行线路)
    string Code = 6;
    int64 UpParkingId = 7;      // 上行场站 Id
    string UpParking = 8;       // 上行场站名
    int64 UpTime = 9;           // 上行单程时间，分钟
    int64 UpLength = 10;        // 上行单程距离(米)
    int64 UpFirstTime = 11;     // 上行/环线首班发车时间,秒
    int64 UpLastTime = 12;      // 上行/环线末班发车时间,秒
    int64 DownParkingId = 13;   // 下行场站 Id
    string DownParking = 14;    // 下行场站名
    int64 DownTime = 15;        // 下行单程时间，分钟
    int64 DownLength = 16;      // 下行单程距离(米)
    int64 DownFirstTime = 17;   // 下行/环线首班发车时间,秒
    int64 DownLastTime = 18;    // 下行/环线末班发车时间,秒
    int64 MinInterval = 19;     // 串车(米)
    int64 LargeInterval = 20;   // 大间隔(米)
    int64 VehicleNumber = 21;   // 备车数
    int64 SaleType = 22;        // 售票方式, 1:无人售票,1:有人售票,9:其他
    int64 NewLineType = 23;     // 运营类型,1:干线, 2:支线, 3:快速, 4:高峰, 5:夜间线路,9:其他
    int64 Status = 24;          // 是否正式路线, 1:正式线路,2:临时线路(客户端不显示),3:停用(客户端不显示)
    int64 CreatedAt = 25;       // 创建时间
    int64 UpdatedAt = 26;       // 跟新时间
    string CityCode = 27;       // 国家城市区域代码
    int64 Price = 28;           // 起始票价, 分, -1未设置票价
    int64 PriceEx = 29;         // 全程票价, 分, -1未设置票价
    int64 UpFirstParkDistance = 30;      // 上行(环行)首站到场站距离，单位：米
    int64 UpLastParkDistance = 31;       // 上行(环行)末站到场站距离，单位：米
    int64 DownFirstParkDistance = 32;    // 下行首站到场站距离，单位：米
    int64 DownLastParkDistance = 33;     // 下行末站到场站距离，单位：米
    int64 InspectionType = 34;           // 考核类型: 0-全部, 1-上行, 2-下行，9-不考核
    int64 IsJudgeCarDistance = 35;       // 判断前后车距, 0-否；1-是
    string PosLineNo = 36;               // POS机线路号
    int64 LineAttribute  = 37;           // 线路属性，1-常规公交；2-定制公交；3-公务车；4-校车；5-旅游专线; 6-村村通
    int64 RunStatus = 38;                // 营运状态，0-停运；1-营运
    int64 SchedulingClass = 39;          // 调度类型，1-单边调度；2-双边调度
    int64 MorningPeakStartTime = 40;     // 早高峰时段开始时间，秒 6:00
    int64 MorningPeakEndTime = 41;       // 早高峰时段结束时间，秒 7:00
    int64 EveningPeakStartTime = 42;     // 晚高峰时段开始时间，秒 18:00
    int64 EveningPeakEndTime = 43;       // 晚高峰时段结束时间，秒 19:00
    int64 LineOpeningTime = 44;          // 线路开通时间，UTC时间戳，秒
    int64 CityType = 45;                 // 城区类型（台州），0-非中心城区，1-中心城区,
    int64 TrunkLineId = 46;              // 主线ID（海信）
}

// 历史线路信息
message HistoryLineItem {
    int64 Id = 1;                        // 线路id (unique(SubCorporationId_Id_MigrationState))
    int64 CorporationId = 2;             // 根机构
    string Corporation = 3;              // 根机构名称
    string Name = 4;                     // 线路名称
    int64 SubCorporationId = 35;         // 线路归属的子机构Id(unique(SubCorporationId_Id_MigrationState))
    string SubCorporation = 36;          // 子机构名称
    int64 LineAttribute  = 39;           // 线路属性，1-常规公交；2-定制公交；3-公务车；4-校车；5-旅游专线; 6-村村通
    int64 RunStatus = 40;                // 营运状态，0-停运；1-营运
    int64 CityType = 47;                 // 城区类型（台州），0-非中心城区，1-中心城区,
    int64 MigrationState = 49;           // 所属机构迁移状态:0-迁入，1-迁出
}

// 机构信息
message CorporationItem {
    int64 Id = 1;       // 机构id
    string Name = 2;    // 机构名称
}

message GetLinesRequest {
    repeated int64 LineIds = 1;
    string Name = 2;
    int64 Offset = 3;
    int64 Limit = 4;
    string Order = 5;
    int64 IsLineAttribute = 6;  // 线路属性村村通是否可见，0-否，1-是
}

message GetLinesResponse {
    string Code = 1;
    string Msg = 2;
    repeated OetLineItem Items = 3;
    int64 TotalCount = 4;
}

message GetLineWithIdRequest {
    int64 LineId = 1;
    int64 IsLineAttribute = 2;  // 线路属性村村通是否可见，0-否，1-是
}

message GetLineWithIdResponse {
    OetLineItem Item = 1;
    repeated int64 SubCorporationIds = 2; // 线路归属的子机构Id集合
}

message GetLineWithCodeRequest {
    int64 TopCorporationId = 1;
    string Code = 2;
    int64 IsLineAttribute = 3;  // 线路属性村村通是否可见，0-否，1-是
}

message GetLineWithCodeResponse {
    OetLineItem Item = 1;
    repeated int64 SubCorporationIds = 2; // 线路归属的子机构Id集合
}

message GetLineWithNameRequest {
    int64 TopCorporationId = 1;
    string Name = 2;
    int64 IsLineAttribute = 3;  // 线路属性村村通是否可见，0-否，1-是
}

message GetLineWithNameResponse {
    string Code = 1;
    string Msg = 2;
    OetLineItem Item = 3;
}

message GetCorporationIdsWithLineTypeRequest {
    int64 LineType = 1;
    int64 IsLineAttribute = 2;  // 线路属性村村通是否可见，0-否，1-是
}

message GetCorporationIdsWithLineTypeResponse {
    repeated int64 CorporationIds = 1;
}

message GetLinesWithTopCorporationIdRequest {
    int64 TopCorporationId = 1;
    int64 IsLineAttribute = 2;  // 线路属性村村通是否可见，0-否，1-是
}

message GetLinesWithTopCorporationIdResponse {
    string Code = 1;
    string Msg = 2;
    repeated OetLineItem Items = 3;
}

message GetLineCountWithTopCorporationIdRequest {
    int64 TopCorporationId = 1;
    int64 IsLineAttribute = 2;  // 线路属性村村通是否可见，0-否，1-是
}

message GetLineCountWithTopCorporationIdResponse {
    string Code = 1;
    string Msg = 2;
    int64 TotalCount = 3;
}

message GetLineLengthWithTopCorporationIdRequest {
    int64 TopCorporationId = 1;
    int64 UpDown = 2; // 0 全部, 1:上行, 2:下行
    int64 IsLineAttribute = 3;  // 线路属性村村通是否可见，0-否，1-是
}

message GetLineLengthWithTopCorporationIdResponse {
    string Code = 1;
    string Msg = 2;
    int64 LineLength = 3;
}

message GetLinesWithUserIdRequest {
    int64 UserId = 1;
    string Name = 2;
    int64 Offset = 3;
    int64 Limit = 4;
    string Order = 5;
    int64 IsLineAttribute = 6;  // 线路属性村村通是否可见，0-否，1-是
}

message GetLinesWithUserIdResponse {
    repeated OetLineItem Items = 1;
    int64 TotalCount = 2;
}

message GetLinesWithSubCorporationIdRequest{
    int64 SubCorporationId = 1;
    int64 IsLineAttribute = 2;  // 线路属性村村通是否可见，0-否，1-是
}

message GetLinesWithSubCorporationIdResponse {
    string Code = 1;
    string Msg = 2;
    repeated OetSubLineItem Items = 3;
}

message GetLinesWithCorporationIdRequest {
    int64 CorporationId = 1;    // 选择的机构id
    int64 IsLineAttribute = 2;  // 线路属性村村通是否可见，0-否，1-是
    bool IsShowChildNode = 3;   // 是否查询选择的机构Id以及下面的子机构，否-false，是-true
    int64 Offset = 4;           // 偏移
    int64 Limit = 5;            // 数量，数量为0读取偏移后的全部表
    string Order = 6;           // 排序，升序：asc；降序：desc
}

message GetLinesWithCorporationIdResponse {
    string Code = 1;
    string Msg = 2;
    repeated OetLineItem Items = 3;  // 注意同一个线路id可能归属多个所属机构SubCorporationId
}

message GetHistoricalLinesWithUserIdRequest {
    int64 UserId = 1;   // 用户id
    bool IsAdmin = 2;   // 是否是管理员，false:普通用户，true:超级管理员
    string Name = 3;    // 线路名称，模糊查询
    int64 IsLineAttribute = 7;  // 线路属性村村通是否可见，0-否，1-是
    int64 QueryDateStart = 8;   // 查询开始日期 格式：2006-01-02 (UTC时间戳，单位：秒) [2021-02-1
    int64 QueryDateEnd = 9;     // 查询结束日期 格式：2006-01-02 (UTC时间戳，单位：秒) 2022-02-1) 开始时间和结束时间相等时搜索当天
}

message GetHistoricalLinesWithUserIdResponse {
    string Code = 1;
    string Msg = 2;
    repeated HistoryLineItem Items = 3; // 注意同一个线路id可能归属多个所属机构SubCorporationId
}

message GetHistoricalCorpsWithLineIdRequest {
    repeated int64 LineIds = 1; // 线路ids
    int64 QueryDateStart = 2;  // 查询开始日期 格式：2006-01-02 (UTC时间戳，单位：秒) [2021-02-1
    int64 QueryDateEnd = 3;    // 查询结束日期 格式：2006-01-02 (UTC时间戳，单位：秒) 2022-02-1) 开始时间和结束时间相等时搜索全部
}

message GetHistoricalCorpsWithLineIdResponse {
    repeated CorporationItem Items = 1;
}

message GetHistoricalLinesWithLineIdRequest {
    repeated int64 LineIds = 1; // 线路ids
    int64 QueryDateStart = 2;  // 查询开始日期 格式：2006-01-02 (UTC时间戳，单位：秒) [2021-02-1
    int64 QueryDateEnd = 3;    // 查询结束日期 格式：2006-01-02 (UTC时间戳，单位：秒) 2022-02-1) 开始时间和结束时间相等时搜索全部
}

message GetHistoricalLinesWithLineIdResponse {
    string Code = 1;
    string Msg = 2;
    repeated HistoryLineItem Items = 3; // 注意同一个线路id可能归属多个历史所属机构SubCorporationId
}

