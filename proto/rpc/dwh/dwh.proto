syntax = "proto3";

package oet_scs_api_dwh;

service DwhRpc {
    // 根据手机号获取订单
    rpc GetContractOrderWithPhone(GetContractOrderWithPhoneRequest) returns (GetContractOrderWithPhoneResponse) {};
    // 查询所有路单
    rpc GetContractRoads(GetContractRoadsRequest) returns (GetContractRoadsResponse) {};
}

message GetContractOrderWithPhoneRequest {
    int64 TopCorporationId   = 1;
    string Phone             = 2;
    int64 StartAt            = 3;
    int64 EndAt              = 4;
    repeated string OrderIds = 5;
}

message GetContractOrderWithPhoneResponse {
    string Code              = 1;
    repeated OrderItem Items = 2;
}
message OrderItem {
    string OrderId                          = 1;   // 订单编号
    string Name                             = 2;   // 下单人姓名
    string Phone                            = 3;   // 下单人手机号
    string UnionId                          = 4;   // 微信unionid
    string OpenId                           = 5;   // 微信openid
    int64 CreatedTime                       = 6;   // 订单创建时间
    int64 Type                              = 7;   // 状态:0-待审核,1-待支付,2-已支付,3-完结,-1-已取消,-2退款申请中
    int64 PayPrice                          = 8;   // 订单金额
    string PaySerialNumber                  = 9;   // 支付流水号
    string RefundSerialNumber               = 10;  // 退款流水号
    string AttractionList                   = 11;  // 行程信息
    int64 Count                             = 12;  // 订单人数
    int64 StartDate                         = 13;  // 开始日期
    int64 EndDate                           = 14;  // 结束日期
    int64 SubType                           = 15;  // 订单子状态:0-正常完结,1-全额退款,2-不分退款,3-异常完结
    int64 TravelAgencyId                    = 16;  // 旅行社标识
    int64 Mold                              = 17;  // 种类,1-福建香客,2-老年团,3-个性化,4-固定
    string Remark                           = 18;  // 订单备注
    repeated SubOrderItem Items             = 100;
    repeated AttractionItem AttractionItems = 101;
}

message AttractionItem {
    string ParkingName   = 1;
    int64 ParkingId      = 2;
    string GtParkingName = 3;
}

message SubOrderItem {
    string SubOrderId                       = 1;   // 订单编号
    string OrderId                          = 2;   // 主订单编号
    string AttractionList                   = 3;   // 行程信息
    int64 Index                             = 4;   // 子订单序号
    int64 VehicleTypeId                     = 5;   // 车型标识 1-12座；2-22坐；6-14座；7-17座；8-20座；9-23座；10-28座
    string OpenId                           = 6;   // 微信openid
    string Phone                            = 7;   // 下单人手机号
    int64 State                             = 8;   // 订单状态,0-空闲,1-呼叫中,2-行驶中,-1-取消,3-完结，4-司机完结,5-后台
    int64 MaxCount                          = 9;   // 子订单最大人数
    int64 SubPrice                          = 10;  // 子订单价格
    repeated AttractionItem AttractionItems = 100;
}

message GetContractRoadsRequest {
    int64 TopCorporationId = 1;
    string Phone           = 2;  // 数据
    string OrderId         = 3;  // 主订单ID
    int64 StartAt          = 4;
    int64 EndAt            = 5;
}

message GetContractRoadsResponse {
    string Code                     = 1;
    int64 TotalCount                = 2;
    repeated ContractRoadItem Items = 3;
}

message ContractRoadItem {
    string RoadId           = 1;   // 包车路单ID
    int64 State             = 2;   // 包车状态:0=呼叫中 -1=用户主动取消 1=被分配，待接单 2=司机确认接单 3=人上车进行中  4=结束 -2=司机取消行程 -3后台取消
    int64 ReleaseTime       = 3;   // 发布时间
    int64 StartParkingId    = 4;   // 起始点标识
    int64 EndParkingId      = 5;   // 结束点标识
    string Phone            = 6;   // 手机号
    string DriverName       = 7;   // 司机姓名
    int64 EndTime           = 8;   // 结束时间
    string SubOrderId       = 9;   // 子订单ID
    int64 Count             = 10;  // 游客人数
    string StartParkingName = 11;  // 起始点标识
    string EndParkingName   = 12;  // 结束点标识
    string OrderId          = 13;  // 主订单ID
    string License          = 14;  // 车牌号
    int64 VehicleId         = 15;  // 车辆ID
}
