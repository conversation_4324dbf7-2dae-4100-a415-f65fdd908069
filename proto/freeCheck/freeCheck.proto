syntax = "proto3";

package oet.scs.api.base.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

service Freecheck {
  rpc GetStaffByStaffIdStr(go.api.Request) returns (go.api.Response) {} // 根据工号找人员(全部人员)
  rpc StaffList(go.api.Request) returns (go.api.Response) {} // 根据工号找人员（账号所属公司下的人员）
  rpc UserList(go.api.Request) returns (go.api.Response) {} // 用户
  rpc AllStaffList(go.api.Request) returns (go.api.Response) {} // 账号所在的顶级机构下的所有人员
  rpc VehicleList(go.api.Request) returns (go.api.Response) {}
  rpc VehicleListAll(go.api.Request) returns (go.api.Response) {}
  rpc LineList(go.api.Request) returns (go.api.Response) {}
  rpc LineStationList(go.api.Request) returns (go.api.Response) {}
  rpc GetVehiclePlanSchedule(go.api.Request) returns (go.api.Response) {}
  rpc CorporationList(go.api.Request) returns (go.api.Response) {}
  rpc CorporationListTree(go.api.Request) returns (go.api.Response) {}

  rpc AllLineList(go.api.Request) returns (go.api.Response) {}
  rpc CorporationLineList(go.api.Request) returns (go.api.Response) {}
  rpc LineHasAllCorp(go.api.Request) returns (go.api.Response) {}
  rpc HasHistoryStaffList(go.api.Request) returns (go.api.Response) {}
  rpc HasHistoryVehicleList(go.api.Request) returns (go.api.Response) {}

  rpc LbpmProcessParam(go.api.Request) returns (go.api.Response) {}
  rpc LbpmApproveProcess(go.api.Request) returns (go.api.Response) {}
  rpc LbpmApprovingProcess(go.api.Request) returns (go.api.Response) {}
  rpc LbpmProcessFormValue(go.api.Request) returns (go.api.Response) {}
  rpc LbpmProcessInstance(go.api.Request) returns (go.api.Response) {}
  rpc LbpmCashierUpdateForm(go.api.Request) returns (go.api.Response) {}

  rpc MessageDelete(go.api.Request) returns (go.api.Response) {}
  rpc MessageList(go.api.Request) returns (go.api.Response) {}
  rpc MessageRead(go.api.Request) returns (go.api.Response) {}
  rpc MessageReadAll(go.api.Request) returns (go.api.Response) {}
  rpc MessageUnReadList(go.api.Request) returns (go.api.Response) {}
  rpc TrafficAccidentListHandle(go.api.Request) returns (go.api.Response) {}
  rpc TrafficAccidentReadAllHandle(go.api.Request) returns (go.api.Response) {}
  rpc TrafficAccidentUnReadList(go.api.Request) returns (go.api.Response) {}


  rpc VehicleOperate(go.api.Request) returns (go.api.Response) {} // 一车一档
  rpc VehicleSafety(go.api.Request) returns (go.api.Response) {}  // 一车一档
  rpc UserStatus(go.api.Request) returns (go.api.Response) {}  // 一车一档

  rpc GetVehicleInsurance(go.api.Request) returns (go.api.Response) {}  // 车辆保险


  rpc ListTicketDataPermission(go.api.Request) returns (go.api.Response) {}  // 票务-变更数据权限列表 用来控制票务列表编辑按钮


  rpc UrlToBase64String(go.api.Request) returns (go.api.Response) {}  // 前端传一个图片url地址，返回base64字符串

  rpc GetH5Permissions(go.api.Request) returns (go.api.Response) {} // H5页面权限
  rpc DeviceFactoryList(go.api.Request) returns (go.api.Response) {} // 设备厂家列表
  rpc DeviceFactoryUserList(go.api.Request) returns (go.api.Response) {} // 设备厂家列表

  rpc LinePlanClassCount(go.api.Request) returns (go.api.Response) {} // 线路计划班次数
  rpc LinePlanList(go.api.Request) returns (go.api.Response) {} // 线路计划班次数
  rpc ShowLinePlan(go.api.Request) returns (go.api.Response) {} // 线路计划班次数

  rpc LineStations(go.api.Request) returns (go.api.Response) {} // 线路站点
  rpc ListStation(go.api.Request) returns (go.api.Response) {} // 站点列表

  rpc SaveSetting(go.api.Request) returns (go.api.Response) {}
  rpc GetSetting(go.api.Request) returns (go.api.Response) {}
  rpc ExportFileList(go.api.Request) returns (go.api.Response) {}
  rpc GetExportFileToBase64(go.api.Request) returns (go.api.Response) {}

  rpc CheckOperationReportIsEnableUpdate(go.api.Request) returns (go.api.Response) {}
  rpc StaffLeaveCount(go.api.Request) returns (go.api.Response) {}
  rpc ListTopViolationCategory(go.api.Request) returns (go.api.Response) {}

  rpc HeadimgShow(go.api.Request) returns (go.api.Response) {}
  rpc UpdateHeadImg(go.api.Request) returns (go.api.Response) {}
  rpc MineInfo(go.api.Request) returns (go.api.Response) {}
}