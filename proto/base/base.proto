syntax = "proto3";

package oet.scs.api.base.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

service Line {
  rpc List(go.api.Request) returns (go.api.Response) {}
}

service Vehicle {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc ListAll(go.api.Request) returns (go.api.Response) {}
  rpc GetLicenseByCode(go.api.Request) returns (go.api.Response) {}
  rpc Detail(go.api.Request) returns (go.api.Response) {} // 车辆详情
}

service Parking {
  rpc List(go.api.Request) returns (go.api.Response) {}
}

service Station {
  rpc List(go.api.Request) returns (go.api.Response) {}
}

service Staff {
  rpc GetStaffByStaffIdStr(go.api.Request) returns (go.api.Response) {} // 根据工号找人员(全部人员)
  rpc AppGetStaffByStaffIdStr(go.api.Request) returns (go.api.Response) {} // 移动端 全部人员 不根据权限
  rpc StaffList(go.api.Request) returns (go.api.Response) {} // 根据工号找人员（账号所属公司下的人员）
  rpc ListTree(go.api.Request) returns (go.api.Response) {} // 全部人员 不根据权限
}


service Corporation {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc ListStaff(go.api.Request) returns (go.api.Response) {}
}