syntax = "proto3";

package oet.scs.api.dss.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

service Dss {
	rpc Auth(go.api.Request) returns (go.api.Response) {}                      //Auth认证, 设备联网时主动上报, 设备号为纯数字, 长度[8,12]位
	rpc SignIn(go.api.Request) returns (go.api.Response) {}                    //司机考勤, 签到【人脸检验后，才是测温酒测，然后才签到】
	rpc SignOut(go.api.Request) returns (go.api.Response) {}                   //司机考勤, 签退
	rpc GetScreensaverInfo(go.api.Request) returns (go.api.Response) {}        //获取屏保图片
	rpc searchVehicles(go.api.Request) returns (go.api.Response) {}            //车辆信息查询
	rpc getRoutineCheckData(go.api.Request) returns (go.api.Response) {}       //车辆例保信息查询
	rpc upRoutineCheckData(go.api.Request) returns (go.api.Response) {}        //车辆例保结果上传
	rpc getDriverPlans(go.api.Request) returns (go.api.Response) {}            //驾驶员计划查询
	rpc getDriverPromiseRecord(go.api.Request) returns (go.api.Response) {}    //获取驾驶员承诺记录
	rpc saveDriverPromiseRecord(go.api.Request) returns (go.api.Response) {}   //保存驾驶员阅读岗前承诺记录
	rpc getDriverNotify(go.api.Request) returns (go.api.Response) {}           //获取岗前告知信息
	rpc getRoutineCheckDataRecord(go.api.Request) returns (go.api.Response) {} //车辆例保历史信息查询
	rpc GetStatisticBillList(go.api.Request) returns (go.api.Response) {}      //路单信息接口
	rpc GetAccidentList(go.api.Request) returns (go.api.Response) {}           //安全事故信息接口
	rpc GetViolationList(go.api.Request) returns (go.api.Response) {}          //违章情况信息接口
	rpc GetOverSpeedList(go.api.Request) returns (go.api.Response) {}          //超速报警信息接口
	rpc selfcheck(go.api.Request) returns (go.api.Response) {}                 //设备自检上报
	rpc getConfig(go.api.Request) returns (go.api.Response) {}                 //获取设备参数, 定时主动查询, 30分钟一次
	rpc PostLog(go.api.Request) returns (go.api.Response) {}                   //终端上报日志
	rpc GetQuestionContent(go.api.Request) returns (go.api.Response) {}        //获取题库信息
	rpc AddQuestionRecord(go.api.Request) returns (go.api.Response) {}         //添加答题记录
	rpc GetDeviceQuestion(go.api.Request) returns (go.api.Response) {}         //获取设备题库信息
	rpc reqData(go.api.Request) returns (go.api.Response) {}                   //终端请求平台获取最新人脸数据
	rpc resData(go.api.Request) returns (go.api.Response) {}                   //终端响应人脸数据同步结果
	rpc Login(go.api.Request) returns (go.api.Response) {}                     //驾驶员登录, 进入系统时刷脸/刷卡会主动上报
	rpc FaceCheck(go.api.Request) returns (go.api.Response) {}                 //人脸校验, 签到时会主动上报
	rpc Heartbeat(go.api.Request) returns (go.api.Response) {}                 //设备心跳, 每30s定时上报一次
	rpc GetFtpAddress(go.api.Request) returns (go.api.Response) {}             //固件升级(废弃)
	rpc UpLoadVideo(go.api.Request) returns (go.api.Response) {}               //酒测视频上传
	rpc FaceRegister(go.api.Request) returns (go.api.Response) {}              //人脸注册
}
