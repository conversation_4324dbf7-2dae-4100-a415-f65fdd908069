syntax = "proto3";

package oet.scs.api.ticket.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

// 点钞
service Countmoney {
  rpc Add(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Logger(go.api.Request) returns (go.api.Response) {}
  rpc AllReview(go.api.Request) returns (go.api.Response) {}
  rpc BatchReview(go.api.Request) returns (go.api.Response) {}
}

// 点票
service Countticket {
  rpc Add(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Logger(go.api.Request) returns (go.api.Response) {}
  rpc AllReview(go.api.Request) returns (go.api.Response) {}
  rpc BatchReview(go.api.Request) returns (go.api.Response) {}
}


service Report {
  rpc ListDefective(go.api.Request) returns (go.api.Response) {}  // 残次币查询

  rpc IncomeCompany(go.api.Request) returns (go.api.Response) {}  // 公司营收报表
  rpc IncomeLine(go.api.Request) returns (go.api.Response) {}  // 线路营收报表
  rpc IncomeVehicle(go.api.Request) returns (go.api.Response) {}  // 车辆营收报表
  rpc AttendantDaily(go.api.Request) returns (go.api.Response) {}  // 乘务员日报
  rpc MultiTicket(go.api.Request) returns (go.api.Response) {}  // 多票制报表

  rpc CharterBusIncomeCreate(go.api.Request) returns (go.api.Response) {}  // 包车款添加
  rpc CharterBusIncomeList(go.api.Request) returns (go.api.Response) {}  // 包车款列表
  rpc CharterBusIncomeExport(go.api.Request) returns (go.api.Response) {}  // 包车款导出
  rpc CharterBusIncomeUpdate(go.api.Request) returns (go.api.Response) {}  // 包车款更新
  rpc CharterBusIncomeDelete(go.api.Request) returns (go.api.Response) {}  // 包车款删除

  rpc CalcCorporationLineIncome(go.api.Request) returns (go.api.Response) {}  // 计算公司线路人次营收表
  rpc CorporationLineIncomeReport(go.api.Request) returns (go.api.Response) {}  // 公司线路人次营收表
  rpc CorporationLineIncomeReportExport(go.api.Request) returns (go.api.Response) {}  // 公司线路人次营收表导出
}

service Ticketdatapermission {
  rpc Add(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Cancel(go.api.Request) returns (go.api.Response) {}
}

service Ticketbankcheck {
  rpc ListDifference(go.api.Request) returns (go.api.Response) {}
  rpc EditDifference(go.api.Request) returns (go.api.Response) {}
  rpc Check(go.api.Request) returns (go.api.Response) {}
  rpc EditDifferenceCountMoney(go.api.Request) returns (go.api.Response) {}
  rpc AutoEditDifferenceCountMoney(go.api.Request) returns (go.api.Response) {}
  rpc ListCheckRecord(go.api.Request) returns (go.api.Response) {}
  rpc ListCheckChangeRecord(go.api.Request) returns (go.api.Response) {}
}