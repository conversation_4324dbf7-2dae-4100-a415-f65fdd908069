syntax = "proto3";

package oet.scs.api.setting.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

// 库存管理
service Accidentsetting {
  rpc AlarmCategoryAll(go.api.Request) returns (go.api.Response) {}
  rpc AlarmCategoryCreate(go.api.Request) returns (go.api.Response) {}
  rpc AlarmCategoryUpdate(go.api.Request) returns (go.api.Response) {}
  rpc AlarmCategoryDelete(go.api.Request) returns (go.api.Response) {}

  rpc ProcessTimeoutSettingSave(go.api.Request) returns (go.api.Response) {}
  rpc ProcessTimeoutSettingAll(go.api.Request) returns (go.api.Response) {}
  rpc AccidentTimeoutSettingSave(go.api.Request) returns (go.api.Response) {}
  rpc AccidentTimeoutSettingAll(go.api.Request) returns (go.api.Response) {}
}


service Alarmrecord {
  rpc ProcessTimeout(go.api.Request) returns (go.api.Response) {}
  rpc ProcessTimeoutExport(go.api.Request) returns (go.api.Response) {}
  rpc AccidentTimeout(go.api.Request) returns (go.api.Response) {}
  rpc AccidentTimeoutExport(go.api.Request) returns (go.api.Response) {}
  rpc ProcessTimeoutAlarmIgnore(go.api.Request) returns (go.api.Response) {}
  rpc ProcessTimeoutAlarmDelete(go.api.Request) returns (go.api.Response) {}
  rpc AccidentTimeoutAlarmIgnore(go.api.Request) returns (go.api.Response) {}
  rpc AccidentTimeoutAlarmDelete(go.api.Request) returns (go.api.Response) {}
}


service Listsetting {
  rpc CustomColumn(go.api.Request) returns (go.api.Response) {}
  rpc SettingInfo(go.api.Request) returns (go.api.Response) {}
}

service Factorysetting {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Import(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc Update(go.api.Request) returns (go.api.Response) {}
  rpc Del(go.api.Request) returns (go.api.Response) {}
  rpc BindUser(go.api.Request) returns (go.api.Response) {}
}

service Lineallowancesetting {
  rpc Update(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc LineRecord(go.api.Request) returns (go.api.Response) {}
}

service Globalsetting {
  rpc Setting(go.api.Request) returns (go.api.Response) {}
  rpc SaveSetting(go.api.Request) returns (go.api.Response) {}
}

service Topiccategory {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Save(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
}



