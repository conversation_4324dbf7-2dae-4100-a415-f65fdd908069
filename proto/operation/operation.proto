syntax = "proto3";

package oet.scs.api.safety.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

service Operation {
  rpc LineSettingList(go.api.Request) returns (go.api.Response) {}
  rpc LineSettingInfo(go.api.Request) returns (go.api.Response) {}
  rpc LineSettingCreate(go.api.Request) returns (go.api.Response) {}
  rpc LineSettingUpdate(go.api.Request) returns (go.api.Response) {}
  rpc LineSettingDelete(go.api.Request) returns (go.api.Response) {}
  rpc LineSettingLog(go.api.Request) returns (go.api.Response) {}
  rpc LineSettingLogExport(go.api.Request) returns (go.api.Response) {}

  rpc CalcLineMileage(go.api.Request) returns (go.api.Response) {}
  rpc MultiDayCalcLineMileage(go.api.Request) returns (go.api.Response) {}
  rpc LineMileageReport(go.api.Request) returns (go.api.Response) {}
  rpc LineMileageReportExport(go.api.Request) returns (go.api.Response) {}
  rpc LineMileageRangeReport(go.api.Request) returns (go.api.Response) {}
  rpc LineMileageRangeReportExport(go.api.Request) returns (go.api.Response) {}
  rpc SaveLineMileage(go.api.Request) returns (go.api.Response) {}
  rpc DeleteLineMileage(go.api.Request) returns (go.api.Response) {}
  rpc LineMileageLog(go.api.Request) returns (go.api.Response) {}
  rpc LineMileageVersion(go.api.Request) returns (go.api.Response) {}
  rpc LineMileageVersionData(go.api.Request) returns (go.api.Response) {}
  rpc LineMileageVersionRecover(go.api.Request) returns (go.api.Response) {}
  rpc VehicleMileageSumReport(go.api.Request) returns (go.api.Response) {}
  rpc VehicleMileageSumReportExport(go.api.Request) returns (go.api.Response) {}
  rpc LineMileageSumReport(go.api.Request) returns (go.api.Response) {}
  rpc LineMileageSumReportExport(go.api.Request) returns (go.api.Response) {}

  rpc LineDriverWorkReport(go.api.Request) returns (go.api.Response) {}
  rpc LineDriverWorkReportExport(go.api.Request) returns (go.api.Response) {}
  rpc LineDriverWorkReportByDriver(go.api.Request) returns (go.api.Response) {}
  rpc LineDriverWorkReportByDriverExport(go.api.Request) returns (go.api.Response) {}
  rpc LineSalaryReport(go.api.Request) returns (go.api.Response) {}
  rpc LineSalarySumReport(go.api.Request) returns (go.api.Response) {}
  rpc SaveLineSalaryReportMore(go.api.Request) returns (go.api.Response) {}


  rpc IrregularLineReport(go.api.Request) returns (go.api.Response) {}
  rpc IrregularLineReportExport(go.api.Request) returns (go.api.Response) {}
  rpc IrregularLineReportImport(go.api.Request) returns (go.api.Response) {}
  rpc IrregularLineReportDelete(go.api.Request) returns (go.api.Response) {}

  rpc PlanScheduleReportImport(go.api.Request) returns (go.api.Response) {}
  rpc PlanScheduleReport(go.api.Request) returns (go.api.Response) {}
  rpc PlanScheduleReportExport(go.api.Request) returns (go.api.Response) {}
  rpc PlanScheduleAvgDayReport(go.api.Request) returns (go.api.Response) {}
  rpc PlanScheduleAvgDayReportExport(go.api.Request) returns (go.api.Response) {}

  // 线路站点分析
  rpc ListRegion(go.api.Request) returns (go.api.Response) {} // 获取区域列表
  rpc ListStation(go.api.Request) returns (go.api.Response) {} // 获取站点列表
  rpc ListStationExport(go.api.Request) returns (go.api.Response) {} // 导出站点列表
  rpc ListStreetPlateByLine(go.api.Request) returns (go.api.Response) {} // 导出路牌-线路维度
  rpc ListStreetPlateByStation(go.api.Request) returns (go.api.Response) {} // 导出路牌-站点维度
  rpc ListLine(go.api.Request) returns (go.api.Response) {}  // 获取线路列表
  rpc ListLineStation(go.api.Request) returns (go.api.Response) {}  // 获取线路站点列表
  rpc ListLineStationExport(go.api.Request) returns (go.api.Response) {}  // 导出线路站点列表
  rpc StationSetting(go.api.Request) returns (go.api.Response) {}  // 站点设置规格、材料
  rpc GetStationSetting(go.api.Request) returns (go.api.Response) {}  // 查询站点规格、材料
  rpc ListStationRealityExport(go.api.Request) returns (go.api.Response) {}  // 导出站点列表实景
  rpc ListStationRealityByLineExport(go.api.Request) returns (go.api.Response) {}  // 导出站点列表实景-线路维度
  rpc ProvincialDemandStationExport(go.api.Request) returns (go.api.Response) {}   // 线路站点分析-省级数据导出

  rpc TimeSlotStationVote(go.api.Request) returns (go.api.Response) {}  // 站点分时投票统计
  rpc TimeSlotStationVoteExport(go.api.Request) returns (go.api.Response) {}  // 站点分时投票统计

  // 线路仿真
  rpc ListCustomLineFolder(go.api.Request) returns (go.api.Response) {} // 获取文件夹
  rpc AddCustomLineFolder (go.api.Request) returns (go.api.Response) {} // 新增文件夹
  rpc EditCustomLineFolder(go.api.Request) returns (go.api.Response) {} // 编辑文件夹
  rpc DelCustomLineFolder(go.api.Request) returns (go.api.Response) {} // 删除文件夹
  rpc SortCustomLineFolder(go.api.Request) returns (go.api.Response) {} // 文件夹排序
  rpc AddCustomLine (go.api.Request) returns (go.api.Response) {} // 新增线路
  rpc EditCustomLine(go.api.Request) returns (go.api.Response) {} // 编辑线路
  rpc DelCustomLine(go.api.Request) returns (go.api.Response) {} // 删除线路
  rpc SyncCustomLine (go.api.Request) returns (go.api.Response) {} // 同步线路站点
  rpc ListCustomLineStation(go.api.Request) returns (go.api.Response) {} // 查询线路站点
  rpc ListCustomLineStationExport(go.api.Request) returns (go.api.Response) {} // 导出线路站点
  rpc ListStreetPlateByCustomLine(go.api.Request) returns (go.api.Response) {} // 导出路牌-线路维度
  rpc PredictLineStation(go.api.Request) returns (go.api.Response) {} // 预测线路站点

  // 材料设置
  rpc ListSpecificationSetting(go.api.Request) returns (go.api.Response) {} // 规格列表
  rpc AddSpecificationSetting (go.api.Request) returns (go.api.Response) {} // 规格配置
  rpc EditSpecificationSetting(go.api.Request) returns (go.api.Response) {} // 编辑规格
  rpc DelSpecificationSetting(go.api.Request) returns (go.api.Response) {} // 删除规格

  // 材料设置
  rpc ListMaterialSetting(go.api.Request) returns (go.api.Response) {} // 规格材料
  rpc AddMaterialSetting (go.api.Request) returns (go.api.Response) {} // 规格材料
  rpc EditMaterialSetting(go.api.Request) returns (go.api.Response) {} // 编辑材料
  rpc DelMaterialSetting(go.api.Request) returns (go.api.Response) {} // 删除材料

  rpc ApprovalCreate(go.api.Request) returns (go.api.Response) {}
  rpc ApprovalList(go.api.Request) returns (go.api.Response) {}
  rpc ApprovalListExport(go.api.Request) returns (go.api.Response) {}
  rpc ApprovalShow(go.api.Request) returns (go.api.Response) {}
  rpc ApprovalTerminate(go.api.Request) returns (go.api.Response) {}
  rpc CheckLineReportAtApproval(go.api.Request) returns (go.api.Response) {}
  rpc CheckLineReportEditStatus(go.api.Request) returns (go.api.Response) {}
  rpc CheckLineNotApproval(go.api.Request) returns (go.api.Response) {}

  rpc OutFrequencyAddWorkReportImport(go.api.Request) returns (go.api.Response) {}
  rpc OutFrequencyAddWorkReportList(go.api.Request) returns (go.api.Response) {}
  rpc OutFrequencyAddWorkReportListExport(go.api.Request) returns (go.api.Response) {}
  rpc OutFrequencyAddWorkSumReportList(go.api.Request) returns (go.api.Response) {}
  rpc OutFrequencyAddWorkSumReportListExport(go.api.Request) returns (go.api.Response) {}
  rpc OutFrequencyAddWorkReportDelete(go.api.Request) returns (go.api.Response) {}


  rpc ListVehicleAdScreenConfig(go.api.Request) returns (go.api.Response) {}
  rpc ListVehicleAdScreenConfigExport(go.api.Request) returns (go.api.Response) {}
  rpc ImportVehicleAdScreenConfig(go.api.Request) returns (go.api.Response) {}
  rpc SetVehicleAdScreenConfig(go.api.Request) returns (go.api.Response) {}
  // 出行需求
  rpc ListTravelNeed(go.api.Request) returns (go.api.Response) {}
  rpc AddTravelNeed(go.api.Request) returns (go.api.Response) {}
  rpc EditTravelNeed(go.api.Request) returns (go.api.Response) {}
  rpc DelTravelNeed(go.api.Request) returns (go.api.Response) {}
  rpc ListTravelNeedDetail(go.api.Request) returns (go.api.Response) {}
  rpc ListTravelNeedDetailExport(go.api.Request) returns (go.api.Response) {}
  rpc DelTravelNeedDetail(go.api.Request) returns (go.api.Response) {}
  rpc AddTravelNeedDetail(go.api.Request) returns (go.api.Response) {}

  rpc AddCharterOrderBrief(go.api.Request) returns (go.api.Response) {}
  rpc ListCharterOrderBrief(go.api.Request) returns (go.api.Response) {}
  rpc EditCharterOrderBrief(go.api.Request) returns (go.api.Response) {}
  rpc DelCharterOrderBrief(go.api.Request) returns (go.api.Response) {}
  rpc ListCharterOrderBriefExport(go.api.Request) returns (go.api.Response) {}
  rpc ListCharterOrderExport(go.api.Request) returns (go.api.Response) {}
  rpc CharterOrderBriefDetail(go.api.Request) returns (go.api.Response) {}
}