syntax = "proto3";

package oet.scs.api.stock.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

// 库存管理
service Stock {
  rpc ListStockMaterialType(go.api.Request) returns (go.api.Response) {}
  rpc AddStockMaterialType(go.api.Request) returns (go.api.Response) {}
  rpc EditStockMaterialType(go.api.Request) returns (go.api.Response) {}
  rpc DeleteStockMaterialType(go.api.Request) returns (go.api.Response) {}
  rpc ListStockMaterialTypePreset(go.api.Request) returns (go.api.Response) {}
  rpc EditStockMaterialTypePreset(go.api.Request) returns (go.api.Response) {}
  rpc ListStockMaterialTypePresetLiable(go.api.Request) returns (go.api.Response) {}

  rpc BranchAddStock(go.api.Request) returns (go.api.Response) {}
  rpc EditStockMaterial(go.api.Request) returns (go.api.Response) {}
  rpc DeleteStockMaterial(go.api.Request) returns (go.api.Response) {}
  rpc ListStock(go.api.Request) returns (go.api.Response) {}
  rpc ListStockRecord(go.api.Request) returns (go.api.Response) {}
  rpc OperateStock(go.api.Request) returns (go.api.Response) {}

  rpc ListRecycleStock(go.api.Request) returns (go.api.Response) {}
  rpc ListRecycleStockRecord(go.api.Request) returns (go.api.Response) {}

  rpc MaterialRequisition(go.api.Request) returns (go.api.Response) {}

  rpc OutStockForm(go.api.Request) returns (go.api.Response) {}
  rpc OutStockFormDetail(go.api.Request) returns (go.api.Response) {}
  rpc ExportOutStockForm(go.api.Request) returns (go.api.Response) {}
  rpc ExportOutStockFormDetail(go.api.Request) returns (go.api.Response) {}
}
