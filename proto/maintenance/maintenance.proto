syntax = "proto3";

package oet.scs.api.safety.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

// 玻璃维修
service Glassrepair {
  rpc AddDraft(go.api.Request) returns (go.api.Response) {} //
  rpc Add(go.api.Request) returns (go.api.Response) {} //
  rpc List(go.api.Request) returns (go.api.Response) {} //
  rpc Export(go.api.Request) returns (go.api.Response) {} //
  rpc GetDetail(go.api.Request) returns (go.api.Response) {} //
  rpc AddForm(go.api.Request) returns (go.api.Response) {} //
  rpc Delete(go.api.Request) returns (go.api.Response) {} //
}

// 车辆调动
service Vehicletransfer {
  rpc Create(go.api.Request) returns (go.api.Response) {} //
  rpc List(go.api.Request) returns (go.api.Response) {} //
  rpc Show(go.api.Request) returns (go.api.Response) {} //
  rpc Delete(go.api.Request) returns (go.api.Response) {} //
  rpc UpdateInfo(go.api.Request) returns (go.api.Response) {} //
  rpc UpdateCancelStatus(go.api.Request) returns (go.api.Response) {}

  rpc TransferPlanList(go.api.Request) returns (go.api.Response) {} //
  rpc TransferPlanShow(go.api.Request) returns (go.api.Response) {} //
  rpc TransferPlanCreate(go.api.Request) returns (go.api.Response) {} //
  rpc TransferPlanEdit(go.api.Request) returns (go.api.Response) {} //
  rpc TransferPlanDelete(go.api.Request) returns (go.api.Response) {} //
  rpc TransferPlanVersionList(go.api.Request) returns (go.api.Response) {} //
  rpc VehicleModelColor(go.api.Request) returns (go.api.Response) {} //
  rpc VehicleModelColorEdit(go.api.Request) returns (go.api.Response) {} //
}

// 车辆管理
service Vehiclemanage {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc ListExport(go.api.Request) returns (go.api.Response) {}
  rpc VehicleTransferRecord(go.api.Request) returns (go.api.Response) {}
  rpc ExportQrcode(go.api.Request) returns (go.api.Response) {}// 导出车辆二维码
  rpc VehicleProfile(go.api.Request) returns (go.api.Response) {}
  rpc VehicleProfileShow(go.api.Request) returns (go.api.Response) {}
  rpc VehicleProfileSave(go.api.Request) returns (go.api.Response) {}
}

// 节油奖
service fuelsavingaward {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Import(go.api.Request) returns (go.api.Response) {}
}

