syntax = "proto3";

package oet.scs.api.workorder.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

// 工单字典
service Dict {
  rpc Add(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc Tree(go.api.Request) returns (go.api.Response) {}
  rpc ListOperation(go.api.Request) returns (go.api.Response) {}
}

service Device {
  rpc AddModel(go.api.Request) returns (go.api.Response) {}
  rpc ListModel(go.api.Request) returns (go.api.Response) {}
  rpc ShowModel(go.api.Request) returns (go.api.Response) {}
  rpc EditModel(go.api.Request) returns (go.api.Response) {}
  rpc DeleteModel(go.api.Request) returns (go.api.Response) {}
  rpc ModelHasChildDeviceModel(go.api.Request) returns (go.api.Response) {}
  rpc BatchCodeHasChildDeviceModel(go.api.Request) returns (go.api.Response) {}
  rpc BatchBindChildModel(go.api.Request) returns (go.api.Response) {}

  rpc AddDetail(go.api.Request) returns (go.api.Response) {}
  rpc ListDetail(go.api.Request) returns (go.api.Response) {}
  rpc ExportListDetail(go.api.Request) returns (go.api.Response) {}
  rpc ListDetailPurchase(go.api.Request) returns (go.api.Response) {}
  rpc EditDetail(go.api.Request) returns (go.api.Response) {}
  rpc ImportDetailUpdate(go.api.Request) returns (go.api.Response) {}
  rpc DeleteDetail(go.api.Request) returns (go.api.Response) {}
  rpc GetDetail(go.api.Request) returns (go.api.Response) {}

  rpc ExportQrcodeDetail(go.api.Request) returns (go.api.Response) {}
  rpc PrintQrcodeDetail(go.api.Request) returns (go.api.Response) {}

  rpc BatchAddAssociation(go.api.Request) returns (go.api.Response) {}
  rpc ImportDeviceAssociation(go.api.Request) returns (go.api.Response) {}
  rpc ListAssociation(go.api.Request) returns (go.api.Response) {}
  rpc ExportListAssociation(go.api.Request) returns (go.api.Response) {}
  rpc Dissociation(go.api.Request) returns (go.api.Response) {}

  rpc AddDeviceCategory(go.api.Request) returns (go.api.Response) {}
  rpc EditDeviceCategory(go.api.Request) returns (go.api.Response) {}
  rpc EditPreset(go.api.Request) returns (go.api.Response) {}
  rpc ListPreset(go.api.Request) returns (go.api.Response) {}

  rpc BatchAssociationObject(go.api.Request) returns (go.api.Response) {}
}

service Childdevice{
  rpc AddCate(go.api.Request) returns (go.api.Response) {}
  rpc ListCate(go.api.Request) returns (go.api.Response) {}
  rpc AddDevice(go.api.Request) returns (go.api.Response) {}
  rpc ListDevice(go.api.Request) returns (go.api.Response) {}
  rpc ExportDevice(go.api.Request) returns (go.api.Response) {}
  rpc ShowDevice(go.api.Request) returns (go.api.Response) {}
  rpc EditDevice(go.api.Request) returns (go.api.Response) {}
  rpc DeleteDevice(go.api.Request) returns (go.api.Response) {}
  rpc ImportUpdateDevice(go.api.Request) returns (go.api.Response) {}
  rpc ExportQrcode(go.api.Request) returns (go.api.Response) {}
}

service Workorder {
  rpc Add(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc ListAll(go.api.Request) returns (go.api.Response) {}
  rpc Merge(go.api.Request) returns (go.api.Response) {}
  rpc GetDetail(go.api.Request) returns (go.api.Response) {}
  rpc RepairChoose(go.api.Request) returns (go.api.Response) {}
  rpc SetDeviceStatus(go.api.Request) returns (go.api.Response) {}
  rpc AssignRepairUser(go.api.Request) returns (go.api.Response) {}

  rpc AddPetition(go.api.Request) returns (go.api.Response) {} // 创建信访工单
  rpc PetitionWorkOrderList(go.api.Request) returns (go.api.Response) {} // 信访工单
  rpc PetitionWorkOrderMineList(go.api.Request) returns (go.api.Response) {} // 我的信访工单
  rpc PetitionWorkOrderShow(go.api.Request) returns (go.api.Response) {} // 信访工单详情
  rpc PetitionWorkOrderHandle(go.api.Request) returns (go.api.Response) {} // 信访工单处理

  rpc YearSumReport(go.api.Request) returns (go.api.Response) {}
  rpc FleetRankReport(go.api.Request) returns (go.api.Response) {}
  rpc LineRankReport(go.api.Request) returns (go.api.Response) {}
  rpc DeviceCateBrokenRateReport(go.api.Request) returns (go.api.Response) {}
  rpc DeviceModelBrokenRateReport(go.api.Request) returns (go.api.Response) {}
  rpc WithInDeviceModelBrokenRateReport(go.api.Request) returns (go.api.Response) {}
  rpc WithOutDeviceModelBrokenRateReport(go.api.Request) returns (go.api.Response) {}
  rpc ChildDeviceCountRankReport(go.api.Request) returns (go.api.Response) {}
  rpc ChildDeviceMoneyRankReport(go.api.Request) returns (go.api.Response) {}
}