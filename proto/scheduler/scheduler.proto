syntax = "proto3";

package oet.scs.api.scheduler.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

// 点钞
service Scheduler {
  rpc GetLaborContractExpireNotify(go.api.Request) returns (go.api.Response) {}
  rpc GetProbationExpireNotify(go.api.Request) returns (go.api.Response) {}
  rpc AddLaborContractExpireNotify(go.api.Request) returns (go.api.Response) {}
  rpc AddProbationExpireNotify(go.api.Request) returns (go.api.Response) {}
  rpc EditLaborContractExpireNotify(go.api.Request) returns (go.api.Response) {}
  rpc EditProbationExpireNotify(go.api.Request) returns (go.api.Response) {}
}