syntax = "proto3";

package oet.scs.api.file.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

service dashboard {
  rpc AllProcess(go.api.Request) returns (go.api.Response) {}
  rpc AccidentProcess(go.api.Request) returns (go.api.Response) {}
  rpc DeleteProcess(go.api.Request) returns (go.api.Response) {}
  rpc ProcessMessage(go.api.Request) returns (go.api.Response) {}
  rpc ProcessMessageDelete(go.api.Request) returns (go.api.Response) {}
  rpc UpdateProcessFormStep(go.api.Request) returns (go.api.Response) {}

  rpc CreateFormStep(go.api.Request) returns (go.api.Response) {}
  rpc ListFormStep(go.api.Request) returns (go.api.Response) {}

  rpc UpdateAccidentForm(go.api.Request) returns (go.api.Response) {}
  rpc UpdateAccidentClosedForm(go.api.Request) returns (go.api.Response) {}
  rpc UpdateAccidentBranchClosedForm(go.api.Request) returns (go.api.Response) {}
  rpc UpdateAccidentLendMoneyForm(go.api.Request) returns (go.api.Response) {}
  rpc UpdateAccidentDrawbackMoneyForm(go.api.Request) returns (go.api.Response) {}
  rpc UpdateAccidentPaymentMoneyForm(go.api.Request) returns (go.api.Response) {}
  rpc AccidentList(go.api.Request) returns (go.api.Response) {}
  rpc AccidentStatusEdit(go.api.Request) returns (go.api.Response) {}

  rpc WorkOrderList(go.api.Request) returns (go.api.Response) {}
  rpc WorkOrderDelete(go.api.Request) returns (go.api.Response) {}
  rpc PetitionWorkOrderList(go.api.Request) returns (go.api.Response) {}
  rpc PetitionWorkOrderDelete(go.api.Request) returns (go.api.Response) {}
  rpc UpdatePetitionWorkOrderForm(go.api.Request) returns (go.api.Response) {}

  rpc DriverMigrationList(go.api.Request) returns (go.api.Response) {}
  rpc DriverMigrationDelete(go.api.Request) returns (go.api.Response) {}
  rpc StaffLeaveRecordList(go.api.Request) returns (go.api.Response) {}
  rpc StaffLeaveRecordDelete(go.api.Request) returns (go.api.Response) {}
  rpc BecomeWorkerRecordList(go.api.Request) returns (go.api.Response) {}
  rpc BecomeWorkerRecordDelete(go.api.Request) returns (go.api.Response) {}

  rpc VehicleMigrationList(go.api.Request) returns (go.api.Response) {}
  rpc VehicleMigrationDelete(go.api.Request) returns (go.api.Response) {}
}