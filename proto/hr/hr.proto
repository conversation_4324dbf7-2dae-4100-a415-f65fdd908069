syntax = "proto3";

package oet.scs.api.hr.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

service Staffarchive {
  rpc SubmitApply(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Info(go.api.Request) returns (go.api.Response) {}
  rpc StaffInfo(go.api.Request) returns (go.api.Response) {}
  rpc Logger(go.api.Request) returns (go.api.Response) {}
  rpc Import(go.api.Request) returns (go.api.Response) {}
  rpc Export(go.api.Request) returns (go.api.Response) {}
  rpc InsuranceRecord(go.api.Request) returns (go.api.Response) {}
  rpc FundRecord(go.api.Request) returns (go.api.Response) {}
  rpc MineInfo(go.api.Request) returns (go.api.Response) {}
  rpc EditMineInfo(go.api.Request) returns (go.api.Response) {}
  rpc UpdateHeadImg(go.api.Request) returns (go.api.Response) {}
}

service Workpost {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc SelectList(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc SwitchStatus(go.api.Request) returns (go.api.Response) {}
}

service Joincompanyapply {
  rpc BatchCreateStaff(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc CheckUser(go.api.Request) returns (go.api.Response) {}
  rpc Apply(go.api.Request) returns (go.api.Response) {}
  rpc ApplyInfo(go.api.Request) returns (go.api.Response) {}
}

service Stafftransfer {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc Show(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
}

service Drivermigration {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc Show(go.api.Request) returns (go.api.Response) {}
  rpc UpdateInfo(go.api.Request) returns (go.api.Response) {}
  rpc UpdateCancelStatus(go.api.Request) returns (go.api.Response) {}
}

service Staffquit {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Show(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc DispatchProcess(go.api.Request) returns (go.api.Response) {}
  rpc Rollback(go.api.Request) returns (go.api.Response) {}
}

service Staffretire {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Show(go.api.Request) returns (go.api.Response) {}
  rpc WorkingList(go.api.Request) returns (go.api.Response) {}
  rpc BatchRetireSubmit(go.api.Request) returns (go.api.Response) {}
  rpc RetireSubmit(go.api.Request) returns (go.api.Response) {}
  rpc EarlyRetireSubmit(go.api.Request) returns (go.api.Response) {}
  rpc AddRetireWork(go.api.Request) returns (go.api.Response) {}
  rpc RelieveRetireWork(go.api.Request) returns (go.api.Response) {}
  rpc Rollback(go.api.Request) returns (go.api.Response) {}
}

service Staffarchiveversion {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc StaffRecord(go.api.Request) returns (go.api.Response) {}
  rpc Export(go.api.Request) returns (go.api.Response) {}
}

service Probation {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc BecomeWorker(go.api.Request) returns (go.api.Response) {}
  rpc ExtendProbation(go.api.Request) returns (go.api.Response) {}
  rpc BecomeWorkerProcess(go.api.Request) returns (go.api.Response) {}
  rpc ShowBecomeWorkerProcess(go.api.Request) returns (go.api.Response) {}
  rpc BecomeWorkerProcessList(go.api.Request) returns (go.api.Response) {}
  rpc BecomeWorkerProcessUpdate(go.api.Request) returns (go.api.Response) {}
}

service Laborcontract {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Import(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc Relieve(go.api.Request) returns (go.api.Response) {}
}

service Insurance {
  rpc Setting(go.api.Request) returns (go.api.Response) {}
  rpc SettingInfo(go.api.Request) returns (go.api.Response) {}
  rpc Import(go.api.Request) returns (go.api.Response) {}
  rpc Export(go.api.Request) returns (go.api.Response) {}
  rpc ExtraMedialList(go.api.Request) returns (go.api.Response) {}
  rpc AnnuityList(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc InsuranceEdit(go.api.Request) returns (go.api.Response) {}
  rpc InsuranceBatchEdit(go.api.Request) returns (go.api.Response) {}
  rpc AnnuityEdit(go.api.Request) returns (go.api.Response) {}
  rpc AnnuityBatchEdit(go.api.Request) returns (go.api.Response) {}
  rpc ExtraMedicalEdit(go.api.Request) returns (go.api.Response) {}
  rpc ExtraMedicalBatchEdit(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc ExtraMedialDelete(go.api.Request) returns (go.api.Response) {}
  rpc AnnuityDelete(go.api.Request) returns (go.api.Response) {}
}

service Fund {
  rpc Setting(go.api.Request) returns (go.api.Response) {}
  rpc SettingInfo(go.api.Request) returns (go.api.Response) {}
  rpc Import(go.api.Request) returns (go.api.Response) {}
  rpc Export(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc BatchEdit(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
}

service Positionaltitleapply {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc Approval(go.api.Request) returns (go.api.Response) {}
  rpc Show(go.api.Request) returns (go.api.Response) {}
}

service Staffassessment {
  rpc Import(go.api.Request) returns (go.api.Response) {}
  rpc Export(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
}

service Worktrain {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc StaffTrainList(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc AttendStaff(go.api.Request) returns (go.api.Response) {}
  rpc AddAttendStaff(go.api.Request) returns (go.api.Response) {}
  rpc EditAttendStaff(go.api.Request) returns (go.api.Response) {}
  rpc DeleteAttendStaff(go.api.Request) returns (go.api.Response) {}
}

// 一人一档
service Driverarchive {
  rpc DriverOperate(go.api.Request) returns (go.api.Response) {}
  rpc DriverSafety(go.api.Request) returns (go.api.Response) {}
  rpc AccidentList(go.api.Request) returns (go.api.Response) {}
  rpc ViolationIllegalList(go.api.Request) returns (go.api.Response) {}
}

// 统计报表
service Staffreport {
  rpc InfoDistribute(go.api.Request) returns (go.api.Response) {}
  //  rpc ListAll(go.api.Request) returns (go.api.Response) {}
  rpc ListJoin(go.api.Request) returns (go.api.Response) {}
  rpc ListQuit(go.api.Request) returns (go.api.Response) {}
  rpc ListInsurance(go.api.Request) returns (go.api.Response) {}
  rpc ListFund(go.api.Request) returns (go.api.Response) {}

  rpc StaffArchiveReport(go.api.Request) returns (go.api.Response) {}  //人员档案报表
  rpc StaffArchiveReportExport(go.api.Request) returns (go.api.Response) {}  //人员档案报表导出
  rpc StaffArchiveReportCalc(go.api.Request) returns (go.api.Response) {}  //人员档案手动计算

  rpc StaffPortrait(go.api.Request) returns (go.api.Response) {}  //人员画像
  rpc StaffPortraitAccidentList(go.api.Request) returns (go.api.Response) {}  //人员画像
  rpc StaffPortraitViolationList(go.api.Request) returns (go.api.Response) {}  //人员画像
  rpc StaffPortraitWorkOrderList(go.api.Request) returns (go.api.Response) {}  //人员画像
  rpc StaffPortraitVetoCreate(go.api.Request) returns (go.api.Response) {}  //人员一票否决添加
  rpc StaffPortraitVetoChange(go.api.Request) returns (go.api.Response) {}  //人员一票否决变更
  rpc StaffPortraitVetoList(go.api.Request) returns (go.api.Response) {}  //人员一票否决列表
}

service Refererreward {
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc Update(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Records(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc Export(go.api.Request) returns (go.api.Response) {}
  rpc StaffRecord(go.api.Request) returns (go.api.Response) {}
}

service Staffleave {
  rpc ApplyList(go.api.Request) returns (go.api.Response) {} // 请假申请列表
  rpc Create(go.api.Request) returns (go.api.Response) {} // 发起请假申请
  rpc Edit(go.api.Request) returns (go.api.Response) {} // 发起请假申请
  rpc Show(go.api.Request) returns (go.api.Response) {} // 请假详情
  rpc Scrap(go.api.Request) returns (go.api.Response) {} // 请假报废
  rpc LeaveList(go.api.Request) returns (go.api.Response) {} // 年休假列表
  rpc LeaveListExport(go.api.Request) returns (go.api.Response) {} // 导出年休假列表
  rpc LeaveEdit(go.api.Request) returns (go.api.Response) {} // 年休假编辑f
  rpc ShowLeaveRule(go.api.Request) returns (go.api.Response) {} // 年休假规则
  rpc SetLeaveRule(go.api.Request) returns (go.api.Response) {} // 年休假规则设置
  rpc StaffLeaveDayReport(go.api.Request) returns (go.api.Response) {} // 司机请假汇总
  rpc StaffLeaveDayReportAll(go.api.Request) returns (go.api.Response) {} // 司机请假汇总全量导出
  rpc StaffLeaveTypeRecord(go.api.Request) returns (go.api.Response) {} // 汇总详情
}

service Headimgapproval {
  rpc List(go.api.Request) returns (go.api.Response) {} // 证件照审核列表
  rpc Show(go.api.Request) returns (go.api.Response) {} // 证件照审核展示
  rpc Pass(go.api.Request) returns (go.api.Response) {} // 证件照审核通过
  rpc Reject(go.api.Request) returns (go.api.Response) {} // 证件照审核拒绝
}

service Staffpayroll {
  rpc List(go.api.Request) returns (go.api.Response) {} // 列表
  rpc Update(go.api.Request) returns (go.api.Response) {} // 修改
  rpc Import(go.api.Request) returns (go.api.Response) {} // 导入
  rpc Calculate(go.api.Request) returns (go.api.Response) {} // 重新计算
}

service Staffpayrollcalc {
  rpc List(go.api.Request) returns (go.api.Response) {} // 列表
  rpc Import(go.api.Request) returns (go.api.Response) {} // 导入
  rpc File(go.api.Request) returns (go.api.Response) {} // 归档
}


service Staffpayrollconfig {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Save(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
}

service Learngroup {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Add(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
}

service Topic {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Add(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc BatchDelete(go.api.Request) returns (go.api.Response) {}
  rpc Import(go.api.Request) returns (go.api.Response) {}
}
service Topicgroup {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Add(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
}
service Testpaper {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Add(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc AddExaminationTask(go.api.Request) returns (go.api.Response) {}
}
service Educationalresource {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Add(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc Import(go.api.Request) returns (go.api.Response) {}
}

service Course {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Add(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc AddExaminationTask(go.api.Request) returns (go.api.Response) {}
}

service Learnexamtask {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc StaffSummaryRecordList(go.api.Request) returns (go.api.Response) {}
  rpc MyLearnList(go.api.Request) returns (go.api.Response) {}
  rpc ShowTaskCourse(go.api.Request) returns (go.api.Response) {}
  rpc ReportLearningProgress(go.api.Request) returns (go.api.Response) {}
  rpc MyExamList(go.api.Request) returns (go.api.Response) {}
  rpc StartExam(go.api.Request) returns (go.api.Response) {}
  rpc ShowTestPaper(go.api.Request) returns (go.api.Response) {}
  rpc SubmitTestPaper(go.api.Request) returns (go.api.Response) {}
  rpc ExamDetail(go.api.Request) returns (go.api.Response) {}
}

service Complaintmanagement {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Add(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc Issue(go.api.Request) returns (go.api.Response) {}
}


