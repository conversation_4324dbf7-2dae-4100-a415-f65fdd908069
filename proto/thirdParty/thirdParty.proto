syntax = "proto3";

package oet.scs.api.erp.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

service Thirdparty {
  rpc LbpmWebServiceInterface(go.api.Request) returns (go.api.Response) {}
  rpc LbpmPushMessage(go.api.Request) returns (go.api.Response) {}
  rpc LbpmWebServiceTest(go.api.Request) returns (go.api.Response) {}
  rpc TestGetParams(go.api.Request) returns (go.api.Response) {}
  rpc TestApproveProcess(go.api.Request) returns (go.api.Response) {}
  rpc DingCallBack(go.api.Request) returns (go.api.Response) {}

  rpc LineSafetyReport(go.api.Request) returns (go.api.Response) {} //线路安全报表 （线路违规、违法、事故数量）

  rpc LoginForLbpmSSO(go.api.Request) returns (go.api.Response) {}
  rpc LoginForDdSSO(go.api.Request) returns (go.api.Response) {} // 钉钉免登陆
  
  rpc ImportLineAdjustmentRecord(go.api.Request) returns (go.api.Response) {}
  rpc LineAdjustmentRecordList(go.api.Request) returns (go.api.Response) {}
}
