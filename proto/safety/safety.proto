syntax = "proto3";

package oet.scs.api.safety.v2;

import "github.com/micro/go-micro/v2/api/proto/api.proto";

service Vehiclecheck {
    rpc List(go.api.Request) returns (go.api.Response) {}   // 自定义门检项目添加
    rpc ListExport(go.api.Request) returns (go.api.Response) {}   // 自定义门检项目添加
    rpc AbnormalList(go.api.Request) returns (go.api.Response) {}  // 自定义门检项目列表
    rpc AbnormalListExport(go.api.Request) returns (go.api.Response) {}  // 自定义门检项目列表
    rpc Show(go.api.Request) returns (go.api.Response) {}  // 自定义门检项目列表

//  rpc AddInspector(go.api.Request) returns (go.api.Response) {}   // 检查人员添加
//  rpc ListInspector(go.api.Request) returns (go.api.Response) {}   // 检查人员列表
//  rpc EditInspector(go.api.Request) returns (go.api.Response) {}   // 检查人员编辑
//
//  rpc Add(go.api.Request) returns (go.api.Response) {}   // 自定义门检项目添加
//  rpc List(go.api.Request) returns (go.api.Response) {}  // 自定义门检项目列表
//  rpc Edit(go.api.Request) returns (go.api.Response) {}  // 自定义门检项目编辑
//  rpc Delete(go.api.Request) returns (go.api.Response) {} // 自定义门检项目删除
//
//  rpc AddItemType(go.api.Request) returns (go.api.Response) {} // 添加门检类别
//  rpc EditItemType(go.api.Request) returns (go.api.Response) {} // 编辑门检类别
//  rpc DeleteItemType(go.api.Request) returns (go.api.Response) {} // 删除门检类别
//
//  rpc IsChecked(go.api.Request) returns (go.api.Response) {} // 司机、管理员过渡页显示
//  rpc TodoList(go.api.Request) returns (go.api.Response) {} // 待门检项目列表
//  rpc TodoListSubstitute(go.api.Request) returns (go.api.Response) {} // 管理员代(代替司机)录入-待门检项目列表
//  rpc AddResult(go.api.Request) returns (go.api.Response) {} // 添加门检结果
//  rpc AddResultSubstitute(go.api.Request) returns (go.api.Response) {} // 管理员代(代替司机)录入-添加门检结果
//  rpc ListWebResult(go.api.Request) returns (go.api.Response) {} // web门检结果列表
//  rpc ListAppResult(go.api.Request) returns (go.api.Response) {} // app门检结果列表
//  rpc GetResultDetail(go.api.Request) returns (go.api.Response) {} // web门检结果详情
//  rpc GetAppResultDetail(go.api.Request) returns (go.api.Response) {} // app门检结果详情
//  rpc EditResult(go.api.Request) returns (go.api.Response) {} // 门检结果整改
//  rpc ReportResult(go.api.Request) returns (go.api.Response) {} // 门检结果上报
//  rpc EditReportResult(go.api.Request) returns (go.api.Response) {} // 修改门检结果上报
//  rpc RepeatResult(go.api.Request) returns (go.api.Response) {} // 关闭重复异常
//  rpc ActiveResult(go.api.Request) returns (go.api.Response) {} // 激活关闭的重复异常
//  rpc ProcessResult(go.api.Request) returns (go.api.Response) {} // 门检结果处理
//  rpc EditProcessResult(go.api.Request) returns (go.api.Response) {} // 门检结果处理
//  rpc SyncPreGenerateRecord(go.api.Request) returns (go.api.Response) {} // 定时任务失败时 同步门检预生成记录
}

service Trafficaccident {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Export(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc Show(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc LendMoneyRecord(go.api.Request) returns (go.api.Response) {}//事故借款记录
  rpc ListRelater(go.api.Request) returns (go.api.Response) {} //事故当事人列表
  rpc ListRelaterForSelect(go.api.Request) returns (go.api.Response) {}
  rpc CreateRelater(go.api.Request) returns (go.api.Response) {} //添加事故当事人
  rpc ShowRelater(go.api.Request) returns (go.api.Response) {} //显示事故当事人
  rpc EditRelater(go.api.Request) returns (go.api.Response) {} //编辑事故当事人
  rpc DeleteRelater(go.api.Request) returns (go.api.Response) {} //删除事故当事人
  rpc UploadConfirmFile(go.api.Request) returns (go.api.Response) {} //上传事故责任认定文件
  rpc RelaterLendMoneyRecord(go.api.Request) returns (go.api.Response) {} //事故当事人借款记录
  rpc CreateBranch(go.api.Request) returns (go.api.Response) {} //添加事故的分支
  rpc DeleteBranch(go.api.Request) returns (go.api.Response) {} //添加事故的删除
  rpc ListBranch(go.api.Request) returns (go.api.Response) {} //事故分支列表
  rpc ShowBranch(go.api.Request) returns (go.api.Response) {} //查看事故分支详情
  rpc ApplyLendMoney(go.api.Request) returns (go.api.Response) {} //发起事故借款申请
  rpc ShowLendMoneyApply(go.api.Request) returns (go.api.Response) {} //事故借款申请详情
  rpc ApplyBranchClose(go.api.Request) returns (go.api.Response) {} //发起事故分支结案申请
  rpc EditApplyBranchClose(go.api.Request) returns (go.api.Response) {} //更新事故分支结案申请表单数据
  rpc ApplyClose(go.api.Request) returns (go.api.Response) {} //发起事故结案申请
  rpc EditApplyClose(go.api.Request) returns (go.api.Response) {} //发起事故结案申请
  rpc Logger(go.api.Request) returns (go.api.Response) {} //事故操作日志
  rpc SafeCheckHandle(go.api.Request) returns (go.api.Response) {} //安委会审核


  rpc BranchLendMoneyRecord(go.api.Request) returns (go.api.Response) {} //分支借款记录详情
  rpc ApplyDrawbackMoney(go.api.Request) returns (go.api.Response) {} //发起事故退款申请
  rpc ShowDrawbackMoneyApply(go.api.Request) returns (go.api.Response) {} //事故退款申请详情
  rpc ApplyPaymentMoney(go.api.Request) returns (go.api.Response) {} //发起事故付款申请
  rpc ShowPaymentMoneyApply(go.api.Request) returns (go.api.Response) {} //事故付款申请详情
  rpc UpdatePaymentMoneyApply(go.api.Request) returns (go.api.Response) {} //更新事故付款申请
  rpc ShowPaymentRecord(go.api.Request) returns (go.api.Response) {} //事故的付款记录
  rpc ShowBranchPaymentRecord(go.api.Request) returns (go.api.Response) {} //事故分支的付款记录
  rpc ShowBranchDetail(go.api.Request) returns (go.api.Response) {} //分支的详情

  rpc ClosePrinted(go.api.Request) returns (go.api.Response) {} //结案事故已打印


  rpc IsExistsOtherBranch(go.api.Request) returns (go.api.Response) {} //查询事故是否存在其他分支
  rpc AutoBranchClose(go.api.Request) returns (go.api.Response) {} //自动分支结案

  rpc ListSettingCorporationCode(go.api.Request) returns (go.api.Response) {} //安全机构编号设置-列表
  rpc EditSettingCorporationCode(go.api.Request) returns (go.api.Response) {} //安全机构编号设置-编辑
  rpc IsAccidentLicenseDateDriver(go.api.Request) returns (go.api.Response) {} //同一辆车同一天是否发起事故
  rpc IsLendBranchMoney(go.api.Request) returns (go.api.Response) {} //同一分支同一金额借款
  rpc AddRecycle(go.api.Request) returns (go.api.Response) {} //事故移入回收站
  rpc ListRecycle(go.api.Request) returns (go.api.Response) {} //事故回收站列表

  rpc VehicleAbnormalStopRecord(go.api.Request) returns (go.api.Response) {} //事故停车记录
  rpc VehicleAbnormalStopRecordByVehicleId(go.api.Request) returns (go.api.Response) {} //车辆的事故停车记录
  rpc VehicleAbnormalStopRecordIgnore(go.api.Request) returns (go.api.Response) {} //事故停车忽略


  rpc YearReport(go.api.Request) returns (go.api.Response) {} //事故年报
  rpc CateReport(go.api.Request) returns (go.api.Response) {} //事故按类别分组报表
  rpc FleetReport(go.api.Request) returns (go.api.Response) {} //事故按车队分组报表
  rpc BranchCompanyReport(go.api.Request) returns (go.api.Response) {} //事故按分公司分组报表
  rpc HourReport(go.api.Request) returns (go.api.Response) {} //事故按小时分组报表
  rpc WeatherReport(go.api.Request) returns (go.api.Response) {} //事故按天气分组报表
  rpc HappenLocationReport(go.api.Request) returns (go.api.Response) {} //事故发生地点报表
  rpc HappenLocationReportExport(go.api.Request) returns (go.api.Response) {} //事故发生地点报表导出
  rpc BranchFleetReport(go.api.Request) returns (go.api.Response) {} //分公司车队报表
  rpc BranchFleetReportExport(go.api.Request) returns (go.api.Response) {} //分公司车队报表导出
  rpc CorporationAccidentList(go.api.Request) returns (go.api.Response) {} //机构事故明细
  rpc CorporationAccidentListExport(go.api.Request) returns (go.api.Response) {} //机构事故明细导出
  rpc CorporationAccidentMoneyList(go.api.Request) returns (go.api.Response) {} //机构事故金额明细
  rpc CorporationAccidentMoneyListExport(go.api.Request) returns (go.api.Response) {} //机构事故金额明细导出
  rpc CorporationAccidentAlarmList(go.api.Request) returns (go.api.Response) {} //机构事故预告警明细
  rpc CorporationAccidentAlarmListExport(go.api.Request) returns (go.api.Response) {} //机构事故预告警明细导出
  rpc CorporationProcessAlarmList(go.api.Request) returns (go.api.Response) {} //机构事故流程预告警明细
  rpc CorporationProcessAlarmListExport(go.api.Request) returns (go.api.Response) {} //机构事故流程预告警明细导出
  rpc NotHasDiffMoneyReport(go.api.Request) returns (go.api.Response) {} //无差额事故报表
  rpc NotHasDiffMoneyReportExport(go.api.Request) returns (go.api.Response) {} //无差额事故报表导出
}

// 质量考核标准
service Qualityassessmentstandard {
  rpc AddCategory(go.api.Request) returns (go.api.Response) {} // 违规类型添加
  rpc ListCategory(go.api.Request) returns (go.api.Response) {}
  rpc EditCategory(go.api.Request) returns (go.api.Response) {}
  rpc DeleteCategory(go.api.Request) returns (go.api.Response) {}
  rpc SaveCategoryPunishSetting(go.api.Request) returns (go.api.Response) {} //处罚配置设置

  rpc AddBatch(go.api.Request) returns (go.api.Response) {} //
  rpc Add(go.api.Request) returns (go.api.Response) {} // 质量考核标准添加
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc SelectList(go.api.Request) returns (go.api.Response) {}
//  rpc Tree(go.api.Request) returns (go.api.Response) {}
  rpc Edit(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc Enable(go.api.Request) returns (go.api.Response) {} // 启用
  rpc Disable(go.api.Request) returns (go.api.Response) {} // 禁用


  rpc AddAssessmentCate(go.api.Request) returns (go.api.Response) {}
  rpc EditAssessmentCate(go.api.Request) returns (go.api.Response) {}
  rpc ListAssessmentCate(go.api.Request) returns (go.api.Response) {}
  rpc DeleteAssessmentCate(go.api.Request) returns (go.api.Response) {}
}

// 交通违规违法
service Trafficviolation {
  rpc AddBatchViolation(go.api.Request) returns (go.api.Response) {} // 违规导入
  rpc AddViolation(go.api.Request) returns (go.api.Response) {} //
  rpc ListViolation(go.api.Request) returns (go.api.Response) {}
  rpc ListServiceViolation(go.api.Request) returns (go.api.Response) {}
  rpc GetViolationById(go.api.Request) returns (go.api.Response) {}
  rpc EditViolation(go.api.Request) returns (go.api.Response) {}
  rpc EditLog(go.api.Request) returns (go.api.Response) {}
  rpc Check(go.api.Request) returns (go.api.Response) {}
  rpc CheckLog(go.api.Request) returns (go.api.Response) {}
  rpc AddRecycle(go.api.Request) returns (go.api.Response) {}
  rpc RecycleList(go.api.Request) returns (go.api.Response) {}
  rpc RecycleDelete(go.api.Request) returns (go.api.Response) {}
  rpc HistoryViolation(go.api.Request) returns (go.api.Response) {}
//  rpc EnableViolation(go.api.Request) returns (go.api.Response) {} // 违规有效
//  rpc DisableViolation(go.api.Request) returns (go.api.Response) {} // 违规无效

//  rpc AddBatchIllegal(go.api.Request) returns (go.api.Response) {} // 违法导入
//  rpc AddIllegal(go.api.Request) returns (go.api.Response) {} // 违法导入
//  rpc ListIllegal(go.api.Request) returns (go.api.Response) {}
//  rpc GetIllegalById(go.api.Request) returns (go.api.Response) {}
//  rpc EditIllegal(go.api.Request) returns (go.api.Response) {}
//  rpc EnableIllegal(go.api.Request) returns (go.api.Response) {} // 违法扣分
//  rpc DisableIllegal(go.api.Request) returns (go.api.Response) {} // 违规不扣分
}

// 重点帮扶
service Emphasishelpdriver {

  rpc AddCause(go.api.Request) returns (go.api.Response) {} //
  rpc ListCause(go.api.Request) returns (go.api.Response) {} //
  rpc EditCause(go.api.Request) returns (go.api.Response) {}
  rpc Add(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {} //
  rpc GetById(go.api.Request) returns (go.api.Response) {} //
  rpc Terminate(go.api.Request) returns (go.api.Response) {} //
  rpc AddRecord(go.api.Request) returns (go.api.Response) {} //
  rpc ListRecord(go.api.Request) returns (go.api.Response) {} //
  rpc EditRecord(go.api.Request) returns (go.api.Response) {}
  rpc ListHistory(go.api.Request) returns (go.api.Response) {}
  rpc ListHistoryRecord(go.api.Request) returns (go.api.Response) {} //
}

// 整改
service Violationrectification {
  rpc Add(go.api.Request) returns (go.api.Response) {} //
  rpc List(go.api.Request) returns (go.api.Response) {} //
//  rpc Read(go.api.Request) returns (go.api.Response) {} //
  rpc GetDetail(go.api.Request) returns (go.api.Response) {}
  rpc Handle(go.api.Request) returns (go.api.Response) {}
  rpc Times(go.api.Request) returns (go.api.Response) {}
  rpc Revoke(go.api.Request) returns (go.api.Response) {}
//  rpc Process(go.api.Request) returns (go.api.Response) {}
//  rpc ListTalk(go.api.Request) returns (go.api.Response) {} //
//  rpc ReadTalk(go.api.Request) returns (go.api.Response) {} //
//  rpc ProcessTalk(go.api.Request) returns (go.api.Response) {} //
//  rpc TalkTips(go.api.Request) returns (go.api.Response) {} //
}

//车险档案
service Vehicleinsurance {
  rpc Import(go.api.Request) returns (go.api.Response) {}
  rpc ImportPDF(go.api.Request) returns (go.api.Response) {}
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Export(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc Update(go.api.Request) returns (go.api.Response) {}
  rpc Show(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc IsExist(go.api.Request) returns (go.api.Response) {}
}

//司机考核
service Driverassess {
  rpc AddDriverReward(go.api.Request) returns (go.api.Response) {}
  rpc DriverRewardDetail(go.api.Request) returns (go.api.Response) {}
  rpc EditDriverReward(go.api.Request) returns (go.api.Response) {}
  rpc DeleteDriverReward(go.api.Request) returns (go.api.Response) {}
  rpc ListDriverReward(go.api.Request) returns (go.api.Response) {}
  rpc ListDriverRewardExport(go.api.Request) returns (go.api.Response) {}
  rpc ManualCalcMonthReport(go.api.Request) returns (go.api.Response) {}
  rpc DriverSafeMonthReport(go.api.Request) returns (go.api.Response) {}
  rpc DriverSafeMonthReportExport(go.api.Request) returns (go.api.Response) {}
  rpc DriverServiceMonthReport(go.api.Request) returns (go.api.Response) {}
  rpc DriverServiceMonthReportExport(go.api.Request) returns (go.api.Response) {}
  rpc DriverSafeMonthReportDetail(go.api.Request) returns (go.api.Response) {}
  rpc DriverServiceMonthReportDetail(go.api.Request) returns (go.api.Response) {}
  rpc ManualCalcYearReport(go.api.Request) returns (go.api.Response) {}
  rpc DriverSafeYearReport(go.api.Request) returns (go.api.Response) {}
  rpc DriverSafeYearReportExport(go.api.Request) returns (go.api.Response) {}
  rpc DriverServiceYearReport(go.api.Request) returns (go.api.Response) {}
  rpc DriverServiceYearReportExport(go.api.Request) returns (go.api.Response) {}
  rpc DriverSafeYearReportDetail(go.api.Request) returns (go.api.Response) {}
  rpc DriverServiceYearReportDetail(go.api.Request) returns (go.api.Response) {}
  rpc DriverWaitWorkReport(go.api.Request) returns (go.api.Response) {}
  rpc DriverWaitWorkApplyLeave(go.api.Request) returns (go.api.Response) {}

  rpc DriverSafeDevoteReport(go.api.Request) returns (go.api.Response) {}
  rpc DriverServiceDevoteReport(go.api.Request) returns (go.api.Response) {}
  rpc DriverSafeDevoteEdit(go.api.Request) returns (go.api.Response) {}
  rpc DriverServiceDevoteEdit(go.api.Request) returns (go.api.Response) {}
  rpc AdditionalMoneySet(go.api.Request) returns (go.api.Response) {}
}


// 安全生产
service Safeproduction {
  rpc List(go.api.Request) returns (go.api.Response) {}
  rpc Create(go.api.Request) returns (go.api.Response) {}
  rpc Update(go.api.Request) returns (go.api.Response) {}
  rpc Scrap(go.api.Request) returns (go.api.Response) {}
  rpc Delete(go.api.Request) returns (go.api.Response) {}
  rpc Summary(go.api.Request) returns (go.api.Response) {}
  rpc IsExistThisMonth(go.api.Request) returns (go.api.Response) {}
}

