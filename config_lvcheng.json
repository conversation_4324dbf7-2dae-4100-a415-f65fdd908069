{"# 规范要求": "参考http://www.oetlink.com/关于《软件程序运行配置文件》管理规范技术要求.pdf", "# 重点说明": "版本一旦发布之后, 配置文件处于受控状态, 不可轻易变更, 如需变更请走变更流程", "# 环境变量": "production:生产环境 local:本地环境 develop:开发环境", "Env": "production", "# 日志等级": "Info:显示所有日志 error:显示错误日志", "DebugLevel": "Info", "# 文件绝对路径前缀": "", "AbsDirPath": "/mnt/www/", "# 文件相对路径前缀": "", "WebRoot": "webroot", "# 文件访问域名端口": "", "StaticFileHttpPrefix": "https://scs.oetlink.com:38085/", "# 是否关联调度": "true:关联调度 false:不关联调度", "IsRelationIss": false, "# 数据库配置": "Host:主机地址 Port:端口 DbName:数据库名称 User:登陆账号 Password:登陆密码", "Database": {"Host": "pgm-bp10w844p58x5qid168220.pg.rds.aliyuncs.com", "Port": 1921, "DbName": "erp", "User": "lvcheng_iss", "Password": "lvcheng123456atIss"}, "# ETCD配置": "Host:主机 Port:端口 Interval:心跳间隔  Ttl:失效时间", "Etcd": {"Host": "**************", "Port": 2379, "Interval": 10, "Ttl": 30}, "# micro配置": "ServiceName:服务名称", "Micro": {"ServiceName": "oet.scs.api.erp.v2"}, "# 小程序消息配置": "AppAccountId:对应的小程序ID DoorCheckTemplateId:门检消息模板ID Page:消息跳转的页面", "Mini": {"AppAccountId": 8, "DoorCheckTemplateId": "ayyjQYKdH2r8rzDKae-x7dyGcXagTX3HP8c-rjQU2d0", "Page": "erp/notice/index"}, "# 是否同步主数据的人员到ERP": "Enable:是否同步 CorporationIds:同步的顶级机构ID", "SyncMasterStaff": {"Enable": false, "CorporationIds": []}, "# 定时任务配置": "IsSendDoorCheckNotify:是否发送门检通知 IsSendLaborContractExpireNotify:是否发送劳动合同过期通知 IsSendStaffProbationExpireNotify:是否发送试用期到期通知 IsPreGenerateDoorCheckRecord:是否预先生成们门检记录 IsCheckTicketDataPermission是否检测票务数据权限 IsProcessAlarm:是否流程报警", "SchedulerJob": {"IsSendDoorCheckNotify": false, "IsSendLaborContractExpireNotify": false, "IsSendStaffProbationExpireNotify": false, "IsPreGenerateDoorCheckRecord": false, "IsCheckTicketDataPermission": false, "IsProcessAlarm": false, "IsCalcStaffArchiveReport": false}, "# 顶级机构ID": "", "TopCorporationId": 0, "# 蓝凌流程配置": "Enable:是否开启 SysId:系统标识 AuthAccount:授权账号 AuthPassword:授权密码 ServicePrefix:ERP接口前缀 LbpmDomain:蓝凌接口前缀 ToDoPrefixDomain:待办跳转地址前缀", "Lbpm": {"Enable": false, "SysId": "erp-tz", "AuthAccount": "ekp", "AuthPassword": "ekp", "ServicePrefix": "http://www.oetlink.com:5678/erp/v2/thirdparty/", "LbpmDomain": "https://10.10.10.10:9092/", "ToDoPrefixDomain": "https://www.oetlink.com"}, "# 适用的权限": "taizhou:台州系统权限 daishan:岱山系统权限 lvcheng:律橙平台权限", "PermissionSys": "lvcheng", "# 工单设备二维码跳转地址": "", "DeviceQrcodeUrl": "https://scs.oetlink.com:38088/erp", "# 票务模板银行对账时需要排除的线路": "", "TicketBankCheckExcludeLine": [], "DingTalkBpm": {"Enable": false, "AgentId": 0, "AppKey": "", "AppSecret": "", "AesKey": "", "Token": "", "DingTalkBpmProcessCode": {"# 员工离职申请流程模板": "", "staff_quit_apply_process": "", "# 物料申领申请流程模板": "", "material_requisition_apply_process": ""}}, "DingTalkAlarm": {"Enable": false, "Url": "https://oapi.dingtalk.com/robot/send?access_token=45159950d794ab1e4ebb6b6712fceeb870c71dc7381b363537c592aa25eceff9"}, "# 人员档案报表统计的机构ID": "", "StaffArchiveReportCorpId": 0}