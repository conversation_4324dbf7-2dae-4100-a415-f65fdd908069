package message

import (
	"app/org/scs/erpv2/api/model"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type MessageReadStatus int64 // (未读/已读)

const (
	UNREAD_1 MessageReadStatus = 1 // 未读
	READ_2   MessageReadStatus = 2 // 已读
)

type MessageProcessStatus int64 // (处理/回复)状态

const (
	PENDING_1 MessageProcessStatus = 1 // 待处理、未处理、处理中、未回复
	RESOLVE_2 MessageProcessStatus = 2 // 已处理、已回复
)

type MessageKind uint8 // 种类
const (
	PROCESS_1      MessageKind = 1 // 流程
	REMIND_2       MessageKind = 2 // 提醒
	NOTIFICATION_3 MessageKind = 3 // 通知
)

type MessageReadType uint8 // 种类
const (
	MESSAGE_READ_1    MessageReadType = 1 // 此消息为 读 类型
	MESSAGE_PROCESS_2 MessageReadType = 2 // 此消息为 处理 类型
)

// Message 消息
type Message struct {
	model.PkId
	model.GroupCorporation
	Type              string               `json:"Type" gorm:"column:type;comment:消息类型;type:varchar"`                                // 消息类型
	RelationId        int64                `json:"RelationId" gorm:"column:relationid;comment:关联消息类型id;type:bigint;default:0"`       // 关联消息类型id 为空表示对应多个ID  需从RelationParam字段中获取
	RelationTableName string               `json:"RelationTableName" gorm:"column:relationtablename;comment:关联表名;type:varchar(100)"` // 关联表名
	RelationParam     model.JSON           `json:"RelationParam" gorm:"column:relationparam;comment:关联数据;type:json"`                 // 关联数据
	SendUserId        int64                `json:"SendUserId" gorm:"column:senduserid;comment:发送人id;type:bigint;default:0"`          // 发送人id 为0表示系统发送
	SendUserName      string               `json:"SendUserName" gorm:"column:sendusername;comment:发送人;type:varchar(50);default:"`    // 发送人 为空表示系统发送
	RecvUserId        int64                `json:"RecvUserId" gorm:"column:recvuserid;comment:接收人id;type:bigint"`                    // 接收人id
	RecvUserName      string               `json:"RecvUserName" gorm:"column:recvusername;comment:接收人;type:varchar(50)"`
	ReadStatus        MessageReadStatus    `json:"ReadStatus" gorm:"column:readstatus;comment:UNREAD_1：接收人未读 READ_2：接收人已读;type:smallint"`                          // UNREAD_1：接收人未读 READ_2：接收人已读 【ReadStatus 已读、未读】和【ProcessStatus 待处理、已处理】一条消息只能持有其中一个状态
	ProcessStatus     MessageProcessStatus `json:"ProcessStatus" gorm:"column:processstatus;comment:PENDING_1：接收人待处理、未处理、处理中、未回复 RESOLVE_2：已处理、已回复;type:smallint"` // PENDING_1：接收人待处理、未处理、处理中、未回复 RESOLVE_2：已处理、已回复 【ReadStatus 已读、未读】和【ProcessStatus 待处理、已处理】一条消息只能持有其中一个状态
	ReadType          MessageReadType      `json:"ReadType" gorm:"column:readtype;comment:消息读取类型 1：未读、已读 2：未处理、已处理;type:smallint"`                                 // 消息读取类型 1：未读、已读 2：未处理、已处理
	Origin            string               `json:"Origin" gorm:"column:origin;comment:消息来源;type:varchar(128);default:''"`                                          // 消息来源
	Kind              MessageKind          `json:"Kind" gorm:"column:kind;comment:消息种类;type:smallint;default:0"`                                                   // 消息种类
	Title             string               `json:"Title" gorm:"column:title;comment:标题;type:varchar(100)"`                                                         // 标题
	Content           string               `json:"Content" gorm:"column:content;comment:通知内容展示文本;type:text"`                                                       // 通知内容展示文本

	model.Timestamp
}

func (m *Message) AddTx(tx *gorm.DB) error {
	if m.Id == 0 {
		m.Id = model.Id()
	}
	return tx.Create(m).Error
}

func (m *Message) Create() error {
	m.Id = model.Id()
	return model.DB().Create(&m).Error
}

func (m *Message) List(types, origins []string, kinds []int64, startAt, endAt time.Time, omitOrigin []string, paginator model.Paginator) ([]Message, int64, error) {
	var rsp []Message
	var totalCount int64
	tx := model.DB().Model(&Message{}).Where("RecvUserId = ?", m.RecvUserId)

	if !startAt.IsZero() {
		tx = tx.Where("CreatedAt >= ?", startAt)
	}

	if !endAt.IsZero() {
		tx = tx.Where("CreatedAt < ?", endAt)
	}

	if len(types) > 0 {
		tx = tx.Where("Type IN ?", types)
	}

	if len(origins) > 0 {
		tx = tx.Where("Origin IN ?", origins)
	}

	if len(kinds) > 0 {
		tx = tx.Where("Kind IN ?", kinds)
	}

	if m.ReadStatus > 0 {
		tx.Where("ReadStatus = ?", m.ReadStatus)
	}

	if m.ProcessStatus > 0 {
		tx.Where("ProcessStatus = ?", m.ProcessStatus)
	}

	if len(omitOrigin) > 0 {
		tx.Where("id not in (?)", model.DB().Model(&Message{}).Select("Id").Where("RecvUserId = ?", m.RecvUserId).Where("Origin in ?", omitOrigin))
	}

	err := tx.Order("CreatedAt DESC").Count(&totalCount).Offset(paginator.Offset).Limit(paginator.Limit).Scan(&rsp).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, 0, nil
		} else {
			return nil, 0, err
		}
	}

	return rsp, totalCount, nil
}

func (m *Message) Read(tx *gorm.DB, userId int64) error {
	return tx.Model(&Message{}).Where("Id = ? AND RecvUserId = ?", m.Id, userId).Scan(m).Update("ReadStatus", READ_2).Error
}

func (m *Message) GetMessagesBy(relationId int64, relationTableName string) []Message {
	var messages []Message
	model.DB().Model(&Message{}).Select("Id", "RecvUserId", "RecvUserName", "CreatedAt", "ReadStatus", "ProcessStatus", "ReadType").
		Where("RelationId =? AND RelationTableName = ?", relationId, relationTableName).
		Order("CreatedAt DESC").Scan(&messages)

	return messages
}

func (m *Message) Delete(ids []int64) error {
	return model.DB().Where("Id IN ?", ids).Delete(&Message{}).Error
}

func (m *Message) TransactionDelete(tx *gorm.DB, id int64) error {
	return tx.Where("Id = ?", id).Delete(&Message{}).Error
}

func (m *Message) TransactionDeleteByRelationId(tx *gorm.DB, relationId int64, tableName string) error {
	return tx.Where("RelationId = ? AND RelationTableName = ?", relationId, tableName).Delete(&Message{}).Error
}

func (m *Message) ReadAll(tx *gorm.DB, omitOrigin []string) ([]Message, error) {
	var rsp []Message
	err := tx.Model(&Message{}).Where("RecvUserId = ? AND ReadStatus = ? AND ProcessStatus = ? AND ReadType = ? AND Id NOT IN (?)", m.RecvUserId, UNREAD_1, 0, MESSAGE_READ_1,
		model.DB().Model(&Message{}).Select("Id").Where("RecvUserId = ?", m.RecvUserId).Where("Origin in ?", omitOrigin)).Scan(&rsp).Update("ReadStatus", READ_2).Error
	return rsp, err
}

func (m *Message) UnReadList(omitOrigin []string) ([]Message, error) {
	var rsp []Message
	err := model.DB().Model(&Message{}).Where("RecvUserId = ? AND (ReadStatus = ? OR ProcessStatus = ?) AND Id NOT IN (?)", m.RecvUserId, UNREAD_1, PENDING_1,
		model.DB().Model(&Message{}).Select("Id").Where("RecvUserId = ?", m.RecvUserId).Where("Origin in ?", omitOrigin)).Scan(&rsp).Error
	return rsp, err
}

func (m *Message) GetDetail(id int64) Message {
	var rsp Message
	model.DB().Model(&Message{}).Where("Id = ?", id).Scan(&rsp)
	return rsp
}

func (m *Message) EditProcessStatus(tx *gorm.DB, userId int64) error {

	return tx.Model(&Message{}).Where("Type = ? AND RelationId = ? AND RecvUserId = ?", m.Type, m.RelationId, userId).Select("ReadStatus", "ProcessStatus").Updates(m).Error

}

func (m *Message) EditReadByRelationId(msgType string, relationIds []int64, userId int64) error {
	return model.DB().Model(&Message{}).Where("Type = ? AND RelationId IN ? AND RecvUserId = ?", msgType, relationIds, userId).Update("ReadStatus", READ_2).Error
}

// UpdateProcessMessageStatus 并行审批时更新其他未审批人消息的状态
func (m *Message) UpdateProcessMessageStatus(relationId int64, relationTableName string, receiver int64) error {
	return model.DB().Model(&Message{}).Where("RelationId = ? AND RelationTableName = ? AND ReadType = ? AND RecvUserId = ?", relationId, relationTableName, MESSAGE_PROCESS_2, receiver).UpdateColumn("ProcessStatus", RESOLVE_2).Error
}

// ProcessTerminatedOrAbandonedUpdateMessageStatus 流程被撤回或者废弃时更新此流程对应的消息状态
func (m *Message) ProcessTerminatedOrAbandonedUpdateMessageStatus(relationId int64, relationTableName string) error {
	return model.DB().Model(&Message{}).Where("RelationId = ? AND RelationTableName = ? AND ReadType = ?", relationId, relationTableName, MESSAGE_PROCESS_2).UpdateColumn("ProcessStatus", RESOLVE_2).Error
}

type ListHandleRsp struct {
	Message
	CurrentHandlerUserName string `json:"CurrentHandlerUserName" gorm:"column:currenthandlerusername"` //  // 当前处理人
	LbpmProcessStatus      int64  `json:"LbpmProcessStatus" gorm:"column:lbpmprocessstatus"`           //  // 流程状态
}

// ListTrafficAccidentProcessHandle 安全事故流程处理列表
func (m *Message) ListTrafficAccidentProcessHandle(userId int64, creatorUserName string, origins []string, corporationIds []int64,
	lineId int64, license, driver string, readStatus MessageReadStatus, processStatus MessageProcessStatus,
	startAt, endAt time.Time, paginator model.Paginator,
	lbpmStatus int64, handlerUserName string, odby, od string) ([]ListHandleRsp, int64, error) {

	var rsp []ListHandleRsp
	var totalCount int64

	tx := model.DB().Model(&Message{}).Select("messages.*, lap.CurrentHandlerUserName AS CurrentHandlerUserName, lap.Status AS LbpmProcessStatus").
		Joins("LEFT JOIN lbpm_apply_processes AS lap ON messages.RelationId=lap.FormInstanceId").Where("messages.RecvUserId = ?", userId)

	if lbpmStatus > 0 {
		tx.Where("lap.Status = ?", lbpmStatus)
	}

	if handlerUserName != "" {
		tx.Where("lap.CurrentHandlerUserName LIKE ?", "%"+handlerUserName+"%")
	}

	if creatorUserName != "" {
		tx.Where("messages.SendUserName LIKE ?", "%"+creatorUserName+"%")
	}
	if len(origins) > 0 {
		tx.Where("messages.Origin IN ?", origins)
	}
	if len(corporationIds) > 0 {
		tx.Where("(messages.RelationParam -> 'Param' ->> 'CorporationId')::BIGINT IN ?", corporationIds)
	}

	if lineId > 0 {
		tx.Where("(messages.RelationParam -> 'Param' ->> 'LineId')::BIGINT = ?", lineId)
	}

	if license != "" {
		tx.Where("messages.RelationParam -> 'Param' ->> 'License' LIKE ?", "%"+license+"%")
	}

	if driver != "" {
		tx.Where("messages.RelationParam -> 'Param' ->> 'DriverName' LIKE ?", "%"+driver+"%")
	}

	if readStatus > 0 {
		tx.Where("messages.ReadStatus = ?", readStatus)
	}

	if processStatus > 0 {
		tx.Where("messages.ProcessStatus = ?", processStatus)
	}

	if !startAt.IsZero() {
		tx.Where("messages.RelationParam -> 'Param' ->> 'HappenAt' >= ?", startAt)
	}

	if !endAt.IsZero() {
		tx.Where("messages.RelationParam -> 'Param' ->> 'HappenAt' < ?", endAt)
	}

	// todo: 此处排序存在问题， json体不同 字段不一致
	var orderBy string
	if odby == "ApplyAt" {
		orderBy = fmt.Sprintf("lap.ApplyAt %s", od)
	} else if odby == "HappenAt" {
		orderBy = fmt.Sprintf("messages.RelationParam -> 'Param' -> 'HappenAt' %s", od)
	}

	err := tx.Order(orderBy).Count(&totalCount).Offset(paginator.Offset).Limit(paginator.Limit).Scan(&rsp).Error
	if err != nil {
		return nil, 0, err
	}

	return rsp, totalCount, nil
}

func (m *Message) ReadAllTrafficAccidentProcessHandle(userId int64, origins []string) error {
	return model.DB().Model(&Message{}).Where("RecvUserId = ? AND ReadStatus = ? AND ProcessStatus = ? AND ReadType = ? AND Origin IN ?", userId, UNREAD_1, 0, MESSAGE_READ_1, origins).Update("ReadStatus", READ_2).Error
}

func (m *Message) UnReadTrafficAccidentProcessHandleList(recvUserId int64, origins []string) ([]Message, error) {
	var rsp []Message
	err := model.DB().Model(&Message{}).Where("RecvUserId = ? AND (ReadStatus = ? OR ProcessStatus = ?) AND Origin IN ?", recvUserId, UNREAD_1, PENDING_1, origins).Scan(&rsp).Error
	return rsp, err
}

func (m *Message) GetTrafficAccidentMessage(origins []string) ([]Message, error) {
	var rsp []Message
	err := model.DB().Model(&Message{}).Where("Origin IN ?", origins).Scan(&rsp).Error
	return rsp, err
}

func (m *Message) UpdateRelationParam(param model.JSON) error {
	return model.DB().Model(&Message{}).Where("Id = ?", m.Id).UpdateColumn("RelationParam", param).Error
}
