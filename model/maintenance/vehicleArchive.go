package maintenance

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type MaintenanceVehicleArchiveReport struct {
	model.PkId
	TopCorporationId    int64   `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;"`
	VehicleId           int64   `json:"VehicleId" gorm:"column:vehicleid;type:integer;comment:车辆Id"`
	License             string  `json:"License" gorm:"column:license;type:varchar(50);comment:车牌"`
	VehicleModel        string  `json:"VehicleModel" gorm:"column:vehiclemodel;type:varchar(100);comment:车辆型号"`
	PowerType           int64   `json:"PowerType" gorm:"column:powertype;type:smallint;comment:动力类型 0:未知;1:柴油车(传统);2:汽油车(传统);3:混合动力车(新能源);4:纯电动车(新能源);5:CNG(新能源);6:LNG;7:纯电动(双路BMS)"`
	ReportMonth         int64   `json:"ReportMonth" gorm:"column:reportmonth;type:integer;comment:统计月份(格式YYYYMM)"`
	StartMileage        float64 `json:"StartMileage" gorm:"column:startmileage;type:decimal(10,2);comment:月初里程表数"`
	EndMileage          float64 `json:"EndMileage" gorm:"column:endmileage;type:decimal(10,2);comment:月末里程表数"`
	YearTotalMileage    float64 `json:"YearTotalMileage" gorm:"column:yeartotalmileage;type:decimal(10,2);comment:本年累计行驶里程"`
	HistoryTotalMileage float64 `json:"HistoryTotalMileage" gorm:"column:historytotalmileage;type:decimal(10,2);comment:历史累计行驶里程"`
	MonthlyEnergy       float64 `json:"MonthlyEnergy" gorm:"column:monthlyenergy;type:decimal(10,2);comment:月能耗(电: kWh/油: L)"`
	EnergyCost          int64   `json:"EnergyCost" gorm:"column:energycost;type:integer;comment:能耗费用(分)"`
	EnergyPer100km      float64 `json:"EnergyPer100km" gorm:"column:energyper100km;type:decimal(10,2);comment:百公里能耗"`
	NingxingSecond      int64   `json:"NingxingSecond" gorm:"column:ningxingsecond;type:integer;comment:宁兴二保金额（单位：分）"`
	NingxingFirst       int64   `json:"NingxingFirst" gorm:"column:ningxingfirst;type:integer;comment:宁兴一保金额（单位：分）"`
	NingxingRepair      int64   `json:"NingxingRepair" gorm:"column:ningxingrepair;type:integer;comment:宁兴维修金额（单位：分）"`
	QianghuaSecond      int64   `json:"QianghuaSecond" gorm:"column:qianghuasecond;type:integer;comment:强华二保金额（单位：分）"`
	QianghuaFirst       int64   `json:"QianghuaFirst" gorm:"column:qianghuafirst;type:integer;comment:强华一保金额（单位：分）"`
	QianghuaRepair      int64   `json:"QianghuaRepair" gorm:"column:qianghuarepair;type:integer;comment:强华维修金额（单位：分）"`
	OtherRepair         int64   `json:"OtherRepair" gorm:"column:otherrepair;type:integer;comment:其他维修金额（单位：分）"`
	MonthlyRepairCost   int64   `json:"MonthlyRepairCost" gorm:"column:monthlyrepaircost;type:integer;comment:月修理费合计（单位：分）"`
	AnnualTotalCost     int64   `json:"AnnualTotalCost" gorm:"column:annualtotalcost;type:integer;comment:年度累计费用（单位：分）"`
	model.Timestamp

	VehicleModelEnergyAverage float64 `json:"VehicleModelEnergyAverage" gorm:"-"` //同型号100KM能耗均值
}

func (m *MaintenanceVehicleArchiveReport) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *MaintenanceVehicleArchiveReport) Create() error {
	return model.DB().Create(&m).Error
}

func (m *MaintenanceVehicleArchiveReport) Update() error {
	return model.DB().Select("*").Updates(&m).Error
}

func (m *MaintenanceVehicleArchiveReport) FirstBy(id int64) MaintenanceVehicleArchiveReport {
	var report MaintenanceVehicleArchiveReport
	model.DB().Model(&MaintenanceVehicleArchiveReport{}).Where("Id = ?", id).First(&report)

	return report
}

func (m *MaintenanceVehicleArchiveReport) FirstByVehicleIdAndMonth(topCorporationId, vehicleId, month int64) MaintenanceVehicleArchiveReport {
	var report MaintenanceVehicleArchiveReport
	model.DB().Model(&MaintenanceVehicleArchiveReport{}).Where("TopCorporationId = ? AND VehicleId = ? AND ReportMonth = ?", topCorporationId, vehicleId, month).First(&report)

	return report
}

func (m *MaintenanceVehicleArchiveReport) GetBy(topCorporationId int64, license string, month int64, paginator model.Paginator) ([]MaintenanceVehicleArchiveReport, int64) {
	tx := model.DB().Model(&MaintenanceVehicleArchiveReport{}).Where("TopCorporationId = ?", topCorporationId)

	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}

	if month > 0 {
		tx.Where("ReportMonth = ?", month)
	}

	var count int64
	tx.Count(&count)

	var reports []MaintenanceVehicleArchiveReport
	tx.Order("CreatedAt desc").Offset(paginator.Offset).Limit(paginator.Limit).Find(&reports)

	return reports, count
}

func (m *MaintenanceVehicleArchiveReport) GetLastEndMileage(topCorporationId, vehicleId, month int64) float64 {
	var mileage float64
	model.DB().Model(&MaintenanceVehicleArchiveReport{}).Select("EndMileage").Where("TopCorporationId = ? AND VehicleId = ? AND ReportMonth < ?", topCorporationId, vehicleId, month).Order("ReportMonth DESC").Limit(1).Scan(&mileage)

	return mileage
}

// GetYearTotalMileage 查询车辆的年总里程
func (m *MaintenanceVehicleArchiveReport) GetYearTotalMileage(topCorporationId, vehicleId, startMonth, endMonth int64) float64 {
	var mileage float64
	model.DB().Model(&MaintenanceVehicleArchiveReport{}).Select("COALESCE(SUM(EndMileage-StartMileage),0) as TotalMileage").Where("TopCorporationId = ? AND VehicleId = ? AND ReportMonth < ? AND ReportMonth >= ?", topCorporationId, vehicleId, endMonth, startMonth).Limit(1).Scan(&mileage)

	return mileage
}

// GetHistoryTotalMileage 查询车辆的总里程
func (m *MaintenanceVehicleArchiveReport) GetHistoryTotalMileage(topCorporationId, vehicleId int64, month int64) float64 {
	var mileage float64
	model.DB().Model(&MaintenanceVehicleArchiveReport{}).Select("COALESCE(SUM(EndMileage-StartMileage),0) as TotalMileage").Where("TopCorporationId = ? AND VehicleId = ? AND ReportMonth < ?", topCorporationId, vehicleId, month).Scan(&mileage)

	return mileage
}

// GetAnnualTotalCost 查询车辆当年累计费用
func (m *MaintenanceVehicleArchiveReport) GetAnnualTotalCost(topCorporationId, vehicleId, startMonth, endMonth int64) int64 {
	var totalCost int64
	model.DB().Model(&MaintenanceVehicleArchiveReport{}).Select("COALESCE(SUM(MonthlyRepairCost),0) as TotalCost").Where("TopCorporationId = ? AND VehicleId = ? AND ReportMonth < ? AND ReportMonth >= ?", topCorporationId, vehicleId, endMonth, startMonth).Scan(&totalCost)

	return totalCost
}

// GetBrandAverage 查询同车辆型号，当前月份，百公里能耗的均值
func (m *MaintenanceVehicleArchiveReport) GetBrandAverage(topCorporationId int64, vehicleModel string, month int64) float64 {
	var average float64
	model.DB().Model(&MaintenanceVehicleArchiveReport{}).Select("COALESCE(AVG(EnergyPer100km),0) as Average").Where("TopCorporationId = ? AND VehicleModel = ? AND ReportMonth = ?", topCorporationId, vehicleModel, month).Scan(&average)

	return average
}

type PowerTypeEnergyCost struct {
	Energy float64 `json:"Energy" gorm:"column:energy"`
	Cost   int64   `json:"Cost" gorm:"column:cost"`
}

// GetEnergyAndCost 查询不同动力类型下车辆的累计能耗和费用
func (m *MaintenanceVehicleArchiveReport) GetEnergyAndCost(topCorporationId int64, powerTypes []int64, month int64) PowerTypeEnergyCost {
	var res PowerTypeEnergyCost
	tx := model.DB().Model(&MaintenanceVehicleArchiveReport{}).Select("COALESCE(SUM(MonthlyEnergy),0) as Energy", "COALESCE(SUM(MonthlyRepairCost),0) as Cost").
		Where("TopCorporationId = ? AND PowerType IN ?", topCorporationId, powerTypes)

	if month > 0 {
		tx.Where("ReportMonth = ?", month)
	}

	tx.Scan(&res)

	return res
}
