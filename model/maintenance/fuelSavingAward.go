package maintenance

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type FuelSavingAwardReport struct {
	model.PkId
	model.Timestamp
	model.Corporations
	StaffArchiveId           int64  `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:人员档案Id"`
	StaffId                  int64  `json:"StaffId" gorm:"column:staffid;type:integer;comment:主数据人员ID;"`
	StaffName                string `json:"StaffName" gorm:"column:staffname;type:varchar;comment:人员姓名"`
	JobNumber                string `json:"JobNumber" gorm:"column:jobnumber;type:varchar;comment:主数据人员工号;"`
	LineId                   int64  `json:"LineId" gorm:"column:lineid;comment:线路id;type:integer"`
	LineName                 string `json:"LineName" gorm:"column:linename;comment:线路;type:varchar"`
	IDCard                   string `json:"IDCard" gorm:"column:idcard;type:varchar;comment:身份证"`
	License                  string `json:"License" gorm:"column:license;type:varchar;comment:车牌号"`
	Month                    string `json:"Month" gorm:"column:month;type:varchar;comment:月份 YYYYMM"`
	RegistrationDate         string `json:"RegistrationDate" gorm:"column:registrationdate;type:varchar;comment:上牌日期"`
	CarModel                 string `json:"CarModel" gorm:"column:carmodel;type:varchar;comment:车型"`
	CarLength                int64  `json:"CarLength" gorm:"column:carlength;type:integer;comment:车长 毫米"`
	FuelType                 string `json:"FuelType" gorm:"column:fueltype;type:varchar;comment:燃料类型"`
	Cash                     int64  `json:"Cash" gorm:"column:cash;type:integer;comment:现金 分"`
	ICCard                   int64  `json:"ICCard" gorm:"column:iccard;type:integer;comment:ic卡 分"`
	Revenue                  int64  `json:"Revenue" gorm:"column:revenue;type:integer;comment:营收 分"`
	Mileage                  int64  `json:"Mileage" gorm:"column:mileage;type:integer;comment:运营公里 单位：米"`
	Fuel                     int64  `json:"Fuel" gorm:"column:fuel;type:integer;comment:单车油料 单位：毫升"`
	PersonMileage            int64  `json:"PersonMileage" gorm:"column:personmileage;type:integer;comment:个人公里 单位：米"`
	FuelConsumption          int64  `json:"FuelConsumption" gorm:"column:fuelconsumption;type:integer;comment:单车油耗 毫升"`
	BaseQuota                int64  `json:"BaseQuota" gorm:"column:basequota;type:integer;comment:基础定额 分"`
	RevenueAllowance         int64  `json:"RevenueAllowance" gorm:"column:revenueallowance;type:integer;comment:营收补贴 分"`
	RoadAllowance            int64  `json:"RoadAllowance" gorm:"column:roadallowance;type:integer;comment:道路补贴 分"`
	VehicleAgeAllowance      int64  `json:"VehicleAgeAllowance" gorm:"column:vehicleageallowance;type:integer;comment:车龄补贴 分"`
	ApprovedAmount           int64  `json:"ApprovedAmount" gorm:"column:approvedamount;type:integer;comment:核定金额 分"`
	AirConditioningAllowance int64  `json:"AirConditioningAllowance" gorm:"column:airconditioningallowance;type:integer;comment:空调补贴 分"`
	SaveFuel                 int64  `json:"SaveFuel" gorm:"column:SaveFuel;type:integer;comment:节省油耗 毫升"`
	Reward                   int64  `json:"Reward" gorm:"column:reward;type:integer;comment:奖励 分"`
	//
	CorporationName string `json:"CorporationName" gorm:"-"`
	CorporationId   int64  `json:"CorporationId" gorm:"-"`
}

func (m *FuelSavingAwardReport) BeforeCreate(tx *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *FuelSavingAwardReport) Create() error {
	return model.DB().Create(&m).Error
}

func (m *FuelSavingAwardReport) Update() error {
	return model.DB().Updates(&m).Error
}

func (m *FuelSavingAwardReport) FindByStaffId(StaffId int64, Month string) (data []FuelSavingAwardReport, err error) {
	err = model.DB().Where("StaffId = ?", StaffId).Where("Month = ?", Month).Find(&data).Error
	return
}

func (m *FuelSavingAwardReport) FindOnly(StaffId, LineId int64, Month string) (data FuelSavingAwardReport, err error) {
	err = model.DB().Where("StaffId = ?", StaffId).Where("LineId = ?", LineId).Where("Month = ?", Month).Find(&data).Error
	return
}

func (m *FuelSavingAwardReport) List(CorporationIds []int64, Month string, StaffId int64, JobNumber string, LineId int64, Paginator model.Paginator) (Data []FuelSavingAwardReport, TotalCount int64, err error) {
	db := model.DB().Model(&FuelSavingAwardReport{}).Scopes(model.WhereCorporations(CorporationIds))
	if Month != "" {
		db = db.Where("Month = ?", Month)
	}
	if StaffId != 0 {
		db = db.Where("StaffId = ?", StaffId)
	}
	if JobNumber != "" {
		db = db.Where("JobNumber LIKE ?", "%"+JobNumber+"%")
	}
	if LineId != 0 {
		db = db.Where("LineId = ?", LineId)
	}
	db.Count(&TotalCount)
	if Paginator.Limit > 0 {
		db = db.Offset(Paginator.Offset).Limit(Paginator.Limit)
	}
	err = db.Find(&Data).Error
	return
}

func (m *FuelSavingAwardReport) IsModified(month string) bool {
	if month == "" {
		return false
	}
	modifyMonth, _ := time.ParseInLocation("200601", month, time.Local)
	modifyOverTime := time.Date(modifyMonth.Year(), modifyMonth.Month()+1, 9, 0, 0, 0, 0, time.Local)
	now := time.Now()
	if now.Unix() >= modifyOverTime.Unix() {
		return false
	}
	return true
}
