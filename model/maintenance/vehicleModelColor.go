package maintenance

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type VehicleModelColor struct {
	model.PkId
	TopCorporationId int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:机构ID"`
	ModelCode        string `json:"ModelCode" gorm:"column:modelcode;type:varchar;comment:车辆型号"`
	Color            string `json:"Color" gorm:"column:color;type:varchar(20);comment:颜色值"`
	model.Timestamp
	model.OpUser
}

func (v *VehicleModelColor) BeforeCreate(db *gorm.DB) error {
	v.Id = model.Id()
	return nil
}

func (v *VehicleModelColor) Create(tx *gorm.DB, colors []VehicleModelColor) error {
	return tx.Model(&VehicleModelColor{}).Create(&colors).Error
}

func (v *VehicleModelColor) Delete(tx *gorm.DB, topCorporationId int64) error {
	return tx.Where("TopCorporationId = ?", topCorporationId).Delete(&VehicleModelColor{}).Error
}

func (v *VehicleModelColor) GetAll(topCorporationId int64) []VehicleModelColor {
	var colors []VehicleModelColor
	model.DB().Model(&VehicleModelColor{}).Where("TopCorporationId = ?", topCorporationId).Find(&colors)

	return colors
}
