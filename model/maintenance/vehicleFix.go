package maintenance

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type VehicleFixRecord struct {
	model.PkId
	TopCorporationId int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;"`
	CorporationId    int64           `json:"CorporationId" gorm:"column:corporationid;type:bigint;"`
	FixOffice        int64           `json:"FixOffice" gorm:"column:fixoffice;type:smallint;comment:维修厂 1强化修理厂 2宁兴修理厂"`
	VehicleId        int64           `json:"VehicleId" gorm:"column:vehicleid;type:bigint;comment:车辆ID"`
	License          string          `json:"License" gorm:"column:license;type:varchar(64);comment:车牌"`
	ReportAt         model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:维修日期"`
	Mileage          float64         `json:"Mileage" gorm:"column:mileage;type:decimal(10,2);comment:里程"`
	LaborFee         int64           `json:"LaborFee" gorm:"column:laborfee;type:integer;comment:工时费 单位：分"`
	MaterialFee      int64           `json:"MaterialFee" gorm:"column:materialfee;type:integer;comment:材料费 单位：分"`
	model.OpUser
	model.Timestamp

	CorporationName string                 `json:"CorporationName" gorm:"-"`
	Items           []VehicleFixRecordItem `json:"Items" gorm:"-"`
}

type VehicleFixRecordItem struct {
	model.PkId
	VehicleFixRecordId int64  `json:"VehicleFixRecordId" gorm:"column:vehiclefixrecordid;type:bigint;"`
	ItemName           string `json:"ItemName" gorm:"column:itemname;type:varchar;comment:修理项目"`
	LaborFee           int64  `json:"LaborFee" gorm:"column:laborfee;type:integer;comment:工时费 单位：分"`
	MaterialFee        int64  `json:"MaterialFee" gorm:"column:materialfee;type:integer;comment:材料费 单位：分"`
	model.Timestamp

	Materials []VehicleFixRecordItemMaterial `json:"Materials" gorm:"-"`
}

type VehicleFixRecordItemMaterial struct {
	model.PkId
	VehicleFixRecordId     int64   `json:"VehicleFixRecordId" gorm:"column:vehiclefixrecordid;type:bigint;"`
	VehicleFixRecordItemId int64   `json:"VehicleFixRecordItem" gorm:"column:vehiclefixrecorditemid;type:bigint;"`
	VehicleId              int64   `json:"VehicleId" gorm:"column:vehicleid;type:bigint;comment:车辆ID"`
	MaterialId             int64   `json:"MaterialId" gorm:"column:materialid;type:bigint;comment:材料ID"`
	MaterialName           string  `json:"MaterialName" gorm:"column:materialname;type:varchar;comment:材料名称"`
	MaterialUnit           string  `json:"MaterialUnit" gorm:"column:materialunit;type:varchar;comment:材料计量单位"`
	Count                  float64 `json:"Count" gorm:"column:count;type:decimal;precision:2;comment:数量"`
	Price                  int64   `json:"Price" gorm:"column:price;type:integer;comment:单价 单位：分"`
	MaterialFee            int64   `json:"MaterialFee" gorm:"column:materialfee;type:integer;comment:材料费 单位：分"`
	LaborFee               int64   `json:"LaborFee" gorm:"column:laborfee;type:integer;comment:工时费 单位：分"`
	TimeLen                float64 `json:"TimeLen" gorm:"column:timelen;type:decimal(10,2);precision:2;comment:时间寿命,单位：月"`
	MileageLen             float64 `json:"MileageLen" gorm:"column:mileagelen;type:decimal(10,2);precision:2;comment:里程寿命 单位：公里"`
	model.Timestamp
}

func (m *VehicleFixRecord) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *VehicleFixRecordItem) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *VehicleFixRecordItemMaterial) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *VehicleFixRecord) Create() error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Create(&m).Error
		if err != nil {
			return err
		}

		if len(m.Items) > 0 {
			for _, item := range m.Items {
				item.VehicleFixRecordId = m.Id
				err = tx.Create(&item).Error
				if err != nil {
					return err
				}
				if len(item.Materials) > 0 {
					for _, material := range item.Materials {
						material.VehicleFixRecordId = m.Id
						material.VehicleFixRecordItemId = item.Id
						err = tx.Create(&material).Error
						if err != nil {
							return err
						}
					}
				}
			}
		}
		return nil
	})
}

func (m *VehicleFixRecord) Update() error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Updates(&m).Error
		if err != nil {
			return err
		}
		//删除之前的维修项目和材料
		err = tx.Where("VehicleFixRecordId = ?", m.Id).Delete(&VehicleFixRecordItem{}).Error
		if err != nil {
			return err
		}
		err = tx.Where("VehicleFixRecordId = ?", m.Id).Delete(&VehicleFixRecordItemMaterial{}).Error
		if err != nil {
			return err
		}

		if len(m.Items) > 0 {
			for _, item := range m.Items {
				item.VehicleFixRecordId = m.Id
				err = tx.Create(&item).Error
				if err != nil {
					return err
				}
				if len(item.Materials) > 0 {
					for _, material := range item.Materials {
						material.VehicleFixRecordId = m.Id
						material.VehicleFixRecordItemId = item.Id
						err = tx.Create(&material).Error
						if err != nil {
							return err
						}
					}
				}
			}
		}
		return nil
	})
}

func (m *VehicleFixRecord) Delete(id int64) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Where("Id = ?", id).Delete(&VehicleFixRecord{}).Error
		if err != nil {
			return err
		}
		err = tx.Where("VehicleFixRecordId = ?", id).Delete(&VehicleFixRecordItem{}).Error
		if err != nil {
			return err
		}
		err = tx.Where("VehicleFixRecordId = ?", id).Delete(&VehicleFixRecordItemMaterial{}).Error
		if err != nil {
			return err
		}

		return nil
	})
}

func (m *VehicleFixRecord) FirstById(id int64) VehicleFixRecord {
	var record VehicleFixRecord
	model.DB().Model(&VehicleFixRecord{}).Where("Id = ?", id).First(&record)

	return record
}

func (m *VehicleFixRecord) GetBy(topCorporationId int64, license, itemName string, fixOffice int64, startAt, endAt time.Time, paginator model.Paginator) ([]VehicleFixRecord, int64) {
	var records []VehicleFixRecord
	tx := model.DB().Model(&VehicleFixRecord{}).Where("TopCorporationId = ?", topCorporationId)

	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}

	if itemName != "" {
		tx.Where("Id IN (?)", model.DB().Model(&VehicleFixRecordItem{}).Select("VehicleFixRecordId").Where("ItemName LIKE ?", "%"+itemName+"%"))
	}

	if fixOffice > 0 {
		tx.Where("FixOffice = ?", fixOffice)
	}

	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt)
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt)
	}

	var count int64
	tx.Count(&count)

	tx.Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&records)
	return records, count
}

func (m *VehicleFixRecordItem) GetBy(recordId int64) []VehicleFixRecordItem {
	var items []VehicleFixRecordItem
	model.DB().Model(&VehicleFixRecordItem{}).Where("VehicleFixRecordId = ?", recordId).Find(&items)

	return items
}

func (m *VehicleFixRecordItemMaterial) GetBy(recordItemId int64) []VehicleFixRecordItemMaterial {
	var materials []VehicleFixRecordItemMaterial
	model.DB().Model(&VehicleFixRecordItemMaterial{}).Where("VehicleFixRecordItemId = ?", recordItemId).Find(&materials)

	return materials
}

// GetFeeBy 获取车辆在不同修理厂的费用
func (m *VehicleFixRecord) GetFeeBy(topCorporationId int64, vehicleId int64, fixOffice int64, startAt, endAt time.Time) int64 {
	var fee int64
	model.DB().Model(&VehicleFixRecord{}).Select("COALESCE(SUM(LaborFee+MaterialFee),0) as Fee").Where("TopCorporationId = ? AND VehicleId = ? AND FixOffice = ? AND ReportAt >= ? AND ReportAt < ?", topCorporationId, vehicleId, fixOffice, startAt, endAt).Scan(&fee)

	return fee
}
