package maintenance

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
	"time"
)

const (
	NingxingFixOffice     = 2
	QianghuaFixOffice     = 1
	FirstMaintenanceType  = 1
	SecondMaintenanceType = 2
)

type VehicleMaintenanceRecord struct {
	model.PkId
	TopCorporationId int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;"`
	VehicleId        int64           `json:"VehicleId" gorm:"column:vehicleid;type:bigint;comment:车辆ID"`
	License          string          `json:"License" gorm:"column:license;type:varchar(64);comment:车牌"`
	ReportAt         model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:保养日期"`
	MaintenanceType  int64           `json:"MaintenanceType" gorm:"column:maintenancetype;type:bigint;comment:保养类型 1一保 2保"`
	MaintenanceFee   int64           `json:"MaintenanceFee" gorm:"column:maintenancefee;type:integer;comment:保养费"`
	FixOffice        int64           `json:"FixOffice" gorm:"column:fixoffice;type:smallint;comment:维修厂 1强华修理厂 2宁兴修理厂"`
	Status           int64           `json:"Status" gorm:"column:status;type:smallint;comment:是否完成  1是 2否"`
	model.OpUser
	model.Timestamp
}

func (m *VehicleMaintenanceRecord) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *VehicleMaintenanceRecord) Create() error {
	return model.DB().Create(&m).Error
}

func (m *VehicleMaintenanceRecord) CreateMany(records []VehicleMaintenanceRecord) error {
	return model.DB().Create(&records).Error
}

func (m *VehicleMaintenanceRecord) UpdateStatus(ids []int64) error {
	return model.DB().Model(&VehicleMaintenanceRecord{}).Where("Id IN ?", ids).Update("status", util.StatusForTrue).Error
}

func (m *VehicleMaintenanceRecord) Delete(ids []int64) error {
	return model.DB().Where("Id IN ?", ids).Delete(&VehicleMaintenanceRecord{}).Error
}

func (m *VehicleMaintenanceRecord) FirstById(id int64) VehicleMaintenanceRecord {
	var record VehicleMaintenanceRecord
	model.DB().Model(&VehicleMaintenanceRecord{}).Where("Id = ?", id).First(&record)

	return record
}

func (m *VehicleMaintenanceRecord) GetBy(topCorporationId int64, license string, maintenanceType, status int64, startAt, endAt time.Time, paginator model.Paginator) ([]VehicleMaintenanceRecord, int64) {
	var records []VehicleMaintenanceRecord
	tx := model.DB().Model(&VehicleMaintenanceRecord{}).Where("TopCorporationId = ?", topCorporationId)

	if license != "" {
		tx.Where("license LIKE ?", "%"+license+"%")
	}

	if maintenanceType > 0 {
		tx.Where("MaintenanceType = ?", maintenanceType)
	}

	if status > 0 {
		tx.Where("Status = ?", status)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt)
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt)
	}

	var count int64
	tx.Count(&count)

	tx.Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&records)
	return records, count
}

// GetFeeBy 获取车辆在日期范围内，不同保养类型和不同修理厂的保养费合计
func (m *VehicleMaintenanceRecord) GetFeeBy(topCorporationId int64, vehicleId int64, maintenanceType, fixOffice int64, startAt, endAt time.Time) int64 {
	var fee int64
	model.DB().Model(&VehicleMaintenanceRecord{}).Select("COALESCE(SUM(MaintenanceFee),0) as Fee").Where("TopCorporationId = ? AND VehicleId = ? AND MaintenanceType = ? AND FixOffice = ? AND ReportAt >= ? AND ReportAt < ?", topCorporationId, vehicleId, maintenanceType, fixOffice, startAt, endAt).Scan(&fee)
	return fee
}
