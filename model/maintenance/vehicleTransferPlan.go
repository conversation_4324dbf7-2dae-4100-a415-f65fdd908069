package maintenance

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type VehicleTransferPlan struct {
	model.PkId
	TopCorporationId         int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:机构ID"`
	Code                     string          `json:"Code" gorm:"column:code;type:varchar;default:;comment:调动编号"`                    //调动批次
	CodeSequence             int64           `json:"CodeSequence" gorm:"column:codesequence;type:integer;default:1;comment:一天中的序号"` //一天中的批次序号
	Title                    string          `json:"Title" gorm:"column:title;type:varchar(200);comment:名称"`
	PlanClassIdxVehicleCount model.JSON      `json:"PlanClassIdxVehicleCount" gorm:"column:planclassidxvehiclecount;type:json;comment:计划班次数和车辆数"`
	PublishStatus            int64           `json:"PublishStatus" gorm:"column:publishstatus;type:smallint;comment:发布状态 -1草稿 1待发布 2审批中  3已发布 4已退回"`
	ApplyStatus              int64           `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;default:0;comment:审批状态 0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃"`
	PublishAt                model.LocalTime `json:"PublishAt" gorm:"column:publishat;type:timestamp;comment:发布时间"`
	CurrentVersionId         int64           `json:"CurrentVersionId" gorm:"column:currentversionid;type:bigint;comment:当前版本ID 关联VehicleTransferPlanVersion表"`
	model.OpUser
	model.Timestamp

	VersionNo            int64                              `json:"VersionNo" gorm:"-"`
	LatestVersionDetails []VehicleTransferPlanVersionDetail `json:"LatestVersionDetails" gorm:"-"`
}

func (p *VehicleTransferPlan) TableName() string {
	return "vehicle_transfer_plans"
}

func (p *VehicleTransferPlan) ApplyStatusFieldName() string {
	return "applystatus"
}

func (p *VehicleTransferPlan) BeforeCreate(tx *gorm.DB) error {
	p.Id = model.Id()
	if p.PublishStatus != util.VehicleTransferPlanPublishStatusForDraft {
		//查询今天最大的序号
		codeSequence := p.GetMaxCodeSequence()
		p.CodeSequence = codeSequence + 1
		p.Code = fmt.Sprintf("%v%04d", time.Now().Format("20060102"), p.CodeSequence)
	}

	return nil
}

func (p *VehicleTransferPlan) BeforeUpdate(tx *gorm.DB) error {
	if p.PublishStatus != util.VehicleTransferPlanPublishStatusForDraft && p.Code == "" {
		//查询今天最大的序号
		codeSequence := p.GetMaxCodeSequence()
		p.CodeSequence = codeSequence + 1
		p.Code = fmt.Sprintf("%v%04d", time.Now().Format("20060102"), p.CodeSequence)
		p.CreatedAt = model.LocalTime(time.Now())
	}

	return nil
}

func (p *VehicleTransferPlan) GetMaxCodeSequence() int64 {
	var codeSequence int64
	model.DB().Model(&VehicleTransferPlan{}).Select("CodeSequence").Where("CreatedAt >= ?", time.Now().Format(model.DateFormat)).Order("CodeSequence DESC").First(&codeSequence)

	return codeSequence
}

func (p *VehicleTransferPlan) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&p).Error
}

func (p *VehicleTransferPlan) TransactionUpdate(tx *gorm.DB) error {
	return tx.Model(&VehicleTransferPlan{}).Select("Title", "PlanClassIdxVehicleCount", "CurrentVersionId", "OpUserId", "OpUserName").Where("id = ?", p.Id).Updates(&p).Error
}

func (p *VehicleTransferPlan) TransactionUpdateColumns(tx *gorm.DB, val interface{}) error {
	return tx.Model(&VehicleTransferPlan{}).Where("id = ?", p.Id).UpdateColumns(val).Error
}
func (p *VehicleTransferPlan) UpdateColumns(val interface{}) error {
	return model.DB().Model(&VehicleTransferPlan{}).Where("id = ?", p.Id).UpdateColumns(val).Error
}

func (p *VehicleTransferPlan) TransactionDelete(tx *gorm.DB) error {
	err := tx.Where("id = ?", p.Id).Delete(&VehicleTransferPlan{}).Error
	if err != nil {
		return err
	}
	err = tx.Where("VehicleTransferPlanId = ?", p.Id).Delete(&VehicleTransferPlanVersion{}).Error
	if err != nil {
		return err
	}

	err = tx.Where("VehicleTransferPlanId = ?", p.Id).Delete(&VehicleTransferPlanVersionDetail{}).Error
	if err != nil {
		return err
	}
	return nil
}

func (p *VehicleTransferPlan) GetBy(topCorporationId, publishStatus, applyStatus int64, title, code string, paginator model.Paginator) ([]VehicleTransferPlan, int64) {
	var plans []VehicleTransferPlan
	tx := model.DB().Model(&VehicleTransferPlan{}).Where("TopCorporationId = ?", topCorporationId)
	if publishStatus > 0 {
		tx.Where("PublishStatus = ?", publishStatus)
	}
	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	}

	if title != "" {
		tx.Where("Title LIKE ?", "%"+title+"%")
	}

	if code != "" {
		tx.Where("Code LIKE ?", "%"+code+"%")
	}

	var count int64
	tx.Count(&count)

	tx.Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&plans)

	return plans, count
}

func (p *VehicleTransferPlan) FirstBy(id int64) VehicleTransferPlan {
	var plan VehicleTransferPlan
	model.DB().Model(&VehicleTransferPlan{}).Where("id = ?", id).First(&plan)
	return plan
}

type VehicleTransferPlanVersion struct {
	model.PkId
	VehicleTransferPlanId int64 `json:"VehicleTransferPlanId" gorm:"column:vehicletransferplanid;type:bigint;comment:方案ID"`
	VersionNo             int64 `json:"VersionNo" gorm:"column:versionno;type:integer;default:1;comment:版本号 默认1 递增1"` //调动批次
	model.OpUser
	model.Timestamp

	IsDraft bool                               `json:"-" gorm:"-"`
	Details []VehicleTransferPlanVersionDetail `json:"Details" gorm:"-"`
}

func (pv *VehicleTransferPlanVersion) BeforeCreate(tx *gorm.DB) error {
	pv.Id = model.Id()

	if !pv.IsDraft {
		maxVersionNo := pv.GetMaxVersionNo()
		pv.VersionNo = maxVersionNo + 1
	}
	return nil
}

func (pv *VehicleTransferPlanVersion) GetMaxVersionNo() int64 {
	var version int64
	model.DB().Model(&VehicleTransferPlanVersion{}).Select("VersionNo").Where("VehicleTransferPlanId = ?", pv.VehicleTransferPlanId).Order("VersionNo DESC").First(&version)

	return version
}

func (pv *VehicleTransferPlanVersion) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&pv).Error
}

func (pv *VehicleTransferPlanVersion) FirstBy(id int64) VehicleTransferPlanVersion {
	var version VehicleTransferPlanVersion
	model.DB().Model(&VehicleTransferPlanVersion{}).Where("Id = ?", id).First(&version)

	return version
}

func (pv *VehicleTransferPlanVersion) GetBy(planId int64) []VehicleTransferPlanVersion {
	var versions []VehicleTransferPlanVersion

	model.DB().Model(&VehicleTransferPlanVersion{}).Where("VehicleTransferPlanId = ?", planId).Order("CreatedAt DESC").Find(&versions)
	return versions
}

type VehicleTransferPlanVersionDetail struct {
	model.PkId
	VehicleTransferPlanId        int64  `json:"VehicleTransferPlanId" gorm:"column:vehicletransferplanid;type:bigint;comment:方案ID"`
	VehicleTransferPlanVersionId int64  `json:"VehicleTransferPlanVersionId" gorm:"column:vehicletransferplanversionid;type:bigint;comment:版本ID"`
	VehicleId                    int64  `json:"VehicleId" gorm:"column:vehicleid;type:bigint;comment:车车辆ID"`
	License                      string `json:"License" gorm:"column:license;type:varchar(50);comment:车牌"`
	OutCorporationId             int64  `json:"OutCorporationId" gorm:"column:outcorporationid;type:bigint;default:0;comment:调出机构Id"`
	OutCorporationName           string `json:"OutCorporationName" gorm:"column:outcorporationname;type:varchar;default:;comment:调出机构"`
	OutLineId                    int64  `json:"OutLineId" gorm:"column:outlineid;type:bigint;default:0;comment:调出线路Id"`
	OutLineName                  string `json:"OutLineName" gorm:"column:outlinename;type:varchar;default:;comment:调出线路"`
	InCorporationId              int64  `json:"InCorporationId" gorm:"column:incorporationid;type:bigint;default:0;comment:调入机构Id"`
	InCorporationName            string `json:"InCorporationName" gorm:"column:incorporationname;type:varchar;default:;comment:调入机构"`
	InLineId                     int64  `json:"InLineId" gorm:"column:inlineid;type:bigint;default:0;comment:调入线路Id"`
	InLineName                   string `json:"InLineName" gorm:"column:inlinename;type:varchar;default:;comment:调入线路"`
	model.OpUser
	model.Timestamp
}

func (pvd *VehicleTransferPlanVersionDetail) BeforeCreate(tx *gorm.DB) error {
	pvd.Id = model.Id()
	return nil
}

func (pvd *VehicleTransferPlanVersionDetail) TransactionCreate(tx *gorm.DB, details []VehicleTransferPlanVersionDetail) error {
	return tx.Create(&details).Error
}

func (pvd *VehicleTransferPlanVersionDetail) GetBy(versionId int64) []VehicleTransferPlanVersionDetail {
	var details []VehicleTransferPlanVersionDetail
	model.DB().Model(&VehicleTransferPlanVersionDetail{}).Where("VehicleTransferPlanVersionId = ?", versionId).Find(&details)

	return details
}
