package maintenance

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type VehicleMigration struct {
	model.PkId
	TopCorporationId int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:机构ID"`
	Code             string `json:"Code" gorm:"column:code;type:varchar;default:;comment:调动批次"`                                               //调动批次
	CodeSequence     int64  `json:"CodeSequence" gorm:"column:codesequence;type:integer;default:1;comment:一天中的批次序号"`                          //一天中的批次序号
	OutCorporationId int64  `json:"OutCorporationId" gorm:"column:outcorporationid;type:bigint;default:0;comment:调出机构Id" validate:"required"` //调出机构Id
	InCorporationId  int64  `json:"InCorporationId" gorm:"column:incorporationid;type:bigint;default:0;comment:调入机构Id" validate:"required"`   //调入机构Id
	//IsNow            int64            `json:"IsNow" gorm:"column:isnow;type:smallint;default:1;comment:审批完调动是否立即生效，1是  2否"`                                         //审批完调动是否立即生效，1是  2否
	UseAt            model.LocalTime `json:"UseAt" gorm:"column:useat;type:timestamp;comment:调入生效时间" validate:"required"` //调动时间，非立即调动时填写
	AcceptUserId     int64           `json:"AcceptUserId" gorm:"column:acceptuserid;type:bigint;default:0;comment:调入机构接收人ID" validate:"required"`
	AcceptUserName   string          `json:"AcceptUserName" gorm:"column:acceptusername;type:varchar;default:;comment:调入机构接收人"`
	MetaInfo         model.JSON      `json:"MetaInfo" gorm:"column:metainfo;type:json;comment:交接单信息"`
	ApplyStatus      int64           `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;default:0;comment:审批状态 0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃"` //审批状态 0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃
	FormStep         int64           `json:"FormStep" gorm:"column:formstep;type:smallint;default:1;comment:表单节点 1发起  2接收人填写 3已填写"`
	IsDone           int64           `json:"IsDone" gorm:"column:isdone;type:smallint;default:2;comment:是否已完成调动  1是 2否"`  //是否已完成调动  1是 2否
	IsCancel         int64           `json:"IsCancel" gorm:"column:iscancel;type:smallint;default:2;comment:是否作废  1是 2否"` //是否作废  1是 2否
	CancelReason     string          `json:"CancelReason" gorm:"column:cancelreason;type:varchar;comment:作废原因"`
	CancelOpUserId   int64           `json:"CancelOpUserId" gorm:"column:cancelopuserid;type:bigint;comment:作废操作人ID"`
	CancelOpUserName string          `json:"CancelOpUserName" gorm:"column:cancelopusername;type:varchar;comment:作废操作人"`
	CancelOpAt       model.LocalTime `json:"CancelOpAt" gorm:"column:cancelopat;type:timestamp;comment:作废操作时间"`
	model.OpUser
	model.Timestamp

	Records            []VehicleMigrationRecord `json:"Records"`
	OutCorporationName string                   `json:"OutCorporationName" gorm:"-"`
	InCorporationName  string                   `json:"InCorporationName" gorm:"-"`
	ProcessHandler     string                   `json:"ProcessHandler" gorm:"-"`
	OutCorporationInfo []string                 `json:"OutCorporationInfo" gorm:"-"`
	InCorporationInfo  []string                 `json:"InCorporationInfo" gorm:"-"`
	UserType           int64                    `json:"UserType" gorm:"-"`
	ProcessTitle       string                   `json:"ProcessTitle" gorm:"-"`
	FormInstanceId     int64                    `json:"FormInstanceId" gorm:"-"`
	ProcessId          string                   `json:"ProcessId" gorm:"-"`
	CurrentHandler     string                   `json:"CurrentHandler" gorm:"-"`
}

type VehicleMigrationRecord struct {
	model.PkId
	VehicleMigrationId int64  `json:"VehicleMigrationId" gorm:"column:vehiclemigrationid;type:bigint;comment:关联vehicle_migrations表主键"`
	VehicleId          int64  `json:"VehicleId" gorm:"column:vehicleid;type:integer;comment:车辆ID" validate:"required"` //车辆ID
	License            string `json:"License" gorm:"column:license;type:varchar;comment:车牌" validate:"required"`       //车牌
	OutLineId          int64  `json:"OutLineId" gorm:"column:outlineid;type:integer;comment:调出线路ID"`                   //调出线路ID
	OutLine            string `json:"OutLine" gorm:"column:outline;type:varchar;comment:调出线路名称"`                       //调出线路名称
	InLineId           int64  `json:"InLineId" gorm:"column:inlineid;type:integer;comment:调入线路ID"`                     //调入线路ID
	InLine             string `json:"InLine" gorm:"column:inline;type:varchar;comment:调入线路名称"`                         //调入线路名称
	IsMotor            int64  `json:"IsMotor" gorm:"column:ismotor;type:smallint;default:2;comment:是否机动线路 1是 2否"`
	model.Timestamp
}

func (m *VehicleMigration) TableName() string {
	return "vehicle_migrations"
}
func (m *VehicleMigration) ApplyStatusFieldName() string {
	return "applystatus"
}

func (m *VehicleMigration) BeforeCreate(tx *gorm.DB) error {
	m.Id = model.Id()
	//查询今天最大的序号
	codeSequence := m.GetMaxCodeSequence()
	m.CodeSequence = codeSequence + 1
	m.Code = fmt.Sprintf("%v%04d", time.Now().Format("20060102"), m.CodeSequence)

	return nil
}

func (m *VehicleMigrationRecord) BeforeCreate(tx *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *VehicleMigrationRecord) UpdateInLine() error {
	return model.DB().Where("Id = ?", m.Id).Select("InLineId", "InLine", "IsMotor").Updates(&m).Error
}

func (m *VehicleMigrationRecord) Delete(vehicleMigrationId int64) error {
	return model.DB().Where("VehicleMigrationId = ?", vehicleMigrationId).Delete(&VehicleMigrationRecord{}).Error
}

func (m *VehicleMigration) Create() error {
	return model.DB().Create(&m).Error
}

func (m *VehicleMigration) Update() error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&VehicleMigration{}).Where("Id = ?", m.Id).Omit("Records").Updates(&m).Error
		if err != nil {
			return err
		}

		err = tx.Where("VehicleMigrationId = ?", m.Id).Delete(&VehicleMigrationRecord{}).Error
		if err != nil {
			return err
		}
		err = tx.Model(m).Association("Records").Replace(&m.Records)
		if err != nil {
			return err
		}

		return nil
	})
}

func (m *VehicleMigration) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *VehicleMigration) TransactionUpdate(t *gorm.DB) error {
	return t.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&VehicleMigration{}).Where("Id = ?", m.Id).Select("*").Omit("Records").Omit("Id", "Code", "CodeSequence").Updates(&m).Error
		if err != nil {
			return err
		}

		err = tx.Where("VehicleMigrationId = ?", m.Id).Delete(&VehicleMigrationRecord{}).Error
		if err != nil {
			return err
		}
		err = tx.Model(&m).Association("Records").Replace(&m.Records)
		if err != nil {
			return err
		}

		return nil
	})
}

func (m *VehicleMigration) FirstBy(id int64) VehicleMigration {
	var record VehicleMigration
	model.DB().Model(&VehicleMigration{}).Preload("Records").First(&record, id)
	return record
}

func (m *VehicleMigration) UpdateApplyStatus(id, status int64) error {
	return model.DB().Model(&VehicleMigration{}).Where("Id = ?", id).Update("ApplyStatus", status).Error
}

func (m *VehicleMigration) UpdateFormStep(id, step int64) error {
	return model.DB().Model(&VehicleMigration{}).Where("Id = ?", id).Update("FormStep", step).Error
}

func (m *VehicleMigration) GetMaxCodeSequence() int64 {
	var codeSequence int64
	model.DB().Model(&VehicleMigration{}).Select("CodeSequence").Where("CreatedAt >= ?", time.Now().Format(model.DateFormat)).Order("CodeSequence DESC").First(&codeSequence)

	return codeSequence
}

func (m *VehicleMigration) Delete() error {
	return model.DB().Delete(&m).Error
}

func (m *VehicleMigration) GetBy(corpIds []int64, license, code, processHandler string, outCorpId, inCorpId, applyStatus int64, startUseAt, endUseAt, startAt, endAt time.Time, paginator model.Paginator) ([]VehicleMigration, int64) {
	tx := model.DB().Model(&VehicleMigration{}).Preload("Records").Where("OutCorporationId IN ? OR InCorporationId IN ?", corpIds, corpIds)
	if code != "" {
		tx.Where("Code LIKE ?", "%"+code+"%")
	}
	if license != "" {
		subQuery := model.DB().Model(&VehicleMigrationRecord{}).Select("VehicleMigrationId").Where("License LIKE ?", "%"+license+"%")
		tx.Where("Id IN (?)", subQuery)
	}
	if processHandler != "" {
		subQuery := model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("ItemTableName = ? AND CurrentHandlerUserName LIKE ?", m.TableName(), "%"+processHandler+"%")
		tx.Where("Id IN (?)", subQuery)
	}
	if outCorpId > 0 {
		tx.Where("OutCorporationId = ?", outCorpId)
	}
	if inCorpId > 0 {
		tx.Where("InCorporationId = ?", inCorpId)
	}
	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	}

	if !startUseAt.IsZero() {
		tx.Where("UseAt >= ?", startUseAt.Format(model.DateFormat))
	}
	if !endUseAt.IsZero() {
		tx.Where("UseAt <= ?", endUseAt.Format(model.DateFormat))
	}

	if !startAt.IsZero() {
		tx.Where("CreatedAt >= ?", startAt.Format(model.TimeFormat))
	}
	if !endAt.IsZero() {
		tx.Where("CreatedAt <= ?", endAt.Format(model.TimeFormat))
	}

	var count int64
	tx.Count(&count)

	var records []VehicleMigration
	tx.Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&records)

	return records, count
}

func (m *VehicleMigration) DashboardGetBy(license, processTitle, code, userName, handlerName string, applyStatus, formStep int64, paginator model.Paginator) ([]VehicleMigration, int64) {
	var migrations []VehicleMigration
	tx := model.DB().Model(&VehicleMigration{}).Preload("Records")

	if license != "" {
		subQuery := model.DB().Model(&VehicleMigrationRecord{}).Select("VehicleMigrationId").Where("License LIKE ?", "%"+license+"%")
		tx.Where("Id IN (?)", subQuery)
	}

	if processTitle != "" {
		tx.Where("Id IN (?)", model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ?", config.VehicleMigrationApplyFormTemplate).Where("Title LIKE ?", "%"+processTitle+"%"))
	}

	if code != "" {
		tx.Where("Code LIKE ?", "%"+code+"%")
	}

	if userName != "" {
		tx.Where("OpUserName LIKE ?", "%"+userName+"%")
	}

	if handlerName != "" {
		subQuery := model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ? AND CurrentHandlerUserName LIKE ?", config.VehicleMigrationApplyFormTemplate, "%"+handlerName+"%")
		tx.Where("Id IN (?)", subQuery)
	}

	if formStep > 0 {
		tx.Where("FormStep = ?", formStep)
	}

	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	}

	var count int64
	tx.Count(&count)

	tx.Order("CreatedAt desc").Offset(paginator.Offset).Limit(paginator.Limit).Find(&migrations)
	return migrations, count
}

func (m *VehicleMigration) GetByAllForRpc(corporationId int64, vehicleIds []int64, applyStatuses []int64, startUseAt, endUseAt time.Time) []VehicleMigration {
	tx := model.DB().Model(&VehicleMigration{}).Preload("Records")
	if corporationId > 0 {
		tx.Where("OutCorporationId = ? OR InCorporationId = ?", corporationId, corporationId)
	}
	if len(vehicleIds) > 0 {
		subQuery := model.DB().Model(&VehicleMigrationRecord{}).Select("VehicleMigrationId").Where("VehicleId IN ?", vehicleIds)
		tx.Where("Id IN (?)", subQuery)
	}

	if len(applyStatuses) > 0 {
		tx.Where("ApplyStatus IN ?", applyStatuses)
	}

	if !startUseAt.IsZero() {
		tx.Where("UseAt >= ?", startUseAt.Format(model.DateFormat))
	}
	if !endUseAt.IsZero() {
		tx.Where("UseAt <= ?", endUseAt.Format(model.DateFormat))
	}
	var records []VehicleMigration
	tx.Order("CreatedAt DESC").Find(&records)

	return records
}

func (m *VehicleMigration) GetRecords() []VehicleMigrationRecord {
	var records []VehicleMigrationRecord
	model.DB().Model(&VehicleMigrationRecord{}).Where("VehicleMigrationId = ?", m.Id).Find(&records)

	return records
}

func (m *VehicleMigration) SetDone() error {
	return model.DB().Model(&VehicleMigration{}).Where("Id = ?", m.Id).Update("IsDone", util.StatusForTrue).Error
}

// GetEnableMigrationRecord 查询可以调动但还未调动的记录
func (m *VehicleMigration) GetEnableMigrationRecord() []VehicleMigration {
	var records []VehicleMigration
	model.DB().Model(&VehicleMigration{}).Where("ApplyStatus = ? AND IsDone = ? AND IsCancel = ?", util.ApplyStatusForDone, util.StatusForFalse, util.StatusForFalse).Where("UseAt <= ?", time.Now().Format(model.TimeFormat)).Find(&records)

	return records
}

func (m *VehicleMigration) GetVehicleMigrationForDoing() []VehicleMigration {
	var records []VehicleMigration
	model.DB().Model(&VehicleMigration{}).Where("ApplyStatus = ? AND IsDone = ?", util.ApplyStatusForDoing, util.StatusForFalse).Find(&records)

	return records
}

func (m *VehicleMigration) IsExistDoingMigration(vehicleId int64) bool {
	var count int64

	subQuery := model.DB().Model(&VehicleMigrationRecord{}).Select("VehicleMigrationId").Where("VehicleId = ?", vehicleId)

	model.DB().Model(&VehicleMigration{}).Where("Id IN (?)", subQuery).Where("ApplyStatus = ? AND IsDone = ?", util.ApplyStatusForDoing, util.StatusForFalse).Count(&count)

	return count > 0
}

func (m *VehicleMigration) UpdateCancelStatus(id int64, value interface{}) error {
	return model.DB().Model(&VehicleMigration{}).Where("Id = ?", id).Updates(value).Error
}
