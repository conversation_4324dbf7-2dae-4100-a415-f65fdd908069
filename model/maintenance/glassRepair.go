package maintenance

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type GlassRepairType int8

const (
	GLASS_REPAIR_GR_1 GlassRepairType = 1
	DRAFT_GR_2        GlassRepairType = 2
)

type GlassRepairStatus int64

const (
	PENDING_GR_1 = 1 // 审批中
	RESOLVE_GR_2 = 2 // 已完成
	REBUT_GR_3   = 3 // 已驳回
	REVOKE_GR_4  = 4 // 已撤销
	DISCARD_GR_5 = 5 // 已废弃
)

type CurrentOperationType int64

const (
	NO_OPERATION_GR_1 CurrentOperationType = 1 // 无操作
	WRITE_GR_2        CurrentOperationType = 2 // 需要填写表单信息

)

type FormStatusType int64

const (
	DEFAULT_GR_1 FormStatusType = 1 // 默认
	REPAIR_GR_2  FormStatusType = 2 // 维修信息
	PAYMENT_GR_3 FormStatusType = 3 // 付款信息

)

// GlassRepair 玻璃维修
type GlassRepair struct {
	model.PkId
	model.Corporations // 提交人的机构

	Type GlassRepairType `json:"Type" gorm:"column:type;type:smallint;comment:维修类型 1玻璃维修 2草稿数据;"` // 维修类型 1玻璃维修 2草稿数据
	//Code        string            `json:"Code" gorm:"column:code;type:varchar(50);uniqueIndex:gr_code_unique;comment:事故编号(玻璃维修编号，非安全-事故上报的编号);"`                // 事故编号(玻璃维修编号，非安全-事故上报的编号)
	ApplyStatus GlassRepairStatus `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;default:1;comment:审批状态 0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃"` // 工单状态 审批状态 PENDING_GR_1:待处理、流转中 RESOLVE_2:已完成、已完结, REBUT_GR_3-驳回 REVOKE_GR_4-撤销, DISCARD_GR_5-废弃

	SubmitUserId   int64  `json:"SubmitUserId" gorm:"column:submituserid;type:bigint;comment:提交人;"`            // 提交人
	SubmitUserName string `json:"SubmitUserName" gorm:"column:submitusername;type:varchar(50);comment:提交人姓名;"` // 提交人姓名

	SubmitAt *model.LocalTime `json:"SubmitAt" gorm:"column:submitat;type:timestamp;comment:提交时间;"` // 提交时间
	HappenAt model.LocalTime  `json:"HappenAt" gorm:"column:happenat;type:timestamp;comment:发生时间;"` // 发生时间

	License string `json:"License" gorm:"column:license;type:varchar(10);comment:车牌号;"` // 车牌号
	LineId  int64  `json:"LineId" gorm:"column:lineid;type:bigint;comment:线路id;"`       // 线路id
	Line    string `json:"Line" gorm:"column:line;type:varchar(50);comment:线路;"`        // 线路
	//Road             string     `json:"Road" gorm:"column:road;type:varchar(100);comment:路段;"`                          // 路段
	WeatherCondition int64      `json:"WeatherCondition" gorm:"column:weathercondition;type:smallint;comment:天气情况;"`    // 天气情况 1-雾,2-雨,3-冰雹,4-晴,5-其他,6-台风,7-雨雪,8-阴
	DamageType       int64      `json:"DamageType" gorm:"column:damagetype;type:smallint;comment:损坏类型 1-自爆，2-破损，3-其他;"` // 损坏类型 1-自爆，2-破损，3-其他
	Files            model.JSON `json:"Files" gorm:"column:files;type:json;comment:基础信息附件;"`                            // 基础信息附件

	RepairAmount    int64      `json:"RepairAmount" gorm:"column:repairamount;type:integer;comment:修复金额 单位分;"`                                                                   // 修复金额 单位分
	RepairShop      int64      `json:"RepairShop" gorm:"column:repairshop;type:smallint;comment:维修车间 1:岩客运西站 2:椒江景元路 3:椒江客运总站 4:椒江工人路 5:路桥桐屿 6:路桥金清 7:综合场站 8:回浦 9:洪家;default:0"` // *维修车间 1:岩客运西站 2:椒江景元路 3:椒江客运总站 4:椒江工人路 5:路桥桐屿 6:路桥金清 7:综合场站 8:回浦 9:洪家
	InWarranty      int64      `json:"InWarranty" gorm:"column:inwarranty;type:smallint;comment:质保期内 1是 2否;default:0"`                                                           // 质保期内 1是 2否
	RepairDoc       model.JSON `json:"RepairDoc" gorm:"column:repairdoc;type:json;comment:维修单据附件;"`                                                                              // 维修单据附件
	VehicleDamage   model.JSON `json:"VehicleDamage" gorm:"column:vehicledamage;type:json;comment:车损照片附件;"`                                                                      // 车损照片附件
	VehicleRepaired model.JSON `json:"VehicleRepaired" gorm:"column:vehiclerepaired;type:json;comment:修复后照片附件;"`                                                                 // 修复后照片附件

	IsDirectIndemnity int64      `json:"IsDirectIndemnity" gorm:"column:isdirectindemnity;type:smallint;comment:直接赔付 1-是 2-否;"` // 直接赔付 1-是 2-否
	PaymentFlow       string     `json:"PaymentFlow" gorm:"column:paymentflow;type:varchar(50);comment:付款流向;"`                  // 付款流向
	PaymentAmount     int64      `json:"PaymentAmount" gorm:"column:paymentamount;type:integer;comment:付款金额 单位分;"`              // 付款金额 单位分
	Payee             string     `json:"Payee" gorm:"column:payee;type:varchar(50);comment:收款人;"`                               // 收款人
	Bank              string     `json:"Bank" gorm:"column:bank;type:varchar(50);comment:开户行;"`                                 // 开户行
	BankAccount       string     `json:"BankAccount" gorm:"column:bankaccount;type:varchar(50);comment:银行账户;"`                  // 银行账户
	ApplyContent      string     `json:"ApplyContent" gorm:"column:applycontent;type:text;comment:申请内容;"`                       // 申请内容
	PaymentMethod     string     `json:"PaymentMethod" gorm:"column:paymentmethod;type:varchar(50);comment:付款方式;"`              // 付款方式
	PaymentFiles      model.JSON `json:"PaymentFiles" gorm:"column:paymentfiles;type:json;comment:付款信息附件;"`                     // 付款信息附件

	CurrentOperation CurrentOperationType `json:"CurrentOperation" gorm:"column:currentoperation;type:smallint;default:1;comment:当前操作;"` // 当前操作 1无需操作 2填写维修记录 3填写付款信息
	FormStatus       FormStatusType       `json:"FormStatus" gorm:"column:formstatus;type:smallint;default:1;comment:表单填写到哪一步;"`         // 表单填写到哪一步
	model.Timestamp
}

func (gr *GlassRepair) TableName() string {
	return "glass_repairs"
}

func (gr *GlassRepair) ApplyStatusFieldName() string {
	return "applystatus"
}

func (gr *GlassRepair) TemplateFormId() string {
	return config.GlassRepairFormTemplate
}

func (gr *GlassRepair) Add() error {
	return model.DB().Create(gr).Error
}
func (gr *GlassRepair) AddTx(tx *gorm.DB) error {
	return tx.Create(gr).Error
}

func (gr *GlassRepair) Update() error {
	return model.DB().Model(&GlassRepair{}).Where("Id = ?", gr.Id).
		Select("License", "LineId", "Line", "HappenAt", "WeatherCondition", "DamageType", "InWarranty", "Files", "RepairShop", "SubmitUserId", "SubmitUserName",
			"Type", "SubmitAt", "GroupId", "CompanyId", "BranchId", "DepartmentId", "FleetId").
		Updates(gr).Error
}

func (gr *GlassRepair) UpdateTx(tx *gorm.DB) error {
	return tx.Model(&GlassRepair{}).Where("Id = ?", gr.Id).
		Select("License", "LineId", "Line", "HappenAt", "WeatherCondition", "DamageType", "InWarranty", "Files", "RepairShop", "SubmitUserId", "SubmitUserName",
			"Type", "SubmitAt", "GroupId", "CompanyId", "BranchId", "DepartmentId", "FleetId").
		Updates(gr).Error
}

// List 需要展示查看人的草稿记录
func (gr *GlassRepair) List(userId int64, corpIds []int64, lineId int64, license string, damageType int64, applyStatus GlassRepairStatus, startAt, endAt time.Time, paginator model.Paginator) ([]GlassRepair, int64, error) {
	var rsp []GlassRepair
	var totalCount int64

	tx := model.DB().Model(&GlassRepair{})

	if userId > 0 {
		tx.Where("(Type = ?", GLASS_REPAIR_GR_1).Or("Type = ? AND SubmitUserId = ?)", DRAFT_GR_2, userId)
	} else {
		tx.Where("Type = ?", GLASS_REPAIR_GR_1)
	}

	if len(corpIds) > 0 {
		tx.Scopes(model.WhereCorporations(corpIds))
	}

	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}

	if damageType > 0 {
		tx.Where("DamageType = ?", damageType)
	}

	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	}

	if !startAt.IsZero() {
		tx.Where("SubmitAt >= ?", startAt)
	}

	if !endAt.IsZero() {
		tx.Where("SubmitAt < ?", endAt)
	}

	err := tx.Count(&totalCount).Order("SubmitAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&rsp).Error
	if err != nil {
		return nil, 0, err
	}

	return rsp, totalCount, nil
}

func (gr *GlassRepair) GetDetail(id int64) error {
	return model.DB().Model(&GlassRepair{}).Where("Id = ?", id).Scan(gr).Error
}

// EditWithFormStatusRepair 更新维修信息
func (gr *GlassRepair) EditWithFormStatusRepair() error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&GlassRepair{}).Where("Id = ?", gr.Id).Select("RepairAmount", "RepairDoc", "VehicleDamage", "VehicleRepaired").Updates(gr).Error
		if err != nil {
			return err
		}

		err = tx.Model(&GlassRepair{}).Where("Id = ?", gr.Id).Update("CurrentOperation", NO_OPERATION_GR_1).Error
		if err != nil {
			return err
		}
		return err
	})
}

// EditWithFormStatusPayment 更新付款信息
func (gr *GlassRepair) EditWithFormStatusPayment() error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&GlassRepair{}).Where("Id = ?", gr.Id).Select(
			"IsDirectIndemnity", "PaymentFlow", "PaymentAmount", "Payee", "BankAccount",
			"Bank", "ApplyContent", "PaymentMethod", "PaymentFiles",
		).Updates(gr).Error
		if err != nil {
			return err
		}

		err = tx.Model(&GlassRepair{}).Where("Id = ?", gr.Id).Update("CurrentOperation", NO_OPERATION_GR_1).Error
		if err != nil {
			return err
		}
		return err
	})

}

func (gr *GlassRepair) UpdateCurrentOperation(id int64, co CurrentOperationType, fs FormStatusType) error {
	return model.DB().Model(&GlassRepair{}).Where("Id = ?", id).Update("CurrentOperation", co).Update("FormStatus", fs).Error
}

func (gr *GlassRepair) TransactionDelete(tx *gorm.DB) error {
	return tx.Delete(&gr).Error
}
