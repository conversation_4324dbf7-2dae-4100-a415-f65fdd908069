package maintenance

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type MaintenanceMaterial struct {
	model.PkId
	TopCorporationId int64   `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;"`
	Name             string  `json:"Name" gorm:"column:name;type:varchar;comment:名称"`
	Unit             string  `json:"Unit" gorm:"column:unit;type:varchar;comment:计量单位"`
	Price            int64   `json:"Price" gorm:"column:price;type:integer;comment:单价 单位：分"`
	TimeLen          float64 `json:"TimeLen" gorm:"column:timelen;type:decimal;precision:2;comment:时间寿命,单位：月"`
	MileageLen       float64 `json:"MileageLen" gorm:"column:mileagelen;type:decimal;precision:2;comment:里程寿命 单位：公里"`
	model.OpUser
	model.Timestamp
}

func (m *MaintenanceMaterial) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *MaintenanceMaterial) Create() error {
	return model.DB().Create(&m).Error
}

func (m *MaintenanceMaterial) CreateMany(items []MaintenanceMaterial) error {
	return model.DB().Create(&items).Error
}

func (m *MaintenanceMaterial) FirstBy(id int64) MaintenanceMaterial {
	var item MaintenanceMaterial
	model.DB().Model(&m).Where("id = ?", id).First(&item)

	return item
}
func (m *MaintenanceMaterial) Update() error {
	return model.DB().Select("*").Updates(&m).Error
}

func (m *MaintenanceMaterial) Delete(ids []int64) error {
	return model.DB().Where("Id IN ?", ids).Delete(&m).Error
}

func (m *MaintenanceMaterial) GetBy(topCorporationId int64, name string, paginator model.Paginator) ([]MaintenanceMaterial, int64) {
	db := model.DB().Model(&MaintenanceMaterial{}).Where("TopCorporationId = ?", topCorporationId)

	if name != "" {
		db.Where("Name LIKE ?", "%"+name+"%")
	}

	var count int64
	db.Count(&count)

	var items []MaintenanceMaterial
	db.Order("CreatedAt desc").Offset(paginator.Offset).Limit(paginator.Limit).Find(&items)

	return items, count

}
