package model

import (
	"app/org/scs/erpv2/api/service/rpc"
	"context"
)

type Corporations struct {
	GroupCorporation
	CompanyCorporation
	BranchCorporation
	DepartmentCorporation
	FleetCorporation
}

func (cs *Corporations) GetCorporation() (int64, string) {
	var corporationId int64
	if cs.FleetId > 0 {
		corporationId = cs.FleetId
	} else if cs.DepartmentId > 0 {
		corporationId = cs.DepartmentId
	} else if cs.BranchId > 0 {
		corporationId = cs.BranchId
	} else if cs.CompanyId > 0 {
		corporationId = cs.CompanyId
	} else if cs.GroupId > 0 {
		corporationId = cs.GroupId
	}

	corporation := rpc.GetCorporationById(context.Background(), corporationId)
	if corporation != nil {
		return corporationId, corporation.Name
	}

	return corporationId, ""

}

func (cs *Corporations) GetCorporationId() int64 {
	var corporationId int64
	if cs.FleetId > 0 {
		corporationId = cs.FleetId
	} else if cs.DepartmentId > 0 {
		corporationId = cs.DepartmentId
	} else if cs.BranchId > 0 {
		corporationId = cs.BranchId
	} else if cs.CompanyId > 0 {
		corporationId = cs.CompanyId
	} else if cs.GroupId > 0 {
		corporationId = cs.GroupId
	}
	return corporationId
}

func (cs *Corporations) Build(corporationId int64) {
	corporation := rpc.GetCorporationDetailById(context.Background(), corporationId)
	if corporation != nil {
		cs.GroupId = corporation.GroupId
		cs.CompanyId = corporation.CompanyId
		cs.BranchId = corporation.BranchId
		cs.DepartmentId = corporation.DepartmentId
		cs.FleetId = corporation.FleetId
	}
}

func (cs *Corporations) ParseCorporation() {
	if cs.FleetId > 0 {
		corporation := rpc.GetCorporationById(context.Background(), cs.FleetId)
		if corporation != nil {
			cs.Fleet = corporation.Name
		}
	}
	if cs.DepartmentId > 0 {
		corporation := rpc.GetCorporationById(context.Background(), cs.DepartmentId)
		if corporation != nil {
			cs.Department = corporation.Name
		}
	}
	if cs.BranchId > 0 {
		corporation := rpc.GetCorporationById(context.Background(), cs.BranchId)
		if corporation != nil {
			cs.Branch = corporation.Name
		}
	}
	if cs.CompanyId > 0 {
		corporation := rpc.GetCorporationById(context.Background(), cs.CompanyId)
		if corporation != nil {
			cs.Company = corporation.Name
		}
	}
	if cs.GroupId > 0 {
		corporation := rpc.GetCorporationById(context.Background(), cs.GroupId)
		if corporation != nil {
			cs.Group = corporation.Name
		}
	}

	return
}
