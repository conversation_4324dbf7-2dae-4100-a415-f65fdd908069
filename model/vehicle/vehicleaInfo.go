package vehicle

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type VehicleInfo struct {
	model.PkId
	License                               string     `json:"License" gorm:"column:license;type:varchar(30);comment:车牌号;uniqueIndex:vehicle_info_license_unique_index;"`
	VehicleId                             int64      `json:"VehicleId" gorm:"column:vehicleid;type:bigint;comment:车牌id;uniqueIndex:vehicle_info_vehicleid_unique_index;"`
	QrcodePath                            string     `json:"QrcodePath" gorm:"column:qrcodepath;type:text;comment:二维码;default:"`
	TechnicalFiles                        model.JSON `json:"TechnicalFiles" gorm:"column:technicalfiles;type:json;comment:技术资料;default:"`
	DriverLicensePhoto                    model.JSON `json:"DriverLicensePhoto" gorm:"column:driverlicensephoto;type:json;comment:行驶证照片;default:"`
	TestReport                            model.JSON `json:"TestReport" gorm:"column:testreport;type:json;comment:检测报告;default:"`
	OperatingPermits                      model.JSON `json:"OperatingPermits" gorm:"column:operatingpermits;type:json;comment:营运证;default:"`
	GradeEvaluationSheet                  model.JSON `json:"GradeEvaluationSheet" gorm:"column:gradeevaluationsheet;type:json;comment:等级评定表;default:"`
	CheckRecord                           model.JSON `json:"CheckRecords" gorm:"column:checkrecords;type:json;comment:道路运输达标车辆检查记录;default:"`
	InsurancePolicy                       model.JSON `json:"InsurancePolicy" gorm:"column:insurancepolicy;type:json;comment:机动车保险保单;default:"`
	CompletedMaintenanceForLevel2         model.JSON `json:"CompletedMaintenanceForLevel2" gorm:"column:completedmaintenanceforlevel2;type:json;comment:二级维护竣工单;default:"`
	VisaMaintenanceForLevel2              model.JSON `json:"VisaMaintenanceForLevel2" gorm:"column:visamaintenanceforlevel2;type:json;comment:二级维护签证单;default:"`
	ElectronCompletedMaintenanceForLevel2 model.JSON `json:"ElectronCompletedMaintenanceForLevel2" gorm:"column:electroncompletedmaintenanceforlevel2;type:json;comment:电车二级维护竣工单;default:"`
	VehicleCertification                  model.JSON `json:"VehicleCertification" gorm:"column:vehiclecertification;type:json;comment:整车合格证;default:"`
	ChassisCertificate                    model.JSON `json:"ChassisCertificate" gorm:"column:chassiscertificate;type:json;comment:底盘合格证;default:"`
	ConformityCertificate                 model.JSON `json:"ConformityCertificate" gorm:"column:conformitycertificate;type:json;comment:强制性产品认证车辆一致性证书;default:"`
	EnvironmentalInfoList                 model.JSON `json:"EnvironmentalInfoList" gorm:"column:environmentalinfolist;type:json;comment:机动车环保信息随车清单;default:"`
	EvEnvironmentalInfoList               model.JSON `json:"EvEnvironmentalInfoList" gorm:"column:evenvironmentalinfolist;type:json;comment:新能源汽车环保信息随车清单;default:"`
	VehiclePurchaseInvoice                model.JSON `json:"VehiclePurchaseInvoice" gorm:"column:vehiclepurchaseinvoice;type:json;comment:购车发票;default:"`
	VehicleRegistrationCertificate        model.JSON `json:"VehicleRegistrationCertificate" gorm:"column:vehicle_registration_certificate;type:json;comment:机动车登记证书;default:"`
	model.OpUser
	model.Timestamp
}

func (m *VehicleInfo) BeforeCreate(tx *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *VehicleInfo) Create() error {
	return model.DB().Create(&m).Error
}

func (m *VehicleInfo) Update() error {
	return model.DB().Updates(&m).Error
}

// FindVehicleInfoByVehicleId 根据车辆id 查询车辆信息
func (m *VehicleInfo) FindVehicleInfoByVehicleId(vehicleId int64) (info VehicleInfo) {
	model.DB().Model(VehicleInfo{}).Where("VehicleId = ?", vehicleId).First(&info)
	return
}

// FindQrcodePathsByVehicleIds 根据车辆id 返回二维码数组
func (m *VehicleInfo) FindQrcodePathsByVehicleIds(vehicleIds []int64) (qrcodePaths []string) {
	model.DB().Model(&VehicleInfo{}).Where("VehicleId IN ?", vehicleIds).Pluck("QrcodePath", &qrcodePaths)
	return
}
