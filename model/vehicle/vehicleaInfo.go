package vehicle

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type VehicleInfo struct {
	model.PkId
	License    string `json:"License" gorm:"column:license;type:varchar(30);comment:车牌号;uniqueIndex:vehicle_info_license_unique_index;"`
	VehicleId  int64  `json:"VehicleId" gorm:"column:vehicleid;type:bigint;comment:车牌id;uniqueIndex:vehicle_info_vehicleid_unique_index;"`
	QrcodePath string `json:"QrcodePath" gorm:"column:qrcodepath;type:text;comment:二维码;default:"`
	model.OpUser
	model.Timestamp
}

func (m *VehicleInfo) BeforeCreate(tx *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *VehicleInfo) Create() error {
	return model.DB().Create(&m).Error
}

func (m *VehicleInfo) Update() error {
	return model.DB().Updates(&m).Error
}

// FindVehicleInfoByVehicleId 根据车辆id 查询车辆信息
func (m *VehicleInfo) FindVehicleInfoByVehicleId(vehicleId int64) (info VehicleInfo) {
	model.DB().Model(VehicleInfo{}).Where("VehicleId = ?", vehicleId).First(&info)
	return
}

// FindQrcodePathsByVehicleIds 根据车辆id 返回二维码数组
func (m *VehicleInfo) FindQrcodePathsByVehicleIds(vehicleIds []int64) (qrcodePaths []string) {
	model.DB().Model(&VehicleInfo{}).Where("VehicleId IN ?", vehicleIds).Pluck("QrcodePath", &qrcodePaths)
	return
}
