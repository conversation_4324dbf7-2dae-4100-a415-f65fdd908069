package workOrder

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/workOrder"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
)

type DictType int64 // 字典类型
const (
	PETITION_ORIGIN_1 DictType = 1 // 信访工单-信访来源
	PETITION_CLASS_2  DictType = 2 // 信访工单-信访分类

	REPAIR_CORPORATION_11 DictType = 11 // 设备报修工单-供货方/厂家/协办单位
	REPAIR_CLASS_13       DictType = 13 // 设备报修工单-设备大类
	REPAIR_CATEGORY_14    DictType = 14 // 设备报修工单-设备种类

	AdvertisingBoardType DictType = 101 // 广告牌类型
)

type DicDelete int8

const (
	NO_0  DicDelete = 0
	YES_1 DicDelete = 1
)

// Dict 字典
type Dict struct {
	model.PkId

	GroupId int64 `json:"GroupId" gorm:"column:groupid;comment:集团id;type:bigint;uniqueIndex:wod_groupid_dicttype_code"` // 集团id

	DictType      DictType  `json:"DictType" gorm:"column:dicttype;comment:字典类型 必填;type:integer;uniqueIndex:wod_groupid_dicttype_code"`               // 字典类型 必填
	DictCode      string    `json:"DictCode" gorm:"column:dictcode;comment:字典编码 默认=id,可以自行设置;type:varchar(50);uniqueIndex:wod_groupid_dicttype_code"` // 字典编码 默认=id,可以自行设置
	DictKey       string    `json:"DictKey" gorm:"column:dictkey;comment:字典内容 必填;type:varchar(50)"`                                                   // 字典内容 必填
	DictValue     string    `json:"DictValue" gorm:"column:dictvalue;comment:字典值;type:varchar(50)"`                                                   // 字典值
	ObjectType    int64     `json:"ObjectType" gorm:"column:objecttype;type:smallint;default:0;comment:关联对象类型 1车辆  2场站  3站点"`
	ParentId      int64     `json:"ParentId"        gorm:"column:parentid;comment:上一级id， 第一级ParentId=0;type:bigint"`       // 上一级id， 第一级ParentId=0
	ParentIdPath  string    `json:"ParentIdPath"    gorm:"column:parentidpath;comment:ParentIdPath(包含自己) 1,2,3;type:text"` // ParentIdPath(包含自己) 1,2,3
	DictNumber    string    `json:"DictNumber" gorm:"column:dictnumber;comment:用于来电工单的编号生成;type:varchar"`                  // ParentIdPath(包含自己) 1,2,3`
	IsDeleted     DicDelete `json:"IsDeleted" gorm:"column:isdeleted;comment:是否已删除 0否 1是;type:smallint"`                   // 是否已删除 0否 1是
	DevicePresets []workOrder.DevicePreset
	model.Timestamp

	Children     []Dict `json:"-" gorm:"-"`
	ChildrenCate []Dict `json:"ChildrenCate" gorm:"foreignkey:ParentId;association_foreignkey:Id;"`
}

func (*Dict) TableName() string {
	return "dict"
}

func (d *Dict) Add() error {
	return model.DB().Create(&d).Error
}

// GetCodeWithOptions 根据 GroupId、DictType 查询dictCode
func (d *Dict) GetCodeWithOptions() (string, error) {
	var rsp Dict
	err := model.DB().Model(&Dict{}).Select("DictCode").Where("GroupId = ? AND DictType = ?", d.GroupId, d.DictType).Order("CreatedAt DESC").Limit(1).Scan(&rsp).Error
	return rsp.DictCode, err
}

func (d *Dict) GetRecords(groupId, dictType int64) []Dict {
	var records []Dict
	model.DB().Model(&Dict{}).Where("GroupId = ? AND DictType = ?", groupId, dictType).Order("CreatedAt ASC").Scan(&records)
	return records
}
func (d *Dict) GetChildRecords(dictId int64) []Dict {
	var records []Dict
	model.DB().Model(&Dict{}).Preload("DevicePresets").Where("ParentId = ?", dictId).Order("CreatedAt ASC").Scan(&records)
	return records
}

// GetWithOptions 根据 GroupId、DictType 查询
func (d *Dict) GetWithOptions() ([]Dict, error) {
	var rsp []Dict
	tx := model.DB().Model(&Dict{}).Where("GroupId = ? AND IsDeleted = ?", d.GroupId, NO_0)

	if d.DictType > 0 {
		tx.Where("DictType = ?", d.DictType)
	}

	if d.ParentId > 0 {
		tx.Where("ParentId = ?", d.ParentId)
	}

	err := tx.Preload("ChildrenCate").Order("CreatedAt ASC").Find(&rsp).Error
	return rsp, err
}

// GetComplaintCateList 获取投诉分类列表
func (d *Dict) GetComplaintCateList() (data []Dict, err error) {
	err = model.DB().Model(&Dict{}).
		Where("IsDeleted = ?", NO_0).
		Where("ParentId = ?", 0).
		Where("DictType = ?", PETITION_CLASS_2).
		Preload("ChildrenCate").Order("CreatedAt ASC").Find(&data).Error
	return
}

func (d *Dict) Edit() error {
	return model.DB().Model(&Dict{}).Where("Id = ?", d.Id).Select("DictKey", "ParentId", "DictValue").Updates(d).Error
}

func (d *Dict) EditPetitionClass2() error {
	tx := model.DB().Begin()
	err := tx.Where("ParentId = ?", d.Id).Delete(&Dict{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	err = tx.Select("*").Updates(&d).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}

func (d *Dict) TransactionEdit(tx *gorm.DB) error {
	return tx.Model(&Dict{}).Where("Id = ?", d.Id).Select("DictKey", "DictValue").Updates(d).Error
}

// 软删除
func (d *Dict) SoftDelete(tx *gorm.DB) error {
	return tx.Model(&Dict{}).Where("Id = ?", d.Id).Update("IsDeleted", YES_1).Error
}

func (d *Dict) Get() (Dict, error) {
	var rsp Dict
	err := model.DB().Model(&Dict{}).Where("Id = ?", d.Id).Scan(&rsp).Error
	if err != nil {
		return Dict{}, err
	}
	return rsp, err
}

func (d *Dict) GetByDictKey(dictKey string, dictType int64) error {
	return model.DB().Model(&Dict{}).Where("DictKey = ?", dictKey).Where("DictType = ?", dictType).Where("IsDeleted = ?", NO_0).Scan(&d).Error
}

func (d *Dict) FirstById(id int64) Dict {
	var dict Dict
	model.DB().Model(&Dict{}).Where("Id = ?", id).First(&dict)
	return dict
}

func (d *Dict) FirstByDictType(dictType, objectType int64) Dict {
	var dict Dict
	model.DB().Model(&Dict{}).Where("DictType = ? AND ObjectType = ?", dictType, objectType).First(&dict)
	return dict
}

func (d *Dict) GetByDictType(dictType int64) []Dict {
	var dicts []Dict
	model.DB().Model(&Dict{}).Where("DictType = ? AND isDeleted != ?", dictType, util.StatusForTrue).Find(&dicts)
	return dicts
}

func (d *Dict) ListPreset(classId, categoryId int64) ([]Dict, error) {
	var rsp []Dict
	tx := model.DB().Model(&Dict{}).
		Preload("DevicePresets", func(db *gorm.DB) *gorm.DB {
			return db.Order("Id ASC")
		}).
		Where("DictType = ?", REPAIR_CATEGORY_14).
		Where("IsDeleted=?", NO_0)
	if classId > 0 {
		tx = tx.Where("ParentId = ?", classId)
	}
	if categoryId > 0 {
		tx = tx.Where("Id = ?", categoryId)
	}
	err := tx.Order("Id ASC").Find(&rsp).Error

	return rsp, err
}
