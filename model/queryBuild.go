package model

import (
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"reflect"
	"sync"
)

type KV struct {
	Key   string
	Value interface{}
}
type Qs struct {
	db             *gorm.DB
	mu             sync.Mutex
	keys           []string
	where          map[string]interface{}
	CorporationIds []int64 // 特殊 用于erp 机构数据权限
	CorporationId  int64   // 特殊 用于erp 机构数据权限
}

func NewQs(forms ...interface{}) *Qs {
	if forms == nil || len(forms) == 0 {
		return &Qs{
			db:    DB(),
			where: make(map[string]interface{}),
			keys:  make([]string, 0),
		}
	}
	qs := new(Qs)
	qs.db = DB()
	qs.where = make(map[string]interface{})
	qs.keys = make([]string, 0)
	form := forms[0]
	v := reflect.ValueOf(form)
	t := reflect.TypeOf(form)
	// 解引用指针
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
		t = t.Elem()
	}
	if t.Kind() != reflect.Struct {
		return &Qs{
			db:    DB(),
			where: make(map[string]interface{}),
			keys:  make([]string, 0),
		}
	}
	//v := reflect.ValueOf(&form).Elem()
	for i := 0; i < t.NumField(); i++ {
		fieldValue := v.Field(i)
		fieldType := t.Field(i)
		// 忽略私有字段
		if !fieldValue.CanInterface() {
			continue
		}
		// 获取json标签作为map的key
		key := fieldType.Tag.Get("qsField")
		if key == "" || key == "-" {
			key = fieldType.Tag.Get("json")
			if key == "" || key == "-" {
				key = fieldType.Name
			}
		}
		sqlSymbol := fieldType.Tag.Get("qs")
		if sqlSymbol == "-" || sqlSymbol == "" {
			continue
		}
		val := fieldValue.Interface()

		if !util.IsEmpty(val) {
			qs.Add(fmt.Sprintf("%s %s ?", key, sqlSymbol), buildNewVal(val, sqlSymbol))
		}
	}
	return qs
}

func buildNewVal(val interface{}, qsTag string) interface{} {
	switch qsTag {
	case "LIKE", "like":
		return fmt.Sprintf("%%%v%%", val)
	default:
		return val
	}
}

func (q *Qs) Add(key string, value ...interface{}) *Qs {
	q.mu.Lock()
	defer q.mu.Unlock()
	if _, exists := q.where[key]; !exists {
		q.keys = append(q.keys, key)
	}
	if len(value) == 0 {
		q.where[key] = nil
	} else {
		q.where[key] = value[0]
	}
	return q
}

func (q *Qs) Remove(key string) {
	q.mu.Lock()
	defer q.mu.Unlock()
	index := util.Index(q.keys, key)
	if index != -1 {
		q.keys = append(q.keys[:index], q.keys[index+1:]...)
	}
	delete(q.where, key)
}

func (q *Qs) OrderedWhere() []KV {
	q.mu.Lock()
	defer q.mu.Unlock()
	result := make([]KV, 0, len(q.keys))
	for _, key := range q.keys {
		result = append(result, struct {
			Key   string
			Value interface{}
		}{key, q.where[key]})
	}
	return result
}

func (q *Qs) Format() *gorm.DB {
	where := q.OrderedWhere()
	if where == nil || len(where) == 0 {
		return q.db
	}
	for _, kv := range where {
		q.db = q.db.Where(kv.Key, kv.Value)
	}
	if q.CorporationIds != nil && len(q.CorporationIds) > 0 {
		q.db = q.db.Scopes(WhereCorporations(q.CorporationIds))
	}
	if q.CorporationId != 0 {
		q.db = q.db.Scopes(WhereCorporation(q.CorporationId))
	}

	return q.db
}

func (q *Qs) Init() {
	q.where = make(map[string]interface{})
	q.keys = make([]string, 0)
}

func PaginationScope(paginator Paginator) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if paginator.Limit <= 0 {
			return db
		}
		return db.Offset(paginator.Offset).Limit(paginator.Limit)
	}
}
