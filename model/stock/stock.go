package stock

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type StockRecordType int64

const (
	InStock  StockRecordType = 1
	OutStock StockRecordType = 2
)

// Stock 库存管理
type Stock struct {
	model.PkId
	model.Corporations       // 只存groupid
	StockMaterialId    int64 `json:"StockMaterialId" gorm:"column:stockmaterialid;type:bigint;comment:物料id;"` //物料id

	// 实时库存数量
	Num int64 `json:"Num" gorm:"column:num;type:integer;comment:实时库存数量;"` // 实时库存数量

	StockRecords  []StockRecord `json:"StockRecords"`  //
	StockMaterial StockMaterial `json:"StockMaterial"` // 物料
	model.Timestamp
}

func (st *Stock) TableName() string {
	return "stocks"
}

func (st *Stock) BeforeCreate(tx *gorm.DB) error {
	if st.Id == 0 {
		st.Id = model.Id()
	}

	return nil
}

func (st *Stock) BranchCreate(items []Stock) error {
	return model.DB().Omit("StockRecords").Create(&items).Error
}

func (st *Stock) GetBy(isRecycle bool, name string, supplier string, classId, categoryId int64, liableStaffId int64, paginator model.Paginator) ([]Stock, int64, error) {
	var (
		rsp        []Stock
		totalCount int64
	)

	tx := model.DB().Model(&Stock{}).Joins("LEFT JOIN stock_materials sm ON stocks.StockMaterialId=sm.Id").
		Preload("StockMaterial").Preload("StockMaterial.LiableStaffs")

	if isRecycle {
		//	回收站数据
		tx = tx.Where("sm.IsRecycle > ?", 0)
	} else {
		//	非回收站数据
		tx = tx.Where("sm.IsRecycle = ?", 0)
	}

	if name != "" {
		tx = tx.Where("sm.Name LIKE ?", "%"+name+"%")
	}
	if supplier != "" {
		tx = tx.Where("sm.SupplierPresetId IN (?)", model.DB().Model(&StockMaterialTypePreset{}).Select("Id").Where("Supplier LIKE ?", "%"+supplier+"%"))
	}
	if liableStaffId > 0 {
		tx = tx.Where("sm.LiablePresetId IN (?)", model.DB().Model(&StockMaterialTypePreset{}).Select("Id").Where("LiableStaffId = ?", liableStaffId))
	}
	if classId > 0 {
		tx = tx.Where("sm.StockMaterialTypeId IN (?)", model.DB().Model(&StockMaterialType{}).Select("Id").Where("ParentId = ?", classId))
	}
	if categoryId > 0 {
		tx = tx.Where("sm.StockMaterialTypeId = ?", categoryId)
	}

	err := tx.Count(&totalCount).Order("stocks.Id Desc").Offset(paginator.Offset).Limit(paginator.Limit).Find(&rsp).Error

	return rsp, totalCount, err
}

func (st *Stock) GetStockById(id int64) *Stock {
	var rsp Stock
	model.DB().Model(&Stock{}).Joins("LEFT JOIN stock_materials sm ON stocks.StockMaterialId=sm.Id").
		Preload("StockMaterial").Where("stocks.Id = ?", id).Find(&rsp)

	return &rsp
}

func (st *Stock) Find(tx *gorm.DB, id int64) error {

	return tx.Model(&Stock{}).Where("Id=?", id).Find(&st).Error
}

func (st *Stock) TransactionUpdateNum(tx *gorm.DB, id int64, num int64) error {

	return tx.Model(&Stock{}).Where("Id=?", id).Update("Num", num).Error
}

type AssociationObjectType int8

const (
	VEHICLE_1 AssociationObjectType = 1 // 车
	PARKING_2 AssociationObjectType = 2 // 场站
	STATION_3 AssociationObjectType = 3 // 站点
)

// StockRecord 库存记录
type StockRecord struct {
	model.PkId
	model.Corporations                          // 只存groupid
	StockId               int64                 `json:"StockId" gorm:"column:stockid;type:bigint;comment:库存管理id;"`                                                    // 库存管理id
	Type                  StockRecordType       `json:"Type" gorm:"column:type;type:smallint;comment:库存记录类型 1入库 2出库;"`                                                // 库存记录类型 1入库 2出库
	OperateNum            int64                 `json:"OperateNum" gorm:"column:operatenum;type:integer;comment:操作数量，入库/出库xx个物料;"`                                    // 操作数量，入库/出库xx个物料
	CurrentNum            int64                 `json:"CurrentNum" gorm:"column:currentnum;type:integer;comment:操作后库存数量，此次入库/出库后库存的数量;"`                              // 操作后库存数量，此次入库/出库后库存的数量
	ApplyStaffId          int64                 `json:"ApplyStaffId" gorm:"column:applystaffid;type:integer;default:0;comment:申领人人员staffId;"`                         // 申领人员
	ApplyStaffName        string                `json:"ApplyStaffName" gorm:"column:applystaffname;type:varchar(50);default:'';comment:申领人;"`                         // 申领人员
	AssociationObjectType AssociationObjectType `json:"AssociationObjectType" gorm:"column:associationobjecttype;type:smallint;default:0;comment:关联对象类型 1车 2场站 3站点;"` // 关联对象类型 1车 2场站 3站点
	AssociationObjectName string                `json:"AssociationObjectName" gorm:"column:associationobjectname;type:varchar(50);default:'';comment:关联对象名;"`         // 关联对象名
	AssociationObjectId   int64                 `json:"AssociationObjectId" gorm:"column:associationobjectid;type:bigint;default:0;comment:关联对象id 车、场、站id;"`          // 关联对象id 车、场、站id
	ProcessMode           int64                 `json:"ProcessMode" gorm:"column:processmode;type:smallint;default:0;comment:处理模式,1 手动处理 2 钉钉审批; 3 lpbm审批 "`
	ApplyStatus           int64                 `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;default:1;comment:审批状态 1-申请审批中,2-申请审批通过,3-撤销,4-驳回"` //审批状态 1-申请审批中,2-申请审批通过,3-撤销,4-驳回
	Remark                string                `json:"Remark" gorm:"column:remark;type:text;comment:备注;"`                                                     // 备注
	model.Timestamp
	model.OpUser
	ApplyProcessBusinessId string `json:"ApplyProcessBusinessId" gorm:"-"` // 审批编号
	Unit                   string `json:"Unit" gorm:"-"`                   // 单位
}

func (sr *StockRecord) BeforeCreate(tx *gorm.DB) error {
	if sr.Id == 0 {
		sr.Id = model.Id()
	}

	return nil
}

func (sr *StockRecord) GetById(stockRId int64) error {
	return model.DB().Model(&StockRecord{}).Where("Id=?", stockRId).Find(&sr).Error
}

func (sr *StockRecord) GetBy(stockId int64, applyStaffId int64, s, e time.Time, paginator model.Paginator) ([]StockRecord, int64, error) {
	var (
		rsp        []StockRecord
		totalCount int64
	)
	tx := model.DB().Model(&StockRecord{}).Where("StockId=?", stockId)
	if !s.IsZero() {
		tx.Where("CreatedAt >=?", s)
	}
	if !e.IsZero() {
		tx.Where("CreatedAt < ?", e)
	}

	if applyStaffId > 0 {
		tx.Where("ApplyStaffId=?", applyStaffId)
	}

	tx.Where("ApplyStatus=?", util.ApplyStatusForDone)
	err := tx.Count(&totalCount).Order("UpdatedAt ASC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&rsp).Error
	return rsp, totalCount, err
}

func (sr *StockRecord) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(sr).Error
}

func (sr *StockRecord) TransactionUpdate(tx *gorm.DB) error {
	return tx.Model(&StockRecord{}).Where("Id=?", sr.Id).Updates(map[string]interface{}{
		"OperateNum":  sr.OperateNum,
		"CurrentNum":  sr.CurrentNum,
		"ApplyStatus": sr.ApplyStatus,
		"OpUserName":  sr.OpUserName,
		"OpUserId":    sr.OpUserId,
	}).Error
}

func (sr *StockRecord) TransactionUpdateApplyStatus(tx *gorm.DB) error {
	return tx.Model(&StockRecord{}).Where("Id=?", sr.Id).Updates(map[string]interface{}{
		"ApplyStatus": sr.ApplyStatus,
	}).Error
}

func (st *StockRecord) TableName() string {
	return "stock_records"
}

func (rec *StockRecord) UpdateApplyStatus(tx *gorm.DB, id, status int64) error {
	return tx.Model(&StockRecord{}).Where("Id = ?", id).Updates(map[string]interface{}{
		"applystatus": status,
	}).Error
}

type AggSumItem struct {
	Stock
	StockId      int64 `json:"StockId"  gorm:"column:stockid;type:bigint"` // 库存管理id
	Degree       int64 `json:"Degree"`                                     // 次数
	Number       int64 `json:"Number"`                                     // 数量
	ApplyStaffId int64 `json:"ApplyStaffId" gorm:"column:applystaffid"`
}

type SumItem struct {
	Degree int64 `json:"Degree"` // 次数
	Number int64 `json:"Number"` // 数量
}

type AggSumParam struct {
	StartAt                     time.Time
	EndAt                       time.Time
	Name                        string `json:"Name"`
	StockMaterialTypeCategoryId int64  // 库存物料类型id 种类

	ApplyStaffId int64
	model.Paginator
}

// 出库方式
func (rec *StockRecord) AggSum(stockMethod int64, p *AggSumParam) ([]AggSumItem, int64, SumItem, error) {
	var (
		items []AggSumItem
		total int64
	)

	var stockRecordTmp = fmt.Sprintf("SELECT ApplyStaffId, StockId, count(*) as Degree,sum(operatenum) as Number FROM stock_records where Type=%v and ApplyStatus=%v",
		stockMethod, util.ApplyStatusForDone)

	if p.ApplyStaffId > 0 {
		stockRecordTmp += fmt.Sprintf(" AND ApplyStaffId=%v ", p.ApplyStaffId)
	}

	if !p.StartAt.IsZero() {
		stockRecordTmp += fmt.Sprintf(" AND UpdatedAt>='%v' AND UpdatedAt<='%v'",
			p.StartAt.Format("2006-01-02 15:04:05"), p.EndAt.Format("2006-01-02 15:04:05"))
	}
	stockRecordTmp += fmt.Sprintf(" GROUP BY ApplyStaffId,StockId")

	var queryCount = fmt.Sprintf(" SELECT COUNT(*) from "+
		"stocks as a,"+
		"(%s) as b,"+
		"(SELECT Id,StockMaterialTypeId,Name FROM stock_materials) as c  "+
		"where a.Id=b.StockId AND a.StockMaterialId=c.Id",
		stockRecordTmp)

	var query = fmt.Sprintf(" select a.Id as Id,a.StockMaterialId as StockMaterialId,b.StockId as stockid,b.Degree as Degree,b.Number as Number,b.ApplyStaffId as applystaffid from "+
		"stocks as a,"+
		"(%s) as b, "+
		"(SELECT Id,StockMaterialTypeId,Name FROM stock_materials) as c  "+
		"where a.Id=b.StockId  AND a.StockMaterialId=c.Id",
		stockRecordTmp)

	if len(p.Name) > 0 {
		queryCount += fmt.Sprintf(" AND c.Name='%v'", p.Name)
		query += fmt.Sprintf(" AND c.Name='%v'", p.Name)
	}

	if p.StockMaterialTypeCategoryId > 0 {
		queryCount += fmt.Sprintf(" AND c.StockMaterialTypeId IN(%v)", p.StockMaterialTypeCategoryId)
		query += fmt.Sprintf(" AND c.StockMaterialTypeId IN(%v)", p.StockMaterialTypeCategoryId)
	}

	err := model.DB().Raw(queryCount).Scan(&total).Error
	if err != nil {
		return nil, 0, SumItem{}, err
	}

	var sumItem SumItem

	var querySum = fmt.Sprintf("SELECT SUM(Degree) as degree, SUM(Number) AS number FROM (%s) as d", query)
	err = model.DB().Raw(querySum).Scan(&sumItem).Error
	if err != nil {
		return nil, 0, sumItem, err
	}

	query += fmt.Sprintf(" ORDER BY b.ApplyStaffId ASC LIMIT %v OFFSET %v", p.Limit, p.Offset)
	err = model.DB().Raw(query).Scan(&items).Error
	if err != nil {
		return nil, 0, sumItem, err
	}
	return items, total, sumItem, err

}
