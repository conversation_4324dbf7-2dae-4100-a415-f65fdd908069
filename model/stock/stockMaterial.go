package stock

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type StockMaterialTypeKind int64

const (
	StockMaterialTypeKindClass    StockMaterialTypeKind = 1 // 大类
	StockMaterialTypeKindCategory StockMaterialTypeKind = 2 // 大类-种类
)

type StockMaterialTypePresetType int64

const (
	StockMaterialTypePresetTypeLiable   StockMaterialTypePresetType = 1 // 责任人
	StockMaterialTypePresetTypeSupplier StockMaterialTypePresetType = 2 // 供货方
)

// StockMaterialType 库存物料类型
type StockMaterialType struct {
	model.PkId
	model.Corporations                       // 只存groupid
	Kind               StockMaterialTypeKind `json:"Kind" gorm:"column:kind;type:smallint;comment:种类的类型,便于查询;"`        // 种类的类型,便于查询
	ParentId           int64                 `json:"ParentId" gorm:"column:parentid;type:bigint;comment:上一级id 顶级默认0;"` // 上一级id 顶级默认0
	Name               string                `json:"Name" gorm:"column:name;type:varchar(50);comment:类型名;"`            // 类型名
	NameAbbr           string                `json:"NameAbbr" gorm:"column:nameabbr;type:varchar(50);comment:类型名简称;"`  // 类型名简称

	StockMaterialTypePresets []StockMaterialTypePreset `json:"StockMaterialTypePresets"`
	model.Timestamp
}

func (smt *StockMaterialType) BeforeCreate(tx *gorm.DB) error {
	smt.Id = model.Id()

	return nil
}

func (smt *StockMaterialType) GetBy(kind StockMaterialTypeKind, parentId int64, name string) ([]StockMaterialType, int64, error) {
	var (
		totalCount int64
		rsp        []StockMaterialType
	)

	tx := model.DB().Model(&StockMaterialType{})
	if kind > 0 {
		tx.Where("Kind=?", kind)
	}
	if parentId > 0 {
		tx.Where("ParentId=?", parentId)
	}
	if name != "" {
		tx.Where("Name LIKE ?", "%"+name+"%")
	}
	err := tx.Count(&totalCount).Scan(&rsp).Error
	return rsp, totalCount, err

}
func (smt *StockMaterialType) Create() error {
	return model.DB().Create(&smt).Error
}

func (smt *StockMaterialType) GetPreset(parentId int64) ([]StockMaterialType, int64, error) {
	var (
		totalCount int64
		rsp        []StockMaterialType
	)

	tx := model.DB().Model(&StockMaterialType{}).
		Preload("StockMaterialTypePresets")

	if parentId > 0 {
		tx.Where("ParentId=?", parentId)
	} else {
		tx.Where("ParentId > ?", 0)
	}

	err := tx.Count(&totalCount).Order("Id ASC").Find(&rsp).Error
	return rsp, totalCount, err
}
func (smt *StockMaterialType) TransactionUpdates(tx *gorm.DB, id int64, columns map[string]interface{}) error {
	return tx.Model(&StockMaterialType{}).Where("Id=?", id).Updates(columns).Error
}

func (smt *StockMaterialType) TransactionDelete(tx *gorm.DB, id int64) error {
	return tx.Where("Id=?", id).Delete(&StockMaterialType{}).Error
}

func (smt *StockMaterialType) FindBy(id int64) (StockMaterialType, error) {
	var rsp StockMaterialType
	err := model.DB().Model(&StockMaterialType{}).Where("Id=?", id).Find(&rsp).Error
	return rsp, err
}

func (smt *StockMaterialType) GetName(id int64) string {
	var rsp StockMaterialType
	model.DB().Model(&StockMaterialType{}).Where("Id=?", id).Find(&rsp)
	return rsp.Name
}

// StockMaterialTypePreset 库存物料类型预设值
type StockMaterialTypePreset struct {
	model.PkId
	model.Corporations        // 只存groupid
	StockMaterialTypeId int64 `json:"StockMaterialTypeId" gorm:"column:stockmaterialtypeid;type:bigint;comment:物料类型id;"` //物料类型id

	Type          StockMaterialTypePresetType `json:"Type" gorm:"column:type;type:smallint;comment:预设类型 1责任人 2供货方;"`           // 预设类型 1责任人 2供货方
	LiableStaffId int64                       `json:"LiableStaffId" gorm:"column:liablestaffid;type:integer;comment:责任人人员id;"` // 责任人人员id
	Supplier      string                      `json:"Supplier" gorm:"column:supplier;type:varchar(50);comment:供货方;"`           // 供货方
	SupplierAbbr  string                      `json:"SupplierAbbr" gorm:"column:supplierabbr;type:varchar(50);comment:供货方简称;"` // 供货方简称

	//
	LiableStaffName   string `json:"LiableStaffName" gorm:"-"`   // 责任人姓名
	LiableCorporation string `json:"LiableCorporation" gorm:"-"` // 责任人所属机构
	Option            string `json:"Option" gorm:"-"`            // 操作类型 create edit delete
	model.Timestamp
}

func (smtp *StockMaterialTypePreset) BeforeCreate(tx *gorm.DB) error {
	smtp.Id = model.Id()

	return nil
}

func (smtp *StockMaterialTypePreset) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&smtp).Error
}

func (smtp *StockMaterialTypePreset) TransactionUpdates(tx *gorm.DB, id int64, columns map[string]interface{}) error {
	return tx.Model(&StockMaterialTypePreset{}).Where("Id=?", id).Updates(columns).Error
}
func (smtp *StockMaterialTypePreset) TransactionDelete(tx *gorm.DB, id int64) error {
	return tx.Model(&StockMaterialTypePreset{}).Delete("Id=?", id).Error
}
func (smtp *StockMaterialTypePreset) TransactionDeleteStockMaterialTypeId(tx *gorm.DB, stockMaterialTypeId int64) error {
	return tx.Model(&StockMaterialTypePreset{}).Delete("StockMaterialTypeId=?", stockMaterialTypeId).Error
}
func (smtp *StockMaterialTypePreset) CountLiables(tx *gorm.DB, stockMaterialTypeId int64) int64 {
	var count int64
	tx.Model(&StockMaterialTypePreset{}).Where("StockMaterialTypeId=? AND Type=?", stockMaterialTypeId, StockMaterialTypePresetTypeLiable).Count(&count)
	return count
}

func (smtp *StockMaterialTypePreset) Find(id int64) error {
	return model.DB().Model(&StockMaterialTypePreset{}).Where("Id=?", id).Find(smtp).Error
}

// ListLiables 获取所有预设责任人列表
func (smtp *StockMaterialTypePreset) ListLiables() ([]StockMaterialTypePreset, error) {
	var (
		rsp []StockMaterialTypePreset
	)
	err := model.DB().Model(&StockMaterialTypePreset{}).Where("Type=?", StockMaterialTypePresetTypeLiable).Scan(&rsp).Error
	return rsp, err
}

// StockMaterial 库存物料
type StockMaterial struct {
	model.PkId
	GroupId int64  `json:"GroupId"      gorm:"column:groupid;type:bigint;comment:集团Id;uniqueIndex:stockmaterial_groupid_name_isrecycle"`
	Group   string `json:"Group"        gorm:"-"`
	model.CompanyCorporation
	model.BranchCorporation
	model.DepartmentCorporation
	model.FleetCorporation
	StockMaterialTypeId int64  `json:"StockMaterialTypeId" gorm:"column:stockmaterialtypeid;type:bigint;comment:物料类型id;"`                     //物料类型id(种类)
	StockId             int64  `json:"StockId" gorm:"column:stockid;type:bigint;comment:库存id;"`                                               // 库存id
	Name                string `json:"Name" gorm:"column:name;type:varchar(50);comment:物料名;uniqueIndex:stockmaterial_groupid_name_isrecycle"` // 物料名

	LiableStaffs []StockMaterialLiable `json:"LiableStaffs"` // 责任人

	SupplierPresetId int64 `json:"SupplierPresetId" gorm:"column:supplierpresetid;type:bigint;comment:供货方预设id;"` // 供货方预设id

	Price     int64           `json:"Price" gorm:"column:price;type:integer;comment:单价 单位分;"`                                                                                                // 单价 单位分
	Unit      string          `json:"Unit" gorm:"column:unit;type:varchar(50);comment:单位;"`                                                                                                  // 单位
	Remarks   string          `json:"Remarks" gorm:"column:remarks;type:varchar(200);comment:备注;"`                                                                                           // 备注
	IsRecycle int64           `json:"IsRecycle" gorm:"column:isrecycle;type:bigint;comment:是否进入回收站（软删除）0-未回收 大于0-被回收，填充id避免唯一约束;default:0;uniqueIndex:stockmaterial_groupid_name_isrecycle"` // 是否进入回收站（软删除） 0未回收， 大于0被回收填充id避免唯一约束
	RecycleAt model.LocalTime `json:"RecycleAt" gorm:"column:recycleat;type:timestamp;comment:移入回收站时间;"`                                                                                     // 移入回收站时间

	//
	Supplier     string `json:"Supplier" gorm:"-"`     // 供货方
	SupplierAbbr string `json:"SupplierAbbr" gorm:"-"` // 供货方简称

	StockMaterialTypeClassId   int64  `json:"StockMaterialTypeClassId" gorm:"-"`   // 物料类型大类id
	StockMaterialTypeClassName string `json:"StockMaterialTypeClassName" gorm:"-"` // 物料类型大类名
	StockMaterialTypeName      string `json:"StockMaterialTypeName" gorm:"-"`      // 物料类型种类名

	Option string `json:"Option" gorm:"-"` // 操作类型 create edit delete
	model.Timestamp
}

func (sm *StockMaterial) BeforeCreate(tx *gorm.DB) error {
	if sm.Id == 0 {
		sm.Id = model.Id()
	}

	return nil
}
func (sm *StockMaterial) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(sm).Error
}
func (sm *StockMaterial) TransactionUpdateInfo(tx *gorm.DB, id int64) error {
	return tx.Select("Name", "Unit", "Remarks").Where("Id=?", id).Updates(sm).Error
}
func (sm *StockMaterial) Recycle() error {
	return model.DB().Model(StockMaterial{}).Where("Id=?", sm.Id).Updates(map[string]interface{}{"IsRecycle": sm.Id, "RecycleAt": time.Now()}).Error
}

// 库存物料责任人表
type StockMaterialLiable struct {
	model.PkId
	model.Corporations       // 只存groupid
	StockMaterialId    int64 `json:"StockMaterialId" gorm:"column:stockmaterialid;type:bigint;comment:库存物料id;"` // 库存物料id

	LiablePresetId int64 `json:"LiablePresetId" gorm:"column:liablepresetid;type:bigint;comment:责任人预设id;"` // 责任人预设id

	LiableStaffId       int64  `json:"LiableStaffId" gorm:"-"`       // 责任人人员id
	LiableStaffName     string `json:"LiableStaffName" gorm:"-"`     // 责任人姓名
	LiableCorporationId int64  `json:"LiableCorporationId" gorm:"-"` // 责任人所属机构id
	LiableCorporation   string `json:"LiableCorporation" gorm:"-"`   // 责任人所属机构
}

func (sml *StockMaterialLiable) BeforeCreate(tx *gorm.DB) error {
	if sml.Id == 0 {
		sml.Id = model.Id()
	}
	return nil
}
func (sml *StockMaterialLiable) TransactionDeleteByStockMaterialId(tx *gorm.DB, stockMaterialId int64) error {
	return tx.Where("StockMaterialId=?", stockMaterialId).Delete(&StockMaterialLiable{}).Error
}
func (sml *StockMaterialLiable) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(sml).Error
}
