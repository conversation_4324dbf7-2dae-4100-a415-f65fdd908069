package model

import "gorm.io/gorm"

func WhereCorporation(corporationId int64) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("GroupId = ? OR CompanyId = ? OR BranchId = ? OR DepartmentId = ? OR FleetId = ?",
			corporationId, corporationId, corporationId, corporationId, corporationId)
	}
}

func WhereCorporations(corporationIds []int64) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("GroupId IN ? OR CompanyId IN ? OR BranchId IN ? OR DepartmentId IN ? OR FleetId IN ?",
			corporationIds, corporationIds, corporationIds, corporationIds, corporationIds)
	}
}
