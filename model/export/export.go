package export

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type ExportFile struct {
	model.PkId
	TopCorporationId int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构"`
	FileName         string          `json:"FileName" gorm:"column:filename;type:varchar(200);comment:文件名"`
	FileSize         int64           `json:"FileSize" gorm:"column:filesize;type:integer;default:0;comment:文件大小:字节数"`
	Key              string          `json:"Key" gorm:"column:key;type:varchar(200);comment:文件标识名"`
	Path             string          `json:"Path" gorm:"column:Path;type:varchar(250);comment:文件路径"`
	Scene            string          `json:"Scene" gorm:"column:scene;type:varchar(100);comment:报表;"`
	StartAt          model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始时间"`
	EndAt            model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束时间"`
	Param            model.JSON      `json:"-" gorm:"column:param;type:json;comment:查询报表的参数"`
	Status           int64           `json:"Status" gorm:"column:status;type:smallint;comment:导出状态 1导出中  2导出成功  3导出失败"`
	ErrorCode        string          `json:"-" gorm:"column:errorcode;type:varchar;comment:失败时的错误码"`
	model.OpUser
	model.Timestamp

	FileUrl string `json:"FileUrl" gorm:"-"`
}

func (e *ExportFile) BeforeCreate(db *gorm.DB) error {
	e.Id = model.Id()
	e.Path = fmt.Sprintf("%s/reports/%s", config.Config.WebRoot, time.Now().Format("20060102"))
	return nil
}

func (e *ExportFile) Create() error {
	return model.DB().Create(&e).Error
}

func (e *ExportFile) UpdateStatus() error {
	e.FileSize = util.GetFileSize(fmt.Sprintf("%v%v", config.Config.AbsDirPath, e.Path))
	return model.DB().Select("Status", "Path", "ErrorCode", "FileSize").Updates(&e).Error
}

func (e *ExportFile) UpdateFail() error {
	e.Status = util.ExportFileStatusForFail
	e.Path = ""
	return model.DB().Select("Status", "Path", "ErrorCode").Updates(&e).Error
}

func (e *ExportFile) UpdateSuccess() error {
	e.Status = util.ExportFileStatusForDone
	e.Path = fmt.Sprintf("%s/%s", e.Path, e.Key)
	e.FileSize = util.GetFileSize(fmt.Sprintf("%v%v", config.Config.AbsDirPath, e.Path))
	return model.DB().Select("Status", "Path", "ErrorCode").Updates(&e).Error
}

func (e *ExportFile) FirstBy(id int64) ExportFile {
	var result ExportFile

	model.DB().Model(&ExportFile{}).Where("id = ?", id).First(&result)

	return result
}

func (e *ExportFile) GetBy(userId int64, scene string, paginator model.Paginator) ([]ExportFile, int64) {
	var records []ExportFile
	tx := model.DB().Model(&ExportFile{}).Where("OpUserId = ?", userId)
	if scene != "" {
		tx = tx.Where("scene = ?", scene)
	}

	var count int64

	tx.Count(&count)

	tx.Order("CreatedAt desc").Offset(paginator.Offset).Limit(paginator.Limit).Find(&records)

	return records, count
}
