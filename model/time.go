package model

import (
	"database/sql/driver"
	"fmt"
	"strconv"
	"time"
)

var TimeFormat = "2006-01-02 15:04:05"
var DateFormat = "2006-01-02"
var DateFormat_Merge = "20060102"
var DateHourFormat = "2006-01-02 15"
var DateHourMinuteFormat = "2006-01-02 15:04"

// LocalTime format json time field by myself
type LocalTime time.Time

func (t *LocalTime) UnmarshalJSON(data []byte) (err error) {
	fmt.Printf("time data1========= %v", string(data))
	if string(data) == "null" || string(data) == `""` || len(data) == 2 {
		*t = LocalTime(time.Time{})
		return
	}
	fmt.Printf("time data2========= %v", string(data))

	now, err := time.ParseInLocation(`"`+TimeFormat+`"`, string(data), time.Local)
	if err != nil {
		now, err = time.ParseInLocation(`"`+DateHourMinuteFormat+`"`, string(data), time.Local)
	}
	if err != nil {
		now, err = time.ParseInLocation(`"`+DateFormat+`"`, string(data), time.Local)
	}
	if err != nil {
		now, err = time.ParseInLocation(`"`+DateHourFormat+`"`, string(data), time.Local)
	}

	fmt.Printf("time UnmarshalJSON============err:%+v", err)
	*t = LocalTime(now)
	return
}

// MarshalJSON on LocalTime format Time field with %Y-%m-%d %H:%M:%S
func (t LocalTime) MarshalJSON() ([]byte, error) {
	if time.Time(t).IsZero() {
		formatted := fmt.Sprintf("\"%s\"", "")
		return []byte(formatted), nil
	}
	b := make([]byte, 0, len(TimeFormat)+2)
	b = append(b, '"')
	b = time.Time(t).AppendFormat(b, TimeFormat)
	b = append(b, '"')
	return b, nil
}

// Value insert timestamp into mysql need this function.
func (t LocalTime) Value() (driver.Value, error) {
	var zeroTime time.Time
	if time.Time(t).UnixNano() == zeroTime.UnixNano() {
		return nil, nil
	}
	return time.Time(t), nil
}

// Scan valueOf time.Time
func (t *LocalTime) Scan(v interface{}) error {
	value, ok := v.(time.Time)
	if ok {
		*t = LocalTime(value)
		return nil
	}
	return fmt.Errorf("===can not convert %v to timestamp", v)
}

func (t LocalTime) String() string {
	return time.Time(t).Format(TimeFormat)
}

func (t LocalTime) FormatIntDate() int64 {
	date, _ := strconv.ParseInt(time.Time(t).Format("20060102"), 10, 64)
	return date
}

func (t LocalTime) ToTime() time.Time {
	at := time.Time(t)
	if at.IsZero() {
		return at
	}
	year, month, day := at.Date()
	return time.Date(year, month, day, at.Hour(), at.Minute(), at.Second(), 0, time.Local)
}
