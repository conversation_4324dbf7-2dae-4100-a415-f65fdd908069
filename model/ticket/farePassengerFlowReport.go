package ticket

import (
	"app/org/scs/erpv2/api/model"
	"fmt"
	"gorm.io/gorm"
	"sort"
)

// FarePassengerFlowReportCalculateFlag 计算标志
var FarePassengerFlowReportCalculateFlag = false

// FarePassengerFlowReport 票款客流仪对比报表
type FarePassengerFlowReport struct {
	model.PkId
	LineId                   int64                     `json:"LineId"     gorm:"column:lineid;comment:线路id;type:bigint;index:farepassengerflowreport_lineid_reportmonth;index:farepassengerflowreport_lineid_reporttime;"`
	ReportMonth              string                    `json:"ReportMonth" gorm:"column:reportmonth;type:varchar;default:;comment:报表月份 YYYYMM;index:farepassengerflowreport_lineid_reportmonth;"`
	ReportTime               string                    `json:"ReportTime" gorm:"column:reporttime;type:varchar;default:;comment:报表月份 YYYYMMDD;index:farepassengerflowreport_lineid_reporttime;"`
	LineName                 string                    `json:"LineName" gorm:"column:linename;type:varchar;default:;comment:"`
	CorporationId            int64                     `json:"CorporationId" gorm:"column:corporationid;type:bigint;default:;comment:"`
	CorporationName          string                    `json:"CorporationName" gorm:"column:corporationname;type:varchar;default:;comment:"`
	PeopleTicketAmount       int64                     `json:"PeopleTicketAmount" gorm:"column:peopleticketamount;type:integer;default:;comment:投币人数"`
	PeopleCardAmount         int64                     `json:"PeopleCardAmount" gorm:"column:peoplecardamount;type:integer;default:;comment:刷卡人数"`
	PeopleCodeAmount         int64                     `json:"PeopleCodeAmount" gorm:"column:peoplecodeamount;type:integer;default:;comment:扫码人数"`
	PeoplePassengerFlowMeter int64                     `json:"PeoplePassengerFlowMeter" gorm:"column:peoplepassengerflowmeter;type:integer;default:;comment:客流仪人数"`
	OddsRatio                string                    `json:"OddsRatio" gorm:"column:oddsratio;type:varchar;default:;comment:对比值"`
	LineOperatingVehicle     int64                     `json:"LineOperatingVehicle" gorm:"column:lineoperatingvehicle;type:integer;default:;comment:运营车辆"`
	LineVehicle              int64                     `json:"LineVehicle" gorm:"column:linevehicle;type:integer;default:;comment:所属车辆"`
	Children                 []FarePassengerFlowReport `json:"Children" gorm:"-"`
}

func (m *FarePassengerFlowReport) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *FarePassengerFlowReport) Create() error { return model.DB().Create(&m).Error }

func (m *FarePassengerFlowReport) Updates() error { return model.DB().Updates(&m).Error }

func (m *FarePassengerFlowReport) Delete(id int64) error {
	return model.DB().Delete(&FarePassengerFlowReport{}, id).Error
}

func (m *FarePassengerFlowReport) Find(lineId int64, reportTime string) error {
	return model.DB().Where("LineId = ?", lineId).Where("ReportTime = ?", reportTime).First(&m).Error
}

func (m *FarePassengerFlowReport) List(lineIds []int64, startTime, endTime string) (finallyData []FarePassengerFlowReport, err error) {
	var data []FarePassengerFlowReport
	err = model.DB().Where("LineId in ?", lineIds).Where("ReportTime BETWEEN ? AND ?", startTime, endTime).Find(&data).Error
	if data != nil {
		groupByLine := make(map[int64][]FarePassengerFlowReport)
		for _, v := range data {
			groupByLine[v.LineId] = append(groupByLine[v.LineId], v)
		}
		for _, reports := range groupByLine {
			if reports != nil {
				var TopReport FarePassengerFlowReport
				for _, v := range reports {
					TopReport.LineId = v.LineId
					TopReport.LineName = v.LineName
					TopReport.CorporationName = v.CorporationName
					TopReport.CorporationId = v.CorporationId
					TopReport.PeopleTicketAmount += v.PeopleTicketAmount
					TopReport.PeopleCardAmount += v.PeopleCardAmount
					TopReport.PeopleCodeAmount += v.PeopleCodeAmount
					TopReport.PeoplePassengerFlowMeter += v.PeoplePassengerFlowMeter
					TopReport.Children = append(TopReport.Children, v)
				}
				if TopReport.PeoplePassengerFlowMeter != 0 {
					TopReport.OddsRatio = fmt.Sprintf("%.2f%%", float64(TopReport.PeopleTicketAmount+TopReport.PeopleCardAmount+TopReport.PeopleCodeAmount)/float64(TopReport.PeoplePassengerFlowMeter)*100)
				}
				sort.Slice(TopReport.Children, func(i, j int) bool {
					return TopReport.Children[i].ReportMonth < TopReport.Children[j].ReportMonth
				})
				finallyData = append(finallyData, TopReport)
			}
		}
	}
	return
}
