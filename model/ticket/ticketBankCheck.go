package ticket

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type TicketBankCheckDifferenceRecordStatus int64

const (
	DOING_TBCDRS_1 TicketBankCheckDifferenceRecordStatus = 1 // 进行中
	DONE_TBCDRS_2  TicketBankCheckDifferenceRecordStatus = 2 // 已失效
)

// TicketBankCheckDifferenceRecord 差额设置记录
type TicketBankCheckDifferenceRecord struct {
	model.PkId
	model.Corporations                                       // 存GroupId
	Difference         int64                                 `json:"Difference" gorm:"column:difference;type:integer;comment:差额 单位分;"`     // 差额 单位分
	OpUserName         string                                `json:"OpUserName" gorm:"column:opusername;type:varchar(50);comment:操作人员账号;"` // 操作人员
	Status             TicketBankCheckDifferenceRecordStatus `json:"Status" gorm:"column:status;type:smallint;comment:状态;"`                // 状态 1进行中 2已失效
	model.Timestamp
}

func (tbc *TicketBankCheckDifferenceRecord) BeforeCreate(tx *gorm.DB) error {
	if tbc.Id == 0 {
		tbc.Id = model.Id()
	}
	return nil
}

func (tbc *TicketBankCheckDifferenceRecord) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&tbc).Error
}

func (tbc *TicketBankCheckDifferenceRecord) TransactionUpdateAllToDone(tx *gorm.DB, groupId int64) error {
	return tx.Model(&TicketBankCheckDifferenceRecord{}).Where("GroupId = ?", groupId).Update("Status", DONE_TBCDRS_2).Error
}

func (tbc *TicketBankCheckDifferenceRecord) GetBy(groupId int64, paginator model.Paginator) ([]TicketBankCheckDifferenceRecord, int64, error) {
	var rsp []TicketBankCheckDifferenceRecord
	var totalCount int64
	err := model.DB().Model(&TicketBankCheckDifferenceRecord{}).Where("GroupId = ?", groupId).
		Count(&totalCount).Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&rsp).Error
	return rsp, totalCount, err
}

func (tbc *TicketBankCheckDifferenceRecord) GetDifference(groupId int64) error {
	return model.DB().Model(&TicketBankCheckDifferenceRecord{}).
		Where("GroupId = ?", groupId).
		Where("Status = ?", DOING_TBCDRS_1).
		Order("CreatedAt DESC").First(&tbc).Error
}

// TicketBankCheckRecord 校对记录 //一个分公司/票款日期/校对 一条校对记录
type TicketBankCheckRecord struct {
	model.PkId
	model.GroupCorporation
	model.CompanyCorporation
	BranchId int64  `json:"BranchId"     gorm:"column:branchid;type:bigint;comment:分公司Id;uniqueIndex:tbcr_branchid_incomeat_checkedat"` // 存至分公司
	Branch   string `json:"Branch"       gorm:"-"`
	model.DepartmentCorporation
	model.FleetCorporation
	IncomeAt                         model.LocalTime `json:"IncomeAt" gorm:"column:incomeat;comment:点钞/票款时间, 票款日期, 真实单据上的日期时间 天;type:date;uniqueIndex:tbcr_branchid_incomeat_checkedat"`           // -- 点钞/票款时间, 票款日期, 真实单据上的日期时间 天
	BankMoney                        int64           `json:"BankMoney" gorm:"column:bankmoney;type:integer;comment:银行清点金额 单位分;"`                                                                   // 银行清点金额 单位分
	BranchIncludedDifferenceMoney    int64           `json:"BranchIncludedDifferenceMoney" gorm:"column:branchincludeddifferencemoney;type:integer;comment:整个分公司的 包括差额实收总金额（修改后点钞实收金额） 单位分;"`      // 整个分公司的 包括差额实收总金额（修改后点钞实收金额） 单位分
	BranchNotIncludedDifferenceMoney int64           `json:"BranchNotIncludedDifferenceMoney" gorm:"column:branchnotincludeddifferencemoney;type:integer;comment:整个分公司的 不包括差额实收总金额（原点钞实收金额） 单位分;"` // 整个分公司的 不包括差额实收总金额（原点钞实收金额） 单位分
	CheckedAt                        model.LocalTime `json:"CheckedAt" gorm:"column:checkedat;type:date;comment:校对日期 天;uniqueIndex:tbcr_branchid_incomeat_checkedat"`                              // 校对日期 天
	model.Timestamp

	ChangeRecords []TicketBankCheckChangeRecord `json:"ChangeRecords"`
}

func (cr *TicketBankCheckRecord) BeforeCreate(tx *gorm.DB) error {
	if cr.Id == 0 {
		cr.Id = model.Id()
	}
	return nil
}

func (cr *TicketBankCheckRecord) TransactionCreate(tx *gorm.DB) error {

	return tx.Create(cr).Error
}

func (cr *TicketBankCheckRecord) GetBy(corpIds []int64, s, e time.Time, paginator model.Paginator) ([]TicketBankCheckRecord, int64, error) {
	var rsp []TicketBankCheckRecord
	var totalCount int64

	tx := model.DB().Model(&TicketBankCheckRecord{}).Where("BranchId IN ?", corpIds)

	if !s.IsZero() {
		tx.Where("CheckedAt >= ?", s)
	}
	if !e.IsZero() {
		tx.Where("CheckedAt < ?", e)
	}

	err := tx.Count(&totalCount).Order("CheckedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&rsp).Error
	return rsp, totalCount, err
}

// GetBranchRecord 查询分公司下记录
func (cr *TicketBankCheckRecord) GetBranchRecord(branchId int64, incomeAt time.Time, checkedAt time.Time) (TicketBankCheckRecord, int64, error) {
	var rsp TicketBankCheckRecord
	var count int64
	err := model.DB().Model(&TicketBankCheckRecord{}).Where("BranchId = ? AND IncomeAt=? AND CheckedAt=?", branchId, incomeAt, checkedAt).Count(&count).Find(&rsp).Error
	return rsp, count, err
}

func (cr *TicketBankCheckRecord) TransactionUpdate(tx *gorm.DB, id int64, columns interface{}) error {
	return tx.Model(&TicketBankCheckRecord{}).Where("Id = ?", id).Updates(columns).Error
}

// TicketBankCheckChangeRecord 校对记录变更记录列表  // 一条校对记录对应多条变更记录
type TicketBankCheckChangeRecord struct {
	model.PkId
	model.Corporations                         // 存至分公司
	TicketBankCheckRecordId    int64           `json:"TicketBankCheckRecordId" gorm:"column:ticketbankcheckrecordid;type:bigint;comment:校对记录id;"`                              // 校对记录id
	LineId                     int64           `json:"LineId" gorm:"column:lineid;type:bigint;comment:线路id;"`                                                                  // 线路id
	Line                       string          `json:"Line" gorm:"column:line;type:varchar(50);comment:线路;"`                                                                   // 线路
	License                    string          `json:"License" gorm:"column:license;type:varchar(50);comment:车牌;"`                                                             // 车牌
	IncomeAt                   model.LocalTime `json:"IncomeAt" gorm:"column:incomeat;comment:点钞/票款时间, 票款日期, 真实单据上的日期时间 天;type:timestamp;"`                                    // -- 点钞/票款时间, 票款日期, 真实单据上的日期时间 天
	BankMoney                  int64           `json:"BankMoney" gorm:"column:bankmoney;type:integer;comment:银行清点金额 单位分;"`                                                     // 银行清点金额 单位分
	IncludedDifferenceMoney    int64           `json:"IncludedDifferenceMoney" gorm:"column:includeddifferencemoney;type:integer;comment:一辆车的 包括差额实收总金额（修改后点钞实收金额） 单位分;"`      // 一辆车的 包括差额实收总金额（修改后点钞实收金额） 单位分
	NotIncludedDifferenceMoney int64           `json:"NotIncludedDifferenceMoney" gorm:"column:notincludeddifferencemoney;type:integer;comment:一辆车的 不包括差额实收总金额（原点钞实收金额） 单位分;"` // 一辆车的 不包括差额实收总金额（原点钞实收金额） 单位分
	model.Timestamp
}

func (ccr *TicketBankCheckChangeRecord) BeforeCreate(tx *gorm.DB) error {
	if ccr.Id == 0 {
		ccr.Id = model.Id()
	}
	return nil
}

func (ccr *TicketBankCheckChangeRecord) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&ccr).Error
}

func (ccr *TicketBankCheckChangeRecord) GetByTicketBankCheckRecordId(ticketBankCheckRecordId int64, paginator model.Paginator) ([]TicketBankCheckChangeRecord, int64, error) {
	var rsp []TicketBankCheckChangeRecord
	var totalCount int64

	err := model.DB().Model(&TicketBankCheckChangeRecord{}).
		Where("TicketBankCheckRecordId = ?", ticketBankCheckRecordId).
		Count(&totalCount).Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&rsp).Error
	return rsp, totalCount, err
}
