package mixReport

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/ticket"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"sort"
	"strings"
	"time"
)

type TicketMoneyMixReportRecord struct {
	model.PkId
	GroupId     int64           `json:"GroupId" gorm:"column:groupid;type:bigint;comment:集团Id"`
	GroupName   string          `json:"GroupName" gorm:"column:groupname;type:varchar;comment:集团"`
	CompanyId   int64           `json:"CompanyId" gorm:"column:companyid;type:bigint;comment:公司Id"`
	CompanyName string          `json:"CompanyName" gorm:"column:companyname;type:varchar;comment:公司"`
	BranchId    int64           `json:"BranchId" gorm:"column:branchid;type:bigint;comment:分公司Id"`
	BranchName  string          `json:"BranchName" gorm:"column:branchname;type:varchar;comment:分公司"`
	FleetId     int64           `json:"FleetId" gorm:"column:fleetid;type:bigint;comment:车队Id"`
	FleetName   string          `json:"FleetName" gorm:"column:fleetname;type:varchar;comment:车队"`
	LineId      int64           `json:"LineId" gorm:"column:lineid;type:bigint;comment:线路ID"`
	LineName    string          `json:"LineName" gorm:"column:linename;type:varchar;comment:线路名称"`
	VehicleId   int64           `json:"VehicleId" gorm:"column:vehicleid;type:bigint;comment:车辆ID"`
	License     string          `json:"License" gorm:"column:license;type:varchar;comment:车牌号"`
	TicketMoney int64           `json:"TicketMoney" gorm:"column:ticketmoney;type:bigint;comment:票务总金额 单位：分"`
	CardMoney   int64           `json:"CardMoney" gorm:"column:cardmoney;type:bigint;comment:刷卡总金额 单位：分"`
	CodeMoney   int64           `json:"CodeMoney" gorm:"column:codemoney;type:bigint;comment:扫码总金额 单位：分"`
	ReportAt    model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:交易时间 票款时间"`
	SettleAt    model.LocalTime `json:"SettleAt" gorm:"column:settleat;type:timestamp;comment:结算时间 录入时间"`
	model.Timestamp
}

func (r *TicketMoneyMixReportRecord) SyncTableColumn(reportAt time.Time) error {
	table := r.GetTable(reportAt.Format("2006"))
	if model.DB().Migrator().HasTable(table) {
		err := model.DB().Table(table).AutoMigrate(&TicketMoneyMixReportRecord{})
		if err != nil {
			return err
		}
	}

	return nil
}

func (r *TicketMoneyMixReportRecord) HasOrCreateTable(reportAt time.Time) error {
	table := r.GetTable(reportAt.Format("2006"))
	if !model.DB().Migrator().HasTable(table) {
		err := model.DB().Table(table).AutoMigrate(&TicketMoneyMixReportRecord{})
		if err != nil {
			return err
		}
		model.CreateUniqueIndex(table, []string{"BranchId", "FleetId", "LineId", "VehicleId", "ReportAt", "SettleAt"})
	}
	return nil
}

func (r *TicketMoneyMixReportRecord) GetTable(suffix string) string {
	return fmt.Sprintf("%s_%v", r.TableName(), suffix)
}

func (r *TicketMoneyMixReportRecord) TableName() string {
	return "ticket_money_mix_report_records"
}

func (r *TicketMoneyMixReportRecord) BeforeCreate(db *gorm.DB) error {
	r.Id = model.Id()
	return nil
}

func (r *TicketMoneyMixReportRecord) Create() error {
	if err := r.HasOrCreateTable(time.Time(r.SettleAt)); err != nil {
		return err
	}

	return model.DB().Table(r.GetTable(time.Time(r.SettleAt).Format("2006"))).Create(&r).Error
}
func (r *TicketMoneyMixReportRecord) Delete(startAt, endAt time.Time) error {
	return model.DB().Table(r.GetTable(startAt.Format("2006"))).Where("SettleAt >= ? AND SettleAt < ?", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat)).Delete(&r).Error
}

func (r *TicketMoneyMixReportRecord) UpdateColumn(column string, val interface{}) error {
	return model.DB().Table(r.GetTable(time.Time(r.SettleAt).Format("2006"))).Where("branchid = ? AND fleetid = ? AND lineid = ? AND vehicleid = ? AND reportat = ? AND settleat = ?",
		r.BranchId, r.FleetId, r.LineId, r.VehicleId, time.Time(r.ReportAt).Format(model.DateFormat), time.Time(r.SettleAt).Format(model.DateFormat)).UpdateColumn(column, val).Error
}

func (r *TicketMoneyMixReportRecord) BuildTable(startAt, endAt time.Time) *gorm.DB {
	reportParts := util.SplitRangeTimeByYear(startAt.AddDate(0, -1, 0), endAt.AddDate(0, 1, 0))
	var sql []interface{}
	var questionSymbol []string
	for i := range reportParts {
		child := model.DB().Table(r.GetTable(reportParts[i].Start.Format("2006"))).Where("SettleAt >= ? AND SettleAt <= ?", reportParts[i].Start.Format(model.DateFormat), reportParts[i].End.Format(model.DateFormat))
		sql = append(sql, child)
		questionSymbol = append(questionSymbol, "?")
	}

	raw := strings.Join(questionSymbol, " UNION ")

	return model.DB().Raw(raw, sql...)
}

type SubTotalItem struct {
	Month             string `json:"Month" gorm:"column:month"`                         // 月份 yyyy-MM
	BranchId          int64  `json:"BranchId" gorm:"column:branchid"`                   // 所属分公司id
	Branch            string `json:"Branch" gorm:"column:branchname"`                   // 所属分公司
	CountVehicle      int64  `json:"CountVehicle" gorm:"column:countvehicle"`           // 车辆数  实际计算的是录入车辆次数  例如：假设一天录入1000辆车的数据那么改天车辆数为1000， 月车辆数为1000*30
	MoneyTicketAmount int64  `json:"MoneyTicketAmount" gorm:"column:moneyticketamount"` // 票款收入 单位分
	MoneyCardAmount   int64  `json:"MoneyCardAmount" gorm:"column:moneycardamount"`     // 刷卡收入 单位分
	MoneyCodeAmount   int64  `json:"MoneyCodeAmount" gorm:"column:moneycodeamount"`     // 扫码收入 单位分
	AVGIncomeVehicle  int64  `json:"AVGIncomeVehicle" gorm:"column:avgincomevehicle"`   // 平均单车单天收入
	TotalAmount       int64  `json:"TotalAmount"`
}

// IncomeBranch 公司营收报表  实际是分公司
func (r *TicketMoneyMixReportRecord) IncomeBranch(timeType ticket.TimeType, branchIds []int64, startAt, endAt time.Time) ([]SubTotalItem, error) {
	var (
		rsp []SubTotalItem
		err error
	)
	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table)

	if timeType == ticket.TIME_INPUT_1 {
		tx.Select(
			"to_char(SettleAt, 'yyyy-MM-dd') AS Month", "BranchId", "BranchName", "count(License) AS CountVehicle", "sum(TicketMoney) AS MoneyTicketAmount", "sum(CardMoney) AS MoneyCardAmount", "sum(CodeMoney) AS MoneyCodeAmount").Where("BranchId IN ? AND SettleAt >= ? AND SettleAt < ?",
			branchIds,
			startAt,
			endAt,
		).Group("to_char(SettleAt, 'yyyy-MM-dd' ), BranchId, BranchName")
	} else if timeType == ticket.TIME_TICKET_2 {
		tx.Select(
			"to_char(ReportAt, 'yyyy-MM-dd') AS Month", "BranchId", "BranchName", "count(License) AS CountVehicle", "sum(TicketMoney) AS MoneyTicketAmount", "sum(CardMoney) AS MoneyCardAmount", "sum(CodeMoney) AS MoneyCodeAmount").Where("BranchId IN ? AND ReportAt >= ? AND ReportAt < ?",
			branchIds,
			startAt,
			endAt,
		).Group("to_char(ReportAt, 'yyyy-MM-dd' ), BranchId, BranchName")
	}

	err = tx.Order("Month DESC").Scan(&rsp).Error

	return rsp, err
}

type IncomeLineRsp struct {
	TotalCount             int64               `json:"TotalCount"`             // 分页总条数
	TotalCountVehicle      int64               `json:"TotalCountVehicle"`      // 车辆数总计
	TotalMoneyTicketAmount int64               `json:"TotalMoneyTicketAmount"` // 票款总计
	TotalMoneyCardAmount   int64               `json:"TotalMoneyCardAmount"`   // 票款总计
	TotalMoneyCodeAmount   int64               `json:"TotalMoneyCodeAmount"`   // 票款总计
	TotalAmount            int64               `json:"TotalAmount"`            // 票款总计
	Data                   []IncomeLineRspItem `json:"Data"`
}

type IncomeLineRspItem struct {
	DateAt            string `json:"DateAt" gorm:"column:dateat"`                       // yyyy-MM-dd
	BranchId          int64  `json:"BranchId" gorm:"column:branchid"`                   // 分公司id
	Branch            string `json:"Branch" gorm:"column:branchname"`                   // 分公司
	FleetId           int64  `json:"FleetId" gorm:"column:fleetid"`                     // 车队id
	Fleet             string `json:"Fleet" gorm:"column:fleetname"`                     // 车队
	LineId            int64  `json:"LineId" gorm:"column:lineid"`                       // 线路id
	Line              string `json:"Line" gorm:"column:linename"`                       // 线路
	CountVehicle      int64  `json:"CountVehicle" gorm:"column:countvehicle"`           // 车辆(次)数
	MoneyTicketAmount int64  `json:"MoneyTicketAmount" gorm:"column:moneyticketamount"` // 票款
	MoneyCardAmount   int64  `json:"MoneyCardAmount" gorm:"column:moneycardamount"`     // 票款
	MoneyCodeAmount   int64  `json:"MoneyCodeAmount" gorm:"column:moneycodeamount"`     // 票款
	TotalAmount       int64  `json:"TotalAmount"`
}

func (r *TicketMoneyMixReportRecord) IncomeLine(timeType ticket.TimeType, branchIds, lineIds, fleetIds []int64, startAt, endAt time.Time) (IncomeLineRsp, error) {
	var rsp IncomeLineRsp

	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table)

	if len(branchIds) > 0 {
		tx.Where("BranchId IN ?", branchIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if len(fleetIds) > 0 {
		tx.Where("FleetId IN ?", fleetIds)
	}
	if timeType == ticket.TIME_INPUT_1 {
		tx.Select(
			"to_char(SettleAt, 'yyyy-MM-dd') as DateAt", "BranchId", "BranchName", "FleetId", "FleetName", "LineId", "LineName", "count(License) AS CountVehicle", "sum(TicketMoney) AS MoneyTicketAmount", "sum(CardMoney) AS MoneyCardAmount", "sum(CodeMoney) AS MoneyCodeAmount").Where(
			"SettleAt >= ? AND SettleAt < ?", startAt, endAt).Group(
			"to_char(SettleAt, 'yyyy-MM-dd'), BranchId,BranchName, FleetId, FleetName, LineId, LineName")
	} else if timeType == ticket.TIME_TICKET_2 {
		tx.Select(
			"to_char(ReportAt, 'yyyy-MM-dd') as DateAt", "BranchId", "BranchName", "FleetId", "FleetName", "LineId", "LineName", "count(License) AS CountVehicle", "sum(TicketMoney) AS MoneyTicketAmount", "sum(CardMoney) AS MoneyCardAmount", "sum(CodeMoney) AS MoneyCodeAmount").Where(
			"ReportAt >= ? AND ReportAt < ?", startAt, endAt).Group(
			"to_char(ReportAt, 'yyyy-MM-dd'), BranchId,BranchName, FleetId, FleetName, LineId, LineName")
	}

	tx.Scan(&rsp.Data).Count(&rsp.TotalCount)

	return rsp, tx.Error

}

// 排序
type LessFuncLine func(p1, p2 *IncomeLineRspItem) bool
type OrderByLine []LessFuncLine

type MultiSorterLine struct {
	Changes   []IncomeLineRspItem
	LessFuncs []LessFuncLine
}

func (m *MultiSorterLine) Len() int {
	return len(m.Changes)
}

func (m *MultiSorterLine) Swap(i, j int) {
	m.Changes[i], m.Changes[j] = m.Changes[j], m.Changes[i]
}

func (m *MultiSorterLine) Less(i, j int) bool {
	// 为了后面编写简便, 这里将需要比较的两个元素赋值给两个单独的变量
	p, q := &m.Changes[i], &m.Changes[j]
	// Try all but the last comparison.
	var k int
	// 由于可能有多个需要排序的字段, 也就对应了多个less函数, 当第一个字段的值相等时,
	// 需要依次尝试比对后续其他字段的值得大小, 所以这里需要获取比较函数的长度, 以便遍历比较
	for k = 0; k < len(m.LessFuncs)-1; k++ {
		// 提取比较函数, 将函数赋值到新的变量中以便调用
		less := m.LessFuncs[k]
		switch {
		case less(p, q):
			// 如果 p < q, 返回值为true, 不存在两个值相等需要比较后续字段的情况, 所以这里直接返回
			// 如果 p > q, 返回值为false, 则调到下一个case中处理
			return true
		case less(q, p):
			// 如果 p > q, 返回值为false, 不存在两个值相等需要比较后续字段的情况, 所以这里直接返回
			return false
		}
		// 如果代码走到这里, 说明m.LessFuncs[k]函数比较后 p == q; 重新开始下一次循环, 更换到下一个比较函数处理
		continue
	}
	// 如果代码走到这里, 说明所有的比较函数执行过后, 所有比较的值都相等
	// 直接返回最后一次的比较结果数据即可
	return m.LessFuncs[k](p, q)
}

func (o OrderByLine) Sort(changes []IncomeLineRspItem) {
	ms := &MultiSorterLine{
		Changes:   changes,
		LessFuncs: o,
	}
	sort.Sort(ms)
}

type IncomeVehicleRsp struct {
	TotalCount             int64 // 分页总条数
	TotalMoneyTicketAmount int64 //点钞总计
	TotalMoneyCardAmount   int64 // 刷卡总计
	TotalMoneyCodeAmount   int64 // 扫码总计
	TotalAmount            int64 // 票款总计
	Data                   []IncomeVehicleRspItem
}

type IncomeVehicleRspItem struct {
	DateAt            time.Time `json:"-" gorm:"column:dateat"`                            //
	DateAtUnix        int64     `json:"DateAt"`                                            //
	BranchId          int64     `json:"BranchId" gorm:"column:branchid"`                   // 分公司id
	Branch            string    `json:"Branch" gorm:"column:branchname"`                   // 分公司
	FleetId           int64     `json:"FleetId" gorm:"column:fleetid"`                     // 车队id
	Fleet             string    `json:"Fleet" gorm:"column:fleetname"`                     // 车队
	LineId            int64     `json:"LineId" gorm:"column:lineid"`                       // 线路id
	Line              string    `json:"Line" gorm:"column:linename"`                       // 线路
	License           string    `json:"License" gorm:"column:license"`                     // 车牌号
	MoneyTicketAmount int64     `json:"MoneyTicketAmount" gorm:"column:moneyticketamount"` // 点钞
	MoneyCardAmount   int64     `json:"MoneyCardAmount" gorm:"column:moneycardamount"`     // 刷卡
	MoneyCodeAmount   int64     `json:"MoneyCodeAmount" gorm:"column:moneycodeamount"`     // 扫码
	TotalAmount       int64     `json:"TotalAmount"`                                       // 总额
}

// IncomeVehicle 车辆营收报表
func (r *TicketMoneyMixReportRecord) IncomeVehicle(timeType ticket.TimeType, branchIds, fleetIds, lineIds []int64, licenses []string, startAt, endAt time.Time) IncomeVehicleRsp {
	var rsp IncomeVehicleRsp

	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table)

	if len(licenses) > 0 {
		tx.Where("License IN ?", licenses)
	}

	if len(branchIds) > 0 {
		tx.Where("BranchId IN ?", branchIds)
	}

	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}

	if len(fleetIds) > 0 {
		tx.Where("FleetId IN ?", fleetIds)
	}

	if timeType == ticket.TIME_INPUT_1 {
		tx.Select(
			"SettleAt AS DateAt", "BranchId", "BranchName", "FleetId", "FleetName", "LineId", "LineName", "License", "SUM(TicketMoney) AS MoneyTicketAmount", " SUM(CardMoney) AS MoneyCardAmount", "SUM(CodeMoney) AS MoneyCodeAmount").Where(
			"SettleAt >= ? AND SettleAt < ?", startAt, endAt).Group("SettleAt,BranchId,BranchName,FleetId,FleetName,LineId,LineName,License")
	} else if timeType == ticket.TIME_TICKET_2 {
		tx.Select(
			"ReportAt As DateAt", "BranchId", "BranchName", "FleetId", "FleetName", "LineId", "LineName", "License", "SUM(TicketMoney) AS MoneyTicketAmount", " SUM(CardMoney) AS MoneyCardAmount", "SUM(CodeMoney) AS MoneyCodeAmount").Where(
			"ReportAt >= ? AND ReportAt < ?", startAt, endAt).Group("ReportAt,BranchId,BranchName,FleetId,FleetName,LineId,LineName,License")
	}

	if timeType == ticket.TIME_INPUT_1 {
		tx.Order("BranchId, SettleAt, LineId").Scan(&rsp.Data).Count(&rsp.TotalCount)
	} else if timeType == ticket.TIME_TICKET_2 {
		tx.Order("BranchId, ReportAt, LineId").Scan(&rsp.Data).Count(&rsp.TotalCount)
	}

	return rsp
}

// 车辆报表排序
type LessFunc func(p1, p2 *IncomeVehicleRspItem) bool
type OrderBy []LessFunc

type MultiSorter struct {
	Changes   []IncomeVehicleRspItem
	LessFuncs []LessFunc
}

func (m *MultiSorter) Len() int {
	return len(m.Changes)
}

func (m *MultiSorter) Swap(i, j int) {
	m.Changes[i], m.Changes[j] = m.Changes[j], m.Changes[i]
}

func (m *MultiSorter) Less(i, j int) bool {
	// 为了后面编写简便, 这里将需要比较的两个元素赋值给两个单独的变量
	p, q := &m.Changes[i], &m.Changes[j]
	// Try all but the last comparison.
	var k int
	// 由于可能有多个需要排序的字段, 也就对应了多个less函数, 当第一个字段的值相等时,
	// 需要依次尝试比对后续其他字段的值得大小, 所以这里需要获取比较函数的长度, 以便遍历比较
	for k = 0; k < len(m.LessFuncs)-1; k++ {
		// 提取比较函数, 将函数赋值到新的变量中以便调用
		less := m.LessFuncs[k]
		switch {
		case less(p, q):
			// 如果 p < q, 返回值为true, 不存在两个值相等需要比较后续字段的情况, 所以这里直接返回
			// 如果 p > q, 返回值为false, 则调到下一个case中处理
			return true
		case less(q, p):
			// 如果 p > q, 返回值为false, 不存在两个值相等需要比较后续字段的情况, 所以这里直接返回
			return false
		}
		// 如果代码走到这里, 说明m.LessFuncs[k]函数比较后 p == q; 重新开始下一次循环, 更换到下一个比较函数处理
		continue
	}
	// 如果代码走到这里, 说明所有的比较函数执行过后, 所有比较的值都相等
	// 直接返回最后一次的比较结果数据即可
	return m.LessFuncs[k](p, q)
}

func (o OrderBy) Sort(changes []IncomeVehicleRspItem) {
	ms := &MultiSorter{
		Changes:   changes,
		LessFuncs: o,
	}
	sort.Sort(ms)
}
