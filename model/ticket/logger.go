package ticket

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type SceneType int64

const (
	SCENE_CREATE SceneType = 1
	SCENE_UPDATE SceneType = 2
)

type TicketLogger struct {
	model.PkId
	TicketMoneyId int64      `json:"TicketMoneyId" gorm:"column:ticketmoneyid;comment:关联点钞票款表主键ID;type:bigint"` //关联点钞票款表主键ID
	Scene         SceneType  `json:"Scene" gorm:"column:scene;comment:场景值  1新增  2更新;type:smallint"`             //场景值  1新增  2更新
	BeforeData    model.JSON `json:"BeforeData" gorm:"column:beforedata;comment:更改前的数据;type:json"`              //更改前的数据
	AfterData     model.JSON `json:"AfterData" gorm:"column:afterdata;comment:更改后的数据;type:json"`                //更改后的数据
	Ip            string     `json:"Ip" gorm:"column:ip;comment:Ip地址;type:varchar(64)"`                         //Ip地址
	model.OpUser
	model.Timestamp
}

func (log *TicketLogger) BeforeCreate(tx *gorm.DB) (err error) {
	log.Id = model.Id()
	return
}

func (log *TicketLogger) Create() error {
	return model.DB().Create(log).Error
}

func (log *TicketLogger) List(ticketMoneyId int64, keyword string, paginator model.Paginator) ([]TicketLogger, int64) {
	var loggers []TicketLogger
	tx := model.DB().Model(&TicketLogger{}).Where("TicketMoneyId = ?", ticketMoneyId)

	if keyword != "" {
		tx = tx.Where("OpUserName LIKE ?", "%"+keyword+"%")
	}

	var count int64
	tx.Count(&count)

	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	tx.Order("CreatedAt DESC").Find(&loggers)

	return loggers, count
}
