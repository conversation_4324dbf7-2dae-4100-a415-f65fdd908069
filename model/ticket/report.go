package ticket

import (
	"app/org/scs/erpv2/api/model"
	"sort"
	"time"
)

type DefectiveTotal struct {
	Defective    int64 // 残次币总数
	MoneyFake    int64
	MoneyForeign int64
	MoneyOther   int64
}

// 残次币查询
func (t *TicketCountMoneys) ListDefective(timeType TimeType, branchIds, fleetIds, lineIds []int64, licenses []string, keyword string, startAt, endAt time.Time, paginator model.Paginator) ([]TicketCountMoneys, int64, DefectiveTotal) {
	var (
		totalCount int64
		rspData    []TicketCountMoneys
		tmp        []TicketCountMoneys
		total      DefectiveTotal
	)

	tx := model.DB().Model(&TicketCountMoneys{}).Where("MoneyFake > ? OR MoneyForeign > ? OR MoneyOther > ?", 0, 0, 0)

	if timeType == TIME_INPUT_1 {
		tx.Where("ReportAt >= ? AND ReportAt < ?", startAt, endAt)
	} else if timeType == TIME_TICKET_2 {
		tx.Where("IncomeAt >= ? AND IncomeAt < ?", startAt, endAt)
	}

	if len(licenses) > 0 {
		tx.Where("License IN ?", licenses)
	}

	if len(branchIds) > 0 {
		tx.Where("BranchId IN ?", branchIds)
	}

	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}

	if len(fleetIds) > 0 {
		tx.Where("FleetId IN ?", fleetIds)
	}

	if keyword != "" {
		tx.Where("License LIKE ? OR Line LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	tx.Scan(&tmp).Count(&totalCount)
	tx.Order("Id DESC").Limit(paginator.Limit).Offset(paginator.Offset).Scan(&rspData)

	for _, moneys := range tmp {
		total.Defective += moneys.MoneyFake + moneys.MoneyForeign + moneys.MoneyOther
		total.MoneyFake += moneys.MoneyFake
		total.MoneyForeign += moneys.MoneyForeign
		total.MoneyOther += moneys.MoneyOther
	}

	return rspData, totalCount, total
}

type SubTotalItem struct {
	Month             string `json:"Month"             gorm:"column:month"`             // 月份 yyyy-MM
	BranchId          int64  `json:"BranchId"          gorm:"column:branchid"`          // 所属分公司id
	Branch            string `json:"Branch"`                                            // 所属分公司
	CountVehicle      int64  `json:"CountVehicle"      gorm:"column:countvehicle"`      // 车辆数  实际计算的是录入车辆次数  例如：假设一天录入1000辆车的数据那么改天车辆数为1000， 月车辆数为1000*30
	MoneyTicketAmount int64  `json:"MoneyTicketAmount" gorm:"column:moneyticketamount"` // 票款收入 单位分
	AVGIncomeVehicle  int64  `json:"AVGIncomeVehicle"  gorm:"column:avgincomevehicle"`  // 平均单车单天收入
}

// 公司营收报表  实际是分公司
func (t *TicketCountMoneys) IncomeBranch(timeType TimeType, branchIds []int64, startAt, endAt time.Time) ([]SubTotalItem, error) {
	var (
		rsp []SubTotalItem
		err error
	)

	tx := model.DB().Model(&TicketCountMoneys{})

	if timeType == TIME_INPUT_1 {
		tx.Select(
			"to_char(ReportAt, 'yyyy-MM-dd') AS Month", "BranchId", "count(License) AS CountVehicle", "sum(TotalAmount) AS MoneyTicketAmount", "(coalesce(sum(TotalAmount)/(nullif(count(License), 0)), 0)) AS AVGIncomeVehicle").Where("BranchId IN ? AND ReportAt >= ? AND ReportAt < ?",
			branchIds,
			startAt,
			endAt,
		).Group("to_char(ReportAt, 'yyyy-MM-dd' ), BranchId")
	} else if timeType == TIME_TICKET_2 {
		tx.Select(
			"to_char(IncomeAt, 'yyyy-MM-dd') AS Month", "BranchId", "count(License) AS CountVehicle", "sum(TotalAmount) AS MoneyTicketAmount", "(coalesce(sum(TotalAmount)/(nullif(count(License), 0)), 0)) AS AVGIncomeVehicle").Where("BranchId IN ? AND IncomeAt >= ? AND IncomeAt < ?",
			branchIds,
			startAt,
			endAt,
		).Group("to_char(IncomeAt, 'yyyy-MM-dd' ), BranchId")
	}

	err = tx.Order("Month DESC").Scan(&rsp).Error

	return rsp, err
}

type IncomeLineRsp struct {
	TotalCount             int64               `json:"TotalCount" gorm:"column:totalcount"`                          // 分页总条数
	TotalCountVehicle      int64               `json:"TotalCountVehicle" gorm:"column:totalcountvehicle"`            // 车辆数总计
	TotalMoneyTicketAmount int64               `json:"TotalMoneyTicketAmount" gorm:"column:total-moneyticketamount"` // 票款总计
	Data                   []IncomeLineRspItem `json:"Data" gorm:"column:data"`
}

type IncomeLineRspItem struct {
	DateAt            string `json:"DateAt" gorm:"column:dateat"`                       // yyyy-MM-dd
	BranchId          int64  `json:"BranchId" gorm:"column:branchid"`                   // 分公司id
	Branch            string `json:"Branch" gorm:"column:branch"`                       // 分公司
	FleetId           int64  `json:"FleetId" gorm:"column:fleetid"`                     // 车队id
	Fleet             string `json:"Fleet" gorm:"column:fleet"`                         // 车队
	LineId            int64  `json:"LineId" gorm:"column:lineid"`                       // 线路id
	Line              string `json:"Line" gorm:"column:line"`                           // 线路
	CountVehicle      int64  `json:"CountVehicle" gorm:"column:countvehicle"`           // 车辆(次)数
	MoneyTicketAmount int64  `json:"MoneyTicketAmount" gorm:"column:moneyticketamount"` // 票款
}

//  线路营收报表
func (t *TicketCountMoneys) IncomeLine(timeType TimeType, branchIds, lineIds, fleetIds []int64, startAt, endAt time.Time) (IncomeLineRsp, error) {
	var rsp IncomeLineRsp
	tx := model.DB().Model(&TicketCountMoneys{})

	if len(branchIds) > 0 {
		tx.Where("BranchId IN ?", branchIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if len(fleetIds) > 0 {
		tx.Where("FleetId IN ?", fleetIds)
	}
	if timeType == TIME_INPUT_1 {
		tx.Select(
			"to_char(ReportAt, 'yyyy-MM-dd') as DateAt", "BranchId", "FleetId", "LineId", "count(License) AS CountVehicle", "sum(TotalAmount) AS MoneyTicketAmount").Where(
			"ReportAt >= ? AND ReportAt < ?", startAt, endAt).Group(
			"to_char(ReportAt, 'yyyy-MM-dd'), BranchId, FleetId, LineId")
	} else if timeType == TIME_TICKET_2 {
		tx.Select(
			"to_char(IncomeAt, 'yyyy-MM-dd') as DateAt", "BranchId", "FleetId", "LineId", "count(License) AS CountVehicle", "sum(TotalAmount) AS MoneyTicketAmount").Where(
			"IncomeAt >= ? AND IncomeAt < ?", startAt, endAt).Group(
			"to_char(IncomeAt, 'yyyy-MM-dd'), BranchId, FleetId, LineId")
	}

	tx.Scan(&rsp.Data).Count(&rsp.TotalCount)

	return rsp, tx.Error

}

type IncomeVehicleRsp struct {
	TotalCount             int64 // 分页总条数
	TotalMoneyTicketAmount int64 // 票款总计
	Data                   []IncomeVehicleRspItem
}

type IncomeVehicleRspItem struct {
	//ReportAt          time.Time `json:"-" gorm:"column:reportat"`
	//ReportAtUnix      int64     `json:"ReportAt"`
	//IncomeAt          time.Time `json:"-" gorm:"column:incomeat"`
	//IncomeAtUnix      int64     `json:"IncomeAt"`
	DateAt            time.Time `json:"-" gorm:"column:dateat"`                      //
	DateAtUnix        int64     `json:"DateAt"`                                      //
	BranchId          int64     `json:"BranchId" gorm:"column:branchid"`             // 分公司id
	Branch            string    `json:"Branch" gorm:"column:branch"`                 // 分公司
	FleetId           int64     `json:"FleetId" gorm:"column:fleetid"`               // 车队id
	Fleet             string    `json:"Fleet" gorm:"column:fleet"`                   // 车队
	LineId            int64     `json:"LineId" gorm:"column:lineid"`                 // 线路id
	Line              string    `json:"Line" gorm:"column:line"`                     // 线路
	License           string    `json:"License" gorm:"column:license"`               // 车牌号
	MoneyTicketAmount int64     `json:"MoneyTicketAmount" gorm:"column:totalamount"` // 票款
}

// 车辆营收报表
func (t *TicketCountMoneys) IncomeVehicle(timeType TimeType, branchIds, fleetIds, lineIds []int64, licenses []string, startAt, endAt time.Time) (IncomeVehicleRsp, error) {
	var rsp IncomeVehicleRsp

	tx := model.DB().Model(&TicketCountMoneys{})

	if len(licenses) > 0 {
		tx.Where("License IN ?", licenses)
	}

	if len(branchIds) > 0 {
		tx.Where("BranchId IN ?", branchIds)
	}

	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}

	if len(fleetIds) > 0 {
		tx.Where("FleetId IN ?", fleetIds)
	}

	if timeType == TIME_INPUT_1 {
		tx.Select(
			"BranchId", "ReportAt AS DateAt", "FleetId", "LineId", "License", "TotalAmount").Where(
			"ReportAt >= ? AND ReportAt < ?", startAt, endAt)
	} else if timeType == TIME_TICKET_2 {
		tx.Select(
			"BranchId", "IncomeAt As DateAt", "FleetId", "LineId", "License", "TotalAmount").Where(
			"IncomeAt >= ? AND IncomeAt < ?", startAt, endAt)
	}

	if timeType == TIME_INPUT_1 {
		tx.Order("BranchId, ReportAt, LineId").Scan(&rsp.Data).Count(&rsp.TotalCount)
	} else if timeType == TIME_TICKET_2 {
		tx.Order("BranchId, IncomeAt, LineId").Scan(&rsp.Data).Count(&rsp.TotalCount)
	}
	for _, item := range rsp.Data {
		rsp.TotalMoneyTicketAmount += item.MoneyTicketAmount
	}
	return rsp, tx.Error
}

// 排序
type LessFuncTicket func(p1, p2 *TicketCountMoneys) bool
type OrderByTicket []LessFuncTicket

type MultiSorterTicket struct {
	Changes   []TicketCountMoneys
	LessFuncs []LessFuncTicket
}

func (m *MultiSorterTicket) Len() int {
	return len(m.Changes)
}

func (m *MultiSorterTicket) Swap(i, j int) {
	m.Changes[i], m.Changes[j] = m.Changes[j], m.Changes[i]
}

func (m *MultiSorterTicket) Less(i, j int) bool {
	// 为了后面编写简便, 这里将需要比较的两个元素赋值给两个单独的变量
	p, q := &m.Changes[i], &m.Changes[j]
	// Try all but the last comparison.
	var k int
	// 由于可能有多个需要排序的字段, 也就对应了多个less函数, 当第一个字段的值相等时,
	// 需要依次尝试比对后续其他字段的值得大小, 所以这里需要获取比较函数的长度, 以便遍历比较
	for k = 0; k < len(m.LessFuncs)-1; k++ {
		// 提取比较函数, 将函数赋值到新的变量中以便调用
		less := m.LessFuncs[k]
		switch {
		case less(p, q):
			// 如果 p < q, 返回值为true, 不存在两个值相等需要比较后续字段的情况, 所以这里直接返回
			// 如果 p > q, 返回值为false, 则调到下一个case中处理
			return true
		case less(q, p):
			// 如果 p > q, 返回值为false, 不存在两个值相等需要比较后续字段的情况, 所以这里直接返回
			return false
		}
		// 如果代码走到这里, 说明m.LessFuncs[k]函数比较后 p == q; 重新开始下一次循环, 更换到下一个比较函数处理
		continue
	}
	// 如果代码走到这里, 说明所有的比较函数执行过后, 所有比较的值都相等
	// 直接返回最后一次的比较结果数据即可
	return m.LessFuncs[k](p, q)
}

func (o OrderByTicket) Sort(changes []TicketCountMoneys) {
	ms := &MultiSorterTicket{
		Changes:   changes,
		LessFuncs: o,
	}
	sort.Sort(ms)
}

// 车辆营收报表 导出
func (t *TicketCountMoneys) IncomeVehicleExportAll(timeType TimeType, branchIds, fleetIds, lineIds []int64, license string, startAt, endAt time.Time) ([]TicketCountMoneys, int64) {
	var (
		rsp        []TicketCountMoneys
		totalCount int64
	)
	tx := model.DB().Model(&TicketCountMoneys{})

	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}

	if len(branchIds) > 0 {
		tx.Where("BranchId IN ?", branchIds)
	}

	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}

	if len(fleetIds) > 0 {
		tx.Where("FleetId IN ?", fleetIds)
	}

	if timeType == TIME_INPUT_1 {
		tx.Select(
			"BranchId", "ReportAt", "FleetId", "LineId", "License", "VehicleCode", "TotalAmount").Where(
			"ReportAt >= ? AND ReportAt < ?", startAt, endAt)
	} else if timeType == TIME_TICKET_2 {
		tx.Select(
			"BranchId", "IncomeAt", "FleetId", "LineId", "License", "VehicleCode", "TotalAmount").Where(
			"IncomeAt >= ? AND IncomeAt < ?", startAt, endAt)
	}

	tx.Count(&totalCount).Scan(&rsp)

	return rsp, totalCount
}

type IncomeVehicleExportGroupRspItem struct {
	//DateAt            time.Time `json:"-" gorm:"column:dateat"`                      //
	//DateAtUnix        int64     `json:"DateAt"`                                      //
	BranchId int64  `json:"BranchId" gorm:"column:branchid"` // 分公司id
	Branch   string `json:"Branch" gorm:"column:branch"`     // 分公司
	//FleetId           int64     `json:"FleetId" gorm:"column:fleetid"`               // 车队id
	//Fleet             string    `json:"Fleet" gorm:"column:fleet"`                   // 车队
	LineId  int64  `json:"LineId" gorm:"column:lineid"`   // 线路id
	Line    string `json:"Line" gorm:"column:line"`       // 线路
	License string `json:"License" gorm:"column:license"` // 车牌号
	//MoneyTicketAmount int64     `json:"MoneyTicketAmount" gorm:"column:totalamount"` // 票款
}

func (t *TicketCountMoneys) IncomeVehicleExportGroup(timeType TimeType, branchIds, fleetIds, lineIds []int64, license string, startAt, endAt time.Time) []IncomeVehicleExportGroupRspItem {
	var (
		rsp []IncomeVehicleExportGroupRspItem
	)
	tx := model.DB().Model(&TicketCountMoneys{})

	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}

	if len(branchIds) > 0 {
		tx.Where("BranchId IN ?", branchIds)
	}

	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}

	if len(fleetIds) > 0 {
		tx.Where("FleetId IN ?", fleetIds)
	}

	if timeType == TIME_INPUT_1 {
		tx.Select(
			"BranchId", "LineId", "License").Where(
			"ReportAt >= ? AND ReportAt < ?", startAt, endAt)
	} else if timeType == TIME_TICKET_2 {
		tx.Select(
			"BranchId", "LineId", "License").Where(
			"IncomeAt >= ? AND IncomeAt < ?", startAt, endAt)
	}

	tx.Group("BranchId, LineId, License").Scan(&rsp)

	return rsp
}

type AttendantDateRsp struct {
	TotalCount   int64                  `json:"TotalCount"            gorm:"column:totalcount"`   // 分页总条数
	TicketCount  int64                  `json:"TicketCount"           gorm:"column:ticketcount"`  // 票张数总计
	TicketAmount int64                  `json:"TicketAmount"          gorm:"column:ticketamount"` // 票金额总计
	Data         []AttendantDateRspItem `json:"Data" gorm:"column:data"`
}

type AttendantDateRspItem struct {
	BranchId            int64     `json:"BranchId"            gorm:"column:branchid"`
	Branch              string    `json:"Branch"              gorm:"column:branch"`
	License             string    `json:"License"             gorm:"column:license"`
	Line                string    `json:"Line"                gorm:"column:line"`
	ReportAt            time.Time `json:"-"                   gorm:"column:reportat"`
	ReportAtUnix        int64     `json:"ReportAt"`
	AttendantStaffId    int64     `json:"AttendantStaffId"    gorm:"column:attendantstaffid"`    // 乘务员staffId 主数据司机表中的Id字段
	AttendantName       string    `json:"AttendantName"       gorm:"column:attendantname"`       // 乘务员姓名
	AttendantStaffIdStr string    `json:"AttendantStaffIdStr" gorm:"column:attendantstaffidstr"` // 乘务员工号
	Ticket100           int64     `json:"Ticket100"           gorm:"column:ticket100"`
	Ticket150           int64     `json:"Ticket150"           gorm:"column:ticket150"`
	Ticket200           int64     `json:"Ticket200"           gorm:"column:ticket200"`
	Ticket250           int64     `json:"Ticket250"           gorm:"column:ticket250"`
	Ticket300           int64     `json:"Ticket300"           gorm:"column:ticket300"`
	Ticket350           int64     `json:"Ticket350"           gorm:"column:ticket350"`
	Ticket400           int64     `json:"Ticket400"           gorm:"column:ticket400"`
	Ticket450           int64     `json:"Ticket450"           gorm:"column:ticket450"`
	Ticket500           int64     `json:"Ticket500"           gorm:"column:ticket500"`
	Ticket550           int64     `json:"Ticket550"           gorm:"column:ticket550"`
	Ticket600           int64     `json:"Ticket600"           gorm:"column:ticket600"`
	Ticket650           int64     `json:"Ticket650"           gorm:"column:ticket650"`
	Ticket700           int64     `json:"Ticket700"           gorm:"column:ticket700"`
	Ticket750           int64     `json:"Ticket750"           gorm:"column:ticket750"`
	Ticket800           int64     `json:"Ticket800"           gorm:"column:ticket800"`
	Ticket850           int64     `json:"Ticket850"           gorm:"column:ticket850"`
	Ticket900           int64     `json:"Ticket900"           gorm:"column:ticket900"`
	Ticket950           int64     `json:"Ticket950"           gorm:"column:ticket950"`
	Ticket1000          int64     `json:"Ticket1000"          gorm:"column:ticket1000"`
	TicketTotalCount    int64     `json:"TicketTotalCount"    gorm:"column:tickettotalcount"`  // 票张数小计
	TicketTotalAmount   int64     `json:"TicketTotalAmount"   gorm:"column:tickettotalamount"` // 票金额小计
}

// 乘务员日报
func (t *TicketCountMoneys) AttendantDate(timeType TimeType, branchIds []int64, lineId, staffId int64, license string, keyword string, startAt, endAt time.Time, paginator model.Paginator) (AttendantDateRsp, error) {
	var rsp AttendantDateRsp
	tx := model.DB().Model(&TicketCountMoneys{}).Select(
		"BranchId", "License", "Line", "ReportAt", "AttendantStaffId", "AttendantName", "AttendantStaffIdStr",
		"Ticket100", "Ticket150", "Ticket200", "Ticket250", "Ticket300", "Ticket350", "Ticket400", "Ticket450", "Ticket500",
		"Ticket550", "Ticket600", "Ticket650", "Ticket700", "Ticket750", "Ticket800", "Ticket850", "Ticket900", "Ticket950", "Ticket1000",
		"TicketTotalCount", "TicketTotalAmount").Where("Type = ?", TICKET)

	if keyword != "" {
		tx.Where("AttendantName LIKE ? OR AttendantStaffIdStr LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}

	if len(branchIds) > 0 {
		tx.Where("BranchId IN ?", branchIds)
	}

	if timeType == 1 {
		tx.Where("ReportAt >= ? AND ReportAt < ?", startAt, endAt)
	} else if timeType == 2 {
		tx.Where("IncomeAt >= ? AND IncomeAt < ?", startAt, endAt)
	}

	if lineId > 0 {
		tx.Where("LineId = ? ", lineId)
	}

	if staffId > 0 {
		tx.Where("AttendantStaffId = ? ", staffId)
	}

	tx.Scan(&rsp.Data).Count(&rsp.TotalCount)

	// 计算总计张数金额
	for _, item := range rsp.Data {
		rsp.TicketCount += item.TicketTotalCount
		rsp.TicketAmount += item.TicketTotalAmount
	}
	rsp.Data = []AttendantDateRspItem{}

	tx.Order("IncomeAt,BranchId,Line").Limit(paginator.Limit).Offset(paginator.Offset).Scan(&rsp.Data)
	return rsp, tx.Error
}

type MultiTicketRsp struct {
	TotalCount   int64 // 分页总条数
	TicketCount  int64 // 票张数总计
	TicketAmount int64 // 票金额总计
	Data         []MultiTicketRspItem
}

type MultiTicketRspItem struct {
	BranchId            int64     `json:"BranchId"            gorm:"column:branchid"`
	Branch              string    `json:"Branch"              gorm:"column:branch"`
	License             string    `json:"License"             gorm:"column:license"`
	Line                string    `json:"Line"                gorm:"column:line"`
	ReportAt            time.Time `json:"-"                   gorm:"column:reportat"`
	ReportAtUnix        int64     `json:"ReportAt"`
	AttendantStaffId    int64     `json:"AttendantStaffId"    gorm:"column:attendantstaffid"`    // 乘务员staffId 主数据司机表中的Id字段
	AttendantName       string    `json:"AttendantName"       gorm:"column:attendantname"`       // 乘务员姓名
	AttendantStaffIdStr string    `json:"AttendantStaffIdStr" gorm:"column:attendantstaffidstr"` // 乘务员工号
	Ticket100           int64     `json:"Ticket100"           gorm:"column:ticket100"`
	Ticket150           int64     `json:"Ticket150"           gorm:"column:ticket150"`
	Ticket200           int64     `json:"Ticket200"           gorm:"column:ticket200"`
	Ticket250           int64     `json:"Ticket250"           gorm:"column:ticket250"`
	Ticket300           int64     `json:"Ticket300"           gorm:"column:ticket300"`
	Ticket350           int64     `json:"Ticket350"           gorm:"column:ticket350"`
	Ticket400           int64     `json:"Ticket400"           gorm:"column:ticket400"`
	Ticket450           int64     `json:"Ticket450"           gorm:"column:ticket450"`
	Ticket500           int64     `json:"Ticket500"           gorm:"column:ticket500"`
	Ticket550           int64     `json:"Ticket550"           gorm:"column:ticket550"`
	Ticket600           int64     `json:"Ticket600"           gorm:"column:ticket600"`
	Ticket650           int64     `json:"Ticket650"           gorm:"column:ticket650"`
	Ticket700           int64     `json:"Ticket700"           gorm:"column:ticket700"`
	Ticket750           int64     `json:"Ticket750"           gorm:"column:ticket750"`
	Ticket800           int64     `json:"Ticket800"           gorm:"column:ticket800"`
	Ticket850           int64     `json:"Ticket850"           gorm:"column:ticket850"`
	Ticket900           int64     `json:"Ticket900"           gorm:"column:ticket900"`
	Ticket950           int64     `json:"Ticket950"           gorm:"column:ticket950"`
	Ticket1000          int64     `json:"Ticket1000"          gorm:"column:ticket1000"`
	TicketTotalCount    int64     `json:"TicketTotalCount"    gorm:"column:tickettotalcount"`  // 票张数小计
	TicketTotalAmount   int64     `json:"TicketTotalAmount"   gorm:"column:tickettotalamount"` // 票金额小计
}

// 多票制
func (t *TicketCountMoneys) MultiTicket(timeType TimeType, branchIds []int64, lineId int64, license string, startAt, endAt time.Time, paginator model.Paginator) (MultiTicketRsp, error) {
	var rsp MultiTicketRsp
	tx := model.DB().Model(&TicketCountMoneys{}).Select(
		"BranchId", "License", "Line", "ReportAt",
		"Ticket100", "Ticket150", "Ticket200", "Ticket250", "Ticket300", "Ticket350", "Ticket400", "Ticket450", "Ticket500",
		"Ticket550", "Ticket600", "Ticket650", "Ticket700", "Ticket750", "Ticket800", "Ticket850", "Ticket900", "Ticket950", "Ticket1000",
		"TicketTotalCount", "TicketTotalAmount").Where("Type = ? AND (MoneyFake > ? OR MoneyForeign > ? OR MoneyOther > ?)", TICKET, 0, 0, 0)

	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}

	if len(branchIds) > 0 {
		tx.Where("BranchId IN ?", branchIds)
	}

	if timeType == 1 {
		tx.Where("ReportAt >= ? AND ReportAt < ?", startAt, endAt)
	} else if timeType == 2 {
		tx.Where("IncomeAt >= ? AND IncomeAt < ?", startAt, endAt)
	}

	if lineId > 0 {
		tx.Where("LineId = ? ", lineId)
	}

	tx.Scan(&rsp.Data).Count(&rsp.TotalCount)

	// 计算总计张数金额
	for _, item := range rsp.Data {
		rsp.TicketCount += item.TicketTotalCount
		rsp.TicketAmount += item.TicketTotalAmount
	}
	rsp.Data = []MultiTicketRspItem{}

	tx.Order("IncomeAt,BranchId,Line").Limit(paginator.Limit).Offset(paginator.Offset).Scan(&rsp.Data)
	return rsp, tx.Error
}
