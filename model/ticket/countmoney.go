package ticket

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type TicketCountMoneysType int64

const (
	MONEY  TicketCountMoneysType = 1 // 点钞
	TICKET TicketCountMoneysType = 2 // 票款
)

type TicketStatus int64

const (
	TO_BE_REVIEWED TicketStatus = 1 // 待复核
	REVIEWED       TicketStatus = 2 // 已复核
)

type TimeType int64

const (
	TIME_INPUT_1  TimeType = 1 // 录入时间
	TIME_TICKET_2 TimeType = 2 // 票款时间
)

type TimeDimension int64

const (
	TIME_MONTHLY_1 TimeDimension = 1 // 按月统计
	TIME_DAILY_2   TimeDimension = 2 // 按天统计
)

type TicketEditStatus int64

const (
	NotChanged_1 TicketEditStatus = 1 // 未修改
	Changed_2    TicketEditStatus = 2 // 已修改
)

type TicketCountMoneys struct {
	model.PkId

	model.GroupCorporation
	model.CompanyCorporation
	model.BranchCorporation
	model.DepartmentCorporation
	model.FleetCorporation

	Type TicketCountMoneysType `json:"Type" gorm:"column:type;comment:记录类型 1点钞记录 2票款记录;type:smallint"` // -- 记录类型 1点钞记录 2票款记录

	License     string `json:"License"     gorm:"column:license;comment:车辆车牌号;type:varchar(50);uniqueIndex:tcm_license_inputdate_timeslot"` // -- 车辆车牌号
	VehicleCode string `json:"VehicleCode" gorm:"column:vehiclecode;comment:车辆编号;type:varchar(50)"`                                         // -- 车辆编号
	LineId      int64  `json:"LineId"      gorm:"column:lineid;comment:默认车属线路 也可以是选择的线路;type:integer"`                                      // -- 默认车属线路 也可以是选择的线路
	Line        string `json:"Line"        gorm:"column:line;comment:线路名;type:varchar(50)"`                                                 // -- 线路名

	DriverStaffId    int64  `json:"DriverStaffId"    gorm:"column:driverstaffid;comment:司机staffId 主数据司机表中的Id字段;type:integer"`          // -- 司机staffId 主数据司机表中的Id字段
	DriverName       string `json:"DriverName"       gorm:"column:drivername;comment:司机姓名;type:varchar(50)"`                           // -- 司机姓名
	DriverStaffIdStr string `json:"DriverStaffIdStr" gorm:"column:driverstaffidstr;comment:司机员工工号 主数据司机表中的staffId字段;type:varchar(50)"` // -- 司机员工工号 主数据司机表中的staffId字段

	AttendantStaffId    int64  `json:"AttendantStaffId"    gorm:"column:attendantstaffid;type:integer"`        // -- 乘务员staffId 主数据司机表中的Id字段 type=2才有值
	AttendantName       string `json:"AttendantName"       gorm:"column:attendantname;type:varchar(50)"`       // -- 乘务员姓名 type=2才有值
	AttendantStaffIdStr string `json:"AttendantStaffIdStr" gorm:"column:attendantstaffidstr;type:varchar(50)"` // -- 乘务员员工工号 主数据司机表中的staffId字段 type=2才有值

	ReviewerUserId     int64  `json:"ReviewerUserId" gorm:"column:revieweruserid;comment:复核员userId;type:integer"`
	ReviewerName       string `json:"ReviewerName"    gorm:"column:reviewername;comment:复核员姓名;type:varchar(50)"`             // -- 复核员姓名
	ReviewerStaffIdStr string `json:"ReviewerStaffIdStr"    gorm:"column:reviewerstaffidstr;comment:复核员工号;type:varchar(50)"` // -- 复核员工号

	InputUserId     int64  `json:"InputUserId" gorm:"column:inputuserid;comment:录入员userId;type:integer"`
	InputName       string `json:"InputName"    gorm:"column:inputname;comment:录入员姓名;type:varchar(50)"`             // -- 录入员姓名
	InputStaffIdStr string `json:"InputStaffIdStr"    gorm:"column:inputstaffidstr;comment:录入员工号;type:varchar(50)"` // -- 录入员工号

	InputDate  time.Time        `json:"InputDate"   gorm:"column:inputdate;comment:录入票款日期,和票款时间绑定 天 仅作为车辆唯一约束使用;type:date;uniqueIndex:tcm_license_inputdate_timeslot"`    // 录入票款日期,和票款时间绑定 天 仅作为车辆唯一约束使用
	ReportAt   time.Time        `json:"ReportAt" gorm:"column:reportat;comment:录入时间,录入日期 天;type:timestamptz;default:current_timestamp"`                                   // -- 录入时间,录入日期 天
	IncomeAt   time.Time        `json:"IncomeAt" gorm:"column:incomeat;comment:点钞/票款时间, 票款日期, 真实单据上的日期时间 天;type:timestamptz;default:current_timestamp"`                   // -- 点钞/票款时间, 票款日期, 真实单据上的日期时间 天
	TimeSlot   int64            `json:"TimeSlot" gorm:"column:timeslot;comment:点钞时间段 1全天 2上午 4下午 8夜班;type:smallint;default:1;uniqueIndex:tcm_license_inputdate_timeslot"` // -- 点钞时间段 1全天 2上午 4下午 8夜班
	Status     TicketStatus     `json:"Status"   gorm:"column:status;comment:状态 1待复核 2已复核;type:smallint;default:1"`                                                       // -- 状态 1待复核 2已复核
	DataStatus int64            `json:"DataStatus" gorm:"column:datastatus;comment:数据状态 0正常 1日期异常 2数据异常 (1+2所有异常);type:smallint;default:0"`                               // -- 数据状态 0正常 1日期异常 2数据异常 (1+2所有异常)
	EditStatus TicketEditStatus `json:"EditStatus" gorm:"column:editstatus;type:smallint;comment:修改状态 1未修改 2已修改;default:0"`                                               // 修改状态 1未修改 2已修改

	Paper10000       int64 `json:"Paper10000"       gorm:"column:paper10000;comment:100元纸币张数;type:integer;default:0"`         // -- 100元纸币张数
	Paper5000        int64 `json:"Paper5000"        gorm:"column:paper5000;comment:50元纸币张数;type:integer;default:0"`           // -- 50元纸币张数
	Paper2000        int64 `json:"Paper2000"        gorm:"column:paper2000;comment:20元纸币张数;type:integer;default:0"`           // -- 20元纸币张数
	Paper1000        int64 `json:"Paper1000"        gorm:"column:paper1000;comment:10元纸币张数;type:integer;default:0"`           // -- 10元纸币张数
	Paper500         int64 `json:"Paper500"         gorm:"column:paper500;comment:5元纸币张数;type:integer;default:0"`             // -- 5元纸币张数
	Paper200         int64 `json:"Paper200"         gorm:"column:paper200;comment:2元纸币张数;type:integer;default:0"`             // -- 2元纸币张数
	Paper100         int64 `json:"Paper100"         gorm:"column:paper100;comment:1元纸币张数;type:integer;default:0"`             // -- 1元纸币张数
	Paper50          int64 `json:"Paper50"          gorm:"column:paper50;comment:5角纸币张数;type:integer;default:0"`              // -- 5角纸币张数
	Paper20          int64 `json:"Paper20"          gorm:"column:paper20;comment:2角纸币张数;type:integer;default:0"`              // -- 2角纸币张数
	Paper10          int64 `json:"Paper10"          gorm:"column:paper10;comment:1角纸币张数;type:integer;default:0"`              // -- 1角纸币张数
	PaperTotalCount  int64 `json:"PaperTotalCount"  gorm:"column:papertotalcount;comment:纸币总张数;type:integer;default:0"`       // -- 纸币总张数
	PaperTotalAmount int64 `json:"PaperTotalAmount" gorm:"column:papertotalamount;comment:纸币总金额 单位 分;type:integer;default:0"` // -- 纸币总金额 单位 分

	Coin100         int64 `json:"Coin100"         gorm:"column:coin100;comment:1元硬币枚数;type:integer;default:0"`             // -- 1元硬币枚数
	Coin50          int64 `json:"Coin50"          gorm:"column:coin50;comment:5角硬币枚数;type:integer;default:0"`              // -- 5角硬币枚数
	Coin10          int64 `json:"Coin10"          gorm:"column:coin10;comment:1角硬币枚数;type:integer;default:0"`              // -- 1角硬币枚数
	CoinTotalCount  int64 `json:"CoinTotalCount"  gorm:"column:cointotalcount;comment:硬币总枚数;type:integer;default:0"`       // -- 硬币总枚数
	CoinTotalAmount int64 `json:"CoinTotalAmount" gorm:"column:cointotalamount;comment:硬币总金额 单位 分;type:integer;default:0"` // -- 硬币总金额 单位 分

	MoneyFake    int64 `json:"MoneyFake"    gorm:"column:moneyfake;comment:假币数;type:integer;default:0"`    // -- 假币数
	MoneyForeign int64 `json:"MoneyForeign" gorm:"column:moneyforeign;comment:外币数;type:integer;default:0"` // -- 外币数
	MoneyOther   int64 `json:"MoneyOther"   gorm:"column:moneyother;comment:其它币数;type:integer;default:0"`  // -- 其它币数

	AddMoney      int64 `json:"AddMoney" gorm:"column:addmoney;type:integer;comment:差额,增加实收总金额 单位分;default:0"`           // 差额,增加实收总金额 单位分
	SubtractMoney int64 `json:"SubtractMoney" gorm:"column:subtractmoney;type:integer;comment:差额,减少实收总金额 单位分;default:0"` // 差额,减少实收总金额 单位分

	TotalAmount      int64 `json:"TotalAmount" gorm:"column:totalamount;comment:实收总金额 单位 分  纸币总金额+硬币总金额-（残次币张数*（100分））;type:integer;default:0"` // --实收总金额 单位 分  纸币总金额+硬币总金额-（残次币张数*（100分））
	ReceivableAmount int64 `json:"ReceivableAmount" gorm:"column:receivableamount;comment:应收总金额 单位 分  纸币总金额+硬币总金额;type:integer;default:0"`      // --应收总金额 单位 分  纸币总金额+硬币总金额

	Ticket100         int64 `json:"Ticket100"         gorm:"column:ticket100;comment:1.0元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 1.0元票额张数  (type=2, 特有的字段)
	Ticket150         int64 `json:"Ticket150"         gorm:"column:ticket150;comment:1.5元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 1.5元票额张数  (type=2, 特有的字段)
	Ticket200         int64 `json:"Ticket200"         gorm:"column:ticket200;comment:2.0元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 2.0元票额张数  (type=2, 特有的字段)
	Ticket250         int64 `json:"Ticket250"         gorm:"column:ticket250;comment:2.5元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 2.5元票额张数  (type=2, 特有的字段)
	Ticket300         int64 `json:"Ticket300"         gorm:"column:ticket300;comment:3.0元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 3.0元票额张数  (type=2, 特有的字段)
	Ticket350         int64 `json:"Ticket350"         gorm:"column:ticket350;comment:3.5元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 3.5元票额张数  (type=2, 特有的字段)
	Ticket400         int64 `json:"Ticket400"         gorm:"column:ticket400;comment:4.0元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 4.0元票额张数  (type=2, 特有的字段)
	Ticket450         int64 `json:"Ticket450"         gorm:"column:ticket450;comment:4.5元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 4.5元票额张数  (type=2, 特有的字段)
	Ticket500         int64 `json:"Ticket500"         gorm:"column:ticket500;comment:5.0元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 5.0元票额张数  (type=2, 特有的字段)
	Ticket550         int64 `json:"Ticket550"         gorm:"column:ticket550;comment:5.5元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 5.5元票额张数  (type=2, 特有的字段)
	Ticket600         int64 `json:"Ticket600"         gorm:"column:ticket600;comment:6.0元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 6.0元票额张数  (type=2, 特有的字段)
	Ticket650         int64 `json:"Ticket650"         gorm:"column:ticket650;comment:6.5元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 6.5元票额张数  (type=2, 特有的字段)
	Ticket700         int64 `json:"Ticket700"         gorm:"column:ticket700;comment:7.0元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 7.0元票额张数  (type=2, 特有的字段)
	Ticket750         int64 `json:"Ticket750"         gorm:"column:ticket750;comment:7.5元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 7.5元票额张数  (type=2, 特有的字段)
	Ticket800         int64 `json:"Ticket800"         gorm:"column:ticket800;comment:8.0元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 8.0元票额张数  (type=2, 特有的字段)
	Ticket850         int64 `json:"Ticket850"         gorm:"column:ticket850;comment:8.5元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 8.5元票额张数  (type=2, 特有的字段)
	Ticket900         int64 `json:"Ticket900"         gorm:"column:ticket900;comment:9.0元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 9.0元票额张数  (type=2, 特有的字段)
	Ticket950         int64 `json:"Ticket950"         gorm:"column:ticket950;comment:9.5元票额张数  (type=2, 特有的字段);type:integer;default:0"`   // -- 9.5元票额张数  (type=2, 特有的字段)
	Ticket1000        int64 `json:"Ticket1000"        gorm:"column:ticket1000;comment:10.0元票额张数  (type=2, 特有的字段);type:integer;default:0"` // -- 10.0元票额张数  (type=2, 特有的字段)
	TicketTotalCount  int64 `json:"TicketTotalCount"  gorm:"column:tickettotalcount;comment:车票总张数;type:integer;default:0"`                // -- 车票总张数
	TicketTotalAmount int64 `json:"TicketTotalAmount" gorm:"column:tickettotalamount;comment:车票票面总金额 单位 分;type:integer;default:0"`        // -- 车票票面总金额 单位 分

	model.TimestampTz
}

func (t *TicketCountMoneys) BeforeCreate(tx *gorm.DB) (err error) {
	if t.Id == 0 {
		t.Id = model.Id()
	}
	return
}

// 检查并设置dataStatus
func (t *TicketCountMoneys) SetTicketDataStatus() {
	// 异常状态规则
	// 时间异常：日期大于1天 (文档没有描述为 >24h)
	// 数据异常：只要司机工号、车牌号、线路缺一个就算异常

	var status int64

	d1 := time.Date(t.ReportAt.Year(), t.ReportAt.Month(), t.ReportAt.Day(), 0, 0, 0, 0, time.UTC)
	d2 := time.Date(t.IncomeAt.Year(), t.IncomeAt.Month(), t.IncomeAt.Day(), 0, 0, 0, 0, time.UTC)
	sub := d1.Sub(d2).Hours()

	if sub > 24 || sub < -24 {
		status += 1
	}

	if t.DriverStaffId == 0 || t.License == "" || t.LineId == 0 {
		status += 2
	}

	t.DataStatus = status

}

// 某天 某分公司下 是否记录某车数据 根据票款日期判断
func (t *TicketCountMoneys) IsExistsLicenseOnDay(branchId int64, License string, startAt, endAt time.Time, timeslot int64) (bool, error) {
	//var rsp bool
	var count int64
	err := model.DB().Model(&TicketCountMoneys{}).Where("BranchId = ? AND License = ? AND IncomeAt >= ? AND IncomeAt < ? AND TimeSlot=?",
		branchId, License, startAt, endAt, timeslot).Count(&count).Error

	return count > 0, err // 找到数据
}

func (t *TicketCountMoneys) Add() error {
	return model.DB().Create(&t).Error
}

// 根据Id查找
func (t *TicketCountMoneys) GetById(id int64) TicketCountMoneys {
	var rsp TicketCountMoneys

	model.DB().Model(&TicketCountMoneys{}).Where("Id = ?", id).Take(&rsp)

	return rsp
}

// 点钞 编辑
func (t *TicketCountMoneys) Edit() error {
	return model.DB().Model(&TicketCountMoneys{}).Where("Id = ?", t.Id).Select(
		"License", "VehicleCode", "FleetId", "LineId", "Line", "DriverStaffId", "DriverName", "DriverStaffIdStr", "IncomeAt", "TimeSlot", "BranchId", "ReportAt", "InputDate", "DataStatus", "EditStatus",
		"Paper10000", "Paper5000", "Paper2000", "Paper1000", "Paper500",
		"Paper200", "Paper100", "Paper50", "Paper20", "Paper10",
		"PaperTotalCount", "PaperTotalAmount",
		"Coin100", "Coin50", "Coin10", "CoinTotalCount", "CoinTotalAmount",
		"MoneyFake", "MoneyForeign", "MoneyOther", "TotalAmount", "ReceivableAmount").Updates(t).Error
}

// EditDiff 编辑差额
func (t *TicketCountMoneys) EditDiff(tx *gorm.DB) error {
	return tx.Model(&TicketCountMoneys{}).Where("Id = ?", t.Id).Select("AddMoney", "SubtractMoney", "TotalAmount", "EditStatus").Updates(t).Error
}

type Total struct {
	ToBeReviewed     int64 // 待复核
	Reviewed         int64 // 已复核
	Abnormal         int64 // 异常
	Defective        int64 // 残次币数量
	ReceivableAmount int64 // 应收总金额
	TotalAmount      int64 // 实收总金额
}

func (t *TicketCountMoneys) List(
	licenses []string,
	corporationIds []int64, // 用户所属机构类型, 如果branchIds为空,将筛选所有复核的机构id
	branchIds, dataStatuses []int64,
	status TicketStatus,
	editStatus TicketEditStatus,
	timeType TimeType, timeSlot, lineId, fleetId, driverStaffId, reviewerUserId, inputUserId int64,
	Type TicketCountMoneysType, keyword string, startAt, endAt time.Time, order, scene string, paginator model.Paginator) ([]TicketCountMoneys, int64, Total) {
	var (
		totalCount int64
		rspData    []TicketCountMoneys
		tmp        []TicketCountMoneys
		total      Total
	)

	tx := model.DB().Model(&TicketCountMoneys{}).Where("Type = ?", Type)

	if len(branchIds) > 0 {
		tx.Where("BranchId IN (?)", branchIds)
	} else {
		tx.Where("GroupId IN (?) OR CompanyId IN (?) OR BranchId IN (?) OR DepartmentId IN (?) OR FleetId IN (?)",
			corporationIds, corporationIds, corporationIds, corporationIds, corporationIds)
	}

	if len(licenses) > 0 {
		tx.Where("License IN (?)", licenses)
	}

	if keyword != "" {
		tx.Where("License LIKE ? OR Line LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	if timeType == TIME_INPUT_1 {
		tx.Where("ReportAt >= ? AND ReportAt < ?", startAt, endAt)
	} else if timeType == TIME_TICKET_2 {
		tx.Where("IncomeAt >= ? AND IncomeAt < ?", startAt, endAt)
	}

	if timeSlot > 0 {
		tx.Where("TimeSlot = ?", timeSlot)
	}

	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	if scene == "bankCheck" && len(config.Config.TicketBankCheckExcludeLine) > 0 {
		tx.Where("Line NOT IN ?", config.Config.TicketBankCheckExcludeLine)
	}

	if fleetId > 0 {
		tx.Where("FleetId = ?", fleetId)
	}

	if status > 0 {
		tx.Where("Status = ?", status)
	}

	if editStatus > 0 {
		tx.Where("EditStatus = ?", editStatus)
	}

	if len(dataStatuses) > 0 {
		tx.Where("DataStatus IN ?", dataStatuses)
	}

	if driverStaffId > 0 {
		tx.Where("DriverStaffId = ?", driverStaffId)
	}

	if reviewerUserId > 0 {
		tx.Where("ReviewerUserId = ?", reviewerUserId)
	}

	if inputUserId > 0 {
		tx.Where("InputUserId = ?", inputUserId)
	}

	tx.Scan(&tmp).Count(&totalCount)

	if order == "" {
		tx.Order("Id DESC")
	} else {
		tx.Order(fmt.Sprintf("TotalAmount %s", order))
	}

	tx.Limit(paginator.Limit).Offset(paginator.Offset).Scan(&rspData)

	for _, moneys := range tmp {
		if moneys.Status == TO_BE_REVIEWED {
			total.ToBeReviewed += 1
		} else if moneys.Status == REVIEWED {
			total.Reviewed += 1
		}

		if moneys.DataStatus > 0 {
			total.Abnormal += 1
		}

		total.ReceivableAmount += moneys.ReceivableAmount
		total.TotalAmount += moneys.TotalAmount
		total.Defective += moneys.MoneyFake + moneys.MoneyForeign + moneys.MoneyOther
	}

	return rspData, totalCount, total

}

// 筛选后全部复核
func (t *TicketCountMoneys) AllReview(
	licenses []string,
	branchIds []int64,
	status TicketStatus,
	timeType TimeType, lineId, fleetId, dataStatus, driverStaffId, reviewerUserId, inputUserId int64,
	Type TicketCountMoneysType, startAt, endAt time.Time) error {
	tx := model.DB().Model(&TicketCountMoneys{}).Select("Status", "ReviewerUserId", "ReviewerName", "ReviewerStaffIdStr")

	if timeType == TIME_INPUT_1 {
		tx.Where("ReportAt >= ? AND ReportAt < ?", startAt, endAt)
	} else if timeType == TIME_TICKET_2 {
		tx.Where("IncomeAt >= ? AND IncomeAt < ?", startAt, endAt)
	}

	if len(licenses) > 0 {
		tx.Where("License IN (?)", licenses)
	}

	if len(branchIds) > 0 {
		tx.Where("BranchId IN (?)", branchIds)
	}

	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	if fleetId > 0 {
		tx.Where("FleetId = ?", fleetId)
	}

	if status > 0 {
		tx.Where("Status = ?", status)
	}

	if dataStatus > -1 {
		tx.Where("DataStatus = ?", dataStatus)
	}

	if driverStaffId > 0 {
		tx.Where("DriverStaffId = ?", driverStaffId)
	}

	if reviewerUserId > 0 {
		tx.Where("ReviewerUserId = ?", reviewerUserId)
	}

	if inputUserId > 0 {
		tx.Where("InputUserId = ?", inputUserId)
	}

	return tx.Updates(t).Error
}

// 批量复核
func (t *TicketCountMoneys) BatchReview(ids []int64) error {
	return model.DB().Model(&TicketCountMoneys{}).Where("Id IN (?)", ids).Select(
		"Status", "ReviewerUserId", "ReviewerName", "ReviewerStaffIdStr").Updates(t).Error
}

// 批量复核
func (t *TicketCountMoneys) Delete(id int64) error {
	return model.DB().Where("Id = ?", id).Delete(&TicketCountMoneys{}).Error
}

type GetByBranchCountRsp struct {
	TotalAmount   int64 `json:"TotalAmount" gorm:"column:totalamount"`     //
	AddMoney      int64 `json:"AddMoney" gorm:"column:addmoney"`           //
	SubtractMoney int64 `json:"SubtractMoney" gorm:"column:subtractmoney"` //
}

// GetByBranchCount 获取分公司点钞实收金额
func (t *TicketCountMoneys) GetByBranchCount(branchId int64, incomeAt time.Time) (GetByBranchCountRsp, error) {
	var rsp GetByBranchCountRsp
	err := model.DB().Model(&TicketCountMoneys{}).Select("sum(TotalAmount) AS TotalAmount, sum(AddMoney) AS AddMoney, sum(SubtractMoney) AS SubtractMoney").
		Where("BranchId=? AND Type=? AND to_char(IncomeAt, 'yyyy-MM-dd')=?", branchId, MONEY, incomeAt.Format("2006-01-02")).Scan(&rsp).Error

	return rsp, err
}

type DontHaveFeetRecord struct {
	Id       int64  `json:"Id" gorm:"column:id"`
	LineId   int64  `json:"LineId" gorm:"column:lineid"`
	License  string `json:"License" gorm:"column:license"`
	GroupId  int64  `json:"GroupId" gorm:"column:groupid"`
	BranchId int64  `json:"BranchId" gorm:"column:branchid"`
}

func (t *TicketCountMoneys) GetDontHasFleetRecord(limit int) []DontHaveFeetRecord {
	var records []DontHaveFeetRecord

	model.DB().Model(&TicketCountMoneys{}).Where("FleetId = ?", 0).Limit(limit).Scan(&records)

	return records
}

func (t *TicketCountMoneys) UpdateFleetId(id, fleetId int64) error {
	return model.DB().Model(&TicketCountMoneys{}).Where("Id = ?", id).UpdateColumn("FleetId", fleetId).Error
}

type LineTicketMoneyItem struct {
	CorporationId int64 `json:"CorporationId" gorm:"column:corporationid"`
	LineId        int64 `json:"LineId" gorm:"column:lineid"`
	TotalMoney    int64 `json:"TotalMoney" gorm:"column:totalmoney"`
}

// LineTicketMoney 线路机构营收
func (t *TicketCountMoneys) LineTicketMoney(lineId int64, startAt, endAt time.Time) []LineTicketMoneyItem {
	var items []LineTicketMoneyItem
	model.DB().Model(&TicketCountMoneys{}).Select("SUM(TotalAmount) as TotalMoney", "FleetId as CorporationId", "LineId").
		Where("LineId = ? AND IncomeAt >= ? AND IncomeAt <= ? AND FleetId >0", lineId, startAt.Format(model.DateFormat), endAt.Format(model.DateFormat)).
		Group("CorporationId,LineId").
		Scan(&items)

	return items

}
