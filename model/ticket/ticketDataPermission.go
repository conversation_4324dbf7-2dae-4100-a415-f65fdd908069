package ticket

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
	"time"
)

type TicketDataPermissionStatus int64

const (
	DOING_TDPS_1 TicketDataPermissionStatus = 1 // 进行中
	DONE_TDPS_2  TicketDataPermissionStatus = 2 // 已失效
)

type TicketDataPermission struct {
	model.PkId
	GroupId int64 `json:"GroupId" gorm:"column:groupid;comment:集团id;type:bigint;unique"` // 集团id

	IsHasPermission int64 `json:"IsHasPermission" gorm:"column:ishaspermission;type:smallint;comment:是否拥有票务数据更改权限;"` // 是否拥有票务数据更改权限 1是 2否

	model.Timestamp
}

func (tdp *TicketDataPermission) GetHasPermission(groupId int64) int64 {
	var permission TicketDataPermission
	model.DB().Model(&TicketDataPermission{}).Where("GroupId = ?", groupId).Find(&permission)
	return permission.IsHasPermission
}

func (tdp *TicketDataPermission) UpdatePermission(groupId int64, val int) error {
	return model.DB().Model(&TicketDataPermission{}).Where("GroupId = ?", groupId).Update("IsHasPermission", val).Error
}

type TicketDataPermissionRecord struct {
	model.PkId
	model.Corporations

	Status   TicketDataPermissionStatus `json:"Status" gorm:"column:status;type:smallint;comment:状态;"`             // 状态 1进行中 2已失效
	StartAt  model.LocalTime            `json:"StartAt" gorm:"column:startat;type:timestamp;comment:有效时间开始时间;"`    // 有效时间开始时间
	EndAt    model.LocalTime            `json:"EndAt" gorm:"column:endat;type:timestamp;comment:有效时间结束时间;"`        // 有效时间结束时间
	IsCancel int64                      `json:"IsCancel" gorm:"column:iscancel;type:smallint;comment:是否作废 1是 2否;"` // 是否作废 1是 2否
	model.OpUser
	model.Timestamp
}

func (tdpr *TicketDataPermissionRecord) BeforeCreate(tx *gorm.DB) error {
	if tdpr.Id == 0 {
		tdpr.Id = model.Id()
	}
	return nil
}

func (tdpr *TicketDataPermissionRecord) Create() error {
	return model.DB().Create(tdpr).Error
}

func (tdpr *TicketDataPermissionRecord) GetBy(status TicketDataPermissionStatus, createdStartAt, createdEndAt time.Time, paginator model.Paginator) ([]TicketDataPermissionRecord, int64, error) {
	var (
		rsp        []TicketDataPermissionRecord
		totalCount int64
	)
	tx := model.DB().Model(&TicketDataPermissionRecord{})

	if status > 0 {
		tx.Where("Status = ?", status)
	}
	if !createdStartAt.IsZero() {
		tx.Where("CreatedAt >= ?", createdStartAt)
	}
	if !createdEndAt.IsZero() {
		tx.Where("CreatedAt < ?", createdEndAt)
	}

	err := tx.Count(&totalCount).Order("CreatedAt DESC").Limit(paginator.Limit).Offset(paginator.Offset).Find(&rsp).Error

	return rsp, totalCount, err
}

func (tdpr *TicketDataPermissionRecord) GetDoing() ([]TicketDataPermissionRecord, int64, error) {
	var (
		rsp        []TicketDataPermissionRecord
		totalCount int64
	)
	tx := model.DB().Model(&TicketDataPermissionRecord{}).Where("Status=?", DOING_TDPS_1)

	err := tx.Count(&totalCount).Order("CreatedAt DESC").Scan(&rsp).Error

	return rsp, totalCount, err
}

// BatchToDone 批量设置为已失效
func (tdp *TicketDataPermissionRecord) BatchToDone(ids []int64) error {
	return model.DB().Model(&TicketDataPermissionRecord{}).Where("Id IN ?", ids).Update("Status", DONE_TDPS_2).Error
}

// UpdateIsCancelTrue 作废，同时更改状态Status为已失效
func (tdpr *TicketDataPermissionRecord) UpdateIsCancelTrue(id int64) error {

	return model.DB().Model(&TicketDataPermissionRecord{}).Where("Id=?", id).Updates(map[string]interface{}{"IsCancel": util.StatusForTrue, "Status": DONE_TDPS_2}).Error
}
