package ticket

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type CharteredBusIncome struct {
	model.PkId
	model.Corporations
	ReportAt    model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:月份"`
	Price       int64           `json:"Price" gorm:"column:price;type:integer;comment:包车单价"`
	TotalMoney  int64           `json:"TotalMoney" gorm:"column:totalmoney;type:integer;comment:包车总费用"`
	TotalPeople int64           `json:"TotalPeople" gorm:"column:totalpeople;type:integer;comment:人数"`
	model.OpUser
	model.Timestamp

	CorporationId   int64  `json:"CorporationId" gorm:"-"`
	CorporationName string `json:"CorporationName" gorm:"-"`
}

func (cbi *CharteredBusIncome) BeforeCreate(db *gorm.DB) error {
	cbi.Id = model.Id()
	return nil
}

func (cbi *CharteredBusIncome) Create() error {
	return model.DB().Create(&cbi).Error
}

func (cbi *CharteredBusIncome) Update() error {
	return model.DB().Select("*").Updates(&cbi).Error
}

func (cbi *CharteredBusIncome) FirstBy(id int64) CharteredBusIncome {
	var income CharteredBusIncome
	model.DB().Model(&CharteredBusIncome{}).Where("id = ?", id).First(&income)
	return income
}

func (cbi *CharteredBusIncome) GetBy(corporationId int64, reportAt time.Time, paginator model.Paginator) ([]CharteredBusIncome, int64) {
	var incomes []CharteredBusIncome
	tx := model.DB().Model(&CharteredBusIncome{})
	if corporationId > 0 {
		tx.Scopes(model.WhereCorporation(corporationId))
	}
	if !reportAt.IsZero() {
		tx.Where("ReportAt = ?", reportAt.Format("2006-01")+"-01")
	}

	var count int64
	tx.Count(&count)

	tx.Order("ReportAt DESC").Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&incomes)
	return incomes, count
}

type CharteredBusIncomeSum struct {
	CorporationId int64 `json:"CorporationId" gorm:"column:corporationid"`
	TotalMoney    int64 `json:"TotalMoney" gorm:"column:totalmoney"`
	TotalPeople   int64 `json:"TotalPeople" gorm:"column:totalpeople"`
}

func (cbi *CharteredBusIncome) GetBySum(reportAt time.Time) []CharteredBusIncomeSum {
	var incomes []CharteredBusIncomeSum
	model.DB().Model(&CharteredBusIncome{}).Select("FleetId as CorporationId", "SUM(TotalMoney) AS TotalMoney", "SUM(TotalPeople) AS TotalPeople").
		Where("ReportAt = ?", reportAt.Format(model.DateFormat)).
		Group("corporationid").
		Scan(&incomes)
	return incomes
}

func (cbi *CharteredBusIncome) Delete() error {
	return model.DB().Delete(&cbi).Error
}
