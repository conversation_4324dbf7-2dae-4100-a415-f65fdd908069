package ticket

import (
	"app/org/scs/erpv2/api/model"
)

// 点票编辑
func (t *TicketCountMoneys) EditTicket() error {
	return model.DB().Model(&TicketCountMoneys{}).Where("Id = ?", t.Id).Select(
		"License", "VehicleCode", "FleetId", "LineId", "Line", "DriverStaffId", "DriverName", "DriverStaffIdStr", "IncomeAt", "TimeSlot", "BranchId", "ReportAt", "InputDate", "DataStatus", "EditStatus",
		"Paper10000", "Paper5000", "Paper2000", "Paper1000", "Paper500", "Paper200", "Paper100", "Paper50", "Paper20", "Paper10",
		"PaperTotalCount", "PaperTotalAmount",
		"Coin100", "Coin50", "Coin10", "CoinTotalCount", "CoinTotalAmount", "MoneyFake", "MoneyForeign", "MoneyOther", "TotalAmount", "ReceivableAmount",
		"TicketAmount100", "TicketAmount150", "TicketAmount200", "TicketAmount250", "TicketAmount300", "TicketAmount350", "TicketAmount400",
		"TicketAmount450", "TicketAmount500", "TicketAmount550", "TicketAmount600", "TicketAmount650", "TicketAmount700", "TicketAmount750",
		"TicketAmount800", "TicketAmount850", "TicketAmount900", "TicketAmount950", "TicketAmount1000",
	).Updates(t).Error
}
