package ticket

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type CorporationLineIncomeReport struct {
	model.PkId
	model.Corporations
	CorporationId        int64           `json:"CorporationId" gorm:"column:corporationid;type:bigint;comment:机构ID;uniqueIndex:income_corporationId_lineId_reportAt_unique_index"`
	CorporationName      string          `json:"CorporationName" gorm:"column:corporationname;type:varchar(100);comment:机构"`
	LineId               int64           `json:"LineId" gorm:"column:lineid;type:integer;comment:线路ID;uniqueIndex:income_corporationId_lineId_reportAt_unique_index"`
	LineName             string          `json:"LineName" gorm:"column:linename;type:varchar(100);comment:线路"`
	ReportAt             model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:报表月份;uniqueIndex:income_corporationId_lineId_reportAt_unique_index"`
	StartAt              model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始日期"`
	EndAt                model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束日期"`
	Price                int64           `json:"Price" gorm:"column:price;type:integer;comment:补贴单价 单位：分"`
	TotalCircle          int64           `json:"TotalCircle" gorm:"column:totalcircle;type:integer;comment:圈次/班次 单位：圈次*10"`
	TotalMileage         int64           `json:"TotalMileage" gorm:"column:totalmileage;type:integer;comment:里程 单位：米"`
	CashPaymentPeople    int64           `json:"CashPaymentPeople" gorm:"column:cashpaymentpeople;type:integer;comment:票款人次"`
	MobilePaymentPeople  int64           `json:"MobilePaymentPeople" gorm:"column:mobilepaymentpeople;type:integer;comment:移动支付人次"`
	NormalIcPeople       int64           `json:"NormalIcPeople" gorm:"column:normalicpeople;type:integer;comment:普通IC卡人次"`
	HalfOlderIcPeople    int64           `json:"HalfOlderIcPeople" gorm:"column:halfoldericpeople;type:integer;comment:半价老年卡人次"`
	FreeOlderIcPeople    int64           `json:"FreeOlderIcPeople" gorm:"column:freeoldericpeople;type:integer;comment:免费老年卡人次"`
	AdultSpecialIcPeople int64           `json:"AdultSpecialIcPeople" gorm:"column:adultspecialicpeople;type:integer;comment:承认优待卡人次"`
	TotalPeople          int64           `json:"TotalPeople" gorm:"column:totalpeople;type:integer;comment:人次合计"`
	FreeChangePeople     int64           `json:"FreeChangePeople" gorm:"column:freechangepeople;type:integer;comment:免费换乘人次"`
	CashPaymentMoney     int64           `json:"CashPaymentMoney" gorm:"column:cashpaymentmoney;type:integer;comment:票款营收金额  单位：分"`
	MobilePaymentMoney   int64           `json:"MobilePaymentMoney" gorm:"column:mobilepaymentmoney;type:integer;comment:移动支付金额 单位：分"`
	NormalIcMoney        int64           `json:"NormalIcMoney" gorm:"column:normalicmoney;type:integer;comment:普通IC卡收入 单位：分"`
	HalfOlderIcMoney     int64           `json:"HalfOlderIcMoney" gorm:"column:halfoldericmoney;type:integer;comment:半价老年卡收入 单位：分"`
	TotalOperationMoney  int64           `json:"TotalOperationMoney" gorm:"column:totaloperationmoney;type:integer;comment:营收总收入合计 单位：分"`
	NormalIcAllowance    int64           `json:"NormalIcAllowance" gorm:"column:normalicallowance;type:integer;comment:普通IC卡补贴金额 单位：分"`
	HalfOlderIcAllowance int64           `json:"HalfOlderIcAllowance" gorm:"column:halfoldericallowance;type:integer;comment:半价老年卡补贴金额 单位：分"`
	FreeOlderIcAllowance int64           `json:"FreeOlderIcAllowance" gorm:"column:freeoldericallowance;type:integer;comment:免费老年卡补贴金额 单位：分"`
	FreeChangeAllowance  int64           `json:"FreeChangeAllowance" gorm:"column:freechangeallowance;type:integer;comment:免费换乘补贴金额 单位：分"`
	TotalAllowance       int64           `json:"TotalAllowance" gorm:"column:totalallowance;type:integer;comment:补贴金额合计 单位：分"`
	TotalMoney           int64           `json:"TotalMoney" gorm:"column:totalmoney;type:integer;comment:总计金额 单位：分"`
	model.Timestamp
	model.OpUser
}

func (c *CorporationLineIncomeReport) TableName() string {
	return "corporation_line_income_reports"
}
func (c *CorporationLineIncomeReport) BeforeCreate(db *gorm.DB) error {
	c.Id = model.Id()
	return nil
}

func (c *CorporationLineIncomeReport) Create() error {
	return model.DB().Create(&c).Error
}

func (c *CorporationLineIncomeReport) Update() error {
	return model.DB().Updates(&c).Error
}

func (c *CorporationLineIncomeReport) DeleteBy(reportAt time.Time) error {
	return model.DB().Where("ReportAt = ?", reportAt.Format(model.DateFormat)).Delete(&CorporationLineIncomeReport{}).Error
}

func (c *CorporationLineIncomeReport) GetBy(corporationIds, lineIds []int64, reportAt time.Time) []CorporationLineIncomeReport {
	var reports []CorporationLineIncomeReport
	tx := model.DB().Model(&CorporationLineIncomeReport{}).Where("ReportAt = ?", reportAt.Format(model.DateFormat))

	if corporationIds != nil && len(corporationIds) > 0 {
		tx.Scopes(model.WhereCorporations(corporationIds))
	}

	if lineIds != nil && len(lineIds) > 0 {
		tx = tx.Where("LineId IN (?)", lineIds)
	}

	tx.Find(&reports)

	return reports
}

func (c *CorporationLineIncomeReport) Find(corporationId, LineId int64, reportAt time.Time) (data CorporationLineIncomeReport) {
	tx := model.DB().Model(&CorporationLineIncomeReport{}).Where("ReportAt = ?", reportAt.Format(model.DateFormat)).Where("LineId = ?", LineId)
	if corporationId != 0 {
		tx.Scopes(model.WhereCorporation(corporationId))
	}
	tx.Find(&data)
	return
}
