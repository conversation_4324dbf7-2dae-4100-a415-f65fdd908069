package learnModel

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

// LearnCourse 学习课程
type LearnCourse struct {
	model.PkId
	model.Timestamp
	Title                string                `json:"Title" gorm:"column:title;type:varchar;default:;comment:题组标题" validate:"required"`
	CategoryId           int64                 `json:"CategoryId" gorm:"column:categoryid;type:bigint;default:;comment:分类id"  validate:"required"`
	CourseType           int64                 `json:"CourseType" gorm:"column:coursetype;type:smallint;default:1;comment:1必修 2选修"  validate:"required"`
	CoverImage           model.JSON            `json:"CoverImage" gorm:"column:coverimage;type:json;default:;comment:封面图"`
	Description          string                `json:"Description" gorm:"column:description;type:varchar;default:;comment:描述说明"`
	ExpirationDate       model.LocalTime       `json:"ExpirationDate" gorm:"column:expirationdate;type:timestamp;default:;comment:截止时间" validate:"required"`
	NeedExamination      int64                 `json:"NeedExamination" gorm:"column:needexamination;type:smallint;default:1;comment:1需要考试 2不需要"  validate:"required"`
	TestPaperId          int64                 `json:"TestPaperId" gorm:"column:testpaperid;type:bigint;default:;comment:试卷id"`
	AllowMakeupExam      int64                 `json:"AllowMakeupExam" gorm:"column:allowmakeupexam;type:smallint;default:1;comment:1允许补考 2不允许"  validate:"required"`
	MakeupExamCount      int64                 `json:"MakeupExamCount" gorm:"column:makeupexamcount;type:integer;default:;comment:补考次数"`
	EducationalResources []EducationalResource `json:"EducationalResources" gorm:"many2many:learn_course_resources;constraint:OnDelete:CASCADE;"` // 学习资料
	//
	TestPaperTitle string `json:"TestPaperTitle" gorm:"column:testpapertitle;->"`
}

func (m *LearnCourse) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *LearnCourse) Create() error {
	return model.DB().Omit("EducationalResources.*").Create(&m).Error
}

func (m *LearnCourse) Update() error {
	tx := model.DB().Begin()
	err := tx.Omit("EducationalResources.*").Updates(&m).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	err = tx.Model(&m).Omit("EducationalResources.*").Association("EducationalResources").Replace(m.EducationalResources)
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}

func (m *LearnCourse) Delete(id int64) error {
	return model.DB().Delete(&LearnCourse{}, id).Error
}

// IsModified 是否可以修改或者删除
func (m *LearnCourse) IsModified() bool {
	var task LearnExamTask
	_ = task.FindByCourseId(m.Id)
	return task.Id == 0
}

func (m *LearnCourse) List(title, startTime, endTime string, courseType, categoryID int64, paginator model.Paginator) (data []LearnCourse, totalCount int64, err error) {
	db := model.DB().Model(&LearnCourse{}).Select("learn_courses.*", "test_papers.Title AS TestPaperTitle").Joins("LEFT JOIN test_papers ON learn_courses.TestPaperId = test_papers.Id")
	if title != "" {
		db = db.Where("learn_courses.Title LIKE ?", "%"+title+"%")
	}
	if courseType != 0 {
		db = db.Where("learn_courses.CourseType = ?", courseType)
	}
	if categoryID != 0 {
		db = db.Where("learn_courses.CategoryId = ?", categoryID)
	}
	if startTime != "" {
		db = db.Where("learn_courses.ExpirationDate >= ?", startTime)
	}
	if endTime != "" {
		db = db.Where("learn_courses.ExpirationDate <= ?", endTime)
	}
	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Offset(paginator.Offset).Limit(paginator.Limit)
	}
	err = db.Order("learn_courses.CreatedAt DESC").Preload("EducationalResources").Find(&data).Error
	return
}

func (m *LearnCourse) FindById(id int64) error {
	return model.DB().First(&m, id).Error
}

func (m *LearnCourse) IsExpiration() bool {
	return time.Now().Unix() >= time.Time(m.ExpirationDate).Unix()
}
