package learnModel

import (
	"app/org/scs/erpv2/api/model"
	"errors"
	"fmt"
	"gorm.io/gorm"
)

type Topic struct {
	model.PkId
	model.Timestamp
	Code         string        `json:"Code" gorm:"column:code;type:varchar;default:;comment:编号;uniqueIndex:topic_code" `
	CodeNumber   int64         `json:"CodeNumber" gorm:"column:codenumber;type:integer;default:;comment:编号数字"`
	TopicType    int64         `json:"TopicType" gorm:"column:topictype;type:integer;default:1;comment:1单选 2多选 3判断"  validate:"required"`
	Content      string        `json:"Content" gorm:"column:content;type:varchar;default:;comment:题目内容"  validate:"required"`
	CategoryId   int64         `json:"CategoryId" gorm:"column:categoryid;type:bigint;default:;comment:分类id"  validate:"required"`
	Analysis     string        `json:"Analysis" gorm:"column:analysis;type:varchar;default:;comment:题目解析"  validate:"required"`
	AnswerRandom int64         `json:"AnswerRandom" gorm:"column:answerrandom;type:integer;default:;comment:答案顺序是否随机 1随机 2不随机"  validate:"required"`
	Answers      []TopicAnswer `gorm:"foreignKey:TopicID" json:"Answers"`
	//
	Category     string `json:"Category" gorm:"column:category;->"`
	ChooseAnswer string `json:"ChooseAnswer" gorm:"-"` // 选择的答案
	IsRight      int64  `json:"IsRight" gorm:"-"`      // 是否选择正确
}

func (m *Topic) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	var maxCodeNumber int64
	model.DB().Model(&Topic{}).Select("COALESCE(MAX(CodeNumber), 0)").Where("TopicType = ?", m.TopicType).Scan(&maxCodeNumber)
	m.CodeNumber = maxCodeNumber + 1
	m.Code = m.GetCode()
	return nil
}

func (m *Topic) GetCode() string {
	prefix := ""
	if m.TopicType == 1 {
		prefix = "danx"
	} else if m.TopicType == 2 {
		prefix = "duox"
	} else {
		prefix = "pand"
	}
	return fmt.Sprintf("%s_%06d", prefix, m.CodeNumber)
}

func (m *Topic) Create() error {
	return model.DB().Create(&m).Error
}

func (m *Topic) TxCreate(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *Topic) Update() error {
	tx := model.DB().Begin()
	err := tx.Where("TopicID = ?", m.Id).Delete(&TopicAnswer{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	err = tx.Updates(&m).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil

}

func (m *Topic) Delete(ids []int64) error {
	tx := model.DB().Begin()
	err := tx.Delete(&Topic{}, ids).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	err = tx.Where("TopicID in ?", ids).Delete(&TopicAnswer{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}

// IsLegalAnswer 是否是合法答案
func (m *Topic) IsLegalAnswer() error {
	if m.Answers == nil || len(m.Answers) == 0 {
		return errors.New("请配置答案列表")
	}
	haveRightAnswer := false
	for _, answer := range m.Answers {
		if answer.IsRight == 1 {
			haveRightAnswer = true
		}
	}
	if !haveRightAnswer {
		return errors.New("请至少配置一个正确答案")
	}
	return nil
}

func (m *Topic) List(code string, topicType, categoryID int64, createAt string, paginator model.Paginator) (data []Topic, totalCount int64, err error) {
	db := model.DB().Model(&Topic{}).Select("topics.*", "topic_categories.Category").Joins("LEFT JOIN topic_categories ON topics.CategoryId = topic_categories.Id")
	if code != "" {
		db = db.Where("Code LIKE ?", "%"+code+"%")
	}
	if topicType != 0 {
		db = db.Where("TopicType = ?", topicType)
	}
	if categoryID != 0 {
		db = db.Where("CategoryId = ?", categoryID)
	}
	if createAt != "" {
		db = db.Where("TO_CHAR(CreatedAt, 'YYYY-MM-DD') = ?", createAt)
	}
	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Offset(paginator.Offset).Limit(paginator.Limit)
	}
	err = db.Preload("Answers").Order("CreatedAt DESC").Find(&data).Error
	return
}

type TopicAnswer struct {
	model.PkId
	model.Timestamp
	TopicID        int64      `json:"TopicID" gorm:"index;not null;column:topicid;type:bigint;default:;comment: 题目id"`
	AnswerType     int64      `json:"AnswerType" gorm:"column:answertype;type:integer;default:;comment:答案类型 1文字 2图片"`
	TextContent    string     `json:"TextContent" gorm:"column:textcontent;type:varchar;default:;comment:答案文字描述"`
	PictureContent model.JSON `json:"PictureContent" gorm:"column:picturecontent;type:json;default:;comment:答案图片描述"`
	IsRight        int64      `json:"IsRight" gorm:"column:isright;type:integer;default:;comment:是否正确 1正确 2不正确"`
	//
	Checked bool `json:"Checked" gorm:"-"` // 是否选择在交卷的时候
}

func (m *TopicAnswer) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}
func (m *TopicAnswer) Create() error {
	return model.DB().Create(&m).Error
}

func (m *TopicAnswer) TxCreate(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *TopicAnswer) Update() error {
	return model.DB().Updates(&m).Error
}

func (m *TopicAnswer) Delete(id int64) error {
	return model.DB().Delete(&TopicAnswer{}, id).Error
}

func (m *TopicAnswer) FindById(id int64) error {
	return model.DB().First(&m, id).Error
}

type TopicGroup struct {
	model.PkId
	model.Timestamp
	Title      string  `json:"Title" gorm:"column:title;type:varchar;default:;comment:题组标题" validate:"required"`
	CategoryId int64   `json:"CategoryId" gorm:"column:categoryid;type:bigint;default:;comment:分类id"  validate:"required"`
	Topics     []Topic `json:"Topics" gorm:"many2many:topic_topic_groups;constraint:OnDelete:CASCADE;"`
	TopicType  int64   `json:"TopicType" gorm:"column:topictype;type:integer;default:1;comment:1单选 2多选 3判断"  validate:"required"`
	//
	Category string `json:"Category" gorm:"-"`
}

func (m *TopicGroup) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *TopicGroup) Create() error {
	return model.DB().Omit("Topics.*").Create(&m).Error
}

func (m *TopicGroup) Update() error {
	tx := model.DB().Begin()
	newTopics := m.Topics
	err := tx.Omit("Topics.*").Updates(&m).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	err = tx.Model(&m).Omit("Topics.*").Association("Topics").Replace(newTopics)
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}

func (m *TopicGroup) Delete(id int64) error {
	return model.DB().Select("Topics").Delete(&TopicGroup{}, id).Error
}

func (m *TopicGroup) List(title string, categoryId, topicType int64, paginator model.Paginator) (data []TopicGroup, totalCount int64, err error) {
	db := model.DB().Model(&TopicGroup{})
	if title != "" {
		db = db.Where("Title LIKE ?", "%"+title+"%")
	}
	if categoryId != 0 {
		db = db.Where("CategoryId = ?", categoryId)
	}
	if topicType != 0 {
		db = db.Where("TopicType = ?", topicType)
	}
	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Offset(paginator.Offset).Limit(paginator.Limit)
	}
	err = db.Order("CreatedAt DESC").Preload("Topics", func(db *gorm.DB) *gorm.DB {
		return db.Preload("Answers")
	}).Find(&data).Error
	return
}

func (m *TopicGroup) List2(qs *model.Qs, paginator model.Paginator) (data []TopicGroup, totalCount int64, err error) {
	db := qs.Format().Model(&TopicGroup{})
	db.Count(&totalCount)
	err = db.Scopes(model.PaginationScope(paginator)).Order("CreatedAt DESC").Preload("Topics", func(db *gorm.DB) *gorm.DB {
		return db.Preload("Answers")
	}).Find(&data).Error
	return
}

type TopicForMini struct {
	Id           string               `json:"Id"`
	Code         string               `json:"Code" `
	CodeNumber   int64                `json:"CodeNumber"`
	TopicType    int64                `json:"TopicType"`
	Content      string               `json:"Content"`
	Analysis     string               `json:"Analysis"`
	AnswerRandom int64                `json:"AnswerRandom"`
	Answers      []TopicAnswerForMini `json:"Answers"`
}

type TopicAnswerForMini struct {
	Id             string     `json:"Id"`
	TopicID        string     `json:"TopicID" `
	AnswerType     int64      `json:"AnswerType"`
	TextContent    string     `json:"TextContent"`
	PictureContent model.JSON `json:"PictureContent" gorm:"column:picturecontent;type:json;default:;comment:答案图片描述"`
	IsRight        int64      `json:"IsRight"`
	Checked        bool       `json:"Checked"` // 是否选择在交卷的时候
}
