package learnModel

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"errors"
	"gorm.io/gorm"
	"strings"
	"time"
)

type TestPaper struct {
	model.PkId
	model.Timestamp
	//StaffIds              string          `json:"StaffIds" gorm:"column:staffids;type:text;default:;comment:所属人员"`
	CategoryId            int64           `json:"CategoryId" gorm:"column:categoryid;type:bigint;default:;comment:分类id"  validate:"required"`
	Title                 string          `json:"Title" gorm:"column:title;type:varchar;default:;comment:试卷标题" validate:"required"`
	ExpirationDate        model.LocalTime `json:"ExpirationDate" gorm:"column:expirationdate;type:timestamp;default:;comment:过期时间" validate:"required"`
	PassingMark           int64           `json:"PassingMark" gorm:"column:passingmark;type:integer;default:;comment:及格分数" validate:"required"`
	ShowWrong             int64           `json:"ShowWrong" gorm:"column:showwrong;type:smallint;default:;comment:是否显示错题 1是2否" validate:"required"`
	ShowAnswer            int64           `json:"ShowAnswer" gorm:"column:showanswer;type:smallint;default:;comment:是否显示答案 1是2否" validate:"required"`
	IsRandom              int64           `json:"IsRandom" gorm:"column:israndom;type:smallint;default:;comment:是否打乱试题 1是2否" validate:"required"`
	RepeatExam            int64           `json:"RepeatExam" gorm:"column:repeatexam;type:smallint;default:;comment:是否可以刷分 1是2否" validate:"required"`
	ScoringType           int64           `json:"ScoringType" gorm:"column:scoringtype;type:smallint;default:;comment:记分类型 1取最高分2取最终分" validate:"required"`
	AnsweringTime         int64           `json:"AnsweringTime" gorm:"column:answeringtime;type:integer;default:;comment:答题时间 秒" validate:"required"`
	TopicType1Score       int64           `json:"TopicType1Score" gorm:"column:topictype1score;type:integer;default:;comment:单选题总分 *10"`
	TopicType1Count       int64           `json:"TopicType1Count" gorm:"column:topictype1count;type:integer;default:;comment:单选题数量"`
	TopicType2Score       int64           `json:"TopicType2Score" gorm:"column:topictype2score;type:integer;default:;comment:多选题总分 *10"`
	TopicType2ScoringType int64           `json:"TopicType2ScoringType" gorm:"column:topictype2scoringtype;type:smallint;default:;comment:多选题得分类型 1全对给分 2比例给分"`
	TopicType2Count       int64           `json:"TopicType2Count" gorm:"column:topictype2count;type:integer;default:;comment:多选题数量"`
	TopicType3Score       int64           `json:"TopicType3Score" gorm:"column:topictype3score;type:integer;default:;comment:判断题总分 *10"`
	TopicType3Count       int64           `json:"TopicType3Count" gorm:"column:topictype3count;type:integer;default:;comment:判断题数量"`
	Topics                []Topic         `json:"Topics" gorm:"many2many:test_paper_topic;constraint:OnDelete:CASCADE;"`
}

func (m *TestPaper) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *TestPaper) Create() error {
	return model.DB().Omit("Topics.*").Create(&m).Error
}

func (m *TestPaper) Update() error {
	tx := model.DB().Begin()
	err := tx.Omit("Topics.*").Updates(&m).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	err = tx.Model(&m).Omit("Topics.*").Association("Topics").Replace(m.Topics)
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}

func (m *TestPaper) Delete(id int64) error {
	return model.DB().Select("Topics").Delete(&TestPaper{}, id).Error
}

func (m *TestPaper) List(title, startTime, endTime string, categoryID int64, paginator model.Paginator) (data []TestPaper, totalCount int64, err error) {
	db := model.DB().Model(&TestPaper{})
	if title != "" {
		db = db.Where("Title LIKE ?", "%"+title+"%")
	}
	if categoryID != 0 {
		db = db.Where("CategoryId = ?", categoryID)
	}
	if startTime != "" {
		db = db.Where("CreatedAt >= ?", startTime)
	}
	if endTime != "" {
		db = db.Where("CreatedAt <= ?", endTime)
	}
	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Offset(paginator.Offset).Limit(paginator.Limit)
	}
	err = db.Preload("Topics", func(db *gorm.DB) *gorm.DB {
		return db.Preload("Answers")
	}).Order("CreatedAt DESC").Find(&data).Error
	return
}

func (m *TestPaper) FindById(id int64) error {
	return model.DB().First(&m, id).Error
}

// FindForExam 考试时找到题目和答案 需要去掉正确答案的字段
func (m *TestPaper) FindForExam(id int64) error {
	err := model.DB().Preload("Topics", func(db *gorm.DB) *gorm.DB {
		return db.Preload("Answers")
	}).First(&m, id).Error
	if err != nil {
		return err
	}
	for i := range m.Topics {
		if m.Topics[i].AnswerRandom == 1 {
			// 答案随机
			arr := util.ShuffleSlice(len(m.Topics[i].Answers))
			var finalAnswer []TopicAnswer
			for _, v := range arr {
				m.Topics[i].Answers[v].IsRight = -1 //将答案隐藏
				finalAnswer = append(finalAnswer, m.Topics[i].Answers[v])
			}
			m.Topics[i].Answers = finalAnswer
		} else {
			for j := range m.Topics[i].Answers {
				m.Topics[i].Answers[j].IsRight = -1 //将答案隐藏
			}
		}
	}
	return nil
}

// IsExpiration 是否过期
func (m *TestPaper) IsExpiration() bool {
	return time.Now().Unix() >= time.Time(m.ExpirationDate).Unix()
}

// IsModified 是否可以修改或者删除
func (m *TestPaper) IsModified() bool {
	var task LearnExamTask
	_ = task.FindByTestPaperId(m.Id)
	return task.Id == 0
}

// Scoring 计算试卷的分数 并且生成记录
func (m *TestPaper) Scoring(staffExamRecordId int64) (finalScore, finalPass int64, err error) {
	if staffExamRecordId == 0 {
		err = errors.New("参数错误")
		return
	}
	if m.Topics == nil || len(m.Topics) == 0 {
		err = errors.New("试卷错误")
		return
	}
	var totalScore int64 // 总分数
	topicTypeScore := map[int64]int64{
		1: m.TopicType1Score / m.TopicType1Count, // 单选题一个的分数,
		2: m.TopicType2Score / m.TopicType2Count, // 多选题一个的分数,
		3: m.TopicType3Score / m.TopicType3Count, // 判断题一个的分数,
	}
	for _, topic := range m.Topics {
		topicScore := topicTypeScore[topic.TopicType]
		var staffExamRecordLine StaffExamRecordLine // 考试记录明细表
		staffExamRecordLine.StaffExamRecordId = staffExamRecordId
		staffExamRecordLine.IsRight = 2
		staffExamRecordLine.TopicId = topic.Id
		var ChooseAnswer []int64 // 用户选择的答案
		var rightNum int64       // 正确答案数量
		var bingoNum int64       // 答对的数量
		for _, answer := range topic.Answers {
			if answer.IsRight == 1 {
				rightNum++
				if answer.Checked {
					ChooseAnswer = append(ChooseAnswer, answer.Id)
					bingoNum++
				}
			}
		}
		staffExamRecordLine.ChooseAnswer = strings.Join(util.Int64ArrToString(ChooseAnswer), ",")
		var addScore int64
		if bingoNum == rightNum {
			// 全对都给分
			addScore = topicScore
		}
		if bingoNum != 0 && bingoNum < rightNum {
			if m.TopicType2ScoringType == 2 {
				addScore = topicScore * (bingoNum / rightNum)
			}
		}
		totalScore += addScore
	}
	var staffExamRecord StaffExamRecord
	_ = staffExamRecord.FindById(staffExamRecordId)
	staffExamRecord.Score = totalScore
	if staffExamRecord.Score >= m.PassingMark {
		staffExamRecord.IsPass = 3
	} else {
		staffExamRecord.IsPass = 2
	}
	err = staffExamRecord.Update()
	if err != nil {
		err = errors.New("试卷批阅发生错误")
		return
	}
	finalScore = staffExamRecord.Score
	finalPass = staffExamRecord.IsPass
	return
}

type AllocationForm struct {
	AllocationType int64   `validate:"required" validate:"required"` // 分配类型 1自选 2群组
	TestPaperId    int64   `json:"TestPaperId"`                      // 试卷id
	CourseId       int64   `json:"CourseId"`                         // 课程id
	GroupIds       []int64 `json:"GroupIds"`                         // 分配类型为群组时 传
	StaffIds       []int64 `json:"StaffIds"`                         // 分配类型为自选时 传
}
