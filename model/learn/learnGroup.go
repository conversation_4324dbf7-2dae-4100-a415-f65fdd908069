package learnModel

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

// LearnGroup 学习考试分组
type LearnGroup struct {
	model.PkId
	model.Timestamp
	Name       string `json:"Name" gorm:"column:name;type:varchar;default:;comment:小组名称;uniqueIndex:learn_group_name"`
	StaffIds   string `json:"StaffIds" gorm:"column:staffids;type:text;default:;comment:人员id"`
	StaffNames string `json:"StaffNames" gorm:"column:staffnames;type:text;default:;comment:人员姓名"`
}

func (m *LearnGroup) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *LearnGroup) Create() error {
	return model.DB().Create(&m).Error
}

func (m *LearnGroup) Update() error {
	return model.DB().Updates(&m).Error
}

func (m *LearnGroup) Delete(id int64) error {
	return model.DB().Delete(&LearnGroup{}, id).Error
}

func (m *LearnGroup) List(name, staffName string, paginator model.Paginator) (data []LearnGroup, totalCount int64, err error) {
	tx := model.DB().Model(&m)
	if name != "" {
		tx = tx.Where("Name LIKE ?", "%"+name+"%")
	}
	if staffName != "" {
		tx = tx.Where("StaffNames LIKE ?", "%"+staffName+"%")
	}
	tx.Count(&totalCount)
	if paginator.Limit > 0 {
		tx = tx.Limit(paginator.Limit).Offset(paginator.Offset)
	}
	err = tx.Order("CreatedAt DESC").Find(&data).Error
	return
}

func (m *LearnGroup) ListByIds(groupIds []int64) (data []LearnGroup) {
	model.DB().Model(&m).Where("Id in ?", groupIds).Find(&data)
	return
}
