package learnModel

import (
	"app/org/scs/erpv2/api/model"
	"fmt"
	"gorm.io/gorm"
)

// EducationalResource 学习资料
type EducationalResource struct {
	model.PkId
	model.Timestamp
	Title        string     `json:"Title" gorm:"column:title;type:varchar;default:;comment:资料标题" validate:"required"`
	ResourceType int64      `json:"ResourceType" gorm:"column:resourcetype;type:integer;default:;comment:资料类型 1视频2pdf"  validate:"required"`
	CategoryIds  string     `json:"CategoryIds" gorm:"column:categoryids;type:varchar;default:;comment:分类ids"  validate:"required"`
	Content      model.JSON `json:"Content" gorm:"column:Content;type:json;default:;comment:学习内容" validate:"required"`
	FinishTime   int64      `json:"FinishTime" gorm:"column:finishtime;type:integer;default:;comment:学习完成时间 秒" `
	//
	CategoryNames string           `json:"CategoryNames" gorm:"-"`
	LearnRecord   StaffLearnRecord `json:"LearnRecord" gorm:"-"`
}

func (m *EducationalResource) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *EducationalResource) Create() error {
	return model.DB().Create(&m).Error
}

func (m *EducationalResource) Update() error {
	return model.DB().Updates(&m).Error
}

func (m *EducationalResource) Delete(id int64) error {
	return model.DB().Delete(&EducationalResource{}, id).Error
}

func (m *EducationalResource) List(title string, categoryID, resourceType int64, paginator model.Paginator) (data []EducationalResource, totalCount int64, err error) {
	db := model.DB().Model(&m)
	if title != "" {
		db = db.Where("Title LIKE ?", "%"+title+"%")
	}
	if categoryID != 0 {
		db = db.Where("CategoryIds LIKE ?", fmt.Sprintf("%%%d%%", categoryID))
	}
	if resourceType != 0 {
		db = db.Where("ResourceType = ?", resourceType)
	}
	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Limit(paginator.Limit).Offset(paginator.Offset)
	}
	err = db.Order("CreatedAt DESC").Find(&data).Error
	return
}
