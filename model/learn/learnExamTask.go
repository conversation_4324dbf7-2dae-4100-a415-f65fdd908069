package learnModel

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"strings"
	"time"
)

// LearnExamTask 学习考试任务表
type LearnExamTask struct {
	model.PkId
	model.Timestamp
	model.OpUser
	TaskType        int64  `json:"TaskType" gorm:"column:tasktype;type:smallint;default:;comment:1学习任务 2考试任务"`
	CategoryId      int64  `json:"CategoryId" gorm:"column:categoryid;type:bigint;default:;comment:分类id" `
	AllocationType  int64  `json:"AllocationType" gorm:"column:allocationtype;type:smallint;default:;comment:分配类型 1自选 2群组"`
	GroupIds        string `json:"GroupIds" gorm:"column:GroupIds;type:text;default:;comment:群组ids"`
	StaffIds        string `json:"StaffIds" gorm:"column:staffids;type:text;default:;comment:人员id"`
	StaffNames      string `json:"StaffNames" gorm:"column:staffnames;type:text;default:;comment:人员姓名"`
	StaffTotalCount int64  `json:"StaffTotalCount" gorm:"column:stafftotalcount;type:integer;default:;comment:人员总数"`
	// 考试任务指派
	TestPaperId    int64  `json:"TestPaperId" gorm:"column:testpaperid;type:bigint;default:;comment:试卷id"`
	TestPaperTitle string `json:"TestPaperTitle" gorm:"column:testpapertitle;type:varchar;default:;comment:试卷名称"`
	ExamFinish     int64  `json:"ExamFinish" gorm:"column:examfinish;type:integer;default:;comment:考试人数"`
	ExamPass       int64  `json:"ExamPass" gorm:"column:exampass;type:integer;default:;comment:通过考试人数"`
	ExamMakeUp     int64  `json:"ExamMakeUp" gorm:"column:exammakeup;type:integer;default:;comment:补考人数"`
	ExamNoPass     int64  `json:"ExamNoPass" gorm:"column:examnopass;type:integer;default:;comment:未通过考试人数"`
	// 学习指派
	CourseId    int64  `json:"CourseId" gorm:"column:courseid;type:bigint;default:;comment:课程id"`
	CourseTitle string `json:"CourseTitle" gorm:"column:coursetitle;type:varchar;default:;comment:课程名称"`
	CourseType  int64  `json:"CourseType" gorm:"column:coursetype;type:smallint;default:1;comment:1必修 2选修" `
	LearnFinish int64  `json:"LearnFinish" gorm:"column:learnfinish;type:integer;default:;comment:学习完成情况"`
	//
	NeedSummary     int64              `json:"NeedSummary" gorm:"column:needsummary;type:smallint;default:1;comment:1不需要汇总2需要汇总"`
	MySummaryReport StaffSummaryReport `json:"MySummaryReport" gorm:"-"` //
}

func (m *LearnExamTask) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *LearnExamTask) Create() error {
	return model.DB().Create(&m).Error
}

func (m *LearnExamTask) Update() error {
	tx := model.DB()
	err := tx.Updates(&m).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}

func (m *LearnExamTask) Delete(id int64) error {
	return model.DB().Delete(&LearnExamTask{}, id).Error
}

func (m *LearnExamTask) IsModified() bool {
	if m.Id == 0 {
		return false
	}
	staffRecords, _, _ := (&StaffSummaryReport{}).List([]int64{}, 0, m.Id, "", model.Paginator{Limit: 0})
	if len(staffRecords) != 0 {
		return false
	} else {
		return true

	}
}

func (m *LearnExamTask) FindById(id int64) error {
	return model.DB().First(&m, id).Error
}

func (m *LearnExamTask) FindByTestPaperId(testPaperId int64) error {
	return model.DB().Model(&m).Where("TestPaperId = ?", testPaperId).Find(&m).Error
}

func (m *LearnExamTask) FindByCourseId(courseId int64) error {
	return model.DB().Model(&m).Where("CourseId = ?", courseId).Find(&m).Error
}

func (m *LearnExamTask) List(courseTitle, testPaperTitle, startTime, endTime string, categoryID, courseType int64, paginator model.Paginator) (data []LearnExamTask, totalCount int64, err error) {
	db := model.DB().Model(&LearnExamTask{})
	if courseTitle != "" {
		db = db.Where("CourseTitle LIKE ?", "%"+courseTitle+"%")
	}
	if testPaperTitle != "" {
		db = db.Where("TestPaperTitle LIKE ?", "%"+testPaperTitle+"%")
	}
	if categoryID != 0 {
		db = db.Where("CategoryId = ?", categoryID)
	}
	if courseType != 0 {
		db = db.Where("CourseType = ?", courseType)
	}
	if startTime != "" {
		db = db.Where("CreatedAt >= ?", startTime)
	}
	if endTime != "" {
		db = db.Where("CreatedAt <= ?", endTime)
	}
	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Offset(paginator.Offset).Limit(paginator.Limit)
	}
	err = db.Preload("Topics").Find(&data).Error
	return
}

// ExamTaskList 考试任务列表
func (m *LearnExamTask) ExamTaskList(testPaperTitle string, categoryID, staffId int64, paginator model.Paginator) (data []LearnExamTask, totalCount int64, err error) {
	db := model.DB().Model(&LearnExamTask{}).Where("TaskType = ?", 2)
	if testPaperTitle != "" {
		db = db.Where("TestPaperTitle LIKE ?", "%"+testPaperTitle+"%")
	}
	if categoryID != 0 {
		db = db.Where("CategoryId = ?", categoryID)
	}
	if staffId != 0 {
		db.Where("(',' || StaffIds || ',') LIKE ?", fmt.Sprintf("%%,%d,%%", staffId))
	}
	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Offset(paginator.Offset).Limit(paginator.Limit)
	}
	err = db.Preload("Topics").Find(&data).Error
	return
}

// LearnTaskList 学习任务列表
func (m *LearnExamTask) LearnTaskList(courseTitle string, categoryID, staffId int64, paginator model.Paginator) (data []LearnExamTask, totalCount int64, err error) {
	db := model.DB().Model(&LearnExamTask{}).Where("TaskType = ?", 1)
	if courseTitle != "" {
		db = db.Where("CourseTitle LIKE ?", "%"+courseTitle+"%")
	}
	if categoryID != 0 {
		db = db.Where("CategoryId = ?", categoryID)
	}
	if staffId != 0 {
		db.Where("(',' || StaffIds || ',') LIKE ?", fmt.Sprintf("%%,%d,%%", staffId))
	}
	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Offset(paginator.Offset).Limit(paginator.Limit)
	}
	err = db.Preload("Topics").Find(&data).Error
	return
}

// PublishExamTask 发布考试任务
func (m *LearnExamTask) PublishExamTask(form AllocationForm, user *auth.AuthUser) error {
	m.TaskType = 2                         // 任务类型  1学习任务 2考试任务"
	m.AllocationType = form.AllocationType //分配类型
	m.OpUserId = user.Id
	m.OpUserName = user.Name
	err := m.SetPublishTaskExamInfo(form.TestPaperId)
	if err != nil {
		return err
	}
	m.SetPublishTaskBaseInfo(form)
	err = m.Create()
	if err != nil {
		return errors.New("指派失败")
	}
	return nil
}

// PublishLearnTask  发布学习任务
func (m *LearnExamTask) PublishLearnTask(form AllocationForm, user *auth.AuthUser) error {
	m.TaskType = 1                         // 任务类型  1学习任务 2考试任务"
	m.AllocationType = form.AllocationType //分配类型
	m.OpUserId = user.Id
	m.OpUserName = user.Name
	err := m.SetPublishTaskCourseInfo(form.CourseId)
	if err != nil {
		return err
	}
	m.SetPublishTaskBaseInfo(form)
	err = m.Create()
	if err != nil {
		return errors.New("指派失败")
	}
	return nil
}

func (m *LearnExamTask) SetPublishTaskCourseInfo(courseId int64) error {
	var course LearnCourse
	err := course.FindById(courseId)
	if err != nil {
		return errors.New("未找到试卷")
	}
	if course.IsExpiration() {
		return errors.New("课程已截止")
	}
	m.CategoryId = course.CategoryId // 分类
	m.CourseId = course.Id
	m.CourseTitle = course.Title
	m.CourseType = course.CourseType
	if course.NeedExamination == 1 {
		return m.SetPublishTaskExamInfo(course.TestPaperId)
	}
	return nil
}

func (m *LearnExamTask) SetPublishTaskExamInfo(testPaperId int64) error {
	var testPaper TestPaper
	err := testPaper.FindById(testPaperId)
	if err != nil {
		return errors.New("未找到试卷")
	}
	if testPaper.IsExpiration() {
		return errors.New("试卷已过期，无法指派")
	}
	m.TestPaperId = testPaper.Id
	m.TestPaperTitle = testPaper.Title
	m.CategoryId = testPaper.CategoryId // 分类
	return nil
}

func (m *LearnExamTask) SetPublishTaskBaseInfo(form AllocationForm) {
	if m.AllocationType == 1 {
		// 自选
		staffIds := util.Int64ArrToString(form.StaffIds)
		m.StaffIds = strings.Join(staffIds, ",")
		m.StaffTotalCount = int64(len(staffIds))
	} else {
		GroupIds := util.Int64ArrToString(form.GroupIds)
		m.GroupIds = strings.Join(GroupIds, ",")
		groups := (&LearnGroup{}).ListByIds(form.GroupIds)
		staffIdMap := make(map[string]byte)
		for _, group := range groups {
			if group.StaffIds == "" {
				continue
			}
			staffIdArr := strings.Split(group.StaffIds, ",")
			for _, staffId := range staffIdArr {
				staffIdMap[staffId] = 0
			}
		}
		var staffIds []string
		for key, _ := range staffIdMap {
			staffIds = append(staffIds, key)
		}
		m.StaffIds = strings.Join(staffIds, ",")
		m.StaffTotalCount = int64(len(staffIds))
	}
}

func (m *LearnExamTask) StartExam(taskId, userId int64) (staffExamRecordId int64, err error) {
	err = m.FindById(taskId)
	if err != nil {
		err = errors.New("未找到该指派任务")
		return
	}
	if m.TaskType == 1 && m.TestPaperId == 0 {
		err = errors.New("此学习任务不需要考试")
		return
	}

	var staffExamSummaryReport StaffSummaryReport
	_ = staffExamSummaryReport.Find(userId, m.Id)
	tx := model.DB().Begin()
	var ExamType int64
	ExamType = 1 // 正常考试
	if staffExamSummaryReport.Id == 0 {
		staff := rpc.GetStaffWithId(context.Background(), userId)
		if staff == nil {
			err = errors.New("找不到员工信息")
			tx.Rollback()
			return
		}
		staffExamSummaryReport.Corporations.Build(staff.CorporationId)
		staffExamSummaryReport.StaffId = userId
		staffExamSummaryReport.StaffName = staff.Name
		staffExamSummaryReport.JobNumber = staff.Code
		staffExamSummaryReport.TaskId = m.Id
		staffExamSummaryReport.TestPaperId = m.TestPaperId
		staffExamSummaryReport.ExamCount = 1
		if m.TaskType == 2 {
			//var testPaper TestPaper
			//_ = testPaper.FindById(m.TestPaperId)
			//if testPaper.RepeatExam == 1 {
			//	staffExamSummaryReport.ExamTotalCount = -99
			//} else {
			//	staffExamSummaryReport.ExamTotalCount = 1
			//}
			staffExamSummaryReport.ExamTotalCount = -99
		} else {
			var course LearnCourse
			_ = course.FindById(m.CourseId)
			if course.AllowMakeupExam == 2 {
				staffExamSummaryReport.ExamTotalCount = 1
			} else {
				staffExamSummaryReport.ExamTotalCount = course.MakeupExamCount + 1
			}
		}
		err = staffExamSummaryReport.TxCreate(tx)
		if err != nil {
			err = errors.New("未知错误,请联系管理员")
			tx.Rollback()
			return
		}
	} else {
		var testPaper TestPaper
		_ = testPaper.FindById(m.TestPaperId)
		if staffExamSummaryReport.IsPass == 3 && testPaper.RepeatExam == 2 {
			// 通过且无法刷分的情况下 无法考试
			err = errors.New("您已通过考试，无法再考")
			tx.Rollback()
			return
		}

		if staffExamSummaryReport.ExamTotalCount != -99 && ((staffExamSummaryReport.ExamCount) >= staffExamSummaryReport.ExamTotalCount) {
			err = errors.New("已无考试次数")
			tx.Rollback()
			return
		}
		staffExamSummaryReport.ExamCount++
		if staffExamSummaryReport.IsPass == 3 {
			ExamType = 3 // 刷分
		}
		if staffExamSummaryReport.IsPass == 2 {
			// 上次是未通过 这次就是补考
			staffExamSummaryReport.ExamMakeUpCount++
			ExamType = 2 // 补考
		}
		err = staffExamSummaryReport.TxUpdate(tx)
		if err != nil {
			err = errors.New("未知错误,请联系管理员")
			tx.Rollback()
			return
		}
	}
	var staffExamRecord StaffExamRecord
	staffExamRecord.StaffId = userId
	staffExamRecord.TaskId = m.Id
	staffExamRecord.TestPaperId = m.TestPaperId
	staffExamRecord.ExamType = ExamType
	err = staffExamRecord.TxCreate(tx)
	if err != nil {
		err = errors.New("未知错误,请联系管理员")
		tx.Rollback()
		return
	}
	tx.Commit()
	return staffExamRecord.Id, nil
}

// Summary 指派任务汇总
func (m *LearnExamTask) Summary() {
	summaryReports, _, _ := (&StaffSummaryReport{}).List([]int64{}, 0, m.Id, "", model.Paginator{Limit: 0})
	var ExamFinish, ExamPass, ExamMakeUp, ExamNoPass, LearnFinish int64
	for _, report := range summaryReports {
		if !time.Time(report.ExamOver).IsZero() {
			ExamFinish++
		}
		if !time.Time(report.LearnOver).IsZero() {
			LearnFinish++
		}
		if report.IsPass == 3 {
			ExamPass++
		}
		if report.IsPass == 2 {
			ExamNoPass++
		}
		if report.ExamMakeUpCount != 0 {
			ExamMakeUp++
		}
	}
	m.ExamFinish = ExamFinish
	m.ExamPass = ExamPass
	m.ExamMakeUp = ExamMakeUp
	m.ExamNoPass = ExamNoPass
	m.LearnFinish = LearnFinish
	m.NeedSummary = 1
	_ = m.Update()
}

func (m *LearnExamTask) ChangeSummaryStatus(id int64) error {
	return model.DB().Model(&m).Update("NeedSummary", 2).Error
}

// StaffSummaryReport 考试学习汇总表
type StaffSummaryReport struct {
	model.PkId
	model.Timestamp
	model.Corporations
	JobNumber string `json:"JobNumber" gorm:"column:jobnumber;type:varchar;default:;comment:工号"`
	StaffName string `json:"StaffName" gorm:"column:staffname;type:varchar;default:;comment:员工名称;"`
	StaffId   int64  `json:"StaffId" gorm:"column:staffid;type:bigint;default:;comment:员工id;uniqueIndex:staff_summary_staffid_taskid;"`
	TaskId    int64  `json:"TaskId" gorm:"column:taskid;type:bigint;default:;comment:学习任务;uniqueIndex:staff_summary_staffid_taskid;"`
	// 考试汇总
	TestPaperId     int64           `json:"TestPaperId" gorm:"column:testpaperid;type:bigint;default:;comment:试卷id;"`
	ExamOver        model.LocalTime `json:"ExamOver" gorm:"column:examover;type:timestamp;default:;comment:考试结束时间"`
	Score           int64           `json:"Score" gorm:"column:score;type:integer;default:;comment:得分 *10"`
	IsPass          int64           `json:"IsPass" gorm:"column:ispass;type:smallint;default:;comment:是否通过 1考试中 2未通过 3已通过"`
	ExamTotalCount  int64           `json:"ExamTotalCount" gorm:"column:examtotalcount;type:integer;integer:;comment:考试机会 -99可以无限考试"`
	ExamCount       int64           `json:"ExamCount" gorm:"column:examcount;type:integer;integer:;comment:已考次数"`
	ExamMakeUpCount int64           `json:"ExamMakeUpCount" gorm:"column:exammakeupcount;type:integer;default:;comment:补考次数"`
	// 学习汇总
	CourseId        int64           `json:"CourseId" gorm:"column:courseid;type:bigint;default:;comment:课程id;"`
	LearnOver       model.LocalTime `json:"LearnOver" gorm:"column:learnover;type:timestamp;default:;comment:学习完成时间"`
	LearnTotalCount int64           `json:"LearnTotalCount" gorm:"column:learntotalcount;type:integer;integer:;comment:学习资料数量"`
	LearnCount      int64           `json:"LearnCount" gorm:"column:learncount;type:integer:;comment:已完成学习资料数量"`

	StaffLearnRecords []StaffLearnRecord `json:"StaffLearnRecords" gorm:"-"`
	StaffExamRecords  []StaffExamRecord  `json:"StaffExamRecords" gorm:"-"`
	CorporationId     int64              `json:"CorporationId" gorm:"-"`
	CorporationName   string             `json:"CorporationName" gorm:"-"`
}

func (m *StaffSummaryReport) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *StaffSummaryReport) Create() error {
	return model.DB().Create(&m).Error
}

func (m *StaffSummaryReport) TxCreate(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *StaffSummaryReport) Update() error {
	return model.DB().Select("*").Updates(&m).Error
}

func (m *StaffSummaryReport) TxUpdate(tx *gorm.DB) error {
	return tx.Select("*").Updates(&m).Error
}

func (m *StaffSummaryReport) List(corporationIds []int64, staffId, taskId int64, staffInfo string, paginator model.Paginator) (data []StaffSummaryReport, totalCount int64, err error) {
	db := model.DB()
	if corporationIds != nil || len(corporationIds) != 0 {
		db = db.Scopes(model.WhereCorporations(corporationIds))
	}
	if staffId != 0 {
		db = db.Where("StaffId = ?", staffId)
	}
	if taskId != 0 {
		db = db.Where("TaskId = ?", taskId)
	}
	if staffInfo != "" {
		db = db.Where("(StaffName LIKE ? OR JobNumber LIKE ?)", "%"+staffInfo+"%", "%"+staffInfo+"%")
	}
	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Limit(paginator.Limit).Offset(paginator.Offset)
	}
	err = db.Find(&data).Error
	return
}

func (m *StaffSummaryReport) Find(staffId, taskId int64) error {
	return model.DB().Where("StaffId = ?", staffId).Where("TaskId = ?", taskId).Find(&m).Error
}

func (m *StaffSummaryReport) ExamSummary(scoreType int64) error {
	examRecords, _ := (&StaffExamRecord{}).List(m.StaffId, m.TaskId)
	var maxScore int64    // 最高分数
	var examCount int64   // 已考次数
	var makeUpCount int64 // 补考次数
	var isPass int64
	isPass = 2
	for index, record := range examRecords {
		examCount++
		if index == 0 {
			m.ExamOver = record.UpdatedAt // 考试完成时间
			if scoreType == 2 {
				// 去最终分
				m.Score = record.Score
			}
		}
		// 取最高分数
		if record.Score > maxScore {
			maxScore = record.Score
		}
		// 补考次数
		if record.ExamType == 2 {
			makeUpCount++
		}
		if record.IsPass == 3 {
			isPass = 3
		}
	}
	m.IsPass = isPass
	if scoreType == 1 {
		m.Score = maxScore
	}
	return m.Update()
}

// StaffExamRecord 考试记录表
type StaffExamRecord struct {
	model.PkId
	model.Timestamp
	StaffId     int64 `json:"StaffId" gorm:"column:staffid;type:bigint;default:;comment:员工id"`
	TaskId      int64 `json:"TaskId" gorm:"column:taskid;type:bigint;default:;comment:学习任务"`
	TestPaperId int64 `json:"TestPaperId" gorm:"column:testpaperid;type:bigint;default:;comment:试卷id"`
	ExamType    int64 `json:"ExamType" gorm:"column:examtype;type:integer;default:;comment:考试性质 1正常考试 2补考 3刷分"`
	Score       int64 `json:"Score" gorm:"column:score;type:integer;default:;comment:得分 *10"`
	IsPass      int64 `json:"IsPass" gorm:"column:ispass;type:smallint;default:;comment:是否通过 1考试中/中途退出 2未通过 3已通过"`

	//StaffExamRecordLines []StaffExamRecordLine `json:"StaffExamRecordLines" gorm:"foreignKey:StaffExamRecordId"`
}

func (m *StaffExamRecord) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *StaffExamRecord) Create() error {
	return model.DB().Create(&m).Error
}

func (m *StaffExamRecord) TxCreate(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *StaffExamRecord) Update() error {
	tx := model.DB()
	err := tx.Updates(&m).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	//err = tx.Model(&m).Association("Topics").Replace(m.Topics)
	//if err != nil {
	//	tx.Rollback()
	//	return err
	//}
	tx.Commit()
	return nil
}

func (m *StaffExamRecord) List(staffId, taskId int64) (data []StaffExamRecord, err error) {
	err = model.DB().Model(&StaffExamRecord{}).Where("StaffId = ?", staffId).Where("TaskId = ?", taskId).Order("CreatedAt DESC").Find(&data).Error
	return
}

func (m *StaffExamRecord) FindById(Id int64) error {
	return model.DB().First(&m, Id).Error
}

// StaffExamRecordLine 考试记录表明细
type StaffExamRecordLine struct {
	model.PkId
	model.Timestamp
	StaffExamRecordId int64  `json:"StaffExamRecordId" gorm:"column:staffexamrecordid;type:bigint;default:;comment:员工考试记录表id"`
	TopicId           int64  `json:"TopicId" gorm:"column:topicid;type:bigint;default:;comment:题目id"`
	ChooseAnswer      string `json:"ChooseAnswer" gorm:"column:chooseanswer;type:varchar;default:;comment:选择答案"`
	Score             int64  `json:"Score" gorm:"column:score;type:integer;default:;comment:题目得分 *10"`
	IsRight           int64  `json:"IsRight" gorm:"column:isright;type:smallint;default:;comment:是否正确 1正确 2部分正确 2错误"`
}

func (m *StaffExamRecordLine) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *StaffExamRecordLine) Create() error {
	return model.DB().Create(&m).Error
}

func (m *StaffExamRecordLine) Update() error {
	tx := model.DB()
	err := tx.Updates(&m).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	//err = tx.Model(&m).Association("Topics").Replace(m.Topics)
	//if err != nil {
	//	tx.Rollback()
	//	return err
	//}
	tx.Commit()
	return nil
}

// StaffLearnRecord 学习记录表
type StaffLearnRecord struct {
	model.PkId
	model.Timestamp
	StaffId                 int64  `json:"StaffId" gorm:"column:staffid;type:bigint;default:;comment:员工id"`
	TaskId                  int64  `json:"TaskId" gorm:"column:taskid;type:bigint;default:;comment:学习任务"`
	CourseId                int64  `json:"CourseId" gorm:"column:testpaperid;type:bigint;default:;comment:课程id"`
	EducationalResourceId   int64  `json:"EducationalResourceId" gorm:"column:educationalresourceid;type:bigint;default:;comment:学习资料id"`
	EducationalResourceName string `json:"EducationalResourceName" gorm:"column:educationalresourcename;type:varchar;default:;comment:学习资料名称"`
	LearnTime               int64  `json:"LearnTime" gorm:"column:learntime;type:integer;default:;comment:学习时间 秒"`
	Status                  int64  `json:"Status" gorm:"column:status;type:smallint;default:;comment:1未完成 2已完成"`
}

func (m *StaffLearnRecord) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *StaffLearnRecord) Create() error {
	return model.DB().Create(&m).Error
}

func (m *StaffLearnRecord) Update() error {
	tx := model.DB()
	err := tx.Updates(&m).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}

func (m *StaffLearnRecord) List(staffId, taskId int64) (data []StaffLearnRecord, err error) {
	err = model.DB().Model(&StaffLearnRecord{}).Where("StaffId = ?", staffId).Where("TaskId = ?", taskId).Find(&data).Error
	return
}
