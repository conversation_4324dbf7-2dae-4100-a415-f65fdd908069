package learnModel

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

// LearnExamTask 学习考试任务表
type LearnExamTask struct {
	model.PkId
	model.Timestamp
	model.OpUser
	TaskName        string `json:"TaskName" gorm:"column:taskname;type:varchar;default:;comment:任务名称"`
	TaskCode        string `json:"TaskCode" gorm:"column:taskcode;type:varchar;default:;comment:任务编号"`
	CodeNumber      int64  `json:"CodeNumber" gorm:"column:codenumber;type:integer;default:;comment:"`
	TaskType        int64  `json:"TaskType" gorm:"column:tasktype;type:smallint;default:;comment:1学习任务 2考试任务"`
	CategoryId      int64  `json:"CategoryId" gorm:"column:categoryid;type:bigint;default:;comment:分类id" `
	AllocationType  int64  `json:"AllocationType" gorm:"column:allocationtype;type:smallint;default:;comment:分配类型 1自选 2群组"`
	GroupIds        string `json:"GroupIds" gorm:"column:GroupIds;type:text;default:;comment:群组ids"`
	StaffIds        string `json:"StaffIds" gorm:"column:staffids;type:text;default:;comment:人员id"`
	StaffNames      string `json:"StaffNames" gorm:"column:staffnames;type:text;default:;comment:人员姓名"`
	StaffTotalCount int64  `json:"StaffTotalCount" gorm:"column:stafftotalcount;type:integer;default:;comment:人员总数"`
	// 考试任务指派
	TestPaperId             int64           `json:"TestPaperId" gorm:"column:testpaperid;type:bigint;default:;comment:试卷id"`
	TestPaperTitle          string          `json:"TestPaperTitle" gorm:"column:testpapertitle;type:varchar;default:;comment:试卷名称"`
	TestPaperExpirationDate model.LocalTime `json:"TestPaperExpirationDate" gorm:"column:testpaperexpirationdate;type:timestamp;default:;comment:试卷过期时间" validate:"required"`
	ExamFinish              int64           `json:"ExamFinish" gorm:"column:examfinish;type:integer;default:;comment:考试人数"`
	ExamPass                int64           `json:"ExamPass" gorm:"column:exampass;type:integer;default:;comment:通过考试人数"`
	ExamMakeUp              int64           `json:"ExamMakeUp" gorm:"column:exammakeup;type:integer;default:;comment:补考人数"`
	ExamNoPass              int64           `json:"ExamNoPass" gorm:"column:examnopass;type:integer;default:;comment:未通过考试人数"`
	// 学习指派
	CourseId             int64           `json:"CourseId" gorm:"column:courseid;type:bigint;default:;comment:课程id"`
	CourseTitle          string          `json:"CourseTitle" gorm:"column:coursetitle;type:varchar;default:;comment:课程名称"`
	CourseExpirationDate model.LocalTime `json:"CourseExpirationDate" gorm:"column:courseexpirationdate;type:timestamp;default:;comment:课程过期时间"`
	CourseType           int64           `json:"CourseType" gorm:"column:coursetype;type:smallint;default:1;comment:1必修 2选修" `
	LearnFinish          int64           `json:"LearnFinish" gorm:"column:learnfinish;type:integer;default:;comment:学习完成情况"`
	//
	NeedSummary     int64              `json:"NeedSummary" gorm:"column:needsummary;type:smallint;default:1;comment:1不需要汇总2需要汇总"`
	MySummaryReport StaffSummaryReport `json:"MySummaryReport" gorm:"-"` //
}

func (m *LearnExamTask) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	var maxCodeNumber int64
	year := time.Now().Format("2006")
	model.DB().Model(&LearnExamTask{}).Select("COALESCE(MAX(CodeNumber), 0)").Where("TO_CHAR(CreatedAt, 'YYYY') = ?", year).Scan(&maxCodeNumber)
	m.CodeNumber = maxCodeNumber + 1
	m.TaskCode = fmt.Sprintf("task_%s_%04d", year, m.CodeNumber)
	return nil
}

func (m *LearnExamTask) Create() error {
	return model.DB().Create(&m).Error
}

func (m *LearnExamTask) TxCreate(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *LearnExamTask) Update() error {
	return model.DB().Updates(&m).Error
}

func (m *LearnExamTask) TxUpdate(tx *gorm.DB) error {
	return tx.Updates(&m).Error
}

func (m *LearnExamTask) Delete(id int64) error {
	return model.DB().Delete(&LearnExamTask{}, id).Error
}

// TestPaperIsExpiration 是否过期
func (m *LearnExamTask) TestPaperIsExpiration() bool {
	return time.Now().Unix() >= time.Time(m.TestPaperExpirationDate).Unix()
}

// CourseIsExpiration 是否过期
func (m *LearnExamTask) CourseIsExpiration() bool {
	return time.Now().Unix() >= time.Time(m.CourseExpirationDate).Unix()
}

func (m *LearnExamTask) IsModified() bool {
	if m.Id == 0 {
		return false
	}
	staffRecords, _ := (&StaffSummaryReport{}).TaskList(m.Id)
	if len(staffRecords) != 0 {
		return false
	} else {
		return true

	}
}

func (m *LearnExamTask) FindById(id int64) error {
	return model.DB().First(&m, id).Error
}

func (m *LearnExamTask) FindByTestPaperId(testPaperId int64) error {
	return model.DB().Model(&m).Where("TestPaperId = ?", testPaperId).Find(&m).Error
}

func (m *LearnExamTask) FindByCourseId(courseId int64) error {
	return model.DB().Model(&m).Where("CourseId = ?", courseId).Find(&m).Error
}

func (m *LearnExamTask) List(courseTitle, testPaperTitle, startTime, endTime string, categoryID, courseType int64, paginator model.Paginator) (data []LearnExamTask, totalCount int64, err error) {
	db := model.DB().Model(&LearnExamTask{})
	if courseTitle != "" {
		db = db.Where("CourseTitle LIKE ?", "%"+courseTitle+"%")
	}
	if testPaperTitle != "" {
		db = db.Where("TestPaperTitle LIKE ?", "%"+testPaperTitle+"%")
	}
	if categoryID != 0 {
		db = db.Where("CategoryId = ?", categoryID)
	}
	if courseType != 0 {
		db = db.Where("CourseType = ?", courseType)
	}
	if startTime != "" {
		db = db.Where("CreatedAt >= ?", startTime)
	}
	if endTime != "" {
		db = db.Where("CreatedAt <= ?", endTime)
	}
	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Offset(paginator.Offset).Limit(paginator.Limit)
	}
	err = db.Order("CreatedAt DESC").Find(&data).Error
	return
}

// PublishExamTask 发布考试任务
func (m *LearnExamTask) PublishExamTask(form AllocationForm, user *auth.AuthUser) error {
	m.TaskType = 2                         // 任务类型  1学习任务 2考试任务
	m.AllocationType = form.AllocationType //分配类型
	m.OpUserId = user.Id
	m.OpUserName = user.Name
	err := m.SetPublishTaskExamInfo(form.TestPaperId)
	if err != nil {
		return err
	}
	m.SetPublishTaskBaseInfo(form)
	tx := model.DB().Begin()
	err = m.TxCreate(tx)
	if err != nil {
		tx.Rollback()
		return errors.New("指派失败")
	}
	// 直接生成学习考试汇总表
	staffIdArr := strings.Split(m.StaffIds, ",")
	for _, staffIdStr := range staffIdArr {
		var staffSummaryReport StaffSummaryReport
		staffId, _ := strconv.ParseInt(staffIdStr, 10, 64)
		staff := rpc.GetStaffWithId(context.Background(), staffId)
		staffSummaryReport.Corporations.Build(staff.CorporationId)
		staffSummaryReport.StaffId = staffId
		staffSummaryReport.StaffName = staff.Name
		staffSummaryReport.JobNumber = staff.Code
		staffSummaryReport.TaskCode = m.TaskCode
		staffSummaryReport.CategoryId = m.CategoryId
		staffSummaryReport.TaskId = m.Id
		staffSummaryReport.TaskType = m.TaskType
		staffSummaryReport.TestPaperTitle = m.TestPaperTitle
		staffSummaryReport.TestPaperId = m.TestPaperId
		staffSummaryReport.TestPaperExpirationDate = m.TestPaperExpirationDate
		staffSummaryReport.ExamTotalCount = -99
		staffSummaryReport.ExamStatus = 2
		err = staffSummaryReport.TxCreate(tx)
		if err != nil {
			tx.Rollback()
			return errors.New("指派失败")
		}
	}
	tx.Commit()
	return nil
}

// PublishLearnTask  发布学习任务
func (m *LearnExamTask) PublishLearnTask(form AllocationForm, user *auth.AuthUser) error {
	m.TaskType = 1 // 任务类型  1学习任务 2考试任务

	m.AllocationType = form.AllocationType //分配类型
	m.OpUserId = user.Id
	m.OpUserName = user.Name
	course, err := m.SetPublishTaskCourseInfo(form.CourseId)
	if err != nil {
		return err
	}
	m.SetPublishTaskBaseInfo(form)
	tx := model.DB().Begin()
	err = m.TxCreate(tx)
	if err != nil {
		tx.Rollback()
		return errors.New("指派失败")
	}
	// 直接生成学习考试汇总表
	staffIdArr := strings.Split(m.StaffIds, ",")
	for _, staffIdStr := range staffIdArr {
		var staffSummaryReport StaffSummaryReport
		staffId, _ := strconv.ParseInt(staffIdStr, 10, 64)
		staff := rpc.GetStaffWithId(context.Background(), staffId)
		staffSummaryReport.Corporations.Build(staff.CorporationId)
		staffSummaryReport.StaffId = staffId
		staffSummaryReport.StaffName = staff.Name
		staffSummaryReport.JobNumber = staff.Code
		staffSummaryReport.TaskCode = m.TaskCode
		staffSummaryReport.CategoryId = m.CategoryId
		staffSummaryReport.TaskId = m.Id
		staffSummaryReport.TaskType = m.TaskType
		staffSummaryReport.TestPaperTitle = m.TestPaperTitle
		staffSummaryReport.TestPaperId = m.TestPaperId
		staffSummaryReport.TestPaperExpirationDate = m.TestPaperExpirationDate
		staffSummaryReport.ExamStatus = 2
		staffSummaryReport.CourseId = m.CourseId
		staffSummaryReport.CourseTitle = m.CourseTitle
		staffSummaryReport.CourseType = m.CourseType
		staffSummaryReport.CourseExpirationDate = course.ExpirationDate
		staffSummaryReport.LearnTotalCount = int64(len(course.EducationalResources))
		if m.TestPaperId != 0 {
			if course.AllowMakeupExam == 2 {
				staffSummaryReport.ExamTotalCount = 1
			} else {
				staffSummaryReport.ExamTotalCount = course.MakeupExamCount + 1
			}
		}
		err = staffSummaryReport.TxCreate(tx)
		if err != nil {
			tx.Rollback()
			return errors.New("指派失败")
		}
	}
	tx.Commit()
	return nil
}

func (m *LearnExamTask) SetPublishTaskCourseInfo(courseId int64) (course LearnCourse, err error) {
	err = course.FindById(courseId)
	if err != nil {
		err = errors.New("未找到试卷")
		return
	}
	if course.IsExpiration() {
		err = errors.New("课程已截止")
		return
	}
	m.CategoryId = course.CategoryId // 分类
	m.CourseId = course.Id
	m.CourseTitle = course.Title
	m.CourseType = course.CourseType
	m.CourseExpirationDate = course.ExpirationDate
	if course.NeedExamination == 1 {
		err = m.SetPublishTaskExamInfo(course.TestPaperId)
		return
	}
	return
}

func (m *LearnExamTask) SetPublishTaskExamInfo(testPaperId int64) error {
	var testPaper TestPaper
	err := testPaper.FindById(testPaperId)
	if err != nil {
		return errors.New("未找到试卷")
	}
	if testPaper.IsExpiration() {
		return errors.New("试卷已过期，无法指派")
	}
	m.TestPaperId = testPaper.Id
	m.TestPaperTitle = testPaper.Title
	m.CategoryId = testPaper.CategoryId // 分类
	m.TestPaperExpirationDate = testPaper.ExpirationDate
	return nil
}

func (m *LearnExamTask) SetPublishTaskBaseInfo(form AllocationForm) {
	if m.AllocationType == 1 {
		// 自选
		staffIds := util.Int64ArrToString(form.StaffIds)
		// 去重
		staffIdMap := make(map[string]byte)
		staffNameMap := make(map[string]byte)
		for _, staffId := range staffIds {
			staffIdMap[staffId] = 1
		}
		for _, staffName := range form.StaffNames {
			staffNameMap[staffName] = 1
		}
		// 去重后的
		var staffIdNew []string
		for key, _ := range staffIdMap {
			staffIdNew = append(staffIdNew, key)
		}
		var staffNames []string
		for key, _ := range staffNameMap {
			staffNames = append(staffNames, key)
		}
		m.StaffIds = strings.Join(staffIdNew, ",")
		m.StaffNames = strings.Join(staffNames, ",")
		m.StaffTotalCount = int64(len(staffIdNew))
	} else {
		GroupIds := util.Int64ArrToString(form.GroupIds)
		m.GroupIds = strings.Join(GroupIds, ",")
		groups := (&LearnGroup{}).ListByIds(form.GroupIds)
		staffIdMap := make(map[string]byte)
		staffNameMap := make(map[string]byte)
		for _, group := range groups {
			if group.StaffIds == "" {
				continue
			}
			staffIdArr := strings.Split(group.StaffIds, ",")
			for _, staffId := range staffIdArr {
				staffIdMap[staffId] = 0
			}
			staffNameArr := strings.Split(group.StaffNames, ",")
			for _, staffName := range staffNameArr {
				staffNameMap[staffName] = 0
			}
		}
		var staffIds []string
		for key, _ := range staffIdMap {
			staffIds = append(staffIds, key)
		}
		var staffNames []string
		for key, _ := range staffNameMap {
			staffNames = append(staffNames, key)
		}
		m.StaffIds = strings.Join(staffIds, ",")
		m.StaffNames = strings.Join(staffNames, ",")
		m.StaffTotalCount = int64(len(staffIds))
	}
}

// StartExam 开始考试
func (m *LearnExamTask) StartExam(taskId, userId int64) (staffExamRecordId int64, err error) {
	err = m.FindById(taskId)
	if err != nil {
		err = errors.New("未找到该指派任务")
		return
	}
	if m.TestPaperIsExpiration() {
		err = errors.New("试卷已过期无法考试")
		return
	}
	if m.TaskType == 1 && m.TestPaperId == 0 {
		err = errors.New("此学习任务不需要考试")
		return
	}
	var staffExamSummaryReport StaffSummaryReport
	_ = staffExamSummaryReport.Find(userId, m.Id)
	if staffExamSummaryReport.Id == 0 {
		err = errors.New("此考试任务，您无需考试")
		return
	}
	if staffExamSummaryReport.TaskType == 1 && (staffExamSummaryReport.LearnCount < staffExamSummaryReport.LearnTotalCount) {
		err = errors.New("学习课程未完成")
		return
	}
	var testPaper TestPaper
	_ = testPaper.FindById(m.TestPaperId)
	if staffExamSummaryReport.IsPass == 3 && testPaper.RepeatExam == 2 {
		// 通过且无法刷分的情况下 无法考试
		err = errors.New("您已通过考试，无法再考")
		return
	}
	if staffExamSummaryReport.ExamTotalCount != -99 && (staffExamSummaryReport.ExamCount >= staffExamSummaryReport.ExamTotalCount) {
		err = errors.New("已无考试次数")
		return
	}
	staff := rpc.GetStaffWithId(context.Background(), userId)
	if staff == nil {
		err = errors.New("找不到员工信息")
		return
	}
	tx := model.DB().Begin()
	var ExamType int64
	ExamType = 1 // 正常考试
	staffExamSummaryReport.ExamCount++
	if staffExamSummaryReport.IsPass == 3 {
		ExamType = 3 // 刷分
	}
	if staffExamSummaryReport.IsPass == 2 {
		// 上次是未通过 这次就是补考
		staffExamSummaryReport.ExamMakeUpCount++
		ExamType = 2 // 补考
	}
	if staffExamSummaryReport.ExamStatus == 2 {
		staffExamSummaryReport.ExamStatus = 1 // 变成补考中
	}
	err = staffExamSummaryReport.TxUpdate(tx)
	if err != nil {
		err = errors.New("未知错误,请联系管理员")
		tx.Rollback()
		return
	}
	var staffExamRecord StaffExamRecord
	staffExamRecord.StaffId = userId
	staffExamRecord.TaskId = m.Id
	staffExamRecord.TestPaperId = m.TestPaperId
	staffExamRecord.ExamType = ExamType
	staffExamRecord.IsPass = 1
	err = staffExamRecord.TxCreate(tx)
	if err != nil {
		err = errors.New("未知错误,请联系管理员")
		tx.Rollback()
		return
	}
	tx.Commit()
	return staffExamRecord.Id, nil
}

// StartLearn 开始学习
func (m *LearnExamTask) StartLearn(taskId, userId int64) (err error) {
	err = m.FindById(taskId)
	if err != nil {
		err = errors.New("未找到该指派任务")
		return
	}
	if m.CourseIsExpiration() {
		err = errors.New("课程已过期无法学习")
		return
	}
	var course LearnCourse
	_ = course.FindById(m.CourseId)
	if course.Id == 0 {
		err = errors.New("未找到课程")
		return
	}
	staff := rpc.GetStaffWithId(context.Background(), userId)
	if staff == nil {
		err = errors.New("找不到员工信息")
		return
	}
	var staffExamSummaryReport StaffSummaryReport
	_ = staffExamSummaryReport.Find(userId, m.Id)
	tx := model.DB().Begin()
	if staffExamSummaryReport.Id == 0 {
		staffExamSummaryReport.Corporations.Build(staff.CorporationId)
		staffExamSummaryReport.StaffId = userId
		staffExamSummaryReport.StaffName = staff.Name
		staffExamSummaryReport.JobNumber = staff.Code
		staffExamSummaryReport.TaskId = m.Id
		staffExamSummaryReport.TaskType = m.TaskType
		staffExamSummaryReport.CategoryId = m.CategoryId
		staffExamSummaryReport.TestPaperId = m.TestPaperId
		staffExamSummaryReport.TestPaperTitle = m.TestPaperTitle
		staffExamSummaryReport.CourseId = m.CourseId
		staffExamSummaryReport.CourseType = m.CourseType
		staffExamSummaryReport.CourseTitle = m.CourseTitle
		if m.TestPaperId != 0 {
			if course.AllowMakeupExam == 2 {
				staffExamSummaryReport.ExamTotalCount = 1
			} else {
				staffExamSummaryReport.ExamTotalCount = course.MakeupExamCount + 1
			}
		}
		err = staffExamSummaryReport.TxCreate(tx)
		if err != nil {
			err = errors.New("未知错误,请联系管理员")
			tx.Rollback()
			return
		}
	}
	// 生成学习记录
	if course.EducationalResources != nil {
		for _, resource := range course.EducationalResources {
			var staffLearnRecord StaffLearnRecord
			_ = staffLearnRecord.Find(staff.Id, m.Id, course.Id, resource.Id)
			if staffLearnRecord.Id == 0 {
				staffLearnRecord.StaffId = staff.Id
				staffLearnRecord.TaskId = m.Id
				staffLearnRecord.CourseId = course.Id
				staffLearnRecord.EducationalResourceId = resource.Id
				staffLearnRecord.EducationalResourceName = resource.Title
				staffLearnRecord.TotalLearnTime = resource.FinishTime
				staffLearnRecord.Status = 1 // 未完成
				err = staffLearnRecord.TxCreate(tx)
				if err != nil {
					tx.Rollback()
					return
				}
			}

		}
	}
	tx.Commit()
	return nil
}

// Summary 指派任务汇总
func (m *LearnExamTask) Summary() {
	summaryReports, _ := (&StaffSummaryReport{}).AllList(m.Id)
	var ExamFinish, ExamPass, ExamMakeUp, ExamNoPass, LearnFinish int64
	for _, report := range summaryReports {
		if !time.Time(report.ExamOver).IsZero() {
			ExamFinish++
		}
		if !time.Time(report.LearnOver).IsZero() {
			LearnFinish++
		}
		if report.IsPass == 3 {
			ExamPass++
		}
		if report.IsPass == 2 {
			ExamNoPass++
		}
		if report.ExamMakeUpCount != 0 {
			ExamMakeUp++
		}
	}
	m.ExamFinish = ExamFinish
	m.ExamPass = ExamPass
	m.ExamMakeUp = ExamMakeUp
	m.ExamNoPass = ExamNoPass
	m.LearnFinish = LearnFinish
	m.NeedSummary = 1
	_ = m.Update()
}

func (m *LearnExamTask) ChangeSummaryStatus(id int64) error {
	return model.DB().Model(&LearnExamTask{}).Where("Id = ?", id).Update("NeedSummary", 2).Error
}

// StaffSummaryReport 考试学习汇总表
type StaffSummaryReport struct {
	model.PkId
	model.Timestamp
	model.Corporations
	JobNumber  string `json:"JobNumber" gorm:"column:jobnumber;type:varchar;default:;comment:工号"`
	StaffName  string `json:"StaffName" gorm:"column:staffname;type:varchar;default:;comment:员工名称;"`
	StaffId    int64  `json:"StaffId" gorm:"column:staffid;type:bigint;default:;comment:员工id;uniqueIndex:staff_summary_staffid_taskid"`
	TaskId     int64  `json:"TaskId" gorm:"column:taskid;type:bigint;default:;comment:任务id;uniqueIndex:staff_summary_staffid_taskid"`
	TaskCode   string `json:"TaskCode" gorm:"column:taskcode;type:varchar;default:;comment:任务编号"`
	TaskType   int64  `json:"TaskType" gorm:"column:tasktype;type:smallint;default:;comment:1学习任务 2考试任务"`
	CategoryId int64  `json:"CategoryId" gorm:"column:categoryid;type:bigint;default:;comment:分类id"`
	// 考试汇总
	TestPaperId             int64           `json:"TestPaperId" gorm:"column:testpaperid;type:bigint;default:;comment:试卷id;"`
	TestPaperTitle          string          `json:"TestPaperTitle" gorm:"column:testpapertitle;type:varchar;default:;comment:试卷名称"`
	TestPaperExpirationDate model.LocalTime `json:"TestPaperExpirationDate" gorm:"column:testpaperexpirationdate;type:timestamp;default:;comment:试卷过期时间" validate:"required"`
	ExamOver                model.LocalTime `json:"ExamOver" gorm:"column:examover;type:timestamp;default:;comment:考试结束时间"`
	Score                   int64           `json:"Score" gorm:"column:score;type:integer;default:;comment:得分 *10"`
	IsPass                  int64           `json:"IsPass" gorm:"column:ispass;type:smallint;default:;comment:是否通过 1考试中 2未通过 3已通过"`
	ExamTotalCount          int64           `json:"ExamTotalCount" gorm:"column:examtotalcount;type:integer;comment:考试机会 -99可以无限考试"`
	ExamCount               int64           `json:"ExamCount" gorm:"column:examcount;type:integer;comment:已考次数"`
	ExamMakeUpCount         int64           `json:"ExamMakeUpCount" gorm:"column:exammakeupcount;type:integer;default:;comment:补考次数"`
	ExamStatus              int64           `json:"ExamStatus" gorm:"column:examstatus;type:smallint;default:;comment:考试状态 1补考 2正常考试 3可刷分 4已完成"`
	// 学习汇总
	CourseId             int64           `json:"CourseId" gorm:"column:courseid;type:bigint;default:;comment:课程id;"`
	CourseTitle          string          `json:"CourseTitle" gorm:"column:coursetitle;type:varchar;default:;comment:课程名称"`
	CourseType           int64           `json:"CourseType" gorm:"column:coursetype;type:smallint;default:1;comment:1必修 2选修" `
	CourseExpirationDate model.LocalTime `json:"CourseExpirationDate" gorm:"column:courseexpirationdate;type:timestamp;default:;comment:课程过期时间"`
	LearnOver            model.LocalTime `json:"LearnOver" gorm:"column:learnover;type:timestamp;default:;comment:学习完成时间"`
	LearnTotalCount      int64           `json:"LearnTotalCount" gorm:"column:learntotalcount;type:integer;comment:学习资料数量"`
	LearnCount           int64           `json:"LearnCount" gorm:"column:learncount;type:integer;comment:已完成学习资料数量"`

	StaffLearnRecords []StaffLearnRecord `json:"StaffLearnRecords" gorm:"-"`
	StaffExamRecords  []StaffExamRecord  `json:"StaffExamRecords" gorm:"-"`
	CorporationId     int64              `json:"CorporationId" gorm:"-"`
	CorporationName   string             `json:"CorporationName" gorm:"-"`
	Course            LearnCourse        `json:"Course" gorm:"foreignKey:CourseId"`
}

func (m *StaffSummaryReport) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *StaffSummaryReport) Create() error {
	return model.DB().Create(&m).Error
}

func (m *StaffSummaryReport) TxCreate(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *StaffSummaryReport) Update() error {
	return model.DB().Select("*").Updates(&m).Error
}

func (m *StaffSummaryReport) TxUpdate(tx *gorm.DB) error {
	return tx.Select("*").Updates(&m).Error
}

func (m *StaffSummaryReport) List(corporationIds []int64, categoryId, staffId, taskId, taskType, courseType, isPass int64, staffInfo, startTime, endTime, courseTitle, testPaperTitle string, paginator model.Paginator) (data []StaffSummaryReport, totalCount int64, err error) {
	db := model.DB().Model(&StaffSummaryReport{})
	if corporationIds != nil && len(corporationIds) != 0 {
		db = db.Scopes(model.WhereCorporations(corporationIds))
	}
	if categoryId != 0 {
		db = db.Where("CategoryId = ?", categoryId)
	}
	if staffId != 0 {
		db = db.Where("StaffId = ?", staffId)
	}
	if taskType != 0 {

		overTime := ""
		if taskType == 1 {
			overTime = "LearnOver"
			db = db.Where("TaskType = ?", taskType)
		} else {
			overTime = "ExamOver"
			db = db.Where("(TestPaperId != ? or TestPaperId IS NOT NULL)", 0)
		}
		if startTime != "" {
			db = db.Where(overTime+" >= ?", startTime)
		}
		if endTime != "" {
			db = db.Where(overTime+" <= ?", endTime)
		}
	}
	if courseType != 0 {
		db = db.Where("CourseType = ?", courseType)
	}
	if isPass != 0 {
		db = db.Where("IsPass = ?", isPass)
	}
	if taskId != 0 {
		db = db.Where("TaskId = ?", taskId)
	}
	if staffInfo != "" {
		db = db.Where("(StaffName LIKE ? OR JobNumber LIKE ?)", "%"+staffInfo+"%", "%"+staffInfo+"%")
	}
	if courseTitle != "" {
		db = db.Where("CourseTitle LIKE ?", "%"+courseTitle+"%")
	}
	if testPaperTitle != "" {
		db = db.Where("TestPaperTitle LIKE ?", "%"+testPaperTitle+"%")
	}
	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Limit(paginator.Limit).Offset(paginator.Offset)
	}
	err = db.Order("CreatedAt DESC").Find(&data).Error
	return
}

func (m *StaffSummaryReport) AllList(taskId int64) (data []StaffSummaryReport, err error) {
	db := model.DB().Where("TaskId = ?", taskId)
	err = db.Find(&data).Error
	return
}

func (m *StaffSummaryReport) TaskList(taskId int64) (data []StaffSummaryReport, err error) {
	db := model.DB().Where("TaskId = ?", taskId).Where("CourseExpirationDate > ?", time.Now())
	err = db.Find(&data).Error
	return
}

func (m *StaffSummaryReport) LearnList(staffId int64, year string, categoryIds, courseTypes, courseStatus []int64, expirationDateSort, learnSort string, paginator model.Paginator) (data []StaffSummaryReport, totalCount int64, err error) {
	db := model.DB().Model(&StaffSummaryReport{}).Where("StaffId = ?", staffId).Where("TaskType = 1").Where("CourseExpirationDate > ?", time.Now())
	if year != "" {
		db = db.Where("TO_CHAR(CourseExpirationDate, 'YYYY') = ?", year)
	}
	if categoryIds != nil || len(categoryIds) != 0 {
		db = db.Where("CategoryId IN (?)", categoryIds)
	}
	if courseTypes != nil || len(courseTypes) != 0 {
		db = db.Where("CourseType IN (?)", courseTypes)
	}
	var wh []string
	var params []interface{}
	if util.IncludeInt64(courseStatus, 1) {
		wh = append(wh, "LearnCount >= LearnTotalCount")
	}
	if util.IncludeInt64(courseStatus, 2) {
		wh = append(wh, "LearnCount < LearnTotalCount")
	}
	if len(wh) > 0 {
		db = db.Where(strings.Join(wh, " OR "), params...)
	}
	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Limit(paginator.Limit).Offset(paginator.Offset)
	}
	db = db.Order("CourseType ASC")
	if learnSort == "" {
		learnSort = "ASC"
	} else {
		if learnSort != "ASC" && learnSort != "DESC" {
			learnSort = "ASC"
		}
	}
	db = db.Order("CAST(LearnCount AS FLOAT) / NULLIF(LearnTotalCount, 0) " + learnSort)
	if expirationDateSort == "" {
		expirationDateSort = "DESC"
	} else {
		if expirationDateSort != "ASC" && expirationDateSort != "DESC" {
			expirationDateSort = "ASC"
		}
	}
	db = db.Order("CourseExpirationDate " + learnSort)
	err = db.Preload("Course").Find(&data).Error
	return
}

func (m *StaffSummaryReport) ExamList(staffId int64, year string, categoryIds, ExamStatus []int64, paginator model.Paginator) (data []StaffSummaryReport, totalCount int64, err error) {
	db := model.DB().Model(&StaffSummaryReport{}).Where("StaffId = ?", staffId).Where("TestPaperExpirationDate > ?", time.Now()).Where("LearnTotalCount = LearnCount")
	if year != "" {
		db = db.Where("TO_CHAR(TestPaperExpirationDate, 'YYYY') = ?", year)
	}
	if categoryIds != nil || len(categoryIds) != 0 {
		db = db.Where("CategoryId IN (?)", categoryIds)
	}
	if ExamStatus != nil || len(ExamStatus) != 0 {
		db = db.Where("ExamStatus IN (?)", ExamStatus)
	}
	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Limit(paginator.Limit).Offset(paginator.Offset)
	}
	db = db.Order("ExamStatus ASC").Order("TestPaperExpirationDate DESC")
	err = db.Find(&data).Error
	return
}

func (m *StaffSummaryReport) Find(staffId, taskId int64) error {
	return model.DB().Where("StaffId = ?", staffId).Where("TaskId = ?", taskId).Find(&m).Error
}

// ExamSummary 考试汇总
func (m *StaffSummaryReport) ExamSummary(scoreType, repeatExam int64) error {
	examRecords, _ := (&StaffExamRecord{}).ExamOverList(m.StaffId, m.TaskId)
	var maxScore int64    // 最高分数
	var examCount int64   // 已考次数
	var makeUpCount int64 // 补考次数
	var isPass int64
	var lastScore int64
	firstRecord := true
	isPass = 2
	for index, record := range examRecords {
		examCount++ // 考试次数加1
		if firstRecord {
			if record.IsPass == 3 && scoreType == 2 {
				m.Score = record.Score // 去取最终分
				firstRecord = false
			}
		}
		// 取最高分数
		if record.Score > maxScore {
			maxScore = record.Score
		}
		// 补考次数
		if record.ExamType == 2 {
			makeUpCount++
		}
		if record.IsPass == 3 {
			m.ExamOver = record.UpdatedAt // 考试通过时间
			isPass = 3
		}
		if index == len(examRecords)-1 {
			lastScore = record.Score
		}
	}
	m.ExamCount = examCount
	m.ExamMakeUpCount = makeUpCount
	m.IsPass = isPass
	if scoreType == 1 {
		m.Score = maxScore
	} else {
		if m.Score == 0 {
			m.Score = lastScore
		}
	}

	if isPass == 3 {
		if repeatExam == 1 {
			m.ExamStatus = 3 // 刷分状态
		} else {
			m.ExamStatus = 4 // 已完成
		}
	} else {
		if m.ExamTotalCount == -99 {
			m.ExamStatus = 1 // 补考
		} else {
			if m.ExamCount >= m.ExamTotalCount {
				m.ExamStatus = 4 // 已完成
			}
		}
	}

	return m.Update()
}

// LearnSummary 学习汇总
func (m *StaffSummaryReport) LearnSummary() error {
	learnRecords, _ := (&StaffLearnRecord{}).List(m.StaffId, m.TaskId)
	var finishLearn int64
	for _, record := range learnRecords {
		if record.Status == 2 {
			finishLearn++
		}
	}
	m.LearnCount = finishLearn
	if m.LearnCount >= m.LearnTotalCount {
		m.LearnOver = model.LocalTime(time.Now())
	}
	return m.Update()
}

// StaffExamRecord 考试记录表
type StaffExamRecord struct {
	model.PkId
	model.Timestamp
	StaffId     int64 `json:"StaffId" gorm:"column:staffid;type:bigint;default:;comment:员工id;index:staff_exam_taskid_staffid"`
	TaskId      int64 `json:"TaskId" gorm:"column:taskid;type:bigint;default:;comment:学习任务;index:staff_exam_taskid_staffid"`
	TestPaperId int64 `json:"TestPaperId" gorm:"column:testpaperid;type:bigint;default:;comment:试卷id"`
	ExamType    int64 `json:"ExamType" gorm:"column:examtype;type:integer;default:;comment:考试性质 1正常考试 2补考 3刷分"`
	Score       int64 `json:"Score" gorm:"column:score;type:integer;default:;comment:得分 *10"`
	IsPass      int64 `json:"IsPass" gorm:"column:ispass;type:smallint;default:;comment:是否通过 1考试中/中途退出 2未通过 3已通过"`

	//StaffExamRecordLines []StaffExamRecordLine `json:"StaffExamRecordLines" gorm:"foreignKey:StaffExamRecordId"`
}

func (m *StaffExamRecord) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *StaffExamRecord) Create() error {
	return model.DB().Create(&m).Error
}

func (m *StaffExamRecord) TxCreate(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *StaffExamRecord) Update() error {
	return model.DB().Updates(&m).Error
}

func (m *StaffExamRecord) TxUpdate(tx *gorm.DB) error {
	return tx.Updates(&m).Error
}

func (m *StaffExamRecord) List(staffId, taskId int64) (data []StaffExamRecord, err error) {
	err = model.DB().Model(&StaffExamRecord{}).Where("StaffId = ?", staffId).Where("TaskId = ?", taskId).Order("CreatedAt DESC").Find(&data).Error
	return
}

func (m *StaffExamRecord) ExamOverList(staffId, taskId int64) (data []StaffExamRecord, err error) {
	err = model.DB().Model(&StaffExamRecord{}).Where("StaffId = ?", staffId).Where("TaskId = ?", taskId).Where("IsPass != ?", 1).Order("CreatedAt DESC").Find(&data).Error
	return
}

func (m *StaffExamRecord) FindById(Id int64) error {
	return model.DB().First(&m, Id).Error
}

func (m *StaffExamRecord) FindLatest(staffId, taskId int64) (err error) {
	err = model.DB().Model(&StaffExamRecord{}).Where("StaffId = ?", staffId).Where("TaskId = ?", taskId).Where("IsPass != ?", 1).Order("CreatedAt DESC").Find(&m).Error
	return
}

// StaffExamRecordLine 考试记录表明细
type StaffExamRecordLine struct {
	model.PkId
	model.Timestamp
	StaffExamRecordId int64  `json:"StaffExamRecordId" gorm:"column:staffexamrecordid;type:bigint;default:;comment:员工考试记录表id"`
	TopicId           int64  `json:"TopicId" gorm:"column:topicid;type:bigint;default:;comment:题目id"`
	ChooseAnswer      string `json:"ChooseAnswer" gorm:"column:chooseanswer;type:varchar;default:;comment:选择答案"`
	Score             int64  `json:"Score" gorm:"column:score;type:integer;default:;comment:题目得分 *10"`
	IsRight           int64  `json:"IsRight" gorm:"column:isright;type:smallint;default:;comment:是否正确 1正确 2部分正确 3错误"`
}

func (m *StaffExamRecordLine) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *StaffExamRecordLine) Create() error {
	return model.DB().Create(&m).Error
}

func (m *StaffExamRecordLine) Update() error {
	return model.DB().Updates(&m).Error
}

func (m *StaffExamRecordLine) List(staffExamRecordId int64) (data []StaffExamRecordLine, err error) {
	err = model.DB().Where("StaffExamRecordId = ?", staffExamRecordId).Find(&data).Error
	return
}

// StaffLearnRecord 学习记录表
type StaffLearnRecord struct {
	model.PkId
	model.Timestamp
	StaffId                 int64  `json:"StaffId" gorm:"column:staffid;type:bigint;default:;comment:员工id;uniqueIndex:staff_learn_record_staff_task_course_resource;"`
	TaskId                  int64  `json:"TaskId" gorm:"column:taskid;type:bigint;default:;comment:学习任务;uniqueIndex:staff_learn_record_staff_task_course_resource"`
	CourseId                int64  `json:"CourseId" gorm:"column:courseid;type:bigint;default:;comment:课程id;uniqueIndex:staff_learn_record_staff_task_course_resource"`
	EducationalResourceId   int64  `json:"EducationalResourceId" gorm:"column:educationalresourceid;type:bigint;default:;comment:学习资料id;uniqueIndex:staff_learn_record_staff_task_course_resource"`
	EducationalResourceName string `json:"EducationalResourceName" gorm:"column:educationalresourcename;type:varchar;default:;comment:学习资料名称"`
	TotalLearnTime          int64  `json:"TotalLearnTime" gorm:"column:totallearntime;type:integer;default:;comment:总学习时间 秒"`
	LearnTime               int64  `json:"LearnTime" gorm:"column:learntime;type:integer;default:;comment:学习时间 秒"`
	Status                  int64  `json:"Status" gorm:"column:status;type:smallint;default:;comment:1未完成 2已完成"`
}

func (m *StaffLearnRecord) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *StaffLearnRecord) Create() error {
	return model.DB().Create(&m).Error
}

func (m *StaffLearnRecord) TxCreate(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *StaffLearnRecord) Update() error {
	return model.DB().Updates(&m).Error
}

func (m *StaffLearnRecord) TxUpdate(tx *gorm.DB) error {
	return tx.Updates(&m).Error
}

func (m *StaffLearnRecord) List(staffId, taskId int64) (data []StaffLearnRecord, err error) {
	err = model.DB().Model(&StaffLearnRecord{}).Where("StaffId = ?", staffId).Where("TaskId = ?", taskId).Find(&data).Error
	return
}

func (m *StaffLearnRecord) Find(taskId, staffId, courseId, educationalResourceId int64) error {
	return model.DB().
		Where("StaffId = ?", staffId).
		Where("TaskId = ?", taskId).
		Where("CourseId = ?", courseId).
		Where("EducationalResourceId = ?", educationalResourceId).Find(&m).Error
}
