package scheduler

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type Scheduler struct {
	model.PkId
	model.GroupCorporation
	Scene      string     `json:"Scene" gorm:"column:scene;type:varchar;comment:场景值"`                         //场景值  labor_contract_expire_notify->劳动合同到期提醒  probation_expire_notify->试用期到期提醒
	BeforeDay  int64      `json:"BeforeDay" gorm:"column:beforeday;type:smallint;comment:过期前多少天发送提醒"`         //过期前多少天发送提醒
	NotifyTime int64      `json:"NotifyTime" gorm:"column:notifytime;type:integer;comment:发送通知的时间点，当天的第多少分钟"` //发送通知的时间点，当天的第多少分钟 390->6:30
	Param      model.JSON `json:"Param" gorm:"column:param;type:json;comment:其他相关参数"`                         //其他相关参数
	model.OpUser
	model.Timestamp
}

func (s *Scheduler) BeforeCreate(tx *gorm.DB) error {
	s.Id = model.Id()
	return nil
}

func (s *Scheduler) Create(schedulers []Scheduler) error {
	return model.DB().Create(schedulers).Error
}

func (s *Scheduler) GetByGroupAndScene(groupId int64, scene string) []Scheduler {
	var schedulers []Scheduler
	model.DB().Model(&Scheduler{}).Where("GroupId = ? AND Scene = ?", groupId, scene).Find(&schedulers)
	return schedulers
}

func (s *Scheduler) GetByScene(scene string) []Scheduler {
	var schedulers []Scheduler
	model.DB().Model(&Scheduler{}).Where("Scene = ?", scene).Find(&schedulers)
	return schedulers
}

func (s *Scheduler) Update(schedulers []Scheduler, groupId int64, scene string) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Where("GroupId = ? AND Scene = ?", groupId, scene).Delete(&Scheduler{}).Error
		if err != nil {
			return err
		}
		err = tx.Create(schedulers).Error
		return err
	})
}
