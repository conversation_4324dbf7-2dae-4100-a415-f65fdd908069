package operation

import (
	"app/org/scs/erpv2/api/model"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type LineBaseDayConfig struct {
	model.PkId
	model.Corporations
	model.OpUser
	model.Timestamp
	Code        string          `json:"Code" gorm:"column:code;type:varchar;comment:编码" `
	LineId      int64           `json:"LineId" gorm:"column:lineid;type:bigint;comment:线路ID" validate:"required"`
	LineName    string          `json:"LineName" gorm:"column:linename;type:varchar;comment:线路"`
	BaseDay     int64           `json:"BaseDay" gorm:"column:baseday;type:bigint;comment:天数 * 10" validate:"required"`
	ApplyStatus int64           `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;comment:审批状态 1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃;default:0"`
	UseStartAt  model.LocalTime `json:"UseStartAt" gorm:"column:usestartat;type:timestamp;comment:生效开始日期"`
	UseEndAt    model.LocalTime `json:"UseEndAt" gorm:"column:useendat;type:timestamp;comment:生效结束日期"`
	Reason      string          `json:"Reason" gorm:"column:reason;type:varchar;comment:修改原因"`

	ScrapDate     model.LocalTime `json:"ScrapDate" gorm:"column:scrapdate;type:timestamp;comment:作废时间 为空-未报废;"`
	ScrapReason   string          `json:"ScrapReason" gorm:"column:scrapreason;type:varchar(255);comment:报废原因"`
	ScrapUserId   int64           `json:"ScrapUserId" gorm:"column:scrapuserid;type:bigint;comment:报废操作人员id"`
	ScrapUserName string          `json:"ScrapUserName" gorm:"column:scrapusername;type:varchar;comment:报废操作人员姓名"`

	CorporationName string `json:"CorporationName" gorm:"-"`
	CurrentHandler  string `json:"CurrentHandler" gorm:"-"`
	ProcessId       string `json:"ProcessId" gorm:"-"`
}

func (m *LineBaseDayConfig) TableName() string {
	return "line_base_day_configs"
}

func (m *LineBaseDayConfig) ApplyStatusFieldName() string {
	return "applystatus"
}

func (m *LineBaseDayConfig) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	m.Code = fmt.Sprintf("%d", m.Id)
	return nil
}

func (m *LineBaseDayConfig) Create() error { return model.DB().Create(&m).Error }

func (m *LineBaseDayConfig) TxCreate(tx *gorm.DB) error { return tx.Create(&m).Error }

func (m *LineBaseDayConfig) Updates() error { return model.DB().Updates(&m).Error }

func (m *LineBaseDayConfig) TxUpdates(tx *gorm.DB) error { return tx.Updates(&m).Error }

func (m *LineBaseDayConfig) GetById(id int64) error { return model.DB().First(&m, id).Error }

func (m *LineBaseDayConfig) List(qs *model.Qs, corporationId int64, pagination model.Paginator) (data []LineBaseDayConfig, totalCount int64, err error) {
	subQuery := model.DB().Model(&LineBaseDayConfig{}).Select("*", "row_number() over (partition by LineId,FleetId,BranchId,CompanyId order by CreatedAt desc,Id desc)")
	tx := qs.Format().Table("(?) AS tmp", subQuery).Where("row_number = ?", 1)
	if corporationId > 0 {
		tx.Scopes(model.WhereCorporation(corporationId))
	}
	tx.Count(&totalCount)
	err = tx.Scopes(model.PaginationScope(pagination)).Order(" CreatedAt DESC").Find(&data).Error
	return
}

func (m *LineBaseDayConfig) Records(qs *model.Qs, corporationId int64, pagination model.Paginator) (data []LineBaseDayConfig, totalCount int64, err error) {
	db := qs.Format().Model(&LineBaseDayConfig{})
	if corporationId > 0 {
		db.Scopes(model.WhereCorporation(corporationId))
	}
	db.Count(&totalCount)
	err = db.Scopes(model.PaginationScope(pagination)).Order("CreatedAt DESC").Find(&data).Error
	return
}

func (m *LineBaseDayConfig) queryOne(qs *model.Qs, corporationId int64) (data LineBaseDayConfig, err error) {
	list, _, err := m.List(qs, corporationId, model.NoPagination)
	if list != nil && len(list) > 0 {
		data = list[0]
	}
	return
}

func (m *LineBaseDayConfig) FindLatest(lineId int64) (data LineBaseDayConfig) {
	now := time.Now()
	date := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Format("2006-01-02")
	model.DB().Model(&LineBaseDayConfig{}).
		Where("LineId = ?", lineId).
		Where("ApplyStatus = ?", 2). // 只取审批通过的
		Where("UseStartAt <= ?", date).
		Where("UseEndAt >= ?", date).
		Order("CreatedAt DESC"). // 按创建时间倒序
		First(&data)
	return
}
