package operation

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type DriverIdentification struct {
	model.PkId
	model.Timestamp
	model.Corporations
	model.OpUser
	DriverId      int64           `json:"DriverId" gorm:"column:driverid;type:bigint;default:;comment:司机id" validate:"required"`
	DriverName    string          `json:"DriverName" gorm:"column:drivername;type:varchar;default:;comment:司机名称" validate:"required"`
	Phone         string          `json:"Phone" gorm:"column:phone;type:varchar;default:;comment:手机号码" validate:"required"`
	DriverStatus  int64           `json:"DriverStatus" gorm:"column:driverstatus;type:smallint;default:;comment:人员状态0:未知 1-在职,2-离职,3-试用期,4-退休,5-退休返聘" validate:"required"`
	EffectiveDate model.LocalTime `json:"EffectiveDate" gorm:"column:effectivedate;type:timestamp;default:;comment:生效日期" validate:"required"`
	ExpireDate    model.LocalTime `json:"ExpireDate" gorm:"column:expiredate;type:timestamp;default:;comment:失效日期" validate:"required"`
	CardType      int64           `json:"CardType" gorm:"column:cardtype;type:smallint;default:;comment:证件类型 1驾驶证 2从业资格证" validate:"required"`
	CardNumber    string          `json:"CardNumber" gorm:"column:cardnumber;type:varchar;default:;comment:证件号码;uniqueIndex:driveridentification_cardnumber" validate:"required"`
	CardStatus    int64           `json:"CardStatus" gorm:"column:cardstatus;type:smallint;default:;comment:证件状态 1正常 2预警 3作废"`
	Attachment    model.JSON      `json:"Attachment" gorm:"column:attachment;type:json;default:;comment:附件"`
	//
	CorporationName string `json:"CorporationName" gorm:"-"`
	CorporationId   int64  `json:"CorporationId" gorm:"-" validate:"required"`
}

func (m *DriverIdentification) BeforeSave(db *gorm.DB) error {
	expireDate := m.ExpireDate.ToTime()
	expireDate = time.Date(expireDate.Year(), expireDate.Month(), expireDate.Day(), 23, 59, 59, 0, time.Local)
	m.ExpireDate = model.LocalTime(expireDate)
	m.SetCardStatus()
	return nil
}

func (m *DriverIdentification) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *DriverIdentification) SetCardStatus() {
	expireDate := m.ExpireDate.ToTime()
	now := time.Now().Unix()
	expireDateMonthAgo := expireDate.AddDate(0, -2, 0)
	if now >= expireDate.Unix() {
		m.CardStatus = 3
	} else if now >= expireDateMonthAgo.Unix() {
		m.CardStatus = 2
	} else {
		m.CardStatus = 1
	}
}

func (m *DriverIdentification) CardTypeText() string {
	if m.CardType == 1 {
		return "驾驶证"
	} else if m.CardType == 2 {
		return "从业资格证"
	}
	return ""
}

func (m *DriverIdentification) Create() error { return model.DB().Create(&m).Error }

func (m *DriverIdentification) TxCreate(tx *gorm.DB) error { return tx.Create(&m).Error }

func (m *DriverIdentification) Updates() error { return model.DB().Updates(&m).Error }

func (m *DriverIdentification) TxUpdates(tx *gorm.DB) error { return tx.Updates(&m).Error }

func (m *DriverIdentification) Delete(id int64) error {
	return model.DB().Delete(&DriverIdentification{}, id).Error
}

func (m *DriverIdentification) GetById(id int64) error { return model.DB().First(&m, id).Error }

func (m *DriverIdentification) List(qs *model.Qs, corporationId int64, pagination model.Paginator) (data []DriverIdentification, totalCount int64, err error) {
	db := qs.Format().Model(&DriverIdentification{})
	if corporationId != 0 {
		db.Scopes(model.WhereCorporation(corporationId))
	}
	db.Count(&totalCount)
	err = db.Scopes(model.PaginationScope(pagination)).Order("CreatedAt DESC").Find(&data).Error
	return
}

func (m *DriverIdentification) QueryOne(qs *model.Qs, corporationId int64) (data DriverIdentification, err error) {
	list, _, err := m.List(qs, corporationId, model.NoPagination)
	if list != nil && len(list) > 0 {
		data = list[0]
	}
	return
}
