package operation

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"strings"
	"time"
)

type LineVehicleMileageReport struct {
	model.PkId
	TopCorporationId              int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	CorporationId                 int64           `json:"CorporationId" gorm:"column:corporationid;type:bigint;default:0;comment:线路所属的机构（车队）ID;index:corp_lineid_reportat_index"`
	CorporationName               string          `json:"CorporationName" gorm:"column:corporationname;type:varchar;default:;comment:线路所属的机构（车队）"`
	LineId                        int64           `json:"LineId" gorm:"column:lineid;type:integer;default:0;comment:线路ID;index:corp_lineid_reportat_index"`
	LineName                      string          `json:"LineName" gorm:"column:linename;type:varchar(100);default:;comment:线路名称"`
	DriverHasLineId               int64           `json:"DriverHasLineId" gorm:"column:driverhaslineid;type:integer;default:0;comment:司机所属的线路ID;"`
	DriverHasLineName             string          `json:"DriverHasLineName" gorm:"column:driverhaslinename;type:varchar(100);default:;comment:司机所属的线路名称"`
	DriverCorporationId           int64           `json:"DriverCorporationId" gorm:"column:drivercorporationid;type:bigint;default:0;comment:司机所属的机构ID;"`
	ReportAt                      model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:报表日期;index:corp_lineid_reportat_index"`
	VehicleId                     int64           `json:"VehicleId" gorm:"column:vehicleid;type:integer;default:0;comment:车辆ID"`
	License                       string          `json:"License" gorm:"column:license;type:varchar(100);default:;comment:车牌号"`
	DriverId                      int64           `json:"DriverId" gorm:"column:driverid;type:integer;default:0;comment:司机ID"`
	DriverName                    string          `json:"DriverName" gorm:"column:drivername;type:varchar(100);default:;comment:司机姓名"`
	FrequencyIndex                string          `json:"FrequencyIndex" gorm:"column:frequencyindex;type:varchar(50);default:;comment:班次"`
	WorkDayCount                  int64           `json:"WorkDayCount" gorm:"column:workdaycount;type:integer;default:0;comment:司机出勤天数 单位：天数*10"`
	VehicleWorkDayCount           int64           `json:"VehicleWorkDayCount" gorm:"column:vehicleworkdaycount;type:integer;default:0;comment:车辆出勤天数 单位：天数*10"`
	FrequencyType                 int64           `json:"FrequencyType" gorm:"column:frequencytype;type:smallint;default:0;comment:出勤类型 1:全班 2:半班 3:机动 4:大机动"`
	WorkType                      int64           `json:"WorkType" gorm:"column:worktype;type:smallint;default:0;comment:班制 1双班  2单班 3混合班  对应line_settings表的FrequencyType字段"`
	FollowVehicleDay              int64           `json:"FollowVehicleDay" gorm:"column:followvehicleday;type:integer;default:0;comment:跟车天数 单位：天数*10"`
	FullWorkDay                   int64           `json:"FullWorkDay" gorm:"column:fullworkday;type:integer;default:0;comment:全班天数 单位：天数*10"`
	HalfWorkDay                   int64           `json:"HalfWorkDay" gorm:"column:halfworkday;type:integer;default:0;comment:半班天数 单位：天数*10"`
	MotorWorkDay                  int64           `json:"MotorWorkDay" gorm:"column:motorworkday;type:integer;default:0;comment:小机动天数 单位：天数*10"`
	MotorBigWorkDay               int64           `json:"MotorBigWorkDay" gorm:"column:motorbigworkday;type:integer;default:0;comment:大机动天数 单位：天数*10"`
	FullRatedMileage              int64           `json:"FullRatedMileage" gorm:"column:fullratedmileage;type:integer;default:0;comment:全程核定单圈公里 单位：米"`
	FullPlanCircle                int64           `json:"FullPlanCircle" gorm:"column:fullplancircle;type:integer;default:0;comment:全程计划圈次 单位：圈次*10"`
	FullDoneCircle                int64           `json:"FullDoneCircle" gorm:"column:fulldonecircle;type:integer;default:0;comment:全程实际完成圈次 单位：圈次*10"`
	RangeRatedMileage             int64           `json:"RangeRatedMileage" gorm:"column:rangeratedmileage;type:integer;default:0;comment:区间核定单圈公里 单位：米"`
	RangePlanCircle               int64           `json:"RangePlanCircle" gorm:"column:rangeplancircle;type:integer;default:0;comment:区间计划圈次 单位：圈次*10"`
	RangeDoneCircle               int64           `json:"RangeDoneCircle" gorm:"column:rangedonecircle;type:integer;default:0;comment:区间实际完成圈次 单位：圈次*10"`
	FixVehicleCircle              int64           `json:"FixVehicleCircle" gorm:"column:fixvehiclecircle;type:integer;default:0;comment:修车圈次 单位：圈次*10"`
	CasualLeaveCircle             int64           `json:"CasualLeaveCircle" gorm:"column:casualleavecircle;type:integer;default:0;comment:事假圈次 单位：圈次*10"`
	AccidentDisputeCircle         int64           `json:"AccidentDisputeCircle" gorm:"column:accidentdisputecircle;type:integer;default:0;comment:事故纠纷圈次 单位：圈次*10"`
	AnnualReviewCircle            int64           `json:"AnnualReviewCircle" gorm:"column:annualreviewcircle;type:integer;default:0;comment:年审圈次 单位：圈次*10"`
	OfficialWorkCircle            int64           `json:"OfficialWorkCircle" gorm:"column:officialworkcircle;type:integer;default:0;comment:公务出勤圈次 单位：圈次*10"`
	CharterBusCircle              int64           `json:"CharterBusCircle" gorm:"column:charterbuscircle;type:integer;default:0;comment:包车圈次 单位：圈次*10"`
	SickLeaveCircle               int64           `json:"SickLeaveCircle" gorm:"column:sickleavecircle;type:integer;default:0;comment:病假圈次 单位：圈次*10"`
	AnnualLeaveCircle             int64           `json:"AnnualLeaveCircle" gorm:"column:annualleavecircle;type:integer;default:0;comment:年休假圈次 单位：圈次*10"`
	TrafficJamCircle              int64           `json:"TrafficJamCircle" gorm:"column:trafficjamcircle;type:integer;default:0;comment:堵车圈次 单位：圈次*10"`
	RestCircle                    int64           `json:"RestCircle" gorm:"column:restcircle;type:integer;default:0;comment:疗休养圈次 单位：圈次*10"`
	MaintenanceCircle             int64           `json:"MaintenanceCircle" gorm:"column:maintenancecircle;type:integer;default:0;comment:保养圈次 单位：圈次*10"`
	AddGasCircle                  int64           `json:"AddGasCircle" gorm:"column:addgascircle;type:integer;default:0;comment:加气圈次 单位：圈次*10"`
	NightBefore22WorkTimeLength   int64           `json:"NightBefore22WorkTimeLength" gorm:"column:nightbefore22worktimelength;type:integer;default:0;comment:22点前夜班时长 单位：秒"`
	NightAfter22WorkTimeLength    int64           `json:"NightAfter22WorkTimeLength" gorm:"column:nightafter22worktimelength;type:integer;default:0;comment:22点后夜班时长 单位：秒"`
	NightTotalWorkTimeLength      int64           `json:"NightTotalWorkTimeLength" gorm:"column:nighttotalworktimelength;type:integer;default:0;comment:夜班总时长 单位：秒"`
	DelayInParkingTimeLength      int64           `json:"DelayInParkingTimeLength" gorm:"column:delayinparkingtimelength;type:integer;default:0;comment:延迟进场时长 单位：秒"`
	CircleMileage                 int64           `json:"CircleMileage" gorm:"column:circlemileage;type:integer;default:0;comment:圈次公里 单位：米"`
	StopWorkRatedMileage          int64           `json:"StopWorkRatedMileage" gorm:"column:stopworkratedmileage;type:integer;default:0;comment:自定义核定公里 单位：米"`
	FullInOutDepotMileage         int64           `json:"FullInOutDepotMileage" gorm:"column:fullinoutdepotmileage;type:integer;default:0;comment:全程进出场公里 单位：米"`
	FullAssistantMileage          int64           `json:"FullAssistantMileage" gorm:"column:fullassistantmileage;type:integer;default:0;comment:全程辅助公里 单位：米"`
	RangeInOutDepotMileage        int64           `json:"RangeInOutDepotMileage" gorm:"column:rangeinoutdepotmileage;type:integer;default:0;comment:区间进出场公里 单位：米"`
	RangeAssistantMileage         int64           `json:"RangeAssistantMileage" gorm:"column:rangeassistantmileage;type:integer;default:0;comment:区间辅助公里 单位：米"`
	CharterBusMileage             int64           `json:"CharterBusMileage" gorm:"column:charterbusmileage;type:integer;default:0;comment:包车公里 单位：米"`
	AnnualReviewMileage           int64           `json:"AnnualReviewMileage" gorm:"column:annualreviewmileage;type:integer;default:0;comment:年审公里 单位：米"`
	TrafficJamMileage             int64           `json:"TrafficJamMileage" gorm:"column:trafficjammileage;type:integer;default:0;comment:堵车公里 单位：米"`
	TotalMileage                  int64           `json:"TotalMileage" gorm:"column:totalmileage;type:integer;default:0;comment:合计公里 单位：米"`
	FullRatedWorkTimeLength       int64           `json:"FullRatedWorkTimeLength" gorm:"column:fullratedworktimelength;type:integer;default:0;comment:全程核定单圈岗上工时 单位：秒"`
	FullRatedNotWorkTimeLength    int64           `json:"FullRatedNotWorkTimeLength" gorm:"column:fullratednotworktimelength;type:integer;default:0;comment:全程核定单圈岗下工时 单位：秒"`
	FullCircleWorkTimeLength      int64           `json:"FullCircleWorkTimeLength" gorm:"column:fullcircleworktimelength;type:integer;default:0;comment:全程圈次岗上工时 单位：秒"`
	FullCircleNotWorkTimeLength   int64           `json:"FullCircleNotWorkTimeLength" gorm:"column:fullcirclenotworktimelength;type:integer;default:0;comment:全程圈次岗下工时 单位：秒"`
	FullInOutDepotTime            int64           `json:"FullInOutDepotTime" gorm:"column:fullinoutdepottime;type:integer;default:0;comment:全程进出场时长 单位：秒"`
	FullAssistantTime             int64           `json:"FullAssistantTime" gorm:"column:fullassistanttime;type:integer;default:0;comment:全程辅助时长 单位：秒"`
	FullStopWorkTimeLength        int64           `json:"FullStopWorkTimeLength" gorm:"column:fullstopworktimelength;type:integer;default:0;comment:全程自定义岗上时长 单位：秒"`
	FullStopNotWorkTimeLength     int64           `json:"FullStopNotWorkTimeLength" gorm:"column:fullstopnotworktimelength;type:integer;default:0;comment:全程自定义岗下时长 单位：秒"`
	RangeRatedWorkTimeLength      int64           `json:"RangeRatedWorkTimeLength" gorm:"column:rangeratedworktimelength;type:integer;default:0;comment:区间核定单圈岗上工时 单位：秒"`
	RangeRatedNotWorkTimeLength   int64           `json:"RangeRatedNotWorkTimeLength" gorm:"column:rangeratednotworktimelength;type:integer;default:0;comment:区间核定单圈岗下工时 单位：秒"`
	RangeCircleWorkTimeLength     int64           `json:"RangeCircleWorkTimeLength" gorm:"column:rangecircleworktimelength;type:integer;default:0;comment:区间圈次岗上时长 单位：秒"`
	RangeCircleNotWorkTimeLength  int64           `json:"RangeCircleNotWorkTimeLength" gorm:"column:rangecirclenotworktimelength;type:integer;default:0;comment:区间圈次岗下时长 单位：秒"`
	RangeInOutDepotTime           int64           `json:"RangeInOutDepotTime" gorm:"column:rangeinoutdepottime;type:integer;default:0;comment:区间进出场时长 单位：秒"`
	RangeAssistantTime            int64           `json:"RangeAssistantTime" gorm:"column:rangeassistanttime;type:integer;default:0;comment:区间辅助时长 单位：秒"`
	RangeStopWorkTimeLength       int64           `json:"RangeStopWorkTimeLength" gorm:"column:rangestopworktimelength;type:integer;default:0;comment:区间自定义岗上时长 单位：秒"`
	RangeStopNotWorkTimeLength    int64           `json:"RangeStopNotWorkTimeLength" gorm:"column:rangestopnotworktimelength;type:integer;default:0;comment:区间自定义岗下时长 单位：秒"`
	AddWorkTimeLength             int64           `json:"AddWorkTimeLength" gorm:"column:addworktimelength;type:integer;default:0;comment:加班时长 单位：秒"`
	AddWorkWorkTimeLength         int64           `json:"AddWorkWorkTimeLength" gorm:"column:addworkworktimelength;type:integer;default:0;comment:加班岗上时长 单位：秒"`
	AddWorkNotWorkTimeLength      int64           `json:"AddWorkNotWorkTimeLength" gorm:"column:addworknotworktimelength;type:integer;default:0;comment:加班岗下时长 单位：秒"`
	AddWorkTimes                  int64           `json:"AddWorkTimes" gorm:"column:addworktimes;type:integer;default:0;comment:加班不足2.5小时次数"`
	FullDayAddWorkTimes           int64           `json:"FullDayAddWorkTimes" gorm:"column:fulldayaddworktimes;type:integer;default:0;comment:加班超过半天不足一天次数"`
	HalfDayAddWorkTimes           int64           `json:"HalfDayAddWorkTimes" gorm:"column:halfdayaddworktimes;type:integer;default:0;comment:加班超过2.5小时不足半天次数"`
	NightAddWorkTimeLength        int64           `json:"NightAddWorkTimeLength" gorm:"column:nightaddworktimelength;type:integer;default:0;comment:夜班加班时长"`
	FixVehicleMileage             int64           `json:"FixVehicleMileage" gorm:"column:fixvehiclemileage;type:integer;default:0;comment:修车公里 单位：米"`
	FixVehicleTimeLength          int64           `json:"FixVehicleTimeLength" gorm:"column:fixvehicletimelength;type:integer;default:0;comment:修车时长(岗下) 单位：秒"`
	FixVehicleWorkTimeLength      int64           `json:"FixVehicleWorkTimeLength" gorm:"column:fixvehicleworktimelength;type:integer;default:0;comment:修车时长(岗上) 单位：秒"`
	CasualLeaveTimeLength         int64           `json:"CasualLeaveTimeLength" gorm:"column:casualleavetimelength;type:integer;default:0;comment:事假时长 单位：秒"`
	CasualLeaveDay                int64           `json:"CasualLeaveDay" gorm:"column:casualleaveday;type:integer;default:0;comment:事假天数 单位：天数*10"`
	CasualLeaveTimes              int64           `json:"CasualLeaveTimes" gorm:"column:casualleavetimes;type:integer;default:0;comment:事假次数"`
	CasualLeaveFirstLastPlanTimes int64           `json:"CasualLeaveFirstLastPlanTimes" gorm:"column:casualleavefirstlastplantimes;type:integer;default:0;comment:事假首末班次数"`
	AccidentDisputeTimeLength     int64           `json:"AccidentDisputeTimeLength" gorm:"column:accidentdisputetimelength;type:integer;default:0;comment:事故纠纷时长 单位：秒"`
	AnnualReviewTimeLength        int64           `json:"AnnualReviewTimeLength" gorm:"column:annualreviewtimelength;type:integer;default:0;comment:年审时长 单位：秒"`
	OfficialWorkTimeLength        int64           `json:"OfficialWorkTimeLength" gorm:"column:officialworktimelength;type:integer;default:0;comment:公务出勤时长 单位：秒"`
	OfficialWorkDay               int64           `json:"OfficialWorkDay" gorm:"column:officialworkday;type:integer;default:0;comment:公务出勤天数 单位：天数*10"`
	CharterBusTimeLength          int64           `json:"CharterBusTimeLength" gorm:"column:charterbustimelength;type:integer;default:0;comment:包车时长 单位：秒"`
	SickLeaveTimeLength           int64           `json:"SickLeaveTimeLength" gorm:"column:sickleavetimelength;type:integer;default:0;comment:病假时长 单位：秒"`
	SickLeaveTimeDay              int64           `json:"SickLeaveTimeDay" gorm:"column:sickleavetimeday;type:integer;default:0;comment:病假天数 单位：天数*10"`
	AnnualLeaveTimeLength         int64           `json:"AnnualLeaveTimeLength" gorm:"column:annualleavetimelength;type:integer;default:0;comment:年休假时长 单位：秒"`
	AnnualLeaveDay                int64           `json:"AnnualLeaveDay" gorm:"column:annualleaveday;type:integer;default:0;comment:年休假天数 单位：天数*10"`
	HolidayDay                    int64           `json:"HolidayDay" gorm:"column:holidayday;type:integer;default:0;comment:节假日天数 单位：天数*10"`
	TrafficJamTimeLength          int64           `json:"TrafficJamTimeLength" gorm:"column:trafficjamtimelength;type:integer;default:0;comment:堵车时长 单位：秒"`
	RestTimeLength                int64           `json:"RestTimeLength" gorm:"column:resttimelength;type:integer;default:0;comment:疗休养时长 单位：秒"`
	RestTimeDay                   int64           `json:"RestTimeDay" gorm:"column:resttimeday;type:integer;default:0;comment:疗休养天数 单位：天数*10"`
	TotalWorkTimeLength           int64           `json:"TotalWorkTimeLength" gorm:"column:totalworktimelength;type:integer;default:0;comment:合计岗上时长 单位：秒"`
	TotalNotWorkTimeLength        int64           `json:"TotalNotWorkTimeLength" gorm:"column:totalnotworktimelength;type:integer;default:0;comment:合计岗下时长 单位：秒"`
	TotalTimeLength               int64           `json:"TotalTimeLength" gorm:"column:totaltimelength;type:integer;default:0;comment:总时长 单位：秒"`
	More                          model.JSON      `json:"More" gorm:"column:more;type:json;default:;comment:备注"`
	VehicleLength                 int64           `json:"VehicleLength" gorm:"column:vehiclelength;type:bigint;comment:车长  单位：毫米"`
	//VehicleModel                  int64           `json:"VehicleModel" gorm:"column:vehiclemodel;type:smallint;comment:车型 1大客 2中客 3小客"`
	IsManualData int64 `json:"IsManualData" gorm:"column:ismanualdata;type:smallint;default:2;comment:是否手动添加的数据  1是 2否 "`
	IsApproval   int64 `json:"IsApproval" gorm:"column:isapproval;type:smallint;default:2;comment:数据是否审批 1是 2否"`
	model.OpUser
	model.Timestamp

	Mores           []model.JSON `json:"Mores" gorm:"-"`
	MainRunLineId   int64        `json:"MainRunLineId" gorm:"-"`   //司机主运营线路Id
	MainRunLineName string       `json:"MainRunLineName" gorm:"-"` //司机主运营线路名称
}

func (r *LineVehicleMileageReport) SyncTableColumn(reportAt time.Time) error {
	table := r.GetTable(reportAt.Format("2006"))
	if model.DB().Migrator().HasTable(table) {
		err := model.DB().Table(table).AutoMigrate(&LineVehicleMileageReport{})
		if err != nil {
			return err
		}
	}

	return nil
}

func (r *LineVehicleMileageReport) HasOrCreateTable(reportAt time.Time) error {
	table := r.GetTable(reportAt.Format("2006"))
	if !model.DB().Migrator().HasTable(table) {
		err := model.DB().Table(table).AutoMigrate(&LineVehicleMileageReport{})
		if err != nil {
			return err
		}
		model.CreateIndex(table, []string{"CorporationId", "LineId", "ReportAt"})
	}
	return nil
}

func (r *LineVehicleMileageReport) GetTable(suffix string) string {
	return fmt.Sprintf("%s_%v", r.TableName(), suffix)
}

func (r *LineVehicleMileageReport) TableName() string {
	return "line_vehicle_mileage_reports"
}
func (r *LineVehicleMileageReport) LineDriverWorkReportTableName() string {
	return "line_driver_work_reports"
}
func (r *LineVehicleMileageReport) LineVehicleMileageSumReportTableName() string {
	return "line_vehicle_mileage_sum_reports"
}

// LineMileageSumReportTableName 累计公里统计报表
func (r *LineVehicleMileageReport) LineMileageSumReportTableName() string {
	return "line_mileage_sum_reports"
}

func (r *LineVehicleMileageReport) CommonSumColumns() []string {
	return []string{
		"SUM(WorkDayCount) AS WorkDayCount", "SUM(FullWorkDay) AS FullWorkDay", "SUM(HalfWorkDay) AS HalfWorkDay",
		"SUM(MotorWorkDay) AS MotorWorkDay", "SUM(MotorBigWorkDay) AS MotorBigWorkDay",
		"SUM(VehicleWorkDayCount) AS VehicleWorkDayCount", "SUM(FullRatedMileage) AS FullRatedMileage",
		"SUM(RangeRatedMileage) AS RangeRatedMileage", "SUM(FixVehicleCircle) AS FixVehicleCircle",
		"SUM(CasualLeaveCircle) AS CasualLeaveCircle", "SUM(AccidentDisputeCircle) AS AccidentDisputeCircle",
		"SUM(NightTotalWorkTimeLength) AS NightTotalWorkTimeLength", "SUM(FixVehicleWorkTimeLength) AS FixVehicleWorkTimeLength",
		"SUM(TotalWorkTimeLength) AS TotalWorkTimeLength", "SUM(TotalNotWorkTimeLength) AS TotalNotWorkTimeLength",
		"SUM(TotalTimeLength) AS TotalTimeLength", "SUM(AnnualReviewTimeLength) AS AnnualReviewTimeLength",
		"SUM(CharterBusTimeLength) AS CharterBusTimeLength", "SUM(HolidayDay) AS HolidayDay",
		"SUM(RestTimeLength) AS RestTimeLength", "SUM(AnnualReviewCircle) AS AnnualReviewCircle",
		"SUM(OfficialWorkCircle) AS OfficialWorkCircle", "SUM(CharterBusCircle) AS CharterBusCircle",
		"SUM(SickLeaveCircle) AS SickLeaveCircle", "SUM(TrafficJamCircle) AS TrafficJamCircle",
		"SUM(CircleMileage) AS CircleMileage", "SUM(StopWorkRatedMileage) AS StopWorkRatedMileage",
		"SUM(CharterBusMileage) AS CharterBusMileage", "SUM(AnnualReviewMileage) AS AnnualReviewMileage",
		"SUM(TrafficJamMileage) AS TrafficJamMileage", "SUM(TotalMileage) AS TotalMileage",
		"SUM(DelayInParkingTimeLength) AS DelayInParkingTimeLength", "SUM(AddWorkTimeLength) AS AddWorkTimeLength",
		"SUM(AddWorkTimes) AS AddWorkTimes", "SUM(FullDayAddWorkTimes) AS FullDayAddWorkTimes",
		"SUM(HalfDayAddWorkTimes) AS HalfDayAddWorkTimes", "SUM(AddWorkWorkTimeLength) AS AddWorkWorkTimeLength",
		"SUM(AddWorkNotWorkTimeLength) AS AddWorkNotWorkTimeLength", "SUM(CasualLeaveTimeLength) AS CasualLeaveTimeLength",
		"SUM(AccidentDisputeTimeLength) AS AccidentDisputeTimeLength", "SUM(OfficialWorkTimeLength) AS OfficialWorkTimeLength",
		"SUM(SickLeaveTimeLength) AS SickLeaveTimeLength", "SUM(TrafficJamTimeLength) AS TrafficJamTimeLength",
		"SUM(OfficialWorkDay) AS OfficialWorkDay", "SUM(AnnualLeaveDay) AS AnnualLeaveDay", "SUM(CasualLeaveDay) AS CasualLeaveDay",
		"SUM(CasualLeaveTimes) AS CasualLeaveTimes", "SUM(CasualLeaveFirstLastPlanTimes) AS CasualLeaveFirstLastPlanTimes",
		"SUM(FixVehicleMileage) AS FixVehicleMileage", "SUM(NightAddWorkTimeLength) AS NightAddWorkTimeLength",
		"SUM(FollowVehicleDay) AS FollowVehicleDay", "SUM(MaintenanceCircle) AS MaintenanceCircle", "SUM(AddGasCircle) AS AddGasCircle",
		"SUM(FixVehicleTimeLength) AS FixVehicleTimeLength", "SUM(RestTimeDay) AS RestTimeDay",
		"SUM(AnnualLeaveCircle) AS AnnualLeaveCircle", "SUM(RestCircle) AS RestCircle",
		"SUM(NightBefore22WorkTimeLength) AS NightBefore22WorkTimeLength", "SUM(NightAfter22WorkTimeLength) AS NightAfter22WorkTimeLength",
		"SUM(FullRatedWorkTimeLength) AS FullRatedWorkTimeLength", "SUM(FullRatedNotWorkTimeLength) AS FullRatedNotWorkTimeLength",
		"SUM(RangeRatedWorkTimeLength) AS RangeRatedWorkTimeLength", "SUM(RangeRatedNotWorkTimeLength) AS RangeRatedNotWorkTimeLength",
		"SUM(AnnualLeaveTimeLength) AS AnnualLeaveTimeLength", "SUM(SickLeaveTimeDay) AS SickLeaveTimeDay",
	}
}

func (r *LineVehicleMileageReport) TransactionCreate(tx *gorm.DB) error {
	if err := r.HasOrCreateTable(time.Time(r.ReportAt)); err != nil {
		return err
	}

	return tx.Table(r.GetTable(time.Time(r.ReportAt).Format("2006"))).Create(&r).Error
}

func (r *LineVehicleMileageReport) TransactionBatchCreate(tx *gorm.DB, reports []LineVehicleMileageReport, reportAt time.Time) error {
	if err := r.HasOrCreateTable(reportAt); err != nil {
		return err
	}
	return tx.Table(r.GetTable(reportAt.Format("2006"))).Create(&reports).Error
}

func (r *LineVehicleMileageReport) TransactionUpdate(tx *gorm.DB) error {
	return tx.Table(r.GetTable(time.Time(r.ReportAt).Format("2006"))).Select("*").Omit("IsApproval").Updates(&r).Error
}

func (r *LineVehicleMileageReport) TransactionUpdateApprovalStatus(tx *gorm.DB, corporationId int64, lineIds []int64, reportAt time.Time, isApproval int64) error {
	return tx.Table(r.GetTable(time.Time(reportAt).Format("2006"))).Where("CorporationId = ? AND LineId IN ? AND ReportAt = ?", corporationId, lineIds, reportAt.Format(model.DateFormat)).
		UpdateColumn("isapproval", isApproval).Error
}

func (r *LineVehicleMileageReport) BatchCreate(tx *gorm.DB, reports []LineVehicleMileageReport) error {
	if err := r.HasOrCreateTable(time.Time(reports[0].ReportAt)); err != nil {
		return err
	}
	return tx.Table(r.GetTable(time.Time(reports[0].ReportAt).Format("2006"))).Create(&reports).Error
}

func (r *LineVehicleMileageReport) TableColumn() []string {
	return []string{
		"Id", "TopCorporationId", "CorporationId", "CorporationName", "LineId", "LineName", "DriverHasLineId",
		"DriverHasLineName", "DriverCorporationId", "ReportAt", "VehicleId", "License", "DriverId", "DriverName",
		"FrequencyIndex", "WorkDayCount", "VehicleWorkDayCount", "FrequencyType", "WorkType", "FollowVehicleDay",
		"FullWorkDay", "HalfWorkDay", "MotorWorkDay", "MotorBigWorkDay", "FullRatedMileage", "FullPlanCircle",
		"FullDoneCircle", "RangeRatedMileage", "RangePlanCircle", "RangeDoneCircle", "FixVehicleCircle", "CasualLeaveCircle",
		"AccidentDisputeCircle", "AnnualReviewCircle", "OfficialWorkCircle", "CharterBusCircle", "SickLeaveCircle",
		"AnnualLeaveCircle", "TrafficJamCircle", "RestCircle", "MaintenanceCircle", "AddGasCircle", "NightBefore22WorkTimeLength",
		"NightAfter22WorkTimeLength", "NightTotalWorkTimeLength", "DelayInParkingTimeLength", "CircleMileage", "StopWorkRatedMileage",
		"FullInOutDepotMileage", "FullAssistantMileage", "RangeInOutDepotMileage", "RangeAssistantMileage", "CharterBusMileage",
		"AnnualReviewMileage", "TrafficJamMileage", "TotalMileage", "FullRatedWorkTimeLength", "FullRatedNotWorkTimeLength",
		"FullCircleWorkTimeLength", "FullCircleNotWorkTimeLength", "FullInOutDepotTime", "FullAssistantTime", "FullStopWorkTimeLength",
		"FullStopNotWorkTimeLength", "RangeRatedWorkTimeLength", "RangeRatedNotWorkTimeLength", "RangeCircleWorkTimeLength",
		"RangeCircleNotWorkTimeLength", "RangeInOutDepotTime", "RangeAssistantTime", "RangeStopWorkTimeLength", "RangeStopNotWorkTimeLength",
		"AddWorkTimeLength", "AddWorkWorkTimeLength", "AddWorkNotWorkTimeLength", "AddWorkTimes", "FullDayAddWorkTimes", "HalfDayAddWorkTimes",
		"NightAddWorkTimeLength", "FixVehicleMileage", "FixVehicleTimeLength", "FixVehicleWorkTimeLength", "CasualLeaveTimeLength",
		"CasualLeaveDay", "CasualLeaveTimes", "CasualLeaveFirstLastPlanTimes", "AccidentDisputeTimeLength", "AnnualReviewTimeLength",
		"OfficialWorkTimeLength", "OfficialWorkDay", "CharterBusTimeLength", "SickLeaveTimeLength", "SickLeaveTimeDay", "AnnualLeaveTimeLength",
		"AnnualLeaveDay", "HolidayDay", "TrafficJamTimeLength", "RestTimeLength", "RestTimeDay", "TotalWorkTimeLength", "TotalNotWorkTimeLength",
		"TotalTimeLength", "More", "VehicleLength", "IsManualData", "IsApproval", "OpUserId", "OpUserName", "CreatedAt", "UpdatedAt",
	}
}
func (r *LineVehicleMileageReport) BuildTable(startAt, endAt time.Time) *gorm.DB {
	reportParts := util.SplitRangeTimeByYear(startAt, endAt)
	var sql []interface{}
	var questionSymbol []string
	for i := range reportParts {
		child := model.DB().Table(r.GetTable(reportParts[i].Start.Format("2006"))).Select(r.TableColumn()).Where("ReportAt >= ? AND ReportAt <= ?", reportParts[i].Start.Format(model.DateFormat), reportParts[i].End.Format(model.DateFormat))
		sql = append(sql, child)
		questionSymbol = append(questionSymbol, "?")
	}

	raw := strings.Join(questionSymbol, " UNION ")

	return model.DB().Raw(raw, sql...)
}

func (r *LineVehicleMileageReport) IsExistRecordByRange(corporationId, lineId int64, dateAts []string, startAt, endAt time.Time) bool {
	table := r.BuildTable(startAt, endAt)
	var count int64
	model.DB().Table("(?) AS v", table).Where("CorporationId = ? AND LineId = ? AND ReportAt IN ?", corporationId, lineId, dateAts).Count(&count)

	return count > 0
}

func (r *LineVehicleMileageReport) FirstById(id int64, reportAt time.Time) LineVehicleMileageReport {
	var report LineVehicleMileageReport
	model.DB().Table(r.GetTable(reportAt.Format("2006"))).Where("Id = ?", id).Find(&report)

	return report
}

func (r *LineVehicleMileageReport) IsExistRecord(corporationId, lineId int64, reportAt time.Time) bool {
	if err := r.HasOrCreateTable(reportAt); err != nil {
		return false
	}
	var count int64
	model.DB().Table(r.GetTable(reportAt.Format("2006"))).Where("CorporationId = ? AND LineId = ? AND ReportAt = ?", corporationId,
		lineId, reportAt.Format(model.DateFormat)).Count(&count)

	return count > 0
}

func (r *LineVehicleMileageReport) GetDriverOtherLineReport(driverId, exceptLineId int64, reportAt time.Time) LineVehicleMileageReport {
	if err := r.HasOrCreateTable(reportAt); err != nil {
		return LineVehicleMileageReport{}
	}
	var report LineVehicleMileageReport
	model.DB().Table(r.GetTable(reportAt.Format("2006"))).Where("DriverId = ? AND LineId != ? AND ReportAt = ? AND WorkDayCount > 0", driverId,
		exceptLineId, reportAt.Format(model.DateFormat)).First(&report)

	return report
}

func (r *LineVehicleMileageReport) Delete(tx *gorm.DB, corporationId, lineId int64, reportAt time.Time) error {
	return tx.Table(r.GetTable(reportAt.Format("2006"))).Where("CorporationId = ? AND LineId = ? AND ReportAt = ?", corporationId,
		lineId, reportAt.Format(model.DateFormat)).Delete(&LineVehicleMileageReport{}).Error
}

func (r *LineVehicleMileageReport) DeleteById() error {
	return model.DB().Table(r.GetTable(time.Time(r.ReportAt).Format("2006"))).Where("Id = ?", r.Id).Delete(&LineVehicleMileageReport{}).Error
}

func (r *LineVehicleMileageReport) HasRecordReportAt(isApproval, corporationId, lineId int64, startAt, endAt time.Time) []model.LocalTime {
	table := r.BuildTable(startAt, endAt)
	var records []model.LocalTime
	tx := model.DB().Table("(?) AS v", table).Select("v.reportat")
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", isApproval)
	}

	tx.Where("CorporationId = ? AND LineId = ?", corporationId, lineId).Group("v.reportat").Pluck("v.reportat", &records)

	return records
}

func (r *LineVehicleMileageReport) GetByRangeDay(isApproval int64, tab, scene, sumType string, corporationIds, lineIds, vehicleIds, driverIds []int64, startAt, endAt time.Time, sortable model.Sortable, paginator model.Paginator) ([]LineVehicleMileageReport, int64) {
	var reports []LineVehicleMileageReport

	table := r.BuildTable(startAt, endAt)
	columns := append(r.CommonSumColumns(), "SUM(FullPlanCircle) AS FullPlanCircle", "SUM(FullDoneCircle) AS FullDoneCircle", "SUM(RangePlanCircle) AS RangePlanCircle",
		"SUM(RangeDoneCircle) AS RangeDoneCircle", "SUM(FullInOutDepotTime) AS FullInOutDepotTime",
		"SUM(RangeInOutDepotTime) AS RangeInOutDepotTime", "SUM(FullAssistantTime) AS FullAssistantTime",
		"SUM(RangeAssistantTime) AS RangeAssistantTime", "SUM(FullStopWorkTimeLength) AS FullStopWorkTimeLength",
		"SUM(RangeStopWorkTimeLength) AS RangeStopWorkTimeLength", "SUM(FullStopNotWorkTimeLength) AS FullStopNotWorkTimeLength",
		"SUM(RangeStopNotWorkTimeLength) AS RangeStopNotWorkTimeLength", "SUM(FullInOutDepotMileage) AS FullInOutDepotMileage",
		"SUM(RangeInOutDepotMileage) AS RangeInOutDepotMileage", "SUM(FullAssistantMileage) AS FullAssistantMileage",
		"SUM(RangeAssistantMileage) AS RangeAssistantMileage", "SUM(FullCircleWorkTimeLength) AS FullCircleWorkTimeLength",
		"SUM(RangeCircleWorkTimeLength) AS RangeCircleWorkTimeLength", "SUM(FullCircleNotWorkTimeLength) AS FullCircleNotWorkTimeLength",
		"SUM(RangeCircleNotWorkTimeLength) AS RangeCircleNotWorkTimeLength")

	//	"CorporationId", "CorporationName", "LineId", "LineName", "VehicleId", "License", "DriverId", "DriverName",
	var groupNames []string

	var orderFiled string
	//人车tab
	if tab == "driverAndVehicle" {
		groupNames = []string{
			"CorporationId", "CorporationName", "LineId", "LineName", "VehicleId", "License", "DriverId", "DriverName",
		}
		orderFiled = "LineId"
	}

	//线路tab
	if tab == "line" {
		groupNames = []string{"CorporationId", "CorporationName", "LineId", "LineName"}
		orderFiled = "LineId"
	}

	//司机tab
	if tab == "driver" {
		if sumType == "line" {
			groupNames = []string{"CorporationId", "CorporationName", "LineId", "LineName", "DriverId", "DriverName"}
		} else {
			groupNames = []string{"DriverId", "DriverName"}
		}
		orderFiled = "DriverId"
	}

	//车辆tab
	if tab == "vehicle" {
		groupNames = []string{"VehicleId", "License"}
		orderFiled = "VehicleId"
	}

	if len(groupNames) == 0 {
		groupNames = []string{
			"CorporationId", "CorporationName", "LineId", "LineName", "VehicleId", "License", "DriverId", "DriverName",
		}
	}

	//明细
	if scene == "detail" {
		groupNames = append(groupNames, "ReportAt")
		orderFiled = "ReportAt"
	}

	columns = append(columns, groupNames...)

	tx := model.DB().Table("(?) AS v", table).Select(columns)
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", isApproval)
	}

	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if len(vehicleIds) > 0 {
		tx.Where("VehicleId IN ?", vehicleIds)
	}
	if len(driverIds) > 0 {
		tx.Where("DriverId IN ?", driverIds)
	}

	if tab == "vehicle" {
		tx.Where("VehicleId > 0")
	}

	//Where("CorporationId = ? AND LineId = ?", corporationId, lineId).
	//Group("CorporationId,CorporationName,LineId,LineName,VehicleId,License,DriverId,DriverName").Scan(&reports)

	tx.Group(strings.Join(groupNames, ","))
	if orderFiled != "" && sortable.SortField == "" {
		tx.Order(orderFiled + " ASC")
	}

	var count int64
	tx.Count(&count)
	if sortable.SortField != "" {
		tx.Order(sortable.SortField + " " + sortable.Order)
	}

	if paginator.Limit > 0 {
		tx.Limit(paginator.Limit).Offset(paginator.Offset)
	}
	tx.Scan(&reports)

	return reports, count
}

func (r *LineVehicleMileageReport) GetSumRangeDay(tab string, corporationIds, lineIds, vehicleIds, driverIds []int64, startAt, endAt time.Time) LineVehicleMileageReport {
	var sumReport LineVehicleMileageReport

	table := r.BuildTable(startAt, endAt)

	columns := append(r.CommonSumColumns(), "SUM(FullPlanCircle) AS FullPlanCircle", "SUM(FullDoneCircle) AS FullDoneCircle", "SUM(RangePlanCircle) AS RangePlanCircle",
		"SUM(RangeDoneCircle) AS RangeDoneCircle", "SUM(FullInOutDepotTime) AS FullInOutDepotTime",
		"SUM(RangeInOutDepotTime) AS RangeInOutDepotTime", "SUM(FullAssistantTime) AS FullAssistantTime",
		"SUM(RangeAssistantTime) AS RangeAssistantTime", "SUM(FullStopWorkTimeLength) AS FullStopWorkTimeLength",
		"SUM(RangeStopWorkTimeLength) AS RangeStopWorkTimeLength", "SUM(FullStopNotWorkTimeLength) AS FullStopNotWorkTimeLength",
		"SUM(RangeStopNotWorkTimeLength) AS RangeStopNotWorkTimeLength", "SUM(FullInOutDepotMileage) AS FullInOutDepotMileage",
		"SUM(RangeInOutDepotMileage) AS RangeInOutDepotMileage", "SUM(FullAssistantMileage) AS FullAssistantMileage",
		"SUM(RangeAssistantMileage) AS RangeAssistantMileage", "SUM(FullCircleWorkTimeLength) AS FullCircleWorkTimeLength",
		"SUM(RangeCircleWorkTimeLength) AS RangeCircleWorkTimeLength", "SUM(FullCircleNotWorkTimeLength) AS FullCircleNotWorkTimeLength",
		"SUM(RangeCircleNotWorkTimeLength) AS RangeCircleNotWorkTimeLength")

	tx := model.DB().Table("(?) AS v", table).Select(columns)
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if len(vehicleIds) > 0 {
		tx.Where("VehicleId IN ?", vehicleIds)
	}
	if len(driverIds) > 0 {
		tx.Where("DriverId IN ?", driverIds)
	}

	if tab == "vehicle" {
		tx.Where("VehicleId > 0")
	}

	tx.Scan(&sumReport)

	return sumReport
}

func (r *LineVehicleMileageReport) GetByDay(isApproval, corporationId, lineId int64, reportAt time.Time) []LineVehicleMileageReport {
	var reports []LineVehicleMileageReport

	tx := model.DB().Table(r.GetTable(reportAt.Format("2006")))
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", isApproval)
	}
	tx.Where("CorporationId = ? AND LineId = ? AND ReportAt = ?", corporationId, lineId, reportAt.Format(model.DateFormat)).
		Find(&reports)

	return reports
}

func (r *LineVehicleMileageReport) GetAllMore(isApproval int64, corporationIds []int64, lineId, vehicleId, driverId int64, startAt, endAt time.Time) []model.JSON {
	var mores []model.JSON
	table := r.BuildTable(startAt, endAt)

	tx := model.DB().Table("(?) AS v", table)
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", isApproval)
	}
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}
	if vehicleId > 0 {
		tx.Where("VehicleId = ?", vehicleId)
	}
	if driverId > 0 {
		tx.Where("DriverId = ?", driverId)
	}

	tx.Where("More IS NOT NULL").Pluck("v.more", &mores)

	return mores
}

func (r *LineVehicleMileageReport) GetAllLineName(vehicleId, driverId int64, startAt, endAt time.Time) ([]string, []string) {
	var lineName []string
	var corporationName []string
	table := r.BuildTable(startAt, endAt)

	tx := model.DB().Table("(?) AS v", table)
	if vehicleId > 0 {
		tx.Where("VehicleId = ?", vehicleId)
	}
	if driverId > 0 {
		tx.Where("DriverId = ?", driverId)
	}
	tx = tx.Session(&gorm.Session{})
	tx.Select("LineName").Where("LineId > 0").Group("linename").Pluck("linename", &lineName)
	tx.Select("CorporationName").Group("corporationname").Pluck("corporationname", &corporationName)

	return lineName, corporationName
}
func (r *LineVehicleMileageReport) GetAllReportAt(corporationIds []int64, lineId int64, startAt, endAt time.Time) []string {
	var reportAts []string
	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table)
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}
	tx.Select("TO_CHAR(ReportAt,'YYYY-MM-DD') as ReportAt").Where("LineId > 0").Group("TO_CHAR(ReportAt,'YYYY-MM-DD')").Pluck("ReportAt", &reportAts)

	return reportAts
}

func (r *LineVehicleMileageReport) GetAllReportAtByStatus(corporationIds []int64, lineId, approvalStatus int64, startAt, endAt time.Time) []string {
	var reportAts []string
	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table)
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}
	if approvalStatus > 0 {
		tx.Where("IsApproval = ?", approvalStatus)
	}
	tx.Select("TO_CHAR(ReportAt,'YYYY-MM-DD') as ReportAt").Where("LineId > 0").Group("TO_CHAR(ReportAt,'YYYY-MM-DD')").Pluck("ReportAt", &reportAts)

	return reportAts
}

func (r *LineVehicleMileageReport) GetLineRunDriverIds(corporationIds []int64, lineIds []int64, startAt, endAt time.Time) []int64 {
	var driverIds []int64
	table := r.BuildTable(startAt, endAt)
	model.DB().Table("(?) AS v", table).Select("DriverId").Where("CorporationId IN ? AND LineId IN ?", corporationIds, lineIds).Group("driverid").Pluck("DriverId", &driverIds)

	return driverIds
}

func (r *LineVehicleMileageReport) GetDriverOperationInfo(driverId int64, startAt, endAt time.Time) []LineVehicleMileageReport {
	var reports []LineVehicleMileageReport
	table := r.BuildTable(startAt, endAt)

	model.DB().Table("(?) AS v", table).Where("DriverId = ?", driverId).Order("ReportAt ASC").Find(&reports)

	return reports
}

type DriverLineWorkDayCountItem struct {
	LineId          int64  `json:"LineId" gorm:"column:lineid"`
	LineName        string `json:"LineName" gorm:"column:linename"`
	CorporationId   int64  `json:"CorporationId" gorm:"column:corporationid"`
	CorporationName string `json:"CorporationName" gorm:"column:corporationname"`
	WorkDayCount    int64  `json:"WorkDayCount" gorm:"column:workdaycount"`
}

func (r *LineVehicleMileageReport) GetDriverLineWorkDayCount(driverId int64, startAt, endAt time.Time) []DriverLineWorkDayCountItem {
	var reports []DriverLineWorkDayCountItem
	table := r.BuildTable(startAt, endAt)

	model.DB().Table("(?) AS v", table).Select("SUM(WorkDayCount) AS WorkDayCount,CorporationId,CorporationName,LineId,LineName").Where("DriverId = ?", driverId).Group("CorporationId,CorporationName,LineId,LineName").Scan(&reports)

	return reports
}

type LineMileageAndCircleSumItem struct {
	CorporationId   int64  `json:"CorporationId" gorm:"column:corporationid"`
	CorporationName string `json:"CorporationName" gorm:"column:corporationname"`
	LineId          int64  `json:"LineId" gorm:"column:lineid"`
	LineName        string `json:"LineName" gorm:"column:linename"`
	LineAttr        int64  `json:"LineAttr"`
	VehicleId       int64  `json:"VehicleId" gorm:"column:vehicleid"`
	License         string `json:"License" gorm:"column:license"`
	TotalMileage    int64  `json:"TotalMileage" gorm:"column:totalmileage"`
	TotalCircle     int64  `json:"TotalCircle" gorm:"column:totalcircle"`
}

func (r *LineVehicleMileageReport) GetLineMileageAndCircleSum(isApproval int64, corporationIds, lineIds []int64, startAt, endAt time.Time) []LineMileageAndCircleSumItem {
	var reports []LineMileageAndCircleSumItem
	table := r.BuildTable(startAt, endAt)

	tx := model.DB().Table("(?) AS v", table).Select("SUM(TotalMileage) AS TotalMileage", "SUM(FullDoneCircle+RangeDoneCircle) AS TotalCircle", "CorporationId", "CorporationName", "LineId", "LineName")

	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", isApproval)
	}

	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	tx.Group("CorporationId,CorporationName,LineId,LineName").Scan(&reports)
	return reports
}

func (r *LineVehicleMileageReport) GetLineTotalMileage(isApproval int64, corporationId, lineId int64, startAt, endAt time.Time) int64 {
	var mileage int64
	table := r.BuildTable(startAt, endAt)

	tx := model.DB().Table("(?) AS v", table).Select("COALESCE(SUM(TotalMileage),0)").Where("CorporationId = ? AND LineId = ?", corporationId, lineId)

	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	tx.Scan(&mileage)
	return mileage
}

func (r *LineVehicleMileageReport) GetVehicleMileageAndCircleSum(isApproval int64, corporationIds, lineIds, vehicleIds []int64, startAt, endAt time.Time) []LineMileageAndCircleSumItem {
	var reports []LineMileageAndCircleSumItem
	table := r.BuildTable(startAt, endAt)

	tx := model.DB().Table("(?) AS v", table).Select("SUM(TotalMileage) AS TotalMileage", "SUM(FullDoneCircle+RangeDoneCircle) AS TotalCircle", "VehicleId", "License").Where("VehicleId > 0")

	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", isApproval)
	}
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if len(vehicleIds) > 0 {
		tx.Where("VehicleId IN ?", vehicleIds)
	}
	tx.Group("VehicleId,License").Scan(&reports)
	return reports
}

// GetDriverHasLines 获取司机的归属线路
func (r *LineVehicleMileageReport) GetDriverHasLines(driverId int64, startAt, endAt time.Time) []string {
	var lineNames []string
	table := r.BuildTable(startAt, endAt)

	model.DB().Table("(?) AS v", table).Select("DriverHasLineName").Where("DriverId = ?", driverId).Group("driverhaslinename").Pluck("DriverHasLineName", &lineNames)

	return lineNames
}

func (r *LineVehicleMileageReport) GetDriverRunCountDataGroupByReportAt(isApproval int64, corporationIds []int64, lineId, vehicleId, driverId, frequencyType int64, startAt, endAt time.Time) int64 {
	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table).Select("WorkDayCount")
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}

	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if lineId > 0 {
		tx = tx.Where("LineId = ?", lineId)
	}

	if vehicleId > 0 {
		tx = tx.Where("VehicleId = ?", vehicleId)
	}

	if driverId > 0 {
		tx = tx.Where("DriverId = ?", driverId)
	}

	if frequencyType > 0 {
		tx = tx.Where("FrequencyType = ?", frequencyType)
	}

	var counts []int64
	tx.Pluck("WorkDayCount", &counts)

	var totalDay int64
	for i := range counts {
		totalDay += counts[i]
	}
	return totalDay
}

func (r *LineVehicleMileageReport) GetDriverRunReportAt(isApproval int64, corporationIds []int64, lineId, vehicleId, driverId, frequencyType int64, startAt, endAt time.Time) []string {
	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table).Select("TO_CHAR(ReportAt,'YYYY-MM-DD') as ReportAt").Where("WorkDayCount > 0")
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}
	if lineId > 0 {
		tx = tx.Where("LineId = ?", lineId)
	}
	if vehicleId > 0 {
		tx = tx.Where("VehicleId = ?", vehicleId)
	}
	if driverId > 0 {
		tx = tx.Where("DriverId = ?", driverId)
	}
	if frequencyType > 0 {
		tx = tx.Where("FrequencyType = ?", frequencyType)
	}

	var lineVehicleMileageReportAts []string
	tx.Pluck("ReportAt", &lineVehicleMileageReportAts)

	return lineVehicleMileageReportAts

}

type DriverRunVehicleDay struct {
	VehicleId     int64 `json:"VehicleId" gorm:"column:vehicleid"`
	VehicleLength int64 `json:"VehicleLength" gorm:"column:vehiclelength"`
	//VehicleModel  int64 `json:"VehicleModel" gorm:"column:vehiclemodel"`
	DayCount int64 `json:"DayCount" gorm:"column:daycount"`
}

func (r *LineVehicleMileageReport) GetLineDriverRunVehicleDay(driverId int64, startAt, endAt time.Time) []DriverRunVehicleDay {
	var items []DriverRunVehicleDay
	table := r.BuildTable(startAt, endAt)

	tx := model.DB().Table("(?) AS v", table).Select("SUM(WorkDayCount) as DayCount", "VehicleLength")
	tx.Where("DriverId = ?", driverId).Group("vehiclelength").Order("SUM(WorkDayCount) desc").Order("VehicleLength DESC").Scan(&items)

	return items
}

func (r *LineVehicleMileageReport) GetLineVehicleLengthGroupBy(isApproval int64, corporationIds []int64, lineId, vehicleId, driverId int64, startAt, endAt time.Time) []int64 {
	var vehicleLength []int64
	//var vehicleModel []int64

	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table)
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}
	if vehicleId > 0 {
		tx.Where("VehicleId = ?", vehicleId)
	}
	if driverId > 0 {
		tx.Where("DriverId = ?", driverId)
	}

	tx.Select("COALESCE(v.VehicleLength,0)").Group("v.vehiclelength").Pluck("v.VehicleLength", &vehicleLength)

	//tx.Select("COALESCE(v.VehicleModel,0)").Group("v.vehiclemodel").Pluck("v.VehicleModel", &vehicleModel)
	return vehicleLength
}

func (r *LineVehicleMileageReport) GetLineVehiclesGroupByVehicleId(corporationId, lineId int64, startAt, endAt time.Time) []int64 {
	var vehicleIds []int64

	table := r.BuildTable(startAt, endAt)
	model.DB().Table("(?) AS v", table).Select("v.VehicleId").
		Where("CorporationId = ? AND LineId = ?", corporationId, lineId).Group("v.vehicleid").Pluck("v.VehicleId", &vehicleIds)

	return vehicleIds
}

type RelateDayCircleItem struct {
	FixVehicleCircle      int64 `json:"FixVehicleCircle" gorm:"column:fixvehiclecircle;"`           //修车圈次 单位：圈次*10
	AccidentDisputeCircle int64 `json:"AccidentDisputeCircle" gorm:"column:accidentdisputecircle;"` //事故纠纷圈次 单位：圈次*10
	OfficialWorkCircle    int64 `json:"OfficialWorkCircle" gorm:"column:officialworkcircle;"`       //公务出勤圈次 单位：圈次*10
	CharterBusCircle      int64 `json:"CharterBusCircle" gorm:"column:charterbuscircle;"`           //包车圈次 单位：圈次*10
	SickLeaveCircle       int64 `json:"SickLeaveCircle" gorm:"column:sickleavecircle;"`             //病假圈次 单位：圈次*10
	AnnualLeaveCircle     int64 `json:"AnnualLeaveCircle" gorm:"column:annualleavecircle;"`         //年休假圈次 单位：圈次*10
	TrafficJamCircle      int64 `json:"TrafficJamCircle" gorm:"column:trafficjamcircle;"`           //堵车圈次 单位：圈次*10
	RestCircle            int64 `json:"RestCircle" gorm:"column:restcircle;"`                       //疗休养圈次 单位：圈次*10
}

// GetLineDriverNotWorkReportAt 获取司机的年休假、疗休养、病假天数
func (r *LineVehicleMileageReport) GetLineDriverNotWorkReportAt(corporationId, lineId, driverId int64, startAt, endAt time.Time) []string {
	var reportAts []string

	table := r.BuildTable(startAt, endAt)

	model.DB().Table("(?) AS v", table).Select("TO_CHAR(ReportAt,'YYYY-MM-DD') as ReportAt").
		Where("CorporationId = ? AND LineId = ? AND DriverId = ?", corporationId, lineId, driverId).
		Where("SickLeaveTimeDay>=1 OR AnnualLeaveDay>=1 OR RestTimeDay>=1").
		Group("TO_CHAR(ReportAt,'YYYY-MM-DD')").
		Pluck("ReportAt", &reportAts)

	return reportAts
}

func (r *LineVehicleMileageReport) GetLineDriverNotWorkCircle(corporationId, lineId, driverId int64, startAt, endAt time.Time, exceptReportAts []string) int64 {
	var item RelateDayCircleItem
	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table).Select("SUM(FixVehicleCircle) as FixVehicleCircle", "SUM(AccidentDisputeCircle) as AccidentDisputeCircle",
		"SUM(OfficialWorkCircle) as OfficialWorkCircle", "SUM(CharterBusCircle) as CharterBusCircle", "SUM(SickLeaveCircle) as SickLeaveCircle",
		"SUM(AnnualLeaveCircle) as AnnualLeaveCircle", "SUM(TrafficJamCircle) as TrafficJamCircle", "SUM(RestCircle) as RestCircle")

	if corporationId > 0 {
		tx.Where("CorporationId = ?", corporationId)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}
	if driverId > 0 {
		tx.Where("DriverId = ?", driverId)
	}

	if len(exceptReportAts) > 0 {
		tx.Where("TO_CHAR(ReportAt,'YYYY-MM-DD') NOT IN ?", exceptReportAts)
	}
	tx.Scan(&item)

	return item.FixVehicleCircle + item.AccidentDisputeCircle + item.OfficialWorkCircle + item.CharterBusCircle + item.SickLeaveCircle + item.AnnualLeaveCircle + item.TrafficJamCircle + item.RestCircle
}

// GetLineDriverDay 获取司机的年休假、疗休养、病假天数
func (r *LineVehicleMileageReport) GetLineDriverDay(corporationId, lineId, driverId int64, startAt, endAt time.Time) []string {
	var reportAts []string

	table := r.BuildTable(startAt, endAt)

	model.DB().Table("(?) AS v", table).Select("TO_CHAR(ReportAt,'YYYY-MM-DD') as ReportAt").
		Where("CorporationId = ? AND LineId = ? AND DriverId = ?", corporationId, lineId, driverId).
		Where("SickLeaveTimeDay>=1 OR AnnualLeaveDay>=1 OR RestTimeDay>=1").
		Group("TO_CHAR(ReportAt,'YYYY-MM-DD')").
		Pluck("ReportAt", &reportAts)

	return reportAts
}

type LineWorkTimeLengthItem struct {
	FullTimeLength  int64 `json:"FullTimeLength" gorm:"column:fulltimelength"`
	RangeTimeLength int64 `json:"RangeTimeLength" gorm:"column:rangetimelength"`
}

func (r *LineVehicleMileageReport) GetLineAllWorkTimeGroupBy(isApproval int64, corporationIds []int64, lineId int64, startAt, endAt time.Time) []LineWorkTimeLengthItem {
	var items []LineWorkTimeLengthItem

	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table).Select("FullRatedWorkTimeLength AS FullTimeLength", "RangeRatedWorkTimeLength AS RangeTimeLength")
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}
	tx.Where("CorporationId IN ? AND LineId = ?", corporationIds, lineId).Group("FullRatedWorkTimeLength,RangeRatedWorkTimeLength").Scan(&items)

	return items
}
func (r *LineVehicleMileageReport) GetLineAllNotWorkTimeGroupBy(isApproval int64, corporationIds []int64, lineId int64, startAt, endAt time.Time) []LineWorkTimeLengthItem {
	var items []LineWorkTimeLengthItem

	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table).Select("FullRatedNotWorkTimeLength AS FullTimeLength", "RangeRatedNotWorkTimeLength AS RangeTimeLength")
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}
	tx.Where("CorporationId IN ? AND LineId = ?", corporationIds, lineId).Group("FullRatedNotWorkTimeLength,RangeRatedNotWorkTimeLength").Scan(&items)

	return items
}
func (r *LineVehicleMileageReport) GetLineHasDriversGroupByDriverId(corporationIds []int64, lineId int64, startAt, endAt time.Time) []int64 {
	var driverIds []int64

	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table).Select("v.DriverId")
	if len(corporationIds) > 0 {
		tx.Where("DriverCorporationId IN ?", corporationIds)
	}
	if lineId > 0 {
		tx.Where("DriverHasLineId = ?", lineId)
	}

	tx.Group("v.driverid").Pluck("v.DriverId", &driverIds)

	return driverIds
}

type DriverRunLineReportDataItem struct {
	LineId                   int64  `json:"LineId" gorm:"column:lineid"`
	LineName                 string `json:"LineName" gorm:"column:linename"`
	WorkDayCount             int64  `json:"WorkDayCount" gorm:"column:workdaycount"`
	TotalCircle              int64  `json:"TotalCircle" gorm:"column:totalcircle"` //FullDoneCircle+RangeDoneCircle
	TotalWorkTimeLength      int64  `json:"TotalWorkTimeLength" gorm:"column:totalworktimelength"`
	TotalNotWorkTimeLength   int64  `json:"TotalNotWorkTimeLength" gorm:"column:totalnotworktimelength"`
	NightTotalWorkTimeLength int64  `json:"NightTotalWorkTimeLength" gorm:"column:nighttotalworktimelength"`
	Sort                     int64  `json:"-"`
	Scene                    string `json:"Scene"`
}

func (r *LineVehicleMileageReport) GetDriverRunLineReportDataGroupByLineId(isApproval int64, driverIds []int64, startAt, endAt time.Time) []DriverRunLineReportDataItem {
	var reports []DriverRunLineReportDataItem

	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table).Select("LineId", "LineName", "SUM(WorkDayCount) AS WorkDayCount", "SUM(FullDoneCircle)+SUM(RangeDoneCircle) AS TotalCircle",
		"SUM(TotalWorkTimeLength-AddWorkWorkTimeLength) AS TotalWorkTimeLength", "SUM(TotalNotWorkTimeLength-AddWorkNotWorkTimeLength) AS TotalNotWorkTimeLength",
		"SUM(NightTotalWorkTimeLength) AS NightTotalWorkTimeLength")
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}
	tx.Where("DriverId IN ?", driverIds).Group("v.lineid,v.linename").Scan(&reports)

	return reports

}

func (r *LineVehicleMileageReport) GetDriverRunLineReportAddWorkTimeLength(isApproval int64, driverIds []int64, sumColumn string, startAt, endAt time.Time) int64 {
	table := r.BuildTable(startAt, endAt)
	column := fmt.Sprintf("COALESCE(SUM(%s),0) AS WorkDayCount", sumColumn)
	var timeLength int64
	tx := model.DB().Table("(?) AS v", table).Select(column)
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}
	tx.Where("DriverId IN ?", driverIds).Scan(&timeLength)

	return timeLength
}

func (r *LineVehicleMileageReport) GetDriverRunLineReportData(isApproval int64, driverIds []int64, startAt, endAt time.Time) DriverRunLineReportDataItem {
	var report DriverRunLineReportDataItem

	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table).Select("SUM(WorkDayCount) AS WorkDayCount", "SUM(FullDoneCircle)+SUM(RangeDoneCircle) AS TotalCircle",
		"SUM(TotalWorkTimeLength) AS TotalWorkTimeLength", "SUM(TotalNotWorkTimeLength) AS TotalNotWorkTimeLength", "SUM(NightTotalWorkTimeLength) AS NightTotalWorkTimeLength")
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	tx.Where("DriverId IN ?", driverIds).Scan(&report)

	return report
}

type LineDriverWorkReportData struct {
	LineVehicleMileageReport
	//FullPlanCircle = FullPlanCircle+RangePlanCircle 每日实际排班圈次 单位：圈次*10
	//FullDoneCircle = FullDoneCircle+RangeDoneCircle 本线路完成圈次 单位：圈次*10
	//FullInOutDepotTime = FullInOutDepotTime+RangeInOutDepotTime 进出场时长 单位：秒
	//FullAssistantTime = FullAssistantTime+RangeAssistantTime 辅助时长 单位：秒
	//FullStopWorkTimeLength = FullStopWorkTimeLength+RangeStopWorkTimeLength 停运岗上时长 单位：秒
	//FullStopNotWorkTimeLength = FullStopNotWorkTimeLength+RangeStopNotWorkTimeLength 停运岗下时长 单位：秒
	//FullInOutDepotMileage = FullInOutDepotMileage+RangeInOutDepotMileage 进出场公里 单位：米
	//FullAssistantMileage = FullAssistantMileage+RangeAssistantMileage 辅助公里 单位：米
	//FullCircleWorkTimeLength = FullCircleWorkTimeLength+RangeCircleWorkTimeLength 圈次岗上工时 单位：秒
	//FullCircleNotWorkTimeLength = FullCircleNotWorkTimeLength+RangeCircleNotWorkTimeLength 圈次岗下工时 单位：秒
}

// GetDriverRunReportDataGroupByLineDriver 司机出勤报表-按线路
func (r *LineVehicleMileageReport) GetDriverRunReportDataGroupByLineDriver(isApproval int64, corporationIds, lineIds, driverIds []int64, startAt, endAt time.Time, sortable model.Sortable, paginator model.Paginator) ([]LineDriverWorkReportData, int64, LineDriverWorkReportData) {
	var reports []LineDriverWorkReportData
	fmt.Println("======================startAt=========================")
	fmt.Println(startAt)
	fmt.Println(endAt)
	fmt.Println("==========================endAt=====================")
	table := r.BuildTable(startAt, endAt)
	var columns = append(r.CommonSumColumns(), "SUM(FullPlanCircle+RangePlanCircle) AS FullPlanCircle",
		"SUM(FullDoneCircle+RangeDoneCircle) AS FullDoneCircle", "SUM(FullInOutDepotTime+RangeInOutDepotTime) AS FullInOutDepotTime",
		"SUM(FullAssistantTime+RangeAssistantTime) AS FullAssistantTime", "SUM(FullStopWorkTimeLength+RangeStopWorkTimeLength) AS FullStopWorkTimeLength",
		"SUM(FullStopNotWorkTimeLength+RangeStopNotWorkTimeLength) AS FullStopNotWorkTimeLength", "SUM(FullInOutDepotMileage+RangeInOutDepotMileage) AS FullInOutDepotMileage",
		"SUM(FullAssistantMileage+RangeAssistantMileage) AS FullAssistantMileage", "SUM(FullCircleWorkTimeLength+RangeCircleWorkTimeLength) AS FullCircleWorkTimeLength",
		"SUM(FullCircleNotWorkTimeLength+RangeCircleNotWorkTimeLength) AS FullCircleNotWorkTimeLength")

	groupColumns := []string{"CorporationId", "CorporationName", "LineId", "LineName", "DriverId", "DriverName"}

	tx := model.DB().Table("(?) AS v", table)

	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}

	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}

	if len(driverIds) > 0 {
		tx.Where("DriverId IN ?", driverIds)
	}

	tx = tx.Session(&gorm.Session{})

	var count int64
	tx.Group(strings.Join(groupColumns, ",")).Count(&count)
	if sortable.SortField == "" {
		sortable.SortField = "LineId"
		sortable.Order = "ASC"
	}
	tx.Select(append(columns, groupColumns...)).Group(strings.Join(groupColumns, ",")).Order(sortable.SortField + " " + sortable.Order).Offset(paginator.Offset).Limit(paginator.Limit).Scan(&reports)

	var sumReport LineDriverWorkReportData
	tx.Select(columns).Scan(&sumReport)

	return reports, count, sumReport
}

// GetDriverRunReportDataGroupByDriver 司机出勤报表-按司机分组
func (r *LineVehicleMileageReport) GetDriverRunReportDataGroupByDriver(isApproval int64, driverIds []int64, startAt, endAt time.Time, sortable model.Sortable, paginator model.Paginator) ([]LineDriverWorkReportData, int64, LineDriverWorkReportData) {
	var reports []LineDriverWorkReportData
	table := r.BuildTable(startAt, endAt)
	var columns = append(r.CommonSumColumns(), "SUM(FullPlanCircle+RangePlanCircle) AS FullPlanCircle",
		"SUM(FullDoneCircle+RangeDoneCircle) AS FullDoneCircle", "SUM(FullInOutDepotTime+RangeInOutDepotTime) AS FullInOutDepotTime",
		"SUM(FullAssistantTime+RangeAssistantTime) AS FullAssistantTime", "SUM(FullStopWorkTimeLength+RangeStopWorkTimeLength) AS FullStopWorkTimeLength",
		"SUM(FullStopNotWorkTimeLength+RangeStopNotWorkTimeLength) AS FullStopNotWorkTimeLength", "SUM(FullInOutDepotMileage+RangeInOutDepotMileage) AS FullInOutDepotMileage",
		"SUM(FullAssistantMileage+RangeAssistantMileage) AS FullAssistantMileage", "SUM(FullCircleWorkTimeLength+RangeCircleWorkTimeLength) AS FullCircleWorkTimeLength",
		"SUM(FullCircleNotWorkTimeLength+RangeCircleNotWorkTimeLength) AS FullCircleNotWorkTimeLength")

	groupColumns := []string{"DriverId", "DriverName"}

	tx := model.DB().Table("(?) AS v", table)

	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if len(driverIds) > 0 {
		tx.Where("DriverId IN ?", driverIds)
	}

	tx = tx.Session(&gorm.Session{})

	var count int64
	tx.Group(strings.Join(groupColumns, ",")).Count(&count)
	if sortable.SortField == "" {
		sortable.SortField = "DriverId"
		sortable.Order = "ASC"
	}
	tx.Select(append(columns, groupColumns...)).Group(strings.Join(groupColumns, ",")).Order(sortable.SortField + " " + sortable.Order).Offset(paginator.Offset).Limit(paginator.Limit).Scan(&reports)

	var sumReport LineDriverWorkReportData
	tx.Select(columns).Scan(&sumReport)

	return reports, count, sumReport
}

type ReportAtDoneCircle struct {
	FullDoneCircle int64           `json:"FullDoneCircle" gorm:"column:fulldonecircle"`
	ReportAt       model.LocalTime `json:"ReportAt" gorm:"column:reportat"`
}

func (r *LineVehicleMileageReport) GetReportAtDoneCircleByDriver(isApproval int64, driverId, corporationId, lineId int64, startAt, endAt time.Time) []ReportAtDoneCircle {
	var reports []ReportAtDoneCircle
	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table).Select("ReportAt", "SUM(FullDoneCircle+RangeDoneCircle) AS FullDoneCircle").Where("DriverId = ?", driverId)
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if corporationId > 0 {
		tx.Where("CorporationId = ?", corporationId)
	}

	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	tx.Group("reportat").Scan(&reports)

	return reports
}

type NotApprovalLineItem struct {
	CorporationId   int64  `json:"CorporationId" gorm:"column:corporationid"`
	CorporationName string `json:"CorporationName" gorm:"column:corporationname"`
	LineId          int64  `json:"LineId" gorm:"column:lineid"`
	LineName        string `json:"LineName" gorm:"column:linename"`
}

func (r *LineVehicleMileageReport) GetNotApprovalLine(corporationIds []int64, startAt, endAt time.Time) []NotApprovalLineItem {
	var reports []NotApprovalLineItem
	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table).Select("LineId", "LineName", "CorporationId", "CorporationName").
		Where("IsApproval = ?", util.StatusForFalse)

	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}

	tx.Group("LineId,LineName,CorporationId,CorporationName").Scan(&reports)

	return reports
}

func (r *LineVehicleMileageReport) GetDriverRunMileage(isApproval int64, driverId int64, startAt, endAt time.Time) int64 {
	var mileage int64
	table := r.BuildTable(startAt, endAt)
	tx := model.DB().Table("(?) AS v", table).Select("SUM(TotalMileage) AS TotalMileage")

	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if driverId > 0 {
		tx.Where("DriverId = ?", driverId)
	}

	tx.Scan(&mileage)

	return mileage
}

type LineVehicleMileageReportLog struct {
	model.PkId
	TopCorporationId int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	CorporationId    int64           `json:"CorporationId" gorm:"column:corporationid;type:bigint;default:0;comment:线路所属的机构（车队）ID;index:report_log_corp_lineid_reportat_index"`
	CorporationName  string          `json:"CorporationName" gorm:"column:corporationname;type:varchar;default:;comment:线路所属的机构（车队）"`
	LineId           int64           `json:"LineId" gorm:"column:lineid;type:integer;default:0;comment:线路ID;index:report_log_corp_lineid_reportat_index"`
	LineName         string          `json:"LineName" gorm:"column:linename;type:varchar(100);default:;comment:线路名称"`
	ReportAt         model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:报表日期;index:report_log_corp_lineid_reportat_index"`
	VehicleId        int64           `json:"VehicleId" gorm:"column:vehicleid;type:integer;default:0;comment:车辆ID"`
	License          string          `json:"License" gorm:"column:license;type:varchar(100);default:;comment:车牌号"`
	DriverId         int64           `json:"DriverId" gorm:"column:driverid;type:integer;default:0;comment:司机ID"`
	DriverName       string          `json:"DriverName" gorm:"column:drivername;type:varchar(100);default:;comment:司机姓名"`
	FrequencyIndex   string          `json:"FrequencyIndex" gorm:"column:frequencyindex;type:varchar(50);default:;comment:班次"`
	Scene            string          `json:"Scene" gorm:"column:scene;type:varchar(100);default:;comment:操作类型"`
	OperateName      string          `json:"OperateName" gorm:"column:operatename;type:varchar(100);default:;comment:操作名称"`
	BeforeData       model.JSON      `json:"BeforeData" gorm:"column:beforedata;type:json;default:null;comment:修改之前的数据"`
	AfterData        model.JSON      `json:"AfterData" gorm:"column:afterdata;type:json;default:null;comment:修改之后的数据"`
	OpUserName       string          `json:"OpUserName" gorm:"column:opusername;type:varchar;default:;comment:操作人"`
	model.Timestamp
}

func (log *LineVehicleMileageReportLog) BeforeCreate(db *gorm.DB) error {
	log.Id = model.Id()
	return nil
}

func (log *LineVehicleMileageReportLog) Create() error {
	return model.DB().Create(&log).Error
}

func (log *LineVehicleMileageReportLog) CreateByTx(tx *gorm.DB) error {
	return tx.Create(&log).Error
}

func (log *LineVehicleMileageReportLog) GetBy(corporationId, lineId int64, startAt, endAt time.Time, paginator model.Paginator) ([]LineVehicleMileageReportLog, int64) {
	var logs []LineVehicleMileageReportLog

	tx := model.DB().Model(&LineVehicleMileageReportLog{}).Where("CorporationId = ? AND LineId = ? AND ReportAt >= ? AND ReportAt <= ?",
		corporationId, lineId, startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))

	var count int64
	tx.Count(&count)

	tx.Order("ReportAt DESC").Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&logs)

	return logs, count
}

type LineVehicleMileageReportVersion struct {
	model.PkId
	TopCorporationId int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	CorporationId    int64           `json:"CorporationId" gorm:"column:corporationid;type:bigint;default:0;comment:线路所属的机构（车队）ID;index:report_log_corp_lineid_reportat_index"`
	CorporationName  string          `json:"CorporationName" gorm:"column:corporationname;type:varchar;default:;comment:线路所属的机构（车队）"`
	LineId           int64           `json:"LineId" gorm:"column:lineid;type:integer;default:0;comment:线路ID;index:report_log_corp_lineid_reportat_index"`
	LineName         string          `json:"LineName" gorm:"column:linename;type:varchar(100);default:;comment:线路名称"`
	ReportAt         model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:报表日期;index:report_log_corp_lineid_reportat_index"`
	Scene            string          `json:"Scene" gorm:"column:scene;type:varchar(100);default:;comment:操作类型 save=>计算保存 history=>历史数据更新  recover=>版本恢复"`
	Data             model.JSON      `json:"-" gorm:"column:data;type:json;default:null;comment:保存的数据"`
	model.OpUser
	model.Timestamp

	VersionData model.JSON `json:"VersionData" gorm:"-"`
}

func (v *LineVehicleMileageReportVersion) BeforeCreate(db *gorm.DB) error {
	v.Id = model.Id()
	return nil
}

func (v *LineVehicleMileageReportVersion) Create() error {
	return model.DB().Create(&v).Error
}

func (v *LineVehicleMileageReportVersion) CreateByTx(tx *gorm.DB) error {
	return tx.Create(&v).Error
}

func (v *LineVehicleMileageReportVersion) GetBy(corporationId, lineId int64, reportAt time.Time) []LineVehicleMileageReportVersion {
	var versions []LineVehicleMileageReportVersion

	tx := model.DB().Model(&LineVehicleMileageReportVersion{}).Where("CorporationId = ? AND LineId = ? AND ReportAt = ?",
		corporationId, lineId, reportAt.Format(model.DateFormat))

	tx.Order("CreatedAt DESC").Find(&versions)

	return versions
}

func (v *LineVehicleMileageReportVersion) FirstBy(id int64) LineVehicleMileageReportVersion {
	var version LineVehicleMileageReportVersion

	model.DB().Model(&LineVehicleMileageReportVersion{}).Where("Id = ?", id).Find(&version)

	return version
}

type LineSalaryReportSetting struct {
	model.PkId
	TopCorporationId int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	CorporationId    int64           `json:"CorporationId" gorm:"column:corporationid;type:bigint;default:0;comment:线路所属的机构（车队）ID;"`
	CorporationName  string          `json:"CorporationName" gorm:"column:corporationname;type:varchar;default:;comment:线路所属的机构（车队）"`
	LineId           int64           `json:"LineId" gorm:"column:lineid;type:integer;default:0;comment:线路ID;"`
	LineName         string          `json:"LineName" gorm:"column:linename;type:varchar(100);default:;comment:线路名称"`
	StartAt          model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始日期;"`
	EndAt            model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束日期;"`
	More             string          `json:"More" gorm:"column:more;type:text;comment:备注"`
	model.Timestamp
	model.OpUser
}

func (ls *LineSalaryReportSetting) BeforeCreate(db *gorm.DB) error {
	ls.Id = model.Id()
	return nil
}

func (ls *LineSalaryReportSetting) UpdateOrCreate() error {
	var record LineSalaryReportSetting
	model.DB().Model(&LineSalaryReportSetting{}).Where("CorporationId = ? AND LineId = ? AND StartAt = ? AND EndAt = ?", ls.CorporationId, ls.LineId, ls.StartAt.String(), ls.EndAt.String()).First(&record)
	if record.Id > 0 {
		return model.DB().Model(&LineSalaryReportSetting{}).Where("Id = ?", record.Id).Updates(&ls).Error
	}
	return model.DB().Create(&ls).Error
}

func (ls *LineSalaryReportSetting) GetMoreByLineIdStartEndAt(corporationId, lineId int64, startAt, endAt time.Time) string {
	var record LineSalaryReportSetting
	model.DB().Model(&LineSalaryReportSetting{}).Where("CorporationId = ? AND LineId = ? AND StartAt = ? AND EndAt = ?", corporationId, lineId, startAt.Format(model.DateFormat), endAt.Format(model.DateFormat)).First(&record)
	return record.More
}
