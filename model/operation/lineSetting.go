package operation

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"encoding/json"
	"gorm.io/gorm"
	"time"
)

type OperationLineSetting struct {
	model.PkId
	TopCorporationId int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	CorporationId    int64           `json:"CorporationId" gorm:"column:corporationid;type:bigint;default:0;comment:线路所属的机构（车队）ID;index:corp_lineid_start_end_index" validate:"required"`
	CorporationName  string          `json:"CorporationName" gorm:"column:corporationname;type:varchar;default:;comment:线路所属的机构（车队）" validate:"required"`
	LineId           int64           `json:"LineId" gorm:"column:lineid;type:integer;default:0;comment:线路ID;index:corp_lineid_start_end_index" validate:"required"`
	LineName         string          `json:"LineName" gorm:"column:linename;type:varchar(100);default:;comment:线路名称"`
	StartAt          model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:设置生效开始日期，业务日期;index:corp_lineid_start_end_index" validate:"required"`
	EndAt            model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:设置生效结束日期，业务日期;index:corp_lineid_start_end_index" validate:"required"`
	WorkRate         int64           `json:"WorkRate" json:"WorkRate" gorm:"column:workrate;type:smallint;default:1;comment:生效频率 单位：天  例：2=>在时间范围内每2天生效一次"`
	SettingItem      model.JSON      `json:"SettingItem" gorm:"column:settingitem;type:json;comment:配置项值" validate:"required"`
	model.OpUser
	model.Timestamp

	UpdateHistoryColumns []string `json:"UpdateHistoryColumns" gorm:"-"` // 修改历史里程报表的字段
	IsUpdateHistory      int64    `json:"IsUpdateHistory" gorm:"-"`      //是否更新历史数据
	IsUpdateManualData   int64    `json:"IsUpdateManualData" gorm:"-"`   //是否更新手动添加的数据
	OnlyUpdateDate       int64    `json:"OnlyUpdateDate" gorm:"-"`       //1是  2否
}

type OperationLineSettingSettingItem struct {
	FullRatedMileage          int64 `json:"FullRatedMileage"`          //全程核定单圈公里 单位：米
	UpFullRatedMileage        int64 `json:"UpFullRatedMileage"`        //上行全程核定单圈公里 单位：米  双向环形线路配置
	DownFullRatedMileage      int64 `json:"DownFullRatedMileage"`      //下行全程核定单圈公里 单位：米  双向环形线路配置
	UpFullRatedWorkTime       int64 `json:"UpFullRatedWorkTime"`       //上行全程核定单圈岗上工时 单位：秒
	UpFullRatedNotWorkTime    int64 `json:"UpFullRatedNotWorkTime"`    //上行全程核定单圈岗下工时 单位：秒
	DownFullRatedWorkTime     int64 `json:"DownFullRatedWorkTime"`     //下行全程核定单圈岗上工时 单位：秒
	DownFullRatedNotWorkTime  int64 `json:"DownFullRatedNotWorkTime"`  //下行全程核定单圈岗下工时 单位：秒
	HasRangeTrip              int64 `json:"HasRangeTrip"`              //是否存在区间趟次 1是 2否
	RangeRatedMileage         int64 `json:"RangeRatedMileage"`         //区间核定单圈公里 单位：米
	UpRangeRatedMileage       int64 `json:"UpRangeRatedMileage"`       //上行区间核定单圈公里 单位：米  双向环形线路配置
	DownRangeRatedMileage     int64 `json:"DownRangeRatedMileage"`     //下行区间核定单圈公里 单位：米  双向环形线路配置
	UpRangeRatedWorkTime      int64 `json:"UpRangeRatedWorkTime"`      //上行区间核定单圈岗上工时 单位：秒
	UpRangeRatedNotWorkTime   int64 `json:"UpRangeRatedNotWorkTime"`   //上行区间核定单圈岗下工时 单位：秒
	DownRangeRatedWorkTime    int64 `json:"DownRangeRatedWorkTime"`    //下行区间核定单圈岗上工时 单位：秒
	DownRangeRatedNotWorkTime int64 `json:"DownRangeRatedNotWorkTime"` //下行区间核定单圈岗下工时 单位：秒
	FrequencyType             int64 `json:"FrequencyType"`             //班制  1双班  2单班  3混合班
	FrequencyDay              int64 `json:"FrequencyDay"`              //班制对应的天数 单位：天
	FullAndRangeInOutDepotField

	//区域里程配置
	CityCenter        float64                  `json:"CityCenter"`        //市级 单位% 两位小数
	Jiaojiang         float64                  `json:"Jiaojiang"`         //椒江区 单位% 两位小数
	Taizhouwan        float64                  `json:"Taizhouwan"`        //台州湾区 单位% 两位小数
	Huangyan          float64                  `json:"Huangyan"`          //黄岩区 单位% 两位小数
	Luqiao            float64                  `json:"Luqiao"`            //路桥区 单位% 两位小数
	Linhai            float64                  `json:"Linhai"`            //临海 单位% 两位小数
	HasFrequencyIndex int64                    `json:"HasFrequencyIndex"` //是否有班次进出场 1是 2否
	FrequencyItems    []OperationFrequencyItem `json:"FrequencyItems"`    //班次进出场的配置

	//行车计划配置 （有班次进出场时 行车计划自动填充班次进出场）
	IsLinkLinePlan int64  `json:"IsLinkLinePlan"` //是否关联行车计划 1是 2否
	LinePlanId     int64  `json:"LinePlanId"`     //行车计划ID
	LinePlanName   string `json:"LinePlanName"`   //行车计划名称
}

// OperationFrequencyItem 班次进出场的配置
type OperationFrequencyItem struct {
	Index          int64  `json:"Index"`          //班次
	PlanDepartAt   int64  `json:"PlanDepartAt"`   //发车时间
	PlanDepartStop string `json:"PlanDepartStop"` //发车站点
	FullAndRangeInOutDepotField
}

type FullAndRangeInOutDepotField struct {
	FullInOutDepotMileage       int64 `json:"FullInOutDepotMileage"`       //全程进出场公里 单位：米
	FullAssistantMileage        int64 `json:"FullAssistantMileage"`        //全程辅助公里 单位：米
	FullInOutDepotTime          int64 `json:"FullInOutDepotTime"`          //全程进出场时长 单位：秒
	FullAssistantTime           int64 `json:"FullAssistantTime"`           //全程辅助时长 单位：秒
	RangeInOutDepotMileage      int64 `json:"RangeInOutDepotMileage"`      //区间进出场公里 单位：米
	RangeAssistantMileage       int64 `json:"RangeAssistantMileage"`       //区间辅助公里 单位：米
	RangeInOutDepotTime         int64 `json:"RangeInOutDepotTime"`         //区间进出场时长 单位：秒
	RangeAssistantTime          int64 `json:"RangeAssistantTime"`          //区间辅助时长 单位：秒
	NightBefore22WorkTimeLength int64 `json:"NightBefore22WorkTimeLength"` //22点前夜班时长 单位：秒
	NightAfter22WorkTimeLength  int64 `json:"NightAfter22WorkTimeLength"`  //22点后夜班时长 单位：秒
	AttendanceType              int64 `json:"AttendanceType"`              //出勤类型  1全班 2半班 3小机动  4大机动
}

type LineReportSettingTimeLength struct {
	StartAt       model.LocalTime `json:"StartAt"`       //开始时间
	EndAt         model.LocalTime `json:"EndAt"`         //结束时间
	Value         int64           `json:"Value"`         //时长 单位：秒
	FrequencyType int64           `json:"FrequencyType"` //班制 1双班  2单班  3混合班
	FrequencyDay  int64           `json:"FrequencyDay"`  //班制对应的天数
}

func (ols *OperationLineSetting) BeforeCreate(db *gorm.DB) error {
	ols.Id = model.Id()
	ols.CreatedAt = model.LocalTime(time.Now())
	return nil
}

func (ols *OperationLineSetting) Create() error {
	return model.DB().Create(&ols).Error
}

func (ols *OperationLineSetting) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&ols).Error
}

func (ols *OperationLineSetting) Update(tx *gorm.DB) error {
	return tx.Select("*").Updates(&ols).Error
}

func (ols *OperationLineSetting) GetByLineId(corporationId, lineId int64) []OperationLineSetting {
	var settings []OperationLineSetting
	tx := model.DB().Model(&OperationLineSetting{}).Where("LineId = ?", lineId)
	if corporationId > 0 {
		tx.Where("CorporationId = ?", corporationId)
	}
	tx.Order("StartAt DESC").Find(&settings)
	return settings
}

func (ols *OperationLineSetting) FirstByLineId(corporationId, lineId int64, at time.Time) OperationLineSetting {
	var dateSetting OperationLineSettingHasDate
	model.DB().Model(&OperationLineSettingHasDate{}).Where("CorporationId = ? AND LineId = ? AND DateAt = ?", corporationId, lineId, at.Format(model.DateFormat)).First(&dateSetting)
	if dateSetting.Id == 0 {
		return OperationLineSetting{}
	}

	var setting OperationLineSetting
	model.DB().Model(&OperationLineSetting{}).Where("CorporationId = ? AND LineId = ? AND Id = ?", corporationId, lineId, dateSetting.OperationLineSettingId).First(&setting)

	return setting
}

func (ols *OperationLineSetting) FirstById(id int64) OperationLineSetting {
	var setting OperationLineSetting
	model.DB().Model(&OperationLineSetting{}).Where("Id = ?", id).First(&setting)

	return setting
}

//func (ols *OperationLineSetting) IsExistTimeCross(corporationId, lineId int64, start, end time.Time) bool {
//	var count int64
//	tx := model.DB().Model(&OperationLineSetting{}).Where("CorporationId = ? AND LineId = ? AND StartAt <= ? AND EndAt >= ?", corporationId, lineId, end.Format(model.DateFormat), start.Format(model.DateFormat))
//
//	if ols.Id > 0 {
//		tx = tx.Where("Id != ?", ols.Id)
//	}
//
//	tx.Count(&count)
//
//	return count > 0
//}

func (ols *OperationLineSetting) GetTimeCrossSetting(corporationIds []int64, lineId int64, start, end time.Time) []OperationLineSetting {
	dateSettingIds := (&OperationLineSettingHasDate{}).GetSettingIds(corporationIds, lineId, start, end)

	var settings []OperationLineSetting
	model.DB().Model(&OperationLineSetting{}).Where("CorporationId IN ? AND Id IN ?", corporationIds, dateSettingIds).
		Order("StartAt ASC").Find(&settings)

	return settings
}

func (ols *OperationLineSetting) IsExistRangeTrip(corporationIds []int64, lineId int64, start, end time.Time) bool {

	dateSettingIds := (&OperationLineSettingHasDate{}).GetSettingIds(corporationIds, lineId, start, end)

	var settings []OperationLineSetting
	tx := model.DB().Model(&OperationLineSetting{}).Where("CorporationId IN ? AND Id IN ?", corporationIds, dateSettingIds)

	tx.Find(&settings)

	for _, setting := range settings {
		var settingItem OperationLineSettingSettingItem
		err := json.Unmarshal(setting.SettingItem, &settingItem)
		if err != nil {
			log.ErrorFields("CalcLineMileage setting.SettingItem json.Unmarshal error", map[string]interface{}{"err": err})
		}
		if settingItem.HasRangeTrip == 1 {
			return true
		}
	}
	return false

}

func (ols *OperationLineSetting) Delete(tx *gorm.DB) error {
	return tx.Where("Id = ?", ols.Id).Delete(&OperationLineSetting{}).Error

}

type OperationLineSettingHasDate struct {
	model.PkId
	OperationLineSettingId int64           `json:"OperationLineSettingId" gorm:"column:operationlinesettingid;type:bigint;comment:OperationLineSetting表ID"`
	CorporationId          int64           `json:"CorporationId" gorm:"column:corporationid;type:bigint;default:0;comment:线路所属的机构（车队）ID;uniqueIndex:corporationid_lineid_has_date_unique_index"`
	LineId                 int64           `json:"LineId" gorm:"column:lineid;type:integer;default:0;comment:线路ID;uniqueIndex:corporationid_lineid_has_date_unique_index"`
	DateAt                 model.LocalTime `json:"DateAt" gorm:"column:dateat;type:timestamp;comment:生效日期;uniqueIndex:corporationid_lineid_has_date_unique_index"`
	model.Timestamp
}

func (d *OperationLineSettingHasDate) BeforeCreate(tx *gorm.DB) error {
	d.Id = model.Id()
	return nil
}

func (d *OperationLineSettingHasDate) Delete(tx *gorm.DB, settingId int64) error {
	return tx.Where("OperationLineSettingId = ?", settingId).Delete(&OperationLineSettingHasDate{}).Error
}

func (d *OperationLineSettingHasDate) Create(tx *gorm.DB, dates []OperationLineSettingHasDate) error {
	return tx.Create(dates).Error
}

func (d *OperationLineSettingHasDate) FirstBy(corporationId, lineId int64, dateAt time.Time) OperationLineSettingHasDate {
	var setting OperationLineSettingHasDate
	model.DB().Model(&OperationLineSettingHasDate{}).Where("CorporationId = ? AND LineId = ? AND DateAt = ?", corporationId, lineId, dateAt.Format(model.DateFormat)).First(&setting)

	return setting
}

func (d *OperationLineSettingHasDate) GetBy(corporationId, lineId int64, start, end time.Time) []OperationLineSettingHasDate {
	var settings []OperationLineSettingHasDate
	model.DB().Model(&OperationLineSettingHasDate{}).Where("CorporationId = ? AND LineId = ? AND DateAt >= ? AND DateAt <= ?", corporationId, lineId, start.Format(model.DateFormat), end.Format(model.DateFormat)).Find(&settings)

	return settings
}

func (d *OperationLineSettingHasDate) GetSettingIds(corporationIds []int64, lineId int64, start, end time.Time) []int64 {
	var settingIds []int64
	model.DB().Model(&OperationLineSettingHasDate{}).Select("OperationLineSettingId").
		Where("CorporationId IN ? AND LineId = ? AND DateAt >= ? AND DateAt <= ?", corporationIds, lineId, start.Format(model.DateFormat), end.Format(model.DateFormat)).
		Group("operationlinesettingid").Pluck("OperationLineSettingId", &settingIds)

	return settingIds
}

func (d *OperationLineSettingHasDate) IsExistDates(corporationId, lineId, settingId int64, dateAts []string) bool {
	var count int64
	tx := model.DB().Model(&OperationLineSettingHasDate{}).Where("CorporationId = ? AND LineId = ? AND DateAt IN ?", corporationId, lineId, dateAts)
	if settingId > 0 {
		tx.Where("OperationLineSettingId != ?", settingId)
	}

	tx.Count(&count)
	return count > 0
}

func (d *OperationLineSettingHasDate) IsExistDateRange(corporationId, lineId int64, start, end time.Time) bool {
	var count int64
	model.DB().Model(&OperationLineSettingHasDate{}).
		Where("CorporationId = ? AND LineId = ? AND DateAt >= ? AND DateAt <= ?", corporationId, lineId, start.Format(model.DateFormat), end.Format(model.DateFormat)).Count(&count)
	return count > 0
}

type OperationLineSettingLog struct {
	model.PkId
	OperationLineSettingId int64      `json:"OperationLineSettingId" gorm:"column:operationlinesettingid;type:bigint;comment:OperationLineSetting表ID"`
	CorporationId          int64      `json:"CorporationId" gorm:"column:corporationid;type:bigint;default:0;comment:线路所属的机构（车队）ID;"`
	CorporationName        string     `json:"CorporationName" gorm:"column:corporationname;type:varchar;default:;comment:线路所属的机构（车队）" validate:"required"`
	LineId                 int64      `json:"LineId" gorm:"column:lineid;type:integer;default:0;comment:线路ID;"`
	LineName               string     `json:"LineName" gorm:"column:linename;type:varchar(100);default:;comment:线路名称"`
	BeforeData             model.JSON `json:"BeforeData" gorm:"column:beforedate;type:json;comment:修改前数据;"`
	AfterData              model.JSON `json:"AfterData" gorm:"column:afterdate;type:json;comment:修改后数据;"`
	Scene                  string     `json:"Scene" gorm:"column:scene;type:varchar;comment:场景值"`
	model.OpUser
	model.Timestamp
}

func (o *OperationLineSettingLog) BeforeCreate(db *gorm.DB) error {
	o.Id = model.Id()
	return nil
}

func (o *OperationLineSettingLog) Create() error {
	return model.DB().Create(o).Error
}

func (o *OperationLineSettingLog) GetBy(corporationIds, lineIds []int64, startAt, endAt time.Time, paginator model.Paginator) ([]OperationLineSettingLog, int64) {
	var logs []OperationLineSettingLog
	tx := model.DB().Model(&OperationLineSettingLog{})
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if !startAt.IsZero() {
		tx.Where("CreatedAt >= ?", startAt.Format(model.TimeFormat))
	}
	if !endAt.IsZero() {
		tx.Where("CreatedAt <= ?", endAt.Format(model.TimeFormat))
	}

	var count int64
	tx.Count(&count)
	tx.Order("CreatedAt desc").Offset(paginator.Offset).Limit(paginator.Limit).Find(&logs)

	return logs, count
}
