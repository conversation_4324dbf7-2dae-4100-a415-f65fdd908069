package operation

import (
	"app/org/scs/erpv2/api/model"
	"fmt"
	"gorm.io/gorm"
)

type TravelNeed struct {
	model.PkId
	TopCorporationId int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	Name             string `json:"Name" gorm:"column:name;type:varchar(255);default:;comment:表单名称"`
	Code             string `json:"Code" gorm:"column:code;uniqueIndex:travel_need_code_unique_index;type:varchar(255);default:;comment:表单Id"`
	Remark           string `json:"Remark" gorm:"column:remark;type:text;default:;comment:备注"`
	Status           int64  `json:"Status" gorm:"column:status;type:smallint;default:0;comment:1:激活 2:停用"`
	QrCodePath       string `json:"QrCodePath" gorm:"column:qrcodepath;type:varchar;comment:二维码存储路径"`
	model.OpUser
	model.Timestamp
}

const (
	TravelNeed_Status_Valid   = 1 // 有效
	TravelNeed_Status_NoValid = 2 // 无效
)

const (
	TravelDayType_Monday    = 0x01 << 0 // 周一 1
	TravelDayType_Tuesday   = 0x01 << 1 // 周二 2
	TravelDayType_Wednesday = 0x01 << 2 // 周三 4
	TravelDayType_Thursday  = 0x01 << 3 // 周四 8
	TravelDayType_Friday    = 0x01 << 4 // 周六 16
	TravelDayType_Saturday  = 0x01 << 5 // 周六 32
	TravelDayType_Sunday    = 0x01 << 6 // 周日 64
)

//出行目的: bit位 1:上班;2:上学;3:业务;4:接送人;5:景点旅游;6:就医;7:文化体育娱乐休闲;8:日常生活;9:回家
const (
	TravelPurpose_GoToWork             = 0x01 << 0 // 上班
	TravelPurpose_GoToSchool           = 0x01 << 1 // 上学
	TravelPurpose_Business             = 0x01 << 2 // 业务
	TravelPurpose_Pickup               = 0x01 << 3 // 接送人
	TravelPurpose_Sightseeing          = 0x01 << 4 //景点旅游
	TravelPurpose_SeekMedicalTreatment = 0x01 << 5 // 就医
	TravelPurpose_PhysicalExercise     = 0x01 << 6 // 文化体育娱乐休闲
	TravelPurpose_DailyLife            = 0x01 << 7 // 日常生活
	TravelPurpose_GoHome               = 0x01 << 8 // 回家
)

var (
	TravelDayArr     = []int64{TravelDayType_Monday, TravelDayType_Tuesday, TravelDayType_Wednesday, TravelDayType_Thursday, TravelDayType_Friday, TravelDayType_Saturday, TravelDayType_Sunday}
	TravelPurposeArr = []int64{TravelPurpose_GoToWork, TravelPurpose_GoToSchool, TravelPurpose_Business, TravelPurpose_Pickup, TravelPurpose_Sightseeing, TravelPurpose_SeekMedicalTreatment, TravelPurpose_PhysicalExercise, TravelPurpose_DailyLife, TravelPurpose_GoHome}
)

type TravelNeedDetail struct {
	model.PkId
	TopCorporationId int64   `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	TravelNeedId     int64   `json:"TravelNeedId" gorm:"column:travelneedid;type:bigint;default:0;comment:出行需求ID"`
	TravelDay        int64   `json:"TravelDay" gorm:"column:travelday;type:smallint;default:0;comment:出行天: bit位: 1:周一;2:周二 ~;7:周日"`
	TravelPurpose    int64   `json:"TravelPurpose" gorm:"column:travelpurpose;type:smallint;default:0;comment:出行目的: bit位: 1:上班 2:上学 3:业务 4:接送人 5:景点旅游 6:就医 7:文化体育娱乐休闲 8:日常生活 9:回家"`
	TravelMode       int64   `json:"TravelMode" gorm:"column:travelmode;type:smallint;default:0;comment:出行方式: 1:全程步行 2:公交车 3:单位或超市班车 4:驾驶小汽车 5:乘坐小汽车 6:出租车 7:摩托车 8:电动自行车或其他助动车 9:脚踏自行车 10:其他"`
	DepartAddress    string  `json:"DepartAddress" gorm:"column:departaddress;type:text;default:0;comment:出发地址"`
	DepartLongitude  float64 `json:"DepartLongitude" gorm:"column:departlongitude;type:numeric;default:0;comment:出发经度"`
	DepartLatitude   float64 `json:"DepartLatitude" gorm:"column:departlatitude;type:numeric;default:0;comment:出发经度"`
	DepartTime       int64   `json:"DepartTime" gorm:"column:departtime;type:integer;default:0;comment:出发时间"`
	ArriveAddress    string  `json:"ArriveAddress" gorm:"column:arriveaddress;type:text;default:0;comment:到达地址"`
	ArriveLongitude  float64 `json:"ArriveLongitude" gorm:"column:arrivelongitude;type:numeric;default:0;comment:到达经度"`
	ArriveLatitude   float64 `json:"ArriveLatitude" gorm:"column:arrivelatitude;type:numeric;default:0;comment:到达经度"`
	ArriveTime       int64   `json:"ArriveTime" gorm:"column:arrivetime;type:integer;default:0;comment:到达时间"`
	Name             string  `json:"Name" gorm:"column:name;type:varchar(255);default:;comment:姓名"`
	Sex              int64   `json:"Sex" gorm:"column:sex;type:smallint;default:0;comment:性别: 0:未知 1:男 2:女"`
	AgeRange         int64   `json:"AgeRange" gorm:"column:agerange;type:smallint;default:0;comment:年龄段: 1:20-30;2:30-40;3:40-50;4:50-60"`
	Phone            string  `json:"Phone" gorm:"column:phone;type:varchar(64);default:;comment:手机号"`
	Occupation       int64   `json:"Occupation" gorm:"column:occupation;type:smallint;default:0;comment:职业:1:学龄前儿童 2:机关、企业、事业单位负责人 3:专业技术人员 4:办事人员和有关人员 5:商业服务人员 6:农林鱼牧业生产人员 7:离退休人员 8:生产运输设备操作人员及有关人员 9:在校学生 10:快递配送及物流人员 11:待业无业人员。"`
	YearIncome       int64   `json:"YearIncome" gorm:"column:yearincome;type:smallint;default:0;comment:年收入:1:0-10w 2:10-20w 3:20-30w"`
	FamilyAddress    string  `json:"FamilyAddress" gorm:"column:familyaddress;type:text;default:;comment:家庭地址"`
	FamilyLongitude  float64 `json:"FamilyLongitude" gorm:"column:familylongitude;type:numeric;default:0;comment:家庭地址经度"`
	FamilyLatitude   float64 `json:"FamilyLatitude" gorm:"column:familylatitude;type:numeric;default:0;comment:家庭地址纬度"`
	model.Timestamp
}

func (this *TravelNeed) BeforeCreate(db *gorm.DB) error {
	this.Id = model.Id()
	return nil
}

func (this *TravelNeed) Create(tx *gorm.DB) error {
	return tx.Create(&this).Error
}

func (this *TravelNeed) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&this).Error
}

func (this *TravelNeed) Update(tx *gorm.DB, as map[string]interface{}) error {
	return tx.Model(&TravelNeed{}).Where("Id = ?", this.Id).Updates(as).Error
}

func (this *TravelNeed) UpdateColumn(tx *gorm.DB, column string, value interface{}) error {
	return tx.Model(&TravelNeed{}).Where("Id = ?", this.Id).Update(column, value).Error
}

func (this *TravelNeed) GetById(Id int64) error {
	return model.DB().Model(&TravelNeed{}).Where("Id = ? ", Id).First(this).Error
}

func (this *TravelNeed) GetByCode(code string) error {
	return model.DB().Model(&TravelNeed{}).Where("Code = ? ", code).First(this).Error
}

func (this *TravelNeed) GetBy(p model.Paginator) ([]TravelNeed, int64, error) {
	var items []TravelNeed
	var tx = model.DB().Model(&TravelNeed{})

	if this.TopCorporationId > 0 {
		tx.Where(" TopCorporationId=?", this.TopCorporationId)
	}

	if this.Name != "" {
		tx.Where(" Name LIKE ?", "%"+this.Name+"%")
	}

	var total int64
	tx.Count(&total)
	if total == 0 {
		return nil, 0, nil
	}

	err := tx.Offset(p.Offset).Limit(p.Limit).Order(" CreatedAt DESC ").Find(&items).Error
	return items, total, err
}

func (this *TravelNeed) DeleteById(tx *gorm.DB, id int64) error {
	return tx.Where("Id = ? ", id).Delete(&TravelNeed{}).Error
}

func (this *TravelNeedDetail) BeforeCreate(db *gorm.DB) error {
	this.Id = model.Id()
	return nil
}

func (this *TravelNeedDetail) Create(tx *gorm.DB) error {
	return tx.Create(&this).Error
}

func (this *TravelNeedDetail) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&this).Error
}

func (this *TravelNeedDetail) Update(tx *gorm.DB, as map[string]interface{}) error {
	return tx.Model(&TravelNeedDetail{}).Where("Id = ?", this.Id).Updates(as).Error
}

func (this *TravelNeedDetail) UpdateColumn(tx *gorm.DB, column string, value interface{}) error {
	return tx.Model(&TravelNeedDetail{}).Where("Id = ?", this.Id).Update(column, value).Error
}

func (this *TravelNeedDetail) GetById(Id int64) error {
	return model.DB().Model(&TravelNeedDetail{}).Where("Id = ? ", Id).First(this).Error
}

func (this *TravelNeedDetail) GetCountBy(travelNeedId int64) (int64, error) {
	var total int64
	err := model.DB().Model(&TravelNeedDetail{}).Where("TravelNeedId = ? ", travelNeedId).Count(&total).Error
	return total, err
}

type TravelNeedDetailParam struct {
	TravelNeedIds  []int64
	TravelPurposes []int64
}

func (this *TravelNeedDetail) GetBy(p model.Paginator, tnp *TravelNeedDetailParam) ([]TravelNeedDetail, int64, error) {
	var items []TravelNeedDetail
	var tx = model.DB().Model(&TravelNeedDetail{})

	if this.TopCorporationId > 0 {
		tx.Where(" TopCorporationId=?", this.TopCorporationId)
	}
	if this.TravelNeedId > 0 {
		tx.Where(" TravelNeedId=?", this.TravelNeedId)
	}

	if this.DepartAddress != "" {
		tx.Where(" DepartAddress LIKE  ? ", "%"+this.DepartAddress+"%")
	}
	if this.ArriveAddress != "" {
		tx.Where(" ArriveAddress LIKE  ? ", "%"+this.ArriveAddress+"%")
	}

	if tnp != nil {
		if len(tnp.TravelPurposes) > 0 {
			var filter string
			for _, travelPurpose := range tnp.TravelPurposes {
				if filter == "" {
					filter = fmt.Sprintf(" TravelPurpose & %v=%v ", travelPurpose, travelPurpose)
				} else {
					filter += fmt.Sprintf(" OR TravelPurpose & %v=%v ", travelPurpose, travelPurpose)
				}
			}
			tx.Where(filter)
		}

		if len(tnp.TravelNeedIds) > 0 {
			tx.Where(" TravelNeedId  IN ? ", tnp.TravelNeedIds)
		}
	}

	var total int64
	tx.Count(&total)
	if total == 0 {
		return nil, 0, nil
	}

	err := tx.Offset(p.Offset).Limit(p.Limit).Order(" CreatedAt DESC ").Find(&items).Error
	return items, total, err
}

func (this *TravelNeedDetail) DeleteById(tx *gorm.DB, id int64) error {
	return tx.Where("Id = ? ", id).Delete(&TravelNeedDetail{}).Error
}
