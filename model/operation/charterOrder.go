package operation

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

type CharterOrderBrief struct {
	model.PkId
	TopCorporationId    int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID;uniqueIndex:CharterOrderBrief_TopCorporationId_Code_index"`
	Code                string          `json:"Code" gorm:"column:code;type:varchar(255);default:;comment:工单单号;uniqueIndex:CharterOrderBrief_TopCorporationId_Code_index"`
	CustomerCompany     string          `json:"CustomerCompany" gorm:"column:customercompany;type:varchar(255);default:;comment:使用单位"`
	CustomerName        string          `json:"CustomerName" gorm:"column:customername;type:varchar(255);default:;comment:客户名称"`
	CustomerPhone       string          `json:"CustomerPhone" gorm:"column:customerphone;type:varchar(255);default:;comment:客户手机号"`
	ContractAttachments model.JSON      `json:"ContractAttachments" gorm:"column:contractattachments;type:json;default:;comment:合同附件"`
	InvoiceAttachments  model.JSON      `json:"InvoiceAttachments" gorm:"column:invoiceattachments;type:json;default:;comment:发票附件"`
	Remark              string          `json:"Remark" gorm:"column:remark;type:text;comment:备注"`
	StartAt             model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:使用开始时间"`
	EndAt               model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:使用结束时间"`
	SelfOrder           int64           `json:"SelfOrder"  gorm:"column:selforder;type:smallint;default:0;comment:是否自行接单: 1: 是 2 否"`
	model.OpUser
	model.EditOpUser
	model.Timestamp

	CharterOrderItems  []CharterOrder         `json:"CharterOrderItems"`
	DispatchOrderItems []CharterDispatchOrder `json:"DispatchOrderItems"`

	TotalVehicleNum   int64    `json:"TotalVehicleNum" gorm:"-"`
	TotalCorporations []string `json:"TotalCorporations" gorm:"-"`
	TotalAmount       int64    `json:"TotalAmount" gorm:"-"`
}

func (this *CharterOrderBrief) BeforeCreate(db *gorm.DB) error {
	this.Id = model.Id()
	return nil
}

func (this *CharterOrderBrief) Create(tx *gorm.DB) error {
	return tx.Omit(clause.Associations).Create(&this).Error
}

func (this *CharterOrderBrief) Updates(tx *gorm.DB) error {
	return tx.Select("CustomerCompany", "CustomerName", "CustomerPhone", "ContractAttachments", "InvoiceAttachments",
		"Remark", "StartAt", "EndAt", "EditUserId", "EditUserName", "SelfOrder").Updates(&this).Error
}

func (this *CharterOrderBrief) GetLatest(startAt, endAt time.Time) (item CharterOrderBrief) {
	model.DB().Model(&CharterOrderBrief{}).Where("CreatedAt >= ? AND CreatedAt <= ?", startAt, endAt).Order("CreatedAt DESC").Limit(1).First(&item)
	return item
}

func (this *CharterOrderBrief) GetById(id int64) (item CharterOrderBrief, err error) {
	err = model.DB().Model(&CharterOrderBrief{}).Where("Id = ?", id).First(&item).Error
	return
}

type CharterOrderBriefParam struct {
	IsPrecision          bool   `json:"IsPrecision"`
	CustomerCompany      string `json:"CustomerCompany"`
	AssignCorporationIds []int64
	StartAt              model.LocalTime
	EndAt                model.LocalTime
	model.Paginator
}

func (this *CharterOrderBrief) GetAll(p CharterOrderBriefParam) (items []CharterOrderBrief, totalCount int64, err error) {
	var tx = model.DB().
		Preload("CharterOrderItems", func(db *gorm.DB) *gorm.DB {
			return db.Order("Sort ASC")
		}).
		Preload("DispatchOrderItems", func(db *gorm.DB) *gorm.DB {
			return db.Order("ExecCorporationId ASC, CreatedAt ASC")
		}).Model(&CharterOrderBrief{}).
		Where("StartAt <= ? AND EndAt >= ?", p.EndAt, p.StartAt)
	if len(p.AssignCorporationIds) > 0 {
		var childTx = model.DB().Model(&CharterOrder{}).Select("CharterOrderBriefId").Where("AssignCorporationId IN  ?", p.AssignCorporationIds)
		tx = tx.Where("Id IN (?)", childTx)
	}

	if p.CustomerCompany != "" {
		if p.IsPrecision {
			tx = tx.Where(" CustomerCompany =? ", p.CustomerCompany)
		} else {
			tx = tx.Where(" CustomerCompany LIKE ?", "%"+p.CustomerCompany+"%")
		}
	}

	tx.Order("CreatedAt DESC")
	tx.Count(&totalCount)
	if totalCount == 0 {
		return nil, 0, err
	}

	err = tx.Limit(p.Limit).Offset(p.Offset).Find(&items).Error

	return
}

// 权限控制
func (this *CharterOrderBrief) GetBy(id int64, authCorpIds []int64) (item CharterOrderBrief, err error) {
	var tx = model.DB().
		Preload("CharterOrderItems", func(db *gorm.DB) *gorm.DB {
			if len(authCorpIds) > 0 {
				return db.Where("AssignCorporationId IN  ?", authCorpIds).Order("Sort ASC")
			} else {
				return db.Order("Sort ASC")
			}
		}).
		Preload("DispatchOrderItems", func(db *gorm.DB) *gorm.DB {
			if len(authCorpIds) > 0 {
				return db.Where("ExecCorporationId IN  ?", authCorpIds).Order("ExecCorporationId ASC, CreatedAt ASC")
			} else {
				return db.Order("ExecCorporationId ASC, CreatedAt ASC")
			}
		}).Model(&CharterOrderBrief{}).Where("Id =? ", id)

	tx.First(&item)
	return
}

func (r *CharterOrderBrief) BatchDelete(tx *gorm.DB, ids []int64) error {
	return tx.Model(&CharterOrderBrief{}).Where("Id IN ?", ids).Delete(&CharterOrderBrief{}).Error
}

type CharterOrder struct {
	model.PkId
	TopCorporationId    int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	CharterOrderBriefId int64           `json:"CharterOrderBriefId" gorm:"column:charterorderbriefid;type:bigint;default:0;uniqueIndex:charterorder_briefId_sort;comment:派车单ID"`
	CustomerName        string          `json:"CustomerName" gorm:"column:customername;type:varchar(255);default:;comment:客户名称"`
	CustomerPhone       string          `json:"CustomerPhone" gorm:"column:customerphone;type:varchar(255);default:;comment:客户手机号"`
	DepartAddress       string          `json:"DepartAddress" gorm:"column:departaddress;type:text;default:0;comment:出发地址"`
	ArriveAddress       string          `json:"ArriveAddress" gorm:"column:arriveaddress;type:text;default:0;comment:到达地址"`
	StartAt             model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:使用开始时间"`
	EndAt               model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:使用结束时间"`
	StartTime           int64           `json:"StartTime" gorm:"column:starttime;type:integer;default:0;comment:开始时间"`
	EndTime             int64           `json:"EndTime" gorm:"column:endtime;type:integer;default:0;comment:到达时间"`
	AssignCorporationId int64           `json:"AssignCorporationId" gorm:"column:assigncorporationid;type:bigint;default:0;comment:指派机构ID"`
	VehicleNum          int64           `json:"VehicleNum" gorm:"column:vehiclenum;type:integer;default:0;comment:车辆数"`
	Amount              int64           `json:"Amount" gorm:"column:amount;type:integer;default:0;comment:金额:单位:分"`
	Remark              string          `json:"Remark" gorm:"column:remark;type:text;comment:备注"`
	Sort                int64           `json:"Sort"  gorm:"column:sort;type:smallint;default:0;;uniqueIndex:charterorder_briefId_sort;comment:任务号:1,2,3,4,5"`

	model.OpUser
	model.Timestamp

	AssignCorporation string `json:"AssignCorporation" gorm:"-"` // 指派机构

}

func (this *CharterOrder) BeforeCreate(db *gorm.DB) error {
	this.Id = model.Id()
	return nil
}

func (this *CharterOrder) Create(tx *gorm.DB) error {
	return tx.Create(&this).Error
}

func (this *CharterOrder) GetMaxSort(tx *gorm.DB, coBriefId int64) (sort int64) {
	var co CharterOrder
	tx.Model(&CharterOrder{}).Where("CharterOrderBriefId = ?", coBriefId).Order(" Sort DESC ").First(&co)

	return co.Sort
}

func (this *CharterOrder) Updates(tx *gorm.DB) error {
	return tx.Select("CustomerName", "CustomerPhone", "DepartAddress", "ArriveAddress", "StartAt",
		"EndAt", "StartTime", "EndTime", "AssignCorporationId", "VehicleNum",
		"Amount", "Remark").Updates(&this).Error
}
func (this *CharterOrder) BatchDeleteWithCOBriefId(tx *gorm.DB, ids []int64) error {
	return tx.Model(&CharterOrder{}).Where("CharterOrderBriefId IN ?", ids).Delete(&CharterOrder{}).Error
}
func (this *CharterOrder) BatchDeleteWithIds(tx *gorm.DB, ids []int64) error {
	return tx.Model(&CharterOrder{}).Where("Id IN ?", ids).Delete(&CharterOrder{}).Error
}

func (this *CharterOrder) GetAllByCOBriefId(coBriefId int64) (items []CharterOrder, err error) {
	err = model.DB().Model(&CharterOrder{}).Where("CharterOrderBriefId = ?", coBriefId).
		Order(" Sort ASC").Find(&items).Error
	return
}

func (this *CharterOrder) GetBy(id int64) (items CharterOrder, err error) {
	err = model.DB().Model(&CharterOrder{}).Where("Id = ?", id).First(&items).Error
	return
}

type CharterDispatchOrder struct {
	model.PkId
	CharterOrderBriefId       int64      `json:"CharterOrderBriefId" gorm:"column:charterorderbriefid;type:bigint;default:0;comment:派车单ID"`
	AssociationCharterOrderId int64      `json:"AssociationCharterOrderId" gorm:"column:associationcharterorderid;type:bigint;default:0;comment:关联订单ID"`
	ExecCorporationId         int64      `json:"ExecCorporationId" gorm:"column:execcorporationid;type:bigint;default:0;comment:执行机构ID"`
	ExecVehicleId             int64      `json:"ExecVehicleId" gorm:"column:execvehicleid;type:integer;default:0;comment:执行车辆ID"`
	ExecDrivers               model.JSON `json:"ExecDrivers" gorm:"column:execdrivers;type:json;comment:执行司机"`
	TravelDates               string     `json:"TravelDates" gorm:"column:traveldates;type:text;comment:执行日期"`
	TripNum                   int64      `json:"TripNum" gorm:"column:tripnum;type:integer;default:0;comment:趟次数"`
	OperationMileage          int64      `json:"OperationMileage" gorm:"column:operationmileage;type:integer;default:0;comment:运营里程"`
	Amount                    int64      `json:"Amount" gorm:"column:amount;type:integer;default:0;comment:金额: 单位:分"`
	model.OpUser
	model.Timestamp

	ExecVehicleLicense string `json:"ExecVehicleLicense" gorm:"-"`
	ExecCorporation    string `json:"ExecCorporation" gorm:"-"`

	// 请求参数
	AssociationCharterOrderIndex int64 `json:"AssociationCharterOrderIndex" gorm:"-"`
}

type ExecDriver struct {
	DriverId    int64  `json:"DriverId"`
	DriverName  string `json:"DriverName"`
	DriverPhone string `json:"DriverPhone"`
}

func (this *CharterDispatchOrder) BeforeCreate(db *gorm.DB) error {
	this.Id = model.Id()
	return nil
}

func (this *CharterDispatchOrder) Create(tx *gorm.DB) error {
	return tx.Create(&this).Error
}

func (this *CharterDispatchOrder) Updates(tx *gorm.DB) error {
	return tx.Select("ExecCorporationId", "ExecVehicleId", "ExecDrivers", "TravelDates", "TripNum",
		"OperationMileage", "Amount", "AssociationCharterOrderId").Updates(&this).Error
}

func (this *CharterDispatchOrder) BatchDeleteWithCOBriefId(tx *gorm.DB, ids []int64) error {
	return tx.Model(&CharterDispatchOrder{}).Where("CharterOrderBriefId IN ?", ids).Delete(&CharterDispatchOrder{}).Error
}

func (this *CharterDispatchOrder) BatchDeleteWithIds(tx *gorm.DB, ids []int64) error {
	return tx.Model(&CharterDispatchOrder{}).Where("Id IN ?", ids).Delete(&CharterDispatchOrder{}).Error
}

func (this *CharterDispatchOrder) GetAllByCOBriefId(coBriefId int64) (items []CharterDispatchOrder, err error) {
	err = model.DB().Model(&CharterDispatchOrder{}).Where("CharterOrderBriefId = ?", coBriefId).
		Order("CreatedAt ASC ").
		Find(&items).Error
	return
}

func (this *CharterDispatchOrder) GetCountByCOId(coid int64) (total int64, err error) {
	err = model.DB().Model(&CharterDispatchOrder{}).Where("AssociationCharterOrderId = ?", coid).Count(&total).Error
	return
}

func (this *CharterDispatchOrder) GetById(id int64) (item CharterDispatchOrder, err error) {
	err = model.DB().Model(&CharterDispatchOrder{}).Where("Id = ?", id).First(&item).Error
	return
}
