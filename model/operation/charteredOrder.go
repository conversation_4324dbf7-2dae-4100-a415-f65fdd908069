package operation

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type CharteredOrder struct {
	model.PkId
	OrderId       string          `json:"OrderId" gorm:"column:orderid;type:varchar;default:;uniqueIndex:charterorder_order_id;comment:外部订单ID"`
	CustomerName  string          `json:"CustomerName" gorm:"column:customername;type:varchar(255);default:;comment:客户名称"`
	CustomerPhone string          `json:"CustomerPhone" gorm:"column:customerphone;type:varchar(255);default:;comment:客户手机号"`
	StartAt       model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:使用开始时间"`
	EndAt         model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:使用结束时间"`
	OrderTime     model.LocalTime `json:"OrderTime" gorm:"column:ordertime;type:timestamp;comment:订单创建时间"`
	PeopleCount   int64           `json:"PeopleCount" gorm:"column:peoplecount;type:integer;default:0;comment:人数"`
	OrderType     int64           `json:"OrderType" gorm:"column:ordertype;type:smallint;default:1;comment:订单类型 1VIP订单 2旅行团 3福建团 4老年团"`
	Status        int64           `json:"Status" gorm:"column:status;type:smallint;default:1;comment:订单状态  1进行中 2已完成"`
	model.Timestamp
}

func (m *CharteredOrder) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *CharteredOrder) Create(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

// FirstById 根据ID查询订单
func (m *CharteredOrder) FirstById(orderId string) CharteredOrder {
	var order CharteredOrder
	model.DB().Model(&CharteredOrder{}).Where("OrderId = ?", orderId).First(&order)

	return order
}

// CharteredOrderTrip 订单对应的行程  1个订单可对应多个行程
type CharteredOrderTrip struct {
	model.PkId
	CharteredOrderId int64           `json:"CharteredOrderId" gorm:"column:charteredorderid;type:bigint;comment:订单表主键ID"`
	FromParkingId    int64           `json:"FromParkingId" gorm:"column:fromparkingid;type:integer;comment:起点场站ID"`
	FromParkingName  string          `json:"FromParkingName" gorm:"column:fromparkingname;type:varchar;comment:起点场站名称"`
	ToParkingId      int64           `json:"ToParkingId" gorm:"column:toparkingid;type:integer;comment:终点场站ID"`
	ToParkingName    string          `json:"ToParkingName" gorm:"column:toparkingname;type:varchar;comment:终点场站名称"`
	OrderType        int64           `json:"OrderType" gorm:"column:ordertype;type:smallint;default:1;comment:订单类型 1VIP订单 2旅行团 3福建团 4老年团"`
	Status           int64           `json:"Status" gorm:"column:status;type:smallint;default:1;comment:行程状态 -2二维码已过期 -1起点错误 1等待中 2排队中 3退出排队 4系统取消排队（过号） 5已完成"`
	ConfirmTime      model.LocalTime `json:"ConfirmTime" gorm:"column:confirmtime;type:timestamp;comment:行程确认时间"`
	model.Timestamp
}

func (m *CharteredOrderTrip) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *CharteredOrderTrip) Create(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *CharteredOrderTrip) FirstById(id int64) CharteredOrderTrip {
	var trip CharteredOrderTrip
	model.DB().Model(&CharteredOrderTrip{}).Where("Id = ?", id).First(&trip)

	return trip
}

// CharteredOrderTripQueueJob 行程单的排序队列
type CharteredOrderTripQueueJob struct {
	model.PkId
	CharteredOrderTripId int64 `json:"CharteredOrderTripId" gorm:"column:charteredordertripid;type:bigint;comment:订单行程表主键ID"`
	QueueCode            int64 `json:"QueueCode" gorm:"column:queuecode;type:integer;comment:排队码"`
	InitSortNum          int64 `json:"InitSortNum" gorm:"column:initsortnum;type:smallint;comment:初始排队序号 当此条数据排队创建时（初始的排队位置），排在第几位（即前方有多少人 +1）"`
	CurrentSort          int64 `json:"CurrentSort" gorm:"column:currentsort;type:smallint;comment:当前排序号 在页面上的排序，升序（小的排前面，大的排后面）"`
	IsJumpQueue          int64 `json:"IsJumpQueue" gorm:"column:isjumpqueue;type:smallint;default:2;comment:是否插队 1是 2否"`
	OrderType            int64 `json:"OrderType" gorm:"column:ordertype;type:smallint;default:1;comment:订单类型 1VIP订单 2旅行团 3福建团 4老年团"`
	OverNumCount         int64 `json:"OverNumCount" gorm:"column:overnumcount;type:smallint;default:0;comment:过号次数 一次过号，不变(需要移到队头)；2次过号，后移3位；3次过号，取消排队"`
	model.Timestamp
}

func (m *CharteredOrderTripQueueJob) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *CharteredOrderTripQueueJob) Create(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

// GetAll 获取今天所有正在排队的序列
func (m *CharteredOrderTripQueueJob) GetAll() []CharteredOrderTripQueueJob {
	var jobs []CharteredOrderTripQueueJob
	model.DB().Model(&CharteredOrderTripQueueJob{}).Where("CreatedAt >= ?", time.Now().Format(model.DateFormat)).Order("CurrentSort ASC").Find(&jobs)

	return jobs
}

// 更新排队序号
func (m *CharteredOrderTripQueueJob) UpdateCurrentSort(id, currentSort int64) error {
	return model.DB().Model(&CharteredOrderTripQueueJob{}).Where("Id = ?", id).Update("CurrentSort", currentSort).Error
}
