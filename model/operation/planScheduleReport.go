package operation

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type PlanScheduleReport struct {
	model.PkId
	TopCorporationId int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	CorporationId    int64           `json:"CorporationId" gorm:"column:corporationid;type:bigint;default:0;comment:线路所属的机构（车队）ID;index:corp_lineid_driverid_reportat_index"`
	CorporationName  string          `json:"CorporationName" gorm:"column:corporationname;type:varchar;default:;comment:线路所属的机构（车队）"`
	LineId           int64           `json:"LineId" gorm:"column:lineid;type:integer;default:0;comment:线路ID;index:corp_lineid_driverid_reportat_index"`
	LineCode         string          `json:"LineCode" gorm:"column:linecode;type:varchar(100);default:;comment:线路编号;"`
	LineName         string          `json:"LineName" gorm:"column:linename;type:varchar(100);default:;comment:线路名称"`
	ReportAt         model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:报表日期;index:corp_lineid_driverid_reportat_index"`
	DriverId         int64           `json:"DriverId" gorm:"column:driverid;type:integer;default:0;comment:司机ID;index:corp_lineid_driverid_reportat_index"`
	DriverName       string          `json:"DriverName" gorm:"column:drivername;type:varchar(100);default:;comment:司机姓名"`
	DriverCode       string          `json:"DriverCode" gorm:"column:drivercode;type:varchar(100);default:;comment:司机工号"`
	FrequencyIndex   int64           `json:"FrequencyIndex" gorm:"column:frequencyindex;type:smallint;default:0;comment:班次"`
	CircleCount      int64           `json:"CircleCount" gorm:"column:circlecount;type:smallint;default:0;comment:圈数 单位：圈数*10"`
	model.Timestamp
	model.OpUser
}

func (pr *PlanScheduleReport) BeforeCreate(db *gorm.DB) error {
	pr.Id = model.Id()
	return nil
}

func (pr *PlanScheduleReport) Create() error {
	return model.DB().Create(&pr).Error
}

func (pr *PlanScheduleReport) Update() error {
	return model.DB().Model(&PlanScheduleReport{}).Where("Id = ?", pr.Id).Select("*").Updates(&pr).Error
}

func (pr *PlanScheduleReport) DeleteByParam() error {
	return model.DB().Model(&PlanScheduleReport{}).Where("CorporationId = ? AND ReportAt = ? AND LineId = ?", pr.CorporationId,
		time.Time(pr.ReportAt).Format(model.DateFormat), pr.LineId).Delete(&PlanScheduleReport{}).Error
}

func (pr *PlanScheduleReport) Delete(ids []int64) error {
	return model.DB().Where("Id IN ?", ids).Delete(&PlanScheduleReport{}).Error
}

func (pr *PlanScheduleReport) GetBy(corporationId int64, lineId int64, startAt, endAt time.Time) []PlanScheduleReport {
	var reports []PlanScheduleReport
	tx := model.DB().Model(&PlanScheduleReport{})

	if corporationId > 0 {
		tx.Where("CorporationId = ?", corporationId)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}

	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}

	tx.Order("ReportAt asc").Find(&reports)

	return reports
}

func (pr *PlanScheduleReport) GetDriverFrequencyIndexCount(corporationId int64, lineId, driverId int64, startAt, endAt time.Time) int64 {
	var count int64
	tx := model.DB().Model(&PlanScheduleReport{}).Select("ReportAt").Where("FrequencyIndex > 0")
	if corporationId > 0 {
		tx.Where("CorporationId = ?", corporationId)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}
	if driverId > 0 {
		tx.Where("DriverId = ?", driverId)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}

	tx.Group("reportat").Count(&count)

	return count
}

func (pr *PlanScheduleReport) GetDriverSumCircleCount(corporationId int64, lineId, driverId int64, startAt, endAt time.Time, exceptReportAts []string) int64 {
	var count int64
	tx := model.DB().Model(&PlanScheduleReport{}).Select("COALESCE(SUM(CircleCount),0) as CircleCount")
	if corporationId > 0 {
		tx.Where("CorporationId = ?", corporationId)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}
	if driverId > 0 {
		tx.Where("DriverId = ?", driverId)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}

	if len(exceptReportAts) > 0 {
		tx.Where("TO_CHAR(ReportAt,'YYYY-MM-DD') NOT IN ?", exceptReportAts)
	}
	tx.Scan(&count)

	return count
}

type ReportAtPlanCircle struct {
	ReportAt   model.LocalTime `json:"ReportAt" gorm:"column:reportat"`
	PlanCircle int64           `json:"PlanCircle" gorm:"column:plancircle"`
}

func (pr *PlanScheduleReport) GetReportAtPlanCircleByDriver(driverId, lineId, corporationId int64, startAt, endAt time.Time) []ReportAtPlanCircle {
	var reports []ReportAtPlanCircle
	tx := model.DB().Model(&PlanScheduleReport{}).Select("ReportAt", "COALESCE(SUM(CircleCount),0) as PlanCircle").
		Where("DriverId = ?", driverId).
		Where("ReportAt >= ?", startAt.Format(model.DateFormat)).
		Where("ReportAt <= ?", endAt.Format(model.DateFormat))

	if corporationId > 0 {
		tx.Where("CorporationId = ?", corporationId)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	tx.Group("reportat").Scan(&reports)

	return reports
}

type DriverPlanDayCountItem struct {
	CorporationId   int64  `json:"CorporationId" gorm:"column:corporationid;"`
	CorporationName string `json:"CorporationName" gorm:"column:corporationname;"`
	LineId          int64  `json:"LineId" gorm:"column:lineid;"`
	LineName        string `json:"LineName" gorm:"column:linename;"`
	DriverId        int64  `json:"DriverId" gorm:"column:driverid;"`
	DayCount        int64  `json:"DayCount" gorm:"column:daycount;"`
}

func (pr *PlanScheduleReport) GetDriverPlanDayCount(lineIds, corporationIds []int64, startAt, endAt time.Time) []DriverPlanDayCountItem {
	var reports []DriverPlanDayCountItem
	tx := model.DB().Model(&PlanScheduleReport{}).Select("CorporationId", "CorporationName", "LineId", "LineName", "DriverId", "COUNT(*) as DayCount").Where("CircleCount > 0")
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}

	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}

	tx.Group("CorporationId,CorporationName,LineId,LineName,DriverId").Scan(&reports)

	return reports
}
