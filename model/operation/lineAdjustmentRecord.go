package operation

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type LineAdjustmentRecord struct {
	model.PkId
	LineName              string `json:"LineName" gorm:"column:linename;type:varchar;default:;comment:线路名"`
	LitterFont            string `json:"LitterFont" gorm:"column:litterfont;type:varchar;default:;comment:小字要求"`
	TravelAdvice          string `json:"TravelAdvice" gorm:"column:traveladvice;type:varchar;default:;comment:出行建议"`
	EffectiveTime         string `json:"EffectiveTime" gorm:"column:effectivetime;type:varchar;default:;comment:生效时间"`
	UpwardTime            string `json:"UpwardTime" gorm:"column:upwardtime;type:varchar;default:;comment:上行时间"`
	UpstreamStations      string `json:"UpstreamStations" gorm:"column:upstreamstations;type:varchar;default:;comment:上行首末站"`
	DownwardTime          string `json:"DownwardTime" gorm:"column:downwardtime;type:varchar;default:;comment:下行时间"`
	DownwardStations      string `json:"DownwardStations" gorm:"column:downwardstations;type:varchar;default:;comment:下行首末站"`
	Picture               string `json:"Picture" gorm:"column:picture;type:varchar;default:;comment:图片地址"`
	AdjustmentType        string `json:"AdjustmentType" gorm:"column:adjustmenttype;type:varchar;default:;comment:调整类型"`
	StartAndEndPoint      string `json:"StartAndEndPoint" gorm:"column:startandendpoint;type:varchar;default:;comment:起结点"`
	FirstAndLastClassTime string `json:"FirstAndLastClassTime" gorm:"column:firstandlastclasstime;type:varchar;default:;comment:首末班"`

	Version int64 `json:"Version" gorm:"column:version;type:integer;default:;comment:版本号"`
	Sort    int64 `json:"Sort" gorm:"column:sort;type:integer;default:;comment:序号"`
	Batch   int64 `json:"Batch" gorm:"column:batch;type:integer;default:;comment:第几批修改"`
	Status  int64 `json:"Status" gorm:"column:status;type:integer;default:;comment:1正式 2测试"`
}

func (m *LineAdjustmentRecord) BeforeCreate(tx *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *LineAdjustmentRecord) Create() error {
	return model.DB().Create(&m).Error
}

func (m *LineAdjustmentRecord) Update() error {
	return model.DB().Updates(&m).Error
}

func (m *LineAdjustmentRecord) List(Version, Status int64) (data []LineAdjustmentRecord, err error) {
	err = model.DB().Model(&m).Where("Status = ?", Status).Where("Version = ?", Version).Order("Batch DESC,Sort ASC").Find(&data).Error
	return
}

type LineAdjustmentVisitRecord struct {
	model.PkId
	model.Timestamp
	Version    int64 `json:"Version" gorm:"column:version;type:integer;default:0;comment:版本号"`
	VisitCount int64 `json:"VisitCount" gorm:"column:visitcount;type:integer;default:0;comment:访问次数"`
	Status     int64 `json:"Status" gorm:"column:status;type:integer;default:;comment:1正式 2测试"`
}

func (m *LineAdjustmentVisitRecord) BeforeCreate(tx *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *LineAdjustmentVisitRecord) Create() error {
	return model.DB().Create(&m).Error
}

func (m *LineAdjustmentVisitRecord) Update() error {
	return model.DB().Updates(&m).Error
}

// FindLatest 找到最新的一条版本信息
func (m *LineAdjustmentVisitRecord) FindLatest(Status int64) (data LineAdjustmentVisitRecord, err error) {
	err = model.DB().Model(&m).Where("Status = ?", Status).Order("CreatedAt DESC").First(&data).Error
	return
}
