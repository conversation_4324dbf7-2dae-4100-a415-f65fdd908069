package operation

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
	"time"
)

type IrregularLineReport struct {
	model.PkId
	TopCorporationId int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	CorporationId    int64           `json:"CorporationId" gorm:"column:corporationid;type:bigint;default:0;comment:线路所属的机构（车队）ID;index:corp_lineid_reportat_index"`
	CorporationName  string          `json:"CorporationName" gorm:"column:corporationname;type:varchar;default:;comment:线路所属的机构（车队）"`
	LineId           int64           `json:"LineId" gorm:"column:lineid;type:integer;default:0;comment:线路ID;index:corp_lineid_reportat_index"`
	LineCode         string          `json:"LineCode" gorm:"column:linecode;type:varchar(100);default:;comment:线路编号;"`
	LineName         string          `json:"LineName" gorm:"column:linename;type:varchar(100);default:;comment:线路名称"`
	LineAttr         int64           `json:"LineAttr" gorm:"column:lineattr;type:smallint;default:0;comment:线路属性，1-常规公交；2-定制公交；3-公务车；4-校车；5-旅游专线; 6-村村通"`
	ReportAt         model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:报表日期;index:corp_lineid_reportat_index"`
	VehicleId        int64           `json:"VehicleId" gorm:"column:vehicleid;type:integer;default:0;comment:车辆ID"`
	License          string          `json:"License" gorm:"column:license;type:varchar(100);default:;comment:车牌号"`
	DriverId         int64           `json:"DriverId" gorm:"column:driverid;type:integer;default:0;comment:司机ID"`
	DriverName       string          `json:"DriverName" gorm:"column:drivername;type:varchar(100);default:;comment:司机姓名"`
	DriverCode       string          `json:"DriverCode" gorm:"column:drivercode;type:varchar(100);default:;comment:司机工号"`
	PlanCircle       int64           `json:"PlanCircle" gorm:"column:plancircle;type:integer;default:0;comment:计划圈次 单位：圈次*10"`
	ActualCircle     int64           `json:"ActualCircle" gorm:"column:actualcircle;type:integer;default:0;comment:实际圈次 单位：圈次*10"`
	ExtraCircle      int64           `json:"ExtraCircle" gorm:"column:extracircle;type:integer;default:0;comment:额外圈次 单位：圈次*10"`
	DoneMileage      int64           `json:"DoneMileage" gorm:"column:donemileage;type:integer;default:0;comment:运营公里 单位：米"`
	CustomMileage    int64           `json:"CustomMileage" gorm:"column:custommileage;type:integer;default:0;comment:自定义公里 单位：米"`
	More             string          `json:"More" gorm:"column:more;type:text;comment:备注"`
	IsApproval       int64           `json:"IsApproval" gorm:"column:isapproval;type:smallint;default:2;comment:数据是否审批 1是 2否"`
	model.Timestamp
	model.OpUser
}

func (il *IrregularLineReport) TableName() string {
	return "irregular_line_reports"
}
func (il *IrregularLineReport) BeforeCreate(db *gorm.DB) error {
	il.Id = model.Id()
	return nil
}

func (il *IrregularLineReport) Create() error {
	return model.DB().Create(&il).Error
}

func (il *IrregularLineReport) Update() error {
	return model.DB().Model(&IrregularLineReport{}).Where("Id = ?", il.Id).Select("*").Omit("IsApproval").Updates(&il).Error
}

func (il *IrregularLineReport) TransactionUpdateApprovalStatus(tx *gorm.DB, corporationId int64, lineIds []int64, reportAt time.Time, isApproval int64) error {
	return tx.Model(&IrregularLineReport{}).Where("CorporationId = ? AND LineId IN ? AND ReportAt = ?", corporationId, lineIds, reportAt.Format(model.DateFormat)).
		UpdateColumn("IsApproval", isApproval).Error
}

func (il *IrregularLineReport) FindByParam() IrregularLineReport {
	var report IrregularLineReport
	model.DB().Model(&IrregularLineReport{}).Where("ReportAt = ? AND LineId = ? AND VehicleId = ? AND DriverId = ?",
		time.Time(il.ReportAt).Format(model.DateFormat), il.LineId, il.VehicleId, il.DriverId).First(&report)

	return report
}

func (il *IrregularLineReport) Delete(ids []int64) error {
	return model.DB().Where("Id IN ?", ids).Delete(&IrregularLineReport{}).Error
}

func (il *IrregularLineReport) GetBy(isApproval int64, corporationIds []int64, lineIds []int64, driverName, license string, startAt, endAt time.Time) []IrregularLineReport {
	var reports []IrregularLineReport
	tx := model.DB().Model(&IrregularLineReport{})
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if driverName != "" {
		tx.Where("DriverName LIKE ?", "%"+driverName+"%")
	}
	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}

	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}

	tx.Order("ReportAt asc").Find(&reports)

	return reports
}

func (il *IrregularLineReport) GetAllReportAt(corporationIds []int64, lineId int64, startAt, endAt time.Time) []string {
	var reportAts []string
	tx := model.DB().Model(&IrregularLineReport{})
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	tx.Select("TO_CHAR(ReportAt,'YYYY-MM-DD') as ReportAt").Where("LineId > 0").Group("TO_CHAR(ReportAt,'YYYY-MM-DD')").Pluck("ReportAt", &reportAts)

	return reportAts
}

func (r *IrregularLineReport) GetReportAtByDriver(isApproval int64, corporationIds []int64, lineId, vehicleId, driverId int64, lineAttrs []int64, startAt, endAt time.Time) []string {
	var reportAts []string
	tx := model.DB().Model(&IrregularLineReport{})
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}
	if lineId > 0 {
		tx = tx.Where("LineId = ?", lineId)
	}
	if vehicleId > 0 {
		tx = tx.Where("VehicleId = ?", vehicleId)
	}
	if driverId > 0 {
		tx = tx.Where("DriverId = ?", driverId)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	tx.Select("TO_CHAR(ReportAt,'YYYY-MM-DD') as ReportAt").Where("LineId > 0").Group("TO_CHAR(ReportAt,'YYYY-MM-DD')").Pluck("ReportAt", &reportAts)

	return reportAts
}

func (il *IrregularLineReport) GetAllReportAtByStatus(corporationIds []int64, lineId, approvalStatus int64, startAt, endAt time.Time) []string {
	var reportAts []string

	tx := model.DB().Model(&IrregularLineReport{})
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}
	if approvalStatus > 0 {
		tx.Where("IsApproval = ?", approvalStatus)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	tx.Select("TO_CHAR(ReportAt,'YYYY-MM-DD') as ReportAt").Where("LineId > 0").Group("TO_CHAR(ReportAt,'YYYY-MM-DD')").Pluck("ReportAt", &reportAts)

	return reportAts
}

//type IrregularLineMileageAnfCircleItem struct {
//	TotalMileage int64 `json:"TotalMileage" gorm:"column:totalmileage"`
//	TotalCircle  int64 `json:"TotalCircle" gorm:"column:totalcircle"`
//}

func (il *IrregularLineReport) GetLinesTotalMileageAndCircle(isApproval int64, corporationIds, lineIds []int64, startAt, endAt time.Time) []LineMileageAndCircleSumItem {
	var reports []LineMileageAndCircleSumItem

	tx := model.DB().Model(&IrregularLineReport{}).
		Select("SUM(DoneMileage+CustomMileage) AS TotalMileage", "SUM(ActualCircle+ExtraCircle) AS TotalCircle", "CorporationId", "CorporationName", "LineId", "LineName")
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	tx.Group("CorporationId,CorporationName,LineId,LineName").Scan(&reports)

	return reports
}

func (il *IrregularLineReport) GetLineTotalMileage(isApproval int64, corporationId, lineId int64, startAt, endAt time.Time) int64 {
	var mileage int64
	tx := model.DB().Model(&IrregularLineReport{}).Select("COALESCE(SUM(DoneMileage+CustomMileage),0)").Where("CorporationId = ? AND LineId = ?", corporationId, lineId)
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	tx.Scan(&mileage)
	return mileage
}

func (il *IrregularLineReport) GetVehiclesTotalMileageAndCircle(isApproval int64, corporationIds, lineIds, vehicleIds []int64, startAt, endAt time.Time) []LineMileageAndCircleSumItem {
	var vehicleReports []LineMileageAndCircleSumItem
	tx := model.DB().Model(&IrregularLineReport{}).Select("SUM(DoneMileage+CustomMileage) AS TotalMileage", "SUM(ActualCircle+ExtraCircle) AS TotalCircle", "VehicleId", "License").Where("VehicleId > 0")
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	if len(vehicleIds) > 0 {
		tx.Where("VehicleId IN ?", vehicleIds)
	}

	tx.Group("VehicleId,License").Scan(&vehicleReports)
	return vehicleReports
}

func (il *IrregularLineReport) GetDriverTotalMileage(isApproval int64, driverId int64, startAt, endAt time.Time) int64 {
	var mileage int64
	tx := model.DB().Model(&IrregularLineReport{}).Select("COALESCE(SUM(DoneMileage+CustomMileage),0)").Where("DriverId = ?", driverId)
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	tx.Scan(&mileage)
	return mileage
}

func (il *IrregularLineReport) GetNotApprovalLine(corporationIds []int64, startAt, endAt time.Time) []NotApprovalLineItem {
	var reports []NotApprovalLineItem
	tx := model.DB().Model(&IrregularLineReport{}).Select("LineId", "LineName", "CorporationId", "CorporationName").
		Where("IsApproval = ?", util.StatusForFalse)

	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}

	tx.Group("LineId,LineName,CorporationId,CorporationName").Scan(&reports)

	return reports
}
