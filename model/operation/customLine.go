package operation

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

const (
	SortInterval = 100000000 // 排序分段10000000
)

type CustomLine struct {
	model.PkId
	TopCorporationId int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	Name             string `json:"Name" gorm:"column:name;type:varchar(255);default:'';comment:线路名"`
	Sheet            int64  `json:"Sheet" gorm:"column:sheet;type:integer;default:0;comment:上下行: 1:上行;2:下行;3:下行"`
	FolderId         int64  `json:"FolderId" gorm:"column:folderid;type:bigint;default:0;comment:文件夹ID"`
	ColorValue       int64  `json:"ColorValue" gorm:"column:colorvalue;type:integer;default:0;comment:色值"`
	Sort             int64  `json:"Sort" gorm:"column:sort;type:bigint;default:0;comment:排序字段"`
	model.OpUser
	model.Timestamp
}

func (ols *CustomLine) BeforeCreate(db *gorm.DB) error {
	ols.Id = model.Id()
	return nil
}

func (ols *CustomLine) Create(tx *gorm.DB) error {
	return tx.Create(&ols).Error
}

func (ols *CustomLine) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&ols).Error
}

func (ols *CustomLine) Update(tx *gorm.DB, as map[string]interface{}) error {
	return tx.Model(&CustomLine{}).Where("Id = ?", ols.Id).Updates(as).Error
}

func (lm *CustomLine) UpdateColumn(tx *gorm.DB, column string, value interface{}) error {
	return tx.Model(&CustomLine{}).Where("Id = ?", lm.Id).Update(column, value).Error
}

func (ols *CustomLine) GetById(Id int64) error {
	return model.DB().Model(&CustomLine{}).Where("Id = ? ", Id).First(ols).Error
}

func (ols *CustomLine) GetMaxSortBy() int64 {
	err := model.DB().Model(&CustomLine{}).Order("Sort DESC").Limit(1).First(ols).Error
	if err != nil {
		return 0
	}
	return ols.Sort
}

func (ols *CustomLine) GetByFolderId(tx *gorm.DB, folderId int64) []CustomLine {
	var items []CustomLine
	tx.Model(&CustomLine{}).Where("FolderId = ? ", folderId).Order("CreatedAt DESC").Find(&items)

	return items
}

func (ols *CustomLine) GetAllBy(topCorpId int64) []CustomLine {
	var items []CustomLine
	model.DB().Model(&CustomLine{}).Where("TopCorporationId=?", topCorpId).Order("Sort asc").Find(&items)

	return items
}

func (lm *CustomLine) DeleteById(tx *gorm.DB) error {
	err := tx.Where("Id = ?", lm.Id).Delete(&CustomLine{}).Error
	return err
}

type CustomLineStation struct {
	model.PkId
	TopCorporationId int64 `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	LineId           int64 `json:"LineId" gorm:"column:lineid;type:bigint;default:0;comment:线路ID"`
	StationId        int64 `json:"StationId" gorm:"column:stationid;type:integer;default:0;comment:站点ID"`
	Sequence         int64 `json:"Sequence" gorm:"column:sequence;type:integer;default:0;comment:站序"`
	Longitude        int64 `json:"Longitude" gorm:"column:longitude;type:integer;default:0;comment:经度"`
	Latitude         int64 `json:"Latitude" gorm:"column:latitude;type:integer;default:0;comment:纬度"`
	Sort             int64 `json:"Sort" gorm:"column:sort;type:integer;default:0;comment:排序"`
	model.Timestamp
}

func (ols *CustomLineStation) BeforeCreate(db *gorm.DB) error {
	ols.Id = model.Id()
	return nil
}

func (ols *CustomLineStation) Create(tx *gorm.DB) error {
	return tx.Create(&ols).Error
}

func (ols *CustomLineStation) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&ols).Error
}

func (ols *CustomLineStation) Update(tx *gorm.DB) error {
	return tx.Select("*").Updates(&ols).Error
}

func (ols *CustomLineStation) GetAllByLineId(lineId int64) []CustomLineStation {
	var items []CustomLineStation
	model.DB().Model(&CustomLineStation{}).Where("LineId = ? ", lineId).Order("Sort asc").Find(&items)

	return items
}

func (ols *CustomLineStation) GetByLineId(lineId int64) []CustomLineStation {
	var items []CustomLineStation
	model.DB().Model(&CustomLineStation{}).Where("LineId = ? ", lineId).Where("Sequence>0").Order("Sort asc").Find(&items)

	return items
}

func (ols *CustomLineStation) GetCountByLineId(lineId int64) int64 {
	var item int64
	model.DB().Model(&CustomLineStation{}).Where("LineId = ? ", lineId).Where("Sequence>0").Count(&item)

	return item
}

func (r *CustomLineStation) DeleteByLineId(tx *gorm.DB, lineId int64) error {
	return tx.Where("LineId = ? ", lineId).Delete(&CustomLineStation{}).Error
}

type Folder struct {
	model.PkId
	TopCorporationId int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	ParentFolderId   int64  `json:"ParentFolderId" gorm:"column:parentfolderid;type:bigint;default:0;comment:父文件夹"`
	Name             string `json:"Name" gorm:"column:name;type:varchar(255);default:'';comment:文件夹"`
	Sort             int64  `json:"Sort" gorm:"column:sort;type:integer;default:0;comment:排序"`
	model.OpUser
	model.Timestamp
}

func (r *Folder) BeforeCreate(db *gorm.DB) error {
	r.Id = model.Id()
	return nil
}

func (r *Folder) Create() error {
	return model.DB().Create(&r).Error
}

func (r *Folder) Update(tx *gorm.DB, as map[string]interface{}) error {
	return tx.Model(&Folder{}).Where("Id = ? ", r.Id).Updates(as).Error
}

func (r *Folder) UpdateColumn(column string, value interface{}) error {
	return model.DB().Model(&Folder{}).Where("Id = ?", r.Id).Update(column, value).Error
}

func (r *Folder) GetByFolderId() []Folder {
	var items []Folder
	model.DB().Model(&Folder{}).Order("CreatedAt DESC").Find(&items)

	return items
}

func (r *Folder) GetAllBy(topCorpId int64) []Folder {
	var items []Folder
	model.DB().Model(&Folder{}).Where("TopCorporationId=?", topCorpId).Order("Sort asc").Order("CreatedAt asc").Find(&items)

	return items
}

func (r *Folder) Delete(tx *gorm.DB, folderId int64) error {
	return tx.Where("Id = ? ", folderId).Delete(&Folder{}).Error
}

func (r *Folder) GetMaxSortBy() int64 {
	err := model.DB().Model(&Folder{}).Order("Sort DESC").Limit(1).First(r).Error
	if err != nil {
		return 0
	}
	return r.Sort
}
