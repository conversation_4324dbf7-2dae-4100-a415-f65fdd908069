package operation

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

// 规格设置
type SpecificationSetting struct {
	model.PkId
	TopCorporationId int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	Name             string `json:"Name" gorm:"column:name;type:varchar(255);default:;comment:规格名"`
	Length           int64  `json:"Length" gorm:"column:length;type:integer;default:0;comment:长,单位: 像素或mm"`
	Width            int64  `json:"Width" gorm:"column:width;type:integer;default:0;comment:宽,单位: 像素或mm"`

	Up                int64      `json:"Up" gorm:"column:up;type:integer;default:0;comment:上边预留空间,单位: 像素"`
	Down              int64      `json:"Down" gorm:"column:down;type:integer;default:0;comment:下边预留空间,单位: 像素"`
	Left              int64      `json:"Left" gorm:"column:left;type:integer;default:0;comment:左边预留空间,单位: 像素"`
	Right             int64      `json:"Right" gorm:"column:right;type:integer;default:0;comment:右边预留空间,单位: 像素"`
	DictId            int64      `json:"DictId" gorm:"column:dictid;type:bigint;default:0;comment: 字典ID"`
	SpecificationType int64      `json:"SpecificationType" gorm:"column:specificationtype;type:integer;default:0;comment: 1:路牌规格 2:广告牌规格"`
	FileId            int64      `json:"FileId" gorm:"column:fileid;type:bigint;default:0;comment:文件ID"`
	SettingItem       model.JSON `json:"SettingItem" gorm:"column:settingitem;type:json;comment:配置项值"`

	AttachId   int64  `json:"AttachId" gorm:"column:attachid;type:bigint;default:0;comment:附件文件ID"`
	AttachName string `json:"AttachName" gorm:"column:attachname;type:text;default:;comment:附件文件名称"`
	model.OpUser
	model.Timestamp
}

type PosterSettingItem struct {
	Id     string `json:"Id"`
	Type   int    `json:"Type"`   // 内容类型：1文字，2图片 3:站点列表
	Remark string `json:"Remark"` // 备注
	X      int    `json:"X"`      // 水平坐标，从左到右
	Y      int    `json:"Y"`      // 垂直坐标，从上到下
	Width  int    `json:"Width"`  // 内容宽度
	Height int    `json:"Height"` // 内容高度

	Content   string `json:"Content"`   // 文字内容
	FontSize  int    `json:"FontSize"`  // 文字大小
	TextAlign int    `json:"TextAlign"` // 文字定位：0居左（默认），1居中
	Color     string `json:"Color"`     // 文字颜色："#000"格式

	Url string `json:"Url"` // 图片地址：外部链接 或 本地文件
}

func (ols *SpecificationSetting) BeforeCreate(db *gorm.DB) error {
	ols.Id = model.Id()
	return nil
}

func (ols *SpecificationSetting) Create(tx *gorm.DB) error {
	return tx.Create(&ols).Error
}

func (ss *SpecificationSetting) Update(tx *gorm.DB, as map[string]interface{}) error {
	return tx.Model(&SpecificationSetting{}).Where(" Id=?", ss.Id).Updates(as).Error
}
func (ss *SpecificationSetting) GetAll() []SpecificationSetting {
	var items []SpecificationSetting
	model.DB().Model(&SpecificationSetting{}).Find(&items)
	return items
}

func (this *SpecificationSetting) GetByName(name string, dictId int64, types int64) error {
	return model.DB().Model(&SpecificationSetting{}).
		Where("Name = ? ", name).
		Where("DictId=?", dictId).
		Where("SpecificationType=?", types).
		First(&this).Error
}

func (ss *SpecificationSetting) GetByStationId(stationId int64) SpecificationSetting {
	var rsp SpecificationSetting
	model.DB().Model(&SpecificationSetting{}).Joins("LEFT JOIN operation_association_settings sm ON specification_settings.Id=sm.SettingId").
		Where("sm.associationobjectid = ? ", stationId).
		Where("sm.associationobjecttype = ? ", AssociationObjectForStation).
		Where("sm.Type= ? ", StationSetting_SpecificationType).Find(&rsp)
	return rsp
}

func (ss *SpecificationSetting) GetAllBy(p model.Paginator) ([]SpecificationSetting, int64, error) {
	var items []SpecificationSetting
	var tx = model.DB().Model(&SpecificationSetting{})

	if ss.TopCorporationId > 0 {
		tx.Where(" TopCorporationId=?", ss.TopCorporationId)
	}
	if ss.SpecificationType > 0 {
		tx.Where(" SpecificationType=?", ss.SpecificationType)
	}

	if ss.DictId > 0 {
		tx.Where(" DictId=?", ss.DictId)
	}

	if ss.Length > 0 {
		tx.Where(" Length<=?", ss.Length)
	}

	if ss.Width > 0 {
		tx.Where(" Width<=?", ss.Width)
	}

	if ss.Right > 0 {
		tx.Where(" Right<=?", ss.Right)
	}
	if ss.Left > 0 {
		tx.Where(" Left<=?", ss.Left)
	}
	var total int64
	tx.Count(&total)
	if total == 0 {
		return nil, 0, nil
	}

	err := tx.Offset(p.Offset).Limit(p.Limit).Order(" CreatedAt ASC ").Find(&items).Error
	return items, total, err
}

func (this *SpecificationSetting) DeleteByDictId(tx *gorm.DB, dictId int64) error {
	return tx.Where(" DictId=?", dictId).Delete(&SpecificationSetting{}).Error
}

func (ss *SpecificationSetting) GetBy() error {
	return model.DB().Model(&SpecificationSetting{}).Where("Id = ? ", ss.Id).First(&ss).Error
}
func (ss *SpecificationSetting) GetByTx(tx *gorm.DB) error {
	return tx.Model(&SpecificationSetting{}).Where("Id = ? ", ss.Id).First(&ss).Error
}

func (ss *SpecificationSetting) Delete(tx *gorm.DB, id int64) error {
	return tx.Where("Id = ? ", id).Delete(&SpecificationSetting{}).Error
}

// 材料
type MaterialSetting struct {
	model.PkId
	TopCorporationId int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	Name             string `json:"Name" gorm:"column:name;type:varchar(255);default:;comment:材料名"`
	FileId           int64  `json:"FileId" gorm:"column:fileid;type:bigint;default:0;comment:文件ID"`
	MaterialType     int64  `json:"MaterialType" gorm:"column:materialtype;type:integer;default:0;comment: 1:路牌材质 101:广告牌材质"`
	DictId           int64  `json:"DictId" gorm:"column:dictid;type:bigint;default:0;comment: 字典ID"`
	model.OpUser
	model.Timestamp
}

func (ols *MaterialSetting) BeforeCreate(db *gorm.DB) error {
	ols.Id = model.Id()
	return nil
}

func (ols *MaterialSetting) Create(tx *gorm.DB) error {
	return tx.Create(&ols).Error
}

func (ss *MaterialSetting) Update(tx *gorm.DB, as map[string]interface{}) error {
	return tx.Model(&MaterialSetting{}).Where(" Id=?", ss.Id).Updates(as).Error
}

func (ss *MaterialSetting) GetByStationId(stationId int64) MaterialSetting {
	var rsp MaterialSetting
	model.DB().Model(&MaterialSetting{}).Joins("LEFT JOIN operation_association_settings sm ON material_settings.Id=sm.SettingId").
		Where("sm.associationobjectid = ? ", stationId).
		Where("sm.associationobjecttype = ? ", AssociationObjectForStation).
		Where("sm.Type= ? ", StationSetting_MaterialType).Find(&rsp)
	return rsp
}

func (ss *MaterialSetting) GetBy() error {
	return model.DB().Model(&MaterialSetting{}).Where("Id = ? ", ss.Id).First(&ss).Error
}

func (ss *MaterialSetting) GetByTx(tx *gorm.DB) error {
	return tx.Model(&MaterialSetting{}).Where("Id = ? ", ss.Id).First(&ss).Error
}

func (this *MaterialSetting) GetByName(name string, dictId, types int64) error {
	return model.DB().Model(&MaterialSetting{}).
		Where("Name = ? ", name).
		Where("DictId=?", dictId).
		Where("MaterialType=?", types).
		First(&this).Error
}

func (ss *MaterialSetting) GetAllBy(p model.Paginator) ([]MaterialSetting, int64, error) {
	var items []MaterialSetting
	var tx = model.DB().Model(&MaterialSetting{})

	if ss.TopCorporationId > 0 {
		tx.Where(" TopCorporationId=?", ss.TopCorporationId)
	}
	if ss.MaterialType > 0 {
		tx.Where(" MaterialType=?", ss.MaterialType)
	}

	if ss.DictId > 0 {
		tx.Where(" DictId=?", ss.DictId)
	}

	var total int64
	tx.Count(&total)
	if total == 0 {
		return nil, 0, nil
	}

	err := tx.Offset(p.Offset).Limit(p.Limit).Order(" CreatedAt ASC ").Find(&items).Error
	return items, total, err
}

func (ss *MaterialSetting) Delete(tx *gorm.DB, id int64) error {
	return tx.Where("Id = ? ", id).Delete(&MaterialSetting{}).Error
}

func (this *MaterialSetting) DeleteByDictId(tx *gorm.DB, dictId int64) error {
	return tx.Where(" DictId=?", dictId).Delete(&MaterialSetting{}).Error
}

const (
	StationSetting_MaterialType      = 1 // 材料
	StationSetting_SpecificationType = 2 // 规格
)

const (
	AssociationObjectForVehicle = 1 // 车
	AssociationObjectForParking = 2 // 场站
	AssociationObjectForStation = 3 // 站点
)

const (
	SettingType_StreetPlate = 1 // 路牌类型
	SettingType_Ads         = 2 // 广告类型
)

/**  1:1 关系
 */
type OperationAssociationSetting struct {
	model.PkId
	AssociationObjectType int64 `json:"AssociationObjectType" gorm:"column:associationobjecttype;type:smallint;comment:关联对象类型 1车 2场站 3站点;index:device_details_groupid_type_obj_idx"`
	AssociationObjectId   int64 `json:"AssociationObjectId" gorm:"column:associationobjectid;type:bigint;comment:关联对象id 车、场、站id;index:device_details_groupid_type_obj_idx"`
	SettingId             int64 `json:"SettingId" gorm:"column:settingid;type:bigint;default:0;comment:设置ID"`
	Type                  int64 `json:"Type"  gorm:"column:type;type:smallint;default:0;comment:类型: 1: 材料 2 规格"`
	model.Timestamp
}

func (ss *OperationAssociationSetting) BeforeCreate(db *gorm.DB) error {
	ss.Id = model.Id()
	return nil
}

func (ss *OperationAssociationSetting) Create(tx *gorm.DB) error {
	return tx.Create(&ss).Error
}

func (ss *OperationAssociationSetting) CreateOrUpdate(tx *gorm.DB, items []OperationAssociationSetting) error {
	if ss.AssociationObjectType == 0 || ss.AssociationObjectId == 0 {
		return nil
	}
	var err error

	err = ss.Delete(tx, ss.AssociationObjectType, ss.AssociationObjectId)
	if err != nil {
		return err
	}

	for _, item := range items {
		err = item.Create(tx)
		if err != nil {
			return err
		}
	}
	return nil
}

func (ss *OperationAssociationSetting) GetByStationId(associationObjectId int64) ([]OperationAssociationSetting, error) {
	var items []OperationAssociationSetting
	err := model.DB().Model(&OperationAssociationSetting{}).Where("AssociationObjectType = ? ", AssociationObjectForStation).Where("AssociationObjectId=?", associationObjectId).Find(&items).Error
	return items, err
}

func (ss *OperationAssociationSetting) GetBySettingId(settingId, types int64) ([]OperationAssociationSetting, error) {
	var items []OperationAssociationSetting
	err := model.DB().Model(&OperationAssociationSetting{}).Where("SettingId = ?", settingId).Where("type=?", types).Find(&items).Error
	return items, err
}

func (ss *OperationAssociationSetting) Delete(tx *gorm.DB, associationObjectType, associationObjectId int64) error {
	return tx.Where("AssociationObjectType = ? ", associationObjectType).Where("AssociationObjectId=?", associationObjectId).Delete(&OperationAssociationSetting{}).Error
}

/**
  出现重复关联
   1 a b c
   1 a b c
*/

type OperationAssociationSettingRecord struct {
	model.PkId
	TopCorporationId       int64 `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	AssociationObjectType  int64 `json:"AssociationObjectType" gorm:"column:associationobjecttype;type:smallint;comment:关联对象类型 1车 2场站 3站点;index:device_details_groupid_type_obj_idx"`
	AssociationObjectId    int64 `json:"AssociationObjectId" gorm:"column:associationobjectid;type:bigint;comment:关联对象id 车、场、站id;index:device_details_groupid_type_obj_idx"`
	DictId                 int64 `json:"DictId" gorm:"column:dictid;type:bigint;default:0;comment: 字典ID"`
	MaterialSettingId      int64 `json:"MaterialSettingId" gorm:"column:materialsettingid;type:bigint;default:0;comment: 材料ID"`
	SpecificationSettingId int64 `json:"SpecificationSettingId" gorm:"column:specificationsettingid;type:bigint;default:0;comment: 材料ID"`
	model.Timestamp
}

func (this *OperationAssociationSettingRecord) BeforeCreate(db *gorm.DB) error {
	this.Id = model.Id()
	return nil
}

func (this *OperationAssociationSettingRecord) Create(tx *gorm.DB) error {
	return tx.Create(&this).Error
}

func (this *OperationAssociationSettingRecord) Update(tx *gorm.DB, as map[string]interface{}) error {
	return tx.Model(&OperationAssociationSettingRecord{}).Where(" Id=?", this.Id).Updates(as).Error
}

func (this *OperationAssociationSettingRecord) Delete(tx *gorm.DB, id int64) error {
	return tx.Where(" Id=?", id).Delete(&OperationAssociationSettingRecord{}).Error
}

func (this *OperationAssociationSettingRecord) DeleteBySpecificationSettingId(tx *gorm.DB, id int64) error {
	return tx.Where(" SpecificationSettingId=?", id).Delete(&OperationAssociationSettingRecord{}).Error
}

func (this *OperationAssociationSettingRecord) DeleteByMaterialSettingId(tx *gorm.DB, id int64) error {
	return tx.Where(" MaterialSettingId=?", id).Delete(&OperationAssociationSettingRecord{}).Error
}

func (this *OperationAssociationSettingRecord) DeleteByDictId(tx *gorm.DB, dictId int64) error {
	return tx.Where(" DictId=?", dictId).Delete(&OperationAssociationSettingRecord{}).Error
}

func (this *OperationAssociationSettingRecord) GetByAssociationObjectId(associationObjectId, associationObjectType int64) (items []OperationAssociationSettingRecord, err error) {
	err = model.DB().Model(&OperationAssociationSettingRecord{}).Where(" AssociationObjectId=?", associationObjectId).
		Where(" AssociationObjectType=?", associationObjectType).Find(&items).Error
	return
}

type OperationAssociationSettingRecordParam struct {
}

func (this *OperationAssociationSettingRecord) GetBy(p model.Paginator, param OperationAssociationSettingRecordParam) (items []OperationAssociationSettingRecord, totalCount int64, err error) {

	var tx = model.DB().Model(&OperationAssociationSettingRecord{})

	if this.TopCorporationId > 0 {
		tx.Where(" TopCorporationId=?", this.TopCorporationId)
	}

	tx.Count(&totalCount)
	if totalCount == 0 {
		return
	}

	err = tx.Offset(p.Offset).Limit(p.Limit).Find(&items).Error
	return
}
