package operation

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"strings"
	"time"
)

type VehicleLineIcReport struct {
	model.PkId
	model.Timestamp
	CorporationId   int64  `json:"CorporationId" gorm:"column:corporationid;type:bigint;default:;comment:组织id"`
	CorporationName string `json:"CorporationName" gorm:"column:corporationname;type:varchar;default:;comment:组织名称"`
	TxnDate         string `json:"TxnDate" gorm:"column:txndate;type:varchar;default:;comment:日期"`
	ErpLineId       int64  `json:"ErpLineId" gorm:"column:erplineid;type:integer;default:;comment:主数据这边线路id"`
	ErpLienName     string `json:"ErpLienName" gorm:"column:erplienname;type:varchar;default:;comment:主数据这边线路名称"`
	VehicleId       int64  `json:"VehicleId" gorm:"column:vehicleid;type:bigint;default:;comment:车辆id"`
	BusNumpalte     string `json:"BusNumpalte" gorm:"column:busnumpalte;type:varchar;default:;comment:车牌号"`
	TxnAmt          int64  `json:"TxnAmt" gorm:"column:txnamt;type:integer;default:;comment:交易金额 *100"`
	Mileage         int64  `json:"Mileage" gorm:"column:mileage;type:integer;default:;comment:公里数 米"`
	CompletedCircle int64  `json:"CompletedCircle" gorm:"column:completedcircle;type:integer;default:;comment:完成圈次"`
	IsMatch         int64  `json:"IsMatch" gorm:"column:ismatch;type:smallint;default:;comment:1匹配2不匹配"`
	ReportType      int64  `json:"ReportType" gorm:"column:reporttype;type:smallint;default:;comment:1调度数据 2IC卡数据"`
	PeopleCount     int64  `json:"PeopleCount" gorm:"column:peoplecount;type:integer;default:;comment:刷卡人次"`
	// ic卡专用字段
	UpLineId      string `json:"UpLineId" gorm:"column:uplineid;type:varchar;default:;comment:ic卡线路id"`
	UpLineToErp   int64  `json:"UpLineToErp" gorm:"column:uplinetoerp;type:bigint;default:;comment:ic卡线路映射到erp的线路id"`
	LineName      string `json:"LineName" gorm:"column:linename;type:varchar;default:;comment:ic卡线路名称"`
	LineNameToErp string `json:"LineNameToErp" gorm:"column:linenametoerp;type:varchar;default:;comment:ic卡线路映射到erp的线路名称"`
	// 调度专用字段
	ActualDepartAt string `json:"ActualDepartAt" gorm:"column:actualdepartat;type:varchar;default:;comment:实际划发车时间"`
	ActualArriveAt string `json:"ActualArriveAt" gorm:"column:actualarriveat;type:varchar;default:;comment:实际划到达时间"`

	// other
	Lines []VehicleLineIcReportLine `json:"Lines" gorm:"-"`
}

func (m *VehicleLineIcReport) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *VehicleLineIcReport) TableName() string {
	return "vehicle_line_ic_reports"
}

func (m *VehicleLineIcReport) GetTable(suffix string) string {
	return fmt.Sprintf("%s_%v", m.TableName(), suffix)
}

func (m *VehicleLineIcReport) HasOrCreateTable(txnDate time.Time) error {
	table := m.GetTable(txnDate.Format("2006"))
	if !model.DB().Migrator().HasTable(table) {
		err := model.DB().Table(table).AutoMigrate(&VehicleLineIcReport{})
		if err != nil {
			return err
		}
		// 创建基础索引
		model.CreateIndex(table, []string{"TxnDate"})
		model.CreateIndex(table, []string{"IsMatch"})
		model.CreateIndex(table, []string{"ReportType"})
		model.CreateIndex(table, []string{"ErpLineId"})
		model.CreateIndex(table, []string{"UpLineToErp"})
		model.CreateIndex(table, []string{"CorporationId"})

		// 联合索引
		model.CreateIndex(table, []string{"TxnDate", "BusNumpalte"})
		model.CreateIndex(table, []string{"CorporationId", "ReportType", "IsMatch", "TxnDate"})

		// 模糊查询 GIN 索引（PostgreSQL 专用）
		model.DB().Exec(fmt.Sprintf(`CREATE EXTENSION IF NOT EXISTS pg_trgm;`))
		model.DB().Exec(fmt.Sprintf(`CREATE INDEX IF NOT EXISTS idx_%s_busnumpalte_trgm ON %s USING gin ("busnumpalte" gin_trgm_ops)`, table, table))
		model.DB().Exec(fmt.Sprintf(`CREATE INDEX IF NOT EXISTS idx_%s_linename_trgm ON %s USING gin ("linename" gin_trgm_ops)`, table, table))
	}
	return nil
}

func (m *VehicleLineIcReport) BuildTable(startAt, endAt time.Time) *gorm.DB {
	reportParts := util.SplitRangeTimeByYear(startAt, endAt)

	var unionSQLs []string
	var unionParams []interface{}

	for _, part := range reportParts {
		tableName := m.GetTable(part.Start.Format("2006")) // 比如 vehicle_line_ic_report_2024
		// 构建每个分表子查询
		subSQL := fmt.Sprintf(`
			SELECT * FROM %s 
			WHERE TxnDate >= ? AND TxnDate <= ?
		`, tableName)
		unionSQLs = append(unionSQLs, subSQL)
		unionParams = append(unionParams, part.Start.Format(model.DateFormat_Merge), part.End.Format(model.DateFormat_Merge))
	}

	// 使用 UNION ALL 避免去重开销
	finalSQL := strings.Join(unionSQLs, " UNION ALL ")

	return model.DB().Raw(finalSQL, unionParams...)
}

func (m *VehicleLineIcReport) ClearTableDataByDateRange(startAt, endAt time.Time, tp int64) error {
	// 分割跨年时间段
	reportParts := util.SplitRangeTimeByYear(startAt, endAt)

	for _, part := range reportParts {
		tableName := m.GetTable(part.Start.Format("2006"))
		// 执行删除操作
		err := model.DB().Table(tableName).
			Where("TxnDate >= ? AND TxnDate <= ? And ReportType = ?", part.Start.Format(model.DateFormat_Merge), part.End.Format(model.DateFormat_Merge), tp).
			Delete(nil).Error
		if err != nil {
			return fmt.Errorf("清空表 %s 数据失败: %v", tableName, err)
		}
	}

	return nil
}

func (m *VehicleLineIcReport) TransactionCreate(tx *gorm.DB) error {
	txnDate, _ := time.ParseInLocation("20060102", m.TxnDate, time.Local)
	if err := m.HasOrCreateTable(txnDate); err != nil {
		return err
	}
	return tx.Table(m.GetTable(txnDate.Format("2006"))).Create(&m).Error
}

func (m *VehicleLineIcReport) TransactionBatchCreate(tx *gorm.DB, reports []VehicleLineIcReport, txnDate time.Time) error {
	if err := m.HasOrCreateTable(txnDate); err != nil {
		return err
	}
	return tx.Table(m.GetTable(txnDate.Format("2006"))).Create(&reports).Error
}

func (m *VehicleLineIcReport) List(StartAt, EndAt, License, LineName string, IsMatch, ReportType int64, LineIds, UpLineIds, CorporationIds []int64, paginator model.Paginator) (list []VehicleLineIcReport, totalCount int64, err error) {
	start, _ := time.ParseInLocation(model.DateFormat_Merge, StartAt, time.Local)
	end, _ := time.ParseInLocation(model.DateFormat_Merge, EndAt, time.Local)
	table := m.BuildTable(start, end)
	db := model.DB().Table("(?) AS v", table)
	if License != "" {
		db = db.Where("BusNumpalte LIKE ?", "%"+License+"%")
	}
	if LineName != "" {
		db = db.Where("LineName LIKE ?", "%"+LineName+"%")
	}
	if IsMatch != 0 {
		db = db.Where("IsMatch = ?", IsMatch)
	}
	if ReportType != 0 {
		db = db.Where("ReportType = ?", ReportType)
	}
	if len(LineIds) != 0 {
		db = db.Where("ErpLineId in ?", LineIds)
	}
	if len(UpLineIds) != 0 {
		db = db.Where("UpLineToErp in ?", UpLineIds)
	}
	if len(CorporationIds) != 0 {
		db = db.Where("CorporationId in ?", CorporationIds)
	}
	db.Count(&totalCount)
	if paginator.Limit != 0 {
		db = db.Limit(paginator.Limit).Offset(paginator.Offset)
	}
	err = db.Order("TxnDate").Order("BusNumpalte").Find(&list).Error
	return
}

type VehicleLineIcReportLine struct {
	model.PkId
	model.Timestamp
	VehicleLineIcReportId int64  `json:"VehicleLineIcReportId" gorm:"column:vehiclelineicreportid;type:bigint;default:;comment:线路报表id"`
	TxnDate               string `json:"TxnDate" gorm:"column:txndate;type:varchar;default:;comment:刷卡日期"`
	TxnTime               string `json:"TxnTime" gorm:"column:txntime;type:varchar;default:;comment:刷卡时间"`
	TxnAmt                int64  `json:"TxnAmt" gorm:"column:txnamt;type:integer;default:;comment:交易金额 *100"`
	UpLineId              string `json:"UpLineId" gorm:"column:uplineid;type:varchar;default:;comment:ic卡线路id"`
	LineName              string `json:"LineName" gorm:"column:linename;type:varchar;default:;comment:ic卡线路名称"`
	UpLineToErp           int64  `json:"UpLineToErp" gorm:"column:uplinetoerp;type:bigint;default:;comment:ic卡线路映射到erp的线路id"`
	LineNameToErp         string `json:"LineNameToErp" gorm:"column:linenametoerp;type:varchar;default:;comment:ic卡线路映射到erp的线路名称"`
}

func (m *VehicleLineIcReportLine) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *VehicleLineIcReportLine) TableName() string {
	return "vehicle_line_ic_report_line"
}

func (m *VehicleLineIcReportLine) GetTable(suffix string) string {
	return fmt.Sprintf("%s_%v", m.TableName(), suffix)
}

func (m *VehicleLineIcReportLine) HasOrCreateTable(txnDate time.Time) error {
	table := m.GetTable(txnDate.Format("2006"))
	if !model.DB().Migrator().HasTable(table) {
		err := model.DB().Table(table).AutoMigrate(&VehicleLineIcReportLine{})
		if err != nil {
			return err
		}
		model.CreateIndex(table, []string{"VehicleLineIcReportId"})
	}
	return nil
}

func (m *VehicleLineIcReportLine) BuildTable(startAt, endAt time.Time) *gorm.DB {
	reportParts := util.SplitRangeTimeByYear(startAt, endAt)
	var sql []interface{}
	var questionSymbol []string
	for i := range reportParts {
		child := model.DB().Table(m.GetTable(reportParts[i].Start.Format("2006"))).Where("TxnDate >= ? AND TxnDate <= ?", reportParts[i].Start.Format(model.DateFormat_Merge), reportParts[i].End.Format(model.DateFormat_Merge))
		sql = append(sql, child)
		questionSymbol = append(questionSymbol, "?")
	}

	raw := strings.Join(questionSymbol, " UNION ")

	return model.DB().Raw(raw, sql...)
}

func (m *VehicleLineIcReportLine) TransactionCreate(tx *gorm.DB) error {
	txnDate, _ := time.ParseInLocation("20060102", m.TxnDate, time.Local)
	if err := m.HasOrCreateTable(txnDate); err != nil {
		return err
	}
	return tx.Table(m.GetTable(txnDate.Format("2006"))).Create(&m).Error
}

func (m *VehicleLineIcReportLine) TransactionBatchCreate(tx *gorm.DB, reports []VehicleLineIcReportLine, txnDate time.Time) error {
	if err := m.HasOrCreateTable(txnDate); err != nil {
		return err
	}
	return tx.Table(m.GetTable(txnDate.Format("2006"))).Create(&reports).Error
}

func (m *VehicleLineIcReportLine) List(vehicleLineIcReportId int64, txnDate string) (list []VehicleLineIcReportLine, err error) {
	start, _ := time.ParseInLocation(model.DateFormat_Merge, txnDate, time.Local)
	end, _ := time.ParseInLocation(model.DateFormat_Merge, txnDate, time.Local)
	table := m.BuildTable(start, end)
	err = model.DB().Table("(?) AS v", table).Where("vehicleLineIcReportId = ?", vehicleLineIcReportId).Order("TxnTime").Find(&list).Error
	return
}

func (m *VehicleLineIcReportLine) ClearTableDataByDateRange(startAt, endAt time.Time) error {
	// 分割跨年时间段
	reportParts := util.SplitRangeTimeByYear(startAt, endAt)

	for _, part := range reportParts {
		tableName := m.GetTable(part.Start.Format("2006"))
		// 执行删除操作
		err := model.DB().Table(tableName).
			Where("TxnDate >= ? AND TxnDate <= ? ", part.Start.Format(model.DateFormat_Merge), part.End.Format(model.DateFormat_Merge)).
			Delete(nil).Error
		if err != nil {
			return fmt.Errorf("清空表 %s 数据失败: %v", tableName, err)
		}
	}

	return nil
}
