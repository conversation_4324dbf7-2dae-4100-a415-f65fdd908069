package operation

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type VehicleIdentification struct {
	model.PkId
	model.Timestamp
	model.Corporations
	model.OpUser
	VehicleId     int64           `json:"VehicleId" gorm:"column:vehicleid;type:bigint;default:;comment:" validate:"required"`
	License       string          `json:"License" gorm:"column:license;type:varchar;default:;comment:车牌号;" validate:"required"`
	VehicleStatus int64           `json:"VehicleStatus" gorm:"column:vehiclestatus;type:smallint;default:;comment:车辆状态 1使用中 2已报废"`
	EffectiveDate model.LocalTime `json:"EffectiveDate" gorm:"column:effectivedate;type:timestamp;default:;comment:生效日期" validate:"required"`
	ExpireDate    model.LocalTime `json:"ExpireDate" gorm:"column:expiredate;type:timestamp;default:;comment:失效日期" validate:"required"`
	CardType      int64           `json:"CardType" gorm:"column:cardtype;type:smallint;default:;comment:证件类型 1行驶证 2营运证 3车辆年检" validate:"required"`
	CardNumber    string          `json:"CardNumber" gorm:"column:cardnumber;type:varchar;default:;comment:证件号码;uniqueIndex:vehicleidentification_cardnumber" validate:"required"`
	CardStatus    int64           `json:"CardStatus" gorm:"column:cardstatus;type:smallint;default:;comment:证件状态 1正常 2预警 3过期"`
	Attachment    model.JSON      `json:"Attachment" gorm:"column:attachment;type:json;default:;comment:附件"`
	//
	CorporationName string `json:"CorporationName" gorm:"-"`
	CorporationId   int64  `json:"CorporationId" gorm:"-" validate:"required"`
}

func (m *VehicleIdentification) BeforeSave(db *gorm.DB) error {
	expireDate := m.ExpireDate.ToTime()
	expireDate = time.Date(expireDate.Year(), expireDate.Month(), expireDate.Day(), 23, 59, 59, 0, time.Local)
	m.ExpireDate = model.LocalTime(expireDate)
	m.SetCardStatus()
	return nil
}

func (m *VehicleIdentification) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *VehicleIdentification) SetCardStatus() {
	expireDate := m.ExpireDate.ToTime()
	now := time.Now().Unix()
	expireDateMonthAgo := expireDate.AddDate(0, 0, -45)
	if now >= expireDate.Unix() {
		m.CardStatus = 3
	} else if now >= expireDateMonthAgo.Unix() {
		m.CardStatus = 2
	} else {
		m.CardStatus = 1
	}
}

func (m *VehicleIdentification) CardTypeText() string {
	if m.CardType == 1 {
		return "行驶证"
	} else if m.CardType == 2 {
		return "营运证"
	} else if m.CardType == 3 {
		return "车辆年检"
	}
	return ""
}

func (m *VehicleIdentification) Create() error { return model.DB().Create(&m).Error }

func (m *VehicleIdentification) TxCreate(tx *gorm.DB) error { return tx.Create(&m).Error }

func (m *VehicleIdentification) Updates() error { return model.DB().Updates(&m).Error }

func (m *VehicleIdentification) TxUpdates(tx *gorm.DB) error { return tx.Updates(&m).Error }

func (m *VehicleIdentification) Delete(id int64) error {
	return model.DB().Delete(&VehicleIdentification{}, id).Error
}

func (m *VehicleIdentification) GetById(id int64) error { return model.DB().First(&m, id).Error }

func (m *VehicleIdentification) List(qs *model.Qs, corporationId int64, pagination model.Paginator) (data []VehicleIdentification, totalCount int64, err error) {
	db := qs.Format().Model(&VehicleIdentification{})
	if corporationId != 0 {
		db.Scopes(model.WhereCorporation(corporationId))
	}
	db.Count(&totalCount)
	err = db.Scopes(model.PaginationScope(pagination)).Order("CreatedAt DESC").Find(&data).Error
	return
}

func (m *VehicleIdentification) QueryOne(qs *model.Qs, corporationId int64) (data VehicleIdentification, err error) {
	list, _, err := m.List(qs, corporationId, model.NoPagination)
	if list != nil && len(list) > 0 {
		data = list[0]
	}
	return
}
