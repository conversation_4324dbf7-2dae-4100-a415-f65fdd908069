package operation

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"math/rand"
	"strings"
	"time"
)

type OperationApproval struct {
	model.PkId
	Code                string          `json:"Code" gorm:"column:code;type:varchar;not null;comment:编号"`
	RelateReport        model.JSON      `json:"RelateReport" gorm:"column:relatereport;type:json;comment:相关的报表,线路公里明细表：line_vehicle_operation_report;司机出勤表：driver_work_report;工资附表：line_salary_report;定制线路表:irregular_line_report;班制外加班明细表:out_frequency_add_work_report" validate:"required"`
	StartAt             model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始时间" validate:"required"`
	EndAt               model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束时间" validate:"required"`
	CorporationId       int64           `json:"CorporationId" gorm:"column:corporationid;type:bigint;comment:机构ID" validate:"required"`
	CorporationName     string          `json:"CorporationName" gorm:"column:corporationname;type:varchar;comment:机构"`
	LineIds             model.JSON      `json:"LineIds" gorm:"column:lineids;type:json;comment:线路ID" validate:"required"`
	LineNames           model.JSON      `json:"LineNames" gorm:"column:linenames;type:json;comment:线路"`
	More                string          `json:"More" gorm:"column:more;type:text;comment:备注"`
	ApplyStatus         int64           `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;comment:审批状态 1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃"`
	OpUserCorporationId int64           `json:"OpUserCorporationId" gorm:"column:opusercorporationid;type:bigint;comment:创建人的机构ID"`
	model.OpUser
	model.Timestamp

	CurrentHandler        string          `json:"CurrentHandler" gorm:"-"`
	CurrentHandlerAt      model.LocalTime `json:"CurrentHandlerAt" gorm:"-"`
	OpUserCorporationName string          `json:"OpUserCorporationName" gorm:"-"`
	EnableUpdate          bool            `json:"EnableUpdate" gorm:"-"`
}

func (oa *OperationApproval) TableName() string {
	return "operation_approvals"
}

func (oa *OperationApproval) MessageType() string {
	return "operation_approval"
}

func (oa *OperationApproval) ApplyStatusFieldName() string {
	return "applystatus"
}

func (oa *OperationApproval) BeforeCreate(db *gorm.DB) error {
	oa.Id = model.Id()

	rand.Seed(time.Now().UnixNano())
	num := rand.Intn(10000)
	oa.Code = fmt.Sprintf("%s%04d", time.Now().Format("20060102"), num)

	return nil
}

func (oa *OperationApproval) Create(tx *gorm.DB) error {
	return tx.Create(&oa).Error
}

func (oa *OperationApproval) Update(tx *gorm.DB) error {
	return tx.Omit("ApplyStatus").Save(&oa).Error
}

func (oa *OperationApproval) UpdateAll(tx *gorm.DB) error {
	return tx.Select("*").Save(&oa).Error
}
func (oa *OperationApproval) UpdateApplyStatus(tx *gorm.DB) error {
	return tx.Select("ApplyStatus").Save(&oa).Error
}

func (oa *OperationApproval) FirstBy(id int64) OperationApproval {
	var approval OperationApproval
	model.DB().Model(&OperationApproval{}).Where("id = ?", id).First(&approval)
	return approval
}

func (oa *OperationApproval) IsExistCrossApproval(corporationId int64, relateReport []string, lineIds []string, startAt, endAt time.Time) bool {
	var count int64
	model.DB().Model(&OperationApproval{}).Where("CorporationId = ?", corporationId).
		Where("StartAt <= ? AND EndAt >= ?", endAt.Format(model.DateFormat), startAt.Format(model.DateFormat)).
		Where("relateReport ?| "+fmt.Sprintf("'{%s}'", strings.Join(relateReport, ","))).Where("LineIds ?| "+fmt.Sprintf("'{%s}'", strings.Join(lineIds, ","))).
		Where("ApplyStatus IN ?", []int64{util.ApplyStatusForDoing, util.ApplyStatusForDone}).Count(&count)
	return count > 0
}

func (oa *OperationApproval) GetCrossApprovalForDone(corporationIds []int64, relateReport []string, lineIds []string, startAt, endAt time.Time) []OperationApproval {
	var approvals []OperationApproval
	model.DB().Model(&OperationApproval{}).Where("CorporationId IN ?", corporationIds).
		Where("StartAt <= ? AND EndAt >= ?", endAt.Format(model.DateFormat), startAt.Format(model.DateFormat)).
		Where("relateReport ?| "+fmt.Sprintf("'{%s}'", strings.Join(relateReport, ","))).Where("LineIds ?| "+fmt.Sprintf("'{%s}'", strings.Join(lineIds, ","))).
		Where("ApplyStatus IN ?", []int64{util.ApplyStatusForDone}).Find(&approvals)
	return approvals
}

func (oa *OperationApproval) GetBy(corporationIds []int64, relateReport []string, lineIds []int64, applyStatus []int64, startAt, endAt time.Time, paginator model.Paginator) ([]OperationApproval, int64) {
	tx := model.DB().Model(&OperationApproval{})
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}

	if len(relateReport) > 0 {
		//values, _ := json.Marshal(relateReport)
		tx.Where("relateReport ?| " + fmt.Sprintf("'{%s}'", strings.Join(relateReport, ",")))
	}

	if len(lineIds) > 0 {
		values := util.IntSliceToStrSlice(lineIds)
		tx.Where("LineIds ?| " + fmt.Sprintf("'{%s}'", strings.Join(values, ",")))
	}

	if !startAt.IsZero() {
		tx.Where("EndAt >= ?", startAt.Format(model.TimeFormat))
	}

	if !endAt.IsZero() {
		tx.Where("StartAt <= ?", endAt.Format(model.TimeFormat))
	}

	if len(applyStatus) > 0 {
		tx.Where("ApplyStatus IN ?", applyStatus)
	}

	var count int64
	tx.Count(&count)

	var approvals []OperationApproval
	tx.Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&approvals)

	return approvals, count
}
