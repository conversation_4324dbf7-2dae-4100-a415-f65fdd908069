package operation

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
)

type CorpRegion struct {
	model.PkId
	TopCorporationId int64      `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	ParentId         int64      `json:"ParentId" gorm:"column:parentid;type:bigint;default:0"`
	Name             string     `json:"Name" gorm:"column:name;type:varchar(64);default:"`
	CityCode         string     `json:"CityCode" gorm:"column:citycode;type:varchar(64);default:"`
	LngLat           model.JSON `json:"LngLat" gorm:"column:lnglat;type:json"`
	Sort             int64      `json:"Sort" gorm:"column:sort;type:integer;default:0"`
	Description      string     `json:"Description" gorm:"column:description;type:varchar(255);default:"`
	IsShow           int64      `json:"IsShow" gorm:"column:isshow;type:smallint;default:0"`
	model.Timestamp

	Points []util.Point `json:"-" gorm:"-"`
}

func (slc *CorpRegion) TableName() string {
	return "corp_regions"
}

func (rg *CorpRegion) Create() error {
	return nil
}

func (rg *CorpRegion) Update() error {
	return nil
}

func (rg *CorpRegion) FirstBy(cityCode string) error {
	return model.DB().Model(&CorpRegion{}).Where("CityCode = ?", cityCode).First(rg).Error
}

func (rg *CorpRegion) GetByTopCorpId(topCorpId int64) []CorpRegion {
	var areas []CorpRegion

	tx := model.DB().Model(&CorpRegion{})

	tx.Where("TopCorporationId=?", topCorpId).Order("CityCode ASC").Scan(&areas)

	return areas
}

func GetRegionName(items []CorpRegion, cityCode string) string {
	for _, item := range items {

		if item.CityCode == cityCode {
			return item.Name
		}
	}
	return ""
}
