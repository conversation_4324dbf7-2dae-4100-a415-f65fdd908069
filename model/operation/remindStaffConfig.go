package operation

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type RemindStaffConfig struct {
	model.PkId
	ConfigType int64  `json:"ConfigType" gorm:"column:configtype;type:smallint;default:;comment:配置类型 1车辆证件有效期 2司机证件有效期"`
	CardType   int64  `json:"CardType" gorm:"column:cardtype;type:smallint;default:;comment:车辆证件有效期中 1行驶证 2营运证 3车辆年检  司机证件有效期中 1驾驶证 2从业资格证"`
	StaffIds   string `json:"StaffIds" gorm:"column:staffids;type:varchar;default:;comment:人员ids ,拼接"`
	StaffNames string `json:"StaffNames" gorm:"column:staffnames;type:varchar;default:;comment:人员名称，方便显示"`
}

func (m *RemindStaffConfig) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *RemindStaffConfig) Configs(configType int64) (list []RemindStaffConfig, err error) {
	err = model.DB().Model(&RemindStaffConfig{}).Where("ConfigType = ?", configType).Order("CardType").Find(&list).Error
	return
}

func (m *RemindStaffConfig) BatchSave(list []RemindStaffConfig) error {
	return model.DB().Save(&list).Error
}

func (m *RemindStaffConfig) Find(configType, cardType int64) error {
	err := model.DB().Model(&RemindStaffConfig{}).Where("ConfigType = ? and CardType = ?", configType, cardType).Find(&m).Error
	return err
}

type IdentificationRemindRecord struct {
	model.PkId
	model.Timestamp
	Type       int64  `json:"Type" gorm:"column:type;type:smallint;default:;comment:类型 1车辆证件照 2司机证件照"`
	DataId     int64  `json:"DataId" gorm:"column:dataid;type:bigint;default:;comment:数据id"`
	StaffIds   string `json:"StaffIds" gorm:"column:staffids;type:varchar;default:;comment:推送人员ids ,拼接"`
	StaffNames string `json:"StaffNames" gorm:"column:staffnames;type:varchar;default:;comment:推送人员名称"`
	CardType   int64  `json:"CardType" gorm:"column:cardtype;type:smallint;default:;comment:车辆证件有效期中 1行驶证 2营运证 3车辆年检  司机证件有效期中 1驾驶证 2从业资格证"`
	CardNumber string `json:"CardNumber" gorm:"column:cardnumber;type:varchar;default:;comment:证件号码"`
	Message    string `json:"Message" gorm:"column:message;type:varchar;default:;comment:推送消息消息"`
}

func (m *IdentificationRemindRecord) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *IdentificationRemindRecord) Create() error {
	return model.DB().Create(&m).Error
}

func (m *IdentificationRemindRecord) List(qs *model.Qs, pagination model.Paginator) (data []VehicleIdentification, totalCount int64, err error) {
	db := qs.Format().Model(&VehicleIdentification{})
	db.Count(&totalCount)
	err = db.Scopes(model.PaginationScope(pagination)).Order("CreatedAt DESC").Find(&data).Error
	return
}
