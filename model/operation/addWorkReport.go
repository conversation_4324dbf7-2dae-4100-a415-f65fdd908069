package operation

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
	"time"
)

const (
	AddWorkType0               int64 = iota // 加班类型
	AnnualType                              // 年审 1
	FollowCarType                           // 跟车 2
	RepairCarType                           // 修车 3
	NoMaterialsStandbyType                  // 无材料待命 4
	OfficialAttendanceType                  // 公务出勤 5
	ShuttleBusType                          // 接送车 6
	AdminAddWorkType                        // 管理员加班 7
	NewCarType                              // 新车上牌 8
	MoveCarType                             // 挪车 9
	MaintenanceType                         // 保养 10
	CharteredBusType                        // 包车 11
	IntensiveTrainType                      // 强化培训 12
	OnDutyTrainType                         // 待岗培训 13
	OtherType                               // 其他 14
	HighManeuverType                        // 大机动 15
	PreJobTrainType                         // 岗前培训 16
	RestType                                // 疗休养 17
	ExploreNewRoute                         // 勘察新路线 18
	HolidayType                             // 节假日 19
	ManeuverType                            // 机动 20
	LowerManeuverType                       // 小机动 21
	ChangeOfDrivingLicenseType              // 行驶证变更 22
	CircleEncryption                        // 增圈加密 23
	ConcertType                             // 演唱会
)

var AddWorkTypeStringMap = map[int64]string{
	AnnualType:                 "年审",
	FollowCarType:              "跟车",
	RepairCarType:              "修车",
	NoMaterialsStandbyType:     "无材料待命",
	OfficialAttendanceType:     "公务出勤",
	ShuttleBusType:             "接送车",
	AdminAddWorkType:           "管理员加班",
	NewCarType:                 "新车上牌",
	MoveCarType:                "挪车",
	MaintenanceType:            "保养",
	CharteredBusType:           "包车",
	IntensiveTrainType:         "强化培训",
	OnDutyTrainType:            "待岗培训",
	OtherType:                  "其他",
	HighManeuverType:           "大机动",
	PreJobTrainType:            "岗前培训",
	RestType:                   "疗休养",
	ExploreNewRoute:            "勘察新路线",
	HolidayType:                "节假日",
	ManeuverType:               "机动",
	LowerManeuverType:          "小机动",
	ChangeOfDrivingLicenseType: "行驶证变更",
	CircleEncryption:           "增圈加密",
	ConcertType:                "演唱会",
}

// AddWorkTypeMap 加班明细类型字典
var AddWorkTypeMap = map[string]int64{
	"年审":     AnnualType,
	"普通场站年审": AnnualType,
	"特殊场站年审": AnnualType,
	"跟车":     FollowCarType,
	"修车":     RepairCarType,
	"无材料待命":  NoMaterialsStandbyType,
	"公务出勤":   OfficialAttendanceType,
	"接送车":    ShuttleBusType,
	"送车":     ShuttleBusType,
	"管理员加班":  AdminAddWorkType,
	"加班":     AdminAddWorkType,
	"新车上牌":   NewCarType,
	"试新车":    NewCarType,
	"挪车":     MoveCarType,
	"移车":     MoveCarType,
	"包车":     CharteredBusType,
	"强化培训":   IntensiveTrainType,
	"保养":     MaintenanceType,
	"二保":     MaintenanceType,
	"待岗培训":   OnDutyTrainType,
	"岗前培训":   PreJobTrainType,
	"其他":     OtherType,
	"其它":     OtherType,
	"大机动":    HighManeuverType,
	"疗休养":    RestType,
	"勘察新路线":  ExploreNewRoute,
	"节假日":    HolidayType,
	"机动":     ManeuverType,
	"小机动":    LowerManeuverType,
	"行驶证变更":  ChangeOfDrivingLicenseType,
	"增圈加密":   CircleEncryption,
	"演唱会":    ConcertType,
	"":       OtherType,
}

type OutFrequencyAddWorkReport struct {
	model.PkId
	TopCorporationId int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	CorporationId    int64           `json:"CorporationId" gorm:"column:corporationid;type:bigint;default:0;comment:线路所属的机构（车队）ID;index:corp_lineid_reportat_index"`
	CorporationName  string          `json:"CorporationName" gorm:"column:corporationname;type:varchar;default:;comment:线路所属的机构（车队）"`
	LineId           int64           `json:"LineId" gorm:"column:lineid;type:integer;default:0;comment:线路ID;index:corp_lineid_reportat_index"`
	LineName         string          `json:"LineName" gorm:"column:linename;type:varchar(100);default:;comment:线路名称"`
	ReportAt         model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:报表日期;index:corp_lineid_reportat_index"`
	VehicleId        int64           `json:"VehicleId" gorm:"column:vehicleid;type:integer;default:0;comment:车辆ID"`
	License          string          `json:"License" gorm:"column:license;type:varchar(100);default:;comment:车牌号"`
	DriverId         int64           `json:"DriverId" gorm:"column:driverid;type:integer;default:0;comment:司机ID"`
	DriverName       string          `json:"DriverName" gorm:"column:drivername;type:varchar(100);default:;comment:司机姓名"`
	JobNumber        string          `json:"JobNumber" gorm:"column:jobnumber;type:varchar(100);comment:工号"`
	AddWorkType      int64           `json:"AddWorkType" gorm:"column:addworktype;type:integer;comment:加班类型"`
	AreaScene        string          `json:"AreaScene" gorm:"column:areascene;type:varchar;comment:区内外"`
	OutFrequencyAddWorkReportSumField
	More             string `json:"More" gorm:"column:more;type:text;comment:备注"`
	IsApproval       int64  `json:"IsApproval" gorm:"column:isapproval;type:smallint;default:2;comment:数据是否审批 1是 2否"`
	AnnualReviewPark int64  `json:"AnnualReviewPark" gorm:"column:annualreviewpark;type:integer;comment:年审场站 1普通场站2特殊场站"`
	AddWorkTypeStr   string `json:"AddWorkTypeStr" gorm:"-"`
	model.Timestamp
	model.OpUser
}

type OutFrequencyAddWorkReportSumField struct {
	DayCount   int64 `json:"DayCount" gorm:"column:daycount;type:smallint;comment:运营天数 单位：天数*10"`
	TimeLength int64 `json:"TimeLength" gorm:"column:timelength;type:integer;comment:时长 单位：秒"`
	Mileage    int64 `json:"Mileage" gorm:"column:mileage;type:integer;comment:运营公里 单位：米"`
}

func (r *OutFrequencyAddWorkReport) TableName() string {
	return "out_frequency_add_work_reports"
}

func (r *OutFrequencyAddWorkReport) BeforeCreate(db *gorm.DB) error {
	r.Id = model.Id()
	return nil
}

func (r *OutFrequencyAddWorkReport) Create(tx *gorm.DB) error {
	return tx.Create(&r).Error
}

func (r *OutFrequencyAddWorkReport) TransactionUpdate(tx *gorm.DB) error {
	return tx.Omit("IsApproval").Save(&r).Error
}

func (r *OutFrequencyAddWorkReport) TransactionUpdateApprovalStatus(tx *gorm.DB, corporationId int64, lineIds []int64, reportAt time.Time, isApproval int64) error {
	return tx.Model(&OutFrequencyAddWorkReport{}).Where("CorporationId = ? AND LineId IN ? AND ReportAt = ?", corporationId, lineIds, reportAt.Format(model.DateFormat)).
		UpdateColumn("IsApproval", isApproval).Error
}

func (r *OutFrequencyAddWorkReport) GetByLineDriverVehicleWorkType(corporationId, lineId, vehicleId, driverId int64, addWorkType int64, reportAt time.Time) OutFrequencyAddWorkReport {
	var report OutFrequencyAddWorkReport
	model.DB().Model(&OutFrequencyAddWorkReport{}).Where("CorporationId = ? AND LineId = ? AND VehicleId = ? AND DriverId = ? AND AddWorkType = ? AND ReportAt = ?", corporationId, lineId, vehicleId, driverId, addWorkType, reportAt.Format(model.DateFormat)).First(&report)
	return report
}

func (r *OutFrequencyAddWorkReport) GetBy(isApproval int64, corporationIds, lineIds, vehicleIds, driverIds []int64, startAt, endAt time.Time, paginator model.Paginator) ([]OutFrequencyAddWorkReport, int64) {
	var reports []OutFrequencyAddWorkReport
	tx := model.DB().Model(&OutFrequencyAddWorkReport{})
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if len(vehicleIds) > 0 {
		tx.Where("VehicleId IN ?", vehicleIds)
	}
	if len(driverIds) > 0 {
		tx.Where("DriverId IN ?", driverIds)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}

	var count int64
	tx.Count(&count)
	if paginator.Limit > 0 {
		tx = tx.Limit(paginator.Limit).Offset(paginator.Offset)
	}
	tx.Order("ReportAt desc").Find(&reports)
	return reports, count
}

func (r *OutFrequencyAddWorkReport) GetSumBy(isApproval int64, corporationIds, lineIds, vehicleIds, driverIds []int64, startAt, endAt time.Time) OutFrequencyAddWorkReportSumField {
	var sum OutFrequencyAddWorkReportSumField
	tx := model.DB().Model(&OutFrequencyAddWorkReport{})
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if len(vehicleIds) > 0 {
		tx.Where("VehicleId IN ?", vehicleIds)
	}
	if len(driverIds) > 0 {
		tx.Where("DriverId IN ?", driverIds)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}

	tx.Select("SUM(DayCount) AS DayCount", "SUM(TimeLength) AS TimeLength", "SUM(Mileage) AS Mileage").Scan(&sum)
	return sum
}

type OutFrequencyAddWorkSumReport struct {
	DriverId     int64    `json:"DriverId" gorm:"column:driverid"`
	DriverName   string   `json:"DriverName" gorm:"column:drivername"`
	JobNumber    string   `json:"JobNumber" gorm:"column:jobnumber"`
	AddWorkType  string   `json:"AddWorkType" gorm:"column:addworktype"`
	AddWorkTimes int64    `json:"AddWorkTimes" gorm:"column:addworktimes"`
	TimeLength   []int64  `json:"TimeLength"`
	Mileage      []int64  `json:"Mileage"`
	LineName     []string `json:"LineName"`
	More         []string `json:"More"`
}

func (r *OutFrequencyAddWorkReport) GetSumGroupBy(isApproval int64, corporationIds, lineIds, vehicleIds, driverIds []int64, startAt, endAt time.Time, paginator model.Paginator) ([]OutFrequencyAddWorkSumReport, int64) {
	var reports []OutFrequencyAddWorkSumReport
	tx := model.DB().Model(&OutFrequencyAddWorkReport{})
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if len(vehicleIds) > 0 {
		tx.Where("VehicleId IN ?", vehicleIds)
	}
	if len(driverIds) > 0 {
		tx.Where("DriverId IN ?", driverIds)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}

	tx.Group("DriverId,DriverName,JobNumber,AddWorkType")
	var count int64
	tx.Count(&count)

	tx.Select("COUNT(*) AS AddWorkTimes", "DriverId", "DriverName", "JobNumber", "AddWorkType").Order("DriverId ASC").Order("AddWorkType ASC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&reports)
	return reports, count
}

func (r *OutFrequencyAddWorkReport) GetAllLineTimeLengthMileageMore(isApproval, driverId int64, addWorkType string, startAt, endAt time.Time) ([]string, []int64, []int64, []string) {
	var lineName []string
	var more []string
	var timeLength []int64
	var mileage []int64
	tx := model.DB().Model(&OutFrequencyAddWorkReport{})
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if driverId > 0 {
		tx.Where("DriverId = ?", driverId)
	}

	if addWorkType != "" {
		tx.Where("AddWorkType = ?", addWorkType)
	}

	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	tx = tx.Session(&gorm.Session{})

	tx.Select("LineName").Group("linename").Pluck("LineName", &lineName)
	tx.Select("TimeLength").Pluck("TimeLength", &timeLength)
	tx.Select("More").Where("More IS NOT NULL").Pluck("More", &more)
	tx.Select("Mileage").Pluck("Mileage", &mileage)

	return lineName, timeLength, mileage, more
}

func (r *OutFrequencyAddWorkReport) BatchDelete(ids []int64) error {
	return model.DB().Model(&OutFrequencyAddWorkReport{}).Where("Id IN ?", ids).Delete(&OutFrequencyAddWorkReport{}).Error
}

func (r *OutFrequencyAddWorkReport) GetAllReportAt(corporationIds []int64, lineId int64, startAt, endAt time.Time) []string {
	var reportAts []string
	tx := model.DB().Model(&OutFrequencyAddWorkReport{})
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	tx.Select("TO_CHAR(ReportAt,'YYYY-MM-DD') as ReportAt").Where("LineId > 0").Group("TO_CHAR(ReportAt,'YYYY-MM-DD')").Pluck("ReportAt", &reportAts)

	return reportAts
}

func (r *OutFrequencyAddWorkReport) GetReportAtByDriver(isApproval int64, corporationIds []int64, lineId, vehicleId, driverId int64, addWorkTypes []int64, startAt, endAt time.Time) []string {
	var reportAts []string
	tx := model.DB().Model(&OutFrequencyAddWorkReport{})
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(addWorkTypes) > 0 {
		tx.Where("AddWorkType IN ?", addWorkTypes)
	}
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}
	if lineId > 0 {
		tx = tx.Where("LineId = ?", lineId)
	}
	if vehicleId > 0 {
		tx = tx.Where("VehicleId = ?", vehicleId)
	}
	if driverId > 0 {
		tx = tx.Where("DriverId = ?", driverId)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	tx.Select("TO_CHAR(ReportAt,'YYYY-MM-DD') as ReportAt").Where("LineId > 0").Group("TO_CHAR(ReportAt,'YYYY-MM-DD')").Pluck("ReportAt", &reportAts)

	return reportAts
}

func (r *OutFrequencyAddWorkReport) GetAllReportAtByStatus(corporationIds []int64, lineId, approvalStatus int64, startAt, endAt time.Time) []string {
	var reportAts []string

	tx := model.DB().Model(&OutFrequencyAddWorkReport{})
	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}
	if approvalStatus > 0 {
		tx.Where("IsApproval = ?", approvalStatus)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	tx.Select("TO_CHAR(ReportAt,'YYYY-MM-DD') as ReportAt").Where("LineId > 0").Group("TO_CHAR(ReportAt,'YYYY-MM-DD')").Pluck("ReportAt", &reportAts)

	return reportAts
}

//func (r *OutFrequencyAddWorkReport) GetLineTotalMileage(isApproval int64, corporationId, lineId int64, starAt, endAt time.Time) int64 {
//	var totalMileage int64
//	tx := model.DB().Model(&OutFrequencyAddWorkReport{})
//	if isApproval == util.StatusForTrue {
//		tx.Where("IsApproval = ?", util.StatusForTrue)
//	}
//	tx.Where("CorporationId = ? AND LineId = ? AND ReportAt >= ? AND ReportAt <= ?", corporationId, lineId, starAt.Format(model.DateFormat), endAt.Format(model.DateFormat)).Scan(&totalMileage)
//	return totalMileage
//}

func (r *OutFrequencyAddWorkReport) GetLinesTotalMileage(isApproval int64, corporationIds, lineIds []int64, startAt, endAt time.Time) []LineMileageAndCircleSumItem {
	var LineTotalMileages []LineMileageAndCircleSumItem
	tx := model.DB().Model(&OutFrequencyAddWorkReport{}).Select("SUM(Mileage) AS TotalMileage", "CorporationId", "CorporationName", "LineId", "LineName")
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	tx.Group("CorporationId,CorporationName,LineId,LineName").Scan(&LineTotalMileages)
	return LineTotalMileages
}

func (r *OutFrequencyAddWorkReport) GetLineTotalMileage(isApproval int64, corporationId, lineId int64, startAt, endAt time.Time) int64 {
	var mileage int64
	tx := model.DB().Model(&OutFrequencyAddWorkReport{}).Select("COALESCE(SUM(Mileage),0)").Where("CorporationId = ? AND LineId = ?", corporationId, lineId)
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	tx.Scan(&mileage)
	return mileage
}

func (r *OutFrequencyAddWorkReport) GetVehiclesTotalMileage(isApproval int64, corporationIds, lineIds, vehicleIds []int64, startAt, endAt time.Time) []LineMileageAndCircleSumItem {
	var vehicleTotalMileages []LineMileageAndCircleSumItem
	tx := model.DB().Model(&OutFrequencyAddWorkReport{}).Select("SUM(Mileage) AS TotalMileage", "VehicleId", "License").Where("VehicleId > 0")
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if len(lineIds) > 0 {
		tx.Where("LineId IN ?", lineIds)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	if len(vehicleIds) > 0 {
		tx.Where("VehicleId IN ?", vehicleIds)
	}

	tx.Group("VehicleId,License").Scan(&vehicleTotalMileages)
	return vehicleTotalMileages
}

func (r *OutFrequencyAddWorkReport) GetDriverTotalMileage(isApproval int64, driverId int64, startAt, endAt time.Time) int64 {
	var mileage int64
	tx := model.DB().Model(&OutFrequencyAddWorkReport{}).Select("COALESCE(SUM(Mileage),0)").Where("DriverId = ?", driverId)
	if isApproval == util.StatusForTrue {
		tx.Where("IsApproval = ?", util.StatusForTrue)
	}

	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}
	tx.Scan(&mileage)
	return mileage
}

func (r *OutFrequencyAddWorkReport) GetNotApprovalLine(corporationIds []int64, startAt, endAt time.Time) []NotApprovalLineItem {
	var reports []NotApprovalLineItem
	tx := model.DB().Model(&OutFrequencyAddWorkReport{}).Select("LineId", "LineName", "CorporationId", "CorporationName").
		Where("IsApproval = ?", util.StatusForFalse)

	if len(corporationIds) > 0 {
		tx.Where("CorporationId IN ?", corporationIds)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}

	tx.Group("LineId,LineName,CorporationId,CorporationName").Scan(&reports)

	return reports
}
