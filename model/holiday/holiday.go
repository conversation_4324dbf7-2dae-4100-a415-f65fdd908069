package holiday

import (
	"app/org/scs/erpv2/api/model"
	"time"
)

type HolidayDate struct {
	Date model.LocalTime `json:"Date" gorm:"column:date;type:timestamp;comment:日期;uniqueIndex:holiday_date"`
	Name string          `json:"Name" gorm:"column:name;type:varchar;comment:节日名称"`
}

func (h *HolidayDate) IsHoliday(date time.Time) bool {
	var count int64
	model.DB().Model(&HolidayDate{}).Where("Date = ?", date.Format(model.DateFormat)).Count(&count)

	return count > 0
}
