package workOrder

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
	"time"
)

// DeviceDetail 设备明细
type DeviceDetail struct {
	model.PkId
	GroupId                int64  `json:"GroupId"      gorm:"column:groupid;type:bigint;comment:集团Id;uniqueIndex:device_group_code;index:device_details_groupid_type_obj_idx"`
	DeviceModelId          int64  `json:"DeviceModelId" gorm:"column:devicemodelid;type:bigint;comment:关联设备型号id;" validate:"required"`
	DeviceModelBatchCodeId int64  `json:"DeviceModelBatchCodeId" gorm:"column:devicemodelbatchcodeid;type:bigint;comment:关联设备型号批次id;" validate:"required"`
	Code                   string `json:"Code" gorm:"column:code;type:varchar(50);default:'';comment:设备编号;uniqueIndex:device_group_code"`
	FactoryCode            string `json:"FactoryCode" gorm:"column:factorycode;type:varchar(50);default:;comment:厂家编号"`
	AssociationObjectType  int64  `json:"AssociationObjectType" gorm:"column:associationobjecttype;type:smallint;comment:关联对象类型 1车 2场站 3站点;index:device_details_groupid_type_obj_idx"`
	AssociationObjectId    int64  `json:"AssociationObjectId" gorm:"column:associationobjectid;type:bigint;comment:关联对象id 车、场、站id;index:device_details_groupid_type_obj_idx"`
	AssociationObjectName  string `json:"AssociationObjectName" gorm:"column:associationobjectname;type:varchar(50);comment:关联对象名;"`
	QrCodeFilePath         string `json:"QrCodeFilePath" gorm:"column:qrcodefilepath;type:text;comment:二维码相对路径 webroot开头;default:''"`

	IsUsed int64  `json:"IsUsed" gorm:"-"` // 该设备是否被使用（被报修）1是 2否
	Group  string `json:"Group" gorm:"-"`

	model.Timestamp
}

func (dd *DeviceDetail) BeforeCreate(db *gorm.DB) error {
	dd.Id = model.Id()
	return nil
}

func (dd *DeviceDetail) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&dd).Error
}
func (dd *DeviceDetail) Adds(items []DeviceDetail) error {
	return model.DB().Create(&items).Error
}

func (dd *DeviceDetail) GetByCode(groupId int64, codeStr string) error {
	return model.DB().Model(&DeviceDetail{}).Where("GroupId = ? AND Code LIKE ?", groupId, codeStr+"%").Order("Id DESC").First(dd).Error
}

func (dd *DeviceDetail) ExistsByModelId(modelId int64) bool {
	var count int64
	model.DB().Model(&DeviceDetail{}).Where("DeviceModelId = ?", modelId).Count(&count)
	return count > 0
}
func (dd *DeviceDetail) ExistsByBatchCodeId(batchCodeId int64) bool {
	var count int64
	model.DB().Model(&DeviceDetail{}).Where("DeviceModelBatchCodeId = ?", batchCodeId).Count(&count)
	return count > 0
}

func (dd *DeviceDetail) List(groupId, associationObjectId, assocStatus int64, deviceCode, factoryCode, assocObject, exactAssocObject string, //deviceDetail表字段
	classIds, categoryIds, supplierIds []int64, name string, //deviceModel表字段
	batchCode string, expireStatus, maintainerFactoryId int64, purchaseAt, expireAt time.Time, //deviceModelBatchCode表字段
	paginator model.Paginator) ([]DeviceDetail, int64, error) {
	var deviceDetails []DeviceDetail
	var totalCount int64

	tx := model.DB().Model(&DeviceDetail{}).Where("GroupId = ?", groupId)

	/**============以下是deviceDetail表字段查询条件**=============**/
	if associationObjectId > 0 {
		tx.Where("AssociationObjectId = ?", associationObjectId)
	}

	if assocStatus == util.StatusForTrue {
		tx.Where("AssociationObjectId > ?", 0)
	}

	if assocStatus == util.StatusForFalse {
		tx.Where("AssociationObjectId = ?", 0)
	}

	if deviceCode != "" {
		tx.Where("Code LIKE ?", "%"+deviceCode+"%")
	}

	if factoryCode != "" {
		tx.Where("FactoryCode LIKE ?", "%"+factoryCode+"%")
	}

	if assocObject != "" {
		tx.Where("AssociationObjectName LIKE ?", "%"+assocObject+"%")
	}

	if exactAssocObject != "" {
		tx.Where("AssociationObjectName LIKE ?", exactAssocObject)
	}

	/**============以下是deviceModel表字段查询条件**=============**/
	var canQueryDeviceModel bool
	deviceModelSubSql := model.DB().Model(&DeviceModel{}).Select("Id")
	if len(classIds) > 0 {
		canQueryDeviceModel = true
		deviceModelSubSql = deviceModelSubSql.Where("DeviceClassDictId IN ?", classIds)
	}

	if len(categoryIds) > 0 {
		canQueryDeviceModel = true
		deviceModelSubSql = deviceModelSubSql.Where("DeviceCategoryDictId IN ?", categoryIds)
	}

	if len(supplierIds) > 0 {
		canQueryDeviceModel = true
		deviceModelSubSql = deviceModelSubSql.Where("SupplierFactoryId IN ?", supplierIds)
	}

	if name != "" {
		canQueryDeviceModel = true
		deviceModelSubSql = deviceModelSubSql.Where("Name LIKE ?", "%"+name+"%")
	}

	if canQueryDeviceModel {
		tx.Where("DeviceModelId IN (?)", deviceModelSubSql)
	}

	/**============以下是deviceModelBatchCode表字段查询条件**=============**/
	var canQueryDeviceModelBatchCode bool
	deviceModelBatchCodeSubSql := model.DB().Model(&DeviceModelBatchCode{}).Select("Id")
	if batchCode != "" {
		canQueryDeviceModelBatchCode = true
		deviceModelBatchCodeSubSql = deviceModelBatchCodeSubSql.Where("BatchCode LIKE ?", "%"+batchCode+"%")
	}

	if expireStatus == util.StatusForTrue {
		canQueryDeviceModelBatchCode = true
		deviceModelBatchCodeSubSql = deviceModelBatchCodeSubSql.Where("ExpireAt >= ?", time.Now().Format(model.DateFormat))
	}

	if expireStatus == util.StatusForFalse {
		canQueryDeviceModelBatchCode = true
		deviceModelBatchCodeSubSql = deviceModelBatchCodeSubSql.Where("ExpireAt < ?", time.Now().Format(model.DateFormat))
	}

	if maintainerFactoryId > 0 {
		canQueryDeviceModelBatchCode = true
		deviceModelBatchCodeSubSql = deviceModelBatchCodeSubSql.Where("MaintainerFactoryId = ?", maintainerFactoryId)
	}

	if !purchaseAt.IsZero() {
		canQueryDeviceModelBatchCode = true
		deviceModelBatchCodeSubSql = deviceModelBatchCodeSubSql.Where("PurchaseAt = ?", purchaseAt.Format(model.DateFormat))
	}

	if !expireAt.IsZero() {
		canQueryDeviceModelBatchCode = true
		deviceModelBatchCodeSubSql = deviceModelBatchCodeSubSql.Where("ExpireAt = ?", expireAt.Format(model.DateFormat))
	}

	if canQueryDeviceModelBatchCode {
		tx.Where("DeviceModelBatchCodeId IN (?)", deviceModelBatchCodeSubSql)
	}

	tx = tx.Count(&totalCount).Order("Code ASC")
	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}
	err := tx.Scan(&deviceDetails).Error
	if err != nil {
		return nil, 0, err
	}

	return deviceDetails, totalCount, nil
}

func (dd *DeviceDetail) GetAssociationObjectIds(topCorporationId int64, deviceCode, associationObjectName string,
	classIds, categoryIds []int64,
	batchCode string, purchaseAt time.Time) []int64 {

	tx := model.DB().Model(&DeviceDetail{}).Select("AssociationObjectId").Where("GroupId = ?", topCorporationId).Where("AssociationObjectId > ?", 0)

	/**============以下是deviceDetail表字段查询条件**=============**/
	if deviceCode != "" {
		tx.Where("Code LIKE ?", "%"+deviceCode+"%")
	}

	if associationObjectName != "" {
		tx.Where("AssociationObjectName LIKE ?", "%"+associationObjectName+"%")
	}

	/**============以下是deviceModel表字段查询条件**=============**/
	var canQueryDeviceModel bool
	deviceModelSubSql := model.DB().Model(&DeviceModel{}).Select("Id")
	if len(classIds) > 0 {
		canQueryDeviceModel = true
		deviceModelSubSql = deviceModelSubSql.Where("DeviceClassDictId IN ?", classIds)
	}

	if len(categoryIds) > 0 {
		canQueryDeviceModel = true
		deviceModelSubSql = deviceModelSubSql.Where("DeviceCategoryDictId IN ?", categoryIds)
	}

	if canQueryDeviceModel {
		tx.Where("DeviceModelId IN (?)", deviceModelSubSql)
	}

	/**============以下是deviceModelBatchCode表字段查询条件**=============**/
	var canQueryDeviceModelBatchCode bool
	deviceModelBatchCodeSubSql := model.DB().Model(&DeviceModelBatchCode{}).Select("Id")
	if batchCode != "" {
		canQueryDeviceModelBatchCode = true
		deviceModelBatchCodeSubSql = deviceModelBatchCodeSubSql.Where("BatchCode LIKE ?", "%"+batchCode+"%")
	}

	if !purchaseAt.IsZero() {
		canQueryDeviceModelBatchCode = true
		deviceModelBatchCodeSubSql = deviceModelBatchCodeSubSql.Where("PurchaseAt = ?", purchaseAt.Format(model.DateFormat))
	}

	if canQueryDeviceModelBatchCode {
		tx.Where("DeviceModelBatchCodeId IN (?)", deviceModelBatchCodeSubSql)
	}

	var objectIds []int64
	tx.Group("associationobjectid").Pluck("AssociationObjectId", &objectIds)
	return objectIds
}

func (dd *DeviceDetail) Edit() error {
	return model.DB().Model(&DeviceDetail{}).Where("Id = ?", dd.Id).Select("DeviceModelBatchCodeId", "FactoryCode").Updates(dd).Error
}

func (dd *DeviceDetail) TransactionEdit(tx *gorm.DB) error {
	return tx.Model(&DeviceDetail{}).Where("Id = ?", dd.Id).Select("DeviceModelBatchCodeId", "FactoryCode").Updates(dd).Error
}

func (dd *DeviceDetail) TransactionDelete(tx *gorm.DB, ids []int64) error {
	return tx.Where("Id IN ?", ids).Delete(dd).Error
}

func (dd *DeviceDetail) Bind(ids []int64, assocType int64, assocId int64, assocName string) error {
	return model.DB().Model(&DeviceDetail{}).Where("Id IN ?", ids).
		Select("AssociationObjectType", "AssociationObjectId", "AssociationObjectName").
		Updates(map[string]interface{}{"AssociationObjectType": assocType, "AssociationObjectId": assocId, "AssociationObjectName": assocName}).Error
}

func (dd *DeviceDetail) BindTx(tx *gorm.DB, ids []int64, assocType int64, assocId int64, assocName string) error {
	return tx.Model(&DeviceDetail{}).Where("Id IN ?", ids).
		Select("AssociationObjectType", "AssociationObjectId", "AssociationObjectName").
		Updates(map[string]interface{}{"AssociationObjectType": assocType, "AssociationObjectId": assocId, "AssociationObjectName": assocName}).Error
}

func (dd *DeviceDetail) Unbind(ids []int64) error {
	return model.DB().Model(&DeviceDetail{}).Where("Id IN ?", ids).
		Select("AssociationObjectType", "AssociationObjectId", "AssociationObjectName").
		Updates(map[string]interface{}{"AssociationObjectType": 0, "AssociationObjectId": 0, "AssociationObjectName": ""}).Error
}

func (dd *DeviceDetail) UnbindTx(tx *gorm.DB, ids []int64) error {
	return tx.Model(&DeviceDetail{}).Where("Id IN ?", ids).
		Select("AssociationObjectType", "AssociationObjectId", "AssociationObjectName").
		Updates(map[string]interface{}{"AssociationObjectType": 0, "AssociationObjectId": 0, "AssociationObjectName": ""}).Error
}

func (dd *DeviceDetail) GetByAssociationObjectId(groupId int64, assocType int64, assocId int64, categoryIds []int64) ([]DeviceDetail, error) {
	var rsp []DeviceDetail
	tx := model.DB().Model(&DeviceDetail{}).Where("GroupId = ? AND AssociationObjectId = ? AND AssociationObjectType = ?", groupId, assocId, assocType)

	if len(categoryIds) > 0 {
		subSql := model.DB().Model(&DeviceModel{}).Select("Id").Where("DeviceCategoryDictId IN ?", categoryIds)
		tx = tx.Where("DeviceModelId IN (?)", subSql)
	}

	err := tx.Scan(&rsp).Error
	return rsp, err
}

func (dd *DeviceDetail) GetByAssociationObjectName(groupId int64, assocType int64, assocName string) ([]DeviceDetail, error) {
	var rsp []DeviceDetail
	err := model.DB().Model(&DeviceDetail{}).Where("GroupId = ? AND AssociationObjectName LIKE ? AND AssociationObjectType = ?", groupId, "%"+assocName+"%", assocType).Scan(&rsp).Error
	return rsp, err
}

//func (dd *DeviceDetail) GetByDetailId(detailId int64) error {
//return model.DB().Model(&DeviceModel{}).Preload("Details", func(db *gorm.DB) *gorm.DB {
//	return db.Model(&DeviceDetail{}).Where("Id=?", detailId)
//}).Find(dm).Error
//
//return model.DB().Model(&DeviceDetail{}).Preload("DeviceModel").Where("Id=?", detailId).First(dd).Error
//}

func (dd *DeviceDetail) FindBy(detailId int64) error {
	return model.DB().Model(&DeviceDetail{}).Where("Id=?", detailId).Scan(&dd).Error
}
func (dd *DeviceDetail) FirstById(id int64) DeviceDetail {
	var deviceDetail DeviceDetail
	model.DB().Model(&DeviceDetail{}).Where("Id = ?", id).First(&deviceDetail)

	return deviceDetail
}
func (dd *DeviceDetail) FirstByCode(groupId int64, code string) DeviceDetail {
	var deviceDetail DeviceDetail
	model.DB().Model(&DeviceDetail{}).Where("GroupId = ? AND Code = ?", groupId, code).First(&deviceDetail)

	return deviceDetail
}

func (dd *DeviceDetail) GetDeviceIdByCateId(cateId int64) []int64 {
	var deviceIds []int64
	model.DB().Model(&DeviceDetail{}).Where("DeviceModelId IN (?)", model.DB().Model(&DeviceModel{}).Select("Id").Where("DeviceCategoryDictId = ?", cateId)).Pluck("id", &deviceIds)

	return deviceIds
}

func (dd *DeviceDetail) GetDeviceIdByModelId(modelId int64, batchCodeIds []int64) []int64 {
	var deviceIds []int64
	tx := model.DB().Model(&DeviceDetail{}).Where("DeviceModelId = ?", modelId)

	if len(batchCodeIds) > 0 {
		tx.Where("DeviceModelBatchCodeId IN ?", batchCodeIds)
	}
	tx.Pluck("Id", &deviceIds)
	return deviceIds
}
