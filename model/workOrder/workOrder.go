package workOrder

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type OrderType int8 // 工单类型 PETITION_1: 信访工单 DEVICE_REPAIR_2: 设备报修工单
const (
	PETITION_1      OrderType = 1 // 信访工单
	DEVICE_REPAIR_2 OrderType = 2 // 设备报修工单
)

//type WorkOrdersStatus int8 //
//const (
//	DRAFT_0     WorkOrdersStatus = 0
//	PENDING_1   WorkOrdersStatus = 1 // 待处理、流转中
//	RESOLVE_2   WorkOrdersStatus = 2 // 已完成、已完结
//	REFUSE_3    WorkOrdersStatus = 3 // 驳回
//	TERMINATE_4 WorkOrdersStatus = 4 // 撤回
//	ABANDON_5   WorkOrdersStatus = 5 // 废弃
//)

//type WorkOrderIsRepairSelect int8
//
//const (
//	NO_1  WorkOrderIsRepairSelect = 1 // 不需要选择
//	YES_2 WorkOrderIsRepairSelect = 2 // 需要选择
//)

//type WorkOrderMaintenanceType int8 // 维修方式
//
//const (
//	REPAIR_WO_1        WorkOrderMaintenanceType = 1 // 维修
//	REPLACE_WO_2       WorkOrderMaintenanceType = 2 // 更换
//	REMOVE_WO_3        WorkOrderMaintenanceType = 3 // 移除
//	REPLACE_CHILD_WO_4 WorkOrderMaintenanceType = 4 // 更换子设备
//)

// 工单
type WorkOrder struct {
	model.PkId

	model.GroupCorporation // 归属集团这一级

	Type OrderType `json:"Type" gorm:"column:type;comment:工单类型 1: 信访工单 2: 设备报修工单;type:smallint"` // 工单类型 PETITION_1: 信访工单 DEVICE_REPAIR_2: 设备报修工单

	// 流程id 考虑加入

	Code        string `json:"Code" gorm:"column:code;comment:工单编号;type:varchar(50);unique:workorders_code"`                         // 工单编号
	Title       string `json:"Title" gorm:"column:title;comment:工单标题;type:text"`                                                     // 工单标题
	ApplyStatus int64  `json:"ApplyStatus" gorm:"column:applystatus;default:1;comment:审批状态 0草稿 1进行中 2已完成 3驳回 4撤回 5废弃;type:smallint"` // 工单状态 审批状态 0草稿 1进行中 2已完成 3驳回 4撤回 5废弃

	CreatorUserId   int64  `json:"CreatorUserId" gorm:"column:creatoruserid;comment:创建人id [Type = 1 受理人] [Type = 2 报修人];type:bigint"` // 创建人id [Type = PETITION_1 受理人] [Type = DEVICE_REPAIR_2 报修人]
	CreatorUserName string `json:"CreatorUserName" gorm:"column:creatorusername;comment:创建人名;type:varchar(50)"`                       // 创建人名

	CreatorCorporationId int64  `json:"CreatorCorporationId" gorm:"column:creatorcorporationid;comment:创建人所属机构id [Type = 1 受理人所属机构] [Type = 2 报修人所属机构];type:bigint"` // 创建人所属机构id [Type = PETITION_1 受理人所属机构] [Type = DEVICE_REPAIR_2 报修人所属机构]
	CreatorCorporation   string `json:"CreatorCorporation" gorm:"column:creatorcorporation;comment:创建人所属机构;type:varchar(50)"`                                        // 创建人所属机构
	CreatorDepartmentId  int64  `json:"CreatorDepartmentId" gorm:"column:creatordepartmentid;comment:创建人所属部门id 机构类型为部门;type:bigint"`                                 // 创建人所属部门id 机构类型为部门
	CreatorDepartment    string `json:"CreatorDepartment" gorm:"column:creatordepartment;comment:创建人所属部门 机构类型为部门;type:varchar(50)"`                                  // 创建人所属部门 机构类型为部门

	PetitionOriginDictId  int64  `json:"PetitionOriginDictId" gorm:"column:petitionorigindictid;comment:[Type = 1 信访来源id];type:bigint"`      // [Type = PETITION_1 信访来源id]
	PetitionOriginDictKey string `json:"PetitionOriginDictKey" gorm:"column:petitionorigindictkey;comment:[Type = 1 信访来源];type:varchar(50)"` // [Type = PETITION_1 信访来源]

	PetitionTypeDictId  int64  `json:"PetitionTypeDictId" gorm:"column:petitiontypedictid;comment:[Type = 1 信访分类id];type:bigint"`      // [Type = PETITION_1 信访分类id]
	PetitionTypeDictKey string `json:"PetitionTypeDictKey" gorm:"column:petitiontypedictkey;comment:[Type = 1 信访分类];type:varchar(50)"` // [Type = PETITION_1 信访分类]

	RecvCorporationId int64           `json:"RecvCorporationId" gorm:"column:recvcorporationid;comment:[Type = 1，2 去向部门id];type:bigint"` // [Type = PETITION_1 去向部门id] [Type = DEVICE_REPAIR_2 去向部门id]
	RecvCorporation   string          `json:"RecvCorporation" gorm:"column:recvcorporation;comment:[Type = 1，2 去向部门];type:varchar(50)"`  // [Type = PETITION_1 去向部门] [Type = DEVICE_REPAIR_2 去向部门]
	PetitionCode      string          `json:"PetitionCode" gorm:"column:petitioncode;comment:[Type = 1 信访件编号];type:varchar(50)"`         // [Type = PETITION_1 信访件编号]
	PetitionAt        model.LocalTime `json:"PetitionAt" gorm:"column:petitionat;comment:[Type = 1 信访时间];type:timestamp"`                // [Type = PETITION_1 信访时间]
	Petitioner        string          `json:"Petitioner" gorm:"column:petitioner;comment:[Type = 1 信访人];type:varchar(50)"`               // [Type = PETITION_1 信访人]
	PetitionerPhone   string          `json:"PetitionerPhone" gorm:"column:petitionerphone;comment:[Type = 1 信访人联系方式];type:varchar(50)"` // [Type = PETITION_1 信访人联系方式]

	Content               string     `json:"Content" gorm:"column:content;comment:[Type = 1 信访人描述] [Type = 2 报修描述];type:text"`                                     // [Type = PETITION_1 信访人描述] [Type = DEVICE_REPAIR_2 报修描述]
	Files                 model.JSON `json:"Files" gorm:"column:files;comment:附件;type:json"`                                                                       // 附件
	FkRepairDeviceId      int64      `json:"FkRepairDeviceId" gorm:"column:fkrepairdeviceid;comment:[Type = 2 报修设备明细id ];type:bigint"`                             // [Type = DEVICE_REPAIR_2 报修设备明细id DeviceDetail.Id]
	RepairDevice          string     `json:"RepairDevice" gorm:"column:repairdevice;comment:[Type = 2 报修设备明细编号];type:varchar(50)"`                                 // [Type = DEVICE_REPAIR_2 报修设备明细编号]
	Lng                   int64      `json:"Lng" gorm:"column:lng;comment:[Type = 2 经度 ];type:integer;default:0"`                                                  // [Type = DEVICE_REPAIR_2 地图选点经度]
	Lat                   int64      `json:"Lat" gorm:"column:lat;comment:[Type = 2 纬度 ];type:integer;default:0"`                                                  // [Type = DEVICE_REPAIR_2 地图选点纬度]
	RepairClassDictId     int64      `json:"RepairClassDictId" gorm:"column:repairclassdictid;comment:[Type = 2 报修大类id];type:bigint;default:0"`                    // [Type = DEVICE_REPAIR_2 报修大类id]
	RepairClassDictKey    string     `json:"RepairClassDictKey" gorm:"column:repairclassdictkey;comment:[Type = 2 报修大类];type:varchar(50);default:''"`              // [Type = DEVICE_REPAIR_2 报修大类]
	License               string     `json:"License" gorm:"column:license;comment:[Type = 2 报修车辆];type:varchar(10);default:''"`                                    // [Type = DEVICE_REPAIR_2 报修车辆]
	LineId                int64      `json:"LineId" gorm:"column:lineid;comment:[Type = 2 报修车辆、站点所属线路id];type:bigint;default:0"`                                   // [Type = DEVICE_REPAIR_2 报修车辆所属线路id]
	Line                  string     `json:"Line" gorm:"column:line;comment:[Type = 2 报修车辆、站点所属线路];type:varchar(50);default:''"`                                   // [Type = DEVICE_REPAIR_2 报修车辆所属线路]
	VehicleCorporationId  int64      `json:"VehicleCorporationId"      gorm:"column:vehiclecorporationid;comment:[Type = 2 报修车辆所属机构id];type:bigint;default:0"`     // [Type = DEVICE_REPAIR_2 报修车辆所属机构id]
	VehicleCorporation    string     `json:"VehicleCorporation"        gorm:"column:vehiclecorporation;comment:[Type = 2 报修车辆所属机构];type:varchar(100);default:''"`  // [Type = DEVICE_REPAIR_2 报修车辆所属机构]
	UpParkingId           int64      `json:"UpParkingId" gorm:"column:upparkingid;comment:报修的是车载设备：停车场（线路上行场区）id 报修的是场站设备：场站;type:bigint;default:0"`               // 报修的是车载设备：停车场（线路上行场区） 报修的是场站设备：场站
	UpParking             string     `json:"UpParking" gorm:"column:upparking;comment:报修的是车载设备：停车场（线路上行场区） 报修的是场站设备：场站;type:varchar(50);default:''"`               // 报修的是车载设备：停车场（线路上行场区） 报修的是场站设备：场站
	StationId             int64      `json:"StationId" gorm:"column:stationid;comment:报修的是站台设备：站点、站台id;type:bigint;default:0"`                                     //报修的是站台设备： 站点、站台id
	Station               string     `json:"Station" gorm:"column:station;comment:报修的是站台设备：站点、站台名;type:varchar(50);default:''"`                                    // 报修的是站台设备：站点、站台名
	Address               string     `json:"Address" gorm:"column:address;comment:报修地址;type:text;default:''"`                                                      // 报修地址
	MaintainerFactoryId   int64      `json:"MaintainerFactoryId" gorm:"column:maintainerfactoryid;type:bigint;default:0;comment:维修方id;"`                           // 维修方id
	MaintainerFactoryKey  string     `json:"MaintainerFactoryKey" gorm:"column:maintainerfactorykey;type:varchar(50);default:'';comment:维修方，质保期内为维修方，质保期外为过保维修方;"` // 维修方，质保期内为供货方，质保期外为过保维修方
	IsInWarranty          int64      `json:"IsInWarranty" gorm:"column:isinwarranty;type:smallint;default:0;comment:是否在保修期内 1是  2否"`
	RepairMethod          int64      `json:"RepairMethod" gorm:"column:repairmethod;type:smallint;default:0;comment:报修方式 0未知  1车队查看  2直接报修"`
	DeviceStatus          int64      `json:"DeviceStatus" gorm:"column:devicestatus;type:smallint;default:0;comment:设备状态 0未知  1设备无异常  2设备异常，厂家维修  3设备异常，修理厂维修"`
	ChildDeviceId         int64      `json:"ChildDeviceId" gorm:"column:childdeviceid;type:bigint;default:0;comment:子设备ID (关联child_devices表)"`
	FormStep              int64      `json:"FormStep" gorm:"column:formstep;type:smallint;default:1;comment:表单当前处的阶段 1默认 2车队查看->3车队已查看->4负责人指派表单->5负责人指派表单已填->6一级指派人指派表单->7一级指派人指派表单已填->8二级指派人指派表单->9二级指派人指派表单已填->10维修表单->11维修表单已填"`
	AssignFirstStepUsers  model.JSON `json:"AssignFirstStepUsers" gorm:"column:assignfirststepusers;type:json;comment:指派的一级审批人"`
	AssignSecondStepUsers model.JSON `json:"AssignSecondStepUsers" gorm:"column:assignsecondstepusers;type:json;comment:指派的二级审批人"`
	AssignThirdStepUsers  model.JSON `json:"AssignThirdStepUsers" gorm:"column:assignthirdstepusers;type:json;comment:指派的三级审批人"`
	AssignRepairUsers     model.JSON `json:"AssignRepairUsers" gorm:"column:assignrepairusers;type:json;comment:指派的维修人员"`

	// 维修方填写下面字段
	ActualDevicesId     int64      `json:"ActualDevicesId" gorm:"column:actualdevicesid;type:bigint;default:0;comment:实际报修设备id，不一定有值;"`           // 实际报修设备id，不一定有值
	MaintainerStaffId   int64      `json:"MaintainerStaffId" gorm:"column:maintainerstaffid;type:bigint;default:0;comment:维修人员id;"`               // 维修人员id
	MaintainerStaffName string     `json:"MaintainerStaffName" gorm:"column:maintainerstaffname;type:varchar(50);default:'';comment:维修人员;"`       // 维修人员
	MaintenanceType     int64      `json:"MaintenanceType" gorm:"column:maintenancetype;type:smallint;default:0;comment:维修方式 1维修、2更换、3移除、4更换零件;"` // 维修方式 1维修、2更换、3移除
	MaintenanceDesc     string     `json:"MaintenanceDesc" gorm:"column:maintenancedesc;type:text;default:'';comment:维修情况;"`                      // 维修情况
	MaintenanceFiles    model.JSON `json:"MaintenanceFiles" gorm:"column:maintenancefiles;type:json;comment:维修附件;"`                               //维修附件
	Amount              int64      `json:"Amount" gorm:"column:amount;type:integer;default:0;comment:维修总金额 单位分;"`                                 // 维修总金额 单位分
	//IsFleetSelect        int64           `json:"IsFleetSelect" gorm:"column:isfleetselect;type:smallint;default:2;comment:是否需要车队填写表单  1是 2否 3已填写"`
	//IsRepairSelect       int64           `json:"IsRepairSelect" gorm:"column:isrepairselect;comment:报修流程当前人员是否需要选择备件 1:不需要选择备件 2:需要;type:smallint;default:1"` // [Type = DEVICE_REPAIR_2 报修流程当前人员是否需要选择备件 NO_1:不需要选择备件 YES_2:需要]
	//IsRepairSelected     int64           `json:"IsRepairSelected" gorm:"column:isrepairselected;type:smallint;comment:是否已填写过表单 1是 2否;default:0"`              // 是否已经选择了备件 1是 2否
	IsMaterialMissing    int64           `json:"IsMaterialMissing" gorm:"column:ismaterialmissing;type:smallint;comment:是否缺失维修物料;default:2"` // 是否缺失维修物料 1是 2否
	RepairConfirmStartAt model.LocalTime `json:"RepairConfirmStartAt" gorm:"column:repairconfirmstartat;type:timestamp;comment:维修确认开始时间"`
	ProcessDoneAt        model.LocalTime `json:"ProcessDoneAt" gorm:"column:processdoneat;type:timestamp;comment:流程结束时间"`
	ParentID             int64           `json:"ParentId" gorm:"column:parentid;comment:主数据id;type:bigint;default:0"`

	model.Timestamp // 创建时间

	TopCorporationName string `json:"TopCorporationName" gorm:"-"`
}

type File struct {
	FkFileId int64  // 文件id
	Url      string // url
	Type     int64  // 文件类型(兼容老数据0也当作图片处理) 1图片 2视频
	Path     string // 文件相对路径
}

func (w *WorkOrder) TableName() string {
	return "work_orders"
}

func (w *WorkOrder) ApplyStatusFieldName() string {
	return "applystatus"
}

func (w *WorkOrder) TemplateFormId() string {
	if w.Type == PETITION_1 {
		return config.PetitionWorkOrderReportFormTemplate
	} else if w.Type == DEVICE_REPAIR_2 {
		return config.DeviceWorkOrderReportFormTemplate
	} else {
		return ""
	}
}

func (w *WorkOrder) Add(tx *gorm.DB) error {
	if w.Id == 0 {
		w.Id = model.Id()
	}
	return tx.Create(w).Error
}

// UpdatePetition 重新发起时 更新信访工单
func (w *WorkOrder) UpdatePetition(tx *gorm.DB) error {
	return tx.Model(&WorkOrder{}).Where("Id=?", w.Id).
		Select(
			"PetitionOriginDictId", "PetitionOriginDictKey", "PetitionTypeDictId", "PetitionTypeDictKey", "RecvCorporationId",
			"RecvCorporation", "PetitionCode", "PetitionAt", "Petitioner", "PetitionerPhone",
			"Content", "Title", "Files",
		).
		Updates(w).Error
}

// UpdateRepair 重新发起时 更新设备报修工单
func (w *WorkOrder) UpdateRepair(tx *gorm.DB) error {
	return tx.Model(&WorkOrder{}).Where("Id=?", w.Id).
		Select(
			"FkRepairDeviceId", "RepairDevice", "Lng", "Lat", "RepairClassDictId",
			"RepairClassDictKey", "License", "LineId", "Line", "VehicleCorporationId",
			"VehicleCorporation", "UpParkingId", "UpParking", "StationId", "Station",
			"Address", "IsInWarranty", "MaintainerFactoryId", "MaintainerFactoryKey", "Files", "Content",
			"RepairMethod", "DeviceStatus", "FormStep",
		).
		Updates(w).Error
}

func (w *WorkOrder) UpdatesTx(tx *gorm.DB, columns map[string]interface{}) error {
	return tx.Model(&WorkOrder{}).Where("Id=?", w.Id).Updates(columns).Error
}

func (w *WorkOrder) FindByCode(code string) WorkOrder {
	var workOrder WorkOrder
	model.DB().Model(&WorkOrder{}).Where("Code = ?", code).First(&workOrder)

	return workOrder
}

// MyList 我的工单列表
func (w *WorkOrder) MyList(customStatus int64, userId int64, wkType []string, code string, applyStatus int64, createUserId int64, processCurrentHandler string, content string, objectName string, lineName string, s time.Time, e time.Time, paginator model.Paginator) ([]WorkOrder, int64, error) {
	var rsp []WorkOrder
	var totalCount int64

	var where *gorm.DB

	switch customStatus {
	case 0:
		//和我相关的所有工单-我的工单
		where = model.DB().Table("lbpm_apply_processes AS lap").Select("ItemId").
			Joins("JOIN lbpm_apply_process_has_handlers laphh ON lap.forminstanceid=laphh.forminstanceid").
			Where("laphh.UserId=?", userId).
			Where("lap.templateformid IN ?", wkType)
	case 1:
		//	待我处理-待处理
		where = model.DB().Table("lbpm_apply_processes AS lap").Select("ItemId").
			Joins("JOIN lbpm_apply_process_has_handlers laphh ON lap.forminstanceid=laphh.forminstanceid").
			Where("laphh.UserId=?", userId).
			Where("laphh.NodeType=?", util.ProcessNodeTypeForApprove).
			Where("laphh.Status=?", util.ProcessNodeHandleStatusForDoing).
			Where("lap.templateformid IN ?", wkType).
			Where("lap.Status != ?", util.ProcessStatusForAbandon) //
	case 2:
		//	已处理
		where = model.DB().Table("lbpm_apply_processes AS lap").Select("ItemId").
			Joins("JOIN lbpm_apply_process_has_handlers laphh ON lap.forminstanceid=laphh.forminstanceid").
			Where("laphh.UserId=?", userId).
			Where("laphh.Status=?", util.ProcessNodeHandleStatusForDone).
			Where("lap.Status=?", util.ProcessStatusForDoing).
			Where("lap.templateformid IN ?", wkType)
	case 3:
		//	已办结
		where = model.DB().Table("lbpm_apply_processes AS lap").Select("ItemId").
			Joins("JOIN lbpm_apply_process_has_handlers laphh ON lap.forminstanceid=laphh.forminstanceid").
			Where("laphh.UserId=?", userId).
			Where("lap.Status=?", util.ProcessStatusForDone).
			Where("lap.templateformid IN ?", wkType)
	case 4:
		//	我创建的
		where = model.DB().Table("lbpm_apply_processes AS lap").Select("ItemId").
			Joins("JOIN lbpm_apply_process_has_handlers laphh ON lap.forminstanceid=laphh.forminstanceid").
			Where("lap.templateformid IN ?", wkType).
			Where("lap.applyuserid = ?", userId)

	case 5:
		//	已处理(手机端)
		where = model.DB().Table("lbpm_apply_processes AS lap").Select("ItemId").
			Joins("JOIN lbpm_apply_process_has_handlers laphh ON lap.forminstanceid=laphh.forminstanceid").
			Where("laphh.UserId=?", userId).
			Where("laphh.NodeType=?", util.ProcessNodeTypeForApprove).
			Where("laphh.Status=?", util.ProcessNodeHandleStatusForDone).
			Where("lap.templateformid IN ?", wkType)

	}

	tx := model.DB().Model(&WorkOrder{}).Where("id IN (?)", where)

	if !s.IsZero() {
		tx.Where("CreatedAt >= ?", s)
	}

	if !e.IsZero() {
		tx.Where("CreatedAt < ?", e)
	}

	if code != "" {
		tx.Where("Code LIKE ?", "%"+code+"%")
	}

	if objectName != "" {
		tx.Where("License LIKE ? OR Station LIKE ? OR (UpParking LIKE ? AND License = '' AND UpParkingId > 0)", "%"+objectName+"%", "%"+objectName+"%", "%"+objectName+"%")
	}

	if lineName != "" {
		tx.Where("Line LIKE ?", "%"+lineName+"%")
	}

	if content != "" {
		tx.Where("Content LIKE ?", "%"+content+"%")
	}

	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	}

	if createUserId > 0 {
		tx.Where("CreatorUserId = ?", createUserId)
	}

	if processCurrentHandler != "" {
		tx.Where("Id IN (?)", model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId IN ?", []string{config.DeviceWorkOrderReportFormTemplate, config.PetitionWorkOrderReportFormTemplate}).Where("CurrentHandlerUserName LIKE ?", "%"+processCurrentHandler+"%"))
	}

	err := tx.Count(&totalCount).Order("CreatedAt DESC").Limit(paginator.Limit).Offset(paginator.Offset).Find(&rsp).Error

	return rsp, totalCount, err
}

// AllList 所有工单列表
func (w *WorkOrder) AllList(groupId int64, vehicleDeviceCorpIs []int64, wkType OrderType, title, code string, maintenanceType int64,
	applyStatus int64, creatorName string, creatorDeptId int64, creatorCorpId int64, license, parking, station, objectName string,
	rClassIds []int64, dCategoryId int64, name, processCurrentHandler, content, repairDeviceCode, lineName string, ProcessTotalStartNum,
	ProcessTotalEndNum, RepairStartNum, RepairEndNum, RepairConfirmStartNum, RepairConfirmEndNum int64, startAt, endAt time.Time, ParentID int64, paginator model.Paginator) ([]WorkOrder, int64, error) {
	var rsp []WorkOrder
	var totalCount int64

	tx := model.DB().Model(&WorkOrder{}).Where("GroupId = ?", groupId)
	if ParentID != 0 {
		tx.Where("ParentID = ?", ParentID)
	} else {
		tx.Where("(ParentID = 0 OR ParentID IS NULL)")
	}
	if wkType > 0 {
		tx.Where("Type = ?", wkType)
	}

	if title != "" {
		tx.Where("Title LIKE ?", "%"+title+"%")
	}

	if content != "" {
		tx.Where("Content LIKE ?", "%"+content+"%")
	}

	if repairDeviceCode != "" {
		tx.Where("RepairDevice LIKE ?", "%"+repairDeviceCode+"%")
	}

	if code != "" {
		tx.Where("Code LIKE ?", "%"+code+"%")
	}

	if maintenanceType > 0 {
		tx.Where("MaintenanceType = ?", maintenanceType)
	}

	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	} else {
		tx.Where("ApplyStatus IN ?", []int64{util.ApplyStatusForNone, util.ApplyStatusForDoing, util.ApplyStatusForDone})
	}

	if creatorName != "" {
		tx.Where("CreatorUserName LIKE ?", "%"+creatorName+"%")
	}

	if lineName != "" {
		tx.Where("Line LIKE ?", "%"+lineName+"%")
	}

	if creatorDeptId > 0 {
		tx.Where("CreatorDepartmentId = ?", creatorDeptId)
	}

	if creatorCorpId > 0 {
		tx.Where("CreatorCorporationId = ?", creatorCorpId)
	}

	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}
	if parking != "" {
		tx.Where("UpParking LIKE ?", "%"+parking+"%")
	}
	if station != "" {
		tx.Where("Station LIKE ?", "%"+station+"%")
	}

	if objectName != "" {
		tx.Where("License LIKE ? OR Station LIKE ? OR (UpParking LIKE ? AND License = '' AND UpParkingId > 0)", "%"+objectName+"%", "%"+objectName+"%", "%"+objectName+"%")
	}

	// 工单表报修设备的大类
	if len(rClassIds) > 0 {
		tx.Where("RepairClassDictId IN ?", rClassIds)
		if len(vehicleDeviceCorpIs) > 0 {
			tx.Where("VehicleCorporationId IN ?", vehicleDeviceCorpIs)
		}
	}

	if dCategoryId > 0 {
		tx.Where("FkRepairDeviceId IN (?)", model.DB().Model(&DeviceDetail{}).Select("Id").Where("DeviceModelId IN (?)", model.DB().Model(&DeviceModel{}).Select("Id").Where("DeviceCategoryDictId = ?", dCategoryId)))
	}

	if name != "" {
		tx.Where("FkRepairDeviceId IN (?)", model.DB().Model(&DeviceDetail{}).Select("Id").Where("DeviceModelId IN (?)", model.DB().Model(&DeviceModel{}).Select("Id").Where("Name LIKE ?", "%"+name+"%")))
	}

	if processCurrentHandler != "" {
		tx.Where("Id IN (?)", model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ?", config.DeviceWorkOrderReportFormTemplate).Where("CurrentHandlerUserName LIKE ?", "%"+processCurrentHandler+"%"))
	}

	if !startAt.IsZero() {
		tx.Where("CreatedAt >= ?", startAt)
	}

	if !endAt.IsZero() {
		tx.Where("CreatedAt < ?", endAt)
	}
	if ProcessTotalStartNum > 0 || ProcessTotalEndNum > 0 {
		dayDiff := fmt.Sprintf("ceil(CAST(extract(epoch FROM(ProcessDoneAt-CreatedAt)) / (24 * 60 * 60) AS NUMERIC))")
		if ProcessTotalStartNum > 0 {
			tx.Where(dayDiff+" >= ?", ProcessTotalStartNum)
		}

		if ProcessTotalEndNum > 0 {
			tx.Where(dayDiff+" <= ?", ProcessTotalEndNum)
		}
	}

	if RepairConfirmStartNum > 0 || RepairConfirmEndNum > 0 {
		dayDiff := fmt.Sprintf("ceil(CAST(extract(epoch FROM(ProcessDoneAt-RepairConfirmStartAt)) / (24 * 60 * 60) AS NUMERIC))")
		if RepairConfirmStartNum > 0 {
			tx.Where(dayDiff+" >= ?", RepairConfirmStartNum)
		}

		if RepairConfirmEndNum > 0 {
			tx.Where(dayDiff+" <= ?", RepairConfirmEndNum)
		}
	}

	if RepairStartNum > 0 || RepairEndNum > 0 {
		dayDiff := fmt.Sprintf("ceil(CAST(extract(epoch FROM(RepairConfirmStartAt-CreatedAt)) / (24 * 60 * 60) AS NUMERIC))")
		if RepairStartNum > 0 {
			tx.Where(dayDiff+" >= ?", RepairStartNum)
		}

		if RepairEndNum > 0 {
			tx.Where(dayDiff+" <= ?", RepairEndNum)
		}
	}
	tx.Count(&totalCount)
	if paginator.Limit > 0 {
		tx = tx.Limit(paginator.Limit).Offset(paginator.Offset)
	}
	err := tx.Order("CreatedAt DESC").Scan(&rsp).Error
	if err != nil {
		return nil, 0, err
	}

	return rsp, totalCount, nil
}

type DashboardList struct {
	Id                     int64  `json:"Id" gorm:"column:id"`
	Code                   int64  `json:"Code" gorm:"column:code"`
	RepairDevice           string `json:"RepairDevice" gorm:"column:repairdevice"`
	CreatorUserId          int64  `json:"CreatorUserId" gorm:"column:creatoruserid"`
	CreatorUserName        string `json:"CreatorUserName" gorm:"column:creatorusername"`
	CreatorCorporationName string `json:"CreatorCorporationName" gorm:"column:creatorcorporationname"`
	ApplyStatus            int64  `json:"ApplyStatus" gorm:"column:applystatus;"`
	//IsRepairSelect  int64           `json:"IsRepairSelect" gorm:"column:isrepairselect;"`
	FormStep  int64           `json:"FormStep" gorm:"column:formstep;"`
	CreatedAt model.LocalTime `json:"CreatedAt" gorm:"column:createdat"`

	ProcessTitle   string `json:"ProcessTitle" gorm:"-"`
	FormInstanceId int64  `json:"FormInstanceId" gorm:"-"`
	ProcessId      string `json:"ProcessId" gorm:"-"`
	CurrentHandler string `json:"CurrentHandler" gorm:"-"`
}

func (w *WorkOrder) DashboardList(processTitle, code, deviceCode, applyUser, currentHandler string, applyStatus, formStep int64, paginator model.Paginator) ([]DashboardList, int64) {
	var workOrders []DashboardList
	var count int64
	tx := model.DB().Model(&WorkOrder{})

	if code != "" {
		tx.Where("Code LIKE ?", "%"+code+"%")
	}

	if deviceCode != "" {
		tx.Where("RepairDevice LIKE ?", "%"+deviceCode+"%")
	}

	if applyUser != "" {
		tx.Where("CreatorUserName LIKE ?", "%"+applyUser+"%")
	}

	if processTitle != "" {
		tx.Where("Id IN (?)", model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ?", config.DeviceWorkOrderReportFormTemplate).Where("Title LIKE ?", "%"+processTitle+"%"))
	}

	if currentHandler != "" {
		tx.Where("Id IN (?)", model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ?", config.DeviceWorkOrderReportFormTemplate).Where("CurrentHandlerUserName LIKE ?", "%"+currentHandler+"%"))
	}

	if formStep > 0 {
		tx.Where("FormStep = ?", formStep)
	}

	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	}

	tx.Count(&count).Order("CreatedAt DESC").Limit(paginator.Limit).Offset(paginator.Offset).Scan(&workOrders)

	return workOrders, count
}

func (w *WorkOrder) GetDetail(id int64) error {
	return model.DB().Model(&WorkOrder{}).Where("Id = ?", id).Scan(w).Error
}

func (w *WorkOrder) FirstBy(id int64) WorkOrder {
	var workOrder WorkOrder
	model.DB().Model(&WorkOrder{}).Where("Id = ?", id).Find(&workOrder)

	return workOrder
}

func (w *WorkOrder) GetCount(startAt, endAt time.Time) int64 {
	var count int64
	model.DB().Model(&WorkOrder{}).Where("CreatedAt >= ? AND CreatedAt < ?", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat)).Count(&count)

	return count
}
func (w *WorkOrder) GetIds(startAt, endAt time.Time, applyStatus int64) []int64 {
	var ids []int64
	tx := model.DB().Model(&WorkOrder{}).Where("CreatedAt >= ? AND CreatedAt < ?", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))

	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	}

	tx.Pluck("id", &ids)

	return ids
}

// 更新维修信息
func (w *WorkOrder) EditMaintenance(tx *gorm.DB) error {
	return tx.Model(&WorkOrder{}).Where("Id = ?", w.Id).Select(
		"MaintainerFactoryId", "MaintainerFactoryKey", "ActualDevicesId", "MaintainerStaffId", "MaintainerStaffName",
		"MaintenanceType", "MaintenanceDesc", "MaintenanceFiles", "Amount", "FormStep",
	).Updates(&w).Error
}

// 设置是否需要选择备件
//func (w *WorkOrder) EditIsRepairSelect(id int64, val int64) error {
//	return model.DB().Model(&WorkOrder{}).Where("Id = ?", id).Update("IsRepairSelect", val).Error
//}

//func (w *WorkOrder) EditDeviceStatus(tx *gorm.DB) error {
//	return tx.Model(&WorkOrder{}).Select("FormStep", "DeviceStatus").Where("Id = ?", w.Id).Updates(&w).Error
//}

//func (w *WorkOrder) SetFleetSelect(formStep int64) error {
//	return model.DB().Model(&WorkOrder{}).Where("Id = ?", w.Id).Update("IsFleetSelect", formStep).Error
//}

// 设置是否缺失维修物料
func (w *WorkOrder) UpdateColumn(id int64, column string, val int64) error {
	return model.DB().Model(&WorkOrder{}).Where("Id = ?", id).Update(column, val).Error
}

func (w *WorkOrder) UpdateColumns(id int64, val map[string]interface{}) error {
	return model.DB().Model(&WorkOrder{}).Where("Id = ?", id).Updates(val).Error
}

func (w *WorkOrder) GetUsedDeviceWorkOrder(deviceDetailId int64) WorkOrder {
	var workOrder WorkOrder
	model.DB().Model(&WorkOrder{}).
		Where("Type=?", DEVICE_REPAIR_2).
		Where("ApplyStatus=?", util.ApplyStatusForDoing).
		Where("FkRepairDeviceId=?", deviceDetailId).First(&workOrder)

	return workOrder
}
func (w *WorkOrder) IsUsed(deviceDetailId int64) (bool, error) {
	var countWO, countWORSP int64
	err := model.DB().Model(&WorkOrder{}).
		Where("Type=?", DEVICE_REPAIR_2).
		Where("ApplyStatus=?", util.ApplyStatusForDoing).
		Where("FkRepairDeviceId=?", deviceDetailId).
		Count(&countWO).Error
	if err != nil {
		return false, err
	}

	err = model.DB().Table("work_orders wo").
		Joins("INNER JOIN work_order_repair_spare_parts worsp ON wo.Id=worsp.FkWorkOrdersId").
		Where("wo.Type=?", DEVICE_REPAIR_2).
		Where("wo.ApplyStatus=?", util.ApplyStatusForDoing).
		Where("worsp.FkDevicesId=?", deviceDetailId).
		Count(&countWORSP).Error
	if err != nil {
		return false, err
	}
	return countWO+countWORSP > 0, nil
}

// GetTotalFlowTime 流转总时长
func (w *WorkOrder) GetTotalFlowTime(workOrderIds []int64) int64 {
	var timeLength int64
	model.DB().Model(&WorkOrder{}).Select("COALESCE(SUM(trunc(EXTRACT(EPOCH FROM (ProcessDoneAt - CreatedAt)))::numeric),0)").Where("Id IN ? AND ProcessDoneAt IS NOT NULL", workOrderIds).Scan(&timeLength)
	return timeLength
}

// GetTotalRepairTime 维修总时长
func (w *WorkOrder) GetTotalRepairTime(workOrderIds []int64) int64 {
	var timeLength int64
	model.DB().Model(&WorkOrder{}).Select("SUM(trunc(EXTRACT(EPOCH FROM (RepairConfirmStartAt - CreatedAt)))::numeric)").Where("Id IN ? AND RepairConfirmStartAt IS NOT NULL", workOrderIds).Scan(&timeLength)
	return timeLength
}

// GetTotalConfirmTime 确认总时长
func (w *WorkOrder) GetTotalConfirmTime(workOrderIds []int64) int64 {
	var timeLength int64
	model.DB().Model(&WorkOrder{}).Select("COALESCE(SUM(trunc(EXTRACT(EPOCH FROM (ProcessDoneAt - RepairConfirmStartAt)))::numeric),0)").Where("Id IN ? AND ProcessDoneAt IS NOT NULL AND RepairConfirmStartAt IS NOT NULL", workOrderIds).Scan(&timeLength)
	return timeLength
}

func (wrc *WorkOrder) Merge(MainWorkOrderId int64, ChildrenIds []int64) error {
	tx := model.DB().Begin().Model(&WorkOrder{})
	tx = tx.Session(&gorm.Session{})
	// 主工单 ParentID为0
	err := tx.Where("ParentID = ?", MainWorkOrderId).
		Updates(map[string]interface{}{"ParentID": 0}).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	// 子工单的子孩子不再作为父级
	for _, id := range ChildrenIds {
		err = tx.Where("ParentID = ?", id).Updates(map[string]interface{}{"ParentID": 0}).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	// 子工单 ParentID为MainWorkOrderId
	err = tx.Where("Id IN ?", ChildrenIds).Updates(map[string]interface{}{"ParentID": MainWorkOrderId}).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}

type CorporationWorkOrderCount struct {
	CorporationId   int64  `json:"CorporationId" gorm:"column:corporationid"`
	CorporationName string `json:"CorporationName" gorm:"column:corporationname"`
	LineId          int64  `json:"LineId" gorm:"column:lineid"`
	LineName        string `json:"LineName" gorm:"column:linename"`
	WorkOrderCount  int64  `json:"WorkOrderCount" gorm:"column:workordercount"`
}

func (w *WorkOrder) GetCorporationWorkOrderCount(startAt, endAt time.Time, classId int64) []CorporationWorkOrderCount {
	var records []CorporationWorkOrderCount
	model.DB().Model(&WorkOrder{}).Select("VehicleCorporationId as CorporationId", "VehicleCorporation as CorporationName", "COUNT(*) as WorkOrderCount").
		Where("RepairClassDictId  = ? AND ApplyStatus !=? AND VehicleCorporationId > 0", classId, util.ApplyStatusForAbandon).
		Where("CreatedAt >= ? AND CreatedAt <= ?", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat)).
		Group("VehicleCorporationId,VehicleCorporation").
		Scan(&records)
	return records
}

func (w *WorkOrder) GetLineWorkOrderCount(startAt, endAt time.Time, classId int64) []CorporationWorkOrderCount {
	var records []CorporationWorkOrderCount
	model.DB().Model(&WorkOrder{}).Select("LineId", "Line as LineName", "COUNT(*) as WorkOrderCount").
		Where("RepairClassDictId  = ? AND ApplyStatus !=? AND LineId > 0", classId, util.ApplyStatusForAbandon).
		Where("CreatedAt >= ? AND CreatedAt <= ?", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat)).
		Group("LineId,Line").
		Scan(&records)
	return records
}

type DeviceWorkOrderCount struct {
	DeviceId       int64 `json:"DeviceId" gorm:"column:deviceid"`
	WorkOrderCount int64 `json:"WorkOrderCount" gorm:"column:workordercount"`
}

func (w *WorkOrder) GetDeviceWorkOrderCount(startAt, endAt time.Time, isInWarranty int64) []DeviceWorkOrderCount {
	var records []DeviceWorkOrderCount
	tx := model.DB().Model(&WorkOrder{}).Select("FkRepairDeviceId as DeviceId", "COUNT(*) as WorkOrderCount").
		Where("ApplyStatus !=?", util.ApplyStatusForAbandon).
		Where("CreatedAt >= ? AND CreatedAt <= ?", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))
	if isInWarranty > 0 {
		tx.Where("IsInWarranty = ?", isInWarranty)
	}

	tx.Group("fkrepairdeviceid").Scan(&records)
	return records
}

// 报修工单 备件表
type WorkOrderRepairSparePart struct {
	model.PkId

	model.GroupCorporation // 归属集团这一级

	FkWorkOrdersId        int64  `json:"FkWorkOrdersId" gorm:"column:fkworkordersid;comment:工单id WorkOrder.Id;type:bigint"`       // 工单id WorkOrder.Id
	CurrentUserId         int64  `json:"CurrentUserId" gorm:"column:currentuserid;comment:当前处理人id;type:bigint"`                   // 当前处理人id
	CurrentUserName       string `json:"CurrentUserName" gorm:"column:currentusername;comment:当前处理人名;type:varchar(50)"`           // 当前处理人名
	CurrentCorporationId  int64  `json:"CurrentCorporationId" gorm:"column:currentcorporationid;comment:当前处理人所属机构id;type:bigint"` // 当前处理人所属机构id
	CurrentCorporation    string `json:"CurrentCorporation" gorm:"column:currentcorporation;comment:当前处理人所属机构;type:varchar(50)"`  // 当前处理人所属机构
	FkDevicesId           int64  `json:"FkDevicesId" gorm:"column:fkdevicesid;comment:设备明细id DeviceDetail.Id;type:bigint"`        // 设备明细id 如果有设备id 则选择的是已有设备，否则为添加的物料
	Name                  string `json:"Name" gorm:"column:name;comment:设备名;type:varchar(50);"`                                   // 设备名
	Price                 int64  `json:"Price" gorm:"column:price;comment:设备单价 单位分;type:bigint"`                                  // 设备单价 单位分
	Num                   int64  `json:"Num" gorm:"column:num;type:integer;default:1;comment:数量;"`                                // 数量
	Code                  string `json:"Code" gorm:"column:code;type:varchar(50);comment:设备明细编号;"`                                // 设备明细编号
	DeviceCategoryDictId  int64  `json:"DeviceCategoryDictId" gorm:"column:devicecategorydictid;comment:设备种类id;type:bigint;"`     // 设备种类id
	DeviceCategoryDictKey string `json:"DeviceCategoryDictKey" gorm:"column:devicecategorydictkey;comment:设备种类;type:varchar(50)"` // 设备种类

	model.Timestamp

	FkDevicesIdStr          string `json:"FkDevicesIdStr" gorm:"-"`          //  // 设备明细id =0为物料 >0为设备 兼容wx小程序大整型
	DeviceCategoryDictIdStr string `json:"DeviceCategoryDictIdStr" gorm:"-"` //    // 设备种类id 兼容wx小程序大整型
}

func (w *WorkOrderRepairSparePart) GetByWorkOrderId(workOrderId int64) ([]WorkOrderRepairSparePart, error) {
	var rsp []WorkOrderRepairSparePart
	err := model.DB().Model(&WorkOrderRepairSparePart{}).Where("FkWorkOrdersId = ?", workOrderId).Scan(&rsp).Error
	return rsp, err
}

func (w *WorkOrderRepairSparePart) Add(tx *gorm.DB, items []WorkOrderRepairSparePart) error {
	return tx.Create(&items).Error
}

func (w *WorkOrderRepairSparePart) TransactionDelete(tx *gorm.DB, workOrderId int64) error {
	return tx.Model(&WorkOrderRepairSparePart{}).Where("FkWorkOrdersId = ?", workOrderId).Delete(&WorkOrderRepairSparePart{}).Error
}

func (w *WorkOrderRepairSparePart) GetTotalMoney(workOrderIds []int64) int64 {
	var money int64
	model.DB().Model(&WorkOrderRepairSparePart{}).Select("SUM(Num*Price)").Where("FkWorkOrdersId IN ?", workOrderIds).Scan(&money)
	return money
}

type WorkOrderReplaceChildDevice struct {
	model.PkId
	WorkOrderId      int64 `json:"WorkOrderId" gorm:"column:workorderid;type:bigint;comment:工单ID"`
	OldChildDeviceId int64 `json:"OldChildDeviceId" gorm:"column:oldchilddeviceid;type:bigint;comment:旧子设备ID"`
	NewChildDeviceId int64 `json:"NewChildDeviceId" gorm:"column:newchilddeviceid;type:bigint;comment:新子设备ID"`
	Price            int64 `json:"Price" gorm:"column:price;type:integer;comment:价格 单位：分"`
	model.Timestamp

	OldChildDeviceModelName   string `json:"OldChildDeviceModelName" gorm:"-"`
	OldChildDeviceCode        string `json:"OldChildDeviceCode" gorm:"-"`
	OldChildDeviceFactoryCode string `json:"OldChildDeviceFactoryCode" gorm:"-"`
	NewChildDeviceModelName   string `json:"NewChildDeviceModelName" gorm:"-"`
	NewChildDeviceCode        string `json:"NewChildDeviceCode" gorm:"-"`
	NewChildDeviceFactoryCode string `json:"NewChildDeviceFactoryCode" gorm:"-"`
}

func (wrc *WorkOrderReplaceChildDevice) BeforeCreate(tx *gorm.DB) error {
	wrc.Id = model.Id()
	return nil
}

func (wrc *WorkOrderReplaceChildDevice) TransactionBatchCreate(tx *gorm.DB, items []WorkOrderReplaceChildDevice) error {
	return tx.Create(&items).Error
}

func (wrc *WorkOrderReplaceChildDevice) GetByWorkOrderId(workOrderId int64) []WorkOrderReplaceChildDevice {
	var items []WorkOrderReplaceChildDevice
	model.DB().Model(&WorkOrderReplaceChildDevice{}).Where("WorkOrderId = ?", workOrderId).Find(&items)
	return items
}

func (wrc *WorkOrderReplaceChildDevice) GetTotalMoney(workOrderIds []int64) int64 {
	var money int64
	model.DB().Model(&WorkOrderReplaceChildDevice{}).Select("SUM(Price)").Where("WorkOrderId IN ?", workOrderIds).Scan(&money)
	return money
}

func (wrc *WorkOrderReplaceChildDevice) GetReplaceChildDevice(workOrderIds []int64) []WorkOrderReplaceChildDevice {
	var records []WorkOrderReplaceChildDevice
	model.DB().Model(&WorkOrderReplaceChildDevice{}).Where("WorkOrderId IN ?", workOrderIds).Find(&records)
	return records
}
