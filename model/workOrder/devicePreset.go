package workOrder

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
)

//type DictType int64 // 字典类型
//const (
//	PETITION_ORIGIN_1 DictType = 1 // 信访工单-信访来源
//	PETITION_CLASS_2  DictType = 2 // 信访工单-信访分类
//
//	REPAIR_CORPORATION_11 DictType = 11 // 设备报修工单-供货方/厂家/协办单位
//	REPAIR_CLASS_13       DictType = 13 // 设备报修工单-设备大类
//	REPAIR_CATEGORY_14    DictType = 14 // 设备报修工单-设备种类
//)
//
//type DicDelete int8
//
//const (
//	NO_0  DicDelete = 0
//	YES_1 DicDelete = 1
//)
//
////const (
////	VEHICLE_DEVICE string = "车载设备"
////	STATION_DEVICE string = "站台设备"
////	PARKING_DEVICE string = "场站设备"
////)
//
//// 字典
//type Dict struct {
//	model.PkId
//
//	GroupId int64 `json:"GroupId" gorm:"column:groupid;comment:集团id;type:bigint;uniqueIndex:wod_groupid_dicttype_code"` // 集团id
//
//	DictType     DictType `json:"DictType" gorm:"column:dicttype;comment:字典类型 必填;type:integer;uniqueIndex:wod_groupid_dicttype_code"`               // 字典类型 必填
//	DictCode     string   `json:"DictCode" gorm:"column:dictcode;comment:字典编码 默认=id,可以自行设置;type:varchar(50);uniqueIndex:wod_groupid_dicttype_code"` // 字典编码 默认=id,可以自行设置
//	DictKey      string   `json:"DictKey" gorm:"column:dictkey;comment:字典内容 必填;type:varchar(50)"`                                                   // 字典内容 必填
//	DictValue    string   `json:"DictValue" gorm:"column:dictvalue;comment:字典值;type:varchar(50)"`                                                   // 字典值
//	ObjectType   int64    `json:"ObjectType" gorm:"column:objecttype;type:smallint;default:0;comment:关联对象类型 1车辆  2场站  3站点"`
//	ParentId     int64    `json:"ParentId"        gorm:"column:parentid;comment:上一级id， 第一级ParentId=0;type:bigint"`       // 上一级id， 第一级ParentId=0
//	ParentIdPath string   `json:"ParentIdPath"    gorm:"column:parentidpath;comment:ParentIdPath(包含自己) 1,2,3;type:text"` // ParentIdPath(包含自己) 1,2,3
//
//	IsDeleted     DicDelete `json:"IsDeleted" gorm:"column:isdeleted;comment:是否已删除 0否 1是;type:smallint"` // 是否已删除 0否 1是
//	DevicePresets []DevicePreset
//	model.Timestamp
//
//	Children []Dict `json:"-" gorm:"-"`
//}
//
//func (*Dict) TableName() string {
//	return "dict"
//}
//
//func (d *Dict) Add() error {
//	return model.DB().Create(&d).Error
//}
//
//// 根据 GroupId、DictType 查询dictCode
//func (d *Dict) GetCodeWithOptions() (string, error) {
//	var rsp Dict
//	err := model.DB().Model(&Dict{}).Select("DictCode").Where("GroupId = ? AND DictType = ?", d.GroupId, d.DictType).Order("CreatedAt DESC").Limit(1).Scan(&rsp).Error
//	return rsp.DictCode, err
//}
//
//func (d *Dict) GetRecords(groupId, dictType int64) []Dict {
//	var records []Dict
//	model.DB().Model(&Dict{}).Where("GroupId = ? AND DictType = ?", groupId, dictType).Order("CreatedAt ASC").Scan(&records)
//	return records
//}
//func (d *Dict) GetChildRecords(dictId int64) []Dict {
//	var records []Dict
//	model.DB().Model(&Dict{}).Preload("DevicePresets").Where("ParentId = ?", dictId).Order("CreatedAt ASC").Scan(&records)
//	return records
//}
//
//// 根据 GroupId、DictType 查询
//func (d *Dict) GetWithOptions() ([]Dict, error) {
//	var rsp []Dict
//	tx := model.DB().Model(&Dict{}).Where("GroupId = ? AND IsDeleted = ?", d.GroupId, NO_0)
//
//	if d.DictType > 0 {
//		tx.Where("DictType = ?", d.DictType)
//	}
//	err := tx.Order("CreatedAt ASC").Scan(&rsp).Error
//	return rsp, err
//}
//
//func (d *Dict) Edit() error {
//	return model.DB().Model(&Dict{}).Where("Id = ?", d.Id).Select("DictKey", "ParentId", "DictValue").Updates(d).Error
//}
//
//func (d *Dict) TransactionEdit(tx *gorm.DB) error {
//	return tx.Model(&Dict{}).Where("Id = ?", d.Id).Select("DictKey", "DictValue").Updates(d).Error
//}
//
//// 软删除
//func (d *Dict) SoftDelete() error {
//	return model.DB().Model(&Dict{}).Where("Id = ?", d.Id).Update("IsDeleted", YES_1).Error
//}
//
//func (d *Dict) Get() (Dict, error) {
//	var rsp Dict
//	err := model.DB().Model(&Dict{}).Where("Id = ?", d.Id).Scan(&rsp).Error
//	if err != nil {
//		return Dict{}, err
//	}
//	return rsp, err
//}
//
//func (d *Dict) FirstById(id int64) Dict {
//	var dict Dict
//	model.DB().Model(&Dict{}).Where("Id = ?", id).First(&dict)
//	return dict
//}
//
//func (d *Dict) ListPreset(classId, categoryId int64) ([]Dict, error) {
//	var rsp []Dict
//	tx := model.DB().Model(&Dict{}).
//		Preload("DevicePresets", func(db *gorm.DB) *gorm.DB {
//			return db.Order("Id ASC")
//		}).
//		Where("DictType = ?", REPAIR_CATEGORY_14).
//		Where("IsDeleted=?", NO_0)
//	if classId > 0 {
//		tx = tx.Where("ParentId = ?", classId)
//	}
//	if categoryId > 0 {
//		tx = tx.Where("Id = ?", categoryId)
//	}
//	err := tx.Order("Id ASC").Find(&rsp).Error
//
//	return rsp, err
//}

// DevicePreset 设备种类的责任人、品牌方、供货方、保修方、过保维修方
type DevicePreset struct {
	model.PkId
	ClassId int64 `json:"ClassId" gorm:"column:classid;comment:设备大类id;type:bigint"`
	DictId  int64 `json:"DictId" gorm:"column:dictid;type:bigint;comment:设备种类字典id;"`
	Type    int64 `json:"Type" gorm:"column:type;type:smallint;comment:类型 1责任人 2品牌方 3过保维修方 4供货方  5保修方;"`
	ItemId  int64 `json:"ItemId" gorm:"column:itemid;type:bigint;comment:根据预设类型决定 1=>staffid，2-5=>device_factories表id;"`
	model.Timestamp

	ShotName    string `json:"ShotName" gorm:"-"`    //供货方、保修方、品牌方、过保维修方缩写
	Name        string `json:"Name" gorm:"-"`        //供货方、保修方、品牌方、过保维修方名称
	StaffName   string `json:"StaffName" gorm:"-"`   // 责任人姓名
	Corporation string `json:"Corporation" gorm:"-"` // 责任人所属机构
	Option      string `json:"Option" gorm:"-"`      // 操作类型 create edit delete
	//Factory     setting.DeviceFactory `json:"Factory" gorm:"-"`
}

func (d *DevicePreset) BeforeCreate(tx *gorm.DB) error {
	d.Id = model.Id()
	return nil
}

func (d *DevicePreset) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&d).Error
}

//func (d *DevicePreset) FirstBy(id int64) DevicePreset {
//	var devicePreset DevicePreset
//	model.DB().Model(&DevicePreset{}).Where("Id = ?", id).Find(&devicePreset)
//	return devicePreset
//}

//func (d *DevicePreset) GetBy(classIds,dictIds []int64, presentType int64) []DevicePreset {
//	var presents []DevicePreset
//	tx := model.DB().Model(&DevicePreset{})
//	if len(dictIds) > 0 {
//		tx = tx.Where("DictId IN ?", dictIds)
//	}
//
//	if presentType > 0 {
//		tx = tx.Where("Type = ?", presentType)
//	}
//
//	tx.Scan(&presents)
//
//	return presents
//}

func (d *DevicePreset) TransactionUpdates(tx *gorm.DB) error {
	return tx.Model(&DevicePreset{}).Select("ItemId", "Type").Where("Id=?", d.Id).Updates(&d).Error
}

func (d *DevicePreset) TransactionDelete(tx *gorm.DB, id int64) error {
	return tx.Model(&DevicePreset{}).Delete("Id=?", id).Error
}

func (d *DevicePreset) CountLiable(tx *gorm.DB, dictId int64) int64 {
	var count int64
	tx.Model(&DevicePreset{}).Where("DictId=? AND Type=?", dictId, util.DevicePresetForLiable).Count(&count)
	return count
}
