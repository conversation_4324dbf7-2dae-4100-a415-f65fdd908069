package workOrder

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type MiniWorkOrder struct {
	model.PkId
	Code                  string `json:"Code" gorm:"column:code;comment:工单编号;type:varchar(50);unique:workorders_code"`
	CreatorPhone          string `json:"CreatorPhone" gorm:"column:creatorphone;comment:创建人手机;type:varchar(50)"`
	CateType              int64  `json:"CateType" gorm:"column:catetype;type:smallint;comment:1投诉  2表扬 3咨询"`
	Content               string `json:"Content" gorm:"column:content;comment:信访内容;type:text"`
	ApplyContent          string `json:"ApplyContent" gorm:"column:applycontent;comment:回复内容;type:text"`
	ApplyStatus           int64  `json:"ApplyStatus" gorm:"column:applystatus;default:2;comment:回复状态 1已回复 2未回复;type:smallint"`
	HandleStaffId         int64  `json:"HandleStaffId" gorm:"column:handlestaffid;type:bigint;default:0;comment:处理人员id;"`
	HandleStaffName       string `json:"HandleStaffName" gorm:"column:handlestaffname;type:varchar(50);default:'';comment:处理人员;"`
	HandleCorporationId   int64  `json:"HandleCorporationId" gorm:"column:handlecorporationid;type:bigint;default:0;comment:处理人机构;"`
	HandleCorporationName string `json:"HandleCorporationName" gorm:"column:handlecorporationname;type:varchar(50);default:'';comment:处理人机构;"`
	model.Timestamp              // 创建时间
}

func (m *MiniWorkOrder) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *MiniWorkOrder) Create() error {
	return model.DB().Create(&m).Error
}
func (m *MiniWorkOrder) FirstBy(id int64) (record MiniWorkOrder) {
	model.DB().Model(&MiniWorkOrder{}).Where("Id = ?", id).First(&record)
	return
}
func (m *MiniWorkOrder) UpdateApply() error {
	return model.DB().Select("ApplyContent", "ApplyStatus", "HandleStaffId", "HandleStaffName", "HandleCorporationId", "HandleCorporationName").Updates(&m).Error
}

func (m *MiniWorkOrder) GetBy(code string, cateType int64, creatorPhone string, paginator model.Paginator) ([]MiniWorkOrder, int64) {
	var records []MiniWorkOrder
	tx := model.DB().Model(&MiniWorkOrder{})
	if code != "" {
		tx.Where("Code LIKE ?", "%"+code+"%")
	}
	if creatorPhone != "" {
		tx.Where("CreatorPhone LIKE ?", "%"+creatorPhone+"%")
	}

	if cateType > 0 {
		tx.Where("CateType  = ?", cateType)
	}

	var count int64
	tx.Count(&count)

	tx.Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&records)

	return records, count
}
