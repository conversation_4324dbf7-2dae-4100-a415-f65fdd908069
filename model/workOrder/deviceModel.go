package workOrder

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
	"time"
)

//DeviceModel 设备型号
type DeviceModel struct {
	model.PkId
	GroupId             int64  `json:"GroupId"      gorm:"column:groupid;type:bigint;comment:集团Id;uniqueIndex:devicemodel_groupid_name;"`
	LiableStaffId       int64  `json:"LiableStaffId" gorm:"column:liablestaffid;type:integer;comment:责任人;default:0"`                  // 责任人
	LiableStaffName     string `json:"LiableStaffName" gorm:"column:liablestaffname;type:varchar(50);comment:责任人姓名;default:''"`       // 责任人姓名
	LiableCorporation   string `json:"LiableCorporation" gorm:"column:liablecorporation;type:varchar(50);comment:责任人所属机构;default:''"` // 责任人所属机构
	LiableCorporationId int64  `json:"LiableCorporationId" gorm:"column:liablecorporationid;type:bigint;comment:责任人所属机构id;default:0"` // 责任人所属机构id
	DeviceModelColumn

	Group         string                 `json:"Group"        gorm:"-"`
	BatchCodes    []DeviceModelBatchCode `json:"BatchCodes" gorm:"-"`
	IsExistDevice bool                   `json:"IsExistDevice" gorm:"-"`
	model.Timestamp
}

type DeviceModelColumn struct {
	Name                  string `json:"Name" gorm:"column:name;type:varchar(50);comment:设备型号名;uniqueIndex:devicemodel_groupid_name;" validate:"required"` // 设备型号名
	DeviceClassDictId     int64  `json:"DeviceClassDictId" gorm:"column:deviceclassdictid;comment:设备大类id;type:bigint" validate:"required"`                 // 设备大类id
	DeviceClassDictKey    string `json:"DeviceClassDictKey" gorm:"column:deviceclassdictkey;comment:设备大类;type:varchar(50)" validate:"required"`            // 设备大类
	DeviceCategoryDictId  int64  `json:"DeviceCategoryDictId" gorm:"column:devicecategorydictid;comment:设备种类id;type:bigint;" validate:"required"`          // 设备种类id
	DeviceCategoryDictKey string `json:"DeviceCategoryDictKey" gorm:"column:devicecategorydictkey;comment:设备种类;type:varchar(50)" validate:"required"`      // 设备种类

	SupplierFactoryId  int64  `json:"SupplierFactoryId" gorm:"column:supplierfactoryid;comment:供货方厂家id device_factories表id;type:bigint;uniqueIndex:devicemodel_groupid_name;" validate:"required"` // 供货方id
	SupplierFactoryKey string `json:"SupplierFactoryKey" gorm:"column:supplierfactorykey;comment:供货方厂家;type:varchar(50)"`                                                                          // 供货方
	BrandFactoryId     int64  `json:"BrandFactoryId" gorm:"column:brandfactoryid;comment:品牌方厂家id device_factories表id;type:bigint;uniqueIndex:devicemodel_groupid_name;" validate:"required"`
	BrandFactoryKey    string `json:"BrandFactoryKey" gorm:"column:brandfactorykey;comment:品牌方厂家;type:varchar(50)"`
	HandlerFactoryId   int64  `json:"HandlerFactoryId" gorm:"column:handlerfactoryid;comment:保修方厂家id device_factories表id;type:bigint" validate:"required"`
	HandlerFactoryKey  string `json:"HandlerFactoryKey" gorm:"column:handlerfactorykey;comment:保修方厂家;type:varchar(50)"`
}

func (dm *DeviceModel) BeforeCreate(db *gorm.DB) error {
	dm.Id = model.Id()
	return nil
}
func (dm *DeviceModel) TransactionAdd(tx *gorm.DB) error {
	return tx.Create(&dm).Error
}

func (dm *DeviceModel) Find(id int64) error {
	return model.DB().Model(&DeviceModel{}).Where("Id = ?", id).Scan(dm).Error
}

func (dm *DeviceModel) FirstBy(id int64) DeviceModel {
	var deviceModel DeviceModel
	model.DB().Model(&DeviceModel{}).Where("Id = ?", id).First(&deviceModel)

	return deviceModel
}

func (dm *DeviceModel) GetAll() []DeviceModel {
	var deviceModels []DeviceModel
	model.DB().Model(&DeviceModel{}).Find(&deviceModels)

	return deviceModels
}
func (dm *DeviceModel) IsExistBy(name string, brandDicId, supplierFactoryId int64) bool {
	var count int64
	model.DB().Model(&DeviceModel{}).Where("Name = ? AND SupplierFactoryId = ? AND BrandFactoryId = ?", name, supplierFactoryId, brandDicId).Count(&count)

	return count > 0
}

func (dm *DeviceModel) FindBy(name string, brandDicId, supplierFactoryId int64) DeviceModel {
	var m DeviceModel
	model.DB().Model(&DeviceModel{}).Where("Name = ? AND SupplierFactoryId = ? AND BrandFactoryId = ?", name, supplierFactoryId, brandDicId).Scan(&m)

	return m
}

func (dm *DeviceModel) List(groupId int64, classIds, categoryIds []int64, supplierKey, brandKey, handlerKey, maintainerKey string, liableStaffIds []int64, name string, isRepairStatus int64, paginator model.Paginator) ([]DeviceModel, int64, error) {
	var rsp []DeviceModel
	var totalCount int64

	tx := model.DB().Model(&DeviceModel{}).Where("GroupId = ?", groupId)

	if len(classIds) > 0 {
		tx.Where("DeviceClassDictId IN ?", classIds)
	}

	if len(categoryIds) > 0 {
		tx.Where("DeviceCategoryDictId IN ?", categoryIds)
	}

	if supplierKey != "" {
		tx.Where("SupplierFactoryKey LIKE ?", "%"+supplierKey+"%")
	}

	if brandKey != "" {
		tx.Where("BrandFactoryKey LIKE ?", "%"+brandKey+"%")
	}

	if handlerKey != "" {
		tx.Where("HandlerFactoryKey LIKE ?", "%"+handlerKey+"%")
	}
	if maintainerKey != "" {
		tx.Where("Id IN (?)", model.DB().Model(&DeviceModelBatchCode{}).Select("DeviceModelId").Where("MaintainerFactoryKey LIKE ?", "%"+maintainerKey+"%"))
	}

	if len(liableStaffIds) > 0 {
		tx.Where("LiableStaffId IN ?", liableStaffIds)
	}

	if name != "" {
		tx.Where("Name LIKE ?", "%"+name+"%")
	}

	if isRepairStatus == util.StatusForTrue {
		tx.Where("Id IN (?)", model.DB().Model(&DeviceModelBatchCode{}).Select("DeviceModelId").Where("ExpireAt >= ?", time.Now().Format(model.DateFormat)))
	}
	if isRepairStatus == util.StatusForFalse {
		tx.Where("Id IN (?)", model.DB().Model(&DeviceModelBatchCode{}).Select("DeviceModelId").Where("ExpireAt < ?", time.Now().Format(model.DateFormat)))
	}

	err := tx.Count(&totalCount).Order("convert_to(DeviceClassDictKey, 'GBK') ASC, convert_to(DeviceCategoryDictKey, 'GBK') ASC, convert_to(Name, 'GBK') ASC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&rsp).Error
	if err != nil {
		return nil, 0, err
	}

	return rsp, totalCount, nil
}

func (dm *DeviceModel) TransactionEdit(tx *gorm.DB) error {
	return tx.Model(&DeviceModel{}).Where("Id = ?", dm.Id).Select(
		"DeviceClassDictId", "DeviceClassDictKey", "DeviceCategoryDictId",
		"DeviceCategoryDictKey", "SupplierFactoryId", "SupplierFactoryKey", "BrandFactoryId",
		"BrandFactoryKey", "HandlerFactoryId", "HandlerFactoryKey", "LiableStaffId", "LiableStaffName",
		"LiableCorporation", "LiableCorporationId", "Name").Updates(dm).Error
}

func (dm *DeviceModel) TransactionDelete(tx *gorm.DB, ids []int64) error {
	return tx.Where("Id IN ?", ids).Delete(&dm).Error
}

// DeviceModelBatchCode 设备型号下的批次
type DeviceModelBatchCode struct {
	model.PkId
	DeviceModelId int64 `json:"DeviceModelId" gorm:"column:devicemodelid;type:bigint;comment:设备型号Id（device_models表）;uniqueIndex:device_model_batch_code_unique_index"`
	DeviceModelBatchCodeColumn
	model.Timestamp

	ChildDeviceCates []DeviceModelBatchCodeHasChildDeviceCate `json:"ChildDeviceCates" gorm:"-"`
	ActionType       string                                   `json:"ActionType" gorm:"-"` //操作类型 update create delete
	IsExistDevice    bool                                     `json:"IsExistDevice" gorm:"-"`
}

type DeviceModelBatchCodeColumn struct {
	Price      int64           `json:"Price" gorm:"column:price;comment:设备单价 单位分;type:integer"`
	BatchCode  string          `json:"BatchCode" gorm:"column:batchcode;comment:批次号;type:varchar(200);uniqueIndex:device_model_batch_code_unique_index" validate:"required"`
	PurchaseAt model.LocalTime `json:"PurchaseAt" gorm:"column:purchaseat;type:timestamp;comment:采购日期;" validate:"required"`
	ExpireAt   model.LocalTime `json:"ExpireAt" gorm:"column:expireat;comment:过保日期;type:timestamp" validate:"required"`
	//MaintainerDictId  int64           `json:"MaintainerDictId" gorm:"column:maintainerdictid;comment:过保维修方id（device_dicts表）;type:bigint" validate:"required"`
	//MaintainerDictKey string          `json:"MaintainerDictKey" gorm:"column:maintainerdictkey;comment:过保维修方名称;type:varchar(100)" validate:"required"`

	MaintainerFactoryId  int64  `json:"MaintainerFactoryId" gorm:"column:maintainerfactoryid;comment:过保维修方id（device_factories表）;type:bigint" validate:"required"`
	MaintainerFactoryKey string `json:"MaintainerFactoryKey" gorm:"column:maintainerfactorykey;comment:过保维修方名称;type:varchar(100)" validate:"required"`
}

func (dbc *DeviceModelBatchCode) BeforeCreate(db *gorm.DB) error {
	dbc.Id = model.Id()
	return nil
}

func (dbc *DeviceModelBatchCode) TransactionAdd(tx *gorm.DB) error {
	return tx.Create(&dbc).Error
}

func (dbc *DeviceModelBatchCode) TransactionUpdate(tx *gorm.DB) error {
	return tx.Model(&DeviceModelBatchCode{}).Where("Id = ?", dbc.Id).Select("*").Updates(&dbc).Error
}

func (dbc *DeviceModelBatchCode) TransactionDelete(tx *gorm.DB) error {
	return tx.Model(&DeviceModelBatchCode{}).Where("Id = ?", dbc.Id).Delete(&dbc).Error
}

func (dbc *DeviceModelBatchCode) TransactionDeleteByModelIds(tx *gorm.DB, modelIds []int64) error {
	return tx.Where("DeviceModelId IN ?", modelIds).Delete(&DeviceModelBatchCode{}).Error
}

func (dbc *DeviceModelBatchCode) FirstBy(id int64) DeviceModelBatchCode {
	var batchCode DeviceModelBatchCode
	model.DB().Model(&DeviceModelBatchCode{}).Where("Id = ?", id).First(&batchCode)

	return batchCode
}

func (dbc *DeviceModelBatchCode) FirstByBatchCode(modelId int64, code string) DeviceModelBatchCode {
	var batchCode DeviceModelBatchCode
	model.DB().Model(&DeviceModelBatchCode{}).Where("DeviceModelId = ? AND BatchCode = ?", modelId, batchCode).First(&batchCode)

	return batchCode
}
func (dbc *DeviceModelBatchCode) GetBy(modelId int64, maintainerKey string, isRepairStatus int64) []DeviceModelBatchCode {
	var batchCodes []DeviceModelBatchCode
	tx := model.DB().Model(&DeviceModelBatchCode{}).Where("DeviceModelId = ?", modelId)

	if maintainerKey != "" {
		tx = tx.Where("MaintainerFactoryKey LIKE ?", "%"+maintainerKey+"%")
	}

	if isRepairStatus == util.StatusForTrue {
		tx = tx.Where("ExpireAt >= ?", time.Now().Format(model.DateFormat))
	}

	if isRepairStatus == util.StatusForFalse {
		tx = tx.Where("ExpireAt < ?", time.Now().Format(model.DateFormat))
	}

	tx.Find(&batchCodes)

	return batchCodes
}

func (dbc *DeviceModelBatchCode) GetPurchaseAt(groupId int64, categoryId int64) []DeviceModelBatchCode {
	var batchCode []DeviceModelBatchCode
	subSql := model.DB().Model(&DeviceModel{}).Select("Id").Where("GroupId = ?", groupId)
	if categoryId > 0 {
		subSql = subSql.Where("DeviceCategoryDictId = ?", categoryId)
	}

	model.DB().Model(&DeviceModelBatchCode{}).Select("PurchaseAt").Where("DeviceModelId IN (?)", subSql).Group("purchaseat").Find(&batchCode)

	return batchCode
}

// GetWithIn 获取在保批次ID
func (dbc *DeviceModelBatchCode) GetWithIn(expireAt time.Time) []DeviceModelBatchCode {
	var batchCodes []DeviceModelBatchCode
	model.DB().Model(&DeviceModelBatchCode{}).Where("expireAt >= ?", expireAt.Format(model.DateFormat)).Find(&batchCodes)

	return batchCodes
}

// GetWithOut 获取过保批次ID
func (dbc *DeviceModelBatchCode) GetWithOut(expireAt time.Time) []DeviceModelBatchCode {
	var batchCodes []DeviceModelBatchCode
	model.DB().Model(&DeviceModelBatchCode{}).Where("expireAt < ?", expireAt.Format(model.DateFormat)).Find(&batchCodes)

	return batchCodes
}

// DeviceModelBatchCodeHasChildDeviceCate 设备型号对应的批次下的子设备
type DeviceModelBatchCodeHasChildDeviceCate struct {
	DeviceModelId          int64 `json:"DeviceModelId" gorm:"column:devicemodelid;type:bigint;comment:主设备型号Id（device_models表）"`
	DeviceModelBatchCodeId int64 `json:"DeviceModelBatchCodeId" gorm:"column:devicemodelbatchcodeid;type:bigint;comment:批次Id（device_model_batch_codes表）"`
	ChildDeviceCateId      int64 `json:"ChildDeviceCateId" gorm:"column:childdevicecateid;type:bigint;comment:子设备种类Id（child_device_cates表）"`
	ChildDeviceModelId     int64 `json:"ChildDeviceModelId" gorm:"column:childdevicemodelid;type:bigint;comment:子设备型号Id（child_device_cates表）"`
	model.Timestamp
	ChildDeviceCateName  string        `json:"ChildDeviceCateName" gorm:"-"`
	ChildDeviceModelName string        `json:"ChildDeviceModelName" gorm:"-"`
	ChildDevices         []ChildDevice `json:"ChildDevice" gorm:"-"`
}

func (dmc *DeviceModelBatchCodeHasChildDeviceCate) BatchCreate(cates []DeviceModelBatchCodeHasChildDeviceCate) error {
	return model.DB().Model(&DeviceModelBatchCodeHasChildDeviceCate{}).Create(&cates).Error
}

func (dmc *DeviceModelBatchCodeHasChildDeviceCate) TransactionBatchCreate(tx *gorm.DB, cates []DeviceModelBatchCodeHasChildDeviceCate) error {
	return tx.Model(&DeviceModelBatchCodeHasChildDeviceCate{}).Create(&cates).Error
}

func (dmc *DeviceModelBatchCodeHasChildDeviceCate) TransactionDeleteBy(tx *gorm.DB, bachCodeIds []int64) error {
	return tx.Where("DeviceModelBatchCodeId IN ?", bachCodeIds).Delete(&DeviceModelBatchCodeHasChildDeviceCate{}).Error
}

func (dmc *DeviceModelBatchCodeHasChildDeviceCate) GetByModelId(modelId int64) []DeviceModelBatchCodeHasChildDeviceCate {
	var cates []DeviceModelBatchCodeHasChildDeviceCate
	model.DB().Model(&DeviceModelBatchCodeHasChildDeviceCate{}).Where("DeviceModelId = ?", modelId).Order("ChildDeviceCateId ASC").Find(&cates)
	return cates
}

func (dmc *DeviceModelBatchCodeHasChildDeviceCate) GetByBatchCodeId(bachCodeId int64) []DeviceModelBatchCodeHasChildDeviceCate {
	var cates []DeviceModelBatchCodeHasChildDeviceCate
	model.DB().Model(&DeviceModelBatchCodeHasChildDeviceCate{}).Where("DeviceModelBatchCodeId = ?", bachCodeId).Order("ChildDeviceCateId ASC").Find(&cates)
	return cates
}
