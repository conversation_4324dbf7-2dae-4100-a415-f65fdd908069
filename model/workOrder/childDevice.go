package workOrder

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type ChildDeviceCate struct {
	model.PkId
	TopCorporationId int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:顶级机构ID"`
	ParentId         int64  `json:"ParentId" gorm:"column:parentid;type:bigint;comment:父级ID，0代表一级"`
	Name             string `json:"Name" gorm:"column:name;type:varchar;comment:名称"`
	ShotName         string `json:"ShotName" gorm:"column:shotname;type:varchar;comment:简称"`
	Supplier         string `json:"Supplier" gorm:"column:supplier;type:varchar;comment:供货方"`
	RepairParty      string `json:"RepairParty" gorm:"column:repairparty;type:varchar;comment:过保维修方"`
	model.Timestamp

	Children    []ChildDeviceCate `json:"Children" gorm:"-"`
	ActionType  string            `json:"ActionType" gorm:"-"`
	IsHasDevice bool              `json:"IsHasDevice" gorm:"-"`
}

func (cdc *ChildDeviceCate) BeforeCreate(db *gorm.DB) error {
	cdc.Id = model.Id()
	return nil
}

func (cdc *ChildDeviceCate) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&cdc).Error
}

func (cdc *ChildDeviceCate) TransactionUpdate(tx *gorm.DB) error {
	return tx.Select("Name", "ShotName", "Supplier", "RepairParty").Updates(&cdc).Error
}

func (cdc *ChildDeviceCate) TransactionDelete(tx *gorm.DB) error {
	return tx.Delete(&cdc).Error
}

func (cdc *ChildDeviceCate) FirstByShotName(shotName string) ChildDeviceCate {
	var cate ChildDeviceCate
	model.DB().Model(&ChildDeviceCate{}).Where("ShotName = ?", shotName).First(&cate)
	return cate
}
func (cdc *ChildDeviceCate) FirstById(id int64) ChildDeviceCate {
	var cate ChildDeviceCate
	model.DB().Model(&ChildDeviceCate{}).Where("Id = ?", id).First(&cate)
	return cate
}

func (cdc *ChildDeviceCate) ListAll(topCorporationId int64) []ChildDeviceCate {
	var cates []ChildDeviceCate
	model.DB().Model(&ChildDeviceCate{}).Where("TopCorporationId = ?", topCorporationId).Find(&cates)
	return cates
}

const (
	ChildDeviceFreezeStatusForDefault   = 0 //未冻结
	ChildDeviceFreezeStatusForUnBinding = 1 //解绑中 报修工单进行中需要拆除的子设备
	ChildDeviceFreezeStatusForBinding   = 2 //绑定中 报修工单进行中需要更换上去的子设备
)

type ChildDevice struct {
	model.PkId
	TopCorporationId    int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:顶级机构ID"`
	AssociationDeviceId int64           `json:"AssociationDeviceId" gorm:"column:associationdeviceid;type:bigint;comment:关联的设备ID（device_details表）"`
	CateId              int64           `json:"CateId" gorm:"column:cateid;type:bigint;comment:关联的设备种类ID（child_device_cates表）" validate:"required"`
	ModelId             int64           `json:"ModelId" gorm:"column:modelid;type:bigint;comment:关联的设备型号ID（child_device_cates表）" validate:"required"`
	Code                string          `json:"Code" gorm:"column:code;type:varchar(100);comment:设备编号;uniqueIndex"`
	CodeSeq             int64           `json:"CodeSeq" gorm:"column:codeseq;type:integer;comment:设备编号序号"`
	FactoryCode         string          `json:"FactoryCode" gorm:"column:factorycode;type:varchar(100);comment:厂家编号"`
	Price               int64           `json:"Price" gorm:"column:price;type:integer;comment:价格"`
	PurchaseAt          model.LocalTime `json:"PurchaseAt" gorm:"column:purchaseat;type:timestamp;comment:采购日期"`
	BatchCode           string          `json:"BatchCode" gorm:"column:batchcode;type:varchar(100);comment:批号" validate:"required"`
	ExpireAt            model.LocalTime `json:"ExpireAt" gorm:"column:expireat;type:timestamp;comment:过保日期"`
	QrCodePath          string          `json:"QrCodePath" gorm:"column:qrcodepath;type:varchar;comment:二维码存储路径"`
	FreezeStatus        int64           `json:"FreezeStatus" gorm:"column:freezestatus;type:smallint;default:0;comment:冻结状态 0未冻结 1解绑中（报修工单进行中需要拆除的子设备）  2绑定中（报修工单进行中需要更换上去的子设备）"`
	model.Timestamp

	CateName                     string `json:"CateName" gorm:"-"`
	ModelName                    string `json:"ModelName" gorm:"-"`
	AssociationDeviceCode        string `json:"AssociationDeviceCode" gorm:"-"`
	AssociationDeviceFactoryCode string `json:"AssociationDeviceFactoryCode" gorm:"-"`
	RepairParty                  string `json:"RepairParty" gorm:"-"`
}

func (cd *ChildDevice) BeforeCreate(db *gorm.DB) error {
	cd.Id = model.Id()
	return nil
}

func (cd *ChildDevice) BatchCreate(devices []ChildDevice) error {
	return model.DB().Model(&ChildDevice{}).Create(&devices).Error
}

func (cd *ChildDevice) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&cd).Error
}

func (cd *ChildDevice) Update() error {
	return model.DB().Model(&ChildDevice{}).Select("FactoryCode", "Price", "ExpireAt").Where("Id = ?", cd.Id).Updates(&cd).Error
}

func (cd *ChildDevice) TransactionUpdate(tx *gorm.DB) error {
	return tx.Model(&ChildDevice{}).Select("FactoryCode", "Price", "ExpireAt").Where("Id = ?", cd.Id).Updates(&cd).Error
}

func (cd *ChildDevice) TransactionUnBind(tx *gorm.DB, associationDeviceIds []int64) error {
	return tx.Model(&ChildDevice{}).Where("AssociationDeviceId IN ?", associationDeviceIds).Update("AssociationDeviceId", 0).Error
}

func (cd *ChildDevice) TransactionUpdateAssociationDeviceIdByIds(tx *gorm.DB, ids []int64, associationDeviceId int64) error {
	return tx.Model(&ChildDevice{}).Where("Id IN ?", ids).Update("AssociationDeviceId", associationDeviceId).Error
}

func (cd *ChildDevice) TransactionUpdateFreezeStatusByIds(tx *gorm.DB, ids []int64, freezeStatus int64) error {
	return tx.Model(&ChildDevice{}).Where("Id IN ?", ids).Update("FreezeStatus", freezeStatus).Error
}
func (cd *ChildDevice) UpdateFreezeStatusAndAssociationIdByIds(ids []int64, freezeStatus int64, associationId int64) error {
	return model.DB().Model(&ChildDevice{}).Where("Id IN ?", ids).Updates(map[string]interface{}{
		"freezestatus":        freezeStatus,
		"associationdeviceid": associationId,
	}).Error
}

func (cd *ChildDevice) GetBy(topCorporationId, cateId, modelId, isEnableUse int64, code, batchCode, associationDeviceCode, factoryCode, repairParty string, purchaseAt, expireAt time.Time, paginator model.Paginator) ([]ChildDevice, int64) {
	tx := model.DB().Model(&cd).Where("TopCorporationId = ?", topCorporationId)
	if cateId > 0 {
		tx = tx.Where("CateId = ?", cateId)
	}

	if modelId > 0 {
		tx = tx.Where("ModelId = ?", modelId)
	}
	if isEnableUse == 1 {
		tx = tx.Where("AssociationDeviceId = ? AND FreezeStatus = ?", 0, ChildDeviceFreezeStatusForDefault)
	}

	if code != "" {
		tx = tx.Where("Code LIKE ?", "%"+code+"%")
	}

	if batchCode != "" {
		tx = tx.Where("BatchCode LIKE ?", "%"+batchCode+"%")
	}

	if factoryCode != "" {
		tx = tx.Where("FactoryCode LIKE ?", "%"+factoryCode+"%")
	}

	if associationDeviceCode != "" {
		tx = tx.Where("AssociationDeviceId IN (?)", model.DB().Model(&DeviceDetail{}).Select("Id").Where("code LIKE ?", "%"+associationDeviceCode+"%"))
	}

	if repairParty != "" {
		tx = tx.Where("ModelId IN (?)", model.DB().Model(&ChildDeviceCate{}).Select("Id").Where("RepairParty LIKE ?", "%"+repairParty+"%"))
	}

	if !purchaseAt.IsZero() {
		tx = tx.Where("PurchaseAt = ?", purchaseAt.Format(model.DateFormat))
	}

	if !expireAt.IsZero() {
		tx = tx.Where("ExpireAt = ?", expireAt.Format(model.DateFormat))
	}

	var count int64
	tx.Count(&count)

	var devices []ChildDevice
	tx.Order("CreatedAt Desc").Offset(paginator.Offset).Limit(paginator.Limit).Find(&devices)
	return devices, count
}

func (cd *ChildDevice) GetMaxCodeSeq(cateId int64, batchCode string) int64 {
	var device ChildDevice
	model.DB().Model(&ChildDevice{}).Where("ModelId = ? AND BatchCode = ?", cateId, batchCode).Order("CodeSeq DESC").Limit(1).First(&device)
	return device.CodeSeq
}

func (cd *ChildDevice) ExistModelId(cateId int64) bool {
	var count int64
	model.DB().Model(&ChildDevice{}).Where("ModelId = ?", cateId).Count(&count)
	return count > 0
}

func (cd *ChildDevice) FirstById(id int64) ChildDevice {
	var device ChildDevice
	model.DB().Model(&ChildDevice{}).Where("Id = ?", id).First(&device)
	return device
}

func (cd *ChildDevice) FirstByCode(code string) ChildDevice {
	var device ChildDevice
	model.DB().Model(&ChildDevice{}).Where("Code = ?", code).First(&device)
	return device
}

func (cd *ChildDevice) FirstByFactoryCode(factoryCode string) ChildDevice {
	var device ChildDevice
	model.DB().Model(&ChildDevice{}).Where("FactoryCode = ?", factoryCode).First(&device)
	return device
}

func (cd *ChildDevice) TransactionDelete(tx *gorm.DB) error {
	return tx.Delete(&cd).Error
}

func (cd *ChildDevice) GetByModelId(modelId int64, freezeStatus int64) []ChildDevice {
	var devices []ChildDevice
	model.DB().Model(&cd).Where("ModelId = ? AND AssociationDeviceId = 0 AND FreezeStatus = ?", modelId, freezeStatus).Order("CreatedAt Desc").Find(&devices)
	return devices
}

func (cd *ChildDevice) GetByAssociationDeviceId(associationDeviceId int64) []ChildDevice {
	var devices []ChildDevice
	model.DB().Model(&cd).Where("AssociationDeviceId = ?", associationDeviceId).Order("CreatedAt Desc").Find(&devices)
	return devices
}
