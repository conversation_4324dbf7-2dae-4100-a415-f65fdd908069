package workOrder

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
	"time"
)

type PetitionWorkOrder struct {
	model.PkId
	model.Corporations                  //创建人所属机构
	Code                string          `json:"Code" gorm:"column:code;comment:工单编号;type:varchar(50);unique:workorders_code"`
	FromDictId          int64           `json:"FromDictId" gorm:"column:fromdictid;comment:信访来源id 关联dicts表 dictType=1的数据;type:bigint"`
	FromDictKey         string          `json:"FromDictKey" gorm:"column:fromdictkey;comment:信访来源名称;type:varchar(50)"`
	LevenOneCateDictId  int64           `json:"LevenOneCateDictId" gorm:"column:levenonecatedictid;comment:信访一级分类id 关联dicts表 dictType=2的数据;type:bigint"`
	LevenOneCateDictKey string          `json:"LevenOneCateDictKey" gorm:"column:levenonecatedictkey;comment:信访一级分类;type:varchar(50)"`
	CateDictId          int64           `json:"CateDictId" gorm:"column:catedictid;comment:信访分类id 关联dicts表 dictType=2的数据;type:bigint"`
	CateDictKey         string          `json:"CateDictKey" gorm:"column:catedictkey;comment:信访分类;type:varchar(50)"`
	ToCorporationId     int64           `json:"ToCorporationId" gorm:"column:tocorporationid;comment:交办部门id;type:bigint"`
	ToCorporationName   string          `json:"ToCorporationName" gorm:"column:tocorporationname;comment:交办部门;type:varchar(50)"`
	ToApplyUserId       int64           `json:"ToApplyUserId" gorm:"column:toapplyuserid;comment:交办人id;type:bigint"`
	ToApplyUserName     string          `json:"ToApplyUserName" gorm:"column:toapplyusername;comment:交办人;type:varchar(50)"`
	LineId              int64           `json:"LineId" gorm:"column:lineid;comment:信访线路id;type:bigint"`
	LineName            string          `json:"LineName" gorm:"column:linename;comment:信访线路;type:varchar(50)"`
	UniqueNo            string          `json:"UniqueNo" gorm:"column:uniqueno;comment:信访件编号;type:varchar(100)"`
	ReportAt            model.LocalTime `json:"ReportAt" gorm:"column:reportat;comment:信访时间;type:timestamp"`
	Petitioner          string          `json:"Petitioner" gorm:"column:petitioner;comment: 信访人;type:varchar(50)"`
	PetitionerPhone     string          `json:"PetitionerPhone" gorm:"column:petitionerphone;comment:信访人联系方式;type:varchar(50)"`
	Title               string          `json:"Title" gorm:"column:title;comment:标题;type:varchar(250)"`
	Content             string          `json:"Content" gorm:"column:content;comment:信访描述;type:text"`
	Files               model.JSON      `json:"Files" gorm:"column:files;comment:附件;type:json"`
	ApplyStatus         int64           `json:"ApplyStatus" gorm:"column:applystatus;default:1;comment:审批状态 0草稿 1进行中 2已完成 3驳回 4撤回 5废弃;type:smallint"`
	FormStep            int64           `json:"FormStep" gorm:"column:formstep;default:1;comment:表单步骤 1默认 2办理人填写 3填写完成,4不认可后的确认，6交办人二、三次处理，8监管中心回复 9结束 10交办人最终回复 11结束"`
	//HandleResult string     `json:"HandleResult" gorm:"column:handleresult;comment:处理意见;type:text"`
	//HandleFiles  model.JSON `json:"HandleFiles" gorm:"column:handlefiles;comment:处理附件;type:json"`
	model.OpUser
	model.Timestamp

	TransferCorporation string                          `json:"TransferCorporation" gorm:"-"` // 流转至机构
	TransferAt          *model.LocalTime                `json:"TransferAt" gorm:"-"`          // 流转到达时间
	TransferUserName    string                          `json:"TransferUserName" gorm:"-"`    // 流转到人员
	ApplyTotalTimeLen   int64                           `json:"ApplyTotalTimeLen" gorm:"-"`   // 流转总时长 单位秒
	CorporationId       int64                           `json:"CorporationId" gorm:"-"`
	CorporationName     string                          `json:"'CorporationName'" gorm:"-"`
	HandleResults       []PetitionWorkOrderHandleResult `json:"HandleResults" gorm:"-"`
	ProcessTitle        string                          `json:"ProcessTitle" gorm:"-"`
	FormInstanceId      int64                           `json:"FormInstanceId" gorm:"-"`
	ProcessId           string                          `json:"ProcessId" gorm:"-"`
	CurrentHandler      string                          `json:"CurrentHandler" gorm:"-"`
}

func (p *PetitionWorkOrder) TableName() string {
	return "petition_work_orders"
}

func (p *PetitionWorkOrder) ApplyStatusFieldName() string {
	return "applystatus"
}

func (p *PetitionWorkOrder) TemplateFormId() string {
	return config.PetitionWorkOrderReportFormTemplate
}

func (p *PetitionWorkOrder) BeforeCreate(db *gorm.DB) error {
	p.Code = util.GenerateIdentifier()
	p.Id = model.Id()
	return nil
}

func (p *PetitionWorkOrder) Create(tx *gorm.DB) error {
	return tx.Create(p).Error
}

func (p *PetitionWorkOrder) UpdateAll(tx *gorm.DB) error {
	return tx.Select("*").Omit("Id", "Code").Updates(&p).Error
}
func (p *PetitionWorkOrder) DashboardUpdate() error {
	return model.DB().Select("*").Omit("Id", "Code").Updates(&p).Error
}

func (p *PetitionWorkOrder) UpdateColumn(id int64, column string, val int64) error {
	return model.DB().Model(&PetitionWorkOrder{}).Where("Id = ?", id).Update(column, val).Error
}

func (p *PetitionWorkOrder) FirstBy(id int64) PetitionWorkOrder {
	var workOrder PetitionWorkOrder
	model.DB().Model(&PetitionWorkOrder{}).Where("Id = ?", id).First(&workOrder)

	return workOrder
}

func (p *PetitionWorkOrder) UpdateFormStep(tx *gorm.DB) error {
	return tx.Select("FormStep").Updates(&p).Error
}

func (p *PetitionWorkOrder) GetBy(code, uniqueNo string, toCorporationId int64, toUserName string, lineId,
	fromDictId, cateDictId int64, startReportAt, endReportAt time.Time, startAt, endAt time.Time, corporationId int64,
	userName string, handlerName string, applyStatus int64, paginator model.Paginator) ([]PetitionWorkOrder, int64, []WorkOrderCateCount) {
	var workOrders []PetitionWorkOrder
	tx := model.DB().Model(&PetitionWorkOrder{})

	if code != "" {
		tx.Where("Code LIKE ?", "%"+code+"%")
	}
	if uniqueNo != "" {
		tx.Where("UniqueNo LIKE ?", "%"+uniqueNo+"%")
	}
	if toCorporationId > 0 {
		tx.Where("ToCorporationId = ?", toCorporationId)
	}
	if toUserName != "" {
		tx.Where("ToApplyUserName LIKE ?", "%"+toUserName+"%")
	}

	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	if fromDictId > 0 {
		tx.Where("FromDictId = ?", fromDictId)
	}
	if cateDictId > 0 {
		tx.Where("CateDictId = ?", cateDictId)
	}

	if !startReportAt.IsZero() {
		tx.Where("ReportAt >= ?", startReportAt.Format(model.TimeFormat))
	}
	if !endReportAt.IsZero() {
		tx.Where("ReportAt <= ?", endReportAt.Format(model.TimeFormat))
	}

	if !startAt.IsZero() {
		tx.Where("CreatedAt >= ?", startAt.Format(model.TimeFormat))
	}
	if !endAt.IsZero() {
		tx.Where("CreatedAt <= ?", endAt.Format(model.TimeFormat))
	}

	if corporationId > 0 {
		tx.Scopes(model.WhereCorporation(corporationId))
	}

	if userName != "" {
		tx.Where("OpUserName LIKE ?", "%"+userName+"%")
	}

	if handlerName != "" {
		subQuery := model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ? AND CurrentHandlerUserName LIKE ?", config.PetitionWorkOrderReportFormTemplate, "%"+handlerName+"%")
		tx.Where("Id IN (?)", subQuery)
	}

	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	}

	tx = tx.Session(&gorm.Session{})
	var count int64
	tx.Count(&count)

	tx.Order("ReportAt desc").Offset(paginator.Offset).Limit(paginator.Limit).Find(&workOrders)

	var cateCounts []WorkOrderCateCount
	tx.Select("CateDictId", "CateDictKey", "Count(*) as TotalCount").Where("ApplyStatus = ?", util.ApplyStatusForDone).Group("CateDictId,CateDictKey").Scan(&cateCounts)
	return workOrders, count, cateCounts
}

func (p *PetitionWorkOrder) DashboardGetBy(processTitle, code, userName, toUserName string, handlerName string, applyStatus int64, formStep int64, paginator model.Paginator) ([]PetitionWorkOrder, int64) {
	var workOrders []PetitionWorkOrder
	tx := model.DB().Model(&PetitionWorkOrder{})

	if processTitle != "" {
		tx.Where("Id IN (?)", model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ?", config.PetitionWorkOrderReportFormTemplate).Where("Title LIKE ?", "%"+processTitle+"%"))
	}

	if code != "" {
		tx.Where("Code LIKE ?", "%"+code+"%")
	}

	if toUserName != "" {
		tx.Where("ToApplyUserName LIKE ?", "%"+toUserName+"%")
	}

	if userName != "" {
		tx.Where("OpUserName LIKE ?", "%"+userName+"%")
	}

	if handlerName != "" {
		subQuery := model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ? AND CurrentHandlerUserName LIKE ?", config.PetitionWorkOrderReportFormTemplate, "%"+handlerName+"%")
		tx.Where("Id IN (?)", subQuery)
	}

	if formStep > 0 {
		tx.Where("FormStep = ?", formStep)
	}

	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	}

	var count int64
	tx.Count(&count)

	tx.Order("ReportAt desc").Offset(paginator.Offset).Limit(paginator.Limit).Find(&workOrders)
	return workOrders, count
}

func (p *PetitionWorkOrder) GetMineList(userId int64, handleStatus int64, code, uniqueNo, userName string, corporationId,
	fromDictId, cateDictId, lineId, toCorporationId int64, startAt, endAt time.Time, paginator model.Paginator) ([]PetitionWorkOrder, int64) {
	var sub = model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").
		Where("ItemTableName = ?", p.TableName()).
		Where("FormInstanceId IN (?)", model.DB().Model(&processModel.LbpmApplyProcessHasHandler{}).
			Select("FormInstanceId").Where("UserId=?", userId))

	if handleStatus == util.WorkOrderHandleStatusForMeWaiting {
		sub = model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").
			Where("ItemTableName = ?", p.TableName()).
			Where("Status == ?", util.ProcessStatusForDoing).
			Where("FormInstanceId IN (?)", model.DB().Model(&processModel.LbpmApplyProcessHasHandler{}).
				Select("FormInstanceId").Where("UserId=?", userId).
				Where("NodeType=?", util.ProcessNodeTypeForApprove).
				Where("Status=?", util.ProcessNodeHandleStatusForDoing))
	}

	if handleStatus == util.WorkOrderHandleStatusForMeDone {
		sub = model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").
			Where("ItemTableName = ?", p.TableName()).
			Where("Status == ?", util.ProcessStatusForDoing).
			Where("FormInstanceId IN (?)", model.DB().Model(&processModel.LbpmApplyProcessHasHandler{}).
				Select("FormInstanceId").Where("UserId=?", userId).
				Where("NodeType=?", util.ProcessNodeTypeForApprove).
				Where("Status=?", util.ProcessNodeHandleStatusForDone))
	}

	if handleStatus == util.WorkOrderHandleStatusForMeOver {
		sub = model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").
			Where("ItemTableName = ?", p.TableName()).
			Where("Status == ?", util.ProcessStatusForDone).
			Where("FormInstanceId IN (?)", model.DB().Model(&processModel.LbpmApplyProcessHasHandler{}).
				Select("FormInstanceId").Where("UserId=?", userId))
	}

	var workOrders []PetitionWorkOrder
	tx := model.DB().Model(&PetitionWorkOrder{}).Where("Id IN (?)", sub)

	if code != "" {
		tx.Where("Code LIKE ?", "%"+code+"%")
	}

	if uniqueNo != "" {
		tx.Where("UniqueNo LIKE ?", "%"+uniqueNo+"%")
	}

	if userName != "" {
		tx.Where("OpUserName LIKE ?", "%"+userName+"%")
	}

	if corporationId > 0 {
		tx.Scopes(model.WhereCorporation(corporationId))
	}
	if fromDictId > 0 {
		tx.Where("FromDictId = ?", fromDictId)
	}
	if cateDictId > 0 {
		tx.Where("CateDictId = ?", cateDictId)
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	if toCorporationId > 0 {
		tx.Where("ToCorporationId = ?", toCorporationId)
	}

	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.TimeFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.TimeFormat))
	}

	var count int64
	tx.Count(&count)

	tx.Order("ReportAt desc").Offset(paginator.Offset).Limit(paginator.Limit).Find(&workOrders)
	return workOrders, count
}

type WorkOrderCateCount struct {
	CateDictId  int64  `json:"CateDictId" gorm:"column:catedictid;"`
	CateDictKey string `json:"CateDictKey" gorm:"column:catedictkey;"`
	TotalCount  int64  `json:"TotalCount" gorm:"column:totalcount;"`
}

func (p *PetitionWorkOrder) CountByCateDict() []WorkOrderCateCount {
	var counts []WorkOrderCateCount
	model.DB().Model(&PetitionWorkOrder{}).Select("CateDictId", "CateDictKey", "Count(*) as TotalCount").Where("ApplyStatus = ?", util.ApplyStatusForDone).Group("CateDictId,CateDictKey").Scan(&counts)

	return counts
}

type PetitionWorkOrderHandleResult struct {
	model.PkId
	PetitionWorkOrderId   int64      `json:"PetitionWorkOrderId" gorm:"column:petitionworkorderid;comment:信访工单id;type:bigint;index:petitionworkorderid_handle_result_index;default:0"`
	CorporationId         int64      `json:"CorporationId" gorm:"column:corporationid;comment:操作人所属部门;type:bigint"`
	CorporationName       string     `json:"CorporationName" gorm:"column:corporationname;comment:操作人所属部门;type:varchar(50)"`
	FormStep              int64      `json:"FormStep" gorm:"column:formstep;default:1;comment:表单步骤 1默认 2办理人填写 3填写完成,4不认可后的确认，6交办人二、三次处理，8监管中心回复 9结束 10交办人最终回复 11结束"`
	IsAccept              int64      `json:"IsAccept" gorm:"column:isaccept;default:1;type:smallint;comment:是否认可/申诉 1是 2否"`
	HandleResult          string     `json:"HandleResult" gorm:"column:handleresult;comment:处理意见;type:text"`
	HandleFiles           model.JSON `json:"HandleFiles" gorm:"column:handlefiles;comment:处理附件;type:json"`
	RelatedStaffId        int64      `json:"RelatedStaffId" gorm:"column:relatedstaffid;comment:当事人ID;type:bigint"`
	RelatedStaffName      string     `json:"RelatedStaffName" gorm:"column:relatedstaffname;comment:当事人;type:varchar"`
	RelatedVehicleId      int64      `json:"RelatedVehicleId" gorm:"column:relatedvehicleid;comment:当事车辆ID;type:bigint"`
	RelatedVehicleLicense string     `json:"RelatedVehicleLicense" gorm:"column:relatedvehiclelicense;comment:当事车车牌;type:varchar"`
	model.OpUser
	model.Timestamp
}

func (p *PetitionWorkOrderHandleResult) BeforeCreate(db *gorm.DB) error {
	p.Id = model.Id()
	return nil
}
func (p *PetitionWorkOrderHandleResult) Create(tx *gorm.DB) error {
	return tx.Create(p).Error
}

func (p *PetitionWorkOrderHandleResult) GetByPetitionId(petitionId int64) []PetitionWorkOrderHandleResult {
	var results []PetitionWorkOrderHandleResult
	model.DB().Model(&PetitionWorkOrderHandleResult{}).Where("PetitionWorkOrderId = ?", petitionId).Order("CreatedAt ASC").Find(&results)
	return results
}
