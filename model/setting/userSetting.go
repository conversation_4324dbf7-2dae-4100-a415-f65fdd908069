package setting

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type UserSetting struct {
	model.PkId
	TopCorporationId int64      `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	UserId           int64      `json:"UserId" gorm:"column:userid;type:integer;comment:用户ID"`
	UserName         string     `json:"UserName" gorm:"column:username;type:varchar;comment:名字"`
	SettingItem      model.JSON `json:"SettingItem" gorm:"column:settingitem;type:json;comment:配置项"`
	model.Timestamp
}

type UserSettingItem struct {
	OperationReportDataIsApproval int64 `json:"OperationReportDataIsApproval"` //查看运营报表数据是否是审批的数据  1是 2否
}

func (us *UserSetting) BeforeCreate(db *gorm.DB) error {
	us.Id = model.Id()
	return nil
}

func (us *UserSetting) UpdateOrCreate() error {
	var count int64
	model.DB().Model(&UserSetting{}).Where("UserId = ?", us.UserId).Count(&count)

	if count > 0 {
		return model.DB().Model(&UserSetting{}).Where("UserId = ?", us.UserId).Update("SettingItem", us.SettingItem).Error
	}

	return model.DB().Create(&us).Error
}

func (us *UserSetting) GetByUser(userId int64) UserSetting {
	var setting UserSetting
	model.DB().Model(&UserSetting{}).Where("UserId = ?", userId).First(&setting)
	return setting
}
