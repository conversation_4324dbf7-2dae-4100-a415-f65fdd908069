package setting

import (
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/model/maintenance"
	operationModel "app/org/scs/erpv2/api/model/operation"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"math"
	"math/big"
	"strings"
	"time"
)

// ConfigType
const (
	ConfigType0                     int64 = iota // 0
	StandardCalDaysConfig                        // 标准计算天数 1
	StandardCalHourConfig                        // 标准计算小时 2
	SingleShiftConfig                            // 单班 3
	DoubleShiftConfig                            // 双班 4
	MixShiftConfig                               // 混合班 5
	BaseSalaryConfig                             // 基础工资 6
	CarLengthConfig                              // 车长补贴 7
	NightConfig                                  // 夜班补贴 8
	ScheduleUpConfig                             // 递增岗上工资 9
	ScheduleDownConfig                           // 递增岗下工资 10
	ScheduleDayConfig                            // 递增日工资 11
	WageUnder2h30mConfig                         // 班制外2.5小时内工资 12
	WageOver2h30mConfig                          // 班制外2.5小时外工资 13
	ExtendedHalfDayWageConfig                    // 班制外超过半天不足1天天资 14
	HolidayConfig                                // 法定节假日加班费 15
	LineLeaderConfig                             // 线组长奖金额 16
	AgeConfig                                    // 司龄津贴 17
	FullDayConfig                                // 全天班餐费补贴 18
	HalfDayConfig                                // 半天班餐费补贴 19
	HighTemConfig                                // 高温补贴 20
	NormalAnnualConfig                           // 普通场站年审 21
	SpecialAnnualConfig                          // 特殊场站年审 22
	OutAnnualConfig                              // 区外场站年审 23
	NewCarAnnualConfig                           // 新车上牌年审 24
	IllnessConfig                                // 病假补贴 25
	FuneralConfig                                // 丧家补贴 26
	RestConfig                                   // 疗休养 27
	PreJobTrainConfig                            // 岗前培训 28
	FollowCarConfig                              // 跟车 29
	OnDutyTrainingConfig                         // 待岗培训 30
	IntensiveTrainConfig                         // 在职强化培训 31
	CharterMissionConfig                         // 包车任务 32
	OfficialTrainConfig                          // 公务培训 33
	EnergySavingConfig                           // 节能降耗 34
	HighManeuverConfig                           // 大机动天数工资 35
	HighManeuverEatConfig                        // 大机动餐补工资 36
	SpecialSalaryConfig                          // 村村通 基础工资特殊情况 37
	PostConfig                                   // 村村通 岗位工资 38
	OffDutySalaryConfig                          // 村村通 岗下工资 39
	PublicHolidayConfig                          // 公休日 40
	UnplannedHalfDayConfig                       // 小蓝巴 计划外半天工资 41
	UnplannedAllDayConfig                        // 小蓝巴 计划外全天工资 42
	PassengerConfig                              // 小蓝巴 人才激励奖 43
	OverWorkConfig                               // 定制公交 加班补贴 44
	ExtraWorkConfig                              // 定制公交 超产奖 45
	ExecutionAwardConfig                         // 定制公交 执行奖 46
	VehicleSubsidyConfig                         //定制公交 出车补贴 47
	LinePriceConfig                              //村村通 线路补贴 48
	WashCarPriceConfig                           // 村村通 洗车补贴 49
	NightWorkConfig                              // 定制公交 夜班班补贴 50
	MealConfig                                   // 定制公交 村村通 餐费补贴51
	BaseSalaryStandardCalDaysConfig              // 小蓝巴  基础工资基本计算天数 52
)

type StaffPayrollConfig struct {
	model.PkId
	model.Timestamp
	PayRollType    string `json:"PayRollType" gorm:"column:payrolltype;type:varchar;comment:工资所属  稳定排班-定制公交-村村通-小蓝巴-通用"`
	ConfigType     int64  `json:"ConfigType" gorm:"column:configtype;type:integer;comment:配置类别"`
	WageBase       int64  `json:"WageBase" gorm:"column:wagebase;type:bigint;comment:工资基数 分"`
	BaseDay        int64  `json:"BaseDay" gorm:"column:baseday;type:bigint;comment:基础天数 天  configType为1时*100 其他*10"`
	BaseTimeLength int64  `json:"BaseTimeLength" gorm:"column:basetimelength;type:bigint;comment:基础时长 秒"`
	// 基础工资
	DrvType int64 `json:"DrvType" gorm:"column:drvtype;type:integer;comment:司机类型 1大客 2小客 3小蓝中巴 4小蓝大客 5定制公交 6村村通"`
	// 车长补贴
	MinCarLength int64 `json:"MinCarLength" gorm:"column:mincarlength;type:integer;comment:最小车长 毫米"`
	MaxCarLength int64 `json:"MaxCarLength" gorm:"column:maxcarlength;type:integer;comment:最大车长 毫米"`
	// 夜班补贴
	NightStartTime    int64 `json:"NightStartTime" gorm:"column:nightstarttime;type:integer;comment:夜班补贴起算时间 秒"`
	NightOverTime     int64 `json:"NightOverTime" gorm:"column:nightovertime;type:integer;comment:超出之后开始补贴时间 秒"`
	NightOverTimeBase int64 `json:"NightOverTimeBase" gorm:"column:nightovertimebase;type:integer;comment:超出之后开始补贴基数 分"`
	NightMax          int64 `json:"NightMax" gorm:"column:nightmax;type:integer;comment:夜班最高补贴 分"`
	//递增 岗上 岗下
	ScheduleConfig1 float64 `json:"ScheduleConfig1" gorm:"column:scheduleconfig1;type:numeric;comment:配置1 12"`
	ScheduleConfig2 float64 `json:"ScheduleConfig2" gorm:"column:scheduleconfig2;type:numeric;comment:配置2 1.5"`
	ScheduleConfig3 float64 `json:"ScheduleConfig3" gorm:"column:scheduleconfig3;type:numeric;comment:配置3 1or0.6"`
	// 递增日工资
	MinScheduleCarLength int64 `json:"MinScheduleCarLength" gorm:"column:minschedulecarlength;type:integer;comment:递增日车长配置"`
	MaxScheduleCarLength int64 `json:"MaxScheduleCarLength" gorm:"column:maxschedulecarlength;type:integer;comment:递增日车长配置"`
	//节能降耗
	MaxEnergySavingReward int64 `json:"MaxEnergySavingReward" gorm:"column:maxenergysavingreward;type:integer;comment:节能降耗最大金额"`
	// 高温补贴月份
	HighTemperatureMonths string `json:"HighTemperatureMonths" gorm:"column:hightemperaturemonths;type:varchar;comment:高温补贴月份"`
	//年审
	AnnualReviewPark   string `json:"AnnualReviewPark" gorm:"column:annualreviewpark;type:varchar;comment:1普通场站2特殊场站3区外"`
	AnnualLimitTime    int64  `json:"AnnualLimitTime" gorm:"column:annuallimittime;type:integer;comment:小于等于的时间"`
	AnnualOverTime     int64  `json:"AnnualOverTime" gorm:"column:annualovertime;type:integer;comment:超出之后开始补贴时间 秒"`
	AnnualOverTimeBase int64  `json:"AnnualOverTimeBase" gorm:"column:annualovertimebase;type:integer;comment:超出之后开始补贴基数 分"`
	AnnualCarNumBase   int64  `json:"AnnualCarNumBase" gorm:"column:annualcarnumbase;type:integer;comment:每多一辆车再增加多少的补贴"`
	AnnualNewCar       int64  `json:"AnnualNewCar" gorm:"column:annualnewcar;type:integer;comment:新车上牌补贴"`
	//病假
	SickConfig1 float64 `json:"SickConfig1" gorm:"column:sickconfig1;type:numeric;comment:配置1 2260 台州最低工资"`
	SickConfig2 float64 `json:"SickConfig2" gorm:"column:sickconfig2;type:numeric;comment:配置2 0.8"`
	SickConfig3 float64 `json:"SickConfig3" gorm:"column:sickconfig3;type:numeric;comment:配置3 21.75"`
	MaxSickDay  int64   `json:"MaxSickDay" gorm:"column:maxsickday;type:integer;comment:最长病假数"`
	// 丧假
	MaxFuneralDay int64 `json:"MaxFuneralDay" gorm:"column:maxfuneralday;type:integer;comment:最长丧假数"`
	// 包车时间
	MinCharterTime int64 `json:"MinCharterTime" gorm:"column:minchartertime;type:integer;comment:包车时间 秒"`
	MaxCharterTime int64 `json:"MaxCharterTime" gorm:"column:maxchartertime;type:integer;comment:包车时间 秒"`
	// 公务员培训时间
	TrainingTime int64 `json:"TrainingTime" gorm:"column:trainingtime;type:integer;comment:培训时间 秒"`
	// 村村通 基础工资特殊情况
	StaffIds string `json:"StaffIds" gorm:"column:staffids;type:varchar;default:;comment:ids"`
	// 定制公交 执行奖励
	LineAttribute int64 `json:"LineAttribute" gorm:"column:lineattribute;type:integer;comment:线路属性  1-常规公交；2-定制公交；3-公务车；4-校车；5-旅游专线; 6-村村通"`
	// 定制公交 出车补贴
	StartMileage      int64   `json:"StartMileage" gorm:"column:startmileage;type:integer;comment:开始补贴公里数 米"`
	SplitMileage      int64   `json:"SplitMileage" gorm:"column:splitmileage;type:integer;comment:分界点公里数 米"`
	MileageBaseWage   int64   `json:"MileageBaseWage" gorm:"column:mileagebasewage;type:integer;comment:基本补贴 分"`
	MileageBaseFactor float64 `json:"MileageBaseFactor" gorm:"column:mileagebasefactor;type:integer;comment:公里补贴系数"`
	// 定制公交 加班补贴 夜班补贴字段
	OverWorkAlias int64 `json:"OverWorkAlias" gorm:"column:overworkalias;type:smallint;comment:1加班5-6 2加班6-18 3加班18-23 4加班23-5 5夜班18-24 6早班24-5 "`
	// 定制公交 超产奖
	VehicleType int64 `json:"VehicleType" gorm:"column:vehicletype;type:smallint;comment:1大巴 2中巴"`
	// 定制公交 餐费补贴类型
	ShiftType int64 `json:"ShiftType" gorm:"column:shifttype;type:smallint;default:;comment:1通勤版 2公交班 3临时定制线路"`
}

func (m *StaffPayrollConfig) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *StaffPayrollConfig) Create() error {
	return model.DB().Create(&m).Error
}

func (m *StaffPayrollConfig) Update() error {
	return model.DB().Select("*").Updates(&m).Error
}

func (m *StaffPayrollConfig) Delete(Id int64) error {
	return model.DB().Delete(&StaffPayrollConfig{}, Id).Error
}

func (m *StaffPayrollConfig) List(ConfigTypes []int64, PayRollType string, Paginator model.Paginator) (Data []StaffPayrollConfig, TotalCount int64, err error) {
	db := model.DB().Model(&StaffPayrollConfig{})
	if ConfigTypes != nil && len(ConfigTypes) != 0 {
		db = db.Where("ConfigType in ?", ConfigTypes)
	}
	if PayRollType != "" {
		db = db.Where("PayRollType = ?", PayRollType)
	}
	db.Count(&TotalCount)
	if Paginator.Limit > 0 {
		db = db.Offset(Paginator.Offset).Limit(Paginator.Limit)
	}
	err = db.Find(&Data).Error
	return
}

var StaffPayrollConfigs []StaffPayrollConfig

func (m *StaffPayrollConfig) GetAllStaffPayrollConfigs(PayRollType string) {
	model.DB().Where("PayRollType = ?", PayRollType).Find(&StaffPayrollConfigs)
}

func (m *StaffPayrollConfig) ClearStaffPayrollConfigCache() {
	StaffPayrollConfigs = nil
}

// CommonWageBase 公共方法获取某个类型的WageBase
func (m *StaffPayrollConfig) CommonWageBase(configType int64) (Value int64) {
	baseConfigs := GetStaffPayrollConfig(configType)
	if len(baseConfigs) > 0 {
		Value = baseConfigs[0].WageBase
	}
	return
}

// CommonBaseDay 公共方法获取某个类型的baseDay
func (m *StaffPayrollConfig) CommonBaseDay(configType int64) (Value int64) {
	baseConfigs := GetStaffPayrollConfig(configType)
	if len(baseConfigs) > 0 {
		Value = baseConfigs[0].BaseDay
	}
	return
}

func GetStaffPayrollConfig(ConfigTypes ...int64) (Data []StaffPayrollConfig) {
	if StaffPayrollConfigs == nil || len(StaffPayrollConfigs) == 0 {
		return
	}
	for _, config := range StaffPayrollConfigs {
		if len(ConfigTypes) > 0 {
			for _, ConfigType := range ConfigTypes {
				if config.ConfigType == ConfigType {
					Data = append(Data, config)
				}
			}
		}
	}
	return
}

// GetBaseSalary 获取基础工资配置
func (m *StaffPayrollConfig) GetBaseSalary(DrvType int64) (Value int64) {
	configs := GetStaffPayrollConfig(BaseSalaryConfig)
	for _, config := range configs {
		if config.DrvType == DrvType {
			Value = config.WageBase
			break
		}
	}
	return
}

// GetVehicleLengthSalary 车长补贴配置
func (m *StaffPayrollConfig) GetVehicleLengthSalary(VehicleLength int64) (Value int64) {
	configs := GetStaffPayrollConfig(CarLengthConfig)
	for _, config := range configs {
		if VehicleLength <= config.MaxCarLength && VehicleLength > config.MinCarLength {
			Value = config.WageBase
			break
		}
	}
	return
}

// GetNightSalary 夜班补贴
func (m *StaffPayrollConfig) GetNightSalary(NightAddWorkTime int64) (Value int64) {
	configs := GetStaffPayrollConfig(NightConfig)
	if len(configs) > 0 {
		if NightAddWorkTime > configs[0].NightStartTime {
			Value += configs[0].WageBase
		}
		fmt.Println("第一阶段：", Value)
		NightAddWorkTime -= configs[0].NightStartTime
		for NightAddWorkTime > configs[0].NightOverTime {
			Value += configs[0].NightOverTimeBase
			fmt.Println("第二阶段：", Value)
			NightAddWorkTime -= configs[0].NightOverTime
		}
		if Value >= configs[0].NightMax {
			Value = configs[0].NightMax
		}
		fmt.Println(Value)
	}
	return
}

// GetInScheduleSalary 计算班制内递增岗上工资
func (m *StaffPayrollConfig) GetInScheduleSalary(InFrequencyTypeCircleWorkTimeLength, InFrequencyTypeCircleNotWorkTimeLength, baseTimeLength int64) (Value1, Value2 int64) {
	if InFrequencyTypeCircleWorkTimeLength == 0 {
		return 0, 0
	}
	configs := GetStaffPayrollConfig(ScheduleUpConfig, ScheduleDownConfig)
	var workConfig, notWorkConfig StaffPayrollConfig
	for _, config := range configs {
		if config.ConfigType == ScheduleUpConfig {
			workConfig = config
		}
		if config.ConfigType == ScheduleDownConfig {
			notWorkConfig = config
		}
	}
	//fmt.Println("InFrequencyTypeCircleWorkTimeLength:", InFrequencyTypeCircleWorkTimeLength)
	//fmt.Println("InFrequencyTypeCircleNotWorkTimeLength:", InFrequencyTypeCircleNotWorkTimeLength)
	//fmt.Println("baseTimeLength:", baseTimeLength)
	//fmt.Println("workConfig.ScheduleConfig1:", workConfig.ScheduleConfig1)
	//fmt.Println("InFrequencyTypeCircleWorkTimeLength:",InFrequencyTypeCircleWorkTimeLength)
	// （班制内岗上+岗下时间-174）/（岗上+岗下）*岗上*12*1.5
	Value1 = calculateInScheduleSalary(
		InFrequencyTypeCircleWorkTimeLength,
		InFrequencyTypeCircleNotWorkTimeLength,
		baseTimeLength,
		workConfig.WageBase,
	)
	if Value1 < 0 {
		Value1 = 0
	}

	Value2 = calculateInScheduleSalary2(
		InFrequencyTypeCircleWorkTimeLength,
		InFrequencyTypeCircleNotWorkTimeLength,
		baseTimeLength,
		notWorkConfig.WageBase,
	)
	if Value2 < 0 {
		Value2 = 0
	}
	return
}

func calculateInScheduleSalary(onDutySec, offDutySec, baseTimeLength, config1 int64) int64 {
	// 定义常量
	base := big.NewInt(baseTimeLength) // 174小时转换为秒
	wageBase := big.NewInt(config1)
	// 全部计算在秒单位进行，最后再转换为小时
	on := big.NewInt(onDutySec)
	off := big.NewInt(offDutySec)
	// 1. 计算总时间(秒)
	totalSec := new(big.Int).Add(on, off)

	// 2. 计算分子: (总秒数-174小时对应的秒数)
	numerator := new(big.Int).Sub(totalSec, base)

	// 3. 计算比率: numerator/totalSec
	ratio := new(big.Rat).SetFrac(numerator, totalSec)

	// 4. 乘以岗上时间(秒)
	step1 := new(big.Rat).Mul(ratio, new(big.Rat).SetInt(on))

	// 5. 转换为小时: 除以3600
	step2 := new(big.Rat).Quo(step1, new(big.Rat).SetInt64(3600))

	// 6. 乘以参数1
	result := new(big.Rat).Mul(step2, new(big.Rat).SetInt(wageBase))
	//// 8. 乘以100转换为分
	//result := new(big.Rat).Mul(step5, new(big.Rat).SetInt(hundred))
	// 四舍五入
	return roundToInt(result)
}
func calculateInScheduleSalary2(onDutySec, offDutySec, baseTimeLength, config1 int64) int64 {
	// 定义常量
	base := big.NewInt(baseTimeLength) // 174小时转换为秒
	wageBase := big.NewInt(config1)
	// 全部计算在秒单位进行，最后再转换为小时
	on := big.NewInt(onDutySec)
	off := big.NewInt(offDutySec)
	// 1. 计算总时间(秒)
	totalSec := new(big.Int).Add(on, off)

	// 2. 计算分子: (总秒数-174小时对应的秒数)
	numerator := new(big.Int).Sub(totalSec, base)

	// 3. 计算比率: numerator/totalSec
	ratio := new(big.Rat).SetFrac(numerator, totalSec)

	// 4. 乘以岗下时间(秒)
	step1 := new(big.Rat).Mul(ratio, new(big.Rat).SetInt(off))

	// 5. 转换为小时: 除以3600
	step2 := new(big.Rat).Quo(step1, new(big.Rat).SetInt64(3600))

	// 6. 乘以参数1
	result := new(big.Rat).Mul(step2, new(big.Rat).SetInt(wageBase))
	// 四舍五入
	return roundToInt(result)
}

func roundToInt(r *big.Rat) int64 {
	floatVal, _ := r.Float64()
	if floatVal >= 0 {
		return int64(floatVal + 0.5)
	}
	return int64(floatVal - 0.5)
}

// GetDayPayInSchedule 计算班制内递增日工资
func (m *StaffPayrollConfig) GetDayPayInSchedule(innerWorkDay, VehicleLength, baseWorkDay int64) int64 {
	var base int64
	configs := GetStaffPayrollConfig(ScheduleDayConfig)
	for _, config := range configs {
		if VehicleLength <= config.MaxScheduleCarLength && VehicleLength > config.MinScheduleCarLength {
			base = config.WageBase
			break
		}
	}
	if innerWorkDay < baseWorkDay {
		return 0
	}

	return (innerWorkDay - baseWorkDay) * base / 100
}

// GetOutScheduleSalary  班制外-递增岗上工资、岗下工资
func (m *StaffPayrollConfig) GetOutScheduleSalary(OutFrequencyTypeCircleWorkTimeLength, OutFrequencyTypeCircleNotWorkTimeLength int64) (Value1, Value2 int64) {
	configs := GetStaffPayrollConfig(ScheduleUpConfig, ScheduleDownConfig)
	var workConfig, notWorkConfig StaffPayrollConfig
	for _, config := range configs {
		if config.ConfigType == ScheduleUpConfig {
			workConfig = config
		}
		if config.ConfigType == ScheduleDownConfig {
			notWorkConfig = config
		}
	}

	// 岗上*12*1.5
	gs := big.NewInt(OutFrequencyTypeCircleWorkTimeLength) // 岗上
	hundred := big.NewInt(100)
	step1 := new(big.Rat).Mul(new(big.Rat).SetInt(gs), new(big.Rat).SetFloat64(workConfig.ScheduleConfig1))
	//step2 := new(big.Rat).Mul(step1, new(big.Rat).SetFloat64(workConfig.ScheduleConfig2))
	//step3 := new(big.Rat).Mul(step2, new(big.Rat).SetFloat64(workConfig.ScheduleConfig3))
	step4 := new(big.Rat).Quo(step1, new(big.Rat).SetInt64(3600))
	result := new(big.Rat).Mul(step4, new(big.Rat).SetInt(hundred))
	Value1 = roundToInt(result)
	// 岗下*12*1.5*0.6
	gx := big.NewInt(OutFrequencyTypeCircleNotWorkTimeLength) // 岗下
	step1 = new(big.Rat).Mul(new(big.Rat).SetInt(gx), new(big.Rat).SetFloat64(notWorkConfig.ScheduleConfig1))
	step4 = new(big.Rat).Quo(step1, new(big.Rat).SetInt64(3600))
	result = new(big.Rat).Mul(step4, new(big.Rat).SetInt(hundred))
	Value2 = roundToInt(result)
	return
}

// GetDayPayOutSchedule 计算班制外递增日工资
func (m *StaffPayrollConfig) GetDayPayOutSchedule(baseWorkDay, VehicleLength int64) int64 {
	var base int64
	configs := GetStaffPayrollConfig(ScheduleDayConfig)
	for _, config := range configs {
		if VehicleLength <= config.MaxScheduleCarLength && VehicleLength > config.MinScheduleCarLength {
			base = config.WageBase
			break
		}
	}
	return baseWorkDay * base
}

func (m *StaffPayrollConfig) GetWageUnder2h30m(count int64) (Value int64) {
	var base int64
	configs := GetStaffPayrollConfig(WageUnder2h30mConfig)
	if len(configs) > 0 {
		base = configs[0].WageBase
	}
	return count * base
}
func (m *StaffPayrollConfig) GetWageOver2h30m(count int64) (Value int64) {
	var base int64
	configs := GetStaffPayrollConfig(WageOver2h30mConfig)
	if len(configs) > 0 {
		base = configs[0].WageBase
	}
	return count * base
}
func (m *StaffPayrollConfig) GetExtendedHalfDayWage(count int64) (Value int64) {
	var base int64
	configs := GetStaffPayrollConfig(ExtendedHalfDayWageConfig)
	if len(configs) > 0 {
		base = configs[0].WageBase
	}
	return count * base
}
func (m *StaffPayrollConfig) GetHolidayOvertimePay(count int64) (Value int64) {
	var base int64
	configs := GetStaffPayrollConfig(HolidayConfig)
	if len(configs) > 0 {
		base = configs[0].WageBase
	}
	return count / 10 * base
}

func (m *StaffPayrollConfig) GetLineLeaderBase(tp int64) (Value int64) {
	if tp == 1 {
		var base int64
		configs := GetStaffPayrollConfig(LineLeaderConfig)
		if len(configs) > 0 {
			base = configs[0].WageBase
		}
		return base
	} else {
		return 0
	}
}

func (m *StaffPayrollConfig) GetEnergySavingReward(staffId int64, month string) (Value int64) {
	var maxReward int64
	configs := GetStaffPayrollConfig(EnergySavingConfig)
	if len(configs) > 0 {
		maxReward = configs[0].MaxEnergySavingReward
	}
	list, _ := (&maintenance.FuelSavingAwardReport{}).FindByStaffId(staffId, month)
	var total int64
	if list != nil {
		for _, v := range list {
			total += v.Reward
		}
	}
	if total > maxReward {
		total = maxReward
	}
	return total
}

func (m *StaffPayrollConfig) GetAgeBase() (Value int64) {
	var base int64
	configs := GetStaffPayrollConfig(AgeConfig)
	if len(configs) > 0 {
		base = configs[0].WageBase
	}
	return base
}

func (m *StaffPayrollConfig) GetRecuperationLeaveBase() (Value int64) {
	var base int64
	configs := GetStaffPayrollConfig(RestConfig)
	if len(configs) > 0 {
		base = configs[0].WageBase
	}
	return base
}

func (m *StaffPayrollConfig) GetCateringAllowance(FullWorkDay, HalfWorkDay, HighManeuverDay int64) (Value int64) {
	configs := GetStaffPayrollConfig(FullDayConfig, HalfDayConfig, HighManeuverEatConfig)
	var fullConfig, halfConfig, highManeuverEat StaffPayrollConfig
	for _, config := range configs {
		if config.ConfigType == FullDayConfig {
			fullConfig = config
		}
		if config.ConfigType == HalfDayConfig {
			halfConfig = config
		}
		if config.ConfigType == HighManeuverEatConfig {
			highManeuverEat = config
		}
	}
	return fullConfig.WageBase*FullWorkDay/10 + halfConfig.WageBase*HalfWorkDay/10 + highManeuverEat.WageBase*HighManeuverDay/10
}

var HighTemperatureMonth = []int{6, 7, 8, 9}

func (m *StaffPayrollConfig) GetHighTemperatureAllowanceBase(month string) (Value int64) {
	var config StaffPayrollConfig
	configs := GetStaffPayrollConfig(HighTemConfig)
	if len(configs) > 0 {
		config = configs[0]
	}
	monthData, _ := time.ParseInLocation("200601", month, time.Local)
	highTemperatureMonth := strings.Split(config.HighTemperatureMonths, ",")
	for _, m := range highTemperatureMonth {
		if m == monthData.Month().String() {
			Value = config.WageBase
			break
		}
	}
	return
}

// GetAnnualAuditAmount 获取年审补贴
func (m *StaffPayrollConfig) GetAnnualAuditAmount(OutFrequencyReports []operationModel.OutFrequencyAddWorkReport) (Value int64) {
	var (
		normal, special, out, newCar StaffPayrollConfig
	)
	configs := GetStaffPayrollConfig(NormalAnnualConfig, SpecialAnnualConfig, OutAnnualConfig, NewCarAnnualConfig)
	if configs != nil {
		for _, c := range configs {
			if c.ConfigType == NormalAnnualConfig {
				normal = c
			}
			if c.ConfigType == SpecialAnnualConfig {
				special = c
			}
			if c.ConfigType == OutAnnualConfig {
				out = c
			}
			if c.ConfigType == NewCarAnnualConfig {
				newCar = c
			}
		}
	}

	if OutFrequencyReports != nil {
		reportMap := make(map[string][]operationModel.OutFrequencyAddWorkReport)
		for _, report := range OutFrequencyReports {
			reportMap[report.ReportAt.String()] = append(reportMap[report.ReportAt.String()], report)
		}
		for _, reports := range reportMap {
			var (
				annualDay         int64          // 年审总天数
				annualTime        int64          // 年审总时长
				carMap            map[string]int // 车辆总数
				isNewCarUpLicense bool           // 是否是新车上牌
				AnnualReviewPark  map[int64]bool // 年审类型
			)
			AnnualReviewPark = make(map[int64]bool)
			carMap = make(map[string]int)
			for _, report := range reports {
				if report.AddWorkType == operationModel.AnnualType {
					annualDay += report.DayCount
					annualTime += report.TimeLength
					carMap[report.License] = 1
					annualReviewPark := report.AnnualReviewPark
					if report.AreaScene == "区外" {
						annualReviewPark = 3
					}
					AnnualReviewPark[annualReviewPark] = true
				}
				if report.AddWorkType == operationModel.NewCarType {
					isNewCarUpLicense = true
				}
			}

			if isNewCarUpLicense {
				Value += annualDay / 10 * newCar.WageBase
			} else {
				// 计算普通场站钱
				var normalWage int64
				if AnnualReviewPark[1] {
					normalAnnualTime := annualTime
					if normalAnnualTime <= normal.AnnualLimitTime {
						normalWage = normal.WageBase
					}
					normalAnnualTime -= normal.AnnualLimitTime
					for normalAnnualTime >= normal.AnnualOverTime {
						normalWage += normal.AnnualOverTimeBase
						normalAnnualTime -= normal.AnnualOverTime
					}
					if len(carMap) > 1 {
						normalWage += (int64(len(carMap)) - 1) * normal.AnnualCarNumBase
					}
				}

				// 计算特殊场站钱
				var specialWage int64
				if AnnualReviewPark[2] {
					specialAnnualTime := annualTime
					if specialAnnualTime <= special.AnnualLimitTime {
						specialWage = special.WageBase
					}
					specialAnnualTime -= special.AnnualLimitTime
					for specialAnnualTime >= special.AnnualOverTime {
						specialWage += special.AnnualOverTimeBase
						specialAnnualTime -= special.AnnualOverTime
					}
					if len(carMap) > 1 {
						specialWage += (int64(len(carMap)) - 1) * special.AnnualCarNumBase
					}
				}

				// 计算区外场站钱
				var outWage int64
				if AnnualReviewPark[2] {
					outAnnualTime := annualTime
					if outAnnualTime <= out.AnnualLimitTime {
						outWage = out.WageBase
					}
					outAnnualTime -= out.AnnualLimitTime
					for outAnnualTime >= out.AnnualOverTime {
						outWage += out.AnnualOverTimeBase
						outAnnualTime -= out.AnnualOverTime
					}
					if len(carMap) > 1 {
						outWage += (int64(len(carMap)) - 1) * out.AnnualCarNumBase
					}
				}
				// 取三个数的最大数
				Value += maxOfThree(normalWage, specialWage, outWage)
			}
		}
	}
	return
}

func maxOfThree(a, b, c int64) int64 {
	return int64(math.Max(float64(a), math.Max(float64(b), float64(c))))
}

func (m *StaffPayrollConfig) GetSpecialLeave(WorkDay, baseWorkDay, PlanWorkDayTotal int64, StartAt, EndAt time.Time, StaffId int64) (Value int64) {
	var (
		illnessDay int64
		funeralDay int64
	)
	illnessDay = (&hrModel.ApplyLeaveRecord{}).GetDayCountByTimeRange(StartAt, EndAt, StaffId, []int64{4}) // 病假天数
	funeralDay = (&hrModel.ApplyLeaveRecord{}).GetDayCountByTimeRange(StartAt, EndAt, StaffId, []int64{4}) // 丧

	var (
		illnessConfig StaffPayrollConfig
		funeralConfig StaffPayrollConfig
	)
	configs := GetStaffPayrollConfig(IllnessConfig, FuneralConfig)
	if len(configs) == 0 {
		return
	}
	for _, c := range configs {
		if c.ConfigType == IllnessConfig {
			illnessConfig = c
		}
		if c.ConfigType == FuneralConfig {
			funeralConfig = c
		}
	}
	// 计算丧假补贴
	var maxDay int64 = 300
	if PlanWorkDayTotal != 0 {
		if maxDay > (PlanWorkDayTotal - WorkDay) {
			maxDay = PlanWorkDayTotal - WorkDay
		}
	}
	if funeralDay > maxDay {
		funeralDay = maxDay
	}
	if funeralDay < 0 {
		funeralDay = 0
	}
	Value += funeralDay * funeralConfig.WageBase / 100

	// 计算病假补贴
	maxDay = 2200
	if maxDay > (baseWorkDay - WorkDay) {
		maxDay = baseWorkDay - WorkDay
	}
	if illnessDay > maxDay {
		illnessDay = maxDay
	}
	if illnessDay < 0 {
		illnessDay = 0
	}
	if illnessDay > 0 {
		day := big.NewRat(illnessDay, 100)
		step1 := new(big.Rat).Mul(new(big.Rat).SetFloat64(illnessConfig.SickConfig1), day)
		result := roundToInt(step1)
		Value += result
	}
	return
}

// GetTrainAllowance 获取培训补贴
func (m *StaffPayrollConfig) GetTrainAllowance(reportMap map[string][]operationModel.OutFrequencyAddWorkReport) (Value int64) {
	var (
		config1 StaffPayrollConfig // 岗前培训
		config2 StaffPayrollConfig // 跟车
		config3 StaffPayrollConfig // 待岗培训
		config4 StaffPayrollConfig // 在职强化培训
	)
	configs := GetStaffPayrollConfig(PreJobTrainConfig, FollowCarConfig, OnDutyTrainingConfig, IntensiveTrainConfig)
	if len(configs) == 0 {
		return
	}
	for _, c := range configs {
		if c.ConfigType == PreJobTrainConfig {
			config1 = c
		}
		if c.ConfigType == FollowCarConfig {
			config2 = c
		}
		if c.ConfigType == OnDutyTrainingConfig {
			config3 = c
		}
		if c.ConfigType == IntensiveTrainConfig {
			config4 = c
		}
	}
	var (
		gangqianpeixun_DayCount int64 // 岗前培训总天数
		genche_DayCount         int64 // 跟车培训总天数
		daigangpeixun_DayCount  int64 // 待岗培训总天数
		zaizhiqianghua_DayCount int64 // 在职强化培训总天数
	)
	for _, reports := range reportMap {
		for _, report := range reports {
			if report.AddWorkType == operationModel.PreJobTrainType {
				gangqianpeixun_DayCount += report.DayCount
			}
			if report.AddWorkType == operationModel.FollowCarType {
				genche_DayCount += report.DayCount
			}
			if report.AddWorkType == operationModel.OnDutyTrainType {
				daigangpeixun_DayCount += report.DayCount
			}
			if report.AddWorkType == operationModel.IntensiveTrainType {
				zaizhiqianghua_DayCount += report.DayCount
			}
		}
	}
	Value += config1.WageBase * gangqianpeixun_DayCount / 10 // 岗前培训
	Value += config2.WageBase * genche_DayCount / 10         // 跟车培训
	Value += config3.WageBase * daigangpeixun_DayCount / 10  // 待岗培训
	Value += config4.WageBase * zaizhiqianghua_DayCount / 10 // 在职强化培训
	return
}

// GetCharterMissionAllowance 获取包车补贴
func (m *StaffPayrollConfig) GetCharterMissionAllowance(OutFrequencyReports []operationModel.OutFrequencyAddWorkReport) (Value int64) {
	charterConfigs := GetStaffPayrollConfig(CharterMissionConfig)
	if len(charterConfigs) == 0 {
		return
	}
	for _, report := range OutFrequencyReports {
		if report.AddWorkType == operationModel.CharteredBusType {
			var base int64
			for _, config := range charterConfigs {
				if report.TimeLength <= config.MaxCharterTime && report.TimeLength > config.MinCharterTime {
					base = config.WageBase
				}
			}
			Value += base
		}
	}
	return
}

// GetOtherSalary 其他：公务员培训和大机动
func (m *StaffPayrollConfig) GetOtherSalary(reportMap map[string][]operationModel.OutFrequencyAddWorkReport) (Value int64) {
	var (
		config6 StaffPayrollConfig // 公务培训
		config7 StaffPayrollConfig // 大机动
	)
	configs := GetStaffPayrollConfig(PreJobTrainConfig, FollowCarConfig, OnDutyTrainingConfig, IntensiveTrainConfig, CharterMissionConfig, EnergySavingConfig, HighManeuverConfig)
	if len(configs) == 0 {
		return
	}
	var HOUR int64 = 3600
	for _, c := range configs {
		if c.ConfigType == OfficialTrainConfig {
			config6 = c
		}
		if c.ConfigType == HighManeuverConfig {
			config7 = c
		}
	}
	var (
		gongwupeixun_TimeLength int64 // 公务培训总时长
		dajidong_DayCount       int64 // 大机动天数
	)
	for _, reports := range reportMap {
		isRepair := false // 修车标识
		var dajidong_needAdd int64
		for _, report := range reports {
			if report.AddWorkType == operationModel.OfficialAttendanceType {
				gongwupeixun_TimeLength += report.TimeLength
			}
			if report.AddWorkType == operationModel.HighManeuverType {
				dajidong_needAdd += report.DayCount
			}
			if report.AddWorkType == operationModel.RepairCarType {
				isRepair = true
			}
		}
		if isRepair {
			dajidong_needAdd = 0
		}
		dajidong_DayCount += dajidong_needAdd
	}
	Value += config7.WageBase * dajidong_DayCount / 10 // 大机动
	// 公务员培训
	p1 := big.NewRat(gongwupeixun_TimeLength, HOUR)
	p2 := big.NewInt(config6.WageBase)
	step := new(big.Rat).Mul(p1, new(big.Rat).SetInt(p2))
	result := roundToInt(step)
	Value += result
	return
}

// GetVillageBaseSalary 获取村村基础工资
func (m *StaffPayrollConfig) GetVillageBaseSalary(staffId int64) (Value int64) {
	var baseSalary int64
	baseConfigs := GetStaffPayrollConfig(BaseSalaryConfig)
	if len(baseConfigs) > 0 {
		baseSalary = baseConfigs[0].WageBase
	}
	specialConfigs := GetStaffPayrollConfig(SpecialSalaryConfig)
	if specialConfigs != nil {
		staffIds := specialConfigs[0].StaffIds
		staffStrIdArr := strings.Split(staffIds, ",")
		staffIdArr := util.StringArrToInt64(staffStrIdArr)
		for _, id := range staffIdArr {
			if id == staffId {
				baseSalary = specialConfigs[0].WageBase
				break
			}
		}
	}
	return baseSalary
}

// GetOverWorkForCustomer 加班补贴 定制公交
func (m *StaffPayrollConfig) GetOverWorkForCustomer(overWorkOn5To6, overWorkOn6To18, overWorkOn18To23, overWorkOn23To5 int64) (Value int64) {
	baseConfigs := GetStaffPayrollConfig(OverWorkConfig)
	if len(baseConfigs) == 0 {
		return 0
	}
	for _, config := range baseConfigs {
		if config.OverWorkAlias == 1 {
			Value += overWorkOn5To6 * config.WageBase
		}
		if config.OverWorkAlias == 2 {
			Value += overWorkOn6To18 * config.WageBase
		}
		if config.OverWorkAlias == 3 {
			Value += overWorkOn18To23 * config.WageBase
		}
		if config.OverWorkAlias == 4 {
			Value += overWorkOn23To5 * config.WageBase
		}
	}
	return
}

// GetNightOverWorkForCustomer 夜班补贴 定制公交
func (m *StaffPayrollConfig) GetNightOverWorkForCustomer(overWorkOn18To24, overWorkOn24To5 int64) (Value int64) {
	baseConfigs := GetStaffPayrollConfig(NightWorkConfig)
	if len(baseConfigs) == 0 {
		return 0
	}
	for _, config := range baseConfigs {
		if config.OverWorkAlias == 5 {
			Value += overWorkOn18To24 * config.WageBase
		}
		if config.OverWorkAlias == 6 {
			Value += overWorkOn24To5 * config.WageBase
		}
	}
	return
}

// GetExtraWorkWageBaseForCustomer 获取超产奖 定制公交
func (m *StaffPayrollConfig) GetExtraWorkWageBaseForCustomer(drvType int64) (Value int64) {
	baseConfigs := GetStaffPayrollConfig(ExtraWorkConfig)
	if len(baseConfigs) == 0 {
		return 0
	}
	for _, config := range baseConfigs {
		if config.VehicleType == drvType {
			Value = config.WageBase
			break
		}
	}
	return
}

// GetCateringAllowanceForCustomer 获取餐费补贴 定制公交
func (m *StaffPayrollConfig) GetCateringAllowanceForCustomer(commutingShiftDay, busShiftDay, tempShiftDay int64) (Value int64) {
	baseConfigs := GetStaffPayrollConfig(MealConfig)
	if len(baseConfigs) == 0 {
		return 0
	}
	for _, config := range baseConfigs {
		if config.OverWorkAlias == 1 {
			Value += commutingShiftDay * config.WageBase
		}
		if config.OverWorkAlias == 2 {
			Value += busShiftDay * config.WageBase
		}
		if config.OverWorkAlias == 3 {
			Value += tempShiftDay * config.WageBase
		}
	}
	return
}

// GetExecutionAward 定制公交执行奖
func (m *StaffPayrollConfig) GetExecutionAward(lineTypes map[int64]bool) (Value int64) {
	baseConfigs := GetStaffPayrollConfig(ExecutionAwardConfig)
	if len(baseConfigs) == 0 {
		return 0
	}
	for _, config := range baseConfigs {
		if lineTypes[config.LineAttribute] {
			if config.WageBase > Value {
				Value = config.WageBase
			}
		}
	}
	return
}

// GetVehicleSubsidyAllowance 定制公交 出车补贴
func (m *StaffPayrollConfig) GetVehicleSubsidyAllowance(mileage int64) (Value int64) {
	baseConfigs := GetStaffPayrollConfig(VehicleSubsidyConfig)
	if len(baseConfigs) == 0 {
		return 0
	}
	config := baseConfigs[0]
	if mileage < config.StartMileage {
		return
	}
	if mileage >= config.StartMileage && mileage <= config.SplitMileage {
		return config.MileageBaseWage
	}
	overMileage := big.NewRat(mileage-config.SplitMileage, 1000)
	step := new(big.Rat).Mul(overMileage, new(big.Rat).SetFloat64(config.MileageBaseFactor))
	result := roundToInt(step)
	return result + config.MileageBaseWage
}
