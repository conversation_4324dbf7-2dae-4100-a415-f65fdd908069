package setting

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type ProcessFormStepSetting struct {
	model.PkId
	TemplateFormId string `json:"TemplateFormId" gorm:"column:templateformid;type:varchar;comment:表单ID"` //表单ID
	Value          int64  `json:"Value" gorm:"column:value;type:smallint;"`
	Desc           string `json:"Desc" gorm:"column:desc;type:varchar;comment:value描述"`
	model.Timestamp
}

func (s *ProcessFormStepSetting) BeforeCreate(db *gorm.DB) error {
	s.Id = model.Id()
	return nil
}
func (s *ProcessFormStepSetting) Create(settings []ProcessFormStepSetting) error {
	return model.DB().Create(&settings).Error
}

func (s *ProcessFormStepSetting) Delete(templateFormId string) error {
	return model.DB().Where("TemplateFormId = ?", templateFormId).Delete(&ProcessFormStepSetting{}).Error
}

func (s *ProcessFormStepSetting) GetBy(templateFormId string, value int64) []ProcessFormStepSetting {
	var settings []ProcessFormStepSetting
	tx := model.DB().Model(&ProcessFormStepSetting{})
	if templateFormId != "" {
		tx = tx.Where("TemplateFormId = ?", templateFormId)
	}
	if value > 0 {
		tx = tx.Where("Value = ?", value)
	}

	tx.Find(&settings)

	return settings
}
