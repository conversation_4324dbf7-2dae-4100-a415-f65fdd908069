package setting

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type LineAllowancePriceSetting struct {
	model.PkId
	model.Corporations
	LineId     int64           `json:"LineId" gorm:"column:lineid;type:bigint;comment:线路ID"`
	LineName   string          `json:"LineName" gorm:"column:linename;type:varchar;comment:线路"`
	Price      int64           `json:"Price" gorm:"column:price;type:integer;comment:补贴金单价 单位：分"`
	UseMonth   model.LocalTime `json:"UseMonth" gorm:"column:usemonth;type:timestamp;comment:生效月份"`
	UseStartAt model.LocalTime `json:"UseStartAt" gorm:"column:usestartat;type:timestamp;comment:生效开始日期"`
	model.OpUser
	model.Timestamp

	CorporationId   int64  `json:"CorporationId" gorm:"-"`
	CorporationName string `json:"CorporationName" gorm:"-"`
}

func (m *LineAllowancePriceSetting) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *LineAllowancePriceSetting) Create() error {
	return model.DB().Create(&m).Error
}

func (m *LineAllowancePriceSetting) FirstById(id int64) LineAllowancePriceSetting {
	var setting LineAllowancePriceSetting
	model.DB().Model(&LineAllowancePriceSetting{}).Where("Id = ?", id).First(&setting)

	return setting
}

func (m *LineAllowancePriceSetting) GetByLineId(corporationId, lineId int64) []LineAllowancePriceSetting {
	var settings []LineAllowancePriceSetting
	model.DB().Model(&LineAllowancePriceSetting{}).
		Scopes(model.WhereCorporation(corporationId)).Where("LineId = ?", lineId).Order("CreatedAt desc").Find(&settings)

	return settings
}
func (m *LineAllowancePriceSetting) IsExist(corporationId, lineId int64) bool {
	var count int64
	model.DB().Model(&LineAllowancePriceSetting{}).Scopes(model.WhereCorporation(corporationId)).Where("LineId = ?", lineId).Count(&count)

	return count > 0
}

func (m *LineAllowancePriceSetting) GetByLineIdAndMonth(corporationId, lineId int64, month time.Time) LineAllowancePriceSetting {
	var setting LineAllowancePriceSetting
	model.DB().Model(&LineAllowancePriceSetting{}).Where("LineId = ?", lineId).Scopes(model.WhereCorporation(corporationId)).Where("UseMonth = ?", month.Format(model.DateFormat)).Order("CreatedAt DESC").First(&setting)

	return setting
}

func (m *LineAllowancePriceSetting) GetBy(corporationId, lineId int64, paginator model.Paginator) ([]LineAllowancePriceSetting, int64) {
	var settings []LineAllowancePriceSetting
	subQuery := model.DB().Model(&LineAllowancePriceSetting{}).Select("*", "row_number() over (partition by LineId,FleetId,BranchId,CompanyId order by CreatedAt desc,Id desc)")

	tx := model.DB().Table("(?) AS tmp", subQuery).Where("row_number = ?", 1)

	if corporationId > 0 {
		tx.Scopes(model.WhereCorporation(corporationId))
	}

	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	var count int64

	tx.Count(&count)

	tx.Order("LineName asc").Order("LineId ASC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&settings)

	return settings, count
}
