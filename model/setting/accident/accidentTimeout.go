package accident

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type AccidentTimeoutSetting struct {
	model.PkId
	TopCorporationId int64 `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:根机构ID"`
	AccidentCate     int64 `json:"AccidentCate" gorm:"column:accidentcate;type:smallint;comment:事故类别 1-车内伤,2-车外伤,3-单方事故,4-倒车,5-刮擦,6-追尾,7-侧翻,8-其他"`
	WarningValue     int64 `json:"WarningValue" gorm:"column:warningvalue;type:smallint;default:0;comment:预警阈值 单位：天"`
	AlarmValue       int64 `json:"AlarmValue" gorm:"column:alarmvalue;type:smallint;default:0;comment:告警阈值 单位：天"`
	model.OpUser
	model.Timestamp

	Details []AccidentTimeoutSettingDetail `json:"Details" gorm:"-"`
}

func (ats *AccidentTimeoutSetting) BeforeCreate(db *gorm.DB) error {
	ats.Id = model.Id()
	return nil
}
func (ats *AccidentTimeoutSetting) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&ats).Error
}

func (ats *AccidentTimeoutSetting) TransactionUpdate(tx *gorm.DB) error {
	return tx.Omit("AccidentCate").Updates(&ats).Error
}

func (ats *AccidentTimeoutSetting) TransactionDelete(tx *gorm.DB) error {
	return tx.Delete(&ats).Error
}

func (ats *AccidentTimeoutSetting) FindBy(id int64) AccidentTimeoutSetting {
	var setting AccidentTimeoutSetting
	model.DB().Model(&AccidentTimeoutSetting{}).Where("Id = ?", id).First(&setting)
	return setting
}

func (ats *AccidentTimeoutSetting) GetAll(topCorporationId int64) []AccidentTimeoutSetting {
	var settings []AccidentTimeoutSetting
	model.DB().Model(&AccidentTimeoutSetting{}).Where("TopCorporationId = ?", topCorporationId).Find(&settings)
	return settings
}

func (ats *AccidentTimeoutSetting) ExistBy(topCorporationId, accidentCate int64) bool {
	var count int64
	model.DB().Model(&AccidentTimeoutSetting{}).Where("TopCorporationId = ? AND AccidentCate = ?", topCorporationId, accidentCate).Count(&count)

	return count > 0
}

type AccidentTimeoutSettingDetail struct {
	model.PkId
	AccidentTimeoutSettingId       int64 `json:"AccidentTimeoutSettingId" gorm:"column:accidenttimeoutsettingid;type:bigint;default:0;comment:预警ID 关联AccidentTimeoutSetting表主键ID"`
	MinMoney                       int64 `json:"MinMoney" gorm:"column:minmoney;type:integer;comment:最小金额 单位：分"`
	MaxMoney                       int64 `json:"MaxMoney" gorm:"column:maxmoney;type:integer;comment:最大金额 单位：分"`
	WarningValue                   int64 `json:"WarningValue" gorm:"column:warningvalue;type:smallint;default:0;comment:预警阈值 单位：天"`
	AlarmValue                     int64 `json:"AlarmValue" gorm:"column:alarmvalue;type:smallint;default:0;comment:告警阈值 单位：天"`
	AccidentAlarmCategorySettingId int64 `json:"AccidentAlarmCategorySettingId" gorm:"column:accidentalarmcategorysettingid;type:bigint;default:0;comment:报警种类ID 关联AccidentAlarmCategorySetting表主键ID"`
	model.Timestamp

	ActionType string `json:"ActionType" gorm:"-"`
}

func (atd *AccidentTimeoutSettingDetail) BeforeCreate(db *gorm.DB) error {
	atd.Id = model.Id()
	return nil
}

func (atd *AccidentTimeoutSettingDetail) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&atd).Error
}

func (atd *AccidentTimeoutSettingDetail) TransactionUpdate(tx *gorm.DB) error {
	return tx.Select("MinMoney", "MaxMoney", "WarningValue", "AlarmValue", "AccidentAlarmCategorySettingId").Updates(&atd).Error
}

func (atd *AccidentTimeoutSettingDetail) TransactionDelete(tx *gorm.DB) error {
	return tx.Delete(&atd).Error
}

func (atd *AccidentTimeoutSettingDetail) TransactionDeleteBy(accidentTimeoutSettingId int64, tx *gorm.DB) error {
	return tx.Where("AccidentTimeoutSettingId = ?", accidentTimeoutSettingId).Delete(&AccidentTimeoutSettingDetail{}).Error
}

func (atd *AccidentTimeoutSettingDetail) FindBy(id int64) AccidentTimeoutSettingDetail {
	var detail AccidentTimeoutSettingDetail
	model.DB().Model(&AccidentTimeoutSettingDetail{}).Where("Id = ?", id).First(&detail)
	return detail
}

func (atd *AccidentTimeoutSettingDetail) First() AccidentTimeoutSettingDetail {
	var detail AccidentTimeoutSettingDetail
	model.DB().Model(&AccidentTimeoutSettingDetail{}).First(&detail)
	return detail
}

func (atd *AccidentTimeoutSettingDetail) GetBy(accidentTimeoutSettingId int64) []AccidentTimeoutSettingDetail {
	var details []AccidentTimeoutSettingDetail
	model.DB().Model(&AccidentTimeoutSettingDetail{}).Where("AccidentTimeoutSettingId = ?", accidentTimeoutSettingId).Find(&details)
	return details
}

func (atd *AccidentTimeoutSettingDetail) FindByDetail(accidentTimeoutSettingId, money int64) AccidentTimeoutSettingDetail {
	var detail AccidentTimeoutSettingDetail
	model.DB().Model(&AccidentTimeoutSettingDetail{}).Where("AccidentTimeoutSettingId = ?", accidentTimeoutSettingId).Where("MineMoney <= ? AND MaxMoney >= ?", money, money).First(&detail)
	return detail
}

func (atd *AccidentTimeoutSettingDetail) IsExistAlarmCategory(categoryId int64) bool {
	var count int64
	model.DB().Model(&AccidentTimeoutSettingDetail{}).Where("AccidentAlarmCategorySettingId = ?", categoryId).Count(&count)
	return count > 0
}
