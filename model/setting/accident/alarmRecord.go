package accident

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
	"time"
)

type AccidentTimeoutAlarmRecord struct {
	model.PkId
	TopCorporationId               int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:根机构ID"`
	TrafficAccidentId              int64           `json:"TrafficAccidentId" gorm:"column:trafficaccidentid;type:bigint;comment:事故ID"`
	TrafficAccidentCode            string          `json:"TrafficAccidentCode" gorm:"column:trafficaccidentcode;type:varchar;comment:事故编号"`
	AccidentCate                   int64           `json:"AccidentCate" gorm:"column:accidentcate;type:smallint;comment:事故类别 1-车内伤,2-车外伤,3-单方事故,4-倒车,5-刮擦,6-追尾,7-侧翻,8-其他"`
	DriverId                       int64           `json:"DriverId" gorm:"column:driverid;type:integer;comment:事故司机ID"`
	DriverName                     string          `json:"DriverName" gorm:"column:drivername;type:varchar;comment:事故司机姓名"`
	VehicleId                      int64           `json:"VehicleId" gorm:"column:vehicleid;type:integer;comment:事故车辆ID"`
	License                        string          `json:"License" gorm:"column:license;type:varchar;comment:事故车牌"`
	AccidentCreatedAt              model.LocalTime `json:"AccidentCreatedAt" gorm:"column:accidentcreatedat;type:timestamp;comment:事故创建时间"`
	AccidentOpUserId               int64           `json:"AccidentOpUserId" gorm:"column:accidentopuserid;type:bigint;comment:事故创建人ID"`
	AccidentOpUserName             string          `json:"AccidentOpUserName" gorm:"column:accidentopusername;type:varchar;comment:事故创建人"`
	AccidentTotalMoney             int64           `json:"AccidentTotalMoney" gorm:"column:accidenttotalmoney;type:bigint;comment:事故总金额"`
	WarningValue                   int64           `json:"WarningValue" gorm:"column:warningvalue;type:smallint;default:0;comment:预警阈值 单位：天"`
	AlarmValue                     int64           `json:"AlarmValue" gorm:"column:alarmvalue;type:smallint;default:0;comment:告警阈值 单位：天"`
	CurrentValue                   int64           `json:"CurrentValue" gorm:"column:currentvalue;type:smallint;default:0;comment:当前的值 指警告产生时实际的值 单位：天"`
	AccidentAlarmCategorySettingId int64           `json:"AccidentAlarmCategorySettingId" gorm:"column:accidentalarmcategorysettingid;type:bigint;default:0;comment:报警种类ID 关联AccidentAlarmCategorySetting表主键ID"`
	AccidentAlarmCategoryTitle     string          `json:"AccidentAlarmCategoryTitle" gorm:"column:accidentalarmcategorytitle;type:varchar;default:;comment:报警种类名称"`
	AlarmType                      int64           `json:"AlarmType" gorm:"column:alarmtype;type:smallint;default:0;comment:类型  1预警  2告警"`
	FinishStatus                   int64           `json:"FinishStatus" gorm:"column:finishstatus;type:smallint;comment:完成状态 1进行中 2历史;"`
	ProcessingTime                 int64           `json:"ProcessingTime" gorm:"column:processingtime;type:integer;comment:事故发起到结案的处理时长，单位：分钟"`
	IsPushWarningNotify            int64           `json:"IsPushWarningNotify" gorm:"column:ispushwarningnotify;type:smallint;comment:是否推送事故预警告警消息 1是 2否 2表示忽略;default:1"`
	model.Timestamp

	CorporationId   int64  `json:"CorporationId" gorm:"-"`
	CorporationName string `json:"CorporationName" gorm:"-"`
}

func (atr *AccidentTimeoutAlarmRecord) BeforeCreate(db *gorm.DB) error {
	atr.Id = model.Id()
	return nil
}
func (atr *AccidentTimeoutAlarmRecord) Create() error {
	return model.DB().Create(&atr).Error
}

func (atr *AccidentTimeoutAlarmRecord) Update() error {
	return model.DB().Omit("TopCorporationId", "TrafficAccidentId", "TrafficAccidentCode", "AccidentCreatedAt", "AccidentOpUserId",
		"AccidentOpUserName", "FinishStatus", "ProcessingTime").Updates(&atr).Error

}

func (atr *AccidentTimeoutAlarmRecord) UpdateFinishStatus() error {
	return model.DB().Select("FinishStatus", "ProcessingTime").Updates(&atr).Error
}

func (atr *AccidentTimeoutAlarmRecord) FindByAccidentId(accidentId int64) AccidentTimeoutAlarmRecord {
	var record AccidentTimeoutAlarmRecord
	model.DB().Model(&AccidentTimeoutAlarmRecord{}).Where("TrafficAccidentId = ?", accidentId).First(&record)

	return record
}

func (atr *AccidentTimeoutAlarmRecord) CountByAccidentIds(accidentId []int64, alarmType int64) int64 {
	var count int64
	model.DB().Model(&AccidentTimeoutAlarmRecord{}).Where("TrafficAccidentId IN ? AND AlarmType = ? AND FinishStatus = ?", accidentId, alarmType, util.AlarmFinishStatusForDoing).Count(&count)

	return count
}

func (atr *AccidentTimeoutAlarmRecord) GetByAccidentIds(accidentId []int64, alarmType int64, paginator model.Paginator) ([]AccidentTimeoutAlarmRecord, int64) {
	var items []AccidentTimeoutAlarmRecord
	tx := model.DB().Model(&AccidentTimeoutAlarmRecord{}).Where("TrafficAccidentId IN ? AND AlarmType = ? AND FinishStatus = ?", accidentId, alarmType, util.AlarmFinishStatusForDoing)

	var count int64
	tx.Count(&count)

	tx.Order("CurrentValue DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&items)
	return items, count
}

func (atr *AccidentTimeoutAlarmRecord) GetBy(topCorporationId, corporationId int64, cateId, alarmType, startDay, endDay, startMoney, endMoney, vehicleId, driverId, alarmCategoryId,
	finishStatus int64, accidentCode string, createStart, createEnd time.Time, paginator model.Paginator) ([]AccidentTimeoutAlarmRecord, int64, int64) {
	var records []AccidentTimeoutAlarmRecord
	tx := model.DB().Model(&AccidentTimeoutAlarmRecord{}).Where("TopCorporationId = ?", topCorporationId)

	if corporationId > 0 {
		tx = tx.Where("TrafficAccidentId IN (?)", model.DB().Model(&safety.TrafficAccident{}).Select("Id").Scopes(model.WhereCorporation(corporationId)))
	}

	if cateId > 0 {
		tx = tx.Where("AccidentCate = ?", cateId)
	}

	if alarmType > 0 {
		tx = tx.Where("AlarmType = ?", alarmType)
	}

	if startDay > 0 {
		tx = tx.Where("CurrentValue >= ?", startDay)
	}

	if endDay > 0 {
		tx = tx.Where("CurrentValue <= ?", endDay)
	}

	if alarmCategoryId > 0 {
		tx = tx.Where("AccidentAlarmCategorySettingId = ?", alarmCategoryId)
	}

	if finishStatus > 0 {
		tx = tx.Where("FinishStatus = ?", finishStatus)
	}

	if accidentCode != "" {
		tx = tx.Where("TrafficAccidentCode LIKE ?", "%"+accidentCode+"%")
	}

	if !createStart.IsZero() {
		tx = tx.Where("AccidentCreatedAt >= ?", createStart.Format(model.TimeFormat))
	}

	if !createEnd.IsZero() {
		tx = tx.Where("AccidentCreatedAt <= ?", createEnd.Format(model.TimeFormat))
	}

	if startMoney > 0 {
		tx = tx.Where("AccidentTotalMoney >= ?", startMoney)
	}
	if endMoney > 0 {
		tx = tx.Where("AccidentTotalMoney <= ?", endMoney)
	}

	if vehicleId > 0 {
		tx = tx.Where("VehicleId = ?", vehicleId)
	}

	if driverId > 0 {
		tx = tx.Where("DriverId = ?", driverId)
	}

	tx = tx.Session(&gorm.Session{})

	var count int64
	tx.Count(&count)

	var sum int64
	tx.Select("SUM(AccidentTotalMoney)").Scan(&sum)

	if finishStatus == util.AlarmFinishStatusForDoing {
		tx = tx.Order("CurrentValue DESC")
	} else {
		tx = tx.Order("AccidentCreatedAt DESC")
	}

	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	tx.Find(&records)
	return records, count, sum
}

func (atr *AccidentTimeoutAlarmRecord) GetByFinishStatus(finishStatus int64) []AccidentTimeoutAlarmRecord {
	var records []AccidentTimeoutAlarmRecord
	model.DB().Model(&AccidentTimeoutAlarmRecord{}).Where("FinishStatus = ? AND IsPushWarningNotify = ?", finishStatus, util.StatusForTrue).Find(&records)
	return records
}

func (atr *AccidentTimeoutAlarmRecord) FirstBy(id int64) AccidentTimeoutAlarmRecord {
	var record AccidentTimeoutAlarmRecord
	model.DB().Model(&AccidentTimeoutAlarmRecord{}).Where("id = ?", id).First(&record)
	return record
}

func (atr *AccidentTimeoutAlarmRecord) UpdateColumn(column string, val interface{}) error {
	return model.DB().Model(&AccidentTimeoutAlarmRecord{}).Where("Id = ?", atr.Id).UpdateColumn(column, val).Error
}

func (atr *AccidentTimeoutAlarmRecord) Delete() error {
	return model.DB().Delete(&atr).Error
}
