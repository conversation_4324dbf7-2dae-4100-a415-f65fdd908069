package accident

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type AccidentAlarmCategorySetting struct {
	model.PkId
	TopCorporationId int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:根机构ID"`
	Type             int64  `json:"Type" gorm:"column:type;type:smallint;default:1;comment:类型  1流程报警  2事故报警"`
	Title            string `json:"Title" gorm:"column:title;type:varchar;comment:种类的名称"`
	model.OpUser
	model.Timestamp

	IsUsed bool `json:"IsUsed" gorm:"-"`
}

func (aac *AccidentAlarmCategorySetting) BeforeCreate(db *gorm.DB) error {
	aac.Id = model.Id()
	return nil
}

// Create 创建
func (aac *AccidentAlarmCategorySetting) Create() error {
	return model.DB().Create(&aac).Error
}

// Update 更新
func (aac *AccidentAlarmCategorySetting) Update() error {
	return model.DB().Updates(&aac).Error
}

// Delete 删除
func (aac *AccidentAlarmCategorySetting) Delete() error {
	return model.DB().Delete(&aac).Error
}

// FindBy 根据ID查找
func (aac *AccidentAlarmCategorySetting) FindBy(id int64) AccidentAlarmCategorySetting {
	var alarm AccidentAlarmCategorySetting
	model.DB().Model(&AccidentAlarmCategorySetting{}).Where("Id = ?", id).First(&alarm)
	return alarm
}

// GetAll 查询所有记录
func (aac *AccidentAlarmCategorySetting) GetAll(topCorporationId int64) []AccidentAlarmCategorySetting {
	var alarms []AccidentAlarmCategorySetting
	model.DB().Model(&AccidentAlarmCategorySetting{}).Where("TopCorporationId = ?", topCorporationId).Order("CreatedAt DESC").Find(&alarms)
	return alarms
}
