package accident

import (
	"app/org/scs/erpv2/api/model"
	"fmt"
	"gorm.io/gorm"
)

type AccidentProcessTimeoutSetting struct {
	model.PkId
	TopCorporationId int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:根机构ID"`
	TemplateFormId   string `json:"TemplateFormId" gorm:"column:templateformid;type:varchar;comment:流程模板唯一标识"`
	TemplateFormName string `json:"TemplateFormName" gorm:"column:templateformname;type:varchar;comment:流程模板唯一标识名称"`
	WarningValue     int64  `json:"WarningValue" gorm:"column:warningvalue;type:smallint;default:0;comment:预警阈值 单位：天"`
	AlarmValue       int64  `json:"AlarmValue" gorm:"column:alarmvalue;type:smallint;default:0;comment:告警阈值 单位：天"`
	model.OpUser
	model.Timestamp

	Details []AccidentProcessTimeoutSettingDetail `json:"Details" gorm:"-"`
}

func (apt *AccidentProcessTimeoutSetting) BeforeCreate(db *gorm.DB) error {
	apt.Id = model.Id()
	return nil
}
func (apt *AccidentProcessTimeoutSetting) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&apt).Error
}

func (apt *AccidentProcessTimeoutSetting) TransactionUpdate(tx *gorm.DB) error {
	return tx.Omit("TemplateFormId", "TemplateFormName").Updates(&apt).Error
}

func (apt *AccidentProcessTimeoutSetting) TransactionDelete(tx *gorm.DB) error {
	return tx.Delete(&apt).Error
}

func (apt *AccidentProcessTimeoutSetting) FindBy(id int64) AccidentProcessTimeoutSetting {
	var setting AccidentProcessTimeoutSetting
	model.DB().Model(&AccidentProcessTimeoutSetting{}).Where("Id = ?", id).First(&setting)
	return setting
}

func (apt *AccidentProcessTimeoutSetting) GetAll(topCorporationId int64) []AccidentProcessTimeoutSetting {
	var settings []AccidentProcessTimeoutSetting
	model.DB().Model(&AccidentProcessTimeoutSetting{}).Where("TopCorporationId = ?", topCorporationId).Find(&settings)
	return settings
}

func (apt *AccidentProcessTimeoutSetting) ExistBy(topCorporationId int64, templateFormId string) bool {
	var count int64
	model.DB().Model(&AccidentProcessTimeoutSetting{}).Where("TopCorporationId = ? AND TemplateFormId = ?", topCorporationId, templateFormId).Count(&count)

	return count > 0
}

type AccidentProcessTimeoutSettingDetail struct {
	model.PkId
	AccidentProcessTimeoutSettingId int64      `json:"AccidentProcessTimeoutSettingId" gorm:"column:accidentprocesstimeoutsettingid;type:bigint;default:0;comment:预警ID 关联AccidentProcessTimeoutSetting表主键ID"`
	Nodes                           model.JSON `json:"Nodes" gorm:"column:nodes;type:json;comment:流程节点集合"`
	WarningValue                    int64      `json:"WarningValue" gorm:"column:warningvalue;type:smallint;default:0;comment:预警阈值 单位：天"`
	AlarmValue                      int64      `json:"AlarmValue" gorm:"column:alarmvalue;type:smallint;default:0;comment:告警阈值 单位：天"`
	AccidentAlarmCategorySettingId  int64      `json:"AccidentAlarmCategorySettingId" gorm:"column:accidentalarmcategorysettingid;type:bigint;default:0;comment:报警种类ID 关联AccidentAlarmCategorySetting表主键ID"`
	model.Timestamp

	ActionType string `json:"ActionType" gorm:"-"`
}

func (apd *AccidentProcessTimeoutSettingDetail) BeforeCreate(db *gorm.DB) error {
	apd.Id = model.Id()
	return nil
}

func (apd *AccidentProcessTimeoutSettingDetail) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&apd).Error
}

func (apd *AccidentProcessTimeoutSettingDetail) TransactionUpdate(tx *gorm.DB) error {
	return tx.Select("Nodes", "WarningValue", "AlarmValue", "AccidentAlarmCategorySettingId").Updates(&apd).Error
}

func (apd *AccidentProcessTimeoutSettingDetail) TransactionDelete(tx *gorm.DB) error {
	return tx.Delete(&apd).Error
}

func (apd *AccidentProcessTimeoutSettingDetail) TransactionDeleteBy(accidentProcessTimeoutSettingId int64, tx *gorm.DB) error {
	return tx.Where("AccidentProcessTimeoutSettingId = ?", accidentProcessTimeoutSettingId).Delete(&AccidentTimeoutSettingDetail{}).Error
}

func (apd *AccidentProcessTimeoutSettingDetail) FindBy(id int64) AccidentProcessTimeoutSettingDetail {
	var detail AccidentProcessTimeoutSettingDetail
	model.DB().Model(&AccidentProcessTimeoutSettingDetail{}).Where("Id = ?", id).First(&detail)
	return detail
}

func (apd *AccidentProcessTimeoutSettingDetail) First() AccidentProcessTimeoutSettingDetail {
	var detail AccidentProcessTimeoutSettingDetail
	model.DB().Model(&AccidentProcessTimeoutSettingDetail{}).First(&detail)
	return detail
}

func (apd *AccidentProcessTimeoutSettingDetail) GetBy(accidentProcessTimeoutSettingId int64) []AccidentProcessTimeoutSettingDetail {
	var details []AccidentProcessTimeoutSettingDetail
	model.DB().Model(&AccidentProcessTimeoutSettingDetail{}).Where("AccidentProcessTimeoutSettingId = ?", accidentProcessTimeoutSettingId).Find(&details)
	return details
}

func (apd *AccidentProcessTimeoutSettingDetail) FindByNode(accidentProcessTimeoutSettingId int64, node string) AccidentProcessTimeoutSettingDetail {
	var detail AccidentProcessTimeoutSettingDetail
	model.DB().Model(&AccidentProcessTimeoutSettingDetail{}).Where("AccidentProcessTimeoutSettingId = ?", accidentProcessTimeoutSettingId).Where(fmt.Sprintf("Nodes ?| ARRAY['%s']", node)).First(&detail)
	return detail
}

func (apd *AccidentProcessTimeoutSettingDetail) IsExistAlarmCategory(categoryId int64) bool {
	var count int64
	model.DB().Model(&AccidentProcessTimeoutSettingDetail{}).Where("AccidentAlarmCategorySettingId = ?", categoryId).Count(&count)
	return count > 0
}
