package setting

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/workOrder"
	"gorm.io/gorm"
)

type DeviceFactory struct {
	model.PkId
	TopCorporationId int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	Name             string `json:"Name" gorm:"column:name;type:varchar;comment:厂家名称;" validate:"required"`
	SimpleName       string `json:"SimpleName" gorm:"column:simplename;type:varchar;comment:厂家简称;" validate:"required"`
	ShotName         string `json:"ShotName" gorm:"column:shotname;type:varchar;comment:厂家缩写;" validate:"required"`
	Contactor        string `json:"Contactor" gorm:"column:contactor;type:varchar;comment:联系人;"`
	Contact          string `json:"Contact" gorm:"column:contact;type:varchar;comment:联系方式;"`
	IsBrandSide      int64  `json:"IsBrandSide" gorm:"column:isbrandside;type:smallint;comment:是否是品牌方 1是 2否;"`
	IsSupplier       int64  `json:"IsSupplier" gorm:"column:issupplier;type:smallint;comment:是否是供货方 1是 2否;"`
	IsHandler        int64  `json:"IsHandler" gorm:"column:ishandler;type:smallint;comment:是否是保修方 1是 2否;"`
	IsRepairer       int64  `json:"IsRepairer" gorm:"column:isrepairer;type:smallint;comment:是否是过保维修方 1是 2否;"`
	model.Timestamp
	model.OpUser

	Users []DeviceFactoryHasUser `json:"Users" gorm:"-"`
}

func (df *DeviceFactory) BeforeCreate(db *gorm.DB) error {
	df.Id = model.Id()

	return nil
}
func (df *DeviceFactory) Create() error {
	return model.DB().Create(&df).Error
}

func (df *DeviceFactory) Update() error {
	return model.DB().Select("Name", "SimpleName", "ShotName", "Contactor", "Contact", "IsBrandSide", "IsSupplier", "IsHandler", "IsRepairer").
		Updates(&df).Error
}

func (df *DeviceFactory) GetAll(topCorporationId int64, name string, classId, categoryId, presetType int64) []DeviceFactory {
	var factories []DeviceFactory
	tx := model.DB().Model(&DeviceFactory{}).Where("TopCorporationId = ?", topCorporationId)
	if name != "" {
		tx = tx.Where("Name LIKE ? OR SimpleName LIKE ? OR ShotName LIKE ?", "%"+name+"%", "%"+name+"%", "%"+name+"%")
	}

	if classId > 0 || categoryId > 0 || presetType > 0 {
		subQuery := model.DB().Model(&workOrder.DevicePreset{}).Select("ItemId")
		if classId > 0 {
			subQuery.Where("ClassId = ?", classId)
		}
		if categoryId > 0 {
			subQuery.Where("DictId = ?", categoryId)
		}

		if presetType > 0 {
			subQuery.Where("Type = ?", presetType)
		}

		tx.Where("Id IN (?)", subQuery)
	}

	tx.Order("CreatedAt DESC").Find(&factories)

	return factories
}

func (df *DeviceFactory) FirstBy(id int64) DeviceFactory {
	var factory DeviceFactory
	model.DB().Model(&DeviceFactory{}).Where("Id = ?", id).First(&factory)

	return factory
}

func (df *DeviceFactory) FirstByName(topCorporationId int64, name, simpleName, shotName string) DeviceFactory {
	var factory DeviceFactory
	model.DB().Model(&DeviceFactory{}).Where("TopCorporationId = ?", topCorporationId).Where("Name = ? OR SimpleName = ? OR ShotName = ?", name, simpleName, shotName).First(&factory)

	return factory
}

func (df *DeviceFactory) Delete() error {
	return model.DB().Where("id = ?", df.Id).Delete(&DeviceFactory{}).Error
}

type DeviceFactoryHasUser struct {
	model.PkId
	Pid             int64  `json:"Pid" gorm:"column:pid;type:bigint;comment:父级ID 0代表顶级;default:0"`
	DeviceFactoryId int64  `json:"DeviceFactoryId" gorm:"column:devicefactoryid;type:bigint;comment:厂家ID"`
	SceneType       int64  `json:"SceneType" gorm:"column:scenetype;type:smallint;comment:类型 1负责人 2保修方指派人员 3过保维修方指派人员"`
	UserId          int64  `json:"UserId" gorm:"column:userid;type:bigint;comment:账号ID"`
	UserName        string `json:"UserName" gorm:"column:username;type:varchar(100);comment:账号"`
	NickName        string `json:"NickName" gorm:"column:nickname;type:varchar(100);comment:姓名"`
	model.Timestamp
	model.OpUser

	Children []DeviceFactoryHasUser `json:"Children" gorm:"-"`
}

func (df *DeviceFactoryHasUser) BeforeCreate(db *gorm.DB) error {
	df.Id = model.Id()
	return nil
}

func (df *DeviceFactoryHasUser) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&df).Error
}

func (df *DeviceFactoryHasUser) BatchCreate(tx *gorm.DB, records []DeviceFactoryHasUser) error {
	return tx.Create(&records).Error
}

func (df *DeviceFactoryHasUser) DeleteBy(tx *gorm.DB, deviceFactoryId int64) error {
	return tx.Where("DeviceFactoryId = ?", deviceFactoryId).Delete(&DeviceFactoryHasUser{}).Error
}

func (df *DeviceFactoryHasUser) GetBy(deviceFactoryId, pid int64, sceneTypes []int64) []DeviceFactoryHasUser {
	var records []DeviceFactoryHasUser
	tx := model.DB().Where("DeviceFactoryId = ? AND Pid = ?", deviceFactoryId, pid)
	if len(sceneTypes) > 0 {
		tx.Where("SceneType IN ?", sceneTypes)
	}
	tx.Find(&records)
	return records
}

func (df *DeviceFactoryHasUser) GetByUser(deviceFactoryId, userId int64, sceneTypes []int64) DeviceFactoryHasUser {
	var record DeviceFactoryHasUser
	tx := model.DB().Where("DeviceFactoryId = ? AND UserId = ?", deviceFactoryId, userId)
	if len(sceneTypes) > 0 {
		tx.Where("SceneType IN ?", sceneTypes)
	}
	tx.Find(&record)
	return record
}
