package setting

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type GlobalSetting struct {
	model.PkId
	TopCorporationId int64      `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	SettingItem      model.JSON `json:"SettingItem" gorm:"column:settingitem;type:json;comment:配置项"`
	SettingType      int64      `json:"SettingType" gorm:"column:settingtype;type:smallint;comment:类型 1运营报表审批中心配置 2机动线路配置"`
	model.OpUser
	model.Timestamp
}

// 运营报表 settingType=1
type GlobalSettingItemForOperationReport struct {
	StartDay         int64   `json:"StartDay"`
	EndDay           int64   `json:"EndDay"`
	DisabledFleetIds []int64 `json:"DisabledFleetIds"`
}

// 机动线路配置 settingType=2
type GlobalSettingItemForMotorLine struct {
	CorporationId int64 `json:"CorporationId"`
	LineId        int64 `json:"LineId"`
}

// 司机贡献考核配置的日期点 settingType=5
type GlobalSettingItemForDriverDevoteCalcDayDot struct {
	DayDot int64 `json:"DayDot"`
}

// 司机考核-奖励设置 settingType=11
type GlobalSettingItemForRewardAssess struct {
	Settings []GlobalSettingItemForRewardAssessItem `json:"Settings"`
}
type GlobalSettingItemForRewardAssessItem struct {
	Name        string                             `json:"Name"`
	RewardMoney int64                              `json:"RewardMoney"` //奖励金额 单位：分
	Children    []GlobalSettingItemForRewardAssess `json:"Children"`    //子级
}

// 司机考核-安全事故考核 settingType=12
type GlobalSettingItemForAccidentAssess struct {
	Line    GlobalSettingItemForAccidentAssessItem
	CctLine GlobalSettingItemForAccidentAssessItem
}
type GlobalSettingItemForAccidentAssessItem struct {
	BaseMoney int64                                   `json:"BaseMoney"` //考核基数 单位：分
	Settings  []GlobalSettingItemForAccidentAssessArr `json:"Settings"`  //设置
}
type GlobalSettingItemForAccidentAssessArr struct {
	IsPeopleHurt  int64 `json:"IsPeopleHurt"`  //是否有人伤  1是 2否
	LiabilityType int64 `json:"LiabilityType"` //责任认定 1-全责,2-主责,3-同责,4-次责,5-无责
	Money         int64 `json:"Money"`         //金额 单位分
}

// 司机考核-安全年度考核 settingType=13
type GlobalSettingItemForViolationYearAssess struct {
	Line    GlobalSettingItemForViolationYearAssessItem
	CctLine GlobalSettingItemForViolationYearAssessItem
}
type GlobalSettingItemForViolationYearAssessItem struct {
	BaseMoney              int64                               `json:"BaseMoney"`              //考核基数 单位：分
	AccidentDeductMoney    int64                               `json:"AccidentDeductMoney"`    //事故扣款金额 单位：分
	LessThanDayDeductMoney int64                               `json:"LessThanDayDeductMoney"` //天数不足（每月）扣款  单位：分
	Settings               []GlobalSettingItemForYearAssessArr `json:"Settings"`               //配置项
}
type GlobalSettingItemForYearAssessArr struct {
	ViolationCategoryId int64 `json:"ViolationCategoryId"` //违规类别
	CheckType           int64 `json:"CheckType"`           //考核方式 1固定金额  2基数比例
	Value               int64 `json:"Value"`               //考核值  固定金额时，存储的是金额，单位：分；基数比例时，存储的是百分比，8023=>80.23%
}

// 司机考核-安全公里考核 settingType=14
type GlobalSettingItemForSafeMileageAssess struct {
	Line    []GlobalSettingItemForSafeMileageAssessItem
	CctLine []GlobalSettingItemForSafeMileageAssessItem
}
type GlobalSettingItemForSafeMileageAssessItem struct {
	AccidentCount int64 `json:"AccidentCount"` //事故数量
	IsReduce      int64 `json:"IsReduce"`      //是否减轻处罚 1是 2否
	KmMoney       int64 `json:"KmMoney"`       //每公里金额 单位：分
}

// 司机考核-安全贡献考核 settingType=15
type GlobalSettingItemForViolationSafeContributionAssess struct {
	Settings []GlobalSettingItemForContributionAssessItem
}
type GlobalSettingItemForContributionAssessItem struct {
	Level int64 `json:"Level"` //档位
	Money int64 `json:"Money"` //金额 单位：分
}

// 司机考核-服务质量年度考核 settingType=16
type GlobalSettingItemForServiceYearAssess struct {
	Line    GlobalSettingItemForServiceYearAssessItem
	CctLine GlobalSettingItemForServiceYearAssessItem
}
type GlobalSettingItemForServiceYearAssessItem struct {
	BaseMoney              int64                               `json:"BaseMoney"`              //考核基数 单位：分
	LessThanDayDeductMoney int64                               `json:"LessThanDayDeductMoney"` //天数不足（每月）扣款
	Settings               []GlobalSettingItemForYearAssessArr `json:"Settings"`               //配置项
}

// 司机考核-服务质量贡献考核 settingType=17
type GlobalSettingItemForServiceSafeContributionAssess struct {
	Settings []GlobalSettingItemForContributionAssessItem
}

// 司机考核-服务质量三方测评考核 settingType=18
type GlobalSettingItemForServiceThirdPartyAssess struct {
	BaseMoney int64                               `json:"BaseMoney"` //基数 单位：分
	Settings  []GlobalSettingItemForYearAssessArr `json:"Settings"`
}

// 司机考核 - 额外纳入计算运营天数的类型 settingType=19
type GlobalSettingItemForExtraCalcDayCount struct {
	Line    []GlobalSettingItemForExtraCalcDayCountItem `json:"Line"`    //常规线
	CctLine []GlobalSettingItemForExtraCalcDayCountItem `json:"CctLine"` //村村通线
	XlbLine []GlobalSettingItemForExtraCalcDayCountItem `json:"XlbLine"` //小蓝巴
}
type GlobalSettingItemForExtraCalcDayCountItem struct {
	LeaveTypes               []int64 `json:"LeaveTypes"`               //请假表中纳入计算的请假类型
	OutFrequencyAddWorkTypes []int64 `json:"OutFrequencyAddWorkTypes"` //班制外加班表中纳入计算的班制外加班类型
	IrregularLineLineAttrs   []int64 `json:"IrregularLineLineAttrs"`   //定制线路明细表中纳入计算的线路属性
}

func (gs *GlobalSetting) BeforeCreate(db *gorm.DB) error {
	gs.Id = model.Id()
	return nil
}

func (gs *GlobalSetting) UpdateOrCreate() error {
	var setting GlobalSetting
	model.DB().Model(&GlobalSetting{}).Where("TopCorporationId = ? AND SettingType = ?", gs.TopCorporationId, gs.SettingType).First(&setting)
	if setting.Id == 0 {
		return model.DB().Create(&gs).Error
	}
	gs.Id = setting.Id
	return model.DB().Select("*").Updates(&gs).Error
}

func (gs *GlobalSetting) GetBy(topCorporationId, settingType int64) GlobalSetting {
	var setting GlobalSetting
	model.DB().Model(&GlobalSetting{}).Where("TopCorporationId = ? AND SettingType = ?", topCorporationId, settingType).First(&setting)

	return setting
}
