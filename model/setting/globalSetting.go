package setting

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type GlobalSetting struct {
	model.PkId
	TopCorporationId int64      `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	SettingItem      model.JSON `json:"SettingItem" gorm:"column:settingitem;type:json;comment:配置项"`
	SettingType      int64      `json:"SettingType" gorm:"column:settingtype;type:smallint;comment:类型 1运营报表审批中心配置 2机动线路配置"`
	model.OpUser
	model.Timestamp
}

// 运营报表 SettingType=1
type GlobalSettingItemForOperationReport struct {
	StartDay         int64   `json:"StartDay"`
	EndDay           int64   `json:"EndDay"`
	DisabledFleetIds []int64 `json:"DisabledFleetIds"`
}

// 机动线路配置 SettingType=2
type GlobalSettingItemForMotorLine struct {
	CorporationId int64 `json:"CorporationId"`
	LineId        int64 `json:"LineId"`
}

// 车辆保养配置 SettingType=3
type GlobalSettingItemForVehicleMaintenance struct {
	TypeId    int64  `json:"TypeId"`
	TypeName  string `json:"TypeName"`
	Fee       int64  `json:"Fee"`
	Materials []struct {
		Id   int64  `json:"Id"`
		Name string `json:"Name"`
	} `json:"Materials"`
}

// 推送账号设置 SettingType=4
type GlobalSettingItemForNotifyUser struct {
	UserId int64 `json:"UserId"`
}

func (gs *GlobalSetting) BeforeCreate(db *gorm.DB) error {
	gs.Id = model.Id()
	return nil
}

func (gs *GlobalSetting) UpdateOrCreate() error {
	var setting GlobalSetting
	model.DB().Model(&GlobalSetting{}).Where("TopCorporationId = ? AND SettingType = ?", gs.TopCorporationId, gs.SettingType).First(&setting)
	if setting.Id == 0 {
		return model.DB().Create(&gs).Error
	}
	gs.Id = setting.Id
	return model.DB().Select("*").Updates(&gs).Error
}

func (gs *GlobalSetting) GetBy(topCorporationId, settingType int64) GlobalSetting {
	var setting GlobalSetting
	model.DB().Model(&GlobalSetting{}).Where("TopCorporationId = ? AND SettingType = ?", topCorporationId, settingType).First(&setting)

	return setting
}
