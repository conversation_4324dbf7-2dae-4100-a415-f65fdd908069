package setting

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type ColumnSetting struct {
	model.PkId
	TopCorporationId int64      `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	UserId           int64      `json:"UserId" gorm:"column:userid;type:integer;comment:用户ID"`
	UserName         string     `json:"UserName" gorm:"column:username;type:varchar;comment:名字"`
	SettingItem      model.JSON `json:"SettingItem" gorm:"column:settingitem;type:json;comment:配置项"`
	TableName        string     `json:"TableName" gorm:"column:tablename;type:varchar;comment:适用的列表"`
	model.Timestamp
}

type ColumnSettingItem struct {
	CheckedColumns       []string                 `json:"CheckedColumns"`         //选中需显示的列名集合
	ColumnAttr           []CheckedColumnValueItem `json:"ColumnAttr"`             //设置的列的属性
	PreFixedColumnCount  int64                    `json:"BeforeFixedColumnCount"` //前面多少列固定
	RearFixedColumnCount int64                    `json:"RearFixedColumnCount"`   //后面多少列固定
}

type CheckedColumnValueItem struct {
	ColumnName   string  `json:"ColumnName"`   //列名
	ColumnCnName string  `json:"ColumnCnName"` //列中文名
	Width        int64   `json:"-"`            //列宽
	Sort         int64   `json:"Sort"`         //列序
	Color        string  `json:"Color"`        //颜色
	Unit         int64   `json:"unit"`         //单位换算
	IsSum        bool    `json:"IsSum"`        //是否求和
	Precision    int64   `json:"Precision"`    //精度 小数点后几位
	Type         string  `json:"Type"`         //数据类型
	Suffix       string  `json:"Suffix"`       //数据后缀 比如%
	HMerge       int     `json:"-"`            //水平合并
	VMerge       int     `json:"-"`            //垂直合并
	Bold         bool    `json:"-"`            //字体加粗
	RowHeight    float64 `json:"-"`            //行高
	BorderStyle  string  `json:"-"`            //边框样式
	IsHidden     bool    `json:"-"`            //是否隐藏
}

func (cs *ColumnSetting) BeforeCreate(db *gorm.DB) error {
	cs.Id = model.Id()
	return nil
}

func (cs *ColumnSetting) Create() error {
	return model.DB().Create(&cs).Error
}

func (cs *ColumnSetting) Update() error {
	return model.DB().Select("*").Updates(&cs).Error
}

func (cs *ColumnSetting) FirstByUserId(userId int64, tableName string) ColumnSetting {
	var setting ColumnSetting
	model.DB().Model(&ColumnSetting{}).Where("UserId = ? AND TableName = ?", userId, tableName).First(&setting)

	return setting
}

func (cs *ColumnSetting) FirstById(id int64) ColumnSetting {
	var setting ColumnSetting
	model.DB().Model(&ColumnSetting{}).Where("Id = ?", id).First(&setting)

	return setting
}

func (cs *ColumnSetting) Delete() error {
	return model.DB().Where("Id = ?", cs.Id).Delete(&ColumnSetting{}).Error
}
