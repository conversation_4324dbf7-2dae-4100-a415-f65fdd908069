package setting

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type ProcessTimeoutAlarmRecord struct {
	model.PkId
	TopCorporationId               int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:根机构ID"`
	FormInstanceId                 int64           `json:"FormInstanceId" gorm:"column:forminstanceid;type:bigint;comment:流程ID"`
	ProcessId                      string          `json:"ProcessId" gorm:"column:processid;type:varchar;comment:流程的ID"`
	ProcessTitle                   string          `json:"ProcessTitle" gorm:"column:processtitle;type:varchar;comment:流程标题"`
	ModelId                        string          `json:"ModelId" gorm:"column:modelid;type:varchar;comment:流程模块标识"`
	TemplateFormId                 string          `json:"TemplateFormId" gorm:"column:templateformid;type:varchar;comment:流程模板唯一标识"`
	TemplateFormName               string          `json:"TemplateFormName" gorm:"column:templateformname;type:varchar;comment:流程模板唯一标识名称"`
	ProcessCreatedAt               model.LocalTime `json:"ProcessCreatedAt" gorm:"column:processcreatedat;type:timestamp;comment:流程创建时间"`
	TrafficAccidentId              int64           `json:"TrafficAccidentId" gorm:"column:trafficaccidentid;type:bigint;comment:事故ID"`
	TrafficAccidentCode            string          `json:"TrafficAccidentCode" gorm:"column:trafficaccidentcode;type:varchar;comment:事故编号"`
	ApprovingUserId                string          `json:"ApprovingUserId" gorm:"column:approvinguserid;type:varchar(200);comment:审批人ID，多个用,隔开"`
	ApprovingUserName              string          `json:"ApprovingUserName" gorm:"column:approvingusername;type:varchar;comment:审批人，多个用,隔开"`
	ApproveStartAt                 model.LocalTime `json:"ApproveStartAt" gorm:"column:approvestartat;type:timestamp;comment:开始审批的时间"`
	ProcessNode                    string          `json:"ProcessNode" gorm:"column:processnode;type:varchar;comment:流程节点名称"`
	WarningValue                   int64           `json:"WarningValue" gorm:"column:warningvalue;type:smallint;default:0;comment:预警阈值 单位：天"`
	AlarmValue                     int64           `json:"AlarmValue" gorm:"column:alarmvalue;type:smallint;default:0;comment:告警阈值 单位：天"`
	CurrentValue                   int64           `json:"CurrentValue" gorm:"column:currentvalue;type:smallint;default:0;comment:当前的值 指警告产生时实际的值 单位：天"`
	AccidentAlarmCategorySettingId int64           `json:"AccidentAlarmCategorySettingId" gorm:"column:accidentalarmcategorysettingid;type:bigint;default:0;comment:报警种类ID 关联AccidentAlarmCategorySetting表主键ID"`
	AccidentAlarmCategoryTitle     string          `json:"AccidentAlarmCategoryTitle" gorm:"column:accidentalarmcategorytitle;type:varchar;default:;comment:报警种类名称"`
	AlarmType                      int64           `json:"AlarmType" gorm:"column:alarmtype;type:smallint;default:0;comment:类型  1预警  2告警"`
	FinishStatus                   int64           `json:"FinishStatus" gorm:"column:finishstatus;type:smallint;comment:完成状态 1进行中 2历史;"`
	ProcessingTime                 int64           `json:"ProcessingTime" gorm:"column:processingtime;type:integer;comment:当前节点审批人实际处理时长，单位：分钟"`
	IsPushWarningNotify            int64           `json:"IsPushWarningNotify" gorm:"column:ispushwarningnotify;type:smallint;comment:当前节点是否推送流程预警告警消息 1是 2否 2表示忽略;default:1"`
	model.Timestamp
}

func (ptr *ProcessTimeoutAlarmRecord) BeforeCreate(db *gorm.DB) error {
	ptr.Id = model.Id()
	return nil
}

func (ptr *ProcessTimeoutAlarmRecord) Create() error {
	return model.DB().Create(&ptr).Error
}

func (ptr *ProcessTimeoutAlarmRecord) Update() error {
	return model.DB().Omit("TopCorporationId", "FormInstanceId", "ProcessId", "ProcessTitle", "ModelId", "TemplateFormId", "TemplateFormName", "ProcessCreatedAt",
		"ApprovingUserId", "ApprovingUserName", "ProcessNode", "FinishStatus", "ProcessingTime").Updates(&ptr).Error
}

func (ptr *ProcessTimeoutAlarmRecord) UpdateFinishStatus() error {
	return model.DB().Select("FinishStatus", "ProcessingTime").Updates(&ptr).Error
}

func (ptr *ProcessTimeoutAlarmRecord) FindByFormInstanceId(formInstanceId int64, userId int64, node string) ProcessTimeoutAlarmRecord {
	var record ProcessTimeoutAlarmRecord
	model.DB().Model(&ProcessTimeoutAlarmRecord{}).Where("FormInstanceId = ? AND ApprovingUserId LIKE ? AND ProcessNode = ?", formInstanceId, "%"+fmt.Sprintf("%v", userId)+"%", node).First(&record)

	return record
}
func (ptr *ProcessTimeoutAlarmRecord) FindByFormInstanceIdAndNode(formInstanceId int64, node string) ProcessTimeoutAlarmRecord {
	var record ProcessTimeoutAlarmRecord
	model.DB().Model(&ProcessTimeoutAlarmRecord{}).Where("FormInstanceId = ? AND ProcessNode = ?", formInstanceId, node).First(&record)

	return record
}

func (ptr *ProcessTimeoutAlarmRecord) CountByFormInstanceIds(formInstanceId []int64, alarmType int64) int64 {
	var count int64
	model.DB().Model(&ProcessTimeoutAlarmRecord{}).Where("FormInstanceId IN ? AND AlarmType = ? AND FinishStatus = ?", formInstanceId, alarmType, util.AlarmFinishStatusForDoing).Count(&count)

	return count
}

func (ptr *ProcessTimeoutAlarmRecord) GetByFormInstanceIds(formInstanceId []int64, alarmType int64, paginator model.Paginator) ([]ProcessTimeoutAlarmRecord, int64) {

	tx := model.DB().Model(&ProcessTimeoutAlarmRecord{}).Where("FormInstanceId IN ? AND AlarmType = ? AND FinishStatus = ?", formInstanceId, alarmType, util.AlarmFinishStatusForDoing)

	var count int64
	tx.Count(&count)

	var items []ProcessTimeoutAlarmRecord
	tx.Order("CurrentValue DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&items)
	return items, count
}

func (ptr *ProcessTimeoutAlarmRecord) GetBy(topCorporationId int64, modelId string, alarmType, startDay, endDay, alarmCategoryId,
	finishStatus int64, approveUserName string, processStart, processEnd time.Time, paginator model.Paginator) ([]ProcessTimeoutAlarmRecord, int64) {
	var records []ProcessTimeoutAlarmRecord
	tx := model.DB().Model(&ProcessTimeoutAlarmRecord{}).Where("TopCorporationId = ?", topCorporationId)
	if modelId != "" {
		tx = tx.Where("ModelId = ?", modelId)
	}

	if alarmType > 0 {
		tx = tx.Where("AlarmType = ?", alarmType)
	}

	if startDay > 0 {
		tx = tx.Where("CurrentValue >= ?", startDay)
	}

	if endDay > 0 {
		tx = tx.Where("CurrentValue <= ?", endDay)
	}

	if alarmCategoryId > 0 {
		tx = tx.Where("AccidentAlarmCategorySettingId = ?", alarmCategoryId)
	}

	if finishStatus > 0 {
		tx = tx.Where("FinishStatus = ?", finishStatus)
	}

	if approveUserName != "" {
		tx = tx.Where("ApprovingUserName LIKE ?", "%"+approveUserName+"%")
	}

	if !processStart.IsZero() {
		tx = tx.Where("ProcessCreatedAt >= ?", processStart.Format(model.TimeFormat))
	}

	if !processEnd.IsZero() {
		tx = tx.Where("ProcessCreatedAt <= ?", processEnd.Format(model.TimeFormat))
	}

	if finishStatus == util.AlarmFinishStatusForDoing {
		tx = tx.Order("CurrentValue DESC")
	} else {
		tx = tx.Order("ProcessCreatedAt DESC")
	}

	var count int64
	tx.Count(&count)

	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	tx.Find(&records)

	return records, count
}

func (ptr *ProcessTimeoutAlarmRecord) GetEnablePushRecord(finishStatus int64) []ProcessTimeoutAlarmRecord {
	var records []ProcessTimeoutAlarmRecord
	model.DB().Model(&ProcessTimeoutAlarmRecord{}).Where("FinishStatus = ? AND IsPushWarningNotify = ?", finishStatus, util.StatusForTrue).Find(&records)
	return records
}

func (ptr *ProcessTimeoutAlarmRecord) FirstBy(id int64) ProcessTimeoutAlarmRecord {
	var record ProcessTimeoutAlarmRecord
	model.DB().Model(&ProcessTimeoutAlarmRecord{}).Where("id = ?", id).First(&record)
	return record
}

func (ptr *ProcessTimeoutAlarmRecord) UpdateColumn(column string, val interface{}) error {
	return model.DB().Model(&ProcessTimeoutAlarmRecord{}).Where("Id = ?", ptr.Id).UpdateColumn(column, val).Error
}

func (ptr *ProcessTimeoutAlarmRecord) Delete() error {
	return model.DB().Delete(&ptr).Error
}
