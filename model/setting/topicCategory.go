package setting

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
	"strings"
)

type TopicCategory struct {
	model.PkId
	Category string `json:"Category" gorm:"column:category;type:varchar;default:;comment:分类;uniqueIndex:topic_category_name"`
}

func (m *TopicCategory) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}
func (m *TopicCategory) Create() error {
	return model.DB().Create(&m).Error
}

func (m *TopicCategory) Update() error {
	return model.DB().Updates(&m).Error
}

func (m *TopicCategory) Delete(id int64) error {
	return model.DB().Delete(&TopicCategory{}, id).Error
}

func (m *TopicCategory) FindByCategory(category string) error {
	return model.DB().Model(&TopicCategory{}).Where("Category = ?", category).Find(&m).Error
}

func (m *TopicCategory) FindCategoryNames(categoryIds string) (categoryNames []string) {
	if categoryIds == "" {
		return
	}
	categoryArr := util.StringArrToInt64(strings.Split(categoryIds, ","))
	model.DB().Model(&TopicCategory{}).Where("Id IN ?", categoryArr).Pluck("Category", &categoryNames)
	return
}

func (m *TopicCategory) List(category string, paginator model.Paginator) (data []TopicCategory, totalCount int64, err error) {
	tx := model.DB().Model(&m)
	if category != "" {
		tx = tx.Where("Category LIKE ?", "%"+category+"%")
	}
	tx.Count(&totalCount)
	if paginator.Limit > 0 {
		tx = tx.Limit(paginator.Limit).Offset(paginator.Offset)
	}
	err = tx.Find(&data).Error
	return
}
