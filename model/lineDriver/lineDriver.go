package lineDriver

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type LineHasDriver struct {
	model.PkId
	model.Corporations
	LineId      int64           `json:"LineId" gorm:"column:lineid;type:integer;comment:线路ID;uniqueIndex:lineid_driverid_startat"`
	LineName    string          `json:"LineName" gorm:"column:linename;type:varchar;comment:线路名称;"`
	DriverId    int64           `json:"DriverId" gorm:"column:driverid;type:integer;comment:司机ID 对应主数据人员表的主键ID;uniqueIndex:lineid_driverid_startat"`
	DriverName  string          `json:"DriverName" gorm:"column:drivername;type:varchar;comment:司机姓名;"`
	DriverPhone string          `json:"DriverPhone" gorm:"column:driverPhone;type:varchar;comment:司机手机号;"`
	StartAt     model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:归属开始时间;uniqueIndex:lineid_driverid_startat"`
	EndAt       model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:归属结束时间;"`
	model.Timestamp
}

func (ld *LineHasDriver) BeforeCreate(db *gorm.DB) error {
	ld.Id = model.Id()
	return nil
}

func (ld *LineHasDriver) Create() error {
	return model.DB().Create(&ld).Error
}

func (ld *LineHasDriver) Updates() error {
	return model.DB().Select("LineId", "LineName", "DriverId", "DriverName", "DriverPhone", "EndAt").Updates(&ld).Error
}

func (ld *LineHasDriver) LatestFirstBy(lineId, driverId int64) LineHasDriver {
	var record LineHasDriver
	model.DB().Model(&LineHasDriver{}).Where("LineId = ? AND DriverId = ?", lineId, driverId).Order("StartAt DESC").First(&record)

	return record
}

func (ld *LineHasDriver) GetLineHasDriverIdBy(lineId int64, reportAt time.Time) []int64 {
	var driverIds []int64
	model.DB().Model(&LineHasDriver{}).Where("LineId = ? AND StartAt <= ? AND EndAt >= ?", lineId, reportAt.Format(model.DateFormat), reportAt.Format(model.DateFormat)).Pluck("DriverId", &driverIds)

	return driverIds
}

func (ld *LineHasDriver) DriverHasLine(driverId int64, reportAt time.Time) LineHasDriver {
	var record LineHasDriver
	model.DB().Model(&LineHasDriver{}).Where("DriverId = ? AND StartAt <= ? AND EndAt >= ?", driverId, reportAt.Format(model.DateFormat), reportAt.Format(model.DateFormat)).First(&record)

	//如果没查到数据 就查询离时间最近的一个归属关系
	if record.Id == 0 {
		model.DB().Model(&LineHasDriver{}).Where("DriverId = ? AND StartAt >= ?", driverId, reportAt.Format(model.DateFormat)).Order("StartAt ASC").First(&record)
	}

	return record
}
