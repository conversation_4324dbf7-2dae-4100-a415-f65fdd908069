package process

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"log"
	"strings"
	"time"
)

type LbpmApplyProcess struct {
	FormInstanceId         int64            `json:"FormInstanceId" gorm:"column:forminstanceid;type:bigint;primaryKey;comment:申请表单实例ID"` //申请表单实例ID
	TopCorporationId       int64            `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:顶级机构Id"`          //顶级机构Id
	CorporationId          int64            `json:"CorporationId" gorm:"column:corporationid;type:bigint;comment:发起人所属机构Id"`
	CorporationName        string           `json:"CorporationName" gorm:"column:corporationname;type:varchar;comment:机构名称"`
	ItemId                 int64            `json:"ItemId" gorm:"column:itemid;type:bigint;comment:关联的具体的申请实例表的ID"`                                //关联的具体的申请实例表的ID  具体是哪张表由TableName来确定
	ItemTableName          string           `json:"ItemTableName" gorm:"column:itemtablename;type:varchar;comment:数据表名称"`                          //数据表名称  关联的数据表的名称
	ItemTableStatusField   string           `json:"ItemTableStatusField" gorm:"column:itemtablestatusfield;type:varchar;comment:业务数据中存储审批状态的字段名称"` //业务数据中存储审批状态的字段名称
	Param                  model.JSON       `json:"Param" gorm:"column:param;type:json;comment:发起审批时用户填的表单数据"`                                     //发起审批时用户填的表单数据
	ProcessFieldValue      model.JSON       `json:"ProcessFieldValue" gorm:"column:processfieldvalue;type:json;comment:流程中用到的参数和值"`                //流程中用到的参数和值
	ApplyUserId            int64            `json:"ApplyUserId" gorm:"column:applyuserid;type:integer;comment:申请人员ID"`                             //申请人员ID
	ApplyUserName          string           `json:"ApplyUserName" gorm:"column:applyusername;type:varchar;comment:申请人姓名"`                          //申请人姓名
	ApplyUserMobile        string           `json:"ApplyUserMobile" gorm:"column:applyusermobile;type:varchar;comment:申请人手机号"`
	ProcessId              string           `json:"ProcessId" gorm:"column:processid;type:varchar;uniqueIndex:lbpm_apply_process_id_unique_index;comment:流程的ID"`                     //流程的ID  发起流程时蓝凌返回的流程标识
	SysId                  string           `json:"SysId" gorm:"column:sysid;type:varchar;comment:系统标识"`                                                                             //系统标识
	ModelId                string           `json:"ModelId" gorm:"column:modelid;type:varchar;comment:业务模块ID"`                                                                       //业务模块ID
	ModelName              string           `json:"ModelName" gorm:"column:modelname;type:varchar;comment:业务模块名称"`                                                                   //业务模块名称
	TemplateFormId         string           `json:"TemplateFormId" gorm:"column:templateformid;type:varchar;comment:表单ID"`                                                           //表单ID
	TemplateFormName       string           `json:"TemplateFormName" gorm:"column:templateformname;type:varchar;comment:表单名称"`                                                       //表单名称
	Title                  string           `json:"Title" gorm:"column:title;type:varchar(100);comment:流程标题"`                                                                        //流程标题
	Status                 int64            `json:"Status" gorm:"column:status;type:smallint;default:0;comment:流程状态 "`                                                               //流程状态 0草稿  1审批进行中  2审批完成  3驳回  4撤回  5废弃
	ApplyAt                *model.LocalTime `json:"ApplyAt" gorm:"column:applyat;type:timestamp;comment:申请提交时间"`                                                                     //申请提交时间
	DoneAt                 *model.LocalTime `json:"DoneAt" gorm:"column:doneat;type:timestamp;comment:申请审批完成的时间"`                                                                    //申请审批完成的时间
	CurrentHandlerUserName string           `json:"CurrentHandlerUserName" gorm:"column:currenthandlerusername;type:text;comment:当前处理人姓名，多个人使用,分隔，不包括抄送节点，审批完成值为最后一个审批人;default:''"` // 当前处理人姓名，多个人使用,分隔，不包括抄送节点，审批完成值为最后一个审批人
	IsPushWarningNotify    int64            `json:"IsPushWarningNotify" gorm:"column:ispushwarningnotify;type:smallint;comment:是否推送流程预警告警消息 1是 2否;default:1"`
	model.Timestamp
}

// MessageType 返回消息类型标识
func (lp *LbpmApplyProcess) MessageType() string {
	return "process_message"
}

// TableName 返回模型对应的表名
func (lp *LbpmApplyProcess) TableName() string {
	return "lbpm_apply_processes"
}

// BeforeCreate 钩子方法
func (lp *LbpmApplyProcess) BeforeCreate(tx *gorm.DB) error {
	return nil
}

// Create 创建
func (lp *LbpmApplyProcess) Create() error {
	return model.DB().Create(lp).Error
}

// TransactionCreate 创建
func (lp *LbpmApplyProcess) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(lp).Error
}

// TransactionUpdate 创建
func (lp *LbpmApplyProcess) TransactionUpdate(tx *gorm.DB) error {
	return tx.Model(&LbpmApplyProcess{}).Select("Status", "Param", "ProcessFieldValue").Where("FormInstanceId = ?", lp.FormInstanceId).Updates(&lp).Error
}

// FindBy 根据主键查询
func (lp *LbpmApplyProcess) FindBy(formInstanceId int64) error {
	return model.DB().Model(&LbpmApplyProcess{}).Where("FormInstanceId = ?", formInstanceId).First(&lp).Error
}

// GetProcess 根据流程ID和状态获取流程
func (lp *LbpmApplyProcess) GetProcess(processId string, status []int64) error {
	return model.DB().Model(&LbpmApplyProcess{}).Where("ProcessId = ? AND Status IN ?", processId, status).First(&lp).Error
}

// GetApproveProcess 根据表单模板和业务主键以及状态获取流程
func (lp *LbpmApplyProcess) GetApproveProcess(templateFormId string, itemId int64, status []int64) error {
	tx := model.DB().Model(&LbpmApplyProcess{}).Where("TemplateFormId = ? AND ItemId = ?", templateFormId, itemId)
	if len(status) > 0 {
		tx = tx.Where("Status IN ?", status)
	}
	return tx.Order("ApplyAt DESC").First(&lp).Error
}

// GetApproveProcessByProcessId 根据表单模板和业务主键、流程ID以及状态获取流程
func (lp *LbpmApplyProcess) GetApproveProcessByProcessId(templateFormId, processId string, itemId int64, status []int64) error {
	tx := model.DB().Model(&LbpmApplyProcess{}).Where("TemplateFormId = ? AND ItemId = ? AND ProcessId = ?", templateFormId, itemId, processId)
	if len(status) > 0 {
		tx = tx.Where("Status IN ?", status)
	}
	return tx.Order("ApplyAt DESC").First(&lp).Error
}

// GetProcessByItemId 根据表单模板和业务主键获取流程
func (lp *LbpmApplyProcess) GetProcessByItemId(itemId int64, tableName string) error {
	return model.DB().Model(&LbpmApplyProcess{}).Where("ItemId = ? AND ItemTableName = ?", itemId, tableName).Order("ApplyAt DESC").First(&lp).Error
}

// GetApprovingProcess 获取流程
func (lp *LbpmApplyProcess) GetApprovingProcess(formInstanceIds []int64, status []int64) []LbpmApplyProcess {
	var processes []LbpmApplyProcess
	model.DB().Model(&LbpmApplyProcess{}).Where("FormInstanceId In ? AND Status IN ?", formInstanceIds, status).Find(&processes)

	return processes
}

func (lp *LbpmApplyProcess) GetProcessesByItemId(templateFormId string, itemId int64) []LbpmApplyProcess {
	var processes []LbpmApplyProcess
	model.DB().Model(&LbpmApplyProcess{}).Where("TemplateFormId = ? AND ItemId = ?", templateFormId, itemId).Order("CreatedAt DESC").Find(&processes)

	return processes
}

// Update 更新流程部分字段
func (lp *LbpmApplyProcess) Update(tx *gorm.DB) error {
	return tx.Select("ItemId", "ItemTableName", "ItemTableStatusField", "Param", "ProcessFieldValue", "Status", "ApplyAt").Updates(&lp).Error
}

// UpdateProcessFieldValue 更新流程的条件字段值
func (lp *LbpmApplyProcess) UpdateProcessFieldValue() error {
	return model.DB().Select("ProcessFieldValue").Updates(&lp).Error
}

// UpdateColumn 更新流程的字段值
func (lp *LbpmApplyProcess) UpdateColumn(column string, value interface{}) error {
	return model.DB().Model(&LbpmApplyProcess{}).Where("FormInstanceId = ?", lp.FormInstanceId).Update(column, value).Error
}

// TransactionUpdateColumn 更新流程的字段值
func (lp *LbpmApplyProcess) TransactionUpdateColumn(tx *gorm.DB, column string, value interface{}) error {
	return tx.Model(&LbpmApplyProcess{}).Where("FormInstanceId = ?", lp.FormInstanceId).Update(column, value).Error
}
func (lp *LbpmApplyProcess) TransactionUpdates(tx *gorm.DB, val map[string]interface{}) error {
	return tx.Model(&LbpmApplyProcess{}).Where("FormInstanceId = ?", lp.FormInstanceId).Updates(val).Error
}

// TransactionDelete 删除流程
func (lp *LbpmApplyProcess) TransactionDelete(tx *gorm.DB) error {
	return tx.Where("FormInstanceId = ?", lp.FormInstanceId).Delete(&LbpmApplyProcess{}).Error
}

// GetRelationItem 查询流程相关的数据
func (lp *LbpmApplyProcess) GetRelationItem() map[string]interface{} {
	var data map[string]interface{}
	model.DB().Table(lp.ItemTableName).Where("Id = ?", lp.ItemId).Scan(&data)
	return data
}

// TransactionDeleteRelationItem 删除流程相关的数据
func (lp *LbpmApplyProcess) TransactionDeleteRelationItem(tx *gorm.DB) error {
	return tx.Exec(fmt.Sprintf("DELETE FROM %s WHERE Id = %v", lp.ItemTableName, lp.ItemId)).Error
}

// UpdateStatus 更新流程状态
func (lp *LbpmApplyProcess) UpdateStatus(status int64) error {
	return model.DB().Model(&LbpmApplyProcess{}).Where("FormInstanceId = ?", lp.FormInstanceId).Updates(map[string]interface{}{
		"Status": status,
		"DoneAt": time.Now().Format(model.TimeFormat),
	}).Error
}

// GetWorkOrderProcessForUserId 获取 和 userId 相关状态 工单
func (lp *LbpmApplyProcess) GetWorkOrderProcessForUserId(templateFormIds []string, statuses []int64, userId int64, startAt, endAt time.Time) ([]LbpmApplyProcess, error) {
	var rsp []LbpmApplyProcess

	tx := model.DB().Model(&LbpmApplyProcess{}).Where("TemplateFormId IN ? AND Status IN ?",
		templateFormIds,
		statuses,
	).Where("EXISTS (?)", model.DB().Model(&LbpmApplyProcessHasHandler{}).Where("UserId = ? AND lbpm_apply_processes.FormInstanceId = lbpm_apply_process_has_handlers.FormInstanceId ", userId))

	if !startAt.IsZero() {
		tx.Where("ApplyAt >= ?", startAt)
	}

	if !endAt.IsZero() {
		tx.Where("ApplyAt < ?", endAt)
	}

	err := tx.Scan(&rsp).Error
	return rsp, err
}

// GetApplyUserIdProcess 获取 流程发起人 相关的流程
func (lp *LbpmApplyProcess) GetApplyUserIdProcess(templateFormIds []string, userId int64, startAt, endAt time.Time) ([]LbpmApplyProcess, error) {
	var rsp []LbpmApplyProcess

	tx := model.DB().Model(&LbpmApplyProcess{}).
		Where("ApplyUserId = ?", userId).
		Where("TemplateFormId IN ?", templateFormIds)

	if !startAt.IsZero() {
		tx.Where("ApplyAt >= ?", startAt)
	}

	if !endAt.IsZero() {
		tx.Where("ApplyAt < ?", endAt)
	}

	err := tx.Scan(&rsp).Error
	if err != nil {
		return nil, err
	}

	return rsp, nil
}

// GetHandlerByItemId 获取流程流转至哪个人
func (lp *LbpmApplyProcess) GetHandlerByItemId(itemTableName string, itemId int64) (LbpmApplyProcessHasHandler, error) {
	var rsp LbpmApplyProcessHasHandler
	err := model.DB().Model(&LbpmApplyProcessHasHandler{}).Where("FormInstanceId IN (?)",
		model.DB().Model(&LbpmApplyProcess{}).Select("FormInstanceId").Where("ItemTableName = ? AND ItemId = ?", itemTableName, itemId)).Order("StartAt DESC").First(&rsp).Error

	return rsp, err
}

// GetFormInstanceId 获取formInstanceId
func (lp *LbpmApplyProcess) GetFormInstanceId(templateFormId string, tableName string, itemId int64) error {
	return model.DB().Model(&LbpmApplyProcess{}).Where("ItemTableName = ? AND ItemId = ? AND TemplateFormId = ?", tableName, itemId, templateFormId).Scan(lp).Error
}

func (lp *LbpmApplyProcess) List(itemId int64, tableName, templateFormId string, status []int64, paginator model.Paginator) []LbpmApplyProcess {
	var processes []LbpmApplyProcess
	tx := model.DB().Model(&LbpmApplyProcess{})
	if itemId > 0 {
		tx = tx.Where("ItemId = ?", itemId)
	}
	if tableName != "" {
		tx = tx.Where("ItemTableName = ?", tableName)
	}

	if templateFormId != "" {
		tx = tx.Where("TemplateFormId = ?", templateFormId)
	}

	if len(status) > 0 {
		tx = tx.Where("Status IN ?", status)
	}

	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	tx.Order("CreatedAt DESC").Find(&processes)

	return processes
}

// IsExistProcess 是否存在已发起的流程
func (lp *LbpmApplyProcess) IsExistProcess(itemId int64, tableName string) bool {
	var count int64
	model.DB().Model(&LbpmApplyProcess{}).Where("ItemTableName = ? AND ItemId = ? AND Status = ?", tableName, itemId, util.ProcessStatusForDoing).Count(&count)

	return count > 0
}

// IsExistNormalProcess 是否存在已发起未废弃的流程
func (lp *LbpmApplyProcess) IsExistNormalProcess(itemId int64, tableName string) bool {
	var count int64
	model.DB().Model(&LbpmApplyProcess{}).Where("ItemTableName = ? AND ItemId = ? AND Status != ?", tableName, itemId, util.ProcessStatusForAbandon).Count(&count)

	return count > 0
}

// GetProcessByTemplateFormIds 根据模板id获取流程
func (lp *LbpmApplyProcess) GetProcessByTemplateFormIds(groupId int64, templateFormIds []string, statuses []int64) ([]LbpmApplyProcess, error) {
	var rsp []LbpmApplyProcess
	err := model.DB().Model(&LbpmApplyProcess{}).Where("TopCorporationId=? AND TemplateFormId IN ? AND Status IN ?", groupId, templateFormIds, statuses).Find(&rsp).Error

	return rsp, err
}

// GetProcessByModelId 根据模板id获取流程
func (lp *LbpmApplyProcess) GetProcessByModelId(topCorporationId int64, modeId string, statuses []int64) []LbpmApplyProcess {
	var processes []LbpmApplyProcess
	model.DB().Model(&LbpmApplyProcess{}).Where("TopCorporationId = ? AND ModelId = ? AND Status IN ?", topCorporationId, modeId, statuses).Find(&processes)

	return processes
}

// GetWarningProcessByModelId 根据模板id获取可以告警的流程
func (lp *LbpmApplyProcess) GetWarningProcessByModelId(topCorporationId int64, modeId string, statuses []int64) []LbpmApplyProcess {
	var processes []LbpmApplyProcess
	model.DB().Model(&LbpmApplyProcess{}).Where("TopCorporationId = ? AND ModelId = ? AND Status IN ? AND IsPushWarningNotify = ?", topCorporationId, modeId, statuses, util.StatusForTrue).Find(&processes)

	return processes
}

func (lp *LbpmApplyProcess) GetBy(processId, title, applyUserName, currentHandler, templateFormId, modelId string, corporationId int64, status []int64, paginator model.Paginator) ([]LbpmApplyProcess, int64) {
	var processes []LbpmApplyProcess
	tx := model.DB().Model(&LbpmApplyProcess{}).Where("Status != ?", util.ProcessStatusForDraft)
	if processId != "" {
		tx = tx.Where("ProcessId LIKE ?", "%"+processId+"%")
	}
	if title != "" {
		tx = tx.Where("Title LIKE ?", "%"+title+"%")
	}

	if applyUserName != "" {
		tx = tx.Where("ApplyUserName LIKE ?", "%"+applyUserName+"%")
	}

	if currentHandler != "" {
		tx = tx.Where("CurrentHandlerUserName LIKE ?", "%"+currentHandler+"%")
	}

	if modelId != "" {
		tx = tx.Where("ModelId = ?", modelId)
	}

	if templateFormId != "" {
		tx = tx.Where("TemplateFormId = ?", templateFormId)
	}

	if corporationId > 0 {
		tx = tx.Where("CorporationId = ?", corporationId)
	}

	if len(status) > 0 {
		tx = tx.Where("Status IN ?", status)
	}
	var count int64
	tx.Count(&count)

	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	tx.Order("CreatedAt DESC").Find(&processes)

	return processes, count
}

func (lp *LbpmApplyProcess) GetAccidentProcessFormStep() int64 {
	var formStep int64
	if lp.ItemTableName != "" && lp.ItemId > 0 {
		model.DB().Table(lp.ItemTableName).Where("Id = ?", lp.ItemId).Select("FormStep").Scan(&formStep)
	}

	return formStep
}

type LbpmApplyProcessHasHandler struct {
	model.PkId
	FormInstanceId int64            `json:"FormInstanceId" gorm:"column:forminstanceid;type:bigint;comment:申请表单实例ID"`             //申请表单实例ID  关联LbpmApplyProcess表的FormInstanceId
	ProcessId      string           `json:"ProcessId" gorm:"column:processid;type:varchar;comment:流程的ID"`                         //流程的ID  发起流程时蓝凌返回的流程标识 关联LbpmApplyProcess表的ProcessId
	Node           string           `json:"Node" json:"Node" gorm:"column:node;type:varchar;comment:处理节点"`                        //处理节点
	NodeType       int64            `json:"NodeType" gorm:"column:nodetype;type:smallint;comment:节点类型 1审批节点   2抄送节点"`             //节点类型
	ApprovalType   int64            `json:"ApprovalType" gorm:"column:approvaltype;type:smallint;default:1;comment:审批方式 1串行 2并行"` //审批方式 1串行 2并行
	UserId         int64            `json:"UserId" gorm:"column:userid;type:integer;comment:处理账号ID"`
	UserName       string           `json:"UserName" gorm:"column:username;type:varchar;comment:处理人姓名"`
	UserMobile     string           `json:"UserMobile" gorm:"column:usermobile;type:varchar;comment:处理人手机号"`
	ProcessParam   model.JSON       `json:"ProcessParam" gorm:"column:processparam;type:json;comment:处理人填写的处理参数和值"`                               //处理人填写的处理参数和值
	Status         int64            `json:"Status" gorm:"column:status;type:smallint;default:0;comment:处理状态 1正在处理 2处理完成  3跳过 （多人并行，一人审批后，其他人跳过）"` //处理状态  1正在处理 2处理完成  3跳过 （多人并行，一人审批后，其他人跳过）
	Result         string           `json:"Result" gorm:"column:result;type:varchar;comment:处理结果"`                                                //处理结果
	StartAt        *model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:处理开始时间"`                                          //处理开始时间
	EndAt          *model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:处理结束时间"`                                              //处理结束时间
	ClientType     string           `json:"ClientType" gorm:"column:clienttype;type:varchar;comment:审批客户端 mobile:移动端  pc:电脑"`
	model.Timestamp
}

// BeforeCreate 钩子方法
func (h *LbpmApplyProcessHasHandler) BeforeCreate(tx *gorm.DB) error {
	h.Id = model.Id()
	now := model.LocalTime(time.Now())
	h.StartAt = &now
	//h.Status = util.ProcessStatusForDoing
	return nil
}

// Create 创建
func (h *LbpmApplyProcessHasHandler) Create() error {
	return model.DB().Create(&h).Error
}

// TransactionCreate 运用事务方式创建
func (h *LbpmApplyProcessHasHandler) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&h).Error
}

// Update 更新部分字段
func (h *LbpmApplyProcessHasHandler) Update() error {
	return model.DB().Select("ProcessParam", "Status", "Result", "EndAt", "ClientType").Updates(&h).Error
}

// ConcurrenceUpdate 并行审批更新
func (h *LbpmApplyProcessHasHandler) ConcurrenceUpdate() error {
	return model.DB().Model(&LbpmApplyProcessHasHandler{}).Where("Id != ? AND FormInstanceId = ? AND Node = ? AND ApprovalType = ? AND NodeType = ?", h.Id, h.FormInstanceId, h.Node, h.ApprovalType, h.NodeType).Updates(map[string]interface{}{
		"status": util.ProcessNodeHandleStatusForOver,
		"endat":  time.Now().Format(model.TimeFormat),
	}).Error
}

// TerminateOrAbandonUpdate 撤回或者废弃流程更新审批状态
func (h *LbpmApplyProcessHasHandler) TerminateOrAbandonUpdate(formInstanceId int64) error {
	return model.DB().Model(&LbpmApplyProcessHasHandler{}).Where("FormInstanceId =  ? AND NodeType = ? AND Status = ?", formInstanceId, util.ProcessNodeTypeForApprove, util.ProcessNodeHandleStatusForDoing).Updates(map[string]interface{}{
		"status": util.ProcessNodeHandleStatusForCancel,
		"endat":  time.Now().Format(model.TimeFormat),
	}).Error
}

// GetConcurrenceHandler 获取并行审批节点处理人
func (h *LbpmApplyProcessHasHandler) GetConcurrenceHandler() []LbpmApplyProcessHasHandler {
	var handlers []LbpmApplyProcessHasHandler
	model.DB().Model(&LbpmApplyProcessHasHandler{}).Where("Id != ? AND FormInstanceId = ? AND Node = ? AND ApprovalType = ? AND NodeType = ?", h.Id, h.FormInstanceId, h.Node, h.ApprovalType, h.NodeType).Find(&handlers)

	return handlers
}

// IsProcessHandler 是否是流程处理人（发起人、审批人、抄送人、当前正在审批的人）
func (h *LbpmApplyProcessHasHandler) IsProcessHandler(userId, formInstanceId, nodeType, status int64) bool {
	var count int64
	tx := model.DB().Model(&LbpmApplyProcessHasHandler{}).Where("FormInstanceId = ? AND UserId = ? AND NodeType = ?", formInstanceId, userId, nodeType)
	if status > 0 {
		tx = tx.Where("Status = ?", status)
	}
	tx.Count(&count)

	return count > 0
}

// ProcessHandlerForUser 用户当前正在审批的流程节点信息
func (h *LbpmApplyProcessHasHandler) ProcessHandlerForUser(userId, formInstanceId int64) error {
	return model.DB().Model(&LbpmApplyProcessHasHandler{}).
		Where("FormInstanceId = ? AND UserId = ? AND Status = ? AND NodeType = ?", formInstanceId, userId, util.ProcessNodeHandleStatusForDoing, util.ProcessNodeTypeForApprove).
		Order("StartAt DESC").First(&h).Error
}

// IsProcessRelater 是否是流程相关人
func (h *LbpmApplyProcessHasHandler) IsProcessRelater(userId, formInstanceId int64) bool {
	var count int64
	model.DB().Model(&LbpmApplyProcessHasHandler{}).Where("FormInstanceId = ? AND UserId = ? ", formInstanceId, userId).Count(&count)
	return count > 0
}

// GetProcessHandlers 获取流程最近的处理节点处理人
func (h *LbpmApplyProcessHasHandler) GetProcessHandlers(formInstanceId int64) []LbpmApplyProcessHasHandler {
	var handlers []LbpmApplyProcessHasHandler
	model.DB().Model(&LbpmApplyProcessHasHandler{}).Where("FormInstanceId = ? ", formInstanceId).
		Where("Node = (?)", model.DB().Model(&LbpmApplyProcessHasHandler{}).Select("Node").Where("FormInstanceId = ? AND NodeType = ? AND Status = ?", formInstanceId, util.ProcessNodeTypeForApprove, util.ProcessNodeHandleStatusForDoing).Order("StartAt DESC").Limit(1)).
		Find(&handlers)

	return handlers
}

// GetApprovingHandler 获取所有[审批人]
func (h *LbpmApplyProcessHasHandler) GetApprovingHandler(templateFormId string, tableName string, itemId int64) ([]LbpmApplyProcessHasHandler, error) {
	var rsp []LbpmApplyProcessHasHandler
	err := model.DB().Model(&LbpmApplyProcessHasHandler{}).
		Where("FormInstanceId = (?) ",
			model.DB().Model(&LbpmApplyProcess{}).Select("FormInstanceId").Where("ItemTableName = ? AND ItemId = ? AND TemplateFormId = ?", tableName, itemId, templateFormId)).
		Where("NodeType = ?", util.ProcessNodeTypeForApprove).
		Order("StartAt ASC").Find(&rsp).Error
	return rsp, err
}

// GetAllHandlerByFormInstanceId 获取所有流程相关人
func (h *LbpmApplyProcessHasHandler) GetAllHandlerByFormInstanceId(formInstanceId int64) []LbpmApplyProcessHasHandler {
	var handlers []LbpmApplyProcessHasHandler
	model.DB().Model(&LbpmApplyProcessHasHandler{}).Where("FormInstanceId = ?", formInstanceId).Order("StartAt ASC").Find(&handlers)
	return handlers
}

// 同步更新流程表中当前处理人字段
func UpdateCurrentUserName() {
	var processes []LbpmApplyProcess
	err := model.DB().Model(&LbpmApplyProcess{}).Where("CurrentHandlerUserName=? AND Status=?", "", util.ProcessStatusForDoing).Scan(&processes).Error
	if err != nil {
		log.Println("============get processes err = ", err)
		return
	}

	for _, process := range processes {

		var handlers []LbpmApplyProcessHasHandler
		model.DB().Model(&LbpmApplyProcessHasHandler{}).Where("FormInstanceId=? AND Status=? AND NodeType=?", process.FormInstanceId, util.ProcessNodeHandleStatusForDoing, util.ProcessNodeTypeForApprove).Scan(&handlers)

		// 一个为串行审批人 多个为并行审批人
		var names []string
		for _, handle := range handlers {
			names = append(names, handle.UserName)
		}

		err := process.UpdateColumn("CurrentHandlerUserName", strings.Join(names, ","))
		if err != nil {
			log.Println("=============UpdateColumn err = ", err)
		}
	}

}

// FindBy 根据id查找
func (h *LbpmApplyProcessHasHandler) FindBy(id int64) (LbpmApplyProcessHasHandler, error) {
	var rsp LbpmApplyProcessHasHandler
	err := model.DB().Model(&LbpmApplyProcessHasHandler{}).Where("Id = ? ", id).Find(&rsp).Error

	return rsp, err
}

func (h *LbpmApplyProcessHasHandler) IsExistHandler(formInstanceId, nodeType, userId, status int64, nodeName string) bool {
	var count int64
	model.DB().Model(&LbpmApplyProcessHasHandler{}).Where("FormInstanceId = ? AND NodeType = ? AND Node = ? AND UserId = ? AND Status = ?", formInstanceId, nodeType, nodeName, userId, status).Count(&count)

	return count > 0
}

// TransactionDelete 运用事务删除
func (h *LbpmApplyProcessHasHandler) TransactionDelete(tx *gorm.DB, formInstanceId int64) error {
	return tx.Where("FormInstanceId = ?", formInstanceId).Delete(&LbpmApplyProcessHasHandler{}).Error
}

func (h *LbpmApplyProcessHasHandler) TransactionDeleteByUser(tx *gorm.DB, formInstanceId, userId, nodeType, isHandle int64) error {
	exec := tx.Where("FormInstanceId = ? AND UserId = ?", formInstanceId, userId).Where("NodeType = ?", nodeType)
	if isHandle == util.StatusForTrue {
		exec = exec.Where("status > ?", util.ProcessNodeHandleStatusForDoing)
	}
	if isHandle == util.StatusForFalse {
		exec = exec.Where("status = ?", util.ProcessNodeHandleStatusForDoing)
	}

	return exec.Delete(&LbpmApplyProcessHasHandler{}).Error
}

func (h *LbpmApplyProcessHasHandler) GetClientTypeCount(templateFormIds []string, itemIds []int64, clientType string) int64 {
	var count int64
	model.DB().Model(&LbpmApplyProcessHasHandler{}).Where("FormInstanceId IN (?)", model.DB().Model(&LbpmApplyProcess{}).Select("FormInstanceId").Where("TemplateFormId IN ?", templateFormIds).Where("ItemId IN ?", itemIds)).Where("ClientType = ?", clientType).Count(&count)

	return count
}

type LbpmApplyProcessDeleteLog struct {
	model.PkId
	FormInstanceId   int64      `json:"FormInstanceId" gorm:"column:forminstanceid;type:bigint;primaryKey;comment:申请表单实例ID"`                         //申请表单实例ID
	ItemId           int64      `json:"ItemId" gorm:"column:itemid;type:bigint;comment:关联的具体的申请实例表的ID"`                                              //关联的具体的申请实例表的ID  具体是哪张表由TableName来确定
	ProcessId        string     `json:"ProcessId" gorm:"column:processid;type:varchar;uniqueIndex:lbpm_apply_process_id_unique_index;comment:流程的ID"` //流程的ID  发起流程时蓝凌返回的流程标识
	TemplateFormId   string     `json:"TemplateFormId" gorm:"column:templateformid;type:varchar;comment:表单ID"`                                       //表单ID
	TemplateFormName string     `json:"TemplateFormName" gorm:"column:templateformname;type:varchar;comment:表单名称"`                                   //表单名称
	Title            string     `json:"Title" gorm:"column:title;type:varchar(100);comment:流程标题"`                                                    //流程标题
	Data             model.JSON `json:"Data" gorm:"column:data;type:json;comment:数据"`                                                                //发起审批时用户填的表单数据
	model.Timestamp
	model.OpUser
}

// BeforeCreate 钩子方法
func (h *LbpmApplyProcessDeleteLog) BeforeCreate(tx *gorm.DB) error {
	h.Id = model.Id()
	return nil
}

// Create 创建
func (h *LbpmApplyProcessDeleteLog) Create() error {
	return model.DB().Create(&h).Error
}
