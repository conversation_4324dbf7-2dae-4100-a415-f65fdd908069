package process

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
)

type DingTalkApplyProcess struct {
	model.PkId
	ProcessInstanceId    string           `json:"ProcessInstanceId" gorm:"column:processinstanceid;type:varchar;comment:流程实例ID;uniqueIndex:ding_talk_apply_process_instance_id_index"` //流程实例ID 发起流程时钉钉返回的流程实例标识
	ProcessInstanceTitle string           `json:"ProcessInstanceTitle" gorm:"column:processinstancetitle;type:varchar;comment:流程实例标题"`                                                 //流程实例标题
	ProcessBusinessId    string           `json:"ProcessBusinessId" gorm:"column:processbusinessid;type:varchar;default:'';comment:流程审批编号"`                                            // 流程审批编号 在钉钉上能对比
	TopCorporationId     int64            `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:顶级机构Id"`                                                          //顶级机构Id
	ItemId               int64            `json:"ItemId" gorm:"column:itemid;type:bigint;comment:关联的具体的申请实例表的ID"`                                                                      //关联的具体的申请实例表的ID  具体是哪张表由ItemTableName来确定
	ItemTableName        string           `json:"ItemTableName" gorm:"column:itemtablename;type:varchar;comment:数据表名称"`                                                                //数据表名称  关联的数据表的名称
	Param                model.JSON       `json:"Param" gorm:"column:param;type:json;comment:发起审批时用户填的表单数据"`                                                                           //发起审批时用户填的表单数据
	ProcessFieldValue    model.JSON       `json:"ProcessFieldValue" gorm:"column:processfieldvalue;type:json;comment:流程中用到的参数和值"`                                                      //流程中用到的参数和值
	ApplyUserId          int64            `json:"ApplyUserId" gorm:"column:applyuserid;type:integer;comment:申请人员ID"`                                                                   //申请人员ID
	ApplyUserName        string           `json:"ApplyUserName" gorm:"column:applyusername;type:varchar;comment:申请人姓名"`                                                                //申请人姓名
	ApplyUserMobile      string           `json:"ApplyUserMobile" gorm:"column:applyusermobile;type:varchar;comment:申请人手机号"`                                                           //申请人手机号
	ApplyDingTalkUserId  string           `json:"ApplyDingTalkUserId" gorm:"column:applydingtalkuserid;type:varchar;comment:申请人的钉钉userId"`                                             //申请人在钉钉中的userId
	ProcessCode          string           `json:"ProcessCode" gorm:"column:processcode;type:varchar;comment:审批模板的唯一标识"`                                                                //审批模板的唯一标识
	TemplateFormId       string           `json:"TemplateFormId" gorm:"column:templateformid;type:varchar;comment:表单模板ID"`                                                             //表单模板ID
	Status               int64            `json:"Status" gorm:"column:status;type:smallint;default:0;comment:流程状态 "`                                                                   //流程状态 1审批进行中  2审批完成  3审批撤销（取消）
	Result               int64            `json:"Result" gorm:"column:result;type:smallint;default:0;comment:流程审批结果 "`                                                                 //流程审批结果 0审批中 1审批通过  2审批拒绝
	ApplyAt              *model.LocalTime `json:"ApplyAt" gorm:"column:applyat;type:timestamp;comment:申请提交时间"`                                                                         //申请提交时间
	DoneAt               *model.LocalTime `json:"DoneAt" gorm:"column:doneat;type:timestamp;comment:申请审批完成的时间"`                                                                        //申请审批完成的时间
	model.Timestamp
}

// BeforeCreate 钩子方法
func (lp *DingTalkApplyProcess) BeforeCreate(tx *gorm.DB) error {
	lp.Id = model.Id()
	return nil
}

// TransactionCreate 创建
func (lp *DingTalkApplyProcess) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(lp).Error
}

func (lp *DingTalkApplyProcess) UpdateStatus(tx *gorm.DB) error {
	return tx.Select("Status", "Result", "DoneAt").Updates(lp).Error
}

func (lp *DingTalkApplyProcess) FirstByInstanceId(instanceId string) DingTalkApplyProcess {
	var process DingTalkApplyProcess
	model.DB().Model(&DingTalkApplyProcess{}).Where("ProcessInstanceId = ?", instanceId).First(&process)

	return process
}

// 未审批
func (lp *DingTalkApplyProcess) NotApproved() []DingTalkApplyProcess {
	var processes []DingTalkApplyProcess
	model.DB().Model(&DingTalkApplyProcess{}).Where("Status = ?", util.ApplyStatusForDoing).Find(&processes)

	return processes
}

func (lp *DingTalkApplyProcess) GetProcessesByItemId(itemTableName string, itemId int64) DingTalkApplyProcess {
	var processes DingTalkApplyProcess
	model.DB().Model(&DingTalkApplyProcess{}).Where("ItemTableName = ? AND ItemId = ?", itemTableName, itemId).First(&processes)

	return processes
}

type DingTalkApplyProcessHasHandler struct {
	model.PkId
	DingTalkApplyProcessId int64            `json:"DingTalkApplyProcessId" gorm:"column:dingtalkapplyprocessid;type:bigint;comment:申请表主键ID"` //申请表主键ID  关联DingTalkApplyProcess表的Id
	ProcessInstanceId      string           `json:"ProcessInstanceId" gorm:"column:processinstanceid;type:varchar;comment:流程实例ID"`           //流程实例ID 发起流程时钉钉返回的流程实例标识 关联DingTalkApplyProcess表的ProcessInstanceId
	NodeType               int64            `json:"NodeType" gorm:"column:nodetype;type:smallint;comment:节点类型"`                              //节点类型  1审批节点   2抄送节点  3发起节点
	UserId                 int64            `json:"UserId" gorm:"column:userid;type:integer;comment:处理人员ID"`                                 //处理人员ID
	UserName               string           `json:"UserName" gorm:"column:username;type:varchar;comment:处理人姓名"`                              //处理人姓名
	UserMobile             string           `json:"UserMobile" gorm:"column:usermobile;type:varchar;comment:处理人手机号"`                         //处理人手机号
	UserDingTalkUserId     string           `json:"UserDingTalkUserId" gorm:"column:userdingtalkuserid;type:varchar;comment:处理人的钉钉userId"`   //处理人在钉钉中的userId
	ProcessParam           model.JSON       `json:"ProcessParam" gorm:"column:processparam;type:json;comment:处理人填写的处理参数和值"`                  //处理人填写的处理参数和值
	Status                 int64            `json:"Status" gorm:"column:status;type:smallint;default:0;comment:处理状态"`                        //处理状态  1正在处理 2处理完成 3取消（说明当前节点有多个审批人并且是或签，其中一个人执行了审批，其他审批人会取消）
	Result                 string           `json:"Result" gorm:"column:result;type:varchar;default:;comment:处理结果"`                          //处理结果
	StartAt                *model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:处理开始时间"`                             //处理开始时间
	EndAt                  *model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:处理结束时间"`                                 //处理结束时间
	model.Timestamp
}

// BeforeCreate 钩子方法
func (h *DingTalkApplyProcessHasHandler) BeforeCreate(tx *gorm.DB) error {
	h.Id = model.Id()
	return nil
}

// TransactionCreate 创建
func (h *DingTalkApplyProcessHasHandler) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(h).Error
}

func (h *DingTalkApplyProcessHasHandler) Create() error {
	return model.DB().Create(h).Error
}

func (h *DingTalkApplyProcessHasHandler) UpdateStatus() error {
	return model.DB().Select("Status", "Result", "ProcessParam", "EndAt").Updates(h).Error
}

func (h *DingTalkApplyProcessHasHandler) FindHandlingBy(dingTalkApplyProcessId, userId int64) DingTalkApplyProcessHasHandler {
	var handler DingTalkApplyProcessHasHandler
	model.DB().Model(&DingTalkApplyProcessHasHandler{}).Where("DingTalkApplyProcessId = ? AND UserId = ? AND Status = ?", dingTalkApplyProcessId, userId, util.ApplyStatusForDoing).First(&handler)
	return handler
}
