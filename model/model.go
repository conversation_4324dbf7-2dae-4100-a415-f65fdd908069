package model

import (
	"app/org/scs/erpv2/api/config"
	protooetcorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"context"
	"fmt"
	"github.com/micro/go-micro/v2/client"
	log "github.com/micro/go-micro/v2/logger"
	"gorm.io/gorm"
	"strings"
	"sync"
	"time"
)

func DB() *gorm.DB {
	return config.Global.DbClient
}

var Id = func() int64 {
	return util.GenerateId()
}

type PkId struct {
	Id int64 `json:"Id" gorm:"column:id;type:bigint;primaryKey;comment:主键ID"`
}

type ExcelForm struct {
	RowIndex int `json:"RowIndex"`
	//Info  *xlsx.Row `json:"Info"`
	Error string `json:"Error"`
}

type CorpId struct {
	GroupCorporation
	CompanyCorporation
	BranchCorporation
	DepartmentCorporation
	FleetCorporation
}

type GetNameEnum uint8

const (
	GROUP      GetNameEnum = 1
	COMPANY    GetNameEnum = 2
	BRANCH     GetNameEnum = 3
	DEPARTMENT GetNameEnum = 4
	FLEET      GetNameEnum = 5
)

type CorpI interface {
	GetName(ctx context.Context, srv protooetcorporation.CorporationService)
}

// GetNames 根据需求获取相应机构的信息
func (c *CorpId) GetNames(ctx context.Context, client client.Client, items []GetNameEnum) {

	if len(items) == 0 {
		return
	}

	cli := protooetcorporation.NewCorporationService("oet.scs.srv.public", client)

	tmp := map[GetNameEnum]CorpI{
		GROUP:      &c.GroupCorporation,
		COMPANY:    &c.CompanyCorporation,
		BRANCH:     &c.BranchCorporation,
		DEPARTMENT: &c.DepartmentCorporation,
		FLEET:      &c.FleetCorporation,
	}
	var wg sync.WaitGroup

	for _, enum := range items {
		wg.Add(1)
		go func(enum GetNameEnum) {
			defer wg.Done()
			tmp[enum].GetName(ctx, cli)
		}(enum)
	}
	wg.Wait()
}

type GroupCorporation struct {
	GroupId int64  `json:"GroupId"      gorm:"column:groupid;type:bigint;comment:集团Id"` //集团Id
	Group   string `json:"Group"        gorm:"-"`                                       //集团
}

type CompanyCorporation struct {
	CompanyId int64  `json:"CompanyId"    gorm:"column:companyid;type:bigint;comment:公司Id"` //公司Id
	Company   string `json:"Company"      gorm:"-"`                                         //公司
}

type BranchCorporation struct {
	BranchId int64  `json:"BranchId"     gorm:"column:branchid;type:bigint;comment:分公司Id"` //分公司Id
	Branch   string `json:"Branch"       gorm:"-"`                                         //分公司
}

type DepartmentCorporation struct {
	DepartmentId int64  `json:"DepartmentId" gorm:"column:departmentid;type:bigint;comment:部门Id"` //部门Id
	Department   string `json:"Department"   gorm:"-"`                                            //部门
}

type FleetCorporation struct {
	FleetId int64  `json:"FleetId"      gorm:"column:fleetid;type:bigint;comment:车队Id"` //车队Id
	Fleet   string `json:"Fleet"        gorm:"-"`                                       //车队Id
}

func (g *GroupCorporation) GetName(ctx context.Context, srv protooetcorporation.CorporationService) {
	g.Group = RpcGetCorporation(ctx, srv, g.GroupId)
}
func (c *CompanyCorporation) GetName(ctx context.Context, srv protooetcorporation.CorporationService) {
	c.Company = RpcGetCorporation(ctx, srv, c.CompanyId)
}
func (b *BranchCorporation) GetName(ctx context.Context, srv protooetcorporation.CorporationService) {
	b.Branch = RpcGetCorporation(ctx, srv, b.BranchId)
}
func (d *DepartmentCorporation) GetName(ctx context.Context, srv protooetcorporation.CorporationService) {
	d.Department = RpcGetCorporation(ctx, srv, d.DepartmentId)
}
func (f *FleetCorporation) GetName(ctx context.Context, srv protooetcorporation.CorporationService) {
	f.Fleet = RpcGetCorporation(ctx, srv, f.FleetId)
}

type TimestampTz struct {
	CreatedAt     time.Time `json:"-"         gorm:"column:createdat;type:timestamptz;default:current_timestamp"` // 创建时间
	CreatedAtUnix int64     `json:"CreatedAt" gorm:"-:all"`                                                       // 创建时间
	UpdatedAt     time.Time `json:"-"         gorm:"column:updatedat;type:timestamptz;default:current_timestamp"` // 更新时间
	UpdatedAtUnix int64     `json:"UpdatedAt" gorm:"-:all"`                                                       // 更新时间
}

func (t *TimestampTz) ToUnix() {
	t.UpdatedAtUnix = t.UpdatedAt.Unix()
	t.CreatedAtUnix = t.CreatedAt.Unix()
}

type Timestamp struct {
	CreatedAt LocalTime `json:"CreatedAt"         gorm:"column:createdat;type:timestamp;comment:创建时间"` // 创建时间
	UpdatedAt LocalTime `json:"UpdatedAt"         gorm:"column:updatedat;type:timestamp;comment:更新时间"` // 更新时间
}

// OpUser 数据的创建人
type OpUser struct {
	OpUserId   int64  `json:"OpUserId" gorm:"column:opuserid;type:bigint;comment:操作人ID"`    //创建人
	OpUserName string `json:"OpUserName" gorm:"column:opusername;type:varchar;comment:操作人"` //创建人
}

func (op *OpUser) ParseOpUser(ctx context.Context) {
	op.OpUserId = auth.User(ctx).GetUserId()
	op.OpUserName = auth.User(ctx).GetUser().GetName()
}

// EditOpUser  数据的创建人
type EditOpUser struct {
	EditUserId   int64  `json:"EditUserId" gorm:"column:edituserid;type:bigint;comment:操作人ID"`    //更新人
	EditUserName string `json:"EditUserName" gorm:"column:editusername;type:varchar;comment:操作人"` //更新人
}

func (op *EditOpUser) ParseEditOpUser(ctx context.Context) {
	op.EditUserId = auth.User(ctx).GetUserId()
	op.EditUserName = auth.User(ctx).GetUser().GetName()
}

var NoPagination = Paginator{Offset: 0, Limit: 0}

type Paginator struct {
	Offset int `json:"Offset"`
	Limit  int `json:"Limit"`
}

type Sortable struct {
	SortField string `json:"SortField"`
	Order     string `json:"Order"`
}

func RpcGetCorporation(ctx context.Context, srv protooetcorporation.CorporationService, corpId int64) string {

	if corpId == 0 {
		return ""
	}
	getById, err := srv.GetById(ctx, &protooetcorporation.GetCorporationByIdRequest{
		Id: corpId,
	})
	if err != nil {
		log.Errorf("corpId = %d, cli1.GetById err= %v", corpId, err.Error())
		return ""
	}
	if getById == nil {
		log.Errorf("corpId = %d, getById == nil", corpId)
		return ""
	}
	return getById.Name
}

func CreateIndex(table string, columns []string) {

	indexName := fmt.Sprintf("%s_%s_idx", table, strings.Join(columns, "_"))

	DB().Table(table).Exec(fmt.Sprintf("CREATE INDEX %s ON %s (%s)", indexName, table, strings.Join(columns, ",")))
}

func CreateUniqueIndex(table string, columns []string) {

	indexName := fmt.Sprintf("%s_%s_uidx", table, strings.Join(columns, "_"))

	DB().Table(table).Exec(fmt.Sprintf("CREATE UNIQUE INDEX %s ON %s (%s)", indexName, table, strings.Join(columns, ",")))
}

func IsExistDataBaseTable(name string) bool {
	query := fmt.Sprintf("SELECT CASE "+
		"WHEN COALESCE(TO_REGCLASS('%s'),'0')  = '0' "+
		"THEN '0' "+
		"ELSE '1' "+
		"END", name)
	var item string
	err := DB().Raw(query).Scan(&item).Error
	if nil != err || "0" == item {
		return false
	}
	return true
}

type LinRoadInfo struct {
	LineId   int64
	LineName string
}
