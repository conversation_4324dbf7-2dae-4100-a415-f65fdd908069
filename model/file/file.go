package file

import "app/org/scs/erpv2/api/model"

type File struct {
	model.PkId
	CorporationId    int64  `json:"CorporationId" gorm:"column:corporationid;type:bigint;comment:所属机构Id"`     //所属机构Id
	TopCorporationId int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:顶级机构"` //顶级机构
	Name             string `json:"Name" gorm:"column:name;type:varchar(255);comment:文件名"`                    //文件名
	Suffix           string `json:"Suffix" gorm:"column:suffix;type:varchar(50);comment:文件后缀"`                //文件后缀
	Path             string `json:"Path" gorm:"column:path;type:varchar(255);comment:文件存储路径"`                 //文件存储路径
	Size             int64  `json:"Size" gorm:"column:size;type:integer;comment:文件大小"`                        //文件大小
	Progress         int64  `json:"Progress" gorm:"column:progress;type:integer;comment:上传进度"`                //上传进度
	model.Timestamp
}

func (f *File) Create() (int64, error) {
	f.Id = model.Id()
	err := model.DB().Create(&f).Error

	return f.Id, err
}

func (f *File) FindBy(id int64) File {
	if id == 0 {
		return File{}
	}
	var file File
	model.DB().Model(&File{}).Where("Id = ?", id).First(&file)
	return file
}

func (f *File) UpdateProgress(id, progress int64) error {
	return model.DB().Model(&File{}).Where("Id = ?", id).UpdateColumn("progress", progress).Error
}
