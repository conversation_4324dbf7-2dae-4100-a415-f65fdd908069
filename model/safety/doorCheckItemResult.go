package safety

import (
	"app/org/scs/erpv2/api/model"
	"encoding/json"
	"gorm.io/gorm"
	"time"
)

type DoorCheckItemResult struct {
	model.PkId

	model.GroupCorporation
	model.CompanyCorporation
	model.BranchCorporation
	model.DepartmentCorporation
	model.FleetCorporation

	License     string `json:"License"        gorm:"column:license;comment:车辆车牌号;type:varchar(50)"`    // 车辆车牌号
	VehicleCode string `json:"VehicleCode"    gorm:"column:vehiclecode;comment:车辆编号;type:varchar(50)"` // 车辆编号
	LineId      int64  `json:"LineId"      gorm:"column:lineid;comment:线路从排班中获取;type:integer"`         // -- 默认车属线路 也可以是选择的线路 v0.2.8 线路从排班中获取
	Line        string `json:"Line"        gorm:"column:line;comment:线路名;type:varchar(50)"`            // -- 线路名

	StaffId       int64  `json:"StaffId"        gorm:"column:staffid;comment:司机员工id;type:integer"`          // 司机员工id
	StaffName     string `json:"StaffName"      gorm:"column:staffname;comment:司机名;type:varchar(50)"`       // 司机名
	Type          int64  `json:"Type"           gorm:"column:type;comment:检测类型 1自检 2抽检;type:smallint"`      // 检测类型 1自检 2抽检
	AdminUserId   int64  `json:"AdminUserId"   gorm:"column:adminuserid;comment:抽检人员工id 自检为0;type:integer"` // 抽检人员工id 自检为0
	AdminUserName string `json:"AdminUserName" gorm:"column:adminusername;comment:抽检人名;type:varchar(50)"`   // 抽检人名 自检为''

	ReportUserId   int64  `json:"ReportUserId" gorm:"column:reportuserid;comment:上报人;type:integer;default:0"`           // 上报人 此项被上报才有值
	ReportUserName string `json:"ReportUserName" gorm:"column:reportusername;comment:上报人名;type:varchar(50);default:''"` // 上报人名

	ResolveUserId   int64  `json:"ResolveUserId" gorm:"column:resolveuserid;comment:解决人;type:integer;default:0"`           // 解决人 此项被解决（整改）才有值
	ResolveUserName string `json:"ResolveUserName" gorm:"column:resolveusername;comment:解决人名;type:varchar(50);default:''"` // 解决人名

	FkRecordId int64 `json:"FkRecordId" gorm:"column:fkrecordid;comment:归属记录id;type:bigint"` // 归属记录id 多对一fk -> door_check_record.Id

	FkItemId int64  `json:"FkItemId" gorm:"column:fkitemid;comment:门检项目id;type:bigint"`     // (所属)门检项目id  fk -> door_check_items.Id
	ItemName string `json:"ItemName" gorm:"column:itemname;comment:门检项目名;type:varchar(60)"` // 门检项目名 door_check_items.Name

	//Result   int64  `json:"Result" gorm:"column:result;type:smallint"`        // 该门检项目结果 1正常 2异常

	Status int64 `json:"Status" gorm:"column:status;comment:该门检项目状态;type:smallint"` // 该门检项目状态 1正常 2待整改 4待解决 8已解决 16已关闭

	ResultJson model.JSON `json:"ResultJson" gorm:"column:resultjson;comment:整改内容;type:json"`
	model.Timestamp
}
type ResultJson struct {
	NotRectified NotRectified // (异常)未整改的内容 | (正常)提交的内容
	Rectified    []Rectified  // (异常)已整改的内容 | (正常)补充提交的内容->暂无
}

type NotRectified struct {
	Labels []ResultJsonLabel
	Files  []ResultJsonFile
	Remark string // 评论 留言
}

type Rectified struct {
	Files     []ResultJsonFile
	Remark    string // 评论 留言
	CreatedAt int64  // 整改、添加、覆盖时间
}

type ResultJsonLabel struct {
	FkLabelId int64  // 标签id
	Name      string // 标签名
}
type ResultJsonFile struct {
	FkFileId int64  // 文件id
	Url      string // url
	Type     int64  // 文件类型(兼容老数据0也当作图片处理) 1图片 2视频
	Path     string // 文件相对路径
}

// 每一项门检结果
type ItemResult struct {
	FkItemId int64  // 门检项目id
	ItemName string // 门检项目名
	Status   int64  // 1正常 2待整改 4待解决 8已解决 16已关闭

	NoticeText string // 上报通知文本

	ReportUserId   int64  // 上报人 此项被上报才有值
	ReportUserName string // 上报人名

	ResolveUserId   int64  // 解决人 此项被解决（整改）才有值
	ResolveUserName string // 解决人名

	ResultJson ResultJson // 如果没有可以为空

	NoticeId int64 // 上报时需要存notice，外部需要获取这个值 所以先生成id
}

func (d *DoorCheckItemResult) BeforeCreate(tx *gorm.DB) (err error) {
	if d.Id == 0 {
		d.Id = model.Id()
	}
	return
}

func (d *DoorCheckItemResult) AddResultAndRecord(checkForm, recordResult int64, statusCount string, items []ItemResult) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {

		// 添加一条门检记录记录
		record := DoorCheckRecords{
			PkId:                  model.PkId{Id: d.Id},
			GroupCorporation:      model.GroupCorporation{GroupId: d.GroupId},
			CompanyCorporation:    model.CompanyCorporation{CompanyId: d.CompanyId},
			BranchCorporation:     model.BranchCorporation{BranchId: d.BranchId},
			DepartmentCorporation: model.DepartmentCorporation{DepartmentId: d.DepartmentId},
			FleetCorporation:      model.FleetCorporation{FleetId: d.FleetId},
			License:               d.License,
			VehicleCode:           d.VehicleCode,
			LineId:                d.LineId,
			Line:                  d.Line,
			StaffId:               d.StaffId,
			StaffName:             d.StaffName,
			Type:                  d.Type,
			AdminUserId:           d.AdminUserId,
			AdminUserName:         d.AdminUserName,
			CheckForm:             checkForm,
			Result:                recordResult,
			StatusCount:           statusCount,
			Timestamp:             model.Timestamp{},
		}
		err := tx.Model(&DoorCheckRecords{}).Create(&record).Error
		if err != nil {
			return err
		}

		for _, item := range items {

			itemResult := *d
			itemResult.Id = model.Id()

			itemResult.FkRecordId = record.Id
			itemResult.FkItemId = item.FkItemId
			itemResult.ItemName = item.ItemName

			itemResult.Status = item.Status

			itemResult.ReportUserId = item.ReportUserId
			itemResult.ReportUserName = item.ReportUserName
			itemResult.ResolveUserId = item.ResolveUserId
			itemResult.ResolveUserName = item.ResolveUserName

			bytes, _ := json.Marshal(item.ResultJson)
			itemResult.ResultJson = model.JSON(bytes)

			err = tx.Model(&DoorCheckItemResult{}).Create(&itemResult).Error
			if err != nil {
				return err
			}

			if item.Status == 4 {
				// 上报
				// 存消息

				notice := DoorCheckNotices{
					PkId:         model.PkId{Id: item.NoticeId},
					FkRecordId:   record.Id,
					FkItemId:     item.FkItemId,
					ItemName:     item.ItemName,
					SendUserId:   item.ReportUserId,
					SendUserName: item.ReportUserName,
					ContentJson:  nil,
					Status:       1,
					NoticeType:   1,
					Timestamp:    model.Timestamp{},
				}

				contentJson := ContentJson{
					Files:  nil,
					Remark: item.NoticeText,
				}
				bytes, err := json.Marshal(contentJson)
				if err != nil {
					return err
				}
				notice.ContentJson = model.JSON(bytes)

				err = (&notice).AddTx(tx, []DoorCheckNoticeReceives{{
					UserId:   item.ReportUserId,
					UserName: item.ResolveUserName,
				}})
				if err != nil {
					return err
				}
			}

		}

		return nil
	})
}

func (d *DoorCheckItemResult) AddTx(tx *gorm.DB) error {
	return tx.Create(d).Error
}

// web详情
func (d *DoorCheckItemResult) GetDetailWeb() ([]DoorCheckItemResult, error) {
	var rsp []DoorCheckItemResult
	err := model.DB().Model(&DoorCheckItemResult{}).Where("FkRecordId = ?", d.Id).Scan(&rsp).Error
	return rsp, err
}

// 按时间查询状态为 待整改（2）的项目
func (d *DoorCheckItemResult) GetWithTimeStatus2(startAt, endAt time.Time) ([]DoorCheckItemResult, error) {
	var rsp []DoorCheckItemResult
	err := model.DB().Model(&DoorCheckItemResult{}).Where("CreatedAt >= ? AND CreatedAt < ? AND Status = ?", startAt, endAt, 2).Scan(&rsp).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		} else {
			return nil, err
		}
	}
	return rsp, err
}
