package safety

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
)

// CashierPayMoneyParam 出纳填写的表单数据
type CashierPayMoneyParam struct {
	FormStep     int64           `json:"FormStep" gorm:"column:formstep;type:smallint;default:1;comment:表单当前处的阶段"`       //表单当前处的阶段
	IsMustPay    int64           `json:"IsMustPay" gorm:"column:ismustpay;type:smallint;default:2;comment:是否需要付款 1是 2否"` //是否需要付款 1是 2否
	PayMoneyAt   model.LocalTime `json:"PayMoneyAt" gorm:"column:paymoneyat;type:timestamp;comment:付款时间"`                //付款时间
	PayMoneyFile model.JSON      `json:"PayMoneyFile" gorm:"column:paymoneyfile;type:json;comment:银行汇款单"`                //银行汇款单
}

// TrafficAccidentLendMoneyRecord 事故借款记录
type TrafficAccidentLendMoneyRecord struct {
	model.PkId
	TrafficAccidentId                int64           `json:"TrafficAccidentId" gorm:"column:trafficaccidentid;type:bigint;comment:事故ID" validate:"required"`                               //事故ID
	TrafficAccidentRelaterId         int64           `json:"TrafficAccidentRelaterId" gorm:"column:trafficaccidentrelaterid;type:bigint;comment:当事人记录ID" validate:"required"`              //当事人记录ID
	TrafficAccidentRelaterName       string          `json:"TrafficAccidentRelaterName" gorm:"-"`                                                                                          //当事人姓名
	TrafficAccidentRelaterBranchId   int64           `json:"TrafficAccidentRelaterBranchId" gorm:"column:trafficaccidentrelaterbranchid;type:bigint;comment:事故分支类型ID" validate:"required"` //事故分支类型ID
	TrafficAccidentRelaterBranchType int64           `json:"TrafficAccidentRelaterBranchType" gorm:"-"`                                                                                    //事故分支类型名称
	LendAt                           model.LocalTime `json:"LendAt" gorm:"column:lendat;type:timestamp;comment:借款时间" validate:"required"`                                                  //借款时间
	LendMoney                        int64           `json:"LendMoney" gorm:"column:lendmoney;type:integer;comment:借款金额" validate:"required"`                                              //借款金额 单位：分
	ApplyUserId                      int64           `json:"ApplyUserId" gorm:"column:applyuserid;type:integer;comment:申请人ID"`                                                             //申请人ID 关联主数据员工ID
	ApplyUserName                    string          `json:"ApplyUserName" gorm:"column:applyusername;type:varchar;comment:申请人姓名"`                                                         //申请人姓名
	LendStaffId                      int64           `json:"LendStaffId" gorm:"column:lendstaffid;type:integer;comment:收款人ID"`                                                             //收款人ID(不一定有值)
	LendStaffName                    string          `json:"LendStaffName" gorm:"column:lendstaffname;type:varchar;comment:收款人姓名"`                                                         //收款人姓名
	LendStaffType                    int64           `json:"LendStaffType" gorm:"column:lendstafftype;type:smallint;comment:收款人类型 1内部员工  2对方" validate:"required"`                         //收款人类型 1内部员工收款  2对方收款
	LendReason                       string          `json:"LendReason" gorm:"column:lendreason;type:varchar;comment:收款原因" validate:"required"`                                            //收款原因
	FlowTo                           string          `json:"FlowTo" gorm:"column:flowto;type:varchar;comment:借款流向" validate:"required"`                                                    //借款流向
	BankCard                         string          `json:"BankCard" gorm:"column:bankcard;type:varchar;comment:银行卡号"`                                                                    //银行卡号
	Bank                             string          `json:"Bank" gorm:"column:bank;type:varchar;comment:开户行"`                                                                             //开户行
	PayWay                           string          `json:"PayWay" gorm:"column:payway;type:varchar;comment:付款方式"`                                                                        //付款方式
	Desc                             string          `json:"Desc" gorm:"column:desc;type:varchar;comment:申请内容"`                                                                            //申请内容
	FilePath                         model.JSON      `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                                                                         //附件
	ApplyStatus                      int64           `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;default:1;comment:申请审批状态"`                                                 //申请审批状态  0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃

	CashierPayMoneyParam

	model.OpUser
	model.Timestamp
	LiabilityType      int64 `json:"LiabilityType" gorm:"-"`      //责任认定 1-全责,2-主责,3-次责,4-同责,5-无责
	IsProcessRelater   bool  `json:"IsProcessRelater" gorm:"-"`   //是否是事故借款申请流程的相关人
	TotalDrawbackMoney int64 `json:"TotalDrawbackMoney" gorm:"-"` //借款记录对应的总的退款金额
	// 消息中心显示额外字段
	TrafficAccidentCode  string                               `json:"TrafficAccidentCode" gorm:"-"`  // 事故编号
	DrawbackMoneyRecords []TrafficAccidentDrawbackMoneyRecord `json:"DrawbackMoneyRecords" gorm:"-"` //退款记录
	License              string                               `json:"License" gorm:"-"`              // TrafficAccident.License
	CorporationId        int64                                `json:"CorporationId" gorm:"-"`        // TrafficAccident.CorporationId
	CorporationName      string                               `json:"CorporationName" gorm:"-"`      // TrafficAccident.CorporationName
	LineId               int64                                `json:"LineId" gorm:"-"`               // TrafficAccident.LineId
	LineName             string                               `json:"LineName" gorm:"-"`             // TrafficAccident.LineName
	DriverName           string                               `json:"DriverName" gorm:"-"`           // TrafficAccident.DriverName
}

func (lmr *TrafficAccidentLendMoneyRecord) TableName() string {
	return "traffic_accident_lend_money_records"
}

func (lmr *TrafficAccidentLendMoneyRecord) ApplyStatusFieldName() string {
	return "applystatus"
}

func (lmr *TrafficAccidentLendMoneyRecord) TemplateFormId() string {
	return config.TrafficAccidentLendMoneyFormTemplate
}

// Create 创建
func (lmr *TrafficAccidentLendMoneyRecord) Create() error {
	lmr.Id = model.Id()
	return model.DB().Create(lmr).Error
}

// FindBy 详情
func (lmr *TrafficAccidentLendMoneyRecord) FindBy(id int64) error {
	return model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Where("Id = ?", id).First(&lmr).Error
}

// TransactionCreate 事务创建
func (lmr *TrafficAccidentLendMoneyRecord) TransactionCreate(tx *gorm.DB) error {
	lmr.Id = model.Id()
	return tx.Create(lmr).Error
}

// TransactionUpdate 事务更新
func (lmr *TrafficAccidentLendMoneyRecord) TransactionUpdate(tx *gorm.DB) error {
	return tx.Select("*").Where("Id = ?", lmr.Id).Updates(lmr).Error
}

// DashboardUpdate 事务更新
func (lmr *TrafficAccidentLendMoneyRecord) DashboardUpdate() error {
	return model.DB().Select("LendAt", "LendMoney", "LendStaffId", "LendStaffName", "LendStaffType", "LendReason", "FlowTo", "BankCard", "Bank", "PayWay", "Desc", "FilePath", "IsMustPay", "PayMoneyAt", "PayMoneyFile").Where("Id = ?", lmr.Id).Updates(lmr).Error
}

// TransactionDelete 事务删除
func (lmr *TrafficAccidentLendMoneyRecord) TransactionDelete(tx *gorm.DB) error {
	return tx.Where("Id = ?", lmr.Id).Delete(&TrafficAccidentLendMoneyRecord{}).Error
}

// GetByBranchId 分支借款记录
func (lmr *TrafficAccidentLendMoneyRecord) GetByBranchId(trafficAccidentRelaterBranchId int64) []TrafficAccidentLendMoneyRecord {
	var records []TrafficAccidentLendMoneyRecord
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Where("TrafficAccidentRelaterBranchId = ? AND ApplyStatus != ?", trafficAccidentRelaterBranchId, util.ApplyStatusForAbandon).Find(&records)

	return records
}

// GetByBranchIdForAll 分支借款记录
func (lmr *TrafficAccidentLendMoneyRecord) GetByBranchIdForAll(trafficAccidentRelaterBranchId int64) []TrafficAccidentLendMoneyRecord {
	var records []TrafficAccidentLendMoneyRecord
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Where("TrafficAccidentRelaterBranchId = ?", trafficAccidentRelaterBranchId).Find(&records)

	return records
}

// GetByRelaterId 当事人借款记录
func (lmr *TrafficAccidentLendMoneyRecord) GetByRelaterId(trafficAccidentRelaterId int64) []TrafficAccidentLendMoneyRecord {
	var records []TrafficAccidentLendMoneyRecord
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Where("TrafficAccidentRelaterId = ?", trafficAccidentRelaterId).Find(&records)

	return records
}

// GetByAccidentId 事故借款记录
func (lmr *TrafficAccidentLendMoneyRecord) GetByAccidentId(trafficAccidentId int64) []TrafficAccidentLendMoneyRecord {
	var records []TrafficAccidentLendMoneyRecord
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Where("TrafficAccidentId = ? AND ApplyStatus != ?", trafficAccidentId, util.ApplyStatusForAbandon).Find(&records)

	return records
}

// GetByAccidentIdByApplyStatus 根据审批状态查询事故借款记录
func (lmr *TrafficAccidentLendMoneyRecord) GetByAccidentIdByApplyStatus(trafficAccidentId int64, applyStatus []int64) []TrafficAccidentLendMoneyRecord {
	var records []TrafficAccidentLendMoneyRecord
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Where("TrafficAccidentId = ? AND ApplyStatus IN ?", trafficAccidentId, applyStatus).Find(&records)

	return records
}

// GetDoneByAccidentId 事故已完成的借款记录
func (lmr *TrafficAccidentLendMoneyRecord) GetDoneByAccidentId(trafficAccidentId int64) []TrafficAccidentLendMoneyRecord {
	var records []TrafficAccidentLendMoneyRecord
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Where("TrafficAccidentId = ? AND ApplyStatus = ?", trafficAccidentId, util.ApplyStatusForDone).Find(&records)

	return records
}

// GetTotalMoneyByRelaterId 当事人借款总金额
func (lmr *TrafficAccidentLendMoneyRecord) GetTotalMoneyByRelaterId(trafficAccidentRelaterId int64) int64 {
	var moneys []int64
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Where("TrafficAccidentRelaterId = ? AND ApplyStatus = ?", trafficAccidentRelaterId, util.ApplyStatusForDone).Pluck("LendMoney", &moneys)

	var total int64
	for i := range moneys {
		total += moneys[i]
	}
	return total
}

// GetTotalMoneyByAccidentId 事故借款总金额
func (lmr *TrafficAccidentLendMoneyRecord) GetTotalMoneyByAccidentId(trafficAccidentId int64) int64 {
	var moneys []int64
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Where("TrafficAccidentId = ? AND ApplyStatus = ?", trafficAccidentId, util.ApplyStatusForDone).Pluck("LendMoney", &moneys)

	var total int64
	for i := range moneys {
		total += moneys[i]
	}
	return total
}

// IsExistDoLendRecord 事故是否存在进行中、已通过、撤回、驳回借款记录
func (lmr *TrafficAccidentLendMoneyRecord) IsExistDoLendRecord(trafficAccidentId int64) bool {
	var count int64
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).
		Where("TrafficAccidentId = ?", trafficAccidentId).
		Where("ApplyStatus > ? AND ApplyStatus < ?", util.ApplyStatusForNone, util.ApplyStatusForAbandon).
		Count(&count)
	return count > 0
}

// IsExistsBatchMoney 查询同一个分支是否存在相同的借款金额
func (lmr *TrafficAccidentLendMoneyRecord) IsExistsBatchMoney() (TrafficAccidentLendMoneyRecord, int64, error) {
	var count int64
	var res TrafficAccidentLendMoneyRecord
	err := model.DB().Model(&TrafficAccidentLendMoneyRecord{}).
		Where("TrafficAccidentRelaterBranchId=? AND LendMoney = ?", lmr.TrafficAccidentRelaterBranchId, lmr.LendMoney).
		Count(&count).Order("LendAt DESC").Limit(1).Find(&res).Error
	return res, count, err
}

// TrafficAccidentDrawbackMoneyRecord 事故退款记录
type TrafficAccidentDrawbackMoneyRecord struct {
	model.PkId
	TrafficAccidentId              int64 `json:"TrafficAccidentId" gorm:"column:trafficaccidentid;type:bigint;comment:事故ID" validate:"required"`           //事故ID
	TrafficAccidentRelaterId       int64 `json:"TrafficAccidentRelaterId" gorm:"column:trafficaccidentrelaterid;type:bigint;comment:当事人记录ID"`              //当事人记录ID 	//当事人姓名
	TrafficAccidentRelaterBranchId int64 `json:"TrafficAccidentRelaterBranchId" gorm:"column:trafficaccidentrelaterbranchid;type:bigint;comment:事故分支类型ID"` //事故分支类型ID
	//TrafficAccidentRelaterBranchType int64           `json:"TrafficAccidentRelaterBranchType" gorm:"-"`                                                                                       //事故分支类型名称
	TrafficAccidentLendMoneyRecordId int64           `json:"TrafficAccidentLendMoneyRecordId" gorm:"column:trafficaccidentlendmoneyrecordid;type:bigint;comment:事故借款的ID" validate:"required"` //事故借款的ID
	DrawbackAt                       model.LocalTime `json:"DrawbackAt" gorm:"column:drawbackat;type:timestamp;comment:退款时间" validate:"required"`                                             //借款时间
	DrawbackMoney                    int64           `json:"DrawbackMoney" gorm:"column:drawbackmoney;type:integer;comment:退款金额" validate:"required"`                                         //借款金额 单位：分
	DrawbackStaffId                  int64           `json:"DrawbackStaffId" gorm:"column:drawbackstaffid;type:integer;comment:退款人ID 系统内部人员才有值"`                                              //收款人ID
	DrawbackStaffName                string          `json:"DrawbackStaffName" gorm:"column:drawbackstaffname;type:varchar;comment:退款人姓名" validate:"required"`                                //收款人姓名
	BankCard                         string          `json:"BankCard" gorm:"column:bankcard;type:varchar;comment:银行卡号"`                                                                       //银行卡号
	Bank                             string          `json:"Bank" gorm:"column:bank;type:varchar;comment:开户行"`                                                                                //开户行
	DrawbackWay                      string          `json:"DrawbackWay" gorm:"column:drawbackway;type:varchar;comment:退款方式"`                                                                 //付款方式
	Desc                             string          `json:"Desc" gorm:"column:desc;type:varchar;comment:申请内容"`                                                                               //申请内容
	FilePath                         model.JSON      `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                                                                            //附件
	ApplyStatus                      int64           `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;default:1;comment:申请审批状态"`                                                    //申请审批状态  0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃
	model.OpUser
	model.Timestamp

	TrafficAccidentCode        string `json:"TrafficAccidentCode" gorm:"-"`        // 事故编号
	TrafficAccidentRelaterName string `json:"TrafficAccidentRelaterName" gorm:"-"` //当事人姓名
	License                    string `json:"License" gorm:"-"`                    // TrafficAccident.License
	CorporationId              int64  `json:"CorporationId" gorm:"-"`              // TrafficAccident.CorporationId
	CorporationName            string `json:"CorporationName" gorm:"-"`            // TrafficAccident.CorporationName
	LineId                     int64  `json:"LineId" gorm:"-"`                     // TrafficAccident.LineId
	LineName                   string `json:"LineName" gorm:"-"`                   // TrafficAccident.LineName
	DriverName                 string `json:"DriverName" gorm:"-"`                 // TrafficAccident.DriverName
}

func (dmr *TrafficAccidentDrawbackMoneyRecord) TableName() string {
	return "traffic_accident_drawback_money_records"
}

func (dmr *TrafficAccidentDrawbackMoneyRecord) ApplyStatusFieldName() string {
	return "applystatus"
}

func (dmr *TrafficAccidentDrawbackMoneyRecord) TemplateFormId() string {
	return config.TrafficAccidentLendMoneyFormTemplate
}

// Create 创建
func (dmr *TrafficAccidentDrawbackMoneyRecord) Create() error {
	dmr.Id = model.Id()
	return model.DB().Create(dmr).Error
}

// TransactionCreate 事务创建
func (dmr *TrafficAccidentDrawbackMoneyRecord) TransactionCreate(tx *gorm.DB) error {
	dmr.Id = model.Id()
	return tx.Create(dmr).Error
}

// TransactionUpdate 事务更新
func (dmr *TrafficAccidentDrawbackMoneyRecord) TransactionUpdate(tx *gorm.DB) error {
	return tx.Select("*").Where("Id = ?", dmr.Id).Updates(dmr).Error
}

// DashboardUpdate 事务更新
func (dmr *TrafficAccidentDrawbackMoneyRecord) DashboardUpdate() error {
	return model.DB().Select("DrawbackAt", "DrawbackMoney", "DrawbackStaffId", "DrawbackStaffName", "BankCard", "Bank", "DrawbackWay", "Desc", "FilePath").Where("Id = ?", dmr.Id).Updates(dmr).Error
}

// FindBy 详情
func (dmr *TrafficAccidentDrawbackMoneyRecord) FindBy(id int64) error {
	return model.DB().Model(&TrafficAccidentDrawbackMoneyRecord{}).Where("Id = ?", id).First(&dmr).Error
}

func (dmr *TrafficAccidentDrawbackMoneyRecord) DoneTotalMoneyForLendMoneyRecord(lendMoneyRecordId int64) int64 {
	var moneys []int64
	model.DB().Model(&TrafficAccidentDrawbackMoneyRecord{}).Where("TrafficAccidentLendMoneyRecordId = ? AND ApplyStatus = ?", lendMoneyRecordId, util.ApplyStatusForDone).Pluck("DrawbackMoney", &moneys)

	var total int64
	for i := range moneys {
		total += moneys[i]
	}
	return total
}

func (dmr *TrafficAccidentDrawbackMoneyRecord) DoneTotalMoneyByAccidentId(accidentId int64) int64 {
	var moneys []int64
	model.DB().Model(&TrafficAccidentDrawbackMoneyRecord{}).Where("TrafficAccidentId = ? AND ApplyStatus = ?", accidentId, util.ApplyStatusForDone).Pluck("DrawbackMoney", &moneys)

	var total int64
	for i := range moneys {
		total += moneys[i]
	}
	return total
}

func (dmr *TrafficAccidentDrawbackMoneyRecord) TotalMoneyForLendMoneyRecord(lendMoneyRecordId int64) int64 {
	var moneys []int64
	model.DB().Model(&TrafficAccidentDrawbackMoneyRecord{}).Where("TrafficAccidentLendMoneyRecordId = ? AND ApplyStatus != ?", lendMoneyRecordId, util.ApplyStatusForAbandon).Pluck("DrawbackMoney", &moneys)

	var total int64
	for i := range moneys {
		total += moneys[i]
	}
	return total
}
func (dmr *TrafficAccidentDrawbackMoneyRecord) GetRecordForLendMoneyRecord(lendMoneyRecordId int64) []TrafficAccidentDrawbackMoneyRecord {
	var records []TrafficAccidentDrawbackMoneyRecord
	model.DB().Model(&TrafficAccidentDrawbackMoneyRecord{}).Where("TrafficAccidentLendMoneyRecordId = ? AND ApplyStatus != ?", lendMoneyRecordId, util.ApplyStatusForAbandon).Find(&records)

	return records
}

func (dmr *TrafficAccidentDrawbackMoneyRecord) TotalMoneyForBranch(branchId int64) int64 {
	var moneys []int64
	model.DB().Model(&TrafficAccidentDrawbackMoneyRecord{}).Where("TrafficAccidentRelaterBranchId = ? AND ApplyStatus != ?", branchId, util.ApplyStatusForAbandon).Pluck("DrawbackMoney", &moneys)

	var total int64
	for i := range moneys {
		total += moneys[i]
	}
	return total
}

// IsExistDoDrawbackRecord 事故是否存在进行中、已通过、撤回、驳回退款记录
func (dmr *TrafficAccidentDrawbackMoneyRecord) IsExistDoDrawbackRecord(trafficAccidentId int64) bool {
	var count int64
	model.DB().Model(&TrafficAccidentDrawbackMoneyRecord{}).
		Where("TrafficAccidentId = ?", trafficAccidentId).
		Where("ApplyStatus > ? AND ApplyStatus < ?", util.ApplyStatusForNone, util.ApplyStatusForAbandon).
		Count(&count)
	return count > 0
}

// TrafficAccidentPaymentMoneyRecord 事故付款记录
type TrafficAccidentPaymentMoneyRecord struct {
	model.PkId
	TrafficAccidentId              int64 `json:"TrafficAccidentId" gorm:"column:trafficaccidentid;type:bigint;comment:事故ID" validate:"required"`                               //事故ID
	TrafficAccidentRelaterId       int64 `json:"TrafficAccidentRelaterId" gorm:"column:trafficaccidentrelaterid;type:bigint;comment:当事人记录ID" validate:"required"`              //当事人记录ID
	TrafficAccidentRelaterBranchId int64 `json:"TrafficAccidentRelaterBranchId" gorm:"column:trafficaccidentrelaterbranchid;type:bigint;comment:事故分支类型ID" validate:"required"` //事故分支类型ID
	FixOffice                      int64 `json:"FixOffice" gorm:"column:fixoffice;type:smallint;comment:维修车间" validate:"required"`                                             // 1-黄岩客运西站 2-椒江景元路 3-椒江客运总站 4-椒江工人路 5-路桥桐屿 6-路桥金清 7-综合场站 8-回浦  9-洪家
	IsFull                         int64 `json:"IsFull" gorm:"column:isfull;type:smallint;comment:全由保险赔付 1是 2否;default:0"`                                                     // 全由保险赔付 1是 2否
	IsLend                         int64 `json:"IsLend" gorm:"column:islend;type:smallint;comment:本次维修是否借款 1是 2否;default:0"`                                                   // 本次维修是否借款 1是 2否

	FixFile
	PaymentInfo
	CashierPayMoneyParam
	ApplyStatus int64 `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;default:1;comment:申请审批状态"` //申请审批状态  0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃
	model.OpUser
	model.Timestamp
	LiabilityType int64 `json:"LiabilityType" gorm:"-"` //责任认定 1-全责,2-主责,3-次责,4-同责,5-无责

	// 接收发起付款流程时的参数，先存到流程表中
	SolutionType             int64      `json:"SolutionType" gorm:"-"`             //解决方式 1-协商处理 2-交警简易程序 3-法院判决 4-保险公司 5-其他方式
	SolutionFilePath         model.JSON `json:"SolutionFilePath" gorm:"-"`         //解决方式对应的附件
	InsuranceCompanyPayMoney int64      `json:"InsuranceCompanyPayMoney" gorm:"-"` //赔付金额 单位：分
	InsurancePayMoney        int64      `json:"InsurancePayMoney" gorm:"-"`        //保险直赔金额 单位：分
	LossMoney                int64      `json:"LossMoney" gorm:"-"`                //经济损失 单位：分
	SolutionDesc             string     `json:"SolutionDesc" gorm:"-"`             //结案说明
	PayOrigin                int64      `json:"PayOrigin" gorm:"-"`                // 赔付来源 1保险公司 2当事人
	PersonalPayRatio         int64      `json:"PersonalPayRatio" gorm:"-"`         //损失金额个人承担比例  1028=>10.28%

	// 消息中心显示额外字段
	TrafficAccidentCode        string `json:"TrafficAccidentCode" gorm:"-"`        // 事故编号
	TrafficAccidentRelaterName string `json:"TrafficAccidentRelaterName" gorm:"-"` //当事人名称
	License                    string `json:"License" gorm:"-"`                    // TrafficAccident.License
	CorporationId              int64  `json:"CorporationId" gorm:"-"`              // TrafficAccident.CorporationId
	CorporationName            string `json:"CorporationName" gorm:"-"`            // TrafficAccident.CorporationName
	LineId                     int64  `json:"LineId" gorm:"-"`                     // TrafficAccident.LineId
	LineName                   string `json:"LineName" gorm:"-"`                   // TrafficAccident.LineName
	DriverName                 string `json:"DriverName" gorm:"-"`                 // TrafficAccident.DriverName

	IsExistsLend int64 `json:"IsExistsLend" gorm:"-"` //1存在借款记录 2不存在借款记录
}

type FixFile struct {
	FixMoney      int64      `json:"FixMoney" gorm:"column:fixmoney;type:integer;comment:维修金额" validate:"required"`               //维修金额
	FixDesc       string     `json:"FixDesc" gorm:"column:fixdesc;type:varchar;comment:维修内容"`                                     //维修内容
	FixBillPath   model.JSON `json:"FixBillPath" gorm:"column:fixbillpath;type:json;comment:维修单据" validate:"required"`            //维修单据
	FixBeforePath model.JSON `json:"FixBeforePath" gorm:"column:fixbeforepath;type:json;comment:维修前照片（车损照片）" validate:"required"` //维修前照片（车损照片）
	FixAfterPath  model.JSON `json:"FixAfterPath" gorm:"column:fixafterpath;type:json;comment:维修后照片" validate:"required"`         //维修后照片
}

type PaymentInfo struct {
	PaymentMoney    int64      `json:"PaymentMoney" gorm:"column:paymentmoney;type:integer;comment:付款金额" validate:"required"`    //付款金额
	FlowTo          string     `json:"FlowTo" gorm:"column:flowto;type:varchar;comment:付款流向" validate:"required"`                //付款流向
	CollectorName   string     `json:"CollectorName" gorm:"column:collectorname;type:varchar;comment:收款人姓名" validate:"required"` //收款人姓名
	BankCard        string     `json:"BankCard" gorm:"column:bankcard;type:varchar;comment:银行卡号"`                                //银行卡号
	Bank            string     `json:"Bank" gorm:"column:bank;type:varchar;comment:开户行"`                                         //开户行
	PaymentWay      string     `json:"PaymentWay" gorm:"column:paymentway;type:varchar;comment:付款方式"`                            //付款方式
	Desc            string     `json:"Desc" gorm:"column:desc;type:varchar;comment:申请内容"`                                        //申请内容
	FilePath        model.JSON `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                                     //附件
	ConfirmFilePath model.JSON `json:"ConfirmFilePath" gorm:"column:confirmfilepath;type:json;comment:道路交通事故认定书"`                // 道路交通事故认定书
}

func (pmr *TrafficAccidentPaymentMoneyRecord) TableName() string {
	return "traffic_accident_payment_money_records"
}
func (pmr *TrafficAccidentPaymentMoneyRecord) ApplyStatusFieldName() string {
	return "applystatus"
}

func (pmr *TrafficAccidentPaymentMoneyRecord) TemplateFormId() string {
	return config.TrafficAccidentPaymentMoneyFormTemplate
}

// Create 创建
func (pmr *TrafficAccidentPaymentMoneyRecord) Create() error {
	pmr.Id = model.Id()
	return model.DB().Create(pmr).Error
}

// TransactionCreate 事务创建
func (pmr *TrafficAccidentPaymentMoneyRecord) TransactionCreate(tx *gorm.DB) error {
	pmr.Id = model.Id()
	return tx.Create(pmr).Error
}

// TransactionUpdate 事务更新
func (pmr *TrafficAccidentPaymentMoneyRecord) TransactionUpdate(tx *gorm.DB) error {
	return tx.Select("FixOffice", "IsFull", "ApplyStatus", "FormStep", "SolutionType", "SolutionFilePath", "InsuranceCompanyPayMoney", "InsurancePayMoney", "LossMoney", "SolutionDesc", "PayOrigin", "PersonalPayRatio").Where("Id = ?", pmr.Id).Updates(pmr).Error
}

func (pmr *TrafficAccidentPaymentMoneyRecord) DashboardUpdate() error {
	return model.DB().Select("FixOffice", "IsFull", "SolutionType",
		"SolutionFilePath", "InsuranceCompanyPayMoney", "InsurancePayMoney", "LossMoney",
		"SolutionDesc", "PayOrigin", "PersonalPayRatio", "FixMoney", "FixDesc", "FixBillPath",
		"FixBeforePath", "FixAfterPath", "PaymentMoney", "FlowTo", "CollectorName", "BankCard",
		"Bank", "PaymentWay", "Desc", "FilePath", "ConfirmFilePath", "IsMustPay", "PayMoneyAt", "PayMoneyFile").Where("Id = ?", pmr.Id).Updates(pmr).Error
}

// FindBy 详情
func (pmr *TrafficAccidentPaymentMoneyRecord) FindBy(id int64) error {
	return model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Where("Id = ?", id).First(&pmr).Error
}

func (pmr *TrafficAccidentPaymentMoneyRecord) GetByBranchId(branchId int64) []TrafficAccidentPaymentMoneyRecord {
	var records []TrafficAccidentPaymentMoneyRecord
	model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Where("TrafficAccidentRelaterBranchId = ?", branchId).Find(&records)

	return records
}

func (pmr *TrafficAccidentPaymentMoneyRecord) GetDoneByBranchId(branchId int64) []TrafficAccidentPaymentMoneyRecord {
	var records []TrafficAccidentPaymentMoneyRecord
	model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Where("TrafficAccidentRelaterBranchId = ? AND ApplyStatus  = ?", branchId, util.ApplyStatusForDone).Find(&records)

	return records
}

func (pmr *TrafficAccidentPaymentMoneyRecord) GetDoneByAccidentId(accidentId int64) []TrafficAccidentPaymentMoneyRecord {
	var records []TrafficAccidentPaymentMoneyRecord
	model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Where("TrafficAccidentId = ? AND ApplyStatus  = ?", accidentId, util.ApplyStatusForDone).Find(&records)

	return records
}

func (pmr *TrafficAccidentPaymentMoneyRecord) SumFixMoney(accidentId int64) int64 {
	var fixMoney int64
	model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Select("COALESCE(SUM(FixMoney), 0)").Where("TrafficAccidentId = ? AND ApplyStatus  = ?", accidentId, util.ApplyStatusForDone).Scan(&fixMoney)

	return fixMoney
}

// ExistPaymentRecord 事故是否存在付款记录
func (pmr *TrafficAccidentPaymentMoneyRecord) ExistPaymentRecord(trafficAccidentId int64) bool {
	var count int64
	model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Where("TrafficAccidentId = ? AND ApplyStatus IN ?", trafficAccidentId, []int64{util.ApplyStatusForDoing, util.ApplyStatusForDone}).Count(&count)
	return count > 0
}

// IsExistDoPaymentRecord 事故是否存在进行中、已通过、撤回、驳回付款记录
func (pmr *TrafficAccidentPaymentMoneyRecord) IsExistDoPaymentRecord(trafficAccidentId int64) bool {
	var count int64
	model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).
		Where("TrafficAccidentId = ?", trafficAccidentId).
		Where("ApplyStatus > ? AND ApplyStatus < ?", util.ApplyStatusForNone, util.ApplyStatusForAbandon).
		Count(&count)
	return count > 0
}

// IsExistDoingPaymentRecord 事故是否存在进行中的付款记录
func (pmr *TrafficAccidentPaymentMoneyRecord) IsExistDoingPaymentRecord(trafficAccidentId int64) bool {
	var count int64
	model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Where("TrafficAccidentId = ? AND ApplyStatus = ?", trafficAccidentId, util.ApplyStatusForDoing).Count(&count)
	return count > 0
}

// UpdateFixFile 更新维修字段
func (pmr *TrafficAccidentPaymentMoneyRecord) UpdateFixFile() error {
	return model.DB().Model(pmr).Select("FixMoney", "FixDesc", "FixBillPath", "FixBeforePath", "FixAfterPath", "FormStep").Updates(&pmr).Error
}

// UpdatePaymentInfo 更新付款字段
func (pmr *TrafficAccidentPaymentMoneyRecord) UpdatePaymentInfo() error {
	return model.DB().Model(pmr).Select("PaymentMoney", "FlowTo", "CollectorName", "BankCard", "Bank", "PaymentWay", "Desc", "FilePath", "FormStep", "IsLend", "ConfirmFilePath").Updates(&pmr).Error
}

// UpdateFormStep 更新表单进度
func (pmr *TrafficAccidentPaymentMoneyRecord) UpdateFormStep(id, step int64) error {
	return model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Where("Id = ?", id).Update("FormStep", step).Error
}
