package safety

import (
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"time"
)

func WhereValidAccident(db *gorm.DB) *gorm.DB {
	return db.Where("OpenStatus != ? AND IsRecycle = ?", util.AccidentOpenStatusForDraft, util.StatusForFalse)
}
func WhereLineId(lineId int64) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("LineId = ?", lineId)
	}
}

func WhereApplyStatusDone(db *gorm.DB) *gorm.DB {
	return db.Where("ApplyStatus = ?", util.ApplyStatusForDone)
}

func TrafficAccidentSubQuery(corporationId, lineId int64, startTime, endTime time.Time) *gorm.DB {
	tx := model.DB().Model(&TrafficAccident{}).Select("Id").
		Scopes(model.WhereCorporation(corporationId)).
		Scopes(WhereValidAccident).
		Where("HappenAt >= ? AND HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat))
	if lineId > 0 {
		tx = tx.Scopes(WhereLineId(lineId))
	}
	return tx
}

// AccidentCount 事故数
func (ta *TrafficAccident) AccidentCount(corporationId, lineId int64, isClosed, liabilityType int64, startTime, endTime time.Time) int64 {
	tx := model.DB().Model(&TrafficAccident{}).Scopes(model.WhereCorporation(corporationId)).
		Scopes(WhereValidAccident).
		Where("HappenAt >= ? AND HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat))
	if isClosed > 0 {
		tx = tx.Where("IsClosed = ?", isClosed)
	}

	if liabilityType > 0 {
		tx = tx.Where("LiabilityType = ?", liabilityType)
	}

	if lineId > 0 {
		tx = tx.Scopes(WhereLineId(lineId))
	}

	var count int64
	tx.Count(&count)

	return count
}

type AccidentHappenLocationItem struct {
	Id                 int64           `json:"Id"`
	Code               string          `json:"Code"`
	HappenLocation     string          `json:"HappenLocation" gorm:"column:happenlocation"`
	HappenLat          int64           `json:"HappenLat" gorm:"column:happenlat"`
	HappenLng          int64           `json:"HappenLng" gorm:"column:happenlng"`
	HappenAt           model.LocalTime `json:"HappenAt" gorm:"column:happenat"`
	Grade              int64           `json:"Grade" gorm:"column:grade"`
	Cate               int64           `json:"Cate" gorm:"column:cate"`
	LineId             int64           `json:"LineId" gorm:"column:lineid"`
	LineName           string          `json:"LineName" gorm:"column:linename"`
	DriverId           int64           `json:"DriverId" gorm:"column:driverid"`
	DriverName         string          `json:"DriverName" gorm:"column:drivername"`
	VehicleId          int64           `json:"VehicleId" gorm:"column:vehicleid"`
	License            string          `json:"License" gorm:"column:license"`
	LiabilityType      int64           `json:"LiabilityType" gorm:"column:liabilitytype"`
	HappenLocationDesc string          `json:"HappenLocationDesc" gorm:"column:happenlocationdesc"`
	WeatherCondition   int64           `json:"WeatherCondition" gorm:"column:weathercondition"`
	RoadCondition      int64           `json:"RoadCondition" gorm:"column:roadcondition"`
	model.Corporations
	CorporationName string `json:"CorporationName" gorm:"-"`
}

// AccidentHappenLocation 事故发生地点
func (ta *TrafficAccident) AccidentHappenLocation(corporationId, lineId int64, vehicleBrokenCate, peopleHurtCate int64, startTime, endTime time.Time) []AccidentHappenLocationItem {
	tx := model.DB().Model(&TrafficAccident{}).Scopes(model.WhereCorporation(corporationId)).
		Scopes(WhereValidAccident).
		Where("HappenAt >= ? AND HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat))
	if vehicleBrokenCate > 0 {
		tx = tx.Where("VehicleBrokenCate = ?", vehicleBrokenCate)
	}
	if peopleHurtCate > 0 {
		tx = tx.Where("PeopleHurtCate = ?", peopleHurtCate)
	}
	if lineId > 0 {
		tx = tx.Scopes(WhereLineId(lineId))
	}
	var items []AccidentHappenLocationItem

	tx.Find(&items)
	return items
}

// AccidentCountForCateIds 事故类别下的事故数
func (ta *TrafficAccident) AccidentCountForCateIds(corporationId, lineId int64, cateIds, branchTypes []int64, startTime, endTime time.Time) int64 {
	tx := model.DB().Model(&TrafficAccident{}).Scopes(model.WhereCorporation(corporationId)).
		Scopes(WhereValidAccident).
		Where("HappenAt >= ? AND HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat))
	if len(cateIds) > 0 {
		tx = tx.Where("Cate IN ?", cateIds)
	}

	if len(branchTypes) > 0 {
		tx = tx.Where("Id IN (?)", model.DB().Model(&TrafficAccidentRelaterBranch{}).Select("TrafficAccidentId").Where("BranchType IN ?", branchTypes))
	}

	if lineId > 0 {
		tx = tx.Scopes(WhereLineId(lineId))
	}

	var count int64
	tx.Count(&count)

	return count
}

type AccidentCountGroupItem struct {
	GroupItem int64  `json:"GroupItem" gorm:"column:groupitem"`
	ItemName  string `json:"ItemName" gorm:"column:itemname"`
	Count     int64  `json:"Count" gorm:"column:count"`
}

// AccidentCountGroupByCate 按事故类别分组后不同类别下的事故数
func (ta *TrafficAccident) AccidentCountGroupByCate(corporationId, lineId int64, startTime, endTime time.Time) []AccidentCountGroupItem {
	var countItems []AccidentCountGroupItem
	tx := model.DB().Model(&TrafficAccident{}).Select("COUNT(*) AS Count", "Cate AS groupitem").Scopes(model.WhereCorporation(corporationId)).
		Scopes(WhereValidAccident).Where("Cate > ?", 0).
		Where("HappenAt >= ? AND HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat))

	if lineId > 0 {
		tx = tx.Scopes(WhereLineId(lineId))
	}

	tx.Group("groupitem").Scan(&countItems)

	return countItems
}

// AccidentCountGroupByFleetId 按车队分组后不同车队下的事故数
func (ta *TrafficAccident) AccidentCountGroupByFleetId(corporationId int64, isClosed int64, startTime, endTime time.Time) []AccidentCountGroupItem {
	var countItems []AccidentCountGroupItem
	tx := model.DB().Model(&TrafficAccident{}).Select("COUNT(*) AS Count", "FleetId AS groupitem").Scopes(model.WhereCorporation(corporationId)).
		Scopes(WhereValidAccident).Where("FleetId > ?", 0).
		Where("HappenAt >= ? AND HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat))

	if isClosed > 0 {
		tx = tx.Where("IsClosed = ?", isClosed)
	}

	tx.Group("groupitem").Scan(&countItems)

	return countItems
}

// AccidentCountGroupByBranchId 按分公司分组后不同公司下的事故数
func (ta *TrafficAccident) AccidentCountGroupByBranchId(corporationId int64, startTime, endTime time.Time) []AccidentCountGroupItem {
	var countItems []AccidentCountGroupItem
	model.DB().Model(&TrafficAccident{}).Select("COUNT(*) AS Count", "BranchId AS groupitem").Scopes(model.WhereCorporation(corporationId)).
		Scopes(WhereValidAccident).Where("BranchId > ?", 0).
		Where("HappenAt >= ? AND HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat)).Group("groupitem").
		Scan(&countItems)

	return countItems
}

// AccidentCountGroupByWeatherCondition 按天气分组后不同天气下的事故数
func (ta *TrafficAccident) AccidentCountGroupByWeatherCondition(corporationId, lineId int64, startTime, endTime time.Time) []AccidentCountGroupItem {
	var countItems []AccidentCountGroupItem
	tx := model.DB().Model(&TrafficAccident{}).Select("COUNT(*) AS Count", "WeatherCondition AS groupitem").Scopes(model.WhereCorporation(corporationId)).
		Scopes(WhereValidAccident).Where("WeatherCondition > ?", 0).
		Where("HappenAt >= ? AND HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat))

	if lineId > 0 {
		tx = tx.Scopes(WhereLineId(lineId))
	}

	tx.Group("groupitem").Scan(&countItems)

	return countItems
}

// AccidentCountGroupByHour 按小时分组后不同小时段下的事故数
func (ta *TrafficAccident) AccidentCountGroupByHour(corporationId, lineId int64, startTime, endTime time.Time) []AccidentCountGroupItem {
	var countItems []AccidentCountGroupItem
	tx := model.DB().Model(&TrafficAccident{}).Select("COUNT(*) AS Count", "EXTRACT(hour FROM HappenAt) AS groupitem").Scopes(model.WhereCorporation(corporationId)).
		Scopes(WhereValidAccident).
		Where("HappenAt >= ? AND HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat))

	if lineId > 0 {
		tx = tx.Scopes(WhereLineId(lineId))
	}

	tx.Group("groupitem").Scan(&countItems)

	return countItems
}

// AccidentClosedMoney 事故结案金额
func (ta *TrafficAccident) AccidentClosedMoney(corporationId, lineId int64, startTime, endTime time.Time) int64 {
	var lossMoney int64
	tx := model.DB().Model(&TrafficAccident{}).Select("COALESCE(SUM(LossMoney), 0) - COALESCE(SUM(InsuranceCompanyPayMoney), 0)").Scopes(model.WhereCorporation(corporationId)).
		Scopes(WhereValidAccident).
		Where("HappenAt >= ? AND HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat)).
		Where("IsClosed = ?", util.StatusForTrue)

	if lineId > 0 {
		tx = tx.Scopes(WhereLineId(lineId))
	}

	tx.Scan(&lossMoney)

	return lossMoney
}

// SumAccidentFixMoney 事故维修总金额
func (pmr *TrafficAccidentPaymentMoneyRecord) SumAccidentFixMoney(corporationId, lineId int64, startTime, endTime time.Time) int64 {
	var fixMoney int64
	model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Select("COALESCE(SUM(FixMoney), 0)").
		Scopes(WhereApplyStatusDone).
		Where("TrafficAccidentId IN (?)", TrafficAccidentSubQuery(corporationId, lineId, startTime, endTime)).Scan(&fixMoney)

	return fixMoney
}

type CorporationMoneyItem struct {
	CorporationId int64 `json:"CorporationId" gorm:"column:corporationid"`
	TotalMoney    int64 `json:"TotalMoney" gorm:"column:totalmoney"`
}

// SumAccidentFixMoneyGroupByFleet 按车队分组后不同车队下的维修总金额
func (pmr *TrafficAccidentPaymentMoneyRecord) SumAccidentFixMoneyGroupByFleet(corporationId int64, startTime, endTime time.Time) []CorporationMoneyItem {
	var items []CorporationMoneyItem

	var paymentMoneyRecordTable = fmt.Sprintf("%s AS pt", pmr.TableName())
	var accidentTable = fmt.Sprintf("%s AS at", (&TrafficAccident{}).TableName())
	join := fmt.Sprintf("LEFT JOIN %s ON pt.TrafficAccidentId = at.Id", accidentTable)
	model.DB().Table(paymentMoneyRecordTable).Select("COALESCE(SUM(pt.FixMoney), 0) AS totalMoney,at.FleetId AS CorporationId").
		Joins(join).Where("pt.ApplyStatus = ?", util.ApplyStatusForDone).
		Scopes(model.WhereCorporation(corporationId)).
		Where("at.OpenStatus != ? AND at.IsRecycle = ?", util.AccidentOpenStatusForDraft, util.StatusForFalse).
		Where("at.HappenAt >= ? AND at.HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat)).Group("at.fleetid").
		Scan(&items)

	return items
}

// SumAccidentLendMoney 事故借款总额
func (lmr *TrafficAccidentLendMoneyRecord) SumAccidentLendMoney(corporationId, lineId int64, startTime, endTime time.Time) int64 {
	var lendMoney int64
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).
		Select("COALESCE(SUM(LendMoney), 0)").
		Scopes(WhereApplyStatusDone).Where("TrafficAccidentId IN (?)", TrafficAccidentSubQuery(corporationId, lineId, startTime, endTime)).Scan(&lendMoney)

	return lendMoney
}

// SumAccidentLendMoneyGroupByFleet 按车队分组后不同车队下的借款总金额
func (lmr *TrafficAccidentLendMoneyRecord) SumAccidentLendMoneyGroupByFleet(corporationId int64, startTime, endTime time.Time) []CorporationMoneyItem {
	var items []CorporationMoneyItem

	var lendMoneyRecordTable = fmt.Sprintf("%s AS lt", lmr.TableName())
	var accidentTable = fmt.Sprintf("%s AS at", (&TrafficAccident{}).TableName())
	join := fmt.Sprintf("LEFT JOIN %s ON lt.TrafficAccidentId = at.Id", accidentTable)
	model.DB().Table(lendMoneyRecordTable).Select("COALESCE(SUM(lt.LendMoney), 0) AS totalMoney,at.FleetId AS CorporationId").
		Joins(join).Where("lt.ApplyStatus = ?", util.ApplyStatusForDone).
		Scopes(model.WhereCorporation(corporationId)).
		Where("at.OpenStatus != ? AND at.IsRecycle = ?", util.AccidentOpenStatusForDraft, util.StatusForFalse).
		Where("at.HappenAt >= ? AND at.HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat)).Group("at.fleetid").
		Scan(&items)

	return items
}

// SumAccidentDrawbackMoney 事故退款总额
func (dmr *TrafficAccidentDrawbackMoneyRecord) SumAccidentDrawbackMoney(corporationId, lineId int64, startTime, endTime time.Time) int64 {
	var drawbackMoney int64
	subQuery := model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Select("Id").Scopes(WhereApplyStatusDone).Where("TrafficAccidentId IN (?)", TrafficAccidentSubQuery(corporationId, lineId, startTime, endTime))
	model.DB().Model(&TrafficAccidentDrawbackMoneyRecord{}).Select("COALESCE(SUM(DrawbackMoney), 0)").Scopes(WhereApplyStatusDone).Where("TrafficAccidentLendMoneyRecordId IN (?)", subQuery).Scan(&drawbackMoney)
	return drawbackMoney
}

// SumAccidentDrawbackMoneyGroupByFleet 按车队分组后不同车队下的退款总金额
func (dmr *TrafficAccidentDrawbackMoneyRecord) SumAccidentDrawbackMoneyGroupByFleet(corporationId int64, startTime, endTime time.Time) []CorporationMoneyItem {
	var items []CorporationMoneyItem

	var drawbackMoneyRecordTable = fmt.Sprintf("%s AS dt", dmr.TableName())
	var accidentTable = fmt.Sprintf("%s AS at", (&TrafficAccident{}).TableName())
	join := fmt.Sprintf("LEFT JOIN %s ON dt.TrafficAccidentId = at.Id", accidentTable)
	model.DB().Table(drawbackMoneyRecordTable).Select("COALESCE(SUM(dt.DrawbackMoney), 0) AS totalMoney,at.FleetId AS CorporationId").
		Joins(join).Where("dt.ApplyStatus = ?", util.ApplyStatusForDone).
		Scopes(model.WhereCorporation(corporationId)).
		Where("at.OpenStatus != ? AND at.IsRecycle = ?", util.AccidentOpenStatusForDraft, util.StatusForFalse).
		Where("at.HappenAt >= ? AND at.HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat)).Group("at.fleetid").
		Scan(&items)

	return items
}

// GetProcessIdWithAccidentRelate 获取事故相关所有流程的的流程ID
func (ta *TrafficAccident) GetProcessIdWithAccidentRelate(corporationId int64, startTime, endTime time.Time) []int64 {
	var itemIds []int64
	//事故ID
	var accidentIds []int64
	TrafficAccidentSubQuery(corporationId, 0, startTime, endTime).Pluck("Id", &accidentIds)
	itemIds = append(itemIds, accidentIds...)
	//事故付款
	var paymentMoneyRecordIds []int64
	model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Select("Id").Where("TrafficAccidentId IN ?", accidentIds).Pluck("Id", &paymentMoneyRecordIds)
	itemIds = append(itemIds, paymentMoneyRecordIds...)
	//事故借款
	var lendMoneyRecordIds []int64
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Select("Id").Where("TrafficAccidentId IN ?", accidentIds).Pluck("Id", &lendMoneyRecordIds)
	itemIds = append(itemIds, lendMoneyRecordIds...)

	//事故退款
	var drawbackMoneyRecordIds []int64
	model.DB().Model(&TrafficAccidentDrawbackMoneyRecord{}).Select("Id").Where("TrafficAccidentId IN ?", accidentIds).Pluck("Id", &drawbackMoneyRecordIds)
	itemIds = append(itemIds, drawbackMoneyRecordIds...)

	//分支结案
	var branchClosedIds []int64
	model.DB().Model(&TrafficAccidentRelaterBranch{}).Select("Id").Where("ClosedApplyStatus > ?", 0).Where("TrafficAccidentId IN ?", accidentIds).Pluck("Id", &branchClosedIds)
	itemIds = append(itemIds, branchClosedIds...)

	var processIds []int64
	model.DB().Model(processModel.LbpmApplyProcess{}).Select("FormInstanceId").Where("ItemId IN ?", itemIds).Pluck("FormInstanceId", &processIds)
	return processIds
}

func (ta *TrafficAccident) GetAccidentId(column string, corporationId int64, startTime, endTime time.Time) []int64 {
	//事故ID
	var accidentIds []int64
	TrafficAccidentSubQuery(corporationId, 0, startTime, endTime).Pluck("Id", &accidentIds)

	return accidentIds

}

func (ta *TrafficAccident) GetAccidentsForReport(column string, corporationId int64, startTime, endTime time.Time, paginator model.Paginator) ([]TrafficAccident, int64) {
	var accidents []TrafficAccident
	tx := model.DB().Model(&TrafficAccident{}).
		Where(column+" = ?", corporationId).
		Scopes(WhereValidAccident).
		Where("HappenAt >= ? AND HappenAt < ?", startTime.Format(model.DateFormat), endTime.Format(model.DateFormat))

	var count int64
	tx.Count(&count)
	tx.Offset(paginator.Offset).Limit(paginator.Limit).Find(&accidents)

	return accidents, count
}

// GetNotHasDiffMoneyAccident 无差额事故
func (ta *TrafficAccident) GetNotHasDiffMoneyAccident(corporationId int64, startTime, endTime time.Time, paginator model.Paginator) ([]TrafficAccident, int64) {
	tx := model.DB().Model(&TrafficAccident{}).
		Scopes(model.WhereCorporation(corporationId)).
		Where("IsClosed = ?", util.StatusForTrue)

	if !startTime.IsZero() {
		tx.Where("ClosedAt >= ?", startTime.Format(model.DateFormat))
	}

	if !endTime.IsZero() {
		tx.Where("ClosedAt <= ?", endTime.Format(model.DateFormat))
	}

	tx.Where(
		model.DB().Where("LossMoney-InsuranceCompanyPayMoney=0 AND Id NOT IN (?)", model.DB().Model(&TrafficAccidentRelaterBranch{}).Select("TrafficAccidentId").Where("BranchType = ?", util.AccidentBranchTypeForSelfVehicleBroken)).
			Or("LossMoney-InsuranceCompanyPayMoney=0 AND Id IN (?)", model.DB().Model(&TrafficAccidentRelaterBranch{}).Select("TrafficAccidentId").Where("BranchType = ? AND LossMoney-InsuranceCompanyPayMoney=0", util.AccidentBranchTypeForSelfVehicleBroken)))

	tx.Where(
		model.DB().Where("LossMoney != 0").Or(
			"Id IN (?)", model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Select("TrafficAccidentId").Where("FixMoney!=0")))

	var count int64
	tx.Count(&count)

	var accidents []TrafficAccident
	tx.Order("ClosedAt ASC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&accidents)
	return accidents, count
}
