package safety

import (
	"app/org/scs/erpv2/api/model"
	"fmt"
	"gorm.io/gorm"
	"strings"
	"time"
)

// TrafficViolationRectification 整改记录
type TrafficViolationRectification struct {
	model.PkId
	model.Corporations
	SubmitCorporationId int64           `json:"SubmitCorporationId"    gorm:"column:submitcorporationid;comment:提交单位;type:bigint"`
	TrafficViolationId  int64           `json:"TrafficViolationId" gorm:"column:trafficviolationid;comment:关联违规Id;type:bigint" validate:"required"`
	ObjectStaffId       int64           `json:"ObjectStaffId" gorm:"column:objectstaffid;comment:整改对象Id;type:bigint"`
	ObjectStaffName     string          `json:"ObjectStaffName" gorm:"column:objectstaffname;comment:整改对象;type:varchar(50)"`
	HeadUserId          int64           `json:"HeadUserId" gorm:"column:headuserid;comment:整改负责人 接收人id;type:bigint" validate:"required"`
	HeadUserName        string          `json:"HeadUserName" gorm:"column:headusername;comment:整改负责人 接收人;type:varchar(50)"`
	FinishAt            model.LocalTime `json:"FinishAt" gorm:"column:finishat;comment:整改期限;type:timestamp" validate:"required"`
	Require             string          `json:"Require" gorm:"column:require;comment:整改要求;type:text" validate:"required"`
	HandleStatus        int64           `json:"HandleStatus" gorm:"column:handlestatus;default:0;comment: 0未回复 1待审批 2驳回 3审批通过 4重新回复;type:smallint"`

	// 处理后对应字段
	HandleUserId    int64           `json:"HandleUserId" gorm:"column:handleuserid;type:bigint;comment:执行人 userId;default:0;"`
	HandleUserName  string          `json:"HandleUserName" gorm:"column:handleusername;type:varchar(50);comment:执行人;default:''"`
	HandleAt        model.LocalTime `json:"HandleAt" gorm:"column:handleat;comment:处理时间;type:timestamp"`
	HandleContent   string          `json:"HandleContent" gorm:"column:handlecontent;comment:整改内容;type:text"`
	HandleFile      model.JSON      `json:"HandleFile" gorm:"column:handlefile;comment:整改附件;type:json"`
	IsDriverHelp    int64           `json:"IsDriverHelp" gorm:"column:isdriverhelp;type:smallint;default:2;comment:是否列入司机重点帮扶 1是 2否"`
	DriverHelpParam model.JSON      `json:"DriverHelpParam" gorm:"column:driverhelpparam;type:json;comment:列入重点帮扶时填的参数"`
	model.Timestamp

	SubmitCorporationName string     `json:"SubmitCorporationName"      gorm:"-"`        // 提交单位
	CorporationName       string     `json:"CorporationName" gorm:"-"`                   // 整改单位（通知接收单位）
	CorporationId         int64      `json:"CorporationId" gorm:"-" validate:"required"` // 整改机构
	FileHttpPrefix        string     `json:"FileHttpPrefix" gorm:"-"`
	License               string     `json:"License" gorm:"-"`
	ViolationStandards    model.JSON `json:"ViolationStandards" gorm:"-"`
}

func (tvr *TrafficViolationRectification) TableName() string {
	return "traffic_violation_rectifications"
}
func (tvr *TrafficViolationRectification) MessageType() string {
	return "traffic_violation_rectification_message"
}

func (tvr *TrafficViolationRectification) TransactionUpdateHandle(tx *gorm.DB) error {
	return tx.Select("HandleUserId", "HandleUserName", "HandleAt", "HandleContent", "HandleFile", "HandleStatus", "IsDriverHelp", "DriverHelpParam").Updates(&tvr).Error
}

func (tvr *TrafficViolationRectification) TransactionUpdateHandleStatus(tx *gorm.DB) error {
	return tx.Select("HandleStatus", "FinishAt").Updates(&tvr).Error
}

func (tvr *TrafficViolationRectification) BeforeCreate(db *gorm.DB) error {
	tvr.Id = model.Id()
	return nil
}

func (tvr *TrafficViolationRectification) Create() error {
	return model.DB().Create(&tvr).Error
}

func (tvr *TrafficViolationRectification) Update() error {
	return model.DB().Updates(&tvr).Error
}

func (tvr *TrafficViolationRectification) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&tvr).Error
}

func (tvr *TrafficViolationRectification) TransactionDelete(tx *gorm.DB) error {
	return tx.Delete(&tvr).Error
}

func (tvr *TrafficViolationRectification) List(objectName, license string, handleStatus int64, cateAttrs []int64, submitCorpIds, corporationIds []int64,
	submitStartAt, submitEndAt, handleStartAt, handleEndAt time.Time, paginator model.Paginator) ([]TrafficViolationRectification, int64) {
	var rectifications []TrafficViolationRectification
	var totalCount int64

	tx := model.DB().Model(&TrafficViolationRectification{}).Scopes(model.WhereCorporations(corporationIds))

	if license != "" {
		tx.Where("TrafficViolationId IN (?)", model.DB().Model(&TrafficViolation{}).Select("Id").Where("License LIKE ?", "%"+license+"%"))
	}

	if objectName != "" {
		tx.Where("ObjectStaffName LIKE ?", "%"+objectName+"%")
	}

	if len(submitCorpIds) > 0 {
		tx.Where("SubmitCorporationId IN ?", submitCorpIds)
	}

	if !submitStartAt.IsZero() {
		tx.Where("CreatedAt >= ?", submitStartAt.Format(model.TimeFormat))
	}

	if !submitEndAt.IsZero() {
		tx.Where("CreatedAt <= ?", submitEndAt.Format(model.TimeFormat))
	}

	if handleStatus >= 0 {
		tx.Where("HandleStatus = ?", handleStatus)
	}

	if len(cateAttrs) > 0 {
		var str []string
		for i := range cateAttrs {
			str = append(str, fmt.Sprintf("standards @> '[{\"QualityAssessmentCateAttr\":%v}]'", cateAttrs[i]))
		}
		subQuery := model.DB().Model(&TrafficViolation{}).Select("Id").Where(strings.Join(str, " OR "))

		tx.Where("TrafficViolationId IN (?)", subQuery)
	}

	if !handleStartAt.IsZero() {
		tx.Where("HandleAt >= ?", handleStartAt.Format(model.TimeFormat))
	}

	if !handleEndAt.IsZero() {
		tx.Where("HandleAt <= ?", handleEndAt.Format(model.TimeFormat))
	}

	tx.Count(&totalCount).Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&rectifications)

	return rectifications, totalCount
}

func (tvr *TrafficViolationRectification) DriverRectificationCount(driverId int64, startAt, endAt time.Time) int64 {
	var count int64
	model.DB().Model(&TrafficViolationRectification{}).Where("ObjectStaffId = ? AND CreatedAt >= ? AND CreatedAt < ?",
		driverId, startAt.Format(model.DateFormat), endAt.Format(model.DateFormat)).Count(&count)

	return count
}

func (tvr *TrafficViolationRectification) DriverLatestRectification(driverId int64) TrafficViolationRectification {
	var rectification TrafficViolationRectification
	model.DB().Model(&TrafficViolationRectification{}).Where("ObjectStaffId = ?", driverId).Order("CreatedAt DESC").Limit(1).Find(&rectification)

	return rectification
}

func (tvr *TrafficViolationRectification) FirstByViolationId(violationId int64) TrafficViolationRectification {
	var violation TrafficViolationRectification
	model.DB().Model(&TrafficViolationRectification{}).Where("TrafficViolationId = ?", violationId).First(&violation)

	return violation
}

func (tvr *TrafficViolationRectification) FirstById(id int64) TrafficViolationRectification {
	var violation TrafficViolationRectification
	model.DB().Model(&TrafficViolationRectification{}).Where("Id = ?", id).First(&violation)

	return violation
}
