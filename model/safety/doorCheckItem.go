package safety

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"strconv"
)

type DoorCheckItemType int64

const (
	CHECK_TYPE_1  DoorCheckItemType = 1 // 门检类别
	CHECK_ITEM_2  DoorCheckItemType = 2 // 门检项目
	CHECK_LABEL_3 DoorCheckItemType = 3 // 门检标签
)

// 定义门检项目  门检类别 门检项目 项目标签
type DoorCheckItems struct {
	model.PkId

	model.GroupCorporation
	model.CompanyCorporation
	model.BranchCorporation
	model.DepartmentCorporation
	model.FleetCorporation

	// 公司 <- 岗位 <- 门检类别 <- 门检项目 <- 项目标签

	WorkPostType int64 `json:"WorkPostType" gorm:"column:workposttype;comment:岗位类型;type:bigint"` // 门检仅支持 1，3   岗位类型 1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他

	ItemType DoorCheckItemType `json:"ItemType"        gorm:"column:itemtype;comment:门检项目类别;type:smallint"` // CHECK_TYPE_1门检类别 CHECK_ITEM_2门检项目 CHECK_LABEL_3项目标签

	IsDescNormal         int64 `json:"IsDescNormal"    gorm:"column:isdescnormal;comment:门检项目-是否描述正常;type:smallint"`                           // 门检项目-是否描述正常 1描述 2不描述
	IsDescAbNormal       int64 `json:"IsDescAbNormal"  gorm:"column:isdescabnormal;comment:门检项目-是否描述异常;type:smallint"`                         // 门检项目-是否描述异常 1描述 2不描述
	IsAttachmentAbNormal int64 `json:"IsAttachmentAbNormal"  gorm:"column:isattachmentabnormal;comment:门检项目-异常是否上传附件;type:smallint;default:1"` // 门检项目-异常是否上传附件 1上传 2不上传
	Nature               int64 `json:"Nature"          gorm:"column:nature;comment:门检标签属性;type:smallint"`                                      // 门检标签属性 0项目的Nature=0 1正常 2异常

	ParentId     int64  `json:"ParentId"        gorm:"column:parentid;comment:门检类别ParentId=0;type:bigint"`       // 门检类别ParentId=0
	ParentIdPath string `json:"ParentIdPath"    gorm:"column:parentidpath;comment:ParentIdPath(包含自己);type:text"` // ParentIdPath(包含自己) 1,2,3

	FkIconFileId int64  `json:"FkIconFileId" gorm:"column:fkiconfileid;comment:门检项目icon;type:bigint"` // 门检项目icon 标签为0 fk -> files.id
	IconFileUrl  string `json:"IconFileUrl"  gorm:"column:iconfileurl;comment:门检项目icon;type:text"`    // 门检项目icon 标签为'' url

	Name string `json:"Name" gorm:"column:name;comment:门检类别名  门检项目名  项目下的门检标签名;type:varchar(50)"` // 门检类别名  门检项目名  项目下的门检标签名
	Sort int64  `json:"Sort" gorm:"column:sort;comment:显示顺序;type:integer"`                        // 显示顺序  ORDER BY Sort ASC

	IsDeleted int64 `json:"IsDeleted" gorm:"column:isdeleted;comment:是否已删除;type:smallint"` // 是否已删除 0否 1是 软删除后不允许恢复

	model.Timestamp
}

func (d *DoorCheckItems) BeforeCreate(tx *gorm.DB) (err error) {
	if d.Id == 0 {
		d.Id = model.Id()
	}
	return
}

// 创建门检类别
func (d *DoorCheckItems) Add() error {
	return model.DB().Create(&d).Error
}

// 创建门检项目和项目标签
func (d *DoorCheckItems) AddItemsAndLabels(normal, abNormal []string) error {

	return model.DB().Transaction(func(tx *gorm.DB) error {
		// 门检项目id
		d.Id = model.Id()
		// 计算该门检项目pid path
		d.ParentIdPath = util.SliceItoA([]int64{d.ParentId, d.Id})
		//
		d.ItemType = CHECK_ITEM_2

		err := tx.Model(&DoorCheckItems{}).Create(&d).Error
		if err != nil {
			return err
		}

		for i, v := range normal {
			var item = *d
			item.Id = model.Id()
			item.ItemType = CHECK_LABEL_3
			item.IsDescNormal = 0
			item.IsDescAbNormal = 0
			item.IsAttachmentAbNormal = 0
			item.Nature = 1
			item.ParentId = d.Id
			item.ParentIdPath = fmt.Sprintf(`%s,%d`, item.ParentIdPath, item.Id)
			item.FkIconFileId = 0
			item.IconFileUrl = ""
			item.Name = v
			item.Sort = int64(i)

			err = tx.Model(&DoorCheckItems{}).Create(&item).Error
			if err != nil {
				return err
			}
		}

		for i, v := range abNormal {
			var item = *d
			item.Id = model.Id()
			item.ItemType = CHECK_LABEL_3
			item.IsDescNormal = 0
			item.IsDescAbNormal = 0
			item.IsAttachmentAbNormal = 0
			item.Nature = 2
			item.ParentId = d.Id
			item.ParentIdPath = fmt.Sprintf(`%s,%d`, item.ParentIdPath, item.Id)
			item.FkIconFileId = 0
			item.IconFileUrl = ""
			item.Name = v
			item.Sort = int64(i)

			err = tx.Model(&DoorCheckItems{}).Create(&item).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}

// 编辑门检类别名
func (d *DoorCheckItems) EditItemTypeName() error {
	return model.DB().Model(&DoorCheckItems{}).Where("Id = ?", d.Id).Select(
		"Name").Updates(d).Error
}

// 软删除 将该项及其子项 IsDeleted=1
func (d *DoorCheckItems) Delete() error {

	return model.DB().Transaction(func(tx *gorm.DB) error {
		var pidPath string

		err := tx.Model(&DoorCheckItems{}).Where("Id = ?", d.Id).Select("ParentIdPath").Scan(&pidPath).Error
		if err != nil {
			return err
		}

		err = tx.Model(&DoorCheckItems{}).Where("ParentIdPath LIKE ?", pidPath+"%").Update("IsDeleted", 1).Error
		if err != nil {
			return err
		}

		return nil
	})

}

// 门检类别
type ItemType struct {
	Id       int64
	IdStr    string // 兼容微信
	Name     string
	ItemList []Item
}

// 门检项目
type Item struct {
	Id                   int64
	IdStr                string // 兼容微信
	Name                 string
	Sort                 int64
	IconFileId           int64  // icon 文件id
	IconFileUrl          string // icon 文件url
	IsDescNormal         int64  // 门检项目-是否描述正常 1描述 2不描述
	IsDescAbNormal       int64  // 门检项目-是否描述异常 1描述 2不描述
	IsAttachmentAbNormal int64  // 门检项目-异常上传附件 1描述 2不描述
	NormalLabels         []Label
	AbnormalLabels       []Label
}

// 门检标签
type Label struct {
	Id    int64
	IdStr string
	Name  string
}

// 自定义门检列表
func (d *DoorCheckItems) List() ([]ItemType, error) {
	var rsp []ItemType

	rows, err := model.DB().Model(&DoorCheckItems{}).Where(
		"CompanyId = ? AND WorkPostType = ? AND ItemType = ? AND ParentId = ? AND IsDeleted = ?",
		d.CompanyId, d.WorkPostType, CHECK_TYPE_1, 0, 0,
	).Select("Id", "Name").Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	for rows.Next() {
		var it ItemType
		err = rows.Scan(&it.Id, &it.Name)
		if err != nil {
			return nil, err
		}
		it.IdStr = strconv.FormatInt(it.Id, 10)

		err = func() error {
			rows2, err := model.DB().Model(&DoorCheckItems{}).Where(
				"ParentId = ? AND IsDeleted = ? AND ItemType = ?", it.Id, 0, CHECK_ITEM_2).Select(
				"Id", "Name", "Sort", "FkIconFileId", "IconFileUrl", "IsDescNormal", "IsDescAbNormal", "IsAttachmentAbNormal").Order("Sort ASC").Rows()
			if err != nil {
				return err
			}
			defer rows2.Close()
			for rows2.Next() {
				var item Item
				err = rows2.Scan(&item.Id, &item.Name, &item.Sort, &item.IconFileId, &item.IconFileUrl, &item.IsDescNormal, &item.IsDescAbNormal, &item.IsAttachmentAbNormal)
				if err != nil {
					return err
				}
				item.IdStr = strconv.FormatInt(item.Id, 10)

				err = func() error {
					rows3, err := model.DB().Model(&DoorCheckItems{}).Where(
						"ParentId = ? AND IsDeleted = ? AND ItemType = ?", item.Id, 0, CHECK_LABEL_3).Select(
						"Id", "Name", "Nature").Order("Sort ASC").Rows()
					if err != nil {
						return err
					}
					defer rows3.Close()
					for rows3.Next() {
						var l Label
						var nature int64
						err = rows3.Scan(&l.Id, &l.Name, &nature)
						if err != nil {
							return err
						}
						l.IdStr = strconv.FormatInt(l.Id, 10)
						if nature == 1 {
							item.NormalLabels = append(item.NormalLabels, l)
						} else if nature == 2 {
							item.AbnormalLabels = append(item.AbnormalLabels, l)
						}
					}
					return err
				}()
				if err != nil {
					return err
				}

				it.ItemList = append(it.ItemList, item)

			}
			return err
		}()
		if err != nil {
			return nil, err
		}

		rsp = append(rsp, it)

	}
	return rsp, err

}

// 同一个项目下是否存在相同标签
func (d *DoorCheckItems) FindName(nature int64, name string) bool {
	var count int64
	model.DB().Model(&DoorCheckItems{}).Where(
		"ParentId = ? AND Nature = ? AND IsDeleted = ? AND Name LIKE ?",
		d.Id, nature, 0, name).Limit(1).Count(&count)
	if count > 0 {
		return true
	} else {
		return false
	}
}

// 删除项目下的标签 新增项目下的标签 编辑项目
func (d *DoorCheckItems) Edit(delNormalLabels, delAbNormalLabels, addNormalLabels, addAbNormalLabels []string) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		// 更新项目信息
		err := tx.Model(&DoorCheckItems{}).Where("Id = ?", d.Id).Select(
			"FkIconFileId", "IconFileUrl", "Sort", "IsDescNormal", "IsDescAbNormal", "IsAttachmentAbNormal").Updates(&d).Error
		if err != nil {
			return err
		}

		// 删除正常标签
		for _, label := range delNormalLabels {
			err = tx.Model(&DoorCheckItems{}).Where("ParentId = ? AND Nature = ? AND IsDeleted = ? AND Name LIKE ?",
				d.Id, 1, 0, label).Update("IsDeleted", 1).Error
			if err != nil {
				return err
			}
		}

		// 删除异常标签
		for _, label := range delAbNormalLabels {
			err = tx.Model(&DoorCheckItems{}).Where("ParentId = ? AND Nature = ? AND IsDeleted = ? AND Name LIKE ?",
				d.Id, 2, 0, label).Update("IsDeleted", 1).Error
			if err != nil {
				return err
			}
		}

		// 查询该项目
		err = tx.Model(&DoorCheckItems{}).Where("Id = ?", d.Id).Find(&d).Error
		if err != nil {
			return err
		}

		// 添加正常标签
		for _, label := range addNormalLabels {

			l := *d
			l.Id = model.Id()
			l.ItemType = CHECK_LABEL_3
			l.IsDescNormal = 0
			l.IsDescAbNormal = 0
			l.IsAttachmentAbNormal = 0
			l.Nature = 1
			l.ParentId = d.Id
			l.ParentIdPath = fmt.Sprintf(`%s,%d`, l.ParentIdPath, l.Id)
			l.FkIconFileId = 0
			l.IconFileUrl = ""
			l.Name = label

			err = tx.Model(&DoorCheckItems{}).Create(&l).Error
			if err != nil {
				return err
			}
		}

		// 添加异常标签
		for _, label := range addAbNormalLabels {

			l := *d
			l.Id = model.Id()
			l.ItemType = CHECK_LABEL_3
			l.IsDescNormal = 0
			l.IsDescAbNormal = 0
			l.IsAttachmentAbNormal = 0
			l.Nature = 2
			l.ParentId = d.Id
			l.ParentIdPath = fmt.Sprintf(`%s,%d`, l.ParentIdPath, l.Id)
			l.FkIconFileId = 0
			l.IconFileUrl = ""
			l.Name = label

			err = tx.Model(&DoorCheckItems{}).Create(&l).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}

// 软删除项目及其下标签
func (d *DoorCheckItems) DeleteItemAndLabelWithId() error {
	return model.DB().Model(&DoorCheckItems{}).Find(&d, "Id = ?", d.Id).Where("ParentIdPath LIKE ?", d.ParentIdPath+"%").Update("IsDeleted", 1).Error
}

// 待检列表
func (d *DoorCheckItems) TodoList() ([]ItemType, error) {
	return d.List()
}

// 根据项目id获取类别
func (d *DoorCheckItems) GetCheckTypeById() (DoorCheckItems, error) {
	var rsp DoorCheckItems

	err := model.DB().Model(&DoorCheckItems{}).Where(
		"Id = (?)",
		model.DB().Model(&DoorCheckItems{}).Select("ParentId").Where("Id = ?", d.Id),
	).First(&rsp).Error

	return rsp, err
}

// 根据项目id获取信息(icon)
func (d *DoorCheckItems) GetById() (DoorCheckItems, error) {
	var rsp DoorCheckItems
	err := model.DB().Model(&DoorCheckItems{}).Select("IconFileUrl").Where("Id = ?", d.Id).Scan(&rsp).Error
	return rsp, err
}
