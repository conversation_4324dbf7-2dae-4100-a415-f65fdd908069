package safety

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"strconv"
)

// 一条记录 多个项目
// 每个项目 多个notice
// 每个notice 一个发送人 多个接收人
type DoorCheckNotices struct {
	model.PkId

	FkRecordId int64 `json:"FkRecordId" gorm:"column:fkrecordid;comment:归属记录id;type:bigint"` // 归属记录id 多对一fk -> door_check_record.Id

	FkItemId int64  `json:"FkItemId" gorm:"column:fkitemid;comment:门检项目id;type:bigint"`     // (所属)门检项目id  fk -> door_check_items.Id
	ItemName string `json:"ItemName" gorm:"column:itemname;comment:门检项目名;type:varchar(60)"` // 门检项目名 door_check_items.Name

	SendUserId   int64  `json:"SendUserId" gorm:"column:senduserid;comment:发送人;type:bigint"`          // 发送人
	SendUserName string `json:"SendUserName" gorm:"column:sendusername;comment:发送人;type:varchar(50)"` // 发送人

	ContentJson model.JSON `json:"ContentJson" gorm:"column:contentjson;comment:上传文件评论;type:json"`

	NoticeType int64 `json:"NoticeType" gorm:"column:noticetype;comment:状态;type:smallint"` // 状态 1管理员上报 2(维修厂)解决  还有司机异常上报、管理员解决这两种情况，是只有通知没有消息所以不存表
	Status     int64 `json:"Status" gorm:"column:status;comment:撤销状态;type:smallint"`       // 状态 1正常 2已撤销

	model.Timestamp
}

type ContentJson struct {
	Files  []ResultJsonFile
	Remark string // 评论 留言
}

func (d *DoorCheckNotices) BeforeCreate(tx *gorm.DB) (err error) {
	if d.Id == 0 {
		d.Id = model.Id()
	}
	return
}

// 通知接受人
type DoorCheckNoticeReceives struct {
	model.PkId

	FkRecordId int64 `json:"FkRecordId" gorm:"column:fkrecordid;comment:归属记录id;type:bigint"` // 归属记录id 多对一fk -> door_check_record.Id

	FkItemId int64  `json:"FkItemId" gorm:"column:fkitemid;comment:门检项目id;type:bigint"`     // (所属)门检项目id  fk -> door_check_items.Id
	ItemName string `json:"ItemName" gorm:"column:itemname;comment:门检项目名;type:varchar(60)"` // 门检项目名 door_check_items.Name

	FkNoticeId int64 `json:"FkNoticeId" gorm:"column:fknoticeid;comment:通知id;type:bigint"` // 通知id

	UserId   int64  `json:"UserId" gorm:"column:userid;comment:接收人;type:bigint"`          // 接收人
	UserName string `json:"UserName" gorm:"column:username;comment:接收人;type:varchar(50)"` // 接收人

	model.Timestamp
}

func (d *DoorCheckNoticeReceives) BeforeCreate(tx *gorm.DB) (err error) {
	if d.Id == 0 {
		d.Id = model.Id()
	}
	return
}

func (d *DoorCheckNotices) Add(tx *gorm.DB, recvs []DoorCheckNoticeReceives) error {

	err := tx.Model(&DoorCheckNotices{}).Create(&d).Error
	if err != nil {
		return err
	}

	for _, recv := range recvs {
		item := DoorCheckNoticeReceives{
			PkId:       model.PkId{Id: model.Id()},
			FkRecordId: d.FkRecordId,
			FkItemId:   d.FkItemId,
			ItemName:   d.ItemName,
			FkNoticeId: d.Id,
			UserId:     recv.UserId,
			UserName:   recv.UserName,
			Timestamp:  model.Timestamp{},
		}
		err = tx.Model(&DoorCheckNoticeReceives{}).Create(&item).Error
		if err != nil {
			return err
		}
	}

	return nil

}

func (d *DoorCheckNotices) AddTx(tx *gorm.DB, recvs []DoorCheckNoticeReceives) error {
	if tx == nil {
		begin := model.DB().Begin()
		err := d.Add(begin, recvs)
		if err != nil {
			begin.Rollback()
		} else {
			begin.Commit()
		}
		return err
	} else {
		return d.Add(tx, recvs)
	}
}

// 撤销上报消息
func (d *DoorCheckNotices) EditReportTx(tx *gorm.DB, oldId int64) error {
	return tx.Model(&DoorCheckNotices{}).Where("Id = ?", oldId).Update("Status", 2).Error
}

type Notice struct {
	Id string // notice id -> string
	DoorCheckNotices
	RecvUserId          int64
	RecvUserName        string
	RecvUserCorporation string
}

func (d *DoorCheckNotices) GetByItemId(fkRecordId, fkItemId int64) ([]Notice, error) {
	var rsp []Notice

	var reportNotice DoorCheckNotices
	var resolveNotice DoorCheckNotices

	err := model.DB().Model(&DoorCheckNotices{}).Where("FkRecordId = ? AND FkItemId = ? AND Status = ? AND NoticeType= ?", fkRecordId, fkItemId, 1, 1).Order("CreatedAt DESC").First(&reportNotice).Error
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}
	err = model.DB().Model(&DoorCheckNotices{}).Where("FkRecordId = ? AND FkItemId = ? AND Status = ? AND NoticeType= ?", fkRecordId, fkItemId, 1, 2).Order("CreatedAt DESC").First(&resolveNotice).Error
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}

	// 获取 上报 接收人 当前只有一人
	var reportRecv DoorCheckNoticeReceives
	err = model.DB().Model(&DoorCheckNoticeReceives{}).Where("FkNoticeId = ?", reportNotice.Id).Order("CreatedAt DESC").First(&reportRecv).Error
	if err != nil {
		return nil, err
	}

	rsp = append(rsp, Notice{
		Id:                  strconv.FormatInt(reportNotice.Id, 10),
		DoorCheckNotices:    reportNotice,
		RecvUserId:          reportRecv.UserId,
		RecvUserName:        reportRecv.UserName,
		RecvUserCorporation: "",
	})

	rsp = append(rsp, Notice{
		Id:                  strconv.FormatInt(resolveNotice.Id, 10),
		DoorCheckNotices:    resolveNotice,
		RecvUserId:          0,
		RecvUserName:        "",
		RecvUserCorporation: "",
	})

	return rsp, err

}
