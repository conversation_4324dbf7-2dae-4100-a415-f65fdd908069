package safety

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

// 配置门检检查人员 每个车队每天值班人员
type DoorCheckInspectors struct {
	model.PkId

	model.GroupCorporation
	model.CompanyCorporation
	model.BranchCorporation
	model.DepartmentCorporation
	model.FleetCorporation

	UserId int64     `json:"UserId" gorm:"column:userid;comment:值班人员 userId;type:bigint"` // 值班人员 userId
	Name   string    `json:"Name" gorm:"column:name;comment:值班人员 姓名;type:varchar(50)"`    // 值班人员 姓名
	DutyAt time.Time `json:"DutyAt" gorm:"column:dutyat;comment:值班时间;type:timestamptz"`   // 值班时间

	model.Timestamp
}

func (d *DoorCheckInspectors) BeforeCreate(tx *gorm.DB) (err error) {
	if d.Id == 0 {
		d.Id = model.Id()
	}
	return
}

// 添加数据
func (d *DoorCheckInspectors) Add() error {
	return model.DB().Model(&DoorCheckInspectors{}).Create(d).Error
}

//type InspectorsListResponse struct {
//	Data []DoorCheckInspectors
//}

// 查询列表
func (d *DoorCheckInspectors) List(startAt, endAt time.Time) ([]DoorCheckInspectors, error) {
	var rsp []DoorCheckInspectors
	err := model.DB().Model(&DoorCheckInspectors{}).Select("Id", "UserId", "Name", "DutyAt").Where(
		"FleetId = ? AND DutyAt >= ? AND DutyAt < ?", d.FleetId, startAt, endAt).Order("DutyAt ASC, CreatedAt ASC").Scan(&rsp).Error
	return rsp, err
}

// 删除
func (d *DoorCheckInspectors) DeleteByIds(ids []int64) error {
	return model.DB().Where("Id IN ?", ids).Delete(&DoorCheckInspectors{}).Error
}
