package safety

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"time"
)

// TrafficAccident 事故
type TrafficAccident struct {
	model.PkId
	model.Corporations                 //集团、公司、分公司、部门、车队
	Code               string          `json:"Code" gorm:"column:code;type:varchar;comment:事故编号"`                                                       //事故编号 分公司序号-事故发生日期-车队号-车号-今年第几个事故（公司下所有的事故）
	CodeSequence       int64           `json:"CodeSequence" gorm:"column:codesequence;type:integer;default:0;comment:分公司在一年中的事故序号"`                     //分公司在一年中的事故序号
	HappenAt           model.LocalTime `json:"HappenAt" gorm:"column:happenat;type:timestamp;comment:事故发生时间" validate:"required"`                       //事故发生时间
	Grade              int64           `json:"Grade" gorm:"column:grade;type:smallint;comment:事故级别 1-轻微事故,2-ICU以上事故,3-留院观察,4-住院治疗" validate:"required"` //事故级别 1-轻微事故,2-ICU以上事故,3-留院观察,4-住院治疗
	IsMinor            int64           `json:"IsMinor" gorm:"column:isminor;comment:是否为小额事故，2-否 1-是;type:smallint;default:2"`                           // 是否为小额事故，1-是 2-否
	//Cate                        int64                    `json:"Cate" gorm:"column:cate;type:smallint;comment:事故类别" validate:"required"`                                  //事故类别 1-车内伤,2-车外伤,3-单方事故,4-倒车,5-刮擦,6-追尾,7-侧翻,8-其他,9-玻璃自爆与破损
	VehicleBrokenCate           int64                    `json:"VehicleBrokenCate" gorm:"column:vehiclebrokencate;type:smallint;comment:车损类别 1-刮擦车损、2-倒车车损、3-追尾车损、4-侧翻车损、5-玻璃自爆与破损、6-场站刮擦（物业）、9-无车损" validate:"required"` //车损类别 1-刮擦车损、2-倒车车损、3-追尾车损、4-侧翻车损、5-玻璃自爆与破损、6-场站刮擦（物业）、9-无车损
	PeopleHurtCate              int64                    `json:"PeopleHurtCate" gorm:"column:peoplehurtcate;type:smallint;comment:人伤类别 1-无人伤、2-车内伤、3-车外伤、4-刮擦导致人伤、5-倒车导致人伤、6-追尾导致人伤、7-侧翻导致人伤" validate:"required"`        //人伤类别 1-无人伤、2-车内伤、3-车外伤、4-刮擦导致人伤、5-倒车导致人伤、6-追尾导致人伤、7-侧翻导致人伤
	IsVideoBroken               int64                    `json:"IsVideoBroken" gorm:"column:isvideobroken;type:smallint;comment:是否视频损坏 1是,2否;default:2"`                                                                  //是否视频损坏 1是,2否
	DriverId                    int64                    `json:"DriverId" gorm:"column:driverid;type:integer;comment:司机ID"`                                                                                               //司机ID id>0:司机正式工 id=0:司机实习工
	DriverName                  string                   `json:"DriverName" gorm:"column:drivername;type:varchar;comment:司机姓名" validate:"required"`                                                                       //司机姓名
	VehicleId                   int64                    `json:"VehicleId" gorm:"column:vehicleid;type:integer;comment:车辆ID" validate:"required"`                                                                         //车辆ID
	License                     string                   `json:"License" gorm:"column:license;type:varchar;comment:车牌" validate:"required"`                                                                               //车牌
	InsuranceCompany            string                   `json:"InsuranceCompany" gorm:"column:insurancecompany;type:varchar;comment:保险公司"`                                                                               //保险公司
	InsuranceCode               string                   `json:"InsuranceCode" gorm:"column:insurancecode;type:varchar;comment:交强险凭证号"`                                                                                   //交强险凭证号
	InsuranceFilePath           string                   `json:"InsuranceFilePath" gorm:"column:insurancefilepath;type:varchar;comment:交强保险单"`
	CommercialInsuranceCode     string                   `json:"CommercialInsuranceCode" gorm:"column:commercialinsurancecode;type:varchar;comment:商业险凭证号"` //商业险凭证号
	CommercialInsuranceFilePath string                   `json:"CommercialInsuranceFilePath" gorm:"column:commercialinsurancefilepath;type:varchar;comment:商业险保单"`
	LineId                      int64                    `json:"LineId" gorm:"column:lineid;type:integer;comment:线路ID"`                                                              //线路ID
	LineName                    string                   `json:"LineName" gorm:"column:linename;type:varchar;comment:线路名称"`                                                          //线路名称
	LiabilityType               int64                    `json:"LiabilityType" gorm:"column:liabilitytype;type:smallint;comment:责任认定 0未知,1-全责,2-主责,3-同责,4-次责,5-无责 6待定"`              //责任认定 0未知,1-全责,2-主责,3-同责,4-次责,5-无责 6待定
	HappenLocation              string                   `json:"HappenLocation" gorm:"column:happenlocation;type:varchar;comment:事故发生地点" validate:"required"`                        //事故发生地点
	HappenLocationDesc          string                   `json:"HappenLocationDesc" gorm:"column:happenlocationdesc;type:varchar;comment:事故发生地点补充"`                                  //事故发生地点
	HappenLat                   int64                    `json:"HappenLat" gorm:"column:happenlat;type:integer;comment:发生地点纬度"`                                                      //发生地点纬度
	HappenLng                   int64                    `json:"HappenLng" gorm:"column:happenlng;type:integer;comment:发生地点经度"`                                                      //发生地点经度
	WeatherCondition            int64                    `json:"WeatherCondition" gorm:"column:weathercondition;type:smallint;comment:天气情况" validate:"required"`                     //天气情况 1-雾,2-雨,3-冰雹,4-晴,5-其他,6-台风,7-雨雪,8-阴
	Desc                        string                   `json:"Desc" gorm:"column:desc;type:varchar;comment:事故概况" validate:"required"`                                              //事故概况
	More                        string                   `json:"More" gorm:"column:more;type:varchar;comment:备注"`                                                                    //备注
	ReportCaseWay               string                   `json:"ReportCaseWay" gorm:"column:reportcaseway;type:varchar;comment:报案方式 " validate:"required"`                           //报案方式 1-110,2-120,3-保险公司,4-其他,5-对方报案 多个用|分割
	ReportCaseCode              string                   `json:"ReportCaseCode" gorm:"column:reportcasecode;type:varchar;comment:保险公司报案号，报案方式为保险公司时必传;"`                             //保险公司报案号，报案方式为保险公司时必传
	ReportCaseFile              model.JSON               `json:"ReportCaseFile" gorm:"column:reportcasefile;type:json;comment:报警凭证，报案方式为保险公司时必传;"`                                   //报警凭证，报案方式为保险公司时必传
	ReportCaseAt                model.LocalTime          `json:"ReportCaseAt" gorm:"column:reportcaseat;type:timestamp;comment:报案时间"`                                                //报案时间
	ReportCaseStaffId           int64                    `json:"ReportCaseStaffId" gorm:"column:reportcasestaffid;type:integer;comment:报案人ID"`                                       //报案人ID 主数据人员ID(对方报案时此字段无值)
	ReportCaseStaffName         string                   `json:"ReportCaseStaffName" gorm:"column:reportcasestaffname;type:varchar;comment:报案人姓名"`                                   //报案人姓名
	RoadCondition               int64                    `json:"RoadCondition" gorm:"column:roadcondition;type:smallint;comment:路况;default:0" validate:"required"`                   //路况 1-斑马线、2-有红绿灯路口、3-无红绿灯路口、4-公交站点、5-场站内、6-其他
	DriverViolations            string                   `json:"DriverViolations" gorm:"column:driverviolation;type:varchar;comment:司机违章行为;default:"`                                //司机违章行为，多个用,分割
	DriverViolationDesc         string                   `json:"DriverViolationDesc" gorm:"column:driverviolationdesc;type:varchar;default:;comment:司机违章行为说明"`                       //司机违章行为说明
	ReportFilePath              model.JSON               `json:"ReportFilePath" gorm:"column:reportfilepath;type:json;comment:现场照片" validate:"required"`                             //现场照片                                                         //补充附件
	FilePath                    model.JSON               `json:"FilePath" gorm:"column:filepath;type:json;comment:补充附件"`                                                             //补充附件
	HurtStatus                  int64                    `json:"HurtStatus" gorm:"column:hurtstatus;type:smallint;default:0;comment:损伤情况 0未知  1-无车损无人伤,2-无车损有人伤,3-有车损无人伤,4-有车损有人伤" ` //损伤情况 0未知  1-无车损无人伤,2-无车损有人伤,3-有车损无人伤,4-有车损有人伤
	IsMustFix                   int64                    `json:"IsMustFix" gorm:"column:ismustfix;type:smallint;default:0;comment:是否需要维修 1是 2否" validate:"required"`                 //是否需要维修 1是 2否
	ConfirmFilePath             model.JSON               `json:"ConfirmFilePath" gorm:"column:confirmfilepath;type:json;comment:事故责任认定附件"`                                           //事故责任认定附件
	OwnStatus                   string                   `json:"OwnStatus" gorm:"column:ownstatus;type:varchar(50);comment:己方损失情况 3己方车损 4己方人伤 多个|隔开;"`                               // 己方损失情况 3己方车损 4己方人伤 多个|隔开
	DriverLicenseFilePath       model.JSON               `json:"DriverLicenseFilePath" gorm:"column:driverlicensefilepath;type:json;comment:驾驶证"`                                    //驾驶证                                                         //补充附件
	DrivingLicenseFilePath      model.JSON               `json:"DrivingLicenseFilePath" gorm:"column:drivinglicensefilepath;type:json;comment:行驶证"`
	ApplyStatus                 int64                    `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;comment:事故上报审批状态;default:0"`                     //事故上报审批状态 0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃
	EditApplyStatus             int64                    `json:"EditApplyStatus" gorm:"column:editapplystatus;type:smallint;comment:事故变更审批状态;default:0"`             //事故变更审批状态 0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃
	ClosedApplyStatus           int64                    `json:"ClosedApplyStatus" gorm:"column:closedapplystatus;type:smallint;comment:事故结案审批状态(暂时废弃不用);default:0"` //事故结案审批状态 0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃
	OpenStatus                  int64                    `json:"OpenStatus" gorm:"column:openstatus;type:smallint;comment:公开状态：1-公开，2-不公开，3草稿;default:1"`            //公开状态，1-公开，2-不公开（上报后未审批通过的，不公开），3-草稿
	IsClosed                    int64                    `json:"IsClosed" gorm:"column:isclosed;type:integer;default:2;comment:是否结案"`                                //是否结案  1是结案,2-未结案
	IsRecycle                   int64                    `json:"IsRecycle" gorm:"column:isrecycle;type:smallint;comment:是否在回收站 1是 2否;default:2"`                     // 是否在回收站 1是 2否
	RecycleAt                   model.LocalTime          `json:"RecycleAt" gorm:"column:recycleat;type:timestamp;comment:移入回收站时间;"`                                  // 移入回收站时间
	IsUpdateForm                int64                    `json:"IsUpdateForm" gorm:"column:isupdateform;type:smallint;comment:是否可以编辑发起事故结案流程的表单数据 1是 2否;default:0"`  // 是否可以编辑发起事故结案流程的表单数据 1是 2否
	Relaters                    []TrafficAccidentRelater `json:"Relaters"`                                                                                           //事故当事人

	//出纳环节填写的字段
	CashierPayMoneyParam

	//以下是事故结案后填充的字段
	PersonalPayRatio         int64           `json:"PersonalPayRatio" gorm:"column:personalpayratio;type:integer;comment:损失金额个人承担比例"`          //损失金额个人承担比例  1028=>10.28%
	LossMoney                int64           `json:"LossMoney" gorm:"column:lossmoney;type:bigint;comment:经济损失"`                               //经济损失 单位：分
	InsuranceCompanyPayMoney int64           `json:"InsuranceCompanyPayMoney" gorm:"column:insurancecompanypaymoney;type:bigint;comment:赔进金额"` //赔进金额 单位：分
	InsurancePayMoney        int64           `json:"InsurancePayMoney" gorm:"column:insurancepaymoney;type:bigint;comment:保险直赔金额"`             //保险直赔金额 单位：分
	CloseDesc                string          `json:"CloseDesc" gorm:"column:closedesc;type:varchar;comment:结案说明"`                              //结案说明
	CloseFilePath            model.JSON      `json:"CloseFilePath" gorm:"column:closefilepath;type:json;comment:结案附件"`                         //结案附件
	ClosedAt                 model.LocalTime `json:"ClosedAt" gorm:"column:closedat;type:timestamp;comment:结案时间"`                              //结案时间
	PayOrigin                int64           `json:"PayOrigin" gorm:"column:payorigin;type:smallint;comment:赔付来源 1保险公司 2当事人;default:0"`        // 赔付来源 1保险公司 2当事人
	IsPrinted                int64           `json:"IsPrinted" gorm:"column:isprinted;type:smallint;comment:是否已经打印 1是 2否;default:0"`           // 是否已经打印 1是 2否
	IsPushWarningNotify      int64           `json:"IsPushWarningNotify" gorm:"column:ispushwarningnotify;type:smallint;comment:是否推送事故预警告警消息 1是 2否;default:1"`
	//以下是安委会审核字段
	CheckLiabilityType int64      `json:"CheckLiabilityType" gorm:"column:checkliabilitytype;type:smallint;comment:安委会审核责任认定 0未知,1-全责,2-主责,3-同责,4-次责,5-无责 6待定"`
	CheckHandleResult  int64      `json:"CheckHandleResult" gorm:"column:checkhandleresult;type:smallint;default:0;comment:安委会处理结果 0未处理过 1减轻处罚 2免于处罚"`
	CheckHandleMore    string     `json:"CheckHandleMore" gorm:"column:checkhandlemore;type:text;comment:安委会处理备注"`
	CheckHandleFiles   model.JSON `json:"CheckHandleFiles" gorm:"column:checkhandlefiles;type:json;comment:安委会处理附件"`

	model.OpUser
	model.Timestamp

	FileHttpPrefix                    string           `json:"FileHttpPrefix" gorm:"-"`
	CorporationName                   string           `json:"CorporationName" gorm:"-"`
	CorporationId                     int64            `json:"CorporationId" gorm:"-"`
	HasRelater                        bool             `json:"HasRelater" gorm:"-"`
	IsProcessHandler                  bool             `json:"IsProcessHandler" gorm:"-"`                  //是否是事故上报、事故结案流程的相关人
	IsEnableClosed                    bool             `json:"IsEnableClosed" gorm:"-"`                    //是否可以结案
	ClosedBranchCount                 int64            `json:"ClosedBranchCount" gorm:"-"`                 //已结案的分支数
	TotalBranchCount                  int64            `json:"TotalBranchCount" gorm:"-"`                  //总的分支数
	HasSelfVehicleBrokenBranch        bool             `json:"HasSelfVehicleBrokenBranch" gorm:"-"`        //是否有己方车损分支
	HasSelfVehicleBrokenBranchProcess bool             `json:"HasSelfVehicleBrokenBranchProcess" gorm:"-"` //是否有己方车损分支相关的流程
	HasApplyingPaymentMoneyProcess    bool             `json:"HasApplyingPaymentMoneyProcess" gorm:"-"`    //是否有正在审批中的付款流程
	HasApplyingLendMoneyProcess       bool             `json:"HasApplyingLendMoneyProcess" gorm:"-"`       //是否有正在审批中的借款流程
	HasPaymentProcess                 bool             `json:"HasApprovingPaymentProcess" gorm:"-"`        //是否有正在审批或者审批通过的付款流程
	HasDoingProcess                   bool             `json:"HasDoingProcess" gorm:"-"`                   //是否有进行中、已通过、撤回、驳回的流程 包括付款、借款、退款、分支结案
	ApplyAt                           *model.LocalTime `json:"ApplyAt" gorm:"-"`                           // 事故流程发起时间
	HasSelfPeopleHurtProcess          bool             `json:"HasSelfPeopleHurtProcess" gorm:"-"`          //是否有己方人伤相关的流程

	// 消息中心显示额外字段
	TrafficAccidentLendMoneyRecordTotal int64  `json:"TrafficAccidentLendMoneyRecordTotal" gorm:"-"` // 借款总金额
	OpIp                                string `json:"OpIp" gorm:"-"`
	InsuranceFileUrl                    string `json:"InsuranceFileUrl" gorm:"-"`
	CommercialInsuranceFileUrl          string `json:"CommercialInsuranceFileUrl" gorm:"-"`

	// 以下是发起事故同时发起维修付款时的参数
	IsCreatePayment             int64      `json:"IsCreatePayment" gorm:"column:iscreatepayment;type:smallint;comment:是否发起借款流程 1是 2否;default:2"`
	TmpFixOffice                int64      `json:"TmpFixOffice" gorm:"column:tmpfixoffice;type:smallint;comment:维修车间 1-黄岩客运西站 2-椒江景元路 3-椒江客运总站 4-椒江工人路 5-路桥桐屿 6-路桥金清 7-综合场站 8-回浦  9-洪家;default:0"`
	TmpIsFull                   int64      `json:"TmpIsFull" gorm:"column:tmpisfull;type:smallint;comment:赔付方式 1保险赔付 2我方司机赔付 3对方赔付;default:0"`
	TmpSolutionType             int64      `json:"TmpSolutionType" gorm:"column:tmpsolutiontype;type:smallint;comment:解决方式  1-协商处理 2-交警简易程序 3-法院判决 4-保险公司 5-其他方式;default:0"`
	TmpSolutionFilePath         model.JSON `json:"TmpSolutionFilePath" gorm:"column:tmpsolutionfilepath;type:json;comment:解决方式对应的附件;"`
	TmpInsuranceCompanyPayMoney int64      `json:"TmpInsuranceCompanyPayMoney" gorm:"column:tmpinsurancecompanypaymoney;type:integer;comment:保险公司赔付金额 单位：分;default:0"`
	TmpInsurancePayMoney        int64      `json:"TmpInsurancePayMoney" gorm:"column:tmpinsurancepaymoney;type:integer;comment:保险直赔金额 单位：分;default:0"`
	TmpLossMoney                int64      `json:"TmpLossMoney" gorm:"column:tmplossmoney;type:integer;comment:经济损失 单位：分;default:0"`
	TmpSolutionDesc             string     `json:"TmpSolutionDesc" gorm:"column:tmpsolutiondesc;text;comment:结案说明;default:''"`
	TmpPayOrigin                int64      `json:"TmpPayOrigin" gorm:"column:tmppayorigin;type:smallint;comment:赔付来源 1:保险公司 2:当事人;default:0"`
	TmpPersonalPayRatio         int64      `json:"TmpPersonalPayRatio" gorm:"column:tmppersonalpayratio;type:integer;comment:损失金额个人承担比例 1028=>10.28%;default:0"`
}

func (ta *TrafficAccident) TableName() string {
	return "traffic_accidents"
}
func (ta *TrafficAccident) MessageType() string {
	return "traffic_accidents"
}
func (ta *TrafficAccident) ApplyStatusFieldName() string {
	return "applystatus"
}
func (ta *TrafficAccident) ClosedApplyStatusFieldName() string {
	return "closedapplystatus"
}
func (ta *TrafficAccident) EditApplyStatusFieldName() string {
	return "editapplystatus"
}

func (ta *TrafficAccident) BeforeCreate(tx *gorm.DB) error {
	ta.IsRecycle = util.StatusForFalse
	ta.Id = model.Id()
	if ta.OpenStatus != util.AccidentOpenStatusForDraft {
		var maxSequenceRecord TrafficAccident
		start := time.Date(time.Time(ta.HappenAt).Year(), 1, 1, 0, 0, 0, 0, time.Local)
		end := start.AddDate(1, 0, 0)
		tx.Model(&TrafficAccident{}).Where("BranchId = ? AND HappenAt >= ? AND HappenAt < ?", ta.BranchId, start.Format(model.TimeFormat), end.Format(model.TimeFormat)).Order("CodeSequence DESC").First(&maxSequenceRecord)
		ta.Code = fmt.Sprintf("%s-%04d", ta.Code, maxSequenceRecord.CodeSequence+1)
		ta.CodeSequence = maxSequenceRecord.CodeSequence + 1
	}
	for i := range ta.Relaters {
		for j := range ta.Relaters[i].Branches {
			ta.Relaters[i].Branches[j].TrafficAccidentId = ta.Id
		}
	}

	return nil
}

// Create 创建
func (ta *TrafficAccident) Create() error {
	return model.DB().Create(&ta).Error
}

// TransactionCreate 事务创建
func (ta *TrafficAccident) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&ta).Error
}

func (ta *TrafficAccident) UpdateDraftAll() error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := model.DB().Select("*").Omit("Relaters", "IsRecycle").Updates(&ta).Error
		if err != nil {
			return err
		}

		if len(ta.Relaters) > 0 {
			for i := range ta.Relaters {
				if ta.Relaters[i].ActionType == "create" {
					ta.Relaters[i].TrafficAccidentId = ta.Id
					for j := range ta.Relaters[i].Branches {
						ta.Relaters[i].Branches[j].TrafficAccidentId = ta.Id
					}
					_ = ta.Relaters[i].Create()

				}

				if ta.Relaters[i].ActionType == "edit" {
					_ = ta.Relaters[i].Update()
					model.DB().Where("TrafficAccidentRelaterId = ?", ta.Relaters[i].Id).Delete(&TrafficAccidentRelaterBranch{})
					for j := range ta.Relaters[i].Branches {
						ta.Relaters[i].Branches[j].TrafficAccidentId = ta.Id
						ta.Relaters[i].Branches[j].TrafficAccidentRelaterId = ta.Relaters[i].Id
						_ = ta.Relaters[i].Branches[j].Create()
					}
				}

				if ta.Relaters[i].ActionType == "delete" {
					_ = ta.Relaters[i].Delete(ta.Relaters[i].Id)
					model.DB().Where("TrafficAccidentRelaterId = ?", ta.Relaters[i].Id).Delete(&TrafficAccidentRelaterBranch{})
				}
			}
		}
		return nil
	})
}

func (ta *TrafficAccident) TransactionUpdateAll(tx *gorm.DB) error {
	err := tx.Select("*").Omit("Relaters", "IsRecycle").Updates(&ta).Error
	if err != nil {
		return err
	}

	if len(ta.Relaters) > 0 {
		for i := range ta.Relaters {
			if ta.Relaters[i].ActionType == "create" {
				ta.Relaters[i].TrafficAccidentId = ta.Id
				ta.Relaters[i].Id = model.Id()
				for j := range ta.Relaters[i].Branches {
					ta.Relaters[i].Branches[j].TrafficAccidentId = ta.Id
				}
				if err := tx.Create(&(ta.Relaters[i])).Error; err != nil {
					return err
				}
			}

			if ta.Relaters[i].ActionType == "edit" {
				if err := tx.Select("*").Omit("CreatedAt", "Branches").Updates(&(ta.Relaters[i])).Error; err != nil {
					return err
				}
				err = tx.Where("TrafficAccidentRelaterId = ?", ta.Relaters[i].Id).Delete(&TrafficAccidentRelaterBranch{}).Error
				if err != nil {
					return err
				}
				for j := range ta.Relaters[i].Branches {
					ta.Relaters[i].Branches[j].TrafficAccidentId = ta.Id
					ta.Relaters[i].Branches[j].TrafficAccidentRelaterId = ta.Relaters[i].Id
					err = tx.Create(&(ta.Relaters[i].Branches[j])).Error
					if err != nil {
						return err
					}
				}
			}

			if ta.Relaters[i].ActionType == "delete" {
				if err := tx.Where("Id = ?", ta.Relaters[i].Id).Delete(&TrafficAccidentRelater{}).Error; err != nil {
					return err
				}

				err = tx.Where("TrafficAccidentRelaterId = ?", ta.Relaters[i].Id).Delete(&TrafficAccidentRelaterBranch{}).Error
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

// Update 更新
func (ta *TrafficAccident) Update() error {
	return model.DB().Model(&TrafficAccident{}).Where("Id = ?", ta.Id).Omit("Code", "ConfirmFilePath", "Relaters", "IsRecycle").Updates(&ta).Error
}

// TransactionUpdate 更新
func (ta *TrafficAccident) TransactionUpdate(tx *gorm.DB) error {
	return tx.Model(&TrafficAccident{}).Select("*").Omit("Id", "Code", "CodeSequence", "ApplyStatus", "EditApplyStatus", "ClosedApplyStatus", "OpenStatus",
		"IsClosed", "IsRecycle", "RecycleAt", "IsUpdateForm", "Relaters", "PersonalPayRatio", "LossMoney", "InsuranceCompanyPayMoney",
		"InsurancePayMoney", "CloseDesc", "CloseFilePath", "ClosedAt", "PayOrigin", "IsPrinted", "FormStep", "IsMustPay", "PayMoneyAt",
		"PayMoneyFile").Where("Id = ?", ta.Id).Updates(&ta).Error
}

func (ta *TrafficAccident) GetCountByTime(branchId int64, start, end time.Time) int64 {
	var accident TrafficAccident
	model.DB().Model(&TrafficAccident{}).
		Where("BranchId = ? AND HappenAt >= ? AND HappenAt < ?", branchId, start.Format(model.TimeFormat), end.Format(model.TimeFormat)).
		Order("CodeSequence DESC").First(&accident)

	return accident.CodeSequence
}

// FindBy 查询详情
func (ta *TrafficAccident) FindBy(id int64) error {
	return model.DB().Model(&TrafficAccident{}).Where("Id = ?", id).First(&ta).Error
}

// ExistBy 是否存在
func (ta *TrafficAccident) ExistBy(id int64) bool {
	var count int64
	model.DB().Model(&TrafficAccident{}).Where("Id = ?", id).Count(&count)
	return count > 0
}

// Delete 删除
func (ta *TrafficAccident) Delete(id int64) error {
	return model.DB().Where("Id = ?", id).Delete(&TrafficAccident{}).Error
}

// UpdateColumn 更新列
func (ta *TrafficAccident) UpdateColumn(id int64, column string, value interface{}) error {
	return model.DB().Model(&TrafficAccident{}).Where("Id = ?", id).UpdateColumn(column, value).Error
}

func (ta *TrafficAccident) UpdateByMap(id int64, values interface{}) error {
	return model.DB().Model(&TrafficAccident{}).Where("Id = ?", id).Updates(values).Error
}

// TransactionUpdateColumn 更新列
func (ta *TrafficAccident) TransactionUpdateColumn(tx *gorm.DB, id int64, column string, value interface{}) error {
	return tx.Model(&TrafficAccident{}).Where("Id = ?", id).UpdateColumn(column, value).Error
}

func (ta *TrafficAccident) CheckIsEnableClosed() bool {
	//没有分支的事故或者有分支并且所有分支都已经结案的事故可以结案
	var count int64
	model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("TrafficAccidentId = ? AND IsClosed = ?", ta.Id, util.StatusForFalse).Count(&count)

	return count == 0
}

// TransactionUpdateCloseInfo 事务更新结案数据
func (ta *TrafficAccident) TransactionUpdateCloseInfo(tx *gorm.DB) error {
	return tx.Select("ClosedApplyStatus", "FormStep", "PersonalPayRatio", "LossMoney", "InsuranceCompanyPayMoney", "InsurancePayMoney", "CloseDesc", "CloseFilePath", "PayOrigin").Updates(&ta).Error
}

// DashboardUpdateCloseInfo 更新结案数据
func (ta *TrafficAccident) DashboardUpdateCloseInfo() error {
	return model.DB().Select("ClosedApplyStatus", "FormStep", "PersonalPayRatio", "LossMoney", "InsuranceCompanyPayMoney", "InsurancePayMoney", "CloseDesc", "CloseFilePath", "PayOrigin", "IsMustPay", "PayMoneyAt", "PayMoneyFile").Updates(&ta).Error
}

// UpdateCloseInfo 更新结案数据
func (ta *TrafficAccident) UpdateCloseInfo() error {
	return model.DB().Select("ClosedApplyStatus", "FormStep", "PersonalPayRatio", "LossMoney", "InsuranceCompanyPayMoney", "InsurancePayMoney", "CloseDesc", "CloseFilePath", "PayOrigin").Updates(&ta).Error
}

// UpdateCheckHandle 更新安委会审核信息
func (ta *TrafficAccident) UpdateCheckHandle() error {
	return model.DB().Select("CheckLiabilityType", "CheckHandleResult", "CheckHandleMore", "CheckHandleFiles").Updates(&ta).Error
}

// TransactionUpdateClose 事务更新结案状态
func (ta *TrafficAccident) TransactionUpdateClose(tx *gorm.DB) error {
	return tx.Model(&TrafficAccident{}).Where("Id=?", ta.Id).Updates(map[string]interface{}{"isclosed": util.StatusForTrue, "closedat": time.Now().Format(model.TimeFormat)}).Error
}

// GetBy 查询列表
func (ta *TrafficAccident) GetBy(userId int64, where map[string]interface{}, paginator model.Paginator, orderBy, order string) ([]TrafficAccident, int64) {
	var accidents []TrafficAccident
	tx := model.DB().Model(&TrafficAccident{}).Where(model.DB().Where(model.DB().Where("OpenStatus = ?", util.AccidentOpenStatusForOpen).Scopes(model.WhereCorporations(where["CorporationIds"].([]int64)))).
		Or("OpenStatus IN ? AND OpUserId = ?", []int64{util.AccidentOpenStatusForClosed, util.AccidentOpenStatusForDraft}, userId)).
		Preload("Relaters")

	//事故编号
	if _, ok := where["Code"]; ok && where["Code"].(string) != "" {
		tx = tx.Where("Code LIKE ?", "%"+where["Code"].(string)+"%")
	}

	if _, ok := where["DriverName"]; ok && where["DriverName"].(string) != "" {
		tx = tx.Where("DriverName LIKE ?", "%"+where["DriverName"].(string)+"%")
	}

	//线路
	if _, ok := where["LineId"]; ok && where["LineId"].(int64) != 0 {
		tx = tx.Where("LineId = ?", where["LineId"])
	}

	//车牌号
	if _, ok := where["License"]; ok && where["License"].(string) != "" {
		tx = tx.Where("License = ?", where["License"])
	}

	//事故级别
	if _, ok := where["Grade"]; ok && where["Grade"].(int64) != 0 {
		tx = tx.Where("Grade = ?", where["Grade"])
	}

	//事故类别
	//if _, ok := where["Cate"]; ok && where["Cate"].(int64) != 0 {
	//	tx = tx.Where("Cate = ?", where["Cate"])
	//}

	//车损类别
	if _, ok := where["VehicleBrokenCate"]; ok && where["VehicleBrokenCate"].(int64) != 0 {
		tx = tx.Where("VehicleBrokenCate = ?", where["VehicleBrokenCate"])
	}
	//人伤类别
	if _, ok := where["PeopleHurtCate"]; ok && where["PeopleHurtCate"].(int64) != 0 {
		tx = tx.Where("PeopleHurtCate = ?", where["PeopleHurtCate"])
	}

	//事故责任认定
	if _, ok := where["LiabilityType"]; ok && where["LiabilityType"].(int64) != 0 {
		tx = tx.Where("LiabilityType = ?", where["LiabilityType"])
	}

	//是否维修
	if _, ok := where["IsMustFix"]; ok && where["IsMustFix"].(int64) != 0 {
		tx = tx.Where("IsMustFix = ?", where["IsMustFix"].(int64))
	}

	//事故发生地点
	if _, ok := where["HappenLocation"]; ok && where["HappenLocation"].(string) != "" {
		tx = tx.Where("HappenLocation LIKE ?", "%"+where["HappenLocation"].(string)+"%")
	}

	//事故发生时间
	if _, ok := where["StartHappenAt"]; ok && where["StartHappenAt"].(string) != "" {
		tx = tx.Where("HappenAt >= ?", where["StartHappenAt"].(string))
	}
	if _, ok := where["EndHappenAt"]; ok && where["EndHappenAt"].(string) != "" {
		tx = tx.Where("HappenAt <= ?", where["EndHappenAt"].(string))
	}

	// 回收站数据
	if _, ok := where["IsRecycle"]; ok && where["IsRecycle"].(int64) != 0 {
		tx = tx.Where("IsRecycle = ?", where["IsRecycle"].(int64))
	}

	// 赔付方式
	if _, ok := where["IsFull"]; ok && where["IsFull"].(int64) != 0 {
		tx = tx.Where("TmpIsFull = ?", where["IsFull"])
	}

	//branchSubQuery := model.DB().Model(&TrafficAccidentRelaterBranch{}).Select("COUNT(*)").Where("IsClosed = ?", util.StatusForTrue).Where("traffic_accidents.id = trafficaccidentid")

	//事故状态（非流程状态）
	if _, ok := where["AccidentScene"]; ok && where["AccidentScene"].(int64) != 0 {
		switch where["AccidentScene"].(int64) {
		//草稿（包含事故草稿、事故提交流程被撤回、事故提交流程被驳回）-> （未提交但已保存的事故）
		case util.AccidentSceneForDraft:
			tx = tx.Where(model.DB().Where("OpenStatus = ? AND ApplyStatus=?", util.AccidentOpenStatusForDraft, util.ApplyStatusForNone))
		//分支已结案（事故有任意一个分支已经结案）->分支结案 （所有提交成功的事故）
		case util.AccidentSceneForBranchClosed:
			tx = tx.Where("ApplyStatus = ?", util.ApplyStatusForDone).Where("ClosedApplyStatus != ?", util.ApplyStatusForDoing).Where("ClosedApplyStatus != ?", util.ApplyStatusForDone).Where("IsClosed != ?", util.StatusForTrue)
		//未结案
		case util.AccidentSceneForDontClose:
			tx = tx.Where("IsClosed != ? AND OpenStatus != ?", util.StatusForTrue, util.AccidentOpenStatusForDraft)
		//结案申请中（事故结案流程发起之后，未通过之前）->所有分支完成结案后，允许事故结案
		case util.AccidentSceneForClosing:
			//tx = tx.Where("ApplyStatus = ? AND EditApplyStatus != ? AND ClosedApplyStatus = ?", util.ApplyStatusForDone, util.ApplyStatusForDoing, util.ApplyStatusForDoing)
			tx = tx.Where(model.DB().Where("ApplyStatus = ? AND EditApplyStatus != ? AND ClosedApplyStatus = ?", util.ApplyStatusForDone, util.ApplyStatusForDoing, util.ApplyStatusForDoing).
				Or(model.DB().Where("Id IN (?)", model.DB().Model(&TrafficAccidentRelaterBranch{}).Select("TrafficAccidentId").Group("trafficaccidentid").Having("count(*) = ?", 1)).
					Where("Id IN (?)", model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Select("TrafficAccidentId").Where("ApplyStatus = ?", util.ApplyStatusForDoing))))
		//已结案（事故已结案）
		case util.AccidentSceneForClosed:
			tx = tx.Where("IsClosed = ?", util.StatusForTrue)
		//已打印结案（事故已结案）
		case util.AccidentSceneForPrintClosed:
			//tx = tx.Where("ApplyStatus = ? AND EditApplyStatus != ? AND ClosedApplyStatus = ?", util.ApplyStatusForDone, util.ApplyStatusForDoing, util.ApplyStatusForDone)
			tx = tx.Where("IsClosed = ?", util.StatusForTrue).Where("IsPrinted = ?", util.StatusForTrue)
		}
	}

	_, hasRelaterName := where["RelaterName"]
	_, hasRelaterLicense := where["RelaterLicense"]
	_, hasRelaterContact := where["RelaterContact"]
	_, hasRelaterInsuranceCompany := where["RelaterInsuranceCompany"]

	if (hasRelaterName && where["RelaterName"].(string) != "") || (hasRelaterLicense && where["RelaterLicense"].(string) != "") ||
		(hasRelaterContact && where["RelaterContact"].(string) != "") || (hasRelaterInsuranceCompany && where["RelaterInsuranceCompany"].(string) != "") {
		existSql := model.DB().Model(&TrafficAccidentRelater{}).Select("TrafficAccidentId")
		if hasRelaterName && where["RelaterName"].(string) != "" {
			existSql = existSql.Where("Name LIKE ?", "%"+where["RelaterName"].(string)+"%")
		}
		if hasRelaterLicense && where["RelaterLicense"].(string) != "" {
			existSql = existSql.Where("License LIKE ?", "%"+where["RelaterLicense"].(string)+"%")
		}
		if hasRelaterContact && where["RelaterContact"].(string) != "" {
			existSql = existSql.Where("Contact LIKE ?", "%"+where["RelaterContact"].(string)+"%")
		}
		if hasRelaterInsuranceCompany && where["RelaterInsuranceCompany"].(string) != "" {
			existSql = existSql.Where("InsuranceCompany LIKE ?", "%"+where["RelaterInsuranceCompany"].(string)+"%")
		}

		tx = tx.Where("Id In (?)", existSql)
	}

	var count int64
	tx.Count(&count)

	orderStr := fmt.Sprintf("%s %s", orderBy, order)

	tx.Order(orderStr).Offset(paginator.Offset).Limit(paginator.Limit).Find(&accidents)

	return accidents, count
}

type DashboardAccidentItem struct {
	Id                             int64  `json:"Id" gorm:"column:id"`
	Code                           string `json:"Code" gorm:"column:code"`
	License                        string `json:"License" gorm:"column:license"`
	IsClosed                       int64  `json:"IsClosed" gorm:"column:isclosed"`
	IsPrinted                      int64  `json:"IsPrinted" gorm:"column:isprinted"`
	ClosedApplyStatus              int64  `json:"ClosedApplyStatus" gorm:"column:closedapplystatus"` //事故结案审批状态 0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃
	ClosedBranchCount              int64  `json:"ClosedBranchCount"`
	TotalBranchCount               int64  `json:"TotalBranchCount"`
	HasApplyingPaymentMoneyProcess bool   `json:"HasApplyingPaymentMoneyProcess"`
}

func (ta *TrafficAccident) DashboardGetAll(code string, status, branchCount int64, paginator model.Paginator) ([]DashboardAccidentItem, int64) {
	var accidents []DashboardAccidentItem
	tx := model.DB().Model(&TrafficAccident{}).Where("OpenStatus = ? AND IsRecycle = ?", util.StatusForTrue, util.StatusForFalse)

	//事故编号
	if code != "" {
		tx = tx.Where("Code LIKE ?", "%"+code+"%")
	}

	if branchCount > 0 {
		branchSubQuery := model.DB().Model(&TrafficAccidentRelaterBranch{}).Select("TrafficAccidentId").Group("trafficaccidentid").Having("COUNT(*) = ?", branchCount)
		tx = tx.Where("Id IN (?)", branchSubQuery)
	}

	if branchCount == 0 {
		branchSubQuery := model.DB().Model(&TrafficAccidentRelaterBranch{}).Select("TrafficAccidentId").Group("trafficaccidentid")
		tx = tx.Where("Id NOT IN (?)", branchSubQuery)
	}

	//事故状态（非流程状态）
	if status != 0 {
		switch status {
		//分支已结案（事故有任意一个分支已经结案）->分支结案 （所有提交成功的事故）
		case util.AccidentSceneForBranchClosed:
			tx = tx.Where("ApplyStatus = ?", util.ApplyStatusForDone).Where("ClosedApplyStatus != ?", util.ApplyStatusForDoing).Where("ClosedApplyStatus != ?", util.ApplyStatusForDone).Where("IsClosed != ?", util.StatusForTrue)

		//结案申请中（事故结案流程发起之后，未通过之前）->所有分支完成结案后，允许事故结案
		case util.AccidentSceneForClosing:
			tx = tx.Where(model.DB().Where("ApplyStatus = ? AND EditApplyStatus != ? AND ClosedApplyStatus = ?", util.ApplyStatusForDone, util.ApplyStatusForDoing, util.ApplyStatusForDoing).
				Or(model.DB().Where("Id IN (?)", model.DB().Model(&TrafficAccidentRelaterBranch{}).Select("TrafficAccidentId").Group("trafficaccidentid").Having("count(*) = ?", 1)).
					Where("Id IN (?)", model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Select("TrafficAccidentId").Where("ApplyStatus = ?", util.ApplyStatusForDoing))))

		//已结案（事故已结案）
		case util.AccidentSceneForClosed:
			tx = tx.Where("IsClosed = ?", util.StatusForTrue)

		//已打印结案（事故已结案）
		case util.AccidentSceneForPrintClosed:
			tx = tx.Where("IsClosed = ?", util.StatusForTrue).Where("IsPrinted = ?", util.StatusForTrue)
		}
	}

	var count int64
	tx.Count(&count)

	tx.Order("HappenAt desc").Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&accidents)

	return accidents, count
}

// IsHasRelater 事故是否有当事人
func (ta *TrafficAccident) IsHasRelater(id int64) bool {
	var count int64
	model.DB().Model(&TrafficAccidentRelater{}).Where("TrafficAccidentId = ?", id).Count(&count)

	return count > 0
}

// GetCount 线路安全报表 （线路违规、违法、事故数量）
func (ta *TrafficAccident) GetCount(groupId int64, startAt, endAt time.Time) ([]LineSafetyCount, error) {
	var rsp []LineSafetyCount
	tx := model.DB().Model(&TrafficAccident{}).Select("LineId, count(LineId) AS Accident").Where("GroupId = ? AND LineId > ?", groupId, 0)
	if !startAt.IsZero() && !endAt.IsZero() {
		tx.Where("HappenAt >= ? AND HappenAt < ?", startAt, endAt)
	}
	err := tx.Group("lineid").Scan(&rsp).Error
	if err != nil {
		return nil, err
	}

	return rsp, err
}

// GetCountWithOption 获取 人员 车辆 相关事故
func (ta *TrafficAccident) GetCountWithOption(staffId int64, license string, startAt, endAt time.Time) ([]TrafficAccident, error) {
	var rsp []TrafficAccident

	tx := model.DB().Model(&TrafficAccident{}).Where("HappenAt >= ? AND HappenAt < ? AND ApplyStatus = ?", startAt, endAt, 2)

	if staffId > 0 {
		tx.Where("DriverId = ?", staffId)
	}

	if license != "" {
		tx.Where("License LIKE ?", license)
	}

	err := tx.Scan(&rsp).Error
	if err != nil {
		return nil, err
	}

	return rsp, err
}

// ExistSelfVehicleBrokenBranch 事故是否存在己方车损分支
func (ta *TrafficAccident) ExistSelfVehicleBrokenBranch(trafficAccidentId int64) bool {
	var count int64
	model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("TrafficAccidentId = ? AND BranchType = ?", trafficAccidentId, util.AccidentBranchTypeForSelfVehicleBroken).Count(&count)
	return count > 0
}

func (ta *TrafficAccident) GetAll() ([]TrafficAccident, error) {
	var rsp []TrafficAccident
	err := model.DB().Model(&TrafficAccident{}).Order("branchid asc , happenat asc").Scan(&rsp).Error
	return rsp, err
}

func (ta *TrafficAccident) GetDriverAccident(driverId int64, startAt, endAt time.Time) []TrafficAccident {
	var accidents []TrafficAccident
	model.DB().Model(&TrafficAccident{}).Where("IsRecycle = ?", util.StatusForFalse).
		Where("DriverId = ? AND HappenAt >= ? AND HappenAt <= ?", driverId, startAt.Format(model.TimeFormat), endAt.Format(model.TimeFormat)).
		Order("HappenAt asc").Find(&accidents)

	return accidents
}

// IsExistsDriverNameLicenseHappenAt 查询同一天同一个司机同一辆车事故是否重存在 不算回收站数据 公开（OpenStatus）数据
func (ta *TrafficAccident) IsExistsDriverNameLicenseHappenAt() (TrafficAccident, int64, error) {
	var count int64
	var res TrafficAccident

	day := time.Time(ta.HappenAt)

	s := time.Date(day.Year(), day.Month(), day.Day(), 0, 0, 0, 0, time.Local)
	e := s.AddDate(0, 0, 1)

	err := model.DB().Model(&TrafficAccident{}).
		Where("DriverName=? AND License LIKE ? AND HappenAt >= ? AND HappenAt < ? AND IsRecycle=? AND OpenStatus=?", ta.DriverName, ta.License, s, e, util.StatusForFalse, util.AccidentOpenStatusForOpen).
		Count(&count).Order("HappenAt DESC").Find(&res).Error

	return res, count, err
}

// GetTimeout 获取滞留x天未结案的事故
func (ta *TrafficAccident) GetTimeout(groupId int64, timeout int64) ([]TrafficAccident, error) {
	var rsp []TrafficAccident
	err := model.DB().Model(&TrafficAccident{}).Where(
		"GroupId=? AND IsClosed = ? AND IsRecycle=? AND OpenStatus=?",
		groupId, util.StatusForFalse, util.StatusForFalse, util.AccidentOpenStatusForOpen).
		Where(fmt.Sprintf("CreatedAt<=(now() - INTERVAL '%d HOUR')", timeout*24)).
		Find(&rsp).Error

	return rsp, err
}

func (ta *TrafficAccident) GetAllDontClosed(topCorporationId int64) []TrafficAccident {
	var accidents []TrafficAccident
	model.DB().Model(&TrafficAccident{}).Where("GroupId = ? AND IsClosed = ? AND IsRecycle = ? AND OpenStatus = ?",
		topCorporationId, util.StatusForFalse, util.StatusForFalse, util.AccidentOpenStatusForOpen).Find(&accidents)

	return accidents
}

func (ta *TrafficAccident) GetAccidentProcess(corporationId, formStep int64, title, accidentCode, applyUserName, templateFormId, currentHandler string, status []int64, paginator model.Paginator) ([]processModel.LbpmApplyProcess, int64) {
	var processes []processModel.LbpmApplyProcess
	tx := model.DB().Model(&processModel.LbpmApplyProcess{}).Where("ModelId = ?", "safety_model").Where("TemplateFormId != ?", config.GlassRepairFormTemplate).Where("Status != ?", util.ProcessStatusForDraft)
	if title != "" {
		tx = tx.Where("Title LIKE ?", "%"+title+"%")
	}
	if accidentCode != "" {
		tx = tx.Where(
			model.DB().Where(
				model.DB().Where("TemplateFormId IN ?", []string{config.TrafficAccidentReportFormTemplate, config.TrafficAccidentEditFormTemplate, config.TrafficAccidentCloseFormTemplate}).
					Where("Param ->> 'Code' LIKE ?", accidentCode),
			).Or(
				model.DB().Where("TemplateFormId IN ?", []string{config.TrafficAccidentLendMoneyFormTemplate, config.TrafficAccidentDrawbackMoneyFormTemplate, config.TrafficAccidentPaymentMoneyFormTemplate, config.TrafficAccidentBranchCloseFormTemplate}).
					Where("Param ->> 'TrafficAccidentCode' LIKE ?", accidentCode)),
		)
	}

	if corporationId > 0 {
		tx = tx.Where("Param ->> 'CorporationId' = ?", corporationId)
	}

	if applyUserName != "" {
		tx = tx.Where("ApplyUserName LIKE ?", "%"+applyUserName+"%")
	}

	if currentHandler != "" {
		tx = tx.Where("CurrentHandlerUserName LIKE ?", "%"+currentHandler+"%")
	}

	if templateFormId != "" {
		tx = tx.Where("TemplateFormId = ?", templateFormId)

		if formStep > 0 {
			if templateFormId == config.TrafficAccidentCloseFormTemplate {
				tx = tx.Where("ItemId IN (?)", model.DB().Model(&TrafficAccident{}).Select("Id").Where("FormStep = ?", formStep))
			}

			if templateFormId == config.TrafficAccidentBranchCloseFormTemplate {
				tx = tx.Where("ItemId IN (?)", model.DB().Model(&TrafficAccidentRelaterBranch{}).Select("Id").Where("FormStep = ?", formStep))
			}

			if templateFormId == config.TrafficAccidentPaymentMoneyFormTemplate {
				tx = tx.Where("ItemId IN (?)", model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Select("Id").Where("FormStep = ?", formStep))
			}

			if templateFormId == config.TrafficAccidentLendMoneyFormTemplate {
				tx = tx.Where("ItemId IN (?)", model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Select("Id").Where("FormStep = ?", formStep))
			}
		}
	}

	if len(status) > 0 {
		tx = tx.Where("Status IN ?", status)
	}
	var count int64
	tx.Count(&count)

	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	tx.Order("CreatedAt DESC").Find(&processes)

	return processes, count
}

// TrafficAccidentRelater 事故当事人
type TrafficAccidentRelater struct {
	model.PkId
	TrafficAccidentId int64      `json:"TrafficAccidentId" gorm:"column:trafficaccidentid;type:bigint;comment:事故ID"`          //事故ID
	Name              string     `json:"Name" gorm:"column:name;type:varchar;comment:当事人姓名" validate:"required"`              //当事人姓名
	IdentifyId        string     `json:"IdentifyId" gorm:"column:identifyid;type:varchar;comment:当事人身份证"`                     //当事人身份证
	Contact           string     `json:"Contact" gorm:"column:contact;type:varchar;comment:当事人联系方式"`                          //当事人联系方式
	LiabilityType     int64      `json:"LiabilityType" gorm:"column:liabilitytype;type:smallint;comment:责任认定"`                //责任认定 1-全责,2-主责,3-次责,4-同责,5-无责
	InjuryType        int64      `json:"InjuryType" gorm:"column:injurytype;type:smallint;comment:伤势情况"`                      //伤势情况 1-无明显伤势,2-轻伤,3-住院,4-ICU,5-死亡
	License           string     `json:"License" gorm:"column:license;type:varchar;comment:车牌号"`                              //车牌号
	InsuranceCompany  string     `json:"InsuranceCompany" gorm:"column:insurancecompany;type:varchar;comment:保险公司"`           //保险公司
	More              string     `json:"More" gorm:"column:more;type:varchar;comment:备注"`                                     //备注
	FilePath          model.JSON `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                                //附件
	PayMoney          int64      `json:"PayMoney" gorm:"column:paymoney;type:integer;comment:对方/保险公司赔付金额"`                    //对方/保险公司赔付金额 单位：分
	LossMoney         int64      `json:"LossMoney" gorm:"column:lossmoney;type:integer;comment:预估经济损失"`                       //预估经济损失 单位：分
	IsSysCreate       int64      `json:"IsSysCreate" gorm:"column:issyscreate;type:smallint;comment:是否是系统创建的当事人  1是 2否"`      //是否是系统创建的当事人  1是 2否
	IsOwnSide         int64      `json:"IsOwnSide" gorm:"column:isownside;type:smallint;comment:是否己方司机(当事人) 1是 2否;default:0"` // 是否己方司机(当事人) 1是 2否
	SolutionType      int64      `json:"SolutionType" gorm:"column:solutiontype;type:smallint;comment:解决方式"`                  //解决方式 1-协商处理 2-交警处理 3-保险公司 4-其他方式
	SolutionDesc      string     `json:"SolutionDesc" gorm:"column:solutiondesc;type:varchar;comment:其他解决方式的补充说明;default:;"`  //其他解决方式的补充说明
	model.OpUser
	model.Timestamp

	TotalLendMoney   int64 `json:"TotalLendMoney" gorm:"-"`
	IsProcessRelater bool  `json:"IsProcessRelater" gorm:"-"` //是否是事故当事人分支结案流程的相关人
	HasBranch        bool  `json:"HasBranch" gorm:"-"`        //是否有分支

	Branches []TrafficAccidentRelaterBranch `json:"Branches"`

	BranchesMap []map[string]interface{} `json:"BranchesMap" gorm:"-"` // 当事人分支信息map

	// 消息中心显示额外字段
	TrafficAccidentCode string `json:"TrafficAccidentCode" gorm:"-"` // 事故编号
	ActionType          string `json:"ActionType" gorm:"-"`          //操作类型 create:新增 edit:更新 delete:删除 为空表示不变

	IsSelfVehicleBroken bool `json:"IsSelfVehicleBroken" gorm:"-"` //是否我方车损
	IsSelfHurt          bool `json:"IsSelfHurt" gorm:"-"`          //是否我方人伤
	IsSideVehicleBroken bool `json:"IsSideVehicleBroken" gorm:"-"` //是否对方车损
	IsSideHurt          bool `json:"IsSideHurt" gorm:"-"`          //是否对方人伤
}

func (tar *TrafficAccidentRelater) BeforeCreate(tx *gorm.DB) error {
	tar.Id = model.Id()
	return nil
}

// Create 创建
func (tar *TrafficAccidentRelater) Create() error {
	tar.Id = model.Id()
	return model.DB().Create(&tar).Error
}

// TransactionCreate 创建
func (tar *TrafficAccidentRelater) TransactionCreate(tx *gorm.DB) error {
	tar.Id = model.Id()
	return tx.Create(&tar).Error
}

// Update 更新
func (tar *TrafficAccidentRelater) Update() error {
	return model.DB().Select("*").Omit("CreatedAt", "Branches").Updates(&tar).Error
}

// AssociationUpdate 关联更新
func (tar *TrafficAccidentRelater) AssociationUpdate() error {
	return model.DB().Session(&gorm.Session{FullSaveAssociations: true}).Select("*").Omit("CreatedAt").Updates(&tar).Error
}

// TransactionUpdate 更新
func (tar *TrafficAccidentRelater) TransactionUpdate(tx *gorm.DB) error {
	return tx.Select("*").Omit("CreatedAt", "Branches").Updates(&tar).Error
}

// Delete 删除
func (tar *TrafficAccidentRelater) Delete(id int64) error {
	return model.DB().Where("Id = ?", id).Delete(&TrafficAccidentRelater{}).Error
}

// TransactionDelete 删除
func (tar *TrafficAccidentRelater) TransactionDelete(tx *gorm.DB, id int64) error {
	return tx.Where("Id = ?", id).Delete(&TrafficAccidentRelater{}).Error
}

// ExistBy 是否存在
func (tar *TrafficAccidentRelater) ExistBy(id int64) bool {
	var count int64
	model.DB().Model(&TrafficAccidentRelater{}).Where("Id = ?", id).Count(&count)
	return count > 0
}

// FindSysCreateRelater 事故系统创建的当事人
func (tar *TrafficAccidentRelater) FindSysCreateRelater(accidentId int64) TrafficAccidentRelater {
	var relater TrafficAccidentRelater
	model.DB().Model(&TrafficAccidentRelater{}).Where("TrafficAccidentId = ? AND IsSysCreate = ?", accidentId, util.StatusForTrue).First(&relater)
	return relater
}

// GetBy 获取列表
func (tar *TrafficAccidentRelater) GetBy(trafficAccidentId int64, name string) []TrafficAccidentRelater {
	var relaters []TrafficAccidentRelater
	tx := model.DB().Model(&TrafficAccidentRelater{}).Where("TrafficAccidentId = ?", trafficAccidentId)
	if name != "" {
		tx = tx.Where("Name LIKE ?", "%"+name+"%")
	}
	tx.Find(&relaters)

	return relaters
}

// GetRelaterBranchesBy 获取当事人、分支
func (tar *TrafficAccidentRelater) GetRelaterBranchesBy(trafficAccidentId int64, name string) []TrafficAccidentRelater {
	var relaters []TrafficAccidentRelater
	tx := model.DB().Model(&TrafficAccidentRelater{}).Preload("Branches").Where("TrafficAccidentId = ?", trafficAccidentId)
	if name != "" {
		tx = tx.Where("Name LIKE ?", "%"+name+"%")
	}
	tx.Find(&relaters)

	return relaters
}

// FindBy 详情
func (tar *TrafficAccidentRelater) FindBy(id int64) error {
	return model.DB().Model(&TrafficAccidentRelater{}).Where("Id = ?", id).First(tar).Error
}

// GetTotalInsuranceCompanyPayMoneyByAccidentId 获取单个事故所有当事人保险公司公司金额之和
//func (tar *TrafficAccidentRelater) GetTotalInsuranceCompanyPayMoneyByAccidentId(trafficAccidentId int64) int64 {
//	var moneys []int64
//	model.DB().Model(&TrafficAccidentRelater{}).Where("TrafficAccidentId = ?", trafficAccidentId).Pluck("InsuranceCompanyPayMoney", &moneys)
//
//	var total int64
//	for i := range moneys {
//		total += moneys[i]
//	}
//	return total
//}

// TrafficAccidentRelaterBranch 事故当事人分支
type TrafficAccidentRelaterBranch struct {
	model.PkId
	TrafficAccidentId        int64      `json:"TrafficAccidentId" gorm:"column:trafficaccidentid;type:bigint;uniqueIndex:traffic_accident_relater_id_branch_type_unique;comment:事故ID" validate:"required"`                  //事故ID
	TrafficAccidentRelaterId int64      `json:"TrafficAccidentRelaterId" gorm:"column:trafficaccidentrelaterid;type:bigint;uniqueIndex:traffic_accident_relater_id_branch_type_unique;comment:当事人记录ID" validate:"required"` //当事人记录ID
	BranchType               int64      `json:"BranchType" gorm:"column:branchtype;type:smallint;uniqueIndex:traffic_accident_relater_id_branch_type_unique;comment:分支类型" validate:"required"`                              //分支类型 1-对方车损,2-对方人伤,3-己方车损,4-己方人伤
	BranchDetail             string     `json:"BranchDetail" gorm:"column:branchdetail;type:varchar;comment:分支详情"`                                                                                                          //分支详情
	FilePath                 model.JSON `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                                                                                                                       //附件
	ClosedApplyStatus        int64      `json:"ClosedApplyStatus" gorm:"column:closedapplystatus;type:smallint;comment:事故分支结案审批状态;default:0"`                                                                               //事故分支结案审批状态  0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃
	IsClosed                 int64      `json:"IsClosed" gorm:"column:isclosed;type:integer;default:2;comment:是否结案"`                                                                                                        //是否结案  1是结案,2-未结案
	IsUpdateForm             int64      `json:"IsUpdateForm" gorm:"column:isupdateform;type:smallint;comment:是否可以编辑发起分支结案流程的表单数据 1是 2否;default:0"`                                                                          // 是否可以编辑发起分支结案流程的表单数据 1是 2否
	//出纳环节填写的字段
	CashierPayMoneyParam

	//分支结案后才会填充的字段
	SolutionType             int64           `json:"SolutionType" gorm:"column:solutiontype;type:smallint;comment:解决方式"`                            //解决方式 1-协商处理 2-交警简易程序 3-法院判决 4-保险公司 5-其他方式
	SolutionFilePath         model.JSON      `json:"SolutionFilePath" gorm:"column:solutionfilepath;type:json;comment:解决方式对应的附件"`                   //解决方式对应的附件
	InsuranceCompanyPayMoney int64           `json:"InsuranceCompanyPayMoney" gorm:"column:insurancecompanypaymoney;type:integer;comment:保险公司赔付金额"` //保险公司赔付金额 单位：分
	InsurancePayMoney        int64           `json:"InsurancePayMoney" gorm:"column:insurancepaymoney;type:integer;comment:保险直赔金额"`                 //保险直赔金额 单位：分
	LossMoney                int64           `json:"LossMoney" gorm:"column:lossmoney;type:integer;comment:经济损失"`                                   //经济损失 单位：分
	SolutionDesc             string          `json:"SolutionDesc" gorm:"column:solutiondesc;type:text;comment:结案说明"`                                //结案说明
	ClosedAt                 model.LocalTime `json:"ClosedAt" gorm:"column:closedat;type:timestamp;comment:结案时间"`                                   //结案时间
	PayOrigin                int64           `json:"PayOrigin" gorm:"column:payorigin;type:smallint;comment:赔付来源 1保险公司 2当事人;default:0"`             // 赔付来源 1保险公司 2当事人
	model.OpUser
	model.Timestamp

	TrafficAccidentCode        string `json:"TrafficAccidentCode" gorm:"-"`        //事故编号
	TrafficAccidentRelaterName string `json:"TrafficAccidentRelaterName" gorm:"-"` //当事人姓名
	LiabilityType              int64  `json:"LiabilityType" gorm:"-"`              //责任认定 1-全责,2-主责,3-次责,4-同责,5-无责

	License         string `json:"License" gorm:"-"`         // TrafficAccident.License
	CorporationId   int64  `json:"CorporationId" gorm:"-"`   // TrafficAccident.CorporationId
	CorporationName string `json:"CorporationName" gorm:"-"` // TrafficAccident.CorporationName
	LineId          int64  `json:"LineId" gorm:"-"`          // TrafficAccident.LineId
	LineName        string `json:"LineName" gorm:"-"`        // TrafficAccident.LineName
	DriverName      string `json:"DriverName" gorm:"-"`      // TrafficAccident.DriverName
}

func (tab *TrafficAccidentRelaterBranch) TableName() string {
	return "traffic_accident_relater_branches"
}

func (tab *TrafficAccidentRelaterBranch) ClosedApplyStatusFieldName() string {
	return "closedapplystatus"
}

func (tab *TrafficAccidentRelaterBranch) BeforeCreate(tx *gorm.DB) error {
	if tab.Id == 0 {
		tab.Id = model.Id()
	}
	return nil
}

// Create 创建
func (tab *TrafficAccidentRelaterBranch) Create() error {
	tab.Id = model.Id()
	return model.DB().Create(&tab).Error
}

// TransactionCreate 创建
func (tab *TrafficAccidentRelaterBranch) TransactionCreate(tx *gorm.DB) error {
	tab.Id = model.Id()
	return tx.Create(&tab).Error
}

func (tab *TrafficAccidentRelaterBranch) CreateReturnId() (int64, error) {
	tab.Id = model.Id()
	err := model.DB().Create(&tab).Error
	if err != nil {
		return 0, err
	} else {
		return tab.Id, nil
	}
}

// Update 更新
func (tab *TrafficAccidentRelaterBranch) Update() error {
	return model.DB().Updates(&tab).Error
}

// UpdateColumns 更新
func (tab *TrafficAccidentRelaterBranch) UpdateColumns(id int64, columns map[string]interface{}) error {
	return model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("Id=?", id).Updates(columns).Error
}

// TransactionUpdateColumns 更新
func (tab *TrafficAccidentRelaterBranch) TransactionUpdateColumns(tx *gorm.DB, id int64, columns map[string]interface{}) error {
	return tx.Model(&TrafficAccidentRelaterBranch{}).Where("Id=?", id).Updates(columns).Error
}

// FindBy 详情
func (tab *TrafficAccidentRelaterBranch) FindBy(id int64) error {
	return model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("Id = ?", id).First(tab).Error
}

// Delete 删除
func (tab *TrafficAccidentRelaterBranch) Delete(id int64) error {
	return model.DB().Where("Id = ?", id).Delete(&TrafficAccidentRelaterBranch{}).Error
}

func (tab *TrafficAccidentRelaterBranch) TransactionDelete(tx *gorm.DB, id int64) error {
	return tx.Where("Id = ?", id).Delete(&TrafficAccidentRelaterBranch{}).Error
}

// ExistBy 是否存在
func (tab *TrafficAccidentRelaterBranch) ExistBy(id int64) bool {
	var count int64
	model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("Id = ?", id).Count(&count)
	return count > 0
}

// GetByAccidentId 获取事故的分支
func (tab *TrafficAccidentRelaterBranch) GetByAccidentId(trafficAccidentId int64) []TrafficAccidentRelaterBranch {
	var branches []TrafficAccidentRelaterBranch
	model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("TrafficAccidentId = ?", trafficAccidentId).Find(&branches)

	return branches
}

// GetBranchCount 获取事故分支的数量
func (tab *TrafficAccidentRelaterBranch) GetBranchCount(trafficAccidentId, isClosed int64) int64 {
	var count int64
	tx := model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("TrafficAccidentId = ?", trafficAccidentId)

	if isClosed > 0 {
		tx = tx.Where("IsClosed = ?", isClosed)
	}

	tx.Count(&count)

	return count
}
func (tab *TrafficAccidentRelaterBranch) GetRelaterBranchCount(trafficAccidentRelaterId, isClosed int64) int64 {
	var count int64
	tx := model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("TrafficAccidentRelaterId = ?", trafficAccidentRelaterId)

	if isClosed > 0 {
		tx = tx.Where("IsClosed = ?", isClosed)
	}

	tx.Count(&count)

	return count
}

// GetByRelaterId 获取事故当事人的分支
func (tab *TrafficAccidentRelaterBranch) GetByRelaterId(relaterId int64) []TrafficAccidentRelaterBranch {
	var branches []TrafficAccidentRelaterBranch
	model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("TrafficAccidentRelaterId = ?", relaterId).Find(&branches)

	return branches
}

// GetRelaterBranchIds 获取当事人分支ID
func (tab *TrafficAccidentRelaterBranch) GetRelaterBranchIds(relaterId int64) []int64 {
	var ids []int64
	model.DB().Model(&TrafficAccidentRelaterBranch{}).Select("Id").Where("TrafficAccidentRelaterId = ?", relaterId).Pluck("Id", &relaterId)

	return ids
}

// IsExistBranchType 事故当事人是否有某个类型的分支
func (tab *TrafficAccidentRelaterBranch) IsExistBranchType(relaterId, branchType int64) bool {
	var count int64
	model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("TrafficAccidentRelaterId = ? AND BranchType = ?", relaterId, branchType).Count(&count)

	return count > 0
}

func (tab *TrafficAccidentRelaterBranch) GetTotalInsuranceCompanyPayMoneyByAccidentId(trafficAccidentId int64) int64 {
	var moneys []int64
	model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("TrafficAccidentId = ?", trafficAccidentId).Pluck("InsuranceCompanyPayMoney", &moneys)

	var total int64
	for i := range moneys {
		total += moneys[i]
	}
	return total
}

// TransactionUpdateCloseInfo 更新结案字段
func (tab *TrafficAccidentRelaterBranch) TransactionUpdateCloseInfo(tx *gorm.DB) error {
	return tx.Select("SolutionType", "SolutionFilePath", "InsuranceCompanyPayMoney", "InsurancePayMoney", "LossMoney", "ClosedApplyStatus", "FormStep", "SolutionDesc", "PayOrigin").Updates(&tab).Error
}

// DashboardUpdateCloseInfo 更新结案字段
func (tab *TrafficAccidentRelaterBranch) DashboardUpdateCloseInfo() error {
	return model.DB().Select("SolutionType", "SolutionFilePath", "InsuranceCompanyPayMoney", "InsurancePayMoney", "LossMoney", "ClosedApplyStatus", "FormStep", "SolutionDesc", "PayOrigin", "IsMustPay", "PayMoneyAt", "PayMoneyFile").Updates(&tab).Error
}

func (tab *TrafficAccidentRelaterBranch) TransactionUpdateClose(tx *gorm.DB) error {
	return tx.Model(&TrafficAccidentRelaterBranch{}).Where("Id=?", tab.Id).Updates(map[string]interface{}{"isclosed": util.StatusForTrue, "closedat": time.Now().Format(model.TimeFormat)}).Error
}

func (tab *TrafficAccidentRelaterBranch) TransactionDashboardUpdateClose(tx *gorm.DB, id int64, column string, value interface{}) error {
	return tx.Model(&TrafficAccidentRelaterBranch{}).Where("Id=?", id).Update(column, value).Error
}

// IsExistHandlingLendMoneyRecord 分支是否存在正在审批的借款记录
func (tab *TrafficAccidentRelaterBranch) IsExistHandlingLendMoneyRecord() bool {
	var count int64
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Where("TrafficAccidentRelaterBranchId = ? AND ApplyStatus = ?", tab.Id, util.ApplyStatusForDoing).Count(&count)
	return count > 0
}

// IsExistHandlingDrawbackMoneyRecord 分支是否存在正在审批的退款记录
func (tab *TrafficAccidentRelaterBranch) IsExistHandlingDrawbackMoneyRecord() bool {
	var count int64
	model.DB().Model(&TrafficAccidentDrawbackMoneyRecord{}).Where("TrafficAccidentRelaterBranchId = ? AND ApplyStatus = ?", tab.Id, util.ApplyStatusForDoing).Count(&count)
	return count > 0
}

// IsExistHandlingPaymentMoneyRecord 分支是否存在正在审批的付款记录
func (tab *TrafficAccidentRelaterBranch) IsExistHandlingPaymentMoneyRecord() bool {
	var count int64
	model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Where("TrafficAccidentRelaterBranchId = ? AND ApplyStatus = ?", tab.Id, util.ApplyStatusForDoing).Count(&count)
	return count > 0
}

// IsExistDonePaymentMoneyRecord 分支是否存在审批完成的付款记录
func (tab *TrafficAccidentRelaterBranch) IsExistDonePaymentMoneyRecord() bool {
	var count int64
	model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Where("TrafficAccidentRelaterBranchId = ? AND ApplyStatus = ?", tab.Id, util.ApplyStatusForDone).Count(&count)
	return count > 0
}

// FindSelfVehicleBrokenBranchByAccidentId 获取事故的己方车损分支
func (tab *TrafficAccidentRelaterBranch) FindSelfVehicleBrokenBranchByAccidentId(trafficAccidentId int64) TrafficAccidentRelaterBranch {
	var branch TrafficAccidentRelaterBranch
	model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("TrafficAccidentId = ? AND BranchType = ?", trafficAccidentId, util.AccidentBranchTypeForSelfVehicleBroken).First(&branch)
	return branch
}

// FindSelfPeopleHurtBranchByAccidentId 获取事故的己方人伤分支
func (tab *TrafficAccidentRelaterBranch) FindSelfPeopleHurtBranchByAccidentId(trafficAccidentId int64) TrafficAccidentRelaterBranch {
	var branch TrafficAccidentRelaterBranch
	model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("TrafficAccidentId = ? AND BranchType = ?", trafficAccidentId, util.AccidentBranchTypeForSelfPeopleHurt).First(&branch)
	return branch
}

// IsExistProcess 分支是否有相关的流程
func (tab *TrafficAccidentRelaterBranch) IsExistProcess(branchId int64) bool {
	//是否有结案流程
	isClosedProcess := (&processModel.LbpmApplyProcess{}).IsExistProcess(branchId, tab.TableName())
	//是否有分支借款流程
	var lendMoneyCount, payMoneyCount int64
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Where("TrafficAccidentRelaterBranchId = ?", branchId).Count(&lendMoneyCount)
	//是否有分支付款流程
	model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Where("TrafficAccidentRelaterBranchId = ?", branchId).Count(&payMoneyCount)

	return isClosedProcess || lendMoneyCount > 0 || payMoneyCount > 0
}

// IsExistNormalProcess 分支是否存在非废弃的流程
func (tab *TrafficAccidentRelaterBranch) IsExistNormalProcess(branchId int64) bool {
	//是否有结案流程
	isClosedProcess := (&processModel.LbpmApplyProcess{}).IsExistNormalProcess(branchId, tab.TableName())
	//是否有分支借款流程
	var lendMoneyCount, payMoneyCount int64
	model.DB().Model(&TrafficAccidentLendMoneyRecord{}).Where("TrafficAccidentRelaterBranchId = ? AND ApplyStatus != ?", branchId, util.ApplyStatusForAbandon).Count(&lendMoneyCount)
	//是否有分支付款流程
	model.DB().Model(&TrafficAccidentPaymentMoneyRecord{}).Where("TrafficAccidentRelaterBranchId = ? AND ApplyStatus != ?", branchId, util.ApplyStatusForAbandon).Count(&payMoneyCount)

	return isClosedProcess || lendMoneyCount > 0 || payMoneyCount > 0
}

// IsExistDoBranch 事故是否存在正在进行中的分支 包含结案正在审批、结案审批通过、结案撤回、结案驳回
func (tab *TrafficAccidentRelaterBranch) IsExistDoBranch(accidentId int64) bool {
	var count int64
	model.DB().Model(&TrafficAccidentRelaterBranch{}).Where("TrafficAccidentId = ?", accidentId).Where("ClosedApplyStatus > ? AND ClosedApplyStatus < ?", util.ApplyStatusForNone, util.ApplyStatusForAbandon).Count(&count)
	return count > 0
}
