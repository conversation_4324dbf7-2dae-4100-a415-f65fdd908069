package safety

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type QualityAssessmentCate struct {
	model.PkId
	model.Corporations
	ParentId int64           `json:"ParentId" gorm:"column:parentid;type:bigint;default:0;comment:父级ID 顶级默认为0"`
	Name     string          `json:"Name" gorm:"column:name;type:varchar;default:;comment:分类名称"`
	Code     string          `json:"Code" gorm:"column:code;type:varchar;default:;comment:分类编号"`
	ExpireAt model.LocalTime `json:"ExpireAt" gorm:"column:expireat;type:timestamp;comment:过期时间"`
	Status   int64           `json:"Status" gorm:"column:status;type:smallint;default:1;comment:1启用  2过期、禁用"`
	model.Timestamp

	Children      []QualityAssessmentCate `json:"Children" gorm:"-"`
	StandardCount int64                   `json:"IsExistStandard" gorm:"-"`
}

func (qac *QualityAssessmentCate) BeforeCreate(db *gorm.DB) error {
	qac.Id = model.Id()
	return nil
}
func (qac *QualityAssessmentCate) Create() error {
	return model.DB().Create(&qac).Error
}

func (qac *QualityAssessmentCate) Update() error {
	return model.DB().Select("Name", "Code", "ExpireAt", "Status").Updates(&qac).Error
}

func (qac *QualityAssessmentCate) Delete() error {
	return model.DB().Delete(&qac).Error
}

func (qac *QualityAssessmentCate) IsExistByCode(corporationId int64, code string) bool {
	var count int64
	model.DB().Model(&QualityAssessmentCate{}).Scopes(model.WhereCorporation(corporationId)).Where("Code = ?", code).Count(&count)

	return count > 0
}

func (qac *QualityAssessmentCate) FindByCode(corporationId int64, code string) QualityAssessmentCate {
	var cate QualityAssessmentCate
	model.DB().Model(&QualityAssessmentCate{}).Scopes(model.WhereCorporation(corporationId)).Where("Code = ?", code).First(&cate)

	return cate
}
func (qac *QualityAssessmentCate) FindBy(id int64) QualityAssessmentCate {
	var cate QualityAssessmentCate
	model.DB().Model(&QualityAssessmentCate{}).Where("Id = ?", id).First(&cate)

	return cate
}

func (qac *QualityAssessmentCate) GetCountByParentId(parentId int64) int64 {
	var count int64
	model.DB().Model(&QualityAssessmentCate{}).Where("ParentId = ?", parentId).Count(&count)

	return count
}

func (qac *QualityAssessmentCate) GetAll(corporationId int64) []QualityAssessmentCate {
	var cates []QualityAssessmentCate
	model.DB().Model(&QualityAssessmentCate{}).Scopes(model.WhereCorporation(corporationId)).Find(&cates)

	return cates
}
