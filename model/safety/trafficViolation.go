package safety

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"strings"
	"time"
)

type TrafficViolation struct {
	model.PkId
	model.Corporations
	SubmitCorporationId     int64           `json:"SubmitCorporationId"   gorm:"column:submitcorporationid;comment:提交人机构;type:bigint"`
	QualityAssessmentCateId int64           `json:"QualityAssessmentCateId" gorm:"column:qualityassessmentcateid;type:bigint;default:0;comment:质量考核规范分类ID 关联quality_assessment_cates表ID"`
	CateAttr                int64           `json:"-" gorm:"column:cateattr;type:smallint;default:1;comment:违规属性 1安全类 2服务类"`
	VehicleId               int64           `json:"VehicleId"   gorm:"column:vehicleid;comment:车辆id;type:integer"`
	License                 string          `json:"License"   gorm:"column:license;comment:车牌号;type:varchar(100)"`
	LineId                  int64           `json:"LineId" gorm:"column:lineid;comment:线路id;type:integer"`
	LineName                string          `json:"LineName" gorm:"column:linename;comment:线路;type:varchar"`
	DriverStaffId           int64           `json:"DriverStaffId" gorm:"column:driverstaffid;comment:司机id;type:bigint"`
	DriverStaffNo           string          `json:"DriverStaffNo" gorm:"column:driverstaffno;comment:司机工号;type:varchar"`
	DriverStaffName         string          `json:"DriverStaffName" gorm:"column:driverstaffname;comment:司机;type:varchar(50)"`
	Origin                  int64           `json:"Origin" gorm:"column:origin;comment:1:现场检查 2:交通违章 3:交通事故 4:三方测评 5:文明城市 6:主动安全 7:视频监控;type:smallint"`
	ReportAt                model.LocalTime `json:"ReportAt" gorm:"column:reportat;comment:违规、违法时间;type:timestamp"`
	Place                   string          `json:"Place" gorm:"column:place;comment:地点;type:text"`
	PlaceText               string          `json:"PlaceText" gorm:"column:placetext;comment:输入的地点;type:text"`
	Content                 string          `json:"Content" gorm:"column:content;comment:违规、违法事件描述;type:text"`
	AttachmentJson          model.JSON      `json:"AttachmentJson" gorm:"column:AttachmentJson;comment:附件;type:json"`
	Standards               model.JSON      `json:"Standards" gorm:"colum:standards;type:json;comment:适用的执行标准"`
	DeductScore             int64           `json:"DeductScore" gorm:"column:deductscore;comment:扣分分值  1/100分 即 1分 记作 100;type:integer"`
	DeductMoney             int64           `json:"DeductMoney" gorm:"column:deductmoney;comment:扣款金额 单位：分;type:integer"`
	IsRecycle               int64           `json:"IsRecycle" gorm:"column:isrecycle;type:smallint;default:2;comment:是否在回收站  1是 2否"`
	Status                  int64           `json:"Status" gorm:"column:status;type:smallint;default:0;comment:0=>未下发 1=>已下发 2=>待审核 3=>驳回 4=>已审核"`
	RecycleMore             string          `json:"RecycleMore" gorm:"column:recyclemore;type:text;comment:回收备注"`
	RecycleFile             model.JSON      `json:"RecycleFile" gorm:"column:recyclefile;type:json;comment:回收附件"`

	model.Timestamp

	SubmitCorporationName      string                       `json:"SubmitCorporationName" gorm:"-"`
	CorporationId              int64                        `json:"CorporationId" gorm:"-"`
	CorporationName            string                       `json:"CorporationName" gorm:"-"`
	FileHttpPrefix             string                       `json:"FileHttpPrefix" gorm:"-"`
	QualityAssessmentStandards []QualityAssessmentStandards `json:"-" gorm:"-"`
	QualityAssessmentCateName  string                       `json:"QualityAssessmentCateName" gorm:"-"`
}

type AttachmentJson struct {
	Files []File
}

type File struct {
	FkFileId int64  // 文件id
	Url      string // url
	Type     int64  // 文件类型(兼容老数据0也当作图片处理) 1图片 2视频
	Path     string // 文件相对路径
}

func (t *TrafficViolation) BeforeCreate(tx *gorm.DB) error {
	t.Id = model.Id()

	return nil
}

func (t *TrafficViolation) Add(tx *gorm.DB) error {
	return tx.Create(&t).Error
}

func (t *TrafficViolation) AddBatch(items []TrafficViolation) error {
	return model.DB().Create(&items).Error
}

func (t *TrafficViolation) List(corporationIds []int64, driver string, cateAttr int64, cateIds, fkQASIds []int64, isRecycle, lineId, vehicleId, origin, deductScore, status int64,
	license, content string, startAt, endAt time.Time, createStartAt, createEndAt time.Time, orderField, order string, paginator model.Paginator) ([]TrafficViolation, int64) {
	var violations []TrafficViolation
	var totalCount int64
	tx := model.DB().Model(&TrafficViolation{}).Scopes(model.WhereCorporations(corporationIds)).Where("IsRecycle = ?", isRecycle)
	if cateAttr != 0 {
		tx = tx.Where("CateAttr = ?", cateAttr)
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.TimeFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ? ", endAt.Format(model.TimeFormat))
	}

	if !createStartAt.IsZero() {
		tx.Where("CreatedAt >= ?", createStartAt.Format(model.TimeFormat))
	}
	if !createEndAt.IsZero() {
		tx.Where("CreatedAt <= ? ", createEndAt.AddDate(0, 0, 1).Format(model.TimeFormat))
	}

	if driver != "" {
		tx.Where("DriverStaffName LIKE ? OR DriverStaffNo LIKE ?", "%"+driver+"%", "%"+driver+"%")
	}

	if len(fkQASIds) > 0 {
		var str []string
		for i := range fkQASIds {
			str = append(str, fmt.Sprintf("standards @> '[{\"CategoryId\":%v}]'", fkQASIds[i]))
		}
		tx.Where(strings.Join(str, " OR "))
	}

	if len(cateIds) > 0 {
		var str []string
		for i := range cateIds {
			str = append(str, fmt.Sprintf("standards @> '[{\"QualityAssessmentCateId\":%v}]'", cateIds[i]))
		}
		tx.Where(strings.Join(str, " OR "))
	}

	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	if vehicleId > 0 {
		tx.Where("VehicleId = ?", vehicleId)
	}

	if origin > 0 {
		tx.Where("Origin = ?", origin)
	}

	if deductScore >= 0 {
		tx.Where("DeductScore = ?", deductScore)
	}

	if status >= 0 {
		tx.Where("Status = ?", status)
	}

	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}

	if content != "" {
		tx.Where("Content LIKE ?", "%"+content+"%")
	}

	tx.Count(&totalCount)
	if orderField != "" && order != "" {
		tx.Order(orderField + " " + order)
	} else {
		tx.Order("CreatedAt DESC")
	}
	tx.Offset(paginator.Offset).Limit(paginator.Limit).Scan(&violations)

	return violations, totalCount
}

func (t *TrafficViolation) GetDriverViolation(driverId int64, startAt, endAt time.Time) []TrafficViolation {
	var violations []TrafficViolation

	model.DB().Model(&TrafficViolation{}).
		Where("IsRecycle = ?", util.StatusForFalse).
		Where("DriverStaffId = ?", driverId).
		Where("Status = ?", util.TrafficViolationStatusForChecked).
		Where("ReportAt >= ? AND ReportAt <= ?", startAt.Format(model.TimeFormat), endAt.Format(model.TimeFormat)).
		Order("ReportAt ASC").Order("CreatedAt DESC").Find(&violations)

	return violations
}

func (t *TrafficViolation) GetDriverAllViolation(driverId int64, cateAttr int64, startAt, endAt time.Time) []TrafficViolation {
	var violations []TrafficViolation

	tx := model.DB().Model(&TrafficViolation{}).
		Where("IsRecycle = ?", util.StatusForFalse).
		Where("DriverStaffId = ?", driverId).
		Where("ReportAt >= ? AND ReportAt <= ?", startAt.Format(model.TimeFormat), endAt.Format(model.TimeFormat))

	if cateAttr > 0 {
		tx.Where("CateAttr = ?", cateAttr)
	}

	tx.Order("ReportAt ASC").Order("CreatedAt DESC").Find(&violations)

	return violations
}

func (t *TrafficViolation) GetViolationByIds(ids []int64) []TrafficViolation {
	var violations []TrafficViolation

	model.DB().Model(&TrafficViolation{}).Where("IsRecycle = ?", util.StatusForFalse).
		Where("Id IN ?", ids).Order("ReportAt ASC").Order("CreatedAt DESC").Find(&violations)

	return violations
}

func (t *TrafficViolation) Edit() error {
	return model.DB().Model(&TrafficViolation{}).Where("Id = ?", t.Id).Select(
		"GroupId", "CompanyId", "BranchId", "DepartmentId", "FleetId", "SubmitCorporationId",
		"VehicleId", "License", "LineId", "LineName", "DriverStaffId", "DriverStaffNo", "DriverStaffName",
		"AttendantStaffId", "AttendantStaffName", "Origin", "ReportAt", "Place", "PlaceText", "Content", "AttachmentJson",
		"QualityAssessmentCateId", "Standards", "DeductScore", "DeductMoney").Updates(&t).Error
}

func (t *TrafficViolation) EditStatus() error {
	return model.DB().Model(&TrafficViolation{}).Where("Id = ?", t.Id).Select("Status").Updates(&t).Error
}

func (t *TrafficViolation) GetDetail() (TrafficViolation, error) {
	var rsp TrafficViolation
	err := model.DB().Model(&TrafficViolation{}).Where("Id = ?", t.Id).Scan(&rsp).Error
	return rsp, err
}

func (t *TrafficViolation) FindBy(id int64) TrafficViolation {
	var violation TrafficViolation
	model.DB().Model(&TrafficViolation{}).Where("Id = ?", id).First(&violation)
	return violation
}

func (t *TrafficViolation) TransactionUpdateStatus(tx *gorm.DB, status int64) error {
	return tx.Model(&TrafficViolation{}).Select("Status").Where("Id = ?", t.Id).Update("Status", status).Error
}

func (t *TrafficViolation) TransactionUpdateRecycle(tx *gorm.DB) error {
	return tx.Model(&TrafficViolation{}).Select("IsRecycle", "RecycleMore", "RecycleFile").Where("Id = ?", t.Id).Updates(&t).Error
}

func (t *TrafficViolation) Delete() error {
	return model.DB().Delete(&t).Error
}

func (t *TrafficViolation) CountByStandardId(standardId int64) int64 {
	var count int64
	model.DB().Table("traffic_violations, jsonb_array_elements(standards) v").Where("(v.value->>'Id')::numeric = ?", standardId).Count(&count)

	return count
}

type LineSafetyCount struct {
	LineId int64 `json:"LineId" gorm:"column:lineid"` //

	Violation int64 `json:"Violation" gorm:"column:violation"` // 违规数量
	Illegal   int64 `json:"Illegal" gorm:"column:illegal"`     // 违法数量
	Accident  int64 `json:"Accident" gorm:"column:accident"`   // 事故数量
}

// 线路安全报表 （线路违规、违法、事故数量）
func (t *TrafficViolation) GetCount(groupId int64, startAt, endAt time.Time) ([]LineSafetyCount, error) {
	var rspViolation []LineSafetyCount
	var rspIllegal []LineSafetyCount
	tx := model.DB().Model(&TrafficViolation{}).Select("LineId, count(LineId) AS Violation").Where("GroupId = ? AND Status = ? AND VehicleLineId > ?", groupId, util.TrafficViolationStatusForChecked, 0)
	if !startAt.IsZero() && !endAt.IsZero() {
		tx.Where("ReportAt >= ? AND ReportAt < ?", startAt, endAt)
	}
	err := tx.Group("vehiclelineid").Scan(&rspViolation).Error // 单字段分组用小写
	if err != nil {
		return nil, err
	}

	//tx2 := model.DB().Model(&TrafficViolation{}).Select("VehicleLineId AS LineId, count(VehicleLineId) AS Illegal").Where("VehicleGroupId = ? AND Status = ? AND Type = ? AND VehicleLineId > ?", groupId, util, 0)
	//if !startAt.IsZero() && !endAt.IsZero() {
	//	tx.Where("TimeAt >= ? AND TimeAt < ?", startAt, endAt)
	//}
	//err = tx2.Group("vehiclelineid").Scan(&rspIllegal).Error
	//if err != nil {
	//	return nil, err
	//}
	rspViolation = append(rspViolation, rspIllegal...)
	return rspViolation, err
}

func (t *TrafficViolation) GetCountWithOption(staffId int64, license string, startAt, endAt time.Time) ([]TrafficViolation, error) {
	var rsp []TrafficViolation

	tx := model.DB().Model(&TrafficViolation{}).Where("ReportAt >= ? AND ReportAt < ? AND Status = ?", startAt, endAt, util.TrafficViolationStatusForChecked)

	if staffId > 0 {
		tx.Where("DriverStaffId = ?", staffId)
	}

	if license != "" {
		tx.Where("License LIKE ?", license)
	}

	err := tx.Scan(&rsp).Error
	if err != nil {
		return nil, err
	}

	return rsp, err
}

type TrafficViolationCheckLog struct {
	model.PkId
	TrafficViolationId int64  `json:"TrafficViolationId" gorm:"column:trafficviolationid;type:bigint;comment:关联违规Id"`
	Status             int64  `json:"Status" gorm:"column:status;type:smallint;default:0;comment:3=>驳回 4=>通过"`
	More               string `json:"More" gorm:"column:more;type:text;comment:意见"`
	model.OpUser
	model.Timestamp

	OpStaffName string `json:"OpStaffName" gorm:"-"`
}

func (tvc *TrafficViolationCheckLog) BeforeCreate(tx *gorm.DB) error {
	tvc.Id = model.Id()
	return nil
}
func (tvc *TrafficViolationCheckLog) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&tvc).Error
}
func (tvc *TrafficViolationCheckLog) GetByViolationId(violationId int64) []TrafficViolationCheckLog {
	var logs []TrafficViolationCheckLog
	model.DB().Model(&TrafficViolationCheckLog{}).Where("TrafficViolationId = ?", violationId).Order("CreatedAt DESC").Find(&logs)

	return logs

}

type TrafficViolationHistory struct {
	Id              int64           `json:"Id" gorm:"column:id;primaryKey;autoIncrement;type:serial"`
	ViolationType   string          `json:"ViolationType" gorm:"column:violationtype;type:varchar;comment:检查类型"`
	Operator        string          `json:"Operator" gorm:"column:operator;type:varchar;comment:操作人"`
	CorporationName string          `json:"CorporationName" gorm:"column:corporationname;type:varchar;comment:单位"`
	Fleet           string          `json:"Fleet" gorm:"column:fleet;type:varchar;comment:车队"`
	LineName        string          `json:"LineName" gorm:"column:linename;type:varchar;comment:线路"`
	License         string          `json:"License" gorm:"column:license;type:varchar;comment:车牌号"`
	Standard        string          `json:"Standard" gorm:"column:standard;type:varchar;comment:违规标准"`
	StandardItem    string          `json:"StandardItem" gorm:"column:standardItem;type:varchar;comment:违规项目"`
	StandardCate    string          `json:"StandardCate" gorm:"column:standardcate;type:varchar;comment:违规类型"`
	ReportAt        model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:检查日期"`
	Place           string          `json:"Place" gorm:"column:place;type:varchar;comment:违章地点"`
	ReduceScore     string          `json:"ReduceScore" gorm:"column:reducescore;type:varchar;comment:违章记分"`
	AddScore        string          `json:"AddScore" gorm:"column:addscore;type:varchar;comment:加分值"`
	StudyScore      string          `json:"StudyScore" gorm:"column:studyscore;type:varchar;comment:学习分"`
	ScoreGrade      string          `json:"ScoreGrade" gorm:"column:scoregrade;type:varchar;comment:记分档"`
	Driver          string          `json:"Driver" gorm:"column:driver;type:varchar;comment:驾驶员"`
	Attendant       string          `json:"Attendant" gorm:"column:attendant;type:varchar;comment:乘务员"`
	IsHandle        string          `json:"IsHandle" gorm:"column:ishandle;type:varchar;comment:是否处理"`
	More            string          `json:"More" gorm:"column:more;type:varchar;comment:备注"`
	model.Timestamp
}

func (tvh *TrafficViolationHistory) GetBy(lineName, corporationName, driver, attendant, license string, startAt, endAt time.Time, paginator model.Paginator) ([]TrafficViolationHistory, int64) {
	var records []TrafficViolationHistory
	var count int64
	tx := model.DB().Model(&TrafficViolationHistory{})

	if lineName != "" {
		tx = tx.Where("LineName LIKE ?", "%"+lineName+"%")
	}

	if corporationName != "" {
		tx = tx.Where("CorporationName LIKE ?", "%"+corporationName+"%")
	}

	if driver != "" {
		tx = tx.Where("Driver LIKE ?", "%"+driver+"%")
	}

	if attendant != "" {
		tx = tx.Where("Attendant LIKE ?", "%"+attendant+"%")
	}

	if license != "" {
		tx = tx.Where("License LIKE ?", "%"+license+"%")
	}

	if !startAt.IsZero() {
		tx = tx.Where("ReportAt >= ?", startAt.Format(model.DateFormat))
	}

	if !endAt.IsZero() {
		tx = tx.Where("ReportAt <= ?", endAt.Format(model.DateFormat))
	}

	tx.Count(&count)

	tx.Order("ReportAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&records)

	return records, count
}

type TrafficViolationLogger struct {
	model.PkId
	TrafficViolationId int64      `json:"TrafficViolationId" gorm:"column:trafficviolationid;type:bigint;"`
	BeforeData         model.JSON `json:"BeforeData" gorm:"column:beforedata;type:json;"`
	AfterData          model.JSON `json:"AfterData" gorm:"column:afterdata;type:json;"`
	model.OpUser
	model.Timestamp
}

func (tvl *TrafficViolationLogger) BeforeCreate(db *gorm.DB) error {
	tvl.Id = model.Id()
	return nil
}

func (tvl *TrafficViolationLogger) Create() error {
	return model.DB().Model(&TrafficViolationLogger{}).Create(&tvl).Error
}

func (tvl *TrafficViolationLogger) GetBy(violationId int64) []TrafficViolationLogger {
	var loggers []TrafficViolationLogger
	model.DB().Model(&TrafficViolationLogger{}).Where("TrafficViolationId = ?", violationId).Find(&loggers)

	return loggers
}
