package safety

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
	"time"
)

type VehicleInsurance struct {
	model.PkId
	model.Corporations
	VehicleId         int64           `json:"VehicleId" gorm:"column:vehicleid;type:integer;comment:车辆ID" validate:"required"`                         //车辆ID
	License           string          `json:"License" gorm:"column:license;type:varchar;comment:车牌号" validate:"required"`                              //车牌号
	StartDate         model.LocalTime `json:"StartDate" gorm:"column:startdate;type:timestamp;comment:起保日期" validate:"required"`                       //起保日期
	EndDate           model.LocalTime `json:"EndDate" gorm:"column:enddate;type:timestamp;comment:终保日期" validate:"required"`                           //终保日期
	InsuranceCompany  string          `json:"InsuranceCompany" gorm:"column:insurancecompany;type:varchar;comment:保险公司"`                               //保险公司
	InsuranceType     int64           `json:"InsuranceType" gorm:"column:insurancetype;type:smallint;comment:保险类型 1=>交强险 2=>商业保险" validate:"required"` //保险类型 1=>交强险 2=>商业保险
	InsuranceCode     string          `json:"InsuranceCode" gorm:"column:insurancecode;type:varchar;comment:保险编号;uniqueIndex" validate:"required"`     //保险编号
	InsuranceFilePath string          `json:"InsuranceFilePath" gorm:"column:insurancefilepath;type:varchar;comment:保险单"`
	CorporationId     int64           `json:"CorporationId" gorm:"-"`
	CorporationName   string          `json:"CorporationName" gorm:"-"`

	model.Timestamp
	InsuranceFileUrl string `json:"InsuranceFileUrl" gorm:"-"`
	VehicleUseStatus int64  `json:"VehicleUseStatus" gorm:"-"` //车辆状态 1使用中  2报废
}

func (vi *VehicleInsurance) BeforeCreate(tx *gorm.DB) error {
	vi.Id = model.Id()
	return nil
}

func (vi *VehicleInsurance) Create() error {
	return model.DB().Create(&vi).Error
}

func (vi *VehicleInsurance) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&vi).Error
}
func (vi *VehicleInsurance) TransactionBatchCreate(tx *gorm.DB, values []VehicleInsurance) error {
	vi.Id = model.Id()
	return tx.Create(&values).Error
}

func (vi *VehicleInsurance) Update() error {
	return model.DB().Omit("InsuranceFilePath").Updates(&vi).Error
}

func (vi *VehicleInsurance) UpdateColumn(column string, value interface{}) error {
	return model.DB().Model(&VehicleInsurance{}).Where("Id = ?", vi.Id).Update(column, value).Error
}

func (vi *VehicleInsurance) FindBy(id int64) VehicleInsurance {
	var insurance VehicleInsurance
	model.DB().Model(&VehicleInsurance{}).Where("id = ?", id).First(&insurance)

	return insurance
}

func (vi *VehicleInsurance) Delete(id int64) error {
	return model.DB().Where("id = ?", id).Delete(&VehicleInsurance{}).Error
}

func (vi *VehicleInsurance) DeleteByVehicleId(tx *gorm.DB, vehicleId int64) error {
	return tx.Where("VehicleId = ?", vehicleId).Delete(&VehicleInsurance{}).Error
}

func (vi *VehicleInsurance) GetByInsuranceCode(code string) VehicleInsurance {
	var insurance VehicleInsurance
	model.DB().Model(&VehicleInsurance{}).Where("InsuranceCode = ?", code).First(&insurance)

	return insurance
}
func (vi *VehicleInsurance) IsExistWithInsuranceCode(code string) bool {
	var count int64
	model.DB().Model(&VehicleInsurance{}).Where("InsuranceCode = ?", code).Count(&count)

	return count > 0
}

func (vi *VehicleInsurance) GetByVehicleId(vehicleId int64) []VehicleInsurance {
	var insurances []VehicleInsurance
	model.DB().Model(&VehicleInsurance{}).Where("VehicleId = ?", vehicleId).Order("StartDate DESC").Find(&insurances)

	return insurances
}

func (vi *VehicleInsurance) GetBy(corporationIds, scrapVehicleIds, allVehicleIds []int64, license, code string, insuranceType, insuranceStatus, vehicleStatus int64, paginator model.Paginator) ([]VehicleInsurance, int64) {
	var insurances []VehicleInsurance
	subQuery := model.DB().Model(&VehicleInsurance{}).Select("*", "row_number() over (partition by VehicleId,InsuranceType order by EndDate desc)")

	tx := model.DB().Table("(?) AS tmp", subQuery).Where("row_number = ?", 1).Scopes(model.WhereCorporations(corporationIds))

	//报废车辆
	if vehicleStatus == util.VehicleStatusForScrap && len(scrapVehicleIds) > 0 {
		tx = tx.Where("VehicleId IN ?", scrapVehicleIds)
	}

	//使用中车辆
	if vehicleStatus == util.VehicleStatusForNormal && len(scrapVehicleIds) > 0 {
		tx = tx.Where("VehicleId NOT IN ?", scrapVehicleIds)
	}

	//未知车辆
	if vehicleStatus == util.VehicleStatusForUnknown && len(allVehicleIds) > 0 {
		tx = tx.Where("VehicleId NOT IN ?", allVehicleIds)
	}

	if license != "" {
		tx = tx.Where("License LIKE ?", "%"+license+"%")
	}

	if insuranceType > 0 {
		tx = tx.Where("InsuranceType = ?", insuranceType)
	}

	if code != "" {
		tx = tx.Where("InsuranceCode = ?", code)
	}

	if insuranceStatus > 0 {
		//保障中
		if insuranceStatus == 1 {
			tx = tx.Where("EndDate > (now()::timestamp + '45 day')")
		}

		//预警
		if insuranceStatus == 2 {
			tx = tx.Where("EndDate >= now()::date AND EndDate <= (now()::timestamp + '45 day') ")
		}

		//过期
		if insuranceStatus == 3 {
			tx = tx.Where("EndDate < now()::date")
		}
	}
	var count int64

	tx.Count(&count)

	tx.Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&insurances)

	return insurances, count
}

func (vi *VehicleInsurance) GetVehicleValidInsurance(vehicleId int64, date time.Time) []VehicleInsurance {
	var insurances []VehicleInsurance
	model.DB().Model(&VehicleInsurance{}).Where("VehicleId = ? AND StartDate <= ? AND EndDate >= ? AND EndDate >= ?", vehicleId, date.Format(model.DateFormat), date.Format(model.DateFormat), time.Now().Format(model.DateFormat)).Order("StartDate DESC").Find(&insurances)

	return insurances
}
