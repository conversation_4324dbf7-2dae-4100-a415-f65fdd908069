package safety

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

type HandleStatusType int8 // 门检记录处理状态
const (
	PRE_GENERATE_1 HandleStatusType = 1 // 预生成记录(未门检)
	CHECKED_2      HandleStatusType = 2 // 已门检记录
)

type ScheduleType int8 // 是否调度排班
const (
	YES_SCHEDULE_1 ScheduleType = 1 // 是调度排班车辆
	NO_SCHEDULE_2  ScheduleType = 2 // 不是调度排班车辆
)

type DoorCheckRecords struct {
	model.PkId

	model.GroupCorporation
	model.CompanyCorporation
	model.BranchCorporation
	model.DepartmentCorporation
	model.FleetCorporation

	License     string `json:"License"        gorm:"column:license;comment:车辆车牌号;type:varchar(50)"`    // 车辆车牌号
	VehicleCode string `json:"VehicleCode"    gorm:"column:vehiclecode;comment:车辆编号;type:varchar(50)"` // 车辆编号
	LineId      int64  `json:"LineId"      gorm:"column:lineid;comment:默认车属线路;type:integer"`           // -- 默认车属线路 也可以是选择的线路
	Line        string `json:"Line"        gorm:"column:line;comment:线路名;type:varchar(50)"`            // -- 线路名

	StaffId       int64  `json:"StaffId"        gorm:"column:staffid;comment:司机员工id;type:integer"`        // 司机员工id
	StaffName     string `json:"StaffName"      gorm:"column:staffname;comment:司机名;type:varchar(50)"`     // 司机名
	Type          int64  `json:"Type"           gorm:"column:type;comment:检测类型;type:smallint"`            // 检测类型 1自检 2抽检
	AdminUserId   int64  `json:"AdminUserId"   gorm:"column:adminuserid;comment:抽检人员工id;type:integer"`    // 抽检人员工id 自检为0
	AdminUserName string `json:"AdminUserName" gorm:"column:adminusername;comment:抽检人名;type:varchar(50)"` // 抽检人名 自检为''

	CheckForm int64 `json:"CheckForm" gorm:"column:checkform;comment:检测形式;type:smallint"` // 检测形式 1司机出场自检 2司机回场自检 4管理员抽检

	Result int64 `json:"Result" gorm:"column:result;comment:自检、抽检最终结果;type:smallint"` // 自检、抽检最终结果(所有结果有一项异常则Result=2) 0未门检 1正常 2异常

	StatusCount string `json:"StatusCount" gorm:"column:statuscount;comment:门检项目各个状态数量;type:string;default:'0,0,0,0,0'"` // 门检项目各个状态数量 '正常数量，待整改数量，待解决数量，已解决数量，已关闭数量'

	HandleStatus HandleStatusType `json:"HandleStatus" gorm:"column:handlestatus;type:smallint;comment:门检记录处理状态;"` // 门检记录处理状态 PRE_GENERATE_1 预生成记录(未门检) CHECKED_2 已门检记录

	Schedule ScheduleType `json:"Schedule" gorm:"column:schedule;type:smallint;comment:调度排班;"` // 调度排班 YES_SCHEDULE_1 是调度排班车辆 NO_SCHEDULE_2 不是调度排班车辆

	model.Timestamp
}

func (d *DoorCheckRecords) BeforeCreate(tx *gorm.DB) (err error) {
	if d.Id == 0 {
		d.Id = model.Id()
	}
	return
}

func (d *DoorCheckRecords) Adds(items []DoorCheckRecords) error {
	return model.DB().Create(&items).Error
}

type IsCheckedResponse struct {
	GoOutChecked  bool // 出场是否进行了门检
	GoBackChecked bool // 回厂是否进行了门检
}

func IsCheckedScope(vehicleCode string, startAt, endAt time.Time) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Model(&DoorCheckRecords{}).Select("Id").Where("VehicleCode LIKE ? AND CreatedAt >= ? AND CreatedAt < ?", vehicleCode, startAt, endAt)
	}
}

func IsCheckedScopeOut(db *gorm.DB) *gorm.DB {
	return db.Where("CheckForm = ? AND HandleStatus = ?", 1, CHECKED_2)
}

func IsCheckedScopeBack(db *gorm.DB) *gorm.DB {
	return db.Where("CheckForm = ? AND HandleStatus = ?", 2, CHECKED_2)
}

// 查询车辆 在时间段是否有出回场门检
func (d *DoorCheckRecords) IsVehicleChecked(startAt, endAt time.Time) (IsCheckedResponse, error) {
	var (
		rsp       IsCheckedResponse
		out, back int64
	)

	err := model.DB().Model(&DoorCheckRecords{}).Scopes(IsCheckedScope(d.VehicleCode, startAt, endAt), IsCheckedScopeOut).First(&out).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			rsp.GoOutChecked = false
			err = nil
		} else {
			return IsCheckedResponse{}, err
		}
	} else {
		rsp.GoOutChecked = true
	}

	err = model.DB().Model(&DoorCheckRecords{}).Scopes(IsCheckedScope(d.VehicleCode, startAt, endAt), IsCheckedScopeBack).First(&back).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			rsp.GoBackChecked = false
			err = nil
		} else {
			return IsCheckedResponse{}, err
		}
	} else {
		rsp.GoBackChecked = true
	}
	return rsp, err
}

// VehicleRecord 车辆门检记录
func (d *DoorCheckRecords) VehicleRecord(groupId int64, license string, checkForm int64, startAt, endAt time.Time) ([]DoorCheckRecords, error) {
	var res []DoorCheckRecords
	err := model.DB().Model(&DoorCheckRecords{}).Where(
		"GroupId = ? AND License = ? AND CheckForm = ? AND CreatedAt >= ? AND CreatedAt < ?",
		groupId, license, checkForm, startAt, endAt,
	).Scan(&res).Error
	return res, err
}

func (d *DoorCheckRecords) UpdatePreGenerateRecord(tx *gorm.DB) error {
	return tx.Model(&DoorCheckRecords{}).Where("Id = ?", d.Id).Select(
		"CompanyId", "BranchId", "DepartmentId", "FleetId", "StaffId",
		"StaffName", "Result", "StatusCount", "HandleStatus",
	).Updates(d).Error
}

func (d *DoorCheckRecords) AddTx(tx *gorm.DB) error {
	return tx.Create(d).Error
}

// 查询web列表
func (d *DoorCheckRecords) ListWeb(
	startAt, endAt time.Time, corporationIds []int64,
	LineId, staffId int64, license string,
) ([]DoorCheckRecords, error) {
	var rsp []DoorCheckRecords

	tx := model.DB().Model(&DoorCheckRecords{}).Where(
		"CreatedAt >= ? AND CreatedAt < ? AND (GroupId IN (?) OR CompanyId IN (?) OR BranchId IN (?) OR DepartmentId IN (?) OR FleetId IN (?))",
		startAt, endAt, corporationIds, corporationIds, corporationIds, corporationIds, corporationIds)

	if LineId > 0 {
		tx.Where("LineId = ?", LineId)
	}
	if staffId > 0 {
		tx.Where("StaffId = ?", staffId)
	}
	//if len(checkForms) > 0 {
	//	tx.Where("CheckForm IN ?", checkForms)
	//}
	//if len(results) > 0 {
	//	tx.Where("Result IN ?", results)
	//}
	//if len(statuses) > 0 {
	//	tx.Where("Status IN ?", statuses)
	//}
	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}

	err := tx.Order("CreatedAt Desc").Scan(&rsp).Error

	return rsp, err
}

// 查询app列表
func (d *DoorCheckRecords) ListApp(
	startAt, endAt time.Time, corporationIds []int64,
	LineId, staffId int64, license, keyword string,
) ([]DoorCheckRecords, error) {
	var rsp []DoorCheckRecords

	tx := model.DB().Model(&DoorCheckRecords{}).Where(
		"CreatedAt >= ? AND CreatedAt < ? AND (GroupId IN (?) OR CompanyId IN (?) OR BranchId IN (?) OR DepartmentId IN (?) OR FleetId IN (?))",
		startAt, endAt, corporationIds, corporationIds, corporationIds, corporationIds, corporationIds)

	if LineId > 0 {
		tx.Where("LineId = ?", LineId)
	}
	if staffId > 0 {
		tx.Where("StaffId = ?", staffId)
	}
	//if checkForm > 0 {
	//	tx.Where("CheckForm = ?", checkForm)
	//}
	//if result > 0 {
	//	tx.Where("Result = ?", result)
	//}

	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}
	if keyword != "" {
		tx.Where("License LIKE ? OR StaffName LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	err := tx.Order("CreatedAt Desc").Scan(&rsp).Error

	return rsp, err
}

type EditResultParamsItems struct {
	ResultEditParams

	Rectified Rectified // 追加整改内容
}

type ResultEditParams struct {
	ItemResultId    int64  //door_check_item_results.Id
	ItemResultIdStr string //door_check_item_results.Id 兼容微信
	ItemStatus      int64  // 项目状态 前端不传 后端设置

	ReportUserId    int64  // 上报人 此项被上报才有值 后端计算
	ReportUserName  string // 上报人名 后端计算
	ResolveUserId   int64  // 解决人 此项被解决（整改）才有值 后端计算
	ResolveUserName string // 解决人名 后端计算

	NoticeUserId   int64  // 上报通知人员id
	NoticeUserName string // 上报通知人员 //前端不传
	NoticeText     string // 上报通知文本
	NoticeId       string // 修改上报通知 旧id
	NewNoticeId    string // 新发送的noticeId
	Files          []ResultJsonFile
}

// 整改
func (d *DoorCheckRecords) Edit(
	items []EditResultParamsItems,
) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {

		//更新json
		for _, item := range items {
			var (
				itemResult DoorCheckItemResult
				rJons      ResultJson
				bytes      []byte
			)

			err := tx.Model(&DoorCheckItemResult{}).Select("ResultJson").Where("Id = ?", item.ItemResultId).Scan(&itemResult).Error
			if err != nil {
				return err
			}

			err = json.Unmarshal(itemResult.ResultJson, &rJons)
			if err != nil {
				return err
			}

			rJons.Rectified = append(rJons.Rectified, item.Rectified)
			bytes, err = json.Marshal(rJons)
			if err != nil {
				return err
			}

			err = tx.Model(&DoorCheckItemResult{}).Select("ResultJson", "Status").Where("Id = ?", item.ItemResultId).Updates(map[string]interface{}{"ResultJson": bytes, "Status": item.ItemStatus}).Error
			if err != nil {
				return err
			}

		}

		// 更新状态统计
		err := tx.Model(&DoorCheckRecords{}).Select("StatusCount").Where("Id = ?", d.Id).Updates(d).Error
		if err != nil {
			return err
		}

		for _, item := range items {
			err := tx.Model(&DoorCheckItemResult{}).Where("Id = ?", item.ItemResultId).Updates(map[string]interface{}{
				"Status":          item.ItemStatus,
				"ResolveUserId":   item.ResolveUserId,
				"ResolveUserName": item.ResolveUserName,
			}).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}

// 上报
func (d *DoorCheckRecords) Report(
	items []EditResultParamsItems,
	sendUserId, fkItemId int64,
	sendUserName, itemName string,

) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {

		// 更新状态统计
		err := tx.Model(&DoorCheckRecords{}).Select("StatusCount").Where("Id = ?", d.Id).Updates(d).Error
		if err != nil {
			return err
		}

		for _, item := range items {
			err := tx.Model(&DoorCheckItemResult{}).Where("Id = ?", item.ItemResultId).Updates(map[string]interface{}{
				"Status":         item.ItemStatus,
				"ReportUserId":   item.ReportUserId,
				"ReportUserName": item.ReportUserName,
			}).Error
			if err != nil {
				return err
			}

			// 存消息
			parseInt, _ := strconv.ParseInt(item.NewNoticeId, 10, 64)

			notice := DoorCheckNotices{
				PkId:         model.PkId{Id: parseInt},
				FkRecordId:   d.Id,
				FkItemId:     fkItemId,
				ItemName:     itemName,
				SendUserId:   sendUserId,
				SendUserName: sendUserName,
				ContentJson:  nil,
				Status:       1,
				NoticeType:   1,
				Timestamp:    model.Timestamp{},
			}

			contentJson := ContentJson{
				Files:  nil,
				Remark: item.NoticeText,
			}
			bytes, err := json.Marshal(contentJson)
			if err != nil {
				return err
			}
			notice.ContentJson = model.JSON(bytes)

			err = (&notice).AddTx(tx, []DoorCheckNoticeReceives{{
				UserId:   item.NoticeUserId,
				UserName: item.NoticeUserName,
			}})
			if err != nil {
				return err
			}

		}

		return nil
	})
}

// 编辑上报
func (d *DoorCheckRecords) EditReport(
	items []EditResultParamsItems,
	sendUserId, fkItemId int64,
	sendUserName, itemName string,

) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {

		for _, item := range items {

			err := tx.Model(&DoorCheckItemResult{}).Where("Id = ?", item.ItemResultId).Updates(map[string]interface{}{

				"ReportUserId":   item.ReportUserId,
				"ReportUserName": item.ReportUserName,
			}).Error
			if err != nil {
				return err
			}

			if item.NoticeId == "" {
				return errors.New("noticeId is empty")
			}

			nid, _ := strconv.ParseInt(item.NoticeId, 10, 64)

			// 撤销旧消息
			err = (&DoorCheckNotices{}).EditReportTx(tx, nid)
			if err != nil {
				return err
			}

			// 存消息
			parseInt, _ := strconv.ParseInt(item.NewNoticeId, 10, 64)
			notice := DoorCheckNotices{
				PkId:         model.PkId{Id: parseInt},
				FkRecordId:   d.Id,
				FkItemId:     fkItemId,
				ItemName:     itemName,
				SendUserId:   sendUserId,
				SendUserName: sendUserName,
				ContentJson:  nil,
				Status:       1,
				NoticeType:   1,
				Timestamp:    model.Timestamp{},
			}

			contentJson := ContentJson{
				Files:  nil,
				Remark: item.NoticeText,
			}
			bytes, err := json.Marshal(contentJson)
			if err != nil {
				return err
			}
			notice.ContentJson = model.JSON(bytes)

			err = (&notice).AddTx(tx, []DoorCheckNoticeReceives{{
				UserId:   item.NoticeUserId,
				UserName: item.NoticeUserName,
			}})
			if err != nil {
				return err
			}

		}

		return nil
	})
}

// 处理
func (d *DoorCheckRecords) Process(
	items []EditResultParamsItems,
	sendUserId, fkItemId int64,
	sendUserName, itemName string,

) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {

		// 更新状态统计
		err := tx.Model(&DoorCheckRecords{}).Select("StatusCount").Where("Id = ?", d.Id).Updates(d).Error
		if err != nil {
			return err
		}

		for _, item := range items {
			err := tx.Model(&DoorCheckItemResult{}).Where("Id = ?", item.ItemResultId).Updates(map[string]interface{}{
				"Status":          item.ItemStatus,
				"ResolveUserId":   item.ResolveUserId,
				"ResolveUserName": item.ResolveUserName,
			}).Error
			if err != nil {
				return err
			}

			// 存消息
			parseInt, _ := strconv.ParseInt(item.NewNoticeId, 10, 64)
			notice := DoorCheckNotices{
				PkId:         model.PkId{Id: parseInt},
				FkRecordId:   d.Id,
				FkItemId:     fkItemId,
				ItemName:     itemName,
				SendUserId:   sendUserId,
				SendUserName: sendUserName,
				ContentJson:  nil,
				Status:       1,
				NoticeType:   2,
				Timestamp:    model.Timestamp{},
			}

			contentJson := ContentJson{
				Files:  item.Files,
				Remark: item.NoticeText,
			}
			bytes, err := json.Marshal(contentJson)
			if err != nil {
				return err
			}
			notice.ContentJson = model.JSON(bytes)

			// 消息反馈给司机 + 上报人

			err = (&notice).AddTx(tx, []DoorCheckNoticeReceives{{
				UserId:   item.NoticeUserId,
				UserName: item.NoticeUserName,
			}})
			if err != nil {
				return err
			}

		}

		return nil
	})
}

// 编辑处理
func (d *DoorCheckRecords) EditProcess(
	items []EditResultParamsItems,
	sendUserId, fkItemId int64,
	sendUserName, itemName string,

) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {

		for _, item := range items {

			err := tx.Model(&DoorCheckItemResult{}).Where("Id = ?", item.ItemResultId).Updates(map[string]interface{}{
				"Status":          item.ItemStatus,
				"ResolveUserId":   item.ResolveUserId,
				"ResolveUserName": item.ResolveUserName,
			}).Error
			if err != nil {
				return err
			}

			if item.NoticeId == "" {
				return errors.New("noticeId is empty")
			}

			nid, _ := strconv.ParseInt(item.NoticeId, 10, 64)

			// 撤销旧消息
			err = (&DoorCheckNotices{}).EditReportTx(tx, nid)
			if err != nil {
				return err
			}

			// 存消息
			parseInt, _ := strconv.ParseInt(item.NewNoticeId, 10, 64)
			notice := DoorCheckNotices{
				PkId:         model.PkId{Id: parseInt},
				FkRecordId:   d.Id,
				FkItemId:     fkItemId,
				ItemName:     itemName,
				SendUserId:   sendUserId,
				SendUserName: sendUserName,
				ContentJson:  nil,
				Status:       1,
				NoticeType:   2,
				Timestamp:    model.Timestamp{},
			}

			contentJson := ContentJson{
				Files:  nil,
				Remark: item.NoticeText,
			}
			bytes, err := json.Marshal(contentJson)
			if err != nil {
				return err
			}
			notice.ContentJson = model.JSON(bytes)

			err = (&notice).AddTx(tx, []DoorCheckNoticeReceives{{
				UserId:   item.NoticeUserId,
				UserName: item.NoticeUserName,
			}})
			if err != nil {
				return err
			}

		}

		return nil
	})
}

// 获取当前记录异常项目
func (d *DoorCheckRecords) GetAbNormalLabels() ([]string, error) {
	var rsp []string

	err := model.DB().Model(&DoorCheckItemResult{}).Select("ItemName").Where("FkRecordId = ? AND Status > ?", d.Id, 1).Find(&rsp).Error

	return rsp, err

}

// 处理门检
func (d *DoorCheckRecords) ProcessResult() error {
	return model.DB().Model(&DoorCheckRecords{}).Select("Process").Where("Id = ?", d.Id).Updates(d).Error
}

func (d *DoorCheckRecords) GetById() (DoorCheckRecords, error) {
	var rsp DoorCheckRecords
	err := model.DB().Model(&DoorCheckRecords{}).Where("Id = ?", d.Id).Scan(&rsp).Error
	return rsp, err
}

func (d *DoorCheckRecords) List(corporationIds []int64, license string, lineId int64, staffId int64, checkForms, results, statuses []int64, startAt, endAt time.Time) ([]DoorCheckRecords, error) {
	var rsp []DoorCheckRecords
	tx := model.DB().Model(&DoorCheckRecords{}).Where("CreatedAt >= ? AND CreatedAt < ? AND (GroupId IN (?) OR CompanyId IN (?) OR BranchId IN (?) OR DepartmentId IN (?) OR FleetId IN (?))",
		startAt, endAt, corporationIds, corporationIds, corporationIds, corporationIds, corporationIds)

	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}

	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	if staffId > 0 {
		tx.Where("StaffId = ?", staffId)
	}

	if len(checkForms) > 0 {
		tx.Where("CheckForm IN ?", checkForms)
	}
	if len(results) > 0 {
		tx.Where("Result IN ?", results)
	}
	if len(statuses) > 0 {
		// statuses = [1, 2, 8]
		// StatusCount ~ "^[^0],[^0],0,[^0],0$"
		querySli := make([]string, 5)
		if util.IncludeInt64(statuses, 1) {
			querySli[0] = "[^0]"
		} else {
			querySli[0] = "0"
		}
		if util.IncludeInt64(statuses, 2) {
			querySli[1] = "[^0]"
		} else {
			querySli[1] = "0"
		}
		if util.IncludeInt64(statuses, 4) {
			querySli[2] = "[^0]"
		} else {
			querySli[2] = "0"
		}
		if util.IncludeInt64(statuses, 8) {
			querySli[3] = "[^0]"
		} else {
			querySli[3] = "0"
		}
		if util.IncludeInt64(statuses, 16) {
			querySli[4] = "[^0]"
		} else {
			querySli[4] = "0"
		}
		tx.Where("StatusCount ~ ?", "^"+strings.Join(querySli, ",")+"$")
	}

	err := tx.Order("HandleStatus Desc, License ASC").Scan(&rsp).Error

	return rsp, err
}

// All 获取时间段内所有车辆
func (d *DoorCheckRecords) All(corporationIds []int64, startAt, endAt time.Time) ([]DoorCheckRecords, error) {
	var rsp []DoorCheckRecords
	err := model.DB().Model(&DoorCheckRecords{}).Where("CreatedAt >= ? AND CreatedAt < ? AND (GroupId IN (?) OR CompanyId IN (?) OR BranchId IN (?) OR DepartmentId IN (?) OR FleetId IN (?))",
		startAt, endAt, corporationIds, corporationIds, corporationIds, corporationIds, corporationIds).Scan(&rsp).Error
	return rsp, err
}

// FindRecord 寻找今日数据
func (d *DoorCheckRecords) FindRecord(groupId int64, startAt, endAt time.Time) ([]DoorCheckRecords, error) {
	var rsp []DoorCheckRecords
	err := model.DB().Model(&DoorCheckRecords{}).Where("CreatedAt >= ? AND CreatedAt < ? AND GroupId = ?",
		startAt, endAt, groupId).Scan(&rsp).Error
	return rsp, err
}
