package safety

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type TrafficAccidentSetting struct {
	model.PkId
	GroupId int64 `json:"GroupId" gorm:"column:groupid;type:bigint;comment:集团id;uniqueIndex:tas_groupid_code;uniqueIndex:tas_groupid_corporationid"` // 集团id
	model.CompanyCorporation
	model.BranchCorporation
	model.DepartmentCorporation
	model.FleetCorporation
	CorporationId int64  `json:"CorporationId" gorm:"column:corporationid;type:bigint;comment:机构id;uniqueIndex:tas_groupid_corporationid"` // 机构id
	Code          string `json:"Code" gorm:"column:code;type:varchar;comment:机构编号;uniqueIndex:tas_groupid_code"`                           //机构编号

	//
	CorporationName string `json:"CorporationName" gorm:"-"`
	Option          string `json:"Option" gorm:"-"`
	model.Timestamp
}

// GetBy 获取列表
func (tas *TrafficAccidentSetting) GetBy(groupId int64) ([]TrafficAccidentSetting, error) {
	var rsp []TrafficAccidentSetting
	err := model.DB().Model(&TrafficAccidentSetting{}).Where("GroupId=?", groupId).Find(&rsp).Error
	return rsp, err
}

func (tas *TrafficAccidentSetting) FindByCorporationId(corporationId int64) TrafficAccidentSetting {
	var setting TrafficAccidentSetting
	model.DB().Model(&TrafficAccidentSetting{}).Where("CorporationId=?", corporationId).First(&setting)
	return setting
}

func (tas *TrafficAccidentSetting) IsExistsCode(groupId int64, code string) bool {
	var count int64
	model.DB().Model(&TrafficAccidentSetting{}).Where("GroupId=?", groupId).Where("Code=?", code).Count(&count)
	return count > 0
}

func (tas *TrafficAccidentSetting) IsExistsCorporationId(groupId, corporationId int64) bool {
	var count int64
	model.DB().Model(&TrafficAccidentSetting{}).Where("GroupId=?", groupId).Where("CorporationId=?", corporationId).Count(&count)
	return count > 0
}

func (tas *TrafficAccidentSetting) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&tas).Error
}

func (tas *TrafficAccidentSetting) TransactionUpdate(tx *gorm.DB, columns map[string]interface{}) error {
	return tx.Model(&TrafficAccidentSetting{}).Where("Id=?", tas.Id).Updates(columns).Error
}
func (tas *TrafficAccidentSetting) TransactionDelete(tx *gorm.DB) error {
	return tx.Where("Id=?", tas.Id).Delete(&TrafficAccidentSetting{}).Error
}
