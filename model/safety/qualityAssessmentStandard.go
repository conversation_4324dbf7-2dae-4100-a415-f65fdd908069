package safety

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"strconv"
)

type ViolationCategoryType int64

const (
	CLASS_1    ViolationCategoryType = 1 // 规范类别    1类 2类 ...
	VALUE_2    ViolationCategoryType = 2 // 扣分   0.1 0.5 ...
	CATEGORY_3 ViolationCategoryType = 3 // 违规类型
)

type ViolationCategoryIsDeleteType int64

const (
	NO_0  ViolationCategoryIsDeleteType = 0 // 未删除
	YES_1 ViolationCategoryIsDeleteType = 1 // 已删除
)

type ViolationCategory struct {
	model.PkId

	//model.GroupCorporation
	//model.CompanyCorporation
	//model.BranchCorporation
	//model.DepartmentCorporation
	//model.FleetCorporation

	// 公司 <- 规范类别 <- 扣分 <- 违规类型
	QualityAssessmentCateId int64                 `json:"QualityAssessmentCateId" gorm:"column:qualityassessmentcateid;type:bigint;default:0;comment:质量考核规范分类ID 关联quality_assessment_cates表ID" validate:"required"`
	ItemType                ViolationCategoryType `json:"ItemType"        gorm:"column:itemtype;comment: CLASS_1 规范类别  VALUE_2 扣分 CATEGORY_3 违规类型;type:smallint"` // CLASS_1 规范类别  VALUE_2 扣分 CATEGORY_3 违规类型

	ParentId     int64  `json:"ParentId"        gorm:"column:parentid;comment:CLASS_1 规范类别 ParentId=0;type:bigint"`    // CLASS_1 规范类别 ParentId=0
	ParentIdPath string `json:"ParentIdPath"    gorm:"column:parentidpath;comment:ParentIdPath(包含自己) 1,2,3;type:text"` // ParentIdPath(包含自己) 1,2,3

	Name        string `json:"Name" gorm:"column:name;comment:门检类别名  门检项目名  项目下的门检标签名;type:varchar(50)"`                    // 门检类别名  门检项目名  项目下的门检标签名
	DeductScore int64  `json:"DeductScore" gorm:"column:deductscore;comment:VALUE_2 扣分分值  1/100分 即 1分 记作 100;type:integer"` // VALUE_2 扣分分值  1/100分 即 1分 记作 100

	IsDeleted ViolationCategoryIsDeleteType `json:"IsDeleted" gorm:"column:isdeleted;comment:是否已删除 NO_0否 YES_1是 软删除后不允许恢复;type:smallint"` // 是否已删除 NO_0否 YES_1是 软删除后不允许恢复

	model.Timestamp
}

func (v *ViolationCategory) BeforeCreate(db *gorm.DB) error {
	v.Id = model.Id()
	return nil
}

func (v *ViolationCategory) Add() error {
	return model.DB().Create(&v).Error
}

func (v *ViolationCategory) GetByParentId() (ViolationCategory, error) {
	var rsp ViolationCategory
	err := model.DB().Model(&ViolationCategory{}).Where("Id = ?", v.ParentId).Scan(&rsp).Error
	return rsp, err
}

type ViolationCategoryClass struct {
	ViolationCategory                          // itemType=规范类别
	SubItems          []ViolationCategoryValue // itemType=扣分
}

type ViolationCategoryValue struct {
	ViolationCategory
	SubItems []ViolationCategory // itemType=违规类型
}

func (v *ViolationCategory) List(assessmentCateId int64) ([]ViolationCategoryClass, error) {
	var rsp []ViolationCategoryClass
	rows1, err := model.DB().Model(&ViolationCategory{}).Where("QualityAssessmentCateId = ?", assessmentCateId).Where("ItemType = ? AND IsDeleted = ?", CLASS_1, NO_0).Select(
		"Id", "ItemType", "ParentId", "ParentIdPath", "Name", "DeductScore").Rows()
	if err != nil {
		return nil, err
	}
	defer rows1.Close()

	for rows1.Next() {
		var vcClass ViolationCategoryClass
		err := rows1.Scan(&vcClass.Id, &vcClass.ItemType, &vcClass.ParentId, &vcClass.ParentIdPath, &vcClass.Name, &vcClass.DeductScore)
		if err != nil {
			return nil, err
		}

		err = func() error {
			rows2, err := model.DB().Model(&ViolationCategory{}).Where("ParentId = ? AND IsDeleted = ?", vcClass.Id, NO_0).Select(
				"Id", "ItemType", "ParentId", "ParentIdPath", "Name", "DeductScore").Rows()
			if err != nil {
				return err
			}
			defer rows2.Close()

			for rows2.Next() {
				var vcValue ViolationCategoryValue

				err := rows2.Scan(&vcValue.Id, &vcValue.ItemType, &vcValue.ParentId, &vcValue.ParentIdPath, &vcValue.Name, &vcValue.DeductScore)
				if err != nil {
					return err
				}

				err = func() error {
					rows3, err := model.DB().Model(&ViolationCategory{}).Where("ParentId = ? AND IsDeleted = ?", vcValue.Id, NO_0).Select(
						"Id", "ItemType", "ParentId", "ParentIdPath", "Name", "DeductScore").Rows()
					if err != nil {
						return err
					}
					defer rows3.Close()

					for rows3.Next() {
						var vcCategory ViolationCategory
						err := rows3.Scan(&vcCategory.Id, &vcCategory.ItemType, &vcCategory.ParentId, &vcCategory.ParentIdPath, &vcCategory.Name, &vcCategory.DeductScore)
						if err != nil {
							return err
						}
						vcValue.SubItems = append(vcValue.SubItems, vcCategory)
					}
					return nil
				}()
				if err != nil {
					return err
				}

				vcClass.SubItems = append(vcClass.SubItems, vcValue)
				//return nil
			}
			return nil
		}()
		if err != nil {
			return nil, err
		}

		rsp = append(rsp, vcClass)

	}

	return rsp, nil
}

func (v *ViolationCategory) Edit() error {
	return model.DB().Model(&ViolationCategory{}).Select("Name").Where("Id = ?", v.Id).Updates(&v).Error
}

func (v *ViolationCategory) Delete() error {
	return model.DB().Model(&ViolationCategory{}).Where("Id = ?", v.Id).Update("IsDeleted", YES_1).Error
}

type QualityAssessmentStandards struct {
	model.PkId
	QualityAssessmentCateId int64  `json:"QualityAssessmentCateId" gorm:"column:qualityassessmentcateid;type:bigint;default:0;comment:质量考核规范分类ID 关联quality_assessment_cates表ID" validate:"required"`
	Code                    string `json:"Code" gorm:"column:code;comment:执行标准号;varchar(100)" validate:"required"`                                 // 执行标准号
	Describe                string `json:"Describe" gorm:"column:describe;comment:执行标准描述;type:text" validate:"required"`                           // 执行标准描述
	ClassId                 int64  `json:"ClassId" gorm:"column:classid;comment:规范类别id;type:bigint" validate:"required"`                           // 冗余 规范类别id
	ClassName               string `json:"ClassName" gorm:"column:classname;comment:规范类别名;type:varchar(50)" validate:"required"`                   // 冗余 规范类别名
	ValueId                 int64  `json:"ValueId" gorm:"column:valueid;comment:扣分id;type:bigint" validate:"required"`                             // 冗余 扣分id
	ValueName               string `json:"ValueName" gorm:"column:valuename;comment:扣分名;type:varchar(50)" validate:"required"`                     // 冗余 扣分名
	DeductScore             int64  `json:"DeductScore" gorm:"column:deductscore;comment:扣分分值 1/100分 即 1分 记作 100;type:integer" validate:"required"` // 冗余 扣分分值  1/100分 即 1分 记作 100
	CategoryId              int64  `json:"CategoryId" gorm:"column:categoryid;comment:规范类别id;type:bigint" validate:"required"`                     // 规范类别id
	CategoryName            string `json:"CategoryName" gorm:"column:categoryname;comment:规范类别名;type:varchar(50)" validate:"required"`             // 规范类别名
	Status                  int64  `json:"Status" gorm:"column:status;comment:状态 1启用 2禁用;type:smallint;default:1"`
	AttrType                int64  `json:"AttrType" gorm:"column:attrtype;type:smallint;comment:属性类型 1安全类 2非安全类 3交通违章 4其他"`
	model.Timestamp

	ViolationCount int64 `json:"ViolationCount" gorm:"-"`
}

func (q *QualityAssessmentStandards) BeforeCreate(db *gorm.DB) error {
	q.Id = model.Id()
	q.Status = util.StatusForTrue
	return nil
}
func (q *QualityAssessmentStandards) Add() error {
	return model.DB().Create(&q).Error
}

func (q *QualityAssessmentStandards) AddBatch(cateId int64, items []QualityAssessmentStandards) error {
	// 查询出所有 规范类别 扣分 违规类型，根据导入的数据查询，是否需要新增
	return model.DB().Transaction(func(tx *gorm.DB) error {
		for _, item := range items {

			list, err := q.TreeTx(tx, cateId)
			if err != nil {
				return err
			}

			qasExist := false //
			describeEq := false

		loop:
			for _, class := range list {
				if item.ClassName == class.Name {
					item.ClassId = class.Id
					for _, value := range class.SubItems {
						if item.ValueName == value.Name {
							item.ValueId = value.Id
							for _, category := range value.SubItems {
								if item.CategoryName == category.Name {
									item.CategoryId = category.Id
									for _, qas := range category.SubItems {
										if item.Code == qas.Code {
											qasExist = true
											if item.Describe == qas.Describe {
												describeEq = true
											}
											break loop
										}
									}
								}
							}
						}
					}
				}
			}

			if qasExist {
				// 质量考核标准存在

				if describeEq {
					//	描述一致
					continue
				} else {
					// 更新
					err = tx.Model(&QualityAssessmentStandards{}).Where(
						"ClassId = ? AND ValueId = ? AND CategoryId = ? AND Code = ?",
						item.ClassId, item.ValueId, item.CategoryId, item.Code).Update(
						"Describe", item.Describe).Error
					if err != nil {
						return err
					}
				}

			} else {
				// 质量考核标准不存在

				if item.ClassId <= 0 {
					vcClass := ViolationCategory{
						PkId:                    model.PkId{Id: model.Id()},
						QualityAssessmentCateId: cateId,
						ItemType:                CLASS_1,
						ParentId:                0,
						ParentIdPath:            "",
						Name:                    item.ClassName,
						DeductScore:             0,
						IsDeleted:               NO_0,
						Timestamp:               model.Timestamp{},
					}
					vcClass.ParentIdPath = strconv.FormatInt(vcClass.Id, 10)

					vcValue := ViolationCategory{
						PkId:                    model.PkId{Id: model.Id()},
						QualityAssessmentCateId: cateId,
						ItemType:                VALUE_2,
						ParentId:                vcClass.Id,
						ParentIdPath:            "",
						Name:                    item.ValueName,
						DeductScore:             0,
						IsDeleted:               NO_0,
						Timestamp:               model.Timestamp{},
					}
					vcValue.ParentIdPath = fmt.Sprintf(`%s,%d`, vcClass.ParentIdPath, vcValue.Id)
					parseFloat, err := strconv.ParseFloat(vcValue.Name, 64)
					if err != nil {
						log.Error("ParseFloat err = ", err)
						return err
					}
					vcValue.DeductScore = int64(parseFloat * 100) // 舍去千分位

					vcCategory := ViolationCategory{
						PkId:                    model.PkId{Id: model.Id()},
						QualityAssessmentCateId: cateId,
						ItemType:                CATEGORY_3,
						ParentId:                vcValue.Id,
						ParentIdPath:            "",
						Name:                    item.CategoryName,
						DeductScore:             0,
						IsDeleted:               NO_0,
						Timestamp:               model.Timestamp{},
					}
					vcCategory.ParentIdPath = fmt.Sprintf(`%s,%d`, vcValue.ParentIdPath, vcCategory.Id)
					err = tx.Create([]*ViolationCategory{&vcClass, &vcValue, &vcCategory}).Error
					if err != nil {
						return err
					}
					item.ClassId = vcClass.Id
					item.ValueId = vcValue.Id
					item.CategoryId = vcCategory.Id

				} else {
					if item.ValueId <= 0 {
						vcValue := ViolationCategory{
							PkId:                    model.PkId{Id: model.Id()},
							QualityAssessmentCateId: cateId,
							ItemType:                VALUE_2,
							ParentId:                item.ClassId,
							ParentIdPath:            "",
							Name:                    item.ValueName,
							DeductScore:             0,
							IsDeleted:               NO_0,
							Timestamp:               model.Timestamp{},
						}
						vcValue.ParentIdPath = fmt.Sprintf(`%d,%d`, item.ClassId, vcValue.Id)
						parseFloat, err := strconv.ParseFloat(vcValue.Name, 64)
						if err != nil {
							log.Error("ParseFloat err = ", err)
							return err
						}
						vcValue.DeductScore = int64(parseFloat * 100) // 舍去千分位

						vcCategory := ViolationCategory{
							PkId:                    model.PkId{Id: model.Id()},
							QualityAssessmentCateId: cateId,
							ItemType:                CATEGORY_3,
							ParentId:                vcValue.Id,
							ParentIdPath:            "",
							Name:                    item.CategoryName,
							DeductScore:             0,
							IsDeleted:               NO_0,
							Timestamp:               model.Timestamp{},
						}
						vcCategory.ParentIdPath = fmt.Sprintf(`%s,%d`, vcValue.ParentIdPath, vcCategory.Id)

						err = tx.Create([]*ViolationCategory{&vcValue, &vcCategory}).Error
						if err != nil {
							return err
						}

						item.ValueId = vcValue.Id
						item.CategoryId = vcCategory.Id
					} else {
						if item.CategoryId <= 0 {
							vcCategory := ViolationCategory{
								PkId:                    model.PkId{Id: model.Id()},
								QualityAssessmentCateId: cateId,
								ItemType:                CATEGORY_3,
								ParentId:                item.ValueId,
								ParentIdPath:            "",
								Name:                    item.CategoryName,
								DeductScore:             0,
								IsDeleted:               NO_0,
								Timestamp:               model.Timestamp{},
							}
							vcCategory.ParentIdPath = fmt.Sprintf(`%d,%d,%d`, item.ClassId, item.ValueId, vcCategory.Id)

							err = tx.Create(&vcCategory).Error
							if err != nil {
								return err
							}

							item.CategoryId = vcCategory.Id
						}
					}
				}

				err = tx.Create(&item).Error
				if err != nil {
					return err
				}
			}
		}

		return nil
	})

}

func (q *QualityAssessmentStandards) List(cateId, status int64, describe string, paginator model.Paginator) ([]QualityAssessmentStandards, int64, error) {
	var rsp []QualityAssessmentStandards
	var totalCount int64
	tx := model.DB().Model(&QualityAssessmentStandards{}).Where("QualityAssessmentCateId = ?", cateId)

	if status > 0 {
		tx.Where("Status = ?", status)
	}

	if describe != "" {
		tx = tx.Where("Describe LIKE ?", "%"+describe+"%")
	}

	err := tx.Count(&totalCount).Order("Code ASC").Order("Id desc").Limit(paginator.Limit).Offset(paginator.Offset).Scan(&rsp).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, 0, nil
		} else {
			return nil, 0, err
		}
	}
	return rsp, totalCount, nil
}

func (q *QualityAssessmentStandards) Edit() error {
	return model.DB().Model(&QualityAssessmentStandards{}).Where("Id = ?", q.Id).
		Select("Describe", "AttrType").Updates(&q).Error
}

func (q *QualityAssessmentStandards) Delete() error {
	return model.DB().Where("Id = ?", q.Id).Delete(&QualityAssessmentStandards{}).Error
}

func (q *QualityAssessmentStandards) EditStatus() error {
	return model.DB().Model(&QualityAssessmentStandards{}).Where("Id = ?", q.Id).Select("Status").Updates(&q).Error
}

func (q *QualityAssessmentStandards) GetById(id int64) QualityAssessmentStandards {
	var rsp QualityAssessmentStandards
	model.DB().Model(&QualityAssessmentStandards{}).Where("Id = ?", id).Find(&rsp)
	return rsp
}

type QualityAssessmentStandardsTree struct {
	ViolationCategory                              // itemType=规范类别
	SubItems          []ViolationCategoryTreeValue // itemType=扣分
}

type ViolationCategoryTreeValue struct {
	ViolationCategory
	SubItems []ViolationCategoryTreeCategory // itemType=违规类型
}

type QAS struct {
	QualityAssessmentStandards
	Name string // 等于code, 前端渲染树结构
}

type ViolationCategoryTreeCategory struct {
	ViolationCategory
	SubItems []QAS
}

func (q *QualityAssessmentStandards) Tree(cateId int64) ([]QualityAssessmentStandardsTree, error) {
	var rsp []QualityAssessmentStandardsTree
	//now := time.Now()
	//today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)

	rows1, err := model.DB().Model(&ViolationCategory{}).Where("QualityAssessmentCateId = ?", cateId).Where("ItemType = ? AND IsDeleted = ?", CLASS_1, NO_0).Select(
		"Id", "ItemType", "ParentId", "ParentIdPath", "Name", "DeductScore").Rows()
	if err != nil {
		return nil, err
	}
	defer rows1.Close()

	for rows1.Next() {
		var vcClass QualityAssessmentStandardsTree
		err := rows1.Scan(&vcClass.Id, &vcClass.ItemType, &vcClass.ParentId, &vcClass.ParentIdPath, &vcClass.Name, &vcClass.DeductScore)
		if err != nil {
			return nil, err
		}

		err = func() error {

			rows2, err := model.DB().Model(&ViolationCategory{}).Where("ParentId = ? AND IsDeleted = ?", vcClass.Id, NO_0).Select(
				"Id", "ItemType", "ParentId", "ParentIdPath", "Name", "DeductScore").Rows()
			if err != nil {
				return err
			}
			defer rows2.Close()

			for rows2.Next() {
				var vcValue ViolationCategoryTreeValue

				err := rows2.Scan(&vcValue.Id, &vcValue.ItemType, &vcValue.ParentId, &vcValue.ParentIdPath, &vcValue.Name, &vcValue.DeductScore)
				if err != nil {
					return err
				}

				err = func() error {
					rows3, err := model.DB().Model(&ViolationCategory{}).Where("ParentId = ? AND IsDeleted = ?", vcValue.Id, NO_0).Select(
						"Id", "GroupId", "CompanyId", "BranchId", "DepartmentId", "FleetId", "ItemType", "ParentId", "ParentIdPath", "Name", "DeductScore").Rows()
					if err != nil {
						return err
					}
					defer rows3.Close()

					for rows3.Next() {
						var vcCategory ViolationCategoryTreeCategory
						err := rows3.Scan(&vcCategory.Id, &vcCategory.ItemType, &vcCategory.ParentId, &vcCategory.ParentIdPath, &vcCategory.Name, &vcCategory.DeductScore)
						if err != nil {
							return err
						}
						vcValue.SubItems = append(vcValue.SubItems, vcCategory)
					}
					return nil
				}()
				if err != nil {
					return err
				}

				vcClass.SubItems = append(vcClass.SubItems, vcValue)
				//return nil
			}
			return nil
		}()
		if err != nil {
			return nil, err
		}

		rsp = append(rsp, vcClass)

	}

	return rsp, nil
}

func (q *QualityAssessmentStandards) TreeTx(tx *gorm.DB, cateId int64) ([]QualityAssessmentStandardsTree, error) {
	var vcs []ViolationCategory

	err := tx.Model(&ViolationCategory{}).Where("QualityAssessmentCateId = ?", cateId).Where("IsDeleted = ?", NO_0).Find(&vcs).Error
	if err != nil {
		return nil, err
	}

	var (
		classVcs    []QualityAssessmentStandardsTree
		valueVcs    []ViolationCategoryTreeValue
		categoryVcs []ViolationCategoryTreeCategory
	)

	for _, vc := range vcs {
		if vc.ItemType == CLASS_1 {

			i := QualityAssessmentStandardsTree{
				ViolationCategory: vc,
				SubItems:          nil,
			}

			classVcs = append(classVcs, i)
		} else if vc.ItemType == VALUE_2 {
			i := ViolationCategoryTreeValue{
				ViolationCategory: vc,
				SubItems:          nil,
			}

			valueVcs = append(valueVcs, i)
		} else if vc.ItemType == CATEGORY_3 {

			i := ViolationCategoryTreeCategory{
				ViolationCategory: vc,
				SubItems:          nil,
			}

			categoryVcs = append(categoryVcs, i)
		}
	}

	for i, valueVc := range valueVcs {
		for _, categoryVc := range categoryVcs {
			if valueVc.Id == categoryVc.ParentId {
				valueVcs[i].SubItems = append(valueVcs[i].SubItems, categoryVc)
			}
		}
	}
	for i, classVc := range classVcs {
		for _, valueVc := range valueVcs {
			if classVc.Id == valueVc.ParentId {
				classVcs[i].SubItems = append(classVcs[i].SubItems, valueVc)
			}
		}
	}

	for i, classVc := range classVcs {
		for j, valueVc := range classVc.SubItems {
			for k, categoryVc := range valueVc.SubItems {

				var qasList []QualityAssessmentStandards

				err = tx.Model(&QualityAssessmentStandards{}).Where("ClassId = ? AND ValueId = ? AND CategoryId = ? AND Status = ?", classVc.Id, valueVc.Id, categoryVc.Id, util.StatusForTrue).Find(&qasList).Error
				if err != nil {
					if errors.Is(err, gorm.ErrRecordNotFound) {
						err = nil
					} else {
						return nil, err
					}
				}

				var QASList []QAS
				for _, standards := range qasList {
					QASList = append(QASList, QAS{
						QualityAssessmentStandards: standards,
						Name:                       standards.Code,
					})
				}

				classVcs[i].SubItems[j].SubItems[k].SubItems = QASList

			}
		}
	}

	return classVcs, nil
}

// 根据执行标准号查询
func (q *QualityAssessmentStandards) GetByCode(companyId int64, code string) (QualityAssessmentStandards, error) {
	var rsp QualityAssessmentStandards
	err := model.DB().Model(&QualityAssessmentStandards{}).Where("CompanyId = ? AND Code = ?", companyId, code).Scan(&rsp).Error
	return rsp, err
}

// 根据违规类别查询质量考核标准
func (q *QualityAssessmentStandards) GetCountByViolationCategoriesId(id int64) (int64, error) {
	var rsp int64

	err := model.DB().Model(&QualityAssessmentStandards{}).Where("ClassId = ? OR ValueId = ? OR CategoryId = ?", id, id, id).Count(&rsp).Error

	return rsp, err
}

func (q *QualityAssessmentStandards) GetCountByQualityAssessmentCateId(cateId int64) int64 {
	var count int64
	model.DB().Model(&QualityAssessmentStandards{}).Where("QualityAssessmentCateId = ?", cateId).Count(&count)

	return count
}
