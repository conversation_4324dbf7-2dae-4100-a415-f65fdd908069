package safety

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
)

type ViolationCategoryType int64

const (
	CLASS_1    ViolationCategoryType = 1 // 规范类别    1类 2类 ...
	VALUE_2    ViolationCategoryType = 2 // 扣分   0.1 0.5 ...
	CATEGORY_3 ViolationCategoryType = 3 // 违规类型
)

type ViolationCategoryIsDeleteType int64

const (
	NO_0  ViolationCategoryIsDeleteType = 0 // 未删除
	YES_1 ViolationCategoryIsDeleteType = 1 // 已删除
)

type ViolationCategory struct {
	model.PkId
	QualityAssessmentCateId int64                                `json:"QualityAssessmentCateId" gorm:"column:qualityassessmentcateid;type:bigint;default:0;comment:质量考核规范分类ID 关联quality_assessment_cates表ID" validate:"required"`
	ParentId                int64                                `json:"ParentId"        gorm:"column:parentid;comment:CLASS_1 规范类别 ParentId=0;type:bigint"`   // CLASS_1 规范类别 ParentId=0
	Name                    string                               `json:"Name" gorm:"column:name;comment:门检类别名  门检项目名  项目下的门检标签名;type:varchar(50)"`             // 门检类别名  门检项目名  项目下的门检标签名
	DeductScore             int64                                `json:"DeductScore" gorm:"column:deductscore;comment: 扣分分值  1/100分 即 1分 记作 100;type:integer"` // 扣分分值  1/100分 即 1分 记作 100
	DeductMoney             int64                                `json:"DeductMoney" gorm:"column:deductmoney;comment: 扣钱金额 单位：分;type:integer"`
	IsDeleted               ViolationCategoryIsDeleteType        `json:"-" gorm:"column:isdeleted;comment:是否已删除 NO_0否 YES_1是 软删除后不允许恢复;type:smallint"` // 是否已删除 NO_0否 YES_1是 软删除后不允许恢复
	DevoteStatus            int64                                `json:"DevoteStatus" gorm:"column:devotestatus;type:smallint;default:2;comment:是否纳入贡献考核 1是 2否"`
	LevelType               int64                                `json:"LevelType" gorm:"column:leveltype;type:smallint;default:0;comment:等级类型 1=>1类 2=>2类 .... "`
	PunishSetting           []ViolationCategoryPunishSettingItem `json:"PunishSetting" gorm:"-"`
	model.Timestamp
	SubItems []ViolationCategory `json:"SubItems" gorm:"-"`
}

func (v *ViolationCategory) BeforeCreate(db *gorm.DB) error {
	v.Id = model.Id()
	return nil
}
func (v *ViolationCategory) FirstById(id int64) ViolationCategory {
	var category ViolationCategory
	model.DB().Model(&ViolationCategory{}).Where("Id = ?", id).First(&category)
	return category
}
func (v *ViolationCategory) FirstByName(qualityAssessmentCateId int64, name string, pid int64) ViolationCategory {
	var category ViolationCategory
	model.DB().Model(&ViolationCategory{}).Where("QualityAssessmentCateId = ? AND Name = ? AND ParentId = ?", qualityAssessmentCateId, name, pid).First(&category)
	return category
}

func (v *ViolationCategory) Add() error {
	return model.DB().Create(&v).Error
}

func (v *ViolationCategory) GetByParentId() (ViolationCategory, error) {
	var rsp ViolationCategory
	err := model.DB().Model(&ViolationCategory{}).Where("Id = ?", v.ParentId).Scan(&rsp).Error
	return rsp, err
}
func (v *ViolationCategory) GetChildren() []ViolationCategory {
	var items []ViolationCategory
	model.DB().Model(&ViolationCategory{}).Where("ParentId = ? AND IsDeleted != ?", v.Id, util.StatusForTrue).Find(&items)
	return items
}

func (v *ViolationCategory) List(assessmentCateId int64) ([]ViolationCategory, error) {
	var categories []ViolationCategory
	model.DB().Model(&ViolationCategory{}).Where("QualityAssessmentCateId = ? AND ParentId = ?", assessmentCateId, 0).Where("IsDeleted = ?", NO_0).Find(&categories)
	for i := range categories {
		var cates1 []ViolationCategory
		model.DB().Model(&ViolationCategory{}).Where("ParentId = ?", categories[i].Id).Where("IsDeleted = ?", NO_0).Find(&cates1)
		categories[i].SubItems = cates1

		for j := range categories[i].SubItems {
			var cates2 []ViolationCategory
			model.DB().Model(&ViolationCategory{}).Where("ParentId = ?", categories[i].SubItems[j].Id).Where("IsDeleted = ?", NO_0).Find(&cates2)
			categories[i].SubItems[j].SubItems = cates2
		}
	}

	return categories, nil
}
func (v *ViolationCategory) ListTop(assessmentCateId int64) []ViolationCategory {
	var categories []ViolationCategory
	model.DB().Model(&ViolationCategory{}).Where("QualityAssessmentCateId = ? AND ParentId = ?", assessmentCateId, 0).Where("IsDeleted = ?", NO_0).Find(&categories)
	return categories
}

func (v *ViolationCategory) Edit() error {
	return model.DB().Model(&ViolationCategory{}).Select("Name").Where("Id = ?", v.Id).Updates(&v).Error
}
func (v *ViolationCategory) UpdateDevoteStatus() error {
	return model.DB().Select("DevoteStatus").Updates(&v).Error
}

func (v *ViolationCategory) Delete() error {
	return model.DB().Model(&ViolationCategory{}).Where("Id = ?", v.Id).Update("IsDeleted", YES_1).Error
}
func (v *ViolationCategory) ParseCategoryNames() []string {
	var names []string
	if v.Id > 0 {
		if v.ParentId > 0 {
			category2 := v.FirstById(v.ParentId)
			if category2.Id > 0 {
				if category2.ParentId > 0 {
					category3 := v.FirstById(category2.ParentId)
					if category3.Id > 0 {
						names = append(names, category3.Name)
					}
				}
				names = append(names, category2.Name)
			}
		}
		names = append(names, v.Name)
	}

	return names
}

type ViolationCategoryPunishSetting struct {
	model.PkId
	ViolationCategoryId int64      `json:"ViolationCategoryId" gorm:"column:violationcategoryid;type:bigint;default:0;comment:分类ID" validate:"required"`
	SettingItem         model.JSON `json:"SettingItem" gorm:"column:settingitem;type:json;comment:配置项"`
	model.Timestamp
}
type ViolationCategoryPunishSettingItem struct {
	DriverAttr  int64 `json:"DriverAttr"`  //司机属性 1常规线  2村村通
	Symbol      int64 `json:"Symbol"`      //判断 1等于 2大于
	YearCount   int64 `json:"YearCount"`   //年发生数量
	MonthCount  int64 `json:"MonthCount"`  //月发生数量
	DeductScore int64 `json:"DeductScore"` //扣分 1/100分 即 1分 记作 100
	CheckType   int64 `json:"CheckType"`   //考核方式 1固定金额  2基数比例
	Value       int64 `json:"Value"`       //考核值  固定金额时，存储的是金额，单位：分；基数比例时，存储的是百分比，8023=>80.23%
	WaitWorkDay int64 `json:"WaitWorkDay"` //待岗天数 单位：天
}

func (m *ViolationCategoryPunishSetting) BeforeCreate(db *gorm.DB) error {
	err := db.Where("ViolationCategoryId = ?", m.ViolationCategoryId).Delete(&ViolationCategoryPunishSetting{}).Error
	if err != nil {
		return err
	}

	m.Id = model.Id()
	return nil
}

func (m *ViolationCategoryPunishSetting) Create() error {
	if len(m.SettingItem) > 0 {
		return model.DB().Create(&m).Error
	}
	return nil
}
func (m *ViolationCategoryPunishSetting) FirstBy(categoryId int64) []ViolationCategoryPunishSettingItem {
	var items []ViolationCategoryPunishSettingItem
	var setting ViolationCategoryPunishSetting
	model.DB().Model(&ViolationCategoryPunishSetting{}).Where("ViolationCategoryId = ?", categoryId).First(&setting)

	if len(setting.SettingItem) > 0 {
		err := json.Unmarshal(setting.SettingItem, &items)
		if err != nil {
			log.ErrorFields("json.Unmarshal ViolationCategoryPunishSettingItem error", map[string]interface{}{"error": err})
		}
	}

	return items
}

type QualityAssessmentStandards struct {
	model.PkId
	QualityAssessmentCateId int64  `json:"QualityAssessmentCateId" gorm:"column:qualityassessmentcateid;type:bigint;default:0;comment:质量考核规范分类ID 关联quality_assessment_cates表ID" validate:"required"`
	Code                    string `json:"Code" gorm:"column:code;comment:执行标准号;varchar(100)" validate:"required"`       // 执行标准号
	Describe                string `json:"Describe" gorm:"column:describe;comment:执行标准描述;type:text" validate:"required"` // 执行标准描述
	//ClassId                 int64  `json:"ClassId" gorm:"column:classid;comment:规范类别id;type:bigint"`                                               // 冗余 规范类别id
	//ClassName               string `json:"ClassName" gorm:"column:classname;comment:规范类别名;type:varchar(50)"`                                       // 冗余 规范类别名
	//ValueId                 int64  `json:"ValueId" gorm:"column:valueid;comment:扣分id;type:bigint"`                                                 // 冗余 扣分id
	//ValueName               string `json:"ValueName" gorm:"column:valuename;comment:扣分名;type:varchar(50)"`                                         // 冗余 扣分名
	CategoryId   int64  `json:"CategoryId" gorm:"column:categoryid;comment:规范类别id;type:bigint" validate:"required"` // 规范类别id
	CategoryName string `json:"CategoryName" gorm:"column:categoryname;comment:规范类别名;type:varchar(50)"`             // 规范类别名
	DeductScore  int64  `json:"DeductScore" gorm:"column:deductscore;comment:扣分分值 1/100分 即 1分 记作 100;type:integer"` // 冗余 扣分分值  1/100分 即 1分 记作 100
	DeductMoney  int64  `json:"DeductMoney" gorm:"column:deductmoney;comment:扣钱金额 单位分;type:integer"`
	Status       int64  `json:"Status" gorm:"column:status;comment:状态 1启用 2禁用;type:smallint;default:1"`
	AttrType     int64  `json:"AttrType" gorm:"column:attrtype;type:smallint;comment:属性类型 1安全类 2非安全类 3交通违章 4其他"`
	model.Timestamp

	ViolationCount            int64    `json:"ViolationCount" gorm:"-"`
	IsDeduct                  int64    `json:"IsDeduct" gorm:"-"`      //是否扣分
	IsDeductMoney             int64    `json:"IsDeductMoney" gorm:"-"` //是否扣钱
	QualityAssessmentCateName string   `json:"QualityAssessmentCateName" gorm:"-"`
	QualityAssessmentCateAttr int64    `json:"QualityAssessmentCateAttr" gorm:"-"`
	FirstCateName             string   `json:"-" gorm:"-"`
	SecondCateName            string   `json:"-" gorm:"-"`
	ThreeCateName             string   `json:"-" gorm:"-"`
	CateAttr                  int64    `json:"CateAttr" gorm:"-"`
	CategoryNames             []string `json:"CategoryNames" gorm:"-"`
	ViolationId               int64    `json:"-"`
	ViolationOrigin           int64    `json:"-"`
	ViolationReportAt         int64    `json:"-"`
}

func (q *QualityAssessmentStandards) BeforeCreate(db *gorm.DB) error {
	q.Id = model.Id()
	q.Status = util.StatusForTrue
	return nil
}
func (q *QualityAssessmentStandards) Add() error {
	return model.DB().Create(&q).Error
}

func (q *QualityAssessmentStandards) AddBatch(items []QualityAssessmentStandards) error {
	return model.DB().Model(&QualityAssessmentStandards{}).Create(&items).Error
}

func (q *QualityAssessmentStandards) List(cateId, status int64, describe string, paginator model.Paginator) ([]QualityAssessmentStandards, int64, error) {
	var rsp []QualityAssessmentStandards
	var totalCount int64
	tx := model.DB().Model(&QualityAssessmentStandards{}).Where("QualityAssessmentCateId = ?", cateId)

	if status > 0 {
		tx.Where("Status = ?", status)
	}

	if describe != "" {
		tx = tx.Where("Describe LIKE ?", "%"+describe+"%")
	}

	err := tx.Count(&totalCount).Order("Code ASC").Order("Id desc").Limit(paginator.Limit).Offset(paginator.Offset).Scan(&rsp).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, 0, nil
		} else {
			return nil, 0, err
		}
	}
	return rsp, totalCount, nil
}

func (q *QualityAssessmentStandards) Edit() error {
	return model.DB().Model(&QualityAssessmentStandards{}).Where("Id = ?", q.Id).
		Select("Describe", "AttrType").Updates(&q).Error
}

func (q *QualityAssessmentStandards) Delete() error {
	return model.DB().Where("Id = ?", q.Id).Delete(&QualityAssessmentStandards{}).Error
}

func (q *QualityAssessmentStandards) EditStatus() error {
	return model.DB().Model(&QualityAssessmentStandards{}).Where("Id = ?", q.Id).Select("Status").Updates(&q).Error
}
func (q *QualityAssessmentStandards) GetById(id int64) QualityAssessmentStandards {
	var rsp QualityAssessmentStandards
	model.DB().Model(&QualityAssessmentStandards{}).Where("Id = ?", id).Find(&rsp)
	return rsp

}

type QualityAssessmentStandardsTree struct {
	ViolationCategory                              // itemType=规范类别
	SubItems          []ViolationCategoryTreeValue // itemType=扣分
}

type ViolationCategoryTreeValue struct {
	ViolationCategory
	SubItems []ViolationCategoryTreeCategory // itemType=违规类型
}

type QAS struct {
	QualityAssessmentStandards
	Name string // 等于code, 前端渲染树结构
}

type ViolationCategoryTreeCategory struct {
	ViolationCategory
	SubItems []QAS
}

//func (q *QualityAssessmentStandards) Tree(cateId int64) ([]QualityAssessmentStandardsTree, error) {
//	var rsp []QualityAssessmentStandardsTree
//	rows1, err := model.DB().Model(&ViolationCategory{}).Where("QualityAssessmentCateId = ? AND ParentId = ?", cateId, 0).Where("IsDeleted = ?", NO_0).Select(
//		"Id", "ItemType", "ParentId", "ParentIdPath", "Name", "DeductScore").Rows()
//	if err != nil {
//		return nil, err
//	}
//	defer rows1.Close()
//
//	for rows1.Next() {
//		var vcClass QualityAssessmentStandardsTree
//		err := rows1.Scan(&vcClass.Id, &vcClass.ItemType, &vcClass.ParentId, &vcClass.ParentIdPath, &vcClass.Name, &vcClass.DeductScore)
//		if err != nil {
//			return nil, err
//		}
//
//		err = func() error {
//
//			rows2, err := model.DB().Model(&ViolationCategory{}).Where("ParentId = ? AND IsDeleted = ?", vcClass.Id, NO_0).Select(
//				"Id", "ItemType", "ParentId", "ParentIdPath", "Name", "DeductScore").Rows()
//			if err != nil {
//				return err
//			}
//			defer rows2.Close()
//
//			for rows2.Next() {
//				var vcValue ViolationCategoryTreeValue
//
//				err := rows2.Scan(&vcValue.Id, &vcValue.ItemType, &vcValue.ParentId, &vcValue.ParentIdPath, &vcValue.Name, &vcValue.DeductScore)
//				if err != nil {
//					return err
//				}
//
//				err = func() error {
//					rows3, err := model.DB().Model(&ViolationCategory{}).Where("ParentId = ? AND IsDeleted = ?", vcValue.Id, NO_0).Select(
//						"Id", "GroupId", "CompanyId", "BranchId", "DepartmentId", "FleetId", "ItemType", "ParentId", "ParentIdPath", "Name", "DeductScore").Rows()
//					if err != nil {
//						return err
//					}
//					defer rows3.Close()
//
//					for rows3.Next() {
//						var vcCategory ViolationCategoryTreeCategory
//						err := rows3.Scan(&vcCategory.Id, &vcCategory.ItemType, &vcCategory.ParentId, &vcCategory.ParentIdPath, &vcCategory.Name, &vcCategory.DeductScore)
//						if err != nil {
//							return err
//						}
//						vcValue.SubItems = append(vcValue.SubItems, vcCategory)
//					}
//					return nil
//				}()
//				if err != nil {
//					return err
//				}
//
//				vcClass.SubItems = append(vcClass.SubItems, vcValue)
//				//return nil
//			}
//			return nil
//		}()
//		if err != nil {
//			return nil, err
//		}
//
//		rsp = append(rsp, vcClass)
//
//	}
//
//	return rsp, nil
//}

// 根据执行标准号查询
func (q *QualityAssessmentStandards) GetByCode(companyId int64, code string) (QualityAssessmentStandards, error) {
	var rsp QualityAssessmentStandards
	err := model.DB().Model(&QualityAssessmentStandards{}).Where("CompanyId = ? AND Code = ?", companyId, code).Scan(&rsp).Error
	return rsp, err
}

// 根据违规类别查询质量考核标准
func (q *QualityAssessmentStandards) GetCountByViolationCategoriesId(id int64) (int64, error) {
	var rsp int64

	err := model.DB().Model(&QualityAssessmentStandards{}).Where("ClassId = ? OR ValueId = ? OR CategoryId = ?", id, id, id).Count(&rsp).Error

	return rsp, err
}

func (q *QualityAssessmentStandards) GetCountByQualityAssessmentCateId(cateId int64) int64 {
	var count int64
	model.DB().Model(&QualityAssessmentStandards{}).Where("QualityAssessmentCateId = ?", cateId).Count(&count)

	return count
}
