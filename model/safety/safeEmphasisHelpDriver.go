package safety

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type SafeEmphasisHelpDriversTerminate int8

const (
	TERMINATE_NO_1  SafeEmphasisHelpDriversTerminate = 1 // 未结束
	TERMINATE_YES_2 SafeEmphasisHelpDriversTerminate = 2 // 已结束
)

type SafeEmphasisHelpDriverRecordsRemind int8

const (
	REMIND_YES_1 SafeEmphasisHelpDriverRecordsRemind = 1 // 开启
	REMIND_NO_2  SafeEmphasisHelpDriverRecordsRemind = 2 // 关闭
)

// 重点帮扶司机
type SafeEmphasisHelpDrivers struct {
	model.PkId

	model.Corporations              // 司机的机构信息
	ViolationRectificationId int64  `json:"ViolationRectificationId" gorm:"column:violationrectificationid;type:bigint;comment:违规整改ID"`
	StaffId                  int64  `json:"StaffId"        gorm:"column:staffid;comment:司机员工id;type:integer"`       // 司机员工id
	StaffName                string `json:"StaffName"      gorm:"column:staffname;comment:司机名;type:varchar(50)"`    // 司机名
	StaffIdStr               string `json:"StaffIdStr"      gorm:"column:staffidstr;comment:司机工号;type:varchar(50)"` // 司机工号
	CorporationId            int64  `json:"CorporationId" gorm:"column:corporationid;comment:司机所属机构id;type:bigint"` // 司机所属机构id
	Corporation              string `json:"Corporation" gorm:"column:corporation;comment:司机所属机构;type:varchar(50)"`  // 司机所属机构

	CauseContent string `json:"CauseContent" gorm:"column:causecontent;comment:列入原因;type:varchar(50)"` // 列入原因

	AddAt    time.Time `json:"AddAt" gorm:"column:addat;comment:列入时间;type:timestamptz"`               // 列入时间
	FinishAt time.Time `json:"FinishAt" gorm:"column:finishat;comment:帮扶截止时间 列入时设置;type:timestamptz"` // 帮扶截止时间 列入时设置

	Terminate   SafeEmphasisHelpDriversTerminate `json:"Terminate" gorm:"column:terminate;comment:ERMINATE_NO_1:未结束 TERMINATE_YES_2: 已结束（已结束称为历史记录）;type:smallint"` // TERMINATE_NO_1:未结束 TERMINATE_YES_2: 已结束（已结束称为历史记录）
	TerminateAt time.Time                        `json:"TerminateAt" gorm:"column:terminateat;comment:帮扶结束时间 用户操作结束;type:timestamptz"`                              // 帮扶结束时间 用户操作结束

	model.Timestamp
}

// 帮扶进展记录
type SafeEmphasisHelpDriverRecords struct {
	model.PkId

	model.GroupCorporation // 司机的机构信息
	model.CompanyCorporation
	model.BranchCorporation
	model.DepartmentCorporation
	model.FleetCorporation

	FkHelpId int64 `json:"FkHelpId" gorm:"column:fkhelpid;comment:重点帮扶id 一对一fk SafeEmphasisHelpDrivers.id;type:bigint"` // 重点帮扶id 一对一fk SafeEmphasisHelpDrivers.id

	Remind         SafeEmphasisHelpDriverRecordsRemind `json:"Remind" gorm:"column:remind;comment:REMIND_YES_1: 开启安全提醒 REMIND_NO_2：关闭安全提醒;type:smallint"` // REMIND_YES_1: 开启安全提醒 REMIND_NO_2：关闭安全提醒
	HonorSunCount  int64                               `json:"HonorSunCount" gorm:"column:honorsuncount;comment:汉纳森异常行为总数;type:integer"`                  // 汉纳森异常行为总数
	ViolationCount int64                               `json:"ViolationCount" gorm:"column:violationcount;comment:违规次数;type:integer"`                     // 违规次数
	OtherHelp      string                              `json:"OtherHelp" gorm:"column:otherhelp;comment:其他帮扶工作;type:text"`                                // 其他帮扶工作
	HelpAt         time.Time                           `json:"HelpAt" gorm:"column:helpat;comment:帮扶（录入）时间;type:timestamptz"`                             // 帮扶（录入）时间

	model.Timestamp
}

// 列入重点帮扶司机原因快捷配置
// 绑定GroupIp 【文档未说明】
type SafeEmphasisHelpDriverCauses struct {
	model.PkId

	model.GroupCorporation
	model.CompanyCorporation
	model.BranchCorporation
	model.DepartmentCorporation
	model.FleetCorporation

	Sort    int64  `json:"Sort" gorm:"column:Sort;comment:排序;type:smallint"`          // 排序
	Content string `json:"Content" gorm:"column:Content;comment:内容;type:varchar(50)"` // 内容

	model.Timestamp
}

func (s *SafeEmphasisHelpDriverCauses) Add(items []SafeEmphasisHelpDriverCauses) error {
	return model.DB().Create(&items).Error
}

func (s *SafeEmphasisHelpDriverCauses) List() ([]SafeEmphasisHelpDriverCauses, error) {
	var rsp []SafeEmphasisHelpDriverCauses
	tx := model.DB().Model(&SafeEmphasisHelpDriverCauses{}).Where("GroupId = ?", s.GroupId)

	err := tx.Scan(&rsp).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		} else {
			return nil, err
		}
	}
	return rsp, nil
}

func (s *SafeEmphasisHelpDriverCauses) Edit(groupId int64, items []SafeEmphasisHelpDriverCauses) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {

		// 删除所有快捷
		err := tx.Where("GroupId = ?", groupId).Delete(&SafeEmphasisHelpDriverCauses{}).Error
		if err != nil {
			return err
		}

		err = tx.Create(items).Error
		if err != nil {
			return err
		}

		return nil
	})
}

func (s *SafeEmphasisHelpDrivers) Add() error {
	return model.DB().Create(&s).Error
}

func (s *SafeEmphasisHelpDrivers) List(corpIds []int64, driver string, startAt, endAt time.Time, paginator model.Paginator) ([]SafeEmphasisHelpDrivers, int64, error) {
	var rsp []SafeEmphasisHelpDrivers
	var totalCount int64

	tx := model.DB().Model(&SafeEmphasisHelpDrivers{}).Where(
		"(GroupId IN ? OR CompanyId IN ? OR BranchId IN ? OR DepartmentId IN ? OR FleetId IN ? OR CorporationId IN ?) AND Terminate = ?",
		corpIds, corpIds, corpIds, corpIds, corpIds, corpIds, s.Terminate)

	if !startAt.IsZero() && !endAt.IsZero() {
		tx.Where("AddAt >= ? AND AddAt < ?", startAt, endAt)
	}

	if driver != "" {
		tx.Where("StaffName LIKE ? OR StaffIdStr LIKE ?", "%"+driver+"%", "%"+driver+"%")
	}

	err := tx.Count(&totalCount).Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&rsp).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, 0, nil
		} else {
			return nil, 0, err
		}
	}

	return rsp, totalCount, nil
}

func (s *SafeEmphasisHelpDrivers) EditTerm() error {

	return model.DB().Model(&SafeEmphasisHelpDrivers{}).Where("Id = ?", s.Id).Select("Terminate", "TerminateAt").Updates(s).Error
}

func (s *SafeEmphasisHelpDrivers) GetDetail() (SafeEmphasisHelpDrivers, error) {

	var rsp SafeEmphasisHelpDrivers

	err := model.DB().Model(&SafeEmphasisHelpDrivers{}).Where("Id = ?", s.Id).Scan(&rsp).Error

	return rsp, err
}

func (s *SafeEmphasisHelpDrivers) AddTx(tx *gorm.DB) error {
	return tx.Create(s).Error
}

func (s *SafeEmphasisHelpDriverRecords) Add() error {
	return model.DB().Create(&s).Error
}

func (s *SafeEmphasisHelpDriverRecords) ListByHelpId(helpId int64) ([]SafeEmphasisHelpDriverRecords, error) {
	var rsp []SafeEmphasisHelpDriverRecords
	err := model.DB().Model(&SafeEmphasisHelpDriverRecords{}).Where("FkHelpId = ?", helpId).Scan(&rsp).Error

	return rsp, err
}

func (s *SafeEmphasisHelpDriverRecords) Edit() error {
	return model.DB().Model(&SafeEmphasisHelpDriverRecords{}).Where("Id = ?", s.Id).Select("Remind", "HonorSunCount", "ViolationCount", "OtherHelp", "HelpAt").Updates(s).Error
}

// 加入重点帮扶次数
func (s SafeEmphasisHelpDrivers) AddCount(staffId int64, startAt, endAt time.Time) (int64, error) {
	var rsp int64
	err := model.DB().Model(&SafeEmphasisHelpDrivers{}).Where("StaffId = ? AND AddAt >= ? AND AddAt < ?", staffId, startAt, endAt).Count(&rsp).Error
	return rsp, err
}
