package safety

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"time"

	"gorm.io/gorm"
)

type VehicleAbnormalStopRecord struct {
	model.PkId
	TopCorporationId int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID"`
	CorporationId    int64           `json:"CorporationId" gorm:"column:corporationid;type:bigint;default:0;comment:车辆所属的机构Id"`
	CorporationName  string          `json:"CorporationName" gorm:"column:corporationname;type:varchar;default:;comment:车辆所属的机构"`
	VehicleId        int64           `json:"VehicleId" gorm:"column:vehicleid;type:bigint;default:0;uniqueIndex:vehicle_abnormal_stop_record_unique_index;comment:车辆ID"`
	License          string          `json:"License" gorm:"column:license;type:varchar;comment:车牌"`
	LineId           int64           `json:"LineId" gorm:"column:lineid;type:bigint;default:0;comment:线路ID"`
	LineName         string          `json:"LineName" gorm:"column:linename;type:varchar;comment:线路"`
	DriverId         int64           `json:"DriverId" gorm:"column:driverid;type:bigint;default:0;comment:司机的ID"`
	DriverName       string          `json:"DriverName" gorm:"column:drivername;type:varchar;comment:司机"`
	ReportAt         model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;uniqueIndex:vehicle_abnormal_stop_record_unique_index;comment:停车时间"`
	Reason           string          `json:"Reason" gorm:"column:reason;type:varchar;comment:停车原因"`
	Status           int64           `json:"Status" gorm:"column:status;type:smallint;default:1;comment:状态 1待处理 2已忽略 3已处理"`
	AccidentCode     string          `json:"AccidentCode" gorm:"column:accidentcode;type:varchar;comment:事故编号"`
}

func (m *VehicleAbnormalStopRecord) BeforeCreate(tx *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *VehicleAbnormalStopRecord) Create() error {
	return model.DB().Create(&m).Error
}

func (m *VehicleAbnormalStopRecord) Update() error {
	return model.DB().Select("Status", "AccidentCode").Updates(&m).Error
}

func (m *VehicleAbnormalStopRecord) FirstBy(id int64) VehicleAbnormalStopRecord {
	var record VehicleAbnormalStopRecord
	model.DB().Model(&VehicleAbnormalStopRecord{}).Where("id = ?", id).First(&record)
	return record
}

func (m *VehicleAbnormalStopRecord) GetBy(topCorporationId int64, corporationIds []int64, license, driverName, lineName string, startAt, endAt time.Time, paginator model.Paginator) ([]VehicleAbnormalStopRecord, int64) {
	tx := model.DB().Model(&VehicleAbnormalStopRecord{}).Where("TopCorporationId = ? AND corporationId 	IN ?", topCorporationId, corporationIds)
	if license != "" {
		tx.Where("License LIKE ?", "%"+license+"%")
	}
	if driverName != "" {
		tx.Where("DriverName LIKE ?", "%"+driverName+"%")
	}
	if lineName != "" {
		tx.Where("LineName LIKE ?", "%"+lineName+"%")
	}
	if !startAt.IsZero() {
		tx.Where("ReportAt >= ?", startAt.Format(model.TimeFormat))
	}
	if !endAt.IsZero() {
		tx.Where("ReportAt <= ?", endAt.Format(model.TimeFormat))
	}

	var count int64
	tx.Count(&count)

	var records []VehicleAbnormalStopRecord

	tx.Order("ReportAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&records)
	return records, count

}

func (m *VehicleAbnormalStopRecord) GetByVehicleId(vehicleId int64) []VehicleAbnormalStopRecord {
	var records []VehicleAbnormalStopRecord
	model.DB().Model(&VehicleAbnormalStopRecord{}).Where("VehicleId = ? AND Status = ?", vehicleId, util.VehicleAbnormalStopHandleStatusForNotHandle).Order("ReportAt DESC").Find(&records)
	return records
}
