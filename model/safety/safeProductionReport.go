package safety

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
)

var summaryField1 = []string{
	"FocusDriver",                                                                                                  // 驾驶员分类管理
	"SafetyRemindersTotal", "ReturningToWork", "NewCarModelDrivers", "DriversWithMoreViolations", "NewLineDrivers", // 安全提醒
	"InterviewTotal", "AccidentInterview", "ViolationInterview", "SmokingInterview", "NoImprovementDefense", // 司机约谈
	"InspectionTotal", "BloodPressure", "AlcoholPressure", "PoorMentalState",
	"MonitorCheck", "DiscoverOrReport", "Correction", // 视频监控
	"FirstAndLastDelay", "LastClassReturnsEarly", // 首末班情况
	"SafetyAndFireTotal", "IdentifyHiddenDangers", "RectificationFrequency", "MinorHiddenDanger", "GeneralHazards", "MajorHiddenDangers", "OverMajorHiddenDangers", // 安全消防检查
	"AccidentReviewTotal", "SiteAccidents", "IrresponsibleAccident", "ResponsibleAccident", "FatigueDriving", "IntersectionAccident", "NearBusStop", "NearZebraCrossing", // 事故审查
	"ConveneBatches", "ShouldParticipantsNumber", "CollectiveLearningDriver", "RemedialTeachingDriver", "MissSchoolDriver", // 安全活动
}
var summaryField2 = [][]string{
	[]string{"SafetyActivitiesList", "典型事故分析", "TypicalAccidentAnalysis"},
	[]string{"SafetyActivitiesList", "安全操作、应急处置", "SafeOperationOrEmergencyResponse"},
	[]string{"SafetyActivitiesList", "违规行为分析", "AnalysisOfViolations"},
	[]string{"SafetyActivitiesList", "规章制度宣贯", "PromotionAndImplementationOfRulesAndRegulations"},
	[]string{"SafetyActivitiesList", "季节性行车要点", "SeasonalDrivingTips"},
	[]string{"SafetyActivitiesList", "安全注意事项统计", "SafetyPrecautions"},
}

type SafeProductionReport struct {
	model.PkId
	model.Corporations //集团、公司、分公司、部门、车队
	model.OpUser
	model.Timestamp
	DriverClassificationManagement                 // 驾驶员分类管理
	SafetyReminder                                 // 安全提醒
	DriverInterview                                // 司机约谈
	PreJobInspection                               // 岗前检查
	VideoMonitoring                                // 视频监控
	FirstAndLastShiftSituation                     // 首末班情况
	SafetyAndFireInspection                        // 安全消防检查
	AccidentReview                                 // 事故审查
	SafetyActivities                               // 安全活动
	ReportMonth                    model.LocalTime `json:"ReportMonth"  gorm:"column:reportmonth;type:timestamp;comment:上报月份;default:" `                                                    // 上报月份
	SupplementaryInformation       string          `json:"SupplementaryInformation"  gorm:"column:supplementaryinformation;type:varchar(255);comment:其他安全生产、平安维稳、特殊敏感事件、险情、不良趋势;default:" ` // 其他安全生产、平安维稳、特殊敏感事件、险情、不良趋势
	ApplyStatus                    int64           `json:"ApplyStatus" gorm:"column:applystatus;default:0;comment:审批状态 0草稿 1进行中 2已完成 3驳回 4撤回 5废弃;type:smallint"`                            // 工单状态 审批状态 0草稿 1进行中 2已完成 3驳回 4撤回 5废弃
	// other
	CorporationName                  string `json:"CorporationName" gorm:"-"`
	CorporationId                    int64  `json:"CorporationId" gorm:"-"`
	TypicalAccidentAnalysis          int64  `json:"TypicalAccidentAnalysis" gorm:"-"`          // 典型事故分析统计
	SafeOperationOrEmergencyResponse int64  `json:"SafeOperationOrEmergencyResponse" gorm:"-"` // 安全操作、应急处置统计
	AnalysisOfViolations             int64  `json:"AnalysisOfViolations" gorm:"-"`             // 违规行为分析统计
	RulesAndRegulations              int64  `json:"RulesAndRegulations" gorm:"-"`              // 规章制度宣贯统计
	SeasonalDrivingTips              int64  `json:"SeasonalDrivingTips" gorm:"-"`              // 季节性行车要点统计
	SafetyPrecautions                int64  `json:"SafetyPrecautions" gorm:"-"`                // 安全注意事项统计
}

// DriverClassificationManagement 驾驶员分类管理
type DriverClassificationManagement struct {
	FocusDriver int64 `json:"FocusDriver"  gorm:"column:focusdriver;type:integer;comment:重点关注档次类驾驶员;default:0"` // 重点关注档次类驾驶员
}

// SafetyReminder 安全提醒
type SafetyReminder struct {
	SafetyRemindersTotal      int64 `json:"SafetyRemindersTotal" gorm:"column:safetyreminderstotal;type:integer;comment:总安全提醒次数;default:0" `          //总安全提醒次数
	ReturningToWorkDrivers    int64 `json:"ReturningToWork" gorm:"column:returningtowork;type:integer;comment:复岗驾驶员;default:0" `                      //复岗驾驶员
	NewCarModelDrivers        int64 `json:"NewCarModelDrivers" gorm:"column:newcarmodeldrivers;type:integer;comment:新车型驾驶员;default:0" `               //新车型驾驶员
	NewLineDrivers            int64 `json:"NewLineDrivers" gorm:"column:newlinedrivers;type:integer;comment:新线路驾驶员;default:0" `                       //新线路驾驶员
	DriversWithMoreViolations int64 `json:"DriversWithMoreViolations" gorm:"column:driverswithmoreviolations;type:integer;comment:违规较多司机;default:0" ` //违规较多司机
}

// DriverInterview 司机约谈
type DriverInterview struct {
	InterviewTotal       int64 `json:"InterviewTotal" gorm:"column:interviewtotal;type:integer;comment:司机约谈总人数;default:0" `             // 司机约谈总人数
	AccidentInterview    int64 `json:"AccidentInterview" gorm:"column:accidentinterview;type:integer;comment:事故约谈;default:0" `          // 事故约谈
	ViolationInterview   int64 `json:"ViolationInterview" gorm:"column:violationinterview;type:integer;comment:违章约谈;default:0" `        // 违章约谈
	SpeedInterview       int64 `json:"SpeedInterview" gorm:"column:speedinterview;type:integer;comment:超速约谈;default:0" `                // 超速约谈
	SmokingInterview     int64 `json:"SmokingInterview" gorm:"column:smokinginterview;type:integer;comment:吸烟约谈;default:0" `            // 吸烟约谈
	NoImprovementDefense int64 `json:"NoImprovementDefense" gorm:"column:noimprovementdefense;type:integer;comment:主动防御未改善;default:0" ` // 主动防御未改善
}

// PreJobInspection 岗前检查
type PreJobInspection struct {
	InspectionTotal       int64  `json:"InspectionTotal" gorm:"column:inspectiontotal;type:integer;comment:检查总人数;default:0" `                     // 检查总人数
	BloodPressure         int64  `json:"BloodPressure" gorm:"column:bloodpressure;type:integer;comment:血压异常;default:0" `                          // 血压异常
	BloodPressureHandle   string `json:"BloodPressureHandle" gorm:"column:bloodpressurehandle;type:varchar(255);comment:血压异常后续处理;default:0" `     // 血压异常后续处理
	AlcoholPressure       int64  `json:"AlcoholPressure" gorm:"column:alcoholpressure;type:integer;comment:酒精异常;default:0" `                      // 酒精异常
	AlcoholPressureHandle string `json:"AlcoholPressureHandle" gorm:"column:alcoholpressurehandle;type:varchar(255);comment:酒精异常后续处理;default:0" ` //酒精异常后续处理
	PoorMentalState       int64  `json:"PoorMentalState" gorm:"column:poormentalstate;type:integer;comment:精神欠佳;default:0" `                      // 精神欠佳
	PoorMentalStateHandle string `json:"PoorMentalStateHandle" gorm:"column:PoorMentalStateHandle;type:varchar(255);comment:酒精异常后续处理;default:0" ` //精神欠佳后续处理
}

// VideoMonitoring 视频监控
type VideoMonitoring struct {
	MonitorCheck     int64 `json:"MonitorCheck" gorm:"column:monitorcheck;type:integer;comment:监控抽查;default:0" `          // 监控抽查
	DiscoverOrReport int64 `json:"DiscoverOrReport" gorm:"column:discoverorreport;type:integer;comment:发现或上报;default:0" ` // 发现或上报
	Correction       int64 `json:"Correction" gorm:"column:correction;type:integer;comment:整改;default:0" `                // 整改
}

// FirstAndLastShiftSituation 首末班情况
type FirstAndLastShiftSituation struct {
	FirstAndLastDelay     int64 `json:"FirstAndLastDelay" gorm:"column:firstandlastdelay;type:integer;comment:首末班延迟;default:0" `          // 首末班延迟
	LastClassReturnsEarly int64 `json:"LastClassReturnsEarly" gorm:"column:lastclassreturnsearly;type:integer;comment:末班提前回场;default:0" ` // 末班提前回场
}

// SafetyAndFireInspection 安全消防检查
type SafetyAndFireInspection struct {
	SafetyAndFireTotal     int64   `json:"SafetyAndFireTotal" gorm:"column:safetyandfiretotal;type:integer;comment:检查总数;default:0" `         // 检查总数
	FireDeviceCheckRate    float64 `json:"FireDeviceCheckRate" gorm:"column:firedevicecheckrate;type:numeric;comment:消防设施巡检率;default:0" `    // 消防设施巡检率
	IdentifyHiddenDangers  int64   `json:"IdentifyHiddenDangers" gorm:"column:identifyhiddendangers;type:integer;comment:发现隐患;default:0" `   // 发现隐患
	RectificationFrequency int64   `json:"RectificationFrequency" gorm:"column:rectificationfrequency;type:integer;comment:整改次数;default:0" ` // 整改次数
	MinorHiddenDanger      int64   `json:"MinorHiddenDanger" gorm:"column:minorhiddendanger;type:integer;comment:轻微隐患;default:0" `           // 轻微隐患
	GeneralHazards         int64   `json:"GeneralHazards" gorm:"column:generalhazards;type:integer;comment:一般隐患;default:0" `                 // 一般隐患
	MajorHiddenDangers     int64   `json:"MajorHiddenDangers" gorm:"column:majorhiddendangers;type:integer;comment:较大隐患;default:0" `         // 较大隐患
	OverMajorHiddenDangers int64   `json:"OverMajorHiddenDangers" gorm:"column:overmajorhiddendangers;type:integer;comment:重大隐患;default:0" ` // 重大隐患

}

// AccidentReview 事故审查
type AccidentReview struct {
	AccidentReviewTotal   int64 `json:"AccidentReviewTotal" gorm:"column:accidentreviewtotal;type:integer;comment:审查总数;default:0" `     // 审查总数
	SiteAccidents         int64 `json:"SiteAccidents" gorm:"column:siteaccidents;type:integer;comment:场站内事故;default:0" `                // 场站内事故
	IrresponsibleAccident int64 `json:"IrresponsibleAccident" gorm:"column:irresponsibleaccident;type:integer;comment:无责事故;default:0" ` // 无责事故
	ResponsibleAccident   int64 `json:"ResponsibleAccident" gorm:"column:responsibleaccident;type:integer;comment:有责事故;default:0" `     // 有责事故
	FatigueDriving        int64 `json:"FatigueDriving" gorm:"column:fatiguedriving;type:integer;comment:疲劳驾驶;default:0" `               // 疲劳驾驶
	IntersectionAccident  int64 `json:"IntersectionAccident" gorm:"column:intersectionaccident;type:integer;comment:路口事故;default:0" `   // 路口事故
	NearBusStop           int64 `json:"NearBusStop" gorm:"column:nearbusstop;type:integer;comment:公交车站附近;default:0" `                   // 公交车站附近
	NearZebraCrossing     int64 `json:"NearZebraCrossing" gorm:"column:nearzebracrossing;type:integer;comment:斑马线附近;default:0" `        // 斑马线附近
}

// SafetyActivities 安全活动
type SafetyActivities struct {
	SafetyActivitiesList     string `json:"SafetyActivitiesList"  gorm:"column:safetyactivitieslist;type:varchar(255);comment:安全活动;default:" validate:"required" ` // 安全活动
	Other                    string `json:"Other"  gorm:"column:other;type:text;comment:其他;default:"`                                                              // 其他
	AttendeeList             string `json:"AttendeeList"  gorm:"column:attendeelist;type:varchar(255);comment:出席人员;default:" validate:"required" `                 // 出席人员
	ConveneBatches           int64  `json:"ConveneBatches"  gorm:"column:convenebatches;type:integer;comment:召开批次;default:0" `                                     // 召开批次
	ShouldParticipantsNumber int64  `json:"ShouldParticipantsNumber"  gorm:"column:shouldparticipantsnumber;type:integer;comment:应参与人数;default:0" `                // 应参与人数
	CollectiveLearningDriver int64  `json:"CollectiveLearningDriver"  gorm:"column:collectivelearningdriver;type:integer;comment:集体学习驾驶员;default:0" `              // 集体学习驾驶员
	RemedialTeachingDriver   int64  `json:"RemedialTeachingDriver"  gorm:"column:remedialteachingdriver;type:integer;comment:补课学习驾驶员;default:0" `                  // 补课学习驾驶员
	MissSchoolDriver         int64  `json:"MissSchoolDriver"  gorm:"column:missschooldriver;type:integer;comment:缺课驾驶员;default:0" `                                // 缺课驾驶员
	MissSchoolReason         string `json:"MissSchoolReason"  gorm:"column:missschoolreason;type:varchar(255);comment:缺课原因;default:" `                             // 缺课原因
}

func (m *SafeProductionReport) TableName() string {
	return "safe_production_reports"
}

func (m *SafeProductionReport) ApplyStatusFieldName() string {
	return "applystatus"
}

func (m *SafeProductionReport) BeforeCreate(tx *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *SafeProductionReport) Create() error {
	return model.DB().Create(&m).Error
}

func (m *SafeProductionReport) Update() error {
	return model.DB().Updates(&m).Error
}

func (m *SafeProductionReport) TxCreate(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *SafeProductionReport) TxUpdate(tx *gorm.DB) error {
	return tx.Updates(&m).Error
}

func (m *SafeProductionReport) FirstBy(Id int64) (data SafeProductionReport) {
	model.DB().Model(&SafeProductionReport{}).First(&data, Id)
	return
}

func (m *SafeProductionReport) Delete() error {
	return model.DB().Delete(&m).Error
}

func (m *SafeProductionReport) UpdateState(Id, Status int64) error {
	return model.DB().Model(&SafeProductionReport{}).Where("Id = ?", Id).Update("ApplyStatus", Status).Error
}

func (m *SafeProductionReport) List(CorporationIds []int64, Month *model.LocalTime, Paginator model.Paginator) (Data []SafeProductionReport, TotalCount int64, err error) {
	db := model.DB().Model(&SafeProductionReport{}).Scopes(model.WhereCorporations(CorporationIds))
	if Month != nil {
		db = db.Where("ReportMonth = ?", Month)
	}
	db.Count(&TotalCount)
	if Paginator.Limit > 0 {
		db = db.Offset(Paginator.Offset).Limit(Paginator.Limit)
	}
	err = db.Find(&Data).Error
	return
}

func (m *SafeProductionReport) Summary(CorporationIds []int64, Month *model.LocalTime) (data SafeProductionReport, err error) {
	err = model.DB().Model(&SafeProductionReport{}).
		Select(summarySelect()).
		Scopes(model.WhereCorporations(CorporationIds)).
		Where("ReportMonth = ?", Month).Where("ApplyStatus=?", util.Safe_Production_Report_Complet).
		Find(&data).Error
	return
}

func summarySelect() (Columns []string) {
	for _, field := range summaryField1 {
		Columns = append(Columns, fmt.Sprintf("COALESCE(SUM(%s), 0) as %s", field, field))
	}
	for _, fieldArr := range summaryField2 {
		Columns = append(Columns, fmt.Sprintf(" COALESCE(SUM(CASE WHEN %s LIKE '%%%s%%' THEN 1 ELSE 0 END), 0) AS %s", fieldArr[0], fieldArr[1], fieldArr[2]))
	}
	return
}

func (m *SafeProductionReport) IsAddable() bool {
	return m.ApplyStatus == util.Safe_Production_Report_Draft || m.ApplyStatus == util.Safe_Production_Report_Approval
}

func (m *SafeProductionReport) IsModifiable() bool {
	return m.ApplyStatus != util.Safe_Production_Report_Approval && m.ApplyStatus != util.Safe_Production_Report_Scrap && m.ApplyStatus != util.Safe_Production_Report_Complet
}

func (m *SafeProductionReport) IsDeletable() bool {
	return m.ApplyStatus != util.Safe_Production_Report_Approval && m.ApplyStatus != util.Safe_Production_Report_Complet
}

// IsExist 判断这个月此车队是否存在报表
func (m *SafeProductionReport) IsExist() bool {
	var data SafeProductionReport
	model.DB().Model(&SafeProductionReport{}).Where("FleetId=?", m.FleetId).Where("ReportMonth = ?", m.ReportMonth).Where("ApplyStatus != ?", util.Safe_Production_Report_Scrap).Find(&data)
	if data.Id == 0 {
		return false
	} else {
		return true
	}
}
