package safety

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

// TrafficAccidentLogger 操作日志记录
type TrafficAccidentLogger struct {
	model.PkId
	TrafficAccidentId int64      `json:"TrafficAccidentId" gorm:"column:trafficaccidentid;type:bigint;comment:关联事故表主键ID"` //关联事故表主键ID
	Scene             int64      `json:"Scene" gorm:"column:scene;type:smallint;comment:场景值"`                             //场景值  1新增  2更新
	BeforeData        model.JSON `json:"BeforeData" gorm:"column:beforedata;type:json;comment:更改前的数据"`                    //更改前的数据
	AfterData         model.JSON `json:"AfterData" gorm:"column:afterdata;type:json;comment:更改后的数据"`                      //更改后的数据
	Snapshot          model.JSON `json:"Snapshot" gorm:"column:snapshot;type:json;comment:更改后所有数据快照;"`                    // 更改后所有数据快照
	ProcessId         string     `json:"ProcessId" gorm:"column:processid;type:varchar(200);comment:关联的流程ID;"`            //关联的流程ID
	Ip                string     `json:"Ip" gorm:"column:ip;type:varchar(64);comment:Ip地址"`                               //Ip地址
	Modular           string     `json:"Modular" gorm:"column:modular;type:varchar(64);comment:来源模块"`                     //来源模块
	model.OpUser
	model.Timestamp
}

var (
	TrafficLoggerSceneCreate               int64 = 1 //事故提交
	TrafficLoggerSceneUpdate               int64 = 2 //事故变更
	TrafficLoggerSceneClosed               int64 = 3 //事故结案
	TrafficLoggerSceneRelater              int64 = 3 //事故当事人新增、更新
	TrafficLoggerModularForAccidentCreate        = "事故提交"
	TrafficLoggerModularForAccidentEdit          = "事故变更"
	TrafficLoggerModularForAccidentConfirm       = "事故责任认定"
	TrafficLoggerModularForCloseApply            = "事故结案"
	TrafficLoggerModularForAccidentRelater       = "事故当事人变更"
	TrafficAccidentLoggerExceptField             = []string{"GroupId", "Group", "CompanyId", "Company", "BranchId", "Branch", "DepartmentId", "Department", "FleetId", "Fleet",
		"IsClosed", "ApplyStatus", "Relaters", "PersonalPayRatio", "LossMoney", "ClosedAt", "CreatedAt", "UpdatedAt", "FileHttpPrefix", "CorporationName", "CorporationId",
		"IsProcessHandler", "IsEnableClosed", "OpUserId", "OpUserName", "OpIp"}
	TrafficAccidentRelaterLoggerExceptField = []string{"TrafficAccidentId", "IsClosed", "SolutionType", "SolutionFilePath", "InsuranceCompanyPayMoney",
		"InsurancePayMoney", "LossMoney", "SolutionDesc", "ClosedAt", "CreatedAt", "UpdatedAt", "OpUserId", "OpUserName", "TotalLendMoney"}
)

func (log *TrafficAccidentLogger) BeforeCreate(tx *gorm.DB) (err error) {
	log.Id = model.Id()
	return
}

func (log *TrafficAccidentLogger) Create() error {
	return model.DB().Create(log).Error
}

func (log *TrafficAccidentLogger) Insert(loggers []TrafficAccidentLogger) error {
	return model.DB().Create(loggers).Error
}

func (log *TrafficAccidentLogger) GetBy(trafficAccidentId int64, keyword string, startAt, endAt time.Time, paginator model.Paginator) ([]TrafficAccidentLogger, int64) {

	var loggers []TrafficAccidentLogger
	tx := model.DB().Model(&TrafficAccidentLogger{}).Where("TrafficAccidentId = ?", trafficAccidentId)

	if keyword != "" {
		tx = tx.Where("OpUserName LIKE ?", "%"+keyword+"%")
	}
	if !startAt.IsZero() {
		tx = tx.Where("CreatedAt >= ?", startAt.Format(model.TimeFormat))
	}

	if !endAt.IsZero() {
		tx = tx.Where("CreatedAt <= ?", endAt.Format(model.TimeFormat))
	}

	var count int64
	tx.Count(&count)

	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	tx.Order("CreatedAt DESC").Find(&loggers)

	return loggers, count
}

func (log *TrafficAccidentLogger) GetNotProcessId() []TrafficAccidentLogger {
	var loggers []TrafficAccidentLogger

	model.DB().Model(&TrafficAccidentLogger{}).Where("ProcessId = '' OR ProcessId IS NULL").Find(&loggers)

	return loggers
}

func (log *TrafficAccidentLogger) Update(id int64, processId string, snapshot []byte) {
	model.DB().Model(&TrafficAccidentLogger{}).Where("id = ?", id).Updates(map[string]interface{}{
		"processid": processId,
		"snapshot":  model.JSON(snapshot),
		"updatedat": model.LocalTime(time.Now()),
	})
}
