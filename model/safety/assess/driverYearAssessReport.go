package assess

import (
	"app/org/scs/erpv2/api/model"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"gorm.io/gorm"
)

type DriverYearAssessReport struct {
	model.PkId
	model.Corporations
	StaffId     int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:司机ID;uniqueIndex:staffid_reportyear_unique_index"`
	StaffName   string          `gorm:"column:staffname;type:varchar(255);comment:司机名称"`
	LineId      int64           `json:"LineId" gorm:"column:lineid;type:integer;comment:所属线路"`
	LineName    string          `gorm:"column:linename;type:varchar(255);comment:所属线路名称"`
	IsCctDriver int64           `json:"IsCctDriver" gorm:"column:iscctdriver;type:smallint;comment:是否村村通司机 1是 2否"`
	ReportYear  int64           `json:"ReportYear" gorm:"column:reportyear;type:integer;comment:报表年份;uniqueIndex:staffid_reportyear_unique_index"`
	StartAt     model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始日期"`
	EndAt       model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束日期"`
	RunDay      int64           `json:"RunDay" gorm:"column:runday;type:integer;comment:年计算天数（实际运营天数）1天=>100"`
	AvgPlanDay  int64           `json:"AvgPlanDay" gorm:"column:avgplanday;type:integer;comment:年基准天数（计划平均天数）1天=>100"`
	AssessRate  int64           `json:"AssessRate" gorm:"column:assessrate;type:integer;comment:考核系数 0.0321 =>321"`

	//安全考核
	SafeCasualLeaveRate     int64 `json:"SafeCasualLeaveRate" gorm:"column:safecasualleaverate;type:integer;comment:安全事假系数 0.0321 =>321"`
	SafeMileagePrice        int64 `json:"SafeMileagePrice" gorm:"column:safemileageprice;type:integer;comment:安全公里单价 单位：分"`
	SafeWorkCutMoney        int64 `json:"SafeWorkCutMoney" gorm:"column:safeworkcutmoney;type:integer;comment:安全出勤扣款金额 单位：分"`
	SafeCasualLeaveCutMoney int64 `json:"SafeCasualLeaveCutMoney" gorm:"column:safecasualleavecutmoney;type:integer;comment:安全事假扣款金额 单位：分"`
	SafeViolationCutMoney   int64 `json:"SafeViolationCutMoney" gorm:"column:safeviolationcutmoney;type:integer;comment:安全违规扣款金额 单位：分"`
	AccidentCutMoney        int64 `json:"AccidentCutMoney" gorm:"column:accidentcutmoney;type:integer;comment:事故扣款金额 单位：分"`
	SafeMileage             int64 `json:"SafeMileage" gorm:"column:safemileage;type:integer;comment:安全公里 单位：米"`
	SafeAssessReward        int64 `json:"SafeAssessReward" gorm:"column:safeassessreward;type:integer;comment:安全年考核奖金 单位：分"`
	SafeMileageReward       int64 `json:"SafeMileageReward" gorm:"column:safemileagereward;type:integer;default:0;comment:安全公里奖金 单位：分"`

	//服务考核
	ServiceViolationCutMoney   int64 `json:"ServiceViolationCutMoney" gorm:"column:serviceviolationcutmoney;type:integer;comment:服务违规扣款金额 单位：分"`
	ServiceCasualLeaveRate     int64 `json:"ServiceCasualLeaveRate" gorm:"column:servicecasualleaverate;type:integer;comment:服务事假系数 0.0321 =>321"`
	ServiceCasualLeaveCutMoney int64 `json:"ServiceCasualLeaveCutMoney" gorm:"column:servicecasualleavecutmoney;type:integer;comment:服务事假扣款金额 单位：分"`
	ServiceWorkCutMoney        int64 `json:"ServiceWorkCutMoney" gorm:"column:serviceworkcutmoney;type:integer;comment:服务出勤扣款金额 单位：分"`
	ThirdPartAssessCutMoney    int64 `json:"ThirdPartAssessCutMoney" gorm:"column:thirdpartassesscutmoney;type:integer;comment:三方测评扣款 单位：分"`
	ServiceAssessReward        int64 `json:"ServiceAssessReward" gorm:"column:serviceassessreward;type:integer;comment:服务年考核奖金 单位：分"`
	ThirdPartAssessReward      int64 `json:"ThirdPartAssessReward" gorm:"column:thirdpartassessreward;type:integer;comment:三方测评奖金 单位：分"`

	model.Timestamp

	CorporationId   int64  `json:"CorporationId" gorm:"-"`
	CorporationName string `json:"CorporationName" gorm:"-"`
}

func (m *DriverYearAssessReport) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}
func (m *DriverYearAssessReport) Create(tx *gorm.DB) error {
	return tx.Create(&m).Error
}
func (m *DriverYearAssessReport) GetBy(corporationIds []int64, lineId, staffId, year int64, paginator model.Paginator) ([]DriverYearAssessReport, int64) {
	var reports []DriverYearAssessReport
	tx := model.DB().Model(&DriverYearAssessReport{})
	if len(corporationIds) > 0 {
		tx.Scopes(model.WhereCorporations(corporationIds))
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	if staffId > 0 {
		tx.Where("StaffId = ?", staffId)
	}

	if year > 0 {
		tx.Where("ReportYear = ?", year)
	}

	var count int64
	tx.Count(&count)

	tx.Order("ReportYear DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&reports)
	return reports, count

}

func (m *DriverYearAssessReport) FirstBy(id int64) DriverYearAssessReport {
	var report DriverYearAssessReport
	model.DB().Model(&DriverYearAssessReport{}).First(&report, id)
	return report
}

func (m *DriverYearAssessReport) LatestCalcTime() model.LocalTime {
	var report DriverYearAssessReport
	model.DB().Model(&DriverYearAssessReport{}).Order("UpdatedAt DESC").Limit(1).First(&report)
	return report.UpdatedAt
}

// DriverMonthWorkRecord 司机每月出勤表
type DriverMonthWorkRecord struct {
	model.PkId
	ReportId        int64           `json:"ReportId" gorm:"column:reportid;type:bigint;comment:报表ID"`
	StaffId         int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:司机ID"`
	StaffName       string          `gorm:"column:staffname;type:varchar(255);comment:司机名称"`
	MainLineId      int64           `json:"MainLineId" gorm:"column:mainlineid;type:integer;comment:主营线路"`
	MainLineName    string          `gorm:"column:mainlinename;type:varchar(255);comment:主营线路名称"`
	ReportMonth     int64           `json:"ReportMonth" gorm:"column:reportmonth;type:integer;comment:报表月份"`
	StartAt         model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始日期"`
	EndAt           model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束日期"`
	RunDay          int64           `json:"RunDay" gorm:"column:runday;type:integer;comment:年计算天数（实际运营天数）1天=>100"`
	AvgPlanDay      int64           `json:"AvgPlanDay" gorm:"column:avgplanday;type:integer;comment:年基准天数（计划平均天数）1天=>100"`
	AssessRate      int64           `json:"AssessRate" gorm:"column:assessrate;type:integer;comment:考核系数 0.0321 =>321"`
	SafeCutMoney    int64           `json:"SafeCutMoney" gorm:"column:safecutmoney;type:integer;default:0;comment:安全扣款金额 单位：分"`
	ServiceCutMoney int64           `json:"ServiceCutMoney" gorm:"column:servicecutmoney;type:integer;default:0;comment:服务扣款金额 单位：分"`
	model.Timestamp
}

func (m *DriverMonthWorkRecord) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}
func (m *DriverMonthWorkRecord) Create(tx *gorm.DB, data []DriverMonthWorkRecord) error {
	return tx.Create(&data).Error
}

func (m *DriverMonthWorkRecord) GetBy(id int64) []DriverMonthWorkRecord {
	var items []DriverMonthWorkRecord
	model.DB().Model(&DriverMonthWorkRecord{}).Where("ReportId = ?", id).Order("ReportAt ASC").Find(&items)

	return items
}

// DriverCasualLeaveRecord 司机每月事假天数记录
type DriverCasualLeaveRecord struct {
	model.PkId
	ReportId    int64           `json:"ReportId" gorm:"column:reportid;type:bigint;comment:报表ID"`
	StaffId     int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:司机ID"`
	StaffName   string          `gorm:"column:staffname;type:varchar(255);comment:司机名称"`
	ReportMonth int64           `json:"ReportMonth" gorm:"column:reportmonth;type:integer;comment:报表月份"`
	StartAt     model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始日期"`
	EndAt       model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束日期"`
	LeaveDay    int64           `json:"LeaveDay" gorm:"column:leaveday;type:integer;comment:请假天数"`
	model.Timestamp
}

func (m *DriverCasualLeaveRecord) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *DriverCasualLeaveRecord) Create(tx *gorm.DB, data []DriverCasualLeaveRecord) error {
	return tx.Create(&data).Error
}

func (m *DriverCasualLeaveRecord) GetBy(id int64) []DriverCasualLeaveRecord {
	var items []DriverCasualLeaveRecord
	model.DB().Model(&DriverCasualLeaveRecord{}).Where("ReportId = ?", id).Order("ReportAt ASC").Find(&items)

	return items
}

// DriverViolationYearAssessRecord 司机年违规记录
type DriverViolationYearAssessRecord struct {
	model.PkId
	ReportId            int64           `json:"ReportId" gorm:"column:reportid;type:bigint;comment:报表ID"`
	StaffId             int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:司机ID"`
	ReportYear          int64           `json:"ReportYear" gorm:"column:reportyear;type:integer;comment:统计年份"`
	StartAt             model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始时间"`
	EndAt               model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束时间"`
	ViolationId         int64           `json:"ViolationId" gorm:"column:violationid;type:bigint;comment:违规ID"`
	ViolationCateAttr   int64           `json:"ViolationCateAttr" gorm:"column:violationcateattr;type:smallint;comment:违规属性 1安全 2服务"`
	ViolationOrigin     int64           `json:"ViolationOrigin" gorm:"column:violationorigin;type:smallint;comment:违规来源 4三方测评"`
	ReportAt            model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:违规时间"`
	CategoryId          int64           `json:"CategoryId" gorm:"column:categoryid;type:bigint;comment:违规项ID"`
	CategoryTopId       int64           `json:"CategoryTopId" gorm:"column:categorytopid;type:bigint;comment:违规项顶级分类ID"`
	StandardId          int64           `json:"StandardId" gorm:"column:standardid;type:bigint;comment:标准ID"`
	CutMoney            int64           `json:"CutMoney" gorm:"column:cutmoney;type:integer;default:0;comment:扣钱金额 单位：分"`
	ThirdAssessCutMoney int64           `json:"ThirdAssessCutMoney" gorm:"column:thirdassesscutmoney;type:integer;default:0;comment:三方测评扣钱金额 单位：分"`
	model.Timestamp

	License  string                                 `json:"License" gorm:"-"`
	LineName string                                 `json:"LineName" gorm:"-"`
	Standard safetyModel.QualityAssessmentStandards `json:"Standard" gorm:"-"`
}

func (m *DriverViolationYearAssessRecord) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *DriverViolationYearAssessRecord) Create(tx *gorm.DB, data []DriverViolationYearAssessRecord) error {
	return tx.Create(&data).Error
}

func (m *DriverViolationYearAssessRecord) GetBy(id int64) []DriverViolationYearAssessRecord {
	var items []DriverViolationYearAssessRecord
	model.DB().Model(&DriverViolationYearAssessRecord{}).Where("ReportId = ?", id).Order("ReportAt ASC").Find(&items)

	return items
}

// DriverAccidentYearAssessRecord 司机年事故记录
type DriverAccidentYearAssessRecord struct {
	model.PkId
	ReportId   int64           `json:"ReportId" gorm:"column:reportid;type:bigint;comment:报表ID"`
	StaffId    int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:司机ID"`
	ReportYear int64           `json:"ReportYear" gorm:"column:reportyear;type:integer;comment:统计年份"`
	StartAt    model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始时间"`
	EndAt      model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束时间"`
	AccidentId int64           `json:"ViolationId" gorm:"column:violationid;type:bigint;comment:违规ID"`
	HappenAt   model.LocalTime `json:"HappenAt" gorm:"column:happenat;type:timestamp;comment:事故发生时间"`
	CutMoney   int64           `json:"CutMoney" gorm:"column:cutmoney;type:integer;default:0;comment:扣钱金额 单位：分"`
	model.Timestamp

	AccidentCheckResult int64  `json:"AccidentCheckResult" gorm:"-"` //1减轻处罚  2免于处罚
	License             string `json:"License" gorm:"-"`
	LineName            string `json:"LineName" gorm:"-"`
	LiabilityType       int64  `json:"LiabilityType" gorm:"-"`
}

func (m *DriverAccidentYearAssessRecord) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *DriverAccidentYearAssessRecord) Create(tx *gorm.DB, data []DriverAccidentYearAssessRecord) error {
	return tx.Create(&data).Error
}

func (m *DriverAccidentYearAssessRecord) GetBy(id int64) []DriverAccidentYearAssessRecord {
	var items []DriverAccidentYearAssessRecord
	model.DB().Model(&DriverAccidentYearAssessRecord{}).Where("ReportId = ?", id).Order("ReportAt ASC").Find(&items)

	return items
}
