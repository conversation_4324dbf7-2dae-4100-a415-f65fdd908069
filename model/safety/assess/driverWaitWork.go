package assess

import (
	"app/org/scs/erpv2/api/model"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"gorm.io/gorm"
)

// DriverWaitWorkReport 司机待岗记录报表
type DriverWaitWorkReport struct {
	model.PkId
	model.Corporations
	StaffId                        int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:司机ID"`
	StaffName                      string          `gorm:"column:staffname;type:varchar(255);comment:司机名称"`
	LineId                         int64           `json:"LineId" gorm:"column:lineid;type:integer;comment:所属线路"`
	LineName                       string          `gorm:"column:linename;type:varchar(255);comment:所属线路名称"`
	CategoryId                     int64           `json:"CategoryId" gorm:"column:categoryid;type:bigint;comment:违规项ID"`
	IsCctDriver                    int64           `json:"IsCctDriver" gorm:"column:iscctdriver;type:smallint;comment:是否村村通司机 1是 2否"`
	ReportYear                     int64           `json:"ReportYear" gorm:"column:reportyear;type:integer;comment:报表年份;"`
	ReportMonth                    int64           `json:"ReportMonth" gorm:"column:reportmonth;type:integer;comment:报表月份;"`
	Times                          int64           `json:"Times" gorm:"column:times;type:smallint;comment:报表年份第几次违规，只算纳入考核的;"`
	StartAt                        model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始日期"`
	EndAt                          model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束日期"`
	AssessFrom                     int64           `json:"AssessFrom" gorm:"column:assessfrom;type:smallint;comment:考核来源 1服务质量考核"`
	WaitDay                        int64           `json:"WaitDay" gorm:"column:waitday;type:smallint;comment:待岗天数"`
	IsDone                         int64           `json:"IsDone" gorm:"column:isdone;type:smallint;comment:是否完成 1是 2否"`
	DriverWaitWorkViolationItemIds model.JSON      `json:"DriverWaitWorkViolationItemIds" gorm:"column:driverwaitworkviolationitemids;type:json;comment:关联的违规和标准列表记录"`
	ApplyLeaveRecordId             int64           `json:"ApplyLeaveRecordId" gorm:"column:applyleaverecordid;type:bigint;comment:请假列表Id"`
	model.Timestamp

	License         string                                   `json:"License" gorm:"-"`
	LiabilityType   int64                                    `json:"LiabilityType" gorm:"-"`
	Standards       []safetyModel.QualityAssessmentStandards `json:"Standards" gorm:"-"`
	CorporationId   int64                                    `json:"CorporationId" gorm:"-"`
	CorporationName string                                   `json:"CorporationName" gorm:"-"`
	Items           []DriverWaitWorkViolationItem            `json:"Items" gorm:"-"`
	LeaveDates      []model.LocalTime                        `json:"LeaveDates" gorm:"-"`
}

func (m *DriverWaitWorkReport) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}
func (m *DriverWaitWorkReport) Create(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *DriverWaitWorkReport) UpdateApplyLeaveRecordId() error {
	return model.DB().Select("ApplyLeaveRecordId").Updates(&m).Error
}

func (m *DriverWaitWorkReport) FirstBy(staffId, year, categoryId, times int64) DriverWaitWorkReport {
	var item DriverWaitWorkReport
	model.DB().Model(&DriverWaitWorkReport{}).Where("StaffId = ? AND ReportYear = ? AND CategoryId = ? AND Times = ?", staffId, year, categoryId, times).First(&item)

	return item
}

func (m *DriverWaitWorkReport) FindById(id int64) DriverWaitWorkReport {
	var item DriverWaitWorkReport
	model.DB().Model(&DriverWaitWorkReport{}).Where("Id = ?", id).First(&item)

	return item
}

func (m *DriverWaitWorkReport) GetBy(corporationIds []int64, lineId, staffId, isDone int64, paginator model.Paginator) ([]DriverWaitWorkReport, int64) {
	var reports []DriverWaitWorkReport
	tx := model.DB().Model(&DriverWaitWorkReport{})
	if len(corporationIds) > 0 {
		tx.Scopes(model.WhereCorporations(corporationIds))
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	if staffId > 0 {
		tx.Where("StaffId = ?", staffId)
	}

	if isDone > 0 {
		tx.Where("IsDone = ?", isDone)
	}

	var count int64
	tx.Count(&count)

	tx.Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&reports)
	return reports, count

}

type DriverWaitWorkViolationItem struct {
	model.PkId
	ViolationId               int64           `json:"ViolationId" gorm:"column:violationid;type:bigint;comment:违规ID"`
	ReportAt                  model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:违规时间"`
	QualityAssessmentCateId   int64           `json:"QualityAssessmentCateId" gorm:"column:qualityassessmentcateid;type:bigint;comment:违规分类ID"`
	QualityAssessmentCateAttr int64           `json:"QualityAssessmentCateAttr" gorm:"column:qualityassessmentcateattr;type:smallint;comment:违规属性 1安全行为 2专项考核 3服务质量"`
	CategoryId                int64           `json:"CategoryId" gorm:"column:categoryid;type:bigint;comment:违规项ID"`
	StandardId                int64           `json:"StandardId" gorm:"column:standardid;type:bigint;comment:标准ID"`
	model.Timestamp

	License      string                                 `json:"License" gorm:"-"`
	LineName     string                                 `json:"LineName" gorm:"-"`
	Standard     safetyModel.QualityAssessmentStandards `json:"Standard" gorm:"-"`
	CategoryName string                                 `json:"CategoryName" gorm:"-"`
}

func (m *DriverWaitWorkViolationItem) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *DriverWaitWorkViolationItem) Create(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *DriverWaitWorkViolationItem) FirstBy(violationId, standardId int64) DriverWaitWorkViolationItem {
	var item DriverWaitWorkViolationItem
	model.DB().Model(&DriverWaitWorkViolationItem{}).Where("ViolationId = ? AND StandardId = ?", violationId, standardId).First(&item)
	return item
}

func (m *DriverWaitWorkViolationItem) GetByIds(ids []int64) []DriverWaitWorkViolationItem {
	var items []DriverWaitWorkViolationItem
	model.DB().Model(&DriverWaitWorkViolationItem{}).Where("Id IN ?", ids).Find(&items)

	return items
}
