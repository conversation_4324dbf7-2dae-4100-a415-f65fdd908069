package assess

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type DriverAssessRewardReport struct {
	model.PkId
	model.Corporations
	StaffId         int64      `json:"StaffId" gorm:"column:staffid;type:integer;comment:司机ID"`
	StaffName       string     `json:"StaffName" gorm:"column:staffname;type:varchar;comment:司机姓名"`
	LineId          int64      `json:"LineId" gorm:"column:lineid;type:integer;comment:线路ID"`
	LineName        string     `json:"LineName" gorm:"column:linename;type:varchar;comment:线路名称"`
	RewardType      string     `json:"RewardType" gorm:"column:rewardtype;type:varchar;comment:奖励类型"`
	ChildRewardType string     `json:"ChildRewardType" gorm:"column:childrewardtype;type:varchar;comment:子类"`
	RewardMoney     int64      `json:"RewardMoney" gorm:"column:rewardmoney;type:integer;comment:奖励金额 单位：分"`
	Month           int64      `json:"Month" gorm:"column:month;type:integer;comment:月份"`
	More            string     `json:"More" gorm:"column:more;type:varchar;comment:备注"`
	File            model.JSON `json:"File" gorm:"column:file;type:json;comment:附件"`
	model.OpUser
	model.Timestamp

	CorporationId   int64  `json:"CorporationId" gorm:"-"`
	CorporationName string `json:"CorporationName" gorm:"-"`
	IsCctDriver     int64  `json:"IsCctDriver" gorm:"-"` //是否村村通司机 1是  2否
}

func (m *DriverAssessRewardReport) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *DriverAssessRewardReport) Create() error {
	return model.DB().Create(&m).Error
}

func (m *DriverAssessRewardReport) Update() error {
	return model.DB().Select("*").Updates(&m).Error
}

func (m *DriverAssessRewardReport) Delete() error {
	return model.DB().Delete(&m).Error
}

func (m *DriverAssessRewardReport) FirstBy(id int64) DriverAssessRewardReport {
	var report DriverAssessRewardReport
	model.DB().Model(&DriverAssessRewardReport{}).Where("Id = ?", id).First(&report)

	return report
}

func (m *DriverAssessRewardReport) GetBy(corporationIds []int64, lineId, staffId, month int64, paginator model.Paginator) ([]DriverAssessRewardReport, int64) {
	var reports []DriverAssessRewardReport
	tx := model.DB().Model(&DriverAssessRewardReport{})
	if len(corporationIds) > 0 {
		tx.Scopes(model.WhereCorporations(corporationIds))
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}
	if staffId > 0 {
		tx.Where("StaffId = ?", staffId)
	}
	if month > 0 {
		tx.Where("Month = ?", month)
	}

	var count int64
	tx.Count(&count)

	tx.Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&reports)

	return reports, count
}
