package assess

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

// DriverDevoteReport 司机贡献
type DriverDevoteReport struct {
	model.PkId
	model.Corporations
	StaffId             int64  `json:"StaffId" gorm:"column:staffid;type:integer;comment:司机ID;uniqueIndex:device_devote_report_staffid_unique_index"`
	StaffName           string `json:"StaffName" gorm:"column:staffname;type:varchar(255);comment:司机名称"`
	LineId              int64  `json:"LineId" gorm:"column:lineid;type:integer;comment:所属线路"`
	LineName            string `json:"LineName" gorm:"column:linename;type:varchar(255);comment:所属线路名称"`
	IsCctDriver         int64  `json:"IsCctDriver" gorm:"column:iscctdriver;type:smallint;comment:是否村村通司机 1是 2否"`
	SafeReportMonth     int64  `json:"-" gorm:"column:safereportmonth;type:integer;comment:安全报表月份;"`
	SafeStartMonth      int64  `json:"SafeStartMonth" gorm:"column:safestartmonth;type:integer;comment:安全起始月份;"`
	SafeMonthSum        int64  `json:"SafeMonthSum" gorm:"column:safemonthsum;type:smallint;comment:安全累计月份;"`
	SafeCurrentLevel    int64  `json:"SafeCurrentLevel" gorm:"column:safecurrentlevel;type:smallint;comment:安全当前考核等级"`
	SafeRewardBaseMoney int64  `json:"SafeRewardBaseMoney" gorm:"column:saferewardbasemoney;type:integer;comment:安全奖金基数 单位：分"`

	ServiceReportMonth       int64           `json:"-" gorm:"column:servicereportmonth;type:integer;comment:服务报表月份;"`
	ServiceStartMonth        int64           `json:"ServiceStartMonth" gorm:"column:servicestartmonth;type:integer;comment:服务起始月份;"`
	ServiceMonthSum          int64           `json:"ServiceMonthSum" gorm:"column:servicemonthsum;type:smallint;comment:服务累计月份;"`
	ServiceCurrentLevel      int64           `json:"ServiceCurrentLevel" gorm:"column:servicecurrentlevel;type:smallint;comment:服务当前考核等级"`
	ServiceRewardBaseMoney   int64           `json:"ServiceRewardBaseMoney" gorm:"column:servicerewardbasemoney;type:integer;comment:服务奖金基数 单位：分"`
	ServiceFourLevelStartAt  model.LocalTime `json:"-" gorm:"column:servicefourlevelstartat;type:timestamp;comment:4类违规查询开始日期"`
	ServiceThreeLevelStartAt model.LocalTime `json:"-" gorm:"column:servicethreelevelstartat;type:timestamp;comment:3类违规查询开始日期"`
	ServiceThreeLevelRemain  model.JSON      `json:"-" gorm:"column:servicethreelevelremain;type:json;comment:3类违规当月剩余没有累计进去的违规"`
	model.Timestamp
	CorporationId   int64                `json:"CorporationId" gorm:"-"`
	CorporationName string               `json:"CorporationName" gorm:"-"`
	Items           []DriverDevoteLogger `json:"Items" gorm:"-"`
}

func (m *DriverDevoteReport) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}
func (m *DriverDevoteReport) Create() error {
	return model.DB().Create(&m).Error
}

func (m *DriverDevoteReport) UpdateSafe(tx *gorm.DB) error {
	return tx.Select("SafeStartMonth", "SafeMonthSum", "SafeCurrentLevel", "SafeRewardBaseMoney").Updates(&m).Error
}

func (m *DriverDevoteReport) UpdateService(tx *gorm.DB) error {
	return tx.Select("ServiceStartMonth", "ServiceMonthSum", "ServiceCurrentLevel",
		"ServiceRewardBaseMoney", "ServiceFourLevelStartAt",
		"ServiceThreeLevelStartAt", "ServiceThreeLevelRemain").Updates(&m).Error
}

func (m *DriverDevoteReport) UpdateColumn(column string, val interface{}) error {
	return model.DB().Model(&DriverDevoteReport{}).Where("Id = ?", m.Id).UpdateColumn(column, val).Error
}
func (m *DriverDevoteReport) UpdateColumns(val map[string]interface{}) error {
	return model.DB().Model(&DriverDevoteReport{}).Where("Id = ?", m.Id).Updates(val).Error
}

func (m *DriverDevoteReport) FirstBy(staffId int64) DriverDevoteReport {
	var item DriverDevoteReport
	model.DB().Model(&DriverDevoteReport{}).Where("StaffId = ?", staffId).First(&item)

	return item
}

func (m *DriverDevoteReport) FindById(id int64) DriverDevoteReport {
	var item DriverDevoteReport
	model.DB().Model(&DriverDevoteReport{}).Where("Id = ?", id).First(&item)

	return item
}

func (m *DriverDevoteReport) GetBy(corporationIds []int64, lineId, staffId int64, paginator model.Paginator) ([]DriverDevoteReport, int64) {
	var reports []DriverDevoteReport
	tx := model.DB().Model(&DriverDevoteReport{})
	if len(corporationIds) > 0 {
		tx.Scopes(model.WhereCorporations(corporationIds))
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	if staffId > 0 {
		tx.Where("StaffId = ?", staffId)
	}

	var count int64
	tx.Count(&count)

	tx.Order("UpdatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&reports)
	return reports, count

}

type DriverDevoteLogger struct {
	model.PkId
	DriverDevoteReportId int64      `json:"DriverDevoteReportId" gorm:"column:driverdevotereportid;type:bigint;comment:考核报表ID"`
	ReportMonth          int64      `json:"ReportMonth" gorm:"column:reportmonth;type:integer;comment:变更月份;"`
	BeforeLevel          int64      `json:"BeforeLevel" gorm:"column:beforelevel;type:smallint;comment:变更前等级"`
	AfterLevel           int64      `json:"AfterLevel" gorm:"column:afterlevel;type:smallint;comment:变更后等级"`
	Action               string     `json:"Action" gorm:"column:action;type:varchar(100);comment:操作 up:升档，down:降档，edit:编辑，under_day:天数不满"`
	Detail               model.JSON `json:"Detail" gorm:"column:detail;type:json;comment:操作详情;"`
	Scene                string     `json:"-" gorm:"column:scene;type:varchar(100);default:;comment:safe=>安全 service=>服务"`
	model.Timestamp
}

func (m *DriverDevoteLogger) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *DriverDevoteLogger) Create(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *DriverDevoteLogger) GetByDevoteId(devoteId int64, scene string) []DriverDevoteLogger {
	var items []DriverDevoteLogger
	model.DB().Model(&DriverDevoteLogger{}).Where("DriverDevoteReportId = ? AND Scene = ?", devoteId, scene).Find(&items)

	return items
}

type DriverDevoteMonthMoneyReport struct {
	model.PkId
	model.Corporations
	StaffId                int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:司机ID;uniqueIndex:staffid_reportmonth_unique_index"`
	StaffName              string          `json:"StaffName" gorm:"column:staffname;type:varchar(255);comment:司机名称"`
	LineId                 int64           `json:"LineId" gorm:"column:lineid;type:integer;comment:所属线路"`
	LineName               string          `json:"LineName" gorm:"column:linename;type:varchar(255);comment:所属线路名称"`
	MainLineId             int64           `json:"MainLineId" gorm:"column:mainlineid;type:integer;comment:主营线路"`
	MainLineName           string          `json:"MainLineName" gorm:"column:mainlinename;type:varchar(255);comment:主营线路名称"`
	IsCctDriver            int64           `json:"IsCctDriver" gorm:"column:iscctdriver;type:smallint;comment:是否村村通司机 1是 2否"`
	ReportMonth            int64           `json:"ReportMonth" gorm:"column:reportmonth;type:integer;comment:报表月份;uniqueIndex:staffid_reportmonth_unique_index"`
	StartAt                model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始日期"`
	EndAt                  model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束日期"`
	RunDay                 int64           `json:"RunDay" gorm:"column:runday;type:integer;comment:实际运营天数 单位：天数*100"`
	AvgPlanDay             int64           `json:"AvgPlanDay" gorm:"column:avgplanday;type:integer;comment:基准天数（计划平均天数）1天=>100"`
	SafeRewardBaseMoney    int64           `json:"SafeRewardBaseMoney" gorm:"column:saferewardbasemoney;type:integer;comment:安全奖金基数 单位：分"`
	SafeDevoteMoney        int64           `json:"SafeDevoteMoney" gorm:"column:safedevotemoney;type:integer;comment:安全奖金 单位：分"`
	ServiceRewardBaseMoney int64           `json:"ServiceRewardBaseMoney" gorm:"column:servicerewardbasemoney;type:integer;comment:服务奖金基数 单位：分"`
	ServiceDevoteMoney     int64           `json:"ServiceDevoteMoney" gorm:"column:servicedevotemoney;type:integer;comment:服务奖金 单位：分 基数*系数"`
	model.Timestamp
}

func (m *DriverDevoteMonthMoneyReport) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *DriverDevoteMonthMoneyReport) Create() error {
	return model.DB().Create(&m).Error
}

func (m *DriverDevoteMonthMoneyReport) Update() error {
	return model.DB().Select("*").Updates(&m).Error
}

// 更新部分字段
func (m *DriverDevoteMonthMoneyReport) UpdateColumns() error {
	return model.DB().Select("RunDay", "AvgPlanDay", "SafeDevoteMoney", "ServiceDevoteMoney", "MainLineId", "MainLineName").Updates(&m).Error
}

// 获取司机某个月的记录
func (m *DriverDevoteMonthMoneyReport) GetDriverMonthReport(staffId, month int64) DriverDevoteMonthMoneyReport {
	var report DriverDevoteMonthMoneyReport
	model.DB().Model(&DriverDevoteMonthMoneyReport{}).Where("StaffId = ? AND ReportMonth = ?", staffId, month).First(&report)

	return report
}
