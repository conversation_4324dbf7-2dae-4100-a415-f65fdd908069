package assess

import (
	"app/org/scs/erpv2/api/model"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"gorm.io/gorm"
)

type DriverMonthAssessReport struct {
	model.PkId
	model.Corporations
	StaffId                      int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:司机ID;uniqueIndex:staffid_reportmonth_unique_index"`
	StaffName                    string          `gorm:"column:staffname;type:varchar(255);comment:司机名称"`
	LineId                       int64           `json:"LineId" gorm:"column:lineid;type:integer;comment:所属线路"`
	LineName                     string          `gorm:"column:linename;type:varchar(255);comment:所属线路名称"`
	MainLineId                   int64           `json:"MainLineId" gorm:"column:mainlineid;type:integer;comment:主营线路"`
	MainLineName                 string          `gorm:"column:mainlinename;type:varchar(255);comment:主营线路名称"`
	IsCctDriver                  int64           `json:"IsCctDriver" gorm:"column:iscctdriver;type:smallint;comment:是否村村通司机 1是 2否"`
	ReportMonth                  int64           `json:"ReportMonth" gorm:"column:reportmonth;type:integer;comment:报表月份;uniqueIndex:staffid_reportmonth_unique_index"`
	StartAt                      model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始日期"`
	EndAt                        model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束日期"`
	RunDay                       int64           `json:"RunDay" gorm:"column:runday;type:integer;comment:计算天数（实际运营天数）1天=>100"`
	AvgPlanDay                   int64           `json:"AvgPlanDay" gorm:"column:avgplanday;type:integer;comment:基准天数（计划平均天数）1天=>100"`
	AssessRate                   int64           `json:"AssessRate" gorm:"column:assessrate;type:integer;comment:考核系数 0.0321 =>321"`
	SafeAssessReward             int64           `json:"SafeAssessReward" gorm:"column:safeassessreward;type:integer;comment:安全行为奖 单位：分"`
	SpecialAssessReward          int64           `json:"SpecialAssessReward" gorm:"column:specialassessreward;type:integer;comment:专项考核奖 单位：分"`
	ServiceAssessReward          int64           `json:"ServiceAssessReward" gorm:"column:serviceassessreward;type:integer;comment:服务质量奖 单位：分"`
	AccidentAssessReward         int64           `json:"AccidentAssessReward" gorm:"column:accidentassessreward;type:integer;comment:事故考核奖 单位：分"`
	AdditionalSafeMoney          int64           `json:"AdditionalSafeMoney" gorm:"column:additionalsafemoney;type:integer;comment:安全额外奖金或者补扣 单位：分"`
	AdditionalSafeMoneyRemark    string          `json:"AdditionalSafeMoneyRemark" gorm:"column:additionalsafemoneyremark;type:varchar(255);comment:安全额外奖金或者补扣备注"`
	AdditionalServiceMoney       int64           `json:"AdditionalServiceMoney" gorm:"column:additionalservicemoney;type:integer;comment:服务额外奖金或者补扣 单位：分"`
	AdditionalServiceMoneyRemark string          `json:"AdditionalServiceMoneyRemark" gorm:"column:additionalservicemoneyremark;type:varchar(255);comment:服务额外奖金或者补扣备注"`
	model.Timestamp

	CorporationId      int64  `json:"CorporationId" gorm:"-"`
	CorporationName    string `json:"CorporationName" gorm:"-"`
	SafeDevoteMoney    int64  `json:"SafeDevoteMoney" gorm:"-"`    // 安全奖金
	ServiceDevoteMoney int64  `json:"ServiceDevoteMoney" gorm:"-"` // 服务奖金
}

func (m *DriverMonthAssessReport) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *DriverMonthAssessReport) Create(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *DriverMonthAssessReport) Updates() error {
	return model.DB().Select("*").Updates(&m).Error
}

func (m *DriverMonthAssessReport) Delete(staffId, month int64) error {
	return model.DB().Where("StaffId = ? AND ReportMonth = ?", staffId, month).Delete(&DriverMonthAssessReport{}).Error
}

func (m *DriverMonthAssessReport) GetBy(corporationIds []int64, lineId, staffId, month int64, paginator model.Paginator) ([]DriverMonthAssessReport, int64) {
	var reports []DriverMonthAssessReport
	tx := model.DB().Model(&DriverMonthAssessReport{})
	if len(corporationIds) > 0 {
		tx.Scopes(model.WhereCorporations(corporationIds))
	}
	if lineId > 0 {
		tx.Where("LineId = ?", lineId)
	}

	if staffId > 0 {
		tx.Where("StaffId = ?", staffId)
	}

	if month > 0 {
		tx.Where("ReportMonth = ?", month)
	}

	var count int64
	tx.Count(&count)

	tx.Order("ReportMonth DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&reports)
	return reports, count

}

func (m *DriverMonthAssessReport) FirstBy(id int64) DriverMonthAssessReport {
	var report DriverMonthAssessReport
	model.DB().Model(&DriverMonthAssessReport{}).First(&report, id)
	return report
}

func (m *DriverMonthAssessReport) LatestCalcTime() model.LocalTime {
	var report DriverMonthAssessReport
	model.DB().Model(&DriverMonthAssessReport{}).Order("UpdatedAt DESC").Limit(1).First(&report)
	return report.UpdatedAt
}

type DriverViolationMonthAssessReportItem struct {
	model.PkId
	ReportId                  int64           `json:"ReportId" gorm:"column:reportid;type:bigint;comment:报表ID"`
	StaffId                   int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:司机ID"`
	ReportMonth               int64           `json:"ReportMonth" gorm:"column:reportmonth;type:integer;comment:统计月份"`
	StartAt                   model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始时间"`
	EndAt                     model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束时间"`
	ViolationId               int64           `json:"ViolationId" gorm:"column:violationid;type:bigint;comment:违规ID"`
	ReportAt                  model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:违规时间"`
	QualityAssessmentCateId   int64           `json:"QualityAssessmentCateId" gorm:"column:qualityassessmentcateid;type:bigint;comment:违规分类ID"`
	QualityAssessmentCateAttr int64           `json:"QualityAssessmentCateAttr" gorm:"column:qualityassessmentcateattr;type:smallint;comment:违规属性 1安全行为 2专项考核 3服务质量"`
	CategoryId                int64           `json:"CategoryId" gorm:"column:categoryid;type:bigint;comment:违规项ID"`
	StandardId                int64           `json:"StandardId" gorm:"column:standardid;type:bigint;comment:标准ID"`
	CutMoney                  int64           `json:"CutMoney" gorm:"column:cutmoney;type:integer;comment:扣钱金额 单位：分"`
	model.Timestamp

	License  string                                 `json:"License" gorm:"-"`
	LineName string                                 `json:"LineName" gorm:"-"`
	Standard safetyModel.QualityAssessmentStandards `json:"Standard" gorm:"-"`
}

func (m *DriverViolationMonthAssessReportItem) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}
func (m *DriverViolationMonthAssessReportItem) Create(tx *gorm.DB, data []DriverViolationMonthAssessReportItem) error {
	return tx.Create(&data).Error
}

func (m *DriverViolationMonthAssessReportItem) GetBy(id int64) []DriverViolationMonthAssessReportItem {
	var items []DriverViolationMonthAssessReportItem
	model.DB().Model(&DriverViolationMonthAssessReportItem{}).Where("ReportId = ?", id).Order("ReportAt ASC").Find(&items)

	return items
}

func (m *DriverViolationMonthAssessReportItem) Delete(staffId, month int64) error {
	return model.DB().Where("ReportId IN (?)", model.DB().Model(&DriverMonthAssessReport{}).Select("Id").Where("StaffId = ? AND ReportMonth = ?", staffId, month)).Delete(&DriverViolationMonthAssessReportItem{}).Error
}

type DriverAccidentMonthAssessReportItem struct {
	model.PkId
	ReportId   int64           `json:"ReportId" gorm:"column:reportid;type:bigint;comment:报表ID"`
	StaffId    int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:司机ID"`
	Month      int64           `json:"Month" gorm:"column:staffid;type:integer;comment:统计月份"`
	StartAt    model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始时间"`
	EndAt      model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束时间"`
	AccidentId int64           `json:"AccidentId" gorm:"column:accidentid;type:bigint;comment:事故ID"`
	HappenAt   model.LocalTime `json:"HappenAt" gorm:"column:happenat;type:timestamp;comment:事故发生时间"`
	CutMoney   int64           `json:"CutMoney" gorm:"column:cutmoney;type:integer;comment:扣钱金额 单位：分"`
	model.Timestamp

	License       string `json:"License" gorm:"-"`
	LineName      string `json:"LineName" gorm:"-"`
	LiabilityType int64  `json:"LiabilityType" gorm:"-"`
}

func (m *DriverAccidentMonthAssessReportItem) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}
func (m *DriverAccidentMonthAssessReportItem) Create(tx *gorm.DB, data []DriverAccidentMonthAssessReportItem) error {
	return tx.Create(&data).Error
}

func (m *DriverAccidentMonthAssessReportItem) GetBy(id int64) []DriverAccidentMonthAssessReportItem {
	var items []DriverAccidentMonthAssessReportItem
	model.DB().Model(&DriverAccidentMonthAssessReportItem{}).Where("ReportId = ?", id).Order("HappenAt ASC").Find(&items)

	return items
}

func (m *DriverAccidentMonthAssessReportItem) Delete(staffId, month int64) error {
	return model.DB().Where("ReportId IN (?)", model.DB().Model(&DriverMonthAssessReport{}).Select("Id").Where("StaffId = ? AND ReportMonth = ?", staffId, month)).Delete(&DriverAccidentMonthAssessReportItem{}).Error
}
