package hr

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
	"time"
)

// InsuranceFundSetting 社保和公积金设置
type InsuranceFundSetting struct {
	model.PkId
	TopCorporationId  int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:顶级机构ID"`                                                    //顶级机构ID
	Scene             int64  `json:"Scene" gorm:"column:scene;type:smallint;comment:场景值  1-社保  2-公积金"`                                                              //场景值  1-社保  2-公积金
	Modular           string `json:"Modular" gorm:"column:modular;type:varchar(64);uniqueIndex:insurance_fund_madular_unique_index;comment:模块" validate:"required"` //模块 endowment-养老  medical-医疗 unemployment-失业 injury-工伤 birth-生育 annuity-年金  extraMedical-补充医疗  fund-公积金
	CompanyPayType    int64  `json:"CompanyPayType" gorm:"column:companypaytype;type:smallint;comment:单位缴纳类型 1-比例  2-固定金额" validate:"required"`                     //单位缴纳类型 1-比例  2-固定金额
	CompanyPayAmount  int64  `json:"CompanyPayAmount" gorm:"column:companypaymount;type:integer;comment:单位缴纳数额"`                                                    //单位缴纳数额  按比例时：10.5%=>1050  按固定金额时，单位：分
	PersonalPayType   int64  `json:"PersonalPayType" gorm:"column:personalpaytype;type:smallint;comment:个人缴纳类型" validate:"required"`                                //个人缴纳类型 1-比例  2-固定金额
	PersonalPayAmount int64  `json:"PersonalPayAmount" gorm:"column:personalpayamount;type:integer;comment:个人缴纳数额"`                                                 //个人缴纳数额  按比例时：10.5%=>1050  按固定金额时，单位：分
	model.OpUser
	model.Timestamp
}

func (set *InsuranceFundSetting) BeforeCreate(tx *gorm.DB) error {
	set.Id = model.Id()
	return nil
}

func (set *InsuranceFundSetting) Create(records []InsuranceFundSetting, scene int64) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Where("scene = ?", scene).Delete(&InsuranceFundSetting{}).Error
		if err != nil {
			return err
		}

		err = tx.Create(&records).Error

		if err != nil {
			return err
		}

		return nil
	})
}

func (set *InsuranceFundSetting) Get(topCorp, scene int64) []InsuranceFundSetting {
	var settings []InsuranceFundSetting
	model.DB().Model(&InsuranceFundSetting{}).Where("TopCorporationId = ? AND Scene = ?", topCorp, scene).Find(&settings)

	return settings
}

// InsuranceRecord 社保记录
type InsuranceRecord struct {
	model.PkId
	TopCorporationId          int64 `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:顶级机构ID"`                                           //顶级机构ID
	StaffArchiveId            int64 `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;uniqueIndex:unique_insurance_staff_adjustDate;comment:人员档案Id"` //人员档案Id
	StaffId                   int64 `json:"StaffId" gorm:"column:staffid;type:integer;comment:主数据人员ID"`                                                           //主数据人员ID
	AdjustDate                int64 `json:"AdjustDate" gorm:"column:adjustdate;type:integer;uniqueIndex:unique_insurance_staff_adjustDate;comment:调整日期"`          //调整日期
	EndowmentBase             int64 `json:"EndowmentBase" gorm:"column:endowmentbase;type:integer;comment:养老基数"`                                                  //养老基数
	EndowmentPersonRatio      int64 `json:"EndowmentPersonRatio" gorm:"column:endowmentpersonratio;type:integer;comment:养老个人比例"`                                  //养老个人比例 10.5%=>1050  -1代表按固定金额缴纳
	EndowmentPersonAmount     int64 `json:"EndowmentPersonAmount" gorm:"column:endowmentpersonamount;type:integer;comment:养老个人"`                                  //养老个人
	EndowmentCompanyRatio     int64 `json:"EndowmentCompanyRatio" gorm:"column:endowmentcompanyratio;type:integer;comment:养老单位比例"`                                //养老单位比例 10.5%=>1050  10.5%=>1050  -1代表按固定金额缴纳
	EndowmentCompanyAmount    int64 `json:"EndowmentCompanyAmount" gorm:"column:endowmentcompanyamount;type:integer;comment:养老单位"`                                //养老单位
	MedicalBase               int64 `json:"MedicalBase" gorm:"column:medicalbase;type:integer;comment:医疗基数"`                                                      //医疗基数
	MedicalPersonRatio        int64 `json:"MedicalPersonRatio" gorm:"column:medicalpersonratio;type:integer;comment:医疗个人比例"`                                      //医疗个人比例 10.5%=>1050  -1代表按固定金额缴纳
	MedicalPersonAmount       int64 `json:"MedicalPersonAmount" gorm:"column:medicalpersonamount;type:integer;comment:医疗个人"`                                      //医疗个人
	MedicalCompanyRatio       int64 `json:"MedicalCompanyRatio" gorm:"column:medicalcompanyratio;type:integer;comment:医疗单位比例"`                                    //医疗单位比例 10.5%=>1050  -1代表按固定金额缴纳
	MedicalCompanyAmount      int64 `json:"MedicalCompanyAmount" gorm:"column:medicalcompanyamount;type:integer;comment:医疗单位"`                                    //医疗单位
	UnemploymentBase          int64 `json:"UnemploymentBase" gorm:"column:unemploymentbase;type:integer;comment:失业基数"`                                            //失业基数
	UnemploymentPersonRatio   int64 `json:"UnemploymentPersonRatio" gorm:"column:unemploymentpersonratio;type:integer;comment:失业个人比例"`                            //失业个人比例 10.5%=>1050  -1代表按固定金额缴纳
	UnemploymentPersonAmount  int64 `json:"UnemploymentPersonAmount" gorm:"column:unemploymentpersonamount;type:integer;comment:失业个人"`                            //失业个人
	UnemploymentCompanyRatio  int64 `json:"UnemploymentCompanyRatio" gorm:"column:unemploymentcompanyratio;type:integer;comment:失业单位比例"`                          //失业单位比例 10.5%=>1050  -1代表按固定金额缴纳
	UnemploymentCompanyAmount int64 `json:"UnemploymentCompanyAmount" gorm:"column:unemploymentcompanyamount;type:integer;comment:失业单位"`                          //失业单位
	BirthBase                 int64 `json:"BirthBase" gorm:"column:birthbase;type:integer;comment:生育基数"`                                                          //生育基数
	BirthPersonRatio          int64 `json:"BirthPersonRatio" gorm:"column:birthpersonratio;type:integer;comment:生育个人比例"`                                          //生育个人比例 10.5%=>1050  -1代表按固定金额缴纳
	BirthPersonAmount         int64 `json:"BirthPersonAmount" gorm:"column:birthpersonamount;type:integer;comment:生育个人"`                                          //生育个人
	BirthCompanyRatio         int64 `json:"BirthCompanyRatio" gorm:"column:birthcompanyratio;type:integer;comment:生育单位比例"`                                        //生育单位比例 10.5%=>1050  -1代表按固定金额缴纳
	BirthCompanyAmount        int64 `json:"BirthCompanyAmount" gorm:"column:birthcompanyamount;type:integer;comment:生育单位"`                                        //生育单位
	InjuryBase                int64 `json:"InjuryBase" gorm:"column:injurybase;type:integer;comment:工伤基数"`                                                        //工伤基数
	InjuryPersonRatio         int64 `json:"InjuryPersonRatio" gorm:"column:injurypersonratio;type:integer;comment:工伤个人比例"`                                        //工伤个人比例 10.5%=>1050  -1代表按固定金额缴纳
	InjuryPersonAmount        int64 `json:"InjuryPersonAmount" gorm:"column:injurypersonamount;type:integer;comment:工伤个人"`                                        //工伤个人
	InjuryCompanyRatio        int64 `json:"InjuryCompanyRatio" gorm:"column:injurycompanyratio;type:integer;comment:工伤单位比例"`                                      //工伤单位比例 10.5%=>1050  -1代表按固定金额缴纳
	InjuryCompanyAmount       int64 `json:"InjuryCompanyAmount" gorm:"column:injurycompanyamount;type:integer;comment:工伤单位"`                                      //工伤单位
	IsAnnuity                 int64 `json:"IsAnnuity" gorm:"column:isannuity;type:integer;comment:是否有企业年金1是 2否"`                                                  //是否有企业年金  1是 2否
	AnnuityBase               int64 `json:"AnnuityBase" gorm:"column:annuitybase;type:integer;comment:年金基数"`                                                      //年金基数
	AnnuityPersonRatio        int64 `json:"AnnuityPersonRatio" gorm:"column:annuitypersonratio;type:integer;comment:年金个人比例"`                                      //年金个人比例 10.5%=>1050  -1代表按固定金额缴纳
	AnnuityPersonAmount       int64 `json:"AnnuityPersonAmount" gorm:"column:annuitypersonamount;type:integer;comment:年金个人"`                                      //年金个人
	AnnuityCompanyRatio       int64 `json:"AnnuityCompanyRatio" gorm:"column:annuitycompanyratio;type:integer;comment:年金单位比例"`                                    //年金单位比例 10.5%=>1050  -1代表按固定金额缴纳
	AnnuityCompanyAmount      int64 `json:"AnnuityCompanyAmount" gorm:"column:annuitycompanyamount;type:integer;comment:年金单位"`                                    //年金单位
	IsExtraMedical            int64 `json:"IsExtraMedical" gorm:"column:isextramedical;type:integer;comment:是否有补充医疗[1是2否]"`                                       //是否有补充医疗 1是 2否
	ExtraMedicalBase          int64 `json:"ExtraMedicalBase" gorm:"column:extramedicalbase;type:integer;comment:补充医疗基数"`                                          //补充医疗基数
	ExtraMedicalPersonRatio   int64 `json:"ExtraMedicalPersonRatio" gorm:"column:extramedicalpersonratio;type:integer;comment:补充医疗个人比例"`                          //补充医疗个人比例 10.5%=>1050  -1代表按固定金额缴纳
	ExtraMedicalPersonAmount  int64 `json:"ExtraMedicalPersonAmount" gorm:"column:extramedicalpersonamount;type:integer;comment:补充医疗个人"`                          //补充医疗个人
	ExtraMedicalCompanyRatio  int64 `json:"ExtraMedicalCompanyRatio" gorm:"column:extramedicalcompanyratio;type:integer;comment:补充医疗单位比例"`                        //补充医疗单位比例 10.5%=>1050  -1代表按固定金额缴纳
	ExtraMedicalCompanyAmount int64 `json:"ExtraMedicalCompanyAmount" gorm:"column:extramedicalcompanyamount;type:integer;comment:补充医疗单位"`                        //补充医疗单位

	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deletedat;type:timestamp;uniqueIndex:unique_insurance_staff_adjustDate;comment:删除时间"`
	model.OpUser
	model.Timestamp
}

func (inr *InsuranceRecord) BeforeCreate(tx *gorm.DB) error {
	inr.Id = model.Id()
	return nil
}
func (inr *InsuranceRecord) Create(records []InsuranceRecord) error {
	return model.DB().Create(&records).Error
}

func (inr *InsuranceRecord) GetLatestFirstRecord(staffArchiveId int64) InsuranceRecord {
	var record InsuranceRecord
	model.DB().Model(&InsuranceRecord{}).Where("StaffArchiveId = ?", staffArchiveId).Order("AdjustDate DESC").First(&record)

	return record
}

func (inr *InsuranceRecord) GetStaffRecord(staffArchiveId int64, paginator model.Paginator) ([]InsuranceRecord, int64) {
	var records []InsuranceRecord
	var count int64
	tx := model.DB().Model(&InsuranceRecord{}).Where("StaffArchiveId = ?", staffArchiveId).Count(&count)

	tx.Order("AdjustDate DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&records)

	return records, count
}

func (inr *InsuranceRecord) List(groupId int64, startAt, endAt time.Time, paginator model.Paginator) ([]InsuranceRecord, int64, error) {
	var rsp []InsuranceRecord
	var totalCount int64
	tx := model.DB().Model(&InsuranceRecord{}).Where("CreatedAt >= ? AND CreatedAt < ?", startAt, endAt).Where("TopCorporationId = ?", groupId)

	tx.Count(&totalCount)
	err := tx.Offset(paginator.Offset).Limit(paginator.Limit).Scan(&rsp).Error

	return rsp, totalCount, err
}

type InsuranceRecordApi struct {
	InsuranceRecord
	StaffName       string `json:"StaffName" gorm:"-"`       //员工姓名
	JobNumber       string `json:"JobNumber" gorm:"-"`       //员工工号
	CorporationName string `json:"CorporationName" gorm:"-"` //机构
}

func (inr *InsuranceRecord) GetBy(scene string, staffIds []int64, startYear, endYear int64, paginator model.Paginator) ([]InsuranceRecordApi, int64) {
	tx := model.DB().Model(&InsuranceRecord{}).Where("StaffId IN ?", staffIds).Where("EXISTS (?)", model.DB().Table("staff_archives").Where("staff_archives.Id = insurance_records.staffarchiveid"))

	if startYear > 0 {
		tx = tx.Where(" AdjustDate >= ?", startYear)
	}

	if endYear > 0 {
		tx = tx.Where(" AdjustDate <= ?", endYear)
	}
	//有企业年金
	if scene == util.ModularForAnnuity {
		tx = tx.Where("IsAnnuity = ?", util.StatusForTrue)
	}

	//有补充医疗
	if scene == util.ModularForExtraMedical {
		tx = tx.Where("IsExtraMedical = ?", util.StatusForTrue)
	}

	var count int64
	tx.Count(&count)

	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	var records []InsuranceRecordApi
	tx.Order("StaffId DESC").Order("AdjustDate DESC").Find(&records)

	return records, count
}

func (inr *InsuranceRecord) BatchUpdate(records []InsuranceRecord, scene string) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		for i := range records {
			switch scene {
			case util.ModularForAnnuity:
				err := tx.Model(&InsuranceRecord{}).Where("Id = ?", records[i].Id).Select("AnnuityBase", "AnnuityPersonAmount", "AnnuityCompanyAmount").Save(&records[i]).Error
				if err != nil {
					return err
				}
				break
			case util.ModularForExtraMedical:
				err := tx.Model(&InsuranceRecord{}).Where("Id = ?", records[i].Id).Select("ExtraMedicalBase", "ExtraMedicalPersonAmount", "ExtraMedicalCompanyAmount").Save(&records[i]).Error
				if err != nil {
					return err
				}
				break
			default:
				err := tx.Model(&InsuranceRecord{}).Where("Id = ?", records[i].Id).Omit("StaffArchiveId", "StaffId", "AdjustDate").Save(&records[i]).Error
				if err != nil {
					return err
				}
			}
		}
		return nil
	})
}

func (inr *InsuranceRecord) Update() error {
	return model.DB().Model(&InsuranceRecord{}).Where("Id = ?", inr.Id).Omit("StaffArchiveId", "StaffId", "AdjustDate").Save(inr).Error
}

func (inr *InsuranceRecord) Destroy() error {
	return model.DB().Delete(inr).Error
}

func (inr *InsuranceRecord) DeleteAnnuityAndExtraMedial(id int64, scene string) error {
	if scene == util.ModularForAnnuity {
		return model.DB().Model(&InsuranceRecord{}).Where("Id = ?", id).Updates(map[string]interface{}{
			"IsAnnuity":            util.StatusForFalse,
			"AnnuityPersonAmount":  0,
			"AnnuityCompanyAmount": 0,
		}).Error
	}
	if scene == util.ModularForExtraMedical {
		return model.DB().Model(&InsuranceRecord{}).Where("Id = ?", id).Updates(map[string]interface{}{
			"IsExtraMedical":            util.StatusForFalse,
			"ExtraMedicalPersonAmount":  0,
			"ExtraMedicalCompanyAmount": 0,
		}).Error
	}
	return nil
}

// FundRecord 公积金记录
type FundRecord struct {
	model.PkId
	TopCorporationId  int64          `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:顶级机构ID"`                                      //顶级机构ID
	StaffArchiveId    int64          `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;uniqueIndex:unique_fund_staff_adjustDate;comment:人员档案Id"` //人员档案Id
	StaffId           int64          `json:"StaffId" gorm:"column:staffid;type:integer;comment:主数据人员ID"`                                                      //主数据人员ID
	AdjustDate        int64          `json:"AdjustDate" gorm:"column:adjustdate;type:integer;uniqueIndex:unique_fund_staff_adjustDate;comment:调整日期"`          //调整日期
	FundBase          int64          `json:"FundBase" gorm:"column:fundbase;type:integer;comment:公积金基数"`                                                      //公积金基数
	FundPersonRatio   int64          `json:"FundPersonRatio" gorm:"column:fundpersonratio;type:integer;comment:公积金个人比例"`                                      //公积金个人比例 10.5%=>1050  -1代表按固定金额缴纳
	FundPersonAmount  int64          `json:"FundPersonAmount" gorm:"column:fundpersonamount;type:integer;comment:公积金个人"`                                      //公积金个人
	FundCompanyRatio  int64          `json:"FundCompanyRatio" gorm:"column:fundcompanyratio;type:integer;comment:公积金单位比例"`                                    //公积金单位比例 10.5%=>1050  -1代表按固定金额缴纳
	FundCompanyAmount int64          `json:"FundCompanyAmount" gorm:"column:fundcompanyamount;type:integer;comment:公积金单位"`                                    //公积金单位
	SumAmount         int64          `json:"SumAmount" gorm:"column:sumamount;type:integer;comment:合计金额"`                                                     //合计金额
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"column:deletedat;type:timestamp;uniqueIndex:unique_fund_staff_adjustDate;comment:删除时间"`
	model.OpUser
	model.Timestamp
}

func (fr *FundRecord) BeforeCreate(tx *gorm.DB) error {
	fr.Id = model.Id()
	return nil
}

func (fr *FundRecord) Create(records []FundRecord) error {
	return model.DB().Create(&records).Error
}

func (fr *FundRecord) GetLatestFirstRecord(staffArchiveId int64) FundRecord {
	var record FundRecord
	model.DB().Model(&FundRecord{}).Where("StaffArchiveId = ?", staffArchiveId).Order("AdjustDate DESC").First(&record)

	return record
}

func (fr *FundRecord) List(groupId int64, startAt, endAt time.Time, paginator model.Paginator) ([]FundRecord, int64, error) {
	var rsp []FundRecord
	var totalCount int64
	tx := model.DB().Model(&FundRecord{}).Where("CreatedAt >= ? AND CreatedAt < ?", startAt, endAt).Where("TopCorporationId = ?", groupId)

	tx.Count(&totalCount)
	err := tx.Offset(paginator.Offset).Limit(paginator.Limit).Scan(&rsp).Error

	return rsp, totalCount, err
}

type FundRecordApi struct {
	FundRecord
	StaffName       string `json:"StaffName" gorm:"-"`       //员工姓名
	JobNumber       string `json:"JobNumber" gorm:"-"`       //员工工号
	CorporationName string `json:"CorporationName" gorm:"-"` //机构
}

func (fr *FundRecord) GetBy(staffIds []int64, startYear, endYear int64, paginator model.Paginator) ([]FundRecordApi, int64) {
	tx := model.DB().Model(&FundRecord{}).Where("StaffId IN ?", staffIds).Where("EXISTS (?)", model.DB().Table("staff_archives").Where("staff_archives.Id = fund_records.staffarchiveid"))

	if startYear > 0 {
		tx = tx.Where(" AdjustDate >= ?", startYear)
	}

	if endYear > 0 {
		tx = tx.Where(" AdjustDate <= ?", endYear)
	}

	var count int64
	tx.Count(&count)

	if paginator.Limit > 0 {
		tx = tx.Order("UpdatedAt DESC").Order("AdjustDate DESC").Order("StaffId DESC").Offset(paginator.Offset).Limit(paginator.Limit)
	}

	var records []FundRecordApi
	tx.Find(&records)

	return records, count
}

func (fr *FundRecord) GetStaffRecord(staffArchiveId int64, paginator model.Paginator) ([]FundRecord, int64) {
	var records []FundRecord
	var count int64
	tx := model.DB().Model(&FundRecord{}).Where("StaffArchiveId = ?", staffArchiveId).Count(&count)

	tx.Order("AdjustDate DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&records)

	return records, count
}

func (fr *FundRecord) BatchUpdate(records []FundRecord) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		for i := range records {
			err := tx.Model(&FundRecord{}).Where("Id = ?", records[i].Id).Omit("StaffArchiveId", "StaffId", "AdjustDate").Updates(&records[i]).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func (fr *FundRecord) Destroy() error {
	return model.DB().Delete(fr).Error
}
