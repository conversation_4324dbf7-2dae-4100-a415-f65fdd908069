package hr

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
	"time"
)

// StaffLaborContract 员工劳动合同
type StaffLaborContract struct {
	model.PkId
	StaffArchiveId  int64            `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID"`             //关联员工档案表主键ID
	StaffId         int64            `json:"StaffId" gorm:"column:staffid;type:integer;comment:员工Id" validate:"required"`             //员工Id 关联主数据人员主键
	StaffName       string           `json:"StaffName" gorm:"-"`                                                                      //员工姓名
	Code            string           `json:"Code" gorm:"column:code;type:varchar(64);comment:合同编号"`                                   //合同编号
	Type            int64            `json:"Type" gorm:"column:type;type:smallint;comment:合同类型" validate:"required"`                  //合同类型 1-无期限,2-固定期限
	StartAt         *model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:签订时间" validate:"required"`           //签订时间
	EndAt           *model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:到期时间"`                                   //到期时间
	Status          int64            `json:"Status" gorm:"column:status;type:smallint;default:0;comment:合同状态 3解除 (只存储解除，其他状态根据时间判断)"` //合同状态  1生效  2到期  3解除 (只存储解除，其他状态根据时间判断)
	HandleStaffId   int64            `json:"HandleStaffId" gorm:"column:handlestaffid;type:integer;comment:经办人"`                      //经办人
	HandleStaffName string           `json:"HandleStaffName" gorm:"column:handlestaffname;type:varchar;comment:经办人姓名"`                //经办人姓名
	//HandleStaffWorkPostId   int64            `json:"HandleStaffWorkPostId" gorm:"column:handlestaffworkpostid;type:bigint;comment:经办人岗位"` //经办人岗位
	//HandleStaffWorkPostName string           `json:"HandleStaffWorkPostName" gorm:"-"`                                                    //经办人岗位名称
	More              string           `json:"More" gorm:"column:more;type:varchar;comment:备注"`                                   //备注
	RelieveContractAt *model.LocalTime `json:"RelieveContractAt" gorm:"column:relievecontractat;type:timestamp;comment:解除劳动合同时间"` //解除劳动合同时间
	FilePath          model.JSON       `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                              //附件
	model.Timestamp
}

// ExpireMessageType 劳动合同即将到期消息提醒的消息类型
func (slc *StaffLaborContract) ExpireMessageType() string {
	return "staff_labor_contract_expire_message"
}

func (slc *StaffLaborContract) TableName() string {
	return "staff_labor_contracts"
}

func (slc *StaffLaborContract) BeforeCreate(tx *gorm.DB) (err error) {
	if slc.Id == 0 {
		slc.Id = model.Id()
	}

	return
}

func (slc *StaffLaborContract) Create(contracts []StaffLaborContract) error {
	return model.DB().Create(&contracts).Error
}

func (slc *StaffLaborContract) FindById(id int64) error {
	return model.DB().Model(&StaffLaborContract{}).Where("Id = ?", id).First(slc).Error
}

func (slc *StaffLaborContract) UpdateStatusToRelieveByStaffArchiveId(staffArchiveId, status int64, relieveAt time.Time) error {
	return model.DB().Model(&StaffLaborContract{}).Where("StaffArchiveId = ?", staffArchiveId).Updates(map[string]interface{}{
		"status":            status,
		"relievecontractat": relieveAt,
	}).Error
}

func (slc *StaffLaborContract) UpdateStatusToRelieveById(id, status int64, relieveAt time.Time) error {
	return model.DB().Model(&StaffLaborContract{}).Where("id = ?", id).Updates(map[string]interface{}{
		"status":            status,
		"relievecontractat": relieveAt,
	}).Error
}

func (slc *StaffLaborContract) Update() error {
	return model.DB().Omit("StaffArchiveId", "StaffId", "Status", "RelieveContractAt", "HandleStaffId", "HandleStaffName").Save(slc).Error
}

func (slc *StaffLaborContract) UpdateStatus(status int64) error {
	return model.DB().Model(&StaffLaborContract{}).Where("Id = ?", slc.Id).Update("Status", status).Error
}

func (slc *StaffLaborContract) GetBy(staffIds []int64, contractType, contractStatus, isDistinct int64, endAtStart, endAtEnd time.Time, paginator model.Paginator) ([]StaffLaborContract, int64) {
	tx := model.DB().Model(&StaffLaborContract{}).Where("StaffId IN ?", staffIds).Where("EXISTS (?)", model.DB().Table("staff_archives").Where("staff_archives.Id = staff_labor_contracts.staffarchiveid"))

	if contractType > 0 {
		tx = tx.Where("Type = ?", contractType)
	}

	if contractStatus == util.LaborContractRelieve {
		tx = tx.Where("Status = ?", contractStatus)
	}
	if contractStatus == util.LaborContractValid {
		tx = tx.Where("EndAt IS NULL OR EndAt >= ?", time.Now().Format(model.DateFormat))
	}
	if contractStatus == util.LaborContractExpiration {
		tx = tx.Where("EndAt IS NOT NULL AND EndAt < ?", time.Now().Format(model.DateFormat))
	}

	if !endAtStart.IsZero() {
		tx = tx.Where("EndAt >= ?", endAtStart.Format(model.DateFormat))
	}

	if !endAtEnd.IsZero() {
		tx = tx.Where("EndAt <= ?", endAtEnd.Format(model.DateFormat))
	}

	if isDistinct > 0 {
		tx = tx.Where("EndAt IN (?)", model.DB().Model(&StaffLaborContract{}).Select("MAX(EndAt)").Group("StaffId"))
	}

	var count int64
	tx.Count(&count)

	tx = tx.Order("StartAt DESC")

	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	var contracts []StaffLaborContract
	tx.Find(&contracts)

	return contracts, count
}

func (slc *StaffLaborContract) GetLatestByStaffArchiveId(staffArchiveId int64) StaffLaborContract {
	var contract StaffLaborContract
	model.DB().Model(&StaffLaborContract{}).Where("StaffArchiveId = ?", staffArchiveId).Order("StartAt DESC").First(&contract)

	return contract
}

func (slc *StaffLaborContract) RollBackStatus(tx *gorm.DB) error {
	return tx.Select("Status", "RelieveContractAt").Updates(&slc).Error
}
