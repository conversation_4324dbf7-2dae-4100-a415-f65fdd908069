package hr

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"reflect"
	"time"
)

type StaffPayrollReport struct {
	model.PkId
	model.Corporations
	StaffArchiveId int64  `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:人员档案Id"`
	StaffId        int64  `json:"StaffId" gorm:"column:staffid;type:integer;comment:主数据人员ID;uniqueIndex:staffpayrollreport_staffid_month_type"`
	StaffName      string `json:"StaffName" gorm:"column:staffname;type:varchar;comment:人员姓名"`
	JobNumber      string `json:"JobNumber" gorm:"column:jobnumber;type:varchar;comment:主数据人员工号;index:staffpayrollreport_jobnumber_month_type"`
	Month          string `json:"Month" gorm:"column:month;type:varchar;comment:月份 YYYY-MM;uniqueIndex:staffpayrollreport_staffid_month_type;index:staffpayrollreport_jobnumber_month_type"`
	LineId         int64  `json:"LineId" gorm:"column:lineid;comment:线路id;type:integer"`
	LineName       string `json:"LineName" gorm:"column:linename;comment:线路;type:varchar"`
	Type           int64  `json:"Type" gorm:"column:type;comment:类型 1稳定排版2小蓝巴3定制公交4村村通;type:integer;uniqueIndex:staffpayrollreport_staffid_month_type;index:staffpayrollreport_jobnumber_month_type"`
	IDCard         string `json:"IDCard" gorm:"column:idcard;type:varchar;comment:身份证"`
	// 基础信息
	RegisterTime                            int64 `json:"RegisterTime" gorm:"column:registertime;type:integer;comment:进公司年份"`
	CarLength                               int64 `json:"CarLength" gorm:"column:carlength;type:integer;comment:车长 毫米"`
	WorkType                                int64 `json:"WorkType" gorm:"column:worktype;type:integer;comment:班制 1 双板 2 单班 3混合班"`
	PlanWorkDayTotal                        int64 `json:"PlanWorkDayTotal" gorm:"column:planworkdaytotal;type:integer;comment:计划天数"`
	ActualDay                               int64 `json:"ActualDay" gorm:"column:actualday;type:integer;comment:实际天数 "`
	InnerWorkDay                            int64 `json:"InnerWorkDay" gorm:"column:innerworkday;type:integer;comment:班制内天数"`
	OutWorkDay                              int64 `json:"OutWorkDay" gorm:"column:outworkday;type:integer;comment:班制外天数"`
	PlanTotalCircle                         int64 `json:"PlanTotalCircle" gorm:"column:plantotalcircle;type:integer;comment:月计划总趟次"`
	TotalCircle                             int64 `json:"TotalCircle" gorm:"column:totalcircle;type:integer;comment:月实际总趟次"`
	FullSinglePassWorkTimeLength            int64 `json:"FullSinglePassWorkTimeLength" gorm:"column:fullsinglepassworktimelength;type:integer;comment:单趟工作总时间 秒"`
	FullWorkTimeLength                      int64 `json:"FullWorkTimeLength" gorm:"column:fullworktimelength;type:integer;comment:单趟运营时间 秒"`
	FullNotWorkTimeLength                   int64 `json:"FullNotWorkTimeLength" gorm:"column:fullnotworktimelength;type:integer;comment:单趟岗下时间 秒"`
	InFrequencyTypeCircleWorkTimeLength     int64 `json:"InFrequencyTypeCircleWorkTimeLength" gorm:"column:infrequencytypecircleworktimelength;type:integer;comment:班制内月实际运营时长 秒"`
	InFrequencyTypeCircleNotWorkTimeLength  int64 `json:"InFrequencyTypeCircleNotWorkTimeLength" gorm:"column:infrequencytypecirclenotworktimelength;type:integer;comment:班制内月岗下时长 秒"`
	InFrequencyTypeCircleTotalTimeLength    int64 `json:"InFrequencyTypeCircleTotalTimeLength" gorm:"column:infrequencytypecircletotaltimelength;type:integer;comment:班制内工作总时长 秒"`
	OutFrequencyTypeCircleWorkTimeLength    int64 `json:"OutFrequencyTypeCircleWorkTimeLength" gorm:"column:outfrequencytypecircleworktimelength;type:integer;comment:班制外月实际运营时长 秒"`
	OutFrequencyTypeCircleNotWorkTimeLength int64 `json:"OutFrequencyTypeCircleNotWorkTimeLength" gorm:"column:outfrequencytypecirclenotworktimelength;type:integer;comment:班制外月岗下时长 秒"`
	OutFrequencyTypeCircleTotalTimeLength   int64 `json:"OutFrequencyTypeCircleTotalTimeLength" gorm:"column:outfrequencytypecircletotaltimelength;type:integer;comment:班制外工作总时长 秒"`
	NightAddWorkTime                        int64 `json:"NightAddWorkTime" gorm:"column:nightaddworktime;type:integer;comment:夜班时间"`
	Holiday                                 int64 `json:"Holiday" gorm:"column:holiday;type:integer;comment:节假日加班天数"`
	HalfDayFrequency                        int64 `json:"HalfDayFrequency" gorm:"column:halfdayfrequency;type:integer;default:;comment:半天班次数 小蓝吧"`
	AllDayFrequency                         int64 `json:"AllDayFrequency" gorm:"column:alldayfrequency;type:integer;default:;comment:全天班次数 小蓝吧"`
	OverWorkHalfDay                         int64 `json:"OverWorkHalfDay" gorm:"column:overworkhalfday;type:integer;default:;comment:出勤加班天数（上/下午班） 小蓝吧"`
	OverWorkAllDay                          int64 `json:"OverWorkAllDay" gorm:"column:overworkallday;type:integer;default:;comment:出勤加班天数（整天班）小蓝吧"`
	ManeuverDay                             int64 `json:"ManeuverDay" gorm:"column:maneuverday;type:integer;default:;comment:机动天数  村村通"`
	WorkDay                                 int64 `json:"WorkDay" gorm:"column:workday;type:integer;default:;comment:上岗天数 村村通"`
	//BaseSalaryForm                  // 基本工资
	BaseSalary          int64 `calc:"1" json:"BaseSalary" gorm:"column:basesalary;type:integer;comment:基本工资"`
	DutySalary          int64 `calc:"1" json:"DutySalary" gorm:"column:dutysalary;type:integer;comment:岗位工资 村村通"`
	CarLengthAllowance  int64 `calc:"1" json:"CarLengthAllowance" gorm:"column:carlengthallowance;type:integer;comment:车长补贴"` // 稳定排版驾驶员字段
	LineAllowance       int64 `calc:"1" json:"LineAllowance" gorm:"column:lineallowance;type:integer;comment:线路班制补贴"`
	NightShiftAllowance int64 `calc:"1" json:"NightShiftAllowance" gorm:"column:nightshiftallowance;type:integer;comment:夜班补贴"`
	//OvertimePay                     // 超时工资明细 稳定排版驾驶员字段
	OnSitePayInSchedule   int64 `calc:"1" json:"OnSitePayInSchedule" gorm:"column:onsitepayinschedule;type:integer;comment:班制内递增岗上工资"`
	OffDutyPayInSchedule  int64 `calc:"1" json:"OffDutyPayInSchedule" gorm:"column:offdutypayinschedule;type:integer;comment:班制内递增岗下工资"`
	OffDutyPay            int64 `calc:"1" json:"OffDutyPay" gorm:"column:offdutypay;type:integer;comment:岗下工资 村村通"`
	WeekdayPay            int64 `calc:"1" json:"WeekdayPay" gorm:"column:weekdaypay;type:integer;comment:公休日工资 村村通"`
	DayPayInSchedule      int64 `calc:"1" json:"DayPayInSchedule" gorm:"column:daypayinschedule;type:integer;comment:班制内递增日工资"`
	OnSitePayOutSchedule  int64 `calc:"1" json:"OnSitePayOutSchedule" gorm:"column:onsitepayoutschedule;type:integer;comment:班制外递增岗上工资"`
	OffDutyPayOutSchedule int64 `calc:"1" json:"OffDutyPayOutSchedule" gorm:"column:offdutypayoutschedule;type:integer;comment:班制外递增岗下工资"`
	DayPayOutSchedule     int64 `calc:"1" json:"DayPayOutSchedule" gorm:"column:daypayoutschedule;type:integer;comment:班制外递增日工资"`
	WageUnder2h30m        int64 `calc:"1" json:"WageUnder2h30m" gorm:"column:wageunder2h30m;type:integer;comment:班制外2.5小时以内工资"`
	WageOver2h30m         int64 `calc:"1" json:"WageOver2h30m" gorm:"column:wageover2h30m;type:integer;comment:班制外2.5小时以外工资"`
	ExtendedHalfDayWage   int64 `calc:"1" json:"ExtendedHalfDayWage" gorm:"column:extendedhalfdaywage;type:integer;comment:班制外超过半天不足一天工资"`
	TotalPayInSchedule    int64 `json:"TotalPayInSchedule" gorm:"column:totalpayinschedule;type:integer;comment:班制内超时工资"`
	TotalPayOutSchedule   int64 `json:"TotalPayOutSchedule" gorm:"column:totalpayoutschedule;type:integer;comment:班制外超时工资"`
	HolidayOvertimePay    int64 `calc:"1" json:"HolidayOvertimePay" gorm:"column:holidayovertimepay;type:integer;comment:节假日加班工资"`
	OverWorkReward        int64 `calc:"1" json:"OverWorkReward" gorm:"column:overworkreward;type:integer;comment:加班费 定制公交"`
	ExtraWorkReward       int64 `calc:"1" json:"ExtraWorkReward" gorm:"column:extraworkreward;type:integer;comment:超产奖励 定制公交"`
	OverWorkHalfDayReward int64 `calc:"1" json:"OverWorkHalfDayReward" gorm:"column:overworkhalfdayreward;type:integer;comment:计划外加班（半天） 小蓝巴"`
	OverWorkAllDayReward  int64 `calc:"1" json:"OverWorkAllDayReward" gorm:"column:overworkalldayreward;type:integer;comment:计划外加班（全天） 小蓝巴"`
	//AssessmentIncentiveAward        // 考核激励奖
	SafeReward          int64 `calc:"1" json:"SafeReward" gorm:"column:safereward;type:integer;comment:安全奖励"`
	ServiceAssessReward int64 `calc:"1" json:"ServiceAssessReward" gorm:"column:serviceassessreward;type:integer;comment:服务考核奖励"`
	LineLeaderReward    int64 `calc:"1" json:"LineLeaderReward" gorm:"column:lineleaderreward;type:integer;comment:线组长奖金"`
	EnergySavingReward  int64 `calc:"1" json:"EnergySavingReward" gorm:"column:energysavingreward;type:integer;comment:节能奖励"` // 稳定排版驾驶员字段
	PassengerReward     int64 `calc:"1" json:"PassengerReward" gorm:"column:passengerreward;type:integer;default:;comment:人次激励奖 小蓝吧"`
	ExecutionAward      int64 `calc:"1" json:"ExecutionAward" gorm:"column:executionaward;type:integer;default:;comment:执行奖 定制公交"`
	//Allowance                       // 津贴
	AgeAllowance             int64 `calc:"1" json:"AgeAllowance" gorm:"column:ageallowance;type:integer;comment:年龄津贴"`
	VehicleSubsidyAllowance  int64 `calc:"1" json:"VehicleSubsidyAllowance" gorm:"column:vehiclesubsidyallowance;type:integer;comment:出车补贴"`
	CateringAllowance        int64 `calc:"1" json:"CateringAllowance" gorm:"column:cateringallowance;type:integer;comment:伙食补贴"`
	HighTemperatureAllowance int64 `calc:"1" json:"HighTemperatureAllowance" gorm:"column:hightemperatureallowance;type:integer;comment:高温补贴"`
	NightWorkAllowance       int64 `calc:"1" json:"NightWorkAllowance" gorm:"column:nightworkallowance;type:integer;comment:夜班补贴 定制公交"`
	//OtherForm                       // 其他明细
	AnnualAuditAmount       int64 `calc:"1" json:"AnnualAuditAmount" gorm:"column:annualauditamount;type:integer;comment:年审金额"`
	ReturnMoneyToOwner      int64 `calc:"1" json:"ReturnMoneyToOwner" gorm:"column:returnmoneytoowner;type:integer;comment:拾金不昧"`
	RecuperationLeave       int64 `calc:"1" json:"RecuperationLeave" gorm:"column:recuperationleave;type:integer;comment:疗休养"`
	SpecialLeave            int64 `calc:"1" json:"SpecialLeave" gorm:"column:specialleave;type:integer;comment:特殊假 病假、婚假、丧假、工伤 "`
	TrainAllowance          int64 `calc:"1" json:"TrainAllowance" gorm:"column:trainallowance;type:integer;comment:培训补贴"`
	CharterMissionAllowance int64 `calc:"1" json:"CharterMissionAllowance" gorm:"column:chartermissionallowance;type:integer;comment:包车补贴"`
	Other                   int64 `calc:"1" json:"Other" gorm:"column:other;type:integer;comment:其他"`
	WashCar                 int64 `calc:"1"  json:"WashCar" gorm:"column:washcar;type:integer;comment:洗车金额"`
	SalaryPayable           int64 `json:"SalaryPayable" gorm:"column:salarypayable;type:integer;comment:应发工资"`
	//WithholdingForm                 // 补扣
	PensionInsurance                 int64 `calc:"2" json:"PensionInsurance" gorm:"column:pensioninsurance;type:integer;comment:养老保险 支持手动编辑"`
	MedicalInsurance                 int64 `calc:"2" json:"MedicalInsurance" gorm:"column:medicalinsurance;type:integer;comment:医疗保险 支持手动编辑"`
	UnemploymentInsurance            int64 `calc:"2" json:"UnemploymentInsurance" gorm:"column:unemploymentinsurance;type:integer;comment:失业保险 支持手动编辑"`
	AccumulationFund                 int64 `calc:"2" json:"AccumulationFund" gorm:"column:accumulationfund;type:integer;comment:公积金 支持手动编辑"`
	IndividualIncomeTax              int64 `calc:"2" json:"IndividualIncomeTax" gorm:"column:individualincometax;type:integer;comment:个人所得税 导入"`
	LastMonthTax                     int64 `calc:"2" json:"LastMonthTax" gorm:"column:lastmonthtax;type:integer;comment:补还上月个税 导入"`
	OtherWithholding                 int64 `calc:"2" json:"OtherWithholding" gorm:"column:otherwithholding;type:integer;comment:其他代扣 支持手动编辑"`
	DormitoryElectricityFee          int64 `calc:"2" json:"DormitoryElectricityFee" gorm:"column:dormitoryelectricityfee;type:integer;comment:宿舍电费 支持手动编辑 导入"`
	PensionInsuranceWithholding      int64 `calc:"2" json:"PensionInsuranceWithholding" gorm:"column:pensioninsurancewithholding;type:integer;comment:养老保险补扣 导入"`
	UnemploymentInsuranceWithholding int64 `calc:"2" json:"UnemploymentInsuranceWithholding" gorm:"column:unemploymentinsurancewithholding;type:integer;comment:失业保险补扣 导入"`
	SafetyIncident                   int64 `calc:"2" json:"SafetyIncident" gorm:"column:safetyincident;type:integer;comment:安全事件扣款 支持手动编辑"`
	ActualPayment                    int64 `json:"ActualPayment" gorm:"column:actualpayment;type:integer;comment:实发工资"`
	//
	CorporationName string `json:"CorporationName" gorm:"-"`
	CorporationId   int64  `json:"CorporationId" gorm:"-"`
}

func (m *StaffPayrollReport) BeforeCreate(tx *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *StaffPayrollReport) Create() error {
	return model.DB().Create(&m).Error
}

func (m *StaffPayrollReport) Update() error {
	return model.DB().Select("*").Updates(&m).Error
}

func (m *StaffPayrollReport) GetById(Id int64) (data StaffPayrollReport, err error) {
	err = model.DB().First(&data, Id).Error
	return
}

func (m *StaffPayrollReport) GetByStaffIdAndMonth(StaffId, Type int64, Month string) (data StaffPayrollReport, err error) {
	err = model.DB().Model(&StaffPayrollReport{}).Where("StaffId = ?", StaffId).Where("Month = ?", Month).Where("Type=?", Type).First(&data).Error
	return
}
func (m *StaffPayrollReport) GetByJobNumberAndMonth(JobNumber, Month string, Type int64) (data StaffPayrollReport, err error) {
	err = model.DB().Model(&StaffPayrollReport{}).Where("JobNumber = ?", JobNumber).Where("Month = ?", Month).Where("Type = ?", Type).First(&data).Error
	return
}
func (m *StaffPayrollReport) List(CorporationIds []int64, Month string, StaffId int64, JobNumber string, LineId int64, Type int64, Paginator model.Paginator) (Data []StaffPayrollReport, TotalCount int64, err error) {
	db := model.DB().Model(&StaffPayrollReport{}).Scopes(model.WhereCorporations(CorporationIds))
	if Month != "" {
		db = db.Where("Month = ?", Month)
	}
	if StaffId != 0 {
		db = db.Where("StaffId = ?", StaffId)
	}
	if JobNumber != "" {
		db = db.Where("JobNumber LIKE ?", "%"+JobNumber+"%")
	}
	if LineId != 0 {
		db = db.Where("LineId = ?", LineId)
	}
	if Type != 0 {
		db = db.Where("Type = ?", Type)
	}
	db.Count(&TotalCount)
	if Paginator.Limit > 0 {
		db = db.Offset(Paginator.Offset).Limit(Paginator.Limit)
	}
	err = db.Find(&Data).Error
	return
}

// CalculateSalaryPayable 计算应发工资
func (m *StaffPayrollReport) CalculateSalaryPayable() {
	var SalaryPayable int64
	ve := reflect.ValueOf(m).Elem()
	te := reflect.TypeOf(m).Elem()
	for i := 0; i < te.NumField(); i++ {
		if te.Field(i).Tag.Get("calc") == "1" {
			SalaryPayable += ve.Field(i).Int()
		}
	}
	m.SalaryPayable = SalaryPayable
}

// CalculateActualPayment 计算实发工资
func (m *StaffPayrollReport) CalculateActualPayment() {
	var WithholdingMoney int64
	ve := reflect.ValueOf(m).Elem()
	te := reflect.TypeOf(m).Elem()
	for i := 0; i < te.NumField(); i++ {
		if te.Field(i).Tag.Get("calc") == "2" {
			WithholdingMoney += ve.Field(i).Int()
		}
	}
	m.ActualPayment = m.SalaryPayable - WithholdingMoney
}

// IsModifiable 是否可以修改
func (m *StaffPayrollReport) IsModifiable() bool {
	if m.Month == "" {
		realData, _ := m.GetById(m.Id)
		m.Month = realData.Month
	}
	// 如果是3月的工资 那么次月九号之后就无法修改 即4月九号之后无法修改
	dataMonthStr := m.Month
	dataMonth, _ := time.ParseInLocation("200601", dataMonthStr, time.Local)
	now := time.Now()
	modifyTime := time.Date(dataMonth.Year(), dataMonth.Month()+1, 9, 0, 0, 0, 0, time.Local)
	if now.Unix() >= modifyTime.Unix() {
		return false
	}
	return true
}

// UpdateEditInfo 修改可编辑字段
func (m *StaffPayrollReport) UpdateEditInfo() error {
	return model.DB().Model(&StaffPayrollReport{}).Where("id = ?", m.Id).Updates(map[string]interface{}{
		"PensionInsurance":        m.PensionInsurance,
		"MedicalInsurance":        m.MedicalInsurance,
		"UnemploymentInsurance":   m.UnemploymentInsurance,
		"AccumulationFund":        m.AccumulationFund,
		"OtherWithholding":        m.OtherWithholding,
		"DormitoryElectricityFee": m.DormitoryElectricityFee,
		"SafetyIncident":          m.SafetyIncident,
		"ActualPayment":           m.ActualPayment,
	}).Error
}

// UpdateImportInfo 修改导入字段
func (m *StaffPayrollReport) UpdateImportInfo() error {
	return model.DB().Model(&StaffPayrollReport{}).Where("id = ?", m.Id).Updates(map[string]interface{}{
		"LastMonthTax":                     m.LastMonthTax,
		"IndividualIncomeTax":              m.IndividualIncomeTax,
		"DormitoryElectricityFee":          m.DormitoryElectricityFee,
		"PensionInsuranceWithholding":      m.PensionInsuranceWithholding,
		"UnemploymentInsuranceWithholding": m.UnemploymentInsuranceWithholding,
		"ActualPayment":                    m.ActualPayment,
	}).Error
}
