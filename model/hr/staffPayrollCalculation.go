package hr

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

const (
	DriverType             int64 = iota // 司机类型
	DriverStableScheduling              // 稳定排班
	DriverBlueBus                       // 小蓝吧
	DriverCustomerLine                  // 定制公交
	DriverVillage                       // 村村通
)

type StaffPayrollCalculation struct {
	model.PkId
	model.Corporations
	StaffArchiveId int64  `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:人员档案Id"`
	StaffName      string `json:"StaffName" gorm:"column:staffname;type:varchar;comment:人员姓名"`
	IDCard         string `json:"IDCard" gorm:"column:idcard;type:varchar;default:;comment:身份证"`
	JobNumber      string `json:"JobNumber" gorm:"column:jobnumber;type:varchar;comment:主数据人员工号;"`
	LineName       string `json:"LineName" gorm:"column:linename;comment:线路;type:varchar"`
	StaffId        int64  `json:"StaffId" gorm:"column:staffid;type:integer;comment:主数据人员ID;uniqueIndex:staffpayrollcalc_staffid_month_lineid_type"`
	Month          string `json:"Month" gorm:"column:month;type:varchar;comment:月份 YYYY-MM;uniqueIndex:staffpayrollcalc_staffid_month_lineid_type"`
	LineId         int64  `json:"LineId" gorm:"column:lineid;type:integer;comment:线路id;uniqueIndex:staffpayrollcalc_staffid_month_lineid_type"`
	Type           int64  `json:"Type" gorm:"column:type;comment:类型 1稳定排版2小蓝巴3定制公交4村村通;type:integer;uniqueIndex:staffpayrollcalc_staffid_month_lineid_type"`
	IsFiled        int64  `json:"IsFiled" gorm:"column:isfiled;type:smallint;default:;comment:1未归档2已归档"`
	// 通用
	PlanWorkDay int64 `json:"PlanWorkDay" gorm:"column:planworkday;type:integer;default:;comment:计划出勤天数 *10"`
	WorkDay     int64 `json:"WorkDay" gorm:"column:workday;type:integer;default:;comment:实际出勤天数/上岗天数 *10"`
	ManeuverDay int64 `json:"ManeuverDay" gorm:"column:maneuverday;type:integer;default:;comment:机动天数 *10"` // 小蓝吧和村村通
	Holiday     int64 `json:"Holiday" gorm:"column:holiday;type:integer;default:;comment:节假日 *10"`
	// 小蓝吧
	HalfDayFrequency int64  `json:"HalfDayFrequency" gorm:"column:halfdayfrequency;type:integer;default:;comment:半天班次数 "`
	AllDayFrequency  int64  `json:"AllDayFrequency" gorm:"column:alldayfrequency;type:integer;default:;comment:全天班次数"`
	NightWorkTime    int64  `json:"NightWorkTime" gorm:"column:nightworktime;type:integer;default:;comment:夜班时间"`
	VehicleLength    int64  `json:"VehicleLength" gorm:"column:vehiclelength;type:integer;default:;comment:车长毫米"`
	PassengerNumber  int64  `json:"PassengerNumber" gorm:"column:passengernumber;type:integer;default:;comment:乘客数量"`
	OverWorkHalfDay  int64  `json:"OverWorkHalfDay" gorm:"column:overworkhalfday;type:integer;default:;comment:出勤加班天数（上/下午班）*10"`
	OverWorkAllDay   int64  `json:"OverWorkAllDay" gorm:"column:overworkallday;type:integer;default:;comment:出勤加班天数（整天班）*10"`
	Remark           string `json:"Remark" gorm:"column:remark;type:varchar;default:;comment:备注"`
	// 定制公交
	Mileage           int64 `json:"Mileage" gorm:"column:mileage;type:integer;default:;comment:里程 米"`
	CommutingShiftDay int64 `json:"CommutingShiftDay" gorm:"column:commutingshiftday;type:integer;default:;comment:通勤班天数 "`
	BusShiftDay       int64 `json:"BusShiftDay" gorm:"column:busshiftday;type:integer;default:;comment:公交班天数"`
	TempShiftDay      int64 `json:"TempShiftDay" gorm:"column:tempshiftday;type:integer;default:;comment:临时线路天数"`
	OverWorkOn5To6    int64 `json:"OverWorkOn5To6" gorm:"column:overworkon5to6;type:integer;default:;comment:5-6加班次数"`
	OverWorkOn6To18   int64 `json:"OverWorkOn6To18" gorm:"column:overworkon6to18;type:integer;default:;comment:6-18加班次数"`
	OverWorkOn18To23  int64 `json:"OverWorkOn18To23" gorm:"column:overworkon18to23;type:integer;default:;comment:18-23加班次数"`
	OverWorkOn23To5   int64 `json:"OverWorkOn23To5" gorm:"column:overworkon23to5;type:integer;default:;comment:23-5加班次数"`
	OverWorkOn18To24  int64 `json:"OverWorkOn18To24" gorm:"column:overworkon18to24;type:integer;default:;comment:18-24夜班次数"`
	OverWorkOn24To5   int64 `json:"OverWorkOn24To5" gorm:"column:overworkon24to5;type:integer;default:;comment:24-5早班次数"`

	// other
	CorporationName string `json:"CorporationName" gorm:"-"`
	CorporationId   int64  `json:"CorporationId" gorm:"-"`
}

func (m *StaffPayrollCalculation) BeforeCreate(tx *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *StaffPayrollCalculation) Create() error {
	return model.DB().Create(&m).Error
}

func (m *StaffPayrollCalculation) Update() error {
	return model.DB().Select("*").Updates(&m).Error
}

// ClearMonth 清空某个月的数据
func (m *StaffPayrollCalculation) ClearMonth(tx *gorm.DB, month string, fleetId, tp int64) error {
	return tx.Model(&StaffPayrollCalculation{}).Where("Month = ?", month).Where("FleetId = ?", fleetId).Where("Type = ?", tp).Delete(&StaffPayrollCalculation{}).Error
}

// File 归档某个车队某个月的数据
func (m *StaffPayrollCalculation) File(tx *gorm.DB, month string, fleetId int64) error {
	return tx.Model(&StaffPayrollCalculation{}).
		Where("Month = ?", month).
		Where("FleetId = ?", fleetId).
		Update("IsFiled", 2).Error
}

func (m *StaffPayrollCalculation) List(CorporationIds []int64, fleetId, lineId, tp, isFiled int64, name, month string, paginator model.Paginator) (data []StaffPayrollCalculation, totalCount int64, err error) {
	db := model.DB().Model(&StaffPayrollCalculation{}).Scopes(model.WhereCorporations(CorporationIds))
	if month != "" {
		db = db.Where("Month = ?", month)
	}
	if name != "" {
		db = db.Where("StaffName LIKE ?", "%"+name+"%")
	}
	if lineId != 0 {
		db = db.Where("LineId = ?", lineId)
	}
	if fleetId != 0 {
		db = db.Where("FleetId = ?", fleetId)
	}
	if tp != 0 {
		db = db.Where("Type = ?", tp)
	}
	if isFiled != 0 {
		db = db.Where("IsFiled = ?", isFiled)
	}

	db.Count(&totalCount)
	if paginator.Limit > 0 {
		db = db.Offset(paginator.Offset).Limit(paginator.Limit)
	}
	err = db.Find(&data).Error
	return
}

// FiledList 查询已归档数据
func (m *StaffPayrollCalculation) FiledList(tp int64, month string) (data []StaffPayrollCalculation, err error) {
	db := model.DB().Model(&StaffPayrollCalculation{}).
		Where("IsFiled = ?", 2).
		Where("Type = ?", tp).
		Where("Month = ?", month)
	if tp == DriverBlueBus {
		db = db.Order("PassengerNumber DESC")
	}
	err = db.Find(&data).Error
	return
}

// StaffPayrollCalculationFileInfo 工资计算表归档信息
type StaffPayrollCalculationFileInfo struct {
	model.PkId
	Month   string `json:"Month" gorm:"column:month;type:varchar;comment:月份 YYYY-MM;uniqueIndex:staffpayrollcal_month_fleetid"`
	FleetId int64  `json:"FleetId" gorm:"column:fleetid;type:bigint;comment:车队id;uniqueIndex:staffpayrollcal_month_fleetid"`
	IsFiled int64  `json:"IsFiled" gorm:"column:isfiled;type:smallint;default:;comment:1未归档2已归档"`
}

func (m *StaffPayrollCalculationFileInfo) BeforeCreate(tx *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *StaffPayrollCalculationFileInfo) Create() error {
	return model.DB().Create(&m).Error
}

func (m *StaffPayrollCalculationFileInfo) Update() error {
	return model.DB().Select("*").Updates(&m).Error
}

func (m *StaffPayrollCalculationFileInfo) TxCreate(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *StaffPayrollCalculationFileInfo) TxUpdate(tx *gorm.DB) error {
	return tx.Select("*").Updates(&m).Error
}

func (m *StaffPayrollCalculationFileInfo) Find(month string, fleetId int64) (err error) {
	err = model.DB().Model(&StaffPayrollCalculationFileInfo{}).Where("Month = ?", month).Where("FleetId = ?", fleetId).Find(&m).Error
	return
}

// File 归档某个车队某个月的数据
func (m *StaffPayrollCalculationFileInfo) File(tx *gorm.DB, month string, fleetId int64) error {

	return tx.Model(&StaffPayrollCalculationFileInfo{}).
		Where("Month = ?", month).
		Where("FleetId = ?", fleetId).
		Update("IsFiled", 2).Error
}
