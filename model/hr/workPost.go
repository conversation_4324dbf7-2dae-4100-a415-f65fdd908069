package hr

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

// WorkPost 岗位管理
type WorkPost struct {
	model.PkId
	model.Corporations
	Type   int64  `json:"Type" gorm:"column:type;type:integer;comment:岗位类型" validate:"required"`     //岗位类型 1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他,9修理工
	Name   string `json:"Name" gorm:"column:name;type:varchar(64);comment:岗位名称" validate:"required"` //岗位名称
	Attr   int64  `json:"Attr" gorm:"column:attr;type:integer;comment:岗位性质"`                         //岗位性质 1-部门领导（正职）,2-部门领导（副职）,3-普通员工
	Status int64  `json:"Status" gorm:"column:status;type:smallint;comment:岗位状态"`                    //岗位状态 1-有效  2-无效
	Code   string `json:"Code" gorm:"column:code;type:varchar;comment:岗位编号"`                         //岗位编号
	model.OpUser
	model.Timestamp
}

func (wp *WorkPost) BeforeCreate(tx *gorm.DB) (err error) {
	wp.Id = model.Id()
	return
}

func (wp *WorkPost) Create() error {
	return model.DB().Create(wp).Error
}

func (wp *WorkPost) Edit() error {
	return model.DB().Model(&WorkPost{}).
		Select("GroupId", "CompanyId", "BranchId", "DepartmentId", "FleetId", "Type", "Name", "Attr", "Code", "OpStaffId").
		Where("Id = ?", wp.Id).Updates(wp).Error
}

func (wp *WorkPost) ReturnCorporationId() int64 {
	if wp.FleetId > 0 {
		return wp.FleetId
	}
	if wp.DepartmentId > 0 {
		return wp.DepartmentId
	}
	if wp.BranchId > 0 {
		return wp.BranchId
	}
	if wp.CompanyId > 0 {
		return wp.CompanyId
	}
	if wp.GroupId > 0 {
		return wp.GroupId
	}

	return 0
}

type WorkPostApi struct {
	WorkPost
	CorporationId   int64  `json:"CorporationId"`
	CorporationName string `json:"CorporationName"`
}

func (wp *WorkPost) GetBy(corporationIds []int64, keyword string, postType, status int64, paginator model.Paginator) ([]WorkPostApi, int64) {
	var posts []WorkPostApi

	tx := model.DB().Model(&WorkPost{})
	if len(corporationIds) > 0 {
		tx = tx.Scopes(model.WhereCorporations(corporationIds))
	}

	if keyword != "" {
		tx = tx.Where("Name LIKE ?", "%"+keyword+"%")
	}

	if postType > 0 {
		tx = tx.Where("Type = ?", postType)
	}

	if status > 0 {
		tx = tx.Where("Status = ?", status)
	}

	var count int64
	tx.Count(&count)
	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}
	tx.Order("Attr ASC, CreatedAt DESC").Scan(&posts)
	return posts, count
}

func (wp *WorkPost) UpdateStatus() error {
	return model.DB().Model(&WorkPost{}).Where("Id = ?", wp.Id).Updates(map[string]interface{}{"Status": wp.Status, "OpUserId": wp.OpUserId}).Error
}

func (wp *WorkPost) PluckIdByName(name string) []int64 {
	var ids []int64
	model.DB().Model(&WorkPost{}).Where("Name LIKE ?", "%"+name+"%").Pluck("Id", &ids)

	return ids
}
func (wp *WorkPost) PluckNameByIds(ids []int64) []string {
	var names []string
	model.DB().Model(&WorkPost{}).Where("Id IN ?", ids).Pluck("Name", &names)

	return names
}

func (wp *WorkPost) FirstById(id int64) error {
	return model.DB().First(wp, id).Error
}
func (wp *WorkPost) FirstByCode(code string) WorkPost {
	var workPost WorkPost
	model.DB().Model(&WorkPost{}).Where("Code = ?", code).First(&workPost)
	return workPost
}

func (wp *WorkPost) GetById(id int64) (WorkPost, error) {
	var workPost WorkPost
	err := model.DB().First(&workPost, id).Error

	return workPost, err
}

func (wp *WorkPost) GetByName(name string) (WorkPost, error) {
	var workPost WorkPost
	err := model.DB().Where("Name = ?", name).First(&workPost).Error

	return workPost, err
}
