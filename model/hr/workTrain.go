package hr

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"time"
)

// WorkTrain 在岗培训管理
type WorkTrain struct {
	model.PkId
	TopCorporationId      int64                     `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:顶级机构ID"`        //顶级机构ID
	Title                 string                    `json:"Title" gorm:"column:title;type:varchar;comment:培训名称" validate:"required"`           //培训名称
	Content               string                    `json:"Content" gorm:"column:content;type:text;comment:培训内容"`                              //培训内容
	StartTime             model.LocalTime           `json:"StartTime" gorm:"column:starttime;type:timestamp;comment:培训时间" validate:"required"` //培训时间
	Location              string                    `json:"Location" gorm:"column:location;type:varchar;comment:地点" validate:"required"`       //地点
	Organizer             string                    `json:"Organizer" gorm:"column:organizer;type:varchar;comment:组织者" validate:"required"`    //组织者
	Teacher               string                    `json:"Teacher" gorm:"column:teacher;type:varchar;comment:讲师"`                             //讲师
	More                  string                    `json:"More" gorm:"column:more;type:varchar;comment:备注"`                                   //备注
	FilePath              model.JSON                `json:"FilePath" gorm:"column:filepath;type:json;comment:附件" validate:"required"`          //附件
	WorkTrainStaffs       []WorkTrainHasStaff       `json:"WorkTrainStaffs"`
	WorkTrainCorporations []WorkTrainHasCorporation `json:"WorkTrainCorporations"`
	model.OpUser
	model.Timestamp
}

func (wt *WorkTrain) BeforeCreate(tx *gorm.DB) error {
	wt.Id = model.Id()
	return nil
}

func (wt *WorkTrain) Create() error {
	return model.DB().Create(wt).Error
}

func (wt *WorkTrain) Update() error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&WorkTrain{}).Where("id = ?", wt.Id).Save(wt).Error
		if err != nil {
			return err
		}
		err = tx.Where("WorkTrainId = ?", wt.Id).Delete(&WorkTrainHasCorporation{}).Error
		if err != nil {
			return err
		}
		fmt.Printf("============WorkTrainCorporations=======%+v \r\n", wt.WorkTrainCorporations)
		err = tx.Model(wt).Session(&gorm.Session{FullSaveAssociations: true}).Association("WorkTrainCorporations").Replace(wt.WorkTrainCorporations)
		if err != nil {
			return err
		}

		return nil
	})
}

func (wt *WorkTrain) FindById(id int64) error {
	return model.DB().Model(&WorkTrain{}).Where("Id = ?", id).First(wt).Error
}

func (wt *WorkTrain) GetBy(topCorp int64, corporationIds []int64, keyword string, startAt, endAt time.Time, paginator model.Paginator) ([]WorkTrain, int64) {
	tx := model.DB().Model(&WorkTrain{}).Preload("WorkTrainStaffs").Preload("WorkTrainCorporations").Where("TopCorporationId = ?", topCorp)

	if len(corporationIds) > 0 {
		tx = tx.Where("Id IN (?)", model.DB().Model(&WorkTrainHasCorporation{}).Select("WorkTrainId").Where("CorporationId IN ?", corporationIds))
	}

	if keyword != "" {
		tx = tx.Where("Title LIKE ?", "%"+keyword+"%")
	}

	if !startAt.IsZero() {
		tx = tx.Where("StartTime >= ?", startAt.Format(model.TimeFormat))
	}

	if !endAt.IsZero() {
		tx = tx.Where("StartTime <= ?", endAt.Format(model.TimeFormat))
	}
	var count int64
	tx.Count(&count)
	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	var workTrains []WorkTrain
	tx.Order("StartTime DESC").Find(&workTrains)

	return workTrains, count
}

type ArchiveWorkTrain struct {
	WorkTrain
	Result   int64 `json:"Result" gorm:"column:result;type:smallint;"`     //考核结果 1通过 2不通过
	IsAttend int64 `json:"IsAttend" gorm:"column:isattend;type:smallint;"` //是否参加 1是 2否
}

func (wt *WorkTrain) GetWorkTrainByArchiveId(archiveId int64) []ArchiveWorkTrain {
	var workTrains []ArchiveWorkTrain
	model.DB().Model(&WorkTrainHasStaff{}).Select("work_trains.*, work_train_has_staffs.Result, work_train_has_staffs.IsAttend").Joins("LEFT JOIN work_trains ON work_trains.Id = work_train_has_staffs.WorkTrainId").
		Where("work_train_has_staffs.StaffArchiveId  = ?", archiveId).
		Order("work_trains.StartTime DESC").Scan(&workTrains)
	fmt.Printf("workTrains=============%+v", workTrains)

	for i := range workTrains {
		workTrains[i].WorkTrainCorporations = workTrains[i].GetCorporation(workTrains[i].Id)
	}

	return workTrains
}

func (wt *WorkTrain) GetCorporation(id int64) []WorkTrainHasCorporation {
	var corporations []WorkTrainHasCorporation
	model.DB().Model(wt).Association("WorkTrainCorporations").Find(&corporations)

	return corporations
}

// WorkTrainHasStaff 培训参与人员
type WorkTrainHasStaff struct {
	model.PkId
	WorkTrainId    int64 `json:"WorkTrainId" gorm:"column:worktrainid;type:bigint;uniqueIndex:work_train_has_staff_unique_index;comment:培训ID"`         //培训ID
	StaffArchiveId int64 `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;uniqueIndex:work_train_has_staff_unique_index;comment:人员档案ID"` //人员档案ID
	StaffId        int64 `json:"StaffId" gorm:"column:staffid;type:bigint;comment:主数据人员ID"`                                                            //主数据人员ID
	Result         int64 `json:"Result" gorm:"column:result;type:smallint;comment:考核结果"`                                                               //考核结果 1通过 2不通过
	IsAttend       int64 `json:"IsAttend" gorm:"column:isattend;type:smallint;comment:是否参加"`                                                           //是否参加 1是 2否
}

func (wts *WorkTrainHasStaff) BeforeCreate(tx *gorm.DB) error {
	wts.Id = model.Id()
	return nil
}
func (wts *WorkTrainHasStaff) GetStaffId(staffIds []int64, paginator model.Paginator) ([]WorkTrainHasStaff, int64) {
	tx := model.DB().Model(&WorkTrainHasStaff{}).Select("StaffId", "StaffArchiveId").Where("StaffId IN ?", staffIds).Group("StaffId, StaffArchiveId")

	var count int64
	tx.Count(&count)
	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	var workTrainHasStaff []WorkTrainHasStaff
	tx.Find(&workTrainHasStaff)

	return workTrainHasStaff, count
}

type WorkTrainStaffApi struct {
	WorkTrainHasStaff
	StaffName       string `json:"StaffName" gorm:"-"`
	JobNumber       string `json:"JobNumber" gorm:"-"`
	CorporationName string `json:"CorporationName" gorm:"-"`
	JobStatus       int64  `json:"JobStatus" gorm:"-"`
}

func (wts *WorkTrainHasStaff) GetWorkTrainStaff(workTrainId int64, result int64) []WorkTrainStaffApi {
	tx := model.DB().Model(&WorkTrainHasStaff{}).Where("WorkTrainId = ?", workTrainId)
	if result > 0 {
		tx = tx.Where("Result = ?", result)
	}

	var workTrainStaff []WorkTrainStaffApi
	tx.Find(&workTrainStaff)

	return workTrainStaff
}

func (wts *WorkTrainHasStaff) GetWorkTrainCount(staffId int64) int64 {
	var count int64
	model.DB().Model(&WorkTrainHasStaff{}).Where("StaffId = ?", staffId).Count(&count)
	return count
}

func (wts *WorkTrainHasStaff) GetAttendCount(staffId int64) int64 {
	var count int64
	model.DB().Model(&WorkTrainHasStaff{}).Where("StaffId = ? AND IsAttend = ?", staffId, util.StatusForTrue).Count(&count)
	return count
}

func (wts *WorkTrainHasStaff) Create(records []WorkTrainHasStaff) error {
	return model.DB().Create(&records).Error
}

func (wts *WorkTrainHasStaff) UpdateResult(workTrainId int64, staffIds []int64, result, isAttend int64) error {
	var status = make(map[string]interface{})
	if result > 0 {
		status["Result"] = result
	}

	if isAttend > 0 {
		status["IsAttend"] = isAttend
	}
	return model.DB().Session(&gorm.Session{AllowGlobalUpdate: true}).Model(&WorkTrainHasStaff{}).Where("WorkTrainId = ?", workTrainId).Where("StaffId IN ?", staffIds).UpdateColumns(status).Error
}

func (wts *WorkTrainHasStaff) DeleteStaff(workTrainId int64, staffIds []int64) error {
	return model.DB().Where("WorkTrainId = ?", workTrainId).Where("StaffId IN ?", staffIds).Delete(&WorkTrainHasStaff{}).Error
}

// WorkTrainHasCorporation 培训 部门
type WorkTrainHasCorporation struct {
	model.PkId
	WorkTrainId     int64  `json:"WorkTrainId" gorm:"column:worktrainid;type:bigint;uniqueIndex:work_train_has_corporation_unique_index;comment:培训ID"`     //培训ID
	CorporationId   int64  `json:"CorporationId" gorm:"column:corporationid;type:bigint;uniqueIndex:work_train_has_corporation_unique_index;comment:机构ID"` //机构ID
	CorporationName string `json:"CorporationName" gorm:"column:corporationname;type:varchar;comment:机构名称"`                                                //机构名称
}

func (wtc *WorkTrainHasCorporation) BeforeCreate(tx *gorm.DB) error {
	wtc.Id = model.Id()
	return nil
}
