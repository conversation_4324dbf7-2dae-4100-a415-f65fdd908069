package hr

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type ApplyUser struct {
	model.PkId
	IdStr            string `json:"IdStr" gorm:"-"`
	TopCorporationId int64  `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:顶级机构ID"`      //顶级机构ID
	CorporationId    int64  `json:"CorporationId" gorm:"column:corporationid;type:bigint;comment:所属机构ID"`            //所属机构ID
	WorkPostType     int64  `json:"WorkPostType" gorm:"column:workposttype;type:integer;comment:岗位类型"`               //岗位类型
	Name             string `json:"Name" gorm:"column:name;type:varchar(64);comment:姓名" validate:"required"`         //姓名
	Contact          string `json:"Contact" gorm:"column:contact;type:varchar(64);comment:联系方式" validate:"required"` //联系方式
	IsApply          int64  `json:"IsApply" gorm:"column:isapply;type:smallint;comment:是否提交申请 默认0否  1是"`             //是否提交申请 默认0否  1是
	model.OpUser
	model.Timestamp
}

// JoinCompanyApply 入职申请
type JoinCompanyApply struct {
	ApplyUser
	Gender             int64                         `json:"Gender" gorm:"column:gender;type:smallint;comment:性别"`                                        //性别
	NativePlace        string                        `json:"NativePlace" gorm:"column:nativeplace;type:varchar;comment:籍贯"`                               //籍贯
	ResidenceAttr      int64                         `json:"ResidenceAttr" gorm:"column:residenceattr;type:smallint;comment:户口性质 1-城镇,2-农村"`              //户口性质 1-城镇,2-农村
	Nation             string                        `json:"Nation" gorm:"column:nation;type:varchar(64);comment:民族"`                                     //民族
	IdentityId         string                        `json:"IdentityId" gorm:"column:identityid;type:varchar(64);comment:身份证" validate:"required,len=18"` //身份证
	HealthStatus       int64                         `json:"HealthStatus" gorm:"column:healthstatus;type:smallint;comment:健康情况"`                          //健康情况 1-健康,2-良好,3-一般,4-慢性病,5-残疾
	IsReversionSoldier int64                         `json:"IsReversionSoldier" gorm:"column:isreversionsoldier;type:smallint;comment:是否复退转军人"`           //是否复退转军人
	ReversionAt        *model.LocalTime              `json:"ReversionAt" gorm:"column:reversionat;type:timestamp;comment:复退转时间"`                          //复退转时间
	PoliticalIdentity  int64                         `json:"PoliticalIdentity" gorm:"column:politicalidentity;type:integer;comment:政治面貌"`                 //政治面貌  1-中国共产党员,2-中国共产党预备党员,3-中国共产主义青年团团员,4-其他党派人士,5-群众
	JoinPartyAt        *model.LocalTime              `json:"JoinPartyAt" gorm:"column:joinpartyat;type:timestamp;comment:入党时间"`                           //入党时间
	MarriageStatus     int64                         `json:"MarriageStatus" gorm:"column:marriagestatus;type:smallint;comment:婚姻状态"`                      //婚姻状态 1-未婚,2-已婚,3-离异
	BearStatus         int64                         `json:"BearStatus" gorm:"column:bearstatus;type:integer;comment:生育信息"`                               //生育信息 1-未育,2-一胎,3-二胎,4-三胎,5-三胎以上
	Address            string                        `json:"Address" gorm:"column:address;type:varchar;comment:家庭住址"`                                     //家庭住址
	DrivingCode        string                        `json:"DrivingCode" gorm:"column:drivingcode;type:varchar(64);comment:驾驶证号"`                         //驾驶证号
	DrivingModel       string                        `json:"DrivingModel" gorm:"column:drivingmodel;type:varchar(64);comment:准驾车型"`                       //准驾车型 多个用英文逗号隔开
	HighestEdu         int64                         `json:"HighestEdu" gorm:"column:highestedu;type:integer;comment:最高学历"`                               //最高学历 1-博士,2-博士在读,3-硕士,4-硕士在读,5-大学本科,6-大学专科,7-中等专科,8-职业高中,9-技工学校,10-普通高中,11-初中,12-小学,13-在职大专,14-在职本科,15-在职硕士,16-在职博士,17-其他
	BaseFilePath       model.JSON                    `json:"BaseFilePath" gorm:"column:basefilepath;type:json;comment:附件"`                                //附件  包含基础信息附件
	Educations         []JoinCompanyApplyEducation   `json:"Educations"`
	Skills             []JoinCompanyApplySkill       `json:"Skills"`
	FamilyMembers      []JoinCompanyApplyMember      `json:"FamilyMembers"`
	Certificates       []JoinCompanyApplyCertificate `json:"Certificates"`
	FileHttpPrefix     string                        `json:"FileHttpPrefix" gorm:"-"`

	ProbationEndAt   *model.LocalTime         `json:"ProbationEndAt" gorm:"-"`
	WorkPostType     int64                    `json:"WorkPostType" gorm:"-"`
	PartyFilePath    model.JSON               `json:"PartyFilePath" gorm:"-"`
	PositionalTitles []map[string]interface{} `json:"PositionalTitles" gorm:"-"`
	WorkPosts        []map[string]interface{} `json:"WorkPosts" gorm:"-"`
	LaborContracts   []map[string]interface{} `json:"LaborContracts" gorm:"-"`
	JobNumber        string                   `json:"JobNumber" gorm:"-"`
	JobStatus        int64                    `json:"JobStatus" gorm:"-"`
	JoinCompanyWay   int64                    `json:"JoinCompanyWay" gorm:"-"`
	JoinAt           *model.LocalTime         `json:"JoinAt" gorm:"-"`
	StartJobAt       *model.LocalTime         `json:"StartJobAt" gorm:"-"`
	RetireAt         *model.LocalTime         `json:"RetireAt" gorm:"-"`
}

func (jca *JoinCompanyApply) BeforeCreate(tx *gorm.DB) (err error) {
	jca.Id = model.Id()
	return
}

func (jca *JoinCompanyApply) Create() error {
	return model.DB().Create(jca).Error
}

func (jca *JoinCompanyApply) BatchCreate(records []JoinCompanyApply) error {
	return model.DB().Create(&records).Error
}

func (jca *JoinCompanyApply) UpdateAll() error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&JoinCompanyApply{}).Omit("Id", "WorkPostType", "CorporationId", "TopCorporationId", "LineId", "Contact").Where("Id = ?", jca.Id).Updates(jca).Error
		if err != nil {
			return err
		}
		err = tx.Model(jca).Association("Educations").Replace(jca.Educations)
		if err != nil {
			return err
		}

		for i := range jca.FamilyMembers {
			if jca.FamilyMembers[i].Relationship == "" {
				jca.FamilyMembers[i].Relationship = jca.FamilyMembers[i].Origin
			}
			if jca.FamilyMembers[i].Contact == "" {
				jca.FamilyMembers[i].Contact = jca.FamilyMembers[i].Phone
			}
		}

		err = tx.Model(jca).Association("FamilyMembers").Replace(jca.FamilyMembers)
		if err != nil {
			return err
		}

		err = tx.Model(jca).Association("Skills").Replace(jca.Skills)
		if err != nil {
			return err
		}

		err = tx.Model(jca).Association("Certificates").Replace(jca.Certificates)
		if err != nil {
			return err
		}

		return nil
	})
}

type ApplyUserApi struct {
	ApplyUser
	CorporationName string `json:"CorporationName"`
	LineName        string `json:"LineName"`
}

func (jca *JoinCompanyApply) GetBy(corporationIds []int64, keyword string, workPostType, isApply int64, start, end time.Time, paginator model.Paginator) ([]ApplyUserApi, int64) {
	var applies []ApplyUserApi
	tx := model.DB().Model(&JoinCompanyApply{}).Where("CorporationId IN ? OR TopCorporationId IN ?", corporationIds, corporationIds)
	if keyword != "" {
		tx = tx.Where("Name LIKE ?", "%"+keyword+"%")
	}

	if workPostType > 0 {
		tx = tx.Where("WorkPostType = ?", workPostType)
	}

	if isApply >= 0 {
		tx = tx.Where("IsApply = ?", isApply)
	}

	if !start.IsZero() {
		tx = tx.Where("UpdatedAt >= ?", start.Format(model.TimeFormat))
	}

	if !end.IsZero() {
		tx = tx.Where("UpdatedAt <= ?", end.Format(model.TimeFormat))
	}

	var count int64
	tx.Count(&count)

	tx.Order("UpdatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&applies)

	return applies, count
}

func (jca *JoinCompanyApply) FirstByContact(contact string) (ApplyUser, error) {
	var apply ApplyUser
	err := model.DB().Model(&JoinCompanyApply{}).Where("Contact = ?", contact).First(&apply).Error
	return apply, err
}

func (jca *JoinCompanyApply) FindById(id int64) error {
	return model.DB().Model(&JoinCompanyApply{}).
		Preload("Educations").
		Preload("FamilyMembers").
		Preload("Skills").
		Preload("Certificates").
		Where("Id = ?", id).First(jca).Error
}

func (jca *JoinCompanyApply) UpdateIsApply(id, status int64) error {
	return model.DB().Model(&JoinCompanyApply{}).Where("Id = ?", id).UpdateColumn("IsApply", status).Error
}

func (jca *JoinCompanyApply) Delete(id int64) error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Delete(&JoinCompanyApply{}, id).Error
		if err != nil {
			return err
		}

		err = tx.Where("JoinCompanyApplyId = ?", id).Delete(&JoinCompanyApplyCertificate{}).Error
		if err != nil {
			return err
		}

		err = tx.Where("JoinCompanyApplyId = ?", id).Delete(&JoinCompanyApplySkill{}).Error
		if err != nil {
			return err
		}

		err = tx.Where("JoinCompanyApplyId = ?", id).Delete(&JoinCompanyApplyMember{}).Error
		if err != nil {
			return err
		}

		err = tx.Where("JoinCompanyApplyId = ?", id).Delete(&JoinCompanyApplyEducation{}).Error
		if err != nil {
			return err
		}

		return nil
	})
}

// JoinCompanyApplyCertificate 从业资格证信息
type JoinCompanyApplyCertificate struct {
	model.PkId
	JoinCompanyApplyId int64           `json:"JoinCompanyApplyId" gorm:"column:joincompanyapplyid;type:bigint;comment:关联入职申请表主键ID"` //关联入职申请表主键ID
	Name               string          `json:"Name" gorm:"column:name;type:varchar(64);comment:资格证名称"`                              //资格证名称
	Code               string          `json:"Code" gorm:"column:code;type:varchar(64);comment:资格证编号"`                              //资格证编号
	StartAt            model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:发证日期"`                           //发证日期
	EndAt              model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:到期日期"`                               //到期日期
	AuthorizeOffice    string          `json:"AuthorizeOffice" gorm:"column:authorizeoffice;type:varchar(64);comment:核发机关"`         //核发机关
	FilePath           model.JSON      `json:"FilePath" gorm:"column:filepath;type:json;comment:从业资格证附件"`                           //从业资格证附件
	model.Timestamp
}

func (cer *JoinCompanyApplyCertificate) BeforeCreate(tx *gorm.DB) (err error) {
	cer.Id = model.Id()
	return
}

// JoinCompanyApplySkill 技能信息
type JoinCompanyApplySkill struct {
	model.PkId
	JoinCompanyApplyId int64      `json:"JoinCompanyApplyId" gorm:"column:joincompanyapplyid;type:bigint;comment:关联入职申请表主键ID"` //关联入职申请表主键ID
	Name               string     `json:"Name" gorm:"column:name;type:varchar(64);comment:技能级别"`                               //技能级别
	More               string     `json:"More" gorm:"column:more;type:varchar;comment:备注"`                                     //备注
	FilePath           model.JSON `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                                //附件
	model.Timestamp
}

func (sk *JoinCompanyApplySkill) BeforeCreate(tx *gorm.DB) (err error) {
	sk.Id = model.Id()
	return
}

// JoinCompanyApplyMember 家庭信息
type JoinCompanyApplyMember struct {
	model.PkId
	JoinCompanyApplyId int64  `json:"JoinCompanyApplyId" gorm:"column:joincompanyapplyid;type:bigint;comment:关联入职申请表主键ID"` //关联入职申请表主键ID
	Name               string `json:"Name" gorm:"column:name;type:varchar(64);comment:家庭联系人"`                              //家庭联系人
	Relationship       string `json:"Relationship" gorm:"column:relationship;type:varchar(64);comment:关系"`                 //关系
	Origin             string `json:"Origin" gorm:"-"`                                                                     //关系
	Contact            string `json:"Contact" gorm:"column:contact;type:varchar(64);comment:联系方式"`                         //联系方式
	Phone              string `json:"Phone" gorm:"-"`                                                                      //联系方式
	IsMainContact      int64  `json:"IsMainContact" gorm:"column:ismaincontact;type:smallint;comment:是否紧急联系人"`             //是否紧急联系人  1是  2不是
	model.Timestamp
}

func (mem *JoinCompanyApplyMember) BeforeCreate(tx *gorm.DB) (err error) {
	mem.Id = model.Id()
	return
}

// JoinCompanyApplyEducation 学历
type JoinCompanyApplyEducation struct {
	model.PkId
	JoinCompanyApplyId int64           `json:"JoinCompanyApplyId" gorm:"column:joincompanyapplyid;type:bigint;comment:关联入职申请表主键ID"` //关联入职申请表主键ID
	Edu                int64           `json:"Edu" gorm:"column:edu;type:integer;comment:学历"`                                       //学历 1-博士,2-博士在读,3-硕士,4-硕士在读,5-大学本科,6-大学专科,7-中等专科,8-职业高中,9-技工学校,10-普通高中,11-初中,12-小学,13-在职大专,14-在职本科,15-在职硕士,16-在职博士,17-其他
	School             string          `json:"School" gorm:"column:school;type:varchar;comment:毕业学校"`                               //毕业学校
	Major              string          `json:"Major" gorm:"column:major;type:varchar;comment:专业"`                                   //专业
	Type               int64           `json:"Type" gorm:"column:type;type:smallint;comment:学历类型"`                                  //学历类型  1全日制  2在职  学历类型为13、14、15、16时为在职类型，其他为全日制类型
	StartAt            model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始时间"`                           //开始时间
	EndAt              model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束时间"`                               //结束时间
	FilePath           model.JSON      `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                                //附件
	model.Timestamp
}

func (edu *JoinCompanyApplyEducation) BeforeCreate(tx *gorm.DB) (err error) {
	edu.Id = model.Id()
	return
}
