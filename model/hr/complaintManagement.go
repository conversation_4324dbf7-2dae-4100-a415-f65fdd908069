package hr

import (
	"app/org/scs/erpv2/api/model"
	commonModel "app/org/scs/erpv2/api/model/common"
	"fmt"
	"gorm.io/gorm"
	"strings"
	"time"
)

const (
	OrderType      = iota
	ComplaintType  // 投诉类 1
	SuggestionType // 建议类 2
)

// ComplaintManagement 投诉管理
type ComplaintManagement struct {
	model.PkId
	model.Timestamp
	model.Corporations
	model.OpUser
	Code                  string          `json:"Code" gorm:"column:code;type:varchar;default:;uniqueIndex:complaint_management_code;comment:编号 TS/JY+车队（椒江 01，黄岩 03,路桥 02）+日期+序号（3 位）"`
	FleetName             string          `json:"FleetName" gorm:"column:fleetname;type:varchar;default:;comment:"`
	OrderType             int64           `json:"OrderType" gorm:"column:ordertype;type:integer;default:;comment:客诉分类"`
	CodeNumber            int64           `json:"CodeNumber" gorm:"column:codenumber;type:integer;default:;comment:投诉编号"`
	LineId                int64           `json:"LineId" gorm:"column:lineid;type:bigint;default:;comment:线路id"`
	LineName              string          `json:"LineName" gorm:"column:linename;type:varchar;default:;comment:线路名称"`
	License               string          `json:"License" gorm:"column:license;type:varchar;default:;comment:车牌号码"`
	DriverId              int64           `json:"DriverId" gorm:"column:driverid;type:bigint;default:;comment:驾驶员id"`
	DriverName            string          `json:"DriverName" gorm:"column:drivername;type:varchar;default:;comment:驾驶员名称"`
	CallTime              model.LocalTime `json:"CallTime" gorm:"column:calltime;type:timestamp;default:;comment:来电时间"`
	CallPeople            string          `json:"CallPeople" gorm:"column:callpeople;type:varchar;default:;comment:来电人员"`
	TelephoneNumber       string          `json:"TelephoneNumber" gorm:"column:telephonenumber;type:varchar;default:;comment:来电号码"`
	Requirement           string          `json:"Requirement" gorm:"column:requirement;type:varchar;default:;comment:要求内容"`
	LevelOne              int64           `json:"LevelOne" gorm:"column:levelone;type:bigint;default:;comment:一级分类"`
	LevelOneKey           string          `json:"LevelOneKey" gorm:"column:levelonekey;type:varchar;default:;comment:一级分类名称"`
	LevelTwo              int64           `json:"LevelTwo" gorm:"column:leveltwo;type:bigint;default:;comment:二级分类"`
	LevelTwoKey           string          `json:"LevelTwoKey" gorm:"column:leveltwokey;type:varchar;default:;comment:二级分类名称"`
	Status                int64           `json:"Status" gorm:"column:status;type:smallint;default:;comment:处理情况 状态 1未办结 2已办结"`
	IssueStatus           int64           `json:"IssueStatus" gorm:"column:issuestatus;type:smallint;default:1;comment:下发状态 1未下发2已下发"`
	FromDictId            int64           `json:"FromDictId" gorm:"column:fromdictid;comment:信访来源id 投诉来源;type:bigint"`
	FromDictKey           string          `json:"FromDictKey" gorm:"column:fromdictkey;comment:信访来源名称 投诉来名称;type:varchar(50)"`
	PetitionWorkOrderId   int64           `json:"PetitionWorkOrderId" gorm:"column:petitionworkorderid;type:bigint;default:;comment:信访工单id"`
	PetitionWorkOrderCode string          `json:"PetitionWorkOrderCode" gorm:"column:petitionworkordercode;type:bigint;default:;comment:信访工单code"`
	//
	CurrentHandler string `json:"current_handler" gorm:"-"`
}

func (m *ComplaintManagement) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	m.GenerateCode()
	return nil
}

func (m *ComplaintManagement) GenerateCode() {
	var maxCodeNumber int64
	model.DB().Select("COALESCE(MAX(CodeNumber), 0)").Model(&ComplaintManagement{}).
		Where("TO_CHAR(CreatedAt, 'YYYY-MM-DD') = ?", time.Now().Format("2006-01-02")).Scan(&maxCodeNumber)
	m.CodeNumber = maxCodeNumber + 1
	dict := (&commonModel.Dict{}).FirstById(m.LevelTwo)
	dictNumber := dict.DictNumber
	if dictNumber == "" {
		if strings.Contains(m.FleetName, "椒江") {
			dictNumber = "01"
		} else if strings.Contains(m.FleetName, "黄岩") {
			dictNumber = "02"
		} else if strings.Contains(m.FleetName, "路桥") {
			dictNumber = "03"
		}
	}
	var first string
	if m.OrderType == ComplaintType {
		first = "TS"
	} else if m.OrderType == SuggestionType {
		first = "JY"
	}
	m.Code = fmt.Sprintf("%s%s%s%s", first, dictNumber, time.Now().Format("20060102"), fmt.Sprintf("%03d", m.CodeNumber))
}

func (m *ComplaintManagement) Create() error {
	return model.DB().Create(&m).Error
}

func (m *ComplaintManagement) Updates() error {
	return model.DB().Updates(&m).Error
}

func (m *ComplaintManagement) TxUpdates(tx *gorm.DB) error {
	return tx.Updates(&m).Error
}

func (m *ComplaintManagement) Delete(id int64) error {
	return model.DB().Delete(&ComplaintManagement{}, id).Error
}

func (m *ComplaintManagement) GetById(id int64) error { return model.DB().First(&m, id).Error }

func (m *ComplaintManagement) List(qs *model.Qs, corporationIds []int64, paginator model.Paginator) (data []ComplaintManagement, totalCount int64, err error) {
	db := qs.Format().Model(&ComplaintManagement{})
	if corporationIds != nil && len(corporationIds) > 0 {
		db = db.Scopes(model.WhereCorporations(corporationIds))
	}
	db.Count(&totalCount)
	err = db.Scopes(model.PaginationScope(paginator)).Order("Code DESC").Find(&data).Error
	return
}

func (m *ComplaintManagement) queryOne(qs *model.Qs, corporationIds []int64) (data ComplaintManagement, err error) {
	list, _, err := m.List(qs, corporationIds, model.NoPagination)
	if list != nil && len(list) > 0 {
		data = list[0]
	}
	return
}
