package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type ApplyLeaveRecord struct {
	model.PkId
	model.Corporations
	Code           string          `json:"Code" gorm:"column:code;type:varchar(255);comment:请假编号"`
	StaffArchiveId int64           `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:人员档案Id"` //人员档案Id
	StaffId        int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:主数据人员ID;"`
	StaffName      string          `json:"StaffName" gorm:"column:staffname;type:varchar;comment:人员姓名"`
	JobNumber      string          `json:"JobNumber" gorm:"column:jobnumber;type:varchar;comment:主数据人员工号;"`
	LeaveType      int64           `json:"LeaveType" gorm:"column:leavetype;type:smallint;comment:请假类型 1年休假 2调休假  3事假 4病假 5婚假 6产假 7陪产假 8丧假 9疗休养 10工伤 11车队值班（机动）12待岗 13停岗"`
	LeaveDateType  int64           `json:"LeaveDateType" gorm:"column:leavedatetype;type:smallint;comment:请假日期类型 1连续  2多日;default:1"`
	StartAt        model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:请假开始时间"`
	EndAt          model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment请假结束时间:"`
	DayCount       int64           `json:"DayCount" gorm:"column:daycount;type:integer;comment:请假天数 单位：天数*10"`
	LeaveReason    string          `json:"LeaveReason" gorm:"column:leavereaseon;type:text;comment:请假原因"`
	LeaveFile      model.JSON      `json:"LeaveFile" gorm:"column:leavefile;type:json;comment:请假附件"`
	ApplyStatus    int64           `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;comment:审批状态 1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃;default:0"`
	From           string          `json:"From" gorm:"column:from;type:varchar;comment:请假来源：erp,ipoc(场站云)"`
	More           string          `json:"More" gorm:"column:more;type:text;comment:请假备注"`
	LeaveProof     model.JSON      `json:"LeaveProof" gorm:"column:leaveproof;type:json;comment:请假凭证"`
	model.OpUser
	model.Timestamp

	CorporationName string                 `json:"CorporationName" gorm:"-"`
	CorporationId   int64                  `json:"CorporationId" gorm:"-"`
	Dates           []ApplyLeaveRecordDate `json:"Dates" gorm:"-"`
	LeaveDates      []string               `json:"LeaveDates" gorm:"-"`
	ProcessTitle    string                 `json:"ProcessTitle" gorm:"-"`
	FormInstanceId  int64                  `json:"FormInstanceId" gorm:"-"`
	ProcessId       string                 `json:"ProcessId" gorm:"-"`
	CurrentHandler  string                 `json:"CurrentHandler" gorm:"-"`
}

func (alr *ApplyLeaveRecord) TableName() string {
	return "apply_leave_records"
}
func (alr *ApplyLeaveRecord) MessageType() string {
	return "apply_leave_records"
}
func (alr *ApplyLeaveRecord) ApplyStatusFieldName() string {
	return "applystatus"
}

func (alr *ApplyLeaveRecord) BeforeCreate(db *gorm.DB) error {
	alr.Id = model.Id()
	//请假编号
	var count int64
	db.Model(&ApplyLeaveRecord{}).Where("Code LIKE ?", time.Now().Format("20060102")+"%").Count(&count)
	alr.Code = fmt.Sprintf("%v%04d", time.Now().Format("20060102"), count+1)
	return nil
}

func (alr *ApplyLeaveRecord) Create() error {
	return model.DB().Create(&alr).Error
}

func (alr *ApplyLeaveRecord) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&alr).Error
}

func (alr *ApplyLeaveRecord) FirstBy(id int64) ApplyLeaveRecord {
	var record ApplyLeaveRecord
	model.DB().Model(&ApplyLeaveRecord{}).Where("Id=?", id).First(&record)
	return record
}

func (alr *ApplyLeaveRecord) GetAllBy(staffIds []int64, leaveType int64, startAt, endAt time.Time) []ApplyLeaveRecord {
	var records []ApplyLeaveRecord
	var totalCount int64
	tx := model.DB().Model(&ApplyLeaveRecord{}).Where("StaffId IN ?", staffIds)

	if !startAt.IsZero() || !endAt.IsZero() {
		sub := model.DB().Model(&ApplyLeaveRecordDate{}).Select("ApplyLeaveRecordId")
		if !startAt.IsZero() {
			sub.Where("Date >= ?", startAt.Format(model.DateFormat))
		}
		if !endAt.IsZero() {
			sub.Where("Date <= ?", endAt.Format(model.DateFormat))
		}
		tx = tx.Where("Id IN (?)", sub)
	}

	if leaveType > 0 {
		tx = tx.Where("LeaveType = ?", leaveType)
	}

	tx.Count(&totalCount)

	tx.Order("StartAt DESC").Scan(&records)

	return records
}

func (alr *ApplyLeaveRecord) GetBy(corporationIds []int64, code, staffName, jobNumber string, leaveType int64, startAt, endAt time.Time, paginator model.Paginator) ([]ApplyLeaveRecord, int64) {
	var records []ApplyLeaveRecord
	var totalCount int64
	tx := model.DB().Model(&ApplyLeaveRecord{}).Scopes(model.WhereCorporations(corporationIds))

	if staffName != "" {
		tx = tx.Where("StaffName LIKE ?", "%"+staffName+"%")
	}

	if jobNumber != "" {
		tx = tx.Where("JobNumber LIKE ?", "%"+jobNumber+"%")
	}

	if !startAt.IsZero() || !endAt.IsZero() {
		sub := model.DB().Model(&ApplyLeaveRecordDate{}).Select("ApplyLeaveRecordId")
		if !startAt.IsZero() {
			sub.Where("Date >= ?", startAt.Format(model.DateFormat))
		}
		if !endAt.IsZero() {
			sub.Where("Date <= ?", endAt.Format(model.DateFormat))
		}
		tx = tx.Where("Id IN (?)", sub)
	}

	if leaveType > 0 {
		tx = tx.Where("LeaveType = ?", leaveType)
	}

	if code != "" {
		tx = tx.Where("Code LIKE ?", "%"+code+"%")
	}

	tx.Count(&totalCount)

	tx.Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&records)

	return records, totalCount
}

func (alr *ApplyLeaveRecord) DashboardGetBy(processTitle, code, userName, handlerName string, applyStatus, formStep int64, paginator model.Paginator) ([]ApplyLeaveRecord, int64) {
	var records []ApplyLeaveRecord
	tx := model.DB().Model(&ApplyLeaveRecord{})

	if processTitle != "" {
		tx.Where("Id IN (?)", model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ?", config.StaffLeaveFormTemplate).Where("Title LIKE ?", "%"+processTitle+"%"))
	}

	if code != "" {
		tx.Where("Code LIKE ?", "%"+code+"%")
	}

	if userName != "" {
		tx.Where("OpUserName LIKE ?", "%"+userName+"%")
	}

	if handlerName != "" {
		subQuery := model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ? AND CurrentHandlerUserName LIKE ?", config.StaffLeaveFormTemplate, "%"+handlerName+"%")
		tx.Where("Id IN (?)", subQuery)
	}

	if formStep > 0 {
		tx.Where("FormStep = ?", formStep)
	}

	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	}

	var count int64
	tx.Count(&count)

	tx.Order("CreatedAt desc").Offset(paginator.Offset).Limit(paginator.Limit).Find(&records)
	return records, count
}

type StaffLeaveTypeCount struct {
	LeaveType int64 `json:"LeaveType" gorm:"column:leavetype;"` //请假类型 1年休假 2调休假  3事假 4病假 5婚假 6产假 7陪产假 8丧假 9疗休养 10工伤 11车队值班（机动）12待岗 13停岗
	DayCount  int64 `json:"DayCount" gorm:"column:daycount;"`   //天数
}

func (alr *ApplyLeaveRecord) StaffLeaveCount(staffId int64, startAt, endAt time.Time) []StaffLeaveTypeCount {
	var records []StaffLeaveTypeCount
	var subQuery = fmt.Sprintf("(SELECT COUNT(*) FROM apply_leave_record_dates WHERE Date >= '%s' AND Date <= '%s' AND apply_leave_records.id = apply_leave_record_dates.applyleaverecordid)", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))
	model.DB().Model(&ApplyLeaveRecord{}).Select("LeaveType", fmt.Sprintf("SUM(%s) AS DayCount", subQuery)).Where("StaffId = ? AND ApplyStatus = ?", staffId, util.ApplyStatusForDone).Group("leavetype").Scan(&records)
	return records
}

type ApplyLeaveRecordDate struct {
	model.PkId
	ApplyLeaveRecordId int64           `json:"ApplyLeaveRecordId" gorm:"column:applyleaverecordid;type:bigint;comment:请假记录ID;index:apply_leave_record_date_id_index"`
	Date               model.LocalTime `json:"Date" gorm:"column:date;type:timestamp;comment:日期"`
	model.Timestamp
}

func (alr *ApplyLeaveRecordDate) BeforeCreate(db *gorm.DB) error {
	alr.Id = model.Id()
	return nil
}

func (alr *ApplyLeaveRecordDate) TransactionCreate(tx *gorm.DB, dates []ApplyLeaveRecordDate) error {
	return tx.Create(&dates).Error
}

func (alr *ApplyLeaveRecordDate) GetByLeaveRecordId(leaveRecordId int64) []ApplyLeaveRecordDate {
	var dates []ApplyLeaveRecordDate
	model.DB().Model(&ApplyLeaveRecordDate{}).Where("ApplyLeaveRecordId = ?", leaveRecordId).Order("Date asc").Find(&dates)

	return dates
}

func (alr *ApplyLeaveRecordDate) GetDriverLeaveRecord(staffId, leaveType int64, reportAt time.Time, exceptLeaveTypes []int64) ApplyLeaveRecordDate {
	//var record ApplyLeaveRecord
	//.Where("StartAt >= ? AND EndAt <= ?", reportAt.Format(model.DateFormat), reportAt.Format(model.DateFormat))

	sub := model.DB().Model(&ApplyLeaveRecord{}).Select("id").Where("StaffId =?", staffId)

	if leaveType > 0 {
		sub = sub.Where("LeaveType = ?", leaveType)
	}

	if len(exceptLeaveTypes) > 0 {
		sub = sub.Where("LeaveType NOT IN ?", exceptLeaveTypes)
	}

	var leaveRecordDate ApplyLeaveRecordDate
	model.DB().Model(&ApplyLeaveRecordDate{}).Where("Date = ? AND ApplyLeaveRecordId IN (?)", reportAt.Format(model.DateFormat), sub).First(&leaveRecordDate)

	return leaveRecordDate
}

type LeaveManagement struct {
	model.PkId
	model.Corporations
	StaffArchiveId int64           `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:人员档案Id"` //人员档案Id
	StaffId        int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:主数据人员ID;uniqueIndex:leave_management_unique_index;"`
	StaffName      string          `json:"StaffName" gorm:"column:staffname;type:varchar;comment:人员姓名"`
	JobNumber      string          `json:"JobNumber" gorm:"column:jobnumber;type:varchar;comment:主数据人员工号;"`
	WorkPostType   int64           `json:"WorkPostType" gorm:"column:workposttype;type:integer;comment:岗位类型 3司机"`
	CalcStartTime  model.LocalTime `json:"CalcStartTime" gorm:"column:calcstarttime;type:timestamp;comment:计算年休假开始的时间 司机：入职时间，其他人员：工作时间"`
	Year           int64           `json:"Year" gorm:"column:year;type:integer;comment:年度;uniqueIndex:leave_management_unique_index;"`
	TotalDay       int64           `json:"TotalDay" gorm:"column:totalday;type:integer;default:0;comment:总天数"`
	UsedDay        int64           `json:"UsedDay" gorm:"column:usedday;type:integer;default:0;comment:被使用的天数"`
	LeaveType      int64           `json:"LeaveType" gorm:"column:leavetype;type:smallint;comment:假期类型 1年假;uniqueIndex:leave_management_unique_index;"`
	Status         int64           `json:"Status" gorm:"column:status;type:smallint;comment:状态  1可使用  2已作废"`
	GiveDate       model.LocalTime `json:"GiveDate" gorm:"column:givedate;type:timestamp;comment:发放日期"`
	model.Timestamp

	CorporationName string          `json:"CorporationName" gorm:"-"`
	JoinAt          model.LocalTime `json:"JoinAt" gorm:"-"`
	StartJobAt      model.LocalTime `json:"StartJobAt" gorm:"-"`
	WorkAge         int64           `json:"WorkAge" gorm:"-"` // 工龄
}

func (lm *LeaveManagement) BeforeCreate(db *gorm.DB) error {
	lm.Id = model.Id()
	return nil
}

func (lm *LeaveManagement) Create() error {
	return model.DB().Create(&lm).Error
}

func (lm *LeaveManagement) Update() error {
	return model.DB().Select("TotalDay", "GiveDate", "WorkPostType", "CalcStartTime").Updates(&lm).Error
}

func (lm *LeaveManagement) UpdateColumn(column string, value interface{}) error {
	return model.DB().Model(&LeaveManagement{}).Where("Id = ?", lm.Id).Update(column, value).Error
}

// SetStatusByBefore 设置年休假作废
func (lm *LeaveManagement) SetStatusByBefore(year int64, leaveType int64) error {
	return model.DB().Model(&LeaveManagement{}).Where("Year < ? AND LeaveType = ?", year, leaveType).Update("Status", util.StatusForFalse).Error
}

func (lm *LeaveManagement) IsExistByStaffIdAndYear(staffId, year int64) bool {
	var count int64
	model.DB().Model(&LeaveManagement{}).Where("StaffId = ? AND Year = ?", staffId, year).Count(&count)
	return count > 0
}

func (lm *LeaveManagement) GetByStaffIdAndYear(staffId, year int64) LeaveManagement {
	var record LeaveManagement
	model.DB().Model(&LeaveManagement{}).Where("StaffId = ? AND Year = ?", staffId, year).First(&record)
	return record
}
func (lm *LeaveManagement) GetStaffLeaveByStatus(staffId, status int64) LeaveManagement {
	var record LeaveManagement
	model.DB().Model(&LeaveManagement{}).Where("StaffId = ? AND Status = ?", staffId, status).First(&record)
	return record
}

func (lm *LeaveManagement) GetBy(corporationIds []int64, code, staffName, jobNumber string, leaveType, workPostType, year int64, paginator model.Paginator) ([]LeaveManagement, int64) {
	var records []LeaveManagement
	var totalCount int64
	tx := model.DB().Model(&LeaveManagement{}).Scopes(model.WhereCorporations(corporationIds))
	if code != "" {
		tx = tx.Where("Code LIKE ?", "%"+code+"%")
	}
	if staffName != "" {
		tx = tx.Where("StaffName LIKE ?", "%"+staffName+"%")
	}
	if jobNumber != "" {
		tx = tx.Where("JobNumber LIKE ?", "%"+jobNumber+"%")
	}

	if leaveType > 0 {
		tx = tx.Where("LeaveType = ?", leaveType)
	}

	if workPostType > 0 {
		tx = tx.Where("WorkPostType = ?", workPostType)
	}

	if year > 0 {
		tx = tx.Where("Year = ?", year)
	}

	tx.Count(&totalCount)

	tx.Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&records)

	return records, totalCount
}

func (lm *LeaveManagement) StaffLatestAnnualLeave(staffId, leaveType int64, at time.Time) LeaveManagement {
	var record LeaveManagement
	model.DB().Model(&LeaveManagement{}).Where("StaffId = ? AND LeaveType = ? AND GiveDate <= ?", staffId, leaveType, at.Format(model.DateFormat)).Order("GiveDate DESC").First(&record)
	return record
}

type LeaveRuleSetting struct {
	model.PkId
	TopCorporationId int64      `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;default:0;comment:顶级机构ID;uniqueIndex:leave_setting_unique_index;"`
	LeaveType        int        `json:"LeaveType" gorm:"column:leavetype;type:smallint;comment:假期类型 1年假;uniqueIndex:leave_setting_unique_index;"`
	SettingItem      model.JSON `json:"SettingItem" gorm:"column:settingitem;type:json;comment:配置项"`
	model.OpUser
	model.Timestamp
}

type AnnualLeaveSettingItem struct {
	Year1To10    int64  `json:"Year1To10"`    //工龄[1,10)年年休假天数
	Year10To20   int64  `json:"Year10To20"`   //工龄[10,20)年年休假天数
	YearOver20   int64  `json:"YearOver20"`   //工龄[20,...)年年休假天数
	GiveMonthDay string `json:"GiveMonthDay"` //发放日期  格式：05-20
}

func (ls *LeaveRuleSetting) BeforeCreate(db *gorm.DB) error {
	ls.Id = model.Id()
	return nil
}

func (ls *LeaveRuleSetting) Create() error {
	return model.DB().Create(&ls).Error
}

func (ls *LeaveRuleSetting) Update() error {
	var count int64
	model.DB().Model(&LeaveRuleSetting{}).Where("TopCorporationId = ? AND LeaveType = ?", ls.TopCorporationId, ls.LeaveType).Count(&count)
	if count > 0 {
		return model.DB().Where("TopCorporationId = ? AND LeaveType = ?", ls.TopCorporationId, ls.LeaveType).Select("SettingItem").Updates(&ls).Error
	} else {
		return ls.Create()
	}
}

func (ls *LeaveRuleSetting) FirstBy(topCorporationId, leaveType int64) LeaveRuleSetting {
	var setting LeaveRuleSetting
	model.DB().Model(&LeaveRuleSetting{}).Where("TopCorporationId = ? AND LeaveType = ?", topCorporationId, leaveType).First(&setting)

	return setting
}
