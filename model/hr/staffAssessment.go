package hr

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type StaffAssessment struct {
	model.PkId
	StaffArchiveId int64 `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;uniqueIndex:staff_assessment_unique_index;comment:人员档案Id"` //人员档案Id
	StaffId        int64 `json:"StaffId" gorm:"column:staffid;type:integer;comment:主数据人员ID"`                                                       //主数据人员ID
	Year           int64 `json:"Year" gorm:"column:year;type:integer;uniqueIndex:staff_assessment_unique_index;comment:考核年度"`                      //考核年度
	Degree         int64 `json:"Degree" gorm:"column:degree;type:smallint;comment:考核等级"`                                                           //考核等级  1-合格，2-不合格，3-良好，4-优秀
	model.OpUser
	model.Timestamp
}

func (sa *StaffAssessment) BeforeCreate(tx *gorm.DB) error {
	sa.Id = model.Id()
	return nil
}

func (sa *StaffAssessment) Create(records []StaffAssessment) error {
	return model.DB().Create(&records).Error
}

type StaffAssessmentApi struct {
	StaffAssessment
	StaffName       string `json:"StaffName" gorm:"-"`
	JobNumber       string `json:"JobNumber" gorm:"-"`
	CorporationName string `json:"CorporationName" gorm:"-"`
}

func (sa *StaffAssessment) GetBy(staffIds, degrees []int64, startYear, endYear int64, paginator model.Paginator) ([]StaffAssessmentApi, int64) {
	tx := model.DB().Model(&StaffAssessment{}).Where("StaffId IN ?", staffIds).Where("EXISTS (?)", model.DB().Table("staff_archives").Where("staff_archives.Id = staff_assessments.staffarchiveid"))

	if startYear > 0 {
		tx = tx.Where("Year >= ?", startYear)
	}
	if endYear > 0 {
		tx = tx.Where("Year <= ?", endYear)
	}

	if len(degrees) > 0 {
		tx = tx.Where("Degree IN ?", degrees)
	}

	var count int64
	tx.Count(&count)
	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	var assessments []StaffAssessmentApi
	tx.Order("CreatedAt DESC").Find(&assessments)

	return assessments, count
}

func (sa *StaffAssessment) GetLatestRecord(staffArchiveId int64, limit int) []StaffAssessment {
	var assessments []StaffAssessment

	model.DB().Model(&StaffAssessment{}).Where("StaffArchiveId = ?", staffArchiveId).Order("Year DESC").Limit(limit).Find(&assessments)

	return assessments
}
