package hr

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type PositionalTitleApply struct {
	model.PkId
	TopCorporationId  int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:顶级机构ID"`                            //顶级机构ID
	StaffArchiveId    int64           `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:人员档案Id"`                                //人员档案Id
	StaffId           int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:人员ID"`                                               //主数据人员ID
	UserId            int64           `json:"UserId" gorm:"column:userid;type:integer;comment:账号ID"`                                                 //主数据人员ID
	Name              string          `json:"Name" gorm:"column:name;type:varchar;comment:职称名称" validate:"required"`                                 //职称名称
	More              string          `json:"More" gorm:"column:more;type:varchar;comment:备注"`                                                       //备注
	CertificateName   string          `json:"CertificateName" gorm:"column:certificatename;type:varchar;comment:证书名称" validate:"required"`           //证书名称
	CertificateOffice string          `json:"CertificateOffice" gorm:"column:certificateoffice;type:varchar;comment:发证机构" validate:"required"`       //发证机构
	CertificateCode   string          `json:"CertificateCode" gorm:"column:certificatecode;type:varchar;comment:证书编号" validate:"required"`           //证书编号
	CertificateDate   model.LocalTime `json:"CertificateDate" gorm:"column:certificatedate;type:timestamp;comment:发证日期" validate:"required"`         //发证日期
	Level             int64           `json:"Level" gorm:"column:level;type:smallint;comment:职称级别 1-正高级、2-副高级、3-中级、4-初级、5-未知"`                       //职称级别 1-正高级、2-副高级、3-中级、4-初级、5-未知
	FilePath          model.JSON      `json:"FilePath" gorm:"column:filepath;type:json;comment:附件" validate:"required"`                              //附件
	ApplyStatus       int64           `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;default:1;comment:审批状态 1-申请审批中,2-申请审批通过,3-撤销,4-驳回"` //审批状态 1-申请审批中,2-申请审批通过,3-撤销,4-驳回
	model.OpUser
	model.Timestamp
	UserName         string `json:"UserName" gorm:"-"`
	IsProcessHandler bool   `json:"IsProcessHandler" gorm:"-"` //是否是流程的相关人
}

func (pta *PositionalTitleApply) TableName() string {
	return "positional_title_applies"
}
func (pta *PositionalTitleApply) ApplyStatusFieldName() string {
	return "applystatus"
}

func (pta *PositionalTitleApply) BeforeCreate(tx *gorm.DB) error {
	pta.Id = model.Id()
	return nil
}

func (pta *PositionalTitleApply) Create() error {
	return model.DB().Create(pta).Error
}

func (pta *PositionalTitleApply) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(pta).Error
}

func (pta *PositionalTitleApply) FindBy(id int64) error {
	return model.DB().Model(&PositionalTitleApply{}).Where("Id = ?", id).First(pta).Error
}

func (pta *PositionalTitleApply) UpdateStatus() error {
	return model.DB().Model(&PositionalTitleApply{}).Select("ApprovalStatus", "OpStaffId").Where("Id = ?", pta.Id).Updates(pta).Error
}

type PositionalTitleApplyApi struct {
	PositionalTitleApply
	StaffName       string           `json:"StaffName" gorm:"-"`
	StaffMobile     string           `json:"StaffMobile" gorm:"-"`
	JobNumber       string           `json:"JobNumber" gorm:"-"`
	CorporationName string           `json:"CorporationName" gorm:"-"`
	WorkPostType    int64            `json:"WorkPostType" gorm:"-"`
	WorkPostName    string           `json:"WorkPostName" gorm:"-"`
	JobStatus       int64            `json:"JobStatus" gorm:"-"`
	JoinCompanyAt   *model.LocalTime `json:"JoinCompanyAt" gorm:"-"`
	ProbationEndAt  *model.LocalTime `json:"ProbationEndAt" gorm:"-"`
	WorkingAge      int64            `json:"WorkingAge" gorm:"-"`
	FileHttpPrefix  string           `json:"FileHttpPrefix" gorm:"-"`
}

func (pta *PositionalTitleApply) GetBy(staffIds []int64, name string, level, approvalStatus int64, paginator model.Paginator) ([]PositionalTitleApplyApi, int64) {
	tx := model.DB().Model(&PositionalTitleApply{}).Where("StaffId IN ?", staffIds).Where("EXISTS (?)", model.DB().Table("staff_archives").Where("staff_archives.Id = positional_title_applies.staffarchiveid"))

	if name != "" {
		tx = tx.Where("Name LIKE ?", "%"+name+"%")
	}

	if level > 0 {
		tx = tx.Where("Level  =  ?", level)
	}

	if approvalStatus > -1 {
		tx = tx.Where("ApplyStatus = ?", approvalStatus)
	}

	var count int64
	tx.Count(&count)
	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	var applies []PositionalTitleApplyApi
	tx.Order("CreatedAt DESC").Find(&applies)

	return applies, count
}
