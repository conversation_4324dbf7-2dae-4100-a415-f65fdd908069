package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	"gorm.io/gorm"
)

type DriverBecomeWorkerRecord struct {
	model.PkId
	StaffArchiveId   int64           `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID"`             //关联员工档案表主键ID
	StaffId          int64           `json:"StaffId" gorm:"column:staffid;type:integer;uniqueIndex:staffid_unique;comment:主数据人员主键ID"` //工号
	FinishSchool     string          `json:"FinishSchool" gorm:"column:finishschool;type:varchar;comment:毕业院校"`
	FinishSchoolAt   model.LocalTime `json:"FinishSchoolAt" gorm:"column:finishshoolat;type:timestamp;comment:毕业时间"`
	JoinAt           model.LocalTime `json:"JoinAt" gorm:"column:joinat;type:timestamp;comment:报到时间"`
	ProbationStartAt model.LocalTime `json:"ProbationStartAt" gorm:"column:probationstartat;type:timestamp;comment:试用期开始时间"`
	ProbationEndAt   model.LocalTime `json:"ProbationEndAt" gorm:"column:probationendat;type:timestamp;comment:试用期结束时间"`
	AssessResult     model.JSON      `json:"AssessResult" gorm:"column:assessresult;type:json;comment:考核结果"`
	FleetCheckStatus int64           `json:"FleetCheckStatus" gorm:"column:fleetcheckstatus;type:smallint;comment:0未知 1正常转正 2延期转正 3不予转正（辞退）"`
	FormStep         int64           `json:"FormStep" gorm:"column:formstep;type:smallint;default:1;comment:表单当前处的阶段 1默认 2车队长填写"`
	ApplyStatus      int64           `json:"ApplyStatus" gorm:"column:applystatus;default:1;comment:审批状态 1进行中 2已完成 3驳回 4撤回 5废弃;type:smallint"` // 工单状态 审批状态 0草稿 1进行中 2已完成 3驳回 4撤回 5废弃
	model.OpUser
	model.Timestamp

	IsRestart      bool            `json:"IsRestart" gorm:"-"`
	ProcessId      string          `json:"ProcessId" gorm:"-"`
	CurrentHandler string          `json:"CurrentHandler" gorm:"-"`
	DoneAt         model.LocalTime `json:"DoneAt" gorm:"-"`
	FormInstanceId int64           `json:"FormInstanceId" gorm:"-"`
	ProcessTitle   string          `json:"ProcessTitle" gorm:"-"`
	StaffName      string          `json:"StaffName" gorm:"-"`
}

func (m *DriverBecomeWorkerRecord) TableName() string {
	return "driver_become_worker_records"
}

func (m *DriverBecomeWorkerRecord) ApplyStatusFieldName() string {
	return "applystatus"
}

func (m *DriverBecomeWorkerRecord) TemplateFormId() string {
	return config.DriverBecomeWorkerApplyFormTemplate
}

func (m *DriverBecomeWorkerRecord) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *DriverBecomeWorkerRecord) Create() error {
	return model.DB().Create(&m).Error
}

func (m *DriverBecomeWorkerRecord) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&m).Error
}
func (m *DriverBecomeWorkerRecord) TransactionUpdate(tx *gorm.DB) error {
	return tx.Select("*").Updates(&m).Error
}

func (m *DriverBecomeWorkerRecord) Update() error {
	return model.DB().Select("AssessResult", "FleetCheckStatus", "FormStep").Updates(&m).Error
}
func (m *DriverBecomeWorkerRecord) FirstBy(id int64) DriverBecomeWorkerRecord {
	var record DriverBecomeWorkerRecord
	model.DB().Model(&DriverBecomeWorkerRecord{}).Where("Id = ?", id).First(&record)
	return record
}

func (m *DriverBecomeWorkerRecord) FirstLatestRecord(staffArchiveId int64) DriverBecomeWorkerRecord {
	var record DriverBecomeWorkerRecord
	model.DB().Model(&DriverBecomeWorkerRecord{}).Where("StaffArchiveId = ?", staffArchiveId).Order("CreatedAt desc").First(&record)
	return record
}

func (m *DriverBecomeWorkerRecord) GetRecords(staffArchiveId int64) []DriverBecomeWorkerRecord {
	var records []DriverBecomeWorkerRecord
	model.DB().Model(&DriverBecomeWorkerRecord{}).Where("StaffArchiveId = ?", staffArchiveId).Order("CreatedAt asc").Find(&records)
	return records
}

func (m *DriverBecomeWorkerRecord) DashboardGetBy(processTitle, userName, handlerName string, applyStatus, formStep int64, paginator model.Paginator) ([]DriverBecomeWorkerRecord, int64) {
	var records []DriverBecomeWorkerRecord
	tx := model.DB().Model(&DriverBecomeWorkerRecord{})

	if processTitle != "" {
		tx.Where("Id IN (?)", model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ?", config.DriverBecomeWorkerApplyFormTemplate).Where("Title LIKE ?", "%"+processTitle+"%"))
	}

	if userName != "" {
		tx.Where("OpUserName LIKE ?", "%"+userName+"%")
	}

	if handlerName != "" {
		subQuery := model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ? AND CurrentHandlerUserName LIKE ?", config.DriverBecomeWorkerApplyFormTemplate, "%"+handlerName+"%")
		tx.Where("Id IN (?)", subQuery)
	}

	if formStep > 0 {
		tx.Where("FormStep = ?", formStep)
	}

	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	}

	var count int64
	tx.Count(&count)

	tx.Order("CreatedAt desc").Offset(paginator.Offset).Limit(paginator.Limit).Find(&records)
	return records, count
}
