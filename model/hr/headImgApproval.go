package hr

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
)

type HeadImgApproval struct {
	model.PkId
	model.Corporations
	model.OpUser
	model.Timestamp
	//Code            string     `json:"Code" gorm:"column:code;type:varchar(255);comment:提交编号"`
	StaffArchiveId  int64           `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:人员档案Id"` //人员档案Id
	StaffId         int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:主数据人员ID;"`
	StaffName       string          `json:"StaffName" gorm:"column:staffname;type:varchar;comment:人员姓名"`
	JobNumber       string          `json:"JobNumber" gorm:"column:jobnumber;type:varchar;comment:主数据人员工号;"`
	ApproveUserId   int64           `json:"ApproveUserId" gorm:"column:approveuserid;type:bigint;comment:操作人ID"`    //审核人员Id
	ApproveUserName string          `json:"ApproveUserName" gorm:"column:approveusername;type:varchar;comment:操作人"` //审核人员姓名
	ApproveTime     model.LocalTime `json:"ApproveTime" gorm:"column:ApproveTime;type:timestamp;comment:操作人"`       //审核人员姓名
	HeadImg         model.JSON      `json:"HeadImg" gorm:"column:headimg;type:json;comment:证件照"`                    //证件照
	HeadImgState    int64           `json:"HeadImgState" gorm:"column:headimgstate;type:smallint;default:0;comment:证件照审核是否通过 -1未上传过照片 审批状态 0草稿 1进行中 2已完成 3驳回 4撤回 5废弃"`
	RejectReason    string          `json:"RejectReason" gorm:"column:rejectreason;type:text;default:;comment:驳回原因"`
	//ApplyStatus     int64           `json:"ApplyStatus" gorm:"column:applystatus;default:0;comment:审批状态 0草稿 1进行中 2已完成 3驳回 4撤回 5废弃;type:smallint"`
	//
	CorporationId   int64  `json:"CorporationId" gorm:"-"`
	CorporationName string `json:"CorporationName" gorm:"-"`
}

func (m *HeadImgApproval) TableName() string {
	return "head_img_approvals"
}

func (m *HeadImgApproval) ApplyStatusFieldName() string {
	return "headimgstate"
}

func (m *HeadImgApproval) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *HeadImgApproval) Create() error {
	return model.DB().Create(&m).Error
}

func (m *HeadImgApproval) Update() error {
	return model.DB().Select(
		"StaffArchiveId", "StaffId", "StaffName", "JobNumber", "ApproveUserId", "ApproveUserName", "ApproveTime", "HeadImg", "HeadImgState", "RejectReason",
	).Updates(&m).Error
}
func (m *HeadImgApproval) TxUpdate(tx *gorm.DB) error {
	return tx.Select(
		"StaffArchiveId", "StaffId", "StaffName", "JobNumber", "ApproveUserId", "ApproveUserName", "ApproveTime", "HeadImg", "HeadImgState", "RejectReason",
	).Updates(&m).Error
}

// FindByStaffId 根据主数据人员ID
func (m *HeadImgApproval) FindByStaffId() error {
	return model.DB().Model(m).Where("StaffId=?", m.StaffId).Order("CreatedAt desc").Find(&m).Error
}

// FindById 根据id
func (m *HeadImgApproval) FindById() error {
	return model.DB().Model(m).Where("Id=?", m.Id).Find(&m).Error
}

// List 列表
func (m *HeadImgApproval) List(corporationIds []int64, staffName, jobNumber string, pagination model.Paginator) (Data []HeadImgApproval, TotalCount int64) {
	tx := model.DB().Model(&HeadImgApproval{}).Scopes(model.WhereCorporations(corporationIds))
	if staffName != "" {
		tx = tx.Where("StaffName LIKE ?", "%"+staffName+"%")
	}

	if jobNumber != "" {
		tx = tx.Where("JobNumber LIKE ?", "%"+jobNumber+"%")
	}
	tx.Count(&TotalCount)
	if pagination.Limit > 0 {
		tx = tx.Offset(pagination.Offset).Limit(pagination.Limit)
	}
	tx.Order("CreatedAt DESC").Find(&Data)
	return
}
