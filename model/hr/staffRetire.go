package hr

import (
	"app/org/scs/erpv2/api/model"

	"gorm.io/gorm"
)

// StaffRetire 退休申请
type StaffRetireApplyRecord struct {
	model.PkId
	StaffArchiveId int64           `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID"` //关联员工档案表主键ID
	StaffId        int64           `json:"StaffId" gorm:"column:staffid;type:bigint;comment:人员ID"`
	StaffName      string          `json:"StaffName" gorm:"column:staffname;type:varchar(64);comment:员工姓名"` //员工姓名
	RetireAt       model.LocalTime `json:"RetireAt" gorm:"column:retireat;type:timestamp;comment:退休时间"`
	WorkPostType   int64           `json:"WorkPostType" gorm:"column:workposttype;type:integer;comment:岗位类型 1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他,9-修理工"`
	RetireType     int64           `json:"RetireType" gorm:"column:retiretype;type:smallint;comment:退休类型 1立即退休 2延迟退休 3提前退休"`
	ApplyStatus    int64           `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;comment:审批状态 1-申请审批中,2-申请审批通过,3-撤销,4-撤回,5-废弃;"`
	RetireMore     string          `json:"RetireMore" gorm:"column:retiremore;type:text;comment:退休原因"`
	model.OpUser
	model.Timestamp
}

func (sr *StaffRetireApplyRecord) BeforeCreate(tx *gorm.DB) error {
	sr.Id = model.Id()
	return nil
}

func (sr *StaffRetireApplyRecord) Create(tx *gorm.DB) error {
	return tx.Create(sr).Error
}

func (sr *StaffRetireApplyRecord) TableName() string {
	return "staff_retire_apply_records"
}

func (sr *StaffRetireApplyRecord) ApplyStatusFieldName() string {
	return "applystatus"
}

func (sr *StaffRetireApplyRecord) FirstBy(id int64) StaffRetireApplyRecord {
	var record StaffRetireApplyRecord
	model.DB().Model(&StaffRetireApplyRecord{}).Where("Id = ?", id).First(&record)
	return record
}

func (sr *StaffRetireApplyRecord) FirstLatestByStaffArchiveId(staffArchiveId int64) StaffRetireApplyRecord {
	var record StaffRetireApplyRecord
	model.DB().Model(&StaffRetireApplyRecord{}).Where("StaffArchiveId = ?", staffArchiveId).Order("CreatedAt DESC").First(&record)
	return record
}

//type StaffRetireRecord struct {
//	model.PkId
//	model.GroupCorporation
//	model.CompanyCorporation
//	model.BranchCorporation
//	model.DepartmentCorporation
//	model.FleetCorporation
//	StaffRetireId int64            `json:"StaffRetireId" gorm:"column:staffretireid;type:bigint;comment:退休ID"`
//	StaffId       int64            `json:"StaffId" gorm:"column:staffid;type:bigint;comment:人员ID"`
//	StaffName     string           `json:"StaffName" gorm:"column:staffname;type:varchar(64);comment:员工姓名"`         //员工姓名
//	Status        int64            `json:"Status" gorm:"column:status;type:smallint;comment:工作状态"`                  //工作状态 1-在职,2-离职,3-试用期,4-退休,5-退休返聘,6-退休待审
//	JobNumber     string           `json:"JobNumber" gorm:"column:jobnumber;type:varchar;comment:工号"`               //工号
//	WorkPostId    int64            `json:"WorkPostId" gorm:"column:workpostid;type:bigint;comment:岗位"`              //岗位
//	WorkPostName  string           `json:"WorkPostName" gorm:"-"`                                                   //岗位名称
//	RetireAt      *model.LocalTime `json:"RetireAt" gorm:"column:retireat;type:timestamp;comment:退休时间"`             //退休时间
//	JoinCompanyAt *model.LocalTime `json:"JoinCompanyAt" gorm:"column:joincompanyat;type:timestamp;comment:参加工作时间"` //参加工作时间
//	FilePath      model.JSON       `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                    //附件
//	model.Timestamp
//}

//func (rec *StaffRetireRecord) FindBy(id int64) error {
//	return model.DB().First(rec, id).Error
//}
//func (rec *StaffRetireRecord) BeforeCreate(tx *gorm.DB) error {
//	rec.Id = model.Id()
//	return nil
//}
//
//func (rec *StaffRetireRecord) SwitchStatus(id int64) error {
//	return model.DB().Model(rec).Select("Status").Update("Status", rec.Status).Error
//}
//
//func (rec *StaffRetireRecord) AfterSave(tx *gorm.DB) error {
//	//更主数据员工状态
//	oetStaff := rpc.GetStaffWithId(context.Background(), rec.StaffId)
//	if oetStaff != nil && oetStaff.WorkingState != rec.Status {
//		oetStaff.WorkingState = rec.Status
//		err := rpc.EditOetStaff(context.Background(), oetStaff)
//		if err != nil {
//			return err
//		}
//	}
//	return nil
//}

// StaffRetireWorkRecord 返聘记录
type StaffRetireWorkRecord struct {
	model.PkId
	StaffArchiveId  int64            `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID" validate:"required"` //关联员工档案表主键ID
	StaffId         int64            `json:"StaffId" gorm:"column:staffid;type:bigint;comment:人员ID"`
	CorporationId   int64            `json:"CorporationId" gorm:"column:corporationid;type:bigint;comment:机构ID" validate:"required"`  //机构ID
	WorkPostId      int64            `json:"WorkPostId" gorm:"column:workpostid;type:bigint;comment:岗位" validate:"required"`          // 返聘岗位id
	RetireWorkAt    model.LocalTime  `json:"RetireWorkAt" gorm:"column:retireworkat;type:timestamp;comment:返聘时间" validate:"required"` // 返聘时间
	RetireWorkEndAt *model.LocalTime `json:"RetireWorkEndAt" gorm:"column:retireworkendat;type:timestamp;comment:解聘时间"`               // 解聘时间
	FilePath        model.JSON       `json:"FilePath" gorm:"column:filepath;type:json;comment:附件" validate:"required"`                //附件
	model.Timestamp
	model.OpUser

	WorkPostName    string `json:"WorkPostName" gorm:"-"`    // 返聘岗位名称
	CorporationName string `json:"CorporationName" gorm:"-"` // 机构名称
}

func (rec *StaffRetireWorkRecord) BeforeCreate(tx *gorm.DB) error {
	rec.Id = model.Id()
	return nil
}
func (rec *StaffRetireWorkRecord) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&rec).Error
}
func (rec *StaffRetireWorkRecord) TransactionUpdateEndAt(tx *gorm.DB) error {
	return tx.Select("RetireWorkEndAt", "OpStaffId").Updates(&rec).Error
}

func (rec *StaffRetireWorkRecord) GetBy(staffId int64) []StaffRetireWorkRecord {
	var records []StaffRetireWorkRecord
	model.DB().Model(&StaffRetireWorkRecord{}).Where("StaffId = ?", staffId).Order("RetireWorkEndAt ASC").Find(&records)
	return records
}

func (rec *StaffRetireWorkRecord) LatestRecord(staffId int64) StaffRetireWorkRecord {
	var record StaffRetireWorkRecord
	model.DB().Model(&StaffRetireWorkRecord{}).Where("StaffId = ? AND RetireWorkEndAt IS NULL", staffId).Order("RetireWorkAt DESC").First(&record)
	return record
}
