package hr

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type DriverMigration struct {
	model.PkId
	TopCorporationId int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:机构ID"`
	Code             string          `json:"Code" gorm:"column:code;type:varchar;default:;comment:调动批次"`                                   //调动批次
	CodeSequence     int64           `json:"CodeSequence" gorm:"column:codesequence;type:integer;default:1;comment:一天中的批次序号"`              //一天中的批次序号
	OutCorporationId int64           `json:"OutCorporationId" gorm:"column:outcorporationid;type:bigint;comment:调出部门" validate:"required"` //调出部门
	InCorporationId  int64           `json:"InCorporationId" gorm:"column:incorporationid;type:bigint;comment:调入部门" validate:"required"`   //调入部门
	UseAt            model.LocalTime `json:"UseAt" gorm:"column:useat;type:timestamp;comment:报道时间;" validate:"required"`                   // 调动时间
	InFleetUserId    int64           `json:"InFleetUserId" gorm:"column:infleetuserid;type:bigint;comment:调入车队的车队长" validate:"required"`
	InFleetUserName  string          `json:"InFleetUserName" gorm:"column:infleetusername;type:varchar;comment:调入车队的车队长"`
	InHrUserId       int64           `json:"InHrUserId" gorm:"column:inhruserid;type:bigint;comment:调入机构劳资" validate:"required"`
	InHrUserName     string          `json:"InHrUserName" gorm:"column:inhrusername;type:varchar;comment:调入机构劳资"`
	InManageUserId   int64           `json:"InManageUserId" gorm:"column:inmanageuserid;type:bigint;comment:调入机构综合"`
	InManageUserName string          `json:"InManageUserName" gorm:"column:inmanageusername;type:varchar;comment:调入机构综合"`
	NotifyUsers      model.JSON      `json:"NotifyUsers" gorm:"column:notifyusers;type:json;comment:知会人"`
	Reason           string          `json:"Reason" gorm:"column:reason;type:text;comment:调动原因"`
	Files            model.JSON      `json:"Files" gorm:"column:files;type:json;comment:附件"`
	IsFleetApply     int64           `json:"IsFleetApply" gorm:"column:isfleetapply;type:smallint;default:1;comment:是否车队发起 1是 2否"`
	ApplyStatus      int64           `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;default:0;comment:审批状态 0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃"` //审批状态 0未发起  1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃
	FormStep         int64           `json:"FormStep" gorm:"column:formstep;type:smallint;default:1;comment:表单节点 1发起  2调入车队劳资填写 3已填写"`
	IsDone           int64           `json:"IsDone" gorm:"column:isdone;type:smallint;default:2;comment:是否已完成调动  1是 2否"` //是否已完成调动  1是 2否
	model.OpUser
	model.Timestamp

	OutCorporationName string                  `json:"OutCorporationName" gorm:"-"`
	InCorporationName  string                  `json:"InCorporationName" gorm:"-"`
	Records            []DriverMigrationRecord `json:"Records"`
	ProcessHandler     string                  `json:"ProcessHandler" gorm:"-"`
	OutCorporationInfo []string                `json:"OutCorporationInfo" gorm:"-"`
	InCorporationInfo  []string                `json:"InCorporationInfo" gorm:"-"`
	ProcessTitle       string                  `json:"ProcessTitle" gorm:"-"`
	FormInstanceId     int64                   `json:"FormInstanceId" gorm:"-"`
	ProcessId          string                  `json:"ProcessId" gorm:"-"`
	CurrentHandler     string                  `json:"CurrentHandler" gorm:"-"`
}
type DriverMigrationRecord struct {
	model.PkId
	DriverMigrationId int64  `json:"DriverMigrationId" gorm:"column:drivermigrationid;type:bigint;comment:关联driver_migrations表主键"`
	DriverId          int64  `json:"DriverId" gorm:"column:driverid;type:integer;comment:司机ID" validate:"required"`
	DriverName        string `json:"DriverName" gorm:"column:drivername;type:varchar;comment:司机" validate:"required"`
	DriverLicense     string `json:"DriverLicense" gorm:"column:driverlicense;type:varchar;comment:准驾车型" `
	OutLineId         int64  `json:"OutLineId" gorm:"column:outlineid;type:integer;comment:调出线路ID"`
	OutLine           string `json:"OutLine" gorm:"column:outline;type:varchar;comment:调出线路名称"`
	InLineId          int64  `json:"InLineId" gorm:"column:inlineid;type:integer;comment:调入线路ID"`
	InLine            string `json:"InLine" gorm:"column:inline;type:varchar;comment:调入线路名称"`
	IsMotor           int64  `json:"IsMotor" gorm:"column:ismotor;type:smallint;default:2;comment:是否机动线路 1是 2否"`
	model.Timestamp

	IdentifyId string `json:"IdentifyId" gorm:"-"`
	Phone      string `json:"Phone" gorm:"-"`
}

func (m *DriverMigration) TableName() string {
	return "driver_migrations"
}

func (m *DriverMigration) ApplyStatusFieldName() string {
	return "applystatus"
}

func (m *DriverMigration) BeforeCreate(tx *gorm.DB) (err error) {
	m.Id = model.Id()
	//查询今天最大的序号
	codeSequence := m.GetMaxCodeSequence()
	m.CodeSequence = codeSequence + 1
	m.Code = fmt.Sprintf("%v%04d", time.Now().Format("20060102"), m.CodeSequence)

	return nil
}

func (m *DriverMigration) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(&m).Error
}

func (m *DriverMigration) TransactionUpdate(t *gorm.DB) error {
	return t.Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&DriverMigration{}).Where("Id = ?", m.Id).Select("*").Omit("Records").Omit("Id", "Code", "CodeSequence").Updates(&m).Error
		if err != nil {
			return err
		}

		err = tx.Where("DriverMigrationId = ?", m.Id).Delete(&DriverMigrationRecord{}).Error
		if err != nil {
			return err
		}
		err = tx.Model(&m).Association("Records").Replace(&m.Records)
		if err != nil {
			return err
		}

		return nil
	})
}

func (m *DriverMigration) FirstBy(id int64) DriverMigration {
	var record DriverMigration
	model.DB().Model(&DriverMigration{}).Preload("Records").First(&record, id)
	return record
}

func (m *DriverMigration) UpdateApplyStatus(id, status int64) error {
	return model.DB().Model(&DriverMigration{}).Where("Id = ?", id).Update("ApplyStatus", status).Error
}

func (m *DriverMigration) UpdateFormStep(id, step int64) error {
	return model.DB().Model(&DriverMigration{}).Where("Id = ?", id).Update("FormStep", step).Error
}

func (m *DriverMigration) GetMaxCodeSequence() int64 {
	var codeSequence int64
	model.DB().Model(&DriverMigration{}).Select("CodeSequence").Where("CreatedAt >= ?", time.Now().Format(model.DateFormat)).Order("CodeSequence DESC").First(&codeSequence)

	return codeSequence
}

func (m *DriverMigrationRecord) BeforeCreate(tx *gorm.DB) error {
	m.Id = model.Id()
	return nil
}

func (m *DriverMigrationRecord) UpdateInLine() error {
	return model.DB().Where("Id = ?", m.Id).Select("InLineId", "InLine", "IsMotor").Updates(&m).Error
}

func (m *DriverMigrationRecord) GetRecordsByMigrationId(migrationId int64) []DriverMigrationRecord {
	var records []DriverMigrationRecord
	model.DB().Model(&DriverMigrationRecord{}).Where("DriverMigrationId = ?", migrationId).Find(&records)

	return records
}

func (m *DriverMigration) GetBy(corpIds []int64, driverName, code, processHandler string, outCorpId, inCorpId, applyStatus int64, startUseAt, endUseAt, startAt, endAt time.Time, paginator model.Paginator) ([]DriverMigration, int64) {
	tx := model.DB().Model(&DriverMigration{}).Preload("Records").Where("OutCorporationId IN ? OR InCorporationId IN ?", corpIds, corpIds)
	if code != "" {
		tx.Where("Code LIKE ?", "%"+code+"%")
	}
	if driverName != "" {
		subQuery := model.DB().Model(&DriverMigrationRecord{}).Select("DriverMigrationId").Where("DriverName LIKE ?", "%"+driverName+"%")
		tx.Where("Id IN (?)", subQuery)
	}
	if processHandler != "" {
		subQuery := model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("ItemTableName = ? AND CurrentHandlerUserName LIKE ?", m.TableName(), "%"+processHandler+"%")
		tx.Where("Id IN (?)", subQuery)
	}
	if outCorpId > 0 {
		tx.Where("OutCorporationId = ?", outCorpId)
	}
	if inCorpId > 0 {
		tx.Where("InCorporationId = ?", inCorpId)
	}
	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	}

	if !startUseAt.IsZero() {
		tx.Where("UseAt >= ?", startUseAt.Format(model.DateFormat))
	}
	if !endUseAt.IsZero() {
		tx.Where("UseAt <= ?", endUseAt.Format(model.DateFormat))
	}

	if !startAt.IsZero() {
		tx.Where("CreatedAt >= ?", startAt.Format(model.TimeFormat))
	}
	if !endAt.IsZero() {
		tx.Where("CreatedAt <= ?", endAt.Format(model.TimeFormat))
	}

	var count int64
	tx.Count(&count)

	var records []DriverMigration
	tx.Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Find(&records)

	return records, count
}

func (m *DriverMigration) DashboardGetBy(processTitle, code, userName, handlerName string, applyStatus, formStep int64, paginator model.Paginator) ([]DriverMigration, int64) {
	var migrations []DriverMigration
	tx := model.DB().Model(&DriverMigration{})

	if processTitle != "" {
		tx.Where("Id IN (?)", model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ?", config.DriverMigrationApplyFormTemplate).Where("Title LIKE ?", "%"+processTitle+"%"))
	}

	if code != "" {
		tx.Where("Code LIKE ?", "%"+code+"%")
	}

	if userName != "" {
		tx.Where("OpUserName LIKE ?", "%"+userName+"%")
	}

	if handlerName != "" {
		subQuery := model.DB().Model(&processModel.LbpmApplyProcess{}).Select("ItemId").Where("TemplateFormId = ? AND CurrentHandlerUserName LIKE ?", config.DriverMigrationApplyFormTemplate, "%"+handlerName+"%")
		tx.Where("Id IN (?)", subQuery)
	}

	if formStep > 0 {
		tx.Where("FormStep = ?", formStep)
	}

	if applyStatus > 0 {
		tx.Where("ApplyStatus = ?", applyStatus)
	}

	var count int64
	tx.Count(&count)

	tx.Order("CreatedAt desc").Offset(paginator.Offset).Limit(paginator.Limit).Find(&migrations)
	return migrations, count
}

func (m *DriverMigration) GetByAllForRpc(corporationId int64, driverIds []int64, applyStatuses []int64, startUseAt, endUseAt time.Time) []DriverMigration {
	tx := model.DB().Model(&DriverMigration{}).Preload("Records")
	if corporationId > 0 {
		tx.Where("OutCorporationId = ? OR InCorporationId = ?", corporationId, corporationId)
	}
	if len(driverIds) > 0 {
		subQuery := model.DB().Model(&DriverMigrationRecord{}).Select("DriverMigrationId").Where("DriverId IN ?", driverIds)
		tx.Where("Id IN (?)", subQuery)
	}

	if len(applyStatuses) > 0 {
		tx.Where("ApplyStatus IN ?", applyStatuses)
	}

	if !startUseAt.IsZero() {
		tx.Where("UseAt >= ?", startUseAt.Format(model.DateFormat))
	}
	if !endUseAt.IsZero() {
		tx.Where("UseAt <= ?", endUseAt.Format(model.DateFormat))
	}
	var records []DriverMigration
	tx.Order("CreatedAt DESC").Find(&records)

	return records
}

// GetEnableMigrationRecord 查询可以调动但还未调动的记录
func (m *DriverMigration) GetEnableMigrationRecord() []DriverMigration {
	var records []DriverMigration
	model.DB().Model(&DriverMigration{}).Where("ApplyStatus = ? AND IsDone = ?", util.ApplyStatusForDone, util.StatusForFalse).Where("UseAt <= ?", time.Now().Format(model.TimeFormat)).Find(&records)

	return records
}

func (m *DriverMigration) GetDriverMigrationForDoing() []DriverMigration {
	var records []DriverMigration
	model.DB().Model(&DriverMigration{}).Where("ApplyStatus = ? AND IsDone = ?", util.ApplyStatusForDoing, util.StatusForFalse).Find(&records)

	return records
}

func (m *DriverMigration) GetRecords() []DriverMigrationRecord {
	var records []DriverMigrationRecord
	model.DB().Model(&DriverMigrationRecord{}).Where("DriverMigrationId = ?", m.Id).Find(&records)

	return records
}

func (m *DriverMigration) SetDone() error {
	return model.DB().Model(&DriverMigration{}).Where("Id = ?", m.Id).Update("IsDone", util.StatusForTrue).Error
}

func (m *DriverMigration) IsExistDoingMigration(driverId int64) bool {
	var count int64

	subQuery := model.DB().Model(&DriverMigrationRecord{}).Select("DriverMigrationId").Where("driverId = ?", driverId)

	model.DB().Model(&DriverMigration{}).Where("Id IN (?)", subQuery).Where("ApplyStatus = ? AND IsDone = ?", util.ApplyStatusForDoing, util.StatusForFalse).Count(&count)

	return count > 0
}
