package hr

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

type ErpArchive struct {
	model.Corporations
	StaffId int64 `json:"StaffId" gorm:"column:staffid;type:integer;uniqueIndex:staffid_unique;comment:主数据人员主键ID"`
	ErpDataForBasic
	HumanRelationId    int64      `json:"HumanRelationId" gorm:"column:humanrelationid;type:bigint;comment:人事关系，关联机构Id"`
	HumanRelationName  string     `json:"HumanRelationName" gorm:"-"`
	EmploymentCate     int64      `json:"EmploymentCate" gorm:"column:employmentcate;type:smallint;comment:用工类别 1一类 2二类  3三类"`
	PartyFilePath      model.JSON `json:"PartyFilePath" gorm:"column:partyfilepath;type:json;comment:附件  党群资料"`
	EditApplyStatus    int64      `json:"EditApplyStatus" gorm:"column:editapplystatus;type:smallint;comment:员工档案编辑审批状态 1-申请审批中,2-申请审批通过,3-驳回（拒绝）,4-撤回，5-废弃;default:2"`
	DepartmentHeadType int64      `json:"DepartmentHeadType" gorm:"column:departmentheadtype;type:smallint;default:2;comment:是否部门负责人 2-否  3-组织人事部门负责人 4-财务部门负责人 5-内审的负责人 6-除组织人事、财务、内审部门外的其他部门负责人"`
	ManagerType        int64      `json:"ManagerType" gorm:"column:managertype;type:smallint;default:4;comment:管理类型 1-市管干部 2-部管干部 3-委管干部 4-企业管理"`
	DutiesCate         int64      `json:"DutiesCate" gorm:"column:dutiescate;type:smallint;default:5;comment:职务类别  1-班子成员正职  2-班子成员副职  3-中层正职  4-中层副职  5-一般员工"`
	IsCctDriver        int64      `json:"IsCctDriver" gorm:"column:iscctdriver;type:smallint;default:2;comment:人员为驾驶员时 标明是否村村通司机 1是 2否"`
	IsLineLeader       int64      `json:"IsLineLeader" gorm:"column:islineleader;type:smallint;default:2;comment:人员为驾驶员时 标明是否为线组长 1是 2否"`
	IsLocal            int64      `json:"IsLocal" gorm:"column:islocal;type:smallint;default:0;comment:人员为驾驶员时 是否为本地人 1是 2否"`
}

type ErpDataForBasic struct {
	Age              int64            `json:"Age" gorm:"column:age;type:smallint;comment:年龄"`                                        //年龄
	BirthDate        *model.LocalTime `json:"BirthDate" gorm:"column:birthdate;type:timestamp;comment:出生日期"`                         //出生日期
	ResidenceAttr    int64            `json:"ResidenceAttr" gorm:"column:residenceattr;type:smallint;comment:户口性质 1-城镇,2-农村"`        //户口性质 1-城镇,2-农村
	Address          string           `json:"Address" gorm:"column:address;type:varchar;comment:家庭住址"`                               //家庭住址
	MarriageStatus   int64            `json:"MarriageStatus" gorm:"column:marriagestatus;type:smallint;comment:婚姻状态 1-未婚,2-已婚,3-离异"` //婚姻状态 1-未婚,2-已婚,3-离异
	BearStatus       int64            `json:"BearStatus" gorm:"column:bearstatus;type:integer;comment:生育信息"`                         //生育信息 1-未育,2-一胎,3-二胎,4-三胎,5-三胎以上,6-已育
	HighestEdu       int64            `json:"HighestEdu" gorm:"column:highestedu;type:integer;comment:最高学历"`                         //最高学历 1-博士研究生,3-硕士研究生,5-本科,6-大专,7-中专,8-高中及以下,9-技工学校,10-普通高中,11-初中,12-小学,13-在职大专,14-在职本科,15-在职硕士,16-在职博士,17-其他,18-在职研究生
	ProbationStartAt *model.LocalTime `json:"ProbationStartAt" gorm:"column:probationstartat;type:timestamp;comment:试用期开始时间"`
	ProbationEndAt   *model.LocalTime `json:"ProbationEndAt" gorm:"column:probationendat;type:timestamp;comment:试用期结束时间"`
	BaseFilePath     model.JSON       `json:"BaseFilePath" gorm:"column:basefilepath;type:json;comment:附件"`   //附件  包含基础信息附件
	RetireMore       string           `json:"RetireMore" gorm:"column:retiremore;type:varchar;comment:退休的备注"` //退休的备注
	IsHasBudget      int64            `json:"IsHasBudget" gorm:"column:ishasbudget;type:smallint;commnet:是否纳入预算 1是 2否;default:1"`
}

// StaffArchive 员工档案信息
type StaffArchive struct {
	model.PkId
	ErpArchive
	Educations        []StaffEducation        `json:"Educations"`           //学历信息
	PositionalTitles  []StaffPositionalTitle  `json:"PositionalTitles"`     //职称信息
	Skills            []StaffSkill            `json:"Skills"`               //技能信息
	FamilyMembers     []StaffFamilyMember     `json:"FamilyMembers"`        //家庭信息
	Certificates      []StaffCertificate      `json:"Certificates"`         //从业资格证
	WorkPosts         []StaffHasWorkPost      `json:"WorkPosts"`            //职务信息
	LaborContracts    []StaffLaborContract    `json:"LaborContracts"`       //劳动合同
	WorkTrains        []ArchiveWorkTrain      `json:"WorkTrains" gorm:"-"`  //在岗培训记录
	PunishmentRecords []StaffPunishmentRecord `json:"PunishmentRecords"`    //处罚记录
	RewardRecords     []StaffRewardRecord     `json:"RewardRecords"`        //奖励记录
	Assessments       []StaffAssessment       `json:"Assessments" gorm:"-"` //考核等级评定
	MasterDataFieldForBasic
	MasterDataFieldForJob
	model.OpUser
	model.Timestamp
	FileHttpPrefix      string          `json:"FileHttpPrefix" gorm:"-"`
	InsuranceRecord     InsuranceRecord `json:"InsuranceRecord" gorm:"-"`
	FundRecord          FundRecord      `json:"FundRecord" gorm:"-"`
	OpIp                string          `json:"OpIp" gorm:"-"`
	IsMineEdit          bool            `json:"IsMineEdit" gorm:"-"`
	StaffName           string          `json:"StaffName" gorm:"-"`
	IsProcessHandler    bool            `json:"IsProcessHandler" gorm:"-"` //是否是流程的相关人
	IdStr               string          `json:"IdStr" gorm:"-"`
	LaborContractStatus int64           `json:"LaborContractStatus" gorm:"-"` //合同状态 0未知 1是有效  2到期  3解除

	Sort            int64 `json:"-" gorm:"-"`               //排序
	CorporationType int64 `json:"CorporationType" gorm:"-"` //机构类型

	BecomeWorkerRecord DriverBecomeWorkerRecord `json:"BecomeWorkerRecord" gorm:"-"` //转正记录
}

// ProbationExpireMessageType 试用期即将到期消息提醒的消息类型
func (sta *StaffArchive) ProbationExpireMessageType() string {
	return "staff_probation_expire_message"
}

func (sta *StaffArchive) TableName() string {
	return "staff_archives"
}

func (sta *StaffArchive) EditApplyStatusFieldName() string {
	return "editapplystatus"
}

func (sta *StaffArchive) BeforeCreate(tx *gorm.DB) (err error) {
	sta.Id = model.Id()
	return
}

func (sta *StaffArchive) UpdateOrCreate(dontPermission map[string]bool) error {
	return model.DB().Transaction(func(tx *gorm.DB) (err error) {
		if sta.Id > 0 {
			var omit []string
			if _, ok := dontPermission["Staffarchive.Edit.BasicInfo"]; ok {
				keys, _ := util.ReturnStructKey(ErpDataForBasic{})
				omit = append(omit, keys...)
			}
			if _, ok := dontPermission["Staffarchive.Edit.JobInfo"]; ok {
				omit = append(omit, "HumanRelationId", "EmploymentCate")
			}
			if _, ok := dontPermission["Staffarchive.Edit.PartyData"]; ok {
				omit = append(omit, "PartyFilePath")
			}
			err = tx.Model(&StaffArchive{}).Omit(clause.Associations).Where("Id = ?", sta.Id).Omit(omit...).Save(sta).Error
		} else {
			err = tx.Omit(clause.Associations).Create(sta).Error
		}

		if err != nil {
			return err
		}

		if _, ok := dontPermission["Staffarchive.Edit.Education"]; !ok {
			err = tx.Where("StaffArchiveId = ?", sta.Id).Delete(&StaffEducation{}).Error
			if err != nil {
				return err
			}
			var educations []StaffEducation
			for i := range sta.Educations {
				if sta.Educations[i].Edu == 0 && sta.Educations[i].School == "" {
					continue
				}
				sta.Educations[i].StaffId = sta.StaffId
				educations = append(educations, sta.Educations[i])
			}
			err = tx.Model(sta).Association("Educations").Replace(&educations)
			if err != nil {
				return err
			}
		}

		if _, ok := dontPermission["Staffarchive.Edit.FamilyMember"]; !ok {
			err = tx.Where("StaffArchiveId = ?", sta.Id).Delete(&StaffFamilyMember{}).Error
			if err != nil {
				return err
			}
			var familyMembers []StaffFamilyMember
			for i := range sta.FamilyMembers {
				if sta.FamilyMembers[i].Name == "" && sta.FamilyMembers[i].Contact == "" {
					continue
				}
				sta.FamilyMembers[i].StaffId = sta.StaffId
				familyMembers = append(familyMembers, sta.FamilyMembers[i])
			}
			err = tx.Model(sta).Association("FamilyMembers").Replace(&familyMembers)
			if err != nil {
				return err
			}
		}

		if _, ok := dontPermission["Staffarchive.Edit.Skill"]; !ok {
			err = tx.Where("StaffArchiveId = ?", sta.Id).Delete(&StaffSkill{}).Error
			if err != nil {
				return err
			}
			var skills []StaffSkill
			for i := range sta.Skills {
				if sta.Skills[i].Name == "" {
					continue
				}
				sta.Skills[i].StaffId = sta.StaffId
				skills = append(skills, sta.Skills[i])
			}
			err = tx.Model(sta).Association("Skills").Replace(&skills)
			if err != nil {
				return err
			}
		}

		if _, ok := dontPermission["Staffarchive.Edit.Certificate"]; !ok {
			err = tx.Where("StaffArchiveId = ?", sta.Id).Delete(&StaffCertificate{}).Error
			if err != nil {
				return err
			}
			var certificates []StaffCertificate
			for i := range sta.Certificates {
				if sta.Certificates[i].Name == "" {
					continue
				}
				sta.Certificates[i].StaffId = sta.StaffId
				certificates = append(certificates, sta.Certificates[i])
			}
			err = tx.Model(sta).Association("Certificates").Replace(&certificates)
			if err != nil {
				return err
			}
		}

		if _, ok := dontPermission["Staffarchive.Edit.PositionalTitle"]; !ok {
			err = tx.Where("StaffArchiveId = ?", sta.Id).Delete(&StaffPositionalTitle{}).Error
			if err != nil {
				return err
			}
			var positionalTitles []StaffPositionalTitle
			for i := range sta.PositionalTitles {
				if sta.PositionalTitles[i].Name == "" {
					continue
				}
				sta.PositionalTitles[i].StaffId = sta.StaffId
				positionalTitles = append(positionalTitles, sta.PositionalTitles[i])
			}
			err = tx.Model(sta).Association("PositionalTitles").Replace(&positionalTitles)
			if err != nil {
				return err
			}
		}

		if _, ok := dontPermission["Staffarchive.Edit.WorkPost"]; !ok {
			err = tx.Where("StaffArchiveId = ?", sta.Id).Delete(&StaffHasWorkPost{}).Error
			if err != nil {
				return err
			}
			var workPosts []StaffHasWorkPost
			for i := range sta.WorkPosts {
				if sta.WorkPosts[i].WorkPostId == 0 {
					continue
				}
				sta.WorkPosts[i].StaffId = sta.StaffId
				if sta.WorkPosts[i].HumanRelationId == 0 {
					sta.WorkPosts[i].HumanRelationId = sta.WorkPosts[i].CorporationId
				}
				workPosts = append(workPosts, sta.WorkPosts[i])
			}

			err = tx.Model(sta).Association("WorkPosts").Replace(&workPosts)
			if err != nil {
				return err
			}
		}

		if _, ok := dontPermission["Staffarchive.Edit.LaborContract"]; !ok {
			err = tx.Where("StaffArchiveId = ?", sta.Id).Delete(&StaffLaborContract{}).Error
			if err != nil {
				return err
			}
			for i := range sta.LaborContracts {
				sta.LaborContracts[i].StaffId = sta.StaffId
			}
			err = tx.Model(sta).Association("LaborContracts").Replace(sta.LaborContracts)
			if err != nil {
				return err
			}
		}

		if _, ok := dontPermission["Staffarchive.Edit.PunishmentRecord"]; !ok {
			err = tx.Where("StaffArchiveId = ?", sta.Id).Delete(&StaffPunishmentRecord{}).Error
			if err != nil {
				return err
			}

			for i := range sta.PunishmentRecords {
				sta.PunishmentRecords[i].StaffId = sta.StaffId
			}
			err = tx.Model(sta).Association("PunishmentRecords").Replace(sta.PunishmentRecords)
			if err != nil {
				return err
			}
		}

		if _, ok := dontPermission["Staffarchive.Edit.RewardRecord"]; !ok {
			err = tx.Where("StaffArchiveId = ?", sta.Id).Delete(&StaffRewardRecord{}).Error
			if err != nil {
				return err
			}
			for i := range sta.RewardRecords {
				sta.RewardRecords[i].StaffId = sta.StaffId
			}
			err = tx.Model(sta).Association("RewardRecords").Replace(sta.RewardRecords)
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func (sta *StaffArchive) UpdateMine() error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Model(&StaffArchive{}).Select("Age", "BirthDate", "ResidenceAttr", "Address", "MarriageStatus", "BearStatus", "HighestEdu", "ProbationEndAt", "BaseFilePath").Where("Id = ?", sta.Id).Save(sta).Error

		if err != nil {
			return err
		}

		err = model.DB().Where("StaffArchiveId = ?", sta.Id).Delete(&StaffEducation{}).Error
		if err != nil {
			return err
		}
		var educations []StaffEducation
		for i := range sta.Educations {
			if sta.Educations[i].Edu == 0 && sta.Educations[i].School == "" {
				continue
			}
			sta.Educations[i].StaffId = sta.StaffId
			educations = append(educations, sta.Educations[i])
		}
		err = tx.Model(sta).Association("Educations").Replace(&educations)
		if err != nil {
			return err
		}

		err = model.DB().Where("StaffArchiveId = ?", sta.Id).Delete(&StaffFamilyMember{}).Error
		if err != nil {
			return err
		}
		var familyMembers []StaffFamilyMember
		for i := range sta.FamilyMembers {
			if sta.FamilyMembers[i].Name == "" && sta.FamilyMembers[i].Contact == "" {
				continue
			}
			sta.FamilyMembers[i].StaffId = sta.StaffId
			familyMembers = append(familyMembers, sta.FamilyMembers[i])
		}
		err = tx.Model(sta).Association("FamilyMembers").Replace(&familyMembers)
		if err != nil {
			return err
		}

		err = model.DB().Where("StaffArchiveId = ?", sta.Id).Delete(&StaffSkill{}).Error
		if err != nil {
			return err
		}
		var skills []StaffSkill
		for i := range sta.Skills {
			if sta.Skills[i].Name == "" {
				continue
			}
			sta.Skills[i].StaffId = sta.StaffId
			skills = append(skills, sta.Skills[i])
		}
		err = tx.Model(sta).Association("Skills").Replace(&skills)
		if err != nil {
			return err
		}

		err = model.DB().Where("StaffArchiveId = ?", sta.Id).Delete(&StaffCertificate{}).Error
		if err != nil {
			return err
		}
		var certificates []StaffCertificate
		for i := range sta.Certificates {
			if sta.Certificates[i].Name == "" {
				continue
			}
			sta.Certificates[i].StaffId = sta.StaffId
			certificates = append(certificates, sta.Certificates[i])
		}
		err = tx.Model(sta).Association("Certificates").Replace(&certificates)
		if err != nil {
			return err
		}
		return nil
	})
}

func (sta *StaffArchive) UpdateProbationEndAt(at time.Time) error {
	return model.DB().Model(&StaffArchive{}).Where("Id = ?", sta.Id).Update("ProbationEndAt", at.Format(model.TimeFormat)).Error
}

func (sta *StaffArchive) UpdateRetireMoreByIds(ids []int64, retireMore string) error {
	return model.DB().Model(&StaffArchive{}).Where("Id IN ?", ids).Update("RetireMore", retireMore).Error
}

func (sta *StaffArchive) UpdateEditApplyStatus(tx *gorm.DB, status int64) error {
	return tx.Model(&StaffArchive{}).Where("Id = ?", sta.Id).Update("EditApplyStatus", status).Error
}

func (sta *StaffArchive) UpdateEditHeadImgStatus(status int64, rejectReason string) error {
	return model.DB().Model(&StaffArchive{}).Where("Id = ?", sta.Id).Update("HeadImgState", status).Update("RejectReason", rejectReason).Error
}

// GetProbationExpireStaff 获取在时间范围内适用即将到期的员工
func (sta *StaffArchive) GetProbationExpireStaff(staffIds []int64, start, end time.Time) []StaffArchive {
	var archives []StaffArchive
	model.DB().Model(&StaffArchive{}).Where("staffId IN ? AND ProbationEndAt >= ? AND ProbationEndAt <= ?", staffIds, start.Format(model.TimeFormat), end.Format(model.TimeFormat)).Find(&archives)
	return archives
}

type ErpWhere struct {
	ResidenceAttr       int64            `json:"ResidenceAttr"`       //户口性质
	HighestEduArr       []int64          `json:"HighestEduArr"`       //最高学历 多选
	MarriageStatusArr   []int64          `json:"MarriageStatusArr"`   //婚姻状态 多选
	BearStatusArr       []int64          `json:"BearStatusArr"`       //生育情况 多选
	Address             string           `json:"Address"`             //家庭住址
	HumanRelationIds    []int64          `json:"HumanRelationIds"`    //人事关系 多选
	EmploymentCateArr   []int64          `json:"EmploymentCateArr"`   //用工类型 多选
	StartProbationEndAt *model.LocalTime `json:"StartProbationEndAt"` //开始试用期结束时间
	EndProbationEndAt   *model.LocalTime `json:"EndProbationEndAt"`   //结束使用去结束时间

	WorkPost      string           `json:"WorkPost"`      //岗位
	WorkPostIds   []int64          `json:"WorkPostIds"`   //岗位ID
	StartWorkAt   *model.LocalTime `json:"StartWorkAt"`   //开始任职时间
	EndWorkAt     *model.LocalTime `json:"EndWorkAt"`     //结束任职时间
	PositionLevel string           `json:"PositionLevel"` //职位等级

	PositionalTitle string `json:"PositionalTitle"` //职称名称
	Skill           string `json:"Skill"`           //技能
	Major           string `json:"Major"`           //专业
	School          string `json:"School"`          //学校

	CertificateName    string           `json:"CertificateName"`    //资格证名称
	StartCertificateAt *model.LocalTime `json:"StartCertificateAt"` //开始资格证时间
	EndCertificateAt   *model.LocalTime `json:"EndCertificateAt"`   //结束资格证时间

}

func (sta *StaffArchive) GetBy(staffIds []int64, where ErpWhere, paginator model.Paginator) ([]StaffArchive, int64) {
	tx := model.DB().Model(&StaffArchive{}).Preload("Educations").
		Preload("FamilyMembers").
		Preload("Skills").
		Preload("Certificates").
		Preload("PositionalTitles").
		Preload("WorkPosts").
		Preload("LaborContracts", func(db *gorm.DB) *gorm.DB {
			return db.Order("StartAt DESC")
		}).
		Preload("PunishmentRecords").
		Preload("RewardRecords").
		Where("StaffId IN ?", staffIds)

	//户口性质
	if where.ResidenceAttr > 0 {
		tx = tx.Where("ResidenceAttr = ?", where.ResidenceAttr)
	}

	//学历
	if len(where.HighestEduArr) > 0 {
		tx = tx.Where("HighestEdu IN ?", where.HighestEduArr)
	}

	//婚姻状态
	if len(where.MarriageStatusArr) > 0 {
		tx = tx.Where("MarriageStatus IN ?", where.MarriageStatusArr)
	}

	//生育状况
	if len(where.BearStatusArr) > 0 {
		tx = tx.Where("BearStatus IN ?", where.BearStatusArr)
	}

	//地址
	if where.Address != "" {
		tx = tx.Where("Address LIKE ?", "%"+where.Address+"%")
	}

	//人事关系
	if len(where.HumanRelationIds) > 0 {
		tx = tx.Where("HumanRelationId IN ?", where.HumanRelationIds)
	}

	//用工类型
	if len(where.EmploymentCateArr) > 0 {
		tx = tx.Where("EmploymentCate IN ?", where.EmploymentCateArr)
	}

	if where.StartProbationEndAt != nil && !time.Time(*where.StartProbationEndAt).IsZero() {
		tx = tx.Where("ProbationEndAt >= ?", time.Time(*where.StartProbationEndAt).Format(model.DateFormat))
	}

	if where.EndProbationEndAt != nil && !time.Time(*where.EndProbationEndAt).IsZero() {
		tx = tx.Where("ProbationEndAt <= ?", time.Time(*where.EndProbationEndAt).Format(model.DateFormat))
	}

	//岗位、任职时间、职位等级
	if len(where.WorkPostIds) > 0 || (where.StartWorkAt != nil && !time.Time(*where.StartWorkAt).IsZero()) || (where.EndWorkAt != nil && !time.Time(*where.EndWorkAt).IsZero()) || where.PositionLevel != "" {
		childTx := model.DB().Model(&StaffHasWorkPost{}).Select("StaffArchiveId").Where("PositionType = ?", util.PositionTypeMain)
		if len(where.WorkPostIds) > 0 {
			childTx = childTx.Where("WorkPostId IN ?", where.WorkPostIds).Where("IsNowJob = ?", util.IsNowJob)
		}
		if where.StartWorkAt != nil && !time.Time(*where.StartWorkAt).IsZero() {
			childTx = childTx.Where("StartAt >= ?", time.Time(*where.StartWorkAt).Format(model.DateFormat))
		}

		if where.EndWorkAt != nil && !time.Time(*where.EndWorkAt).IsZero() {
			childTx = childTx.Where("StartAt <= ?", time.Time(*where.EndWorkAt).Format(model.DateFormat))
		}

		if where.PositionLevel != "" {
			childTx = childTx.Where("PositionLevel LIKE ?", "%"+where.PositionLevel+"%")
		}
		tx = tx.Where("Id IN (?)", childTx)
	}

	//职称
	if where.PositionalTitle != "" {
		tx = tx.Where("Id IN (?)", model.DB().Model(&StaffPositionalTitle{}).Select("StaffArchiveId").Where("Name LIKE ?", "%"+where.PositionalTitle+"%"))
	}

	//技能
	if where.Skill != "" {
		tx = tx.Where("Id IN (?)", model.DB().Model(&StaffSkill{}).Select("StaffArchiveId").Where("Name LIKE ?", "%"+where.Skill+"%"))
	}

	//专业、学校
	if where.Major != "" || where.School != "" {
		childTx := model.DB().Model(&StaffEducation{}).Select("StaffArchiveId")

		if where.Major != "" {
			childTx = childTx.Where("Major LIKE ?", "%"+where.Major+"%")
		}
		if where.School != "" {
			childTx = childTx.Where("School LIKE ?", "%"+where.School+"%")
		}
		tx = tx.Where("Id IN (?)", childTx)
	}

	//资格证名称、资格证到期时间
	if len(where.CertificateName) > 0 || (where.StartCertificateAt != nil && !time.Time(*where.StartCertificateAt).IsZero()) || (where.EndCertificateAt != nil && !time.Time(*where.EndCertificateAt).IsZero()) {
		childTx := model.DB().Model(&StaffCertificate{}).Select("StaffArchiveId")

		if where.Major != "" {
			childTx = childTx.Where("Name LIKE ?", "%"+where.CertificateName+"%")
		}

		if where.StartCertificateAt != nil && !time.Time(*where.StartCertificateAt).IsZero() {
			childTx = childTx.Where("EndAt >= ?", time.Time(*where.StartCertificateAt).Format(model.DateFormat))
		}

		if where.EndCertificateAt != nil && !time.Time(*where.EndCertificateAt).IsZero() {
			childTx = childTx.Where("EndAt <= ?", time.Time(*where.EndCertificateAt).Format(model.DateFormat))
		}

		tx = tx.Where("Id IN (?)", childTx)
	}

	var count int64
	tx.Count(&count)

	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	var archives []StaffArchive
	tx.Order("Id ASC").Find(&archives)
	return archives, count
}

func (sta *StaffArchive) GetAll() []StaffArchive {
	var archives []StaffArchive
	model.DB().Preload("Educations").
		Preload("FamilyMembers").
		Preload("Skills").
		Preload("Certificates").
		Preload("PositionalTitles").
		Preload("WorkPosts").
		Preload("LaborContracts").Order("Id ASC").Find(&archives)
	return archives
}

func (sta *StaffArchive) FindById(id int64) error {
	return model.DB().Model(&StaffArchive{}).
		Preload("Educations", func(db *gorm.DB) *gorm.DB {
			return db.Order("StartAt DESC")
		}).
		Preload("FamilyMembers").
		Preload("Skills").
		Preload("Certificates").
		Preload("PositionalTitles").
		Preload("RewardRecords").
		Preload("PunishmentRecords").
		Preload("WorkPosts", func(db *gorm.DB) *gorm.DB {
			return db.Order("PositionType ASC").Order("IsNowJob ASC").Order("StartAt DESC")
		}).
		Preload("LaborContracts", func(db *gorm.DB) *gorm.DB {
			return db.Order("StartAt DESC")
		}).
		Where("Id = ?", id).First(sta).Error
}

func (sta *StaffArchive) FindByStaffId(staffId int64) error {
	return model.DB().Model(&StaffArchive{}).
		Preload("WorkPosts", func(db *gorm.DB) *gorm.DB {
			return db.Order("PositionType ASC").Order("IsNowJob ASC").Order("StartAt DESC")
		}).
		Preload("LaborContracts", func(db *gorm.DB) *gorm.DB {
			return db.Order("StartAt DESC")
		}).
		Where("StaffId = ?", staffId).First(sta).Error
}
func (sta *StaffArchive) FirstByStaffId(staffId int64) StaffArchive {
	var staff StaffArchive
	model.DB().Model(&StaffArchive{}).Where("StaffId = ?", staffId).First(&staff)
	return staff
}

func (sta *StaffArchive) FindMineInfoByStaffId(staffId int64) error {
	return model.DB().Model(&StaffArchive{}).
		Preload("Educations", func(db *gorm.DB) *gorm.DB {
			return db.Order("StartAt DESC")
		}).
		Preload("FamilyMembers").
		Preload("Skills").
		Preload("Certificates").
		Where("StaffId = ?", staffId).First(sta).Error
}

func (sta *StaffArchive) ExistByStaffId(staffId int64) bool {
	var count int64
	model.DB().Model(&StaffArchive{}).Where("StaffId = ?", staffId).Count(&count)

	return count > 0
}

// UpdateCorporation 更新机构和人事关系
func (sta *StaffArchive) UpdateCorporation(corporationId, humanRelationId int64) error {
	corporation := rpc.GetCorporationDetailById(context.Background(), corporationId)

	if corporation != nil {
		sta.GroupId = corporation.GroupId
		sta.CompanyId = corporation.CompanyId
		sta.BranchId = corporation.BranchId
		sta.DepartmentId = corporation.DepartmentId
		sta.FleetId = corporation.FleetId
	}
	sta.HumanRelationId = humanRelationId

	return model.DB().Model(&StaffArchive{}).Select("GroupId", "CompanyId", "BranchId", "DepartmentId", "FleetId", "HumanRelationId").Where("Id = ?", sta.Id).Updates(sta).Error
}

// MasterDataFieldForBasic 主数据基础数据字段，ERP不保存此信息
type MasterDataFieldForBasic struct {
	Name               string           `json:"Name" gorm:"-" validate:"required"`       //姓名
	Gender             int64            `json:"Gender" gorm:"-"`                         //性别 1男 2女
	NativePlace        string           `json:"NativePlace" gorm:"-"`                    //籍贯
	Nation             string           `json:"Nation" gorm:"-"`                         //民族
	IdentityId         string           `json:"IdentityId" gorm:"-" validate:"required"` //身份证
	Contact            string           `json:"Contact" gorm:"-" validate:"required"`    //联系方式
	HealthStatus       int64            `json:"HealthStatus" gorm:"-"`                   //健康情况 1-健康,2-良好,3-一般,4-不健康,5-残疾,6-生理缺陷
	IsReversionSoldier int64            `json:"IsReversionSoldier" gorm:"-"`             //是否复退转军人 1-是,2-否
	ReversionAt        *model.LocalTime `json:"ReversionAt" gorm:"-"`                    //复退转时间
	PoliticalIdentity  int64            `json:"PoliticalIdentity" gorm:"-"`              //政治面貌 1-中共党员,2-中共预备党员,3-共青团员,4-其他,5-群众,6-民革党员,7-民盟盟员,8-民建会员,9-民进会员,10-农工党党员,11-致公党党员,12-九三学社社员,13-台盟盟员,14-无党派民主人士
	JoinPartyAt        *model.LocalTime `json:"JoinPartyAt" gorm:"-"`                    //入党时间
	DrivingCode        string           `json:"DrivingCode" gorm:"-"`                    //驾驶证号
	DrivingModel       string           `json:"DrivingModel" gorm:"-"`                   //准驾车型 多选 1-A1,2-A2,3-A3,4-B1,5-B2,6-C1,7-C2,8-C3,9-D,10-E
	HeadImg            HeadImg          `json:"HeadImg" gorm:"-"`                        //头像照片
	//LineId             int64            `json:"LineId" gorm:"-"`                         //线路
	//AttendanceCard     string           `json:"AttendanceCard" gorm:"-"`                 //考勤卡号
}

type HeadImg struct {
	Id     int64  `json:"Id"`
	Name   string `json:"Name"`
	Path   string `json:"Path"`
	Type   int64  `json:"Type"`
	Url    string `json:"Url"`
	Suffix string `json:"Suffix"`
}

// MasterDataFieldForJob 主数据在职数据字段，ERP不保存此信息
type MasterDataFieldForJob struct {
	JobNumber       string           `json:"JobNumber" gorm:"-" validate:"required"`     //工号
	JobStatus       int64            `json:"JobStatus" gorm:"-" validate:"required"`     //工作状态 1-在职,2-离职,3-试用期,4-退休,5-退休返聘
	CorporationId   int64            `json:"CorporationId" gorm:"-" validate:"required"` //机构
	CorporationName string           `json:"CorporationName" gorm:"-"`                   //机构名称
	JoinCompanyWay  int64            `json:"JoinCompanyWay" gorm:"-"`                    //进入公司途径 1-在编,2-公开招聘,3-派遣,4-军转安置,5-借用,6-自主录用,7-自主招聘,8-转岗,9-竞聘上岗,10-其他
	WorkPostType    int64            `json:"WorkPostType" gorm:"-"`                      //岗位类型 1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他,9-修理工
	JoinAt          *model.LocalTime `json:"JoinAt" gorm:"-"`                            //入职时间、进现单位时间
	StartJobAt      *model.LocalTime `json:"StartJobAt" gorm:"-"`                        //参加工作时间、工龄起算时间
	RetireAt        *model.LocalTime `json:"RetireAt" gorm:"-"`                          //退休时间
}

// StaffCertificate 从业资格证信息
type StaffCertificate struct {
	model.PkId
	StaffArchiveId  int64            `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID"` //关联员工档案表主键ID
	StaffId         int64            `json:"StaffId" gorm:"column:staffid;type:integer;comment:员工Id 关联主数据人员主键"`           //员工Id 关联主数据人员主键
	Name            string           `json:"Name" gorm:"column:name;type:varchar(64);comment:资格证名称"`                      //资格证名称
	Code            string           `json:"Code" gorm:"column:code;type:varchar(64);comment:资格证编号"`                      //资格证编号
	StartAt         *model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:发证日期"`                   //发证日期
	EndAt           *model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:到期日期"`                       //到期日期
	AuthorizeOffice string           `json:"AuthorizeOffice" gorm:"column:authorizeoffice;type:varchar(64);comment:核发机关"` //核发机关
	FilePath        model.JSON       `json:"FilePath" gorm:"column:filepath;type:json;comment:从业资格证附件"`                   //从业资格证附件
	model.Timestamp
}

func (cert *StaffCertificate) BeforeCreate(tx *gorm.DB) (err error) {
	if cert.Id == 0 {
		cert.Id = model.Id()
	}
	return
}

// TitleSkill 职称、技能共用字段
type TitleSkill struct {
	StaffArchiveId int64      `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID"` //关联员工档案表主键ID
	StaffId        int64      `json:"StaffId" gorm:"column:staffid;type:integer;comment:员工Id"`                     //员工Id 关联主数据人员主键
	Name           string     `json:"Name" gorm:"column:name;type:varchar(64);comment:名称"`                         //职称名称、技能级别
	More           string     `json:"More" gorm:"column:more;type:varchar;comment:备注"`                             //备注
	FilePath       model.JSON `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                        //附件
}

// StaffPositionalTitle 员工职称信息
type StaffPositionalTitle struct {
	model.PkId
	TitleSkill
	Level             int64           `json:"Level" gorm:"column:level;type:smallint;comment:职称级别 1-正高级、2-副高级、3-中级、4-初级、5-未知"` //职称级别 1-正高级、2-副高级、3-中级、4-初级、5-未知
	CertificateName   string          `json:"CertificateName" gorm:"column:certificatename;type:varchar;comment:证书名称"`         //证书名称
	CertificateOffice string          `json:"CertificateOffice" gorm:"column:certificateoffice;type:varchar;comment:发证机构"`     //发证机构
	CertificateCode   string          `json:"CertificateCode" gorm:"column:certificatecode;type:varchar;comment:证书编号"`         //证书编号
	CertificateDate   model.LocalTime `json:"CertificateDate" gorm:"column:certificatedate;type:timestamp;comment:发证日期"`
	model.Timestamp
}

func (pt *StaffPositionalTitle) BeforeCreate(tx *gorm.DB) (err error) {
	if pt.Id == 0 {
		pt.Id = model.Id()
	}

	return
}

func (pt *StaffPositionalTitle) Create() error {
	return model.DB().Create(pt).Error
}

// StaffSkill 员工技能信息
type StaffSkill struct {
	model.PkId
	TitleSkill
	model.Timestamp
}

func (sk *StaffSkill) BeforeCreate(tx *gorm.DB) (err error) {
	if sk.Id == 0 {
		sk.Id = model.Id()
	}

	return
}

// StaffEducation 员工学历
type StaffEducation struct {
	model.PkId
	StaffArchiveId int64            `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID"`    //关联员工档案表主键ID
	StaffId        int64            `json:"StaffId" gorm:"column:staffid;type:integer;comment:员工Id"`                        //员工Id 关联主数据人员主键
	Edu            int64            `json:"Edu" gorm:"column:edu;type:integer;comment:学历"`                                  //学历 1-博士研究生 2-硕士研究生 3-本科 4-大专 5-中专 6-高中及以下 7-省委党校研究生  8-在职博士生 9-在职研究生 10-在职本科 11-在职大专
	AcademicDegree int64            `json:"AcademicDegree" gorm:"column:academicdegree;type:smallint;comment:学位;default:0"` //1-博士 2-硕士  3-学士  4-其他
	School         string           `json:"School" gorm:"column:school;type:varchar;comment:毕业学校"`                          //毕业学校
	Major          string           `json:"Major" gorm:"column:major;type:varchar;comment:专业"`                              //专业
	Type           int64            `json:"Type" gorm:"column:type;type:smallint;comment:学历类型"`                             //学历类型  1全日制  2在职
	StartAt        *model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:开始时间"`                      //开始时间
	EndAt          *model.LocalTime `json:"EndAt" gorm:"column:endat;type:timestamp;comment:结束时间"`                          //结束时间
	FilePath       model.JSON       `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                           //附件
	model.Timestamp
}

const (
	Education13 = 13 //在职大专
	Education14 = 14 //在职本科
	Education15 = 15 //在职硕士
	Education16 = 16 //在职博士
	FullTimeEdu = 1  //全日制
	OnJobEdu    = 2  //在职
)

// BeforeCreate 创建钩子函数
func (edu *StaffEducation) BeforeCreate(tx *gorm.DB) (err error) {
	if edu.Id == 0 {
		edu.Id = model.Id()
	}

	//edu.Type = FullTimeEdu
	//if edu.Edu == Education13 || edu.Edu == Education14 || edu.Edu == Education15 || edu.Edu == Education16 {
	//	edu.Type = OnJobEdu
	//}
	return nil
}

// StaffFamilyMember 员工家庭信息
type StaffFamilyMember struct {
	model.PkId
	StaffArchiveId int64  `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID"` //关联员工档案表主键ID
	StaffId        int64  `json:"StaffId" gorm:"column:staffid;type:integer;comment:员工Id "`                    //员工Id 关联主数据人员主键
	Name           string `json:"Name" gorm:"column:name;type:varchar(64);comment:家庭联系人"`                      //家庭联系人
	Relationship   string `json:"Relationship" gorm:"column:relationship;type:varchar(64);comment:关系"`         //关系
	Contact        string `json:"Contact" gorm:"column:contact;type:varchar(64);comment:联系方式"`                 //联系方式
	IsMainContact  int64  `json:"IsMainContact" gorm:"column:ismaincontact;type:smallint;comment:是否紧急联系人"`     //是否紧急联系人  1是 2不是
	model.Timestamp
}

func (mem *StaffFamilyMember) BeforeCreate(tx *gorm.DB) (err error) {
	if mem.Id == 0 {
		mem.Id = model.Id()
	}

	return
}

// StaffHasWorkPost 员工现任职务、历任职务信息
type StaffHasWorkPost struct {
	model.PkId
	StaffArchiveId    int64            `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID"` //关联员工档案表主键ID
	StaffId           int64            `json:"StaffId" gorm:"column:staffid;type:integer;comment:员工Id"`                     //员工Id 关联主数据人员主键
	CorporationId     int64            `json:"CorporationId" gorm:"column:corporationid;type:bigint;comment:机构Id"`          //机构Id
	CorporationName   string           `json:"CorporationName" gorm:"-"`                                                    //机构名
	PreCorporationId  int64            `json:"-" gorm:"column:precorporationid;type:bigint;comment:所属部门"`                   //调动类型为借用、借调、挂职时，记录之前的所属部门
	HumanRelationId   int64            `json:"HumanRelationId" gorm:"column:humanrelationid;type:bigint;comment:人事关系"`      //人事关系，关联机构Id
	HumanRelationName string           `json:"HumanRelationName" gorm:"-"`                                                  //人事关系名称
	TransferType      int64            `json:"TransferType" gorm:"column:transfertype;type:smallint;comment:职位调动类型"`        //职位调动类型 1-调动,2-任职,3-免职,4-任免,5-借调,50-借调结束,6-借用,60-借用结束,7-挂职,70-挂职结束
	WorkPostType      int64            `json:"WorkPostType" gorm:"column:workposttype;type:integer;comment:岗位类型"`           //岗位类型 1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他,9修理工
	WorkPostId        int64            `json:"WorkPostId" gorm:"column:workpostid;type:bigint;comment:岗位"`                  //岗位
	WorkPostName      string           `json:"WorkPostName" gorm:"-"`                                                       //岗位名
	PositionLevel     string           `json:"PositionLevel" gorm:"column:positionlevel;type:varchar(32);comment:职位等级"`     //职位等级
	PositionType      int64            `json:"PositionType" gorm:"column:positiontype;type:smallint;comment:任职类型"`          //任职类型 1-主职,2-兼职
	IsNowJob          int64            `json:"IsNowJob" gorm:"column:isnowjob;type:smallint;default:1;comment:是否现任职务"`      //现任职务:1  历任职务:2
	StartAt           *model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:任职时间"`                   //任职时间
	More              string           `json:"More" gorm:"column:more;type:varchar;comment:备注"`                             //备注
	FilePath          model.JSON       `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                        //附件
	model.Timestamp
}

func (swp *StaffHasWorkPost) GetByArchiveId(archiveId int64) []StaffHasWorkPost {
	var posts []StaffHasWorkPost
	model.DB().Model(&StaffHasWorkPost{}).Where("StaffArchiveId = ?", archiveId).Order("PositionType ASC").Order("IsNowJob ASC").Order("StartAt DESC").Find(&posts)
	return posts
}

func (swp *StaffHasWorkPost) GetByStaffIds(staffIds []int64) []StaffHasWorkPost {
	var posts []StaffHasWorkPost
	model.DB().Model(&StaffHasWorkPost{}).Where("StaffId IN ? AND IsNowJob = ?", staffIds, util.IsNowJob).Find(&posts)
	return posts
}

func (swp *StaffHasWorkPost) GetNowWorkPostIdByArchiveId(archiveId int64) []int64 {
	var workPostIds []int64
	model.DB().Model(&StaffHasWorkPost{}).Where("StaffArchiveId = ? AND IsNowJob = ?", archiveId, util.IsNowJob).Order("PositionType ASC").Order("StartAt DESC").Pluck("Id", &workPostIds)
	return workPostIds
}

func (swp *StaffHasWorkPost) GetStaffIdByWorkPostId(workPostIds []int64) []int64 {
	var staffIds []int64
	model.DB().Model(&StaffHasWorkPost{}).Where("WorkPostId IN ? AND IsNowJob = ?", workPostIds, util.IsNowJob).Pluck("StaffId", &staffIds)
	return staffIds
}

func (swp *StaffHasWorkPost) BeforeCreate(tx *gorm.DB) (err error) {
	if swp.Id == 0 {
		swp.Id = model.Id()
	}

	return
}

// StaffPunishmentRecord 员工处罚记录
type StaffPunishmentRecord struct {
	model.PkId
	StaffArchiveId int64           `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID"` //关联员工档案表主键ID
	StaffId        int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:员工Id"`                     //员工Id 关联主数据人员主键
	StartAt        model.LocalTime `json:"StartAt" gorm:"column:startAt;type:timestamp;comment:时间"`                     //时间
	Desc           string          `json:"Desc" gorm:"column:desc;type:varchar;comment:处罚措施"`                           //处罚措施
	More           string          `json:"More" gorm:"column:more;type:varchar;comment:备注描述"`                           //备注描述
	FilePath       model.JSON      `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                        //附件
	model.Timestamp
}

func (spr *StaffPunishmentRecord) BeforeCreate(tx *gorm.DB) (err error) {
	if spr.Id == 0 {
		spr.Id = model.Id()
	}

	return
}

// StaffRewardRecord 员工奖励记录
type StaffRewardRecord struct {
	model.PkId
	StaffArchiveId int64           `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID"` //关联员工档案表主键ID
	StaffId        int64           `json:"StaffId" gorm:"column:staffid;type:integer;comment:员工Id"`                     //员工Id 关联主数据人员主键
	StartAt        model.LocalTime `json:"StartAt" gorm:"column:startAt;type:timestamp;comment:时间"`                     //时间
	Desc           string          `json:"Desc" gorm:"column:desc;type:varchar;comment:奖励措施"`                           //奖励措施
	More           string          `json:"More" gorm:"column:more;type:varchar;comment:备注描述"`                           //备注描述
	FilePath       model.JSON      `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                        //附件
	model.Timestamp
}

func (srr *StaffRewardRecord) BeforeCreate(tx *gorm.DB) (err error) {
	if srr.Id == 0 {
		srr.Id = model.Id()
	}

	return
}
