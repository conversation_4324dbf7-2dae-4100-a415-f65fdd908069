package hr

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"time"

	"gorm.io/gorm"
)

// StaffQuit 离职管理
type StaffQuit struct {
	model.PkId
	Topic         string            `json:"Topic" gorm:"column:topic;type:varchar;comment:描述"` //描述
	More          string            `json:"More" gorm:"column:more;type:varchar;comment:备注"`   //备注
	Records       []StaffQuitRecord `json:"Records"`
	IsMustProcess bool              `json:"IsMustProcess" gorm:"-"` //是否需要走流程
	model.OpUser
	model.Timestamp
}

func (sq *StaffQuit) BeforeCreate(tx *gorm.DB) error {
	sq.Id = model.Id()
	return nil
}

func (sq *StaffQuit) Create() error {
	return model.DB().Create(sq).Error
}

func (sq *StaffQuit) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(sq).Error
}

func (sq *StaffQuit) GetBy(corporationIds []int64, keyword string) []StaffQuit {
	var records []StaffQuit
	var where = func(db *gorm.DB) *gorm.DB {
		db = db.Scopes(model.WhereCorporations(corporationIds))
		if keyword != "" {
			db = db.Where("staffName LIKE ?", "%"+keyword+"%")
		}
		return db
	}
	model.DB().Model(&StaffQuit{}).Preload("Records", func(db *gorm.DB) *gorm.DB {
		return where(db)
	}).Where("EXISTS (?)", model.DB().Model(&StaffQuitRecord{}).Select("StaffQuitId").Scopes(where).Where("staff_quits.Id = StaffQuitId")).Find(&records)

	return records
}

func (sq *StaffQuit) FindBy(id int64) error {
	return model.DB().Model(&StaffQuit{}).Preload("Records").Where("Id = ?", id).First(&sq).Error
}

type StaffQuitRecord struct {
	model.PkId
	model.Corporations
	StaffQuitId           int64            `json:"StaffQuitId" gorm:"column:staffquitid;type:bigint;comment:离职ID"`
	StaffArchiveId        int64            `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID"` //关联员工档案表主键ID
	StaffId               int64            `json:"StaffId" gorm:"column:staffid;type:bigint;comment:人员ID" validate:"required"`
	StaffName             string           `json:"StaffName" gorm:"column:staffname;type:varchar;comment:姓名"`
	WorkPostName          string           `json:"WorkPostName" gorm:"column:workpostname;type:varchar;comment:岗位名称"`
	WorkPostType          int64            `json:"WorkPostType" gorm:"column:workposttype;type:integer;comment:岗位类型"`
	JobNumber             string           `json:"JobNumber" gorm:"column:jobnumber;type:varchar;comment:工号"`
	ContractEndAt         *model.LocalTime `json:"ContractEndAt" gorm:"column:contractendat;type:timestamp;comment:合同终止时间" validate:"required"`             //合同终止时间
	JoinCompanyAt         *model.LocalTime `json:"JoinCompanyAt" gorm:"column:joincompanyat;type:timestamp;comment:参加工作时间"`                                 //参加工作时间
	RelieveContractAt     *model.LocalTime `json:"RelieveContractAt" gorm:"column:relievecontractat;type:timestamp;comment:解除劳动合同时间" validate:"required"`   //解除劳动合同时间
	RelieveContractType   int64            `json:"RelieveContractType" gorm:"column:relievecontracttype;type:smallint;comment:合同解除类型 " validate:"required"` //合同解除类型 1-自主解除劳动合同，2-公司解除，3—协商解除 4去世 5试用期不予转正
	YearHolidayRemain     int64            `json:"YearHolidayRemain" gorm:"column:yearholidayremain;type:integer;comment:年休假剩余天数"`                          //年休假剩余天数
	ExchangeHolidayRemain int64            `json:"ExchangeHolidayRemain" gorm:"column:exchangeholidayremain;type:integer;comment:调休假剩余天数"`                  //调休假剩余天数
	FilePath              model.JSON       `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                                                    //附件
	ApplyStatus           int64            `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;comment:审批状态;default:0"`                              //审批状态 0还未发起审批  1审批中  2审批完成  3审批撤销（取消）
	ApplyResult           int64            `json:"ApplyResult" gorm:"column:applyresult;type:smallint;default:0;comment:流程审批结果 "`                           //审批结果  0没结果  1审批通过  2拒绝

	model.OpUser
	model.Timestamp

	OpIp          string `json:"OpIp" gorm:"-"`
	WorkPostId    int64  `json:"WorkPostId" gorm:"-"`
	CorporationId int64  `json:"CorporationId" gorm:"-"`

	IdentityId     string `json:"IdentityId" gorm:"-"`
	FileHttpPrefix string `json:"FileHttpPrefix" gorm:"-"`
	CurrentHandler string `json:"CurrentHandler" gorm:"-"` //流程当前处理人
	HasProcess     bool   `json:"HasProcess" gorm:"-"`     //是否有流程
}

func (rec *StaffQuitRecord) TableName() string {
	return "staff_quit_records"
}

func (rec *StaffQuitRecord) ApplyStatusFieldName() string {
	return "applystatus"
}

func (rec *StaffQuitRecord) FindBy(id int64) StaffQuitRecord {
	var record StaffQuitRecord
	model.DB().Model(&StaffQuitRecord{}).Where("Id = ?", id).First(&record)
	return record
}

func (rec *StaffQuitRecord) UpdateApplyStatus(tx *gorm.DB, id, status, result int64) error {
	return tx.Model(&StaffQuitRecord{}).Where("Id = ?", id).Updates(map[string]interface{}{
		"applystatus": status,
		"applyresult": result,
	}).Error
}

func (rec *StaffQuitRecord) BeforeCreate(tx *gorm.DB) error {
	rec.Id = model.Id()
	return nil
}

func (rec *StaffQuitRecord) Create() error {
	return model.DB().Create(&rec).Error
}

func (rec *StaffQuitRecord) BatchCreate(items []StaffQuitRecord) error {
	return model.DB().Create(&items).Error
}

func (rec *StaffQuitRecord) TransactionDelete(tx *gorm.DB) error {
	return tx.Delete(&rec).Error
}

func (rec *StaffQuitRecord) IsExistDoingQuit(staffId int64) bool {
	var count int64
	model.DB().Model(&StaffQuitRecord{}).Where("StaffId = ? AND ApplyStatus IN ?", staffId, []int64{util.ApplyStatusForDoing, util.ApplyStatusForDone}).Count(&count)
	return count > 0
}

//func (rec *StaffQuitRecord) AfterCreate(tx *gorm.DB) error {
//	//更主数据员工状态
//	oetStaff := rpc.GetStaffWithId(context.Background(), rec.StaffId)
//	if oetStaff != nil {
//		oetStaff.WorkingState = util.JobStatusQuit
//		err := rpc.EditOetStaff(context.Background(), oetStaff)
//		if err != nil {
//			return err
//		}
//	}
//	//更新劳动合同状态
//	var archive StaffArchive
//	tx.Model(&StaffArchive{}).Where("StaffId = ?", rec.StaffId).First(&archive)
//	if archive.Id == 0 {
//		return errors.New("archive not found")
//	}
//	var contract StaffLaborContract
//	tx.Model(&StaffLaborContract{}).Where("StaffArchiveId = ?", archive.Id).Order("EndAt DESC").First(&contract)
//	err := tx.Model(&StaffLaborContract{}).Where("Id = ?", contract.Id).Update("Status", util.LaborContractRelieve).Error
//	return err
//}

func (rec *StaffQuitRecord) List(groupId int64, startAt, endAt time.Time, paginator model.Paginator) ([]StaffQuitRecord, int64, error) {
	var rsp []StaffQuitRecord
	var totalCount int64
	tx := model.DB().Model(&StaffQuitRecord{}).Where("CreatedAt >= ? AND CreatedAt < ?", startAt, endAt).Where("GroupId = ?", groupId)

	tx.Count(&totalCount)
	err := tx.Offset(paginator.Offset).Limit(paginator.Limit).Scan(&rsp).Error

	return rsp, totalCount, err
}

func (rec *StaffQuitRecord) GetByStaffIds(staffIds []int64) []StaffQuitRecord {
	var records []StaffQuitRecord
	model.DB().Model(&StaffQuitRecord{}).Where("StaffId IN ?", staffIds).Find(&records)

	return records
}

func (rec *StaffQuitRecord) GetBy(corporationIds []int64, staffName string, paginator model.Paginator) ([]StaffQuitRecord, int64) {
	var records []StaffQuitRecord
	var totalCount int64
	tx := model.DB().Model(&StaffQuitRecord{}).Scopes(model.WhereCorporations(corporationIds))
	if staffName != "" {
		tx = tx.Where("StaffName LIKE ?", "%"+staffName+"%")
	}

	tx.Count(&totalCount)

	tx.Order("RelieveContractAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&records)

	return records, totalCount
}
func (rec *StaffQuitRecord) GetAll() []StaffQuitRecord {
	var records []StaffQuitRecord
	model.DB().Model(&StaffQuitRecord{}).Find(&records)

	return records
}

func (rec *StaffQuitRecord) GetLatestByStaffId(staffId int64) StaffQuitRecord {
	var record StaffQuitRecord

	model.DB().Model(&StaffQuitRecord{}).Where("StaffId = ?", staffId).Order("CreatedAt DESC").First(&record)
	return record
}
