package hr

import (
	"app/org/scs/erpv2/api/model"
	"fmt"
	"gorm.io/gorm"
)

var StaffArchiveHistoryTablePrefix = "staff_archive_history_"

type StaffArchiveVersion struct {
	model.PkId
	TopCorporationId int64                 `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:顶级机构ID"` //顶级机构ID
	CorporationId    int64                 `json:"CorporationId" gorm:"column:corporationid;type:bigint;comment:所属机构ID"`       //所属机构ID
	Desc             string                `json:"Desc" gorm:"column:desc;type:varchar;comment:描述"`                            //描述
	TableName        string                `json:"ItemTableName" gorm:"column:tablename;type:varchar(64);comment:历史数据存的表"`     //历史数据存的表
	StaffCount       int64                 `json:"StaffCount" gorm:"column:staffcount;type:integer;comment:数据量"`               //数据量
	Histories        []StaffArchiveHistory `json:"-" gorm:"-"`
	model.OpUser
	model.Timestamp
}

func (sav *StaffArchiveVersion) BeforeCreate(db *gorm.DB) error {
	sav.Id = model.Id()
	if sav.StaffCount > 0 {
		sav.TableName = fmt.Sprintf("%s%v", StaffArchiveHistoryTablePrefix, sav.Id)
		//新增一张记录表
		return db.Table(sav.TableName).AutoMigrate(&StaffArchiveHistory{})
	}

	return nil
}

func (sav *StaffArchiveVersion) Create() error {
	return model.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Create(sav).Error
		if err != nil {
			return err
		}
		if len(sav.Histories) > 0 {
			err := tx.Table(sav.TableName).Create(sav.Histories).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func (sav *StaffArchiveVersion) FirstBy(id int64) error {
	return model.DB().First(sav, id).Error
}

func (sav *StaffArchiveVersion) GetAll() []StaffArchiveVersion {
	var records []StaffArchiveVersion
	model.DB().Model(&StaffArchiveVersion{}).Order("CreatedAt DESC").Find(&records)
	return records
}

func (sav *StaffArchiveVersion) GetHistory(paginator model.Paginator) ([]StaffArchiveHistory, int64) {
	var records []StaffArchiveHistory
	tx := model.DB().Table(sav.TableName)

	var count int64
	tx.Count(&count)

	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Offset)
	}
	tx.Order("Id ASC").Find(&records)

	return records, count
}

// StaffArchiveHistory 员工档案历史记录
type StaffArchiveHistory struct {
	model.PkId
	ErpArchive
	CorporationId      int64            `json:"CorporationId" gorm:"column:corporationid;type:bigint;comment:机构"`                  //机构
	LineId             int64            `json:"LineId" gorm:"column:lineid;type:integer;comment:所属线路ID"`                           //所属线路ID
	Name               string           `json:"Name" gorm:"column:name;type:varchar(64);comment:姓名"`                               //姓名
	Gender             int64            `json:"Gender" gorm:"column:gender;type:smallint;comment:性别"`                              //性别
	NativePlace        string           `json:"NativePlace" gorm:"column:nativeplace;type:varchar;comment:籍贯"`                     //籍贯
	Nation             string           `json:"Nation" gorm:"column:nation;type:varchar(64);comment:民族"`                           //民族
	IdentityId         string           `json:"IdentityId" gorm:"column:identityid;type:varchar(64);comment:身份证"`                  //身份证
	PoliticalIdentity  int64            `json:"PoliticalIdentity" gorm:"column:politicalidentity;type:integer;comment:政治面貌"`       //政治面貌
	Contact            string           `json:"Contact" gorm:"column:contact;type:varchar(64);comment:联系方式"`                       //联系方式
	HealthStatus       int64            `json:"HealthStatus" gorm:"column:healthstatus;type:smallint;comment:健康情况"`                //健康情况
	IsReversionSoldier int64            `json:"IsReversionSoldier" gorm:"column:isreversionsoldier;type:smallint;comment:是否复退转军人"` //是否复退转军人
	ReversionAt        *model.LocalTime `json:"ReversionAt" gorm:"column:reversionat;type:timestamp;comment:复退转时间"`                //复退转时间
	DrivingCode        string           `json:"DrivingCode" gorm:"column:drivingcode;type:varchar(64);comment:驾驶证号"`               //驾驶证号
	DrivingModel       string           `json:"DrivingModel" gorm:"column:drivingmodel;type:varchar(64);comment:准驾车型"`             //准驾车型 多个用英文逗号隔开
	JoinPartyAt        *model.LocalTime `json:"JoinPartyAt" gorm:"column:joinpartyat;type:timestamp;comment:入党时间"`                 //入党时间
	JobNumber          string           `json:"JobNumber" gorm:"column:jobnumber;type:varchar;comment:工号"`                         //工号
	JobStatus          int64            `json:"JobStatus" gorm:"column:jobstatus;type:smallint;comment:工作状态"`                      //工作状态
	JoinCompanyWay     int64            `json:"JoinCompanyWay" gorm:"column:joincompanyway;type:smallint;comment:进入公司途径"`          //进入公司途径
	JoinAt             *model.LocalTime `json:"JoinAt" gorm:"column:joinat;type:timestamp;comment:入职时间、进现单位时间"`                    //入职时间、进现单位时间
	StartJobAt         *model.LocalTime `json:"StartJobAt" gorm:"column:startjobat;type:timestamp;comment:参加工作时间"`                 //参加工作时间、工龄起算时间
	RetireAt           *model.LocalTime `json:"RetireAt" gorm:"column:retireat;type:timestamp;comment:退休时间"`                       //退休时间
	Educations         model.JSON       `json:"Educations" gorm:"column:educations;type:json;comment:学历信息"`                        //学历信息
	PositionalTitles   model.JSON       `json:"PositionalTitles" gorm:"column:positionaltitles;type:json;comment:职称信息"`            //职称信息
	Skills             model.JSON       `json:"Skills" gorm:"column:skills;type:json;comment:技能信息"`                                //技能信息
	FamilyMembers      model.JSON       `json:"FamilyMembers" gorm:"column:familymembers;type:json;comment:家庭信息"`                  //家庭信息
	Certificates       model.JSON       `json:"Certificates" gorm:"column:certificates;type:json;comment:从业资格证"`                   //从业资格证
	WorkPosts          model.JSON       `json:"WorkPosts" gorm:"column:workposts;type:json;comment:职务信息"`                          //职务信息
	LaborContracts     model.JSON       `json:"LaborContracts" gorm:"column:laborcontracts;type:json;comment:劳动合同"`                //劳动合同
}
