package hr

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

// StaffVetoRecord 一票否决
type StaffVetoRecord struct {
	model.PkId
	TopCorporationId int64           `json:"TopCorporationId" gorm:"column:topcorporationid;type:bigint;comment:机构ID"`
	StaffId          int64           `json:"StaffId" gorm:"column:staffid;type:bigint;comment:员工ID"`
	StaffName        string          `json:"StaffName" gorm:"column:staffname;type:varchar;comment:员工姓名"`
	ReportAt         model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:记录时间"`
	Reason           string          `json:"Reason" gorm:"column:reason;type:text;comment:否决原因 / 变更原因"`
	Status           int64           `json:"Status" gorm:"column:status;type:smallint;default:1;comment:是否有效  1是  2否"`
	RecordId         int64           `json:"RecordId" gorm:"column:recordid;type:bigint;comment:sceneType=2时  关联的本表记录ID"`
	SceneType        int64           `json:"SceneType" gorm:"column:scenetype;type:smallint;comment:1添加记录 2变更记录"`

	model.OpUser
	model.Timestamp
}

func (m *StaffVetoRecord) BeforeCreate(db *gorm.DB) error {
	m.Id = model.Id()
	m.CreatedAt = model.LocalTime(time.Now())
	m.UpdatedAt = model.LocalTime(time.Now())
	return nil
}

func (m *StaffVetoRecord) Create() error {
	return model.DB().Create(&m).Error
}

func (m *StaffVetoRecord) UpdateStatus(status int64) error {
	return model.DB().Model(&StaffVetoRecord{}).Where("Id = ?", m.Id).Update("Status", status).Error
}

func (m *StaffVetoRecord) GetByStaffId(staffId int64) []StaffVetoRecord {
	var records []StaffVetoRecord
	model.DB().Model(&StaffVetoRecord{}).Where("StaffId = ?", staffId).Find(&records)
	return records
}

func (m *StaffVetoRecord) FirstBy(id int64) StaffVetoRecord {
	var record StaffVetoRecord
	model.DB().Model(&StaffVetoRecord{}).Where("Id = ?", id).First(&record)
	return record
}
