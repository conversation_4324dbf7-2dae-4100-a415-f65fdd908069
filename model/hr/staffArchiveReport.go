package hr

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

type StaffArchiveDayReport struct {
	model.PkId
	model.Corporations
	Total         int64           `json:"Total" gorm:"column:total;type:integer;default:0;comment:总人数"`
	GroupTotal    model.JSON      `json:"GroupTotal" gorm:"column:grouptotal;type:json;comment:各人员类型分组下的总人数"`         //类型: 1管理  2驾驶员  3乘务员  4修理工  5辅助工  6劳务派遣
	GroupAddTotal model.JSON      `json:"GroupAddTotal" gorm:"column:groupaddtotal;type:json;comment:各人员类型分组下增加的总人数"` //类型: 1管理  2驾驶员  3乘务员  4修理工  5辅助工  6劳务派遣
	GroupSubTotal model.JSON      `json:"GroupSubTotal" gorm:"column:groupsubtotal;type:json;comment:各人员类型分组下减少的总人数"` //类型: 1管理  2驾驶员  3乘务员  4修理工  5辅助工  6劳务派遣
	ReportAt      model.LocalTime `json:"ReportAt" gorm:"column:reportat;type:timestamp;comment:统计日期"`
	model.Timestamp
}

func (sdr *StaffArchiveDayReport) BeforeCreate(db *gorm.DB) error {
	sdr.Id = model.Id()
	return nil
}

func (sdr *StaffArchiveDayReport) Create() error {
	return model.DB().Create(&sdr).Error
}

func (sdr *StaffArchiveDayReport) Update() error {
	return model.DB().Select("Total", "GroupTotal", "GroupAddTotal", "GroupSubTotal").Updates(&sdr).Error
}

func (sdr *StaffArchiveDayReport) GetBy(corporationIds []int64, startAt, endAt time.Time) []StaffArchiveDayReport {
	var reports []StaffArchiveDayReport

	model.DB().Model(&StaffArchiveDayReport{}).Scopes(model.WhereCorporations(corporationIds)).
		Where("ReportAt >= ? AND ReportAt < ?", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat)).Find(&reports)

	return reports
}

func (sdr *StaffArchiveDayReport) GetByDay(corporationIds []int64, reportAt time.Time) []StaffArchiveDayReport {
	var reports []StaffArchiveDayReport

	model.DB().Model(&StaffArchiveDayReport{}).Scopes(model.WhereCorporations(corporationIds)).
		Where("ReportAt = ?", reportAt.Format(model.DateFormat)).Find(&reports)

	return reports
}

func (sdr *StaffArchiveDayReport) FirstRecord(reportAt time.Time) StaffArchiveDayReport {
	var report StaffArchiveDayReport
	model.DB().Model(&StaffArchiveDayReport{}).Where("GroupId = ? AND CompanyId = ? AND BranchId = ? AND DepartmentId = ? AND FleetId = ? AND ReportAt = ?",
		sdr.GroupId, sdr.CompanyId, sdr.BranchId, sdr.DepartmentId, sdr.FleetId, reportAt.Format(model.DateFormat)).First(&report)

	return report
}
