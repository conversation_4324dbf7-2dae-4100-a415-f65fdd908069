package hr

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

// RefererReward 推荐人奖励集Id
type RefererReward struct {
	model.PkId
	SerialNumber   int64      `json:"SerialNumber" gorm:"column:serialnumber;type:bigint;uniqueIndex:uniq_serialnumber;comment:奖励批次"`        //20060102 +0001(当天提交记录数)
	Topic          string     `json:"Topic" gorm:"column:topic;type:varchar;comment:主题"`                                                     // 描述
	ApplyStatus    int64      `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;default:1;comment:审批状态 1-申请审批中,2-申请审批通过,3-撤销,4-驳回"` //审批状态 1-申请审批中,2-申请审批通过,3-撤销,4-驳回
	OpGroupId      int64      `json:"OpGroupId"      gorm:"column:opgroupid;type:bigint;comment:集团Id"`                                       //集团Id
	OpCompanyId    int64      `json:"OpCompanyId"    gorm:"column:opcompanyid;type:bigint;comment:公司Id"`                                     //公司Id
	OpBranchId     int64      `json:"OpBranchId"     gorm:"column:opbranchid;type:bigint;comment:分公司Id"`                                     //分公司Id
	OpDepartmentId int64      `json:"OpDepartmentId" gorm:"column:opdepartmentid;type:bigint;comment:部门Id"`                                  //部门Id
	OpFleetId      int64      `json:"OpFleetId"      gorm:"column:opfleetid;type:bigint;comment:车队Id"`                                       //车队Id
	Attachments    model.JSON `json:"Attachments" gorm:"column:attachments;type:json;comment:附件"`
	model.OpUser
	Records []RefererRewardItem `json:"Records"`
	model.Timestamp

	SumMoney  int64 `json:"SumMoney"  gorm:"-"`
	SumPeople int64 `json:"SumPeople" gorm:"-"`
}

func (*RefererReward) TableName() string {
	return "referer_rewards"
}

func (rr *RefererReward) BeforeCreate(tx *gorm.DB) (err error) {
	rr.Id = model.Id()
	return
}

func (ta *RefererReward) ApplyStatusFieldName() string {
	return "applystatus"
}

func (rr *RefererReward) GetById(id int64) error {
	return model.DB().Model(&RefererReward{}).Where("Id=?", id).First(rr).Error
}

func (rr *RefererReward) Create(tx *gorm.DB) error {
	return tx.Transaction(func(tx *gorm.DB) (err error) {

		err = tx.Omit(clause.Associations).Create(rr).Error

		if len(rr.Records) > 0 {
			var items []RefererRewardItem
			for _, value := range rr.Records {
				items = append(items, value)
			}

			err = tx.Model(rr).Association("Records").Replace(&items)
			if err != nil {
				return err
			}
		}

		return nil
	})
}

func (rr *RefererReward) SelectLatest(now time.Time) error {
	year, month, day := now.Date()
	todayAt := time.Date(year, month, day, 0, 0, 0, 0, time.Local)
	tomorrowAt := time.Date(year, month, day+1, 0, 0, 0, 0, time.Local)
	return model.DB().Model(&RefererReward{}).
		Where(fmt.Sprintf("CreatedAt >= '%v'", todayAt.Format("2006-01-02 15:04:05"))).
		Where(fmt.Sprintf("CreatedAt < '%v'", tomorrowAt.Format("2006-01-02 15:04:05"))).Order("SerialNumber DESC").First(rr).Error
}

func (rr *RefererReward) Update() error {
	return model.DB().Transaction(func(tx *gorm.DB) (err error) {
		if rr.Id <= 0 {
			return nil
		}

		err = tx.Model(&RefererReward{}).Omit(clause.Associations).Where("Id = ?", rr.Id).
			Select("Topic").Updates(rr).Error

		if err != nil {
			return err
		}

		err = tx.Model(&RefererRewardItem{}).Where("RefererRewardId = ?", rr.Id).Delete(&RefererRewardItem{}).Error
		if err != nil {
			return err
		}

		if len(rr.Records) > 0 {
			var items []RefererRewardItem
			for _, value := range rr.Records {
				items = append(items, value)
			}

			err = tx.Model(rr).Association("Records").Replace(&items)
			if err != nil {
				return err
			}
		}

		return nil
	})
}

func (rr *RefererReward) UpdateSelect(tx *gorm.DB, id int64, m map[string]interface{}) error {
	return tx.Model(&RefererReward{}).Where("Id", id).Updates(m).Error
}

func (rr *RefererReward) List(topic, opUserName string, corpIds []int64, start, end time.Time, paginator model.Paginator) ([]RefererReward, int64, error) {
	var (
		totalCount int64
		rsp        []RefererReward
	)

	tx := model.DB().Model(&RefererReward{}).Where("OpGroupId IN ? OR OpCompanyId IN ? OR OpBranchId IN ? OR OpDepartmentId IN ? OR OpFleetId IN ?",
		corpIds, corpIds, corpIds, corpIds, corpIds)

	if len(topic) > 0 {
		tx = tx.Where("Topic LIKE ?", "%"+topic+"%")
	}

	if opUserName != "" {
		tx = tx.Where("OpUserName LIKE ? ", "%"+opUserName+"%")
	}

	if !start.IsZero() {
		tx = tx.Where("CreatedAt >= ?", start.Format(model.TimeFormat))
	}

	if !end.IsZero() {
		tx = tx.Where("CreatedAt <= ?", end.Format(model.TimeFormat))
	}

	err := tx.Count(&totalCount).Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&rsp).Error
	return rsp, totalCount, err
}

type RefererRewardItem struct {
	model.PkId
	RefererRewardId int64  `json:"RefererRewardId" gorm:"column:refererrewardid;type:integer;comment:推荐人奖励集合Id"`
	StaffId         int64  `json:"StaffId" gorm:"column:staffid;type:integer;comment:司机ID"`
	StaffWorkPostId int64  `json:"StaffWorkPostId" gorm:"column:staffworkpostid;type:bigint;comment:岗位Id"` // 岗位
	DrivingModel    string `json:"DrivingModel" gorm:"column:drivingmodel;type:text;comment:准驾车型"`         // 准驾车型

	CaptainStaffId int64           `json:"CaptainStaffId" gorm:"column:captainstaffid;type:integer;comment:员工所属车队长"` // 员工所属车队长
	JoinCompanyAt  model.LocalTime `json:"JoinCompanyAt" gorm:"column:joincompanyat;type:timestamp;comment:入职时间"`    // 入职时间
	RewardAt       model.LocalTime `json:"RewardAt" gorm:"column:rewardat;type:timestamp;comment:奖励时间"`              // 奖励时间
	RefererStaffId int64           `json:"RefererStaffId" gorm:"column:refererstaffid;type:integer;comment:推荐人ID"`   //  推荐人ID
	RewardMoney    int64           `json:"RewardMoney" gorm:"column:rewardmoney;type:integer;comment:奖励金额"`          // 奖励金额
	model.CorpId
	model.Timestamp
}

func (rr *RefererRewardItem) BeforeCreate(tx *gorm.DB) (err error) {
	rr.Id = model.Id()
	return
}

func (*RefererRewardItem) TableName() string {
	return "referer_reward_items"
}

func (rr *RefererRewardItem) Add() error {
	return model.DB().Create(&rr).Error
}

func (rr *RefererRewardItem) Aggs(refererRewardId int64) (total int64, money int64) {
	model.DB().Model(RefererRewardItem{}).Where("RefererRewardId=?", refererRewardId).Count(&total).Pluck("COALESCE(SUM(RewardMoney), 0) as RewardMoney", &money)
	return
}

func (rr *RefererRewardItem) GetByStaffId(staffId int64) error {
	return model.DB().Model(RefererRewardItem{}).Where("StaffId=?", staffId).Where("RefererRewardId IN (?) ",
		model.DB().Model(RefererReward{}).Select("Id").Where("ApplyStatus IN ?", []int64{util.ApplyStatusForDoing, util.ApplyStatusForDone}),
	).First(&rr).Error

}
func (rr *RefererRewardItem) ListByRefererRewardId(refererRewardId int64, paginator model.Paginator) ([]RefererRewardItem, int64, error) {
	var (
		totalCount int64
		rsp        []RefererRewardItem
	)

	tx := model.DB().Model(&RefererRewardItem{})

	err := tx.Where("RefererRewardId=?", refererRewardId).
		Count(&totalCount).Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&rsp).Error
	return rsp, totalCount, err
}

func (rr *RefererRewardItem) ListByRefererRewardIds(refererRewardIds []int64, paginator model.Paginator) ([]RefererRewardItem, int64, error) {
	var (
		totalCount int64
		rsp        []RefererRewardItem
	)

	tx := model.DB().Model(&RefererRewardItem{})

	err := tx.Where("RefererRewardId IN ?", refererRewardIds).
		Count(&totalCount).Order("CreatedAt DESC").Offset(paginator.Offset).Limit(paginator.Limit).Scan(&rsp).Error
	return rsp, totalCount, err
}
