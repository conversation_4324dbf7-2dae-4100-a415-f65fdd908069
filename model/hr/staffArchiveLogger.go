package hr

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

// StaffArchiveLogger 操作日志记录
type StaffArchiveLogger struct {
	model.PkId
	StaffArchiveId int64      `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID"`    //关联员工档案表主键ID
	Scene          int64      `json:"Scene" gorm:"column:scene;type:smallint;comment:场景值"`                            //场景值  1新增  2更新
	BeforeData     model.JSON `json:"BeforeData" gorm:"column:beforedata;type:json;comment:更改前的数据"`                   //更改前的数据
	AfterData      model.JSON `json:"AfterData" gorm:"column:afterdata;type:json;comment:更改后的数据"`                     //更改后的数据
	Ip             string     `json:"Ip" gorm:"column:ip;type:varchar(64);comment:Ip地址"`                              //Ip地址
	Modular        string     `json:"Modular" gorm:"column:modular;type:varchar(64);comment:来源模块"`                    //来源模块
	ProcessId      string     `json:"ProcessId" gorm:"column:processid;type:varchar(200);comment:关联的流程ID;default:''"` //关联的流程ID
	model.OpUser
	model.Timestamp
}

var (
	ArchiveLoggerSceneCreate         int64 = 1
	ArchiveLoggerSceneUpdate         int64 = 2
	ArchiveLoggerModularForArchive         = "员工档案管理"
	ArchiveLoggerModularForTransfer        = "员工调动管理"
	ArchiveLoggerModularForRetire          = "员工退休管理"
	ArchiveLoggerModularForQuit            = "员工离职管理"
	ArchiveLoggerModularForProbation       = "员工试用期管理"
	ArchiveLoggerExceptField               = []string{"Id", "CreatedAt", "UpdatedAt", "Age", "BirthDate", "GroupId", "Group", "CompanyId", "Company", "BranchId", "Branch",
		"DepartmentId", "Department", "FleetId", "Fleet", "EditApplyStatus", "FileHttpPrefix", "InsuranceRecord", "FundRecord", "OpUserId", "OpUserName", "OpIp", "IsMineEdit"}
	ArchiveLoggerExceptFieldForMineEdit = []string{"Id", "CreatedAt", "UpdatedAt", "Age", "BirthDate", "GroupId", "Group", "CompanyId", "Company", "BranchId", "Branch",
		"DepartmentId", "Department", "FleetId", "Fleet", "HumanRelationId", "HumanRelationName", "EmploymentCate", "PartyFilePath", "EditApplyStatus", "WorkPosts",
		"LaborContracts", "WorkTrains", "PunishmentRecords", "RewardRecords", "FileHttpPrefix", "InsuranceRecord", "FundRecord", "OpUserId", "OpUserName", "OpIp", "JobNumber", "JobStatus",
		"CorporationId", "CorporationName", "JoinCompanyWay", "WorkPostType", "JoinAt", "StartJobAt", "RetireAt", "IsMineEdit"}
)

func (log *StaffArchiveLogger) BeforeCreate(tx *gorm.DB) (err error) {
	log.Id = model.Id()
	return
}

func (log *StaffArchiveLogger) Create() error {
	return model.DB().Create(log).Error
}

func (log *StaffArchiveLogger) Insert(loggers []StaffArchiveLogger) error {
	return model.DB().Create(loggers).Error
}

func (log *StaffArchiveLogger) GetBy(staffArchiveId int64, keyword string, startAt, endAt time.Time, paginator model.Paginator) ([]StaffArchiveLogger, int64) {

	var loggers []StaffArchiveLogger
	tx := model.DB().Model(&StaffArchiveLogger{}).Where("StaffArchiveId = ?", staffArchiveId)

	if keyword != "" {
		tx = tx.Where("OpStaffName LIKE ?", "%"+keyword+"%")
	}
	if !startAt.IsZero() {
		tx = tx.Where("CreatedAt >= ?", startAt.Format(model.TimeFormat))
	}

	if !endAt.IsZero() {
		tx = tx.Where("CreatedAt <= ?", endAt.Format(model.TimeFormat))
	}

	var count int64
	tx.Count(&count)

	if paginator.Limit > 0 {
		tx = tx.Offset(paginator.Offset).Limit(paginator.Limit)
	}

	tx.Order("CreatedAt DESC").Find(&loggers)

	return loggers, count
}
