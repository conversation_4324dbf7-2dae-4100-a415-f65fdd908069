package hr

import (
	"app/org/scs/erpv2/api/model"
	"gorm.io/gorm"
	"time"
)

var (
	InCorporation  int64 = 1
	OutCorporation int64 = 2

	TransferTypeTransfer           int64 = 1  //调动
	TransferTypeAppointment        int64 = 2  //任职
	TransferTypeRemoval            int64 = 3  //免职
	TransferTypeAppointmentRemoval int64 = 4  //任免
	TransferTypeLoan               int64 = 5  //借调
	TransferTypeLoanEnd            int64 = 50 //借调结束
	TransferTypeBorrow             int64 = 6  //借用
	TransferTypeBorrowEnd          int64 = 60 //借用结束
	TransferTypeSecondment         int64 = 7  //挂职
	TransferTypeSecondmentEnd      int64 = 70 //挂职结束
)

// StaffTransfer 调岗管理
type StaffTransfer struct {
	model.PkId
	Topic         string                `json:"Topic" gorm:"column:topic;type:varchar;comment:描述"`                  //描述
	More          string                `json:"More" gorm:"column:more;type:varchar;comment:备注"`                    //备注
	WorkPostType  int64                 `json:"WorkPostType" gorm:"column:workposttype;type:smallint;comment:岗位类型"` //岗位类型 1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他
	NoticeUserIds model.JSON            `json:"NoticeUserIds" gorm:"column:noticeuserids;type:json;comment:知会人ID"`  //知会人ID
	Records       []StaffTransferRecord `json:"Records"`
	ApplyStatus   int64                 `json:"ApplyStatus" gorm:"column:applystatus;type:smallint;default:2;comment:审批状态 1-申请审批中,2-申请审批通过,3-撤销,4-驳回"` //审批状态 1-申请审批中,2-申请审批通过,3-撤销,4-驳回
	model.OpUser
	model.Timestamp

	OpIp                   string `json:"OpIp" gorm:"-"`
	IsProcessHandler       bool   `json:"IsProcessHandler" gorm:"-"`       //是否是流程的相关人
	CurrentHandlerUserName string `json:"CurrentHandlerUserName" gorm:"-"` // 流程当前处理人
}

func (st *StaffTransfer) TableName() string {
	return "staff_transfers"
}
func (st *StaffTransfer) ApplyStatusFieldName() string {
	return "applystatus"
}

func (st *StaffTransfer) BeforeCreate(tx *gorm.DB) error {
	st.Id = model.Id()
	return nil
}

func (st *StaffTransfer) Create() error {
	return model.DB().Create(st).Error
}

func (st *StaffTransfer) TransactionCreate(tx *gorm.DB) error {
	return tx.Create(st).Error
}

func (st *StaffTransfer) FindBy(id int64) error {
	return model.DB().Model(&StaffTransfer{}).Preload("Records").First(&st, id).Error
}

func (st *StaffTransfer) FirstBy(id int64) StaffTransfer {
	var record StaffTransfer
	model.DB().Model(&StaffTransfer{}).First(&record, id)
	return record
}

func (st *StaffTransfer) UpdateApplyStatus(id, status int64) error {
	return model.DB().Model(&StaffTransfer{}).Where("Id = ?", id).Update("ApplyStatus", status).Error
}

func (st *StaffTransfer) Delete(id int64) error {
	return model.DB().Model(&StaffTransfer{}).Where("Id = ?", id).Delete(StaffTransfer{}).Error
}

func (st *StaffTransfer) GetBy(outCorpIds, inCorpIds []int64, transferType int64, keyword string, startAt time.Time) []StaffTransfer {
	var records []StaffTransfer
	var where = func(db *gorm.DB) *gorm.DB {
		if keyword != "" {
			db = db.Where("staffName LIKE ?", "%"+keyword+"%")
		}

		if transferType > 0 {
			db = db.Where("Type = ?", transferType)
		}

		if len(outCorpIds) > 0 {
			db = db.Where("OutCorporationId IN ? OR OutCorporationId = ?", outCorpIds, 0)
		}

		if len(inCorpIds) > 0 {
			db = db.Where("InCorporationId IN ? OR InCorporationId = ?", inCorpIds, 0)
		}

		if !startAt.IsZero() {
			db = db.Where("StartAt >= ?", startAt.Format(model.TimeFormat))
		}
		return db
	}
	model.DB().Model(&StaffTransfer{}).
		Preload("Records", func(db *gorm.DB) *gorm.DB {
			return where(db)
		}).Where("EXISTS (?)", model.DB().Model(&StaffTransferRecord{}).Select("StaffTransferId").Scopes(where).Where("staff_transfers.Id = StaffTransferId")).
		Order("CreatedAt DESC").
		Find(&records)

	return records
}

type StaffTransferRecord struct {
	model.PkId
	StaffTransferId  int64            `json:"StaffTransferRecordId" gorm:"column:stafftransferid;type:bigint;comment:调动记录Id"`   //调动记录Id
	StaffArchiveId   int64            `json:"StaffArchiveId" gorm:"column:staffarchiveid;type:bigint;comment:关联员工档案表主键ID"`      //关联员工档案表主键ID
	StaffId          int64            `json:"StaffId" gorm:"column:staffid;type:integer;comment:主数据人员主键ID" validate:"required"` //主数据人员主键ID
	StaffName        string           `json:"StaffName" gorm:"column:staffname;type:varchar(64);comment:主数据人员姓名"`               //主数据人员姓名
	Type             int64            `json:"Type" gorm:"column:type;type:smallint;comment:调动类型" validate:"required"`           //调动类型 1-调动,2-任职,3-免职,4-任免,5-借调,50-借调结束,6-借用,60-借用结束,7-挂职,70-挂职结束
	OutCorporationId int64            `json:"OutCorporationId" gorm:"column:outcorporationid;type:bigint;comment:调出部门"`         //调出部门
	OutWorkPostType  int64            `json:"OutWorkPostType" gorm:"column:outworkposttype;type:integer;comment:调出岗位类型"`        //调出岗位类型 1-普通管理人员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他
	OutWorkPostId    int64            `json:"OutWorkPostId" gorm:"column:outworkpostid;type:bigint;comment:调出岗位"`               //调出岗位
	InCorporationId  int64            `json:"InCorporationId" gorm:"column:incorporationid;type:bigint;comment:调入部门"`           //调入部门
	InWorkPostType   int64            `json:"InWorkPostType" gorm:"column:inworkposttype;type:integer;comment:调入岗位类型"`          //调入岗位类型 1-普通管理人员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他
	InWorkPostId     int64            `json:"InWorkPostId" gorm:"column:inworkpostid;type:bigint;comment:调入岗位"`                 //调入岗位
	PositionType     int64            `json:"PositionType" gorm:"column:positiontype;type:smallint;comment:任职类型"`               //任职类型 1-主职,2-兼职
	StartAt          *model.LocalTime `json:"StartAt" gorm:"column:startat;type:timestamp;comment:任职时间"`                        //任职时间
	Salary           string           `json:"Salary" gorm:"column:salary;type:varchar;comment:薪水"`                              //薪水
	FilePath         model.JSON       `json:"FilePath" gorm:"column:filepath;type:json;comment:附件"`                             //附件

	InWorkPostName     string `json:"InWorkPostName" gorm:"-"`
	OutWorkPostName    string `json:"OutWorkPostName" gorm:"-"`
	InCorporationName  string `json:"InCorporationName" gorm:"-"`
	OutCorporationName string `json:"OutCorporationName" gorm:"-"`
	model.Timestamp
}

func (rec *StaffTransferRecord) BeforeCreate(tx *gorm.DB) error {
	rec.Id = model.Id()
	return nil
}

//func (rec *StaffTransferRecord) AfterCreate(tx *gorm.DB) error {
//	var archive StaffArchive
//	err := archive.FindByStaffId(rec.StaffId)
//	if err != nil {
//		return err
//	}
//	oetStaff := rpc.GetStaffWithId(context.Background(), rec.StaffId)
//	if oetStaff == nil {
//		return errors.New("oet staff not found")
//	}
//
//	//：新增一个岗位，原职位不改变，
//	//任职：人事关系改变，所属部门改变；借用、借调、挂职：人事关系不变，所属部门改变；
//	//：，新增一个调入岗位，设置为现任，所属部门改成调入部门，人事关系改成调入部门；
//	//免职：将调出岗位设置为历任，人事关系不变，部门不变；借用结束、借调结束、挂职结束：将调出岗位设置为历任，人事关系不变，所属部门改成之前的部门；
//
//	//任职、借用、借调、挂职、任免、调动：新增一个调入岗位，设置为现任
//	if rec.Type == TransferTypeAppointment || rec.Type == TransferTypeBorrow || rec.Type == TransferTypeLoan || rec.Type == TransferTypeSecondment || rec.Type == TransferTypeAppointmentRemoval || rec.Type == TransferTypeTransfer {
//		//新增岗位
//		if rec.InCorporationId != 0 && rec.InWorkPostId != 0 {
//			var newWorkPostType = StaffHasWorkPost{
//				StaffArchiveId: archive.Id,
//				StaffId:        rec.StaffId,
//				CorporationId:  rec.InCorporationId,
//				WorkPostType:   rec.InWorkPostType,
//				WorkPostId:     rec.InWorkPostId,
//				PositionType:   rec.PositionType,
//				IsNowJob:       util.IsNowJob,
//				StartAt:        rec.StartAt,
//			}
//
//			if rec.Type == TransferTypeBorrow || rec.Type == TransferTypeLoan || rec.Type == TransferTypeSecondment {
//				newWorkPostType.PreCorporationId = oetStaff.CorporationId
//				newWorkPostType.HumanRelationId = archive.HumanRelationId
//			} else {
//				newWorkPostType.HumanRelationId = rec.InCorporationId
//				newWorkPostType.PreCorporationId = rec.OutCorporationId
//			}
//
//			err = tx.Create(&newWorkPostType).Error
//			if err != nil {
//				return err
//			}
//		}
//	}
//	var preCorporationId int64
//	//免职、任免、调动、借用结束、借调结束、挂职结束：将调出岗位设置为历任
//	if rec.Type == TransferTypeRemoval || rec.Type == TransferTypeAppointmentRemoval || rec.Type == TransferTypeTransfer || rec.Type == TransferTypeBorrowEnd || rec.Type == TransferTypeLoanEnd || rec.Type == TransferTypeSecondmentEnd {
//		//查询调出岗位
//		var workPost StaffHasWorkPost
//		tx.Model(&StaffHasWorkPost{}).Where("CorporationId = ? AND StaffArchiveId = ? AND WorkPostId = ? AND IsNowJob = ?", rec.OutWorkPostId, archive.Id, rec.OutWorkPostId, util.IsNowJob).First(&workPost)
//		if workPost.Id > 0 {
//			//更新职务为历任
//			err = tx.Model(&StaffHasWorkPost{}).Where("Id =  ?", workPost.Id).UpdateColumn("IsNowJob", util.NotIsNowJob).Error
//			if err != nil {
//				return err
//			}
//			preCorporationId = workPost.PreCorporationId
//		}
//	}
//
//	var humanRelationId int64
//	//免职、借用、借调、挂职、借用结束、借调结束、挂职结束：人事关系不变；任免、任职、调动：人事关系更新为调入部门
//	if rec.Type == TransferTypeRemoval || rec.Type == TransferTypeBorrow || rec.Type == TransferTypeLoan || rec.Type == TransferTypeSecondment || rec.Type == TransferTypeBorrowEnd || rec.Type == TransferTypeLoanEnd || rec.Type == TransferTypeSecondmentEnd {
//		humanRelationId = archive.HumanRelationId
//	} else {
//		humanRelationId = rec.InCorporationId
//	}
//
//	var corporationId int64
//	//借用、借调、挂职、借用结束、借调结束、挂职结束：所属部门改为原来的部门；免职：所属部门不变；任免、任职、调动：所属部门更新为调入部门
//	if rec.Type == TransferTypeBorrowEnd || rec.Type == TransferTypeLoanEnd || rec.Type == TransferTypeSecondmentEnd {
//		corporationId = preCorporationId
//	} else {
//		if rec.Type == TransferTypeRemoval {
//			corporationId = archive.CorporationId
//		} else {
//			corporationId = rec.InCorporationId
//		}
//	}
//
//	//更新部门和人事关系
//	if oetStaff.CorporationId != corporationId {
//		oetStaff.CorporationId = corporationId
//		err := rpc.EditOetStaff(context.Background(), oetStaff)
//		if err != nil {
//			return err
//		}
//	}
//
//	var arch StaffArchive
//	err = arch.UpdateCorporation(corporationId, humanRelationId)
//	if err != nil {
//		return err
//	}
//
//	return nil
//}
