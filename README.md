# Media Service

This is the Media service

Generated with

```
micro new app/org/scs/erpv2/api --namespace=oet.scs --alias=erp --type=api
```

## Getting Started

- [Configuration](#configuration)
- [Dependencies](#dependencies)
- [Usage](#usage)

## Configuration

- FQDN: oet.scs.api.erp
- Type: api
- Alias: erp

## Dependencies

Micro services depend on service discovery. The default is multicast DNS, a zeroconf system.

In the event you need a resilient multi-host setup we recommend consul.

```
# install consul
brew install consul

# run consul
consul agent -dev
```

## Usage

A Makefile is included for convenience

Build the binary

```
make build
```

Run the service
```
./erp-api
```

Build a docker image
```
make docker
```
