{"Env": "production", "DebugLevel": "Info", "AbsDirPath": "/mnt/www/", "WebRoot": "webroot", "StaticFileHttpPrefix": "https://erp.dsgj.online:36085/", "IsRelationIss": false, "Database": {"Host": "*************", "Port": 15432, "DbName": "erpv2", "User": "postgres", "Password": "DTStack@2023"}, "Etcd": {"Host": "************", "Port": 2379, "Interval": 10, "Ttl": 30}, "Micro": {"ServiceName": "oet.scs.api.erp.v2"}, "Mini": {"AppAccountId": 0, "DoorCheckTemplateId": "", "Page": "erp/notice/index"}, "PermissionSys": "<PERSON><PERSON>an", "SchedulerJob": {"IsSendDoorCheckNotify": false, "IsSendLaborContractExpireNotify": true, "IsSendStaffProbationExpireNotify": true, "IsCheckTicketDataPermission": false}, "Lbpm": {"Enable": false}, "DingTalkBpm": {"Enable": true, "AgentId": **********, "AppKey": "dingwrhpne9wielevoxe", "AppSecret": "fNMJgReHUneSMMRUyRXT9EXgW1IT6UAW-sjhJmCLIZgxlKaSwU2QQr8-rDpRTGS5", "AesKey": "qskn3pVAABBF3wJkQD4EMmC4OM7l5EaS83Xuhmt89tN", "Token": "POLFWunHFqLrQ5oaRtb6jTj2DlaIseO9lly", "DingTalkBpmProcessCode": {"# 员工离职申请流程模板": "", "staff_quit_apply_process": "PROC-07DDB382-17D6-4504-B98E-E4BC8C6CC5E3", "# 物料申领申请流程模板": "", "material_requisition_apply_process": "PROC-090C2864-FF51-4A05-B346-FB80EA504C26"}}, "SyncMasterStaff": {"Enable": true, "CorporationIds": [1373149365465558017, 1328307510681996289]}, "TopCorporationId": 1328307510681996289}