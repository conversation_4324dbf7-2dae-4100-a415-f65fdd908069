CREATE DATABASE "erpv2"
    ENCODING 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8';

-- ----------------------------
-- Table Structure For door_check_inspectors
-- ----------------------------
DROP TABLE IF EXISTS "door_check_inspectors";
CREATE TABLE door_check_inspectors (
  "Id" bigint NOT NULL,
  "GroupId" bigint,
  "CompanyId" bigint,
  "BranchId" bigint,
  "DepartmentId" bigint,
  "FleetId" bigint,
  "StaffId" bigint,
  "Name" varchar(50),
  "Dutyat" timestamptz(6),
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "door_check_inspectors" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For door_check_item_results
-- ----------------------------
DROP TABLE IF EXISTS "door_check_item_results";
CREATE TABLE door_check_item_results (
  "Id" bigint NOT NULL,
  "GroupId" bigint,
  "CompanyId" bigint,
  "BranchId" bigint,
  "DepartmentId" bigint,
  "FleetId" bigint,
  "License" varchar(50),
  "Vehiclecode" varchar(50),
  "LineId" integer,
  "Line" varchar(50),
  "StaffId" integer,
  "Staffname" varchar(50),
  "Type" smallint,
  "AdminstaffId" integer,
  "Adminstaffname" varchar(50),
  "FkrecordId" bigint,
  "FkitemId" bigint,
  "Itemname" varchar(60),
  "Status" smallint,
  "Resultjson" jsonb,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6),
  "ReportstaffId" integer DEFAULT 0,
  "Reportstaffname" varchar(50) DEFAULT '',
  "ResolvestaffId" integer DEFAULT 0,
  "Resolvestaffname" varchar(50) DEFAULT ''
)
;
--ALTER TABLE "door_check_item_results" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For door_check_items
-- ----------------------------
DROP TABLE IF EXISTS "door_check_items";
CREATE TABLE door_check_items (
  "Id" bigint NOT NULL,
  "GroupId" bigint,
  "CompanyId" bigint,
  "BranchId" bigint,
  "DepartmentId" bigint,
  "FleetId" bigint,
  "Workposttype" bigint,
  "Itemtype" smallint,
  "Isdescnormal" smallint,
  "Isdescabnormal" smallint,
  "Nature" smallint,
  "ParentId" bigint,
  "Parentidpath" text,
  "FkiconfileId" bigint,
  "Iconfileurl" text,
  "Name" varchar(50),
  "Sort" integer,
  "Isdeleted" smallint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6),
  "Isattachmentabnormal" smallint DEFAULT 1
)
;
--ALTER TABLE "door_check_items" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For door_check_notice_receives
-- ----------------------------
DROP TABLE IF EXISTS "door_check_notice_receives";
CREATE TABLE door_check_notice_receives (
  "Id" bigint NOT NULL,
  "FkrecordId" bigint,
  "FkitemId" bigint,
  "Itemname" varchar(60),
  "FknoticeId" bigint,
  "StaffId" bigint,
  "Staffname" varchar(50),
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "door_check_notice_receives" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For door_check_notices
-- ----------------------------
DROP TABLE IF EXISTS "door_check_notices";
CREATE TABLE door_check_notices (
  "Id" bigint NOT NULL,
  "FkrecordId" bigint,
  "FkitemId" bigint,
  "Itemname" varchar(60),
  "SendstaffId" bigint,
  "Sendstaffname" varchar(50),
  "Contentjson" jsonb,
  "Noticetype" smallint,
  "Status" smallint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "door_check_notices" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For door_check_records
-- ----------------------------
DROP TABLE IF EXISTS "door_check_records";
CREATE TABLE door_check_records (
  "Id" bigint NOT NULL,
  "GroupId" bigint,
  "CompanyId" bigint,
  "BranchId" bigint,
  "DepartmentId" bigint,
  "FleetId" bigint,
  "License" varchar(50),
  "Vehiclecode" varchar(50),
  "LineId" integer,
  "Line" varchar(50),
  "StaffId" integer,
  "Staffname" varchar(50),
  "Type" smallint,
  "AdminstaffId" integer,
  "Adminstaffname" varchar(50),
  "Checkform" smallint,
  "Result" smallint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6),
  "Statuscount" text DEFAULT '0,0,0,0,0'
)
;
--ALTER TABLE "door_check_records" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For files
-- ----------------------------
DROP TABLE IF EXISTS "files";
CREATE TABLE files (
  "Id" integer NOT NULL DEFAULT nextval('files_id_seq'::regclass),
  "CorporationId" bigint NOT NULL,
  "TopcorporationId" bigint NOT NULL,
  "Name" text NOT NULL,
  "Suffix" varchar(50) DEFAULT '',
  "Path" text NOT NULL,
  "Size" integer NOT NULL,
  "Progress" smallint DEFAULT 0,
  "CreatedAt" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "UpdatedAt" timestamptz(6) DEFAULT CURRENT_TIMESTAMP
)
;
--ALTER TABLE "files" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For fund_records
-- ----------------------------
DROP TABLE IF EXISTS "fund_records";
CREATE TABLE fund_records (
  "Id" bigint NOT NULL,
  "TopcorporationId" bigint,
  "StaffarchiveId" bigint,
  "StaffId" integer,
  "Adjustdate" integer,
  "Fundbase" integer,
  "Fundpersonamount" integer,
  "Fundcompanyamount" integer,
  "Sumamount" integer,
  "Deletedat" timestamp(6),
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "fund_records" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For insurance_fund_settings
-- ----------------------------
DROP TABLE IF EXISTS "insurance_fund_settings";
CREATE TABLE insurance_fund_settings (
  "Id" bigint NOT NULL,
  "TopcorporationId" bigint,
  "Scene" smallint,
  "Modular" varchar(64),
  "Companypaytype" smallint,
  "Companypaymount" integer,
  "Personalpaytype" smallint,
  "Personalpayamount" integer,
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "insurance_fund_settings" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For insurance_records
-- ----------------------------
DROP TABLE IF EXISTS "insurance_records";
CREATE TABLE insurance_records (
  "Id" bigint NOT NULL,
  "TopcorporationId" bigint,
  "StaffarchiveId" bigint,
  "StaffId" integer,
  "Adjustdate" integer,
  "Endowmentbase" integer,
  "Endowmentpersonamount" integer,
  "Endowmentcompanyamount" integer,
  "Medicalbase" integer,
  "Medicalpersonamount" integer,
  "Medicalcompanyamount" integer,
  "Unemploymentbase" integer,
  "Unemploymentpersonamount" integer,
  "Unemploymentcompanyamount" integer,
  "Injurybase" integer,
  "Injurypersonamount" integer,
  "Injurycompanyamount" integer,
  "Isannuity" integer,
  "Annuitybase" integer,
  "Annuitypersonamount" integer,
  "Annuitycompanyamount" integer,
  "Isextramedical" integer,
  "Extramedicalbase" integer,
  "Extramedicalpersonamount" integer,
  "Extramedicalcompanyamount" integer,
  "Deletedat" timestamp(6),
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "insurance_records" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For join_company_applies
-- ----------------------------
DROP TABLE IF EXISTS "join_company_applies";
CREATE TABLE join_company_applies (
  "Id" bigint NOT NULL,
  "TopcorporationId" bigint,
  "CorporationId" bigint,
  "Workposttype" integer,
  "LineId" integer,
  "Name" varchar(64),
  "Contact" varchar(64),
  "Isapply" smallint,
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6),
  "Gender" smallint,
  "Nativeplace" varchar,
  "Residenceattr" smallint,
  "Nation" varchar(64),
  "IdentityId" varchar(64),
  "Healthstatus" smallint,
  "Isreversionsoldier" smallint,
  "Reversionat" timestamp(6),
  "Politicalidentity" integer,
  "Joinpartyat" timestamp(6),
  "Marriagestatus" smallint,
  "Bearstatus" integer,
  "Address" varchar,
  "Drivingcode" varchar(64),
  "Drivingmodel" varchar(64),
  "Highestedu" integer,
  "Basefilepath" jsonb
)
;
--ALTER TABLE "join_company_applies" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For join_company_apply_certificates
-- ----------------------------
DROP TABLE IF EXISTS "join_company_apply_certificates";
CREATE TABLE join_company_apply_certificates (
  "Id" bigint NOT NULL,
  "JoincompanyapplyId" bigint,
  "Name" varchar(64),
  "Code" varchar(64),
  "Startat" timestamp(6),
  "Endat" timestamp(6),
  "Authorizeoffice" varchar(64),
  "Filepath" jsonb,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "join_company_apply_certificates" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For join_company_apply_educations
-- ----------------------------
DROP TABLE IF EXISTS "join_company_apply_educations";
CREATE TABLE join_company_apply_educations (
  "Id" bigint NOT NULL,
  "JoincompanyapplyId" bigint,
  "Edu" integer,
  "School" varchar,
  "Major" varchar,
  "Type" smallint,
  "Startat" timestamp(6),
  "Endat" timestamp(6),
  "Filepath" jsonb,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "join_company_apply_educations" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For join_company_apply_members
-- ----------------------------
DROP TABLE IF EXISTS "join_company_apply_members";
CREATE TABLE join_company_apply_members (
  "Id" bigint NOT NULL,
  "JoincompanyapplyId" bigint,
  "Name" varchar(64),
  "Relationship" varchar(64),
  "Contact" varchar(64),
  "Ismaincontact" smallint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "join_company_apply_members" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For join_company_apply_skills
-- ----------------------------
DROP TABLE IF EXISTS "join_company_apply_skills";
CREATE TABLE join_company_apply_skills (
  "Id" bigint NOT NULL,
  "JoincompanyapplyId" bigint,
  "Name" varchar(64),
  "More" varchar,
  "Filepath" jsonb,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "join_company_apply_skills" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For positional_title_applies
-- ----------------------------
DROP TABLE IF EXISTS "positional_title_applies";
CREATE TABLE positional_title_applies (
  "Id" bigint NOT NULL,
  "TopcorporationId" bigint,
  "StaffarchiveId" bigint,
  "StaffId" integer,
  "Name" varchar,
  "More" varchar,
  "Certificate_name" varchar,
  "Certificate_office" varchar,
  "Certificate_code" varchar,
  "Certificate_date" timestamp(6),
  "Filepath" jsonb,
  "Approvalstatus" smallint,
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "positional_title_applies" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_archive_history_252518349146030272
-- ----------------------------
DROP TABLE IF EXISTS "staff_archive_history_252518349146030272";
CREATE TABLE staff_archive_history_252518349146030272 (
  "Id" bigint NOT NULL,
  "GroupId" bigint,
  "CompanyId" bigint,
  "BranchId" bigint,
  "DepartmentId" bigint,
  "FleetId" bigint,
  "StaffId" integer,
  "Age" smallint,
  "Birthdate" timestamp(6),
  "Residenceattr" smallint,
  "Address" varchar,
  "Marriagestatus" smallint,
  "Bearstatus" integer,
  "Highestedu" integer,
  "Probationendat" timestamp(6),
  "Workposttype" integer,
  "Basefilepath" jsonb,
  "Partyfilepath" jsonb,
  "CorporationId" bigint,
  "LineId" integer,
  "Name" varchar(64),
  "Gender" smallint,
  "Nativeplace" varchar,
  "Nation" varchar(64),
  "IdentityId" varchar(64),
  "Politicalidentity" integer,
  "Contact" varchar(64),
  "Healthstatus" smallint,
  "Isreversionsoldier" smallint,
  "Reversionat" timestamp(6),
  "Drivingcode" varchar(64),
  "Drivingmodel" varchar(64),
  "Joinpartyat" timestamp(6),
  "Jobnumber" varchar,
  "Jobstatus" smallint,
  "Joincompanyway" smallint,
  "Joinat" timestamp(6),
  "Startjobat" timestamp(6),
  "Retireat" timestamp(6),
  "Educations" jsonb,
  "Positionaltitles" jsonb,
  "Skills" jsonb,
  "Familymembers" jsonb,
  "Certificates" jsonb,
  "Workposts" jsonb,
  "Laborcontracts" jsonb
)
;
--ALTER TABLE "staff_archive_history_252518349146030272" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_archive_history_252518638551897344
-- ----------------------------
DROP TABLE IF EXISTS "staff_archive_history_252518638551897344";
CREATE TABLE staff_archive_history_252518638551897344 (
  "Id" bigint NOT NULL,
  "GroupId" bigint,
  "CompanyId" bigint,
  "BranchId" bigint,
  "DepartmentId" bigint,
  "FleetId" bigint,
  "StaffId" integer,
  "Age" smallint,
  "Birthdate" timestamp(6),
  "Residenceattr" smallint,
  "Address" varchar,
  "Marriagestatus" smallint,
  "Bearstatus" integer,
  "Highestedu" integer,
  "Probationendat" timestamp(6),
  "Workposttype" integer,
  "Basefilepath" jsonb,
  "Partyfilepath" jsonb,
  "CorporationId" bigint,
  "LineId" integer,
  "Name" varchar(64),
  "Gender" smallint,
  "Nativeplace" varchar,
  "Nation" varchar(64),
  "IdentityId" varchar(64),
  "Politicalidentity" integer,
  "Contact" varchar(64),
  "Healthstatus" smallint,
  "Isreversionsoldier" smallint,
  "Reversionat" timestamp(6),
  "Drivingcode" varchar(64),
  "Drivingmodel" varchar(64),
  "Joinpartyat" timestamp(6),
  "Jobnumber" varchar,
  "Jobstatus" smallint,
  "Joincompanyway" smallint,
  "Joinat" timestamp(6),
  "Startjobat" timestamp(6),
  "Retireat" timestamp(6),
  "Educations" jsonb,
  "Positionaltitles" jsonb,
  "Skills" jsonb,
  "Familymembers" jsonb,
  "Certificates" jsonb,
  "Workposts" jsonb,
  "Laborcontracts" jsonb
)
;
--ALTER TABLE "staff_archive_history_252518638551897344" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_archive_loggers
-- ----------------------------
DROP TABLE IF EXISTS "staff_archive_loggers";
CREATE TABLE staff_archive_loggers (
  "Id" bigint NOT NULL,
  "StaffarchiveId" bigint,
  "Scene" smallint,
  "Beforedata" jsonb,
  "Afterdata" jsonb,
  "Ip" varchar(64),
  "Modular" varchar(64),
  "Opusername" varchar(64),
  "OpuserId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_archive_loggers" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_archive_versions
-- ----------------------------
DROP TABLE IF EXISTS "staff_archive_versions";
CREATE TABLE staff_archive_versions (
  "Id" bigint NOT NULL,
  "TopcorporationId" bigint,
  "CorporationId" bigint,
  "Desc" varchar,
  "Tablename" varchar(64),
  "Staff_count" integer,
  "Op_staff_name" varchar(64),
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_archive_versions" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_archives
-- ----------------------------
DROP TABLE IF EXISTS "staff_archives";
CREATE TABLE staff_archives (
  "Id" bigint NOT NULL,
  "GroupId" bigint,
  "CompanyId" bigint,
  "BranchId" bigint,
  "DepartmentId" bigint,
  "FleetId" bigint,
  "StaffId" integer,
  "Age" smallint,
  "Birthdate" timestamp(6),
  "Residenceattr" smallint,
  "Address" varchar,
  "Marriagestatus" smallint,
  "Bearstatus" integer,
  "Highestedu" integer,
  "Probationendat" timestamp(6),
  "Workposttype" integer,
  "Basefilepath" jsonb,
  "Partyfilepath" jsonb,
  "Line_Id" bigint,
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6),
  "HumanrelationId" bigint,
  "Employmentcate" smallint
)
;
--ALTER TABLE "staff_archives" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_assessments
-- ----------------------------
DROP TABLE IF EXISTS "staff_assessments";
CREATE TABLE staff_assessments (
  "Id" bigint NOT NULL,
  "StaffarchiveId" bigint,
  "StaffId" integer,
  "Year" integer,
  "Degree" smallint,
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_assessments" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_certificates
-- ----------------------------
DROP TABLE IF EXISTS "staff_certificates";
CREATE TABLE staff_certificates (
  "Id" bigint NOT NULL,
  "StaffarchiveId" bigint,
  "StaffId" integer,
  "Name" varchar(64),
  "Code" varchar(64),
  "Startat" timestamp(6),
  "Endat" timestamp(6),
  "Authorizeoffice" varchar(64),
  "Filepath" jsonb,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_certificates" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_educations
-- ----------------------------
DROP TABLE IF EXISTS "staff_educations";
CREATE TABLE staff_educations (
  "Id" bigint NOT NULL,
  "StaffarchiveId" bigint,
  "StaffId" integer,
  "Edu" integer,
  "School" varchar,
  "Major" varchar,
  "Type" smallint,
  "Startat" timestamp(6),
  "Endat" timestamp(6),
  "Filepath" jsonb,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_educations" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_family_members
-- ----------------------------
DROP TABLE IF EXISTS "staff_family_members";
CREATE TABLE staff_family_members (
  "Id" bigint NOT NULL,
  "StaffarchiveId" bigint,
  "StaffId" integer,
  "Name" varchar(64),
  "Relationship" varchar(64),
  "Contact" varchar(64),
  "Ismaincontact" smallint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_family_members" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_has_work_posts
-- ----------------------------
DROP TABLE IF EXISTS "staff_has_work_posts";
CREATE TABLE staff_has_work_posts (
  "Id" bigint NOT NULL,
  "StaffarchiveId" bigint,
  "StaffId" integer,
  "CorporationId" bigint,
  "Workposttype" integer,
  "WorkpostId" bigint,
  "Positionlevel" varchar(32),
  "Positiontype" smallint,
  "Isnowjob" smallint DEFAULT 1,
  "Startat" timestamp(6),
  "More" varchar,
  "Filepath" jsonb,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6),
  "PrecorporationId" bigint,
  "HumanrelationId" bigint,
  "Transfertype" smallint
)
;
--ALTER TABLE "staff_has_work_posts" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_labor_contracts
-- ----------------------------
DROP TABLE IF EXISTS "staff_labor_contracts";
CREATE TABLE staff_labor_contracts (
  "Id" bigint NOT NULL,
  "StaffarchiveId" bigint,
  "StaffId" integer,
  "Code" varchar(64),
  "Type" smallint,
  "Startat" timestamp(6),
  "Endat" timestamp(6),
  "Filepath" jsonb,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6),
  "Status" smallint,
  "HandlestaffId" integer,
  "Handlestaffname" varchar,
  "HandlestaffworkpostId" bigint,
  "More" varchar
)
;
--ALTER TABLE "staff_labor_contracts" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_positional_titles
-- ----------------------------
DROP TABLE IF EXISTS "staff_positional_titles";
CREATE TABLE staff_positional_titles (
  "Id" bigint NOT NULL,
  "StaffarchiveId" bigint,
  "StaffId" integer,
  "Name" varchar(64),
  "More" varchar,
  "Filepath" jsonb,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_positional_titles" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_punishment_records
-- ----------------------------
DROP TABLE IF EXISTS "staff_punishment_records";
CREATE TABLE staff_punishment_records (
  "Id" bigint NOT NULL,
  "StaffarchiveId" bigint,
  "StaffId" integer,
  "StartAt" timestamp(6),
  "Desc" varchar,
  "More" varchar,
  "Filepath" jsonb,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_punishment_records" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_quit_records
-- ----------------------------
DROP TABLE IF EXISTS "staff_quit_records";
CREATE TABLE staff_quit_records (
  "Id" bigint NOT NULL,
  "GroupId" bigint,
  "CompanyId" bigint,
  "BranchId" bigint,
  "DepartmentId" bigint,
  "FleetId" bigint,
  "StaffquitId" bigint,
  "StaffId" bigint,
  "Staff_name" varchar(64),
  "Jobnumber" varchar,
  "WorkpostId" bigint,
  "Contractendat" timestamp(6),
  "Relievecontracttype" smallint,
  "Joincompanyat" timestamp(6),
  "Yearholidayremain" integer,
  "Exchangeholidayremain" integer,
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6),
  "Workpostname" varchar
)
;
--ALTER TABLE "staff_quit_records" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_quits
-- ----------------------------
DROP TABLE IF EXISTS "staff_quits";
CREATE TABLE staff_quits (
  "Id" bigint NOT NULL,
  "Topic" varchar,
  "More" varchar,
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_quits" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_retire_records
-- ----------------------------
DROP TABLE IF EXISTS "staff_retire_records";
CREATE TABLE staff_retire_records (
  "Id" bigint NOT NULL,
  "GroupId" bigint,
  "CompanyId" bigint,
  "BranchId" bigint,
  "DepartmentId" bigint,
  "FleetId" bigint,
  "StaffretireId" bigint,
  "StaffId" bigint,
  "Staffname" varchar(64),
  "Status" smallint,
  "Jobnumber" varchar,
  "WorkpostId" bigint,
  "Retireat" timestamp(6),
  "Joincompanyat" timestamp(6),
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6),
  "Filepath" jsonb
)
;
--ALTER TABLE "staff_retire_records" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_retires
-- ----------------------------
DROP TABLE IF EXISTS "staff_retires";
CREATE TABLE staff_retires (
  "Id" bigint NOT NULL,
  "Topic" varchar,
  "More" varchar,
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_retires" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_reward_records
-- ----------------------------
DROP TABLE IF EXISTS "staff_reward_records";
CREATE TABLE staff_reward_records (
  "Id" bigint NOT NULL,
  "StaffarchiveId" bigint,
  "StaffId" integer,
  "StartAt" timestamp(6),
  "Desc" varchar,
  "More" varchar,
  "Filepath" jsonb,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_reward_records" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_skills
-- ----------------------------
DROP TABLE IF EXISTS "staff_skills";
CREATE TABLE staff_skills (
  "Id" bigint NOT NULL,
  "StaffarchiveId" bigint,
  "StaffId" integer,
  "Name" varchar(64),
  "More" varchar,
  "Filepath" jsonb,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_skills" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_transfer_records
-- ----------------------------
DROP TABLE IF EXISTS "staff_transfer_records";
CREATE TABLE staff_transfer_records (
  "Id" bigint NOT NULL,
  "StafftransferId" bigint,
  "StaffId" bigint,
  "Staffname" varchar(64),
  "Type" smallint,
  "OutcorporationId" bigint,
  "Outworkposttype" integer,
  "OutworkpostId" bigint,
  "IncorporationId" bigint,
  "Inworkposttype" integer,
  "InworkpostId" bigint,
  "Positiontype" smallint,
  "Startat" timestamp(6),
  "Salary" varchar,
  "Filepath" jsonb,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_transfer_records" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For staff_transfers
-- ----------------------------
DROP TABLE IF EXISTS "staff_transfers";
CREATE TABLE staff_transfers (
  "Id" bigint NOT NULL,
  "Topic" varchar,
  "More" varchar,
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "staff_transfers" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For ticket_count_moneys
-- ----------------------------
DROP TABLE IF EXISTS "ticket_count_moneys";
CREATE TABLE ticket_count_moneys (
  "Id" bigint NOT NULL,
  "GroupId" bigint,
  "CompanyId" bigint,
  "BranchId" bigint,
  "DepartmentId" bigint,
  "FleetId" bigint,
  "Type" smallint,
  "License" varchar(50),
  "Vehiclecode" varchar(50),
  "LineId" integer,
  "Line" varchar(50),
  "DriverstaffId" integer,
  "Drivername" varchar(50),
  "Driverstaffidstr" varchar(50),
  "AttendantstaffId" integer,
  "Attendantname" varchar(50),
  "Attendantstaffidstr" varchar(50),
  "ReviewerstaffId" integer,
  "Reviewername" varchar(50),
  "Reviewerstaffidstr" varchar(50),
  "InputstaffId" integer,
  "Inputname" varchar(50),
  "Reportat" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "Incomeat" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "Timeslot" smallint DEFAULT 1,
  "Status" smallint DEFAULT 1,
  "Datastatus" smallint DEFAULT 0,
  "Paper10000" integer DEFAULT 0,
  "Paper5000" integer DEFAULT 0,
  "Paper2000" integer DEFAULT 0,
  "Paper1000" integer DEFAULT 0,
  "Paper500" integer DEFAULT 0,
  "Paper200" integer DEFAULT 0,
  "Paper100" integer DEFAULT 0,
  "Paper50" integer DEFAULT 0,
  "Paper20" integer DEFAULT 0,
  "Paper10" integer DEFAULT 0,
  "Papertotalcount" integer DEFAULT 0,
  "Papertotalamount" integer DEFAULT 0,
  "Coin100" integer DEFAULT 0,
  "Coin50" integer DEFAULT 0,
  "Coin10" integer DEFAULT 0,
  "Cointotalcount" integer DEFAULT 0,
  "Cointotalamount" integer DEFAULT 0,
  "Moneyfake" integer DEFAULT 0,
  "Moneyforeign" integer DEFAULT 0,
  "Moneyother" integer DEFAULT 0,
  "Totalamount" integer DEFAULT 0,
  "Receivableamount" integer DEFAULT 0,
  "Ticket100" integer DEFAULT 0,
  "Ticket150" integer DEFAULT 0,
  "Ticket200" integer DEFAULT 0,
  "Ticket250" integer DEFAULT 0,
  "Ticket300" integer DEFAULT 0,
  "Ticket350" integer DEFAULT 0,
  "Ticket400" integer DEFAULT 0,
  "Ticket450" integer DEFAULT 0,
  "Ticket500" integer DEFAULT 0,
  "Ticket550" integer DEFAULT 0,
  "Ticket600" integer DEFAULT 0,
  "Ticket650" integer DEFAULT 0,
  "Ticket700" integer DEFAULT 0,
  "Ticket750" integer DEFAULT 0,
  "Ticket800" integer DEFAULT 0,
  "Ticket850" integer DEFAULT 0,
  "Ticket900" integer DEFAULT 0,
  "Ticket950" integer DEFAULT 0,
  "Ticket1000" integer DEFAULT 0,
  "Tickettotalcount" integer DEFAULT 0,
  "Tickettotalamount" integer DEFAULT 0,
  "CreatedAt" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "UpdatedAt" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "Inputdate" Date,
  "Inputstaffidstr" varchar(50)
)
;
--ALTER TABLE "ticket_count_moneys" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For ticket_loggers
-- ----------------------------
DROP TABLE IF EXISTS "ticket_loggers";
CREATE TABLE ticket_loggers (
  "Id" bigint NOT NULL,
  "TicketmoneyId" bigint,
  "Scene" smallint,
  "Beforedata" jsonb,
  "Afterdata" jsonb,
  "Ip" varchar(64),
  "Opusername" varchar(64),
  "OpuserId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "ticket_loggers" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For work_posts
-- ----------------------------
DROP TABLE IF EXISTS "work_posts";
CREATE TABLE work_posts (
  "Id" bigint NOT NULL,
  "GroupId" bigint,
  "CompanyId" bigint,
  "BranchId" bigint,
  "DepartmentId" bigint,
  "FleetId" bigint,
  "Type" integer,
  "Name" varchar(64),
  "Attr" integer,
  "Status" smallint,
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6),
  "Code" varchar(255) DEFAULT ''
)
;
--ALTER TABLE "work_posts" OWNER TO "Taizhou_erp";
COMMENT ON COLUMN "work_posts"."GroupId" IS '集团';
COMMENT ON COLUMN "work_posts"."CompanyId" IS '公司';
COMMENT ON COLUMN "work_posts"."BranchId" IS '分公司';
COMMENT ON COLUMN "work_posts"."DepartmentId" IS '部门';
COMMENT ON COLUMN "work_posts"."FleetId" IS '车队';
COMMENT ON COLUMN "work_posts"."Type" IS '岗位类型 1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他';
COMMENT ON COLUMN "work_posts"."Name" IS '岗位名称';
COMMENT ON COLUMN "work_posts"."Attr" IS '岗位性质 1-部门领导（正职）,2-部门领导（副职）,3-普通员工';
COMMENT ON COLUMN "work_posts"."Status" IS '岗位状态 1-有效  2-无效';
COMMENT ON COLUMN "work_posts"."OpstaffId" IS '操作人ID';
COMMENT ON COLUMN "work_posts"."Code" IS '岗位编号';

-- ----------------------------
-- Table Structure For work_train_has_corporations
-- ----------------------------
DROP TABLE IF EXISTS "work_train_has_corporations";
CREATE TABLE work_train_has_corporations (
  "Id" bigint NOT NULL,
  "WorktrainId" bigint,
  "CorporationId" bigint,
  "Corporationname" varchar
)
;
--ALTER TABLE "work_train_has_corporations" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For work_train_has_staffs
-- ----------------------------
DROP TABLE IF EXISTS "work_train_has_staffs";
CREATE TABLE work_train_has_staffs (
  "Id" bigint NOT NULL,
  "WorktrainId" bigint,
  "StaffarchiveId" bigint,
  "StaffId" bigint,
  "Result" smallint,
  "Isattend" smallint
)
;
--ALTER TABLE "work_train_has_staffs" OWNER TO "Taizhou_erp";

-- ----------------------------
-- Table Structure For work_trains
-- ----------------------------
DROP TABLE IF EXISTS "work_trains";
CREATE TABLE work_trains (
  "Id" bigint NOT NULL,
  "TopcorporationId" bigint,
  "Title" varchar,
  "Content" text,
  "Starttime" timestamp(6),
  "Location" varchar,
  "Organizer" varchar,
  "Teacher" varchar,
  "More" varchar,
  "Filepath" jsonb,
  "OpstaffId" bigint,
  "CreatedAt" timestamp(6),
  "UpdatedAt" timestamp(6)
)
;
--ALTER TABLE "work_trains" OWNER TO "Taizhou_erp";

