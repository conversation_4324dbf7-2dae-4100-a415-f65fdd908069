ALTER TABLE hr_staff_infos ADD COLUMN Code varchar(255) DEFAULT '';
ALTER TABLE hr_staff_infos ALTER COLUMN Code SET NOT NULL;
ALTER TABLE hr_staff_infos ALTER COLUMN Code DROP DEFAULT;

ALTER TABLE hr_staff_infos ADD CONSTRAINT hr_staff_infos_TopCorporationId_Code_key UNIQUE (TopCorporationId, Code);

ALTER TABLE staff_files ALTER COLUMN Name TYPE text;

ALTER TABLE staff_files ADD COLUMN Code varchar(255) DEFAULT '';
ALTER TABLE staff_files ADD CONSTRAINT staff_files_FileLabelId_Code_key UNIQUE (FileLabelId, Code);

ALTER TABLE hr_staff_infos ALTER COLUMN JobsLevel TYPE varchar(50);
ALTER TABLE hr_staff_infos ALTER COLUMN JobsLevel SET DEFAULT '';
