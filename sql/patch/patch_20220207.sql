ALTER TABLE maintenance_permit ADD COLUMN VehicleCode varchar(255) DEFAULT '';
ALTER TABLE maintenance_permit ALTER COLUMN VehicleCode SET NOT NULL;
ALTER TABLE maintenance_permit ALTER COLUMN VehicleCode DROP DEFAULT;
ALTER TABLE maintenance_permit ADD CONSTRAINT t_lmaintenance_permit_TopCorporationId_VehicleCode_key UNIQUE (TopCorporationId, VehicleCode);

ALTER TABLE maintenance_assess ADD COLUMN VehicleCode varchar(255) DEFAULT '';
ALTER TABLE maintenance_assess ALTER COLUMN VehicleCode SET NOT NULL;
ALTER TABLE maintenance_assess ALTER COLUMN VehicleCode DROP DEFAULT;
ALTER TABLE maintenance_assess ADD CONSTRAINT maintenance_assess_TopCorporationId_VehicleCode_key UNIQUE (TopCorporationId, VehicleCode);

ALTER TABLE maintenance_driving_certificate ADD COLUMN VehicleCode varchar(255) DEFAULT '';
ALTER TABLE maintenance_driving_certificate ALTER COLUMN VehicleCode SET NOT NULL;
ALTER TABLE maintenance_driving_certificate ALTER COLUMN VehicleCode DROP DEFAULT;
ALTER TABLE maintenance_driving_certificate ADD CONSTRAINT maintenance_driving_certificate_TopCorpId_VehicleCode_key UNIQUE (TopCorporationId, VehicleCode);