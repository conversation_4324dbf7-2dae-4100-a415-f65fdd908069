CREATE DATABASE "erpv2"
    ENCODING 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8';

-- 人资-劳动合同表
CREATE TABLE IF NOT EXISTS hr_labor_contract
(
    Id               serial,
    CorporationId    bigint      NOT NULL,     -- 机构id
    TopCorporationId bigint      NOT NULL,     -- 根机构id
    StaffId          integer     NOT NULL,     -- 员工id
    Name             varchar(50) NOT NULL,     -- 员工名
    Code             varchar(50) NOT NULL,     -- 合同编号
    Type             smallint    NOT NULL,     -- 合同类型 1固定期限 2无固定期限
    Salary           integer     NOT NULL,     -- 合同薪资 月薪 单位分
    EffectedAt       bigint      NOT NULL,     -- 合同生效日期
    ExpiredAt        bigint      NOT NULL,     -- 合同终止日期
    Status           smallint       DEFAULT 1, -- 合同状态 1正常 2终止 4解约
    StaffIdStatus    smallint       DEFAULT 1, -- (合同上的)员工状态 1正式 2试用期
    ProbationAt      bigint         DEFAULT 0, -- 试用期到期日期 unix timestamp s
    ProbationSal     integer        DEFAULT 0, -- 试用期薪资 月薪 单位分
    CreatedAt        timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt        timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT hr_labor_contract_pkey PRIMARY KEY (Id),
    CONSTRAINT hr_labor_contract_Code_key UNIQUE (TopCorporationId, Code)
);

-- 人资-社保基数表
CREATE TABLE IF NOT EXISTS hr_insurance_base
(
    Id                       serial,
    CorporationId            bigint      NOT NULL,     -- 机构id
    TopCorporationId         bigint      NOT NULL,     -- 根机构id
    StaffId                  integer     NOT NULL,     -- 员工id
    Name                     varchar(50) NOT NULL,     -- 员工名
    IdNumber                 varchar(50) NOT NULL,     -- 身份证号
    EndowmentBase            integer     NOT NULL,     -- 养老基数 单位分
    MedicalBase              integer     NOT NULL,     -- 医疗基数 单位分
    UnemploymentBase         integer        DEFAULT 0, -- 失业基数 单位分 (岱山特有字段)
    IndustrialInjuryBase     integer        DEFAULT 0, -- 工伤基数 单位分 (岱山特有字段)
    EndowmentUnit            integer     NOT NULL,     -- 养老单位 单位分
    EndowmentPersonal        integer     NOT NULL,     -- 养老个人 单位分
    UnemploymentUnit         integer     NOT NULL,     -- 失业单位 单位分
    UnemploymentPersonal     integer     NOT NULL,     -- 失业个人 单位分
    Medical                  integer     NOT NULL,     -- 医疗保险 单位分
    CivilServiceSubsidy      integer     NOT NULL,     -- 公务员补助 单位分
    ReliefFund               integer     NOT NULL,     -- 救助金 单位分
    IndustrialInjuryExpenses integer     NOT NULL,     -- 工伤费 单位分
    ChildbearingExpenses     integer     NOT NULL,     -- 生育费 单位分
    CreatedAt                timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt                timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT hr_insurance_base_pkey PRIMARY KEY (Id),
    CONSTRAINT hr_insurance_base_StaffId_key UNIQUE (StaffId)
);

-- 人资-公积金管理
CREATE TABLE IF NOT EXISTS hr_accumulation_fund
(
    Id               serial,
    CorporationId    bigint      NOT NULL,     -- 机构id
    TopCorporationId bigint      NOT NULL,     -- 根机构id
    StaffId          integer     NOT NULL,     -- 员工id
    Name             varchar(50) NOT NULL,     -- 员工名
    IdNumber         varchar(50) NOT NULL,     -- 身份证号
    Base             integer     NOT NULL,     -- 公积金基数 单位分
    Unit             integer     NOT NULL,     -- 公积金单位部分 单位分
    Personal         integer     NOT NULL,     -- 公积金个人部分 单位分
    TotalAmount      integer        DEFAULT 0, -- 合计金额
    BuyAt            bigint         DEFAULT 0, -- 购买日期
    PayAt            bigint         DEFAULT 0, -- 缴纳日期
    PayTime          bigint         DEFAULT 0, -- 缴纳时间 单位月
    InsuranceBuyAt   integer        DEFAULT 0, -- 社保购入日期
    InsurancePayAt   integer        DEFAULT 0, -- 社保缴纳日期
    InsurancePayTime integer        DEFAULT 0, -- 社保缴纳时长 单位月
    CreatedAt        timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt        timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT hr_accumulation_fund_pkey PRIMARY KEY (Id)
);

-- 安全-司机驾驶证管理表
CREATE TABLE IF NOT EXISTS sc_driver_license
(
    Id               serial,
    CorporationId    bigint      NOT NULL,      -- 机构id
    TopCorporationId bigint      NOT NULL,      -- 根机构id
    StaffId          integer     NOT NULL,      -- 司机员工id
    Name             varchar(50) NOT NULL,      -- 司机姓名
    IdCode           varchar(50) NOT NULL,      -- 驾驶证号
    Code             varchar(50) NOT NULL,      -- 档案编号
    Model            varchar(50) NOT NULL,      -- 准驾车型
    IssuingAt        bigint         DEFAULT 0,  -- 发证日期
    ExpiredAt        bigint         DEFAULT 0,  -- 到期日期
    Authority        varchar(50)    DEFAULT '', -- 核发机关
    Addr             text           DEFAULT '', -- 发证地址
    Responsible      varchar(50),               -- 责任人
    Status           smallint    NOT NULL,      -- 驾驶证状态 1正常 2已换
    CreatedAt        timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt        timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT sc_driver_license_pkey PRIMARY KEY (Id),
    CONSTRAINT sc_driver_license_driver_staff_id_key UNIQUE (StaffId),
    CONSTRAINT sc_driver_license_no_key UNIQUE (TopCorporationId, Code)
);

-- 通用 (台州) 表

-- 目录标签表
CREATE TABLE IF NOT EXISTS file_labels
(
    Id               serial,                   -- id
    CorporationId    bigint      NOT NULL,     -- 机构id
    TopCorporationId bigint      NOT NULL,     -- 根机构id

    ParentId         integer        DEFAULT 0, -- 上级节点数据库id，默认0为顶级节点(目录)
    Name             varchar(50) NOT NULL,     -- 目录名

    CreatedAt        timestamptz(6) DEFAULT current_timestamp,
    UpdatedAt        timestamptz(6) DEFAULT current_timestamp,
    CONSTRAINT file_labels_pkey PRIMARY KEY (Id),
    CONSTRAINT file_labels_TopCorporationId_ParentId_Name_key UNIQUE (TopCorporationId, ParentId, Name)
);

-- -- 员工标签下的文件
CREATE TABLE IF NOT EXISTS staff_files
(
    Id               serial,                    -- 文件id
    CorporationId    bigint  NOT NULL,          -- 机构id  冗余字段 上传使用code此字段为0
    TopCorporationId bigint  NOT NULL,          -- 根机构id 冗余字段
    StaffId          integer        DEFAULT 0,  -- 关联人员 冗余字段
    StaffName        varchar(50)    DEFAULT '', -- 人员名 冗余字段
    Code             varchar(255)   DEFAULT '', -- 批量导入用此字段 人员id 控制面板录入的唯一值

    FileLabelId      integer NOT NULL,          -- 标签id 一对一fk->file_labels.Id
    Name             text    NOT NULL,          -- 文件名 格式：
    Suffix           varchar(50)    DEFAULt '', -- 文件后缀 例子：".jpeg"
    Path             text    NOT NULL,          -- 文件相对路径 包含后缀
    Size             integer NOT NULL,          -- 文件大小 单位Byte
    Progress         smallint       DEFAULT 0, /* 文件上传进度：0-100 */

    CreatedAt        timestamptz(6) DEFAULT current_timestamp,
    UpdatedAt        timestamptz(6) DEFAULT current_timestamp,
    CONSTRAINT staff_files_pkey PRIMARY KEY (Id),
    CONSTRAINT staff_files_FileLabelId_Code_key UNIQUE (FileLabelId, Code)
);

-- 人资-员工信息表
CREATE TABLE IF NOT EXISTS hr_staff_infos
(
    Id                serial,
    StaffId           integer        NOT NULL,   -- 员工id
    Code              varchar(255)   NOT NULL,   -- 人员id 控制面板录入的唯一值
    CorporationId     bigint         NOT NULL,   -- 机构id
    TopCorporationId  bigint         NOT NULL,   -- 根机构id
    StaffName         varchar(50)    NOT NULL,   -- 员工名
    Occupation        smallint       DEFAULT 0,  -- 工种 从主数据获取 0-司机; 1-乘务员;2-管理员

    JobsName          varchar(50)    DEFAULT '', -- 职位名
    JobsType          smallint       DEFAULT 0,  -- 职位类别 0无 1编制 2外聘 3临时
    JobsLevel         varchar(50)    DEFAULT '', -- 职位等级
    JobsAt            timestamptz(6) NOT NULL,   -- 任职时间
    JobsTitle         smallint       DEFAULT 0,  -- 职称 0无 1员级 2助理级 3中级 4副高级 5正高级

    EducationLevel    smallint       DEFAULT 0,  -- 学历 0无 1小学 2初中 3普通高中 4技工学校 5职业高中 6中等专科 7大学专科 8大学本科 9硕士在读 10硕士 11博士在读 12博士 20其它
    School            varchar(50)    DEFAULT '', -- 学校
    Major             varchar(50)    DEFAULT '', -- 专业
    EducationStartAt  timestamptz(6) NOT NULL,-- (学历)起始时间
    EducationEndAt    timestamptz(6) NOT NULL,--(学历)终止时间

    ContinuingName    varchar(50)    DEFAULT '', -- 继续教育单位名称
    ContinuingContent varchar(150)   DEFAULT '', -- 继续教育内容
    ContinuingStartAt timestamptz(6) NOT NULL,-- (继续教育)起始时间
    ContinuingEndAt   timestamptz(6) NOT NULL,--(继续教育)终止始时间
    ContinuingRemark  text           DEFAULT '', -- 继续教育备注

    CreatedAt         timestamptz(6) DEFAULT current_timestamp,
    UpdatedAt         timestamptz(6) DEFAULT current_timestamp,
    CONSTRAINT hr_staff_infos_pkey PRIMARY KEY (StaffId),
    CONSTRAINT hr_staff_infos_TopCorporationId_Code_key UNIQUE (TopCorporationId, Code)
);

-- 人资-员工信息表-历任职位表
CREATE TABLE IF NOT EXISTS hr_staff_info_jobs
(
    Id               serial,
    CorporationId    bigint         NOT NULL,   -- 机构id
    TopCorporationId bigint         NOT NULL,   -- 根机构id
    StaffId          integer        NOT NULL,   -- 员工id 多对一fk->hr_staff_infos.StaffId
    StaffName        varchar(50)    NOT NULL,   -- 员工名

    JobsName         varchar(50)    NOT NULL,   -- 职位名
    JobsAt           timestamptz(6) NOT NULL,   -- 任职时间
    Remark           varchar(50)    DEFAULT '', -- 备注

    CreatedAt        timestamptz(6) DEFAULT current_timestamp,
    UpdatedAt        timestamptz(6) DEFAULT current_timestamp,
    CONSTRAINT hr_staff_info_jobs_pkey PRIMARY KEY (Id),
    CONSTRAINT hr_staff_info_jobs_StaffId_JobsAt_key UNIQUE (StaffId, JobsAt)
);

-- 人资-员工信息表-个人技能表
CREATE TABLE IF NOT EXISTS hr_staff_info_skills
(
    Id               serial,
    CorporationId    bigint      NOT NULL,      -- 机构id
    TopCorporationId bigint      NOT NULL,      -- 根机构id
    StaffId          integer     NOT NULL,      -- 员工id 多对一fk->hr_staff_infos.StaffId
    StaffName        varchar(50) NOT NULL,      -- 员工名

    SkillName        varchar(50) NOT NULL,      -- 技能名
    SkillLevel       varchar(50)    DEFAULT '', -- 技能级别
    Remark           varchar(50)    DEFAULT '',--备注

    CreatedAt        timestamptz(6) DEFAULT current_timestamp,
    UpdatedAt        timestamptz(6) DEFAULT current_timestamp,
    CONSTRAINT hr_staff_info_skills_pkey PRIMARY KEY (Id),
    CONSTRAINT hr_staff_info_skills_StaffId_SkillName_key UNIQUE (StaffId, SkillName)
);

-- 人资-员工信息表-从业资格证表
CREATE TABLE IF NOT EXISTS hr_staff_info_certificates
(
    Id                 serial,
    CorporationId      bigint         NOT NULL,   -- 机构id
    TopCorporationId   bigint         NOT NULL,   -- 根机构id
    StaffId            integer        NOT NULL,   -- 员工id 多对一fk->t2_hr_staff_info.StaffId
    StaffName          varchar(50)    NOT NULL,   -- 员工名

    CertificateName    varchar(50)    NOT NULL,   -- 从业资格证名称
    CertificateCode    varchar(100)   DEFAULT '', -- 从业资格证号
    CertificateUnit    varchar(100)   DEFAULT '', -- 发证机关
    CertificateStartAt timestamptz(6) NOT NULL,   -- 有效期起始时间
    CertificateEndAt   timestamptz(6) NOT NULL,   -- 有效期终止时间

    CreatedAt          timestamptz(6) DEFAULT current_timestamp,
    UpdatedAt          timestamptz(6) DEFAULT current_timestamp,
    CONSTRAINT hr_staff_info_certificates_pkey PRIMARY KEY (Id),
    CONSTRAINT hr_staff_info_certificates_StaffId_CertificateName_key UNIQUE (StaffId, CertificateName)
);

--------------v1.2

-- 上传文件表
CREATE TABLE IF NOT EXISTS files
(
    Id               serial,
    CorporationId    bigint  NOT NULL,          -- 机构id
    TopCorporationId bigint  NOT NULL,          -- 根机构id

    Name             text    NOT NULL,          -- 文件名  格式："abc.png" 源文件名
    Suffix           varchar(50)    DEFAULt '', -- 文件后缀  例子：".jpeg"
    Path             text    NOT NULL,          -- 文件相对路径  包含文件名、后缀名 uuid.png
    Size             integer NOT NULL,          -- 文件大小  单位Byte
    Progress         smallint       DEFAULT 0,  -- 文件上传进度：0-100

    CreatedAt        timestamptz(6) DEFAULT current_timestamp,
    UpdatedAt        timestamptz(6) DEFAULT current_timestamp,
    CONSTRAINT files_pkey PRIMARY KEY (Id)
);

-- door check
-- 安全-门检-设置门检项目、标签表
-- 项目(item) 例如：三漏、体温、酒精、血压...
---- 正常
------ 标签1(label)
------ 标签2
---- 异常
------ 标签1  例如：漏油
------ 标签2  例如：漏水
CREATE TABLE IF NOT EXISTS security_door_check_items
(
    Id               serial,                    -- 门检项目id  门检标签id
    CorporationId    bigint      NOT NULL,      -- 机构id
    TopCorporationId bigint      NOT NULL,      -- 根机构id

    ItemType         smallint    NOT NULL,      -- 门检项目类别  0门检标签的ItemType=0  1司机门检项目  2车辆门检项目
    IsDescNormal     integer        DEFAULT 0,  -- 门检项目-是否描述正常 1描述 2不描述
    IsDescAbNormal   integer        DEFAULT 0,  -- 门检项目-是否描述异常 1描述 2不描述
    Nature           smallint    NOT NULL,      -- 门检标签属性 0项目的Nature=0 1正常 2异常
    ParentId         integer        DEFAULT 0,  -- 门检标签归属的门检项目Id  门检项目的ParentId=0

    FkIconFileId     integer        DEFAULT 0,  -- 门检项目icon 标签为0 fk -> files.id
    IconFileUrl      text           DEFAULT '', -- 门检项目icon 标签为'' url

    Name             varchar(50) NOT NULL,      -- 门检项目名  项目下的门检标签名
    Sort             smallint       DEFAULT 1,  -- 显示顺序  ORDER BY Sort ASC

    IsDeleted        smallint       DEFAULT 0,  -- 是否已删除 0否 1是 软删除后不允许恢复

    CreatedAt        timestamptz(6) DEFAULT current_timestamp,
    UpdatedAt        timestamptz(6) DEFAULT current_timestamp,
    CONSTRAINT sc_door_check_items_pkey PRIMARY KEY (Id)
--     考虑用户在删除一个项目后 再创建一个同名项目(项目下的标签与之前不同) 之前的数据需要展示 所以不使用唯一约束 在代码中查询判断保持以下唯一性
--     同一个机构下 同一个项目类别(ItemType) (IsDeleted=0的)项目名唯一
--     同一个项目名 (IsDeleted=0的)标签名唯一
--     软删除后不允许恢复,仅供匹配历史记录使用
--     CONSTRAINT t2_sc_door_check_items_TopCorporationId_ParentId_Name_key UNIQUE (TopCorporationId, ParentId, Name)
);

-- 安全-门检-车辆自检、抽检记录表 每提交一次结果增加一行记录
CREATE TABLE IF NOT EXISTS security_door_check_records
(
    Id               serial,                   --
    CorporationId    bigint      NOT NULL,     -- 机构id
    TopCorporationId bigint      NOT NULL,     -- 根机构id

    License          varchar(50) NOT NULL,     -- 车辆车牌号
    VehicleCode      varchar(50) NOT NULL,     -- 车辆编号
    StaffId          integer     NOT NULL,     -- 司机员工id
    StaffName        varchar(50) NOT NULL,     -- 司机名
    Type             smallint    NOT NULL,     -- 检测类型 1自检 2抽检
    AdminStaffId     integer     NOT NULL,     -- 抽检人员工id 自检为0
    AdminStaffName   varchar(50) NOT NULL,     -- 抽检人名 自检为''

    CheckForm        smallint       DEFAULT 1, -- 检测形式 1司机出场自检 2司机回场自检 4管理员抽检

    Result           smallint       DEFAULT 1, -- 自检、抽检最终结果(所有结果有一项异常则Result=2) 1正常 2异常
    Status           smallint       DEFAULT 1, -- 状态 Result=1时 status=1正常   2(Result=2时)待整改 4(Result=2时)已整改 8(Result=2时)已上报
    -- 子项状态全部相同时 取相同状态值 否则取值为待整改

    CreatedAt        timestamptz(6) DEFAULT current_timestamp,
    UpdatedAt        timestamptz(6) DEFAULT current_timestamp,
    CONSTRAINT security_door_check_record_pkey PRIMARY KEY (Id)
);

-- 安全-门检-车辆自检、抽检结果表 记录某一次自检、抽检的具体项目及结果
--
CREATE TABLE IF NOT EXISTS security_door_check_item_results
(
    Id               serial,               --
    CorporationId    bigint      NOT NULL, -- 机构id
    TopCorporationId bigint      NOT NULL, -- 根机构id

    License          varchar(50) NOT NULL, -- 冗余字段 车辆车牌号
    VehicleCode      varchar(50) NOT NULL, -- 冗余字段 车辆编号
    StaffId          integer     NOT NULL, -- 冗余字段 司机员工id
    StaffName        varchar(50) NOT NULL, -- 冗余字段 司机名
    Type             smallint    NOT NULL, -- 冗余字段 检测类型 1自检 2抽检
    AdminStaffId     integer     NOT NULL, -- 冗余字段 抽检人员工id 自检为0
    AdminStaffName   varchar(50) NOT NULL, -- 冗余字段 抽检人名 自检为''

    FkRecordId       integer     NOT NULL, -- 归属记录id 多对一fk -> t2_security_door_check_record.Id

    FkItemId         integer     NOT NULL, -- (所属)门检项目id  fk -> security_door_check_items.Id
    ItemName         varchar(50) NOT NULL, -- 门检项目名 冗余字段 security_door_check_items.Name
    Result           smallint    NOT NULL, -- 该门检项目结果 1正常 2异常
    Status           smallint    NOT NULL, -- 该门检项目状态 1正常 2待整改 4已整改 8已上报
    ResultJson       json, /* --项目内容
                            {
                                "NotRectified":   { --(异常情况)未整改、需要整改的内容 或 (正常情况)证据
                                    "Labels": [
                                        {
                                            "FkLabelId": int64, -- 标签id fk -> security_door_check_items.Id
                                            "Name": string, -- 标签名
                                        }
                                    ],
                                    "Files": [
                                        {
                                            "FkFileId": int64, -- 文件id fk -> files.Id
                                            "Path": string, -- 文件相对路径 files.Path
                                            "Type": int64, --文件类型(兼容老数据0也当作图片处理) 1图片 2视频
                                            "Url": string, -- URL
                                        }
                                    ]
                                    "Remark": string // 评论
                                },
                                "Rectified":    [
                                    { --(异常情况)已整改(整改后)的内容 -- (正常情况)暂无
                                        "Files": [
                                            {
                                                "FkFileId": int64, -- 文件id fk -> files.Id
                                                "Path": string, -- 文件相对路径 files.Path
                                                "Type": int64, --文件类型(兼容老数据0也当作图片处理) 1图片 2视频
                                                "Url": string, -- URL
                                            }
                                        ],
                                        "Remark": string // 评论
                                        "CreatedAt": int64 // 整改(添加)时间 unix timestamp
                                    },
                                ]-- 追加数据

                            } */

    CreatedAt        timestamptz(6) DEFAULT current_timestamp,
    UpdatedAt        timestamptz(6) DEFAULT current_timestamp,
    CONSTRAINT security_door_check_item_results_pkey PRIMARY KEY (Id)
);

-- 车辆门检微信二维码表 door check
-- CREATE TABLE IF NOT EXISTS t2_door_check_wechat_qr_code
-- (
--     Id               serial,                    --
--     CorporationId    bigint      NOT NULL,      -- 机构id
--     TopCorporationId bigint      NOT NULL,      -- 根机构id
--
--     License          varchar(50) NOT NULL,      -- 车辆车牌号
--     VehicleCode      varchar(50) NOT NULL,      -- 车辆编号
--
--     FileId           integer        DEFAULT 0,  -- 附件id fk -> files.Id
--     FilePath         text           DEFAULT '', -- 附件文件相对路径 fk -> files.Path
--
--     CreatedAt        timestamptz(6) DEFAULT current_timestamp,
--     UpdatedAt        timestamptz(6) DEFAULT current_timestamp,
--     CONSTRAINT t2_door_check_wechat_qr_code_pkey PRIMARY KEY (Id),
--     CONSTRAINT t2_door_check_wechat_qr_code_License_key UNIQUE (License),
--     CONSTRAINT t2_door_check_wechat_qr_code_VehicleCode_key UNIQUE (VehicleCode)
-- );

------- v1.3

-- 票务模块

-- 点钞/票务记录
CREATE TABLE IF NOT EXISTS ticket_count_moneys
(
    Id                  serial,                                   --

    GroupId             bigint      NOT NULL,                     -- 集团Id
    CompanyId           bigint      NOT NULL,                     -- 公司Id
    BranchId            bigint      NOT NULL,                     -- 分公司Id
    DepartmentId        bigint      NOT NULL,                     -- 部门Id
    FleetId             bigint      NOT NULL,                     -- 车队Id

    Type                smallint       DEFAULT 0,                 -- 记录类型 1点钞记录 2票款记录

    License             varchar(50) NOT NULL,                     -- 车辆车牌号
    VehicleCode         varchar(50) NOT NULL,                     -- 车辆编号
    LineId              integer        DEFAULT 0,                 -- 默认车属线路 也可以是选择的线路
    Line                varchar(50)    DEFAULT '',                -- 线路名

    DriverStaffId       integer     NOT NULL,                     -- 司机staffId 主数据司机表中的Id字段
    DriverName          varchar(50)    DEFAULT '',                -- 司机姓名
    DriverStaffIdStr    varchar(50)    DEFAULT '',                -- 司机员工工号 主数据司机表中的staffId字段

    AttendantStaffId    integer     NOT NULL,                     -- 乘务员staffId 主数据司机表中的Id字段 type=2才有值
    AttendantName       varchar(50)    DEFAULT '',                -- 乘务员姓名 type=2才有值
    AttendantStaffIdStr varchar(50)    DEFAULT '',                -- 乘务员员工工号 主数据司机表中的staffId字段 type=2才有值

    ReviewerStaffId     integer     NOT NULL,                     -- 复核员staffId 主数据司机表中的Id字段
    ReviewerName        varchar(50)    DEFAULT '',                -- 复核员姓名
    ReviewerStaffIdStr  varchar(50)    DEFAULT '',                -- 复核员工号 主数据司机表中的staffId字段

    InputStaffId        integer     NOT NULL,                     -- 录入员staffId 主数据司机表中的Id字段
    InputName           varchar(50)    DEFAULT '',                -- 录入员姓名

    ReportAt            timestamptz(6) DEFAULT current_timestamp, -- 录入时间
    IncomeAt            timestamptz(6) DEFAULT current_timestamp, -- 点钞时间/票款时间 天
    TimeSlot            smallint       DEFAULT 1,                 -- 点钞时间段 1全天 2上午 4下午 8夜班
    Status              smallint       DEFAULT 1,                 -- 状态 1正常 2日期异常 4数据异常 (2+4所有异常)


    Paper10000          integer        DEFAULT 0,                 -- 100元纸币张数
    Paper5000           integer        DEFAULT 0,                 -- 50元纸币张数
    Paper2000           integer        DEFAULT 0,                 -- 20元纸币张数
    Paper1000           integer        DEFAULT 0,                 -- 10元纸币张数
    Paper500            integer        DEFAULT 0,                 -- 5元纸币张数
    Paper200            integer        DEFAULT 0,                 -- 2元纸币张数
    Paper100            integer        DEFAULT 0,                 -- 1元纸币张数
    Paper50             integer        DEFAULT 0,                 -- 5角纸币张数
    Paper20             integer        DEFAULT 0,                 -- 2角纸币张数
    Paper10             integer        DEFAULT 0,                 -- 1角纸币张数
    PaperTotalCount     integer        DEFAULT 0,                 -- 纸币总张数
    PaperTotalAmount    integer        DEFAULT 0,                 -- 纸币总金额 单位 分

    Coin100             integer        DEFAULT 0,                 -- 1元硬币枚数
    Coin50              integer        DEFAULT 0,                 -- 5角硬币枚数
    Coin10              integer        DEFAULT 0,                 -- 1角硬币枚数
    CoinTotalCount      integer        DEFAULT 0,                 -- 硬币总枚数
    CoinTotalAmount     integer        DEFAULT 0,                 -- 硬币总金额 单位 分

    MoneyFake           integer        DEFAULT 0,                 -- 假币数
    MoneyForeign        integer        DEFAULT 0,                 -- 外币数
    MoneyOther          integer        DEFAULT 0,                 -- 其它币数

    TotalAmount         integer        DEFAULT 0,                 --总金额 单位 分  纸币总金额+硬币总金额-（残次币张数*（100分））
    ReceivableAmount    integer        DEFAULT 0,                 --应收总金额 单位 分  纸币总金额+硬币总金额  不扣除残次币金额

    Ticket100           integer        DEFAULT 0,                 -- 1.0元票额张数 (type=2, 特有的字段)
    Ticket150           integer        DEFAULT 0,                 -- 1.5元票额张数 (type=2, 特有的字段)
    Ticket200           integer        DEFAULT 0,                 -- 2.0元票额张数 (type=2, 特有的字段)
    Ticket250           integer        DEFAULT 0,                 -- 2.5元票额张数 (type=2, 特有的字段)
    Ticket300           integer        DEFAULT 0,                 -- 3.0元票额张数 (type=2, 特有的字段)
    Ticket350           integer        DEFAULT 0,                 -- 3.5元票额张数 (type=2, 特有的字段)
    Ticket400           integer        DEFAULT 0,                 -- 4.0元票额张数 (type=2, 特有的字段)
    Ticket450           integer        DEFAULT 0,                 -- 4.5元票额张数 (type=2, 特有的字段)
    Ticket500           integer        DEFAULT 0,                 -- 5.0元票额张数 (type=2, 特有的字段)
    Ticket550           integer        DEFAULT 0,                 -- 5.5元票额张数 (type=2, 特有的字段)
    Ticket600           integer        DEFAULT 0,                 -- 6.0元票额张数 (type=2, 特有的字段)
    Ticket650           integer        DEFAULT 0,                 -- 6.5元票额张数 (type=2, 特有的字段)
    Ticket700           integer        DEFAULT 0,                 -- 7.0元票额张数 (type=2, 特有的字段)
    Ticket750           integer        DEFAULT 0,                 -- 7.5元票额张数 (type=2, 特有的字段)
    Ticket800           integer        DEFAULT 0,                 -- 8.0元票额张数 (type=2, 特有的字段)
    Ticket850           integer        DEFAULT 0,                 -- 8.5元票额张数 (type=2, 特有的字段)
    Ticket900           integer        DEFAULT 0,                 -- 9.0元票额张数 (type=2, 特有的字段)
    Ticket950           integer        DEFAULT 0,                 -- 9.5元票额张数 (type=2, 特有的字段)
    Ticket1000          integer        DEFAULT 0,                 -- 10.0元票额张数 (type=2, 特有的字段)
    TicketTotalCount    integer        DEFAULT 0,                 -- 票总张数 (type=2, 特有的字段)
    TicketTotalAmount   integer        DEFAULT 0,                 -- 票总金额 单位 分 (type=2, 特有的字段)

    CreatedAt           timestamptz(6) DEFAULT current_timestamp,
    UpdatedAt           timestamptz(6) DEFAULT current_timestamp,
    CONSTRAINT ticket_count_moneys_pkey PRIMARY KEY (Id)
);