-- 上传文件
INSERT INTO permissions(Entity, ApiUrl) VALUES('文件分片上传','/erp/v2/file/Upload');
-- 查询机构列表
INSERT INTO permissions(Entity, ApiUrl) VALUES('机构列表','/erp/v2/corporation/List');
INSERT INTO permissions(Entity, ApiUrl) VALUES('机构人员列表','/erp/v2/corporation/ListStaff');
INSERT INTO permissions(Entity, ApiUrl) VALUES('机构列表配置','/erp/v2/corporation/ListConfig');

--车辆
INSERT INTO permissions(Entity, ApiUrl) VALUES('车辆列表','/erp/v2/vehicle/List');
--线路
INSERT INTO permissions(Entity, ApiUrl) VALUES('线路列表','/erp/v2/line/List');

INSERT INTO permissions(Entity, ApiUrl) VALUES('查询所有员工','/erp/v2/staff/GetStaffByStaffIdStr');
INSERT INTO permissions(Entity, ApiUrl) VALUES('查询账号所属员工','/erp/v2/staff/List');

--新版人资
INSERT INTO permissions(Entity, ApiUrl) VALUES('导入员工档案','/erp/v2/staffarchive/Import');
INSERT INTO permissions(Entity, ApiUrl) VALUES('员工档案列表','/erp/v2/staffarchive/List');
INSERT INTO permissions(Entity, ApiUrl) VALUES('员工档案导出','/erp/v2/staffarchive/Export');
INSERT INTO permissions(Entity, ApiUrl) VALUES('通过入职申请','/erp/v2/staffarchive/SubmitApply');
INSERT INTO permissions(Entity, ApiUrl) VALUES('新增员工档案','/erp/v2/staffarchive/Create');
INSERT INTO permissions(Entity, ApiUrl) VALUES('编辑员工档案信息','/erp/v2/staffarchive/Edit');
INSERT INTO permissions(Entity, ApiUrl) VALUES('员工档案详情','/erp/v2/staffarchive/Info');
INSERT INTO permissions(Entity, ApiUrl) VALUES('根据员工ID查询档案信息','/erp/v2/staffarchive/StaffInfo');
INSERT INTO permissions(Entity, ApiUrl) VALUES('员工档案修改日志记录','/erp/v2/staffarchive/Logger');

INSERT INTO permissions(Entity, ApiUrl) VALUES('添加入职人员','/erp/v2/joincompanyapply/BatchCreateStaff');
INSERT INTO permissions(Entity, ApiUrl) VALUES('入职管理列表','/erp/v2/joincompanyapply/List');
INSERT INTO permissions(Entity, ApiUrl) VALUES('删除入职人员','/erp/v2/joincompanyapply/Delete');
INSERT INTO permissions(Entity, ApiUrl) VALUES('查看入职详情','/erp/v2/joincompanyapply/ApplyInfo');


-- 岗位管理
INSERT INTO permissions(Entity, ApiUrl) VALUES('岗位管理列表','/erp/v2/workpost/List');
INSERT INTO permissions(Entity, ApiUrl) VALUES('岗位管理添加','/erp/v2/workpost/Create');
INSERT INTO permissions(Entity, ApiUrl) VALUES('岗位管理编辑','/erp/v2/workpost/Edit');
INSERT INTO permissions(Entity, ApiUrl) VALUES('岗位管理设置状态','/erp/v2/workpost/SwitchStatus');
INSERT INTO permissions(Entity, ApiUrl) VALUES('岗位选择列表','/erp/v2/workpost/SelectList');

INSERT INTO permissions(Entity, ApiUrl) VALUES('员工调岗列表','/erp/v2/stafftransfer/List');
INSERT INTO permissions(Entity, ApiUrl) VALUES('新增员工调岗','/erp/v2/stafftransfer/Create');

INSERT INTO permissions(Entity, ApiUrl) VALUES('员工离职列表','/erp/v2/staffquit/List');
INSERT INTO permissions(Entity, ApiUrl) VALUES('新增员工离职','/erp/v2/staffquit/Create');

INSERT INTO permissions(Entity, ApiUrl) VALUES('员工退休列表','/erp/v2/staffretire/List');
INSERT INTO permissions(Entity, ApiUrl) VALUES('新增员工退休','/erp/v2/staffretire/Create');
INSERT INTO permissions(Entity, ApiUrl) VALUES('退休返聘','/erp/v2/staffretire/SwitchStatus');

INSERT INTO permissions(Entity, ApiUrl) VALUES('员工历史版本列表','/erp/v2/staffarchiveversion/List');
INSERT INTO permissions(Entity, ApiUrl) VALUES('新增历史版本','/erp/v2/staffarchiveversion/Create');
INSERT INTO permissions(Entity, ApiUrl) VALUES('历史版本对应的员工记录','/erp/v2/staffarchiveversion/StaffRecord');
INSERT INTO permissions(Entity, ApiUrl) VALUES('导出员工记录','/erp/v2/staffarchiveversion/Export');

-- 门检
INSERT INTO permissions(Entity, ApiUrl) VALUES('检查人员创建','/erp/v2/doorcheck/AddInspector');
INSERT INTO permissions(Entity, ApiUrl) VALUES('检查人员列表','/erp/v2/doorcheck/ListInspector');
INSERT INTO permissions(Entity, ApiUrl) VALUES('检查人员编辑','/erp/v2/doorcheck/EditInspector');
INSERT INTO permissions(Entity, ApiUrl) VALUES('自定义门检项目创建','/erp/v2/doorcheck/Add');
INSERT INTO permissions(Entity, ApiUrl) VALUES('自定义门检项目编辑','/erp/v2/doorcheck/Edit');
INSERT INTO permissions(Entity, ApiUrl) VALUES('自定义门检项目删除','/erp/v2/doorcheck/Delete');
INSERT INTO permissions(Entity, ApiUrl) VALUES('自定义门检项目列表','/erp/v2/doorcheck/List');
INSERT INTO permissions(Entity, ApiUrl) VALUES('门检类别新增','/erp/v2/doorcheck/AddItemType');
INSERT INTO permissions(Entity, ApiUrl) VALUES('门检类别编辑','/erp/v2/doorcheck/EditItemType');
INSERT INTO permissions(Entity, ApiUrl) VALUES('门检类别删除','/erp/v2/doorcheck/DeleteItemType');
INSERT INTO permissions(Entity, ApiUrl) VALUES('门检结果新增','/erp/v2/doorcheck/AddResult');
INSERT INTO permissions(Entity, ApiUrl) VALUES('门检结果web列表','/erp/v2/doorcheck/ListWebResult');
INSERT INTO permissions(Entity, ApiUrl) VALUES('门检结果app列表','/erp/v2/doorcheck/ListAppResult');
INSERT INTO permissions(Entity, ApiUrl) VALUES('门检结果详情','/erp/v2/doorcheck/GetResultDetail');
INSERT INTO permissions(Entity, ApiUrl) VALUES('门检结果app详情','/erp/v2/doorcheck/GetAppResultDetail');
INSERT INTO permissions(Entity, ApiUrl) VALUES('门检结果整改','/erp/v2/doorcheck/EditResult');
INSERT INTO permissions(Entity, ApiUrl) VALUES('门检结果上报','/erp/v2/doorcheck/ReportResult');
INSERT INTO permissions(Entity, ApiUrl) VALUES('门检项目(待检查)列表','/erp/v2/doorcheck/TodoList');
INSERT INTO permissions(Entity, ApiUrl) VALUES('过渡页司机是否门检','/erp/v2/doorcheck/IsChecked');
INSERT INTO permissions(Entity, ApiUrl) VALUES('门检结果处理','/erp/v2/doorcheck/ProcessResult');

-- 票务模块

-- 点钞
INSERT INTO permissions(Entity, ApiUrl) VALUES('点钞创建','/erp/v2/countmoney/Add');
INSERT INTO permissions(Entity, ApiUrl) VALUES('点钞列表','/erp/v2/countmoney/List');
INSERT INTO permissions(Entity, ApiUrl) VALUES('点钞编辑','/erp/v2/countmoney/Edit');
INSERT INTO permissions(Entity, ApiUrl) VALUES('点钞删除','/erp/v2/countmoney/Delete');
INSERT INTO permissions(Entity, ApiUrl) VALUES('点钞操作日志列表','/erp/v2/countmoney/Logger');
INSERT INTO permissions(Entity, ApiUrl) VALUES('点钞批量复核','/erp/v2/countmoney/BatchReview');
INSERT INTO permissions(Entity, ApiUrl) VALUES('点钞全部复核','/erp/v2/countmoney/AllReview');

-- 票款
INSERT INTO permissions(Entity, ApiUrl) VALUES('票款创建','/erp/v2/countticket/Add');
INSERT INTO permissions(Entity, ApiUrl) VALUES('票款列表','/erp/v2/countticket/List');
INSERT INTO permissions(Entity, ApiUrl) VALUES('票款编辑','/erp/v2/countticket/Edit');
INSERT INTO permissions(Entity, ApiUrl) VALUES('票款删除','/erp/v2/countticket/Delete');
INSERT INTO permissions(Entity, ApiUrl) VALUES('票款操作日志列表','/erp/v2/countticket/Logger');
INSERT INTO permissions(Entity, ApiUrl) VALUES('票款批量复核','/erp/v2/countticket/BatchReview');
INSERT INTO permissions(Entity, ApiUrl) VALUES('票款全部复核','/erp/v2/countticket/AllReview');

-- 报表
INSERT INTO permissions(Entity, ApiUrl) VALUES('残次币列表','/erp/v2/report/ListDefective');
INSERT INTO permissions(Entity, ApiUrl) VALUES('公司营收报表','/erp/v2/report/IncomeCompany');
INSERT INTO permissions(Entity, ApiUrl) VALUES('线路营收报表','/erp/v2/report/IncomeLine');
INSERT INTO permissions(Entity, ApiUrl) VALUES('车辆营收报表','/erp/v2/report/IncomeVehicle');
INSERT INTO permissions(Entity, ApiUrl) VALUES('乘务员日报','/erp/v2/report/AttendantDaily');
INSERT INTO permissions(Entity, ApiUrl) VALUES('多票制报表','/erp/v2/report/MultiTicket');


-- 请假
INSERT INTO permissions(Entity, ApiUrl) VALUES('请假申请列表','/erp/v2/staffleave/ApplyList');
INSERT INTO permissions(Entity, ApiUrl) VALUES('发起请假申请','/erp/v2/staffleave/Create');
INSERT INTO permissions(Entity, ApiUrl) VALUES('请假详情','/erp/v2/staffleave/Show');
INSERT INTO permissions(Entity, ApiUrl) VALUES('年休假列表','/erp/v2/staffleave/LeaveList');
INSERT INTO permissions(Entity, ApiUrl) VALUES('导出年休假列表','/erp/v2/staffleave/LeaveListExport');
INSERT INTO permissions(Entity, ApiUrl) VALUES('年休假编辑','/erp/v2/staffleave/LeaveEdit');
INSERT INTO permissions(Entity, ApiUrl) VALUES('年休假规则','/erp/v2/staffleave/ShowLeaveRule');
INSERT INTO permissions(Entity, ApiUrl) VALUES('年休假规则设置','/erp/v2/staffleave/SetLeaveRule');
