{"# 规范要求": "参考http://www.oetlink.com/关于《软件程序运行配置文件》管理规范技术要求.pdf", "# 重点说明": "版本一旦发布之后, 配置文件处于受控状态, 不可轻易变更, 如需变更请走变更流程", "# 环境变量": "production:生产环境 local:本地环境 develop:开发环境", "Env": "local", "# 日志等级": "Info:显示所有日志 error:显示错误日志", "DebugLevel": "Info", "# 文件绝对路径前缀": "", "AbsDirPath": "/mnt/www/", "# 文件相对路径前缀": "", "WebRoot": "webroot", "# 文件访问域名端口": "", "StaticFileHttpPrefix": "https://*************:38085/", "# 是否关联调度": "true:关联调度 false:不关联调度", "IsRelationIss": true, "# 是否迁移数据表": "true迁移  false不迁移", "EnableMigrateDatabase": false, "# 数据库配置": "Host:主机地址 Port:端口 DbName:数据库名称 User:登陆账号 Password:登陆密码", "Database": {"Host": "*************", "Port": 5432, "DbName": "erp", "User": "oet_erp", "Password": "pass123456"}, "# ETCD配置": "Host:主机 Port:端口 Interval:心跳间隔  Ttl:失效时间", "Etcd": {"Host": "*************", "Port": 2379, "Interval": 10, "Ttl": 30}, "# micro配置": "ServiceName:服务名称", "Micro": {"ServiceName": "oet.scs.api.erp.v2"}, "# 小程序消息配置": "AppAccountId:对应的小程序ID DoorCheckTemplateId:门检消息模板ID Page:消息跳转的页面", "Mini": {"AppAccountId": 0, "DoorCheckTemplateId": "", "Page": "erp/notice/index"}, "# 是否同步主数据的人员到ERP": "Enable:是否同步 CorporationIds:同步的顶级机构ID", "SyncMasterStaff": {"Enable": false, "CorporationIds": []}, "# 定时任务配置": "IsSendDoorCheckNotify:是否发送门检通知 IsSendLaborContractExpireNotify:是否发送劳动合同过期通知 IsSendStaffProbationExpireNotify:是否发送试用期到期通知 IsPreGenerateDoorCheckRecord:是否预先生成们门检记录 IsCheckTicketDataPermission是否检测票务数据权限 IsProcessAlarm:是否流程报警", "SchedulerJob": {"IsSendDoorCheckNotify": false, "IsSendLaborContractExpireNotify": false, "IsSendStaffProbationExpireNotify": false, "IsPreGenerateDoorCheckRecord": false, "IsCheckTicketDataPermission": false, "IsProcessAlarm": false, "IsCalcStaffArchiveReport": false}, "# 顶级机构ID": "", "TopCorporationId": 1505961073969202177, "# 蓝凌流程配置": "Enable:是否开启 SysId:系统标识 AuthAccount:授权账号 AuthPassword:授权密码 ServicePrefix:ERP接口前缀 LbpmDomain:蓝凌接口前缀 ToDoPrefixDomain:待办跳转地址前缀", "Lbpm": {"Enable": false, "SysId": "erp-tz", "AuthAccount": "ekp", "AuthPassword": "ekp", "LbpmDomain": "http://oatest.tzgjjt.com:9005/", "ServicePrefix": "http://test.oetlink.com:48080/erp/v3/thirdpart/", "ToDoPrefixDomain": "http://lbpm.local.test:9900"}, "# 适用的权限": "local:本地(所有权限) taizhou:台州系统权限 daishan:岱山系统权限 lvcheng:律橙平台权限", "PermissionSys": "local", "# 工单设备二维码跳转地址": "", "DeviceQrcodeUrl": "http://*************:5173/oa/#/process/WorkorderQRCode", "# 票务模板银行对账时需要排除的线路": "", "TicketBankCheckExcludeLine": [], "# 钉钉流程审批配置": "", "DingTalkBpm": {"Enable": false, "AgentId": **********, "AppKey": "dingvqp4hrzt2a4bgsua", "AppSecret": "Fimk9C9BW7KoGreyvZgMIiEqZ9u-mm9AMN7XCzC7pEafhQ0tp8J-pNnK_qwI-7rn", "AesKey": "In7fG6lw0OxvYmWvQIuzjb6YrGwV6I3aUzSTC6loDs7", "Token": "hzlcoet12138", "DingTalkBpmProcessCode": {"# 员工离职申请流程模板": "", "staff_quit_apply_process": "", "# 物料申领申请流程模板": "", "material_requisition_apply_process": "PROC-F6F6BE1F-B57C-4D1B-8863-E7F7DD4A9CC5"}}, "# 钉钉告警通知配置": "", "DingTalkAlarm": {"Enable": false, "Url": "https://oapi.dingtalk.com/robot/send?access_token=45159950d794ab1e4ebb6b6712fceeb870c71dc7381b363537c592aa25eceff9"}, "# 人员档案报表统计的机构ID": "", "StaffArchiveReportCorpId": 1728200467558695982}