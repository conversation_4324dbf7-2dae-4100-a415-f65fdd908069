#!/bin/bash

#export GO111MODULE=off

cd $GOPATH/src

protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/corporation/corporation.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/oetstaff/oetstaff.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/oetvehicle/oetvehicle.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/oetvehiclemodel/oetvehiclemodel.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/user/user.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/permission/permission.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/oetline/oetline.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/tree/tree.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/schedule/schedule.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/schedule/dataform.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/schedule/assess_line_stations.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/mini/mini.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/parking/oetparking.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/station/oetstation.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/erp/erp.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/oetlinestation/oetlinestation.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/mini_station/station.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/doorcheck/doorcheck.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/rpc/oetvehicle/oetvehicledriver.proto

protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/base/base.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/freeCheck/freeCheck.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/dss/dss.proto

protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/file/file.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/hr/hr.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/safety/safety.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/maintenance/maintenance.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/health/health.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/ticket/ticket.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/thirdParty/thirdParty.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/workOrder/workOrder.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/scheduler/scheduler.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/stock/stock.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/setting/setting.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/operation/operation.proto
protoc --proto_path=. --micro_out=. --go_out=:. app/org/scs/erpv2/proto/dashboard/dashboard.proto


#返回当前目录
cd -
#linux编译
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -ldflags '-extldflags "-static"' -o erp-api main.go
#go build -a -ldflags '-extldflags "-static"' -o erp-api main.go
#./erp-api --config=./config_dev.json

#go run main.go --config=./config_dev.json
#scp erp-api dong_meng@192.168.2.222:~/erp

