package log

import (
	"github.com/sirupsen/logrus"
)

var logger *logrus.Logger

func InitLogger(logLevel string) {
	logger = logrus.New()

	level := logrus.ErrorLevel
	switch {
	case logLevel == "debug":
		level = logrus.DebugLevel
	case logLevel == "info":
		level = logrus.InfoLevel
	case logLevel == "error":
		level = logrus.ErrorLevel
	default:
		level = logrus.ErrorLevel
	}
	logger.Formatter = &logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
	}

	logger.SetLevel(level)
}

func NewLog() *logrus.Logger {
	return logger
}

func DebugFields(msg string, fields map[string]interface{}) {
	logger.WithFields(fields).Debug(msg)
}

func PrintFields(msg string, fields map[string]interface{}) {
	logger.WithFields(fields).Info(msg)
}
func ErrorFields(msg string, fields map[string]interface{}) {
	logger.WithFields(fields).Error(msg)
}

func Printf(format string, v ...interface{}) {
	logger.Printf(format, v)
}

func Errorf(format string, v ...interface{}) {
	logger.Errorf(format, v)
}

func Error(v ...interface{}) {
	logger.Errorln(v)
}

func Fatalf(format string, v ...interface{}) {
	logger.Fatalf(format, v)
}

func Fatal(v ...interface{}) {
	logger.Fatalln(v)
}

func Info(v ...interface{}) {
	logger.Infoln(v)
}
