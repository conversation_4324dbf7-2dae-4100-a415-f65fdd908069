package router

import (
	"app/org/scs/erpv2/api/handler/base"
	protoBase "app/org/scs/erpv2/api/proto/base"
	"github.com/micro/go-micro/v2"
)

func RegisterBaseRouter(s micro.Service) {
	//车辆
	_ = protoBase.RegisterVehicleHandler(s.Server(), &base.Vehicle{})

	//线路
	_ = protoBase.RegisterLineHandler(s.Server(), &base.Line{})

	//人员
	_ = protoBase.RegisterStaffHandler(s.Server(), &base.Staff{Client: s.Client()})

	//场站
	_ = protoBase.RegisterParkingHandler(s.Server(), &base.Parking{})

	//站点
	_ = protoBase.RegisterStationHandler(s.Server(), &base.Station{})

	//机构
	_ = protoBase.RegisterCorporationHandler(s.Server(), &base.Corporation{})

}
