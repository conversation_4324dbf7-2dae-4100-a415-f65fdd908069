package router

import (
	"app/org/scs/erpv2/api/handler/operation"
	operationProto "app/org/scs/erpv2/api/proto/operation"
	protoRpc "app/org/scs/erpv2/api/proto/rpc/erp"
	"github.com/micro/go-micro/v2"
)

func RegisterOperationRouter(s micro.Service) {
	//运营管理
	_ = operationProto.RegisterOperationHandler(s.Server(), &operation.OperationHandler{})

	// 运营 RPC
	_ = protoRpc.RegisterErpOperationHandler(s.Server(), &operation.RpcOperation{})
}
