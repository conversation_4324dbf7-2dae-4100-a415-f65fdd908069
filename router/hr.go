package router

import (
	hrHandler "app/org/scs/erpv2/api/handler/hr"
	learnHr "app/org/scs/erpv2/api/handler/learn"
	settingHandler "app/org/scs/erpv2/api/handler/setting"
	protoHr "app/org/scs/erpv2/api/proto/hr"
	protoRpc "app/org/scs/erpv2/api/proto/rpc/erp"
	"github.com/micro/go-micro/v2"
)

func RegisterHrRouter(s micro.Service) {
	//岗位管理
	_ = protoHr.RegisterWorkpostHandler(s.Server(), &hrHandler.WorkPost{})

	//入职管理
	_ = protoHr.RegisterJoincompanyapplyHandler(s.Server(), &hrHandler.JoinCompanyApply{})

	//档案管理
	_ = protoHr.RegisterStaffarchiveHandler(s.Server(), &hrHandler.StaffArchive{})

	//员工调动管理
	_ = protoHr.RegisterStafftransferHandler(s.Server(), &hrHandler.StaffTransfer{})

	//司机调动管理
	_ = protoHr.RegisterDrivermigrationHandler(s.Server(), &hrHandler.DriverMigration{})

	//员工离职管理
	_ = protoHr.RegisterStaffquitHandler(s.Server(), &hrHandler.StaffQuit{})

	//员工退休管理
	_ = protoHr.RegisterStaffretireHandler(s.Server(), &hrHandler.StaffRetire{})

	//员工档案版本
	_ = protoHr.RegisterStaffarchiveversionHandler(s.Server(), &hrHandler.StaffArchiveVersion{})

	//试用期管理
	_ = protoHr.RegisterProbationHandler(s.Server(), &hrHandler.Probation{})

	//劳动合同管理
	_ = protoHr.RegisterLaborcontractHandler(s.Server(), &hrHandler.LaborContract{})

	//社保
	_ = protoHr.RegisterInsuranceHandler(s.Server(), &hrHandler.Insurance{})

	//公积金
	_ = protoHr.RegisterFundHandler(s.Server(), &hrHandler.Fund{})

	//职称申请
	_ = protoHr.RegisterPositionaltitleapplyHandler(s.Server(), &hrHandler.PositionalTitleApply{})

	//人员考核
	_ = protoHr.RegisterStaffassessmentHandler(s.Server(), &hrHandler.StaffAssessment{})

	//在岗培训
	_ = protoHr.RegisterWorktrainHandler(s.Server(), &hrHandler.WorkTrain{})

	// 一人一档
	_ = protoHr.RegisterDriverarchiveHandler(s.Server(), &hrHandler.DriverArchive{})

	// 统计报表
	_ = protoHr.RegisterStaffreportHandler(s.Server(), &hrHandler.StaffReport{})

	// 岗位 RPC
	_ = protoRpc.RegisterErpWorkpostHandler(s.Server(), &hrHandler.RpcWorkPost{})

	// 假勤 RPC
	_ = protoRpc.RegisterErpLeaveHandler(s.Server(), &hrHandler.RpcLeave{})

	// 人资 RPC
	_ = protoRpc.RegisterErpHrHandler(s.Server(), &hrHandler.RpcHr{})

	// 推荐奖励
	_ = protoHr.RegisterRefererrewardHandler(s.Server(), &hrHandler.RefererReward{})

	// 请假
	_ = protoHr.RegisterStaffleaveHandler(s.Server(), &hrHandler.StaffLeave{})

	// 证件照审批
	_ = protoHr.RegisterHeadimgapprovalHandler(s.Server(), &hrHandler.HeadImgApproval{})

	// 工资
	_ = protoHr.RegisterStaffpayrollHandler(s.Server(), &hrHandler.StaffPayrollHandler{})
	_ = protoHr.RegisterStaffpayrollcalcHandler(s.Server(), &hrHandler.StaffPayrollCalculationHandler{})
	// 工资配置
	_ = protoHr.RegisterStaffpayrollconfigHandler(s.Server(), &settingHandler.StaffPayrollConfigHandler{})
	// 学习考试
	_ = protoHr.RegisterLearngroupHandler(s.Server(), &learnHr.LearnGroupHandler{})                   // 学习小组
	_ = protoHr.RegisterTopicHandler(s.Server(), &learnHr.TopicHandler{})                             // 题目管理
	_ = protoHr.RegisterTopicgroupHandler(s.Server(), &learnHr.TopicGroupHandler{})                   // 题目组别管理
	_ = protoHr.RegisterTestpaperHandler(s.Server(), &learnHr.TestPaperHandler{})                     // 试卷管理
	_ = protoHr.RegisterEducationalresourceHandler(s.Server(), &learnHr.EducationalResourceHandler{}) // 学习资料管理
	_ = protoHr.RegisterCourseHandler(s.Server(), &learnHr.CourseHandler{})                           // 课程管理
	_ = protoHr.RegisterLearnexamtaskHandler(s.Server(), &learnHr.LearnExamTaskHandler{})             // 指派记录
	// 投诉管理
	_ = protoHr.RegisterComplaintmanagementHandler(s.Server(), &hrHandler.ComplaintManagementHandler{})
}
