package router

import (
	"app/org/scs/erpv2/api/handler/ticket"
	"app/org/scs/erpv2/api/handler/ticket/countmoney"
	"app/org/scs/erpv2/api/handler/ticket/countticket"
	"app/org/scs/erpv2/api/handler/ticket/report"
	protoTicket "app/org/scs/erpv2/api/proto/ticket"
	"github.com/micro/go-micro/v2"
)

func RegisterTicketRouter(s micro.Service) {
	// 点钞
	_ = protoTicket.RegisterCountmoneyHandler(s.Server(), &countmoney.CountMoney{Client: s.Client()})

	// 点票
	_ = protoTicket.RegisterCountticketHandler(s.Server(), &countticket.CountTicket{Client: s.Client()})

	// 报表
	_ = protoTicket.RegisterReportHandler(s.Server(), &report.Report{Client: s.Client()})

	// 票务数据权限
	_ = protoTicket.RegisterTicketdatapermissionHandler(s.Server(), &ticket.TicketDataPermission{})

	// 银行校对差额
	_ = protoTicket.RegisterTicketbankcheckHandler(s.Server(), &ticket.TicketBankCheck{})
}
