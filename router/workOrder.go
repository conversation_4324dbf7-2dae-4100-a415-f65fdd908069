package router

import (
	"app/org/scs/erpv2/api/handler/workOrder"
	erpProto "app/org/scs/erpv2/api/proto/rpc/erp"
	protoWorkOrder "app/org/scs/erpv2/api/proto/workOrder"
	"github.com/micro/go-micro/v2"
)

func RegisterWorkOrderRouter(s micro.Service) {

	// 设备
	_ = protoWorkOrder.RegisterDeviceHandler(s.Server(), &workOrder.Device{})

	// 工单
	_ = protoWorkOrder.RegisterWorkorderHandler(s.Server(), &workOrder.WorkOrder{})

	//子设备
	_ = protoWorkOrder.RegisterChilddeviceHandler(s.Server(), &workOrder.ChildDevice{})

	//工单对外RPC
	_ = erpProto.RegisterErpWorkOrderHandler(s.Server(), &workOrder.RpcWorkOrder{})
}
