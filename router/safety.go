package router

import (
	safetyHandler "app/org/scs/erpv2/api/handler/safety"
	protoRpc "app/org/scs/erpv2/api/proto/rpc/erp"
	protoSafety "app/org/scs/erpv2/api/proto/safety"
	"github.com/micro/go-micro/v2"
)

func RegisterSafetyRouter(s micro.Service) {

	// 门检
	_ = protoSafety.RegisterVehiclecheckHandler(s.Server(), &safetyHandler.VehicleCheckHandler{})

	// 安全
	_ = protoSafety.RegisterQualityassessmentstandardHandler(s.Server(), &safetyHandler.QualityAssessmentStandard{})

	//违规违法
	_ = protoSafety.RegisterTrafficviolationHandler(s.Server(), &safetyHandler.TrafficViolation{})

	//重点帮扶
	_ = protoSafety.RegisterEmphasishelpdriverHandler(s.Server(), &safetyHandler.EmphasisHelpDriver{})

	//整改
	_ = protoSafety.RegisterViolationrectificationHandler(s.Server(), &safetyHandler.TrafficViolationRectification{})

	//事故
	_ = protoSafety.RegisterTrafficaccidentHandler(s.Server(), &safetyHandler.TrafficAccident{})

	//车险
	_ = protoSafety.RegisterVehicleinsuranceHandler(s.Server(), &safetyHandler.VehicleInsurance{})

	// 安全 RPC
	_ = protoRpc.RegisterErpSafetyHandler(s.Server(), &safetyHandler.RpcSafety{})

}
