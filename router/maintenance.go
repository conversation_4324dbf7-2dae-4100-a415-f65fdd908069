package router

import (
	"app/org/scs/erpv2/api/handler/maintenance"
	protoMaintenance "app/org/scs/erpv2/api/proto/maintenance"
	protoRpc "app/org/scs/erpv2/api/proto/rpc/erp"
	"github.com/micro/go-micro/v2"
)

func RegisterMaintenanceRouter(s micro.Service) {
	//玻璃维修
	_ = protoMaintenance.RegisterGlassrepairHandler(s.Server(), &maintenance.GlassRepair{})

	//车辆调动
	_ = protoMaintenance.RegisterVehicletransferHandler(s.Server(), &maintenance.VehicleMigration{})

	//车辆管理
	_ = protoMaintenance.RegisterVehiclemanageHandler(s.Server(), &maintenance.VehicleManage{})

	// 车辆调动 RPC
	_ = protoRpc.RegisterErpMaintenanceHandler(s.Server(), &maintenance.RpcMaintenance{})

	// 节油奖
	_ = protoMaintenance.RegisterFuelsavingawardHandler(s.Server(), &maintenance.FuelSavingAwardHandler{})
}
