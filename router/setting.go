package router

import (
	settingHandler "app/org/scs/erpv2/api/handler/setting"
	accidentSettingHandler "app/org/scs/erpv2/api/handler/setting/accident"
	protoSetting "app/org/scs/erpv2/api/proto/setting"
	"github.com/micro/go-micro/v2"
)

func RegisterSettingRouter(s micro.Service) {
	//事故报警设置
	_ = protoSetting.RegisterAccidentsettingHandler(s.Server(), &accidentSettingHandler.AccidentSetting{})

	//报警记录
	_ = protoSetting.RegisterAlarmrecordHandler(s.Server(), &settingHandler.AlarmRecord{})

	//列表设置
	_ = protoSetting.RegisterListsettingHandler(s.Server(), &settingHandler.ListSetting{})

	//厂家设置
	_ = protoSetting.RegisterFactorysettingHandler(s.Server(), &settingHandler.DeviceFactory{})

	//线路补贴设置
	_ = protoSetting.RegisterLineallowancesettingHandler(s.Server(), &settingHandler.LineAllowancePrice{})

	//全局设置
	_ = protoSetting.RegisterGlobalsettingHandler(s.Server(), &settingHandler.GlobalSetting{})

	//学习考试分类设置
	_ = protoSetting.RegisterTopiccategoryHandler(s.Server(), &settingHandler.TopicCategoryHandler{})
}
