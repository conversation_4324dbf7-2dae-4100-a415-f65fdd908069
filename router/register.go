package router

import (
	"app/org/scs/erpv2/api/handler/file"
	"app/org/scs/erpv2/api/handler/freeCheck"
	health "app/org/scs/erpv2/api/handler/health"
	schedulerHandler "app/org/scs/erpv2/api/handler/scheduler"
	"app/org/scs/erpv2/api/handler/thirdParty"
	protoFile "app/org/scs/erpv2/api/proto/file"
	protoFreeCheck "app/org/scs/erpv2/api/proto/freeCheck"
	protoHealth "app/org/scs/erpv2/api/proto/health"
	protoScheduler "app/org/scs/erpv2/api/proto/scheduler"
	protoThirdParty "app/org/scs/erpv2/api/proto/thirdParty"
	"github.com/micro/go-micro/v2"
)

func RegisterRouter(s micro.Service) {

	//健康检查
	_ = protoHealth.RegisterHealthHandler(s.Server(), &health.Health{Client: s.Client()})

	//第三方调用的接口
	_ = protoThirdParty.RegisterThirdpartyHandler(s.Server(), &thirdParty.ThirdParty{})

	// 分片上传
	_ = protoFile.RegisterFileHandler(s.Server(), &file.File{})

	//免权限校验的接口
	_ = protoFreeCheck.RegisterFreecheckHandler(s.Server(), &freeCheck.FreeCheck{})

	//任务调度
	_ = protoScheduler.RegisterSchedulerHandler(s.Server(), &schedulerHandler.Scheduler{})

	// 人资
	RegisterHrRouter(s)

	// 安全
	RegisterSafetyRouter(s)

	// 票务
	RegisterTicketRouter(s)

	// 基础数据查询 车、线路
	RegisterBaseRouter(s)

	// 工单
	RegisterWorkOrderRouter(s)

	// 库存管理
	RegisterStockRouter(s)

	// 设置中心
	RegisterSettingRouter(s)

	RegisterDssRouter(s)

	//机务
	RegisterMaintenanceRouter(s)

	//运营管理
	RegisterOperationRouter(s)

	//后台管理
	RegisterDashboardRouter(s)

	// 公共表
	RegisterCommonRouter(s)
}
