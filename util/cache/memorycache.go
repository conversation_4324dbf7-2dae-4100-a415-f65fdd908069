package cache

import (
	"sync"
	"time"
)

// CacheItem 缓存对象
type CacheItem struct {
	Value     interface{}   // 实际缓存的对象
	TTL       time.Duration // 存活时间
	CreatedAt time.Time     // 创建时间，和 TTL 一起决定是否过期
}

// Expired 缓存是否过期
func (c *CacheItem) Expired() bool {
	return time.Now().Sub(c.CreatedAt) > c.TTL
}

// LocalCache 本地缓存实现类
type LocalCache struct {
	sync.RWMutex                       // 继承读写锁，用于并发控制
	Items        map[string]*CacheItem // K-V存储
	GCDuration   time.Duration         // 惰性删除， 后台运行时间间隔
}

// NewLocalCache 新建本地缓存
func NewLocalCache(gcDuration time.Duration) *LocalCache {
	localCache := &LocalCache{Items: map[string]*CacheItem{}, GCDuration: gcDuration}

	// 启动协程，定期扫描过期键，进行删除
	go localCache.GC()

	return localCache
}

// Set 存入对象
func (lc *LocalCache) Set(key string, value interface{}, ttl time.Duration) {
	lc.Lock()
	defer lc.Unlock()

	lc.Items[key] = &CacheItem{
		Value:     value,
		TTL:       ttl,
		CreatedAt: time.Now(),
	}
}

// Get 查询对象 key不存在或过期返回: nil
func (lc *LocalCache) Get(key string) interface{} {
	lc.RLock()
	defer lc.RUnlock()

	if item, ok := lc.Items[key]; ok {
		if !item.Expired() {
			return item
		} else {
			// 键已过期, 直接删除
			// 需要注意的是，这里不能调用lc.Del()方法，因为go的读写锁是不支持锁升级的
			delete(lc.Items, key)
		}
	}

	return nil
}

// Del 删除缓存
func (lc *LocalCache) Del(key string) {
	lc.Lock()
	defer lc.Unlock()

	if _, ok := lc.Items[key]; ok {
		delete(lc.Items, key)
	}
}

// GC 异步执行，扫描过期键并删除
func (lc *LocalCache) GC() {
	for {
		select {
		case <-time.After(lc.GCDuration):
			keysToExpire := make([]string, 0)

			lc.RLock()
			for key, item := range lc.Items {
				if item.Expired() {
					keysToExpire = append(keysToExpire, key)
				}
			}
			lc.RUnlock()

			for _, k := range keysToExpire {
				lc.Del(k)
			}
		}
	}
}

var memoryCache *LocalCache

func InitMemoryCache(gcDuration time.Duration) {
	memoryCache = NewLocalCache(gcDuration)
}

func GetMemoryCache() *LocalCache {
	return memoryCache
}
