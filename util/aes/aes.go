package aes

import (
	"crypto/aes"
)

func ECBEncrypt(origData, key []byte) (encrypted []byte) {
	cipherT, _ := aes.NewCipher(generateKey(key))
	length := (len(origData) + aes.BlockSize) / aes.BlockSize
	plain := make([]byte, length*aes.BlockSize)
	copy(plain, origData)
	pad := byte(len(plain) - len(origData))
	for i := len(origData); i < len(plain); i++ {
		plain[i] = pad
	}
	encrypted = make([]byte, len(plain))
	// 分组分块加密
	for bs, be := 0, cipherT.BlockSize(); bs <= len(origData); bs, be = bs+cipherT.BlockSize(), be+cipherT.BlockSize() {
		cipherT.Encrypt(encrypted[bs:be], plain[bs:be])
	}

	return encrypted
}

func ECBDecrypt(encrypted, key []byte) (decrypted []byte) {
	cipherT, _ := aes.NewCipher(generateKey(key))
	decrypted = make([]byte, len(encrypted))
	//
	for bs, be := 0, cipherT.BlockSize(); bs < len(encrypted); bs, be = bs+cipherT.BlockSize(), be+cipherT.BlockSize() {
		cipherT.Decrypt(decrypted[bs:be], encrypted[bs:be])
	}

	trim := 0
	if len(decrypted) > 0 {
		trim = len(decrypted) - int(decrypted[len(decrypted)-1])
	}

	return decrypted[:trim]
}

func generateKey(key []byte) (genKey []byte) {
	genKey = make([]byte, 16)
	copy(genKey, key)
	for i := 16; i < len(key); {
		for j := 0; j < 16 && i < len(key); j, i = j+1, i+1 {
			genKey[j] ^= key[i]
		}
	}
	return genKey
}
