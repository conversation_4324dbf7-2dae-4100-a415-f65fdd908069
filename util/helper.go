package util

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	api "github.com/micro/go-micro/v2/api/proto"
	"github.com/shopspring/decimal"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
	"io/ioutil"
	"math/rand"
	"reflect"
	"strconv"
	"strings"
	"time"
)

func Validator() *validator.Validate {
	return validator.New()
}

func StructAConvertStructB(a, b interface{}) error {
	aByte, err := json.Marshal(a)
	if err != nil {
		return err
	}
	err = json.Unmarshal(aByte, b)
	return err
}

func StructToMapByMarshal(in interface{}) (map[string]interface{}, error) {
	aByte, err := json.Marshal(in)
	if err != nil {
		return nil, err
	}
	var b map[string]interface{}
	err = json.Unmarshal(aByte, &b)
	return b, err
}

func StructToMapByReflect(in interface{}, tagName string) (map[string]interface{}, error) {
	out := make(map[string]interface{})

	v := reflect.ValueOf(in)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct { // 非结构体返回错误提示
		return nil, fmt.Errorf("ToMap only accepts struct or struct pointer; got %T", v)
	}

	t := v.Type()
	// 遍历结构体字段
	// 指定tagName值为map中key;字段值为map中value
	for i := 0; i < v.NumField(); i++ {
		fieldValue := v.Field(i)
		if fieldValue.Kind() == reflect.Ptr {
			fieldValue = fieldValue.Elem()
		}
		if fieldValue.Kind() == reflect.Struct {
			tt := fieldValue.Type()
			for j := 0; j < fieldValue.NumField(); j++ {
				fi := tt.Field(j)
				if tagValue := fi.Tag.Get(tagName); tagValue != "" && tagValue != "-" {
					out[tagValue] = fieldValue.Field(j).Interface()
				}
			}
		} else {
			fi := t.Field(i)
			if tagValue := fi.Tag.Get(tagName); tagValue != "" && tagValue != "-" {
				out[tagValue] = v.Field(i).Interface()
			}
		}
	}
	return out, nil
}

func ReturnStructKey(in interface{}) ([]string, error) {
	inByte, err := json.Marshal(in)
	if err != nil {
		return nil, err
	}

	var m = make(map[string]interface{})
	err = json.Unmarshal(inByte, &m)

	var res []string
	for key := range m {
		res = append(res, key)
	}
	return res, err
}

// RandomString 随机字符串
func RandomString(n int, alphabets ...byte) string {
	const alphaNum = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	var b = make([]byte, n)
	var isRand bool
	if num, err := rand.Read(b); num != n || err != nil {
		rand.Seed(time.Now().UnixNano())
		isRand = true
	}
	for i, v := range b {
		if len(alphabets) == 0 {
			if isRand {
				b[i] = alphaNum[rand.Intn(len(alphaNum))]
			} else {
				b[i] = alphaNum[v%byte(len(alphaNum))]
			}
		} else {
			if isRand {
				b[i] = alphabets[rand.Intn(len(alphabets))]
			} else {
				b[i] = alphabets[v%byte(len(alphabets))]
			}
		}
	}
	return string(b)
}

func GetAge(birthDate time.Time) int64 {
	age := time.Now().Year() - birthDate.Year()
	if age < 0 {
		age = 0
	} else {
		if time.Now().Month() < birthDate.Month() {
			age = age - 1
		} else if time.Now().Month() == birthDate.Month() {
			if time.Now().Day() < birthDate.Day() {
				age = age - 1
			}
		}
	}

	return int64(age)
}

func SendResp(rsp *api.Response, msgId string, errCode string, data map[string]interface{}) error {
	var msg string

	errMsg, ok := ErrorMsgDB[errCode]
	if ok {
		msg = errMsg.Chinese
	} else {
		msg = fmt.Sprintf("error(%v) is unknown", errCode)
	}

	respMsg := map[string]interface{}{
		"Id": msgId,
		"Result": map[string]interface{}{
			"Data": data,
			"Code": errCode,
			"Msg":  msg,
		},
	}

	b, _ := json.Marshal(respMsg)
	rsp.StatusCode = 200
	rsp.Body = string(b)

	return nil
}

func Index(vs []string, t string) int {
	for i, v := range vs {
		if v == t {
			return i
		}
	}
	return -1
}

func IndexInt64(vs []int64, t int64) int {
	for i, v := range vs {
		if v == t {
			return i
		}
	}
	return -1
}

func Include(vs []string, t string) bool {
	return Index(vs, t) >= 0
}

func IncludeInt64(vs []int64, t int64) bool {
	return IndexInt64(vs, t) >= 0
}

func Any(vs []string, f func(string) bool) bool {
	for _, v := range vs {
		if f(v) {
			return true
		}
	}
	return false
}

func All(vs []string, f func(string) bool) bool {
	for _, v := range vs {
		if !f(v) {
			return false
		}
	}
	return true
}

func Filter(vs []string, f func(string) bool) []string {
	vsf := make([]string, 0)
	for _, v := range vs {
		if f(v) {
			vsf = append(vsf, v)
		}
	}
	return vsf
}

func Map(vs []string, f func(string) string) []string {
	vsm := make([]string, len(vs))
	for i, v := range vs {
		vsm[i] = f(v)
	}
	return vsm
}

// IsRepeat 查重
func IsRepeat(vs []string) bool {
	mapTemp := make(map[string]byte, len(vs))
	for _, s := range vs {
		if _, ok := mapTemp[s]; ok {
			return true
		} else {
			mapTemp[s] = 0
		}
	}
	return false
}

// RemoveRepeatByLoop 循环去重 时间换空间
func RemoveRepeatByLoop(vs []string) []string {
	res := []string{}
	for _, s := range vs {
		isRepeat := false // 是否重复
		for _, s2 := range res {
			if s == s2 {
				isRepeat = true
				break
			}
		}
		if isRepeat {
			continue
		} else {
			res = append(res, s)
		}
	}
	return res
}

// RemoveRepeatByMap map去重 空间换时间
func RemoveRepeatByMap(vs []string) []string {
	mapTemp := make(map[string]byte, len(vs))
	res := []string{}
	for _, s := range vs {
		if _, ok := mapTemp[s]; !ok {
			mapTemp[s] = 0
			res = append(res, s)
		}
	}
	return res
}

// RemoveRepeatByMapInt64 map去重 空间换时间
func RemoveRepeatByMapInt64(vs []int64) []int64 {
	mapTemp := make(map[int64]byte)
	res := []int64{}
	for _, s := range vs {
		if _, ok := mapTemp[s]; !ok {
			mapTemp[s] = 0
			res = append(res, s)
		}
	}
	return res
}

// SliceItoA 用于 SQL IN 语句的 []int64 转 string
// example: []int{1,3,5,6} -> "1,3,5,6"
func SliceItoA(v []int64) string {
	var rsp []string

	if len(v) == 0 {
		return ""
	}

	for _, i := range v {
		rsp = append(rsp, strconv.FormatInt(i, 10))
	}

	return strings.Join(rsp, ",")
}

// DiffNatureDays 求两个时间戳(单位 秒)之间的间隔自然天数
func DiffNatureDays(t1, t2 int64) int64 {

	var (
		diffDays     int64 = 1
		SecondsOfDay int64 = 60 * 60 * 24
	)

	if t1 == t2 {
		return diffDays
	}

	if t1 > t2 {
		t1, t2 = t2, t1
	}

	secDiff := t2 - t1
	if secDiff >= SecondsOfDay {
		tmpDays := secDiff / SecondsOfDay // int计算会舍去小数位
		diffDays += tmpDays
	}

	return diffDays
}

// UTF82GBK : transform UTF8 rune into GBK byte array
func UTF82GBK(src string) ([]byte, error) {
	GB18030 := simplifiedchinese.All[0]
	return ioutil.ReadAll(transform.NewReader(bytes.NewReader([]byte(src)), GB18030.NewEncoder()))
}

// GBK2UTF8 : transform  GBK byte array into UTF8 string
func GBK2UTF8(src []byte) (string, error) {
	GB18030 := simplifiedchinese.All[0]
	bts, err := ioutil.ReadAll(transform.NewReader(bytes.NewReader(src), GB18030.NewDecoder()))
	return string(bts), err
}

// SortSwapChineseASC 数字 < 字母大写 < 字母小写 < 汉语拼音 升序
func SortSwapChineseASC(iString, jString string) bool {
	a, _ := UTF82GBK(iString)
	b, _ := UTF82GBK(jString)
	bLen := len(b)
	for idx, chr := range a {
		if idx > bLen-1 {
			return false
		}
		if chr != b[idx] {
			return chr < b[idx]
		}
	}
	return true
}

// ChineseNumberReplace 汉语简写数字 一、二、三 转换 1, 2, 3
func ChineseNumberReplace(s string) string {

	//
	nums := []string{"一", "二", "三", "四", "五", "六"}
	numsReplace := []string{"1", "2", "3", "4", "5", "6"}

	for _, v := range []rune(s) {
		for i, num := range nums {
			if string(v) == num {
				s = strings.ReplaceAll(s, num, numsReplace[i])
			}
		}
	}

	return s
}

// StringSliceToInt64Slice string slice -> int64 slice 不成功补充0 ["1", "a", "2"] -> [1, 0, 2]
func StringSliceToInt64Slice(sli []string) []int64 {
	var rsp []int64
	for _, s := range sli {
		parseInt, _ := strconv.ParseInt(s, 10, 64)
		rsp = append(rsp, parseInt)
	}
	return rsp
}

// GenerateIdentifier 生成编号 20060201999999 时间+6位随机数
func GenerateIdentifier() string {
	now := time.Now()
	r := rand.New(rand.NewSource(now.UnixNano()))
	return fmt.Sprintf(`%s%06v`, now.Format("20060102"), r.Int31n(1000000))
}

func NilSliceForNoStaffId() interface{} {
	return map[string]interface{}{
		"Items":      nil,
		"TotalCount": 0,
	}
}

func SliceIntIndexOf(slice []int64, needle int64) int {
	for i := range slice {
		if needle == slice[i] {
			return i
		}
	}
	return -1
}

type ReportAtPart struct {
	Start time.Time
	End   time.Time
}

func SplitRangeTimeByYear(startAt, endAt time.Time) []ReportAtPart {
	if startAt.Unix() > endAt.Unix() {
		return nil
	}

	var items []ReportAtPart
	for {
		nextAt := time.Date(startAt.Year()+1, 1, 1, 0, 0, 0, 0, time.Local)
		if nextAt.Unix() >= endAt.Unix() {
			items = append(items, ReportAtPart{Start: startAt, End: endAt})
			break
		}

		items = append(items, ReportAtPart{Start: startAt, End: nextAt})

		startAt = nextAt
	}

	return items
}

func GetDateFromRangeTime(startAt, endAt time.Time) []string {
	var ats []string
	for {
		ats = append(ats, startAt.Format("2006-01-02"))
		if startAt.Unix() >= endAt.Unix() {
			break
		}

		startAt = startAt.AddDate(0, 0, 1)
	}

	return ats
}

func IntSliceToStrSlice(s []int64) []string {
	if s == nil {
		return nil
	}
	r := make([]string, len(s))
	for i, v := range s {
		r[i] = strconv.FormatInt(v, 10)
	}

	return r
}

// GetHourRound2BySecond 将秒数转成小时  并保留2位小数之后再转成秒
func GetHourRound2BySecond(sec int64) int64 {
	hour := decimal.NewFromFloat(float64(sec)).Div(decimal.NewFromFloat(3600)).Round(2)

	return hour.Mul(decimal.NewFromFloat(3600)).IntPart()
}

func IDecimal(a, b int64, bit int64) float64 {
	if a%b == 0 {
		return float64(a / b)
	} else {
		var format = "%" + fmt.Sprintf("%v", bit) + "f"
		var v = float64(a) / float64(b)
		value, _ := strconv.ParseFloat(fmt.Sprintf(format, v), 64)
		return value
	}
}

func PaginateResult(limit, offset int, items []interface{}) []interface{} {
	var results []interface{}
	if limit+offset >= len(items) {
		if offset < len(items) {
			results = items[offset:]
		}
	} else {
		results = items[offset:(offset + limit)]
	}

	return results
}

func GetAgeFromIDCard(idCard string) (int, error) {
	if len(idCard) != 18 {
		return 0, fmt.Errorf("身份证号码长度不正确，应为18位")
	}

	// 提取出生日期
	birthday := idCard[6:14]
	year, _ := strconv.Atoi(birthday[0:4])
	month, _ := strconv.Atoi(birthday[4:6])
	day, _ := strconv.Atoi(birthday[6:])

	// 使用time包计算年龄
	birthTime := time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.Local)
	now := time.Now()
	age := now.Year() - birthTime.Year()
	// 如果今年的生日还没过，则年龄减一
	if now.Month() < birthTime.Month() || (now.Month() == birthTime.Month() && now.Day() < birthTime.Day()) {
		age--
	}

	return age, nil
}
