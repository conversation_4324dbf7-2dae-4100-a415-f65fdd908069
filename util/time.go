package util

import (
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"
)

const (
	TimeFormat      = "2006-01-02 15:04:05"
	TimeDateFormat  = "2006-01-02"
	TimeMonthFormat = "2006-01"

	TimeDateFormat_Merge  = "20060102"
	TimeMonthFormat_Merge = "200601"
	TimeYearFormat_Merge  = "2006"
)

const (
	SecondWithDay = 23*60*60 + 59*60 + 59
)

// IsSameDay 是否是同一天
func IsSameDay(t1, t2 time.Time) bool {
	y1, m1, d1 := t1.Date()
	y2, m2, d2 := t2.Date()

	return y1 == y2 && m1 == m2 && d1 == d2
}

func DiffDay(t1, t2 time.Time) int64 {
	y1, m1, d1 := t1.Date()
	y2, m2, d2 := t2.Date()

	s1 := time.Date(y1, m1, d1, 0, 0, 0, 0, time.Local)
	e1 := time.Date(y2, m2, d2, 0, 0, 0, 0, time.Local)

	return int64(math.Ceil(e1.Sub(s1).Hours() / 24))
}

func RangeTimeWithDuration(start, end time.Time, rangeFunc func(s, e time.Time), duration time.Duration) {
	if start.Unix() >= end.Unix() {
		return
	}

	for {
		act_et := start.Add(duration)

		if end.Unix() <= act_et.Unix() {
			rangeFunc(start, end)
			break
		}

		rangeFunc(start, act_et)
		start = start.Add(duration)
	}
}

func RangeTimeWithDataType(start, end time.Time, rangeFunc func(s, e time.Time), tp string) {
	if start.Unix() >= end.Unix() {
		return
	}

	for {
		act_et := nextTime(start, tp)

		if end.Unix() <= act_et.Unix() {
			rangeFunc(start, end)
			break
		}

		rangeFunc(start, act_et)
		start = nextTime(start, tp)
	}
}

func nextTime(t time.Time, tp string) time.Time {
	switch tp {
	case "day":
		return t.Add(24 * time.Hour)
	case "month":
		return time.Date(t.Year(), t.Month()+1, 1, 0, 0, 0, 0, t.Location())
	case "year":
		//start =====>2021.1.1 00:00:00 ----> 2022.1.1 00:00:00 ====> end
		return time.Date(t.Year()+1, 1, 1, 0, 0, 0, 0, t.Location())
	default:
		return time.Now()
	}
}

func NextTime(t time.Time, tp string) time.Time {
	switch tp {
	case "day":
		return t.Add(24 * time.Hour)
	case "month":
		return time.Date(t.Year(), t.Month()+1, 1, 0, 0, 0, 0, t.Location())
	case "year":
		return time.Date(t.Year()+1, 1, 1, 0, 0, 0, 0, t.Location())
	case "week":
		return GetNextWeekDate(t)
	default:
		return time.Now()
	}
}

// Str2Time 时间字符串转化成对象
func Str2Time(formatTimeStr, timeLayout string) time.Time {
	loc, _ := time.LoadLocation("Local")
	theTime, _ := time.ParseInLocation(timeLayout, formatTimeStr, loc) //使用模板在对应时区转化为time.time类型
	return theTime
}

func Str2TimeUnix(formatTimeStr, timeLayout string) int64 {
	return Str2Time(formatTimeStr, timeLayout).Unix()
}

func Str2ZeroTimeUnix(formatTimeStr, timeLayout string) int64 {
	var t = Str2Time(formatTimeStr, timeLayout)
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location()).Unix()
}

func GetFirstDateOfWeek(now time.Time) time.Time {
	offset := int(time.Monday - now.Weekday())
	if offset > 0 {
		offset = -6
	}

	weekStartDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, offset)
	return weekStartDate
}

func GetNextWeekDate(now time.Time) time.Time {
	return GetFirstDateOfWeek(now).AddDate(0, 0, 7)
}

func GetLastDateOfWeek(now time.Time) time.Time {
	return GetFirstDateOfWeek(now).AddDate(0, 0, 6)
}

func GroupDateTime(startAt, endAt time.Time, dateType int64) [][]string {
	fmt.Println("开始时间,结束时间,时间类型:[%v][%v][%v]", startAt, endAt, dateType)

	var dataTypeStr string
	switch dateType {
	case 0:
		dataTypeStr = "day"
	case 1:
		dataTypeStr = "week"
	case 2:
		dataTypeStr = "month"
	case 3:
		dataTypeStr = "year"
	case 4:
		return [][]string{}
	case 10: // 日汇总
		dataTypeStr = "daySum"
	}

	var items [][]string

	var initAt = startAt
	for {
		var (
			item     []string
			newActAt time.Time
		)

		// 日汇总
		if dataTypeStr == "daySum" {
			newActAt = endAt
		} else {
			newActAt = NextTime(initAt, dataTypeStr)
		}
		if newActAt.Unix() >= initAt.Unix() {
			item = append(item, initAt.Format(TimeDateFormat))
		}

		for {
			initAt = initAt.Add(24 * time.Hour)
			if initAt.Unix() > endAt.Unix() {
				break
			}

			if newActAt.Unix() > initAt.Unix() {
				item = append(item, initAt.Format(TimeDateFormat))
			} else {
				break
			}
		}

		items = append(items, item)

		if newActAt.Unix() >= endAt.Unix() {
			break
		}
	}
	return items
}

// GetDateFirstEndTime t 日期的开始时间00:00:00~23:59:59
func GetDateFirstEndTime(t time.Time) (startTime, endTime time.Time) {
	startTime = time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)
	endTime = time.Unix(startTime.Unix()+SecondWithDay, 0)
	return
}

// ConversionTimeStr 21600===>06:00:00
func ConversionTimeStr(second int64) string {

	var currentTime = time.Now()
	var zeroTime = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, currentTime.Location())
	zeroTime = zeroTime.Add(time.Duration(second * 1e9))

	var zeroStr = zeroTime.Format("2006-01-02")
	var zeroSecondStr = zeroTime.Format("2006-01-02 15:04:05")
	return strings.Replace(zeroSecondStr, fmt.Sprintf("%v ", zeroStr), "", 1)
}

// 06:00:00 ===>21600
func ConversionToHMS(str string) (int64, int64, int64) {
	var strArr = strings.Split(str, ":")
	var (
		hour   int64
		minute int64
		second int64
	)
	if len(strArr) == 3 {
		hour, _ = strconv.ParseInt(strArr[0], 10, 64)
		minute, _ = strconv.ParseInt(strArr[1], 10, 64)
		second, _ = strconv.ParseInt(strArr[2], 10, 64)
	}
	if len(strArr) == 2 {
		hour, _ = strconv.ParseInt(strArr[0], 10, 64)
		minute, _ = strconv.ParseInt(strArr[1], 10, 64)
	}
	return hour, minute, second
}

func ConversionToInt64(str string) int64 {
	hour, minute, second := ConversionToHMS(str)
	return hour*60*60 + minute*60 + second
}

// DateStampToStr 时间戳转文字
func DateStampToStr(date int64, strType string) string {
	if date <= 0 {
		return ""
	} else if date < 0 {
		return "-" + time.Unix(date*-1, 0).Format(strType)
	} else {
		return time.Unix(date, 0).Format(strType)
	}
}

// TimeToSecond 时间转秒	仅支持时间格式 HH:mm:ss
func TimeToSecond(Time string) int64 {
	var timeArr = strings.Split(Time, ":")
	var timeArrLen = len(timeArr)
	var timeIntArr []int64
	var second int64
	for _, v := range timeArr {
		var IdInt, _ = strconv.ParseInt(v, 10, 64)
		timeIntArr = append(timeIntArr, IdInt)
	}
	if timeArrLen == 3 {
		second = timeIntArr[0]*3600 + timeIntArr[1]*60 + timeIntArr[2]
	} else {
		second = timeIntArr[0]*3600 + timeIntArr[1]*60
	}
	return second
}

// SecondToTime 秒转时间
func SecondToTime(result int64) string {
	if result < 0 {
		result = -result
		return fmt.Sprintf("-%02v:%02v:%02v", result/3600, result%3600/60, result%3600%60)
	}
	return fmt.Sprintf("%02v:%02v:%02v", result/3600, result%3600/60, result%3600%60)
}

// GetSecondStr 秒转成hh:mm:ss时间格式，不留00:00:00
func GetSecondStr(num int64) string {
	var str = ""
	if num < 0 {
		num = -num
		str = "-"
	}
	if num > 60*60 {
		str += fmt.Sprintf("%02v:%02v:%02v", num/3600, num%3600/60, num%3600%60)
	} else if num > 60 {
		str += fmt.Sprintf("00:%02v:%02v", num%3600/60, num%3600%60)
	} else if num >= 0 {
		str += fmt.Sprintf("00:00:%02v", num%3600%60)
	}
	return str
}

func GetYesterdayAndTodayZeroTime() (time.Time, time.Time) {
	year, month, day := time.Now().Date()
	yesterday := time.Date(year, month, day-1, 0, 0, 0, 0, time.Local)
	today := time.Date(year, month, day, 0, 0, 0, 0, time.Local)
	return yesterday, today
}

func GetTodayAndTomorrowZeroTime() (time.Time, time.Time) {
	year, month, day := time.Now().Date()
	tomorrow := time.Date(year, month, day+1, 0, 0, 0, 0, time.Local)
	today := time.Date(year, month, day, 0, 0, 0, 0, time.Local)
	return today, tomorrow
}

func GetYesterdayAndTodayZeroTimeWithAddDay(cTime time.Time, diffDay int) (time.Time, time.Time) {
	year, month, day := cTime.Date()
	yesterday := time.Date(year, month, day+diffDay-1, 0, 0, 0, 0, time.Local)
	today := time.Date(year, month, day+diffDay, 0, 0, 0, 0, time.Local)
	return yesterday, today
}

func GetTodayAndTomorrowZeroTimeWithAddDay(cTime time.Time, diffDay int) (time.Time, time.Time) {
	year, month, day := cTime.Date()
	tomorrow := time.Date(year, month, day+diffDay+1, 0, 0, 0, 0, time.Local)
	today := time.Date(year, month, day+diffDay, 0, 0, 0, 0, time.Local)
	return today, tomorrow
}

func GetCurrentTimeZeroAndEndTimeWithAddDay(cTime time.Time, diffDay int) (time.Time, time.Time) {
	year, month, day := cTime.Date()

	zeroAt := time.Date(year, month, day+diffDay, 0, 0, 0, 0, time.Local)
	endAt := time.Date(year, month, day+diffDay, 23, 59, 59, 0, time.Local)
	return zeroAt, endAt
}
func GetMinuteStr(num int64) string {
	var str = ""
	if num < 0 {
		num = -num
		str = "-"
	}
	if num > 60*60 {
		str += fmt.Sprintf("%02v:%02v", num/3600, num%3600/60)
	} else {
		str += fmt.Sprintf("00:%02v", num%3600/60)
	}
	return str
}
