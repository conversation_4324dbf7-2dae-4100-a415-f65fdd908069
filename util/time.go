package util

import (
	"fmt"
)

//秒转时间
func SecondToTime(result int64) string {
	if result < 0 {
		result = -result
		return fmt.Sprintf("-%02v:%02v:%02v", result/3600, result%3600/60, result%3600%60)
	}
	return fmt.Sprintf("%02v:%02v:%02v", result/3600, result%3600/60, result%3600%60)
}

//秒转成hh:mm:ss时间格式，不留00:00:00
func GetSecondStr(num int64) string {
	var str = ""
	if num < 0 {
		num = -num
		str = "-"
	}
	if num > 60*60 {
		str += fmt.Sprintf("%02v:%02v:%02v", num/3600, num%3600/60, num%3600%60)
	} else if num > 60 {
		str += fmt.Sprintf("00:%02v:%02v", num%3600/60, num%3600%60)
	} else if num >= 0 {
		str += fmt.Sprintf("00:00:%02v", num%3600%60)
	}
	return str
}

func GetMinuteStr(num int64) string {
	var str = ""
	if num < 0 {
		num = -num
		str = "-"
	}
	if num > 60*60 {
		str += fmt.Sprintf("%02v:%02v", num/3600, num%3600/60)
	} else {
		str += fmt.Sprintf("00:%02v", num%3600/60)
	}
	return str
}
