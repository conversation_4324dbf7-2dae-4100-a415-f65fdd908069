package util

import (
	"encoding/json"
	"math"
	"sync"
)

type Point struct {
	Lng float64 //经度(180°W-180°E) -180 - 0 - 180
	Lat float64 //纬度（90°S-90°N）-90 -0 -90
}

type Area struct {
	Points []Point //多边形坐标 顺时针方向
}

//实例化一个面
func newGISArea(points []Point) *Area {
	area := new(Area)
	area.Points = points
	return area
}

//判断一个点是否在一个面内
//如果点位于多边形的顶点或边上，也算做点在多边形内，直接返回true
//@param point  指定点坐标
func (a *Area) pointInArea(point Point) bool {
	pointNum := len(a.Points) //点个数
	intersectCount := 0       //cross points count of x
	precision := 2e-10        //浮点类型计算时候与0比较时候的容差
	p1 := Point{}             //neighbour bound vertices
	p2 := Point{}
	p := point //测试点
	//log.PrintFields("pointArea=============", map[string]interface{}{"points": a.Points})
	p1 = a.Points[0] //left vertex
	for i := 0; i < pointNum; i++ {
		if p.Lng == p1.Lng && p.Lat == p1.Lat {
			return true
		}
		p2 = a.Points[i%pointNum]
		if p.Lat < math.Min(p1.Lat, p2.Lat) || p.Lat > math.Max(p1.Lat, p2.Lat) {
			p1 = p2
			continue //next ray left point
		}

		if p.Lat > math.Min(p1.Lat, p2.Lat) && p.Lat < math.Max(p1.Lat, p2.Lat) {
			if p.Lng <= math.Max(p1.Lng, p2.Lng) { //x is before of ray
				if p1.Lat == p2.Lat && p.Lng >= math.Min(p1.Lng, p2.Lng) {
					return true
				}

				if p1.Lng == p2.Lng { //ray is vertical
					if p1.Lng == p.Lng { //overlies on a vertical ray
						return true
					} else { //before ray
						intersectCount++
					}
				} else { //cross point on the left side
					xinters := (p.Lat-p1.Lat)*(p2.Lng-p1.Lng)/(p2.Lat-p1.Lat) + p1.Lng
					if math.Abs(p.Lng-xinters) < precision {
						return true
					}

					if p.Lng < xinters { //before ray
						intersectCount++
					}
				}
			}
		} else { //special case when ray is crossing through the vertex
			if p.Lat == p2.Lat && p.Lng <= p2.Lng { //p crossing over p2
				p3 := a.Points[(i+1)%pointNum]
				if p.Lat >= math.Min(p1.Lat, p3.Lat) && p.Lat <= math.Max(p1.Lat, p3.Lat) {
					intersectCount++
				} else {
					intersectCount += 2
				}
			}
		}
		p1 = p2 //next ray left point
	}

	if intersectCount%2 == 0 { //偶数在多边形外
		return false
	} else { //奇数在多边形内
		return true
	}
}

func (a *Area) pointInAreaV2(point Point) bool {
	pointNum := len(a.Points) //点个数
	intersectCount := 0       //cross points count of x
	precision := 2e-10        //浮点类型计算时候与0比较时候的容差
	p1 := Point{}             //neighbour bound vertices
	p2 := Point{}
	p := point //测试点

	// 先确认所有点是不是在最小经纬度与最大经纬度范围内
	var (
		minLng, maxLng, minLat, maxLat float64
	)
	for i := 0; i < pointNum; i++ {
		if i == 0 {
			minLng = a.Points[i].Lng
			maxLng = a.Points[i].Lng
			minLat = a.Points[i].Lat
			maxLat = a.Points[i].Lat
		} else {
			minLng = math.Min(minLng, a.Points[i].Lng)
			maxLng = math.Max(maxLng, a.Points[i].Lng)
			minLat = math.Min(minLat, a.Points[i].Lat)
			maxLat = math.Max(maxLat, a.Points[i].Lat)
		}
	}

	if p.Lat < minLat || p.Lat > maxLat {
		return false
	}

	// 经度必须大于等于
	if p.Lng < minLng || p.Lng > maxLng {
		return false
	}

	//log.PrintFields("pointArea=============", map[string]interface{}{"points": a.Points})
	p1 = a.Points[0] //left vertex
	for i := 0; i < pointNum; i++ {
		if p.Lng == p1.Lng && p.Lat == p1.Lat {
			return true
		}
		p2 = a.Points[i%pointNum]
		if p.Lat < math.Min(p1.Lat, p2.Lat) || p.Lat > math.Max(p1.Lat, p2.Lat) {
			p1 = p2
			continue //next ray left point
		}

		//// 经度必须大于等于
		//if p.Lng < math.Min(p1.Lng, p2.Lng) || p.Lng > math.Max(p1.Lng, p2.Lng) {
		//	p1 = p2
		//	continue //next ray left point
		//}

		if p.Lat > math.Min(p1.Lat, p2.Lat) && p.Lat < math.Max(p1.Lat, p2.Lat) {
			if p.Lng <= math.Max(p1.Lng, p2.Lng) { //x is before of ray
				if p1.Lat == p2.Lat && p.Lng >= math.Min(p1.Lng, p2.Lng) {
					return true
				}

				if p1.Lng == p2.Lng { //ray is vertical
					if p1.Lng == p.Lng { //overlies on a vertical ray
						return true
					} else { //before ray
						intersectCount++
					}
				} else { //cross point on the left side
					xinters := (p.Lat-p1.Lat)*(p2.Lng-p1.Lng)/(p2.Lat-p1.Lat) + p1.Lng
					if math.Abs(p.Lng-xinters) < precision {
						return true
					}

					if p.Lng < xinters { //before ray
						intersectCount++
					}
				}
			}
		} else { //special case when ray is crossing through the vertex
			if p.Lat == p2.Lat && p.Lng <= p2.Lng { //p crossing over p2
				p3 := a.Points[(i+1)%pointNum]
				if p.Lat >= math.Min(p1.Lat, p3.Lat) && p.Lat <= math.Max(p1.Lat, p3.Lat) {
					intersectCount++
				} else {
					intersectCount += 2
				}
			}
		}
		p1 = p2 //next ray left point
	}
	if intersectCount%2 == 0 { //偶数在多边形外
		return false
	} else { //奇数在多边形内
		return true
	}
}

//CheckPointInPolygon 检测坐标点是否在某一多边形区域内
func CheckPointInPolygon(point Point, points []Point) bool {
	area := newGISArea(points)
	rt := area.pointInArea(point)
	return rt
}

var checkPointRepeatMap sync.Map

//CheckPointInPolygon 检测坐标点是否在某一多边形区域内 v2版本
func CheckPointInPolygonV2(point Point, points []Point) bool {
	//points = RemoveRepeatPoint(points)
	area := newGISArea(points)
	rt := area.pointInAreaV2(point)
	return rt
}

func RemoveRepeatPoint(points []Point) (newPoints []Point) {
	if len(points) < 2 {
		return points
	}

	pointStr, _ := json.Marshal(&points)

	value, ok := checkPointRepeatMap.Load(pointStr)
	if ok {
		return value.([]Point)
	}

	newPoints = points
	var (
		newRepeat = false
	)
	for {
		for i := 0; i < len(newPoints); i++ {
			for j := 1; j < len(newPoints); j++ {
				if i != j && newPoints[i].Lat == newPoints[j].Lat && newPoints[i].Lng == newPoints[j].Lng {
					newRepeat = true
					if i+1 >= len(newPoints) {
						newPoints = append(newPoints[:i])
					} else {
						newPoints = append(newPoints[:i], newPoints[i+1:]...)
					}
				}
			}
		}
		if !newRepeat {
			checkPointRepeatMap.Store(pointStr, newPoints)
			return newPoints
		} else {
			newRepeat = false
		}
	}

}

// CheckPointInRound 检测坐标点是否在某一圆形区域内
func CheckPointInRound(targetPoint, centerPoint Point, radius int64) bool {
	//计算目标点到中心点间的距离
	distance := CalcDistance(targetPoint.Lng, targetPoint.Lat, centerPoint.Lng, centerPoint.Lat)

	return int64(distance) < radius
}

/*
   关于经纬度十进制表示法
   对于两个点，在经度相等的情况下：
   纬度每隔0.00001度，距离相差约1.1米；每隔0.0001度，距离相差约11米；每隔0.001度，距离相差约111米；每隔0.01度，距离相差约1113米；每隔0.1度，距离相差约11132米。

   对于两个点，在纬度相等的情况下：
   经度每隔0.00001度，距离相差约1米；每隔0.0001度，距离相差约10米；每隔0.001度，距离相差约100米；每隔0.01度，距离相差约1000米；每隔0.1度，距离相差约10000米。
*/
func CalcDistance(lngA float64, latA float64, lngB float64, latB float64) float64 {
	//https://www.cnblogs.com/softfair/p/distance_of_two_latitude_and_longitude_points.html
	//用haversine公式计算球面两点间的距离。
	//经纬度转换成弧度
	lng1 := ConvertDegrees2Radians(lngA)
	lat1 := ConvertDegrees2Radians(latA)
	lng2 := ConvertDegrees2Radians(lngB)
	lat2 := ConvertDegrees2Radians(latB)

	//差值
	var vLon = math.Abs(lng1 - lng2)
	var vLat = math.Abs(lat1 - lat2)

	//h is the great circle distance in radians, great circle就是一个球体上的切面，它的圆心即是球心的一个周长最大的圆。
	var h = HaverSin(vLat) + math.Cos(lat1)*math.Cos(lat2)*HaverSin(vLon)

	var EARTH_RADIUS float64 = 6371004 //m
	var distance = 2 * EARTH_RADIUS * math.Asin(math.Sqrt(h))

	return distance
}

func HaverSin(theta float64) float64 {
	var v = math.Sin(theta / 2)

	return v * v
}

func ConvertDegrees2Radians(degrees float64) float64 {
	return degrees * 3.1415926 / 180
}
