package util

type ErrorInfo struct {
	ID      string
	English string
	Chinese string
}

var ErrorVar = []ErrorInfo{
	{"0", "OK", "操作成功"},

	{"SN1001", "Sign Error", "签名异常"},
	{"SN1002", "Sign Error", "签名超时"},
	{"SN1003", "Sign Error", "签名参数错误"},

	{"OP1001", "Operate Error", "参数取值不合法"},
	{"OP1004", "Operate Error", "系统错误"},

	{"OP7004", "Operate Error", "用户信息获取错误"},
	{"OP7005", "Operate Error", "车辆信息获取错误"},
	{"OP7006", "Operate Error", "机构信息获取错误"},

	// OP75XX 安全模块错误
	{"OP7501", "Operate Error", "创建失败，门检项目中存在同属性且同名标签"},
	{"OP7502", "Operate Error", "只有司机和管理员能提交门检结果"},
	{"OP7507", "Operate Error", "身份不是司机或者管理员，无法进行门检"},
	{"OP7508", "Operate Error", "身份不是管理员或者干部，无法进行代录入"},
	{"OP7509", "Operate Error", "代录入无法进行抽检"},

	// OP77XX 票务模块错误
	{"OP7700", "Operate Error", "上报日期当天已录入该车辆票/款数据"},
	{"OP7701", "Operate Error", "当前票款状态不可删除"},
	{"OP7702", "Operate Error", "当前票款状态不可修改"},
}

var ErrorMsgDB map[string]ErrorInfo

func init() {
	ErrorCodeInit()
}

func ErrorCodeInit() {
	ErrorMsgDB = make(map[string]ErrorInfo)

	for _, val := range ErrorVar {
		ErrorMsgDB[val.ID] = ErrorInfo{val.ID, val.English, val.Chinese}
	}
}
