package util

import (
	"crypto/rand"
	"math/big"
	"strconv"
)

func GenerateRandomString(length int) string {
	// 36进制，正好是 [0-9]+[a-z] 的长度
	const base = 36

	// 用于每次读一个新的随机数出来
	size := big.NewInt(base)

	n := make([]byte, length)
	for i, _ := range n {
		c, _ := rand.Int(rand.Reader, size)
		// 把小于等于36的数字 按照36进制恰好可以转换成数字或者a-z的字符
		str := strconv.FormatInt(c.Int64(), base)
		n[i] = str[0]
	}
	return string(n)
}
