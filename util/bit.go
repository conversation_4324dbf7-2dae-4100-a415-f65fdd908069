package util

// tm 所有项列表
// vs 传入值列表
func GetIntWithIntsBits(vs []int64, tms []int64) int64 {

	var num int64

	for _, vv := range vs {
		if IncludeInt64(tms, vv) {
			num += vv
		}
	}
	return num
}

// 二进制位运算
func GetIntArrWithBits(v int64, values []int64) []int64 {
	var vs = make([]int64, 0)

	for _, vv := range values {
		if isExistBit(v, vv) {
			vs = append(vs, vv)
		}
	}
	return vs
}

func isExistBit(v int64, vv int64) bool {
	if v&vv == vv {
		return true
	}
	return false
}
