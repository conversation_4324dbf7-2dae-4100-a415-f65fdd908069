package util

const (
	UserTypeForSingleCorporation   = 1 //单机构用户
	UserTypeForMultipleCorporation = 2 //多机构用户
	UserTypeForAdmin               = 3 //admin
)

const (
	StatusForTrue  = 1 //是、真、通过
	StatusForFalse = 2 //否、假、拒绝
)

const (
	Male   = 1 //男
	Female = 2 //女
)

const (
	IsNowJob    = 1 //现任职务
	NotIsNowJob = 2 //历任职务
)

const (
	PositionTypeMain    = 1 //主职
	PositionTypeNotMain = 2 //兼职、副职
)

// 流程实例的状态
const (
	ProcessStatusForDraft     = 0 //流程是草稿状态
	ProcessStatusForDoing     = 1 //流程是进行中状态
	ProcessStatusForDone      = 2 //流程是完成状态
	ProcessStatusForRefuse    = 3 //流程是驳回、拒绝 (在LBPM中，代表流程被驳回到起草节点，需起草人重新发起流程；在钉钉OA中，代表流程是拒绝状态，并且流程已结束)
	ProcessStatusForTerminate = 4 //流程是撤回
	ProcessStatusForAbandon   = 5 //流程是废弃
)

// 流程实例的结果（钉钉OA）
const (
	ProcessResultForPass     = 1 //同意
	ProcessResultForRefuse   = 2 //拒绝（钉钉OA）
	ProcessResultForRedirect = 3 // 转发
)

// 业务审批状态（对应流程实例状态）
const (
	ApplyStatusForNone      = 0 //还未发起审批
	ApplyStatusForDoing     = 1 //审批中
	ApplyStatusForDone      = 2 //审批完成
	ApplyStatusForReject    = 3 //审批被驳回（lbpm中流程被驳回到起草节点）
	ApplyStatusForTerminate = 4 //审批人撤回
	ApplyStatusForAbandon   = 5 //审批被废弃
)

// 流程实例的操作类型
const (
	ProcessActionTypeForApproval  = 1 //审批流程
	ProcessActionTypeForTerminate = 2 //起草人撤回流程
	ProcessActionTypeForAbandon   = 3 //起草人废弃流程
	ProcessActionTypeForRestart   = 4 //起草人重新发起流程
)

// 流程节点审批类型
const (
	ProcessNodeApprovalTypeForSerial      = 1 //串行审批
	ProcessNodeApprovalTypeForConcurrence = 2 //并行审批
)

// 流程节点状态
const (
	ProcessNodeHandleStatusForDoing  = 1 //节点待审批
	ProcessNodeHandleStatusForDone   = 2 //节点审批完成
	ProcessNodeHandleStatusForOver   = 3 //跳过审批（当节点审批是并行，并且有一个人审批通过，其他人则不需要审批，直接跳过）
	ProcessNodeHandleStatusForCancel = 4 //取消审批（当流程被撤回或者废弃，未审批的审批人改为取消审批）
)

// 流程节点处理结果
const (
	ProcessNodeHandleResultForPass     = "handler_pass"     //同意
	ProcessNodeHandleResultForRefuse   = "handler_refuse"   //驳回（lbpm）、拒绝（钉钉）
	ProcessNodeHandleResultForRedirect = "handler_redirect" //转交（钉钉）
)

// 流程节点的标识(lbpm)
const (
	ProcessNodeNameForStart  = "N1" //开始节点的名字
	ProcessNodeNameForCreate = "N2" //起草节点的名字
	ProcessNodeNameForEnd    = "N3" //结束节点的名字
)

// 流程节点类型
const (
	ProcessNodeTypeForApprove   = 1 //审批节点
	ProcessNodeTypeForNotice    = 2 //抄送节点
	ProcessNodeTypeForCreator   = 3 //发起人节点
	ProcessNodeTypeForReCreator = 4 //重新发起人节点（流程被驳回到发起节点，需重新发起）

	ProcessMessageTypeForApprove = 1 //审批消息
	ProcessMessageTypeForNotice  = 2 //抄送消息
)

// 事故结案状态
const (
	//AccidentClosedStatusForDefault = 1 //事故未结案
	//AccidentClosedStatusForDoing   = 2 //事故结案申请处理中
	//AccidentClosedStatusForDone    = 3 //事故已结案

	AccidentOpenStatusForOpen   = 1 //公开
	AccidentOpenStatusForClosed = 2 //不公开
	AccidentOpenStatusForDraft  = 3 //草稿

	AccidentSceneForDraft = 1 //草稿（包含事故草稿、事故提交流程被撤回、事故提交流程被驳回）-> （未提交但已保存的事故）
	//AccidentSceneForSubmitting   = 2 //上报中（事故提交流程正在审批中）
	//AccidentSceneForAbandoned    = 3 //已废弃（事故提交流程被废弃）
	//AccidentSceneForEditing      = 4 //变更中（事故变更申请审批中）
	AccidentSceneForBranchClosed = 5 //分支已结案（事故有任意一个分支已经结案）->  分支结案 （所有提交成功的事故）
	AccidentSceneForDontClose    = 6 //未结案（事故提交流程审批通过，事故结案流程未发起之前）
	AccidentSceneForClosing      = 7 //结案申请中（事故结案流程发起之后，未通过之前）-> 事故结案申请中（所有分支完成结案后，允许事故结案）
	AccidentSceneForClosed       = 8 //已结案（事故已结案）-> （事故结案流程审批通过，但是没有打印流程）
	AccidentSceneForPrintClosed  = 9 //已打印结案（事故打印被点击后展示）

	AccidentHurtStatus_1 = 1 // 1-无车损无人伤
	AccidentHurtStatus_2 = 2 //2-无车损有人伤,
	AccidentHurtStatus_3 = 3 //3-有车损无人伤
	AccidentHurtStatus_4 = 4 //4-有车损有人伤

	AccidentLiabilityTypeForUnknown = 0 //未知
	AccidentLiabilityTypeForAll     = 1 //全责
	AccidentLiabilityTypeForMain    = 2 //主责
	AccidentLiabilityTypeForEqual   = 3 //同责
	AccidentLiabilityTypeForSub     = 4 //次责
	AccidentLiabilityTypeForNull    = 5 //无责
	AccidentLiabilityTypeForPending = 6 //待定

)

const (
	AccidentBranchTypeForSideVehicleBroken = 1 //对方车损
	AccidentBranchTypeForSidePeopleHurt    = 2 //对方人伤
	AccidentBranchTypeForSelfVehicleBroken = 3 //己方车损
	AccidentBranchTypeForSelfPeopleHurt    = 4 //己方人伤

	AccidentPeopleHurtCateForInner = 2 //人上类型-车内伤
)

// 工作状态 1-在职,2-离职,3-试用期,4-退休,5-退休返聘
const (
	JobStatusWorking    = 1
	JobStatusQuit       = 2
	JobStatusProbation  = 3
	JobStatusRetire     = 4
	JobStatusRetireWork = 5

	RelieveContractTypeForRefuseBecomeWorker = 5 //解除劳动合同类型-不予转正
)

// 退休状态
const (
	RetireStatusForDoing   = 1 //退休审批 （退休时间已到 但未确认）
	RetireStatusForDone    = 2 //退休
	RetireStatusForWorking = 3 //退休返聘
)

// 退休类型
const (
	RetireTypeForNow     = 1 //立即退休
	RetireTypeForDelay   = 2 //延迟退休
	RetireStatusForEarly = 3 //提前退休
)

// 转正状态
const (
	BecomeWorkerCheckStatusForUnknown = 0 //未知
	BecomeWorkerCheckStatusForDone    = 1 //正常转正
	BecomeWorkerCheckStatusForDelay   = 2 //延迟转正
	BecomeWorkerCheckStatusForRefuse  = 3 //不予转正（辞退）
)

const (
	IsReversionSoldier    = 1
	NotIsReversionSoldier = 2
)
const (
	SceneForInsurance = 1
	SceneForFund      = 2
)

const (
	ProcessFormStepStart      = 1   //第一步
	ProcessFormStepTwo        = 2   //第二步
	ProcessFormStepThree      = 3   //第三步
	ProcessFormStepFour       = 4   //第四步
	ProcessFormStepFive       = 5   //第五步
	ProcessFormStepSix        = 6   //第六步
	ProcessFormStepSeven      = 7   //第七步
	ProcessFormStepEight      = 8   //第八步
	ProcessFormStepNine       = 9   //第九步
	ProcessFormStepTen        = 10  //第10步
	ProcessFormStepEleven     = 11  //第11步
	ProcessFormStepForCashier = 100 //出纳环节
)

const (
	TrafficViolationStatusForNotSend  = 0 //未下发
	TrafficViolationStatusForSend     = 1 //已下发
	TrafficViolationStatusForChecking = 2 //待审核
	TrafficViolationStatusForRefuse   = 3 //驳回
	TrafficViolationStatusForChecked  = 4 //已审核

	TrafficViolationCateAttrForSafe    = 1 //安全类
	TrafficViolationCateAttrForService = 2 //服务类

	TrafficViolationOriginForThirdAssess = 4 //三方测评

	RectificationHandleStatusForNotReply = 0 //未回复
	RectificationHandleStatusForChecking = 1 //待审批
	RectificationHandleStatusForRefuse   = 2 //驳回
	RectificationHandleStatusForChecked  = 3 //审批通过

	QualityAssessmentStandardsAttrTypeForSafe      = 1 //安全
	QualityAssessmentStandardsAttrTypeForNotSafe   = 2 //非安全
	QualityAssessmentStandardsAttrTypeForViolation = 3 //交通违章

	QualityAssessmentCateAttrForSafeAction = 1 //安全行为
	QualityAssessmentCateAttrForSpecial    = 2 //专项考核
	QualityAssessmentCateAttrForService    = 3 //服务质量

	ViolationAssessCheckTypeForFixed   = 1 //固定金额
	ViolationAssessCheckTypeForPercent = 2 //比例

	RegularLine = 1 //常规线路
	CctLine     = 2 //村村通线路

	WaitWorkAssessFromService = 1 //待岗考核记录来源 1服务质量考核

	//考核分类的类别等级 1=>Ⅰ类  2=>Ⅱ类 .....
	ViolationCategoryLevelTypeFor1 = 1
	ViolationCategoryLevelTypeFor2 = 2
	ViolationCategoryLevelTypeFor3 = 3
	ViolationCategoryLevelTypeFor4 = 4
	ViolationCategoryLevelTypeFor5 = 5
)

const (
	TypeForPayRadio    = 1 //按比例缴纳
	TypeForPayFixMoney = 2 //固定金额缴纳
)

const (
	ModularForEndowment    = "endowment"    //养老
	ModularForMedical      = "medical"      //医疗
	ModularForUnemployment = "unemployment" //失业
	ModularForInjury       = "injury"       //工伤
	ModularForBirth        = "birth"        //工伤
	ModularForAnnuity      = "annuity"      //年金
	ModularForExtraMedical = "extraMedical" //补充医疗
	ModularForFund         = "fund"         //公积金
)

const (
	LaborContractDefault    = 0 //默认值
	LaborContractValid      = 1 //生效
	LaborContractExpiration = 2 //到期
	LaborContractRelieve    = 3 //解除
)

const (
	OptionCreate = "create" // 创建
	OptionEdit   = "edit"   // 编辑
	OptionDelete = "delete" // 删除
)

// 事故流程报警种类的类型
const (
	AlarmCategoryTypeFormProcess  = 1 //流程报警
	AlarmCategoryTypeFormAccident = 2 //事故报警
)

const (
	TrafficAccidentCateForOne   = 1 //车内伤
	TrafficAccidentCateForTwo   = 2 //车外伤
	TrafficAccidentCateForThree = 3 //单方事故
	TrafficAccidentCateForFour  = 4 //倒车
	TrafficAccidentCateForFive  = 5 //刮擦
	TrafficAccidentCateForSix   = 6 //追尾
	TrafficAccidentCateForSeven = 7 //侧翻
	TrafficAccidentCateForEight = 8 //其他
	TrafficAccidentCateForNine  = 9 //玻璃自爆与破损

	TrafficAccidentCheckHandleResultForLess = 1 //减轻处罚
	TrafficAccidentCheckHandleResultForNot  = 2 //免于处罚
)

const (
	AlarmTypeForWarning int64 = 1 //预警
	AlarmTypeForAlarm   int64 = 2 //告警

	AlarmFinishStatusForDoing int64 = 1 //告警完成状态 1进行中
	AlarmFinishStatusForDone  int64 = 2 //告警完成状态  2完成
)

// 库存出库方式
const (
	StockProcessMode_ManualOperation = 1 // 手动操作
	StockProcessMode_DingTalk        = 2 // 钉钉审批
	StockProcessMode_Lbpm            = 3 // 蓝凌审批
)

const (
	DingDingFormNameLabel_ActualNumber = "实领数量" // 钉钉库存出库表单标签
)
const (
	MileageReportForFullWork     = 1 //全班
	MileageReportForHalfWork     = 2 //半班
	MileageReportForMotorWork    = 3 //小机动
	MileageReportForBigMotorWork = 4 //大机动
)

const (
	LineTypeUnknown      int64 = 0
	LineTypeDoubleStd    int64 = 1 //标准双向
	LineTypeSingleCircle int64 = 2 //单向环行线路
	LineTypeDoubleCircle int64 = 3 //双向环行线路
	LineTypeSingleRand   int64 = 4 //机动型, 景区单向随机(每个站点都是首末站)
)
const (
	LeaveTypeForAnnualLeave      = 1  //年休假
	LeaveTypeForChangeLeave      = 2  //调休假
	LeaveTypeForCasualLeave      = 3  //事假
	LeaveTypeForSickLeave        = 4  //病假
	LeaveTypeForMarriageLeave    = 5  //婚假
	LeaveTypeForMaternityLeave   = 6  //产假
	LeaveTypeForPaternityLeave   = 7  //陪产假
	LeaveTypeForBereavementLeave = 8  //丧假
	LeaveTypeForRestLeave        = 9  //疗休养
	LeaveTypeForWaitWork         = 12 //待岗
	LeaveTypeForStopWork         = 13 //停岗

	LeaveDateTypeLinkDay  = 1 //连续日期
	LeaveDateTypeMultiDay = 2 //多日不连续
)

const (
	LineSheetForUp     int64 = 1 //上行
	LineSheetForDown   int64 = 2 //下行
	LineSheetForCircle int64 = 3 //环形
)

const (
	DictTypeForDeviceClass = 13 //设备大类
	DictTypeForDeviceCate  = 14 //设备种类

	DeviceAssociationObjectForVehicle = 1 // 车
	DeviceAssociationObjectForParking = 2 // 场站
	DeviceAssociationObjectForStation = 3 // 站点
)

const (
	DevicePresetForLiable     = 1 // 责任人
	DevicePresetForBrand      = 2 // 品牌方
	DevicePresetForMaintainer = 3 // 过保维修方
	DevicePresetForSupplier   = 4 // 供货方
	DevicePresetForHandler    = 5 // 保修方

	DeviceFactoryUserSceneTypeForMain     = 1 //负责人
	DeviceFactoryUserSceneTypeForHandler  = 2 //保修方
	DeviceFactoryUserSceneTypeForRepairer = 3 //过保维修方
)

const (
	MaintenanceTypeForRepair             = 1 // 维修
	MaintenanceTypeForReplace            = 2 // 更换
	MaintenanceTypeForRemove             = 3 // 移除
	MaintenanceTypeForReplaceChildDevice = 4 // 更换子设备
)

const (
	CorporationTypeForGroup      = 1
	CorporationTypeForCompany    = 2
	CorporationTypeForBranch     = 3
	CorporationTypeForDepartment = 4
	CorporationTypeForFleet      = 5
)
const (
	JoinCompanyWay_13 = 3 //派遣

	WorkPostType_1 = 1 //普通管理员,
	WorkPostType_2 = 2 //干部
	WorkPostType_3 = 3 //驾驶员
	WorkPostType_4 = 4 //乘务员
	WorkPostType_5 = 5 //安保人员
	WorkPostType_6 = 6 //辅工
	WorkPostType_7 = 7 //仓管人员
	WorkPostType_8 = 8 //其他
	WorkPostType_9 = 9 //修理工
)

const (
	WorkOrderRepairMethodForNone  = 0 //未知
	WorkOrderRepairMethodForFleet = 1 //车队查看
	WorkOrderRepairMethodForFix   = 2 //直接维修

	WorkOrderHandleStatusForMeWaiting = 1 //等待我处理
	WorkOrderHandleStatusForMeDone    = 2 //我已处理 流程未结束
	WorkOrderHandleStatusForMeOver    = 3 //我已处理 流程已结束
)

const (
	VehicleTransferPlanPublishStatusForDraft    = -1 //-1草稿
	VehicleTransferPlanPublishStatusForDoing    = 1  //1待发布
	VehicleTransferPlanPublishStatusForApplying = 2  //2审批中
	VehicleTransferPlanPublishStatusForDone     = 3  //3已发布
	VehicleTransferPlanPublishStatusForDrawback = 4  //4已退回

	VehicleTransferTypeForPlan     = 1 //执行方案
	VehicleTransferTypeForBackPlan = 2 //回退方案
	VehicleTransferTypeForFleet    = 3 //车队调动

	VehicleStatusForNormal         = 1 //车辆运营
	VehicleStatusForScrap          = 2 //车辆报废
	VehicleStatusForShouldScrap    = 3 //待报废
	VehicleStatusForTransfer       = 4 //待转让
	VehicleStatusForShouldTransfer = 5 //已转让
	VehicleStatusForOutage         = 6 //停运
	VehicleStatusForUnknown        = 7 //车辆未知
)

const (
	GlobalSettingTypeForDriverDevoteCalcDayDot  = 5  //司机贡献考核设置的计算时间点配置
	GlobalSettingTypeForAccidentAssess          = 12 //月事故考核
	GlobalSettingTypeForSafeYearAssess          = 13 //安全年考核
	GlobalSettingTypeForSafeMileage             = 14 //安全公里考核
	GlobalSettingTypeForSafeContribution        = 15 //安全贡献考核
	GlobalSettingTypeForServiceYearAssess       = 16 //服务年考核
	GlobalSettingTypeForServiceContribution     = 17 //服务贡献考核
	GlobalSettingTypeForServiceThirdPartyAssess = 18 //三方测评年考核
	GlobalSettingTypeForExtraCalcDayCount       = 19 //额外纳入运营天数的类型配置
)

// station queue: first, middle, last, sample
const (
	Station_Queue_Middle = 0 // 0-中途站
	Station_Queue_First  = 1 // 1-起点站
	Station_Queue_Last   = 2 // 2-终点站
	Station_Queue_Sample = 3 // 3-普通采样点
)
const (
	ExportFileStatusForDoing = 1 // 导出中
	ExportFileStatusForDone  = 2 // 导出成功
	ExportFileStatusForFail  = 3 // 导出失败
)

const (
	VehicleModelForMiddle = 1 //中巴
	VehicleModelForBig    = 2 //大客
)

const (
	Safe_Production_Report_Draft    = 0 // 0-草稿
	Safe_Production_Report_Approval = 1 // 1-审批中
	Safe_Production_Report_Complet  = 2 // 2-完成
	Safe_Production_Report_Reject   = 3 // 3-驳回
	Safe_Production_Report_Revoke   = 4 // 4-撤回
	Safe_Production_Report_Scrap    = 5 // 5-废弃
)

var JobStatusMap = map[string]int64{"在职": 1, "离职": 2, "试用期": 3, "退休": 4, "退休返聘": 5}
var GenderMap = map[string]int64{"男": 1, "女": 2}
var ResidenceAttrMap = map[string]int64{"城镇": 1, "农村": 2}
var PoliticalIdentityMap = map[string]int64{"中国共产党员": 1, "中国共产党预备党员": 2, "中国共产主义青年团团员": 3, "其他党派人士": 4, "群众": 5}
var HealthStatusMap = map[string]int64{"健康": 1, "良好": 2, "一般": 3, "慢性病": 4, "残疾": 5}
var StatusMap = map[string]int64{"是": 1, "否": 2}
var MarriageStatusMap = map[string]int64{"未婚": 1, "已婚": 2, "离异": 3}
var BearStatusMap = map[string]int64{"未育": 1, "一胎": 2, "二胎": 3, "三胎": 4, "三胎以上": 5, "已育": 6}
var HighestEduMap = map[string]int64{"博士": 1, "博士在读": 2, "硕士": 3, "硕士在读": 4, "大学本科": 5, "大学专科": 6, "中等专科": 7, "职业高中": 8, "技工学校": 9, "普通高中": 10, "初中": 11, "小学": 12, "在职大专": 13, "在职本科": 14, "在职硕士": 15, "在职博士": 16, "其他": 17, "在职研究生": 18}
var JoinCompanyWayMap = map[string]int64{"在编": 1, "公开招聘": 2, "派遣": 3, "军转安置": 4, "借用": 5, "自主录用": 6, "自主招聘": 7, "转岗": 8, "竞聘上岗": 9, "其他": 10}
var WorkPostTypeMap = map[string]int64{"普通管理员": 1, "干部": 2, "驾驶员": 3, "乘务员": 4, "安保人员": 5, "辅工": 6, "仓管人员": 7, "其他": 8, "修理工": 9}
var PositionTypeMap = map[string]int64{"主职": 1, "兼职": 2}
var IsNowJobMap = map[string]int64{"现任职务": 1, "历任职务": 2}
var LaborContractType = map[string]int64{"无期限": 1, "固定期限": 2}
var AssessmentDegree = map[string]int64{"合格": 1, "不合格": 2, "良好": 3, "优秀": 4}
var ViolationOrigin = map[string]int64{"现场检查": 1, "交通违章": 2, "交通事故": 3, "三方测评": 4, "文明城市": 5, "主动安全": 6, "视频监控": 7}
var TrafficAccidentGradeMap = map[int64]string{1: "轻微事故", 2: "ICU以上事故", 3: "留院观察", 4: "住院治疗"}
var RelieveContractTypeMap = map[int64]string{1: "自主解除劳动合同", 2: "公司解除", 3: "协商解除", 4: "去世", 5: "试用期不予转正"}
var AccidentFixOfficeMap = map[int64]string{1: "黄岩客运西站", 2: "椒江景元路", 3: "椒江客运总站", 4: "椒江工人路", 5: "路桥桐屿", 6: "路桥金清", 7: "综合场站", 8: "回浦", 9: "洪家"}
var InsuranceTypeMap = map[string]int64{"交强险": 1, "商业险": 2}
var EmploymentCateMap = map[string]int64{"一类": 1, "二类": 2, "三类": 3}

// MasterToErp 主数据岗位类型：0-司机; 1-乘务员;2-管理员;3-辅工; 4-辅岗; 5-干部; 6-仓管人员; 7-安保人员;8-修理工; 10-其他;
var MasterToErp = map[int64]int64{2: 1, 5: 2, 0: 3, 1: 4, 7: 5, 3: 6, 6: 7, 8: 9, 10: 8}

// ErpToMaster ERP岗位类型：1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他; 9-修理工
var ErpToMaster = map[int64]int64{1: 2, 2: 5, 3: 0, 4: 1, 5: 7, 6: 3, 7: 6, 8: 10, 9: 8}
var ErpWorkPostTypeMap = map[int64]string{1: "普通管理员", 2: "干部", 3: "驾驶员", 4: "乘务员", 5: "安保人员", 6: "辅工", 7: "仓管人员", 8: "其他", 9: "修理工"}
var TrafficAccidentCateMap = map[int64]string{1: "车内伤", 2: "车外伤", 3: "单方事故", 4: "倒车", 5: "刮擦", 6: "追尾", 7: "侧翻", 8: "其他"}
var LogSceneValMap = map[string]string{"edit": "编辑", "delete": "删除", "create": "新增", "calc": "计算"}
var FrequencyTypeMap = map[int64]string{1: "双班", 2: "单班", 3: "混合班"}
var VehicleModelMap = map[int64]string{1: "中巴", 2: "大客"}
var StationDirectionMap = map[int64]string{0: "未知", 1: "东向西", 2: "西向东", 3: "南向北", 4: "北向南"}
var StationBusBoardTypeMap = map[int64]string{0: "无站牌", 1: "普通站牌", 2: "电子站牌", 3: "简易站牌"} //1-普通站牌，2-电子站牌，3-简易站牌
var StationPlatformTypeMap = map[int64]string{0: "非标准站", 1: "港湾式", 2: "直线式"}                  // 1-港湾式，2-直线式
var LineSheetMap = map[int64]string{0: "未知", 1: "上行", 2: "下行", 3: "环形"}
var LineAttrMap = map[int64]string{1: "常规公交", 2: "定制公交", 3: "公务车", 4: "校车", 5: "旅游专线", 6: "村村通"}

var StationRealityImageOrientationMap = map[string]string{"1": "正面", "2": "左面", "3": "右面", "4": "线路牌1", "5": "线路牌2"}

var OetLineTypeMap = map[int64]string{
	LineTypeUnknown:      "未知",
	LineTypeDoubleStd:    "标准双向",
	LineTypeSingleCircle: "单向环行线路",
	LineTypeDoubleCircle: "双向环行线路",
	LineTypeSingleRand:   "机动型",
}

const (
	Oet_Station_Queue_First  = 1 // 1-起点站
	Oet_Station_Queue_Middle = 2 // 2-中途站
	Oet_Station_Queue_Last   = 3 // 3-终点站
)

var OetStationTypeMap = map[int64]string{
	Oet_Station_Queue_Middle: "中途站",
	Oet_Station_Queue_First:  "起点站",
	Oet_Station_Queue_Last:   "终点站",
}

var OetLineStationTypeMap = map[int64]string{
	Station_Queue_Middle: "中途站",
	Station_Queue_First:  "起点站",
	Station_Queue_Last:   "终点站",
	Station_Queue_Sample: "普通采样点",
}

var CoordinateSystemMap = map[int64]string{
	1: "WGS84", 2: "GCJ02",
}

// ThirdBranchToOetCorporationId IC卡的机构编号和平台机构ID的映射
var ThirdBranchToOetCorporationId = map[string]int64{
	//一公司
	"8001": 1518210482337481731,
	//二公司
	"8002": 1518210375047185410,
	//三公司
	"8003": 1518210993757357060,
	//四公司
	"8004": 1518212091473822725,
	//村村通公司
	"8012": 0,

	//一公司一车队
	"8101": 1519706879830262790,
	//一公司二车队
	"8102": 1519707195694908423,
	//一公司三车队
	"8103": 1519707559844381705,
	//一公司四车队
	"8104": 1519707397130552328,
	//一公司中心大道车队
	"8401": 1846461825940456665,

	//二公司一车队
	"8201": 1519709661056467982,
	//二公司二车队
	"8202": 1519711340145411088,
	//二公司三车队
	"8203": 1519711579396899857,
	//二公司四车队
	"8204": 1519711772527821842,
	//二公司五车队
	"8205": 1519711978040329235,

	//三公司一车队
	"8301": 1519712307444188180,
	//三公司二车队
	"8302": 1519712465795941397,
	//三公司三车队
	"8303": 1519712641109459990,
	//三公司四车队
	"8304": 1519712888539841559,

	//村村通
	"8121": 0,
}
