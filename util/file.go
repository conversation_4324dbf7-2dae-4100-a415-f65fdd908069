package util

import (
	"archive/zip"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"os"
	"path"
	"strings"
)

// 创建文件返回保存文件路径
func CreateFile(fileName, fileData string, dirs ...string) (*os.File, string, error) {
	suffix := path.Ext(fileName) // 文件名必须存在后缀
	if "" == suffix {
		return nil, "", errors.New("FT1001")
	}

	var (
		file     *os.File
		filePath string
		err      error
	)

	defer func() {
		if err := CloseFile(file); nil != err {
			log.Println(" CloseFile[err]:", err)
		}

		if nil != err {
			if err := RemoveFile(filePath); nil != err {
				log.Println(" RemoveFile[err]:", err)
			}
		}
	}()

	if 0 < len(dirs) {
		filePath += strings.Join(dirs, "/") + "/"
		if err := VerifyMkdirExistAndCreate(filePath); nil != err {
			return nil, "", err
		}
	}

	filePath += fileName

	file, err = CreateFileWriteData(filePath, fileData)
	if nil != err {
		return file, "", err
	}

	return file, filePath, nil
}

func CreateFileWriteData(filePath, fileData string) (*os.File, error) {
	file, err := os.Create(filePath)
	if nil != err {
		log.Println(" os.Create[err]:", err)
		return file, err
	}

	bytes, err := Base64Decode(fileData)
	if err != nil {
		log.Println(" base64.StdEncoding.DecodeString[err]:", err)
		return file, err
	}

	_, err = file.Write(bytes) // 写入数据
	if err != nil {
		log.Println(" file.Write[err]:", err)
		return file, err
	}

	return file, nil
}

func OpenFileWriteData(filePath, fileData string) error {
	file, err := os.OpenFile(filePath, os.O_RDWR|os.O_APPEND|os.O_SYNC, 0)
	if nil != err {
		log.Println("open ", filePath, " [err]:", err)
		return err
	}

	defer file.Close()

	fileBytes, err := base64.StdEncoding.DecodeString(fileData)
	if nil != err {
		log.Println(" base64.StdEncoding.DecodeString[err]:", err)
		return err
	}

	return WriteFileDataWithFile(file, fileBytes)
}

func WriteFileDataWithFile(file *os.File, bytes []byte) error {
	_, err := file.Write(bytes)
	if nil != err {
		log.Println(" file.write[err]:", err)
		return err
	}

	return nil
}

// 检查目录是否存在，不存在则创建
func VerifyMkdirExistAndCreate(dir string) error {
	exist, err := PathExists(dir)
	if err != nil {
		return err
	}
	if !exist {
		// 创建文件夹
		if err := os.MkdirAll(dir, os.ModePerm); err != nil {
			return err
		} else {
			return nil
		}
	} else {
		return nil
	}
}

// 判断文件夹是否存在
func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}

	if os.IsNotExist(err) {
		return false, nil
	}

	return false, err
}

func CloseFile(f *os.File) error {
	if nil == f {
		return nil
	}

	if err := f.Close(); nil != err {
		return err
	}

	return nil
}

// 文件存在则删除
func RemoveFile(fp string) error {
	isExist, err := PathExists(fp)
	if nil != err {
		return err
	}

	if isExist {
		if err := os.Remove(fp); nil != err {
			return err
		}
	}

	return nil
}

func Base64Decode(s string) ([]byte, error) {
	return base64.StdEncoding.DecodeString(s)
}

func ReadAll(filePth string) ([]byte, error) {
	f, err := os.Open(filePth)
	if err != nil {
		return nil, err
	}

	return ioutil.ReadAll(f)
}

// FilesToZip 将磁盘地址文件 生成zip压缩包 asNames: 重命名，和filesPath对应
func FilesToZip(filesPath []string, zipFilePath string, asNames []string) error {
	if len(asNames) > 0 {
		if len(filesPath) != len(asNames) {
			return errors.New("params missing")
		}
	}
	archive, err := os.Create(zipFilePath)
	if err != nil {

		return err
	}
	defer archive.Close()

	zipWriter := zip.NewWriter(archive)
	defer zipWriter.Close()

	for i, filePath := range filesPath {
		//打开要压缩的文件
		f, err := os.Open(filePath)
		if err != nil {
			return err
		}

		//获取文件的描述
		info, err := f.Stat()
		if err != nil {
			f.Close()
			return err
		}

		header, err := zip.FileInfoHeader(info)
		if err != nil {
			f.Close()
			return err
		}
		header.Name = asNames[i] // 压缩包内部的路径
		/*
		   预定义压缩算法。
		   archive/zip包中预定义的有两种压缩方式。一个是仅把文件写入到zip中。不做压缩。一种是压缩文件然后写入到zip中。默认的Store模式。就是只保存不压缩的模式。
		   Store   unit16 = 0  //仅存储文件
		   Deflate unit16 = 8  //压缩文件
		*/
		header.Method = zip.Deflate
		//创建压缩包头部信息
		w, err := zipWriter.CreateHeader(header)
		if err != nil {
			f.Close()
			return err
		}
		//将源复制到目标，将fileToZip 写入writer   是按默认的缓冲区32k循环操作的，不会将内容一次性全写入内存中,这样就能解决大文件的问题
		_, err = io.Copy(w, f)
		if err != nil {
			f.Close()
			return err
		}

		f.Close()
	}

	return nil

}

func GetFileSize(filePath string) int64 {
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		fmt.Printf("Error obtaining file info: %s\n", err)
		return 0
	}

	return fileInfo.Size()
}
