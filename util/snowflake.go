package util

import (
	"crypto/rand"
	"math"
	"math/big"
	"sync"
	"time"
)

type SnowFlakeWorker struct {
	MachineID     int64      // 机器 id 占10位, 十进制范围是 [ 0, 1023 ]
	Sn            int64      // 序列号占 12 位,十进制范围是 [ 0, 4095 ]
	LastTimeStamp int64      // 上次的时间戳(毫秒级), 1秒=1000毫秒, 1毫秒=1000微秒,1微秒=1000纳秒
	Lock          sync.Mutex // 加锁保证唯一性
}

func (w *SnowFlakeWorker) init() {
	w.LastTimeStamp = time.Now().UnixNano() / 1000000
}

func (w *SnowFlakeWorker) SetMachineId(mid int64) {
	// 把机器 id 左移 12 位,让出 12 位空间给序列号使用
	w.MachineID = mid << 12
}

func (w *SnowFlakeWorker) GenerateId() int64 {
	// 加锁/解锁
	w.Lock.Lock()
	defer w.Lock.Unlock()

	curTimeStamp := time.Now().UnixNano() / 1000000

	// 同一毫秒
	if curTimeStamp == w.LastTimeStamp {
		w.Sn++
		// 序列号占 12 位,十进制范围是 [ 0, 4095 ]
		if w.Sn > 4095 {
			time.Sleep(time.Millisecond)
			curTimeStamp = time.Now().UnixNano() / 1000000
			w.LastTimeStamp = curTimeStamp
			w.Sn = 0
		}

		// 取 64 位的二进制数 0000000000 0000000000 0000000000 0001111111111 1111111111 1111111111  1 ( 这里共 41 个 1 )和时间戳进行并操作
		// 并结果( 右数 )第 42 位必然是 0,  低 41 位也就是时间戳的低 41 位
		rightBinValue := curTimeStamp & 0x1FFFFFFFFFF
		// 机器 id 占用10位空间,序列号占用12位空间,所以左移 22 位; 经过上面的并操作,左移后的第 1 位,必然是 0
		rightBinValue <<= 22
		id := rightBinValue | w.MachineID | w.Sn
		return id
	}

	if curTimeStamp > w.LastTimeStamp {
		w.Sn = 0
		w.LastTimeStamp = curTimeStamp
		// 取 64 位的二进制数 0000000000 0000000000 0000000000 0001111111111 1111111111 1111111111  1 ( 这里共 41 个 1 )和时间戳进行并操作
		// 并结果( 右数 )第 42 位必然是 0,  低 41 位也就是时间戳的低 41 位
		rightBinValue := curTimeStamp & 0x1FFFFFFFFFF
		// 机器 id 占用10位空间,序列号占用12位空间,所以左移 22 位; 经过上面的并操作,左移后的第 1 位,必然是 0
		rightBinValue <<= 22
		id := rightBinValue | w.MachineID | w.Sn
		return id
	}

	if curTimeStamp < w.LastTimeStamp {
		return 0
	}

	return 0
}

var Worker *SnowFlakeWorker

func InitSnowFlakeWorker() {
	Worker = &SnowFlakeWorker{}
	Worker.init()
	// 机器 id 占10位, 十进制范围是 [ 0, 1023 ]
	mid := RandRandom(0, 1023)
	Worker.SetMachineId(mid)
}

func GenerateId() int64 {
	return Worker.GenerateId()
}

func RandRandom(min, max int64) int64 {
	if min < 0 {
		f64Min := math.Abs(float64(min))
		i64Min := int64(f64Min)
		result, _ := rand.Int(rand.Reader, big.NewInt(max+1+i64Min))

		return result.Int64() - i64Min
	} else {
		result, _ := rand.Int(rand.Reader, big.NewInt(max-min+1))
		return min + result.Int64()
	}
}
