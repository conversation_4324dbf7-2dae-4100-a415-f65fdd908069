package util

import (
	"fmt"
	"github.com/skip2/go-qrcode"
)

//type QrCode struct {
//	Width  int    // 二维码宽度
//	Height int    // 二维码高度
//	Param  string // 二维码参数
//}
//
const (
	Default_QrCode_Width  = 296 // 默认二维码宽度 100
	Default_QrCode_Height = 296 // 默认二维码高度 100

	Default_Suffix       = ".png" // 文件后缀
	Default_Width_Height = 256    // 文件后缀

)

//
//func ParseQrCodeParam(format string, val ...interface{}) *QrCode {
//	return &QrCode{
//		Width:  Default_QrCode_Width,
//		Height: Default_QrCode_Height,
//		//Param:  fmt.Sprintf(format, val),
//	}
//}
//
//func NewQrCode(qrParam string) *QrCode {
//	return &QrCode{
//		Width:  Default_QrCode_Width,
//		Height: Default_QrCode_Height,
//		Param:  qrParam,
//	}
//}
//
//func (this *QrCode) Encode() (image.Image, error) {
//	code, err := qr.Encode(this.Param, qr.L, qr.Unicode)
//	if nil != err {
//		return nil, err
//	}
//
//	code, err = barcode.Scale(code, this.Width, this.Height)
//	if nil != err {
//		return nil, err
//	}
//
//	return code, nil
//}
//
//func (this *QrCode) Save(filePath string) error {
//	file, err := os.Create(filePath)
//	if nil != err {
//		return err
//	}
//
//	defer file.Close()
//
//	qrImage, err := this.Encode()
//	if nil != err {
//		return err
//	}
//
//	err = png.Encode(file, qrImage)
//	if nil != err {
//		return err
//	}
//
//	return nil
//}

func SaveQrcode(fileDir, fileName string, qrcodeParam string) error {

	err := VerifyMkdirExistAndCreate(fileDir)
	if err != nil {
		fmt.Println("VerifyMkdirExistAndCreate error ==", err)
		return err
	}

	err = qrcode.WriteFile(qrcodeParam, qrcode.Medium, Default_Width_Height, fmt.Sprintf("%s%s", fileDir, fileName))
	if err != nil {
		fmt.Println("二维码生成错误 ==", err)
		return err
	}
	return nil
}
