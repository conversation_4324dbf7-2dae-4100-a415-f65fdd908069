package response

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"fmt"
	api "github.com/micro/go-micro/v2/api/proto"
)

func Error(rsp *api.Response, code string) error {
	var msg string

	if m, ok := MsgMap[code]; ok {
		msg = m.Message
	}

	if msg == "" {
		msg = code
		code = FAIL
	}

	respMsg := map[string]interface{}{
		"Result": map[string]interface{}{
			"Data": nil,
			"Code": code,
			"Msg":  msg,
		},
	}

	b, _ := json.Marshal(respMsg)
	rsp.StatusCode = 200
	rsp.Body = string(b)

	return nil
}

func ErrorWithMsg(rsp *api.Response, code string, msg string) error {
	respMsg := map[string]interface{}{
		"Result": map[string]interface{}{
			"Data": nil,
			"Code": code,
			"Msg":  msg,
		},
	}

	b, _ := json.Marshal(respMsg)
	rsp.StatusCode = 200
	rsp.Body = string(b)
	return nil
}

func Success(rsp *api.Response, data interface{}) error {
	respMsg := map[string]interface{}{
		"Result": map[string]interface{}{
			"Data": data,
			"Code": SUCCESS,
			"Msg":  MsgMap[SUCCESS].Message,
		},
	}
	b, _ := json.Marshal(respMsg)

	rsp.StatusCode = 200
	rsp.Body = string(b)
	return nil
}

func SuccessBigString(rsp *api.Response, data *string) error {
	respMsg := map[string]interface{}{
		"Result": map[string]interface{}{
			"Data": *data,
			"Code": SUCCESS,
			"Msg":  MsgMap[SUCCESS].Message,
		},
	}
	b, _ := json.Marshal(respMsg)

	rsp.StatusCode = 200
	rsp.Body = string(b)
	return nil
}

func SendResp2Dss(rsp *api.Response, data map[string]interface{}) error {
	respMsg := map[string]interface{}{
		"data": data,
		"code": 200,
		"msg":  "成功",
	}

	b, _ := json.Marshal(respMsg)

	rsp.StatusCode = 200
	rsp.Body = string(b)
	log.Printf("SendResp2Dss --> %v\n", string(b))
	return nil
}

func BindForm(req *api.Request, form interface{}) (errorCode string) {
	err := json.Unmarshal([]byte(req.Body), form)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"error": err.Error()})
		return ParamsInvalid
	}
	err = util.Validator().Struct(form)
	if err != nil {
		log.ErrorFields("Validator Struct err", map[string]interface{}{"error": err.Error()})
		return ParamsInvalid
	}
	return ""
}

func BindFormWithoutValidator(req *api.Request, form interface{}) (errorCode string) {
	err := json.Unmarshal([]byte(req.Body), form)
	if err != nil {
		fmt.Println(err)
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"error": err.Error()})
		return ParamsInvalid
	}
	return ""
}
