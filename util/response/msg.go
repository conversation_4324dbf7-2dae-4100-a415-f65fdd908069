package response

const (
	SUCCESS              = "0"
	BadRequest           = "400"
	FAIL                 = "500"
	HttpMethodNotAllowed = "405"
	Unauthorized         = "401"
	Forbidden            = "403"

	SignInvalid = "SN1001"
	UnknownApp  = "AP1001"

	ParamsInvalid            = "PA1001"
	ParamsMissing            = "PA1011"
	PasswordNotMatch         = "PA1004"
	CorporationParamNotFleet = "PA1100"
	NotFundUserInfo          = "PA1101"
	NotFleetAccount          = "PA1102"
	CodeDontRepeat           = "PA1103"
	DeleteParentDataFail     = "PA1104"

	AccountOrPasswordInvalid = "LG1001"
	PasswordInvalid          = "LG1005"
	AccountExpired           = "LG1003"
	LoginFail                = "LG1004"
	LoginExpire              = "LG1005"

	DbNotFoundRecord  = "DB1001"
	DbSaveFail        = "DB1002"
	DbUpdateFail      = "DB1003"
	DbQueryFail       = "DB1004"
	DbObjectDuplicate = "DB1005"
	DbDeleteFail      = "DB1006"

	InsuranceSettingNotFund = "DB2001"
	FundSettingNotFund      = "DB2002"
	LineNotFund             = "DB2003"

	RepeatApplyProcess = "BPM1001"
	CreateProcessFail  = "BPM1002"

	FileExtInvalid    = "FT1001"
	FileSizeOverLimit = "FT1002"

	AccidentUnCloseDoNotPrint  = "OP7511"
	LeaveDayCountOverEnableDay = "OP7512"
	SetProcessFormStepFail     = "OP7513"
)

type Info struct {
	Code    string
	Meta    string
	Message string
}

var infos = []Info{
	{SUCCESS, "OK", "操作成功"},
	{"500", "FAIL", "操作失败，请重试"},
	{"405", "NOT ALLOWED", "请求方式不被允许"},
	{"400", "BAD REQUEST", "不合法请求"},
	{"401", "UNAUTHORIZED", "未登录或登录已过期"},
	{"403", "FORBIDDEN", "没有操作权限"},

	{"PA1001", "params invalid", "参数不可用"},
	{"PA1011", "params missing", "缺少必要参数"},
	{"PA1004", "params invalid", "输入的密码不一致"},
	{"PA1100", "params invalid", "所选机构不是车队，不可用"},
	{"PA1101", "params invalid", "没有找到账号信息"},
	{NotFleetAccount, "params invalid", "当前登录账号所在的机构不是车队"},
	{"PA1103", "params invalid", "编号已存在，无法保存"},
	{"PA1104", "params invalid", "下级存在数据，无法删除"},
	{"PA1105", "params invalid", "当前登录账号所在的机构不是车队"},
	{"PA1106", "params invalid", "不支持多机构账号"},

	{"DB1001", "DB Error", "未查询到数据"},
	{"DB1002", "DB Error", "数据保存失败"},
	{"DB1003", "DB Error", "数据更新失败"},
	{"DB1004", "DB Error", "查询数据失败"},
	{"DB1005", "DB Error", "对象已存在，请勿重复添加"},
	{"DB1006", "DB Error", "数据删除失败"},

	{"DB2001", "DB Error", "请先设置社保缴纳比例"},
	{"DB2002", "DB Error", "请先设置公积金缴纳比例"},
	{"DB2003", "DB Error", "选择的线路不存在"},

	{"SN1001", "Sign Error", "签名校验失败"},

	{"LG1001", "Login Error", "用户名或密码错误"},
	{"LG1002", "Session Error", "会话过期，请重新登陆"},
	{"LG1003", "Session Error", "帐号已过期"},
	{"LG1004", "Login Error", "登录失败"},
	{"LG1005", "Login Error", "登录已过期，请重新登录"},
	{"LG1006", "Login Error", "该登录账号未找到对应机构"},

	{"AM1001", "unknown agreement", "协议版本不支持"},
	{"AP1001", "unknown app", "应用不支持"},
	{"FT1001", "unknown file", "文件格式不支持"},
	{"FT1002", "big file", "文件大小超过限制"},

	{"BPM1001", "PROCESS REPEAT", "流程审批中，请勿重复提交申请"},
	{"BPM1002", "PROCESS FAIL", "流程发起失败"},
	{"BPM1002", "PROCESS REPEAT", "库存不足"},
	{"BPM1003", "PROCESS REPEAT", "有数据正在审批或者审批通过，暂时无法更改数据"},

	// 安全模块
	{"OP1001", "Operate Error", "违规类别已绑定质量考核标准，不允许删除"},
	//{"OP7001", "Operate Error", "员工手机号未关联"}, // 平台账号未与erp员工通过手机号关联
	{"OP7004", "Operate Error", "用户信息获取错误"}, // rpc error
	{"OP7005", "Operate Error", "车辆信息获取错误"}, // rpc error
	{"OP7006", "Operate Error", "机构信息获取错误"}, // rpc error
	{"OP7500", "Operate Error", "创建失败，门检项目已存在"},
	{"OP7501", "Operate Error", "创建失败，门检项目中存在同属性且同名标签"},
	{"OP7502", "Operate Error", "只有司机和管理员能提交门检结果"},
	{"OP7503", "Operate Error", "车辆所属机构不在门检配置机构中"},
	{"OP7504", "Operate Error", "该机构不在门检配置机构中"},
	{"OP7505", "Operate Error", "没有找到该车辆信息，或者您没有该车辆管理权限"},
	{"OP7506", "Operate Error", "没有找到车辆信息，该机构下可能没有车辆"},
	{"OP7507", "Operate Error", "身份不是司机或者管理员，无法进行门检"},
	{"OP7508", "Operate Error", "身份不是管理员或者干部，无法进行代录入"},
	{"OP7509", "Operate Error", "代录入无法进行抽检"},
	{"OP7510", "Operate Error", "配置文件中未找到对应事故机构编号"},
	{"OP7511", "Operate Error", "事故未结案，不可打印"},
	{"OP7512", "Operate Error", "请假天数超过可用天数，无法申请"},
	{"OP7513", "Operate Error", "FormStep无效，请在formStep中重新定义"},
	{"OP7514", "Operate Error", "设备正在报修中，不能重复提交"},
	{"OP7515", "Operate Error", "请假未通过审核，无法作废"},
	{"OP7516", "Operate Error", "未查询车辆信息"},
	{"OP7517", "Operate Error", "压缩文件生成失败"},
	{"OP7518", "Operate Error", "获取二维码列表失败"},
	{"OP7520", "Operate Error", "此安全生产表无法删除"},
	{"OP7521", "Operate Error", "此安全生产表无法修改"},
	{"OP7522", "Operate Error", "此通知无法撤回"},
	{"OP7523", "Operate Error", "该车队此月已提交"},
	{"OP7524", "Operate Error", "请假修改未通过审核，无法作废"},
	// 人资模块
	{"OP8001", "Operate Error", "入职未满6个月，无法提交引荐奖励"},
	{"OP8002", "Operate Error", "已提交引荐人奖励，请勿重复提交"},
	{"OP8003", "Operate Error", "引荐名单重复，请勿提交"},
	{"OP9001", "Operate Error", "同品牌、同型号、同供货方只允许有一条数据"},
	{"OP9002", "Operate Error", "线路未设定配置项内容，请完成线路配置后，重新点击计算"},
	{"OP9003", "Operate Error", "选择的时间段内存在未计算数据的时间，请先计算数据"},
	{"OP9004", "Operate Error", "选择的时间段与其他时间段有交叉，请确认"},
	{"OP9005", "Operate Error", "设置的起止时间最大时长：3年"},
	{"OP9006", "Operate Error", "选择的时间范围最长不能超过31天"},
	{"OP9007", "Operate Error", "证件照已审核通过"},
	{"OP9008", "Operate Error", "请输入驳回理由"},
	{"OP9009", "Operate Error", "证件照同步失败"},
	{"OP9010", "Operate Error", "此工资数据无法进行修改"},
	// 运营模块
	{"OP9901", "Operate Error", "运营配置存在站点绑定记录"},
	{"OP9902", "Operate Error", "请配置站点规格后再生成海报"},
	{"OP9903", "Operate Error", "当前只支持一个任务在执行，请耐心等待后再重试"},
	{"OP9904", "Operate Error", "该广告类型下无此材料"},
	{"OP9905", "Operate Error", "该广告类型下无此规格"},
	{"OP9906", "Operate Error", "广告类型不匹配"},
	{"OP9907", "Operate Error", "当前表单状态已失效，无法提交"},
	{"OP9908", "Operate Error", "后台正在计算报表数据，请间隔5分钟后再操作"},
	{"OP9909", "Operate Error", "当前任务单存在派车单信息，不允许删除"},
	{"OP9910", "Operate Error", "当前任务单可能已被删除，请刷新页面再试"},

	{"LA1001", "Operate Error", "试卷已过期"},
}

var MsgMap = make(map[string]Info)

func init() {
	for _, val := range infos {
		MsgMap[val.Code] = Info{val.Code, val.Meta, val.Message}
	}
}
