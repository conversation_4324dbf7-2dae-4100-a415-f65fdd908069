package xlsx

import (
	"app/org/scs/erpv2/api/util"
	"fmt"
	"github.com/tealeg/xlsx"
	"strconv"
	"strings"
)

type ExportTaskIter interface {
	Enable()
}

type XlsxCellMetadataAndHeader struct {
	HeaderName   string
	CellMetadata xlsx.CellMetadata
	Width        int64
}

//通用-复杂表头数据结构
type xlsxMergeCell struct {
	hcells int
	vcells int
	vlue   string
}

//创建简易表格，包含1张表并且设置简易一级表头
func CreateEasyXlsx(mergeArr []xlsxMergeCell, menuArr []string) (*xlsx.File, *xlsx.Sheet, *xlsx.Style) {
	file := xlsx.NewFile()
	sheet, err := file.AddSheet("Sheet1")
	if err != nil {
		fmt.Printf(err.Error())
	}
	style := xlsxSheetAddHead(sheet, mergeArr, menuArr)
	return file, sheet, style
}

//给xlsx表设置简易一级表头
func xlsxSheetAddHead(sheet *xlsx.Sheet, mergeArr []xlsxMergeCell, menuArr []string) *xlsx.Style {
	//设置表格排列样式
	style := GetExcelPubStyle()

	//生成表头

	rowCellNum := 0        //统计一行的单元格个数，也就是表格的列数
	vcellsIndex := []int{} //记录使用了垂直合并的单元格下标，用于补充空白单元格

	if len(mergeArr) > 0 {
		//多级表头的一级表头
		row := sheet.AddRow()
		for _, c := range mergeArr {
			xlsxRowAddCellReturn(row, style, c.vlue).Merge(c.hcells, c.vcells)
			if c.hcells > 0 {
				//填补横向合并所占的空白单元格位置
				for ci := 0; ci < c.hcells; ci++ {
					xlsxRowAddCell(row, style, "")
				}
				rowCellNum += c.hcells + 1
			} else {
				rowCellNum++
			}
			if c.vcells > 0 {
				vcellsIndex = append(vcellsIndex, rowCellNum-1)
			}
		}
	}

	if len(menuArr) > 0 {
		row := sheet.AddRow()
		if len(vcellsIndex) > 0 {
			//二级表头
			//按照表格列数循环添加单元格，若当前单元格下标跟在vcellsIndex中存在，则添加空单元格
			menuArrIndex := 0
			for idx := 0; idx < rowCellNum; idx++ {
				isNullCell := false
				for _, v := range vcellsIndex {
					if idx == v {
						isNullCell = true
						break
					}
				}
				if isNullCell {
					xlsxRowAddCell(row, style, "")
				} else {
					xlsxRowAddCell(row, style, menuArr[menuArrIndex])
					menuArrIndex++
				}
			}
		} else {
			//普通表头
			for _, k := range menuArr {
				xlsxRowAddCell(row, style, k)
			}
		}

	}

	return style
}

//获取excel通用样式
func GetExcelPubStyle() *xlsx.Style {
	//设置表格排列样式
	style := xlsx.NewStyle()
	alignment := xlsx.Alignment{
		Horizontal: "center", //水平居中
		Vertical:   "center", //垂直居中
	}
	border := xlsx.Border{
		Left:   "thin",
		Right:  "thin",
		Top:    "thin",
		Bottom: "thin",
	}
	style.Border = border
	style.ApplyBorder = true
	style.Alignment = alignment
	style.ApplyAlignment = true
	return style
}

//表格行增加单元格
func xlsxRowAddCellReturn(row *xlsx.Row, style *xlsx.Style, value interface{}) *xlsx.Cell {
	cell := row.AddCell()
	cell.SetStyle(style)
	cell.SetValue(value)
	return cell
}

func xlsxRowAddCell(row *xlsx.Row, style *xlsx.Style, value interface{}) {
	cell := row.AddCell()
	cell.SetStyle(style)
	cell.SetValue(value)
	return
}

func xlsxRowAddCells(row *xlsx.Row, style *xlsx.Style, values []interface{}) {
	if len(values) > 0 {
		for i := range values {
			cell := row.AddCell()
			cell.SetStyle(style)
			cell.SetValue(values[i])
		}
	}
}

func GetExportFileName(table, name, startAt, endAt, file string) string {
	var starSplit = strings.Split(startAt, " ")
	var endSplit = strings.Split(endAt, " ")
	if len(starSplit) != len(endSplit) {
		if starSplit[0] == endSplit[0] {
			startAt = starSplit[0]
			endAt = starSplit[0]
		}
	}

	var starAtStr = GetAtStrNoPoint(startAt)
	var endAtStr = GetAtStrNoPoint(endAt)

	var fileName = table
	if name != "" {
		fileName += "_" + name
	}
	if startAt != "" {
		if startAt == endAt {
			fileName += "_" + starAtStr
		} else {
			if endAt != "" {
				fileName += "_" + starAtStr + "_" + endAtStr
			} else {
				fileName += "_" + starAtStr
			}
		}
	}
	fileName += "." + file

	return fileName
}

//获取导出的文件名
func GetAtStrNoPoint(dateStr string) string {
	var part = strings.Split(dateStr, " ")
	var date = strings.Split(part[0], "/")
	var str = GetAtStrByArr(date)
	if len(part) > 1 {
		var time = strings.Split(part[1], ":")
		str += GetAtStrByArr(time)
	}
	return str
}
func GetAtStrByArr(arr []string) string {
	var str = ""
	for _, v := range arr {
		num, _ := strconv.ParseInt(v, 10, 64)
		if num < 10 {
			v = fmt.Sprintf("0%v", num)
		}
		str += v
	}
	return str
}

func getTwoValDivisionFloat(val1, val2 float64, decimal string) float64 {
	if val2 <= 0 {
		return 0
	} else {
		var format = ""
		if decimal != "" {
			format = "%." + decimal + "f"
			val, _ := strconv.ParseFloat(fmt.Sprintf(format, val1/val2), 64)
			return val
		} else {
			return val1 / val2
		}

	}
}

func changeDateStr(dateStr string) string {
	if dateStr != "" {
		var split = strings.Split(dateStr, "-")
		return split[0] + "/" + split[1] + "/" + split[2]
	} else {
		return ""
	}
}

func GetStreamFile(fileName, path string, xlsxCellMetadataAndHeaders []XlsxCellMetadataAndHeader) (*xlsx.StreamFile, error) {
	err := util.VerifyMkdirExistAndCreate(path)
	if err != nil {
		fmt.Println("创建,错误信息[%v]", err)
		return nil, err
	}

	var absFileName = path + "/" + fileName

	var (
		headNames   []string
		headerTypes []*xlsx.CellMetadata
	)

	for i := 0; i < len(xlsxCellMetadataAndHeaders); i++ {
		headNames = append(headNames, xlsxCellMetadataAndHeaders[i].HeaderName)
		headerTypes = append(headerTypes, &xlsxCellMetadataAndHeaders[i].CellMetadata)
	}

	var sheetName = "Sheet1"

	file, err := xlsx.NewStreamFileBuilderForPath(absFileName)
	if err != nil {
		fmt.Println("NewStreamFileBuilderForPath:", err)
		return nil, err
	}

	err = file.AddSheetWithDefaultColumnMetadata(sheetName, headNames, headerTypes)
	if err != nil {
		fmt.Println("AddSheetWithDefaultColumnMetadata:", err)
		return nil, err
	}

	streamFile, err := file.Build()
	if err != nil {
		fmt.Println("file.Build:", err)
		return nil, err
	}

	return streamFile, nil
}
