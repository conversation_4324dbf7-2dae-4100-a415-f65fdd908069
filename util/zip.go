package util

import (
	"app/org/scs/erpv2/api/log"
	"archive/zip"
	"bytes"
	"errors"
	"fmt"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
	"io"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"

	"github.com/micro/go-micro/v2/logger"
)

func WriteFileContent(fileName string, bytes []byte) error {
	var (
		file *os.File
		err  error
	)
	file, err = os.Create(fileName)
	if nil != err {
		logger.Error(" os.Create[err]:", err)
		return err
	}

	_, err = file.Write(bytes) // 写入数据
	if err != nil {
		logger.Error(" file.Write[err]:", err)
		return err
	}
	return nil
}

// 本级目录不会压缩(假设目录: /mnt/1/update , baseFolder /mnt/1 那么update会压缩到zip文件中,但是1不存在zip当中)
func ZipWriter(baseFolder, zipName string) error {
	baseFolder = baseFolder + "/"

	// Get a Buffer to Write To
	outFile, err := os.Create(zipName)
	if err != nil {
		return err
	}
	defer outFile.Close()

	// Create a new zip archive.
	w := zip.NewWriter(outFile)

	// Add some files to the archive.
	err = addFiles(w, baseFolder, "")
	if err != nil {
		logger.Error("addFiles error:", err)
		return err
	}

	// Make sure to check the error on Close.
	err = w.Close()
	if err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func MultiFileCompression(zipPath string, filepaths []string) {
	if filepaths == nil {
		return
	}
	zipFile, err := os.Create(zipPath)
	if err != nil {
		log.ErrorFields("zip文件生成失败", map[string]interface{}{"err": err})
		return
	}
	defer zipFile.Close()
	// 创建 ZIP 写入器
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()
	// 遍历文件列表，将每个文件添加到 ZIP 中
	for _, file := range filepaths {
		// 打开文件
		fileToZip, err := os.Open(file)
		if err != nil {
			logger.Error("Failed to open file")
			continue
		}
		defer fileToZip.Close()

		// 获取文件信息
		fileInfo, err := fileToZip.Stat()
		if err != nil {
			logger.Error("Failed to get file info")
			continue
		}

		// 创建 ZIP 文件头
		header, err := zip.FileInfoHeader(fileInfo)
		if err != nil {
			logger.Error("Failed to create header")
			continue
		}

		// 设置文件头名称
		header.Name = filepath.Base(file)

		// 设置压缩方法
		header.Method = zip.Deflate

		// 添加文件头到 ZIP
		writer, err := zipWriter.CreateHeader(header)
		if err != nil {
			logger.Error("Failed to create header in ZIP")
			continue
		}

		// 将文件内容复制到 ZIP
		_, err = io.Copy(writer, fileToZip)
		if err != nil {
			logger.Error("Failed to add file to zip")
			continue
		}
	}

}

func addFiles(w *zip.Writer, basePath, baseInZip string) error {
	files, err := ioutil.ReadDir(basePath)
	if err != nil {
		logger.Error("ReadDir error:", err)
		return err
	}

	if len(files) == 0 {
		file, err := w.Create(baseInZip)
		if err != nil {
			return err
		}

		_, err = file.Write(nil) // 空目录条目需要一个空的文件内容
	}

	for _, file := range files {
		if !file.IsDir() {
			dat, err := ioutil.ReadFile(basePath + file.Name())
			if err != nil {
				logger.Error("addFiles: err " + file.Name())
				return err
			}

			// Add some files to the archive.
			f, err := w.Create(baseInZip + file.Name())
			if err != nil {
				logger.Error("addFiles: err " + file.Name())
				return err
			}
			_, err = f.Write(dat)
			if err != nil {
				logger.Error("addFiles: err " + file.Name())
				return err
			}
		} else if file.IsDir() {
			newBase := basePath + file.Name() + "/"

			logger.Debug("Recursing and Adding SubDir: " + file.Name())
			logger.Debug("Recursing and Adding SubDir: " + newBase)

			if err = addFiles(w, newBase, baseInZip+file.Name()+"/"); err != nil {
				logger.Error("addFiles: err " + file.Name())
				return err
			}
		}
	}
	return nil
}

// 解压
func DeCompress(zipFile, dest string) error {
	reader, err := zip.OpenReader(zipFile)
	if err != nil {
		return err
	}
	defer reader.Close()
	for _, file := range reader.File {
		filename := dest + "/" + file.Name
		if file.FileInfo().IsDir() {
			var dir = getDir(filename)
			if dir == "" {
				return errors.New("解压文件异常")
			}
			err = os.MkdirAll(dir, 0755)
			if err != nil {
				return err
			}
			continue
		}

		rc, err := file.Open()
		if err != nil {
			return err
		}

		w, err := os.Create(filename)
		if err != nil {
			return err
		}
		_, err = io.Copy(w, rc)
		if err != nil {
			return err
		}
		rc.Close()
		w.Close()
	}
	return nil
}

// 解压压缩包
func Unzip(zipFile string, destDir string) error {
	zipReader, err := zip.OpenReader(zipFile)
	if err != nil {
		return err
	}
	defer zipReader.Close()

	for _, f := range zipReader.File {

		fileNameBytes, _ := GbkToUtf8([]byte(f.Name))
		fileName := string(DelTailZero(fileNameBytes)) // 文件名(带后缀名)

		fpath := filepath.Join(destDir, fileName)
		if f.FileInfo().IsDir() {
			os.MkdirAll(fpath, os.ModePerm)
		} else {
			if err = os.MkdirAll(filepath.Dir(fpath), os.ModePerm); err != nil {
				return err
			}

			inFile, err := f.Open()
			if err != nil {
				return err
			}
			defer inFile.Close()

			outFile, err := os.OpenFile(fpath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
			if err != nil {
				return err
			}
			defer outFile.Close()

			_, err = io.Copy(outFile, inFile)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func getDir(path string) string {
	return subString(path, 0, strings.LastIndex(path, "/"))
}

func subString(str string, start, end int) string {
	rs := []rune(str)
	length := len(rs)

	if start < 0 || start > length {
		return ""
	}

	if end < start || end > length {
		return ""
	}

	return string(rs[start:end])
}

func GbkToUtf8(s []byte) ([]byte, error) {
	reader := transform.NewReader(bytes.NewReader(s),
		simplifiedchinese.GBK.NewDecoder())
	return ioutil.ReadAll(reader)
}

func DelTailZero(bytes []byte) []byte {
	var index int
	for _, value := range bytes {
		if 0 != value {
			bytes[index] = value
			index++
		}
	}
	return bytes[:index]
}
