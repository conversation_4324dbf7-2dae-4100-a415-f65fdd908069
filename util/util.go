package util

import (
	"fmt"
	"math/rand"
	"strconv"
	"time"
)

func Int64ArrToString(arr []int64) (data []string) {
	for _, v := range arr {
		data = append(data, fmt.Sprintf("%d", v))
	}
	return
}

func StringArrToInt64(arr []string) (data []int64) {
	for _, v := range arr {
		d, _ := strconv.Atoi(v)
		data = append(data, int64(d))
	}
	return
}

// ShuffleSlice 生成从 1 到 n 的乱序切片
func ShuffleSlice(n int) []int {
	if n <= 0 {
		return []int{}
	}

	// 初始化切片 [1, 2, ..., n]
	slice := make([]int, n)
	for i := 0; i < n; i++ {
		slice[i] = i + 1
	}

	// 设置随机种子并打乱切片
	rand.Seed(time.Now().UnixNano())
	rand.Shuffle(n, func(i, j int) {
		slice[i], slice[j] = slice[j], slice[i]
	})
	return slice
}
