package util

import (
	"fmt"
	"math/rand"
	"reflect"
	"strconv"
	"time"
)

func Int64ArrToString(arr []int64) (data []string) {
	for _, v := range arr {
		data = append(data, fmt.Sprintf("%d", v))
	}
	return
}

func StringArrToInt64(arr []string) (data []int64) {
	for _, v := range arr {
		d, _ := strconv.Atoi(v)
		data = append(data, int64(d))
	}
	return
}

// ShuffleSlice 生成从 1 到 n 的乱序切片
func ShuffleSlice(n int) []int {
	if n <= 0 {
		return []int{}
	}

	// 初始化切片 [1, 2, ..., n]
	slice := make([]int, n)
	for i := 0; i < n; i++ {
		slice[i] = i + 1
	}

	// 设置随机种子并打乱切片
	rand.Seed(time.Now().UnixNano())
	rand.Shuffle(n, func(i, j int) {
		slice[i], slice[j] = slice[j], slice[i]
	})
	return slice
}

func GetBusCompanyNowMonth() (month string, startAt, endAt time.Time) {
	now := time.Now()
	splitDay := time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local)
	if now.Unix() >= splitDay.Unix() {
		month = now.Format("200601")
		startAt = time.Date(now.Year(), now.Month()-1, 26, 0, 0, 0, 0, time.Local)
		endAt = time.Date(now.Year(), now.Month(), 25, 0, 0, 0, 0, time.Local)
	} else {
		month = time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local).Format("200601")
		startAt = time.Date(now.Year(), now.Month()-2, 26, 0, 0, 0, 0, time.Local)
		endAt = time.Date(now.Year(), now.Month()-1, 25, 0, 0, 0, 0, time.Local)
	}
	return
}

func GetBusCompanyStartAndEnd(month string, format string) (start, end string, startAt, endAt time.Time) {
	if format == "" {
		format = "2006-01"
	}
	now, _ := time.ParseInLocation(format, month, time.Local)
	startAt = time.Date(now.Year(), now.Month()-1, 26, 0, 0, 0, 0, time.Local)
	start = startAt.Format(format)
	endAt = time.Date(now.Year(), now.Month(), 25, 0, 0, 0, 0, time.Local)
	end = endAt.Format(format)
	return
}

// IsEmpty 判断是否为空值
func IsEmpty(val interface{}) bool {
	v := reflect.ValueOf(val)
	switch v.Kind() {
	case reflect.String:
		return v.Len() == 0
	case reflect.Ptr, reflect.Interface:
		return v.IsNil()
	case reflect.Slice, reflect.Array, reflect.Map:
		return v.Len() == 0
	case reflect.Bool:
		return !v.Bool()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return v.Int() == 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return v.Uint() == 0
	case reflect.Float32, reflect.Float64:
		return v.Float() == 0
	default:
		return true
	}
}
