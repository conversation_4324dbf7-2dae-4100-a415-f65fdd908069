package main

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/database"
	"app/org/scs/erpv2/api/handler/operation/management"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/middleware"
	"app/org/scs/erpv2/api/router"
	"app/org/scs/erpv2/api/scheduler"
	"app/org/scs/erpv2/api/util"
	"app/org/scs/erpv2/api/util/cache"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/micro/go-micro/v2"
	"github.com/micro/go-micro/v2/registry"
	"github.com/micro/go-micro/v2/server/grpc"
	"github.com/micro/go-plugins/registry/etcdv3/v2"
	"github.com/micro/go-plugins/wrapper/monitoring/prometheus/v2"
)

func main() {

	log.InitLogger(config.Config.DebugLevel)

	filename := flag.String("config", "", "config file")
	flag.Parse()

	if len(*filename) == 0 {
		flag.PrintDefaults()
		return
	}

	// 读取配置文件
	err := config.InitConfig(*filename)
	if err != nil {
		fmt.Printf("load config file error: %v \n", err)
		return
	}

	//初始化雪花算法
	util.InitSnowFlakeWorker()

	// 初始化数据库
	err = database.DBConnection()
	if err != nil {
		log.Printf("init gorm database error: %v", err)
		return
	}

	// init http client.
	config.Global.HttpClient = &http.Client{Timeout: 10 * time.Second}

	// 初始化一个每隔4小时惰性清理过期键的 local cache
	cache.InitMemoryCache(4 * time.Hour)
	etcdAddr := fmt.Sprintf("%s:%d", config.Config.Etcd.Host, config.Config.Etcd.Port)

	reg := etcdv3.NewRegistry(func(op *registry.Options) {
		op.Addrs = []string{etcdAddr}
		op.Timeout = time.Duration(config.Config.Etcd.Interval) * time.Second
	})

	// New Service
	service := micro.NewService(
		micro.Name(config.Config.Micro.ServiceName),
		micro.RegisterTTL(time.Second*30),
		//micro.Address("*************:5678"),
		//micro.RegisterInterval(time.Second*10),
		micro.WrapHandler(middleware.Auth, middleware.CheckHasAccount, prometheus.NewHandlerWrapper(prometheus.ServiceName(config.Config.Micro.ServiceName))),
		micro.Version("latest"),
		micro.Registry(reg),
	)

	config.Global.MicroClient = service.Client()
	// Initialise service
	_ = service.Server().Init(grpc.MaxMsgSize(100 * 1024 * 1024))
	service.Init()

	//注册路由
	router.RegisterRouter(service)
	go func() {
		//自动迁移数据库
		if config.Config.EnableMigrateDatabase {
			go database.Migrate()
		}

		//同步权限
		go database.CheckAndAddPermission()
		//初始化调度任务
		go scheduler.InitJobScheduler()
		//启动时执行调度任务
		go scheduler.StartScheduler()

		go management.PosterTaskServe()

		//go command.TakeThirdIcCardTradeData()

		//go hr.SyncHistoryApplyLeaveTime()
		//go lineDriverReport.TakeIssData()
		//go hr.SyncHistoryQuitRecordInfo()
	}()

	//更新票务车队ID
	//go countmoney.UpdateFleet()
	//go safety.UpdateAccidentMessage()

	//go safety.UpdateTrafficAccidentCode()
	//go safety.FixHistoryLogger()

	go func() {
		sigs := make(chan os.Signal, 1)
		signal.Notify(sigs, os.Interrupt, os.Kill, syscall.SIGTERM) //kill pid, kill process name
		<-sigs
		log.Printf("Signal received: %v", sigs)
		releaseDbConn()
	}()
	// Run service
	if err := service.Run(); err != nil {
		log.Fatal(err)
	}
}

func releaseDbConn() {
	// for i, _ := range config.Config.DBP {
	conn, _ := config.Global.DbClient.DB()
	if nil != conn {
		conn.Close()
	}
}
