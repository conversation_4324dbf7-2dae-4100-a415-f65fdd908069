#!/bin/bash

APP_NAME=erp-api
basedir=$(cd `dirname $0`/; pwd)
DATE=$(date +%Y%m%d_%H%M)
LOG_DIR=$basedir/log

check_init() {
	echo
}

find_process() {
	PROCESS_ID=$(ps -ef | grep $APP_NAME |grep -v "grep" | awk '{print $2}')
}

app_start() {
	check_init
	chmod 700 $basedir/$APP_NAME
	mkdir -p $LOG_DIR
	nohup $basedir/$APP_NAME --config=$basedir/config.json > $LOG_DIR/$DATE.log 2>&1 &
}

app_stop() {
	check_init
	find_process
	if [ "$PROCESS_ID" != "" ]; then
		killall $APP_NAME
	fi
}

app_status() {
	check_init
	find_process
	
	echo "-------------------      $APP_NAME运行情况    -------------------"
	if [ "$PROCESS_ID" != "" ]; then
		echo "状态: 启动                    PID: $PROCESS_ID"
	else
		echo "状态: 停止"
		exit 1
	fi
}

case "$1" in
	start)
		app_start
		;;

	stop)
		app_stop
		;;

	restart)
		app_stop
		app_start
		;;

	status)
		app_status
		;;

	*)
		echo "Usage: $0 {start|stop|restart|status}"
		exit 1
		;;
esac

