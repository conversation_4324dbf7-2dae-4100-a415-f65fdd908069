{"Env": "local", "DebugLevel": "Info", "AbsDirPath": "/mnt/www/", "WebRoot": "webroot", "StaticFileHttpPrefix": "https://scs.oetlink.com:38085/", "IsRelationIss": true, "# 是否迁移数据表": "true迁移  false不迁移", "EnableMigrateDatabase": true, "Database": {"Host": "*************", "Port": 5432, "DbName": "erp-putuoshan", "User": "oet_erp", "Password": "pass123456"}, "Etcd": {"Host": "*************", "Port": 2379, "Interval": 10, "Ttl": 30}, "Micro": {"ServiceName": "oet.scs.api.erp.v2"}, "Lbpm": {"Enable": true, "SysId": "erp-tz", "AuthAccount": "ekp", "AuthPassword": "ekp", "RestApiAuthAccount": "ekp", "RestApiAuthPassword": "Tzgj@2021", "LbpmDomain": "http://oatest.tzgjjt.com:9005/", "ServicePrefix": "http://test.oetlink.com:48080/erp/v3/thirdpart/", "ToDoPrefixDomain": "http://*************:8060"}, "DingTalkBpm": {"Enable": false, "AgentId": **********, "AppKey": "dingvqp4hrzt2a4bgsua", "AppSecret": "Fimk9C9BW7KoGreyvZgMIiEqZ9u-mm9AMN7XCzC7pEafhQ0tp8J-pNnK_qwI-7rn", "AesKey": "cJh0fyYJVXJFPhoHkmF6MjktzfCcfJixVNevqhH73q8", "Token": "PzkmcEX3XVXxBmcGXLAC6kwkfNxDKmTsoy94xB"}, "PermissionSys": "local", "SyncMasterStaff": {"Enable": false, "CorporationIds": [1505961073969202177]}, "SchedulerJob": {"IsSendDoorCheckNotify": false, "IsSendLaborContractExpireNotify": false, "IsSendStaffProbationExpireNotify": false, "IsPreGenerateDoorCheckRecord": false, "IsCheckTicketDataPermission": false, "IsProcessAlarm": false, "IsCalcStaffArchiveReport": false, "# 是否同步主数据线路人员归属关系数据": "", "IsTakeOetLineHasDriverRecord": false, "# 是否计算员工的年休假": "", "IsCalcStaffAnnualLeave": false, "# 是否定时获取台州IC卡交易结算数据": "", "IsTakeTaizhouThirdIcCardTradeData": false}, "TopCorporationId": 1505961073969202177, "TicketBankCheckExcludeLine": ["231", "253", "232", "255", "259", "258", "851", "257", "251", "256", "265", "862", "863", "860", "861A", "861B", "861C"], "# 人员档案报表统计的机构ID": "", "StaffArchiveReportCorpId": 1728200467558695982, "# IC卡数据库连接配置": "", "ICMysqlDatabase": {"DbHost": "127.0.0.1:3306", "DbUser": "root", "DbPass": "123456", "DbName": "tzy<PERSON>"}}