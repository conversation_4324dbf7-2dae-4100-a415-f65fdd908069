package database

import (
	"app/org/scs/erpv2/api/config"
	protoPermission "app/org/scs/erpv2/api/proto/rpc/permission"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"strings"
)

var Permissions = []map[string]string{
	{"Entity": "文件分片上传", "ApiUrl": "/erp/v2/file/Upload", "Sys": ""},
	{"Entity": "机构列表", "ApiUrl": "/erp/v2/corporation/List", "Sys": ""},
	{"Entity": "机构人员列表", "ApiUrl": "/erp/v2/corporation/ListStaff", "Sys": ""},
	{"Entity": "机构列表配置", "ApiUrl": "/erp/v2/corporation/ListConfig", "Sys": ""},
	{"Entity": "车辆列表", "ApiUrl": "/erp/v2/vehicle/List", "Sys": ""},
	{"Entity": "全部车辆列表", "ApiUrl": "/erp/v2/vehicle/ListAll", "Sys": ""},
	{"Entity": "通过Code获取车辆", "ApiUrl": "/erp/v2/vehicle/GetLicenseByCode", "Sys": ""},
	{"Entity": "线路列表", "ApiUrl": "/erp/v2/line/List", "Sys": ""},
	{"Entity": "场站（停车场）列表", "ApiUrl": "/erp/v2/parking/List", "Sys": ""},
	{"Entity": "站点列表", "ApiUrl": "/erp/v2/station/List", "Sys": ""},
	{"Entity": "查询所有员工", "ApiUrl": "/erp/v2/staff/GetStaffByStaffIdStr", "Sys": ""},
	{"Entity": "查询账号所属员工", "ApiUrl": "/erp/v2/staff/List", "Sys": ""},

	{"Entity": "导入员工档案", "ApiUrl": "/erp/v2/staffarchive/Import", "Sys": "taizhou,daishan"},
	{"Entity": "员工档案列表", "ApiUrl": "/erp/v2/staffarchive/List", "Sys": "taizhou,daishan"},
	{"Entity": "员工档案导出", "ApiUrl": "/erp/v2/staffarchive/Export", "Sys": "taizhou,daishan"},
	{"Entity": "通过入职申请", "ApiUrl": "/erp/v2/staffarchive/SubmitApply", "Sys": "taizhou,daishan"},
	{"Entity": "新增员工档案", "ApiUrl": "/erp/v2/staffarchive/Create", "Sys": "taizhou,daishan"},
	{"Entity": "编辑员工档案信息", "ApiUrl": "/erp/v2/staffarchive/Edit", "Sys": "taizhou,daishan"},
	{"Entity": "员工档案详情", "ApiUrl": "/erp/v2/staffarchive/Info", "Sys": "taizhou,daishan"},
	{"Entity": "根据员工ID查询档案信息", "ApiUrl": "/erp/v2/staffarchive/StaffInfo", "Sys": "taizhou,daishan"},
	{"Entity": "员工档案修改日志记录", "ApiUrl": "/erp/v2/staffarchive/Logger", "Sys": "taizhou,daishan"},
	{"Entity": "员工社保记录", "ApiUrl": "/erp/v2/staffarchive/InsuranceRecord", "Sys": "taizhou,daishan"},
	{"Entity": "员工公积金记录", "ApiUrl": "/erp/v2/staffarchive/FundRecord", "Sys": "taizhou,daishan"},
	{"Entity": "我的档案信息", "ApiUrl": "/erp/v2/staffarchive/MineInfo", "Sys": "taizhou,daishan"},
	{"Entity": "编辑我的档案信息", "ApiUrl": "/erp/v2/staffarchive/EditMineInfo", "Sys": "taizhou,daishan"},
	{"Entity": "更新证件照", "ApiUrl": "/erp/v2/staffarchive/UpdateHeadImg", "Sys": "taizhou"},

	{"Entity": "查看个人信息", "ApiUrl": "Staffarchive.BasicInfo", "Sys": "taizhou,daishan"},
	{"Entity": "编辑个人信息", "ApiUrl": "Staffarchive.Edit.BasicInfo", "Sys": "taizhou,daishan"},
	{"Entity": "导出个人信息", "ApiUrl": "Staffarchive.Export.BasicInfo", "Sys": "taizhou,daishan"},

	{"Entity": "查看学历信息", "ApiUrl": "Staffarchive.Education", "Sys": "taizhou,daishan"},
	{"Entity": "编辑学历信息", "ApiUrl": "Staffarchive.Edit.Education", "Sys": "taizhou,daishan"},
	{"Entity": "导出学历信息", "ApiUrl": "Staffarchive.Export.Education", "Sys": "taizhou,daishan"},

	{"Entity": "查看职称信息", "ApiUrl": "Staffarchive.PositionalTitle", "Sys": "taizhou,daishan"},
	{"Entity": "编辑职称信息", "ApiUrl": "Staffarchive.Edit.PositionalTitle", "Sys": "taizhou,daishan"},
	{"Entity": "导出职称信息", "ApiUrl": "Staffarchive.Export.PositionalTitle", "Sys": "taizhou,daishan"},

	{"Entity": "查看技能信息", "ApiUrl": "Staffarchive.Skill", "Sys": "taizhou,daishan"},
	{"Entity": "编辑技能信息", "ApiUrl": "Staffarchive.Edit.Skill", "Sys": "taizhou,daishan"},
	{"Entity": "导出技能信息", "ApiUrl": "Staffarchive.Export.Skill", "Sys": "taizhou,daishan"},

	{"Entity": "查看家庭信息", "ApiUrl": "Staffarchive.FamilyMember", "Sys": "taizhou,daishan"},
	{"Entity": "编辑家庭信息", "ApiUrl": "Staffarchive.Edit.FamilyMember", "Sys": "taizhou,daishan"},
	{"Entity": "导出家庭信息", "ApiUrl": "Staffarchive.Export.FamilyMember", "Sys": "taizhou,daishan"},

	{"Entity": "查看从业资格证", "ApiUrl": "Staffarchive.Certificate", "Sys": "taizhou,daishan"},
	{"Entity": "编辑从业资格证", "ApiUrl": "Staffarchive.Edit.Certificate", "Sys": "taizhou,daishan"},
	{"Entity": "导出从业资格证", "ApiUrl": "Staffarchive.Export.Certificate", "Sys": "taizhou,daishan"},

	{"Entity": "查看在职信息", "ApiUrl": "Staffarchive.JobInfo", "Sys": "taizhou,daishan"},
	{"Entity": "编辑在职信息", "ApiUrl": "Staffarchive.Edit.JobInfo", "Sys": "taizhou,daishan"},
	{"Entity": "导出在职信息", "ApiUrl": "Staffarchive.Export.JobInfo", "Sys": "taizhou,daishan"},

	{"Entity": "查看职务信息", "ApiUrl": "Staffarchive.WorkPost", "Sys": "taizhou,daishan"},
	{"Entity": "编辑职务信息", "ApiUrl": "Staffarchive.Edit.WorkPost", "Sys": "taizhou,daishan"},
	{"Entity": "导出职务信息", "ApiUrl": "Staffarchive.Export.WorkPost", "Sys": "taizhou,daishan"},

	{"Entity": "查看劳动合同", "ApiUrl": "Staffarchive.LaborContract", "Sys": "taizhou,daishan"},
	{"Entity": "编辑劳动合同", "ApiUrl": "Staffarchive.Edit.LaborContract", "Sys": "taizhou,daishan"},
	{"Entity": "导出劳动合同", "ApiUrl": "Staffarchive.Export.LaborContract", "Sys": "taizhou,daishan"},

	{"Entity": "查看党群资料", "ApiUrl": "Staffarchive.PartyData", "Sys": "taizhou,daishan"},
	{"Entity": "编辑党群资料", "ApiUrl": "Staffarchive.Edit.PartyData", "Sys": "taizhou,daishan"},

	//{"Entity": "查看廉政档案", "ApiUrl": "Staffarchive.IntegrityData"},
	{"Entity": "查看惩处记录", "ApiUrl": "Staffarchive.PunishmentRecord", "Sys": "taizhou,daishan"},
	{"Entity": "编辑惩处记录", "ApiUrl": "Staffarchive.Edit.PunishmentRecord", "Sys": "taizhou,daishan"},
	{"Entity": "查看奖励记录", "ApiUrl": "Staffarchive.RewardRecord", "Sys": "taizhou,daishan"},
	{"Entity": "编辑奖励记录", "ApiUrl": "Staffarchive.Edit.RewardRecord", "Sys": "taizhou,daishan"},

	{"Entity": "添加入职人员", "ApiUrl": "/erp/v2/joincompanyapply/BatchCreateStaff", "Sys": "taizhou,daishan"},
	{"Entity": "入职管理列表", "ApiUrl": "/erp/v2/joincompanyapply/List", "Sys": "taizhou,daishan"},
	{"Entity": "删除入职人员", "ApiUrl": "/erp/v2/joincompanyapply/Delete", "Sys": "taizhou,daishan"},
	{"Entity": "查看入职详情", "ApiUrl": "/erp/v2/joincompanyapply/ApplyInfo", "Sys": "taizhou,daishan"},

	{"Entity": "岗位管理列表", "ApiUrl": "/erp/v2/workpost/List", "Sys": "taizhou,daishan"},
	{"Entity": "岗位管理添加", "ApiUrl": "/erp/v2/workpost/Create", "Sys": "taizhou,daishan"},
	{"Entity": "岗位管理编辑", "ApiUrl": "/erp/v2/workpost/Edit", "Sys": "taizhou,daishan"},
	{"Entity": "岗位管理设置状态", "ApiUrl": "/erp/v2/workpost/SwitchStatus", "Sys": "taizhou,daishan"},
	{"Entity": "岗位选择列表", "ApiUrl": "/erp/v2/workpost/SelectList", "Sys": "taizhou,daishan"},

	{"Entity": "员工调岗列表", "ApiUrl": "/erp/v2/stafftransfer/List", "Sys": "taizhou,daishan"},
	{"Entity": "新增员工调岗", "ApiUrl": "/erp/v2/stafftransfer/Create", "Sys": "taizhou,daishan"},
	{"Entity": "员工调动详情", "ApiUrl": "/erp/v2/stafftransfer/Show", "Sys": "taizhou,daishan"},
	{"Entity": "员工调动删除", "ApiUrl": "/erp/v2/stafftransfer/Delete", "Sys": "taizhou,daishan"},

	{"Entity": "司机调动列表", "ApiUrl": "/erp/v2/drivermigration/List", "Sys": "taizhou"},
	{"Entity": "司机调动添加", "ApiUrl": "/erp/v2/drivermigration/Create", "Sys": "taizhou"},
	{"Entity": "司机调动详情", "ApiUrl": "/erp/v2/drivermigration/Show", "Sys": "taizhou"},
	{"Entity": "司机调动更新线路", "ApiUrl": "/erp/v2/drivermigration/UpdateInfo", "Sys": "taizhou"},
	{"Entity": "司机调动撤销", "ApiUrl": "/erp/v2/drivermigration/UpdateCancelStatus", "Sys": "taizhou"},

	{"Entity": "员工离职列表", "ApiUrl": "/erp/v2/staffquit/List", "Sys": "taizhou,daishan"},
	{"Entity": "员工离职详情", "ApiUrl": "/erp/v2/staffquit/Show", "Sys": "taizhou"},
	{"Entity": "新增员工离职", "ApiUrl": "/erp/v2/staffquit/Create", "Sys": "taizhou,daishan"},
	{"Entity": "发起员工离职申请", "ApiUrl": "/erp/v2/staffquit/DispatchProcess", "Sys": "daishan"},
	{"Entity": "离职回档", "ApiUrl": "/erp/v2/staffquit/Rollback", "Sys": "taizhou"},

	{"Entity": "退休员工列表", "ApiUrl": "/erp/v2/staffretire/List", "Sys": "taizhou,daishan"},
	{"Entity": "退休详情", "ApiUrl": "/erp/v2/staffretire/Show", "Sys": "taizhou"},
	{"Entity": "在职人员列表", "ApiUrl": "/erp/v2/staffretire/WorkingList", "Sys": "taizhou,daishan"},
	{"Entity": "批量退休审批", "ApiUrl": "/erp/v2/staffretire/BatchRetireSubmit", "Sys": "taizhou,daishan"},
	{"Entity": "退休审批", "ApiUrl": "/erp/v2/staffretire/RetireSubmit", "Sys": "taizhou,daishan"},
	{"Entity": "提前退休", "ApiUrl": "/erp/v2/staffretire/EarlyRetireSubmit", "Sys": "taizhou,daishan"},
	{"Entity": "新增退休返聘记录", "ApiUrl": "/erp/v2/staffretire/AddRetireWork", "Sys": "taizhou,daishan"},
	{"Entity": "解除返聘", "ApiUrl": "/erp/v2/staffretire/RelieveRetireWork", "Sys": "taizhou,daishan"},
	{"Entity": "退休回档", "ApiUrl": "/erp/v2/staffretire/Rollback", "Sys": "taizhou"},

	{"Entity": "员工历史版本列表", "ApiUrl": "/erp/v2/staffarchiveversion/List", "Sys": "taizhou,daishan"},
	{"Entity": "新增历史版本", "ApiUrl": "/erp/v2/staffarchiveversion/Create", "Sys": "taizhou,daishan"},
	{"Entity": "历史版本对应的员工记录", "ApiUrl": "/erp/v2/staffarchiveversion/StaffRecord", "Sys": "taizhou,daishan"},
	{"Entity": "导出员工记录", "ApiUrl": "/erp/v2/staffarchiveversion/Export", "Sys": "taizhou,daishan"},

	{"Entity": "一人一档运营档案", "ApiUrl": "/erp/v2/driverarchive/DriverOperate", "Sys": "taizhou,daishan"},
	{"Entity": "一人一档安全档案", "ApiUrl": "/erp/v2/driverarchive/DriverSafety", "Sys": "taizhou,daishan"},
	{"Entity": "违章违规列表", "ApiUrl": "/erp/v2/driverarchive/ViolationIllegalList", "Sys": "taizhou,daishan"},
	{"Entity": "安全事故列表", "ApiUrl": "/erp/v2/driverarchive/AccidentList", "Sys": "taizhou,daishan"},

	//人资2.0
	{"Entity": "试用期列表", "ApiUrl": "/erp/v2/probation/List", "Sys": "taizhou,daishan"},
	{"Entity": "试用期转正", "ApiUrl": "/erp/v2/probation/BecomeWorker", "Sys": "taizhou,daishan"},
	{"Entity": "试用期延长", "ApiUrl": "/erp/v2/probation/ExtendProbation", "Sys": "taizhou,daishan"},
	{"Entity": "试用期转正流程发起", "ApiUrl": "/erp/v2/probation/BecomeWorkerProcess", "Sys": "taizhou"},
	{"Entity": "试用期转正记录详情", "ApiUrl": "/erp/v2/probation/ShowBecomeWorkerProcess", "Sys": "taizhou"},
	{"Entity": "试用期转正流程列表", "ApiUrl": "/erp/v2/probation/BecomeWorkerProcessList", "Sys": "taizhou"},
	{"Entity": "试用期转正流程审批数据更新", "ApiUrl": "/erp/v2/probation/BecomeWorkerProcessUpdate", "Sys": "taizhou"},

	{"Entity": "劳动合同导入", "ApiUrl": "/erp/v2/laborcontract/Import", "Sys": "taizhou,daishan"},
	{"Entity": "劳动合同列表", "ApiUrl": "/erp/v2/laborcontract/List", "Sys": "taizhou,daishan"},
	{"Entity": "劳动合同添加", "ApiUrl": "/erp/v2/laborcontract/Create", "Sys": "taizhou,daishan"},
	{"Entity": "劳动合同编辑", "ApiUrl": "/erp/v2/laborcontract/Edit", "Sys": "taizhou,daishan"},
	{"Entity": "劳动合同解除", "ApiUrl": "/erp/v2/laborcontract/Relieve", "Sys": "taizhou,daishan"},

	{"Entity": "劳动合同到期提醒记录", "ApiUrl": "/erp/v2/scheduler/GetLaborContractExpireNotify", "Sys": "taizhou,daishan"},
	{"Entity": "试用期到期提醒记录", "ApiUrl": "/erp/v2/scheduler/GetProbationExpireNotify", "Sys": "taizhou,daishan"},
	{"Entity": "设置劳动合同到期提醒", "ApiUrl": "/erp/v2/scheduler/AddLaborContractExpireNotify", "Sys": "taizhou,daishan"},
	{"Entity": "设置试用期到期提醒", "ApiUrl": "/erp/v2/scheduler/AddProbationExpireNotify", "Sys": "taizhou,daishan"},
	{"Entity": "编辑劳动合同到期提醒", "ApiUrl": "/erp/v2/scheduler/EditLaborContractExpireNotify", "Sys": "taizhou,daishan"},
	{"Entity": "编辑试用期到期提醒", "ApiUrl": "/erp/v2/scheduler/EditProbationExpireNotify", "Sys": "taizhou,daishan"},

	{"Entity": "社保缴纳比例设置", "ApiUrl": "/erp/v2/insurance/Setting", "Sys": "taizhou,daishan"},
	{"Entity": "社保缴纳比例详情", "ApiUrl": "/erp/v2/insurance/SettingInfo", "Sys": "taizhou,daishan"},
	{"Entity": "社保导入", "ApiUrl": "/erp/v2/insurance/Import", "Sys": "taizhou,daishan"},
	{"Entity": "社保导出", "ApiUrl": "/erp/v2/insurance/Export", "Sys": "taizhou,daishan"},
	{"Entity": "补充医疗列表", "ApiUrl": "/erp/v2/insurance/ExtraMedialList", "Sys": "taizhou,daishan"},
	{"Entity": "企业年金列表", "ApiUrl": "/erp/v2/insurance/AnnuityList", "Sys": "taizhou,daishan"},
	{"Entity": "社保列表", "ApiUrl": "/erp/v2/insurance/List", "Sys": "taizhou,daishan"},
	{"Entity": "社保添加", "ApiUrl": "/erp/v2/insurance/Create", "Sys": "taizhou,daishan"},
	{"Entity": "社保编辑", "ApiUrl": "/erp/v2/insurance/InsuranceEdit", "Sys": "taizhou,daishan"},
	{"Entity": "社保批量编辑", "ApiUrl": "/erp/v2/insurance/InsuranceBatchEdit", "Sys": "taizhou,daishan"},
	{"Entity": "企业年金编辑", "ApiUrl": "/erp/v2/insurance/AnnuityEdit", "Sys": "taizhou,daishan"},
	{"Entity": "企业年金批量编辑", "ApiUrl": "/erp/v2/insurance/AnnuityBatchEdit", "Sys": "taizhou,daishan"},
	{"Entity": "补充医疗编辑", "ApiUrl": "/erp/v2/insurance/ExtraMedicalEdit", "Sys": "taizhou,daishan"},
	{"Entity": "补充医疗批量编辑", "ApiUrl": "/erp/v2/insurance/ExtraMedicalBatchEdit", "Sys": "taizhou,daishan"},
	{"Entity": "社保删除", "ApiUrl": "/erp/v2/insurance/Delete", "Sys": "taizhou,daishan"},
	{"Entity": "补充医疗删除", "ApiUrl": "/erp/v2/insurance/ExtraMedialDelete", "Sys": "taizhou,daishan"},
	{"Entity": "企业年金删除", "ApiUrl": "/erp/v2/insurance/AnnuityDelete", "Sys": "taizhou,daishan"},

	{"Entity": "公积金缴纳比例设置", "ApiUrl": "/erp/v2/fund/Setting", "Sys": "taizhou,daishan"},
	{"Entity": "公积金缴纳比例详情", "ApiUrl": "/erp/v2/fund/SettingInfo", "Sys": "taizhou,daishan"},
	{"Entity": "公积金导入", "ApiUrl": "/erp/v2/fund/Import", "Sys": "taizhou,daishan"},
	{"Entity": "公积金导出", "ApiUrl": "/erp/v2/fund/Export", "Sys": "taizhou,daishan"},
	{"Entity": "公积金列表", "ApiUrl": "/erp/v2/fund/List", "Sys": "taizhou,daishan"},
	{"Entity": "公积金添加", "ApiUrl": "/erp/v2/fund/Create", "Sys": "taizhou,daishan"},
	{"Entity": "公积金编辑", "ApiUrl": "/erp/v2/fund/Edit", "Sys": "taizhou,daishan"},
	{"Entity": "公积金批量编辑", "ApiUrl": "/erp/v2/fund/BatchEdit", "Sys": "taizhou,daishan"},
	{"Entity": "公积金删除", "ApiUrl": "/erp/v2/fund/Delete", "Sys": "taizhou,daishan"},

	{"Entity": "职称补贴申请列表", "ApiUrl": "/erp/v2/positionaltitleapply/List", "Sys": "taizhou"},
	{"Entity": "职称补贴申请添加", "ApiUrl": "/erp/v2/positionaltitleapply/Create", "Sys": "taizhou"},
	{"Entity": "职称补贴申请审批", "ApiUrl": "/erp/v2/positionaltitleapply/Approval", "Sys": "taizhou"},
	{"Entity": "职称补贴申请详情", "ApiUrl": "/erp/v2/positionaltitleapply/Show", "Sys": "taizhou"},

	{"Entity": "人员考核导入", "ApiUrl": "/erp/v2/staffassessment/Import", "Sys": "taizhou"},
	{"Entity": "人员考核导出", "ApiUrl": "/erp/v2/staffassessment/Export", "Sys": "taizhou"},
	{"Entity": "人员考核列表", "ApiUrl": "/erp/v2/staffassessment/List", "Sys": "taizhou"},
	{"Entity": "人员考核添加", "ApiUrl": "/erp/v2/staffassessment/Create", "Sys": "taizhou"},

	{"Entity": "培训列表", "ApiUrl": "/erp/v2/worktrain/List", "Sys": "taizhou"},
	{"Entity": "人员培训列表", "ApiUrl": "/erp/v2/worktrain/StaffTrainList", "Sys": "taizhou"},
	{"Entity": "新增培训", "ApiUrl": "/erp/v2/worktrain/Create", "Sys": "taizhou"},
	{"Entity": "编辑培训", "ApiUrl": "/erp/v2/worktrain/Edit", "Sys": "taizhou"},
	{"Entity": "培训参加的人员管理", "ApiUrl": "/erp/v2/worktrain/AttendStaff", "Sys": "taizhou"},
	{"Entity": "添加培训人员", "ApiUrl": "/erp/v2/worktrain/AddAttendStaff", "Sys": "taizhou"},
	{"Entity": "批量编辑培训人员", "ApiUrl": "/erp/v2/worktrain/EditAttendStaff", "Sys": "taizhou"},
	{"Entity": "删除培训人员", "ApiUrl": "/erp/v2/worktrain/DeleteAttendStaff", "Sys": "taizhou"},

	{"Entity": "人员信息分布", "ApiUrl": "/erp/v2/staffreport/InfoDistribute", "Sys": "daishan"},
	{"Entity": "入职人员统计", "ApiUrl": "/erp/v2/staffreport/ListJoin", "Sys": "daishan"},
	{"Entity": "离职人员统计", "ApiUrl": "/erp/v2/staffreport/ListQuit", "Sys": "daishan"},
	{"Entity": "社保人员统计", "ApiUrl": "/erp/v2/staffreport/ListInsurance", "Sys": "daishan"},
	{"Entity": "公积金人员统计", "ApiUrl": "/erp/v2/staffreport/ListFund", "Sys": "daishan"},

	{"Entity": "人员档案报表", "ApiUrl": "/erp/v2/staffreport/StaffArchiveReport", "Sys": "taizhou"},
	{"Entity": "人员档案报表导出", "ApiUrl": "/erp/v2/staffreport/StaffArchiveReportExport", "Sys": "taizhou"},
	{"Entity": "手动计算人员档案报表数据", "ApiUrl": "/erp/v2/staffreport/StaffArchiveReportCalc", "Sys": "taizhou"},

	{"Entity": "人员画像", "ApiUrl": "/erp/v2/staffreport/StaffPortrait", "Sys": "taizhou"},
	{"Entity": "人员画像-事故列表", "ApiUrl": "/erp/v2/staffreport/StaffPortraitAccidentList", "Sys": "taizhou"},
	{"Entity": "人员画像-违规列表", "ApiUrl": "/erp/v2/staffreport/StaffPortraitViolationList", "Sys": "taizhou"},
	{"Entity": "人员画像-工单列表", "ApiUrl": "/erp/v2/staffreport/StaffPortraitWorkOrderList", "Sys": "taizhou"},
	{"Entity": "一票否决添加", "ApiUrl": "/erp/v2/staffreport/StaffPortraitVetoCreate", "Sys": "taizhou"},
	{"Entity": "一票否决变更", "ApiUrl": "/erp/v2/staffreport/StaffPortraitVetoChange", "Sys": "taizhou"},
	{"Entity": "一票否决列表", "ApiUrl": "/erp/v2/staffreport/StaffPortraitVetoList", "Sys": "taizhou"},

	{"Entity": "检查人员创建", "ApiUrl": "/erp/v2/doorcheck/AddInspector", "Sys": "taizhou"},
	{"Entity": "检查人员列表", "ApiUrl": "/erp/v2/doorcheck/ListInspector", "Sys": "taizhou"},
	{"Entity": "检查人员编辑", "ApiUrl": "/erp/v2/doorcheck/EditInspector", "Sys": "taizhou"},

	{"Entity": "请假申请列表", "ApiUrl": "/erp/v2/staffleave/ApplyList", "Sys": "taizhou"},
	{"Entity": "发起请假申请", "ApiUrl": "/erp/v2/staffleave/Create", "Sys": "taizhou"},
	{"Entity": "发起请假修改", "ApiUrl": "/erp/v2/staffleave/Edit", "Sys": "taizhou"},
	{"Entity": "请假详情", "ApiUrl": "/erp/v2/staffleave/Show", "Sys": "taizhou"},
	{"Entity": "请假作废", "ApiUrl": "/erp/v2/staffleave/Scrap", "Sys": "taizhou"},
	{"Entity": "年休假列表", "ApiUrl": "/erp/v2/staffleave/LeaveList", "Sys": "taizhou"},
	{"Entity": "导出年休假列表", "ApiUrl": "/erp/v2/staffleave/LeaveListExport", "Sys": "taizhou"},
	{"Entity": "年休假编辑", "ApiUrl": "/erp/v2/staffleave/LeaveEdit", "Sys": "taizhou"},
	{"Entity": "年休假规则", "ApiUrl": "/erp/v2/staffleave/ShowLeaveRule", "Sys": "taizhou"},
	{"Entity": "年休假规则设置", "ApiUrl": "/erp/v2/staffleave/SetLeaveRule", "Sys": "taizhou"},
	{"Entity": "请假汇总表", "ApiUrl": "/erp/v2/staffleave/StaffLeaveDayReport", "Sys": "taizhou"},
	{"Entity": "汇总明细", "ApiUrl": "/erp/v2/staffleave/StaffLeaveTypeRecord", "Sys": "taizhou"},

	//{"Entity": "自定义门检项目创建", "ApiUrl": "/erp/v2/doorcheck/Add", "Sys": "taizhou"},
	//{"Entity": "自定义门检项目编辑", "ApiUrl": "/erp/v2/doorcheck/Edit", "Sys": "taizhou"},
	//{"Entity": "自定义门检项目删除", "ApiUrl": "/erp/v2/doorcheck/Delete", "Sys": "taizhou"},
	//{"Entity": "自定义门检项目列表", "ApiUrl": "/erp/v2/doorcheck/List", "Sys": "taizhou"},
	//{"Entity": "门检类别新增", "ApiUrl": "/erp/v2/doorcheck/AddItemType", "Sys": "taizhou"},
	//{"Entity": "门检类别编辑", "ApiUrl": "/erp/v2/doorcheck/EditItemType", "Sys": "taizhou"},
	//{"Entity": "门检类别删除", "ApiUrl": "/erp/v2/doorcheck/DeleteItemType", "Sys": "taizhou"},
	//{"Entity": "门检结果新增", "ApiUrl": "/erp/v2/doorcheck/AddResult", "Sys": "taizhou"},
	//{"Entity": "门检结果新增(代录入)", "ApiUrl": "/erp/v2/doorcheck/AddResultSubstitute", "Sys": "taizhou"},
	//{"Entity": "门检结果web列表", "ApiUrl": "/erp/v2/doorcheck/ListWebResult", "Sys": "taizhou"},
	//{"Entity": "门检结果app列表", "ApiUrl": "/erp/v2/doorcheck/ListAppResult", "Sys": "taizhou"},
	//{"Entity": "门检结果详情", "ApiUrl": "/erp/v2/doorcheck/GetResultDetail", "Sys": "taizhou"},
	//{"Entity": "门检结果app详情", "ApiUrl": "/erp/v2/doorcheck/GetAppResultDetail", "Sys": "taizhou"},
	//{"Entity": "门检结果整改", "ApiUrl": "/erp/v2/doorcheck/EditResult", "Sys": "taizhou"},
	//{"Entity": "门检结果关闭重复", "ApiUrl": "/erp/v2/doorcheck/RepeatResult", "Sys": "taizhou"},
	//{"Entity": "门检结果激活关闭的重复", "ApiUrl": "/erp/v2/doorcheck/ActiveResult", "Sys": "taizhou"},
	//{"Entity": "门检结果上报", "ApiUrl": "/erp/v2/doorcheck/ReportResult", "Sys": "taizhou"},
	//{"Entity": "门检结果上报修改", "ApiUrl": "/erp/v2/doorcheck/EditReportResult", "Sys": "taizhou"},
	//{"Entity": "门检项目(待检查)列表", "ApiUrl": "/erp/v2/doorcheck/TodoList", "Sys": "taizhou"},
	//{"Entity": "门检项目(待检查)列表(代录入)", "ApiUrl": "/erp/v2/doorcheck/TodoListSubstitute", "Sys": "taizhou"},
	//{"Entity": "过渡页司机是否门检", "ApiUrl": "/erp/v2/doorcheck/IsChecked", "Sys": "taizhou"},
	//{"Entity": "门检结果处理", "ApiUrl": "/erp/v2/doorcheck/ProcessResult", "Sys": "taizhou"},
	//{"Entity": "门检结果处理修改", "ApiUrl": "/erp/v2/doorcheck/EditProcessResult", "Sys": "taizhou"},

	{"Entity": "门检结果", "ApiUrl": "/erp/v2/vehiclecheck/List", "Sys": "taizhou"},
	{"Entity": "门检结果导出", "ApiUrl": "/erp/v2/vehiclecheck/ListExport", "Sys": "taizhou"},
	{"Entity": "门检异常结果", "ApiUrl": "/erp/v2/vehiclecheck/AbnormalList", "Sys": "taizhou"},
	{"Entity": "门检异常结果导出", "ApiUrl": "/erp/v2/vehiclecheck/AbnormalListExport", "Sys": "taizhou"},
	{"Entity": "门检结果详情", "ApiUrl": "/erp/v2/vehiclecheck/Show", "Sys": "taizhou"},

	{"Entity": "事故管理列表", "ApiUrl": "/erp/v2/trafficaccident/List", "Sys": "taizhou"},
	{"Entity": "事故管理列表导出", "ApiUrl": "/erp/v2/trafficaccident/Export", "Sys": "taizhou"},
	{"Entity": "事故上报", "ApiUrl": "/erp/v2/trafficaccident/Create", "Sys": "taizhou"},
	{"Entity": "事故基本详情", "ApiUrl": "/erp/v2/trafficaccident/Show", "Sys": "taizhou"},
	{"Entity": "事故编辑", "ApiUrl": "/erp/v2/trafficaccident/Edit", "Sys": "taizhou"},
	{"Entity": "事故删除", "ApiUrl": "/erp/v2/trafficaccident/Delete", "Sys": "taizhou"},
	{"Entity": "事故借款记录", "ApiUrl": "/erp/v2/trafficaccident/LendMoneyRecord", "Sys": "taizhou"},
	{"Entity": "事故当事人列表", "ApiUrl": "/erp/v2/trafficaccident/ListRelater", "Sys": "taizhou"},
	{"Entity": "事故当事人列表（用于下拉选择）", "ApiUrl": "/erp/v2/trafficaccident/ListRelaterForSelect", "Sys": "taizhou"},
	{"Entity": "事故当事人添加", "ApiUrl": "/erp/v2/trafficaccident/CreateRelater", "Sys": "taizhou"},
	{"Entity": "事故当事人详情", "ApiUrl": "/erp/v2/trafficaccident/ShowRelater", "Sys": "taizhou"},
	{"Entity": "事故当事人编辑", "ApiUrl": "/erp/v2/trafficaccident/EditRelater", "Sys": "taizhou"},
	{"Entity": "删除事故当事人", "ApiUrl": "/erp/v2/trafficaccident/DeleteRelater", "Sys": "taizhou"},
	{"Entity": "事故认定附件添加", "ApiUrl": "/erp/v2/trafficaccident/UploadConfirmFile", "Sys": "taizhou"},
	{"Entity": "事故当事人借款记录", "ApiUrl": "/erp/v2/trafficaccident/RelaterLendMoneyRecord", "Sys": "taizhou"},
	{"Entity": "事故分支添加", "ApiUrl": "/erp/v2/trafficaccident/CreateBranch", "Sys": "taizhou"},
	{"Entity": "事故分支删除", "ApiUrl": "/erp/v2/trafficaccident/DeleteBranch", "Sys": "taizhou"},
	{"Entity": "事故分支列表", "ApiUrl": "/erp/v2/trafficaccident/ListBranch", "Sys": "taizhou"},
	{"Entity": "事故分支详情", "ApiUrl": "/erp/v2/trafficaccident/ShowBranch", "Sys": "taizhou"},
	{"Entity": "发起事故借款申请", "ApiUrl": "/erp/v2/trafficaccident/ApplyLendMoney", "Sys": "taizhou"},
	{"Entity": "事故借款申请详情", "ApiUrl": "/erp/v2/trafficaccident/ShowLendMoneyApply", "Sys": "taizhou"},
	{"Entity": "申请事故分支结案", "ApiUrl": "/erp/v2/trafficaccident/ApplyBranchClose", "Sys": "taizhou"},
	{"Entity": "编辑事故分支结案表单数据", "ApiUrl": "/erp/v2/trafficaccident/EditApplyBranchClose", "Sys": "taizhou"},
	{"Entity": "申请事故结案", "ApiUrl": "/erp/v2/trafficaccident/ApplyClose", "Sys": "taizhou"},
	{"Entity": "编辑事故结案表单数据", "ApiUrl": "/erp/v2/trafficaccident/EditApplyClose", "Sys": "taizhou"},
	{"Entity": "事故操作日志", "ApiUrl": "/erp/v2/trafficaccident/Logger", "Sys": "taizhou"},
	{"Entity": "事故安委会审核", "ApiUrl": "/erp/v2/trafficaccident/SafeCheckHandle", "Sys": "taizhou"},

	{"Entity": "事故分支借款记录", "ApiUrl": "/erp/v2/trafficaccident/BranchLendMoneyRecord", "Sys": "taizhou"},
	{"Entity": "事故退款申请", "ApiUrl": "/erp/v2/trafficaccident/ApplyDrawbackMoney", "Sys": "taizhou"},
	{"Entity": "事故退款详情", "ApiUrl": "/erp/v2/trafficaccident/ShowDrawbackMoneyApply", "Sys": "taizhou"},
	{"Entity": "事故付款申请", "ApiUrl": "/erp/v2/trafficaccident/ApplyPaymentMoney", "Sys": "taizhou"},
	{"Entity": "事故付款详情", "ApiUrl": "/erp/v2/trafficaccident/ShowPaymentMoneyApply", "Sys": "taizhou"},
	{"Entity": "更新事故付款信息", "ApiUrl": "/erp/v2/trafficaccident/UpdatePaymentMoneyApply", "Sys": "taizhou"},
	{"Entity": "事故的付款记录", "ApiUrl": "/erp/v2/trafficaccident/ShowPaymentRecord", "Sys": "taizhou"},
	{"Entity": "更新分支的付款记录", "ApiUrl": "/erp/v2/trafficaccident/ShowBranchPaymentRecord", "Sys": "taizhou"},
	{"Entity": "分支详情", "ApiUrl": "/erp/v2/trafficaccident/ShowBranchDetail", "Sys": "taizhou"},
	{"Entity": "结案事故已打印", "ApiUrl": "/erp/v2/trafficaccident/ClosePrinted", "Sys": "taizhou"},
	{"Entity": "事故流程处理列表", "ApiUrl": "/erp/v2/trafficaccident/ListHandle", "Sys": "taizhou"},
	{"Entity": "事故流程处理全部已读", "ApiUrl": "/erp/v2/trafficaccident/ReadAllHandle", "Sys": "taizhou"},
	{"Entity": "事故流程处理未读数量", "ApiUrl": "/erp/v2/trafficaccident/UnReadList", "Sys": "taizhou"},
	{"Entity": "查询事故是否存在其他分支", "ApiUrl": "/erp/v2/trafficaccident/IsExistsOtherBranch", "Sys": "taizhou"},
	{"Entity": "自动分支结案", "ApiUrl": "/erp/v2/trafficaccident/AutoBranchClose", "Sys": "taizhou"},
	{"Entity": "安全机构编号设置列表", "ApiUrl": "/erp/v2/trafficaccident/ListSettingCorporationCode", "Sys": "taizhou"},
	{"Entity": "安全机构编号设置编辑", "ApiUrl": "/erp/v2/trafficaccident/EditSettingCorporationCode", "Sys": "taizhou"},
	{"Entity": "同一辆车同一天是否发起事故", "ApiUrl": "/erp/v2/trafficaccident/IsAccidentLicenseDateDriver", "Sys": "taizhou"},
	{"Entity": "同一分支同一金额借款", "ApiUrl": "/erp/v2/trafficaccident/IsLendBranchMoney", "Sys": "taizhou"},
	{"Entity": "事故移入回收站", "ApiUrl": "/erp/v2/trafficaccident/AddRecycle", "Sys": "taizhou"},
	{"Entity": "事故回收站列表", "ApiUrl": "/erp/v2/trafficaccident/ListRecycle", "Sys": "taizhou"},
	{"Entity": "事故处理", "ApiUrl": "/erp/v2/trafficaccident/HandleProcess", "Sys": "taizhou"}, //接口未实现，仅作控制权限之用

	{"Entity": "事故停车记录", "ApiUrl": "/erp/v2/trafficaccident/VehicleAbnormalStopRecord", "Sys": "taizhou"},
	{"Entity": "车辆的事故停车记录", "ApiUrl": "/erp/v2/trafficaccident/VehicleAbnormalStopRecordByVehicleId", "Sys": "taizhou"},
	{"Entity": "事故停车忽略", "ApiUrl": "/erp/v2/trafficaccident/VehicleAbnormalStopRecordIgnore", "Sys": "taizhou"},

	{"Entity": "事故年报", "ApiUrl": "/erp/v2/trafficaccident/YearReport", "Sys": "taizhou"},
	{"Entity": "事故按类别分组报表", "ApiUrl": "/erp/v2/trafficaccident/CateReport", "Sys": "taizhou"},
	{"Entity": "事故按车队分组报表", "ApiUrl": "/erp/v2/trafficaccident/FleetReport", "Sys": "taizhou"},
	{"Entity": "事故按分公司分组报表", "ApiUrl": "/erp/v2/trafficaccident/BranchCompanyReport", "Sys": "taizhou"},
	{"Entity": "事故按小时分组报表", "ApiUrl": "/erp/v2/trafficaccident/HourReport", "Sys": "taizhou"},
	{"Entity": "事故按天气分组报表", "ApiUrl": "/erp/v2/trafficaccident/WeatherReport", "Sys": "taizhou"},
	{"Entity": "事故发生地点报表", "ApiUrl": "/erp/v2/trafficaccident/HappenLocationReport", "Sys": "taizhou"},
	{"Entity": "事故发生地点报表导出", "ApiUrl": "/erp/v2/trafficaccident/HappenLocationReportExport", "Sys": "taizhou"},
	{"Entity": "分公司车队事故报表", "ApiUrl": "/erp/v2/trafficaccident/BranchFleetReport", "Sys": "taizhou"},
	{"Entity": "分公司车队事故报表导出", "ApiUrl": "/erp/v2/trafficaccident/BranchFleetReportExport", "Sys": "taizhou"},
	{"Entity": "机构事故明细", "ApiUrl": "/erp/v2/trafficaccident/CorporationAccidentList", "Sys": "taizhou"},
	{"Entity": "机构事故明细导出", "ApiUrl": "/erp/v2/trafficaccident/CorporationAccidentListExport", "Sys": "taizhou"},
	{"Entity": "机构事故金额明细", "ApiUrl": "/erp/v2/trafficaccident/CorporationAccidentMoneyList", "Sys": "taizhou"},
	{"Entity": "机构事故金额明细导出", "ApiUrl": "/erp/v2/trafficaccident/CorporationAccidentMoneyListExport", "Sys": "taizhou"},
	{"Entity": "机构事故预告警明细", "ApiUrl": "/erp/v2/trafficaccident/CorporationAccidentAlarmList", "Sys": "taizhou"},
	{"Entity": "机构事故预告警明细导出", "ApiUrl": "/erp/v2/trafficaccident/CorporationAccidentAlarmListExport", "Sys": "taizhou"},
	{"Entity": "机构事故流程预告警明细", "ApiUrl": "/erp/v2/trafficaccident/CorporationProcessAlarmList", "Sys": "taizhou"},
	{"Entity": "机构事故流程预告警明细导出", "ApiUrl": "/erp/v2/trafficaccident/CorporationProcessAlarmListExport", "Sys": "taizhou"},
	{"Entity": "无差额事故报表", "ApiUrl": "/erp/v2/trafficaccident/NotHasDiffMoneyReport", "Sys": "taizhou"},
	{"Entity": "无差额事故报表导出", "ApiUrl": "/erp/v2/trafficaccident/NotHasDiffMoneyReportExport", "Sys": "taizhou"},

	{"Entity": "车险导入", "ApiUrl": "/erp/v2/vehicleinsurance/Import", "Sys": "taizhou"},
	{"Entity": "车险导入PDF文件", "ApiUrl": "/erp/v2/vehicleinsurance/ImportPDF", "Sys": "taizhou"},
	{"Entity": "车险列表", "ApiUrl": "/erp/v2/vehicleinsurance/List", "Sys": "taizhou"},
	{"Entity": "车险导出", "ApiUrl": "/erp/v2/vehicleinsurance/Export", "Sys": "taizhou"},
	{"Entity": "车险新增", "ApiUrl": "/erp/v2/vehicleinsurance/Create", "Sys": "taizhou"},
	{"Entity": "车险更新", "ApiUrl": "/erp/v2/vehicleinsurance/Update", "Sys": "taizhou"},
	{"Entity": "车险详情", "ApiUrl": "/erp/v2/vehicleinsurance/Show", "Sys": "taizhou"},
	{"Entity": "车险删除", "ApiUrl": "/erp/v2/vehicleinsurance/Delete", "Sys": "taizhou"},
	{"Entity": "车险是否存在", "ApiUrl": "/erp/v2/vehicleinsurance/IsExist", "Sys": "taizhou"},

	{"Entity": "玻璃维修存草稿", "ApiUrl": "/erp/v2/glassrepair/AddDraft", "Sys": "taizhou"},
	{"Entity": "玻璃维修创建", "ApiUrl": "/erp/v2/glassrepair/Add", "Sys": "taizhou"},
	{"Entity": "玻璃维修列表", "ApiUrl": "/erp/v2/glassrepair/List", "Sys": "taizhou"},
	{"Entity": "玻璃维修列表导出", "ApiUrl": "/erp/v2/glassrepair/Export", "Sys": "taizhou"},
	{"Entity": "玻璃维修详情", "ApiUrl": "/erp/v2/glassrepair/GetDetail", "Sys": "taizhou"},
	{"Entity": "玻璃维修添加表单", "ApiUrl": "/erp/v2/glassrepair/AddForm", "Sys": "taizhou"},
	{"Entity": "玻璃维修删除", "ApiUrl": "/erp/v2/glassrepair/Delete", "Sys": "taizhou"},

	{"Entity": "点钞创建", "ApiUrl": "/erp/v2/countmoney/Add", "Sys": "taizhou"},
	{"Entity": "点钞列表", "ApiUrl": "/erp/v2/countmoney/List", "Sys": "taizhou"},
	{"Entity": "点钞编辑", "ApiUrl": "/erp/v2/countmoney/Edit", "Sys": "taizhou"},
	{"Entity": "点钞删除", "ApiUrl": "/erp/v2/countmoney/Delete", "Sys": "taizhou"},
	{"Entity": "点钞操作日志列表", "ApiUrl": "/erp/v2/countmoney/Logger", "Sys": "taizhou"},
	{"Entity": "点钞批量复核", "ApiUrl": "/erp/v2/countmoney/BatchReview", "Sys": "taizhou"},
	{"Entity": "点钞全部复核", "ApiUrl": "/erp/v2/countmoney/AllReview", "Sys": "taizhou"},
	{"Entity": "票款创建", "ApiUrl": "/erp/v2/countticket/Add", "Sys": "taizhou"},
	{"Entity": "票款列表", "ApiUrl": "/erp/v2/countticket/List", "Sys": "taizhou"},
	{"Entity": "票款编辑", "ApiUrl": "/erp/v2/countticket/Edit", "Sys": "taizhou"},
	{"Entity": "票款删除", "ApiUrl": "/erp/v2/countticket/Delete", "Sys": "taizhou"},
	{"Entity": "票款操作日志列表", "ApiUrl": "/erp/v2/countticket/Logger", "Sys": "taizhou"},
	{"Entity": "票款批量复核", "ApiUrl": "/erp/v2/countticket/BatchReview", "Sys": "taizhou"},
	{"Entity": "票款全部复核", "ApiUrl": "/erp/v2/countticket/AllReview", "Sys": "taizhou"},
	{"Entity": "残次币列表", "ApiUrl": "/erp/v2/report/ListDefective", "Sys": "taizhou"},
	{"Entity": "公司营收报表", "ApiUrl": "/erp/v2/report/IncomeCompany", "Sys": "taizhou"},
	{"Entity": "线路营收报表", "ApiUrl": "/erp/v2/report/IncomeLine", "Sys": "taizhou"},
	{"Entity": "车辆营收报表", "ApiUrl": "/erp/v2/report/IncomeVehicle", "Sys": "taizhou"},
	{"Entity": "乘务员日报", "ApiUrl": "/erp/v2/report/AttendantDaily", "Sys": "taizhou"},
	{"Entity": "多票制报表", "ApiUrl": "/erp/v2/report/MultiTicket", "Sys": "taizhou"},
	{"Entity": "票务数据更改权限列表", "ApiUrl": "/erp/v2/ticketdatapermission/List", "Sys": "taizhou"},
	{"Entity": "票务数据更改权限新增", "ApiUrl": "/erp/v2/ticketdatapermission/Add", "Sys": "taizhou"},
	{"Entity": "票务数据更改权限作废", "ApiUrl": "/erp/v2/ticketdatapermission/Cancel", "Sys": "taizhou"},
	{"Entity": "银行校对差额编辑", "ApiUrl": "/erp/v2/ticketbankcheck/EditDifference", "Sys": "taizhou"},
	{"Entity": "银行校对差额列表", "ApiUrl": "/erp/v2/ticketbankcheck/ListDifference", "Sys": "taizhou"},
	{"Entity": "银行数据校对", "ApiUrl": "/erp/v2/ticketbankcheck/Check", "Sys": "taizhou"},
	{"Entity": "点钞差额编辑", "ApiUrl": "/erp/v2/ticketbankcheck/EditDifferenceCountMoney", "Sys": "taizhou"},
	{"Entity": "点钞差额系统自动编辑", "ApiUrl": "/erp/v2/ticketbankcheck/AutoEditDifferenceCountMoney", "Sys": "taizhou"},
	{"Entity": "校对记录列表", "ApiUrl": "/erp/v2/ticketbankcheck/ListCheckRecord", "Sys": "taizhou"},
	{"Entity": "校对记录变更记录列表", "ApiUrl": "/erp/v2/ticketbankcheck/ListCheckChangeRecord", "Sys": "taizhou"},

	{"Entity": "包车款添加", "ApiUrl": "/erp/v2/report/CharterBusIncomeCreate", "Sys": "taizhou"},
	{"Entity": "包车款列表", "ApiUrl": "/erp/v2/report/CharterBusIncomeList", "Sys": "taizhou"},
	{"Entity": "包车款导出", "ApiUrl": "/erp/v2/report/CharterBusIncomeExport", "Sys": "taizhou"},
	{"Entity": "包车款更新", "ApiUrl": "/erp/v2/report/CharterBusIncomeUpdate", "Sys": "taizhou"},
	{"Entity": "包车款删除", "ApiUrl": "/erp/v2/report/CharterBusIncomeDelete", "Sys": "taizhou"},

	{"Entity": "公司线路人次营收表计算", "ApiUrl": "/erp/v2/report/CalcCorporationLineIncome", "Sys": "taizhou"},
	{"Entity": "公司线路人次营收表", "ApiUrl": "/erp/v2/report/CorporationLineIncomeReport", "Sys": "taizhou"},
	{"Entity": "公司线路人次营收表导出", "ApiUrl": "/erp/v2/report/CorporationLineIncomeReportExport", "Sys": "taizhou"},

	{"Entity": "违规类型新增", "ApiUrl": "/erp/v2/qualityassessmentstandard/AddCategory", "Sys": "taizhou"},
	{"Entity": "违规类型列表", "ApiUrl": "/erp/v2/qualityassessmentstandard/ListCategory", "Sys": "taizhou"},
	{"Entity": "违规类型编辑", "ApiUrl": "/erp/v2/qualityassessmentstandard/EditCategory", "Sys": "taizhou"},
	{"Entity": "违规类型删除", "ApiUrl": "/erp/v2/qualityassessmentstandard/DeleteCategory", "Sys": "taizhou"},
	{"Entity": "违规处罚配置", "ApiUrl": "/erp/v2/qualityassessmentstandard/SaveCategoryPunishSetting", "Sys": "taizhou"},
	{"Entity": "质量考核标准导入", "ApiUrl": "/erp/v2/qualityassessmentstandard/AddBatch", "Sys": "taizhou"},
	{"Entity": "质量考核标准新增", "ApiUrl": "/erp/v2/qualityassessmentstandard/Add", "Sys": "taizhou"},
	{"Entity": "质量考核标准列表", "ApiUrl": "/erp/v2/qualityassessmentstandard/List", "Sys": "taizhou"},
	{"Entity": "质量考核标准表单选择列表", "ApiUrl": "/erp/v2/qualityassessmentstandard/SelectList", "Sys": "taizhou"},
	{"Entity": "质量考核标准列表树", "ApiUrl": "/erp/v2/qualityassessmentstandard/Tree", "Sys": "taizhou"},
	{"Entity": "质量考核标准编辑", "ApiUrl": "/erp/v2/qualityassessmentstandard/Edit", "Sys": "taizhou"},
	{"Entity": "质量考核标准删除", "ApiUrl": "/erp/v2/qualityassessmentstandard/Delete", "Sys": "taizhou"},
	{"Entity": "质量考核标准启用", "ApiUrl": "/erp/v2/qualityassessmentstandard/Enable", "Sys": "taizhou"},
	{"Entity": "质量考核标准禁用", "ApiUrl": "/erp/v2/qualityassessmentstandard/Disable", "Sys": "taizhou"},

	{"Entity": "质量考核标准分类添加", "ApiUrl": "/erp/v2/qualityassessmentstandard/AddAssessmentCate", "Sys": "taizhou"},
	{"Entity": "质量考核标准分类编辑", "ApiUrl": "/erp/v2/qualityassessmentstandard/EditAssessmentCate", "Sys": "taizhou"},
	{"Entity": "质量考核标准分类列表", "ApiUrl": "/erp/v2/qualityassessmentstandard/ListAssessmentCate", "Sys": "taizhou"},
	{"Entity": "质量考核标准分类删除", "ApiUrl": "/erp/v2/qualityassessmentstandard/DeleteAssessmentCate", "Sys": "taizhou"},

	{"Entity": "交通违规导入", "ApiUrl": "/erp/v2/trafficviolation/AddBatchViolation", "Sys": "taizhou"},
	{"Entity": "交通违规新增", "ApiUrl": "/erp/v2/trafficviolation/AddViolation", "Sys": "taizhou"},
	{"Entity": "交通违规列表", "ApiUrl": "/erp/v2/trafficviolation/ListViolation", "Sys": "taizhou"},
	{"Entity": "服务违规列表", "ApiUrl": "/erp/v2/trafficviolation/ListServiceViolation", "Sys": "taizhou"},
	{"Entity": "交通违规根据id查询", "ApiUrl": "/erp/v2/trafficviolation/GetViolationById", "Sys": "taizhou"},
	{"Entity": "交通违规编辑", "ApiUrl": "/erp/v2/trafficviolation/EditViolation", "Sys": "taizhou"},
	{"Entity": "交通违规编辑日志", "ApiUrl": "/erp/v2/trafficviolation/EditLog", "Sys": "taizhou"},
	{"Entity": "交通违规加入回收站", "ApiUrl": "/erp/v2/trafficviolation/AddRecycle", "Sys": "taizhou"},
	{"Entity": "交通违规回收站列表", "ApiUrl": "/erp/v2/trafficviolation/RecycleList", "Sys": "taizhou"},
	{"Entity": "交通违规审核", "ApiUrl": "/erp/v2/trafficviolation/Check", "Sys": "taizhou"},
	{"Entity": "交通违规审核日志", "ApiUrl": "/erp/v2/trafficviolation/CheckLog", "Sys": "taizhou"},
	{"Entity": "删除回收站违规数据", "ApiUrl": "/erp/v2/trafficviolation/RecycleDelete", "Sys": "taizhou"},
	{"Entity": "老ERP数据", "ApiUrl": "/erp/v2/trafficviolation/HistoryViolation", "Sys": "taizhou"},
	//{"Entity": "交通违规违规有效", "ApiUrl": "/erp/v2/trafficviolation/EnableViolation", "Sys": "taizhou"},
	//{"Entity": "交通违规违规无效", "ApiUrl": "/erp/v2/trafficviolation/DisableViolation", "Sys": "taizhou"},
	//{"Entity": "交通违法导入", "ApiUrl": "/erp/v2/trafficviolation/AddBatchIllegal", "Sys": "taizhou"},
	//{"Entity": "交通违法新增", "ApiUrl": "/erp/v2/trafficviolation/AddIllegal", "Sys": "taizhou"},
	//{"Entity": "交通违法列表", "ApiUrl": "/erp/v2/trafficviolation/ListIllegal", "Sys": "taizhou"},
	//{"Entity": "交通违法根据id查询", "ApiUrl": "/erp/v2/trafficviolation/GetIllegalById", "Sys": "taizhou"},
	//{"Entity": "交通违法编辑", "ApiUrl": "/erp/v2/trafficviolation/EditIllegal", "Sys": "taizhou"},
	//{"Entity": "交通违法违法扣分", "ApiUrl": "/erp/v2/trafficviolation/EnableIllegal", "Sys": "taizhou"},
	//{"Entity": "交通违法违规不扣分", "ApiUrl": "/erp/v2/trafficviolation/DisableIllegal", "Sys": "taizhou"},

	{"Entity": "重点帮扶快捷原因新增", "ApiUrl": "/erp/v2/emphasishelpdriver/AddCause", "Sys": "taizhou"},
	{"Entity": "重点帮扶快捷原因列表", "ApiUrl": "/erp/v2/emphasishelpdriver/ListCause", "Sys": "taizhou"},
	{"Entity": "重点帮扶快捷原因编辑", "ApiUrl": "/erp/v2/emphasishelpdriver/EditCause", "Sys": "taizhou"},
	{"Entity": "重点帮扶新增", "ApiUrl": "/erp/v2/emphasishelpdriver/Add", "Sys": "taizhou"},
	{"Entity": "重点帮扶列表", "ApiUrl": "/erp/v2/emphasishelpdriver/List", "Sys": "taizhou"},
	{"Entity": "重点帮扶获取详情", "ApiUrl": "/erp/v2/emphasishelpdriver/GetById", "Sys": "taizhou"},
	{"Entity": "重点帮扶结束", "ApiUrl": "/erp/v2/emphasishelpdriver/Terminate", "Sys": "taizhou"},
	{"Entity": "重点帮扶进展新增", "ApiUrl": "/erp/v2/emphasishelpdriver/AddRecord", "Sys": "taizhou"},
	{"Entity": "重点帮扶进展列表", "ApiUrl": "/erp/v2/emphasishelpdriver/ListRecord", "Sys": "taizhou"},
	{"Entity": "重点帮扶进展编辑", "ApiUrl": "/erp/v2/emphasishelpdriver/EditRecord", "Sys": "taizhou"},
	{"Entity": "重点帮扶历史列表", "ApiUrl": "/erp/v2/emphasishelpdriver/ListHistory", "Sys": "taizhou"},
	{"Entity": "重点帮扶历史进展", "ApiUrl": "/erp/v2/emphasishelpdriver/ListHistoryRecord", "Sys": "taizhou"},

	{"Entity": "整改新增", "ApiUrl": "/erp/v2/violationrectification/Add", "Sys": "taizhou"},
	{"Entity": "整改列表", "ApiUrl": "/erp/v2/violationrectification/List", "Sys": "taizhou"},
	//{"Entity": "整改已读", "ApiUrl": "/erp/v2/saferectifications/Read", "Sys": "taizhou"},
	{"Entity": "整改详情", "ApiUrl": "/erp/v2/violationrectification/GetDetail", "Sys": "taizhou"},
	{"Entity": "整改处理", "ApiUrl": "/erp/v2/violationrectification/Handle", "Sys": "taizhou"},
	{"Entity": "整改次数", "ApiUrl": "/erp/v2/violationrectification/Times", "Sys": "taizhou"},
	{"Entity": "整改撤回", "ApiUrl": "/erp/v2/violationrectification/Revoke", "Sys": "taizhou"},
	//{"Entity": "整改（约谈）列表", "ApiUrl": "/erp/v2/saferectifications/ListTalk", "Sys": "taizhou"},
	//{"Entity": "整改（约谈）已读", "ApiUrl": "/erp/v2/saferectifications/ReadTalk", "Sys": "taizhou"},
	//{"Entity": "整改（约谈）处理", "ApiUrl": "/erp/v2/saferectifications/ProcessTalk", "Sys": "taizhou"},
	//{"Entity": "整改（约谈）司机信息", "ApiUrl": "/erp/v2/saferectifications/TalkTips", "Sys": "taizhou"},

	{"Entity": "司机考核-奖励添加", "ApiUrl": "/erp/v2/driverassess/AddDriverReward", "Sys": "taizhou"},
	{"Entity": "司机考核-奖励详情", "ApiUrl": "/erp/v2/driverassess/DriverRewardDetail", "Sys": "taizhou"},
	{"Entity": "司机考核-奖励编辑", "ApiUrl": "/erp/v2/driverassess/EditDriverReward", "Sys": "taizhou"},
	{"Entity": "司机考核-奖励删除", "ApiUrl": "/erp/v2/driverassess/DeleteDriverReward", "Sys": "taizhou"},
	{"Entity": "司机考核-奖励列表", "ApiUrl": "/erp/v2/driverassess/ListDriverReward", "Sys": "taizhou"},
	{"Entity": "司机考核-奖励列表导出", "ApiUrl": "/erp/v2/driverassess/ListDriverRewardExport", "Sys": "taizhou"},
	{"Entity": "司机考核-月考核手动计算", "ApiUrl": "/erp/v2/driverassess/ManualCalcMonthReport", "Sys": "taizhou"},
	{"Entity": "司机考核-安全月考核", "ApiUrl": "/erp/v2/driverassess/DriverSafeMonthReport", "Sys": "taizhou"},
	{"Entity": "司机考核-安全月考核导出", "ApiUrl": "/erp/v2/driverassess/DriverSafeMonthReportExport", "Sys": "taizhou"},
	{"Entity": "司机考核-服务月考核", "ApiUrl": "/erp/v2/driverassess/DriverServiceMonthReport", "Sys": "taizhou"},
	{"Entity": "司机考核-服务月考核导出", "ApiUrl": "/erp/v2/driverassess/DriverServiceMonthReportExport", "Sys": "taizhou"},
	{"Entity": "司机考核-安全月考核明细", "ApiUrl": "/erp/v2/driverassess/DriverSafeMonthReportDetail", "Sys": "taizhou"},
	{"Entity": "司机考核-服务月考核明细", "ApiUrl": "/erp/v2/driverassess/DriverServiceMonthReportDetail", "Sys": "taizhou"},

	{"Entity": "司机考核-年考核手动计算", "ApiUrl": "/erp/v2/driverassess/ManualCalcYearReport", "Sys": "taizhou"},
	{"Entity": "司机考核-安全年考核", "ApiUrl": "/erp/v2/driverassess/DriverSafeYearReport", "Sys": "taizhou"},
	{"Entity": "司机考核-安全年考核导出", "ApiUrl": "/erp/v2/driverassess/DriverSafeYearReportExport", "Sys": "taizhou"},
	{"Entity": "司机考核-服务年考核", "ApiUrl": "/erp/v2/driverassess/DriverServiceYearReport", "Sys": "taizhou"},
	{"Entity": "司机考核-服务年考核导出", "ApiUrl": "/erp/v2/driverassess/DriverServiceYearReportExport", "Sys": "taizhou"},
	{"Entity": "司机考核-按全年考核明细", "ApiUrl": "/erp/v2/driverassess/DriverSafeYearReportDetail", "Sys": "taizhou"},
	{"Entity": "司机考核-服务年考核明细", "ApiUrl": "/erp/v2/driverassess/DriverServiceYearReportDetail", "Sys": "taizhou"},
	{"Entity": "司机考核-待岗考核列表", "ApiUrl": "/erp/v2/driverassess/DriverWaitWorkReport", "Sys": "taizhou"},
	{"Entity": "司机考核-发起待岗申请", "ApiUrl": "/erp/v2/driverassess/DriverWaitWorkApplyLeave", "Sys": "taizhou"},
	{"Entity": "司机考核-安全贡献考核", "ApiUrl": "/erp/v2/driverassess/DriverSafeDevoteReport", "Sys": "taizhou"},
	{"Entity": "司机考核-服务贡献考核", "ApiUrl": "/erp/v2/driverassess/DriverServiceDevoteReport", "Sys": "taizhou"},
	{"Entity": "司机考核-安全贡献考核编辑", "ApiUrl": "/erp/v2/driverassess/DriverSafeDevoteEdit", "Sys": "taizhou"},
	{"Entity": "司机考核-服务贡献考核编辑", "ApiUrl": "/erp/v2/driverassess/DriverServiceDevoteEdit", "Sys": "taizhou"},

	{"Entity": "消息中心列表", "ApiUrl": "/erp/v2/message/List", "Sys": "taizhou"},
	{"Entity": "消息中心已读消息", "ApiUrl": "/erp/v2/message/Read", "Sys": "taizhou"},
	{"Entity": "消息中心已读全部消息", "ApiUrl": "/erp/v2/message/ReadAll", "Sys": "taizhou"},
	{"Entity": "消息中心我的未读消息", "ApiUrl": "/erp/v2/message/UnReadList", "Sys": "taizhou"},
	{"Entity": "字典创建", "ApiUrl": "/erp/v2/dict/Add", "Sys": "taizhou"},
	{"Entity": "字典列表", "ApiUrl": "/erp/v2/dict/List", "Sys": "taizhou"},
	{"Entity": "运营配置", "ApiUrl": "/erp/v2/dict/ListOperation", "Sys": "taizhou"},
	{"Entity": "字典编辑", "ApiUrl": "/erp/v2/dict/Edit", "Sys": "taizhou"},
	{"Entity": "字典删除", "ApiUrl": "/erp/v2/dict/Delete", "Sys": "taizhou"},
	{"Entity": "字典树", "ApiUrl": "/erp/v2/dict/Tree", "Sys": "taizhou"},
	{"Entity": "投诉分类树", "ApiUrl": "/erp/v2/dict/ComplaintTree", "Sys": "taizhou"},

	{"Entity": "设备型号新增", "ApiUrl": "/erp/v2/device/AddModel", "Sys": "taizhou"},
	{"Entity": "设备型号列表", "ApiUrl": "/erp/v2/device/ListModel", "Sys": "taizhou"},
	{"Entity": "设备型号详情", "ApiUrl": "/erp/v2/device/ShowModel", "Sys": "taizhou"},
	{"Entity": "设备型号编辑", "ApiUrl": "/erp/v2/device/EditModel", "Sys": "taizhou"},
	{"Entity": "设备型号下的子设备型号和子设备列表", "ApiUrl": "/erp/v2/device/ModelHasChildDeviceModel", "Sys": "taizhou"},
	{"Entity": "批次下的子设备型号和子设备列表", "ApiUrl": "/erp/v2/device/BatchCodeHasChildDeviceModel", "Sys": "taizhou"},
	{"Entity": "设备型号批量绑定子设备型号", "ApiUrl": "/erp/v2/device/BatchBindChildModel", "Sys": "taizhou"},
	{"Entity": "设备型号删除", "ApiUrl": "/erp/v2/device/DeleteModel", "Sys": "taizhou"},
	{"Entity": "设备明细管理新增", "ApiUrl": "/erp/v2/device/AddDetail", "Sys": "taizhou"},
	{"Entity": "车辆设备管理采购日期", "ApiUrl": "/erp/v2/device/ListDetailPurchase", "Sys": "taizhou"},
	{"Entity": "设备明细管理列表", "ApiUrl": "/erp/v2/device/ListDetail", "Sys": "taizhou"},
	{"Entity": "设备明细管理列表导出", "ApiUrl": "/erp/v2/device/ExportListDetail", "Sys": "taizhou"},

	{"Entity": "设备明细管理编辑", "ApiUrl": "/erp/v2/device/EditDetail", "Sys": "taizhou"},
	{"Entity": "设备明细管理导入更新", "ApiUrl": "/erp/v2/device/ImportDetailUpdate", "Sys": "taizhou"},
	{"Entity": "设备明细管理删除", "ApiUrl": "/erp/v2/device/DeleteDetail", "Sys": "taizhou"},
	{"Entity": "设备明细获取详情", "ApiUrl": "/erp/v2/device/GetDetail", "Sys": "taizhou", "IsH5Permission": "true"},
	{"Entity": "设备明细导出二维码", "ApiUrl": "/erp/v2/device/ExportQrcodeDetail", "Sys": "taizhou"},
	{"Entity": "设备明细打印二维码", "ApiUrl": "/erp/v2/device/PrintQrcodeDetail", "Sys": "taizhou"},
	{"Entity": "设备关联管理批量绑定", "ApiUrl": "/erp/v2/device/BatchAddAssociation", "Sys": "taizhou", "IsH5Permission": "true"},
	{"Entity": "设备关联导入", "ApiUrl": "/erp/v2/device/ImportDeviceAssociation", "Sys": "taizhou"},
	{"Entity": "设备关联管理批量列表", "ApiUrl": "/erp/v2/device/ListAssociation", "Sys": "taizhou"},
	{"Entity": "设备关联管理批量列表导出", "ApiUrl": "/erp/v2/device/ExportListAssociation", "Sys": "taizhou"},
	{"Entity": "设备关联管理批量解绑", "ApiUrl": "/erp/v2/device/Dissociation", "Sys": "taizhou", "IsH5Permission": "true"},
	{"Entity": "设备预设新增设备种类", "ApiUrl": "/erp/v2/device/AddDeviceCategory", "Sys": "taizhou"},
	{"Entity": "设备预设编辑设备种类", "ApiUrl": "/erp/v2/device/EditDeviceCategory", "Sys": "taizhou"},
	{"Entity": "设备预设编辑", "ApiUrl": "/erp/v2/device/EditPreset", "Sys": "taizhou"},
	{"Entity": "设备预设列表", "ApiUrl": "/erp/v2/device/ListPreset", "Sys": "taizhou"},
	{"Entity": "设备批量绑定", "ApiUrl": "/erp/v2/device/BatchAssociationObject", "Sys": "taizhou"},

	{"Entity": "子设备种类添加", "ApiUrl": "/erp/v2/childdevice/AddCate", "Sys": "taizhou"},
	{"Entity": "子设备种类列表", "ApiUrl": "/erp/v2/childdevice/ListCate", "Sys": "taizhou"},
	{"Entity": "子设备添加", "ApiUrl": "/erp/v2/childdevice/AddDevice", "Sys": "taizhou", "IsH5Permission": "true"},
	{"Entity": "子设备列表", "ApiUrl": "/erp/v2/childdevice/ListDevice", "Sys": "taizhou", "IsH5Permission": "true"},
	{"Entity": "子设备列表导出", "ApiUrl": "/erp/v2/childdevice/ExportDevice", "Sys": "taizhou"},
	{"Entity": "子设备详情", "ApiUrl": "/erp/v2/childdevice/ShowDevice", "Sys": "taizhou", "IsH5Permission": "true"},
	{"Entity": "子设备编辑", "ApiUrl": "/erp/v2/childdevice/EditDevice", "Sys": "taizhou", "IsH5Permission": "true"},
	{"Entity": "子设备删除", "ApiUrl": "/erp/v2/childdevice/DeleteDevice", "Sys": "taizhou", "IsH5Permission": "true"},
	{"Entity": "子设备导入更新", "ApiUrl": "/erp/v2/childdevice/ImportUpdateDevice", "Sys": "taizhou"},
	{"Entity": "子设备二维码导出", "ApiUrl": "/erp/v2/childdevice/ExportQrcode", "Sys": "taizhou"},

	{"Entity": "工单新增", "ApiUrl": "/erp/v2/workorder/Add", "Sys": "taizhou", "IsH5Permission": "true"},
	{"Entity": "工单我的列表", "ApiUrl": "/erp/v2/workorder/List", "Sys": "taizhou"},
	{"Entity": "工单列表", "ApiUrl": "/erp/v2/workorder/ListAll", "Sys": "taizhou"},
	{"Entity": "工单合并", "ApiUrl": "/erp/v2/workorder/Merge", "Sys": "taizhou"},
	{"Entity": "工单详情", "ApiUrl": "/erp/v2/workorder/GetDetail", "Sys": "taizhou"},
	{"Entity": "工单报修工单选备件", "ApiUrl": "/erp/v2/workorder/RepairChoose", "Sys": "taizhou"},
	{"Entity": "工单报修车队更改设备状态", "ApiUrl": "/erp/v2/workorder/SetDeviceStatus", "Sys": "taizhou"},
	{"Entity": "工单报修指派维修人", "ApiUrl": "/erp/v2/workorder/AssignRepairUser", "Sys": "taizhou"},

	{"Entity": "信访工单新增", "ApiUrl": "/erp/v2/workorder/AddPetition", "Sys": "taizhou"},
	{"Entity": "信访工单列表", "ApiUrl": "/erp/v2/workorder/PetitionWorkOrderList", "Sys": "taizhou"},
	{"Entity": "我的信访工单", "ApiUrl": "/erp/v2/workorder/PetitionWorkOrderMineList", "Sys": "taizhou"},
	{"Entity": "信访工单详情", "ApiUrl": "/erp/v2/workorder/PetitionWorkOrderShow", "Sys": "taizhou"},
	{"Entity": "信访工单处理", "ApiUrl": "/erp/v2/workorder/PetitionWorkOrderHandle", "Sys": "taizhou"},

	{"Entity": "工单年汇总报表", "ApiUrl": "/erp/v2/workorder/YearSumReport", "Sys": "taizhou"},
	{"Entity": "车载设备机构故障排名报表", "ApiUrl": "/erp/v2/workorder/FleetRankReport", "Sys": "taizhou"},
	{"Entity": "车载设备故障线路排名报表", "ApiUrl": "/erp/v2/workorder/LineRankReport", "Sys": "taizhou"},
	{"Entity": "设备种类故障率报表", "ApiUrl": "/erp/v2/workorder/DeviceCateBrokenRateReport", "Sys": "taizhou"},
	{"Entity": "设备型号故障率报表", "ApiUrl": "/erp/v2/workorder/DeviceModelBrokenRateReport", "Sys": "taizhou"},
	{"Entity": "保内型号故障率报表", "ApiUrl": "/erp/v2/workorder/WithInDeviceModelBrokenRateReport", "Sys": "taizhou"},
	{"Entity": "保外型号故障率报表", "ApiUrl": "/erp/v2/workorder/WithOutDeviceModelBrokenRateReport", "Sys": "taizhou"},
	{"Entity": "易耗零件排名报表（按数量）", "ApiUrl": "/erp/v2/workorder/ChildDeviceCountRankReport", "Sys": "taizhou"},
	{"Entity": "易耗零件排名报表（按总价）", "ApiUrl": "/erp/v2/workorder/ChildDeviceMoneyRankReport", "Sys": "taizhou"},

	{"Entity": "库存物料类型列表", "ApiUrl": "/erp/v2/stock/ListStockMaterialType", "Sys": "daishan,lvcheng"},
	{"Entity": "库存物料类型创建", "ApiUrl": "/erp/v2/stock/AddStockMaterialType", "Sys": "daishan,lvcheng"},
	{"Entity": "库存物料类型编辑", "ApiUrl": "/erp/v2/stock/EditStockMaterialType", "Sys": "daishan,lvcheng"},
	{"Entity": "库存物料类型删除", "ApiUrl": "/erp/v2/stock/DeleteStockMaterialType", "Sys": "daishan,lvcheng"},
	{"Entity": "库存物料类型预设列表", "ApiUrl": "/erp/v2/stock/ListStockMaterialTypePreset", "Sys": "daishan,lvcheng"},
	{"Entity": "库存物料类型预设编辑", "ApiUrl": "/erp/v2/stock/EditStockMaterialTypePreset", "Sys": "daishan,lvcheng"},
	{"Entity": "库存物料类型预设责任人列表", "ApiUrl": "/erp/v2/stock/ListStockMaterialTypePresetLiable", "Sys": "daishan,lvcheng"},
	{"Entity": "库存物料批量添加库存", "ApiUrl": "/erp/v2/stock/BranchAddStock", "Sys": "daishan,lvcheng"},
	{"Entity": "库存物料编辑", "ApiUrl": "/erp/v2/stock/EditStockMaterial", "Sys": "daishan,lvcheng"},
	{"Entity": "库存物料删除", "ApiUrl": "/erp/v2/stock/DeleteStockMaterial", "Sys": "daishan,lvcheng"},
	{"Entity": "库存列表", "ApiUrl": "/erp/v2/stock/ListStock", "Sys": "daishan,lvcheng"},
	{"Entity": "库存记录列表", "ApiUrl": "/erp/v2/stock/ListStockRecord", "Sys": "daishan,lvcheng"},
	{"Entity": "库存入库/出库", "ApiUrl": "/erp/v2/stock/OperateStock", "Sys": "daishan,lvcheng"},
	{"Entity": "物料申请", "ApiUrl": "/erp/v2/stock/MaterialRequisition", "Sys": "daishan,lvcheng", "IsH5Permission": "true"},
	{"Entity": "出库报表", "ApiUrl": "/erp/v2/stock/OutStockForm", "Sys": "daishan,lvcheng"},
	{"Entity": "出库报表详情", "ApiUrl": "/erp/v2/stock/OutStockFormDetail", "Sys": "daishan,lvcheng"},
	{"Entity": "出库报表-导出", "ApiUrl": "/erp/v2/stock/ExportOutStockForm", "Sys": "daishan,lvcheng"},
	{"Entity": "出库报表详情-导出", "ApiUrl": "/erp/v2/stock/ExportOutStockFormDetail", "Sys": "daishan,lvcheng"},

	{"Entity": "回收站库存列表", "ApiUrl": "/erp/v2/stock/ListRecycleStock", "Sys": "daishan,lvcheng"},
	{"Entity": "回收站库存记录列表", "ApiUrl": "/erp/v2/stock/ListRecycleStockRecord", "Sys": "daishan,lvcheng"},

	{"Entity": "车辆调动添加", "ApiUrl": "/erp/v2/vehicletransfer/Create", "Sys": "taizhou"},
	{"Entity": "车辆调动列表", "ApiUrl": "/erp/v2/vehicletransfer/List", "Sys": "taizhou"},
	{"Entity": "车辆调动详情", "ApiUrl": "/erp/v2/vehicletransfer/Show", "Sys": "taizhou"},
	{"Entity": "车辆调动删除", "ApiUrl": "/erp/v2/vehicletransfer/Delete", "Sys": "taizhou"},
	{"Entity": "车辆调动接收人审批", "ApiUrl": "/erp/v2/vehicletransfer/UpdateInfo", "Sys": "taizhou"},
	{"Entity": "车辆调动撤销", "ApiUrl": "/erp/v2/vehicletransfer/UpdateCancelStatus", "Sys": "taizhou"},

	{"Entity": "车辆调动方案列表", "ApiUrl": "/erp/v2/vehicletransfer/TransferPlanList", "Sys": "taizhou"},
	{"Entity": "车辆调动方案详情", "ApiUrl": "/erp/v2/vehicletransfer/TransferPlanShow", "Sys": "taizhou"},
	{"Entity": "车辆调动方案新增", "ApiUrl": "/erp/v2/vehicletransfer/TransferPlanCreate", "Sys": "taizhou"},
	{"Entity": "车辆调动方案编辑", "ApiUrl": "/erp/v2/vehicletransfer/TransferPlanEdit", "Sys": "taizhou"},
	{"Entity": "车辆调动方案删除", "ApiUrl": "/erp/v2/vehicletransfer/TransferPlanDelete", "Sys": "taizhou"},
	{"Entity": "车辆调动方案版本列表", "ApiUrl": "/erp/v2/vehicletransfer/TransferPlanVersionList", "Sys": "taizhou"},
	{"Entity": "车型颜色列表", "ApiUrl": "/erp/v2/vehicletransfer/VehicleModelColor", "Sys": "taizhou"},
	{"Entity": "车型颜色设置", "ApiUrl": "/erp/v2/vehicletransfer/VehicleModelColorEdit", "Sys": "taizhou"},

	{"Entity": "车辆管理列表", "ApiUrl": "/erp/v2/vehiclemanage/List", "Sys": "taizhou"},
	{"Entity": "车辆管理列表导出", "ApiUrl": "/erp/v2/vehiclemanage/ListExport", "Sys": "taizhou"},
	{"Entity": "车辆二维码导出", "ApiUrl": "/erp/v2/vehiclemanage/ExportQrcode", "Sys": "taizhou"},
	{"Entity": "车辆调动记录", "ApiUrl": "/erp/v2/vehiclemanage/VehicleTransferRecord", "Sys": "taizhou"},

	// 人资v0.4.7
	{"Entity": "新增推荐奖励", "ApiUrl": "/erp/v2/refererreward/Create", "Sys": "taizhou"},
	{"Entity": "查询推荐奖励列表", "ApiUrl": "/erp/v2/refererreward/List", "Sys": "taizhou"},
	{"Entity": "推荐明细列表", "ApiUrl": "/erp/v2/refererreward/Records", "Sys": "taizhou"},
	{"Entity": "导出推荐明细列表", "ApiUrl": "/erp/v2/refererreward/Export", "Sys": "taizhou"},

	{"Entity": "事故报警种类列表", "ApiUrl": "/erp/v2/accidentsetting/AlarmCategoryAll", "Sys": "taizhou"},
	{"Entity": "事故报警种类新增", "ApiUrl": "/erp/v2/accidentsetting/AlarmCategoryCreate", "Sys": "taizhou"},
	{"Entity": "事故报警种类更新", "ApiUrl": "/erp/v2/accidentsetting/AlarmCategoryUpdate", "Sys": "taizhou"},
	{"Entity": "事故报警种类删除", "ApiUrl": "/erp/v2/accidentsetting/AlarmCategoryDelete", "Sys": "taizhou"},
	{"Entity": "事故超时报警设置", "ApiUrl": "/erp/v2/accidentsetting/AccidentTimeoutSettingSave", "Sys": "taizhou"},
	{"Entity": "事故报警设置详情", "ApiUrl": "/erp/v2/accidentsetting/AccidentTimeoutSettingAll", "Sys": "taizhou"},
	{"Entity": "事故流程报警设置", "ApiUrl": "/erp/v2/accidentsetting/ProcessTimeoutSettingSave", "Sys": "taizhou"},
	{"Entity": "事故流程报警设置详情", "ApiUrl": "/erp/v2/accidentsetting/ProcessTimeoutSettingAll", "Sys": "taizhou"},

	{"Entity": "流程报警记录", "ApiUrl": "/erp/v2/alarmrecord/ProcessTimeout", "Sys": "taizhou"},
	{"Entity": "流程报警记录导出", "ApiUrl": "/erp/v2/alarmrecord/ProcessTimeoutExport", "Sys": "taizhou"},
	{"Entity": "流程报警记录忽略", "ApiUrl": "/erp/v2/alarmrecord/ProcessTimeoutAlarmIgnore", "Sys": "taizhou"},
	{"Entity": "流程报警记录删除", "ApiUrl": "/erp/v2/alarmrecord/ProcessTimeoutAlarmDelete", "Sys": "taizhou"},
	{"Entity": "事故报警记录", "ApiUrl": "/erp/v2/alarmrecord/AccidentTimeout", "Sys": "taizhou"},
	{"Entity": "事故报警记录导出", "ApiUrl": "/erp/v2/alarmrecord/AccidentTimeoutExport", "Sys": "taizhou"},
	{"Entity": "事故报警记录忽略", "ApiUrl": "/erp/v2/alarmrecord/AccidentTimeoutAlarmIgnore", "Sys": "taizhou"},
	{"Entity": "事故报警记录删除", "ApiUrl": "/erp/v2/alarmrecord/AccidentTimeoutAlarmDelete", "Sys": "taizhou"},

	//运营管理
	{"Entity": "运营管理线路里程设置列表", "ApiUrl": "/erp/v2/operation/LineSettingList", "Sys": "taizhou"},
	{"Entity": "运营管理线路里程设置详情", "ApiUrl": "/erp/v2/operation/LineSettingInfo", "Sys": "taizhou"},
	{"Entity": "运营管理线路里程设置新增", "ApiUrl": "/erp/v2/operation/LineSettingCreate", "Sys": "taizhou"},
	{"Entity": "运营管理线路里程设置更新", "ApiUrl": "/erp/v2/operation/LineSettingUpdate", "Sys": "taizhou"},
	{"Entity": "运营管理线路里程设置删除", "ApiUrl": "/erp/v2/operation/LineSettingDelete", "Sys": "taizhou"},
	{"Entity": "运营管理线路里程设置日志", "ApiUrl": "/erp/v2/operation/LineSettingLog", "Sys": "taizhou"},
	{"Entity": "运营管理线路里程设置日志导出", "ApiUrl": "/erp/v2/operation/LineSettingLogExport", "Sys": "taizhou"},

	{"Entity": "线路公里明细表", "ApiUrl": "/erp/v2/operation/LineMileageReport", "Sys": "taizhou"},
	{"Entity": "线路公里明细表导出", "ApiUrl": "/erp/v2/operation/LineMileageReportExport", "Sys": "taizhou"},
	{"Entity": "线路公里明细表计算", "ApiUrl": "/erp/v2/operation/CalcLineMileage", "Sys": "taizhou"},
	{"Entity": "线路公里明细表批量计算", "ApiUrl": "/erp/v2/operation/MultiDayCalcLineMileage", "Sys": "taizhou"},
	{"Entity": "线路公里明细表保存", "ApiUrl": "/erp/v2/operation/SaveLineMileage", "Sys": "taizhou"},
	{"Entity": "线路公里明细表删除", "ApiUrl": "/erp/v2/operation/DeleteLineMileage", "Sys": "taizhou"},
	{"Entity": "线路公里明细表操作日志", "ApiUrl": "/erp/v2/operation/LineMileageLog", "Sys": "taizhou"},
	{"Entity": "线路司机出勤报表", "ApiUrl": "/erp/v2/operation/LineDriverWorkReport", "Sys": "taizhou"},
	{"Entity": "线路司机出勤报表导出", "ApiUrl": "/erp/v2/operation/LineDriverWorkReportExport", "Sys": "taizhou"},
	{"Entity": "线路司机出勤报表-司机维度", "ApiUrl": "/erp/v2/operation/LineDriverWorkReportByDriver", "Sys": "taizhou"},
	{"Entity": "线路司机出勤报表导出-司机维度", "ApiUrl": "/erp/v2/operation/LineDriverWorkReportByDriverExport", "Sys": "taizhou"},
	{"Entity": "线路工资报表", "ApiUrl": "/erp/v2/operation/LineSalaryReport", "Sys": "taizhou"},
	{"Entity": "线路工资汇总报表", "ApiUrl": "/erp/v2/operation/LineSalarySumReport", "Sys": "taizhou"},
	{"Entity": "线路工资报表备注保存", "ApiUrl": "/erp/v2/operation/SaveLineSalaryReportMore", "Sys": "taizhou"},
	{"Entity": "运营统计报表", "ApiUrl": "/erp/v2/operation/LineMileageRangeReport", "Sys": "taizhou"},
	{"Entity": "运营统计报表导出", "ApiUrl": "/erp/v2/operation/LineMileageRangeReportExport", "Sys": "taizhou"},
	{"Entity": "线路公里明细报表版本列表", "ApiUrl": "/erp/v2/operation/LineMileageVersion", "Sys": "taizhou"},
	{"Entity": "线路公里明细报表版本数据详情", "ApiUrl": "/erp/v2/operation/LineMileageVersionData", "Sys": "taizhou"},
	{"Entity": "线路公里明细报表版本恢复", "ApiUrl": "/erp/v2/operation/LineMileageVersionRecover", "Sys": "taizhou"},

	{"Entity": "累计公里统计报表（线路维度）", "ApiUrl": "/erp/v2/operation/LineMileageSumReport", "Sys": "taizhou"},
	{"Entity": "累计公里统计报表（线路维度）导出", "ApiUrl": "/erp/v2/operation/LineMileageSumReportExport", "Sys": "taizhou"},
	{"Entity": "累计公里统计报表（车辆维度）导出", "ApiUrl": "/erp/v2/operation/VehicleMileageSumReport", "Sys": "taizhou"},
	{"Entity": "累计公里统计报表（车辆维度）导出", "ApiUrl": "/erp/v2/operation/VehicleMileageSumReportExport", "Sys": "taizhou"},

	{"Entity": "月计划排班表导入", "ApiUrl": "/erp/v2/operation/PlanScheduleReportImport", "Sys": "taizhou"},
	{"Entity": "月计划排班表", "ApiUrl": "/erp/v2/operation/PlanScheduleReport", "Sys": "taizhou"},
	{"Entity": "月计划排班表导出", "ApiUrl": "/erp/v2/operation/PlanScheduleReportExport", "Sys": "taizhou"},
	{"Entity": "平均计划天数报表", "ApiUrl": "/erp/v2/operation/PlanScheduleAvgDayReport", "Sys": "taizhou"},
	{"Entity": "平均计划天数报表导出", "ApiUrl": "/erp/v2/operation/PlanScheduleAvgDayReportExport", "Sys": "taizhou"},

	{"Entity": "定制线路报表", "ApiUrl": "/erp/v2/operation/IrregularLineReport", "Sys": "taizhou"},
	{"Entity": "定制线路报表导出", "ApiUrl": "/erp/v2/operation/IrregularLineReportExport", "Sys": "taizhou"},
	{"Entity": "定制线路报表导入", "ApiUrl": "/erp/v2/operation/IrregularLineReportImport", "Sys": "taizhou"},
	{"Entity": "定制线路报表数据删除", "ApiUrl": "/erp/v2/operation/IrregularLineReportDelete", "Sys": "taizhou"},

	{"Entity": "班制外加班数据导入", "ApiUrl": "/erp/v2/operation/OutFrequencyAddWorkReportImport", "Sys": "taizhou"},
	{"Entity": "班制外加班列表", "ApiUrl": "/erp/v2/operation/OutFrequencyAddWorkReportList", "Sys": "taizhou"},
	{"Entity": "班制外加班列表导出", "ApiUrl": "/erp/v2/operation/OutFrequencyAddWorkReportListExport", "Sys": "taizhou"},
	{"Entity": "班制外加班汇总列表", "ApiUrl": "/erp/v2/operation/OutFrequencyAddWorkSumReportList", "Sys": "taizhou"},
	{"Entity": "班制外加班汇总列表导出", "ApiUrl": "/erp/v2/operation/OutFrequencyAddWorkSumReportListExport", "Sys": "taizhou"},
	{"Entity": "班制外加班删除", "ApiUrl": "/erp/v2/operation/OutFrequencyAddWorkReportDelete", "Sys": "taizhou"},

	{"Entity": "运营报表审批提交（重新提交)", "ApiUrl": "/erp/v2/operation/ApprovalCreate", "Sys": "taizhou"},
	{"Entity": "运营报表审批列表", "ApiUrl": "/erp/v2/operation/ApprovalList", "Sys": "taizhou"},
	{"Entity": "运营报表审批列表导出", "ApiUrl": "/erp/v2/operation/ApprovalListExport", "Sys": "taizhou"},
	{"Entity": "运营报表审批详情", "ApiUrl": "/erp/v2/operation/ApprovalShow", "Sys": "taizhou"},
	{"Entity": "撤回已通过的运营报表审", "ApiUrl": "/erp/v2/operation/ApprovalTerminate", "Sys": "taizhou"},
	{"Entity": "检查是否有未保存数据的日期线路", "ApiUrl": "/erp/v2/operation/CheckLineReportAtApproval", "Sys": "taizhou"},
	{"Entity": "检查数据是否可编辑", "ApiUrl": "/erp/v2/operation/CheckLineReportEditStatus", "Sys": "taizhou"},
	{"Entity": "检查未审批的线路", "ApiUrl": "/erp/v2/operation/CheckLineNotApproval", "Sys": "taizhou"},

	{"Entity": "列表自定义设置新增", "ApiUrl": "/erp/v2/listsetting/CustomColumn", "Sys": "taizhou"},
	{"Entity": "列表自定义设置详情", "ApiUrl": "/erp/v2/listsetting/SettingInfo", "Sys": "taizhou"},
	{"Entity": "厂家设置列表", "ApiUrl": "/erp/v2/factorysetting/List", "Sys": "taizhou"},
	{"Entity": "厂家设置导入", "ApiUrl": "/erp/v2/factorysetting/Import", "Sys": "taizhou"},
	{"Entity": "厂家设置新增", "ApiUrl": "/erp/v2/factorysetting/Create", "Sys": "taizhou"},
	{"Entity": "厂家设置更新", "ApiUrl": "/erp/v2/factorysetting/Update", "Sys": "taizhou"},
	{"Entity": "厂家设置删除", "ApiUrl": "/erp/v2/factorysetting/Del", "Sys": "taizhou"},
	{"Entity": "厂家指派管理", "ApiUrl": "/erp/v2/factorysetting/BindUser", "Sys": "taizhou"},

	{"Entity": "线路补贴价格配置列表", "ApiUrl": "/erp/v2/lineallowancesetting/List", "Sys": "taizhou"},
	{"Entity": "线路补贴价格配置记录", "ApiUrl": "/erp/v2/lineallowancesetting/LineRecord", "Sys": "taizhou"},
	{"Entity": "线路补贴价格配置编辑", "ApiUrl": "/erp/v2/lineallowancesetting/Update", "Sys": "taizhou"},

	{"Entity": "运营报表表单审批权限设置详情", "ApiUrl": "/erp/v2/globalsetting/Setting", "Sys": "taizhou"},
	{"Entity": "运营报表表单审批权限设置", "ApiUrl": "/erp/v2/globalsetting/SaveSetting", "Sys": "taizhou"},

	{"Entity": "ERP后台-所有流程", "ApiUrl": "/erp/v2/dashboard/AllProcess", "Sys": "taizhou"},
	{"Entity": "ERP后台-事故流程干预", "ApiUrl": "/erp/v2/dashboard/AccidentProcess", "Sys": "taizhou"},
	{"Entity": "ERP后台-删除流程", "ApiUrl": "/erp/v2/dashboard/DeleteProcess", "Sys": "taizhou"},
	{"Entity": "ERP后台-添加formStep", "ApiUrl": "/erp/v2/dashboard/CreateFormStep", "Sys": "taizhou"},
	{"Entity": "ERP后台-formStep列表", "ApiUrl": "/erp/v2/dashboard/ListFormStep", "Sys": "taizhou"},

	{"Entity": "ERP后台-编辑事故结案表单", "ApiUrl": "/erp/v2/dashboard/UpdateAccidentClosedForm", "Sys": "taizhou"},
	{"Entity": "ERP后台-编辑事故分支结案表单", "ApiUrl": "/erp/v2/dashboard/UpdateAccidentBranchClosedForm", "Sys": "taizhou"},
	{"Entity": "ERP后台-编辑事故表单", "ApiUrl": "/erp/v2/dashboard/UpdateAccidentForm", "Sys": "taizhou"},
	{"Entity": "ERP后台-编辑事故借款表单", "ApiUrl": "/erp/v2/dashboard/UpdateAccidentLendMoneyForm", "Sys": "taizhou"},
	{"Entity": "ERP后台-编辑事故退款表单", "ApiUrl": "/erp/v2/dashboard/UpdateAccidentDrawbackMoneyForm", "Sys": "taizhou"},
	{"Entity": "ERP后台-编辑事故付款表单", "ApiUrl": "/erp/v2/dashboard/UpdateAccidentPaymentMoneyForm", "Sys": "taizhou"},
	{"Entity": "ERP后台-流程相关消息列表", "ApiUrl": "/erp/v2/dashboard/ProcessMessage", "Sys": "taizhou"},
	{"Entity": "ERP后台-删除流程相关消息", "ApiUrl": "/erp/v2/dashboard/ProcessMessageDelete", "Sys": "taizhou"},
	{"Entity": "ERP后台-更新流程formStep和状态", "ApiUrl": "/erp/v2/dashboard/UpdateProcessFormStep", "Sys": "taizhou"},
	{"Entity": "ERP后台-事故状态干预列表", "ApiUrl": "/erp/v2/dashboard/AccidentList", "Sys": "taizhou"},
	{"Entity": "ERP后台-事故状态编辑", "ApiUrl": "/erp/v2/dashboard/AccidentStatusEdit", "Sys": "taizhou"},
	{"Entity": "ERP后台-工单列表", "ApiUrl": "/erp/v2/dashboard/WorkOrderList", "Sys": "taizhou"},
	{"Entity": "ERP后台-工单删除", "ApiUrl": "/erp/v2/dashboard/WorkOrderDelete", "Sys": "taizhou"},

	{"Entity": "ERP后台-信访工单列表", "ApiUrl": "/erp/v2/dashboard/PetitionWorkOrderList", "Sys": "taizhou"},
	{"Entity": "ERP后台-信访工单表单编辑", "ApiUrl": "/erp/v2/dashboard/UpdatePetitionWorkOrderForm", "Sys": "taizhou"},
	{"Entity": "ERP后台-信访工单删除", "ApiUrl": "/erp/v2/dashboard/PetitionWorkOrderDelete", "Sys": "taizhou"},

	{"Entity": "ERP后台-司机调动列表", "ApiUrl": "/erp/v2/dashboard/DriverMigrationList", "Sys": "taizhou"},
	{"Entity": "ERP后台-司机调动删除", "ApiUrl": "/erp/v2/dashboard/DriverMigrationDelete", "Sys": "taizhou"},
	{"Entity": "ERP后台-人员请假列表", "ApiUrl": "/erp/v2/dashboard/StaffLeaveRecordList", "Sys": "taizhou"},
	{"Entity": "ERP后台-人员请假删除", "ApiUrl": "/erp/v2/dashboard/StaffLeaveRecordDelete", "Sys": "taizhou"},
	{"Entity": "ERP后台-司机转正列表", "ApiUrl": "/erp/v2/dashboard/BecomeWorkerRecordList", "Sys": "taizhou"},
	{"Entity": "ERP后台-司机转正删除", "ApiUrl": "/erp/v2/dashboard/BecomeWorkerRecordDelete", "Sys": "taizhou"},

	{"Entity": "ERP后台-车辆调动列表", "ApiUrl": "/erp/v2/dashboard/VehicleMigrationList", "Sys": "taizhou"},
	{"Entity": "ERP后台-车辆调动删除", "ApiUrl": "/erp/v2/dashboard/VehicleMigrationDelete", "Sys": "taizhou"},

	// v0.9.6
	// 线路仿真
	{"Entity": "线路仿真-获取文件夹", "ApiUrl": "/erp/v2/operation/ListCustomLineFolder", "Sys": "taizhou"},
	{"Entity": "线路仿真-新增文件夹", "ApiUrl": "/erp/v2/operation/AddCustomLineFolder", "Sys": "taizhou"},
	{"Entity": "线路仿真-编辑文件夹", "ApiUrl": "/erp/v2/operation/EditCustomLineFolder", "Sys": "taizhou"},
	{"Entity": "线路仿真-删除文件夹", "ApiUrl": "/erp/v2/operation/DelCustomLineFolder", "Sys": "taizhou"},
	{"Entity": "线路仿真-文件夹排序", "ApiUrl": "/erp/v2/operation/SortCustomLineFolder", "Sys": "taizhou"},
	{"Entity": "线路仿真-新增线路", "ApiUrl": "/erp/v2/operation/AddCustomLine", "Sys": "taizhou"},
	{"Entity": "线路仿真-编辑线路", "ApiUrl": "/erp/v2/operation/EditCustomLine", "Sys": "taizhou"},
	{"Entity": "线路仿真-编辑线路", "ApiUrl": "/erp/v2/operation/EditCustomLine", "Sys": "taizhou"},
	{"Entity": "线路仿真-删除线路", "ApiUrl": "/erp/v2/operation/DelCustomLine", "Sys": "taizhou"},
	{"Entity": "线路仿真-删除线路", "ApiUrl": "/erp/v2/operation/DelCustomLine", "Sys": "taizhou"},
	{"Entity": "线路仿真-同步线路站点", "ApiUrl": "/erp/v2/operation/SyncCustomLine", "Sys": "taizhou"},
	{"Entity": "线路仿真-查询线路站点", "ApiUrl": "/erp/v2/operation/ListCustomLineStation", "Sys": "taizhou"},
	{"Entity": "线路仿真-导出线路站点", "ApiUrl": "/erp/v2/operation/ListCustomLineStationExport", "Sys": "taizhou"},
	{"Entity": "线路仿真-导出路牌-线路维度", "ApiUrl": "/erp/v2/operation/ListStreetPlateByCustomLine", "Sys": "taizhou"},
	{"Entity": "线路仿真-线路预测", "ApiUrl": "/erp/v2/operation/PredictLineStation", "Sys": "taizhou"},

	// 线路站点分析
	{"Entity": "线路站点分析-查询站点规格、材料", "ApiUrl": "/erp/v2/operation/StationSetting", "Sys": "taizhou"},
	{"Entity": "线路站点分析-站点设置规格、材料", "ApiUrl": "/erp/v2/operation/GetStationSetting", "Sys": "taizhou"},
	{"Entity": "线路站点分析-行政区域", "ApiUrl": "/erp/v2/operation/ListRegion", "Sys": "taizhou"},
	{"Entity": "线路站点分析-导出站点列表", "ApiUrl": "/erp/v2/operation/ListStation", "Sys": "taizhou"},
	{"Entity": "线路站点分析-导出省级站点数据", "ApiUrl": "/erp/v2/operation/ProvincialDemandStationExport", "Sys": "taizhou"},
	{"Entity": "线路站点分析-导出路牌-线路维度", "ApiUrl": "/erp/v2/operation/ListStreetPlateByLine", "Sys": "taizhou"},
	{"Entity": "线路站点分析-导出路牌-站点维度", "ApiUrl": "/erp/v2/operation/ListStreetPlateByStation", "Sys": "taizhou"},
	{"Entity": "线路站点分析-导出获取站点列表", "ApiUrl": "/erp/v2/operation/ListStationExport", "Sys": "taizhou"},
	{"Entity": "线路站点分析-查询线路站点列表", "ApiUrl": "/erp/v2/operation/ListLineStation", "Sys": "taizhou"},
	{"Entity": "线路站点分析-导出线路站点列表", "ApiUrl": "/erp/v2/operation/ListLineStationExport", "Sys": "taizhou"},
	{"Entity": "线路站点分析-导出站点列表实景", "ApiUrl": "/erp/v2/operation/ListStationRealityExport", "Sys": "taizhou"},
	{"Entity": "线路站点分析-导出站点列表实景-线路维度", "ApiUrl": "/erp/v2/operation/ListStationRealityByLineExport", "Sys": "taizhou"},

	// 站点分时投票统计
	{"Entity": "站点分时投票统计", "ApiUrl": "/erp/v2/operation/TimeSlotStationVote", "Sys": "taizhou"},
	{"Entity": "导出站点分时投票统计", "ApiUrl": "/erp/v2/operation/TimeSlotStationVoteExport", "Sys": "taizhou"},

	// 车内广告屏
	{"Entity": "车内广告-列表", "ApiUrl": "/erp/v2/operation/ListVehicleAdScreenConfig", "Sys": "taizhou"},
	{"Entity": "车内广告-列表", "ApiUrl": "/erp/v2/operation/ListVehicleAdScreenConfigExport", "Sys": "taizhou"},
	{"Entity": "车内广告-导入", "ApiUrl": "/erp/v2/operation/ImportVehicleAdScreenConfig", "Sys": "taizhou"},
	{"Entity": "设置车辆广告屏配置", "ApiUrl": "/erp/v2/operation/SetVehicleAdScreenConfig", "Sys": "taizhou"},
	{"Entity": "车辆详情", "ApiUrl": "/erp/v2/vehicle/Detail", "Sys": "taizhou"},

	// 材料设置
	{"Entity": "运营设置-材料列表", "ApiUrl": "/erp/v2/operation/ListMaterialSetting", "Sys": "taizhou"},
	{"Entity": "运营设置-新增材料", "ApiUrl": "/erp/v2/operation/AddMaterialSetting", "Sys": "taizhou"},
	{"Entity": "运营设置-编辑材料", "ApiUrl": "/erp/v2/operation/EditMaterialSetting", "Sys": "taizhou"},
	{"Entity": "运营设置-删除材料", "ApiUrl": "/erp/v2/operation/DelMaterialSetting", "Sys": "taizhou"},

	// 规格设置
	{"Entity": "运营设置-规格列表", "ApiUrl": "/erp/v2/operation/ListSpecificationSetting", "Sys": "taizhou"},
	{"Entity": "运营设置-新增规格", "ApiUrl": "/erp/v2/operation/AddSpecificationSetting", "Sys": "taizhou"},
	{"Entity": "运营设置-编辑规格", "ApiUrl": "/erp/v2/operation/EditSpecificationSetting", "Sys": "taizhou"},
	{"Entity": "运营设置-删除规格", "ApiUrl": "/erp/v2/operation/DelSpecificationSetting", "Sys": "taizhou"},

	// 出行需求
	{"Entity": "出行需求-列表", "ApiUrl": "/erp/v2/operation/ListTravelNeed", "Sys": "taizhou"},
	{"Entity": "出行需求-添加", "ApiUrl": "/erp/v2/operation/AddTravelNeed", "Sys": "taizhou"},
	{"Entity": "出行需求-编辑", "ApiUrl": "/erp/v2/operation/EditTravelNeed", "Sys": "taizhou"},
	{"Entity": "出行需求-删除", "ApiUrl": "/erp/v2/operation/DelTravelNeed", "Sys": "taizhou"},
	{"Entity": "出行需求-获取出行需求明细列表", "ApiUrl": "/erp/v2/operation/ListTravelNeedDetail", "Sys": "taizhou"},
	{"Entity": "出行需求-导出出行需求明细列表", "ApiUrl": "/erp/v2/operation/ListTravelNeedDetailExport", "Sys": "taizhou"},
	{"Entity": "出行需求-删除出行明细", "ApiUrl": "/erp/v2/operation/DelTravelNeedDetail", "Sys": "taizhou"},

	// 派车单
	{"Entity": "添加派车工单", "ApiUrl": "/erp/v2/operation/AddCharterOrderBrief", "Sys": "taizhou"},
	{"Entity": "查询派车工单", "ApiUrl": "/erp/v2/operation/ListCharterOrderBrief", "Sys": "taizhou"},
	{"Entity": "更新派车工单", "ApiUrl": "/erp/v2/operation/EditCharterOrderBrief", "Sys": "taizhou"},
	{"Entity": "删除派车工单", "ApiUrl": "/erp/v2/operation/DelCharterOrderBrief", "Sys": "taizhou"},
	{"Entity": "导出派车工单", "ApiUrl": "/erp/v2/operation/ListCharterOrderBriefExport", "Sys": "taizhou"},
	{"Entity": "导出派车工单详情", "ApiUrl": "/erp/v2/operation/ListCharterOrderExport", "Sys": "taizhou"},
	{"Entity": "查询派车工单详情", "ApiUrl": "/erp/v2/operation/CharterOrderBriefDetail", "Sys": "taizhou"},

	// 安全生产表
	{"Entity": "查询安全生产工单列表", "ApiUrl": "/erp/v2/safeproduction/List", "Sys": "taizhou"},
	{"Entity": "添加安全生产工单", "ApiUrl": "/erp/v2/safeproduction/Create", "Sys": "taizhou"},
	{"Entity": "更新安全生产工单", "ApiUrl": "/erp/v2/safeproduction/Update", "Sys": "taizhou"},
	{"Entity": "删除安全生产工单", "ApiUrl": "/erp/v2/safeproduction/Delete", "Sys": "taizhou"},
	{"Entity": "废弃安全生产工单", "ApiUrl": "/erp/v2/safeproduction/Scrap", "Sys": "taizhou"},
	{"Entity": "安全生产工单汇总", "ApiUrl": "/erp/v2/safeproduction/Summary", "Sys": "taizhou"},
	{"Entity": "安全生产工单本月是否存在", "ApiUrl": "/erp/v2/safeproduction/IsExistThisMonth", "Sys": "taizhou"},

	// 证件照审核
	{"Entity": "证件照审核列表", "ApiUrl": "/erp/v2/headimgapproval/List", "Sys": "taizhou"},
	{"Entity": "证件照审核详情", "ApiUrl": "/erp/v2/headimgapproval/Show", "Sys": "taizhou"},
	{"Entity": "证件照审核通过", "ApiUrl": "/erp/v2/headimgapproval/Pass", "Sys": "taizhou"},
	{"Entity": "证件照审核驳回", "ApiUrl": "/erp/v2/headimgapproval/Reject", "Sys": "taizhou"},
	// 工资报表
	{"Entity": "工资列表", "ApiUrl": "/erp/v2/staffpayroll/List", "Sys": "taizhou"},
	{"Entity": "工资列表更新", "ApiUrl": "/erp/v2/staffpayroll/Update", "Sys": "taizhou"},
	{"Entity": "工资导入", "ApiUrl": "/erp/v2/staffpayroll/Import", "Sys": "taizhou"},
	{"Entity": "工资重新计算", "ApiUrl": "/erp/v2/staffpayroll/Calculate", "Sys": "taizhou"},
	// 工资核算数据导入界面
	{"Entity": "工资核算数据列表", "ApiUrl": "/erp/v2/staffpayrollcalc/List", "Sys": "taizhou"},
	{"Entity": "工资核算数据导入", "ApiUrl": "/erp/v2/staffpayrollcalc/Import", "Sys": "taizhou"},
	{"Entity": "工资核算数据导入归档", "ApiUrl": "/erp/v2/staffpayrollcalc/File", "Sys": "taizhou"},

	{"Entity": "工资配置列表", "ApiUrl": "/erp/v2/staffpayrollconfig/List", "Sys": "taizhou"},
	{"Entity": "工资配置保存", "ApiUrl": "/erp/v2/staffpayrollconfig/Save", "Sys": "taizhou"},
	{"Entity": "工资配置删除", "ApiUrl": "/erp/v2/staffpayrollconfig/Delete", "Sys": "taizhou"},

	// 节油奖
	{"Entity": "节油奖列表", "ApiUrl": "/erp/v2/fuelsavingaward/List", "Sys": "taizhou"},
	{"Entity": "节油奖导入", "ApiUrl": "/erp/v2/fuelsavingaward/Import", "Sys": "taizhou"},
	// 题目分类
	{"Entity": "题目分类列表", "ApiUrl": "/erp/v2/topiccategory/List", "Sys": "taizhou"},
	{"Entity": "题目分类保存", "ApiUrl": "/erp/v2/topiccategory/Save", "Sys": "taizhou"},
	{"Entity": "题目分类删除", "ApiUrl": "/erp/v2/topiccategory/Delete", "Sys": "taizhou"},
	// 学习考试
	{"Entity": "学习群组列表", "ApiUrl": "/erp/v2/learngroup/List", "Sys": "taizhou"},
	{"Entity": "学习群组新增", "ApiUrl": "/erp/v2/learngroup/Add", "Sys": "taizhou"},
	{"Entity": "学习群组编辑", "ApiUrl": "/erp/v2/learngroup/Edit", "Sys": "taizhou"},
	{"Entity": "学习群组删除", "ApiUrl": "/erp/v2/learngroup/Delete", "Sys": "taizhou"},

	{"Entity": "题目列表", "ApiUrl": "/erp/v2/topic/List", "Sys": "taizhou"},
	{"Entity": "题目新增", "ApiUrl": "/erp/v2/topic/Add", "Sys": "taizhou"},
	{"Entity": "题目编辑", "ApiUrl": "/erp/v2/topic/Edit", "Sys": "taizhou"},
	{"Entity": "题目批量删除", "ApiUrl": "/erp/v2/topic/BatchDelete", "Sys": "taizhou"},
	{"Entity": "题目批量导入", "ApiUrl": "/erp/v2/topic/Import", "Sys": "taizhou"},

	{"Entity": "题目群组列表", "ApiUrl": "/erp/v2/topicgroup/List", "Sys": "taizhou"},
	{"Entity": "题目群组新增", "ApiUrl": "/erp/v2/topicgroup/Add", "Sys": "taizhou"},
	{"Entity": "题目群组编辑", "ApiUrl": "/erp/v2/topicgroup/Edit", "Sys": "taizhou"},
	{"Entity": "题目群组删除", "ApiUrl": "/erp/v2/topicgroup/Delete", "Sys": "taizhou"},

	{"Entity": "试卷列表", "ApiUrl": "/erp/v2/testpaper/List", "Sys": "taizhou"},
	{"Entity": "试卷新增", "ApiUrl": "/erp/v2/testpaper/Add", "Sys": "taizhou"},
	{"Entity": "试卷编辑", "ApiUrl": "/erp/v2/testpaper/Edit", "Sys": "taizhou"},
	{"Entity": "试卷删除", "ApiUrl": "/erp/v2/testpaper/Delete", "Sys": "taizhou"},
	{"Entity": "试卷指派", "ApiUrl": "/erp/v2/testpaper/AddExaminationTask", "Sys": "taizhou"},

	{"Entity": "学习资料列表", "ApiUrl": "/erp/v2/educationalresource/List", "Sys": "taizhou"},
	{"Entity": "学习资料新增", "ApiUrl": "/erp/v2/educationalresource/Add", "Sys": "taizhou"},
	{"Entity": "学习资料编辑", "ApiUrl": "/erp/v2/educationalresource/Edit", "Sys": "taizhou"},
	{"Entity": "学习资料删除", "ApiUrl": "/erp/v2/educationalresource/Delete", "Sys": "taizhou"},
	{"Entity": "学习资料导入", "ApiUrl": "/erp/v2/educationalresource/Import", "Sys": "taizhou"},

	{"Entity": "课程列表", "ApiUrl": "/erp/v2/course/List", "Sys": "taizhou"},
	{"Entity": "课程新增", "ApiUrl": "/erp/v2/course/Add", "Sys": "taizhou"},
	{"Entity": "课程编辑", "ApiUrl": "/erp/v2/course/Edit", "Sys": "taizhou"},
	{"Entity": "课程删除", "ApiUrl": "/erp/v2/course/Delete", "Sys": "taizhou"},
	{"Entity": "课程指派", "ApiUrl": "/erp/v2/course/AddExaminationTask", "Sys": "taizhou"},

	{"Entity": "指派记录", "ApiUrl": "/erp/v2/learnexamtask/List", "Sys": "taizhou"},
	{"Entity": "用户学习考试记录", "ApiUrl": "/erp/v2/learnexamtask/StaffSummaryRecordList", "Sys": "taizhou"},
	{"Entity": "学习课程列表", "ApiUrl": "/erp/v2/learnexamtask/MyLearnList", "Sys": "taizhou"},
	{"Entity": "学习课程详情", "ApiUrl": "/erp/v2/learnexamtask/ShowTaskCourse", "Sys": "taizhou"},
	{"Entity": "上报学习进度", "ApiUrl": "/erp/v2/learnexamtask/ReportLearningProgress", "Sys": "taizhou"},
	{"Entity": "考试列表", "ApiUrl": "/erp/v2/learnexamtask/MyExamList", "Sys": "taizhou"},
	{"Entity": "开始考试", "ApiUrl": "/erp/v2/learnexamtask/StartExam", "Sys": "taizhou"},
	{"Entity": "试卷展示", "ApiUrl": "/erp/v2/learnexamtask/ShowTestPaper", "Sys": "taizhou"},
	{"Entity": "试卷交卷", "ApiUrl": "/erp/v2/learnexamtask/SubmitTestPaper", "Sys": "taizhou"},
	{"Entity": "考试详情", "ApiUrl": "/erp/v2/learnexamtask/ExamDetail", "Sys": "taizhou"},

	{"Entity": "票款客流仪对比报表", "ApiUrl": "/erp/v2/farepassengerflowreport/List", "Sys": "taizhou"},
	{"Entity": "票款客流仪对比报表计算", "ApiUrl": "/erp/v2/farepassengerflowreport/Calculate", "Sys": "taizhou"},

	{"Entity": "投诉管理列表", "ApiUrl": "/erp/v2/complaintmanagement/List", "Sys": "taizhou"},
	{"Entity": "投诉管理新增", "ApiUrl": "/erp/v2/complaintmanagement/Add", "Sys": "taizhou"},
	{"Entity": "投诉管理更新", "ApiUrl": "/erp/v2/complaintmanagement/Edit", "Sys": "taizhou"},
	{"Entity": "投诉管理删除", "ApiUrl": "/erp/v2/complaintmanagement/Delete", "Sys": "taizhou"},
	{"Entity": "投诉管理下发", "ApiUrl": "/erp/v2/complaintmanagement/Issue", "Sys": "taizhou"},

	{"Entity": "线路基准天数列表", "ApiUrl": "/erp/v2/operation/LineBaseDayConfigList", "Sys": "taizhou"},
	{"Entity": "线路基准天数新增", "ApiUrl": "/erp/v2/operation/LineBaseDayConfigAdd", "Sys": "taizhou"},
	{"Entity": "线路基准天数编辑", "ApiUrl": "/erp/v2/operation/LineBaseDayConfigScrap", "Sys": "taizhou"},

	// 瑞安
	{"Entity": "车辆证件有效期管理列表", "ApiUrl": "/erp/v2/operation/VehicleIdentificationList", "Sys": "ruian"},
	{"Entity": "车辆证件有效期管理新增", "ApiUrl": "/erp/v2/operation/VehicleIdentificationAdd", "Sys": "ruian"},
	{"Entity": "车辆证件有效期管理编辑", "ApiUrl": "/erp/v2/operation/VehicleIdentificationEdit", "Sys": "ruian"},
	{"Entity": "车辆证件有效期管理导入信息", "ApiUrl": "/erp/v2/operation/VehicleIdentificationExcelImport", "Sys": "ruian"},
	{"Entity": "车辆证件有效期管理导入附件", "ApiUrl": "/erp/v2/operation/VehicleIdentificationFileImport", "Sys": "ruian"},

	{"Entity": "司机证件有效期管理列表", "ApiUrl": "/erp/v2/operation/DriverIdentificationList", "Sys": "ruian"},
	{"Entity": "司机证件有效期管理新增", "ApiUrl": "/erp/v2/operation/DriverIdentificationAdd", "Sys": "ruian"},
	{"Entity": "司机证件有效期管理编辑", "ApiUrl": "/erp/v2/operation/DriverIdentificationEdit", "Sys": "ruian"},
	{"Entity": "司机证件有效期管理导入信息", "ApiUrl": "/erp/v2/operation/DriverIdentificationExcelImport", "Sys": "ruian"},
	{"Entity": "司机证件有效期管理导入附件", "ApiUrl": "/erp/v2/operation/DriverIdentificationFileImport", "Sys": "ruian"},

	{"Entity": "提醒人员配置列表", "ApiUrl": "/erp/v2/operation/RemindStaffConfigList", "Sys": "ruian"},
	{"Entity": "提醒人员配置保存", "ApiUrl": "/erp/v2/operation/RemindStaffConfigBatchSave", "Sys": "ruian"},

	{"Entity": "车辆档案", "ApiUrl": "/erp/v2/vehiclemanage/VehicleProfile", "Sys": "ruian"},
	{"Entity": "车辆档案保存", "ApiUrl": "/erp/v2/vehiclemanage/VehicleProfileSave", "Sys": "ruian"},
	{"Entity": "车辆档案详情", "ApiUrl": "/erp/v2/vehiclemanage/VehicleProfileShow", "Sys": "ruian"},

	{"Entity": "IC卡车辆线路对比报表", "ApiUrl": "/erp/v2/operation/VehicleLineRoadReport", "Sys": "taizhou"},
	{"Entity": "IC卡车辆线路对比报表计算", "ApiUrl": "/erp/v2/operation/VehicleLineRoadReportCalc", "Sys": "taizhou"},
}

func CheckAndAddPermission() {
	for i := range Permissions {
		var sysArr []string
		if Permissions[i]["Sys"] != "" {
			sysArr = strings.Split(Permissions[i]["Sys"], ",")
		}

		if config.Config.PermissionSys == "local" || Permissions[i]["Sys"] == "" || util.Include(sysArr, config.Config.PermissionSys) {
			_ = rpc.AddPermission(context.Background(), &protoPermission.AddPermissionRequest{
				Entity: Permissions[i]["Entity"],
				ApiUrl: Permissions[i]["ApiUrl"],
			})

			if Permissions[i]["IsH5Permission"] == "true" {
				config.Global.H5Permissions = append(config.Global.H5Permissions, Permissions[i]["ApiUrl"])
			}
		}

		//log.ErrorFields("==========================permission error=========================", map[string]interface{}{"err": err, "url": Permissions[i]["ApiUrl"]})

	}
}
