package database

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	fileModel "app/org/scs/erpv2/api/model/file"
	maintenanceModel "app/org/scs/erpv2/api/model/maintenance"
	messageModel "app/org/scs/erpv2/api/model/message"
	operationModel "app/org/scs/erpv2/api/model/operation"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	settingModel "app/org/scs/erpv2/api/model/setting"
	ticketModel "app/org/scs/erpv2/api/model/ticket"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	"time"
)

func Migrate() {
	config.Global.DbClient.AutoMigrate(
		&fileModel.File{},

		//票务
		&ticketModel.TicketCountMoneys{},
		&ticketModel.TicketLogger{},
		&ticketModel.TicketDataPermission{},
		&ticketModel.TicketDataPermissionRecord{},
		&ticketModel.TicketBankCheckDifferenceRecord{},
		&ticketModel.TicketBankCheckRecord{},
		&ticketModel.TicketBankCheckChangeRecord{},
		&ticketModel.CharteredBusIncome{},
		&ticketModel.CorporationLineIncomeReport{},
		// 门检
		//&safetyModel.DoorCheckItems{},
		//&safetyModel.DoorCheckItemResult{},
		//&safetyModel.DoorCheckRecords{},
		//&safetyModel.DoorCheckInspectors{},
		//&safetyModel.DoorCheckNotices{},
		//&safetyModel.DoorCheckNoticeReceives{},

		// 消息中心
		&messageModel.Message{},

		// 安全
		&safetyModel.ViolationCategory{},
		&safetyModel.TrafficViolation{},
		&safetyModel.TrafficViolationLogger{},
		&safetyModel.TrafficViolationCheckLog{},
		&safetyModel.TrafficViolationRectification{},
		&safetyModel.TrafficViolationHistory{},
		&safetyModel.QualityAssessmentCate{},
		&safetyModel.QualityAssessmentStandards{},
		//&safetyModel.SafeRectifications{},
		//&safetyModel.SafeRectificationsObject{},
		&safetyModel.SafeEmphasisHelpDrivers{},
		&safetyModel.SafeEmphasisHelpDriverRecords{},
		&safetyModel.SafeEmphasisHelpDriverCauses{},

		//&hrModel.WorkPost{},
		//&hrModel.JoinCompanyApply{},
		//&hrModel.JoinCompanyApplyCertificate{},
		//&hrModel.JoinCompanyApplySkill{},
		//&hrModel.JoinCompanyApplyMember{},
		//&hrModel.JoinCompanyApplyEducation{},
		//&hrModel.StaffArchive{},
		//&hrModel.StaffCertificate{},
		//&hrModel.StaffPositionalTitle{},
		//&hrModel.StaffSkill{},
		//&hrModel.StaffEducation{},
		//&hrModel.StaffFamilyMember{},
		//&hrModel.StaffHasWorkPost{},
		//&hrModel.StaffLaborContract{},
		//&hrModel.StaffPunishmentRecord{},
		//&hrModel.StaffRewardRecord{},
		//&hrModel.StaffArchiveLogger{},
		//&hrModel.StaffTransfer{},
		//&hrModel.StaffTransferRecord{},
		//&hrModel.StaffQuit{},
		//&hrModel.StaffQuitRecord{},
		//&hrModel.StaffRetireWorkRecord{},
		//&hrModel.StaffArchiveVersion{},
		//&hrModel.DriverBecomeWorkerRecord{},
		//&hrModel.StaffVetoRecord{},
		//
		//&hrModel.InsuranceFundSetting{},
		//&hrModel.InsuranceRecord{},
		//&hrModel.FundRecord{},
		//&hrModel.PositionalTitleApply{},
		//&hrModel.StaffAssessment{},
		//
		//&hrModel.WorkTrain{},
		//&hrModel.WorkTrainHasStaff{},
		//&hrModel.WorkTrainHasCorporation{},
		//&hrModel.DriverMigration{},
		//&hrModel.DriverMigrationRecord{},
		//
		//&hrModel.RefererReward{},
		//&hrModel.RefererRewardItem{},

		&safetyModel.TrafficAccident{},
		&safetyModel.TrafficAccidentRelater{},
		&safetyModel.TrafficAccidentRelaterBranch{},
		&safetyModel.TrafficAccidentLendMoneyRecord{},
		&safetyModel.TrafficAccidentDrawbackMoneyRecord{},
		&safetyModel.TrafficAccidentPaymentMoneyRecord{},
		&safetyModel.TrafficAccidentLogger{},
		&safetyModel.TrafficAccidentSetting{},
		&safetyModel.VehicleInsurance{},

		//&processModel.LbpmApplyProcess{},
		//&processModel.LbpmApplyProcessDeleteLog{},
		//&processModel.LbpmApplyProcessHasHandler{},
		//&processModel.DingTalkApplyProcess{},
		//&processModel.DingTalkApplyProcessHasHandler{},

		//&commonModel.Dict{},
		//&workOrderModel.DeviceModel{},
		//&workOrderModel.DeviceModelBatchCode{},
		//&workOrderModel.DeviceModelBatchCodeHasChildDeviceCate{},
		//&workOrderModel.DeviceDetail{},
		//&workOrderModel.WorkOrder{},
		&workOrderModel.MiniWorkOrder{},
		//&workOrderModel.PetitionWorkOrder{},
		//&workOrderModel.PetitionWorkOrderHandleResult{},
		//&workOrderModel.WorkOrderRepairSparePart{},
		//&workOrderModel.DevicePreset{},
		//&workOrderModel.ChildDevice{},
		//&workOrderModel.ChildDeviceCate{},
		//&workOrderModel.WorkOrderReplaceChildDevice{},
		//
		//&schedulerModel.Scheduler{},
		//
		//&stockModel.StockMaterialType{},
		//&stockModel.StockMaterialTypePreset{},
		//&stockModel.StockMaterial{},
		//&stockModel.Stock{},
		//&stockModel.StockRecord{},
		//&stockModel.StockMaterialLiable{},

		//&accidentSettingModel.AccidentAlarmCategorySetting{},
		//&accidentSettingModel.AccidentProcessTimeoutSetting{},
		//&accidentSettingModel.AccidentProcessTimeoutSettingDetail{},
		//&accidentSettingModel.AccidentTimeoutSetting{},
		//&accidentSettingModel.AccidentTimeoutSettingDetail{},
		//&accidentSettingModel.AccidentTimeoutAlarmRecord{},
		//&settingModel.ProcessTimeoutAlarmRecord{},
		//&settingModel.ColumnSetting{},
		//&settingModel.UserSetting{},
		//&settingModel.LineAllowancePriceSetting{},
		&settingModel.GlobalSetting{},

		//&maintenanceModel.VehicleMigration{},
		//&maintenanceModel.VehicleMigrationRecord{},

		//&hrModel.StaffArchiveDayReport{},

		//&operationModel.OperationLineSetting{},
		//&operationModel.OperationLineSettingHasDate{},
		//&operationModel.OperationLineSettingLog{},
		//&operationModel.LineVehicleMileageReportLog{},
		//&operationModel.LineVehicleMileageReportVersion{},
		//&operationModel.LineSalaryReportSetting{},
		//&operationModel.IrregularLineReport{},
		//&operationModel.PlanScheduleReport{},
		//&operationModel.OperationApproval{},
		//&operationModel.OutFrequencyAddWorkReport{},
		//&lineDriverModel.LineHasDriver{},
		//&holidayModel.HolidayDate{},
		//
		//&hrModel.LeaveManagement{},
		//&hrModel.LeaveRuleSetting{},
		//&hrModel.ApplyLeaveRecord{},
		//&hrModel.ApplyLeaveRecordDate{},
		//
		//&settingModel.ProcessFormStepSetting{},
		//&settingModel.DeviceFactory{},
		//&settingModel.DeviceFactoryHasUser{},
		//
		//&maintenanceModel.VehicleTransferPlan{},
		//&maintenanceModel.VehicleTransferPlanVersion{},
		//&maintenanceModel.VehicleTransferPlanVersionDetail{},
		&maintenanceModel.VehicleModelColor{},
		&maintenanceModel.VehicleMaintenanceRecord{},
		&maintenanceModel.VehicleFixRecord{},
		&maintenanceModel.VehicleFixRecordItem{},
		&maintenanceModel.VehicleFixRecordItemMaterial{},
		&maintenanceModel.MaintenanceMaterial{},
		&maintenanceModel.MaintenanceVehicleArchiveReport{},
		//
		//&operationModel.SpecificationSetting{},
		//&operationModel.MaterialSetting{},
		//&operationModel.OperationAssociationSetting{},
		//&operationModel.CustomLine{},
		//&operationModel.CustomLineStation{},
		//&operationModel.Folder{},
		//&operationModel.CorpRegion{},
		//&exportModel.ExportFile{},
		//&operationModel.TravelNeed{},
		//&operationModel.TravelNeedDetail{},
		//&operationModel.OperationAssociationSettingRecord{},
		//&operationModel.CharterOrderBrief{},
		//&operationModel.CharterOrder{},
		//&operationModel.CharterDispatchOrder{},
	)

	//按年存储的线路公里明细报表字段更新
	start := time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)

	for {
		if start.Year() > time.Now().Year() {
			break
		}

		err := (&operationModel.LineVehicleMileageReport{}).SyncTableColumn(start)
		log.ErrorFields("SyncTableColumn error", map[string]interface{}{"err": err})
		start = start.AddDate(1, 0, 0)
	}
}
