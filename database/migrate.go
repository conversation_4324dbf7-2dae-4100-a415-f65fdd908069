package database

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	commonModel "app/org/scs/erpv2/api/model/common"
	exportModel "app/org/scs/erpv2/api/model/export"
	fileModel "app/org/scs/erpv2/api/model/file"
	holidayModel "app/org/scs/erpv2/api/model/holiday"
	hrModel "app/org/scs/erpv2/api/model/hr"
	learnModel "app/org/scs/erpv2/api/model/learn"
	lineDriverModel "app/org/scs/erpv2/api/model/lineDriver"
	maintenanceModel "app/org/scs/erpv2/api/model/maintenance"
	messageModel "app/org/scs/erpv2/api/model/message"
	operationModel "app/org/scs/erpv2/api/model/operation"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	assessModel "app/org/scs/erpv2/api/model/safety/assess"
	schedulerModel "app/org/scs/erpv2/api/model/scheduler"
	settingModel "app/org/scs/erpv2/api/model/setting"
	accidentSettingModel "app/org/scs/erpv2/api/model/setting/accident"
	stockModel "app/org/scs/erpv2/api/model/stock"
	ticketModel "app/org/scs/erpv2/api/model/ticket"
	vehicleModel "app/org/scs/erpv2/api/model/vehicle"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	"time"
)

func Migrate() {
	config.Global.DbClient.AutoMigrate(
		&fileModel.File{},

		//票务
		&ticketModel.TicketCountMoneys{},
		&ticketModel.TicketLogger{},
		&ticketModel.TicketDataPermission{},
		&ticketModel.TicketDataPermissionRecord{},
		&ticketModel.TicketBankCheckDifferenceRecord{},
		&ticketModel.TicketBankCheckRecord{},
		&ticketModel.TicketBankCheckChangeRecord{},
		&ticketModel.CharteredBusIncome{},
		&ticketModel.CorporationLineIncomeReport{},
		// 门检
		&safetyModel.DoorCheckItems{},
		&safetyModel.DoorCheckItemResult{},
		&safetyModel.DoorCheckRecords{},
		&safetyModel.DoorCheckInspectors{},
		&safetyModel.DoorCheckNotices{},
		&safetyModel.DoorCheckNoticeReceives{},

		// 消息中心
		&messageModel.Message{},

		// 安全
		&safetyModel.ViolationCategory{},
		&safetyModel.ViolationCategoryPunishSetting{},
		&safetyModel.TrafficViolation{},
		&safetyModel.TrafficViolationLogger{},
		&safetyModel.TrafficViolationCheckLog{},
		&safetyModel.TrafficViolationRectification{},
		&safetyModel.TrafficViolationHistory{},
		&safetyModel.QualityAssessmentCate{},
		&safetyModel.QualityAssessmentStandards{},
		//&safetyModel.SafeRectifications{},
		//&safetyModel.SafeRectificationsObject{},
		&safetyModel.SafeEmphasisHelpDrivers{},
		&safetyModel.SafeEmphasisHelpDriverRecords{},
		&safetyModel.SafeEmphasisHelpDriverCauses{},
		&assessModel.DriverAssessRewardReport{},
		&assessModel.DriverMonthAssessReport{},
		&assessModel.DriverViolationMonthAssessReportItem{},
		&assessModel.DriverAccidentMonthAssessReportItem{},
		&assessModel.DriverWaitWorkReport{},
		&assessModel.DriverWaitWorkViolationItem{},

		&assessModel.DriverYearAssessReport{},
		&assessModel.DriverMonthWorkRecord{},
		&assessModel.DriverCasualLeaveRecord{},
		&assessModel.DriverViolationYearAssessRecord{},
		&assessModel.DriverAccidentYearAssessRecord{},
		&assessModel.DriverDevoteReport{},
		&assessModel.DriverDevoteLogger{},
		&assessModel.DriverDevoteMonthMoneyReport{},

		&hrModel.WorkPost{},
		&hrModel.JoinCompanyApply{},
		&hrModel.JoinCompanyApplyCertificate{},
		&hrModel.JoinCompanyApplySkill{},
		&hrModel.JoinCompanyApplyMember{},
		&hrModel.JoinCompanyApplyEducation{},
		&hrModel.StaffArchive{},
		&hrModel.StaffCertificate{},
		&hrModel.StaffPositionalTitle{},
		&hrModel.StaffSkill{},
		&hrModel.StaffEducation{},
		&hrModel.StaffFamilyMember{},
		&hrModel.StaffHasWorkPost{},
		&hrModel.StaffLaborContract{},
		&hrModel.StaffPunishmentRecord{},
		&hrModel.StaffRewardRecord{},
		&hrModel.StaffArchiveLogger{},
		&hrModel.StaffTransfer{},
		&hrModel.StaffTransferRecord{},
		&hrModel.StaffQuit{},
		&hrModel.StaffQuitRecord{},
		//&hrModel.StaffRetire{},
		//&hrModel.StaffRetireRecord{},
		&hrModel.StaffRetireWorkRecord{},
		&hrModel.StaffArchiveVersion{},
		&hrModel.DriverBecomeWorkerRecord{},
		&hrModel.StaffVetoRecord{},

		&hrModel.InsuranceFundSetting{},
		&hrModel.InsuranceRecord{},
		&hrModel.FundRecord{},
		&hrModel.PositionalTitleApply{},
		&hrModel.StaffAssessment{},

		&hrModel.WorkTrain{},
		&hrModel.WorkTrainHasStaff{},
		&hrModel.WorkTrainHasCorporation{},
		&hrModel.DriverMigration{},
		&hrModel.DriverMigrationRecord{},

		&hrModel.RefererReward{},
		&hrModel.RefererRewardItem{},
		&hrModel.HeadImgApproval{},
		&hrModel.StaffPayrollReport{},

		&safetyModel.TrafficAccident{},
		&safetyModel.TrafficAccidentRelater{},
		&safetyModel.TrafficAccidentRelaterBranch{},
		&safetyModel.TrafficAccidentLendMoneyRecord{},
		&safetyModel.TrafficAccidentDrawbackMoneyRecord{},
		&safetyModel.TrafficAccidentPaymentMoneyRecord{},
		&safetyModel.TrafficAccidentLogger{},
		&safetyModel.TrafficAccidentSetting{},
		&maintenanceModel.GlassRepair{},
		&safetyModel.VehicleInsurance{},
		&safetyModel.SafeProductionReport{},

		&processModel.LbpmApplyProcess{},
		&processModel.LbpmApplyProcessDeleteLog{},
		&processModel.LbpmApplyProcessHasHandler{},
		&processModel.DingTalkApplyProcess{},
		&processModel.DingTalkApplyProcessHasHandler{},

		&commonModel.Dict{},
		&workOrderModel.DeviceModel{},
		&workOrderModel.DeviceModelBatchCode{},
		&workOrderModel.DeviceModelBatchCodeHasChildDeviceCate{},
		&workOrderModel.DeviceDetail{},
		&workOrderModel.WorkOrder{},
		&workOrderModel.PetitionWorkOrder{},
		&workOrderModel.PetitionWorkOrderHandleResult{},
		&workOrderModel.WorkOrderRepairSparePart{},
		&workOrderModel.DevicePreset{},
		&workOrderModel.ChildDevice{},
		&workOrderModel.ChildDeviceCate{},
		&workOrderModel.WorkOrderReplaceChildDevice{},

		&schedulerModel.Scheduler{},

		&stockModel.StockMaterialType{},
		&stockModel.StockMaterialTypePreset{},
		&stockModel.StockMaterial{},
		&stockModel.Stock{},
		&stockModel.StockRecord{},
		&stockModel.StockMaterialLiable{},

		&accidentSettingModel.AccidentAlarmCategorySetting{},
		&accidentSettingModel.AccidentProcessTimeoutSetting{},
		&accidentSettingModel.AccidentProcessTimeoutSettingDetail{},
		&accidentSettingModel.AccidentTimeoutSetting{},
		&accidentSettingModel.AccidentTimeoutSettingDetail{},
		&accidentSettingModel.AccidentTimeoutAlarmRecord{},
		&settingModel.ProcessTimeoutAlarmRecord{},
		&settingModel.ColumnSetting{},
		&settingModel.UserSetting{},
		&settingModel.LineAllowancePriceSetting{},
		&settingModel.GlobalSetting{},
		&settingModel.TopicCategory{},

		&maintenanceModel.VehicleMigration{},
		&maintenanceModel.VehicleMigrationRecord{},

		&hrModel.StaffArchiveDayReport{},

		&operationModel.OperationLineSetting{},
		&operationModel.OperationLineSettingHasDate{},
		&operationModel.OperationLineSettingLog{},
		&operationModel.LineVehicleMileageReportLog{},
		&operationModel.LineVehicleMileageReportVersion{},
		&operationModel.LineSalaryReportSetting{},
		&operationModel.IrregularLineReport{},
		&operationModel.PlanScheduleReport{},
		&operationModel.OperationApproval{},
		&operationModel.OutFrequencyAddWorkReport{},

		&operationModel.CustomLineStation{},
		&operationModel.CustomLine{},
		&operationModel.Folder{},
		&operationModel.LineAdjustmentRecord{},
		&operationModel.LineAdjustmentVisitRecord{},

		&lineDriverModel.LineHasDriver{},
		&holidayModel.HolidayDate{},

		&hrModel.LeaveManagement{},
		&hrModel.LeaveRuleSetting{},
		&hrModel.ApplyLeaveRecord{},
		&hrModel.ApplyLeaveRecordDate{},

		&settingModel.ProcessFormStepSetting{},
		&settingModel.DeviceFactory{},
		&settingModel.DeviceFactoryHasUser{},
		&settingModel.StaffPayrollConfig{},

		&maintenanceModel.VehicleTransferPlan{},
		&maintenanceModel.VehicleTransferPlanVersion{},
		&maintenanceModel.VehicleTransferPlanVersionDetail{},
		&maintenanceModel.VehicleModelColor{},
		&maintenanceModel.FuelSavingAwardReport{},

		&operationModel.SpecificationSetting{},
		&operationModel.MaterialSetting{},
		&operationModel.OperationAssociationSetting{},
		&operationModel.CustomLine{},
		&operationModel.CustomLineStation{},
		&operationModel.Folder{},
		&operationModel.CorpRegion{},
		&operationModel.TravelNeed{},
		&operationModel.TravelNeedDetail{},
		&operationModel.OperationAssociationSettingRecord{},
		&exportModel.ExportFile{},
		&operationModel.CharterOrderBrief{},
		&operationModel.CharterOrder{},
		&operationModel.CharterDispatchOrder{},

		&vehicleModel.VehicleInfo{},

		learnModel.LearnGroup{},
		learnModel.Topic{},
		learnModel.TopicAnswer{},
		learnModel.TopicGroup{},
		learnModel.EducationalResource{},
		learnModel.TestPaper{},
		learnModel.LearnCourse{},
		learnModel.LearnExamTask{},
		learnModel.StaffSummaryReport{},
		learnModel.StaffExamRecord{},
		learnModel.StaffExamRecordLine{},
		learnModel.StaffLearnRecord{},
	)

	//按年存储的线路公里明细报表字段更新
	start := time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)

	for {
		if start.Year() > time.Now().Year() {
			break
		}

		err := (&operationModel.LineVehicleMileageReport{}).SyncTableColumn(start)
		log.ErrorFields("SyncTableColumn error", map[string]interface{}{"err": err})
		start = start.AddDate(1, 0, 0)
	}
}
