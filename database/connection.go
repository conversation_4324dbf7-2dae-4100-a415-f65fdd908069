package database

import (
	"app/org/scs/erpv2/api/config"
	"fmt"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
	"log"
	"os"
	"strings"
	"time"
)

func DBConnection() error {
	dbConf := config.Config.Database
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=disable TimeZone=Asia/Shanghai",
		dbConf.Host, dbConf.User, dbConf.Password, dbConf.DbName, dbConf.Port)

	conn, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: initDbLogger(),
		//SkipDefaultTransaction:                   true, // 禁用全局事务
		DisableForeignKeyConstraintWhenMigrating: true, // 禁用自动创建外键
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   "",                                // table name prefix, table for `User` would be `t_users`
			SingularTable: false,                             // use singular table name, table for `User` would be `user` with this option enabled
			NoLowerCase:   false,                             // skip the snake_casing of names
			NameReplacer:  strings.NewReplacer("CID", "Cid"), // use name replacer to change struct/field name before convert it to db name
		},
	})
	if err != nil {
		return err
	}
	config.Global.DbClient = conn

	DB, err := config.Global.DbClient.DB()

	if err != nil {
		return err
	}

	// SetMaxIdleConnects 设置空闲连接池中连接的最大数量
	DB.SetMaxIdleConns(10)

	// SetMaxOpenConnects 设置打开数据库连接的最大数量。
	DB.SetMaxOpenConns(100)

	// SetConnMaxLifetime 设置了连接可复用的最大时间。
	DB.SetConnMaxLifetime(time.Hour)

	return nil

}

func initDbLogger() logger.Interface {
	var logLevel = logger.Error
	switch config.Config.DebugLevel {
	case "info":
		logLevel = logger.Info
	case "error":
		logLevel = logger.Error
	case "debug":
		logLevel = logger.Info
	default:
		logLevel = logger.Info
	}

	// io writer
	write := log.New(os.Stdout, "\r\n", log.LstdFlags)

	// config
	conf := logger.Config{
		SlowThreshold:             200 * time.Millisecond, // 慢 SQL 阈值, 200ms
		LogLevel:                  logLevel,               // 日志级别
		IgnoreRecordNotFoundError: true,                   // 忽略ErrRecordNotFound（记录未找到）错误
		Colorful:                  true,                   // 彩色打印
	}

	return logger.New(write, conf)
}

func InitIcMysqlConnect() (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		config.Config.ICMysqlDatabase.DbUser, config.Config.ICMysqlDatabase.DbPass, config.Config.ICMysqlDatabase.DbHost, config.Config.ICMysqlDatabase.DbName)
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Printf("init db err:%v", map[string]interface{}{"err": err})
		return nil, err
	}
	return db, nil
}
