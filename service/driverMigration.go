package service

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service/rpc"
	"context"
	"errors"
	"gorm.io/gorm"
)

func UpdateDriverToOet(migration hrModel.DriverMigration) error {
	records := migration.GetRecords()

	for i := range records {
		oetStaff := rpc.GetStaffWithId(context.TODO(), records[i].DriverId)
		if oetStaff == nil {
			return errors.New("not found oet staff")
		}
		oetStaff.CorporationId = migration.InCorporationId
		oetStaff.LineId = records[i].InLineId
		err := rpc.EditOetStaff(context.TODO(), migration.OpUserId, oetStaff)
		if err != nil {
			log.ErrorFields("UpdateDriverToOet rpc EditOetStaff error", map[string]interface{}{"err": err})
			return err
		}
	}
	//更新状态为已调动
	err := migration.SetDone()
	if err != nil {
		log.ErrorFields("UpdateDriverToOet DriverMigration.SetDone error", map[string]interface{}{"err": err})
		return err
	}

	return nil
}

func SendDriverMigrationProcessStatusChangeMsg(vehicleMigrationId int64) {
	_ = rpc.DriverMigrationStatusChange(vehicleMigrationId)
}

func DeleteDriverMigrationAndRelation(tx *gorm.DB, ids []int64) error {
	err := tx.Where("Id IN ?", ids).Delete(&hrModel.DriverMigration{}).Error
	if err != nil {
		return err
	}

	//删除关联调动人员
	err = tx.Where("DriverMigrationId IN ?", ids).Delete(&hrModel.DriverMigrationRecord{}).Error
	if err != nil {
		return err
	}

	//删除流程
	var formInstanceIds []int64
	model.DB().Model(&processModel.LbpmApplyProcess{}).Select("FormInstanceId").Where("ItemId IN ? AND ItemTableName = ?", ids, (&hrModel.DriverMigration{}).TableName()).Pluck("FormInstanceId", &formInstanceIds)

	if len(formInstanceIds) > 0 {
		err = DeleteProcessAndMessage(tx, formInstanceIds)
		if err != nil {
			return err
		}
	}

	return nil
}
