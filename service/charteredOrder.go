package service

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/operation"
	"app/org/scs/erpv2/api/util"
	"sort"
)

// AddCharteredOrderTipToQueue 加入行程单到排队序列
func AddCharteredOrderTipToQueue(trip operation.CharteredOrderTrip) {
	//查询今天所有正在排队的序列
	queueJobs := (&operation.CharteredOrderTripQueueJob{}).GetAll()

	currentQueueJob := operation.CharteredOrderTripQueueJob{
		CharteredOrderTripId: trip.Id,
		InitSortNum:          int64(len(queueJobs) + 1), // 初始排队序号为当前队列长度+1
		CurrentSort:          int64(len(queueJobs) + 1), // 当前排序号初始等于初始排队序号
		OrderType:            trip.OrderType,            // 使用行程单的订单类型
		OverNumCount:         0,                         // 过号次数初始为0
	}
	//以QueueCode进行升序排列
	sort.SliceStable(queueJobs, func(i, j int) bool {
		return queueJobs[i].QueueCode < queueJobs[j].QueueCode
	})

	currentQueueJob.QueueCode = queueJobs[len(queueJobs)-1].QueueCode + 1

	sort.SliceStable(queueJobs, func(i, j int) bool {
		return queueJobs[i].CurrentSort < queueJobs[j].CurrentSort
	})

	// 如果是VIP订单，则执行插队逻辑
	if trip.OrderType == 1 {
		currentQueueJob.IsJumpQueue = util.StatusForTrue
		queueJobs = JumpQueue(queueJobs, currentQueueJob)
	} else {
		// 非VIP订单直接添加到队尾
		currentQueueJob.IsJumpQueue = util.StatusForFalse
		currentQueueJob.CurrentSort = queueJobs[len(queueJobs)-1].CurrentSort + 1
		queueJobs = append(queueJobs, currentQueueJob)
	}

	//循环队列，找到当前的那个数据，将初始排队序号设置为当前所在位置
	for i, job := range queueJobs {
		if job.Id == currentQueueJob.Id {
			currentQueueJob.InitSortNum = int64(i + 1)
			break
		}
	}
	tx := model.DB().Begin()
	// 保存到数据库
	err := currentQueueJob.Create(tx)
	if err != nil {
		tx.Rollback()
		return
	}
	// 更新其他排队记录的CurrentSort
	for _, job := range queueJobs {
		if job.Id != currentQueueJob.Id {
			//更新排队序号
			err = (&operation.CharteredOrderTripQueueJob{}).UpdateCurrentSort(job.Id, job.CurrentSort)
			if err != nil {
				tx.Rollback()
				return
			}
		}
	}

	tx.Commit()
	return
}

// JumpQueue 插队逻辑处理
func JumpQueue(jobs []operation.CharteredOrderTripQueueJob, currentQueueJob operation.CharteredOrderTripQueueJob) []operation.CharteredOrderTripQueueJob {
	insertIndex := -1

	// 寻找插入位置
	for i := range jobs {
		// 如果是插队或者是VIP，跳过这个位置
		if jobs[i].IsJumpQueue == 1 || jobs[i].OrderType == 1 {
			continue
		}

		// 检查下一个元素
		if i < len(jobs)-1 {
			// 如果下一个是插队或VIP，也跳过
			if jobs[i+1].IsJumpQueue == 1 || jobs[i+1].OrderType == 1 {
				continue
			}
		}

		// 找到合适的插入位置
		insertIndex = i + 1
		break
	}

	// 如果没找到合适的位置，插入到队尾
	if insertIndex == -1 {
		currentQueueJob.CurrentSort = int64(jobs[len(jobs)-1].CurrentSort + 1)
		return append(jobs, currentQueueJob)
	}
	// 设置当前排序号
	currentQueueJob.CurrentSort = jobs[insertIndex].CurrentSort + 1

	// 更新插入位置后所有元素的CurrentSort
	for i := insertIndex + 1; i < len(jobs); i++ {
		jobs[i].CurrentSort++
	}

	// 在指定位置插入元素
	result := append(jobs[:insertIndex+1], append([]operation.CharteredOrderTripQueueJob{currentQueueJob}, jobs[insertIndex+1:]...)...)
	return result
}
