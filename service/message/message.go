package message

import (
	"app/org/scs/erpv2/api/log"
	messageModel "app/org/scs/erpv2/api/model/message"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/model/safety"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"fmt"
)

func BuildMessage(process processModel.LbpmApplyProcess, handlerUser processService.HandlerUser, lbpmMessageType int64) {
	param, _ := json.Marshal(process)

	message := messageModel.Message{
		Type:              process.MessageType(),
		RelationId:        process.FormInstanceId,
		RelationTableName: process.TableName(),
		RelationParam:     param,
		SendUserId:        process.ApplyUserId,
		SendUserName:      process.ApplyUserName,
		RecvUserId:        handlerUser.Id,
		RecvUserName:      handlerUser.Name,
		ReadStatus:        0,
		ProcessStatus:     0,
		ReadType:          0,
		Title:             process.TemplateFormName,
		Origin:            process.TemplateFormId,
		Kind:              messageModel.PROCESS_1,
	}

	message.GroupId = process.TopCorporationId

	if lbpmMessageType == util.ProcessMessageTypeForApprove {
		message.ReadType = messageModel.MESSAGE_PROCESS_2
		message.ProcessStatus = messageModel.PENDING_1
	} else if lbpmMessageType == util.ProcessMessageTypeForNotice {
		message.ReadType = messageModel.MESSAGE_READ_1
		message.ReadStatus = messageModel.UNREAD_1
	}

	NewSendMessage(message, WithWxMessage("流程审批", []string{process.TemplateFormName, fmt.Sprintf("提交人：%s", process.ApplyUserName)}, "请尽快到ERP后台管理系统进行处理", "", handlerUser.Mobile)).Send()

	return
}

type AccidentMessageForm struct {
	TrafficAccidentId int64  `json:"TrafficAccidentId"`
	RelationId        int64  `json:"RelationId"`
	DriverName        string `json:"DriverName"`
	License           string `json:"Licence"`
	ApplyUserId       int64  `json:"ApplyUserId"`
	ReceiveUserId     int64  `json:"ReceiveUserId"`
}

func BuildAccidentMessage(form AccidentMessageForm, messageType int64, tableName string) {
	applyUser := rpc.GetUserInfoById(context.TODO(), form.ApplyUserId)
	if applyUser == nil {
		log.ErrorFields("applyUser not exist", map[string]interface{}{"userId": form.ApplyUserId})
		return
	}
	receiveUser := rpc.GetUserInfoById(context.TODO(), form.ReceiveUserId)
	if receiveUser == nil {
		log.ErrorFields("receiveUser not exist", map[string]interface{}{"userId": form.ReceiveUserId})
		return
	}

	param, _ := json.Marshal(form)
	message := messageModel.Message{
		Type:              (&safety.TrafficAccident{}).MessageType(),
		RelationId:        form.RelationId,
		RelationTableName: tableName,
		RelationParam:     param,
		SendUserId:        form.ApplyUserId,
		SendUserName:      applyUser.Nickname,
		RecvUserId:        form.ReceiveUserId,
		RecvUserName:      receiveUser.Nickname,
		Title:             "事故审批",
		Origin:            tableName,
		Kind:              messageModel.PROCESS_1,
	}
	corporation := rpc.GetCorporationDetailById(context.TODO(), applyUser.CorporationId)
	if corporation != nil {
		message.GroupId = corporation.GroupId
	}

	if messageType == util.ProcessMessageTypeForApprove {
		message.ReadType = messageModel.MESSAGE_PROCESS_2
		message.ProcessStatus = messageModel.PENDING_1
	} else if messageType == util.ProcessMessageTypeForNotice {
		message.ReadType = messageModel.MESSAGE_READ_1
		message.ReadStatus = messageModel.UNREAD_1
	}

	NewSendMessage(message).Send()

	return
}
