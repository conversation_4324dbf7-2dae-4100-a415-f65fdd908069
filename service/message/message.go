package message

import (
	messageModel "app/org/scs/erpv2/api/model/message"
	processModel "app/org/scs/erpv2/api/model/process"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"fmt"
)

func BuildMessage(process processModel.LbpmApplyProcess, handlerUser processService.HandlerUser, lbpmMessageType int64) {
	param, _ := json.Marshal(process)

	message := messageModel.Message{
		Type:              process.MessageType(),
		RelationId:        process.FormInstanceId,
		RelationTableName: process.TableName(),
		RelationParam:     param,
		SendUserId:        process.ApplyUserId,
		SendUserName:      process.ApplyUserName,
		RecvUserId:        handlerUser.Id,
		RecvUserName:      handlerUser.Name,
		ReadStatus:        0,
		ProcessStatus:     0,
		ReadType:          0,
		Title:             process.TemplateFormName,
		Origin:            process.TemplateFormId,
		Kind:              messageModel.PROCESS_1,
	}

	message.GroupId = process.TopCorporationId

	if lbpmMessageType == util.ProcessMessageTypeForApprove {
		message.ReadType = messageModel.MESSAGE_PROCESS_2
		message.ProcessStatus = messageModel.PENDING_1
	} else if lbpmMessageType == util.ProcessMessageTypeForNotice {
		message.ReadType = messageModel.MESSAGE_READ_1
		message.ReadStatus = messageModel.UNREAD_1
	}

	NewSendMessage(message, WithWxMessage("流程审批", []string{process.TemplateFormName, fmt.Sprintf("提交人：%s", process.ApplyUserName)}, "请尽快到ERP后台管理系统进行处理", "", handlerUser.Mobile)).Send()

	return
}
