package message

import (
	"app/org/scs/erpv2/api/log"
	maintenanceModel "app/org/scs/erpv2/api/model/maintenance"
	processModel "app/org/scs/erpv2/api/model/process"
	settingModel "app/org/scs/erpv2/api/model/setting"
	accidentSettingModel "app/org/scs/erpv2/api/model/setting/accident"
	"app/org/scs/erpv2/api/service/process/dingTalkBpm"
	"fmt"
)

// PushWorkNotifySendDingTalk 发送钉钉工作通知
func PushWorkNotifySendDingTalk(mobile string, msg map[string]interface{}) {
	var loopCount int64
Loop:
	loopCount++
	token, err := dingTalkBpm.GetAccessToken()
	if err != nil {
		log.ErrorFields("PushWorkNotifySendDingTalk GetAccessToken error", msg)
		if loopCount > 10 {
			return
		}
		goto Loop
	}

	dingUserId, err := dingTalkBpm.GetUserId(mobile, token)
	if err != nil {
		log.ErrorFields("PushWorkNotifySendDingTalk GetUserId error", msg)
		return
	}

	err = dingTalkBpm.PushWorkMessageToDingTalk(token, []string{dingUserId}, msg)
	log.ErrorFields("PushWorkNotifySendDingTalk PushWorkMessageToDingTalk", map[string]interface{}{"err": err})
}

func BuildAccidentTimeoutPushMsg(alarmRecord accidentSettingModel.AccidentTimeoutAlarmRecord) map[string]interface{} {
	mapAlarm := map[int64]string{1: "预警", 2: "告警"}
	mapAlarmColor := map[int64]string{1: "FF9900", 2: "F03752"}
	txt := fmt.Sprintf("您发起的事故已超过%v天未结案，请关注。如已处理，请忽略。", alarmRecord.CurrentValue)
	return map[string]interface{}{
		"msgtype": "oa",
		"oa": map[string]interface{}{
			"head": map[string]string{
				"bgcolor": mapAlarmColor[alarmRecord.AlarmType],
			},
			"body": map[string]interface{}{
				"title": "事故预警",
				"form": []map[string]string{
					{
						"key":   "事故编号:",
						"value": alarmRecord.TrafficAccidentCode,
					},
					{
						"key":   "事故发起时间:",
						"value": alarmRecord.AccidentCreatedAt.String(),
					},
					{
						"key":   "预警状态:",
						"value": mapAlarm[alarmRecord.AlarmType],
					},
					{
						"key":   "提醒事项:",
						"value": txt,
					},
				},
				"author": "ERP",
			},
		},
	}
}

func BuildProcessPushMsg(alarmRecord settingModel.ProcessTimeoutAlarmRecord) map[string]interface{} {
	mapAlarm := map[int64]string{1: "预警", 2: "告警"}
	mapAlarmColor := map[int64]string{1: "FF9900", 2: "F03752"}
	txt := fmt.Sprintf("您已超过%v天未处理该流程，请尽快处理。如已处理，请忽略。", alarmRecord.CurrentValue)
	return map[string]interface{}{
		"msgtype": "oa",
		"oa": map[string]interface{}{
			"head": map[string]string{
				"bgcolor": mapAlarmColor[alarmRecord.AlarmType],
			},
			"body": map[string]interface{}{
				"title": "流程预警",
				"form": []map[string]string{
					{
						"key":   "事故编号:",
						"value": alarmRecord.TrafficAccidentCode,
					},
					{
						"key":   "流程类型:",
						"value": alarmRecord.TemplateFormName,
					},
					{
						"key":   "预警状态:",
						"value": mapAlarm[alarmRecord.AlarmType],
					},
					{
						"key":   "提醒事项:",
						"value": txt,
					},
				},
				"author": "ERP",
			},
		},
	}
}

func BuildVehicleMigrationPushMsg(record maintenanceModel.VehicleMigration) map[string]interface{} {
	//查询流程
	var process processModel.LbpmApplyProcess
	_ = process.GetProcessByItemId(record.Id, record.TableName())
	return map[string]interface{}{
		"msgtype": "oa",
		"oa": map[string]interface{}{
			"head": map[string]string{
				"bgcolor": "F03752",
			},
			"body": map[string]interface{}{
				"title": "车辆调动提醒",
				"form": []map[string]string{
					{
						"key":   "调动批次:",
						"value": record.Code,
					},
					{
						"key":   "流转至:",
						"value": process.CurrentHandlerUserName,
					},
					{
						"key":   "调入时间:",
						"value": record.UseAt.ToTime().Format("2006-01-02"),
					},
					{
						"key":   "提醒事项:",
						"value": "请留意车辆调入时间，保证车辆调入时间达到前已完成调动审批。否则将影响车辆排班信息",
					},
				},
				"author": "ERP",
			},
		},
	}
}

func BuildVehicleMigrationProcessStatusChangePushMsg(record maintenanceModel.VehicleMigration) map[string]interface{} {
	status := map[int64]string{1: "正在审批中", 2: "已审批完成", 3: "已驳回", 4: "已撤回", 5: "已废弃"}
	return map[string]interface{}{
		"msgtype": "oa",
		"oa": map[string]interface{}{
			"head": map[string]string{
				"bgcolor": "F03752",
			},
			"body": map[string]interface{}{
				"title": "车辆调动提醒",
				"form": []map[string]string{
					{
						"key":   "调动批次:",
						"value": record.Code,
					},
					{
						"key":   "流程状态:",
						"value": status[record.ApplyStatus],
					},
					{
						"key":   "调入时间:",
						"value": record.UseAt.ToTime().Format("2006-01-02"),
					},
					{
						"key":   "提醒事项:",
						"value": fmt.Sprintf("调动流程%s，请重新编排调入车辆的排班信息", status[record.ApplyStatus]),
					},
				},
				"author": "ERP",
			},
		},
	}
}
