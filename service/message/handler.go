package message

import (
	"gorm.io/gorm"
)

type MessageHandler interface {
	Handle(tx *gorm.DB, ids []int64, staffId int64) error
	MessageType() string
}

var MessageHandlers = make(map[string]MessageHandler)

func init() {
	//MessageHandlers[(&SafeRectificationsMessageHandler{}).MessageType()] = &SafeRectificationsMessageHandler{}
}

func DispatchMessageHandler(tx *gorm.DB, ids []int64, userId int64, messageType string) error {
	m, ok := MessageHandlers[messageType]
	if ok {
		return m.Handle(tx, ids, userId)
	}
	return nil
}

//type SafeRectificationsMessageHandler struct {
//}
//
//func (*SafeRectificationsMessageHandler) Handle(tx *gorm.DB, ids []int64, staffId int64) error {
//	return (&safetyModel.SafeRectifications{}).EditReadStatus(tx, ids, staffId)
//}
//func (*SafeRectificationsMessageHandler) MessageType() string {
//	return (&safetyModel.SafeRectifications{}).MessageType()
//}
