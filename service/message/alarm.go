package message

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
)

// 发送监控告警至钉钉群

type Alarm struct {
	url     string
	Content string
}

func NewAlarm(content string) *Alarm {
	return &Alarm{url: config.Config.DingTalkAlarm.Url, Content: content}
}

type Rsp struct {
	Content struct {
		ErrCode int64  `json:"errcode"`
		ErrMsg  string `json:"errmsg"`
	} `json:"Content"`
}

func (a *Alarm) Send() error {
	if a.url == "" {
		log.ErrorFields("a.url == '', 请检查配置文件正确后，通过构造器创建告警消息对象,", map[string]interface{}{"err": nil})
		return errors.New("请检查配置文件正确后，通过构造器创建告警消息对象")
	}

	if !config.Config.DingTalkAlarm.Enable {
		log.ErrorFields("配置文件未开启告警", map[string]interface{}{"err": nil})
		return nil
	}

	buf := bytes.NewBuffer([]byte(fmt.Sprintf(`{"msgtype": "text","text": {"content":"%s"}}`, a.Content)))

	request, err := http.NewRequest("POST", a.url, buf)
	if err != nil {
		log.ErrorFields("http.NewRequest err", map[string]interface{}{"err": err})
		return errors.New("http.NewRequest err")
	}

	request.Header.Set("Content-Type", "application/json")

	resp, err := config.Global.HttpClient.Do(request)
	if err != nil {
		log.ErrorFields("config.Global.HttpClient.Do err", map[string]interface{}{"err": err})
		return errors.New("config.Global.HttpClient.Do err")
	}

	defer resp.Body.Close()

	result, _ := ioutil.ReadAll(resp.Body)

	var rsp Rsp
	_ = json.Unmarshal(result, &rsp)

	if rsp.Content.ErrCode != 0 {
		return errors.New("rsp.Content.ErrCode != 0, errmsg=" + rsp.Content.ErrMsg)
	}
	return nil
}
