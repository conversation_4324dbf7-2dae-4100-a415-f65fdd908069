package message

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	messageModel "app/org/scs/erpv2/api/model/message"
	protoMini "app/org/scs/erpv2/api/proto/rpc/mini"
	"app/org/scs/erpv2/api/service/rpc"
	"context"
	"errors"
	"gorm.io/gorm"
)

//OfficialAccountMsg 微信服务号消息
type OfficialAccountMsg struct {
	templateId   string // 微信服务号模板id
	appAccountId int64  // 微信服务号appid

	First          string
	Keywords       []string
	Remark         string
	Path           string
	ReceiverMobile string
}

//NewOfficialAccountMsg 通过配置文件创建服务号消息
func NewOfficialAccountMsg(first string, keywords []string, remark string, path string, receiverMobile string) *OfficialAccountMsg {
	return &OfficialAccountMsg{First: first, Keywords: keywords, Remark: remark, Path: path, ReceiverMobile: receiverMobile, templateId: config.Config.Mini.DoorCheckTemplateId, appAccountId: config.Config.Mini.AppAccountId}
}

func (oam *OfficialAccountMsg) Send() error {

	if oam.templateId == "" || oam.appAccountId == 0 {
		log.ErrorFields("oam.templateId == '' || oam.appAccountId == 0, 请通过构造器创建微信消息对象", map[string]interface{}{"err": nil})
		return errors.New("缺失必要字段，请通过构造器创建微信消息对象")
	}

	var content []*protoMini.UniformMsgKeyValue
	if oam.templateId == config.Config.Mini.DoorCheckTemplateId {
		if len(oam.Keywords) != 2 {
			log.ErrorFields("SendOfficialAccountNotify error:keywords is invalid", map[string]interface{}{"keywords": oam.Keywords, "msgId": oam.templateId})
			return errors.New("keywords is invalid")
		}
		if oam.Remark == "" {
			oam.Remark = "请尽快处理"
		}
		content = []*protoMini.UniformMsgKeyValue{
			{
				Key:   "first",
				Value: oam.First,
				Color: "#000000",
			},
			{
				Key:   "keyword1",
				Value: oam.Keywords[0],
				Color: "#000000",
			},
			{
				Key:   "keyword2",
				Value: oam.Keywords[1],
				Color: "#000000",
			},
			{
				Key:   "remark",
				Value: oam.Remark,
				Color: "#000000",
			},
		}
	}

	err := rpc.SendUniformMsg(context.Background(), oam.appAccountId, oam.ReceiverMobile, oam.templateId, oam.Path, content)
	if err != nil {
		log.ErrorFields("rpc.SendUniformMsg error = ", map[string]interface{}{"err": err})
		return err
	}
	return nil
}

//SendMessage 发送消息
type SendMessage struct {
	tx         *gorm.DB
	ErpMessage *messageModel.Message
	WxMessage  *OfficialAccountMsg
}

type Option func(message *SendMessage)

// WithWxMessage 需要发送微信消息
func WithWxMessage(first string, keywords []string, remark string, path string, receiverMobile string) Option {
	return func(message *SendMessage) {
		message.WxMessage = NewOfficialAccountMsg(first, keywords, remark, path, receiverMobile)
	}
}

// WithTx ErpMessage 需要使用事务提交
func WithTx(tx *gorm.DB) Option {
	return func(message *SendMessage) {
		message.tx = tx
	}
}

func NewSendMessage(erpMessage messageModel.Message, opts ...Option) *SendMessage {

	msg := &SendMessage{ErpMessage: &erpMessage}

	for _, opt := range opts {
		opt(msg)
	}

	return msg
}

// Send 发送{根据参数发送 wx,erp}平台消息
func (sm *SendMessage) Send() {
	sm.SendErp()
	if sm.WxMessage != nil {
		sm.SendWx()
	}
}

// SendWx 发送{wx}平台消息
func (sm *SendMessage) SendWx() {
	err := sm.WxMessage.Send()
	if err != nil {
		log.ErrorFields("ErpMessage.WxMessage.Send err", map[string]interface{}{"err": err})
	}
}

// SendErp 发送{erp内部}平台消息
func (sm *SendMessage) SendErp() {
	if sm.tx == nil {
		err1 := sm.ErpMessage.Create()
		if err1 != nil {
			log.ErrorFields("ErpMessage.Create err", map[string]interface{}{"err": err1})
		}
	} else {
		err1 := sm.ErpMessage.AddTx(sm.tx)
		if err1 != nil {
			log.ErrorFields("ErpMessage.Create err", map[string]interface{}{"err": err1})
		}
	}
}
