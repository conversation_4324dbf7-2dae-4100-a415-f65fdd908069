package service

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	maintenanceModel "app/org/scs/erpv2/api/model/maintenance"
	processModel "app/org/scs/erpv2/api/model/process"
	protoVehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service/message"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"gorm.io/gorm"
)

func UpdateVehicleToOet(transfer maintenanceModel.VehicleMigration, transferType int64, planCode string) error {
	records := transfer.GetRecords()
	outCorporation := rpc.GetCorporationById(context.Background(), transfer.OutCorporationId)
	var outCorporationName, inCorporationName string
	if outCorporation != nil {
		outCorporationName = outCorporation.Name
	}

	inCorporation := rpc.GetCorporationById(context.Background(), transfer.InCorporationId)
	if inCorporation != nil {
		inCorporationName = inCorporation.Name
	}
	if len(records) > 0 {
		for i := range records {
			srcVehicle := protoVehicle.SrcVehicle{
				CorporationId: transfer.OutCorporationId,
				Corporation:   outCorporationName,
				Id:            records[i].VehicleId,
				License:       records[i].License,
				LineId:        records[i].OutLineId,
				LineName:      records[i].OutLine,
				TransferMode:  transferType,
				SchemeNo:      planCode,
			}
			dstVehicle := protoVehicle.DstVehicle{
				CorporationId: transfer.InCorporationId,
				Corporation:   inCorporationName,
				LineId:        records[i].InLineId,
				Line:          records[i].InLine,
			}
			err := rpc.MoveVehicle(context.Background(), srcVehicle, dstVehicle, transfer.OpUserId, transfer.UserType == util.UserTypeForAdmin)
			if err != nil {
				log.ErrorFields("VehicleUpdateToOet rpc.MoveVehicle error", map[string]interface{}{"err": err, "record": records[i]})
				return err
			}
		}
	}

	//更新状态为已调动
	err := transfer.SetDone()
	if err != nil {
		log.ErrorFields("VehicleUpdateToOet VehicleTransfer.SetDone error", map[string]interface{}{"err": err})
		return err
	}

	return nil
}

func SendVehicleMigrationProcessStatusChangeMsg(vehicleMigrationId int64) {
	record := (&maintenanceModel.VehicleMigration{}).FirstBy(vehicleMigrationId)
	if record.Id == 0 {
		return
	}
	user := rpc.GetUserInfoById(context.TODO(), record.AcceptUserId)
	if user == nil {
		return
	}

	msg := message.BuildVehicleMigrationProcessStatusChangePushMsg(record)

	message.PushWorkNotifySendDingTalk(user.Phone, msg)

	_ = rpc.VehicleMigrationStatusChange(record.Id)
}

func DeleteVehicleMigrationAndRelation(tx *gorm.DB, ids []int64) error {
	err := tx.Where("Id IN ?", ids).Delete(&maintenanceModel.VehicleMigration{}).Error
	if err != nil {
		return err
	}

	//删除关联调动车辆
	err = tx.Where("VehicleMigrationId IN ?", ids).Delete(&maintenanceModel.VehicleMigrationRecord{}).Error
	if err != nil {
		return err
	}

	//删除流程
	var formInstanceIds []int64
	model.DB().Model(&processModel.LbpmApplyProcess{}).Select("FormInstanceId").Where("ItemId IN ? AND ItemTableName = ?", ids, (&maintenanceModel.VehicleMigration{}).TableName()).Pluck("FormInstanceId", &formInstanceIds)

	if len(formInstanceIds) > 0 {
		err = DeleteProcessAndMessage(tx, formInstanceIds)
		if err != nil {
			return err
		}
	}

	return nil
}
