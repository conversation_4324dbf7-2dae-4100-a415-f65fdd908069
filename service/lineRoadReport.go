package service

import (
	"app/org/scs/erpv2/api/database"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	operationModel "app/org/scs/erpv2/api/model/operation"
	oet_scs_api_iss "app/org/scs/erpv2/api/proto/rpc/line_road"
	protoVehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service/rpc"
	"context"
	"encoding/json"
	"fmt"
	"gorm.io/gorm"
	"strings"
	"time"
)

var VehicleLineRoadReportCalcFlag = false

// VehicleLineRoadReportCalcTimeRange 以ic卡为数据源匹配调度数据
func VehicleLineRoadReportCalcTimeRange(ctx context.Context, startAt, endAt string, topCorporationId int64) {
	db, err := database.InitIcMysqlConnect()
	if err != nil {
		log.ErrorFields("InitIcMysqlConnect  error", map[string]interface{}{"error": err.Error()})
	}
	VehicleLineRoadReportCalcFlag = true
	defer func() {
		gepDb, _ := db.DB()
		_ = gepDb.Close()
		VehicleLineRoadReportCalcFlag = false
	}()
	startTime, _ := time.ParseInLocation("20060102", startAt, time.Local)
	endTime, _ := time.ParseInLocation("20060102", endAt, time.Local)
	// 清空记录
	_ = (&operationModel.VehicleLineIcReport{}).ClearTableDataByDateRange(startTime, endTime, 2)
	// 1线路数据
	oetLines := rpc.GetLinesWithTopCorporationId(context.Background(), topCorporationId, 1)
	posLineNoMap := make(map[string]model.LinRoadInfo)
	if oetLines != nil {
		for _, oetLine := range oetLines {
			if oetLine.PosLineNo == "" {
				continue
			}
			thirdLines := strings.Split(oetLine.PosLineNo, ",")
			for j := range thirdLines {
				posLineNoMap[thirdLines[j]] = model.LinRoadInfo{LineId: oetLine.Id, LineName: oetLine.Name}
			}
		}
	}
	for startTime.Unix() <= endTime.Unix() {
		fmt.Println(fmt.Sprintf("正在计算,%s", startTime.Format("20060102")))
		err := VehicleLineRoadReportCalc(ctx, db, startTime.Format("20060102"), topCorporationId, posLineNoMap)
		if err != nil {
			log.ErrorFields("VehicleLineRoadReportCalc  error", map[string]interface{}{"error": err.Error()})
		}
		startTime = startTime.AddDate(0, 0, 1)
	}
}

func VehicleLineRoadReportCalc(ctx context.Context, db *gorm.DB, txnDate string, topCorporationId int64, posLineNoMap map[string]model.LinRoadInfo) error {
	//startAt, _ := time.ParseInLocation("20060102", txnDate, time.Local)
	//endAt := time.Date(startAt.Year(), startAt.Month(), startAt.Day(), 23, 59, 59, 0, startAt.Location())
	// 一、原始数据获取
	// 原始数据获取 2IC卡营收数据
	cardData := VehicleICColumnData(db, txnDate, txnDate, "")
	// 原始数据获取，里程数和完成圈次
	//reports, _ := (&operationModel.LineVehicleMileageReport{}).GetByRangeDay(0, "vehicle", "detail", "", []int64{}, []int64{}, []int64{}, []int64{}, startAt, endAt, model.Sortable{}, model.Paginator{Limit: 0})
	// 二、数据处理
	//licenseMileageMap := make(map[string][]operationModel.LineVehicleMileageReport)
	//for _, item := range reports {
	//	licenseMileageMap[item.License] = append(licenseMileageMap[item.License], item)
	//}
	// 车牌 时间 line1_line2_营收数据
	resultMap := make(map[string]map[string]map[string]VehicleICColumn)
	licenseToVehicleMap := make(map[string]*protoVehicle.OetVehicleItem)
	vehicleIdToLineRoadFormItems := make(map[int64]map[string][]*oet_scs_api_iss.LineRoadFormItem)
	corporationMap := make(map[int64]string)
	for index, v := range cardData {
		vehicleItem, exist := licenseToVehicleMap[v.BusNumpalte]
		if !exist {
			vehicleItem = rpc.GetVehicleWithLicense(ctx, &protoVehicle.GetVehicleWithLicenseRequest{License: v.BusNumpalte, CorporationId: topCorporationId})
			licenseToVehicleMap[v.BusNumpalte] = vehicleItem
		}
		var vehicleId int64
		var vehicleRoadFormList []*oet_scs_api_iss.LineRoadFormItem
		if vehicleItem != nil {
			vehicleId = vehicleItem.Id
			if _, exist = vehicleIdToLineRoadFormItems[vehicleId]; !exist {
				vehicleIdToLineRoadFormItems[vehicleId] = make(map[string][]*oet_scs_api_iss.LineRoadFormItem)
			}
			vehicleRoadFormList, exist = vehicleIdToLineRoadFormItems[vehicleId][v.TxnDate]
			sTime, _ := time.ParseInLocation("20060102", v.TxnDate, time.Local)
			eTime := time.Date(sTime.Year(), sTime.Month(), sTime.Day(), 23, 59, 59, 0, time.Local)
			if !exist {
				vehicleRoadFormList, _ = rpc.GetVehicleLineRoadFormList(ctx, &oet_scs_api_iss.GetVehicleLineRoadFormsRequest{
					VehicleId: vehicleId,
					StartAt:   sTime.Unix(),
					EndAt:     eTime.Unix(),
					Offset:    0,
					Limit:     9999,
				})
				vehicleIdToLineRoadFormItems[vehicleId][v.TxnDate] = vehicleRoadFormList
			}
			bytes, _ := json.Marshal(vehicleRoadFormList)
			fmt.Println(fmt.Sprintf("车牌号为%s,车辆id为%d,开始时间为:%s-%d,结束时间为:%s-%d,获取到的调度数据为:%s",
				v.BusNumpalte, vehicleId,
				sTime.Format("20060102"), sTime.Unix(),
				eTime.Format("20060102"), eTime.Unix(),
				string(bytes),
			))
		}
		lineRoadItem := getVehicleRoadInfo(vehicleRoadFormList, v)
		if lineRoadItem != nil {
			cardData[index].ErpLineId = lineRoadItem.LineId
			cardData[index].ErpLienName = lineRoadItem.Line
			cardData[index].Mileage += lineRoadItem.ActualMileage
			cardData[index].CompletedCircle = 1
			corporationName, exist := corporationMap[lineRoadItem.CorporationId]
			if !exist {
				detailById := rpc.GetCorporationDetailById(ctx, lineRoadItem.CorporationId)
				if detailById != nil {
					corporationName = detailById.Item.Name
				}
				corporationMap[lineRoadItem.CorporationId] = corporationName
			}
			cardData[index].CorporationId = lineRoadItem.CorporationId
			cardData[index].CorporationName = corporationName
		}
		if _, ok := resultMap[v.BusNumpalte]; !ok {
			resultMap[v.BusNumpalte] = make(map[string]map[string]VehicleICColumn)
		}

		if _, ok := resultMap[v.BusNumpalte][v.TxnDate]; !ok {
			resultMap[v.BusNumpalte][v.TxnDate] = make(map[string]VehicleICColumn)
		}
		key := fmt.Sprintf("%s_%d", cardData[index].UpLineId, cardData[index].ErpLineId)
		data := resultMap[v.BusNumpalte][v.TxnDate][key]
		data.PeopleCount++
		data.ErpLienName = cardData[index].ErpLienName
		data.ErpLineId = cardData[index].ErpLineId
		data.UpLineId = cardData[index].UpLineId
		data.LineName = cardData[index].LineName
		data.TxnAmt += cardData[index].TxnAmt
		data.VehicleId = vehicleId
		data.UpLineToErp = posLineNoMap[data.UpLineId].LineId
		data.LineNameToErp = posLineNoMap[data.UpLineId].LineName
		data.CorporationId = cardData[index].CorporationId
		data.CorporationName = cardData[index].CorporationName
		data.CompletedCircle += cardData[index].CompletedCircle
		data.Mileage += cardData[index].Mileage
		resultMap[v.BusNumpalte][v.TxnDate][key] = data
	}
	// 三、数据输出
	//var result []operationModel.VehicleLineIcReport
	for license, dateMap := range resultMap {
		for date, keyMap := range dateMap {
			for _, data := range keyMap {
				data.BusNumpalte = license
				data.TxnDate = date
				var isMatch int64
				if data.UpLineToErp == data.ErpLineId {
					isMatch = 1
				} else {
					isMatch = 2
				}
				data := operationModel.VehicleLineIcReport{
					BusNumpalte:     data.BusNumpalte,
					TxnDate:         data.TxnDate,
					UpLineId:        data.UpLineId,
					LineName:        data.LineName,
					TxnAmt:          data.TxnAmt,
					VehicleId:       data.VehicleId,
					ErpLineId:       data.ErpLineId,
					ErpLienName:     data.ErpLienName,
					Mileage:         data.Mileage,
					CompletedCircle: data.CompletedCircle,
					UpLineToErp:     data.UpLineToErp,
					LineNameToErp:   data.LineNameToErp,
					PeopleCount:     data.PeopleCount,
					CorporationId:   data.CorporationId,
					CorporationName: data.CorporationName,
					IsMatch:         isMatch,
					ReportType:      2,
				}
				err := data.TransactionCreate(model.DB())
				if err != nil {
					log.ErrorFields("VehicleLineIcReport  TransactionCreate", map[string]interface{}{"error": err.Error()})
				}
			}
		}
	}
	return nil
}

func getVehicleRoadInfo(arr []*oet_scs_api_iss.LineRoadFormItem, card VehicleICColumn) (data *oet_scs_api_iss.LineRoadFormItem) {
	if arr == nil {
		return nil
	}
	reportAt, _ := time.ParseInLocation("20060102150405", card.TxnDate+card.TxnTime, time.Local)
	reportAtUnix := reportAt.Unix()
	for _, v := range arr {
		if v == nil {
			continue
		}
		if reportAtUnix >= v.ActualDepartAt && reportAtUnix <= v.ActualArriveAt {
			data = v
			break
		}
	}
	return
}

// VehicleLineRoadReportCalcTimeRangeReverse 以调度数据为数据源匹配所有的ic数据
func VehicleLineRoadReportCalcTimeRangeReverse(ctx context.Context, startAt, endAt string, topCorporationId int64) {
	db, err := database.InitIcMysqlConnect()
	if err != nil {
		log.ErrorFields("InitIcMysqlConnect  error", map[string]interface{}{"error": err.Error()})
	}
	VehicleLineRoadReportCalcFlag = true
	defer func() {
		gepDb, _ := db.DB()
		_ = gepDb.Close()
		VehicleLineRoadReportCalcFlag = false
	}()
	startTime, _ := time.ParseInLocation("20060102", startAt, time.Local)
	endTime, _ := time.ParseInLocation("20060102", endAt, time.Local)

	// 获取车辆信息车辆数据
	vehicles, count := rpc.GetVehiclesWithTopCorporationId(context.Background(), topCorporationId)
	fmt.Println("=======vehicles- count=======", count)
	if vehicles == nil || len(vehicles) == 0 {
		fmt.Println("未获取到车辆信息")
		return
	}
	// 线路数据
	oetLines := rpc.GetLinesWithTopCorporationId(context.Background(), topCorporationId, 1)
	posLineNoMap := make(map[string]model.LinRoadInfo)
	if oetLines != nil {
		for _, oetLine := range oetLines {
			if oetLine.PosLineNo == "" {
				continue
			}
			thirdLines := strings.Split(oetLine.PosLineNo, ",")
			for j := range thirdLines {
				posLineNoMap[thirdLines[j]] = model.LinRoadInfo{LineId: oetLine.Id, LineName: oetLine.Name}
			}
		}
	}
	// 清空记录
	_ = (&operationModel.VehicleLineIcReport{}).ClearTableDataByDateRange(startTime, endTime, 1)
	_ = (&operationModel.VehicleLineIcReportLine{}).ClearTableDataByDateRange(startTime, endTime)
	for startTime.Unix() <= endTime.Unix() {
		fmt.Println(fmt.Sprintf("正在计算,%s", startTime.Format("20060102")))
		err := VehicleLineRoadReportCalcReverse(context.Background(), db, startTime.Format("20060102"), topCorporationId, vehicles, posLineNoMap)
		if err != nil {
			log.ErrorFields("VehicleLineRoadReportCalc  error", map[string]interface{}{"error": err.Error()})
		}
		startTime = startTime.AddDate(0, 0, 1)
	}
}

func VehicleLineRoadReportCalcReverse(ctx context.Context, db *gorm.DB, txnDate string, topCorporationId int64, vehicles []*protoVehicle.OetVehicleItem, posLineNoMap map[string]model.LinRoadInfo) error {
	startAt, _ := time.ParseInLocation("20060102", txnDate, time.Local)
	//endAt := time.Date(startAt.Year(), startAt.Month(), startAt.Day(), 23, 59, 59, 0, startAt.Location())
	// 一、原始数据获取
	// 1、IC卡营收数据
	cardData := VehicleICColumnData(db, txnDate, txnDate, "")
	// 2、里程数和完成圈次
	//reports, _ := (&operationModel.LineVehicleMileageReport{}).GetByRangeDay(0, "driverAndVehicle", "detail", "", []int64{}, []int64{}, []int64{}, []int64{}, startAt, endAt, model.Sortable{}, model.Paginator{Limit: 0})
	// 二、数据处理
	//licenseMileageMap := make(map[string]map[int64][]operationModel.LineVehicleMileageReport) // 按车牌路边分类的公里圈次信息
	//for _, item := range reports {
	//	if _, exist := licenseMileageMap[item.License]; !exist {
	//		licenseMileageMap[item.License] = make(map[int64][]operationModel.LineVehicleMileageReport)
	//	}
	//	licenseMileageMap[item.License][item.LineId] = append(licenseMileageMap[item.License][item.Id], item)
	//}
	licenseIcData := make(map[string][]VehicleICColumn) // 按车牌分类的ic卡数据
	for _, item := range cardData {
		licenseIcData[item.BusNumpalte] = append(licenseIcData[item.BusNumpalte], item)
	}
	// 三、结果
	sTime, _ := time.ParseInLocation("20060102", txnDate, time.Local)
	eTime := time.Date(sTime.Year(), sTime.Month(), sTime.Day(), 23, 59, 59, 0, time.Local)
	corporationMap := make(map[int64]string)
	for _, vehicle := range vehicles {
		vehicleRoadFormList, _ := rpc.GetVehicleLineRoadFormList(context.Background(), &oet_scs_api_iss.GetVehicleLineRoadFormsRequest{
			VehicleId: vehicle.Id,
			StartAt:   sTime.Unix(),
			EndAt:     eTime.Unix(),
			Offset:    0,
			Limit:     9999,
		})
		if vehicleRoadFormList == nil || len(vehicleRoadFormList) == 0 {
			continue
		}
		roadMap := make(map[int64][]*oet_scs_api_iss.LineRoadFormItem) // 按线路分类
		for _, roadInfo := range vehicleRoadFormList {
			corporationName, exist := corporationMap[roadInfo.CorporationId]
			if !exist {
				detailById := rpc.GetCorporationDetailById(context.Background(), roadInfo.CorporationId)
				if detailById != nil {
					corporationName = detailById.Item.Name
				}
				corporationMap[roadInfo.CorporationId] = corporationName
			}
			roadMap[roadInfo.LineId] = append(roadMap[roadInfo.LineId], roadInfo)
		}
		for _, roadList := range roadMap {
			var data operationModel.VehicleLineIcReport
			var lines []operationModel.VehicleLineIcReportLine
			data.TxnDate = txnDate
			data.ReportType = 1
			data.IsMatch = 1
			for _, roadInfo := range roadList {
				corporationName := corporationMap[roadInfo.CorporationId]
				data.ErpLineId = roadInfo.LineId
				data.ErpLienName = roadInfo.Line
				data.VehicleId = vehicle.Id
				data.BusNumpalte = vehicle.License
				data.CorporationName = corporationName
				data.CorporationId = roadInfo.CorporationId
				data.Mileage += roadInfo.ActualMileage
				data.CompletedCircle++
				reportLine, _ := GetIcCardInfo(licenseIcData[vehicle.License], roadInfo.ActualDepartAt, roadInfo.ActualArriveAt)
				if reportLine != nil {
					for index, v := range reportLine {
						reportLine[index].UpLineToErp = posLineNoMap[v.UpLineId].LineId
						reportLine[index].LineNameToErp = posLineNoMap[v.UpLineId].LineName
						lines = append(lines, operationModel.VehicleLineIcReportLine{
							TxnDate:       reportLine[index].TxnDate,
							TxnTime:       reportLine[index].TxnTime,
							UpLineId:      reportLine[index].UpLineId,
							LineName:      reportLine[index].LineName,
							TxnAmt:        reportLine[index].TxnAmt,
							UpLineToErp:   reportLine[index].UpLineToErp,
							LineNameToErp: reportLine[index].LineNameToErp,
						})
					}
				}
			}

			//data.Mileage, data.CompletedCircle = SumMileageInfo(licenseMileageMap[vehicle.License][lineId])
			icLineMpa := make(map[int64][]operationModel.VehicleLineIcReportLine)
			for _, v := range lines {
				icLineMpa[v.UpLineToErp] = append(icLineMpa[v.UpLineToErp], v)
			}
			if len(icLineMpa) != 0 {
				for upLineToErpId, lineArr := range icLineMpa {
					newData := data
					if newData.ErpLineId != upLineToErpId {
						newData.IsMatch = 2
					} else {
						newData.IsMatch = 1
					}
					newData.PeopleCount = int64(len(lineArr))
					for _, v := range lineArr {
						newData.TxnAmt += v.TxnAmt
						newData.UpLineId = v.UpLineId
						newData.LineName = v.LineName
						newData.UpLineToErp = v.UpLineToErp
						newData.LineNameToErp = v.LineNameToErp
					}
					err := newData.TransactionCreate(model.DB())
					if err != nil {
						log.ErrorFields("data  TransactionCreate error", map[string]interface{}{"error": err.Error()})
						continue
					}
					for index := range lineArr {
						lineArr[index].VehicleLineIcReportId = newData.Id
					}
					if len(lineArr) != 0 {
						err = (&operationModel.VehicleLineIcReportLine{}).TransactionBatchCreate(model.DB(), lineArr, startAt)
						if err != nil {
							log.ErrorFields("VehicleLineIcReportLine  TransactionBatchCreate error", map[string]interface{}{"error": err.Error()})
							continue
						}
					}
				}
			} else {
				data.IsMatch = 2
				err := data.TransactionCreate(model.DB())
				if err != nil {
					log.ErrorFields("data  TransactionCreate error", map[string]interface{}{"error": err.Error()})
					continue
				}
			}
		}
	}
	return nil
}

func SumMileageInfo(arr []operationModel.LineVehicleMileageReport) (mileage, completedCircle int64) {
	if arr == nil || len(arr) == 0 {
		return
	}
	for _, v := range arr {
		mileage += v.TotalMileage
		completedCircle += v.FullDoneCircle
	}
	return
}

func GetIcCardInfo(cardList []VehicleICColumn, actualDepartAt, actualArriveAt int64) (result []VehicleICColumn, totalAmt int64) {
	if cardList == nil || len(cardList) == 0 {
		return
	}
	for _, card := range cardList {
		if card.TxnDate == "" || card.TxnTime == "" {
			continue
		}
		txnTime, _ := time.ParseInLocation("20060102150405", card.TxnDate+card.TxnTime, time.Local)
		if txnTime.Unix() >= actualDepartAt && txnTime.Unix() <= actualArriveAt {
			result = append(result, card)
			totalAmt += card.TxnAmt
		}
	}
	return
}
