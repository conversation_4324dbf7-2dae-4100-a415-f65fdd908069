package auth

import (
	"app/org/scs/erpv2/api/log"
	protooetcorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/micro/go-micro/v2/metadata"
	"strconv"
	"strings"
)

const (
	CONTEXT_TOKEN_NAME         = "X-Token"
	CONTEXT_STAFF_ID_NAME      = "X-Staff-Id"
	CONTEXT_USER_ID_NAME       = "X-User-Id"
	CONTEXT_REAL_IP_NAME       = "X-Real-Ip"
	CONTEXT_PHONE_NAME         = "X-Phone"
	CONTEXT_CORPORATIONID_NAME = "X-Corporationid"
	CONTEXT_CLIENT_TYPE_NAME   = "X-Client-Type"
	CONTEXT_IS_ADMIN           = "X-User-Isadmin"
	CONTEXT_USER_TYPE          = "X-User-Type"

	ClientTypeForWechat   = "0"
	ClientTypeForDingTalk = "7"
)

type ContextMeta metadata.Metadata

// GetContextMeta 获取context meta 信息
func GetContextMeta(ctx context.Context) ContextMeta {
	meta, _ := metadata.FromContext(ctx)
	return ContextMeta(meta)

}

func (cm ContextMeta) Get(key string) string {
	val, ok := cm[key]
	if ok {
		return val
	}
	// attempt to get lower case
	val, _ = cm[strings.Title(key)]
	return val

}

// Auth 登录授权信息
type Auth struct {
	userId         int64
	staffId        int64
	publicUserId   int64
	phone          string
	corporationId  int64
	clineIp        string
	user           *AuthUser
	UserType       int64 //1：单机构用户  2：多机构用户  3：admin
	clientType     string
	authClientType string
}

// Check 校验授权
func Check(ctx context.Context, token string) bool {
	meta := GetContextMeta(ctx)
	//如果请求参数中不存在token 则使用context中的x-token
	if token == "" || len(token) <= 32 {
		token = meta.Get(CONTEXT_TOKEN_NAME)
	}

	if token == "" || len(token) <= 32 {
		log.ErrorFields("auth check token invalid", map[string]interface{}{"token": token})
		return false
	}
	//解析token中的参数
	_, err := base64.StdEncoding.DecodeString(token[32:])
	if err != nil {
		log.ErrorFields("base64.StdEncoding.DecodeString token error", map[string]interface{}{"err": err})
		return false
	}
	return true

}

// User 授权用户信息
func User(ctx context.Context) *Auth {
	var auth Auth
	meta := GetContextMeta(ctx)

	fmt.Printf("auth.meta=: %+v \n", meta)
	token := meta.Get(CONTEXT_TOKEN_NAME)

	//解析token中的参数
	if len(token) > 32 {
		tokenUser, _ := base64.StdEncoding.DecodeString(token[32:])
		auth.parseToken(tokenUser)
	}
	fmt.Printf("auth.parsedToken=: %+v \n", auth)

	//获取context中的client IP
	auth.clineIp = meta.Get(CONTEXT_REAL_IP_NAME)
	auth.clientType = "pc"
	//判断用户是admin、还是单机构、多机构
	isAdmin := meta.Get(CONTEXT_IS_ADMIN)
	if isAdmin == "true" {
		auth.UserType = util.UserTypeForAdmin
	} else {
		auth.UserType, _ = strconv.ParseInt(meta.Get(CONTEXT_USER_TYPE), 10, 64)
	}

	auth.clineIp = meta.Get(CONTEXT_REAL_IP_NAME)

	//PublicUserId存在 说明是小程序的请求 此字段代表的是账号的ID
	if meta.Get(CONTEXT_CLIENT_TYPE_NAME) == ClientTypeForWechat || (meta.Get(CONTEXT_CLIENT_TYPE_NAME) == ClientTypeForDingTalk && auth.authClientType == ClientTypeForDingTalk) {
		auth.userId = auth.publicUserId
		//非小程序用户才可以使用token中的corporationId，小程序用户CorporationId统一为顶级机构
		auth.corporationId = 0
		auth.clientType = "mobile"
	}

	//如果token中解析出的userId为空  则尝试去取context中的userId
	if auth.userId == 0 && meta.Get(CONTEXT_USER_ID_NAME) != "" {
		auth.userId, _ = strconv.ParseInt(meta.Get(CONTEXT_USER_ID_NAME), 10, 64)
	}

	//如果token中解析出的staffId为空  则尝试去取context中的staffId
	if auth.staffId == 0 && meta.Get(CONTEXT_STAFF_ID_NAME) != "" {
		auth.staffId, _ = strconv.ParseInt(meta.Get(CONTEXT_STAFF_ID_NAME), 10, 64)
	}

	//如果token中解析出的phone为空  则尝试去取context中的phone
	if auth.phone == "" && meta.Get(CONTEXT_PHONE_NAME) != "" {
		auth.phone = meta.Get(CONTEXT_PHONE_NAME)
	}

	//如果token中解析出的corporationId为空  则尝试去取context中的corporationId
	if auth.corporationId == 0 && meta.Get(CONTEXT_CORPORATIONID_NAME) != "" {
		auth.corporationId, _ = strconv.ParseInt(meta.Get(CONTEXT_CORPORATIONID_NAME), 10, 64)
	}

	//如果userId为空,但phone不为空，则通过phone获取user
	if auth.userId == 0 && auth.phone != "" && auth.corporationId != 0 {
		user := rpc.GetUserInfoByPhone(ctx, auth.corporationId, auth.phone)
		if user != nil {
			auth.userId = user.Id
		}
	}

	//小程序用户需通过userid获取所属机构
	if auth.userId > 0 && auth.corporationId == 0 {
		auth.corporationId = auth.GetUser().CorporationId
	}

	fmt.Printf("auth.user=: %+v \n", auth)
	return &auth
}

func (a *Auth) parseToken(token []byte) {
	var tokenParam map[string]interface{}
	dec := json.NewDecoder(bytes.NewReader(token))
	dec.UseNumber()
	_ = dec.Decode(&tokenParam)
	fmt.Printf("auth.token=: %+v \n", tokenParam)

	if _, ok := tokenParam["UserId"]; ok {
		a.userId, _ = tokenParam["UserId"].(json.Number).Int64()
	}
	if _, ok := tokenParam["ClientType"]; ok {
		clientType, _ := tokenParam["ClientType"].(json.Number).Int64()
		a.authClientType = fmt.Sprintf("%v", clientType)
	}

	if _, ok := tokenParam["StaffId"]; ok {
		a.staffId, _ = tokenParam["StaffId"].(json.Number).Int64()
	}

	if _, ok := tokenParam["PublicUserId"]; ok {
		a.publicUserId, _ = tokenParam["PublicUserId"].(json.Number).Int64()
	}

	if _, ok := tokenParam["CorporationId"]; ok {
		a.corporationId, _ = tokenParam["CorporationId"].(json.Number).Int64()
	}

	if _, ok := tokenParam["Phone"]; ok {
		a.phone = tokenParam["Phone"].(string)
	}
}

//func (a *Auth) GetStaffId() int64 {
//	return a.staffId
//}

func (a *Auth) GetUserId() int64 {
	return a.userId
}

func (a *Auth) GetUser() *AuthUser {
	//如果userId存在  则获取账号信息
	if a.userId != 0 && a.UserType != util.UserTypeForAdmin {
		return NewUserById(a.userId)
	}
	return &AuthUser{}
}

func (a *Auth) GetPhone() string {
	return a.phone
}
func (a *Auth) GetClientIp() string {
	return a.clineIp
}
func (a *Auth) GetClientType() string {
	return a.clientType
}

func (a *Auth) GetCorporationId() int64 {
	return a.corporationId
}

func (a *Auth) GetTopCorporationId() int64 {
	if a.HasUser() {
		return a.GetUser().GetTopCorporationId()
	}

	return 0
}

func (a *Auth) HasPermission(permission string) bool {
	return a.user.HasPermission(permission)
}

//func (a *Auth) HasStaff() bool {
//	return a.staffId != 0
//}

func (a *Auth) HasUser() bool {
	return a.userId != 0
}

func (a *Auth) IsAdmin() bool {
	return a.UserType == util.UserTypeForAdmin
}

func (a *Auth) IsMultipleCorporation() bool {
	return a.UserType == util.UserTypeForMultipleCorporation
}

// AuthUser 授权的账号信息
type AuthUser struct {
	Id               int64
	Account          string
	Name             string
	Phone            string
	CorporationId    int64 //所属机构
	CorporationName  string
	TopCorporationId int64 //顶级机构
	CorporationType  int64 // 机构类型
	permissions      []string
}

func NewUserById(userId int64) *AuthUser {
	user := rpc.GetUserInfoById(context.TODO(), userId)
	if user == nil {
		return &AuthUser{}
	}
	var (
		topCorporationId int64
		corporationType  int64
	)
	if user.CorporationId != 0 {
		corporation := rpc.GetCorporationDetailById(context.Background(), user.CorporationId)
		if corporation != nil {
			topCorporationId = corporation.GroupId
			user.Corporation = corporation.Item.Name
			corporationType = getCorporationType(user.CorporationId, corporation)
		}
	}

	return &AuthUser{
		Id:               user.Id,
		Account:          user.Username,
		Name:             user.Nickname,
		Phone:            user.Phone,
		permissions:      user.ApiPermissions,
		TopCorporationId: topCorporationId,
		CorporationId:    user.CorporationId,
		CorporationType:  corporationType,
		CorporationName:  user.Corporation,
	}
}

func getCorporationType(corId int64, item *protooetcorporation.GetCorporationDetailByIdResponse) int64 {
	if item == nil {
		return 0
	}
	switch corId {
	case item.GroupId:
		return 1 // model.GROUP
	case item.CompanyId:
		return 2
	case item.BranchId:
		return 3
	case item.DepartmentId:
		return 4
	case item.FleetId:
		return 5 // model.FLEET
	}
	return 0
}

func NewUserByPhone(corporationId int64, phone string) *AuthUser {
	user := rpc.GetUserInfoByPhone(context.TODO(), corporationId, phone)
	if user == nil {
		return &AuthUser{}
	}
	var topCorporationId int64
	if user.CorporationId != 0 {
		corporation := rpc.GetCorporationDetailById(context.Background(), user.CorporationId)
		if corporation != nil {
			topCorporationId = corporation.GroupId
			user.Corporation = corporation.Item.Name
		}
	}

	return &AuthUser{
		Id:               user.Id,
		Account:          user.Username,
		Name:             user.Nickname,
		Phone:            user.Phone,
		permissions:      user.ApiPermissions,
		TopCorporationId: topCorporationId,
		CorporationId:    user.CorporationId,
		CorporationName:  user.Corporation,
	}
}
func (u *AuthUser) GetName() string {
	return u.Name
}
func (u *AuthUser) GetTopCorporationId() int64 {
	return u.TopCorporationId
}

func (u *AuthUser) GetPermissions() []string {
	return u.permissions
}

func (u *AuthUser) SetPermissions(permissions []string) {
	u.permissions = append(u.permissions, permissions...)
}

func (u *AuthUser) HasPermission(permission string) bool {
	for i := range u.permissions {
		if u.permissions[i] == permission {
			return true
		}
	}
	return false
}

type UserInfo struct {
	UserId   int64  `json:"UserId"`
	UserName string `json:"UserName"`
	NickName string `json:"NickName"`
}
