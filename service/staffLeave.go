package service

import (
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/util"
	"gorm.io/gorm"
	"time"
)

// GetDriverLeaveDay 获取司机在某天、某个请假类型下的请假天数 单位：天数*10
func GetDriverLeaveDay(driverId, leaveType int64, reportAt time.Time) int64 {
	leaveRecord := (&hrModel.ApplyLeaveRecordDate{}).GetDriverLeaveRecord(driverId, leaveType, reportAt, nil)
	if leaveRecord.Id == 0 {
		return 0
	}
	return 1 * 10
}

// GetDriverLeaveReportAtByRange 获取司机在对应时间段范围内完整一天的请假日期以及半天的请假日期
func GetDriverLeaveReportAtByRange(driverId int64, startAt, endAt time.Time, exceptLeaveTypes []int64) ([]string, []string) {
	var oneDayReportAts []string
	var halfDayReportAts []string
	reportAts := util.GetDateFromRangeTime(startAt, endAt)
	for i := range reportAts {
		reportAt, _ := time.ParseInLocation(model.DateFormat, reportAts[i], time.Local)
		leaveRecord := (&hrModel.ApplyLeaveRecordDate{}).GetDriverLeaveRecord(driverId, 0, reportAt, exceptLeaveTypes)
		if leaveRecord.Id > 0 {
			oneDayReportAts = append(oneDayReportAts, reportAts[i])
		}
	}

	return oneDayReportAts, halfDayReportAts
}

func DeleteStaffLeaveRecordAndRelation(tx *gorm.DB, ids []int64) error {
	err := tx.Where("Id IN ?", ids).Delete(&hrModel.ApplyLeaveRecord{}).Error
	if err != nil {
		return err
	}

	//删除关联日期
	err = tx.Where("ApplyLeaveRecordId IN ?", ids).Delete(&hrModel.ApplyLeaveRecordDate{}).Error
	if err != nil {
		return err
	}

	//删除流程
	var formInstanceIds []int64
	model.DB().Model(&processModel.LbpmApplyProcess{}).Select("FormInstanceId").Where("ItemId IN ? AND ItemTableName = ?", ids, (&hrModel.ApplyLeaveRecord{}).TableName()).Pluck("FormInstanceId", &formInstanceIds)

	if len(formInstanceIds) > 0 {
		err = DeleteProcessAndMessage(tx, formInstanceIds)
		if err != nil {
			return err
		}
	}

	return nil
}

func DeleteBecomeWorkerRecordAndRelation(tx *gorm.DB, ids []int64) error {
	err := tx.Where("Id IN ?", ids).Delete(&hrModel.DriverBecomeWorkerRecord{}).Error
	if err != nil {
		return err
	}

	//删除流程
	var formInstanceIds []int64
	model.DB().Model(&processModel.LbpmApplyProcess{}).Select("FormInstanceId").Where("ItemId IN ? AND ItemTableName = ?", ids, (&hrModel.DriverBecomeWorkerRecord{}).TableName()).Pluck("FormInstanceId", &formInstanceIds)

	if len(formInstanceIds) > 0 {
		err = DeleteProcessAndMessage(tx, formInstanceIds)
		if err != nil {
			return err
		}
	}

	return nil
}
