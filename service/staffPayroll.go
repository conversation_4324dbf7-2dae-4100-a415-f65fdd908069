package service

import (
	"app/org/scs/erpv2/api/handler/setting"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	operationModel "app/org/scs/erpv2/api/model/operation"
	assessModel "app/org/scs/erpv2/api/model/safety/assess"
	settingModle "app/org/scs/erpv2/api/model/setting"
	protoStaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"fmt"
	"math"
	"math/big"
	"strconv"
	"strings"

	"time"
)

type LineDriverWorkReportItem struct {
	operationModel.LineDriverWorkReportData
	LineWorkDay                             int64   `json:"LineWorkDay"`                             //汇总线路天数 单位：天数*10
	InFrequencyTypeCircleWorkTimeLength     int64   `json:"InFrequencyTypeCircleWorkTimeLength"`     //班制内圈次岗上时长 单位：秒
	InFrequencyTypeCircleNotWorkTimeLength  int64   `json:"InFrequencyTypeCircleNotWorkTimeLength"`  //班制内圈次岗下时长 单位：秒
	OutFrequencyTypeCircleWorkTimeLength    int64   `json:"OutFrequencyTypeCircleWorkTimeLength"`    //班制外圈次岗上时长 单位：秒
	OutFrequencyTypeCircleNotWorkTimeLength int64   `json:"OutFrequencyTypeCircleNotWorkTimeLength"` //班制外圈次岗下时长 单位：秒
	PlanWorkDay                             int64   `json:"PlanWorkDay"`                             //计划出勤天数
	PlanWorkRate                            float64 `json:"PlanWorkRate"`                            //班次执行率 单位：%
	PlanTotalCircle                         int64   `json:"PlanTotalCircle"`                         //计划排班总圈数 单位：圈数*10
	InPlanDoneCircle                        int64   `json:"InPlanDoneCircle"`                        //排班内完成圈次
	OutPlanDoneCircle                       int64   `json:"OutPlanDoneCircle"`                       //排班外完成圈次
	WorkAndNotWorkTimeLength
	JobNumber string `json:"JobNumber"` //司机工号
}
type WorkAndNotWorkTimeLength struct {
	FullWorkTimeLength            []operationModel.LineReportSettingTimeLength `json:"FullWorkTimeLength"`            //全程岗上时长 单位：秒
	FullNotWorkTimeLength         []operationModel.LineReportSettingTimeLength `json:"FullNotWorkTimeLength"`         //全程岗下时长 单位：秒
	FullSinglePassWorkTimeLength  []operationModel.LineReportSettingTimeLength `json:"FullSinglePassWorkTimeLength"`  //全程单趟工作时长 单位：秒
	RangeWorkTimeLength           []operationModel.LineReportSettingTimeLength `json:"RangeWorkTimeLength"`           //区间岗上时长 单位：秒
	RangeNotWorkTimeLength        []operationModel.LineReportSettingTimeLength `json:"RangeNotWorkTimeLength"`        //区间岗下时长 单位：秒
	RangeSinglePassWorkTimeLength []operationModel.LineReportSettingTimeLength `json:"RangeSinglePassWorkTimeLength"` //区间单趟工作时长 单位：秒
	HasRangeTrip                  int64                                        `json:"HasRangeTrip"`                  //是否有区间
	FrequencyType                 int64                                        `json:"FrequencyType"`                 //班制 1双班  2单班  3混合班
	FrequencyDay                  int64                                        `json:"FrequencyDay"`                  //班制对应的天数
}

// CalcDrivePayroll 计算稳定排班
func CalcDrivePayroll(staff *protoStaff.OetStaffItem) {
	//now := time.Now()
	//以上月26号到本月25号为一个周期
	//splitDay := time.Date(now.Year(), now.Month(), 26, 0, 0, 0, 0, time.Local)
	//nineDay := time.Date(now.Year(), now.Month(), 9, 0, 0, 0, 0, time.Local) // 九号-26之间无法计算工资
	//if now.Unix() >= nineDay.Unix() && now.Unix() < splitDay.Unix() {
	//	return
	//}
	month, startAt, endAt := util.GetBusCompanyNowMonth()
	report, _ := (&hrModel.StaffPayrollReport{}).GetByStaffIdAndMonth(staff.Id, hrModel.DriverStableScheduling, month)
	if report.Id == 0 {
		report.Type = hrModel.DriverStableScheduling
		report.Month = month
		report.StaffId = staff.Id
	}
	report.JobNumber = staff.Code
	report.StaffName = staff.Name
	report.LineId = staff.LineId
	lineItem, _ := rpc.GetLineWithId(context.Background(), report.LineId)
	if lineItem != nil {
		report.LineName = lineItem.Name
	}
	report.Build(staff.CorporationId)
	report.IDCard = staff.IdentifyId
	var staffArchive hrModel.StaffArchive
	_ = staffArchive.FindByStaffId(staff.Id) // 驾驶员档案
	if staffArchive.Id == 0 {
		return
	}
	fmt.Println("==============================staff.name:", staff.Name)
	var (
		baseWorkDay                             int64           // 基础上班天数 天
		baseTimeLength                          int64           // 标准工作时间 秒
		drvType                                 int64           //1大客 2小客 3小蓝中巴 4小蓝大客
		baseSalary                              int64           // 基础工资
		workDay                                 int64           // 出勤天数
		workType                                int64           // 工作类型 班制 1双班  2单班 3混合班
		innerWorkDay                            int64           //班制内出勤天数
		outWorkDay                              int64           //班制外出勤天数
		linePrice                               int64           // 线路补贴
		lineWashPrice                           int64           // 线路洗车补贴
		vehicleLength                           int64           // 车长 根据出勤时间的车长长度来获取
		vehicleLengthMap                        map[int64]int64 // 车长记录表 key是车长 value是出勤天数
		vehicleBaseSalary                       int64           // 车长补贴基本系数
		nightAddWorkTime                        int64           // 夜班加班时长 秒
		InFrequencyTypeCircleWorkTimeLength     int64           // 班制内圈次岗上时长 单位：秒
		InFrequencyTypeCircleNotWorkTimeLength  int64           //班制内圈次岗下时长 单位：秒
		OutFrequencyTypeCircleWorkTimeLength    int64           //班制外圈次岗上时长 单位：秒
		OutFrequencyTypeCircleNotWorkTimeLength int64           //班制外圈次岗下时长 单位：秒
		AddWorkTimes                            int64           // 加班不足2.5小时次数
		HalfDayAddWorkTimes                     int64           // 加班超过2.5小时不足半天次数
		FullDayAddWorkTimes                     int64           // 加班超过半天不足一天次数
		Holiday                                 int64           // 节假日加班
		FullWorkDay                             int64           // 全天班
		HalfWorkDay                             int64           //半天班
		RestTimeDay                             int64           // 休疗养天数
		PlanWorkDayTotal                        int64           // 总计划出勤天数
		HighManeuverDay                         int64           // 大机动天数
		PlanTotalCircle                         int64           // 月计划总圈次
		FullSinglePassWorkTimeLength            int64           // 全程单圈工作时长
		FullWorkTimeLength                      int64           // 全程单圈岗上时长
		FullNotWorkTimeLength                   int64           //全程单圈岗下时长
		FullDoneCircle                          int64           // 实际完成圈次
	)
	vehicleLengthMap = make(map[int64]int64)
	// 出勤表
	var driverWorkReportMaxWorkDay int64 = 0
	driverWorkReportItems, _, _ := (&operationModel.LineVehicleMileageReport{}).
		GetDriverRunReportDataGroupByLineDriver(1, []int64{}, []int64{}, []int64{staff.Id}, startAt, endAt, model.Sortable{Order: ""}, model.Paginator{Offset: 0, Limit: 1000})
	driverVehicleLength := make(map[int64]int64)
	if driverWorkReportItems != nil {
		for i, v := range driverWorkReportItems {
			var reportItem = LineDriverWorkReportItem{
				LineDriverWorkReportData: driverWorkReportItems[i],
			}
			workDay += v.WorkDayCount // 计算出勤时间

			nightAddWorkTime += v.NightTotalWorkTimeLength // 计算夜班时间 小时
			if _, ok := driverVehicleLength[driverWorkReportItems[i].DriverId]; ok {
				v.VehicleLength = driverVehicleLength[driverWorkReportItems[i].DriverId]
			} else {
				driverRunVehicles := (&operationModel.LineVehicleMileageReport{}).GetLineDriverRunVehicleDay(driverWorkReportItems[i].DriverId, startAt, endAt)
				if len(driverRunVehicles) > 0 {
					if driverRunVehicles[0].VehicleLength == 0 {
						vehicle := rpc.GetVehicleWithId(context.TODO(), driverRunVehicles[0].VehicleId)
						if vehicle != nil {
							vehicleSize := strings.Split(vehicle.CarBodySize, "*")
							if len(vehicleSize) > 0 {
								v.VehicleLength, _ = strconv.ParseInt(strings.Trim(vehicleSize[0], " "), 10, 64)
							}
						}
					} else {
						v.VehicleLength = driverRunVehicles[0].VehicleLength
					}
					driverVehicleLength[driverWorkReportItems[i].DriverId] = v.VehicleLength
				}
			}

			vehicleLengthMap[v.VehicleLength] = v.WorkDayCount
			if vehicleLengthMap[v.VehicleLength] > vehicleLength {
				vehicleLength = v.VehicleLength
			}
			// 计算线路补贴
			salaryPrice, washPrice := (&setting.LineAllowancePrice{}).GetSalaryPriceAndWashPrice(v.LineId)
			if salaryPrice > linePrice {
				linePrice = salaryPrice
			}
			if washPrice > lineWashPrice {
				lineWashPrice = washPrice
			}
			//  计算班制内 班制外时间
			//获取和所选时间有交叉的配置项
			settings := (&operationModel.OperationLineSetting{}).GetTimeCrossSetting([]int64{driverWorkReportItems[i].CorporationId}, driverWorkReportItems[i].LineId, startAt, endAt)
			reportItem.WorkAndNotWorkTimeLength = ParseTimeLengthByTimeRange(settings, startAt, endAt)
			LineWorkDay := (&operationModel.LineVehicleMileageReport{}).GetDriverRunCountDataGroupByReportAt(1, []int64{driverWorkReportItems[i].CorporationId}, 0, 0, driverWorkReportItems[i].DriverId, 0, startAt, endAt)
			lineWorkDay := LineWorkDay / 10
			if lineWorkDay <= reportItem.FrequencyDay {
				innerOutFrequencyTypeCircleWorkTimeLength := v.AddWorkWorkTimeLength                                                   //班制外圈次岗上时长 单位：秒
				innerOutFrequencyTypeCircleNotWorkTimeLength := v.AddWorkNotWorkTimeLength                                             //班制外圈次岗下时长 单位：秒
				innerInFrequencyTypeCircleWorkTimeLength := v.TotalWorkTimeLength - innerOutFrequencyTypeCircleWorkTimeLength          //班制内圈次岗上时长 单位：秒
				innerInFrequencyTypeCircleNotWorkTimeLength := v.TotalNotWorkTimeLength - innerOutFrequencyTypeCircleNotWorkTimeLength //班制内圈次岗下时长 单位：秒

				OutFrequencyTypeCircleWorkTimeLength += innerOutFrequencyTypeCircleWorkTimeLength
				OutFrequencyTypeCircleNotWorkTimeLength += innerOutFrequencyTypeCircleNotWorkTimeLength
				InFrequencyTypeCircleWorkTimeLength += innerInFrequencyTypeCircleWorkTimeLength
				InFrequencyTypeCircleNotWorkTimeLength += innerInFrequencyTypeCircleNotWorkTimeLength
			} else {
				innerOutFrequencyTypeCircleWorkTimeLength := (((v.TotalWorkTimeLength - v.AddWorkWorkTimeLength) / lineWorkDay) * (lineWorkDay - reportItem.FrequencyDay)) + v.AddWorkWorkTimeLength
				innerOutFrequencyTypeCircleNotWorkTimeLength := (((v.TotalNotWorkTimeLength - v.AddWorkNotWorkTimeLength) / lineWorkDay) * (lineWorkDay - reportItem.FrequencyDay)) + v.AddWorkNotWorkTimeLength
				innerInFrequencyTypeCircleWorkTimeLength := v.TotalWorkTimeLength - innerOutFrequencyTypeCircleWorkTimeLength
				innerInFrequencyTypeCircleNotWorkTimeLength := v.TotalNotWorkTimeLength - innerOutFrequencyTypeCircleNotWorkTimeLength

				OutFrequencyTypeCircleWorkTimeLength += innerOutFrequencyTypeCircleWorkTimeLength
				OutFrequencyTypeCircleNotWorkTimeLength += innerOutFrequencyTypeCircleNotWorkTimeLength
				InFrequencyTypeCircleWorkTimeLength += innerInFrequencyTypeCircleWorkTimeLength
				InFrequencyTypeCircleNotWorkTimeLength += innerInFrequencyTypeCircleNotWorkTimeLength
			}
			fmt.Println("reportItem.FrequencyType:", reportItem.FrequencyType)
			if v.WorkDayCount > driverWorkReportMaxWorkDay {
				workType = reportItem.FrequencyType
				fmt.Println("workType:", workType)
				driverWorkReportMaxWorkDay = v.WorkDayCount
			}
			//从请假列表里获取时间段内请假全天的日期和请假半天的日期
			exceptLeaveTypes := []int64{util.LeaveTypeForCasualLeave, util.LeaveTypeForWaitWork, util.LeaveTypeForStopWork}
			oneDayLeave, _ := GetDriverLeaveReportAtByRange(driverWorkReportItems[i].DriverId, startAt, endAt, exceptLeaveTypes)
			reportItem.PlanTotalCircle = (&operationModel.PlanScheduleReport{}).GetDriverSumCircleCount(driverWorkReportItems[i].CorporationId, driverWorkReportItems[i].LineId, driverWorkReportItems[i].DriverId, startAt, endAt, oneDayLeave)
			PlanTotalCircle += reportItem.PlanTotalCircle // 月计划总圈次
			// 实际完成圈次
			FullDoneCircle += v.FullDoneCircle
			// 单趟工作总时间 单趟运营时间 单趟岗下时间
			if reportItem.FullSinglePassWorkTimeLength != nil {
				for _, f := range reportItem.FullSinglePassWorkTimeLength {
					FullSinglePassWorkTimeLength += f.Value
				}
			}
			if reportItem.FullWorkTimeLength != nil {
				for _, f := range reportItem.FullWorkTimeLength {
					FullWorkTimeLength += f.Value
				}
			}
			if reportItem.FullNotWorkTimeLength != nil {
				for _, f := range reportItem.FullNotWorkTimeLength {
					FullNotWorkTimeLength += f.Value
				}
			}

			AddWorkTimes += v.AddWorkTimes               // 加班不足2.5小时次数
			HalfDayAddWorkTimes += v.HalfDayAddWorkTimes // 加班超过2.5小时不足半天次数
			FullDayAddWorkTimes += v.FullDayAddWorkTimes // 加班超过半天不足一天次数
			Holiday += v.HolidayDay                      //节假日加班
			FullWorkDay += v.FullWorkDay                 // 全天班数
			HalfWorkDay += v.HalfWorkDay                 // 半天班数

			RestTimeDay += v.RestTimeDay //RestTimeDay

			PlanWorkDay := (&operationModel.PlanScheduleReport{}).GetDriverFrequencyIndexCount(driverWorkReportItems[i].CorporationId, driverWorkReportItems[i].LineId, driverWorkReportItems[i].DriverId, startAt, endAt)
			PlanWorkDayTotal += PlanWorkDay
		}
	} // 计算各种数据
	report.PlanWorkDayTotal = PlanWorkDayTotal                         // 计划出勤天数
	report.PlanTotalCircle = PlanTotalCircle / 10                      // 月计划总趟次
	report.TotalCircle = FullDoneCircle / 10                           // 月实际总趟次
	report.FullSinglePassWorkTimeLength = FullSinglePassWorkTimeLength // 单趟工作总时间
	report.FullWorkTimeLength = FullWorkTimeLength                     // 单趟运营时间
	report.FullNotWorkTimeLength = FullNotWorkTimeLength               // 单趟岗下时间
	// 准假车型 司机类别
	drvLicenseTypes := strings.Split(staff.DrvLicenseTypeStr, ",") // 准假车型表
	for _, drvLicenseType := range drvLicenseTypes {
		if drvLicenseType == "A1" || drvLicenseType == "A3" {
			// 大客
			drvType = 1
		}
		if drvLicenseType == "A2" || drvLicenseType == "B1" {
			drvType = 2
		}
	} // 根据车型获得基础工资
	// 班制外加班明细表
	OutFrequencyReports, _ := (&operationModel.OutFrequencyAddWorkReport{}).GetBy(1, []int64{}, []int64{}, []int64{}, []int64{staff.Id}, startAt, endAt, model.Paginator{Limit: 0})
	if OutFrequencyReports != nil {
		for _, report := range OutFrequencyReports {
			if report.AddWorkType == operationModel.HighManeuverType {
				HighManeuverDay += report.DayCount
			}
		}
	}
	// 计算班制内天数，班制外天数
	var configType int64
	if workType == 1 {
		configType = settingModle.DoubleShiftConfig
	} else if workType == 2 {
		configType = settingModle.SingleShiftConfig
	} else {
		configType = settingModle.MixShiftConfig
	}
	report.WorkType = workType // 班制
	configs := settingModle.GetStaffPayrollConfig(configType)
	if len(configs) > 0 {
		day := configs[0].BaseDay
		if workDay > day {
			innerWorkDay = day / 10
			outWorkDay = (workDay - day) / 10
		} else {
			innerWorkDay = workDay / 10
			outWorkDay = 0
		}
	}
	report.InnerWorkDay = innerWorkDay // 班制内天数
	report.OutWorkDay = outWorkDay     // 班制外天数
	// 基础上班天数 22
	configs = settingModle.GetStaffPayrollConfig(settingModle.StandardCalDaysConfig)
	if len(configs) > 0 {
		baseWorkDay = configs[0].BaseDay
	}
	// 基础上班天数 22
	configs = settingModle.GetStaffPayrollConfig(settingModle.StandardCalHourConfig)
	if len(configs) > 0 {
		baseTimeLength = configs[0].BaseTimeLength
	}
	notWorkDay := (&hrModel.ApplyLeaveRecord{}).GetDayCountByTimeRange(startAt, endAt, staff.Id, []int64{1, 5, 7}) // 休息天数 年假1 陪产假5 婚嫁7
	actualDay := workDay*10 + notWorkDay*100                                                                       // 实际折算天数
	if actualDay > baseWorkDay {
		actualDay = baseWorkDay
	}
	report.ActualDay = workDay / 10 // 实际天数
	if baseWorkDay == 0 {
		log.ErrorFields("标准计算天数为0", nil)
		return
	}
	fmt.Println("baseWorkDay:", baseWorkDay)
	// 开始汇总
	var config settingModle.StaffPayrollConfig
	fmt.Println("drvType:", drvType)
	baseSalary = config.GetBaseSalary(drvType)
	fmt.Println("baseSalary:", baseSalary)
	report.BaseSalary = calculateSalary(actualDay, baseSalary, baseWorkDay) // 应发的基础工资
	fmt.Println("vehicleLengthMap:", vehicleLengthMap)
	fmt.Println("vehicleLength:", vehicleLength)
	report.CarLength = vehicleLength // 车长
	vehicleBaseSalary = config.GetVehicleLengthSalary(vehicleLength)
	report.CarLengthAllowance = calculateSalary(actualDay, vehicleBaseSalary, baseWorkDay) // 车长补贴

	report.LineAllowance = calculateSalary(actualDay, linePrice, baseWorkDay) // 线路补贴
	report.WashCar = lineWashPrice                                            // 洗车补贴
	fmt.Println("nightAddWorkTime:", nightAddWorkTime)
	report.NightAddWorkTime = nightAddWorkTime                           // 夜班时间
	report.NightShiftAllowance = config.GetNightSalary(nightAddWorkTime) // 夜班补贴
	fmt.Println("NightShiftAllowance:", report.NightShiftAllowance)
	fmt.Println("InFrequencyTypeCircleWorkTimeLength:", InFrequencyTypeCircleWorkTimeLength)
	fmt.Println("InFrequencyTypeCircleNotWorkTimeLength:", InFrequencyTypeCircleNotWorkTimeLength)
	report.InFrequencyTypeCircleWorkTimeLength = InFrequencyTypeCircleWorkTimeLength                                           // 班制内月实际运营时长
	report.InFrequencyTypeCircleNotWorkTimeLength = InFrequencyTypeCircleNotWorkTimeLength                                     // 班制内月岗下时长
	report.InFrequencyTypeCircleTotalTimeLength = InFrequencyTypeCircleWorkTimeLength + InFrequencyTypeCircleNotWorkTimeLength // 班制内工作总时长
	report.OnSitePayInSchedule,
		report.OffDutyPayInSchedule =
		config.GetInScheduleSalary(InFrequencyTypeCircleWorkTimeLength, InFrequencyTypeCircleNotWorkTimeLength, baseTimeLength) // 班制内-递增岗上工资、岗下工资
	fmt.Println("innerWorkDay:", innerWorkDay)
	fmt.Println("baseWorkDay:", baseWorkDay)
	report.DayPayInSchedule =
		config.GetDayPayInSchedule(innerWorkDay*100, vehicleLength, baseWorkDay) // 班制内 递增岗日工资
	report.OutFrequencyTypeCircleWorkTimeLength = OutFrequencyTypeCircleWorkTimeLength                                            // 班制外月实际运营时长
	report.OutFrequencyTypeCircleNotWorkTimeLength = OutFrequencyTypeCircleNotWorkTimeLength                                      // 班制外月岗下时长
	report.OutFrequencyTypeCircleTotalTimeLength = OutFrequencyTypeCircleWorkTimeLength + OutFrequencyTypeCircleNotWorkTimeLength // 班制外工作总时长
	report.OnSitePayOutSchedule, report.OffDutyPayOutSchedule =
		config.GetOutScheduleSalary(OutFrequencyTypeCircleWorkTimeLength, OutFrequencyTypeCircleNotWorkTimeLength) // 班制外-递增岗上工资
	report.DayPayOutSchedule =
		config.GetDayPayOutSchedule(outWorkDay, vehicleLength) // 班制外递增日工资

	report.WageUnder2h30m = config.GetWageUnder2h30m(AddWorkTimes)                                                     // 班制外-2.5小时内工资
	report.WageOver2h30m = config.GetWageOver2h30m(HalfDayAddWorkTimes)                                                // 班制外-2.5小时外工资
	report.ExtendedHalfDayWage = config.GetExtendedHalfDayWage(FullDayAddWorkTimes)                                    // 班制外-超过半天不足 1 天工资
	report.TotalPayInSchedule = report.OnSitePayInSchedule + report.OffDutyPayInSchedule + report.DayPayInSchedule     // 班制内超时工资
	report.TotalPayOutSchedule = report.OnSitePayOutSchedule + report.OffDutyPayOutSchedule + report.DayPayOutSchedule // 班制外超时工资
	report.Holiday = Holiday / 10                                                                                      // 节假日加班天数
	report.HolidayOvertimePay = config.GetHolidayOvertimePay(Holiday)                                                  // 节假日加班工资

	// 服务奖金
	monthInt, _ := strconv.Atoi(month)
	assessReports := (&assessModel.DriverDevoteMonthMoneyReport{}).GetDriverMonthReport(staff.Id, int64(monthInt))
	driverMonthAssessReports, _ := (&assessModel.DriverMonthAssessReport{}).GetBy([]int64{}, 0, staff.Id, int64(monthInt), model.Paginator{Offset: 0, Limit: 9999})
	var additionalSafeMoney int64
	var additionalServiceMoney int64
	var serviceAssessReward int64      // 服务质量奖
	var safeSpecialAssessReward int64  // 专项考核奖
	var safeAccidentAssessReward int64 // 事故考核奖
	var safeAssessReward int64         // 规范行为奖
	for _, v := range driverMonthAssessReports {
		additionalSafeMoney += v.AdditionalSafeMoney
		additionalServiceMoney += v.AdditionalServiceMoney
		serviceAssessReward += v.ServiceAssessReward
		safeSpecialAssessReward += v.SpecialAssessReward
		safeAssessReward += v.SafeAssessReward
		safeAccidentAssessReward += v.AccidentAssessReward
	}

	report.ServiceAssessReward = assessReports.ServiceDevoteMoney + additionalServiceMoney + serviceAssessReward
	report.SafeReward = assessReports.SafeDevoteMoney + additionalSafeMoney + safeSpecialAssessReward + safeAccidentAssessReward + safeAssessReward

	lineLeaderBase := config.GetLineLeaderBase(staffArchive.IsLineLeader)
	report.LineLeaderReward = calculateSalary(actualDay, lineLeaderBase, baseWorkDay) // 线组长奖金额

	report.EnergySavingReward = config.GetEnergySavingReward(staff.Id, month) // 节能降耗

	joinAt := model.LocalTime(time.Unix(staff.WorkTime, 0))
	year := yearsSince(time.Time(joinAt))
	ageBase := config.GetAgeBase()
	// 司龄津贴 实际折算天数算法不同
	report.RegisterTime = staff.WorkTime                             // 进公司年份
	ageActualDay := workDay*10 + notWorkDay*100 + HighManeuverDay*10 // 实际折算天数
	if ageActualDay > baseWorkDay {
		ageActualDay = baseWorkDay
	}
	report.AgeAllowance = calculateSalary(ageActualDay, year*ageBase, baseWorkDay) // 司龄津贴
	fmt.Println("FullWorkDay:", FullWorkDay)
	fmt.Println("HalfWorkDay:", HalfWorkDay)
	report.CateringAllowance = config.GetCateringAllowance(FullWorkDay, HalfWorkDay, HighManeuverDay) // 餐费补贴
	fmt.Println("report.CateringAllowance:", report.CateringAllowance)
	highTemperature := config.GetHighTemperatureAllowanceBase(month)
	report.HighTemperatureAllowance = calculateSalary(actualDay, highTemperature, baseWorkDay) // 高温补贴

	report.AnnualAuditAmount = config.GetAnnualAuditAmount(OutFrequencyReports) // 年审补贴

	recuperationLeaveBase := config.GetRecuperationLeaveBase()
	report.RecuperationLeave = RestTimeDay * recuperationLeaveBase / 10                                                   // 疗休养
	report.SpecialLeave = config.GetSpecialLeave(workDay*10, baseWorkDay, PlanWorkDayTotal*100, startAt, endAt, staff.Id) //病假、婚假、丧假补贴
	// 按日期分类
	reportMap := make(map[string][]operationModel.OutFrequencyAddWorkReport)
	for _, report := range OutFrequencyReports {
		reportMap[report.ReportAt.String()] = append(reportMap[report.ReportAt.String()], report)
	}
	report.TrainAllowance = config.GetTrainAllowance(reportMap)
	report.CharterMissionAllowance = config.GetCharterMissionAllowance(OutFrequencyReports)
	report.Other = config.GetOtherSalary(reportMap)
	report.CalculateSalaryPayable()
	report.CalculateActualPayment()
	if report.Id == 0 {
		_ = report.Create()
	} else {
		_ = report.Update()
	}
}

// CalcDrivePayrollForVillage 计算村村通
func CalcDrivePayrollForVillage(staffId int64, staffs []hrModel.StaffPayrollCalculation, startAt, endAt time.Time) {
	oetStaff := rpc.GetStaffWithId(context.Background(), staffId)
	fmt.Println("oetStaff", oetStaff)
	if oetStaff == nil {
		return
	}
	fmt.Println("staffs", staffs)
	var staff hrModel.StaffPayrollCalculation
	if len(staffs) == 0 {
		return
	}
	// 计算各种数据
	var (
		month           string // 月份
		baseWorkDay     int64  // 标准计算天数  * 100
		baseWorkDayCeil int64  // 向上取整  * 100
		workDay         int64  // 出勤天数 * 100
		notWorkDay      int64  // 休假日期 *1
		maneuverDay     int64  // 机动天数
		highManeuverDay int64  // 大机动天数
		holiday         int64  // 节假日加班 *10
	)
	for _, s := range staffs {
		workDay += s.WorkDay * 10
		maneuverDay += s.ManeuverDay * 10
		holiday += s.Holiday
	}
	// 开始汇总
	staff = staffs[0]
	report, _ := (&hrModel.StaffPayrollReport{}).GetByStaffIdAndMonth(staff.StaffId, hrModel.DriverVillage, staff.Month)
	if report.Id == 0 {
		report.Type = hrModel.DriverVillage
		report.Month = staff.Month
		report.StaffId = staff.StaffId
	}
	report.IDCard = staff.IDCard
	report.StaffName = staff.StaffName
	report.JobNumber = staff.JobNumber
	report.LineId = staff.LineId
	report.LineName = staff.LineName
	report.WorkDay = workDay / 100         // 上岗天数
	report.ManeuverDay = maneuverDay / 100 // 机动天数
	report.Holiday = holiday / 10          // 节假日加班天数
	report.Build(oetStaff.CorporationId)

	month = staff.Month
	configs := settingModle.GetStaffPayrollConfig(settingModle.StandardCalDaysConfig)
	if len(configs) > 0 {
		baseWorkDay = configs[0].BaseDay
	}
	if baseWorkDay == 0 {
		log.ErrorFields("标准计算天数为0", nil)
		return
	}
	notWorkDay = (&hrModel.ApplyLeaveRecord{}).GetDayCountByTimeRange(startAt, endAt, staff.StaffId, []int64{1, 5, 7}) // 休息天数 年假1 陪产假5 婚嫁7
	notWorkDay = notWorkDay * 100                                                                                      // 放大一百倍
	actualDay := workDay + notWorkDay + maneuverDay                                                                    // 实际折算天数
	if actualDay > baseWorkDay {
		actualDay = baseWorkDay
	}
	report.ActualDay = workDay / 100 // 实际出勤天数
	// 开始汇总
	var config settingModle.StaffPayrollConfig
	baseSalary := config.GetVillageBaseSalary(staff.StaffId)
	report.BaseSalary = calculateSalary(actualDay, baseSalary, baseWorkDay) // 应发的基础工资

	postSalary := config.CommonWageBase(settingModle.PostConfig)
	report.DutySalary = calculateSalary(actualDay, postSalary, baseWorkDay) // 应发的岗位工资

	offDutySalary := config.CommonWageBase(settingModle.OffDutySalaryConfig)
	report.OffDutyPay = offDutySalary * workDay / 100 // 岗下工资
	// 公休日加班
	baseWorkDayCeil = int64(math.Ceil(float64(baseWorkDay)/100) * 100)
	publicHolidaySalary := config.CommonWageBase(settingModle.PublicHolidayConfig)
	report.WeekdayPay = (workDay + maneuverDay - baseWorkDayCeil) * publicHolidaySalary / 100
	// 节假日加班
	holidaySalary := config.CommonWageBase(settingModle.HolidayConfig)
	report.HolidayOvertimePay = holidaySalary * holiday / 10
	// 服务奖金
	monthInt, _ := strconv.Atoi(month)
	assessReports := (&assessModel.DriverDevoteMonthMoneyReport{}).GetDriverMonthReport(staff.StaffId, int64(monthInt))
	driverMonthAssessReports, _ := (&assessModel.DriverMonthAssessReport{}).GetBy([]int64{}, 0, staff.StaffId, int64(monthInt), model.Paginator{Offset: 0, Limit: 9999})
	var additionalSafeMoney int64
	var additionalServiceMoney int64
	var serviceAssessReward int64      // 服务质量奖
	var safeSpecialAssessReward int64  // 专项考核奖
	var safeAccidentAssessReward int64 // 事故考核奖
	var safeAssessReward int64         // 规范行为奖
	for _, v := range driverMonthAssessReports {
		additionalSafeMoney += v.AdditionalSafeMoney
		additionalServiceMoney += v.AdditionalServiceMoney
		serviceAssessReward += v.ServiceAssessReward
		safeSpecialAssessReward += v.SpecialAssessReward
		safeAssessReward += v.SafeAssessReward
		safeAccidentAssessReward += v.AccidentAssessReward
	}

	report.ServiceAssessReward = assessReports.ServiceDevoteMoney + additionalServiceMoney + serviceAssessReward
	report.SafeReward = assessReports.SafeDevoteMoney + additionalSafeMoney + safeSpecialAssessReward + safeAccidentAssessReward + safeAssessReward

	report.EnergySavingReward = config.GetEnergySavingReward(staff.StaffId, month) // 节能降耗

	// 班制外加班明细表
	OutFrequencyReports, _ := (&operationModel.OutFrequencyAddWorkReport{}).GetBy(1, []int64{}, []int64{}, []int64{}, []int64{staff.StaffId}, startAt, endAt, model.Paginator{Limit: 0})
	if OutFrequencyReports != nil {
		for _, outReport := range OutFrequencyReports {
			if outReport.AddWorkType == operationModel.HighManeuverType {
				highManeuverDay += outReport.DayCount
			}
		}
	}
	// 司龄津贴
	// oetStaff := rpc.GetStaffWithId(context.Background(), staffId)
	fmt.Println("姓名:", oetStaff.Name)
	fmt.Println("入职时间staff.workTime:", oetStaff.WorkTime)
	joinAt := model.LocalTime(time.Unix(oetStaff.WorkTime, 0))
	year := yearsSince(time.Time(joinAt))
	ageBase := config.GetAgeBase()
	report.RegisterTime = oetStaff.WorkTime                       // 进公司年份
	ageActualDay := workDay + notWorkDay*100 + highManeuverDay*10 // 实际折算天数
	if ageActualDay > baseWorkDay {
		ageActualDay = baseWorkDay
	}
	report.AgeAllowance = calculateSalary(ageActualDay, year*ageBase, baseWorkDay) // 司龄津贴
	// 线路补贴
	lineSalary := config.CommonWageBase(settingModle.LinePriceConfig)
	report.LineAllowance = calculateSalary(actualDay, lineSalary, baseWorkDay)
	// 餐费补贴
	dishSalary := config.CommonWageBase(settingModle.MealConfig)
	highManeuverSalary := config.CommonWageBase(settingModle.HighManeuverEatConfig)
	report.CateringAllowance = workDay*dishSalary/100 + highManeuverDay*highManeuverSalary/10
	// 高温补贴
	highTemperature := config.GetHighTemperatureAllowanceBase(month)
	report.HighTemperatureAllowance = calculateSalary(actualDay, highTemperature, baseWorkDay)
	// 年审补贴
	report.AnnualAuditAmount = config.GetAnnualAuditAmount(OutFrequencyReports)
	// 洗车补贴
	washCarSalary := config.CommonWageBase(settingModle.WashCarPriceConfig)
	report.WashCar = calculateSalary(actualDay, washCarSalary, baseWorkDay)
	// 疗休养
	recuperationLeaveBase := config.GetRecuperationLeaveBase()
	RestTimeDay := (&hrModel.ApplyLeaveRecord{}).GetDayCountByTimeRange(startAt, endAt, staff.StaffId, []int64{9}) // 休息天数 疗休养9
	report.RecuperationLeave = RestTimeDay * recuperationLeaveBase
	//病假、婚假、丧假补贴
	report.SpecialLeave = config.GetSpecialLeave(workDay, baseWorkDay, 0, startAt, endAt, staff.StaffId)
	// 按日期分类
	reportMap := make(map[string][]operationModel.OutFrequencyAddWorkReport)
	for _, report := range OutFrequencyReports {
		reportMap[report.ReportAt.String()] = append(reportMap[report.ReportAt.String()], report)
	}
	report.TrainAllowance = config.GetTrainAllowance(reportMap)
	report.CharterMissionAllowance = config.GetCharterMissionAllowance(OutFrequencyReports)
	report.Other = config.GetOtherSalary(reportMap)
	report.CalculateSalaryPayable()
	report.CalculateActualPayment()
	if report.Id == 0 {
		err := report.Create()
		fmt.Println(err)
	} else {
		err := report.Update()
		fmt.Println(err)
	}
}

// CalcDrivePayrollForCustomer 计算定制公交
func CalcDrivePayrollForCustomer(staffId int64, staffs []hrModel.StaffPayrollCalculation, startAt, endAt time.Time) {
	oetStaff := rpc.GetStaffWithId(context.Background(), staffId)
	if oetStaff == nil {
		return
	}
	var staffArchive hrModel.StaffArchive
	_ = staffArchive.FindByStaffId(staffId) // 驾驶员档案
	if staffArchive.Id == 0 {
		return
	}
	var staff hrModel.StaffPayrollCalculation
	if len(staffs) == 0 {
		return
	}
	// 计算各种数据
	var (
		month             string         // 月份
		baseWorkDay       int64          // 标准计算天数  * 100
		baseWorkDayCeil   int64          // 向上取整  * 100
		workDay           int64          // 出勤天数 * 100
		notWorkDay        int64          // 休假日期 *1
		highManeuverDay   int64          // 大机动天数
		holiday           int64          // 节假日 *10
		overWorkOn5To6    int64          // 加班5-6
		overWorkOn6To18   int64          // 加班6-18
		overWorkOn18To23  int64          // 加班18-23
		overWorkOn23To5   int64          // 加班23-5
		overWorkOn18To24  int64          // 加班18-24
		overWorkOn24To5   int64          // 加班24-5
		drvType           int64          // 司机类型 1大巴 2中巴
		commutingShiftDay int64          // 通勤班天数
		busShiftDay       int64          // 公交班天数
		tempShiftDay      int64          // 临时线路天数
		lineType          map[int64]bool // 1-常规公交；2-定制公交；3-公务车；4-校车；5-旅游专线; 6-村村通
		mileage           int64          // 公里数 米
	)
	lineType = make(map[int64]bool)
	for _, s := range staffs {
		workDay += s.WorkDay * 10
		holiday += s.Holiday
		overWorkOn5To6 += s.OverWorkOn5To6
		overWorkOn6To18 += s.OverWorkOn6To18
		overWorkOn18To23 += s.OverWorkOn18To23
		overWorkOn23To5 += s.OverWorkOn23To5
		overWorkOn18To24 += s.OverWorkOn18To24
		overWorkOn24To5 += s.OverWorkOn24To5
		commutingShiftDay += s.CommutingShiftDay
		busShiftDay += s.BusShiftDay
		tempShiftDay += s.TempShiftDay
		mileage += s.Mileage
		line, _ := rpc.GetLineWithId(context.Background(), s.LineId)
		if line != nil {
			lineType[line.LineAttribute] = true
		}
	}
	// 开始汇总
	staff = staffs[0]
	report, _ := (&hrModel.StaffPayrollReport{}).GetByStaffIdAndMonth(staff.StaffId, hrModel.DriverCustomerLine, staff.Month)
	if report.Id == 0 {
		report.Type = hrModel.DriverCustomerLine
		report.Month = staff.Month
		report.StaffId = staff.StaffId
	}
	report.IDCard = staff.IDCard
	report.JobNumber = staff.JobNumber
	report.StaffName = staff.StaffName
	report.LineId = staff.LineId
	report.LineName = staff.LineName
	report.WorkDay = workDay / 100 // 上岗天数
	report.Holiday = holiday / 10  // 节假日

	month = staff.Month
	configs := settingModle.GetStaffPayrollConfig(settingModle.StandardCalDaysConfig)
	if len(configs) > 0 {
		baseWorkDay = configs[0].BaseDay
	}
	if baseWorkDay == 0 {
		log.ErrorFields("标准计算天数为0", nil)
		return
	}
	notWorkDay = (&hrModel.ApplyLeaveRecord{}).GetDayCountByTimeRange(startAt, endAt, staff.StaffId, []int64{1, 5, 7}) // 休息天数 年假1 陪产假5 婚嫁7
	notWorkDay = notWorkDay * 100                                                                                      // 放大一百倍
	actualDay := workDay + notWorkDay                                                                                  // 实际折算天数
	if actualDay > baseWorkDay {
		actualDay = baseWorkDay
	}
	report.ActualDay = workDay / 100 // 实际出勤天数
	// 开始汇总
	var config settingModle.StaffPayrollConfig
	report.BaseSalary = config.CommonWageBase(settingModle.BaseSalaryConfig) // 应发的基础工资
	// 公休日加班
	baseWorkDayCeil = int64(math.Ceil(float64(baseWorkDay)/100) * 100)
	publicHolidaySalary := config.CommonWageBase(settingModle.PublicHolidayConfig)
	report.WeekdayPay = (workDay - baseWorkDayCeil) * publicHolidaySalary / 100
	// 加班补贴
	report.OverWorkReward = config.GetOverWorkForCustomer(overWorkOn5To6, overWorkOn6To18, overWorkOn18To23, overWorkOn23To5)
	// 节假日加班
	holidaySalary := config.CommonWageBase(settingModle.HolidayConfig)
	report.Holiday = holiday / 10
	report.HolidayOvertimePay = holidaySalary * holiday / 10
	// 超产奖
	drvLicenseTypes := strings.Split(oetStaff.DrvLicenseTypeStr, ",") // 准假车型表
	for _, drvLicenseType := range drvLicenseTypes {
		if drvLicenseType == "A1" || drvLicenseType == "A3" {
			// 大客
			drvType = 1
		}
		if drvLicenseType == "A2" || drvLicenseType == "B1" {
			drvType = 2
		}
	} // 准假车型 司机类别
	extraWorkSalary := config.GetExtraWorkWageBaseForCustomer(drvType)
	report.ExtraWorkReward = (workDay - baseWorkDayCeil) * extraWorkSalary / 100

	// 服务奖金
	monthInt, _ := strconv.Atoi(month)
	assessReports := (&assessModel.DriverDevoteMonthMoneyReport{}).GetDriverMonthReport(staff.StaffId, int64(monthInt))
	driverMonthAssessReports, _ := (&assessModel.DriverMonthAssessReport{}).GetBy([]int64{}, 0, staff.StaffId, int64(monthInt), model.Paginator{Offset: 0, Limit: 9999})
	var additionalSafeMoney int64
	var additionalServiceMoney int64
	var serviceAssessReward int64      // 服务质量奖
	var safeSpecialAssessReward int64  // 专项考核奖
	var safeAccidentAssessReward int64 // 事故考核奖
	var safeAssessReward int64         // 规范行为奖
	for _, v := range driverMonthAssessReports {
		additionalSafeMoney += v.AdditionalSafeMoney
		additionalServiceMoney += v.AdditionalServiceMoney
		serviceAssessReward += v.ServiceAssessReward
		safeSpecialAssessReward += v.SpecialAssessReward
		safeAssessReward += v.SafeAssessReward
		safeAccidentAssessReward += v.AccidentAssessReward
	}

	report.ServiceAssessReward = assessReports.ServiceDevoteMoney + additionalServiceMoney + serviceAssessReward
	report.SafeReward = assessReports.SafeDevoteMoney + additionalSafeMoney + safeSpecialAssessReward + safeAccidentAssessReward + safeAssessReward
	//线组长奖金额
	lineLeaderBase := config.GetLineLeaderBase(staffArchive.IsLineLeader)
	report.LineLeaderReward = calculateSalary(actualDay, lineLeaderBase, baseWorkDay) // 线组长奖金额
	// 执行奖金
	report.ExecutionAward = config.GetExecutionAward(lineType)
	// 餐费补贴
	report.CateringAllowance = config.GetCateringAllowanceForCustomer(commutingShiftDay, busShiftDay, tempShiftDay)
	// 司龄津贴
	joinAt := model.LocalTime(time.Unix(oetStaff.WorkTime, 0))
	year := yearsSince(time.Time(joinAt))
	ageBase := config.GetAgeBase()
	report.RegisterTime = oetStaff.WorkTime                       // 进公司年份
	ageActualDay := workDay + notWorkDay*100 + highManeuverDay*10 // 实际折算天数
	if ageActualDay > baseWorkDay {
		ageActualDay = baseWorkDay
	}
	report.AgeAllowance = calculateSalary(ageActualDay, year*ageBase, baseWorkDay) // 司龄津贴
	// 出车补贴
	report.VehicleSubsidyAllowance = config.GetVehicleSubsidyAllowance(mileage)
	// 高温补贴
	highTemperature := config.GetHighTemperatureAllowanceBase(month)
	report.HighTemperatureAllowance = calculateSalary(actualDay, highTemperature, baseWorkDay)
	// 夜班补贴
	report.NightWorkAllowance = config.GetNightOverWorkForCustomer(overWorkOn18To24, overWorkOn24To5)
	// 班制外加班明细表
	OutFrequencyReports, _ := (&operationModel.OutFrequencyAddWorkReport{}).GetBy(1, []int64{}, []int64{}, []int64{}, []int64{staff.StaffId}, startAt, endAt, model.Paginator{Limit: 0})
	if OutFrequencyReports != nil {
		for _, outReport := range OutFrequencyReports {
			if outReport.AddWorkType == operationModel.HighManeuverType {
				highManeuverDay += outReport.DayCount
			}
		}
	}
	// 年审补贴
	report.AnnualAuditAmount = config.GetAnnualAuditAmount(OutFrequencyReports)
	// 洗车补贴
	washCarSalary := config.CommonWageBase(settingModle.WashCarPriceConfig)
	report.WashCar = calculateSalary(actualDay, washCarSalary, baseWorkDay)
	// 疗休养
	recuperationLeaveBase := config.GetRecuperationLeaveBase()
	RestTimeDay := (&hrModel.ApplyLeaveRecord{}).GetDayCountByTimeRange(startAt, endAt, staff.StaffId, []int64{9}) // 休息天数 疗休养9
	report.RecuperationLeave = RestTimeDay * recuperationLeaveBase
	//病假、婚假、丧假补贴
	report.SpecialLeave = config.GetSpecialLeave(workDay, baseWorkDay, 0, startAt, endAt, staff.StaffId)
	// 按日期分类
	reportMap := make(map[string][]operationModel.OutFrequencyAddWorkReport)
	for _, report := range OutFrequencyReports {
		reportMap[report.ReportAt.String()] = append(reportMap[report.ReportAt.String()], report)
	}
	report.TrainAllowance = config.GetTrainAllowance(reportMap)
	report.CharterMissionAllowance = config.GetCharterMissionAllowance(OutFrequencyReports)
	report.Other = config.GetOtherSalary(reportMap)
	report.Build(oetStaff.CorporationId)
	report.CalculateSalaryPayable()
	report.CalculateActualPayment()
	if report.Id == 0 {
		_ = report.Create()
	} else {
		_ = report.Update()
	}
}

// CalcDrivePayrollForBlueBus 计算小蓝巴
func CalcDrivePayrollForBlueBus(staffId int64, staffs []hrModel.StaffPayrollCalculation, startAt, endAt time.Time, passengerReward bool) {
	oetStaff := rpc.GetStaffWithId(context.Background(), staffId)
	if oetStaff == nil {
		return
	}
	var staffArchive hrModel.StaffArchive
	_ = staffArchive.FindByStaffId(staffId) // 驾驶员档案
	if staffArchive.Id == 0 {
		return
	}
	var staff hrModel.StaffPayrollCalculation
	if len(staffs) == 0 {
		return
	}
	// 计算各种数据
	var (
		month           string // 月份
		baseWorkDay     int64  // 标准计算天数  * 100
		baseWorkDay2    int64  // 基础工资的标准计算天数  * 100
		planWorkDay     int64  // 计划天数 * 10
		workDay         int64  // 出勤天数 * 100
		notWorkDay      int64  // 休假日期 *1
		highManeuverDay int64  // 大机动天数 *10
		holiday         int64  // 节假日 *10
		drvType         int64  // 司机类型 1大巴 2中巴
		overWorkHalfDay int64  // 出勤加班天数 半天班 *10
		overWorkAllDay  int64  // 出勤加班天数（整天班）*10
		nightWorkTime   int64  // 夜班时长 秒
		fullWorkDay     int64  // 全天班 次数
		halfWorkDay     int64  // 半天班 次数
		linePrice       int64  // 线路补贴
		lineWashPrice   int64  // 线路补贴

	)
	for _, s := range staffs {
		workDay += s.WorkDay * 10
		holiday += s.Holiday
		overWorkHalfDay += s.OverWorkHalfDay
		overWorkAllDay += s.OverWorkAllDay
		nightWorkTime += s.NightWorkTime
		planWorkDay += s.PlanWorkDay
		fullWorkDay += s.AllDayFrequency
		halfWorkDay += s.HalfDayFrequency
		// 计算线路补贴
		salaryPrice, washPrice := (&setting.LineAllowancePrice{}).GetSalaryPriceAndWashPrice(s.LineId)
		if salaryPrice > linePrice {
			linePrice = salaryPrice
		}
		if washPrice > lineWashPrice {
			lineWashPrice = washPrice
		}
	}
	// 开始汇总
	staff = staffs[0]
	report, _ := (&hrModel.StaffPayrollReport{}).GetByStaffIdAndMonth(staff.StaffId, hrModel.DriverBlueBus, staff.Month)
	if report.Id == 0 {
		report.Type = hrModel.DriverBlueBus
		report.Month = staff.Month
		report.StaffId = staff.StaffId
	}
	report.IDCard = staff.IDCard
	report.JobNumber = staff.JobNumber
	report.StaffName = staff.StaffName
	report.LineId = staff.LineId
	report.LineName = staff.LineName
	report.WorkDay = workDay / 100                // 上岗天数
	report.Holiday = holiday / 10                 // 节假日
	report.OverWorkHalfDay = overWorkHalfDay / 10 // 半天班
	report.OverWorkAllDay = overWorkAllDay / 10   // 全天班
	report.PlanWorkDayTotal = planWorkDay / 10    // 计划天数
	report.NightAddWorkTime = nightWorkTime       // 夜班时间
	month = staff.Month
	//应发的基础工资
	var config settingModle.StaffPayrollConfig
	baseWorkDay = config.CommonBaseDay(settingModle.StandardCalDaysConfig)
	fmt.Println(baseWorkDay)
	if baseWorkDay == 0 {
		log.ErrorFields("标准计算天数为0", nil)
		return
	}

	baseWorkDay2 = config.CommonBaseDay(settingModle.BaseSalaryStandardCalDaysConfig)
	fmt.Println(baseWorkDay2)
	if baseWorkDay2 == 0 {
		log.ErrorFields("标准计算天数为0", nil)
		return
	}
	drvLicenseTypes := strings.Split(oetStaff.DrvLicenseTypeStr, ",") // 准假车型表
	for _, drvLicenseType := range drvLicenseTypes {
		if drvLicenseType == "A1" || drvLicenseType == "A3" {
			// 大客
			drvType = 1
		}
		if drvLicenseType == "A2" || drvLicenseType == "B1" {
			drvType = 2
		}
	} // 准假车型 司机类别
	// 班制外加班明细表
	OutFrequencyReports, _ := (&operationModel.OutFrequencyAddWorkReport{}).GetBy(1, []int64{}, []int64{}, []int64{}, []int64{staff.StaffId}, startAt, endAt, model.Paginator{Limit: 0})
	if OutFrequencyReports != nil {
		for _, outReport := range OutFrequencyReports {
			if outReport.AddWorkType == operationModel.HighManeuverType {
				highManeuverDay += outReport.DayCount
			}
		}
	}
	notWorkDay = (&hrModel.ApplyLeaveRecord{}).GetDayCountByTimeRange(startAt, endAt, staff.StaffId, []int64{1, 5, 7}) // 休息天数 年假1 陪产假5 婚嫁7
	notWorkDay = notWorkDay * 100                                                                                      // 放大一百倍
	// 其他 实际折算天数
	actualDay := workDay + notWorkDay // 实际折算天数
	if actualDay > baseWorkDay {
		actualDay = baseWorkDay
	}
	// 基础工资 实际折算天数
	actualDay2 := workDay + notWorkDay
	if actualDay > baseWorkDay2 {
		actualDay = baseWorkDay2
	}

	report.ActualDay = workDay / 100 // 实际出勤天数
	baseSalary := config.GetBaseSalary(drvType)
	report.BaseSalary = calculateSalary(actualDay2, baseSalary, baseWorkDay2)
	//计划外加班半天
	unplannedHalfSalary := config.CommonWageBase(settingModle.UnplannedHalfDayConfig)
	report.OverWorkHalfDayReward = unplannedHalfSalary * overWorkHalfDay / 10
	// 计划外加班整天
	unplannedAllSalary := config.CommonWageBase(settingModle.UnplannedAllDayConfig)
	report.OverWorkAllDayReward = unplannedAllSalary * overWorkAllDay / 10
	// 节假日加班
	holidaySalary := config.CommonWageBase(settingModle.HolidayConfig)
	report.Holiday = holiday / 10
	report.HolidayOvertimePay = holidaySalary * holiday / 10
	// 夜班补贴
	report.NightShiftAllowance = config.GetNightSalary(nightWorkTime)
	// 安全服务奖金
	// 服务奖金
	monthInt, _ := strconv.Atoi(month)
	assessReports := (&assessModel.DriverDevoteMonthMoneyReport{}).GetDriverMonthReport(staff.StaffId, int64(monthInt))
	driverMonthAssessReports, _ := (&assessModel.DriverMonthAssessReport{}).GetBy([]int64{}, 0, staff.StaffId, int64(monthInt), model.Paginator{Offset: 0, Limit: 9999})
	var additionalSafeMoney int64
	var additionalServiceMoney int64
	var serviceAssessReward int64      // 服务质量奖
	var safeSpecialAssessReward int64  // 专项考核奖
	var safeAccidentAssessReward int64 // 事故考核奖
	var safeAssessReward int64         // 规范行为奖
	for _, v := range driverMonthAssessReports {
		additionalSafeMoney += v.AdditionalSafeMoney
		additionalServiceMoney += v.AdditionalServiceMoney
		serviceAssessReward += v.ServiceAssessReward
		safeSpecialAssessReward += v.SpecialAssessReward
		safeAssessReward += v.SafeAssessReward
		safeAccidentAssessReward += v.AccidentAssessReward
	}

	report.ServiceAssessReward = assessReports.ServiceDevoteMoney + additionalServiceMoney + serviceAssessReward
	report.SafeReward = assessReports.SafeDevoteMoney + additionalSafeMoney + safeSpecialAssessReward + safeAccidentAssessReward + safeAssessReward
	// 人次激励奖
	passengerSalary := config.CommonWageBase(settingModle.PassengerConfig)
	if passengerReward {
		report.PassengerReward = passengerSalary
	} else {
		report.PassengerReward = 0
	}
	// 司龄津贴
	joinAt := model.LocalTime(time.Unix(oetStaff.WorkTime, 0))
	year := yearsSince(time.Time(joinAt))
	ageBase := config.GetAgeBase()
	report.RegisterTime = oetStaff.WorkTime                       // 进公司年份
	ageActualDay := workDay + notWorkDay*100 + highManeuverDay*10 // 实际折算天数
	if ageActualDay > baseWorkDay {
		ageActualDay = baseWorkDay
	}
	report.AgeAllowance = calculateSalary(ageActualDay, year*ageBase, baseWorkDay) // 司龄津贴
	// 餐费补贴
	report.CateringAllowance = config.GetCateringAllowance(fullWorkDay*10, halfWorkDay*10, highManeuverDay)
	// 线路补贴
	report.LineAllowance = calculateSalary(actualDay, linePrice, baseWorkDay)
	// 高温补贴
	highTemperature := config.GetHighTemperatureAllowanceBase(month)
	report.HighTemperatureAllowance = calculateSalary(actualDay, highTemperature, baseWorkDay)
	// 疗休养
	recuperationLeaveBase := config.GetRecuperationLeaveBase()
	RestTimeDay := (&hrModel.ApplyLeaveRecord{}).GetDayCountByTimeRange(startAt, endAt, staff.StaffId, []int64{9}) // 休息天数 疗休养9
	report.RecuperationLeave = RestTimeDay * recuperationLeaveBase
	// 洗车补贴
	report.WashCar = lineWashPrice
	// 年审补贴
	report.AnnualAuditAmount = config.GetAnnualAuditAmount(OutFrequencyReports)
	//病假、婚假、丧假补贴
	report.SpecialLeave = config.GetSpecialLeave(workDay, baseWorkDay, 0, startAt, endAt, staff.StaffId)
	// 按日期分类
	reportMap := make(map[string][]operationModel.OutFrequencyAddWorkReport)
	for _, report := range OutFrequencyReports {
		reportMap[report.ReportAt.String()] = append(reportMap[report.ReportAt.String()], report)
	}
	report.TrainAllowance = config.GetTrainAllowance(reportMap)
	report.CharterMissionAllowance = config.GetCharterMissionAllowance(OutFrequencyReports)
	report.Other = config.GetOtherSalary(reportMap)
	report.Build(oetStaff.CorporationId)
	report.CalculateSalaryPayable()
	report.CalculateActualPayment()
	if report.Id == 0 {
		_ = report.Create()
	} else {
		_ = report.Update()
	}
}

func calculateSalary(ActualDay, Base, baseWorkDay int64) int64 {
	days := big.NewInt(ActualDay)
	totalDays := big.NewInt(baseWorkDay)
	amountInCents := big.NewInt(Base)
	ten := big.NewInt(10) // 用于四舍五入到角位
	// 1. 计算比例: 18/22
	ratio := new(big.Rat).SetFrac(days, totalDays)
	// 2. 计算金额（分）: (18/22) * 150000
	resultRat := new(big.Rat).Mul(ratio, new(big.Rat).SetInt(amountInCents))
	// 3. 四舍五入到角位（0.1元 = 10分）
	// 先将结果除以10，得到角位的数量
	scaled := new(big.Rat).Quo(resultRat, new(big.Rat).SetInt(ten))

	// 转换为浮点数以便四舍五入
	scaledFloat, _ := scaled.Float64()
	rounded := int64(scaledFloat + 0.5) // 四舍五入

	// 4. 转换回分单位（乘以10）
	finalCents := rounded * 10
	return finalCents
}

func yearsSince(t time.Time) int64 {
	now := time.Now()

	// 计算年份差异
	years := now.Year() - t.Year()

	// 如果今年的生日还没到，减去1年
	if now.YearDay() < t.YearDay() {
		years--
	}

	return int64(years)
}

func ParseTimeLengthByTimeRange(settings []operationModel.OperationLineSetting, startAt, endAt time.Time) WorkAndNotWorkTimeLength {
	for i := range settings {
		if time.Time(settings[i].StartAt).Unix() < startAt.Unix() {
			settings[i].StartAt = model.LocalTime(startAt)
		}
		if time.Time(settings[i].EndAt).Unix() > endAt.Unix() {
			settings[i].EndAt = model.LocalTime(endAt)
		}
	}

	var timeLengthSetting WorkAndNotWorkTimeLength
	for i := range settings {
		var settingItem operationModel.OperationLineSettingSettingItem
		err := json.Unmarshal(settings[i].SettingItem, &settingItem)
		if err != nil {
			log.ErrorFields("LineSalaryReport setting.SettingItem json.Unmarshal error", map[string]interface{}{"err": err})
			return timeLengthSetting
		}

		if settingItem.HasRangeTrip == util.StatusForTrue {
			timeLengthSetting.HasRangeTrip = util.StatusForTrue
		}
		if i == len(settings)-1 {
			timeLengthSetting.FrequencyType = settingItem.FrequencyType
			timeLengthSetting.FrequencyDay = settingItem.FrequencyDay
		}

		timeLengthSetting.FullWorkTimeLength = append(timeLengthSetting.FullWorkTimeLength, operationModel.LineReportSettingTimeLength{
			StartAt:       settings[i].StartAt,
			EndAt:         settings[i].EndAt,
			Value:         settingItem.UpFullRatedWorkTime,
			FrequencyType: settingItem.FrequencyType,
			FrequencyDay:  settingItem.FrequencyDay,
		})
		timeLengthSetting.FullNotWorkTimeLength = append(timeLengthSetting.FullNotWorkTimeLength, operationModel.LineReportSettingTimeLength{
			StartAt:       settings[i].StartAt,
			EndAt:         settings[i].EndAt,
			Value:         settingItem.UpFullRatedNotWorkTime,
			FrequencyType: settingItem.FrequencyType,
			FrequencyDay:  settingItem.FrequencyDay,
		})
		timeLengthSetting.FullSinglePassWorkTimeLength = append(timeLengthSetting.FullSinglePassWorkTimeLength, operationModel.LineReportSettingTimeLength{
			StartAt:       settings[i].StartAt,
			EndAt:         settings[i].EndAt,
			Value:         settingItem.UpFullRatedWorkTime + settingItem.UpFullRatedNotWorkTime,
			FrequencyType: settingItem.FrequencyType,
			FrequencyDay:  settingItem.FrequencyDay,
		})
		timeLengthSetting.RangeWorkTimeLength = append(timeLengthSetting.RangeWorkTimeLength, operationModel.LineReportSettingTimeLength{
			StartAt:       settings[i].StartAt,
			EndAt:         settings[i].EndAt,
			Value:         settingItem.UpRangeRatedWorkTime,
			FrequencyType: settingItem.FrequencyType,
			FrequencyDay:  settingItem.FrequencyDay,
		})
		timeLengthSetting.RangeNotWorkTimeLength = append(timeLengthSetting.RangeNotWorkTimeLength, operationModel.LineReportSettingTimeLength{
			StartAt:       settings[i].StartAt,
			EndAt:         settings[i].EndAt,
			Value:         settingItem.UpRangeRatedNotWorkTime,
			FrequencyType: settingItem.FrequencyType,
			FrequencyDay:  settingItem.FrequencyDay,
		})
		timeLengthSetting.RangeSinglePassWorkTimeLength = append(timeLengthSetting.RangeSinglePassWorkTimeLength, operationModel.LineReportSettingTimeLength{
			StartAt:       settings[i].StartAt,
			EndAt:         settings[i].EndAt,
			Value:         settingItem.UpRangeRatedWorkTime + settingItem.UpRangeRatedNotWorkTime,
			FrequencyType: settingItem.FrequencyType,
			FrequencyDay:  settingItem.FrequencyDay,
		})
	}
	return timeLengthSetting
}
