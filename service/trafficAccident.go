package service

import (
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"encoding/json"
	"gorm.io/gorm"
	"strings"
	"time"
)

func BuildAccidentLogger(userId int64, userName, ip string, _oldData, _newData *safetyModel.TrafficAccident, processId string) {
	// _oldData, _newData 改为指针
	// fatal error: newproc: function arguments too large for new goroutine

	var oldData, newData safetyModel.TrafficAccident
	if _oldData != nil {
		oldData = *_oldData
	} else {
		oldData = safetyModel.TrafficAccident{}
	}
	if _newData != nil {
		newData = *_newData
	} else {
		newData = safetyModel.TrafficAccident{}
	}

	scene := safetyModel.TrafficLoggerSceneUpdate
	modular := safetyModel.TrafficLoggerModularForAccidentEdit
	var beforeData, afterData []byte

	snapshot, _ := json.Marshal(newData)

	if oldData.Id == 0 {
		scene = safetyModel.TrafficLoggerSceneCreate
		modular = safetyModel.TrafficLoggerModularForAccidentCreate
		_, newDiff := FindDifferentField(nil, newData, safetyModel.TrafficAccidentLoggerExceptField)
		afterData, _ = json.Marshal(newDiff)
	} else {
		oldDiff, newDiff := FindDifferentField(oldData, newData, safetyModel.TrafficAccidentLoggerExceptField)
		beforeData, _ = json.Marshal(oldDiff)
		afterData, _ = json.Marshal(newDiff)
	}

	if (len(beforeData) == 0 && len(afterData) == 0) || string(beforeData) == string(afterData) {
		return
	}

	CreateLogger(userId, userName, newData.Id, scene, ip, modular, beforeData, afterData, snapshot, processId)
}

func CreateLogger(userId int64, userName string, id, scene int64, ip, modular string, beforeData, afterData, snapshot []byte, processId string) {
	var trafficAccidentLogger safetyModel.TrafficAccidentLogger
	trafficAccidentLogger.Scene = scene
	trafficAccidentLogger.BeforeData = beforeData
	trafficAccidentLogger.AfterData = afterData

	trafficAccidentLogger.TrafficAccidentId = id
	trafficAccidentLogger.Ip = ip
	trafficAccidentLogger.Modular = modular

	trafficAccidentLogger.OpUserId = userId
	trafficAccidentLogger.OpUserName = userName

	trafficAccidentLogger.Snapshot = snapshot
	trafficAccidentLogger.ProcessId = processId

	_ = trafficAccidentLogger.Create()
}

func BuildAccidentProcessTitle(accident *safetyModel.TrafficAccident) string {
	// 车牌/事故发生日期/机构/线路/司机/流程种类

	var titleArr []string
	titleArr = append(titleArr, accident.License)
	happenAt := time.Time(accident.HappenAt).Format(model.DateFormat)
	titleArr = append(titleArr, happenAt)

	if accident.CorporationName == "" {
		_, accident.CorporationName = accident.Corporations.GetCorporation()
	}

	titleArr = append(titleArr, accident.CorporationName)
	titleArr = append(titleArr, accident.LineName)
	titleArr = append(titleArr, accident.DriverName)

	return strings.Join(titleArr, "/")
}

// IsExistDoingProcess 是否有进行中、已通过、撤回、驳回的流程 包括付款、借款、退款、分支结案
func IsExistDoingProcess(accident *safetyModel.TrafficAccident) bool {
	//是否存在进行中、已通过、撤回、驳回的结案分支
	isExistDoBranch := (&safetyModel.TrafficAccidentRelaterBranch{}).IsExistDoBranch(accident.Id)

	if isExistDoBranch {
		return true
	}

	//是否存在进行中、已通过、撤回、驳回的付款记录
	isExistDoPayment := (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).IsExistDoPaymentRecord(accident.Id)
	if isExistDoPayment {
		return true
	}

	//是否存在进行中、已通过、撤回、驳回的借款记录
	isExistDoLend := (&safetyModel.TrafficAccidentLendMoneyRecord{}).IsExistDoLendRecord(accident.Id)
	if isExistDoLend {
		return true
	}

	//是否存在进行中、已通过、撤回、驳回的借退款记录
	isExistDoDrawback := (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).IsExistDoDrawbackRecord(accident.Id)
	if isExistDoDrawback {
		return true
	}

	return false

}

func DeleteBranchAndRelationData(tx *gorm.DB, branchIds []int64) error {
	//删除分支
	err := tx.Where("Id IN ?", branchIds).Delete(&safetyModel.TrafficAccidentRelaterBranch{}).Error
	if err != nil {
		return err
	}

	//删除付款记录、流程、流程对应审批人、流程对应消息
	var paymentRecordIds, processIds []int64
	model.DB().Model(&safetyModel.TrafficAccidentPaymentMoneyRecord{}).Select("Id").Where("TrafficAccidentRelaterBranchId IN ?", branchIds).Pluck("Id", &paymentRecordIds)
	if len(paymentRecordIds) > 0 {
		var formInstanceIds []int64
		model.DB().Model(&processModel.LbpmApplyProcess{}).Select("FormInstanceId").Where("ItemId IN ? AND ItemTableName = ?", paymentRecordIds, (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).TableName())
		if len(formInstanceIds) > 0 {
			processIds = append(processIds, formInstanceIds...)
		}

		err = tx.Where("Id IN ?", paymentRecordIds).Delete(&safetyModel.TrafficAccidentPaymentMoneyRecord{}).Error
		if err != nil {
			return err
		}
	}

	//删除借款记录、流程、流程对应审批人、流程对应消息
	var lendRecordIds []int64
	model.DB().Model(&safetyModel.TrafficAccidentLendMoneyRecord{}).Select("Id").Where("TrafficAccidentRelaterBranchId IN ?", branchIds).Pluck("Id", &paymentRecordIds)
	if len(paymentRecordIds) > 0 {
		var formInstanceIds []int64
		model.DB().Model(&processModel.LbpmApplyProcess{}).Select("FormInstanceId").Where("ItemId IN ? AND ItemTableName = ?", lendRecordIds, (&safetyModel.TrafficAccidentLendMoneyRecord{}).TableName())
		if len(formInstanceIds) > 0 {
			processIds = append(processIds, formInstanceIds...)
		}

		err = tx.Where("Id IN ?", lendRecordIds).Delete(&safetyModel.TrafficAccidentLendMoneyRecord{}).Error
		if err != nil {
			return err
		}
	}

	var drawbackRecordIds []int64
	model.DB().Model(&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).Select("Id").Where("TrafficAccidentRelaterBranchId IN ?", branchIds).Pluck("Id", &drawbackRecordIds)
	if len(drawbackRecordIds) > 0 {
		var formInstanceIds []int64
		model.DB().Model(&processModel.LbpmApplyProcess{}).Select("FormInstanceId").Where("ItemId IN ? AND ItemTableName = ?", drawbackRecordIds, (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).TableName())
		if len(formInstanceIds) > 0 {
			processIds = append(processIds, formInstanceIds...)
		}

		err = tx.Where("Id IN ?", drawbackRecordIds).Delete(&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).Error
		if err != nil {
			return err
		}
	}

	//分支结案流程
	var formInstanceIds []int64
	model.DB().Model(&processModel.LbpmApplyProcess{}).Select("FormInstanceId").Where("ItemId IN ? AND ItemTableName = ?", branchIds, (&safetyModel.TrafficAccidentRelaterBranch{}).TableName())
	if len(formInstanceIds) > 0 {
		processIds = append(processIds, formInstanceIds...)
	}

	if len(processIds) > 0 {
		err = DeleteProcessAndMessage(tx, processIds)
		if err != nil {
			return err
		}
	}

	return nil
}
