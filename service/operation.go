package service

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	operationModel "app/org/scs/erpv2/api/model/operation"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"github.com/shopspring/decimal"
	"sort"
	"strconv"
	"time"
)

// GetDriverMainRunLineInfo
// 获取司机主运营线路：司机在哪条线路上的出勤天数多，这条线路即为主营线路；如果出现相同的出勤天数，则按线路ID升序排列，取前者为主营线路
func GetDriverMainRunLineInfo(driverId int64, startAt, endAt time.Time) operationModel.DriverLineWorkDayCountItem {
	//获取司机在时间段内在每条线上的出勤时间
	reports := (&operationModel.LineVehicleMileageReport{}).GetDriverLineWorkDayCount(driverId, startAt, endAt)

	sort.SliceStable(reports, func(i, j int) bool {
		if reports[i].WorkDayCount == reports[j].WorkDayCount {
			return reports[i].WorkDayCount > reports[j].WorkDayCount && reports[i].LineId < reports[j].LineId
		}
		return reports[i].WorkDayCount > reports[j].WorkDayCount
	})

	if len(reports) > 0 {
		return reports[0]
	}

	return operationModel.DriverLineWorkDayCountItem{}
}

// GetLineMainRunDriverIds 获取线路下在时间段内主要运营过的司机
func GetLineMainRunDriverIds(corporationId, lineId int64, startAt, endAt time.Time) []int64 {
	var mainDriverIds []int64
	//查询线路在时间段内运营过的所有司机
	allDriverIds := (&operationModel.LineVehicleMileageReport{}).GetLineRunDriverIds([]int64{corporationId}, []int64{lineId}, startAt, endAt)
	if len(allDriverIds) > 0 {
		for i := range allDriverIds {
			runLine := GetDriverMainRunLineInfo(allDriverIds[i], startAt, endAt)
			if runLine.LineId == lineId && runLine.CorporationId == corporationId {
				mainDriverIds = append(mainDriverIds, allDriverIds[i])
			}
		}
	}

	return mainDriverIds
}

func GetLinesMainRunDriverIds(corporationIds, lineIds []int64, startAt, endAt time.Time) ([]int64, map[int64]operationModel.DriverLineWorkDayCountItem) {
	var mainDriverIds []int64
	var driverMainRunLine = make(map[int64]operationModel.DriverLineWorkDayCountItem)
	//查询线路在时间段内运营过的所有司机
	allDriverIds := (&operationModel.LineVehicleMileageReport{}).GetLineRunDriverIds(corporationIds, lineIds, startAt, endAt)
	if len(allDriverIds) > 0 {
		for i := range allDriverIds {
			runLine := GetDriverMainRunLineInfo(allDriverIds[i], startAt, endAt)
			if util.IncludeInt64(lineIds, runLine.LineId) && util.IncludeInt64(corporationIds, runLine.CorporationId) {
				mainDriverIds = append(mainDriverIds, allDriverIds[i])
				driverMainRunLine[allDriverIds[i]] = runLine
			}
		}
	}

	return mainDriverIds, driverMainRunLine
}

type ApprovalHandler struct {
	HasApproval bool     `json:"-"`
	Creators    []string `json:"Creator"`
	Appliers    []string `json:"Applier"`
	Publishers  []string `json:"Publisher"`
}

func GetApprovalHandler(corporationIds, lineIds []int64, relateReport []string, startAt, endAt time.Time) ApprovalHandler {
	var lineIdStr []string
	for _, lineId := range lineIds {
		lineIdStr = append(lineIdStr, strconv.FormatInt(lineId, 10))
	}

	approvals := (&operationModel.OperationApproval{}).GetCrossApprovalForDone(corporationIds, relateReport, lineIdStr, startAt, endAt)
	var creators []string
	var appliers []string
	for i := range approvals {
		if !util.Include(creators, approvals[i].OpUserName) {
			creators = append(creators, approvals[i].OpUserName)
		}
		processes := (&processModel.LbpmApplyProcess{}).GetProcessesByItemId(config.OperationReportApplyFormTemplate, approvals[i].Id)
		if len(processes) > 0 {
			processHandlers := (&processModel.LbpmApplyProcessHasHandler{}).GetAllHandlerByFormInstanceId(processes[0].FormInstanceId)
			for j := range processHandlers {
				if processHandlers[j].NodeType == util.ProcessNodeTypeForApprove && processHandlers[j].UserName != approvals[i].OpUserName && !util.Include(appliers, processHandlers[j].UserName) {
					appliers = append(appliers, processHandlers[j].UserName)
				}
			}
		}
	}

	return ApprovalHandler{
		Creators:   creators,
		Appliers:   appliers,
		Publishers: creators,
	}
}

// CalcInAndOutPlanDoneCircle 计算排班内完成圈次和排班外完成圈次
func CalcInAndOutPlanDoneCircle(isApproval int64, startAt, endAt time.Time, driverId, corporationId, lineId int64) (int64, int64) {
	//查询司机每日实际完成圈次
	reportAtDoneCircles := (&operationModel.LineVehicleMileageReport{}).GetReportAtDoneCircleByDriver(isApproval, driverId, corporationId, lineId, startAt, endAt)
	var doneCircleMap = make(map[string]int64)
	for i := range reportAtDoneCircles {
		at := reportAtDoneCircles[i].ReportAt.ToTime().Format(model.DateFormat)
		doneCircleMap[at] = reportAtDoneCircles[i].FullDoneCircle
	}

	//查询司机每日计划排班圈次
	reportAtPlanCircles := (&operationModel.PlanScheduleReport{}).GetReportAtPlanCircleByDriver(driverId, lineId, corporationId, startAt, endAt)
	var planCircleMap = make(map[string]int64)
	for i := range reportAtPlanCircles {
		at := reportAtPlanCircles[i].ReportAt.ToTime().Format(model.DateFormat)
		planCircleMap[at] = reportAtPlanCircles[i].PlanCircle
	}

	var inPlanDoneCircle, outPlanDoneCircle int64

	for {
		if startAt.Unix() > endAt.Unix() {
			break
		}
		at := startAt.Format(model.DateFormat)
		doneCircle, _ := doneCircleMap[at]
		planCircle, _ := planCircleMap[at]

		//查询司机请假记录
		leaveRecord := (&hrModel.ApplyLeaveRecordDate{}).GetDriverLeaveRecord(driverId, 0, startAt, nil)
		if leaveRecord.Id > 0 {
			outPlanDoneCircle += doneCircle
		} else {
			if doneCircle <= planCircle {
				inPlanDoneCircle += doneCircle
			} else {
				inPlanDoneCircle += planCircle
				outPlanDoneCircle += doneCircle - planCircle
			}
		}
		startAt = startAt.AddDate(0, 0, 1)
	}

	return inPlanDoneCircle, outPlanDoneCircle
}

func CalcLineCityMileage(corporationId, lineId, IsRelateIrregularLineReport, isApproval int64, startAt, endAt time.Time) (cityCenter, jiaojiang, taizhouwan, huangyan, luqiao, linhai int64) {
	//获取线路每天的里程
	for {
		if startAt.Unix() > endAt.Unix() {
			break
		}
		//查询配置项
		setting := (&operationModel.OperationLineSetting{}).FirstByLineId(corporationId, lineId, startAt)
		if setting.Id == 0 {
			return
		}
		var settingItem operationModel.OperationLineSettingSettingItem
		_ = json.Unmarshal(setting.SettingItem, &settingItem)
		if settingItem.CityCenter == 0 && settingItem.Taizhouwan == 0 && settingItem.Jiaojiang == 0 && settingItem.Huangyan == 0 && settingItem.Luqiao == 0 && settingItem.Linhai == 0 {
			startAt = startAt.AddDate(0, 0, 1)
			continue
		}
		var mileage int64
		mileage += (&operationModel.LineVehicleMileageReport{}).GetLineTotalMileage(isApproval, corporationId, lineId, startAt, startAt)
		mileage += (&operationModel.OutFrequencyAddWorkReport{}).GetLineTotalMileage(isApproval, corporationId, lineId, startAt, startAt)
		if IsRelateIrregularLineReport == util.StatusForTrue {
			mileage += (&operationModel.IrregularLineReport{}).GetLineTotalMileage(isApproval, corporationId, lineId, startAt, startAt)
		}

		cityCenter += decimal.NewFromInt(mileage).Mul(decimal.NewFromFloat(settingItem.CityCenter)).Div(decimal.NewFromInt(100)).IntPart()
		jiaojiang += decimal.NewFromInt(mileage).Mul(decimal.NewFromFloat(settingItem.Jiaojiang)).Div(decimal.NewFromInt(100)).IntPart()
		taizhouwan += decimal.NewFromInt(mileage).Mul(decimal.NewFromFloat(settingItem.Taizhouwan)).Div(decimal.NewFromInt(100)).IntPart()
		huangyan += decimal.NewFromInt(mileage).Mul(decimal.NewFromFloat(settingItem.Huangyan)).Div(decimal.NewFromInt(100)).IntPart()
		luqiao += decimal.NewFromInt(mileage).Mul(decimal.NewFromFloat(settingItem.Luqiao)).Div(decimal.NewFromInt(100)).IntPart()
		linhai += decimal.NewFromInt(mileage).Mul(decimal.NewFromFloat(settingItem.Linhai)).Div(decimal.NewFromInt(100)).IntPart()

		startAt = startAt.AddDate(0, 0, 1)
	}

	return
}
