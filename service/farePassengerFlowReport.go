package service

import (
	"app/org/scs/erpv2/api/log"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/model/ticket"
	"app/org/scs/erpv2/api/model/ticket/mixReport"
	protoLine "app/org/scs/erpv2/api/proto/rpc/oetline"
	protoVehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	"app/org/scs/erpv2/api/service/rpc"
	"context"
	"fmt"
	"gorm.io/gorm"
	"math"
	"strings"
	"time"
)

func CalculateFarePassengerFlowReport(db *gorm.DB, oetLines []*protoLine.OetLineItem, lineIds []int64, start, end, reportMonth string) {
	ticket.FarePassengerFlowReportCalculateFlag = true
	startTime, _ := time.ParseInLocation("20060102", start, time.Local)
	endTime, _ := time.ParseInLocation("20060102", end, time.Local)
	oetLineMap := make(map[int64]*protoLine.OetLineItem)
	lineMap := make(map[int64]map[string]ticket.FarePassengerFlowReport)
	// 1.刷卡人数 - 扫码人数
	for _, oetLine := range oetLines {
		oetLineMap[oetLine.Id] = oetLine
		if oetLine.PosLineNo != "" {
			thirdLines := strings.Split(oetLine.PosLineNo, ",")
			var cardDtlDataList []MysqlTableColumn
			var codeDtlDataList []MysqlTableColumn
			for _, thirdLineNo := range thirdLines {
				cardData := queryLinePeopleCardAmount(db, thirdLineNo, start, end)
				fmt.Println("cardData:长度是:,", len(cardData))
				cardDtlDataList = append(cardDtlDataList, cardData...)
				codeData := queryLinePeopleCodeAmount(db, thirdLineNo, start, end)
				fmt.Println("codeData:长度是:,", len(codeData))
				codeDtlDataList = append(codeDtlDataList, codeData...)
			}
			for i := range cardDtlDataList {
				if _, ok := lineMap[oetLine.Id]; !ok {
					lineMap[oetLine.Id] = make(map[string]ticket.FarePassengerFlowReport)
				}
				report := lineMap[oetLine.Id][cardDtlDataList[i].SettDate]
				report.ReportTime = cardDtlDataList[i].SettDate
				report.PeopleCardAmount++
				lineMap[oetLine.Id][cardDtlDataList[i].SettDate] = report
			}
			for i := range codeDtlDataList {
				if _, ok := lineMap[oetLine.Id]; !ok {
					lineMap[oetLine.Id] = make(map[string]ticket.FarePassengerFlowReport)
				}
				report := lineMap[oetLine.Id][codeDtlDataList[i].SettDate]
				report.ReportTime = codeDtlDataList[i].SettDate
				report.PeopleCodeAmount++
				lineMap[oetLine.Id][codeDtlDataList[i].SettDate] = report
			}
		}
		// 3.客流仪人数
		pfItems := rpc.GetLinePfWithLineIds(context.Background(), oetLine.Id, startTime, endTime)
		if pfItems != nil {
			for _, item := range pfItems {
				if _, ok := lineMap[oetLine.Id]; !ok {
					lineMap[oetLine.Id] = make(map[string]ticket.FarePassengerFlowReport)
				}
				fmt.Println(fmt.Sprintf("路线：%s,日期：%s,客流仪人数：%d", oetLine.Name, item.ReportAt, item.InPeople))
				newT, _ := time.ParseInLocation("2006-01-02", item.ReportAt, time.Local)
				newReportAt := newT.Format("20060102")
				report := lineMap[oetLine.Id][newReportAt]
				report.ReportTime = newReportAt
				report.PeoplePassengerFlowMeter += item.InPeople
				lineMap[oetLine.Id][newReportAt] = report
			}
		}
	}
	// 2.投币人数 - 运营车辆 - 点钞收入
	lineRsp, _ := (&mixReport.TicketMoneyMixReportRecord{}).IncomeLine(2, []int64{}, lineIds, []int64{}, startTime, endTime)
	for _, v := range lineRsp.Data {
		if _, ok := lineMap[v.LineId]; !ok {
			lineMap[v.LineId] = make(map[string]ticket.FarePassengerFlowReport)
		}

		allowance := (&settingModel.LineAllowancePriceSetting{}).GetByLineIdAndMonth(v.BranchId, v.LineId, endTime)
		peopleTicketAmount := math.Ceil(float64(v.MoneyTicketAmount) / float64(allowance.Price))
		dateAt, _ := time.ParseInLocation("2006-01-02", v.DateAt, time.Local)
		dateAtStr := dateAt.Format("20060102")
		report := lineMap[v.LineId][dateAtStr]
		report.LineName = v.Line
		report.ReportTime = dateAtStr
		report.LineOperatingVehicle = v.CountVehicle
		report.PeopleTicketAmount = int64(peopleTicketAmount)
		lineMap[v.LineId][dateAtStr] = report
	}
	// 保存数据
	for lineId, m := range lineMap {
		oetLine := oetLineMap[lineId]
		corporationName := ""
		var CorporationId int64
		if oetLine != nil {
			corporation := rpc.GetCorporationById(context.Background(), oetLine.CorporationId)
			if corporation != nil {
				corporationName = corporation.Name
				CorporationId = corporation.Id
			}
		}
		_, count := rpc.GetVehiclesWithOption(context.Background(), &protoVehicle.GetVehiclesWithOptionRequest{
			CorporationId:   CorporationId,
			LineIds:         []int64{lineId},
			UseStatus:       1,
			IsShowChildNode: true,
		})
		for _, v := range m {
			if oetLine != nil {
				v.LineId = oetLine.Id
				v.LineName = oetLine.Name
			}
			v.CorporationId = CorporationId
			v.CorporationName = corporationName
			v.LineVehicle = count
			v.ReportMonth = reportMonth
			if v.PeoplePassengerFlowMeter != 0 {
				v.OddsRatio = fmt.Sprintf("%.2f%%", float64(v.PeopleTicketAmount+v.PeopleCardAmount+v.PeopleCodeAmount)/float64(v.PeoplePassengerFlowMeter))
			}
			var old ticket.FarePassengerFlowReport
			_ = old.Find(v.LineId, v.ReportTime)
			if old.Id != 0 {
				v.Id = old.Id
				err := v.Updates()
				if err != nil {
					log.ErrorFields("Updates error", map[string]interface{}{"err": err})
				}
			} else {
				err := v.Create()
				if err != nil {
					log.ErrorFields("Create error", map[string]interface{}{"err": err})
				}
			}
		}
	}
	ticket.FarePassengerFlowReportCalculateFlag = false
}

type MysqlTableColumn struct {
	SettDate string `json:"SettDate" gorm:"column:sett_date"`
}

func queryLinePeopleCardAmount(db *gorm.DB, thirdLineNo string, start, end string) (data []MysqlTableColumn) {
	db.Table("card_dtl").Select("sett_date").
		Where("SETT_DATE>=?", start).
		Where("SETT_DATE<=?", end).
		Where("TXN_MCHNT_ID = ?", "8000").
		Where("UP_LINE_ID = ?", thirdLineNo).Find(&data)
	return
}

func queryLinePeopleCodeAmount(db *gorm.DB, thirdLineNo string, start, end string) (data []MysqlTableColumn) {
	db.Table("code_dtl").Select("sett_date").
		Where("SETT_DATE>=?", start).
		Where("SETT_DATE<=?", end).
		Where("MCHNT_ID = ?", "8000").
		Where("LINE_ID = ?", thirdLineNo).Find(&data)
	return
}
