package service

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	"app/org/scs/erpv2/api/service/rpc"
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

func CreateOrUpdateHeadImgApproval(ctx context.Context, StaffId int64, HeadImg model.JSON) error {
	var data hrModel.HeadImgApproval
	data.StaffId = StaffId
	//err := data.FindByStaffId()
	//if err != nil {
	//	return err
	//}
	staffInfo := rpc.GetStaffWithId(ctx, StaffId)
	if staffInfo == nil {
		log.ErrorFields("rpc.GetStaffWithId error", map[string]interface{}{"id": StaffId})
		return errors.New("rpc.GetStaffWithId error")
	}
	data.StaffId = staffInfo.Id
	data.JobNumber = staffInfo.StaffId
	data.StaffName = staffInfo.Name
	data.Corporations.Build(staffInfo.CorporationId)
	var staffArchive hrModel.StaffArchive
	_ = staffArchive.FindByStaffId(data.StaffId)
	data.StaffArchiveId = staffArchive.Id
	data.HeadImg = HeadImg
	data.HeadImgState = 1 // 审批中
	tx := model.DB().Begin()
	err := tx.Create(&data).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	user := auth.User(ctx).GetUser()
	byteParam, _ := json.Marshal(&data)
	processTitle := fmt.Sprintf("%s提交的证件照", user.Name)
	corporation := rpc.GetCorporationDetailById(ctx, staffInfo.CorporationId)
	if corporation == nil {
		tx.Rollback()
		return errors.New("未找到员工机构")
	}
	var departmentCode string
	var departmentName string
	if corporation.Item != nil {
		departmentName = corporation.Item.Name
		departmentCode = corporation.Item.Virtual
	}
	formData := map[string]interface{}{
		"DepartmentName": departmentName,
		"DepartmentCode": departmentCode,
	}
	_, err = processService.NewDispatchProcess(user, config.HeadImgFormTemplate, processTitle, data.Id, data.TableName(), data.ApplyStatusFieldName(), string(byteParam), formData)
	if err != nil {
		log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}
