package service

import (
	"app/org/scs/erpv2/api/log"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	settingModel "app/org/scs/erpv2/api/model/setting"
	accidentSettingModel "app/org/scs/erpv2/api/model/setting/accident"
	"app/org/scs/erpv2/api/util"
	"time"
)

// CheckProcessTimeOutAlarm 被预警告警的处理人审批消息后 更新告警为历史
func CheckProcessTimeOutAlarm(handlers []processModel.LbpmApplyProcessHasHandler) {
	for i := range handlers {
		//检测当前节点当前人是否已审批
		var isApproved bool
		processHandler, _ := (&processModel.LbpmApplyProcessHasHandler{}).FindBy(handlers[i].Id)
		if processHandler.Id > 0 && processHandler.Status != util.ProcessNodeHandleStatusForDoing {
			isApproved = true
		}

		if isApproved {
			alarm := (&settingModel.ProcessTimeoutAlarmRecord{}).FindByFormInstanceId(handlers[i].FormInstanceId, handlers[i].UserId, handlers[i].Node)
			if alarm.Id > 0 {
				//更新为历史  并计算审批时长
				var processingTime int64
				if processHandler.StartAt != nil {
					start := time.Time(*processHandler.StartAt)
					var end = time.Now()
					if processHandler.EndAt != nil {
						end = time.Time(*processHandler.EndAt)
					}
					processingTime = (end.Unix() - start.Unix()) / 60
				}
				alarm.FinishStatus = util.AlarmFinishStatusForDone
				alarm.ProcessingTime = processingTime
				err := alarm.UpdateFinishStatus()
				if err != nil {
					log.ErrorFields("CheckProcessTimeOutAlarm ProcessTimeoutAlarmRecord.UpdateFinishStatus error", map[string]interface{}{"err": err})
				}
			}
		}
	}
}

// CheckAccidentAlarm 被预警告警的事故结案后/移出回收站后 更新事故预警告警处理
func CheckAccidentAlarm(accidentId int64) {
	var accident safetyModel.TrafficAccident
	err := accident.FindBy(accidentId)
	if err != nil {
		return
	}
	if accident.IsClosed == util.StatusForTrue {
		alarm := (&accidentSettingModel.AccidentTimeoutAlarmRecord{}).FindByAccidentId(accidentId)
		if alarm.Id > 0 {
			alarm.FinishStatus = util.AlarmFinishStatusForDone
			closedAt := time.Time(accident.ClosedAt)
			if time.Time(accident.ClosedAt).IsZero() {
				closedAt = time.Now()
			}
			alarm.ProcessingTime = (closedAt.Unix() - time.Time(accident.CreatedAt).Unix()) / 60
			err = alarm.UpdateFinishStatus()
			if err != nil {
				log.ErrorFields("CheckAccidentAlarm AccidentTimeoutAlarmRecord.UpdateFinishStatus error", map[string]interface{}{"err": err})
			}
		}
	}
}
