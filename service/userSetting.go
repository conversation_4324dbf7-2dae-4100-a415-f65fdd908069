package service

import (
	"app/org/scs/erpv2/api/log"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"encoding/json"
)

func GetUserSetting(userId int64) settingModel.UserSettingItem {
	setting := (&settingModel.UserSetting{}).GetByUser(userId)
	var item settingModel.UserSettingItem
	if setting.Id != 0 {
		err := json.Unmarshal(setting.SettingItem, &item)
		log.ErrorFields("UserSettingItem Unmarshal error", map[string]interface{}{"err": err})
	}
	return item
}
