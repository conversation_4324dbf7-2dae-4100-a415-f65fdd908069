package service

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"fmt"
)

// AuthCorporationIdProvider 根据用户权限机构ID和前端选择的机构ID 返回交集机构ID
func AuthCorporationIdProvider(ctx context.Context, corporationIds []int64) []int64 {
	if !auth.User(ctx).HasUser() {
		return []int64{}
	}

	authCorporationIds := rpc.GetUserAuthCorporationIds(ctx, auth.User(ctx).GetUserId())

	fmt.Printf("AuthCorporationIds=============%+v \r\n", authCorporationIds)
	if authCorporationIds == nil {
		return nil
	}

	if len(corporationIds) == 0 {
		return authCorporationIds
	}
	var corpIds []int64
	for i := range corporationIds {
		if util.IncludeInt64(authCorporationIds, corporationIds[i]) {
			corpIds = append(corpIds, corporationIds[i])
		}
	}

	return corpIds

}

type CorporationSimpleInfo struct {
	Id                 int64
	Name               string
	Type               int64
	LinkCorporationIds []int64
}

func GetDepartment(departmentInfo CorporationSimpleInfo, child *[]CorporationSimpleInfo) {
	departments := rpc.CorporationList(context.Background(), departmentInfo.Id)
	if len(departments) > 0 {
		for i := range departments {
			depart := CorporationSimpleInfo{Id: departments[i].Id, Name: departments[i].Name, Type: departments[i].Type}
			*child = append(*child, depart)
			GetDepartment(depart, child)
		}
	}
}

func GetFleet(topCorporationId int64) []CorporationSimpleInfo {
	var corporations []CorporationSimpleInfo
	GetDepartment(CorporationSimpleInfo{Id: topCorporationId}, &corporations)
	var fleets []CorporationSimpleInfo
	for i := range corporations {
		if corporations[i].Type == util.CorporationTypeForFleet {
			fleets = append(fleets, corporations[i])
		}
	}

	return fleets
}

func GetBranchCompany(topCorporationId int64) []CorporationSimpleInfo {
	var corporations []CorporationSimpleInfo
	GetDepartment(CorporationSimpleInfo{Id: topCorporationId}, &corporations)
	var branches []CorporationSimpleInfo
	for i := range corporations {
		if corporations[i].Type == util.CorporationTypeForBranch {
			branches = append(branches, corporations[i])
		}
	}

	return branches
}

func BuildStaffArchiveReportBranch() ([]CorporationSimpleInfo, []int64) {
	var childCorporationIds []int64
	var branches []CorporationSimpleInfo
	branches = append(branches, CorporationSimpleInfo{Id: 0, Name: "巴士本部", Type: 0})
	var departmentIds []int64
	//获取机构下的子机构
	corporations := rpc.CorporationList(context.Background(), config.Config.StaffArchiveReportCorpId)
	for j := range corporations {
		var corp = CorporationSimpleInfo{Id: corporations[j].Id, Name: corporations[j].Name, Type: corporations[j].Type}
		childCorporationIds = append(childCorporationIds, corp.Id)
		if corporations[j].Type == util.CorporationTypeForBranch {
			corp.LinkCorporationIds = append(corp.LinkCorporationIds, corp.Id)
			branches = append(branches, corp)
		}
		if corporations[j].Type == util.CorporationTypeForDepartment || corporations[j].Type == util.CorporationTypeForFleet {
			departmentIds = append(departmentIds, corp.Id)
			//获取部门下的部门
			var childDepartments []CorporationSimpleInfo
			GetDepartment(corp, &childDepartments)
			if len(childDepartments) > 0 {
				for d := range childDepartments {
					departmentIds = append(departmentIds, childDepartments[d].Id)
				}
			}
		}
	}

	branches[0].LinkCorporationIds = departmentIds

	return branches, childCorporationIds

}

func BuildStaffArchiveReportDepartment(branchId int64) ([]CorporationSimpleInfo, []int64) {
	var childCorporationIds []int64
	var departments []CorporationSimpleInfo
	//获取分公司下下的部门/车队
	corporations := rpc.CorporationList(context.Background(), branchId)
	for j := range corporations {
		var corp = CorporationSimpleInfo{Id: corporations[j].Id, Name: corporations[j].Name, Type: corporations[j].Type}
		if corporations[j].Type == util.CorporationTypeForDepartment || corporations[j].Type == util.CorporationTypeForFleet {
			childCorporationIds = append(childCorporationIds, corp.Id)
			corp.LinkCorporationIds = append(corp.LinkCorporationIds, corp.Id)

			//获取部门下的部门
			var childDepartments []CorporationSimpleInfo
			GetDepartment(corp, &childDepartments)
			if len(childDepartments) > 0 {
				for d := range childDepartments {
					childCorporationIds = append(childCorporationIds, childDepartments[d].Id)
					corp.LinkCorporationIds = append(corp.LinkCorporationIds, childDepartments[d].Id)
				}
			}

			departments = append(departments, corp)
		}
	}

	return departments, childCorporationIds

}

type BranchAndFleetItem struct {
	CorporationSimpleInfo
	Fleets []CorporationSimpleInfo
}

func BuildBranchAndFleet(corporationId int64) []BranchAndFleetItem {
	var items []BranchAndFleetItem
	branches := GetBranchCompany(corporationId)
	for i := range branches {
		var item = BranchAndFleetItem{
			CorporationSimpleInfo: branches[i],
		}
		corporations := rpc.CorporationList(context.Background(), branches[i].Id)
		for j := range corporations {
			if corporations[j].Type == util.CorporationTypeForFleet {
				item.Fleets = append(item.Fleets, CorporationSimpleInfo{
					Id:   corporations[j].Id,
					Name: corporations[j].Name,
					Type: corporations[j].Type,
				})
				item.LinkCorporationIds = append(item.LinkCorporationIds, corporations[j].Id)
			}
		}
		if len(item.LinkCorporationIds) > 0 {
			items = append(items, item)
		}
	}

	return items
}
