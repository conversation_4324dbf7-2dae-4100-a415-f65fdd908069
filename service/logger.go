package service

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
)

func FindDifferentField(oldData, newData interface{}, exceptField []string) (map[string]interface{}, map[string]interface{}) {
	newField, err := util.StructToMapByReflect(newData, "json")
	if err != nil {
		log.ErrorFields("StructToMapByReflect newData err", map[string]interface{}{"err": err})
		return nil, nil
	}

	if oldData == nil {
		for key := range newField {
			if util.Include(exceptField, key) {
				delete(newField, key)
			}
		}
		return nil, newField
	}

	oldField, err := util.StructToMapByReflect(oldData, "json")
	if err != nil {
		log.ErrorFields("StructToMapByReflect oldData err", map[string]interface{}{"err": err})
		return nil, nil
	}

	for key := range oldField {
		if util.Include(exceptField, key) {
			delete(oldField, key)
			delete(newField, key)
		}

		_, isInt64 := oldField[key].(int64)
		_, isString := oldField[key].(string)
		_, isTime := oldField[key].(model.LocalTime)
		_, isJson := oldField[key].(model.JSON)
		if isInt64 || isString || isTime {
			if oldField[key] == newField[key] {
				delete(oldField, key)
				delete(newField, key)
			}
		} else if isJson {
			var oldSlice []map[string]interface{}
			var newSlice []map[string]interface{}
			err1 := json.Unmarshal(oldField[key].(model.JSON), &oldSlice)
			err2 := json.Unmarshal(newField[key].(model.JSON), &newSlice)
			if err1 == nil && err2 == nil {
				if len(newSlice) != len(oldSlice) {
					continue
				}
				var isDiff bool
				for i := range oldSlice {
					var has bool
					if _, ok := oldSlice[i]["Url"]; ok {
						for j := range newSlice {
							if newSlice[j]["Url"] == oldSlice[i]["Url"] {
								has = true
							}
						}
					}
					if !has {
						isDiff = true
					}
				}

				if !isDiff {
					delete(oldField, key)
					delete(newField, key)
				}
				continue
			}

		} else {
			oldTmp, _ := json.Marshal(oldField[key])
			newTmp, _ := json.Marshal(newField[key])
			if string(oldTmp) == string(newTmp) {
				delete(oldField, key)
				delete(newField, key)
			}
		}
	}

	return oldField, newField
}
