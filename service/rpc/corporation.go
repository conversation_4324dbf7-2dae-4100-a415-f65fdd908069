package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	protoCorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	"context"
)

func GetCorporationNameById(ctx context.Context, corporationId int64) string {
	if corporationId == 0 {
		return ""
	}
	client := protoCorporation.NewCorporationService(srvPublicServiceName, config.Global.MicroClient)
	corporation, err := client.GetById(ctx, &protoCorporation.GetCorporationByIdRequest{
		Id: corporationId,
	})

	if err != nil {
		log.ErrorFields("rpc GetById error", map[string]interface{}{"err": err, "id": corporationId})
		return ""
	}

	return corporation.Name
}

// GetCorporationById 用机构ID获取机构详情
func GetCorporationById(ctx context.Context, corporationId int64) *protoCorporation.CorporationItem {
	if corporationId == 0 {
		return nil
	}
	client := protoCorporation.NewCorporationService(srvPublicServiceName, config.Global.MicroClient)
	corporation, err := client.GetById(ctx, &protoCorporation.GetCorporationByIdRequest{
		Id: corporationId,
	})

	if err != nil {
		log.ErrorFields("rpc GetById error", map[string]interface{}{"err": err, "id": corporationId})
		return nil
	}

	return corporation
}

// GetCorporationByName 根据机构名查询机构
func GetCorporationByName(ctx context.Context, name string) *protoCorporation.CorporationItem {
	if name == "" {
		return nil
	}
	client := protoCorporation.NewCorporationService(srvPublicServiceName, config.Global.MicroClient)
	corporation, err := client.Get(ctx, &protoCorporation.GetCorporationByNameRequest{
		Name: name,
	})

	if err != nil {
		log.ErrorFields("rpc GetCorporationByName error", map[string]interface{}{"err": err, "name": name})
		return nil
	}

	return corporation
}

// GetCorporationDetailById 查询机构详细信息 包含上级机构 以及机构类型
func GetCorporationDetailById(ctx context.Context, corporationId int64) *protoCorporation.GetCorporationDetailByIdResponse {
	if corporationId == 0 {
		return nil
	}
	client := protoCorporation.NewCorporationService(srvPublicServiceName, config.Global.MicroClient)
	corporation, err := client.GetCorporationDetailById(ctx, &protoCorporation.GetCorporationDetailByIdRequest{
		CorporationId: corporationId,
	})

	if err != nil {
		log.ErrorFields("rpc GetCorporationDetailById error", map[string]interface{}{"err": err, "id": corporationId})
		return nil
	}

	return corporation
}

// GetTopCorporationById 获取顶级机构详情
func GetTopCorporationById(ctx context.Context, corporationId int64) *protoCorporation.CorporationItem {
	if corporationId == 0 {
		return nil
	}
	client := protoCorporation.NewCorporationService(srvPublicServiceName, config.Global.MicroClient)
	corporation, err := client.GetTopDetailById(ctx, &protoCorporation.GetTopDetailByIdRequest{
		CorporationId: corporationId,
	})

	if err != nil {
		log.ErrorFields("rpc GetById error", map[string]interface{}{"err": err, "id": corporationId})
		return nil
	}
	if corporation.Item == nil {
		log.ErrorFields("rpc GetById corporation.Item is nil", map[string]interface{}{"id": corporationId})
		return nil
	}

	return corporation.Item
}

// CorporationTree 机构树形结构
func CorporationTree(ctx context.Context, corpId int64, order string) ([]*protoCorporation.CorporationBriefItem, int64, error) {
	client := protoCorporation.NewCorporationService(srvPublicServiceName, config.Global.MicroClient)
	corporation, err := client.Tree(ctx, &protoCorporation.TreeCorporationRequest{
		ParentId: corpId,
		Order:    order,
	})

	if err != nil {
		log.ErrorFields("rpc Tree error", map[string]interface{}{"err": err, "id": corpId})
		return nil, 0, err
	}
	if corporation.Items == nil {
		log.ErrorFields("rpc Tree corporation.Item is nil", map[string]interface{}{"id": corpId})
		return nil, 0, err
	}

	return corporation.Items, corporation.TotalCount, nil
}

// CorporationList 机构列表
func CorporationList(ctx context.Context, corpId int64) []*protoCorporation.CorporationItem {
	client := protoCorporation.NewCorporationService(srvPublicServiceName, config.Global.MicroClient)
	corporation, err := client.List(ctx, &protoCorporation.ListCorporationRequest{
		ParentId: corpId,
		Limit:    9999,
	})

	if err != nil {
		log.ErrorFields("rpc CorporationList error", map[string]interface{}{"err": err, "id": corpId})
		return nil
	}

	return corporation.Items
}
