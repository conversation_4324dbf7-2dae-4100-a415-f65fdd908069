package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	protoParking "app/org/scs/erpv2/api/proto/rpc/parking"
	"context"
)

// GetParkingWithId 场站详情
func GetParkingWithId(ctx context.Context, id int64) *protoParking.OetParkingItem {
	client := protoParking.NewOetParkingService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetParkingWithId(ctx, &protoParking.GetParkingWithIdRequest{
		ParkingId: id,
	})

	if err != nil || response == nil {
		log.ErrorFields("rpc GetParkingWithId error", map[string]interface{}{"err": err, "id": id})
		return nil
	}

	return response.Item
}

// GetListWithCorpId 获取顶级机构下类型为停车场的场站列表
func GetListWithCorpId(ctx context.Context, topCorpId int64, name string, parkingType int64) []*protoParking.OetParkingItem {
	client := protoParking.NewOetParkingService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetListWithCorpId(ctx, &protoParking.GetListWithCorpIdRequest{
		CorporationId: topCorpId,
		ParkingType:   parkingType,
		Name:          name,
	})

	if err != nil || response == nil {
		log.ErrorFields("rpc GetParkingWithId error", map[string]interface{}{"err": err})
		return nil
	}

	return response.Items
}

func GetParkingListWithOpt(ctx context.Context, opt *protoParking.GetListWithCorpIdRequest) ([]*protoParking.OetParkingItem, int64) {
	client := protoParking.NewOetParkingService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetListWithCorpId(ctx, opt)

	if err != nil || response == nil {
		log.ErrorFields("rpc GetParkingWithId error", map[string]interface{}{"err": err})
		return nil, 0
	}

	return response.Items, response.TotalCount
}
