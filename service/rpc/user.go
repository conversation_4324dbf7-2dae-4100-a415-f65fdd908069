package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	protoUser "app/org/scs/erpv2/api/proto/rpc/user"
	"context"
	"fmt"
)

// GetUserInfoById 获取登录账号信息
func GetUserInfoById(ctx context.Context, userId int64) *protoUser.MainUser {
	client := protoUser.NewUserService(srvPublicServiceName, config.Global.MicroClient)
	response, err := client.GetMainUserById(ctx, &protoUser.GetMainUserByIdRequest{
		Id: userId,
	})

	if err != nil {
		log.ErrorFields("rpc oet GetMainUserById error", map[string]interface{}{"err": err, "userId": userId})
		return nil
	}

	return response
}

func GetUserInfoByPhone(ctx context.Context, corporationId int64, phone string) *protoUser.MainUser {
	client := protoUser.NewUserService(srvPublicServiceName, config.Global.MicroClient)
	response, err := client.GetMainUserByPhone(ctx, &protoUser.GetMainUserByPhoneRequest{
		Phone:         phone,
		CorporationId: corporationId,
	})

	if err != nil {
		log.ErrorFields("rpc oet GetMainUserByPhone error", map[string]interface{}{"err": err})
		return nil
	}

	return response
}

func ListMainUser(ctx context.Context, corporationId int64, name, phone string, status int64) []*protoUser.MainUser {
	client := protoUser.NewUserService(srvPublicServiceName, config.Global.MicroClient)
	response, err := client.ListMainUser(ctx, &protoUser.ListMainUserRequest{
		Phone:                    phone,
		CorporationId:            corporationId,
		IsShowSonCorporationUser: true,
		Offset:                   0,
		Limit:                    99999,
		Nickname:                 name,
		Status:                   status,
	})

	if err != nil {
		log.ErrorFields("rpc oet GetMainUserByPhone error", map[string]interface{}{"err": err})
		return nil
	}

	fmt.Printf("ListMainUser,len:%+v,total:%v \n", len(response.Items), response.TotalCount)

	return response.Items
}
