package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	protoMini "app/org/scs/erpv2/api/proto/rpc/mini"
	protoPermission "app/org/scs/erpv2/api/proto/rpc/permission"
	protoTree "app/org/scs/erpv2/api/proto/rpc/tree"
	protoUser "app/org/scs/erpv2/api/proto/rpc/user"
	"context"
	"errors"
	"fmt"
)

var srvPublicServiceName = "oet.scs.srv.public"
var srvAppServiceName = "oet.scs.srv.app"
var srvIssServiceName = "oet.scs.api.iss"
var srvMiniServiceName = "oet.scs.api.mini"
var srvIpocServiceName = "oet.scs.api.ipoc"
var srvDwhServiceName = "oet.scs.api.dwh"

// AddPermission 新增系统权限
func AddPermission(ctx context.Context, permission *protoPermission.AddPermissionRequest) error {
	client := protoPermission.NewPermissionService(srvPublicServiceName, config.Global.MicroClient)
	_, err := client.AddPermission(ctx, permission)
	if err != nil {
		//log.ErrorFields("rpc oet AddPermission error", map[string]interface{}{"err": err})
		return err
	}
	//
	//if response.Id == 0 {
	//	log.ErrorFields("rpc oet AddPermission is fail", map[string]interface{}{"permission": permission})
	//	return errors.New("AddPermission maybe exists")
	//}

	return nil

}

// SendUniformMsg 发送公众号消息
func SendUniformMsg(ctx context.Context, appAccountId int64, toUserPhone, templateId, pagePath string, data []*protoMini.UniformMsgKeyValue) error {
	client := protoMini.NewMessageService(srvMiniServiceName, config.Global.MicroClient)

	opt := &protoMini.SendUniformMsgRequest{
		AppAccountId: appAccountId,
		UserType:     2,
		ToUser:       toUserPhone,
		MpTemplate: &protoMini.UniformMpTmpMsg{
			TemplateId: templateId,
			URL:        "",
			PagePath:   pagePath,
			Items:      data,
		},
	}

	log.PrintFields("SendUniformMsg params =", map[string]interface{}{"opt": opt})

	response, err := client.SendUniformMsg(ctx, opt)
	fmt.Println("===============opt=", opt)
	fmt.Println("===============response=", response)
	if err != nil || response == nil {
		log.ErrorFields("rpc mini SendUniformMsg error", map[string]interface{}{"err": err})
		return err
	}

	//marshal, err := json.Marshal(response)
	//if err != nil {
	//	log.ErrorFields("rpc mini SendUniformMsg error", map[string]interface{}{"err": err})
	//	return err
	//}

	if response.Code != "0" {
		log.ErrorFields("rpc mini SendUniformMsg error", map[string]interface{}{"response": response})
		return errors.New("SendUniformMsg response.code != '0'")
	}

	return nil

}

// GetUserAuthCorporationIds 获取用户授权的机构
func GetUserAuthCorporationIds(ctx context.Context, userId int64) []int64 {
	client := protoTree.NewTreeService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetUserCorpIds(ctx, &protoTree.GetUserCorpIdsRequest{
		UserId: userId,
	})

	if err != nil || response == nil {
		log.ErrorFields("rpc Tree error", map[string]interface{}{"err": err, "userId": userId})
		return nil
	}

	return response.CorpIds
}

func LoginForMobile(ctx context.Context, mobile string) *protoUser.EncodeAuthResponse {
	client := protoUser.NewUserService(srvPublicServiceName, config.Global.MicroClient)
	response, err := client.EncodeAuth(ctx, &protoUser.EncodeAuthRequest{
		WxSiteAppId:   "DingTalk",
		WxSiteCode:    mobile,
		ModuleType:    0x800,
		CorporationId: config.Config.TopCorporationId,
		RepeatLogin:   1, //支持重复登录
	})

	if err != nil {
		log.ErrorFields("rpc oet GetMainUserById error", map[string]interface{}{"err": err})
		return nil
	}

	return response
}
