package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	protoStation "app/org/scs/erpv2/api/proto/rpc/station"
	"app/org/scs/erpv2/api/util"
	"context"
	"fmt"
	"strconv"
	"strings"
)

// GetStationsWithOption 搜索站点
func GetStationsWithOption(ctx context.Context, topCorpId int64, name string) []*protoStation.OetStationItem {
	client := protoStation.NewOetStationService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetStationsWithOption(ctx, &protoStation.GetStationsWithOptionRequest{
		CorporationId: topCorpId,
		Name:          name,
	})

	if err != nil || response == nil {
		log.ErrorFields("rpc GetStationsWithOption error", map[string]interface{}{"err": err})
		return nil
	}

	return response.Items
}

func GetStationsWithOpt(ctx context.Context, opt *protoStation.GetStationsWithOptionRequest) ([]*protoStation.OetStationItem, int64) {
	client := protoStation.NewOetStationService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetStationsWithOption(ctx, opt)

	if err != nil || response == nil {
		log.ErrorFields("rpc GetStationsWithOption error", map[string]interface{}{"err": err})
		return nil, 0
	}

	return response.Items, response.TotalCount
}

func GetStationsWithOption2(ctx context.Context, opt *protoStation.GetStationsWithOption2Request) ([]*protoStation.OetStationItem, int64) {
	client := protoStation.NewOetStationService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetStationsWithOption2(ctx, opt)

	if err != nil || response == nil {
		log.ErrorFields("rpc GetStationsWithOption error", map[string]interface{}{"err": err})
		return nil, 0
	}

	return response.Items, response.TotalCount
}
func SearchStationsWithMoreCustomPoints(items []*protoStation.OetStationItem, points [][]string) ([]*protoStation.OetStationItem, int64) {
	if len(points) == 0 {
		return items, int64(len(items))
	}
	var (
		regions   = make([][]util.Point, 0)
		retItemss = make([]*protoStation.OetStationItem, 0)
	)
	for _, point := range points {
		var region = make([]util.Point, 0)
		for _, p := range point {
			ps := strings.Split(p, ",")
			if len(ps) >= 2 {
				lng, _ := strconv.ParseFloat(ps[0], 64)
				lat, _ := strconv.ParseFloat(ps[1], 64)
				region = append(region, util.Point{
					Lng: lng,
					Lat: lat,
				})
			}
		}
		regions = append(regions, region)
	}
	for _, region := range regions {
		if len(region) <= 2 {
			continue
		}
		fmt.Println(region)
		retItems := SearchStationsWithCustomRegion(items, region)
		retItemss = append(retItemss, retItems...)
	}

	// 去重
	res := make([]*protoStation.OetStationItem, 0)
	for _, s := range retItemss {
		isRepeat := false // 是否重复
		for _, s2 := range res {
			if s.Id == s2.Id {
				isRepeat = true
				break
			}
		}
		if isRepeat {
			continue
		} else {
			res = append(res, s)
		}
	}
	return res, int64(len(res))

}

// 计算站点是否在区域内
func SearchStationsWithCustomRegion(items []*protoStation.OetStationItem, region []util.Point) []*protoStation.OetStationItem {
	var (
		retItems = make([]*protoStation.OetStationItem, 0)
		retCh    = make(chan *protoStation.OetStationItem, 1)
		ch       = make(chan int64, 4)
	)
	for _, item := range items {
		go func(item *protoStation.OetStationItem, region []util.Point) {
			ch <- 1
			ok := util.CheckPointInPolygonV2(util.Point{Lng: item.Longitude, Lat: item.Latitude}, region)
			if ok {
				retCh <- item
			} else {
				retCh <- &protoStation.OetStationItem{}
			}
			<-ch
		}(item, region)
	}

	for range items {
		retItem := <-retCh
		if retItem != nil && retItem.Id > 0 {
			retItems = append(retItems, retItem)
		}
	}
	return retItems
}

func GetStationWithId(ctx context.Context, stationId int64) *protoStation.OetStationItem {
	client := protoStation.NewOetStationService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetStationWithId(ctx, &protoStation.GetStationWithIdRequest{
		StationId: stationId,
	})

	if err != nil || response == nil {
		log.ErrorFields("rpc GetStationsWithOption error", map[string]interface{}{"err": err})
		return nil
	}

	return response.Item
}
