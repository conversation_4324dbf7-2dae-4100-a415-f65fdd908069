package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	protoStaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"context"
	"errors"
	"fmt"
	"time"
)

func GetStaffNameWithId(ctx context.Context, staffId int64) string {
	if staffId == 0 {
		return ""
	}
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	staff, err := client.GetStaffWithId(ctx, &protoStaff.GetStaffWithIdRequest{
		Id: staffId,
	})

	if err != nil {
		log.ErrorFields("rpc GetStaffWithId error", map[string]interface{}{"err": err, "id": staffId})
		return ""
	}

	if staff == nil {
		log.ErrorFields("rpc GetStaffWithId is nil", map[string]interface{}{"id": staffId})
		return ""
	}
	return staff.Item.Name
}

// GetStaffWithId 根据ID获取员工信息
func GetStaffWithId(ctx context.Context, staffId int64) *protoStaff.OetStaffItem {
	if staffId == 0 {
		return nil
	}
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	staff, err := client.GetStaffWithId(ctx, &protoStaff.GetStaffWithIdRequest{
		Id: staffId,
	})

	if err != nil {
		log.ErrorFields("rpc GetStaffWithId error", map[string]interface{}{"err": err, "id": staffId})
		return nil
	}

	if staff == nil {
		log.ErrorFields("rpc GetStaffWithId is nil", map[string]interface{}{"id": staffId})
		return nil
	}
	return staff.Item
}

// GetStaffWithStaffId 根据工号获取员工
func GetStaffWithStaffId(ctx context.Context, topCorporationId int64, staffCode string) *protoStaff.OetStaffItem {
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	staff, err := client.GetStaffWithStaffId(ctx, &protoStaff.GetStaffWithStaffIdRequest{
		StaffId:          staffCode,
		TopCorporationId: topCorporationId,
	})

	if err != nil {
		log.ErrorFields("rpc GetStaffWithStaffId error", map[string]interface{}{"err": err, "code": staffCode})
		return nil
	}

	if staff == nil {
		log.ErrorFields("rpc GetStaffWithStaffId error", map[string]interface{}{"err": err, "code": staffCode})
		return nil
	}
	return staff.Item
}

// GetStaffWithIds 根据IDs获取员工
func GetStaffWithIds(ctx context.Context, staffIds []int64) []*protoStaff.OetStaffItem {
	if len(staffIds) == 0 {
		return nil
	}
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetStaffWithIds(ctx, &protoStaff.GetStaffWithIdsRequest{
		Ids: staffIds,
	})

	if err != nil {
		log.ErrorFields("rpc GetStaffWithIds error", map[string]interface{}{"err": err, "ids": staffIds})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc GetStaffWithIds is nil", map[string]interface{}{"ids": staffIds})
		return nil
	}
	return response.Items
}

// GetStaffWithPhone 根据手机号查找员工
func GetStaffWithPhone(ctx context.Context, topCorporationId int64, phone string) *protoStaff.OetStaffItem {
	if phone == "" {
		return nil
	}
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	staff, err := client.GetStaffWithPhone(ctx, &protoStaff.GetStaffWithPhoneRequest{
		TopCorporationId: topCorporationId,
		Phone:            phone,
	})

	if err != nil {
		log.ErrorFields("rpc GetStaffWithPhone error", map[string]interface{}{"err": err, "phone": phone, "topCorporationId": topCorporationId})
		return nil
	}

	if staff == nil {
		log.ErrorFields("rpc GetStaffWithPhone is nil", map[string]interface{}{"phone": phone, "topCorporationId": topCorporationId})
		return nil
	}

	return staff.Item
}

// GetStaffWithIdentifyId 根据身份证查找员工
func GetStaffWithIdentifyId(ctx context.Context, topCorporationId int64, identifyId string) *protoStaff.OetStaffItem {
	if identifyId == "" {
		return nil
	}
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	staff, err := client.GetStaffWithIdentifyId(ctx, &protoStaff.GetStaffWithIdentifyIdRequest{
		TopCorporationId: topCorporationId,
		IdentifyId:       identifyId,
	})

	if err != nil {
		log.ErrorFields("rpc GetStaffWithIdentifyId error", map[string]interface{}{"err": err, "identifyId": identifyId, "topCorporationId": topCorporationId})
		return nil
	}

	if staff == nil {
		log.ErrorFields("rpc GetStaffWithIdentifyId is nil", map[string]interface{}{"identifyId": identifyId, "topCorporationId": topCorporationId})
		return nil
	}

	return staff.Item
}

// GetStaffsWithOption 查询顶级机构下的员工
func GetStaffsWithOption(ctx context.Context, topCorporationId int64) []*protoStaff.OetStaffItem {
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetStaffsWithOptions(ctx, &protoStaff.GetStaffsWithOptionsRequest{
		TopCorporationId: topCorporationId,
		Sex:              3,
		Occupation:       999,
	})

	if err != nil {
		log.ErrorFields("rpc GetStaffsWithOptions error", map[string]interface{}{"err": err, "topCorporationId": topCorporationId})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc GetStaffsWithOptions is nil", map[string]interface{}{"topCorporationId": topCorporationId})
		return nil
	}

	return response.Items
}

// GetStaffsWithOpt 根据条件查找员工（单选条件）
func GetStaffsWithOpt(ctx context.Context, opt *protoStaff.GetStaffsWithOptionsRequest) *protoStaff.GetStaffsWithOptionsResponse {
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetStaffsWithOptions(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc GetStaffsWithOptions error", map[string]interface{}{"err": err, "opt": opt})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc GetStaffsWithOptions is nil", map[string]interface{}{"opt": opt})
		return nil
	}

	return response
}

// EditOetStaff 编辑员工信息
func EditOetStaff(ctx context.Context, userId int64, oetStaff *protoStaff.OetStaffItem) error {
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)

	response, err := client.Edit(ctx, &protoStaff.EditRequest{
		Item:    oetStaff,
		Version: 1,
		UserId:  userId,
	})
	log.ErrorFields("rpc EditOetStaff", map[string]interface{}{"err": err, "response": response, "item": oetStaff, "userId": userId})
	if err != nil {
		log.ErrorFields("rpc oet Edit error", map[string]interface{}{"err": err})
		return err
	}

	if response.Code != "0" {
		log.ErrorFields("rpc oet Edit fail", map[string]interface{}{"msg": response.Msg})
		return errors.New("edit oet staff info fail: " + response.Msg)
	}

	return nil
}

// CreateOetStaff 创建员工信息
func CreateOetStaff(ctx context.Context, oetStaff *protoStaff.OetStaffItem) (*protoStaff.OetStaffItem, error) {
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.BatchCreate(ctx, &protoStaff.BatchCreateRequest{
		Items: []*protoStaff.OetStaffItem{oetStaff},
	})
	if err != nil {
		log.ErrorFields("rpc oet create error", map[string]interface{}{"err": err})
		return nil, err
	}

	if response.Code != "0" {
		log.ErrorFields("rpc oet create fail", map[string]interface{}{"msg": response.Msg})
		return nil, errors.New("rpc oet create info fail: " + response.Msg)
	}
	fmt.Printf("CreateOetStaff response=================%+v", response)

	return response.Items[0], nil
}

// GetStaffsWithCorporationIds 根据多机构获取员工列表信息
func GetStaffsWithCorporationIds(ctx context.Context, corporationIds []int64, name string) []*protoStaff.OetStaffItem {
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetStaffsWithCorpIds(ctx, &protoStaff.GetStaffsWithCorpIdsRequest{
		CorporationIds: corporationIds,
		Occupation:     999,
		Sex:            3,
		Name:           name,
	})

	if err != nil {
		log.ErrorFields("rpc GetStaffsWithCorpIds error", map[string]interface{}{"err": err, "ids": corporationIds})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc GetStaffsWithCorpIds is nil", map[string]interface{}{"ids": corporationIds})
		return nil
	}

	return response.Items
}

// GetStaffsWithCorporationId 获取机构以及子机构下的所有员工
func GetStaffsWithCorporationId(ctx context.Context, corporationId int64, name string) []*protoStaff.OetStaffItem {
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetStaffsWithCorpId(ctx, &protoStaff.GetStaffsWithCorpIdRequest{
		CorporationId:   corporationId,
		Order:           "asc",
		Name:            name,
		Occupation:      999,
		Sex:             3,
		IsShowChildNode: true,
	})

	if err != nil {
		log.ErrorFields("rpc GetStaffsWithCorpId error", map[string]interface{}{"err": err, "id": corporationId})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc GetStaffsWithCorpId is nil", map[string]interface{}{"id": corporationId})
		return nil
	}

	return response.Items
}

// GetStaffsWithCorporationIdFalse 获取机构下的员工 不包含子机构
func GetStaffsWithCorporationIdFalse(ctx context.Context, corporationId int64) []*protoStaff.OetStaffItem {
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetStaffsWithCorpId(ctx, &protoStaff.GetStaffsWithCorpIdRequest{
		CorporationId:   corporationId,
		Order:           "asc",
		Occupation:      999,
		Sex:             3,
		IsShowChildNode: false,
	})

	if err != nil {
		log.ErrorFields("rpc GetStaffsWithCorpId error", map[string]interface{}{"err": err, "id": corporationId})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc GetStaffsWithCorpId is nil", map[string]interface{}{"id": corporationId})
		return nil
	}

	return response.Items
}

// GetStaffsWithMultiOptions 根据多选条件获取人员列表信息
func GetStaffsWithMultiOptions(ctx context.Context, where protoStaff.GetStaffsWithMultiOptionsRequest) ([]*protoStaff.OetStaffItem, int64) {
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetStaffsWithMultiOptions(ctx, &where)

	if err != nil {
		log.ErrorFields("rpc GetStaffsWithMultiOptions error", map[string]interface{}{"err": err, "where": where})
		return nil, 0
	}

	if response == nil {
		log.ErrorFields("rpc GetStaffsWithMultiOptions is nil", map[string]interface{}{"where": where})
		return nil, 0
	}

	return response.Items, response.TotalCount
}

// GetHistoricalStaffsWithUserId 获取当前用户授权机构下的所有人员 包含历史
func GetHistoricalStaffsWithUserId(ctx context.Context, userId int64, startAt, endAt time.Time) []*protoStaff.HistoryStaffItem {
	client := protoStaff.NewOetStaffService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetHistoricalStaffsWithUserId(ctx, &protoStaff.GetHistoricalStaffsWithUserIdRequest{
		UserId:         userId,
		QueryDateStart: startAt.Unix(),
		QueryDateEnd:   endAt.Unix(),
	})

	if err != nil {
		log.ErrorFields("rpc GetHistoricalStaffsWithUserId 1 error", map[string]interface{}{"err": err, "userId": userId, "start": startAt, "end": endAt})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc GetHistoricalStaffsWithUserId 2 error", map[string]interface{}{"err": err, "userId": userId, "start": startAt, "end": endAt})

		return nil
	}

	return response.Items
}
