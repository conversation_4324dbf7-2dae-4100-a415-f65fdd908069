package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	protoVehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	protoVehicleModel "app/org/scs/erpv2/api/proto/rpc/oetvehiclemodel"
	"context"
	"errors"
	"time"
)

// GetVehiclesWithOption 根据条件筛选车辆列表
func GetVehiclesWithOption(ctx context.Context, opt *protoVehicle.GetVehiclesWithOptionRequest) ([]*protoVehicle.OetVehicleItem, int64) {
	client := protoVehicle.NewOetVehicleService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetVehiclesWithOption(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc oet GetVehiclesWithOption error", map[string]interface{}{"err": err})
		return nil, 0
	}

	if response == nil {
		log.ErrorFields("rpc oet GetVehiclesWithOption fail", map[string]interface{}{"opt": opt})
		return nil, 0
	}

	return response.Items, response.TotalCount
}

// GetVehicleWithLicense 根据车牌号获取车辆信息
func GetVehicleWithLicense(ctx context.Context, opt *protoVehicle.GetVehicleWithLicenseRequest) *protoVehicle.OetVehicleItem {
	client := protoVehicle.NewOetVehicleService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetVehicleWithLicense(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc oet GetVehicleWithLicense error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc oet GetVehicleWithLicense fail", map[string]interface{}{"opt": opt})
		return nil
	}

	return response.Item
}

// GetVehicleWithId 根据ID查询车辆
func GetVehicleWithId(ctx context.Context, vehicleId int64) *protoVehicle.OetVehicleItem {
	client := protoVehicle.NewOetVehicleService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetVehicleWithId(ctx, &protoVehicle.GetVehicleWithIdRequest{
		VehicleId: vehicleId,
	})

	if err != nil {
		log.ErrorFields("rpc oet GetVehicleWithId error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc oet GetVehicleWithId fail", map[string]interface{}{"vehicleId": vehicleId})
		return nil
	}

	return response.Item
}

// GetVehiclesWithOptionCorpIds 获取多机构下的车辆
func GetVehiclesWithOptionCorpIds(ctx context.Context, opt *protoVehicle.GetVehiclesWithOptionCorpIdsRequest) ([]*protoVehicle.OetVehicleItem, int64) {
	client := protoVehicle.NewOetVehicleService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetVehiclesWithOptionCorpIds(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc oet GetVehiclesWithOptionCorpIds error", map[string]interface{}{"err": err})
		return nil, 0
	}

	if response == nil {
		log.ErrorFields("rpc oet GetVehiclesWithOptionCorpIds fail", map[string]interface{}{"opt": opt})
		return nil, 0
	}

	return response.Items, response.TotalCount
}

// GetVehiclesWithTopCorporationId 获取顶级机构下的所有车辆
func GetVehiclesWithTopCorporationId(ctx context.Context, topCorpId int64) ([]*protoVehicle.OetVehicleItem, int64) {
	client := protoVehicle.NewOetVehicleService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetVehiclesWithTopCorporationId(ctx, &protoVehicle.GetVehiclesWithTopCorporationIdRequest{
		TopCorporationId: topCorpId,
	})

	if err != nil {
		log.ErrorFields("rpc oet GetVehiclesWithOption error", map[string]interface{}{"err": err})
		return nil, 0
	}

	if response == nil {
		log.ErrorFields("rpc oet GetVehiclesWithOption fail", map[string]interface{}{"topCorpId": topCorpId})
		return nil, 0
	}

	return response.Items, int64(len(response.Items))
}

// GetVehiclesWithLicenseOption 根机构下车牌号模糊搜索车辆
func GetVehiclesWithLicenseOption(ctx context.Context, opt *protoVehicle.GetVehiclesWithLicenseOptionRequest) ([]*protoVehicle.OetVehicleItem, int64) {
	client := protoVehicle.NewOetVehicleService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetVehiclesWithLicenseOption(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc oet GetVehiclesWithLicenseOption error", map[string]interface{}{"err": err})
		return nil, 0
	}

	if response == nil {
		log.ErrorFields("rpc oet GetVehiclesWithLicenseOption fail", map[string]interface{}{"opt": opt})
		return nil, 0
	}

	return response.Items, int64(len(response.Items))
}

func GetVehicleWithCode(ctx context.Context, topCorpId int64, code string) *protoVehicle.OetVehicleItem {
	client := protoVehicle.NewOetVehicleService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetVehicleWithCode(ctx, &protoVehicle.GetVehicleWithCodeRequest{
		CorporationId: topCorpId,
		Code:          code,
	})

	if err != nil || response == nil {
		log.ErrorFields("rpc GetVehicleWithCode error", map[string]interface{}{"err": err})
		return nil
	}

	return response.Item
}

func MoveVehicle(ctx context.Context, src protoVehicle.SrcVehicle, dst protoVehicle.DstVehicle, userId int64, isAdmin bool) error {
	client := protoVehicle.NewOetVehicleService(srvAppServiceName, config.Global.MicroClient)
	srcVehicles := []*protoVehicle.SrcVehicle{&src}
	response, err := client.Move(ctx, &protoVehicle.MoveRequest{
		SrcVehicles: srcVehicles,
		DstVehicle:  &dst,
		UserId:      userId,
		IsAdmin:     isAdmin,
	})

	if err != nil || response == nil {
		log.ErrorFields("rpc MoveVehicle error", map[string]interface{}{"err": err})
		return err
	}

	if response.Code != "0" {
		return errors.New(response.Msg)
	}

	return nil
}

// GetVehicleIdsByLineIdsAndCorporationIds 搜索多机构、线路下的所有车辆  合并条件
func GetVehicleIdsByLineIdsAndCorporationIds(ctx context.Context, topCorpId int64, lineIds, corporationIds []int64) []int64 {
	if len(lineIds) == 0 && len(corporationIds) == 0 {
		return nil
	}

	vehicleItems, _ := GetVehiclesWithTopCorporationId(ctx, topCorpId)

	var lineVehicleItems = make([]*protoVehicle.OetVehicleItem, 0)
	if len(lineIds) > 0 {
		for i := 0; i < len(vehicleItems); i++ {
			for _, lineId := range lineIds {
				if vehicleItems[i].LineId == lineId {
					lineVehicleItems = append(lineVehicleItems, vehicleItems[i])
					break
				}
			}
		}
	} else {
		lineVehicleItems = vehicleItems
	}

	var corpVehicleItems = make([]*protoVehicle.OetVehicleItem, 0)
	if len(corporationIds) > 0 {
		for i := 0; i < len(lineVehicleItems); i++ {
			for _, corpId := range corporationIds {
				if lineVehicleItems[i].SonCorporationId == corpId {
					corpVehicleItems = append(corpVehicleItems, lineVehicleItems[i])
					break
				}
			}
		}
	} else {
		corpVehicleItems = lineVehicleItems
	}

	var vehicleIds []int64

	for _, value := range corpVehicleItems {
		vehicleIds = append(vehicleIds, value.Id)
	}
	return vehicleIds

}

// GetVehicleModelList 车辆型号列表
func GetVehicleModelList(ctx context.Context, topCorporationId int64) []*protoVehicleModel.OetVehicleModelItem {
	client := protoVehicleModel.NewOetVehicleModelService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetVehicleModelsWithOption(ctx, &protoVehicleModel.GetVehicleModelsWithOptionRequest{
		TopCorporationId: topCorporationId,
	})

	if err != nil {
		log.ErrorFields("rpc oet GetVehicleModelsWithOption error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc oet GetVehicleModelsWithOption fail", map[string]interface{}{"topCorporationId": topCorporationId})
		return nil
	}

	return response.Items
}

// GetVehicleTransferRecordList 车辆调动记录
func GetVehicleTransferRecordList(ctx context.Context, vehicleId int64, startAt, endAt time.Time) ([]*protoVehicle.OetVehicleRecordItem, int64) {
	client := protoVehicle.NewOetVehicleService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetVehicleRecords(ctx, &protoVehicle.GetVehicleRecordsRequest{
		VehicleId: vehicleId,
		StartAt:   startAt.Unix(),
		EndAt:     endAt.Unix(),
		Limit:     0,
		Offset:    0,
		Order:     "DESC",
	})

	if err != nil {
		log.ErrorFields("rpc oet GetVehicleRecords error", map[string]interface{}{"err": err})
		return nil, 0
	}

	if response == nil {
		log.ErrorFields("rpc oet GetVehicleRecords fail", map[string]interface{}{"vehicleId": vehicleId})
		return nil, 0
	}

	return response.Items, response.TotalCount
}

// GetVehicleDriverByVehicleId 查询车辆绑定的司机
func GetVehicleDriverByVehicleId(ctx context.Context, vehicleId int64) []*protoVehicle.VehicleDriverItem {
	client := protoVehicle.NewVehicleDriverService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetVehicleDriverByVehicleId(ctx, &protoVehicle.GetVehicleDriverByVehicleIdReq{
		VehicleId: vehicleId,
	})

	if err != nil {
		log.ErrorFields("rpc oet GetVehicleDriverByVehicleId error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc oet GetVehicleDriverByVehicleId fail", map[string]interface{}{"vehicleId": vehicleId})
		return nil
	}

	return response.Items
}

func GetHistoricalVehiclesWithUserId(ctx context.Context, userId int64, startAt, endAt time.Time) []*protoVehicle.HistoryVehicleItem {
	client := protoVehicle.NewOetVehicleService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetHistoricalVehiclesWithUserId(ctx, &protoVehicle.GetHistoricalVehiclesWithUserIdRequest{
		UserId:         userId,
		QueryDateStart: startAt.Unix(),
		QueryDateEnd:   endAt.Unix(),
	})

	if err != nil {
		log.ErrorFields("rpc oet GetHistoricalVehiclesWithUserId error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc oet GetHistoricalVehiclesWithUserId fail", map[string]interface{}{"userId": userId, "start": startAt, "end": endAt})
		return nil
	}

	return response.Items
}
