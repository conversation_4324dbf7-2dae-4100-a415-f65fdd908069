package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	protoLineStation "app/org/scs/erpv2/api/proto/rpc/oetlinestation"
	"app/org/scs/erpv2/api/util"
	"context"
)

func GetLineStationInfosWithLineIds(ctx context.Context, lineIds []int64) []*protoLineStation.LineStationInfo {
	client := protoLineStation.NewOetLineStationService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetLineStationInfosWithLineIds(ctx, &protoLineStation.GetLineStationInfosWithLineIdsRequest{
		LineIds:    lineIds,
		FilterType: 1,
	})

	if err != nil {
		log.ErrorFields("rpc oet GetLineStationInfosWithLineIds error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc oet GetLineStationInfosWithLineIds fail", map[string]interface{}{"LineIds": lineIds})
		return nil
	}

	return response.Items
}

func GetLineStationInfosWithTopCorpId(ctx context.Context, topCorporationId int64) []*protoLineStation.LineStationInfo {
	client := protoLineStation.NewOetLineStationService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetLineStationInfosWithTopCorpId(ctx, &protoLineStation.GetLineStationInfosWithTopCorpIdRequest{
		TopCorpId:  topCorporationId,
		FilterType: 1,
	})

	if err != nil {
		log.ErrorFields("rpc oet GetLineStationInfosWithTopCorpId error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc oet GetLineStationInfosWithTopCorpId fail", map[string]interface{}{"topCorporationId": topCorporationId})
		return nil
	}

	return response.Items
}

func GetOetLineStationWithLineId(ctx context.Context, opt *protoLineStation.GetOetLineStationWithLineIdRequest) *protoLineStation.GetOetLineStationWithLineIdResponse {
	client := protoLineStation.NewOetLineStationService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetOetLineStationWithLineId(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc oet GetOetLineStationWithLineId error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc oet GetOetLineStationWithLineId fail", nil)
		return nil
	}
	log.ErrorFields("rpc oet GetOetLineStationWithLineId ", map[string]interface{}{"len": len(response.Items)})
	log.ErrorFields("rpc oet GetOetLineStationWithLineId ", map[string]interface{}{"len": len(response.SkipItems)})
	return response
}

func GetFirstStation(items []*protoLineStation.OetLineStationItem) string {
	for i := 0; i < len(items); i++ {
		if items[i].FirstLast == util.Station_Queue_First {
			return items[i].StationName
		}
	}
	return ""
}

func GetLastStation(items []*protoLineStation.OetLineStationItem) string {
	for i := len(items) - 1; i >= 0; i-- {
		if items[i].FirstLast == util.Station_Queue_Last {
			return items[i].StationName
		}
	}
	return ""
}

func GetLineStationInfoWithLineId(ctx context.Context, opt *protoLineStation.GetLineStationInfoWithLineIdRequest) *protoLineStation.GetLineStationInfoWithLineIdResponse {
	client := protoLineStation.NewOetLineStationService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetLineStationInfoWithLineId(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc oet GetOetLineStationWithLineId error", map[string]interface{}{"err": err})
		return nil
	}
	return response
}
