package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	protoDoorCheck "app/org/scs/erpv2/api/proto/rpc/doorcheck"
	"context"
)

func GetDoorCheckResults(ctx context.Context, option protoDoorCheck.GetResultsRequest) *protoDoorCheck.GetResultsResponse {
	client := protoDoorCheck.NewDoorcheckService(srvIpocServiceName, config.Global.MicroClient)
	results, err := client.GetResults(ctx, &option)

	if err != nil {
		log.ErrorFields("rpc DoorCheck GetResults error", map[string]interface{}{"err": err, "option": option})
		return nil
	}

	return results
}

func GetDoorCheckResultDetail(ctx context.Context, option protoDoorCheck.GetResultDetailsRequest) *protoDoorCheck.GetResultDetailsResponse {
	client := protoDoorCheck.NewDoorcheckService(srvIpocServiceName, config.Global.MicroClient)
	result, err := client.GetResultDetails(ctx, &option)

	if err != nil {
		log.ErrorFields("rpc DoorCheck GetResultDetails error", map[string]interface{}{"err": err, "option": option})
		return nil
	}

	return result
}
