package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	oet_scs_api_device "app/org/scs/erpv2/api/proto/rpc/subscribe"
	"context"
	"time"
)

func GetLinePfWithLineIds(ctx context.Context, lineId int64, start, end time.Time) []*oet_scs_api_device.LinePfItem {
	if lineId == 0 {
		return nil
	}
	client := oet_scs_api_device.NewRpcApiApcService(srvScsApiServiceName, config.Global.MicroClient)
	linePf, err := client.GetLinePfWithLineId(ctx, &oet_scs_api_device.GetLinePfWithLineIdRequest{
		LineId:      lineId,
		StartAtUnix: start.Unix(),
		EndAtUnix:   end.Unix(),
	})
	if err != nil {
		log.ErrorFields("rpc GetLinePfWithLineIds error", map[string]interface{}{"err": err, "lineId": lineId})
		return nil
	}
	if linePf == nil {
		log.ErrorFields("rpc GetLinePfWithLineIds is nil", map[string]interface{}{"lineId": lineId})
		return nil
	}
	return linePf.Items
}
