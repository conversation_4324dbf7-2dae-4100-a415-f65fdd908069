package rpc

import (
	"app/org/scs/erpv2/api/config"
	oet_scs_api_iss "app/org/scs/erpv2/api/proto/rpc/line_road"
	"context"
)

func GetVehicleLineRoadFormList(ctx context.Context, req *oet_scs_api_iss.GetVehicleLineRoadFormsRequest) (list []*oet_scs_api_iss.LineRoadFormItem, err error) {
	service := oet_scs_api_iss.NewLineroadformService(srvIssServiceName, config.Global.MicroClient)
	resp, err := service.GetVehicleLineRoadForms(ctx, req)
	if resp == nil {
		return nil, err
	}
	return resp.Items, err
}
