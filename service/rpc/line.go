package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	protoLine "app/org/scs/erpv2/api/proto/rpc/oetline"
	"context"
	"time"
)

// GetLineWithId 22年7月12： 获取线路所属(子)机构(车队) 应使用第二个返回参数
func GetLineWithId(ctx context.Context, lineId int64) (*protoLine.OetLineItem, []int64) {
	if lineId == 0 {
		return nil, nil
	}
	client := protoLine.NewOetLineService(srvAppServiceName, config.Global.MicroClient)
	line, err := client.GetLineWithId(ctx, &protoLine.GetLineWithIdRequest{
		LineId:          lineId,
		IsLineAttribute: 1,
	})

	if err != nil {
		log.ErrorFields("rpc GetLineWithId error", map[string]interface{}{"err": err, "lineId": lineId})
		return nil, nil
	}

	if line == nil {
		log.ErrorFields("rpc GetLineWithId is nil", map[string]interface{}{"lineId": lineId})
		return nil, nil
	}

	return line.Item, line.SubCorporationIds
}

func GetLineWithCode(ctx context.Context, topCorporationId int64, code string) (*protoLine.OetLineItem, []int64) {
	client := protoLine.NewOetLineService(srvAppServiceName, config.Global.MicroClient)
	line, err := client.GetLineWithCode(ctx, &protoLine.GetLineWithCodeRequest{
		TopCorporationId: topCorporationId,
		Code:             code,
		IsLineAttribute:  1,
	})

	if err != nil {
		log.ErrorFields("rpc GetLineWithCode error", map[string]interface{}{"err": err, "code": code})
		return nil, nil
	}

	if line == nil {
		log.ErrorFields("rpc GetLineWithCode is nil", map[string]interface{}{"code": code})
		return nil, nil
	}

	return line.Item, line.SubCorporationIds
}

func GetLineWithName(ctx context.Context, topCorporationId int64, name string) *protoLine.OetLineItem {
	client := protoLine.NewOetLineService(srvAppServiceName, config.Global.MicroClient)
	line, err := client.GetLineWithName(ctx, &protoLine.GetLineWithNameRequest{
		TopCorporationId: topCorporationId,
		Name:             name,
		IsLineAttribute:  1,
	})

	if err != nil {
		log.ErrorFields("rpc GetLineWithCode error", map[string]interface{}{"err": err, "name": name})
		return nil
	}

	if line == nil {
		log.ErrorFields("rpc GetLineWithCode is nil", map[string]interface{}{"name": name})
		return nil
	}

	return line.Item
}
func GetLinesWithCorporationId(ctx context.Context, corporationId int64) []*protoLine.OetLineItem {
	client := protoLine.NewOetLineService(srvAppServiceName, config.Global.MicroClient)
	line, err := client.GetLinesWithCorporationId(ctx, &protoLine.GetLinesWithCorporationIdRequest{
		CorporationId:   corporationId,
		IsShowChildNode: true,
		IsLineAttribute: 1,
		Offset:          0,
		Limit:           99999,
	})

	if err != nil {
		log.ErrorFields("rpc GetLinesWithCorporationId error", map[string]interface{}{"err": err, "corporationId": corporationId})
		return nil
	}

	if line == nil {
		log.ErrorFields("rpc GetLinesWithCorporationId is nil", map[string]interface{}{"corporationId": corporationId})
		return nil
	}

	return line.Items
}

// GetLinesWithUserId 查询用户有权限看到的线路列表
func GetLinesWithUserId(ctx context.Context, opt *protoLine.GetLinesWithUserIdRequest) ([]*protoLine.OetLineItem, int64) {
	client := protoLine.NewOetLineService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetLinesWithUserId(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc oet GetLinesWithUserId error", map[string]interface{}{"err": err})
		return nil, 0
	}

	if response == nil {
		log.ErrorFields("rpc oet GetLinesWithUserId fail", map[string]interface{}{"opt": opt})
		return nil, 0
	}

	return response.Items, response.TotalCount
}

func GetLinesWithTopCorporationId(ctx context.Context, topCorporationId, isLineAttribute int64) []*protoLine.OetLineItem {
	client := protoLine.NewOetLineService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetLinesWithTopCorporationId(ctx, &protoLine.GetLinesWithTopCorporationIdRequest{
		TopCorporationId: topCorporationId,
		IsLineAttribute:  isLineAttribute,
	})

	if err != nil {
		log.ErrorFields("rpc oet GetLinesWithTopCorporationId error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc oet GetLinesWithTopCorporationId fail", map[string]interface{}{"topCorpId": topCorporationId})
		return nil
	}

	return response.Items
}

// GetHistoricalLinesWithUserId 获取当前账号授权机构下的所有线路（包括历史存在过的线）
func GetHistoricalLinesWithUserId(ctx context.Context, userId int64, name string, start, end time.Time) []*protoLine.HistoryLineItem {
	client := protoLine.NewOetLineService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetHistoricalLinesWithUserId(ctx, &protoLine.GetHistoricalLinesWithUserIdRequest{
		UserId:          userId,
		Name:            name,
		IsLineAttribute: 1,
		QueryDateStart:  start.Unix(),
		QueryDateEnd:    end.Unix(),
	})

	if err != nil {
		log.ErrorFields("rpc oet GetHistoricalLinesWithUserId error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc oet GetHistoricalLinesWithUserId fail", map[string]interface{}{"userId": userId, "name": name, "start": start, "end": end})
		return nil
	}

	return response.Items
}

// GetHistoricalCorpsWithLineId 获取线路所属的机构（包含历史）
func GetHistoricalCorpsWithLineId(ctx context.Context, lineIds []int64, start, end time.Time) []*protoLine.CorporationItem {
	client := protoLine.NewOetLineService(srvAppServiceName, config.Global.MicroClient)
	response, err := client.GetHistoricalCorpsWithLineId(ctx, &protoLine.GetHistoricalCorpsWithLineIdRequest{
		LineIds:        lineIds,
		QueryDateStart: start.Unix(),
		QueryDateEnd:   end.Unix(),
	})

	if err != nil {
		log.ErrorFields("rpc oet GetHistoricalCorpsWithLineId error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc oet GetHistoricalCorpsWithLineId fail", map[string]interface{}{"lineIds": lineIds, "start": start, "end": end})
		return nil
	}

	return response.Items
}
