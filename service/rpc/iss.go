package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	protoIss "app/org/scs/erpv2/api/proto/rpc/iss"
	"context"
	"errors"
)

// GetFirstLastPlanScheduleWithVehicleId 查询调度系统车辆的首末班排班信息
func GetFirstLastPlanScheduleWithVehicleId(ctx context.Context, opt *protoIss.GetFirstLastPlanScheduleWithVehicleIdRequest) []*protoIss.FirstLastPlanScheduleWithVehicleIdItem {
	client := protoIss.NewScheduleService(srvIssServiceName, config.Global.MicroClient)
	response, err := client.GetFirstLastPlanScheduleWithVehicleId(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc iss GetFirstLastPlanScheduleWithVehicleId error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc iss GetFirstLastPlanScheduleWithVehicleId fail", map[string]interface{}{"opt": opt})
		return nil
	}

	return response.Items
}

// DriverRunFormSum 司机运营汇总
func DriverRunFormSum(ctx context.Context, driverId int64, s, e int64) *protoIss.DriverRunFormSumResponse {
	client := protoIss.NewDataformService(srvIssServiceName, config.Global.MicroClient)

	opt := &protoIss.DriverRunFormSumRequest{
		DriverId: driverId,
		StartAt:  s,
		EndAt:    e,
	}

	response, err := client.DriverRunFormSum(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc iss DriverRunFormSum error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc iss DriverRunFormSum fail", map[string]interface{}{"opt": opt})
		return nil
	}

	return response
}

// VehicleRunFormSum 车辆运营汇总
func VehicleRunFormSum(ctx context.Context, vehicleId int64, s, e int64) *protoIss.VehicleRunFormSumResponse {
	client := protoIss.NewDataformService(srvIssServiceName, config.Global.MicroClient)

	opt := &protoIss.VehicleRunFormSumRequest{
		VehicleId: vehicleId,
		StartAt:   s,
		EndAt:     e,
	}

	response, err := client.VehicleRunFormSum(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc iss VehicleRunFormSum error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc iss VehicleRunFormSum fail", map[string]interface{}{"opt": opt})
		return nil
	}

	return response
}

func LineRunFormDaySum(ctx context.Context, corporationId, lineId int64, s, e int64) []*protoIss.LineRunFormDaySumItem {
	client := protoIss.NewDataformService(srvIssServiceName, config.Global.MicroClient)

	opt := &protoIss.LineRunFormDaySumRequest{
		CorporationId: corporationId,
		LineId:        lineId,
		StartAt:       s,
		EndAt:         e,
	}

	response, err := client.LineRunFormDaySum(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc iss LineRunFormDaySum error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc iss LineRunFormDaySum fail", map[string]interface{}{"opt": opt})
		return nil
	}

	if response.Code != "0" {
		log.ErrorFields("rpc iss LineRunFormDaySum fail", map[string]interface{}{"response": response})
		return response.Items
	}

	return response.Items
}

func GetLineCardCount(ctx context.Context, items []*protoIss.LineCorpItem) []*protoIss.LineCardItem {
	client := protoIss.NewDataformService(srvIssServiceName, config.Global.MicroClient)

	opt := &protoIss.LineCardRequest{
		Items: items,
	}

	response, err := client.LineCard(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc iss LineCard error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc iss LineCard fail", map[string]interface{}{"opt": opt})
		return nil
	}

	if response.Code != "0" {
		log.ErrorFields("rpc iss LineCard fail", map[string]interface{}{"response": response})
		return response.Items
	}

	return response.Items
}

func GetLineTaskWithLineId(ctx context.Context, corporationId, lineId int64) []*protoIss.LineTaskItem {
	client := protoIss.NewLinetaskService(srvIssServiceName, config.Global.MicroClient)

	opt := &protoIss.GetLineTaskWithLineIdRequest{
		CorporationId: corporationId,
		LineId:        lineId,
	}

	response, err := client.GetLineTaskWithLineId(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc iss GetLineTaskWithLineId error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc iss GetLineTaskWithLineId fail", map[string]interface{}{"opt": opt})
		return nil
	}

	if response.Code != "0" {
		log.ErrorFields("rpc iss GetLineTaskWithLineId fail", map[string]interface{}{"response": response})
		return response.Items
	}

	return response.Items
}

func GetLineTaskDetail(ctx context.Context, lineTaskId int64) []*protoIss.LineTaskDetailItem {
	client := protoIss.NewLinetaskdetailService(srvIssServiceName, config.Global.MicroClient)

	opt := &protoIss.GetLineTaskDetailsWithLineTaskIdRequest{
		LineTaskId: lineTaskId,
	}

	response, err := client.GetLineTaskDetailsWithLineTaskId(ctx, opt)

	if err != nil {
		log.ErrorFields("rpc iss GetLineTaskDetailsWithLineTaskId error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc iss GetLineTaskDetailsWithLineTaskId fail", map[string]interface{}{"opt": opt})
		return nil
	}

	if response.Code != "0" {
		log.ErrorFields("rpc iss GetLineTaskDetailsWithLineTaskId fail", map[string]interface{}{"response": response})
		return response.Items
	}

	return response.Items
}

func GetAssessLineStationByLineId(ctx context.Context, req *protoIss.GetAssessLineStationRequest) map[int64][]*protoIss.AssessLineStationItem {
	client := protoIss.NewAssesslinestationsService(srvIssServiceName, config.Global.MicroClient)

	response, err := client.GetAssessLineStation(ctx, req)

	if err != nil {
		log.ErrorFields("rpc iss GetAssessLineStationByLineId error", map[string]interface{}{"err": err})
		return nil
	}

	if response == nil {
		log.ErrorFields("rpc iss GetAssessLineStationByLineId fail", map[string]interface{}{"req": req})
		return nil
	}

	var itemMap = make(map[int64][]*protoIss.AssessLineStationItem, 0)
	for _, item := range response.Items {
		if value, ok := itemMap[item.Sheet]; ok {
			itemMap[item.Sheet] = append(value, item)
		} else {
			itemMap[item.Sheet] = []*protoIss.AssessLineStationItem{item}
		}

	}
	return itemMap

}

const (
	AssessLineStation_FirstLast_First = 1 //  1 首次
	AssessLineStation_FirstLast_Last  = 2 // 2 末次
)

// 0 中途  1 首次  2 末次
func GetAssessLineStationByOpt(sheet, stationId, seq, firstLast int64, itemMap map[int64][]*protoIss.AssessLineStationItem) *protoIss.AssessLineStationItem {
	if stationId == 0 {
		return nil
	}

	if items, ok := itemMap[sheet]; !ok {
		return nil
	} else {
		for _, item := range items {
			if item.StationId == stationId && item.StationSeq == seq && item.FirstLast == firstLast {
				return item
			}
		}
	}
	return nil

}

func VehicleMigrationStatusChange(vehicleMigrationId int64) error {
	client := protoIss.NewNotifyService(srvIssServiceName, config.Global.MicroClient)

	response, err := client.VehicleMigrationStatusChange(context.TODO(), &protoIss.VehicleMigrationStatusChangeRequest{
		WorkPostId: vehicleMigrationId,
	})

	if err != nil {
		log.ErrorFields("rpc iss VehicleMigrationStatusChange error", map[string]interface{}{"err": err})
		return err
	}

	if response == nil {
		log.ErrorFields("rpc iss VehicleMigrationStatusChange fail", map[string]interface{}{"id": vehicleMigrationId})
		return err
	}

	if response.Code != "0" {
		log.ErrorFields("rpc iss VehicleMigrationStatusChange fail", map[string]interface{}{"response": response})
		return errors.New("rpc iss VehicleMigrationStatusChange fail")
	}

	return nil
}

func DriverMigrationStatusChange(driverMigrationId int64) error {
	client := protoIss.NewNotifyService(srvIssServiceName, config.Global.MicroClient)

	response, err := client.DriverMigrationStatusChange(context.TODO(), &protoIss.DriverMigrationStatusChangeRequest{
		WorkPostId: driverMigrationId,
	})

	if err != nil {
		log.ErrorFields("rpc iss DriverMigrationStatusChange error", map[string]interface{}{"err": err})
		return err
	}

	if response == nil {
		log.ErrorFields("rpc iss DriverMigrationStatusChange fail", map[string]interface{}{"id": driverMigrationId})
		return err
	}

	if response.Code != "0" {
		log.ErrorFields("rpc iss DriverMigrationStatusChange fail", map[string]interface{}{"response": response})
		return errors.New("rpc iss DriverMigrationStatusChange fail")
	}

	return nil
}
