package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	protoMiniStation "app/org/scs/erpv2/api/proto/rpc/mini_station"
	"context"
)

func GetStationTimeSlotVotes(ctx context.Context, opt *protoMiniStation.TimeSlotVoteRequest) ([]*protoMiniStation.TimeSlotVoteItem, error) {
	client := protoMiniStation.NewStationService(srvMiniServiceName, config.Global.MicroClient)
	response, err := client.TimeSlotVote(ctx, opt)

	if err != nil || response == nil {
		log.ErrorFields("rpc GetStationTimeSlotVote error", map[string]interface{}{"err": err})
		return nil, err
	}

	return response.Items, nil
}
