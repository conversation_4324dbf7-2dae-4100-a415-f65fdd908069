package rpc

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	protoDwh "app/org/scs/erpv2/api/proto/rpc/dwh"
	"context"
	"time"
)

func GetContractOrderWithPhone(ctx context.Context, topCorporationId int64, phone string, orderIds []string, startAt, endAt time.Time) []*protoDwh.OrderItem {
	client := protoDwh.NewDwhRpcService(srvDwhServiceName, config.Global.MicroClient)
	response, err := client.GetContractOrderWithPhone(ctx, &protoDwh.GetContractOrderWithPhoneRequest{
		TopCorporationId: topCorporationId,
		Phone:            phone,
		StartAt:          startAt.Unix(),
		EndAt:            endAt.Unix(),
		OrderIds:         orderIds,
	})

	if err != nil {
		log.ErrorFields("rpc dwh GetContractOrderWithPhone error", map[string]interface{}{"err": err})
		return nil
	}
	if response == nil {
		log.ErrorFields("rpc dwh GetContractOrderWithPhone fail", map[string]interface{}{"topCorporationId": topCorporationId, "phone": phone, "startAt": startAt, "endAt": endAt})
		return nil
	}

	return response.Items
}

func GetContractRoads(ctx context.Context, topCorporationId int64, phone, orderId string, startAt, endAt time.Time) []*protoDwh.ContractRoadItem {
	client := protoDwh.NewDwhRpcService(srvDwhServiceName, config.Global.MicroClient)
	response, err := client.GetContractRoads(ctx, &protoDwh.GetContractRoadsRequest{
		TopCorporationId: topCorporationId,
		Phone:            phone,
		OrderId:          orderId,
		StartAt:          startAt.Unix(),
		EndAt:            endAt.Unix(),
	})

	if err != nil {
		log.ErrorFields("rpc dwh GetContractRoads error", map[string]interface{}{"err": err})
		return nil
	}
	if response == nil {
		log.ErrorFields("rpc dwh GetContractRoads fail", map[string]interface{}{"topCorporationId": topCorporationId, "phone": phone, "orderId": orderId, "startAt": startAt, "endAt": endAt})
		return nil
	}

	return response.Items
}
