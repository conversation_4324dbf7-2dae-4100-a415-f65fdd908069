package service

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	protoStaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

func ErpStaffFieldToOetStaffField(archive hrModel.StaffArchive, oetStaff *protoStaff.OetStaffItem) {
	oetStaff.Id = archive.StaffId
	oetStaff.TopCorporationId = archive.GroupId
	oetStaff.CorporationId = archive.CorporationId
	//oetStaff.LineId = archive.LineId
	//oetStaff.AttendanceCard = archive.AttendanceCard

	if oetStaff.Code == "" {
		//用身份证后7位
		code := []byte(archive.IdentityId)[11:]
		if strings.HasPrefix(string(code), "0") {
			code = []byte(archive.IdentityId)[10:]
		}
		codeStr := strings.ToLower(string(code))
		oetStaff.Code = strings.ReplaceAll(codeStr, "x", "10")
	}

	oetStaff.Name = archive.Name
	oetStaff.StaffId = archive.JobNumber

	oetStaff.Sex = archive.Gender == util.Male
	oetStaff.NativePlace = archive.NativePlace
	oetStaff.IdentifyId = archive.IdentityId
	oetStaff.Phone = archive.Contact
	oetStaff.DrvAddress = archive.Address
	if archive.JoinAt != nil {
		oetStaff.RegisterTime = time.Time(*archive.JoinAt).Unix()
	}

	oetStaff.DrvLicense = archive.DrivingCode

	oetStaff.DrvLicenseTypeStr = archive.DrivingModel

	oetStaff.WorkingState = archive.JobStatus

	oetStaff.Occupation = util.ErpToMaster[archive.WorkPostType]

	//出生日期
	if archive.IdentityId != "" {
		date := []byte(archive.IdentityId)[6:14]
		birthDate, _ := time.ParseInLocation("20060102", string(date), time.Local)
		oetStaff.BirthDate = birthDate.Unix()
	}

	oetStaff.NationStr = archive.Nation

	oetStaff.Degree = archive.HighestEdu
	oetStaff.PoliticsStatus = archive.PoliticalIdentity

	oetStaff.MaritalStatus = archive.MarriageStatus
	oetStaff.Healthy = archive.HealthStatus
	oetStaff.UpdatedAt = time.Now().Unix()

	if archive.StartJobAt != nil {
		oetStaff.WorkTime = time.Time(*archive.StartJobAt).Unix()
	}

	if archive.JoinPartyAt != nil {
		oetStaff.PartyTime = time.Time(*archive.JoinPartyAt).Unix()
	}

	oetStaff.IsOfflapSoldier = archive.IsReversionSoldier == util.IsReversionSoldier
	if archive.ReversionAt != nil {
		oetStaff.OfflapTime = time.Time(*archive.ReversionAt).Unix()
	}

	oetStaff.WaysToEnter = strconv.FormatInt(archive.JoinCompanyWay, 10)
	if archive.RetireAt != nil {
		oetStaff.RetireDate = time.Time(*archive.RetireAt).Unix()
	}
	fmt.Printf("ErpStaffFieldToOetStaffField oetstaff: %+v \n", oetStaff)

	return
}

func OetStaffFieldToErpStaffField(oetStaff *protoStaff.OetStaffItem, archive *hrModel.StaffArchive) {
	archive.CorporationId = oetStaff.CorporationId
	archive.Name = oetStaff.Name

	if oetStaff.Sex {
		archive.Gender = util.Male
	} else {
		archive.Gender = util.Female
	}

	archive.NativePlace = oetStaff.NativePlace

	archive.Nation = oetStaff.NationStr

	archive.IdentityId = oetStaff.IdentifyId
	if archive.IdentityId != "" {
		date := []byte(archive.IdentityId)[6:14]
		birthTime, _ := time.ParseInLocation("20060102", string(date), time.Local)
		birthDate := model.LocalTime(birthTime)
		archive.BirthDate = &birthDate
		archive.Age = util.GetAge(birthTime)
	}
	archive.PoliticalIdentity = oetStaff.PoliticsStatus

	archive.WorkPostType = util.MasterToErp[oetStaff.Occupation]

	archive.Contact = oetStaff.Phone
	archive.HealthStatus = oetStaff.Healthy
	if oetStaff.IsOfflapSoldier {
		archive.IsReversionSoldier = util.IsReversionSoldier
	} else {
		archive.IsReversionSoldier = util.NotIsReversionSoldier
	}
	reversionAt := model.LocalTime(time.Unix(oetStaff.OfflapTime, 0))
	archive.ReversionAt = &reversionAt
	archive.DrivingCode = oetStaff.DrvLicense

	archive.DrivingModel = oetStaff.DrvLicenseTypeStr

	partyAt := model.LocalTime(time.Unix(oetStaff.PartyTime, 0))
	archive.JoinPartyAt = &partyAt
	archive.JobNumber = oetStaff.StaffId

	archive.JobStatus = oetStaff.WorkingState

	if oetStaff.WaysToEnter != "" {
		archive.JoinCompanyWay, _ = strconv.ParseInt(oetStaff.WaysToEnter, 10, 64)
	}

	joinAt := model.LocalTime(time.Unix(oetStaff.RegisterTime, 0))
	archive.JoinAt = &joinAt
	startJobAt := model.LocalTime(time.Unix(oetStaff.WorkTime, 0))
	archive.StartJobAt = &startJobAt
	retireAt := model.LocalTime(time.Unix(oetStaff.RetireDate, 0))
	archive.RetireAt = &retireAt
	if oetStaff.ExterHeadPortrait != "" {
		arr := strings.Split(oetStaff.ExterHeadPortrait, "/")
		fileName := arr[len(arr)-1]
		fileNameArr := strings.Split(fileName, ".")
		suffix := ""
		if len(fileNameArr) > 1 {
			suffix = fileNameArr[1]
		}
		archive.HeadImg = hrModel.HeadImg{
			Name:   fileName,
			Path:   oetStaff.ExterHeadPortrait,
			Suffix: suffix,
			Url:    oetStaff.ExterHeadPortrait,
		}
	}
	return
}

func FilterOetStaff(oetStaffs []*protoStaff.OetStaffItem, keyword, nativePlace, drivingModel string, jobStatus, workPostType, politicalIdentity, joinCompanyWay []int64, gender int64) ([]int64, map[int64]*protoStaff.OetStaffItem) {
	//筛选主数据字段：员工姓名、工号、在职状态、政治身份、性别、籍贯、进入公司途径、准驾车型
	var results = make(map[int64]*protoStaff.OetStaffItem)
	var resultIds []int64
	for i := range oetStaffs {
		//过滤掉离职的
		//if oetStaffs[i].WorkingState == util.JobStatusQuit {
		//	continue
		//}

		if keyword != "" {
			if !strings.Contains(oetStaffs[i].Name, keyword) && !strings.Contains(oetStaffs[i].StaffId, keyword) {
				continue
			}
		}

		if len(jobStatus) > 0 {
			if !util.IncludeInt64(jobStatus, oetStaffs[i].WorkingState) {
				continue
			}
		}

		if len(workPostType) > 0 {
			var oetOccupation []int64
			//主数据岗位类型：0-司机; 1-乘务员;2-管理员;3-辅工; 4-辅岗; 5-干部; 6-仓管人员; 7-安保人员;8-修理工, 10-其他;
			//ERP岗位类型：1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他
			erpToMaster := map[int64]int64{1: 2, 2: 5, 3: 0, 4: 1, 5: 7, 6: 3, 7: 6, 8: 10, 9: 8}
			for i := range workPostType {
				oetOccupation = append(oetOccupation, erpToMaster[workPostType[i]])
			}

			if !util.IncludeInt64(oetOccupation, oetStaffs[i].Occupation) {
				continue
			}
		}

		if len(politicalIdentity) > 0 {
			if !util.IncludeInt64(politicalIdentity, oetStaffs[i].PoliticsStatus) {
				continue
			}
		}

		if gender > 0 {
			var oetSex int64
			if oetStaffs[i].Sex {
				oetSex = util.Male
			} else {
				oetSex = util.Female
			}

			if oetSex != gender {
				continue
			}
		}

		if nativePlace != "" {
			if !strings.Contains(oetStaffs[i].NativePlace, nativePlace) {
				continue
			}
		}

		if len(joinCompanyWay) > 0 {
			way, _ := strconv.ParseInt(oetStaffs[i].WaysToEnter, 10, 64)
			if !util.IncludeInt64(joinCompanyWay, way) {
				continue
			}
		}

		if drivingModel != "" {
			if !strings.Contains(oetStaffs[i].DrvLicenseTypeStr, drivingModel) { //TODO
				continue
			}
		}
		results[oetStaffs[i].Id] = oetStaffs[i]

		resultIds = append(resultIds, oetStaffs[i].Id)

	}

	return resultIds, results
}

func HiddenFieldByPermission(archive *hrModel.StaffArchive, user *auth.AuthUser, from string) {
	if from == "Export" {
		//个人信息
		if !user.HasPermission("Staffarchive.Export.BasicInfo") {
			archive.MasterDataFieldForBasic = hrModel.MasterDataFieldForBasic{}
			archive.ErpDataForBasic = hrModel.ErpDataForBasic{}
		}
		//在职信息
		if !user.HasPermission("Staffarchive.Export.JobInfo") {
			archive.MasterDataFieldForJob = hrModel.MasterDataFieldForJob{}
			archive.HumanRelationId = 0
			archive.EmploymentCate = 0
		}
		//学历信息
		if !user.HasPermission("Staffarchive.Export.Education") {
			archive.Educations = []hrModel.StaffEducation{}
		}
		//职称信息
		if !user.HasPermission("Staffarchive.Export.PositionalTitle") {
			archive.PositionalTitles = []hrModel.StaffPositionalTitle{}
		}
		//技能信息
		if !user.HasPermission("Staffarchive.Export.Skill") {
			archive.Skills = []hrModel.StaffSkill{}
		}
		//家庭成员
		if !user.HasPermission("Staffarchive.Export.FamilyMember") {
			archive.FamilyMembers = []hrModel.StaffFamilyMember{}
		}
		//从业资格证
		if !user.HasPermission("Staffarchive.Export.Certificate") {
			archive.Certificates = []hrModel.StaffCertificate{}
		}
		//职务信息
		if !user.HasPermission("Staffarchive.Export.WorkPost") {
			archive.WorkPosts = []hrModel.StaffHasWorkPost{}
		}
		//劳动合同
		if !user.HasPermission("Staffarchive.Export.LaborContract") {
			archive.LaborContracts = []hrModel.StaffLaborContract{}
		}
		archive.PunishmentRecords = []hrModel.StaffPunishmentRecord{}
		archive.RewardRecords = []hrModel.StaffRewardRecord{}
	} else {
		//个人信息
		if !user.HasPermission("Staffarchive.BasicInfo") && !user.HasPermission("Staffarchive.Edit.BasicInfo") {
			archive.MasterDataFieldForBasic = hrModel.MasterDataFieldForBasic{}
			archive.ErpDataForBasic = hrModel.ErpDataForBasic{}
		}
		//在职信息
		if !user.HasPermission("Staffarchive.JobInfo") && !user.HasPermission("Staffarchive.Edit.JobInfo") {
			archive.MasterDataFieldForJob = hrModel.MasterDataFieldForJob{}
			archive.HumanRelationId = 0
		}
		//学历信息
		if !user.HasPermission("Staffarchive.Education") && !user.HasPermission("Staffarchive.Edit.Education") {
			archive.Educations = []hrModel.StaffEducation{}
		}
		//职称信息
		if !user.HasPermission("Staffarchive.PositionalTitle") && !user.HasPermission("Staffarchive.Edit.PositionalTitle") {
			archive.PositionalTitles = []hrModel.StaffPositionalTitle{}
		}
		//技能信息
		if !user.HasPermission("Staffarchive.Skill") && !user.HasPermission("Staffarchive.Edit.Skill") {
			archive.Skills = []hrModel.StaffSkill{}
		}
		//家庭成员
		if !user.HasPermission("Staffarchive.FamilyMember") && !user.HasPermission("Staffarchive.Edit.FamilyMember") {
			archive.FamilyMembers = []hrModel.StaffFamilyMember{}
		}
		//从业资格证
		if !user.HasPermission("Staffarchive.Certificate") && !user.HasPermission("Staffarchive.Edit.Certificate") {
			archive.Certificates = []hrModel.StaffCertificate{}
		}
		//职务信息
		if !user.HasPermission("Staffarchive.WorkPost") && !user.HasPermission("Staffarchive.Edit.WorkPost") {
			archive.WorkPosts = []hrModel.StaffHasWorkPost{}
		}
		//劳动合同
		if !user.HasPermission("Staffarchive.LaborContract") && !user.HasPermission("Staffarchive.Edit.LaborContract") {
			archive.LaborContracts = []hrModel.StaffLaborContract{}
		}
		//惩处记录
		if !user.HasPermission("Staffarchive.PunishmentRecord") && !user.HasPermission("Staffarchive.Edit.PunishmentRecord") {
			archive.PunishmentRecords = []hrModel.StaffPunishmentRecord{}
		}
		//奖励奖励
		if !user.HasPermission("Staffarchive.RewardRecord") && !user.HasPermission("Staffarchive.Edit.RewardRecord") {
			archive.RewardRecords = []hrModel.StaffRewardRecord{}
		}
	}

	//党团资料
	if !user.HasPermission("Staffarchive.PartyData") && !user.HasPermission("Staffarchive.Edit.PartyData") {
		archive.PartyFilePath = nil
	}
}

func UpdateOetByPermission(oetStaff *protoStaff.OetStaffItem, user *auth.AuthUser) {
	//获取旧的Oet数据
	oldOetStaff := rpc.GetStaffWithId(context.Background(), oetStaff.Id)
	if oldOetStaff == nil {
		return
	}

	if !user.HasPermission("Staffarchive.Edit.BasicInfo") {
		oetStaff.Name = oldOetStaff.Name
		oetStaff.Sex = oldOetStaff.Sex
		oetStaff.NativePlace = oldOetStaff.NativePlace
		oetStaff.NationStr = oldOetStaff.NationStr
		oetStaff.Nation = oldOetStaff.Nation
		oetStaff.IdentifyId = oldOetStaff.IdentifyId
		oetStaff.Phone = oldOetStaff.Phone
		oetStaff.Healthy = oldOetStaff.Healthy
		oetStaff.IsOfflapSoldier = oldOetStaff.IsOfflapSoldier
		oetStaff.OfflapTime = oldOetStaff.OfflapTime
		oetStaff.PoliticsStatus = oldOetStaff.PoliticsStatus
		oetStaff.PartyTime = oldOetStaff.PartyTime
		oetStaff.DrvLicense = oldOetStaff.DrvLicense
		oetStaff.DrvLicenseTypeStr = oldOetStaff.DrvLicenseTypeStr
	}

	if !user.HasPermission("Staffarchive.Edit.JobInfo") {
		oetStaff.StaffId = oldOetStaff.StaffId
		oetStaff.WorkingState = oldOetStaff.WorkingState
		oetStaff.CorporationId = oldOetStaff.CorporationId
		oetStaff.WaysToEnter = oldOetStaff.WaysToEnter
		oetStaff.Occupation = oldOetStaff.Occupation
		oetStaff.RegisterTime = oldOetStaff.RegisterTime
		oetStaff.WorkTime = oldOetStaff.WorkTime
		oetStaff.RetireDate = oldOetStaff.RetireDate
	}
}

type OetWhere struct {
	Keyword              string           `json:"Keyword"`              //员工姓名、工号 主数据
	Phone                string           `json:"Phone"`                //手机号
	JobStatusArr         []int64          `json:"JobStatusArr"`         //在职状态  主数据|多选  1-在职,2-离职,3-试用期,4-退休,5-退休返聘
	Gender               int64            `json:"Gender"`               //性别 主数据 1男 2女
	NativePlace          string           `json:"NativePlace"`          //籍贯 主数据
	WorkPostTypeArr      []int64          `json:"WorkPostTypeArr"`      //岗位类型 主数据|多选  1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他
	Nation               string           `json:"Nation"`               //民族 主数据
	IsReversionSoldier   int64            `json:"IsReversionSoldier"`   //是否复转退 主数据  1-是,2-否
	PoliticalIdentityArr []int64          `json:"PoliticalIdentityArr"` //政治身份 主数据|多选 1-中国共产党员,2-中国共产党预备党员,3-中国共产主义青年团团员,4-其他党派人士,5-群众
	JoinCompanyWayArr    []int64          `json:"JoinCompanyWayArr"`    //加入公司途径 主数据|多选 1-在编,2-公开招聘,3-派遣,4-军转安置,5-借用,6-自主录用,7-自主招聘,8-转岗,9-竞聘上岗,11-招聘,12-选调,13劳务派遣,10-其他
	StartBirthAt         *model.LocalTime `json:"StartBirthAt"`         //开始出生日期 主数据
	EndBirthAt           *model.LocalTime `json:"EndBirthAt"`           //结束出生日期 主数据
	HealthStatusArr      []int64          `json:"HealthStatusArr"`      //健康状况 主数据|多选 1-健康,2-良好,3-一般,4-慢性病,5-残疾
	StartReversionAt     *model.LocalTime `json:"StartReversionAt"`     //开始复转退时间 主数据
	EndReversionAt       *model.LocalTime `json:"EndReversionAt"`       //结束复转退时间 主数据
	StartJoinAt          *model.LocalTime `json:"StartJoinAt"`          //开始入职时间 主数据
	EndJoinAt            *model.LocalTime `json:"EndJoinAt"`            //结束入职时间 主数据
	StartJobAt           *model.LocalTime `json:"StartJobAt"`           //开始参加工作时间 主数据
	EndJobAt             *model.LocalTime `json:"EndJobAt"`             //结束参加工作时间 主数据
	StartRetireAt        *model.LocalTime `json:"StartRetireAt"`        //开始退休时间 主数据
	EndRetireAt          *model.LocalTime `json:"EndRetireAt"`          //结束退休时间 主数据
	DrivingModel         string           `json:"DrivingModel"`         //准驾车型 主数据 1-A1,2-A2,3-A3,4-B1,5-B2,6-C1,7-C2,8-C3,9-D,10-E
	SortField            string           `json:"SortField"`            //排序字段 RetireDateAt:退休时间
	SortOrder            string           `json:"SortOrder"`            //排序方式
}

func SelectOetStaffByMultiWhere(ctx context.Context, corporationIds []int64, where OetWhere, isPaginator bool, offset, limit int64) (map[int64]*protoStaff.OetStaffItem, []int64, int64) {

	fmt.Printf("OetWhere==================%+v \n", where)
	var selectWhere protoStaff.GetStaffsWithMultiOptionsRequest
	if where.SortOrder != "" {
		selectWhere.Order = where.SortOrder
	} else {
		selectWhere.Order = "DESC"
	}
	selectWhere.SortArg = where.SortField

	if len(corporationIds) == 0 {
		log.ErrorFields("SelectOetStaffByMultiWhere corporationIds is empty", nil)
		return nil, nil, 0
	}

	//机构
	selectWhere.CorporationIds = corporationIds

	//姓名、工号
	if where.Keyword != "" {
		selectWhere.NameOrStaffId = where.Keyword
	}

	selectWhere.Phone = where.Phone

	//工作状态
	if len(where.JobStatusArr) > 0 {
		selectWhere.WorkingStateArray = where.JobStatusArr
	}

	//性别
	selectWhere.Sex = 3
	if where.Gender > 0 {
		selectWhere.Sex = where.Gender
	}

	//籍贯
	if where.NativePlace != "" {
		selectWhere.NativePlace = where.NativePlace
	}

	//岗位类型
	if len(where.WorkPostTypeArr) > 0 {
		//主数据岗位类型：0-司机; 1-乘务员;2-管理员;3-辅工; 4-辅岗; 5-干部; 6-仓管人员; 7-安保人员; 10-其他;
		//ERP岗位类型：1-普通管理员,2-干部,3-驾驶员,4-乘务员,5-安保人员,6-辅工,7-仓管人员,8-其他
		erpToMaster := map[int64]int64{1: 2, 2: 5, 3: 0, 4: 1, 5: 7, 6: 3, 7: 6, 8: 10}

		var occupations []int64
		for i := range where.WorkPostTypeArr {
			occupations = append(occupations, erpToMaster[where.WorkPostTypeArr[i]])
		}

		selectWhere.OccupationArray = occupations
	}

	//民族
	if where.Nation != "" {
		selectWhere.NationStr = where.Nation
	}

	//是否复转退
	selectWhere.IsOfflapSoldier = 999
	if where.IsReversionSoldier > 0 {
		if where.IsReversionSoldier == util.StatusForTrue {
			selectWhere.IsOfflapSoldier = where.IsReversionSoldier
		} else {
			selectWhere.IsOfflapSoldier = 0
		}
	}

	//政治面貌
	if len(where.PoliticalIdentityArr) > 0 {
		selectWhere.PoliticsStatusArray = where.PoliticalIdentityArr
	}

	//进入公司途径
	if len(where.JoinCompanyWayArr) > 0 {
		for i := range where.JoinCompanyWayArr {
			selectWhere.WaysToEnterArray = append(selectWhere.WaysToEnterArray, strconv.FormatInt(where.JoinCompanyWayArr[i], 10))
		}
	}

	//出生日期
	if where.StartBirthAt != nil && time.Time(*where.StartBirthAt).Unix() > 0 {
		selectWhere.BirthDateStartTime = time.Time(*where.StartBirthAt).Unix()
	}
	if where.EndBirthAt != nil && time.Time(*where.EndBirthAt).Unix() > 0 {
		end := time.Time(*where.EndBirthAt).AddDate(0, 0, 1)
		selectWhere.BirthDateEndTime = end.Unix()
	}

	//健康状态
	if len(where.HealthStatusArr) > 0 {
		selectWhere.HealthyArray = where.HealthStatusArr
	}

	//复转退时间
	if where.StartReversionAt != nil && time.Time(*where.StartReversionAt).Unix() > 0 {
		selectWhere.OfflapTimeStartTime = time.Time(*where.StartReversionAt).Unix()
	}
	if where.EndReversionAt != nil && time.Time(*where.EndReversionAt).Unix() > 0 {
		end := time.Time(*where.EndReversionAt).AddDate(0, 0, 1)
		selectWhere.OfflapTimeEndTime = end.Unix()
	}

	//进入公司时间
	if where.StartJoinAt != nil && time.Time(*where.StartJoinAt).Unix() > 0 {
		selectWhere.RegisterTimeStartTime = time.Time(*where.StartJoinAt).Unix()
	}
	if where.EndJoinAt != nil && time.Time(*where.EndJoinAt).Unix() > 0 {
		end := time.Time(*where.EndJoinAt).AddDate(0, 0, 1)
		selectWhere.RegisterTimeEndTime = end.Unix()
	}

	//参加工作时间
	if where.StartJobAt != nil && time.Time(*where.StartJobAt).Unix() > 0 {
		selectWhere.WorkTimeStartTime = time.Time(*where.StartJobAt).Unix()
	}
	if where.EndJobAt != nil && time.Time(*where.EndJobAt).Unix() > 0 {
		end := time.Time(*where.EndJobAt).AddDate(0, 0, 1)
		selectWhere.WorkTimeEndTime = end.Unix()
	}

	//退休时间
	if where.StartRetireAt != nil && time.Time(*where.StartRetireAt).Unix() > 0 {
		selectWhere.RetireDateStartTime = time.Time(*where.StartRetireAt).Unix()
	}
	if where.EndRetireAt != nil && time.Time(*where.EndRetireAt).Unix() > 0 {
		end := time.Time(*where.EndRetireAt).AddDate(0, 0, 1)
		selectWhere.RetireDateEndTime = end.Unix()
	}

	//驾照类型
	if where.DrivingModel != "" {
		selectWhere.DrvLicenseTypeStr = where.DrivingModel
	}

	if isPaginator {
		selectWhere.Offset = offset
		selectWhere.Limit = limit
	}

	fmt.Printf("selectWhere: %+v \n", selectWhere)
	staffs, count := rpc.GetStaffsWithMultiOptions(ctx, selectWhere)

	var staffIds []int64
	var oetStaffMap = make(map[int64]*protoStaff.OetStaffItem)
	for i := range staffs {
		staffIds = append(staffIds, staffs[i].Id)
		oetStaffMap[staffs[i].Id] = staffs[i]
	}

	return oetStaffMap, staffIds, count
}

func SelectTopCorporationOetStaffByStaffName(ctx context.Context, keyword string) (map[int64]*protoStaff.OetStaffItem, []int64, int64) {
	//当前登陆人
	user := auth.User(ctx).GetUser()
	corporationId := user.TopCorporationId

	staffs := rpc.GetStaffsWithCorporationId(ctx, corporationId, keyword)

	var staffIds []int64
	var oetStaffMap = make(map[int64]*protoStaff.OetStaffItem)
	for i := range staffs {
		staffIds = append(staffIds, staffs[i].Id)
		oetStaffMap[staffs[i].Id] = staffs[i]
	}

	return oetStaffMap, staffIds, int64(len(staffIds))
}

//func GetTopCorporationId(ctx context.Context) int64 {
//	if auth.User(ctx).HasStaff() {
//		oetStaff := rpc.GetStaffWithId(ctx, auth.User(ctx).GetStaffId())
//		if oetStaff == nil {
//			return 0
//		}
//	}
//
//	if auth.User(ctx).HasUser() {
//		return auth.User(ctx).GetUser().GetTopCorporationId()
//	}
//
//	return 0
//}

func UpdateStaffArchiveInfo(staffArchiveId int64, param hrModel.StaffArchive) error {
	var oldInfo hrModel.StaffArchive
	err := oldInfo.FindById(staffArchiveId)
	if err != nil {
		return errors.New("staffArchive.FindById error:" + err.Error())
	}

	corporation := rpc.GetCorporationDetailById(context.TODO(), param.CorporationId)

	if corporation != nil {
		param.GroupId = corporation.GroupId
		param.CompanyId = corporation.CompanyId
		param.BranchId = corporation.BranchId
		param.DepartmentId = corporation.DepartmentId
		param.FleetId = corporation.FleetId
	}
	user := auth.NewUserById(param.OpUserId)
	oetStaff := rpc.GetStaffWithId(context.TODO(), param.StaffId)
	if oetStaff != nil {
		ErpStaffFieldToOetStaffField(param, oetStaff)
		UpdateOetByPermission(oetStaff, user)
		err := rpc.EditOetStaff(context.TODO(), param.OpUserId, oetStaff)
		if err != nil {
			log.ErrorFields("rpc EditOetStaff err", map[string]interface{}{"err": err})
			return errors.New("rpc EditOetStaff err:" + err.Error())
		}
	}

	//年龄和出生日期
	if param.IdentityId != "" && len(param.IdentityId) == 18 {
		date := []byte(param.IdentityId)[6:14]
		birthDate, _ := time.ParseInLocation("20060102", string(date), time.Local)
		localTime := model.LocalTime(birthDate)
		param.BirthDate = &localTime
		param.Age = (time.Now().Unix() - birthDate.Unix()) / (365 * 24 * 3600)
	}

	var permission = ReturnEditPermission(user)
	param.EditApplyStatus = util.ApplyStatusForDone
	err = param.UpdateOrCreate(permission)
	if err != nil {
		log.ErrorFields("StaffArchive.UpdateOrCreate fail", map[string]interface{}{"err": err})
		return errors.New("StaffArchive.UpdateOrCreate error:" + err.Error())
	}
	return nil
}

//func UpdateStaffArchiveHeadImg(staffArchiveId int64, param hrModel.StaffArchive) error {
//	corporation := rpc.GetCorporationDetailById(context.TODO(), param.CorporationId)
//	if corporation != nil {
//		param.GroupId = corporation.GroupId
//		param.CompanyId = corporation.CompanyId
//		param.BranchId = corporation.BranchId
//		param.DepartmentId = corporation.DepartmentId
//		param.FleetId = corporation.FleetId
//	}
//	user := auth.NewUserById(param.OpUserId)
//	oetStaff := rpc.GetStaffWithId(context.TODO(), param.StaffId)
//	if oetStaff != nil {
//		if param.HeadImg != nil {
//			var headImg []hrModel.HeadImg
//			bytes, _ := param.HeadImg.MarshalJSON()
//			_ = json.Unmarshal(bytes, &headImg)
//			oetStaff.HeadImg = headImg[0].Path
//		}
//		UpdateOetByPermission(oetStaff, user)
//		err := rpc.EditOetStaff(context.TODO(), param.OpUserId, oetStaff)
//		if err != nil {
//			log.ErrorFields("rpc EditOetStaff err", map[string]interface{}{"err": err})
//			return errors.New("rpc EditOetStaff err:" + err.Error())
//		}
//	}
//	return nil
//}

func UpdateMineArchive(param hrModel.StaffArchive) error {
	var oldInfo hrModel.StaffArchive
	err := oldInfo.FindMineInfoByStaffId(param.StaffId)
	if err != nil {
		return errors.New("oldInfo.FindMineInfoByStaffId error: " + err.Error())
	}

	oetStaff := rpc.GetStaffWithId(context.Background(), oldInfo.StaffId)
	if oetStaff == nil {
		log.ErrorFields("rpc.GetStaffWithId error", map[string]interface{}{"err": err})
		return errors.New("rpc.GetStaffWithId error: " + err.Error())
	}
	ErpStaffFieldToOetStaffField(param, oetStaff)

	var authUser auth.AuthUser
	authUser.SetPermissions([]string{"Staffarchive.Edit.BasicInfo"})
	UpdateOetByPermission(oetStaff, &authUser)

	err = rpc.EditOetStaff(context.Background(), 0, oetStaff)
	if err != nil {
		log.ErrorFields("rpc EditOetStaff err", map[string]interface{}{"err": err})
		return errors.New("rpc.EditOetStaff error: " + err.Error())
	}

	//年龄和出生日期
	if param.IdentityId != "" && len(param.IdentityId) == 18 {
		date := []byte(param.IdentityId)[6:14]
		birthDate, _ := time.ParseInLocation("20060102", string(date), time.Local)
		localTime := model.LocalTime(birthDate)
		param.BirthDate = &localTime
		param.Age = (time.Now().Unix() - birthDate.Unix()) / (365 * 24 * 3600)
	}

	err = param.UpdateMine()
	if err != nil {
		log.ErrorFields("StaffArchive.UpdateMine fail", map[string]interface{}{"err": err})
		return errors.New("StaffArchive.UpdateMine error: " + err.Error())
	}

	return nil
}

func ReturnEditPermission(user *auth.AuthUser) map[string]bool {
	var permission = make(map[string]bool)
	if !user.HasPermission("Staffarchive.Edit.BasicInfo") {
		permission["Staffarchive.Edit.BasicInfo"] = false
	}

	if !user.HasPermission("Staffarchive.Edit.Education") {
		permission["Staffarchive.Edit.Education"] = false
	}

	if !user.HasPermission("Staffarchive.Edit.PositionalTitle") {
		permission["Staffarchive.Edit.PositionalTitle"] = false
	}

	if !user.HasPermission("Staffarchive.Edit.Skill") {
		permission["Staffarchive.Edit.Skill"] = false
	}

	if !user.HasPermission("Staffarchive.Edit.FamilyMember") {
		permission["Staffarchive.Edit.FamilyMember"] = false
	}

	if !user.HasPermission("Staffarchive.Edit.Certificate") {
		permission["Staffarchive.Edit.Certificate"] = false
	}

	if !user.HasPermission("Staffarchive.Edit.JobInfo") {
		permission["Staffarchive.Edit.JobInfo"] = false
	}

	if !user.HasPermission("Staffarchive.Edit.WorkPost") {
		permission["Staffarchive.Edit.WorkPost"] = false
	}

	if !user.HasPermission("Staffarchive.Edit.LaborContract") {
		permission["Staffarchive.Edit.LaborContract"] = false
	}

	if !user.HasPermission("Staffarchive.Edit.PartyData") {
		permission["Staffarchive.Edit.PartyData"] = false
	}

	return permission
}

func BuildStaffArchiveLogger(oldData, newData *hrModel.StaffArchive, processId string) {
	scene := hrModel.ArchiveLoggerSceneUpdate
	var beforeData, afterData []byte
	if oldData.Id == 0 {
		scene = hrModel.ArchiveLoggerSceneCreate
		_, newDiff := FindDifferentField(nil, *newData, hrModel.ArchiveLoggerExceptField)
		afterData, _ = json.Marshal(newDiff)
	} else {
		exceptFiled := hrModel.ArchiveLoggerExceptField
		if newData.IsMineEdit {
			exceptFiled = hrModel.ArchiveLoggerExceptFieldForMineEdit
		}
		oldDiff, newDiff := FindDifferentField(*oldData, *newData, exceptFiled)
		beforeData, _ = json.Marshal(oldDiff)
		afterData, _ = json.Marshal(newDiff)
	}
	CreateStaffArchiveLogger(newData.Id, newData.OpUserId, newData.OpUserName, scene, newData.OpIp, hrModel.ArchiveLoggerModularForArchive, beforeData, afterData, processId)
}

func CreateStaffArchiveLogger(staffArchiveId, userId int64, userName string, scene int64, ip, modular string, beforeData, afterData []byte, processId string) {
	var logger hrModel.StaffArchiveLogger
	logger.StaffArchiveId = staffArchiveId
	logger.Scene = scene
	logger.Modular = modular
	logger.BeforeData = beforeData
	logger.AfterData = afterData
	logger.Ip = ip
	logger.ProcessId = processId
	logger.OpUserId = userId
	logger.OpUserName = userName

	_ = logger.Create()
}

// StaffTransferAfterUpdateStaffArchive 员工调动成功之后更新员工档案相关信息
func StaffTransferAfterUpdateStaffArchive(tx *gorm.DB, rec hrModel.StaffTransferRecord) error {
	var archive hrModel.StaffArchive
	err := archive.FindByStaffId(rec.StaffId)
	if err != nil {
		return err
	}

	oetStaff := rpc.GetStaffWithId(context.Background(), rec.StaffId)
	if oetStaff == nil {
		return errors.New("oet staff not found")
	}

	//：新增一个岗位，原职位不改变，
	//任职：人事关系改变，所属部门改变；借用、借调、挂职：人事关系不变，所属部门改变；
	//：，新增一个调入岗位，设置为现任，所属部门改成调入部门，人事关系改成调入部门；
	//免职：将调出岗位设置为历任，人事关系不变，部门不变；借用结束、借调结束、挂职结束：将调出岗位设置为历任，人事关系不变，所属部门改成之前的部门；

	//任职、借用、借调、挂职、任免、调动：新增一个调入岗位，设置为现任
	if rec.Type == hrModel.TransferTypeAppointment || rec.Type == hrModel.TransferTypeBorrow || rec.Type == hrModel.TransferTypeLoan || rec.Type == hrModel.TransferTypeSecondment || rec.Type == hrModel.TransferTypeAppointmentRemoval || rec.Type == hrModel.TransferTypeTransfer {
		//新增岗位
		if rec.InCorporationId != 0 && rec.InWorkPostId != 0 {
			var newWorkPostType = hrModel.StaffHasWorkPost{
				StaffArchiveId: archive.Id,
				StaffId:        rec.StaffId,
				CorporationId:  rec.InCorporationId,
				WorkPostType:   rec.InWorkPostType,
				WorkPostId:     rec.InWorkPostId,
				PositionType:   rec.PositionType,
				IsNowJob:       util.IsNowJob,
				StartAt:        rec.StartAt,
			}

			if rec.Type == hrModel.TransferTypeBorrow || rec.Type == hrModel.TransferTypeLoan || rec.Type == hrModel.TransferTypeSecondment {
				newWorkPostType.PreCorporationId = oetStaff.CorporationId
				newWorkPostType.HumanRelationId = archive.HumanRelationId
			} else {
				newWorkPostType.HumanRelationId = rec.InCorporationId
				newWorkPostType.PreCorporationId = rec.OutCorporationId
			}

			err = tx.Create(&newWorkPostType).Error
			if err != nil {
				return err
			}
		}
	}
	var preCorporationId int64
	//免职、任免、调动、借用结束、借调结束、挂职结束：将调出岗位设置为历任
	if rec.Type == hrModel.TransferTypeRemoval || rec.Type == hrModel.TransferTypeAppointmentRemoval || rec.Type == hrModel.TransferTypeTransfer || rec.Type == hrModel.TransferTypeBorrowEnd || rec.Type == hrModel.TransferTypeLoanEnd || rec.Type == hrModel.TransferTypeSecondmentEnd {
		//查询调出岗位
		var workPost hrModel.StaffHasWorkPost
		tx.Model(&hrModel.StaffHasWorkPost{}).Where("CorporationId = ? AND StaffArchiveId = ? AND WorkPostId = ? AND IsNowJob = ?", rec.OutCorporationId, archive.Id, rec.OutWorkPostId, util.IsNowJob).First(&workPost)
		if workPost.Id > 0 {
			//更新职务为历任
			err = tx.Model(&hrModel.StaffHasWorkPost{}).Where("Id =  ?", workPost.Id).UpdateColumn("IsNowJob", util.NotIsNowJob).Error
			if err != nil {
				return err
			}
			preCorporationId = workPost.PreCorporationId
		}
	}

	var humanRelationId int64
	//免职、借用、借调、挂职、借用结束、借调结束、挂职结束：人事关系不变；
	//任免、任职、调动：人事关系更新为调入部门
	if rec.Type == hrModel.TransferTypeRemoval || rec.Type == hrModel.TransferTypeBorrow || rec.Type == hrModel.TransferTypeLoan || rec.Type == hrModel.TransferTypeSecondment || rec.Type == hrModel.TransferTypeBorrowEnd || rec.Type == hrModel.TransferTypeLoanEnd || rec.Type == hrModel.TransferTypeSecondmentEnd {
		humanRelationId = archive.HumanRelationId
	} else {
		humanRelationId = rec.InCorporationId
	}

	var corporationId int64
	//借用、借调、挂职、借用结束、借调结束、挂职结束：所属部门改为原来的部门；
	//免职：所属部门不变；
	//任免、任职、调动：所属部门更新为调入部门
	if rec.Type == hrModel.TransferTypeBorrowEnd || rec.Type == hrModel.TransferTypeLoanEnd || rec.Type == hrModel.TransferTypeSecondmentEnd {
		corporationId = preCorporationId
	} else {
		if rec.Type == hrModel.TransferTypeRemoval {
			corporationId = oetStaff.CorporationId
		} else {
			corporationId = rec.InCorporationId
		}
	}

	//更新部门和人事关系
	if corporationId != 0 && oetStaff.CorporationId != corporationId {
		transfer := (&hrModel.StaffTransfer{}).FirstBy(rec.StaffTransferId)
		oetStaff.CorporationId = corporationId
		err := rpc.EditOetStaff(context.Background(), transfer.OpUserId, oetStaff)
		if err != nil {
			return err
		}
	}

	err = archive.UpdateCorporation(corporationId, humanRelationId)
	if err != nil {
		return err
	}

	return nil
}

func StaffQuitAfterUpdateStaffArchive(tx *gorm.DB, rec hrModel.StaffQuitRecord) error {
	//更主数据员工状态
	oetStaff := rpc.GetStaffWithId(context.Background(), rec.StaffId)
	if oetStaff != nil {
		oetStaff.WorkingState = util.JobStatusQuit
		err := rpc.EditOetStaff(context.Background(), 0, oetStaff)
		if err != nil {
			return err
		}
	}
	//更新劳动合同状态
	var archive hrModel.StaffArchive
	tx.Model(&hrModel.StaffArchive{}).Where("StaffId = ?", rec.StaffId).First(&archive)
	if archive.Id == 0 {
		return errors.New("archive not found")
	}
	var contract hrModel.StaffLaborContract
	tx.Model(&hrModel.StaffLaborContract{}).Where("StaffArchiveId = ?", archive.Id).Order("EndAt DESC").First(&contract)
	err := tx.Model(&hrModel.StaffLaborContract{}).Where("Id = ?", contract.Id).Updates(map[string]interface{}{
		"status":            util.LaborContractRelieve,
		"relievecontractat": rec.RelieveContractAt,
	}).Error

	return err
}
