package service

import (
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/model/workOrder"
	"gorm.io/gorm"
)

func DeleteWorkOrderAndRelation(tx *gorm.DB, workOrderIds []int64) error {
	err := tx.Where("Id IN ?", workOrderIds).Delete(&workOrder.WorkOrder{}).Error
	if err != nil {
		return err
	}

	//删除工单备件
	err = tx.Where("FkWorkOrdersId IN ?", workOrderIds).Delete(&workOrder.WorkOrderRepairSparePart{}).Error
	if err != nil {
		return err
	}

	err = tx.Where("WorkOrderId IN ?", workOrderIds).Delete(&workOrder.WorkOrderReplaceChildDevice{}).Error
	if err != nil {
		return err
	}

	//删除流程
	var formInstanceIds []int64
	model.DB().Model(&processModel.LbpmApplyProcess{}).Select("FormInstanceId").Where("ItemId IN ? AND ItemTableName = ?", workOrderIds, (&workOrder.WorkOrder{}).TableName()).Pluck("FormInstanceId", &formInstanceIds)

	if len(formInstanceIds) > 0 {
		err = DeleteProcessAndMessage(tx, formInstanceIds)
		if err != nil {
			return err
		}
	}

	return nil
}

func DeletePetitionWorkOrderAndRelation(tx *gorm.DB, workOrderIds []int64) error {
	err := tx.Where("Id IN ?", workOrderIds).Delete(&workOrder.PetitionWorkOrder{}).Error
	if err != nil {
		return err
	}

	//删除审批记录
	err = tx.Where("PetitionWorkOrderId IN ?", workOrderIds).Delete(&workOrder.PetitionWorkOrderHandleResult{}).Error
	if err != nil {
		return err
	}

	//删除流程
	var formInstanceIds []int64
	model.DB().Model(&processModel.LbpmApplyProcess{}).Select("FormInstanceId").Where("ItemId IN ? AND ItemTableName = ?", workOrderIds, (&workOrder.PetitionWorkOrder{}).TableName()).Pluck("FormInstanceId", &formInstanceIds)

	if len(formInstanceIds) > 0 {
		err = DeleteProcessAndMessage(tx, formInstanceIds)
		if err != nil {
			return err
		}
	}

	return nil
}
