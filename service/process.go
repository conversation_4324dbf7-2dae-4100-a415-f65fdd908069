package service

import (
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/message"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"gorm.io/gorm"
	"sort"
	"time"
)

func DeleteProcessAndMessage(tx *gorm.DB, formInstanceIds []int64) error {
	err := tx.Where("RelationId IN ? AND RelationTableName = ?", formInstanceIds, (&processModel.LbpmApplyProcess{}).TableName()).Delete(&message.Message{}).Error
	if err != nil {
		return err
	}

	err = tx.Where("FormInstanceId IN ?", formInstanceIds).Delete(&processModel.LbpmApplyProcessHasHandler{}).Error
	if err != nil {
		return err
	}

	err = tx.Where("FormInstanceId IN ?", formInstanceIds).Delete(&processModel.LbpmApplyProcess{}).Error
	if err != nil {
		return err
	}

	return nil
}

func UpdateProcessCorporationIdForHistory() {
	time.Sleep(60 * time.Second)
	//var i = 0
	//for {
	//	if i > 8000 {
	//		break
	//	}
	var records []processModel.LbpmApplyProcess
	model.DB().Model(&records).Where("(CorporationId = ? OR CorporationId IS NULL) AND applyUserId > ? AND Status > ?", 0, 0, 0).Find(&records)

	if len(records) > 0 {
		for j := range records {
			user := rpc.GetUserInfoById(context.TODO(), records[j].ApplyUserId)
			if user != nil {
				records[j].UpdateColumn("CorporationId", user.CorporationId)
				corpName := user.Corporation
				if user.CorporationId > 0 && user.Corporation == "" {
					corp := rpc.GetCorporationById(context.TODO(), user.CorporationId)
					if corp != nil {
						corpName = corp.Name
					}
				}
				records[j].UpdateColumn("CorporationName", corpName)
			}
		}
	}

}

func GeProcessCurrentApprovingHandler(formInstanceId int64) []processModel.LbpmApplyProcessHasHandler {
	handlers := (&processModel.LbpmApplyProcessHasHandler{}).GetAllHandlerByFormInstanceId(formInstanceId)

	sort.SliceStable(handlers, func(i, j int) bool {
		return time.Time(*handlers[i].StartAt).Unix() >= time.Time(*handlers[j].StartAt).Unix()
	})

	var node string
	var processHandlers []processModel.LbpmApplyProcessHasHandler
	for i, handler := range handlers {
		if i == 0 && handler.NodeType == util.ProcessNodeTypeForNotice {
			break
		}

		if i == 0 && handler.NodeType == util.ProcessNodeTypeForApprove && handler.Node != "" {
			node = handler.Node
			if handler.Status == util.ProcessNodeHandleStatusForDoing {
				processHandlers = append(processHandlers, handler)
			}
			continue
		}

		if i > 0 && handler.Node != node {
			break
		}

		if handler.Status == util.ProcessNodeHandleStatusForDoing {
			processHandlers = append(processHandlers, handler)
		}
	}

	return processHandlers
}

func RemoveProcessCurrentHandler(formInstanceId int64) {
	var process processModel.LbpmApplyProcess
	_ = process.FindBy(formInstanceId)

	if (process.Status == util.ProcessStatusForDone ||
		process.Status == util.ProcessStatusForTerminate ||
		process.Status == util.ProcessStatusForAbandon) && process.CurrentHandlerUserName != "" {
		_ = process.UpdateColumn("CurrentHandlerUserName", "")

		model.DB().Model(&processModel.LbpmApplyProcessHasHandler{}).Where("FormInstanceId = ? AND NodeType = ? AND Status = ?",
			process.FormInstanceId, util.ProcessNodeTypeForApprove, util.ProcessNodeHandleStatusForDoing).
			UpdateColumn("status", util.ProcessNodeHandleStatusForCancel)
	}
}
