package export

import (
	"app/org/scs/erpv2/api/log"
	exportModel "app/org/scs/erpv2/api/model/export"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"fmt"
	"github.com/tealeg/xlsx"
	"sort"
	"strings"
)

type LineCorporationItem struct {
	LineId        int64
	LineName      string
	CorporationId int64
}

func ExportLineDriverWork(data []map[string]interface{}, exportFile exportModel.ExportFile, exportGroup, from string, handler service.ApprovalHandler) error {
	//设置导出字段
	var headers = map[string]settingModel.CheckedColumnValueItem{
		"Index":                                   {ColumnCnName: "序号", Type: "index", Sort: 1},
		"ReportAt":                                {ColumnCnName: "日期", Type: "date", Sort: 2, Width: 25},
		"CorporationName":                         {ColumnCnName: "所属机构", Type: "string", Sort: 3},
		"LineName":                                {ColumnCnName: "运营线路", Type: "mix", Sort: 4},
		"MainRunLineName":                         {ColumnCnName: "主运营线路", Type: "mix", Sort: 5},
		"DriverName":                              {ColumnCnName: "运营司机", Type: "string", Sort: 6},
		"JobNumber":                               {ColumnCnName: "工号", Type: "mix", Sort: 7},
		"DriverHasLineName":                       {ColumnCnName: "归属线路", Type: "mix", Sort: 8},
		"PlanWorkDay":                             {ColumnCnName: "计划出勤天数", Type: "number", Sort: 9, Unit: 0, IsSum: true, Precision: 0},
		"PlanWorkRate":                            {ColumnCnName: "班次执行率", Type: "string", Sort: 10, Suffix: "%"},
		"PlanTotalCircle":                         {ColumnCnName: "月计划排班总圈次", Type: "number", Sort: 11, Unit: 10, IsSum: true, Precision: 2},
		"WorkDayCount":                            {ColumnCnName: "本线路出勤天数", Type: "number", Sort: 12, Unit: 10, IsSum: true, Precision: 2},
		"LineWorkDay":                             {ColumnCnName: "汇总线路天数", Type: "number", Sort: 13, Unit: 10, IsSum: true, Precision: 2},
		"VehicleLength":                           {ColumnCnName: "车长", Type: "number", Sort: 14, Unit: 1000, IsSum: false, Precision: 3},
		"VehicleModelStr":                         {ColumnCnName: "车型", Type: "string", Sort: 15},
		"FrequencyTypeStr":                        {ColumnCnName: "班制", Type: "string", Sort: 16},
		"FollowVehicleDay":                        {ColumnCnName: "跟车天数", Type: "number", Sort: 17, Unit: 10, IsSum: true, Precision: 2},
		"FullWorkDay":                             {ColumnCnName: "全班天数", Type: "number", Sort: 18, Unit: 10, IsSum: true, Precision: 2},
		"HalfWorkDay":                             {ColumnCnName: "半班天数", Type: "number", Sort: 19, Unit: 10, IsSum: true, Precision: 2},
		"MotorWorkDay":                            {ColumnCnName: "机动天数", Type: "number", Sort: 20, Unit: 10, IsSum: true, Precision: 2},
		"MotorBigWorkDay":                         {ColumnCnName: "大机动天数", Type: "number", Sort: 21, Unit: 10, IsSum: true, Precision: 2},
		"FullPlanCircle":                          {ColumnCnName: "每日实际排班圈次", Type: "number", Sort: 22, Unit: 10, IsSum: true, Precision: 2},
		"FullDoneCircle":                          {ColumnCnName: "本线路完成圈次", Type: "number", Sort: 23, Unit: 10, IsSum: true, Precision: 2},
		"FixVehicleCircle":                        {ColumnCnName: "修车圈次", Type: "number", Sort: 24, Unit: 10, IsSum: true, Precision: 2},
		"CasualLeaveCircle":                       {ColumnCnName: "事假圈次", Type: "number", Sort: 25, Unit: 10, IsSum: true, Precision: 2},
		"AccidentDisputeCircle":                   {ColumnCnName: "事故圈次", Type: "number", Sort: 26, Unit: 10, IsSum: true, Precision: 2},
		"AnnualReviewCircle":                      {ColumnCnName: "年审圈次", Type: "number", Sort: 27, Unit: 10, IsSum: true, Precision: 2},
		"OfficialWorkCircle":                      {ColumnCnName: "公务出勤圈次", Type: "number", Sort: 28, Unit: 10, IsSum: true, Precision: 2},
		"CharterBusCircle":                        {ColumnCnName: "包车圈次", Type: "number", Sort: 29, Unit: 10, IsSum: true, Precision: 2},
		"SickLeaveCircle":                         {ColumnCnName: "病假圈次", Type: "number", Sort: 30, Unit: 10, IsSum: true, Precision: 2},
		"TrafficJamCircle":                        {ColumnCnName: "堵车圈次", Type: "number", Sort: 31, Unit: 10, IsSum: true, Precision: 2},
		"MaintenanceCircle":                       {ColumnCnName: "保养圈次", Type: "number", Sort: 32, Unit: 10, IsSum: true, Precision: 2},
		"AddGasCircle":                            {ColumnCnName: "加气圈次", Type: "number", Sort: 33, Unit: 10, IsSum: true, Precision: 2},
		"NightTotalWorkTimeLength":                {ColumnCnName: "夜班总时长", Type: "number", Sort: 34, Unit: 3600, IsSum: true, Precision: 2},
		"DelayInParkingTimeLength":                {ColumnCnName: "延迟进场时长", Type: "number", Sort: 35, Unit: 3600, IsSum: true, Precision: 2},
		"CircleMileage":                           {ColumnCnName: "圈次公里", Type: "number", Sort: 36, Unit: 1000, IsSum: true, Precision: 3},
		"StopWorkRatedMileage":                    {ColumnCnName: "自定义核定公里", Type: "number", Sort: 37, Unit: 1000, IsSum: true, Precision: 3},
		"FullInOutDepotMileage":                   {ColumnCnName: "进出场公里 ", Type: "number", Sort: 38, Unit: 1000, IsSum: true, Precision: 3},
		"FullAssistantMileage":                    {ColumnCnName: "辅助公里 ", Type: "number", Sort: 39, Unit: 1000, IsSum: true, Precision: 3},
		"CharterBusMileage":                       {ColumnCnName: "包车公里 ", Type: "number", Sort: 40, Unit: 1000, IsSum: true, Precision: 3},
		"AnnualReviewMileage":                     {ColumnCnName: "年审公里 ", Type: "number", Sort: 41, Unit: 1000, IsSum: true, Precision: 3},
		"TrafficJamMileage":                       {ColumnCnName: "堵车公里 ", Type: "number", Sort: 42, Unit: 1000, IsSum: true, Precision: 3},
		"TotalMileage":                            {ColumnCnName: "合计公里 ", Type: "number", Sort: 43, Unit: 1000, IsSum: true, Precision: 3},
		"FullCircleWorkTimeLength":                {ColumnCnName: "圈次岗上时长", Type: "number", Sort: 44, Unit: 3600, IsSum: true, Precision: 2},
		"FullCircleNotWorkTimeLength":             {ColumnCnName: "圈次岗下时长", Type: "number", Sort: 45, Unit: 3600, IsSum: true, Precision: 2},
		"FullInOutDepotTime":                      {ColumnCnName: "进出场时长", Type: "number", Sort: 46, Unit: 3600, IsSum: true, Precision: 2},
		"FullAssistantTime":                       {ColumnCnName: "辅助时长", Type: "number", Sort: 47, Unit: 3600, IsSum: true, Precision: 2},
		"FullStopWorkTimeLength":                  {ColumnCnName: "自定义岗上时长", Type: "number", Sort: 48, Unit: 3600, IsSum: true, Precision: 2},
		"FullStopNotWorkTimeLength":               {ColumnCnName: "自定义岗下时长", Type: "number", Sort: 49, Unit: 3600, IsSum: true, Precision: 2},
		"AddWorkTimeLength":                       {ColumnCnName: "加班时长", Type: "number", Sort: 50, Unit: 3600, IsSum: true, Precision: 2},
		"AddWorkWorkTimeLength":                   {ColumnCnName: "加班岗上时长", Type: "number", Sort: 51, Unit: 3600, IsSum: true, Precision: 2},
		"AddWorkNotWorkTimeLength":                {ColumnCnName: "加班岗下时长", Type: "number", Sort: 52, Unit: 3600, IsSum: true, Precision: 2},
		"AddWorkTimes":                            {ColumnCnName: "不足2.5小时次数", Type: "number", Sort: 53, Unit: 0, IsSum: true},
		"FullDayAddWorkTimes":                     {ColumnCnName: "超过半天不足一天次数", Type: "number", Sort: 54, Unit: 0, IsSum: true},
		"HalfDayAddWorkTimes":                     {ColumnCnName: "超过2.5小时不足半天次数", Type: "number", Sort: 55, Unit: 0, IsSum: true},
		"NightAddWorkTimeLength":                  {ColumnCnName: "夜班加班时长", Type: "number", Sort: 56, Unit: 3600, IsSum: true, Precision: 2},
		"FixVehicleMileage":                       {ColumnCnName: "修车公里 ", Type: "number", Sort: 57, Unit: 1000, IsSum: true, Precision: 3},
		"FixVehicleTimeLength":                    {ColumnCnName: "修车岗下时长", Type: "number", Sort: 58, Unit: 3600, IsSum: true, Precision: 2},
		"FixVehicleWorkTimeLength":                {ColumnCnName: "修车岗上时长", Type: "number", Sort: 59, Unit: 3600, IsSum: true, Precision: 2},
		"CasualLeaveTimeLength":                   {ColumnCnName: "事假时长", Type: "number", Sort: 60, Unit: 3600, IsSum: true, Precision: 2},
		"CasualLeaveDay":                          {ColumnCnName: "事假天数", Type: "number", Sort: 61, Unit: 10, IsSum: true, Precision: 2},
		"CasualLeaveTimes":                        {ColumnCnName: "事假次数", Type: "number", Sort: 62, Unit: 0, IsSum: true, Precision: 2},
		"CasualLeaveFirstLastPlanTimes":           {ColumnCnName: "事假首末班次数", Type: "number", Sort: 63, Unit: 0, IsSum: true},
		"AccidentDisputeTimeLength":               {ColumnCnName: "事故纠纷时长", Type: "number", Sort: 64, Unit: 3600, IsSum: true, Precision: 2},
		"AnnualReviewTimeLength":                  {ColumnCnName: "年审时长", Type: "number", Sort: 65, Unit: 3600, IsSum: true, Precision: 2},
		"OfficialWorkTimeLength":                  {ColumnCnName: "公务出勤时长", Type: "number", Sort: 66, Unit: 3600, IsSum: true, Precision: 2},
		"OfficialWorkDay":                         {ColumnCnName: "公务出勤天数", Type: "number", Sort: 67, Unit: 10, IsSum: true, Precision: 2},
		"CharterBusTimeLength":                    {ColumnCnName: "包车时长", Type: "number", Sort: 68, Unit: 3600, IsSum: true, Precision: 2},
		"SickLeaveTimeLength":                     {ColumnCnName: "病假时长", Type: "number", Sort: 69, Unit: 3600, IsSum: true, Precision: 2},
		"SickLeaveTimeDay":                        {ColumnCnName: "病假天数", Type: "number", Sort: 70, Unit: 10, IsSum: true, Precision: 2},
		"AnnualLeaveDay":                          {ColumnCnName: "年休假天数", Type: "number", Sort: 71, Unit: 10, IsSum: true, Precision: 2},
		"HolidayDay":                              {ColumnCnName: "节假日天数", Type: "number", Sort: 72, Unit: 10, IsSum: true, Precision: 2},
		"TrafficJamTimeLength":                    {ColumnCnName: "堵车时长", Type: "number", Sort: 73, Unit: 3600, IsSum: true, Precision: 2},
		"RestTimeDay":                             {ColumnCnName: "疗休养天数", Type: "number", Sort: 74, Unit: 10, IsSum: true, Precision: 2},
		"TotalWorkTimeLength":                     {ColumnCnName: "合计岗上时长", Type: "number", Sort: 75, Unit: 3600, IsSum: true, Precision: 2},
		"TotalNotWorkTimeLength":                  {ColumnCnName: "合计岗下时长", Type: "number", Sort: 76, Unit: 3600, IsSum: true, Precision: 2},
		"TotalTimeLength":                         {ColumnCnName: "总时长", Type: "number", Sort: 77, Unit: 3600, IsSum: true, Precision: 2},
		"InFrequencyTypeCircleWorkTimeLength":     {ColumnCnName: "班制内圈次岗上时长", Type: "number", Sort: 78, Unit: 3600, IsSum: true, Precision: 2},
		"InFrequencyTypeCircleNotWorkTimeLength":  {ColumnCnName: "班制内圈次岗下时长", Type: "number", Sort: 79, Unit: 3600, IsSum: true, Precision: 2},
		"OutFrequencyTypeCircleWorkTimeLength":    {ColumnCnName: "班制外圈次岗上时长", Type: "number", Sort: 80, Unit: 3600, IsSum: true, Precision: 2},
		"OutFrequencyTypeCircleNotWorkTimeLength": {ColumnCnName: "班制外圈次岗下时长", Type: "number", Sort: 81, Unit: 3600, IsSum: true, Precision: 2},
		"FullSinglePassWorkTimeLengthValue":       {ColumnCnName: "全程单圈工作时长", Type: "number", Sort: 82, Unit: 3600, IsSum: false, Precision: 2},
		"FullWorkTimeLengthValue":                 {ColumnCnName: "全程单圈岗上时长", Type: "number", Sort: 83, Unit: 3600, IsSum: false, Precision: 2},
		"FullNotWorkTimeLengthValue":              {ColumnCnName: "全程单圈岗下时长", Type: "number", Sort: 84, Unit: 3600, IsSum: false, Precision: 2},
		"RangeSinglePassWorkTimeLengthValue":      {ColumnCnName: "区间单圈工作时长", Type: "number", Sort: 85, Unit: 3600, IsSum: false, Precision: 2},
		"RangeWorkTimeLengthValue":                {ColumnCnName: "区间单圈岗上时长", Type: "number", Sort: 86, Unit: 3600, IsSum: false, Precision: 2},
		"RangeNotWorkTimeLengthValue":             {ColumnCnName: "区间单圈岗下时长", Type: "number", Sort: 87, Unit: 3600, IsSum: false, Precision: 2},
		"InPlanDoneCircle":                        {ColumnCnName: "排班内完成圈次", Type: "number", Sort: 88, Unit: 10, IsSum: true, Precision: 2},
		"OutPlanDoneCircle":                       {ColumnCnName: "排班外完成圈次", Type: "number", Sort: 89, Unit: 10, IsSum: true, Precision: 2},
		"More":                                    {ColumnCnName: "备注", Type: "string", Sort: 90, Width: 25},
	}
	var columns []settingModel.CheckedColumnValueItem

	//获取用户的配置
	setting := (&settingModel.ColumnSetting{}).FirstByUserId(exportFile.OpUserId, exportFile.Scene)
	if setting.Id > 0 {
		var settingItem settingModel.ColumnSettingItem
		err := json.Unmarshal(setting.SettingItem, &settingItem)
		if err != nil {
			log.ErrorFields("ExportLineDriverWork SettingItem json.Unmarshal error", map[string]interface{}{"err": err})
		} else {
			firstColumn := headers["Index"]
			firstColumn.ColumnName = "Index"
			firstColumn.Sort = 0
			columns = append(columns, firstColumn)
			for _, settingItemColumn := range settingItem.ColumnAttr {
				if util.Include(settingItem.CheckedColumns, settingItemColumn.ColumnName) {
					if _, ok := headers[settingItemColumn.ColumnName]; ok {
						column := headers[settingItemColumn.ColumnName]
						column.ColumnName = settingItemColumn.ColumnName
						column.Sort = settingItemColumn.Sort
						column.Color = settingItemColumn.Color
						columns = append(columns, column)
					} else {
						columns = append(columns, settingItemColumn)
					}
				}
			}
		}
	}

	if len(columns) == 0 {
		for columnName, column := range headers {
			column.ColumnName = columnName
			columns = append(columns, column)
		}
	}

	sort.Slice(columns, func(i, j int) bool {
		return columns[i].Sort < columns[j].Sort
	})
	fmt.Printf("=======columns===============%+v,\n", columns)

	excel := xlsx.NewFile()
	//按线路分sheet导出
	if exportGroup == "line" {
		var lineData = make(map[string][]map[string]interface{})
		var lineSlice []LineCorporationItem
		for i := range data {
			var lineId interface{}
			var lineName string
			if from == "driver" {
				lineId = data[i]["MainRunLineId"]
				lineName, _ = data[i]["MainRunLineName"].(string)
			} else {
				lineId = data[i]["LineId"]
				lineName, _ = data[i]["LineName"].(string)
			}

			mapKey := fmt.Sprintf("%v_%v", int64(data[i]["CorporationId"].(float64)), int64(lineId.(float64)))

			if _, ok := lineData[mapKey]; !ok {
				lineSlice = append(lineSlice, LineCorporationItem{
					LineId:        int64(lineId.(float64)),
					LineName:      lineName,
					CorporationId: int64(data[i]["CorporationId"].(float64)),
				})
			}

			lineData[mapKey] = append(lineData[mapKey], data[i])
		}

		sort.SliceStable(lineSlice, func(i, j int) bool {
			return lineSlice[i].LineName < lineSlice[j].LineName
		})

		for _, line := range lineSlice {
			var mapKey = fmt.Sprintf("%v_%v", line.CorporationId, line.LineId)
			if _, ok := lineData[mapKey]; ok {
				sheet, err := excel.AddSheet(line.LineName)
				if err != nil {
					log.ErrorFields("ExportLineDriverWork excel.AddSheetLineName error", map[string]interface{}{"err": err})
					return err
				}
				var sumData = make(map[string]interface{})
				sumData["Index"] = "合计"
				for _, d := range lineData[mapKey] {
					for j := range columns {
						if columns[j].IsSum {
							//合计
							t, _ := sumData[columns[j].ColumnName].(float64)
							n, _ := d[columns[j].ColumnName].(float64)
							sumData[columns[j].ColumnName] = t + n
						}
					}
				}
				BuildExcelBaseHeader(sheet, columns)
				BuildExcelMainData(sheet, columns, lineData[mapKey])
				BuildExcelMainData(sheet, ReBuildSumColumnStyle(columns), []map[string]interface{}{sumData})
				BuildExcelBottomSign(sheet, columns, ApprovalHandlerSignParser(handler))
			}
		}
	} else {
		sheet, err := excel.AddSheet("sheet1")
		if err != nil {
			log.ErrorFields("ExportLineDriverWork excel.AddSheet1 error", map[string]interface{}{"err": err})
			return err
		}
		var sumData = make(map[string]interface{})
		sumData["Index"] = "合计"
		for _, d := range data {
			for j := range columns {
				if columns[j].IsSum {
					//合计
					t, _ := sumData[columns[j].ColumnName].(float64)
					n, _ := d[columns[j].ColumnName].(float64)
					sumData[columns[j].ColumnName] = t + n
				}
			}
		}
		BuildExcelBaseHeader(sheet, columns)
		BuildExcelMainData(sheet, columns, data)
		BuildExcelMainData(sheet, ReBuildSumColumnStyle(columns), []map[string]interface{}{sumData})
		BuildExcelBottomSign(sheet, columns, ApprovalHandlerSignParser(handler))
	}

	return SaveExcelFile(excel, exportFile)
}

// ApprovalHandlerSignParser 设置报表签字行
func ApprovalHandlerSignParser(handler service.ApprovalHandler) []string {
	var sign []string
	if handler.HasApproval {
		sign = append(sign, "车队正副队长审核：")
		sign = append(sign, strings.Join(handler.Appliers, "，"))
		sign = append(sign, "提交人：")
		sign = append(sign, strings.Join(handler.Creators, "，"))
	}

	return sign
}
