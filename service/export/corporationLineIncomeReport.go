package export

import (
	"app/org/scs/erpv2/api/model/export"
	settingModel "app/org/scs/erpv2/api/model/setting"
	ticketModel "app/org/scs/erpv2/api/model/ticket"
	"encoding/json"
	"github.com/tealeg/xlsx"
	"sort"
	"strings"
)

func ExportCorporationLineIncomeReport(reports map[int64][]ticketModel.CorporationLineIncomeReport, exportFile export.ExportFile) error {
	//获取用户设置的字段
	var tableColumns = map[string]settingModel.CheckedColumnValueItem{
		"CorporationName":      {ColumnCnName: "车队", Type: "string", Width: 20, Sort: 1},
		"LineName":             {ColumnCnName: "线路", Type: "string", Sort: 2},
		"Price":                {ColumnCnName: "补贴单价", Type: "mix", Sort: 3, Unit: 100, IsSum: false, Precision: 2},
		"TotalCircle":          {ColumnCnName: "班次", Type: "number", Sort: 4, Unit: 10, IsSum: true, Precision: 1},
		"TotalMileage":         {ColumnCnName: "行程（公里）", Type: "number", Width: 15, Sort: 5, Unit: 1000, IsSum: true, Precision: 3},
		"CashPaymentPeople":    {ColumnCnName: "票款人次", Type: "number", Sort: 6, IsSum: true},
		"MobilePaymentPeople":  {ColumnCnName: "移动支付", Type: "number", Sort: 7, IsSum: true},
		"NormalIcPeople":       {ColumnCnName: "普通IC卡", Type: "number", Sort: 8, IsSum: true},
		"HalfOlderIcPeople":    {ColumnCnName: "半价老年卡", Type: "number", Sort: 9, IsSum: true},
		"FreeOlderIcPeople":    {ColumnCnName: "免费老年卡", Type: "number", Sort: 10, IsSum: true},
		"AdultSpecialIcPeople": {ColumnCnName: "成人优待", Type: "number", Sort: 11, IsSum: true},
		"TotalPeople":          {ColumnCnName: "合计", Type: "number", Sort: 12, IsSum: true},
		"FreeChangePeople":     {ColumnCnName: "其中：免费换乘", Type: "number", Width: 15, Sort: 13, IsSum: true},
		"CashPaymentMoney":     {ColumnCnName: "票款营收", Type: "number", Sort: 14, Unit: 100, IsSum: true, Precision: 2},
		"MobilePaymentMoney":   {ColumnCnName: "移动支付", Type: "number", Sort: 15, Unit: 100, IsSum: true, Precision: 2},
		"NormalIcMoney":        {ColumnCnName: "普通IC卡", Type: "number", Sort: 16, Unit: 100, IsSum: true, Precision: 2},
		"HalfOlderIcMoney":     {ColumnCnName: "半价老年卡", Type: "number", Sort: 17, Unit: 100, IsSum: true, Precision: 2},
		"TotalOperationMoney":  {ColumnCnName: "合计", Type: "number", Sort: 18, Unit: 100, IsSum: true, Precision: 2},
		"NormalIcAllowance":    {ColumnCnName: "普通IC卡", Type: "number", Sort: 19, Unit: 100, IsSum: true, Precision: 2},
		"HalfOlderIcAllowance": {ColumnCnName: "半价老年卡", Type: "number", Sort: 20, Unit: 100, IsSum: true, Precision: 2},
		"FreeOlderIcAllowance": {ColumnCnName: "免费老年卡", Type: "number", Sort: 21, Unit: 100, IsSum: true, Precision: 2},
		"FreeChangeAllowance":  {ColumnCnName: "免费换乘", Type: "number", Sort: 22, Unit: 100, IsSum: true, Precision: 2},
		"TotalAllowance":       {ColumnCnName: "合计", Type: "number", Sort: 23, Unit: 100, IsSum: true, Precision: 2},
		"TotalMoney":           {ColumnCnName: "总计", Type: "number", Width: 20, Sort: 24, Unit: 100, IsSum: true, Precision: 2},
	}

	var columns []settingModel.CheckedColumnValueItem
	for columnName, column := range tableColumns {
		column.ColumnName = columnName
		columns = append(columns, column)
	}

	sort.SliceStable(columns, func(i, j int) bool {
		return columns[i].Sort < columns[j].Sort
	})

	excel := xlsx.NewFile()
	sheet, err := excel.AddSheet("sheet1")
	if err != nil {
		return err
	}

	BuildCorporationLineIncomeReportExcelHeader(sheet, columns)
	var sumData = make(map[string]interface{})
	sumData["CorporationName"] = "营收含税合计"
	sumData["LineName"] = "营收含税合计"
	sumData["Price"] = "--"
	for _, reportData := range reports {
		var data []map[string]interface{}
		for i := range reportData {
			resultByte, err := json.Marshal(reportData[i])
			if err != nil {
				return err
			}
			var dataMap map[string]interface{}
			err = json.Unmarshal(resultByte, &dataMap)
			if err != nil {
				return err
			}
			data = append(data, dataMap)
		}

		var subSumData = make(map[string]interface{})
		subSumData["CorporationName"] = reportData[0].CorporationName
		subSumData["LineName"] = "小计"
		subSumData["Price"] = "--"
		for _, d := range data {
			for j := range columns {
				if columns[j].IsSum {
					//车队合计
					t, _ := subSumData[columns[j].ColumnName].(float64)
					n, _ := d[columns[j].ColumnName].(float64)
					subSumData[columns[j].ColumnName] = t + n

					//总合计
					a, _ := sumData[columns[j].ColumnName].(float64)
					sumData[columns[j].ColumnName] = a + n
				}
			}
		}

		BuildExcelMainData(sheet, columns, data)
		BuildExcelMainData(sheet, ReBuildSumColumnStyle(columns), []map[string]interface{}{subSumData})
	}

	var sumColumns []settingModel.CheckedColumnValueItem
	for _, column := range columns {
		column.Color = "#dfff10"
		column.Bold = true
		column.RowHeight = 72
		column.BorderStyle = "medium"
		sumColumns = append(sumColumns, column)
	}
	sumColumns[0].HMerge = 1
	sumColumns[1].IsHidden = true
	BuildExcelMainData(sheet, sumColumns, []map[string]interface{}{sumData})

	return SaveExcelFile(excel, exportFile)
}

func BuildCorporationLineIncomeReportExcelHeader(sheet *xlsx.Sheet, columns []settingModel.CheckedColumnValueItem) {
	sheet.SheetViews = []xlsx.SheetView{{Pane: &xlsx.Pane{
		TopLeftCell: "C3",
		State:       "frozen",
		XSplit:      2,
		YSplit:      2,
	}}}

	var topFirstHeaders []settingModel.CheckedColumnValueItem
	for i := 0; i < 5; i++ {
		column := columns[i]
		column.VMerge = 1
		column.Bold = true
		topFirstHeaders = append(topFirstHeaders, column)
	}

	topFirstHeaders = append(topFirstHeaders, settingModel.CheckedColumnValueItem{
		ColumnCnName: "客运量",
		HMerge:       7,
		Bold:         true,
	}, settingModel.CheckedColumnValueItem{
		ColumnCnName: "客运收入",
		HMerge:       4,
		Bold:         true,
	}, settingModel.CheckedColumnValueItem{
		ColumnCnName: "政府补贴",
		HMerge:       4,
		Bold:         true,
	})
	lastColumn := columns[len(columns)-1]
	lastColumn.VMerge = 1
	lastColumn.Bold = true
	topFirstHeaders = append(topFirstHeaders, lastColumn)

	align := xlsx.Alignment{
		Horizontal: "center",
		Vertical:   "center",
		WrapText:   true,
	}
	border := xlsx.Border{
		Left:        "thin",
		LeftColor:   "000000",
		Top:         "thin",
		TopColor:    "000000",
		Right:       "thin",
		RightColor:  "000000",
		Bottom:      "thin",
		BottomColor: "000000",
	}
	style := xlsx.NewStyle()
	style.Border = border
	style.Alignment = align
	//添加一级表头
	row := sheet.AddRow()
	row.SetHeight(36)
	for _, header := range topFirstHeaders {
		cell := row.AddCell()
		cell.Merge(header.HMerge, header.VMerge)
		cell.SetString(header.ColumnCnName)
		if header.Color != "" {
			bgColor := strings.ReplaceAll(header.Color, "#", "")
			style.Fill = xlsx.Fill{PatternType: "solid", FgColor: bgColor}
		} else {
			style.Fill = xlsx.Fill{PatternType: "solid", FgColor: "cccccc"}
		}
		if header.Bold {
			style.Font.Bold = true
		}
		cell.SetStyle(style)
		if header.HMerge > 0 {
			for i := 0; i < header.HMerge; i++ {
				row.AddCell()
			}
		}
	}

	//二级表头
	row = sheet.AddRow()
	row.SetHeight(36)
	for colIndex, header := range columns {
		if header.Width > 0 {
			_ = sheet.SetColWidth(colIndex, colIndex, float64(header.Width))
		}
		cell := row.AddCell()
		cell.SetString(header.ColumnCnName)
		if header.Color != "" {
			bgColor := strings.ReplaceAll(header.Color, "#", "")
			style.Fill = xlsx.Fill{PatternType: "solid", FgColor: bgColor}
		} else {
			style.Fill = xlsx.Fill{PatternType: "solid", FgColor: "cccccc"}
		}
		if header.Bold {
			style.Font.Bold = true
		}
		cell.SetStyle(style)
	}
}
