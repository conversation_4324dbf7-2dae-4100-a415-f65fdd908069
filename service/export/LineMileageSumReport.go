package export

import (
	"app/org/scs/erpv2/api/model"
	exportModel "app/org/scs/erpv2/api/model/export"
	operationModel "app/org/scs/erpv2/api/model/operation"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"fmt"
	"github.com/tealeg/xlsx"
	"sort"
	"time"
)

type LineMileageSumReportRsp struct {
	operationModel.LineMileageAndCircleSumItem
	CityCenter int64 `json:"CityCenter"` //市级
	Jiaojiang  int64 `json:"Jiaojiang"`  //椒江区
	Taizhouwan int64 `json:"Taizhouwan"` //台州湾区
	Huangyan   int64 `json:"Huangyan"`   //黄岩区
	Luqiao     int64 `json:"Luqiao"`     //路桥区
	Linhai     int64 `json:"Linhai"`     //临海
}

func LineMileageSumReport(records []LineMileageSumReportRsp, exportFile exportModel.ExportFile) error {
	//获取用户设置的字段
	var tableColumns = map[string]settingModel.CheckedColumnValueItem{
		"Index":           {ColumnCnName: "序号", Type: "index", Sort: 1},
		"ReportAt":        {ColumnCnName: "日期段", Type: "string", Sort: 2},
		"LineName":        {ColumnCnName: "运营线路", Type: "string", Sort: 3},
		"CorporationName": {ColumnCnName: "所属机构", Type: "string", Sort: 4},
		"LineAttr":        {ColumnCnName: "线路属性", Type: "string", Sort: 5},
		"TotalCircle":     {ColumnCnName: "实际圈次", Type: "number", Sort: 6, Unit: 10, IsSum: true, Precision: 2},
		"TotalMileage":    {ColumnCnName: "运营公里", Type: "number", Sort: 7, Unit: 1000, IsSum: true, Precision: 3},
		"CityCenter":      {ColumnCnName: "市级", Type: "number", Sort: 8, Unit: 1000, IsSum: true, Precision: 3},
		"Jiaojiang":       {ColumnCnName: "台州湾区", Type: "number", Sort: 9, Unit: 1000, IsSum: true, Precision: 3},
		"Taizhouwan":      {ColumnCnName: "椒江区", Type: "number", Sort: 10, Unit: 1000, IsSum: true, Precision: 3},
		"Huangyan":        {ColumnCnName: "黄岩区", Type: "number", Sort: 11, Unit: 1000, IsSum: true, Precision: 3},
		"Luqiao":          {ColumnCnName: "路桥区", Type: "number", Sort: 12, Unit: 1000, IsSum: true, Precision: 3},
		"Linhai":          {ColumnCnName: "临海市", Type: "number", Sort: 13, Unit: 1000, IsSum: true, Precision: 3},
	}

	var columns []settingModel.CheckedColumnValueItem
	for columnName, column := range tableColumns {
		column.ColumnName = columnName
		columns = append(columns, column)
	}

	sort.SliceStable(columns, func(i, j int) bool {
		return columns[i].Sort < columns[j].Sort
	})

	//处理汇总数据
	//var data []map[string]interface{}
	//var sumData = make(map[string]interface{})
	//sumData["Index"] = "合计"
	//for i := range records {
	//	resultByte, err := json.Marshal(records[i])
	//	if err != nil {
	//		return err
	//	}
	//	var dataMap map[string]interface{}
	//	err = json.Unmarshal(resultByte, &dataMap)
	//	if err != nil {
	//		return err
	//	}
	//	dataMap["ReportAt"] = fmt.Sprintf("%s - %s", exportFile.StartAt.ToTime().Format(model.DateFormat), exportFile.EndAt.ToTime().Format(model.DateFormat))
	//	dataMap["LineAttr"] = util.LineAttrMap[records[i].LineAttr]
	//
	//	for j := range columns {
	//		if columns[j].IsSum {
	//			//合计
	//			t, _ := sumData[columns[j].ColumnName].(float64)
	//			n, _ := dataMap[columns[j].ColumnName].(float64)
	//			sumData[columns[j].ColumnName] = t + n
	//		}
	//	}
	//	data = append(data, dataMap)
	//}

	excel := xlsx.NewFile()

	err := handleCityDataAndBuildSheet(excel, "", "", records, columns, exportFile.StartAt.ToTime(), exportFile.EndAt.ToTime())
	if err != nil {
		return err
	}

	err = handleCityDataAndBuildSheet(excel, "CityCenter", "市级", records, columns, exportFile.StartAt.ToTime(), exportFile.EndAt.ToTime())
	if err != nil {
		return err
	}
	err = handleCityDataAndBuildSheet(excel, "Taizhouwan", "台州湾区", records, columns, exportFile.StartAt.ToTime(), exportFile.EndAt.ToTime())
	if err != nil {
		return err
	}
	err = handleCityDataAndBuildSheet(excel, "Jiaojiang", "椒江区", records, columns, exportFile.StartAt.ToTime(), exportFile.EndAt.ToTime())
	if err != nil {
		return err
	}

	err = handleCityDataAndBuildSheet(excel, "Huangyan", "黄岩区", records, columns, exportFile.StartAt.ToTime(), exportFile.EndAt.ToTime())
	if err != nil {
		return err
	}
	err = handleCityDataAndBuildSheet(excel, "Luqiao", "路桥区", records, columns, exportFile.StartAt.ToTime(), exportFile.EndAt.ToTime())
	if err != nil {
		return err
	}
	err = handleCityDataAndBuildSheet(excel, "Linhai", "临海市", records, columns, exportFile.StartAt.ToTime(), exportFile.EndAt.ToTime())
	if err != nil {
		return err
	}

	return SaveExcelFile(excel, exportFile)
}

func handleCityDataAndBuildSheet(excel *xlsx.File, city, cityName string, records []LineMileageSumReportRsp, columns []settingModel.CheckedColumnValueItem, startAt, endAt time.Time) error {
	var data []map[string]interface{}
	var sumData = make(map[string]interface{})
	sumData["Index"] = "合计"
	for i := range records {
		resultByte, err := json.Marshal(records[i])
		if err != nil {
			return err
		}
		var dataMap map[string]interface{}
		err = json.Unmarshal(resultByte, &dataMap)
		if err != nil {
			return err
		}

		dataMap["ReportAt"] = fmt.Sprintf("%s - %s", startAt.Format(model.DateFormat), endAt.Format(model.DateFormat))
		dataMap["LineAttr"] = util.LineAttrMap[records[i].LineAttr]

		if city == "" {
			data = append(data, dataMap)
			for j := range columns {
				if columns[j].IsSum {
					//合计
					t, _ := sumData[columns[j].ColumnName].(float64)
					n, _ := dataMap[columns[j].ColumnName].(float64)
					sumData[columns[j].ColumnName] = t + n
				}
			}
		} else {
			if _, ok := dataMap[city]; ok {
				if dataMap[city].(float64) > 0 {
					data = append(data, dataMap)
					for j := range columns {
						if columns[j].IsSum {
							//合计
							t, _ := sumData[columns[j].ColumnName].(float64)
							n, _ := dataMap[columns[j].ColumnName].(float64)
							sumData[columns[j].ColumnName] = t + n
						}
					}
				}
			}
		}
	}

	var sheet *xlsx.Sheet
	var err error
	if city == "" {
		sheet, err = excel.AddSheet("汇总")
		if err != nil {
			return err
		}
	} else {
		sheet, err = excel.AddSheet(cityName)
		if err != nil {
			return err
		}
	}

	BuildExcelBaseHeader(sheet, columns)
	BuildExcelMainData(sheet, columns, data)
	BuildExcelMainData(sheet, ReBuildSumColumnStyle(columns), []map[string]interface{}{sumData})
	return nil
}
