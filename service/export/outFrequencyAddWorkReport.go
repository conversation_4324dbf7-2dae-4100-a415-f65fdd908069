package export

import (
	"app/org/scs/erpv2/api/model"
	exportModel "app/org/scs/erpv2/api/model/export"
	operationModel "app/org/scs/erpv2/api/model/operation"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"github.com/tealeg/xlsx"
	"sort"
	"strings"
	"time"
)

func ExportOutFrequencyAddWorkReport(records []operationModel.OutFrequencyAddWorkReport, exportFile exportModel.ExportFile, handler service.ApprovalHandler) error {
	//获取用户设置的字段
	var tableColumns = map[string]settingModel.CheckedColumnValueItem{
		"Index":          {ColumnCnName: "序号", Type: "index", Sort: 1},
		"ReportAt":       {ColumnCnName: "日期", Type: "date", Sort: 2},
		"DriverName":     {ColumnCnName: "运营司机", Type: "string", Sort: 3},
		"JobNumber":      {ColumnCnName: "工号", Type: "string", Sort: 4},
		"LineName":       {ColumnCnName: "运营线路", Type: "string", Sort: 5},
		"License":        {ColumnCnName: "运营车辆", Type: "string", Sort: 6},
		"AddWorkTypeStr": {ColumnCnName: "加班类型", Type: "string", Sort: 7},
		"AreaScene":      {ColumnCnName: "区内外", Type: "string", Sort: 8},
		"DayCount":       {ColumnCnName: "运营天数", Type: "number", Sort: 9, Unit: 10, IsSum: true, Precision: 2},
		"TimeLength":     {ColumnCnName: "时长", Type: "number", Sort: 10, Unit: 3600, IsSum: true, Precision: 2},
		"Mileage":        {ColumnCnName: "运营公里", Type: "number", Sort: 11, Unit: 1000, IsSum: true, Precision: 3},
		"More":           {ColumnCnName: "备注", Type: "string", Sort: 12},
	}

	var columns []settingModel.CheckedColumnValueItem
	for columnName, column := range tableColumns {
		column.ColumnName = columnName
		columns = append(columns, column)
	}

	sort.SliceStable(columns, func(i, j int) bool {
		return columns[i].Sort < columns[j].Sort
	})

	//处理数据
	var data []map[string]interface{}
	var sumData = make(map[string]interface{})
	sumData["Index"] = "合计"
	for i := range records {
		records[i].AddWorkTypeStr = operationModel.AddWorkTypeStringMap[records[i].AddWorkType]
		resultByte, err := json.Marshal(records[i])
		if err != nil {
			return err
		}
		var dataMap map[string]interface{}
		err = json.Unmarshal(resultByte, &dataMap)
		if err != nil {
			return err
		}
		dataMap["ReportAt"] = time.Time(records[i].ReportAt).Format(model.DateFormat)
		for j := range columns {
			if columns[j].IsSum {
				//合计
				t, _ := sumData[columns[j].ColumnName].(float64)
				n, _ := dataMap[columns[j].ColumnName].(float64)
				sumData[columns[j].ColumnName] = t + n
			}
		}

		data = append(data, dataMap)
	}
	excel := xlsx.NewFile()
	sheet, err := excel.AddSheet("sheet1")
	if err != nil {
		return err
	}
	BuildExcelBaseHeader(sheet, columns)
	BuildExcelMainData(sheet, columns, data)
	BuildExcelMainData(sheet, ReBuildSumColumnStyle(columns), []map[string]interface{}{sumData})
	BuildExcelBottomSign(sheet, columns, ApprovalHandlerSignParser(handler))
	return SaveExcelFile(excel, exportFile)
}

func ExportOutFrequencyAddWorkSumReport(records []operationModel.OutFrequencyAddWorkSumReport, exportFile exportModel.ExportFile) error {
	//获取用户设置的字段
	var tableColumns = map[string]settingModel.CheckedColumnValueItem{
		"Index":        {ColumnCnName: "序号", Type: "index", Sort: 1},
		"ReportAt":     {ColumnCnName: "日期", Type: "date", Sort: 2},
		"DriverName":   {ColumnCnName: "运营司机", Type: "string", Sort: 3},
		"JobNumber":    {ColumnCnName: "工号", Type: "string", Sort: 4},
		"LineName":     {ColumnCnName: "运营线路", Type: "string", Sort: 5},
		"AddWorkType":  {ColumnCnName: "加班类型", Type: "string", Sort: 7},
		"AddWorkTimes": {ColumnCnName: "加班次数", Type: "number", Sort: 9, Unit: 0, IsSum: true},
		"TimeLength":   {ColumnCnName: "时长", Type: "string", Sort: 10},
		"Mileage":      {ColumnCnName: "运营公里", Type: "string", Sort: 11},
		"More":         {ColumnCnName: "备注", Type: "string", Sort: 12},
	}

	var columns []settingModel.CheckedColumnValueItem
	for columnName, column := range tableColumns {
		column.ColumnName = columnName
		columns = append(columns, column)
	}

	sort.SliceStable(columns, func(i, j int) bool {
		return columns[i].Sort < columns[j].Sort
	})

	//处理数据
	var data []map[string]interface{}
	var sumData = make(map[string]interface{})
	sumData["Index"] = "合计"
	for i := range records {
		resultByte, err := json.Marshal(records[i])
		if err != nil {
			return err
		}
		var dataMap map[string]interface{}
		err = json.Unmarshal(resultByte, &dataMap)
		if err != nil {
			return err
		}
		dataMap["ReportAt"] = fmt.Sprintf("%s-%s", time.Time(exportFile.StartAt).Format(model.DateFormat), time.Time(exportFile.EndAt).Format(model.DateFormat))
		dataMap["LineName"] = strings.Join(records[i].LineName, "/")
		timeLength := util.IntSliceToStrSlice(records[i].TimeLength)
		dataMap["TimeLength"] = strings.Join(timeLength, "/")

		var mileages []string
		for j := range records[i].Mileage {
			mileage := decimal.NewFromInt(records[i].Mileage[j]).Div(decimal.NewFromFloat(1000)).Round(3)
			mileages = append(mileages, mileage.String())
		}
		dataMap["Mileage"] = strings.Join(mileages, "/")
		dataMap["More"] = strings.Join(records[i].More, "/")

		for j := range columns {
			if columns[j].IsSum {
				//合计
				t, _ := sumData[columns[j].ColumnName].(float64)
				n, _ := dataMap[columns[j].ColumnName].(float64)
				sumData[columns[j].ColumnName] = t + n
			}
		}
		data = append(data, dataMap)
	}
	excel := xlsx.NewFile()
	sheet, err := excel.AddSheet("sheet1")
	if err != nil {
		return err
	}
	BuildExcelBaseHeader(sheet, columns)
	BuildExcelMainData(sheet, columns, data)
	BuildExcelMainData(sheet, ReBuildSumColumnStyle(columns), []map[string]interface{}{sumData})
	return SaveExcelFile(excel, exportFile)
}
