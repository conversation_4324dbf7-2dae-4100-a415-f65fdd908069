package export

import (
	"app/org/scs/erpv2/api/model"
	exportModel "app/org/scs/erpv2/api/model/export"
	operationModel "app/org/scs/erpv2/api/model/operation"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"github.com/tealeg/xlsx"
	"sort"
	"time"
)

func ExportIrregularLine(records []operationModel.IrregularLineReport, exportFile exportModel.ExportFile, handler service.ApprovalHandler) error {
	//获取用户设置的字段
	var tableColumns = map[string]settingModel.CheckedColumnValueItem{
		"Index":           {ColumnCnName: "序号", Type: "index", Sort: 1},
		"ReportAt":        {ColumnCnName: "日期", Type: "date", Sort: 2},
		"CorporationName": {ColumnCnName: "所属机构", Type: "string", Sort: 3},
		"LineName":        {ColumnCnName: "运营线路", Type: "string", Sort: 4},
		"LineAttr":        {ColumnCnName: "线路属性", Type: "string", Sort: 5},
		"License":         {ColumnCnName: "运营车辆", Type: "string", Sort: 6},
		"DriverName":      {ColumnCnName: "运营司机", Type: "string", Sort: 7},
		"DriverCode":      {ColumnCnName: "司机工号", Type: "string", Sort: 8},
		"PlanCircle":      {ColumnCnName: "计划圈次", Type: "number", Sort: 9, Unit: 10, IsSum: true, Precision: 2},
		"ActualCircle":    {ColumnCnName: "实际圈次", Type: "number", Sort: 10, Unit: 10, IsSum: true, Precision: 2},
		"DoneMileage":     {ColumnCnName: "运营公里", Type: "number", Sort: 11, Unit: 1000, IsSum: true, Precision: 3},
		"ExtraCircle":     {ColumnCnName: "额外圈次", Type: "number", Sort: 12, Unit: 10, IsSum: true, Precision: 2},
		"CustomMileage":   {ColumnCnName: "自定义公里", Type: "number", Sort: 13, Unit: 1000, IsSum: true, Precision: 3},
		"More":            {ColumnCnName: "备注", Type: "string", Sort: 14},
	}

	var columns []settingModel.CheckedColumnValueItem
	for columnName, column := range tableColumns {
		column.ColumnName = columnName
		columns = append(columns, column)
	}

	sort.SliceStable(columns, func(i, j int) bool {
		return columns[i].Sort < columns[j].Sort
	})

	//处理数据
	var data []map[string]interface{}
	var sumData = make(map[string]interface{})
	sumData["Index"] = "合计"
	for i := range records {
		resultByte, err := json.Marshal(records[i])
		if err != nil {
			return err
		}
		var dataMap map[string]interface{}
		err = json.Unmarshal(resultByte, &dataMap)
		if err != nil {
			return err
		}
		dataMap["ReportAt"] = time.Time(records[i].ReportAt).Format(model.DateFormat)
		dataMap["LineAttr"] = util.LineAttrMap[records[i].LineAttr]

		for j := range columns {
			if columns[j].IsSum {
				//合计
				t, _ := sumData[columns[j].ColumnName].(float64)
				n, _ := dataMap[columns[j].ColumnName].(float64)
				sumData[columns[j].ColumnName] = t + n
			}
		}
		data = append(data, dataMap)
	}

	excel := xlsx.NewFile()
	sheet, err := excel.AddSheet("sheet1")
	if err != nil {
		return err
	}

	BuildExcelBaseHeader(sheet, columns)
	BuildExcelMainData(sheet, columns, data)
	BuildExcelMainData(sheet, ReBuildSumColumnStyle(columns), []map[string]interface{}{sumData})
	BuildExcelBottomSign(sheet, columns, ApprovalHandlerSignParser(handler))
	return SaveExcelFile(excel, exportFile)
}
