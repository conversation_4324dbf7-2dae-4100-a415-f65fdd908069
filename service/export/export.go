package export

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	exportModel "app/org/scs/erpv2/api/model/export"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/util"
	"fmt"
	"github.com/shopspring/decimal"
	"github.com/tealeg/xlsx"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

var ExcelBaseStyle = struct {
	align  xlsx.Alignment
	border xlsx.Border
}{
	align: xlsx.Alignment{
		Horizontal: "center",
		Vertical:   "center",
		WrapText:   true,
	},
	border: xlsx.Border{
		Left:        "thin",
		LeftColor:   "000000",
		Top:         "thin",
		TopColor:    "000000",
		Right:       "thin",
		RightColor:  "000000",
		Bottom:      "thin",
		BottomColor: "000000",
	},
}

func ReBuildSumColumnStyle(columns []settingModel.CheckedColumnValueItem) []settingModel.CheckedColumnValueItem {
	var sumColumns []settingModel.CheckedColumnValueItem
	for _, column := range columns {
		column.Color = "#cccccc"
		column.Bold = true
		sumColumns = append(sumColumns, column)
	}
	return sumColumns
}

func BuildExcelBaseHeader(sheet *xlsx.Sheet, columns []settingModel.CheckedColumnValueItem) {
	sheet.SheetViews = []xlsx.SheetView{{Pane: &xlsx.Pane{
		TopLeftCell: "B2",
		State:       "frozen",
		XSplit:      1,
		YSplit:      1,
	}}}
	//添加表头
	row := sheet.AddRow()
	var rowHeight float64
	for colIndex, cellData := range columns {
		if rowHeight == 0 && cellData.RowHeight != 0 {
			rowHeight = cellData.RowHeight
		}
		if cellData.Width > 0 {
			_ = sheet.SetColWidth(colIndex, colIndex, float64(cellData.Width))
		}
		cell := row.AddCell()
		cell.SetString(cellData.ColumnCnName)
		style := xlsx.NewStyle()
		if cellData.Color != "" {
			bgColor := strings.ReplaceAll(cellData.Color, "#", "")
			style.Fill = xlsx.Fill{PatternType: "solid", FgColor: bgColor}
		} else {
			style.Fill = xlsx.Fill{PatternType: "solid", FgColor: "cccccc"}
		}
		style.Border = ExcelBaseStyle.border
		style.Alignment = ExcelBaseStyle.align
		style.Font.Bold = true
		cell.SetStyle(style)
	}

	if rowHeight > 0 {
		row.SetHeight(rowHeight)
	} else {
		row.SetHeight(36)
	}
}

func BuildExcelMainData(sheet *xlsx.Sheet, columns []settingModel.CheckedColumnValueItem, data []map[string]interface{}) {
	//添加数据
	for i, rowData := range data {
		row := sheet.AddRow()
		var rowHeight float64
		for _, cellData := range columns {
			if rowHeight == 0 && cellData.RowHeight != 0 {
				rowHeight = cellData.RowHeight
			}
			if cellData.IsHidden {
				continue
			}
			cell := row.AddCell()
			style := xlsx.NewStyle()
			style.Alignment = ExcelBaseStyle.align
			style.Border = ExcelBaseStyle.border
			if cellData.BorderStyle != "" {
				style.Border.Left = cellData.BorderStyle
				style.Border.Top = cellData.BorderStyle
				style.Border.Right = cellData.BorderStyle
				style.Border.Bottom = cellData.BorderStyle
			}
			if cellData.Color != "" {
				bgColor := strings.ReplaceAll(cellData.Color, "#", "")
				style.Fill = xlsx.Fill{PatternType: "solid", FgColor: bgColor}
			}
			if cellData.Bold {
				style.Font.Bold = true
			}
			cell.SetStyle(style)

			value, isExistColumn := rowData[cellData.ColumnName]

			if cellData.HMerge > 0 {
				cell.Merge(cellData.HMerge, cellData.VMerge)
				for j := 0; j < cell.HMerge; j++ {
					hideCell := row.AddCell()
					hideCell.SetStyle(style)
				}
			}
			if cellData.VMerge > 0 {
				cell.Merge(cellData.HMerge, cellData.VMerge)
			}

			if cellData.Type == "index" && !isExistColumn {
				value = i + 1
			}
			if cellData.Type == "number" {
				err, ok := value.(float64)
				log.ErrorFields("value.(float64) error", map[string]interface{}{"err": err, "i": i, "value": value, "column": cellData.ColumnName})

				//需要进行单位换算
				if cellData.Unit > 0 {
					if ok {
						v := value.(float64)
						value = decimal.NewFromFloat(v).Div(decimal.NewFromInt(cellData.Unit)).Round(int32(cellData.Precision)).InexactFloat64()
					}
				}
			}

			if cellData.Suffix != "" {
				cell.SetString(fmt.Sprintf("%v%s", value, cellData.Suffix))
			} else {
				if cellData.Type == "number" {
					_, ok := value.(float64)
					if !ok {
						cell.SetFloat(0)
					} else {
						cell.SetFloat(value.(float64))
					}
				} else if cellData.Type == "string" {
					_, ok := value.(string)
					if !ok {
						cell.SetString("")
					} else {
						cell.SetString(value.(string))
					}
				} else if cellData.Type == "mix" {
					strVal, ok := value.(string)
					if ok {
						numVal, err := strconv.ParseInt(strVal, 10, 64)
						if err != nil {
							cell.SetString(strVal)
						} else {
							cell.SetInt64(numVal)
						}
					} else {
						numVal, ok := value.(float64)
						if ok {
							cell.SetFloat(numVal)
						} else {
							cell.SetValue(value)
						}
					}
				} else {
					if value == nil {
						cell.SetValue("")
					} else {
						cell.SetValue(value)
					}
				}
			}
		}

		if rowHeight > 0 {
			row.SetHeight(rowHeight)
		} else {
			row.SetHeight(36)
		}
	}
}

func BuildExcelBottomSign(sheet *xlsx.Sheet, columns []settingModel.CheckedColumnValueItem, lastSign []string) {
	//签字行
	log.ErrorFields("========sign=======", map[string]interface{}{"lastSign": lastSign})
	row := sheet.AddRow()
	var rowHeight float64
	for _, cellData := range columns {
		if rowHeight == 0 && cellData.RowHeight != 0 {
			rowHeight = cellData.RowHeight
		}
	}
	if rowHeight > 0 {
		row.SetHeight(rowHeight)
	} else {
		row.SetHeight(36)
	}

	cell1 := row.AddCell()
	cell1.Merge(0, 1)

	cell := row.AddCell()
	cell.Merge(len(columns)-2, 1)
	var sign string
	for i := range lastSign {
		sign += lastSign[i]
		if i%2 > 0 {
			sign += "             "
		}
	}
	style := xlsx.NewStyle()
	style.Fill.PatternType = "solid"
	style.Fill.FgColor = "dddddd"
	style.Font.Bold = true
	style.Font.Size = 16
	style.Font.Color = "000000"
	style.Alignment.Vertical = "center"
	cell.SetStyle(style)
	cell.SetString(sign)
}

func SaveExcelFile(excel *xlsx.File, exportFileRecord exportModel.ExportFile) error {
	err := util.VerifyMkdirExistAndCreate(config.Config.AbsDirPath + exportFileRecord.Path)
	if err != nil {
		log.ErrorFields("ExportLineDriverWork excel.Save() error", map[string]interface{}{"err": err})
		return err
	}

	filePath := GetExcelFilePath(exportFileRecord)
	err = excel.Save(filePath)
	if err != nil {
		log.ErrorFields("SaveExcelFile excel.Save() error", map[string]interface{}{"err": err})
		return err
	}

	return nil
}
func GetExcelFilePath(exportFileRecord exportModel.ExportFile) string {
	return fmt.Sprintf("%s%s/%s", config.Config.AbsDirPath, exportFileRecord.Path, exportFileRecord.Key)
}
func GetExcelFolder(exportFileRecord exportModel.ExportFile) string {
	return fmt.Sprintf("%s%s", config.Config.AbsDirPath, exportFileRecord.Path)
}

func CreateExportFileRecord(userId int64, fileName, scene string, param []byte, startAt, endAt time.Time) (exportModel.ExportFile, error) {
	authUser := auth.NewUserById(userId)
	var suffix = filepath.Ext(fileName) // .xlsx
	var exportFile = exportModel.ExportFile{
		TopCorporationId: authUser.TopCorporationId,
		FileName:         fileName,
		Key:              fmt.Sprintf("%s-%v%s", time.Now().Format("20060102150405"), model.Id(), suffix),
		Scene:            scene,
		StartAt:          model.LocalTime(startAt),
		EndAt:            model.LocalTime(endAt),
		Param:            param,
		Status:           util.ExportFileStatusForDoing,
	}
	exportFile.OpUserId = authUser.Id
	exportFile.OpUserName = authUser.Name

	err := exportFile.Create()
	if err != nil {
		log.ErrorFields("ExportLineDriverWork Create error", map[string]interface{}{"err": err})
		return exportFile, err
	}

	return exportFile, nil
}
