package export

import (
	exportModel "app/org/scs/erpv2/api/model/export"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"github.com/tealeg/xlsx"
	"sort"
)

const VehicleCheckResultExportScene = "vehicle_check_result"
const VehicleCheckAbnormalResultExportScene = "vehicle_check_abnormal_result"

var StaffOccupationMap = map[int64]string{0: "司机", 1: "乘务员", 2: "管理员", 3: "辅工", 4: "辅岗", 5: "干部", 6: "仓管人员", 7: "安保人员", 8: "修理工", 10: "其他"}
var VehicleCheckTypeMap = map[int64]string{1: "回场门检", 2: "出场门检"}
var VehicleCheckStatusMap = map[int64]string{1: "正常", 2: "异常"}
var VehicleCheckDealStatusMap = map[int64]string{1: "未处理", 2: "处理中", 3: "已处理"}
var VehicleCheckAllowOperationMap = map[int64]string{1: "允许", 2: "不允许"}

func ExportVehicleCheckResult(records []map[string]interface{}, exportFile exportModel.ExportFile) error {
	//获取用户设置的字段
	var tableColumns = map[string]settingModel.CheckedColumnValueItem{
		"Index":                  {ColumnCnName: "序号", Type: "index", Sort: 1},
		"License":                {ColumnCnName: "车牌", Type: "string", Sort: 2},
		"Corporation":            {ColumnCnName: "车辆所属机构", Type: "string", Sort: 3},
		"PlanScheduleStaffNames": {ColumnCnName: "排班司机", Type: "string", Sort: 4},
		"StaffName":              {ColumnCnName: "提交人", Type: "string", Sort: 5},
		"Occupation":             {ColumnCnName: "提交人岗位", Type: "string", Sort: 6},
		"CreatedAt":              {ColumnCnName: "提交时间", Type: "string", Sort: 7},
		"CheckType":              {ColumnCnName: "提交类型", Type: "string", Sort: 8},
		"CheckStatus":            {ColumnCnName: "检查结果", Type: "string", Sort: 9},
	}

	var columns []settingModel.CheckedColumnValueItem
	for columnName, column := range tableColumns {
		column.ColumnName = columnName
		columns = append(columns, column)
	}

	sort.SliceStable(columns, func(i, j int) bool {
		return columns[i].Sort < columns[j].Sort
	})

	//处理数据
	excel := xlsx.NewFile()
	sheet, err := excel.AddSheet("sheet1")
	if err != nil {
		return err
	}

	BuildExcelBaseHeader(sheet, columns)
	BuildExcelMainData(sheet, columns, records)
	return SaveExcelFile(excel, exportFile)
}

func ExportVehicleCheckAbnormalResult(records []map[string]interface{}, exportFile exportModel.ExportFile) error {
	//获取用户设置的字段
	var tableColumns = map[string]settingModel.CheckedColumnValueItem{
		"Index":                  {ColumnCnName: "序号", Type: "index", Sort: 1},
		"Code":                   {ColumnCnName: "异常编号", Type: "string", Sort: 2},
		"License":                {ColumnCnName: "车牌", Type: "string", Sort: 3},
		"Corporation":            {ColumnCnName: "车辆所属机构", Type: "string", Sort: 4},
		"PlanScheduleStaffNames": {ColumnCnName: "排班司机", Type: "string", Sort: 5},
		"StaffName":              {ColumnCnName: "提交人", Type: "string", Sort: 6},
		"Occupation":             {ColumnCnName: "提交人岗位", Type: "string", Sort: 7},
		"CreatedAt":              {ColumnCnName: "提交时间", Type: "string", Sort: 8},
		"CheckType":              {ColumnCnName: "提交类型", Type: "string", Sort: 9},
		"AbLabelCount":           {ColumnCnName: "异常标签数", Type: "number", Sort: 10},
		"DealLabelCount":         {ColumnCnName: "已处理标签数", Type: "number", Sort: 11},
		"DealStatus":             {ColumnCnName: "处理状态", Type: "string", Sort: 12},
		"AllowOperation":         {ColumnCnName: "允许运营", Type: "string", Sort: 13},
	}

	var columns []settingModel.CheckedColumnValueItem
	for columnName, column := range tableColumns {
		column.ColumnName = columnName
		columns = append(columns, column)
	}

	sort.SliceStable(columns, func(i, j int) bool {
		return columns[i].Sort < columns[j].Sort
	})

	//处理数据
	excel := xlsx.NewFile()
	sheet, err := excel.AddSheet("sheet1")
	if err != nil {
		return err
	}

	BuildExcelBaseHeader(sheet, columns)
	BuildExcelMainData(sheet, columns, records)
	return SaveExcelFile(excel, exportFile)
}
