package export

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	exportModel "app/org/scs/erpv2/api/model/export"
	operationModel "app/org/scs/erpv2/api/model/operation"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"fmt"
	"github.com/tealeg/xlsx"
	"sort"
	"time"
)

func ExportLineMileageRangeReportFile(records []operationModel.LineVehicleMileageReport, exportFile exportModel.ExportFile, scene, tab string) error {
	var headers = map[string]settingModel.CheckedColumnValueItem{
		"Index":                         {ColumnCnName: "序号", Type: "index", Sort: 1},
		"ReportAt":                      {ColumnCnName: "日期", Type: "string", Sort: 2, Width: 25},
		"CorporationName":               {ColumnCnName: "所属机构", Type: "string", Sort: 3},
		"LineName":                      {ColumnCnName: "运营线路", Type: "string", Sort: 4},
		"DriverHasLineName":             {ColumnCnName: "司机归属线路", Type: "string", Sort: 5},
		"License":                       {ColumnCnName: "运营车辆", Type: "string", Sort: 6},
		"DriverName":                    {ColumnCnName: "运营司机", Type: "string", Sort: 7},
		"WorkDayCount":                  {ColumnCnName: "司机出勤天数", Type: "number", Sort: 8, Unit: 10, IsSum: true, Precision: 2},
		"VehicleWorkDayCount":           {ColumnCnName: "车辆出勤天数", Type: "number", Sort: 9, Unit: 10, IsSum: true, Precision: 2},
		"VehicleLength":                 {ColumnCnName: "车长", Type: "number", Sort: 10, Unit: 1000, IsSum: false, Precision: 3},
		"MainRunLineName":               {ColumnCnName: "主运营线路", Type: "string", Sort: 11},
		"VehicleModelStr":               {ColumnCnName: "车型", Type: "string", Sort: 12},
		"FullWorkDay":                   {ColumnCnName: "全班天数", Type: "number", Sort: 13, Unit: 10, IsSum: true, Precision: 2},
		"HalfWorkDay":                   {ColumnCnName: "半班天数", Type: "number", Sort: 14, Unit: 10, IsSum: true, Precision: 2},
		"MotorWorkDay":                  {ColumnCnName: "小机动天数", Type: "number", Sort: 15, Unit: 10, IsSum: true, Precision: 2},
		"MotorBigWorkDay":               {ColumnCnName: "大机动天数", Type: "number", Sort: 16, Unit: 10, IsSum: true, Precision: 2},
		"FullRatedMileage":              {ColumnCnName: "全程核定单圈公里", Type: "number", Sort: 17, Unit: 1000, IsSum: true, Precision: 3},
		"FullPlanCircle":                {ColumnCnName: "全程计划圈次", Type: "number", Sort: 18, Unit: 10, IsSum: true, Precision: 2},
		"FullDoneCircle":                {ColumnCnName: "全程实际完成圈次", Type: "number", Sort: 19, Unit: 10, IsSum: true, Precision: 2},
		"RangeRatedMileage":             {ColumnCnName: "区间核定单圈公里", Type: "number", Sort: 20, Unit: 1000, IsSum: true, Precision: 3},
		"RangePlanCircle":               {ColumnCnName: "区间计划圈次", Type: "number", Sort: 21, Unit: 10, IsSum: true, Precision: 2},
		"RangeDoneCircle":               {ColumnCnName: "区间实际完成圈次", Type: "number", Sort: 22, Unit: 10, IsSum: true, Precision: 2},
		"FixVehicleCircle":              {ColumnCnName: "修车圈次", Type: "number", Sort: 23, Unit: 10, IsSum: true, Precision: 2},
		"CasualLeaveCircle":             {ColumnCnName: "事假圈次", Type: "number", Sort: 24, Unit: 10, IsSum: true, Precision: 2},
		"AccidentDisputeCircle":         {ColumnCnName: "事故圈次", Type: "number", Sort: 25, Unit: 10, IsSum: true, Precision: 2},
		"AnnualReviewCircle":            {ColumnCnName: "年审圈次", Type: "number", Sort: 26, Unit: 10, IsSum: true, Precision: 2},
		"OfficialWorkCircle":            {ColumnCnName: "公务出勤圈次", Type: "number", Sort: 27, Unit: 10, IsSum: true, Precision: 2},
		"CharterBusCircle":              {ColumnCnName: "包车圈次", Type: "number", Sort: 28, Unit: 10, IsSum: true, Precision: 2},
		"SickLeaveCircle":               {ColumnCnName: "病假圈次", Type: "number", Sort: 29, Unit: 10, IsSum: true, Precision: 2},
		"AnnualLeaveCircle":             {ColumnCnName: "年休假圈次", Type: "number", Sort: 30, Unit: 10, IsSum: true, Precision: 2},
		"TrafficJamCircle":              {ColumnCnName: "堵车圈次", Type: "number", Sort: 31, Unit: 10, IsSum: true, Precision: 2},
		"RestCircle":                    {ColumnCnName: "疗休养圈次", Type: "number", Sort: 32, Unit: 10, IsSum: true, Precision: 2},
		"MaintenanceCircle":             {ColumnCnName: "保养圈次", Type: "number", Sort: 33, Unit: 10, IsSum: true, Precision: 2},
		"AddGasCircle":                  {ColumnCnName: "加气圈次", Type: "number", Sort: 34, Unit: 10, IsSum: true, Precision: 2},
		"NightBefore22WorkTimeLength":   {ColumnCnName: "22点前夜班时长", Type: "number", Sort: 35, Unit: 3600, IsSum: true, Precision: 2},
		"NightAfter22WorkTimeLength":    {ColumnCnName: "22点后夜班时长", Type: "number", Sort: 36, Unit: 3600, IsSum: true, Precision: 2},
		"NightTotalWorkTimeLength":      {ColumnCnName: "夜班总时长", Type: "number", Sort: 37, Unit: 3600, IsSum: true, Precision: 2},
		"NightAddWorkTimeLength":        {ColumnCnName: "夜班加班时长", Type: "number", Sort: 38, Unit: 3600, IsSum: true, Precision: 2},
		"DelayInParkingTimeLength":      {ColumnCnName: "延迟进场时长", Type: "number", Sort: 39, Unit: 3600, IsSum: true, Precision: 2},
		"CircleMileage":                 {ColumnCnName: "圈次公里", Type: "number", Sort: 40, Unit: 1000, IsSum: true, Precision: 3},
		"StopWorkRatedMileage":          {ColumnCnName: "自定义核定公里", Type: "number", Sort: 41, Unit: 1000, IsSum: true, Precision: 3},
		"FullInOutDepotMileage":         {ColumnCnName: "全程进出场公里 ", Type: "number", Sort: 42, Unit: 1000, IsSum: true, Precision: 3},
		"FullAssistantMileage":          {ColumnCnName: "全程辅助公里 ", Type: "number", Sort: 43, Unit: 1000, IsSum: true, Precision: 3},
		"RangeInOutDepotMileage":        {ColumnCnName: "区间进出场公里", Type: "number", Sort: 44, Unit: 1000, IsSum: true, Precision: 3},
		"RangeAssistantMileage":         {ColumnCnName: "区间辅助公里", Type: "number", Sort: 45, Unit: 1000, IsSum: true, Precision: 3},
		"CharterBusMileage":             {ColumnCnName: "包车公里 ", Type: "number", Sort: 46, Unit: 1000, IsSum: true, Precision: 3},
		"TrafficJamMileage":             {ColumnCnName: "堵车公里 ", Type: "number", Sort: 47, Unit: 1000, IsSum: true, Precision: 3},
		"AnnualReviewMileage":           {ColumnCnName: "年审公里 ", Type: "number", Sort: 48, Unit: 1000, IsSum: true, Precision: 3},
		"FixVehicleMileage":             {ColumnCnName: "修车公里 ", Type: "number", Sort: 49, Unit: 1000, IsSum: true, Precision: 3},
		"TotalMileage":                  {ColumnCnName: "合计公里 ", Type: "number", Sort: 50, Unit: 1000, IsSum: true, Precision: 3},
		"CasualLeaveTimes":              {ColumnCnName: "事假次数", Type: "number", Sort: 51, Unit: 0, IsSum: true},
		"CasualLeaveDay":                {ColumnCnName: "事假天数", Type: "number", Sort: 52, Unit: 10, IsSum: true, Precision: 2},
		"SickLeaveTimeDay":              {ColumnCnName: "病假天数", Type: "number", Sort: 53, Unit: 10, IsSum: true, Precision: 2},
		"CasualLeaveFirstLastPlanTimes": {ColumnCnName: "事假首末班次数", Type: "number", Sort: 54, Unit: 0, IsSum: true},
		"OfficialWorkDay":               {ColumnCnName: "公务出勤天数", Type: "number", Sort: 55, Unit: 10, IsSum: true, Precision: 2},
		"RestTimeDay":                   {ColumnCnName: "疗休养天数", Type: "number", Sort: 56, Unit: 10, IsSum: true, Precision: 2},
		"AnnualLeaveDay":                {ColumnCnName: "年休假天数", Type: "number", Sort: 57, Unit: 10, IsSum: true, Precision: 2},
		"HolidayDay":                    {ColumnCnName: "节假日天数", Type: "number", Sort: 58, Unit: 10, IsSum: true, Precision: 2},
		"FollowVehicleDay":              {ColumnCnName: "跟车天数", Type: "number", Sort: 59, Unit: 10, IsSum: true, Precision: 2},
		"FullRatedWorkTimeLength":       {ColumnCnName: "全程核定单圈岗上时长", Type: "number", Sort: 60, Unit: 3600, IsSum: true, Precision: 2},
		"FullRatedNotWorkTimeLength":    {ColumnCnName: "全程核定单圈岗下时长", Type: "number", Sort: 61, Unit: 3600, IsSum: true, Precision: 2},
		"FullCircleWorkTimeLength":      {ColumnCnName: "全程圈次岗上时长", Type: "number", Sort: 62, Unit: 3600, IsSum: true, Precision: 2},
		"FullCircleNotWorkTimeLength":   {ColumnCnName: "全程圈次岗下时长", Type: "number", Sort: 63, Unit: 3600, IsSum: true, Precision: 2},
		"FullInOutDepotTime":            {ColumnCnName: "全程进出场时长", Type: "number", Sort: 64, Unit: 3600, IsSum: true, Precision: 2},
		"FullAssistantTime":             {ColumnCnName: "全程辅助时长", Type: "number", Sort: 65, Unit: 3600, IsSum: true, Precision: 2},
		"FullStopWorkTimeLength":        {ColumnCnName: "全程自定义岗上时长", Type: "number", Sort: 66, Unit: 3600, IsSum: true, Precision: 2},
		"FullStopNotWorkTimeLength":     {ColumnCnName: "全程自定义岗下时长", Type: "number", Sort: 67, Unit: 3600, IsSum: true, Precision: 2},
		"RangeRatedWorkTimeLength":      {ColumnCnName: "区间核定单圈岗上时长", Type: "number", Sort: 68, Unit: 3600, IsSum: true, Precision: 2},
		"RangeRatedNotWorkTimeLength":   {ColumnCnName: "区间核定单圈岗下时长", Type: "number", Sort: 69, Unit: 3600, IsSum: true, Precision: 2},
		"RangeCircleWorkTimeLength":     {ColumnCnName: "区间圈次岗上时长", Type: "number", Sort: 70, Unit: 3600, IsSum: true, Precision: 2},
		"RangeCircleNotWorkTimeLength":  {ColumnCnName: "区间圈次岗下时长", Type: "number", Sort: 71, Unit: 3600, IsSum: true, Precision: 2},
		"RangeInOutDepotTime":           {ColumnCnName: "区间进出场时长", Type: "number", Sort: 72, Unit: 3600, IsSum: true, Precision: 2},
		"RangeAssistantTime":            {ColumnCnName: "区间辅助时长", Type: "number", Sort: 73, Unit: 3600, IsSum: true, Precision: 2},
		"RangeStopWorkTimeLength":       {ColumnCnName: "区间自定义岗上时长", Type: "number", Sort: 74, Unit: 3600, IsSum: true, Precision: 2},
		"RangeStopNotWorkTimeLength":    {ColumnCnName: "区间自定义岗下时长", Type: "number", Sort: 75, Unit: 3600, IsSum: true, Precision: 2},
		"AddWorkTimeLength":             {ColumnCnName: "加班时长", Type: "number", Sort: 76, Unit: 3600, IsSum: true, Precision: 2},
		"AddWorkWorkTimeLength":         {ColumnCnName: "加班岗上时长", Type: "number", Sort: 77, Unit: 3600, IsSum: true, Precision: 2},
		"AddWorkNotWorkTimeLength":      {ColumnCnName: "加班岗下时长", Type: "number", Sort: 78, Unit: 3600, IsSum: true, Precision: 2},
		"AddWorkTimes":                  {ColumnCnName: "不足2.5小时次数", Type: "number", Sort: 79, Unit: 0, IsSum: true},
		"FullDayAddWorkTimes":           {ColumnCnName: "超过半天不足一天次数", Type: "number", Sort: 80, Unit: 0, IsSum: true},
		"HalfDayAddWorkTimes":           {ColumnCnName: "超过2.5小时不足半天次数", Type: "number", Sort: 81, Unit: 0, IsSum: true},
		"FixVehicleWorkTimeLength":      {ColumnCnName: "修车岗上时长", Type: "number", Sort: 82, Unit: 3600, IsSum: true, Precision: 2},
		"FixVehicleTimeLength":          {ColumnCnName: "修车岗下时长", Type: "number", Sort: 83, Unit: 3600, IsSum: true, Precision: 2},
		"CasualLeaveTimeLength":         {ColumnCnName: "事假时长", Type: "number", Sort: 84, Unit: 3600, IsSum: true, Precision: 2},
		"AccidentDisputeTimeLength":     {ColumnCnName: "事故纠纷时长", Type: "number", Sort: 85, Unit: 3600, IsSum: true, Precision: 2},
		"AnnualReviewTimeLength":        {ColumnCnName: "年审时长", Type: "number", Sort: 86, Unit: 3600, IsSum: true, Precision: 2},
		"OfficialWorkTimeLength":        {ColumnCnName: "公务出勤时长", Type: "number", Sort: 87, Unit: 3600, IsSum: true, Precision: 2},
		"CharterBusTimeLength":          {ColumnCnName: "包车时长", Type: "number", Sort: 88, Unit: 3600, IsSum: true, Precision: 2},
		"SickLeaveTimeLength":           {ColumnCnName: "病假时长", Type: "number", Sort: 89, Unit: 3600, IsSum: true, Precision: 2},
		"AnnualLeaveTimeLength":         {ColumnCnName: "年休假时长", Type: "number", Sort: 90, Unit: 3600, IsSum: true, Precision: 2},
		"TrafficJamTimeLength":          {ColumnCnName: "堵车时长", Type: "number", Sort: 91, Unit: 3600, IsSum: true, Precision: 2},
		"RestTimeLength":                {ColumnCnName: "疗休养时长", Type: "number", Sort: 92, Unit: 3600, IsSum: true, Precision: 2},
		"TotalWorkTimeLength":           {ColumnCnName: "合计岗上时长", Type: "number", Sort: 93, Unit: 3600, IsSum: true, Precision: 2},
		"TotalNotWorkTimeLength":        {ColumnCnName: "合计岗下时长", Type: "number", Sort: 94, Unit: 3600, IsSum: true, Precision: 2},
		"TotalTimeLength":               {ColumnCnName: "总时长", Type: "number", Sort: 95, Unit: 3600, IsSum: true, Precision: 2},
		"More":                          {ColumnCnName: "备注", Type: "string", Sort: 96, Width: 25},
	}
	var hideColumns = []string{"FrequencyIndex", "FrequencyType", "WorkTypeStr"}
	if tab == "line" {
		hideColumns = append(hideColumns, "DriverHasLineName", "License", "DriverName", "MainRunLineName", "VehicleModelStr")
	}

	if tab == "driver" {
		hideColumns = append(hideColumns, "License", "VehicleWorkDayCount", "VehicleLength", "VehicleModelStr")
	}

	if tab == "vehicle" {
		hideColumns = append(hideColumns, "DriverHasLineName", "DriverName", "WorkDayCount", "MainRunLineName")
	}

	var columns []settingModel.CheckedColumnValueItem

	//获取用户的配置
	setting := (&settingModel.ColumnSetting{}).FirstByUserId(exportFile.OpUserId, exportFile.Scene)
	if setting.Id > 0 {
		var settingItem settingModel.ColumnSettingItem
		err := json.Unmarshal(setting.SettingItem, &settingItem)
		if err != nil {
			log.ErrorFields("ExportLineDriverWork SettingItem json.Unmarshal error", map[string]interface{}{"err": err})
		} else {
			firstColumn := headers["Index"]
			firstColumn.ColumnName = "Index"
			firstColumn.Sort = 0
			columns = append(columns, firstColumn)
			for _, settingItemColumn := range settingItem.ColumnAttr {
				if util.Include(settingItem.CheckedColumns, settingItemColumn.ColumnName) && !util.Include(hideColumns, settingItemColumn.ColumnName) {
					if _, ok := headers[settingItemColumn.ColumnName]; ok {
						column := headers[settingItemColumn.ColumnName]
						column.ColumnName = settingItemColumn.ColumnName
						column.Sort = settingItemColumn.Sort
						column.Color = settingItemColumn.Color
						columns = append(columns, column)
					} else {
						columns = append(columns, settingItemColumn)
					}
				}
			}
		}
	}

	if len(columns) == 0 {
		for columnName, column := range headers {
			if !util.Include(hideColumns, columnName) {
				column.ColumnName = columnName
				columns = append(columns, column)
			}
		}
	}

	sort.Slice(columns, func(i, j int) bool {
		return columns[i].Sort < columns[j].Sort
	})
	fmt.Printf("=======columns===============%+v,\n", columns)

	//处理数据
	var data []map[string]interface{}
	var sumData = make(map[string]interface{})
	sumData["Index"] = "合计"
	for i := range records {
		resultByte, err := json.Marshal(records[i])
		if err != nil {
			return err
		}
		var dataMap map[string]interface{}
		err = json.Unmarshal(resultByte, &dataMap)
		if err != nil {
			return err
		}
		if scene == "detail" {
			dataMap["ReportAt"] = time.Time(records[i].ReportAt).Format(model.DateFormat)
		} else {
			dataMap["ReportAt"] = fmt.Sprintf("%s-%s", time.Time(exportFile.StartAt).Format(model.DateFormat), time.Time(exportFile.EndAt).Format(model.DateFormat))
		}

		if records[i].VehicleId > 0 {
			if records[i].VehicleLength >= 6000 {
				dataMap["VehicleModelStr"] = util.VehicleModelMap[util.VehicleModelForBig]
			}
			if records[i].VehicleLength < 6000 && records[i].VehicleLength > 0 {
				dataMap["VehicleModelStr"] = util.VehicleModelMap[util.VehicleModelForMiddle]
			}
		}

		for j := range columns {
			if columns[j].IsSum {
				//合计
				t, _ := sumData[columns[j].ColumnName].(float64)
				n, _ := dataMap[columns[j].ColumnName].(float64)
				sumData[columns[j].ColumnName] = t + n
			}
		}

		data = append(data, dataMap)
	}
	excel := xlsx.NewFile()
	sheet, err := excel.AddSheet("sheet1")
	if err != nil {
		return err
	}
	//表头
	BuildExcelBaseHeader(sheet, columns)
	//数据
	BuildExcelMainData(sheet, columns, data)
	//求和
	BuildExcelMainData(sheet, ReBuildSumColumnStyle(columns), []map[string]interface{}{sumData})

	return SaveExcelFile(excel, exportFile)
}
