package service

import (
	"app/org/scs/erpv2/api/database"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	operationModel "app/org/scs/erpv2/api/model/operation"
	settingModel "app/org/scs/erpv2/api/model/setting"
	ticketModel "app/org/scs/erpv2/api/model/ticket"
	protoLine "app/org/scs/erpv2/api/proto/rpc/oetline"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"fmt"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"strings"
	"time"
)

var IsProcessingCalcCorporationLineIcReport = false

// 公司线路人次营收报表计算
type CalcCorporationLineIcReport struct {
	TopCorporationId int64     `json:"TopCorporationId"`
	CorporationId    int64     `json:"CorporationId"` //子机构
	ReportMonth      time.Time //报表所属月份
	StartAt          time.Time //数据开始时间
	EndAt            time.Time //数据结束时间
	Data             map[int64]ticketModel.CorporationLineIncomeReport
	model.OpUser
}

func (c *CalcCorporationLineIcReport) Process() {
	db, err := database.InitIcMysqlConnect()
	if err != nil {
		log.ErrorFields(" database.InitIcMysqlConnect error", map[string]interface{}{"error": err})
		return
	}

	//删除历史数据
	err = (&ticketModel.CorporationLineIncomeReport{}).DeleteBy(c.ReportMonth)
	if err != nil {
		log.ErrorFields("CalcCorporationLineIcReport.DeleteBy error", map[string]interface{}{"error": err})
		return
	}
	IsProcessingCalcCorporationLineIcReport = true
	//获取所有线路
	oetLines := rpc.GetLinesWithTopCorporationId(context.Background(), c.TopCorporationId, 1)
	for i := range oetLines {
		c.Data = make(map[int64]ticketModel.CorporationLineIncomeReport)
		c.calcLineData(db, oetLines[i])
	}
	IsProcessingCalcCorporationLineIcReport = false
}

type thirdTableColumn struct {
	TxnLineId  string `json:"TXN_LINE_ID" gorm:"column:TXN_LINE_ID"`   //线路编号
	TxnArrayId string `json:"TXN_ARRAY_ID" gorm:"column:TXN_ARRAY_ID"` //车队编号
	Value      int64  `json:"Value" gorm:"column:value"`               //值
}

func (c *CalcCorporationLineIcReport) calcLineData(db *gorm.DB, oetLine *protoLine.OetLineItem) {
	c.queryTicketMoney(oetLine.Id)
	c.queryMileageCircle(oetLine.Id)
	if oetLine.PosLineNo != "" {
		thirdLines := strings.Split(oetLine.PosLineNo, ",")
		for j := range thirdLines {
			thirdLineNo := thirdLines[j]
			c.queryMobilePaymentPeople(db, thirdLineNo)
			c.queryNormalIcPeople(db, thirdLineNo)
			c.queryHalfOlderIcPeople(db, thirdLineNo)
			c.queryFreeOlderIcPeople(db, thirdLineNo)
			c.queryAdultSpecialIcPeople(db, thirdLineNo)
			c.queryFreeChangePeople(db, thirdLineNo)
			c.queryMobilePaymentMoney(db, thirdLineNo)
			c.queryNormalIcMoney(db, thirdLineNo)
			c.queryHalfOlderIcMoney(db, thirdLineNo)
		}
	}

	for corporationId := range c.Data {
		var data = c.Data[corporationId]
		if corporationId == 0 {
			corporationId = oetLine.SubCorporationIds[0]
		}

		//查询补贴单价
		allowance := (&settingModel.LineAllowancePriceSetting{}).GetByLineIdAndMonth(corporationId, oetLine.Id, c.ReportMonth)
		data.Price = allowance.Price

		//计算票款人次
		if data.Price > 0 {
			data.CashPaymentPeople = decimal.NewFromInt(data.CashPaymentMoney).Div(decimal.NewFromInt(data.Price)).Ceil().IntPart()
		}

		//计算合计人次
		data.TotalPeople = data.CashPaymentPeople + data.MobilePaymentPeople + data.NormalIcPeople + data.HalfOlderIcPeople + data.FreeOlderIcPeople + data.AdultSpecialIcPeople

		//计算合计营收
		data.TotalOperationMoney = data.CashPaymentMoney + data.MobilePaymentMoney + data.NormalIcMoney + data.HalfOlderIcMoney

		//普通IC卡补贴
		data.NormalIcAllowance = decimal.NewFromInt(data.NormalIcMoney).Mul(decimal.NewFromFloat(0.4)).Div(decimal.NewFromFloat(0.6)).Ceil().IntPart()

		//半价老年卡补贴
		data.HalfOlderIcAllowance = data.HalfOlderIcMoney

		//免费老年卡补贴
		data.FreeOlderIcAllowance = (data.FreeOlderIcPeople + data.AdultSpecialIcPeople) * data.Price

		//免费换乘补贴
		data.FreeChangeAllowance = data.FreeChangePeople * data.Price

		//补贴合计
		data.TotalAllowance = data.NormalIcAllowance + data.HalfOlderIcAllowance + data.FreeOlderIcAllowance + data.FreeChangeAllowance

		//总金额
		data.TotalMoney = data.TotalOperationMoney + data.TotalAllowance

		data.Corporations.Build(corporationId)
		data.CorporationId, data.CorporationName = data.Corporations.GetCorporation()
		data.LineId = oetLine.Id
		data.LineName = oetLine.Name
		data.ReportAt = model.LocalTime(c.ReportMonth)
		data.StartAt = model.LocalTime(c.StartAt)
		data.EndAt = model.LocalTime(c.EndAt)
		data.OpUser = c.OpUser

		err := data.Create()
		if err != nil {
			log.ErrorFields("calcLineData Create data error", map[string]interface{}{"error": err})
		}
	}
}

func (c *CalcCorporationLineIcReport) calcCharterBusData() {
	moneys := (&ticketModel.CharteredBusIncome{}).GetBySum(c.ReportMonth)
	for i := range moneys {
		var data = ticketModel.CorporationLineIncomeReport{
			CashPaymentPeople:   moneys[i].TotalPeople,
			TotalPeople:         moneys[i].TotalPeople,
			CashPaymentMoney:    moneys[i].TotalMoney,
			TotalOperationMoney: moneys[i].TotalMoney,
			TotalMoney:          moneys[i].TotalMoney,
		}

		data.Corporations.Build(moneys[i].CorporationId)
		data.CorporationId, data.CorporationName = data.Corporations.GetCorporation()
		data.LineName = "包车"
		data.ReportAt = model.LocalTime(c.ReportMonth)
		data.StartAt = model.LocalTime(c.StartAt)
		data.EndAt = model.LocalTime(c.EndAt)
		data.OpUser = c.OpUser

		err := data.Create()
		if err != nil {
			log.ErrorFields("calcCharterBusData Create data error", map[string]interface{}{"error": err})
		}
	}
}

// 查询票务数据
func (c *CalcCorporationLineIcReport) queryMileageCircle(lineId int64) {
	//线路公里明细表
	lineMileageReports := (&operationModel.LineVehicleMileageReport{}).GetLineMileageAndCircleSum(util.StatusForFalse, nil, []int64{lineId}, c.StartAt, c.EndAt)
	for i := range lineMileageReports {
		if _, ok := c.Data[lineMileageReports[i].CorporationId]; ok {
			data := c.Data[lineMileageReports[i].CorporationId]
			data.TotalCircle += lineMileageReports[i].TotalCircle
			data.TotalMileage += lineMileageReports[i].TotalMileage
			c.Data[lineMileageReports[i].CorporationId] = data
		} else {
			c.Data[lineMileageReports[i].CorporationId] = ticketModel.CorporationLineIncomeReport{
				TotalMileage: lineMileageReports[i].TotalMileage,
				TotalCircle:  lineMileageReports[i].TotalCircle,
			}
		}
	}

	//班制外加班明细表
	outAddWorkMileageReports := (&operationModel.OutFrequencyAddWorkReport{}).GetLinesTotalMileage(util.StatusForFalse, nil, []int64{lineId}, c.StartAt, c.EndAt)
	for i := range outAddWorkMileageReports {
		if _, ok := c.Data[outAddWorkMileageReports[i].CorporationId]; ok {
			data := c.Data[outAddWorkMileageReports[i].CorporationId]
			data.TotalCircle += outAddWorkMileageReports[i].TotalCircle
			data.TotalMileage += outAddWorkMileageReports[i].TotalMileage
			c.Data[outAddWorkMileageReports[i].CorporationId] = data
		} else {
			c.Data[outAddWorkMileageReports[i].CorporationId] = ticketModel.CorporationLineIncomeReport{
				TotalMileage: outAddWorkMileageReports[i].TotalMileage,
				TotalCircle:  outAddWorkMileageReports[i].TotalCircle,
			}
		}
	}

	//定制线路表数据
	irregularLineReports := (&operationModel.IrregularLineReport{}).GetLinesTotalMileageAndCircle(util.StatusForFalse, nil, []int64{lineId}, c.StartAt, c.EndAt)
	for i := range irregularLineReports {
		if _, ok := c.Data[irregularLineReports[i].CorporationId]; ok {
			data := c.Data[irregularLineReports[i].CorporationId]
			data.TotalCircle += irregularLineReports[i].TotalCircle
			data.TotalMileage += irregularLineReports[i].TotalMileage
			c.Data[irregularLineReports[i].CorporationId] = data
		} else {
			c.Data[irregularLineReports[i].CorporationId] = ticketModel.CorporationLineIncomeReport{
				TotalMileage: irregularLineReports[i].TotalMileage,
				TotalCircle:  irregularLineReports[i].TotalCircle,
			}
		}
	}
}

// 查询票务数据
func (c *CalcCorporationLineIcReport) queryTicketMoney(lineId int64) {
	moneys := (&ticketModel.TicketCountMoneys{}).LineTicketMoney(lineId, c.StartAt, c.EndAt)

	for i := range moneys {
		if _, ok := c.Data[moneys[i].CorporationId]; ok {
			data := c.Data[moneys[i].CorporationId]
			data.CashPaymentMoney += moneys[i].TotalMoney
			c.Data[moneys[i].CorporationId] = data
		} else {
			c.Data[moneys[i].CorporationId] = ticketModel.CorporationLineIncomeReport{
				CashPaymentMoney: moneys[i].TotalMoney,
			}
		}
	}
}

func (c *CalcCorporationLineIcReport) commonWhere(thirdLineNo string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("SETT_DATE>=?", c.StartAt.Format("20060102")).
			Where("SETT_DATE<=?", c.EndAt.Format("20060102")).
			Where("TXN_MCHNT_ID = ?", "8000").
			Where("TXN_LINE_ID = ?", thirdLineNo)
	}
}
func (c *CalcCorporationLineIcReport) buildSumColumn(sumColumn string) []string {
	return []string{fmt.Sprintf("SUM(%s) as value", sumColumn), "TXN_LINE_ID", "TXN_ARRAY_ID"}
}

// 查询移动支付人次
func (c *CalcCorporationLineIcReport) queryMobilePaymentPeople(db *gorm.DB, thirdLineNo string) {
	//移动支付人次
	var cardRecords []thirdTableColumn
	var codeRecords []thirdTableColumn
	cardType := []string{"Y101", "Y102", "E100", "E101"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardRecords)
	codeQrId := []string{"84057505", "50023301", "********", "50023301", "50023301", "50023301", "50023301", "50023301", "50023301", "50011000", "50011000", "50023301", "50011000", "50023301", "50023301", "50011000", "50011000",
		"50011000", "50023301", "50011000", "50023301", "50023301", "50023301", "2002900", "3473320", "50011000", "1493350", "84087508", "84017501", "84247524"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeRecords)
	cardRecords = append(cardRecords, codeRecords...)

	for i := range cardRecords {
		arrayCode := cardRecords[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.MobilePaymentPeople += cardRecords[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				MobilePaymentPeople: cardRecords[i].Value,
			}
		}
	}
}

// 查询普通IC卡人次
func (c *CalcCorporationLineIcReport) queryNormalIcPeople(db *gorm.DB, thirdLineNo string) {
	//普通IC卡
	var cardRecords []thirdTableColumn
	var codeRecords []thirdTableColumn
	cardType := []string{"D130", "D101", "D1B1", "D1B4", "D1B0", "D1A4", "D1B3", "D1A9"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardRecords)

	codeQrId := []string{"84307530", "2113450", "84237523", "84057505"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeRecords)
	cardRecords = append(cardRecords, codeRecords...)

	for i := range cardRecords {
		arrayCode := cardRecords[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.NormalIcPeople += cardRecords[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				NormalIcPeople: cardRecords[i].Value,
			}
		}
	}
}

// 查询半价老年卡人次
func (c *CalcCorporationLineIcReport) queryHalfOlderIcPeople(db *gorm.DB, thirdLineNo string) {
	//半价老年卡
	var cardRecords []thirdTableColumn
	var codeRecords []thirdTableColumn
	cardType := []string{"D1A5", "E143"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardRecords)

	codeQrId := []string{"84437543"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeRecords)

	cardRecords = append(cardRecords, codeRecords...)

	for i := range cardRecords {
		arrayCode := cardRecords[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.HalfOlderIcPeople += cardRecords[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				HalfOlderIcPeople: cardRecords[i].Value,
			}
		}
	}
}

// 查询免费老年卡人次
func (c *CalcCorporationLineIcReport) queryFreeOlderIcPeople(db *gorm.DB, thirdLineNo string) {
	//免费老年卡
	var cardRecords []thirdTableColumn
	var codeRecords []thirdTableColumn
	cardType := []string{"D1B5", "E144"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardRecords)

	codeQrId := []string{"84447544"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeRecords)

	cardRecords = append(cardRecords, codeRecords...)

	for i := range cardRecords {
		arrayCode := cardRecords[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.FreeOlderIcPeople += cardRecords[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				FreeOlderIcPeople: cardRecords[i].Value,
			}
		}
	}
}

// 查询成人优待卡人次
func (c *CalcCorporationLineIcReport) queryAdultSpecialIcPeople(db *gorm.DB, thirdLineNo string) {
	//成人优待卡
	var cardRecords []thirdTableColumn
	var codeRecords []thirdTableColumn
	cardType := []string{"D1A3", "E145", "E146", "D1C6"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardRecords)

	codeQrId := []string{"84457545", "84327532", "84287528"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeRecords)

	cardRecords = append(cardRecords, codeRecords...)

	for i := range cardRecords {
		arrayCode := cardRecords[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.AdultSpecialIcPeople += cardRecords[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				AdultSpecialIcPeople: cardRecords[i].Value,
			}
		}
	}
}

// 查询免费换乘人次
func (c *CalcCorporationLineIcReport) queryFreeChangePeople(db *gorm.DB, thirdLineNo string) {
	//换乘人次
	var cardRecords []thirdTableColumn
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_PRE_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ('D1A5', 'D130', 'D1B1', 'D1B4') AND `ERR_CD` IN ('0000', 'C21013') ").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardRecords)
	for i := range cardRecords {
		arrayCode := cardRecords[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.FreeChangePeople += cardRecords[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				FreeChangePeople: cardRecords[i].Value,
			}
		}
	}
}

// 查询移动支付金额
func (c *CalcCorporationLineIcReport) queryMobilePaymentMoney(db *gorm.DB, thirdLineNo string) {
	//移动支付
	var cardMoneys []thirdTableColumn
	var codeMoneys []thirdTableColumn
	cardType := []string{"Y101", "Y102", "E100", "E101"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_AMT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardMoneys)

	codeQrId := []string{"84057505", "50023301", "********", "50023301", "50023301", "50023301", "50023301", "50023301", "50023301", "50011000", "50011000", "50023301", "50011000", "50023301", "50023301", "50011000", "50011000",
		"50011000", "50023301", "50011000", "50023301", "50023301", "50023301", "2002900", "3473320", "50011000", "1493350", "84087508", "84017501", "84247524"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_AMT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeMoneys)
	cardMoneys = append(cardMoneys, codeMoneys...)
	for i := range cardMoneys {
		arrayCode := cardMoneys[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.MobilePaymentMoney += cardMoneys[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				MobilePaymentMoney: cardMoneys[i].Value,
			}
		}
	}
}

// 查询普通IC卡金额
func (c *CalcCorporationLineIcReport) queryNormalIcMoney(db *gorm.DB, thirdLineNo string) {
	//普通IC卡
	var cardMoneys []thirdTableColumn
	var codeMoneys []thirdTableColumn
	cardType := []string{"D130", "D101", "D1B1", "D1B4", "D1B0", "D1A4", "D1B3", "D1A9"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_AMT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardMoneys)

	codeQrId := []string{"84307530", "2113450", "84237523", "84057505"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_AMT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeMoneys)

	cardMoneys = append(cardMoneys, codeMoneys...)

	for i := range cardMoneys {
		arrayCode := cardMoneys[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.NormalIcMoney += cardMoneys[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				NormalIcMoney: cardMoneys[i].Value,
			}
		}
	}
}

// 查询半价老年卡金额
func (c *CalcCorporationLineIcReport) queryHalfOlderIcMoney(db *gorm.DB, thirdLineNo string) {
	//半价老年卡
	var cardMoneys []thirdTableColumn
	var codeMoneys []thirdTableColumn
	cardType := []string{"D1A5", "E143"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_AMT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardMoneys)

	codeQrId := []string{"84437543"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_AMT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeMoneys)

	cardMoneys = append(cardMoneys, codeMoneys...)

	for i := range cardMoneys {
		arrayCode := cardMoneys[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.HalfOlderIcMoney += cardMoneys[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				HalfOlderIcMoney: cardMoneys[i].Value,
			}
		}
	}
}
