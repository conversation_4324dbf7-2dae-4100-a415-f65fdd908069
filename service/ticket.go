package service

import (
	"app/org/scs/erpv2/api/database"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	operationModel "app/org/scs/erpv2/api/model/operation"
	settingModel "app/org/scs/erpv2/api/model/setting"
	ticketModel "app/org/scs/erpv2/api/model/ticket"
	"app/org/scs/erpv2/api/model/ticket/mixReport"
	protoLine "app/org/scs/erpv2/api/proto/rpc/oetline"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var IsProcessingCalcCorporationLineIcReport = false

// CalcCorporationLineIcReport 公司线路人次营收报表计算
type CalcCorporationLineIcReport struct {
	TopCorporationId int64     `json:"TopCorporationId"`
	CorporationId    int64     `json:"CorporationId"` //子机构
	ReportMonth      time.Time //报表所属月份
	StartAt          time.Time //数据开始时间
	EndAt            time.Time //数据结束时间 2025-04-05 00:00:00
	EndAt2           time.Time //数据结束时间 2025-04-05 23:59:59
	Data             map[int64]ticketModel.CorporationLineIncomeReport
	Data2            map[string]ticketModel.CorporationLineIncomeReport
	model.OpUser
}

func (c *CalcCorporationLineIcReport) Process() {
	db, err := database.InitIcMysqlConnect()
	if err != nil {
		log.ErrorFields(" database.InitIcMysqlConnect error", map[string]interface{}{"error": err})
		return
	}

	//删除历史数据
	err = (&ticketModel.CorporationLineIncomeReport{}).DeleteBy(c.ReportMonth)
	if err != nil {
		log.ErrorFields("CalcCorporationLineIcReport.DeleteBy error", map[string]interface{}{"error": err})
		return
	}
	IsProcessingCalcCorporationLineIcReport = true
	//
	settMap := c.CompleteSettlement(db)
	freeChangeMap := c.CompleteFreeChangePeople(db)
	lineArrayMap := c.LineArrayMap(db)
	//获取所有线路
	oetLines := rpc.GetLinesWithTopCorporationId(context.Background(), c.TopCorporationId, 1)
	for i := range oetLines {
		fmt.Println(fmt.Sprintf("============共%d条线路,计算第%d条线路:%s====================", len(oetLines), i+1, oetLines[i].Name))
		c.Data = make(map[int64]ticketModel.CorporationLineIncomeReport)
		c.Data2 = make(map[string]ticketModel.CorporationLineIncomeReport)
		c.calcLineData(db, oetLines[i], settMap, freeChangeMap, lineArrayMap)
	}
	IsProcessingCalcCorporationLineIcReport = false
}

type thirdTableColumn struct {
	TxnLineId  string `json:"TXN_LINE_ID" gorm:"column:TXN_LINE_ID"`   //线路编号
	TxnArrayId string `json:"TXN_ARRAY_ID" gorm:"column:TXN_ARRAY_ID"` //车队编号
	Value      int64  `json:"Value" gorm:"column:value"`               //值
}

func (c *CalcCorporationLineIcReport) calcLineData(db *gorm.DB, oetLine *protoLine.OetLineItem, settMap map[string][]thirdNewTableColumn, freeChangeMap map[string]int64, lineArrayMap map[string]string) {
	c.queryTicketMoney(oetLine.Id)
	c.queryMileageCircle(oetLine.Id)
	if oetLine.PosLineNo != "" {
		thirdLines := strings.Split(oetLine.PosLineNo, ",")
		for j := range thirdLines {
			thirdLineNo := thirdLines[j]
			key := fmt.Sprintf("%d_%d", util.ThirdBranchToOetCorporationId[lineArrayMap[thirdLineNo]], oetLine.Id)
			//c.queryMobilePaymentPeople(db, thirdLineNo)
			//c.queryNormalIcPeople(db, thirdLineNo)
			//c.queryHalfOlderIcPeople(db, thirdLineNo)
			//c.queryFreeOlderIcPeople(db, thirdLineNo)
			//c.queryAdultSpecialIcPeople(db, thirdLineNo)
			//c.queryFreeChangePeople(db, thirdLineNo, oetLine.Id)
			//c.queryMobilePaymentMoney(db, thirdLineNo)
			//c.queryNormalIcMoney(db, thirdLineNo)
			//c.queryHalfOlderIcMoney(db, thirdLineNo)
			c.queryCompleteMoney(settMap[thirdLineNo], key)
			fmt.Println("==========================freeChangePeople:", freeChangeMap[thirdLineNo], "==========================")
			c.SetFreeChangePeople(freeChangeMap[thirdLineNo], key)
		}
	}

	for key := range c.Data2 {
		corporationIdAndLineId := strings.Split(key, "_")
		var corporationId int64
		if len(corporationIdAndLineId) == 2 {
			corporationId, _ = strconv.ParseInt(corporationIdAndLineId[0], 10, 64)
		}
		if corporationId == 0 {
			corporationId = oetLine.SubCorporationIds[0]
		}
		//corporationId = oetLine.SubCorporationIds[0]
		var data = c.Data2[key]
		//查询补贴单价
		allowance := (&settingModel.LineAllowancePriceSetting{}).GetByLineIdAndMonth(corporationId, oetLine.Id, c.ReportMonth)
		data.Price = allowance.Price

		//计算票款人次
		if data.Price > 0 {
			data.CashPaymentPeople = decimal.NewFromInt(data.CashPaymentMoney).Div(decimal.NewFromInt(data.Price)).Ceil().IntPart()
		}

		//计算合计人次
		data.TotalPeople = data.CashPaymentPeople + data.MobilePaymentPeople + data.NormalIcPeople + data.HalfOlderIcPeople + data.FreeOlderIcPeople + data.AdultSpecialIcPeople
		//fmt.Println("票款人数：", data.CashPaymentPeople)
		//fmt.Println("移动人次：", data.MobilePaymentPeople)
		//fmt.Println("普通ic卡人次：", data.NormalIcPeople)
		//fmt.Println("半价卡次：", data.TotalPeople)
		//fmt.Println("免费人次：", data.TotalPeople)
		//fmt.Println("成人优待人次：", data.TotalPeople)
		//fmt.Println("免费换乘：", data.FreeChangePeople)
		//计算合计营收
		data.TotalOperationMoney = data.CashPaymentMoney + data.MobilePaymentMoney + data.NormalIcMoney + data.HalfOlderIcMoney

		//普通IC卡补贴
		data.NormalIcAllowance = decimal.NewFromInt(data.NormalIcMoney).Mul(decimal.NewFromFloat(0.4)).Div(decimal.NewFromFloat(0.6)).Ceil().IntPart()

		//半价老年卡补贴
		data.HalfOlderIcAllowance = data.HalfOlderIcMoney

		//免费老年卡补贴
		data.FreeOlderIcAllowance = (data.FreeOlderIcPeople + data.AdultSpecialIcPeople) * data.Price

		//免费换乘补贴
		data.FreeChangeAllowance = data.FreeChangePeople * data.Price

		//补贴合计
		data.TotalAllowance = data.NormalIcAllowance + data.HalfOlderIcAllowance + data.FreeOlderIcAllowance + data.FreeChangeAllowance

		//总金额
		data.TotalMoney = data.TotalOperationMoney + data.TotalAllowance

		data.Corporations.Build(corporationId)
		data.CorporationId, data.CorporationName = data.Corporations.GetCorporation()
		data.LineId = oetLine.Id
		data.LineName = oetLine.Name
		data.ReportAt = model.LocalTime(c.ReportMonth)
		data.StartAt = model.LocalTime(c.StartAt)
		data.EndAt = model.LocalTime(c.EndAt)
		data.OpUser = c.OpUser
		oldData := (&ticketModel.CorporationLineIncomeReport{}).Find(data.CorporationId, data.LineId, c.ReportMonth)
		if oldData.Id != 0 {
			data.Id = oldData.Id
		}
		if data.Id == 0 {
			err := data.Create()
			if err != nil {
				log.ErrorFields("calcLineData Create data error", map[string]interface{}{"error": err})
			}
		} else {
			err := data.Update()
			if err != nil {
				log.ErrorFields("calcLineData Update data error", map[string]interface{}{"error": err})
			}
		}
	}
}

func (c *CalcCorporationLineIcReport) calcCharterBusData() {
	moneys := (&ticketModel.CharteredBusIncome{}).GetBySum(c.ReportMonth)
	for i := range moneys {
		var data = ticketModel.CorporationLineIncomeReport{
			CashPaymentPeople:   moneys[i].TotalPeople,
			TotalPeople:         moneys[i].TotalPeople,
			CashPaymentMoney:    moneys[i].TotalMoney,
			TotalOperationMoney: moneys[i].TotalMoney,
			TotalMoney:          moneys[i].TotalMoney,
		}

		data.Corporations.Build(moneys[i].CorporationId)
		data.CorporationId, data.CorporationName = data.Corporations.GetCorporation()
		data.LineName = "包车"
		data.ReportAt = model.LocalTime(c.ReportMonth)
		data.StartAt = model.LocalTime(c.StartAt)
		data.EndAt = model.LocalTime(c.EndAt)
		data.OpUser = c.OpUser

		err := data.Create()
		if err != nil {
			log.ErrorFields("calcCharterBusData Create data error", map[string]interface{}{"error": err})
		}
	}
}

// 查询票务数据
func (c *CalcCorporationLineIcReport) queryMileageCircle(lineId int64) {
	//线路公里明细表
	lineMileageReports := (&operationModel.LineVehicleMileageReport{}).GetLineMileageAndCircleSum(util.StatusForFalse, nil, []int64{lineId}, c.StartAt, c.EndAt)
	for i := range lineMileageReports {
		key := fmt.Sprintf("%d_%d", lineMileageReports[i].CorporationId, lineMileageReports[i].LineId)

		if _, ok := c.Data2[key]; ok {
			data := c.Data2[key]
			data.TotalCircle += lineMileageReports[i].TotalCircle
			data.TotalMileage += lineMileageReports[i].TotalMileage
			c.Data2[key] = data
		} else {
			c.Data2[key] = ticketModel.CorporationLineIncomeReport{
				TotalMileage: lineMileageReports[i].TotalMileage,
				TotalCircle:  lineMileageReports[i].TotalCircle,
			}
		}
		//if _, ok := c.Data[lineMileageReports[i].CorporationId]; ok {
		//	data := c.Data[lineMileageReports[i].CorporationId]
		//	data.TotalCircle += lineMileageReports[i].TotalCircle
		//	data.TotalMileage += lineMileageReports[i].TotalMileage
		//	c.Data[lineMileageReports[i].CorporationId] = data
		//} else {
		//	c.Data[lineMileageReports[i].CorporationId] = ticketModel.CorporationLineIncomeReport{
		//		TotalMileage: lineMileageReports[i].TotalMileage,
		//		TotalCircle:  lineMileageReports[i].TotalCircle,
		//	}
		//}
	}

	//班制外加班明细表
	outAddWorkMileageReports := (&operationModel.OutFrequencyAddWorkReport{}).GetLinesTotalMileage(util.StatusForFalse, nil, []int64{lineId}, c.StartAt, c.EndAt)
	for i := range outAddWorkMileageReports {
		if _, ok := c.Data[outAddWorkMileageReports[i].CorporationId]; ok {
			data := c.Data[outAddWorkMileageReports[i].CorporationId]
			data.TotalCircle += outAddWorkMileageReports[i].TotalCircle
			data.TotalMileage += outAddWorkMileageReports[i].TotalMileage
			c.Data[outAddWorkMileageReports[i].CorporationId] = data
		} else {
			c.Data[outAddWorkMileageReports[i].CorporationId] = ticketModel.CorporationLineIncomeReport{
				TotalMileage: outAddWorkMileageReports[i].TotalMileage,
				TotalCircle:  outAddWorkMileageReports[i].TotalCircle,
			}
		}
	}

	//定制线路表数据
	irregularLineReports := (&operationModel.IrregularLineReport{}).GetLinesTotalMileageAndCircle(util.StatusForFalse, nil, []int64{lineId}, c.StartAt, c.EndAt)
	for i := range irregularLineReports {
		if _, ok := c.Data[irregularLineReports[i].CorporationId]; ok {
			data := c.Data[irregularLineReports[i].CorporationId]
			data.TotalCircle += irregularLineReports[i].TotalCircle
			data.TotalMileage += irregularLineReports[i].TotalMileage
			c.Data[irregularLineReports[i].CorporationId] = data
		} else {
			c.Data[irregularLineReports[i].CorporationId] = ticketModel.CorporationLineIncomeReport{
				TotalMileage: irregularLineReports[i].TotalMileage,
				TotalCircle:  irregularLineReports[i].TotalCircle,
			}
		}
	}
}

// 查询票务数据
func (c *CalcCorporationLineIcReport) queryTicketMoney(lineId int64) {
	moneys := (&mixReport.TicketMoneyMixReportRecord{}).LineTicketMoney([]int64{lineId}, c.StartAt, c.EndAt)

	for i := range moneys {
		key := fmt.Sprintf("%d_%d", moneys[i].CorporationId, moneys[i].LineId)
		if _, ok := c.Data2[key]; ok {
			data := c.Data2[key]
			data.CashPaymentMoney += moneys[i].TotalMoney
			c.Data2[key] = data
		} else {
			c.Data2[key] = ticketModel.CorporationLineIncomeReport{
				CashPaymentMoney: moneys[i].TotalMoney,
			}
		}

		//if _, ok := c.Data[lineId]; ok {
		//	data := c.Data[lineId]
		//	data.CashPaymentMoney += moneys[i].TotalMoney
		//	c.Data[lineId] = data
		//} else {
		//	c.Data[lineId] = ticketModel.CorporationLineIncomeReport{
		//		CashPaymentMoney: moneys[i].TotalMoney,
		//	}
		//}
	}
}

func (c *CalcCorporationLineIcReport) commonWhere(thirdLineNo string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("SETT_DATE>=?", c.StartAt.Format("20060102")).
			Where("SETT_DATE<=?", c.EndAt.Format("20060102")).
			Where("TXN_MCHNT_ID = ?", "8000").
			Where("TXN_LINE_ID = ?", thirdLineNo)
	}
}
func (c *CalcCorporationLineIcReport) buildSumColumn(sumColumn string) []string {
	return []string{fmt.Sprintf("SUM(%s) as value", sumColumn), "TXN_LINE_ID", "TXN_ARRAY_ID"}
}

// 查询移动支付人次
func (c *CalcCorporationLineIcReport) queryMobilePaymentPeople(db *gorm.DB, thirdLineNo string) {
	//移动支付人次
	var cardRecords []thirdTableColumn
	var codeRecords []thirdTableColumn
	cardType := []string{"Y101", "Y102", "E100", "E101"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardRecords)
	codeQrId := []string{"84057505", "50023301", "********", "50023301", "50023301", "50023301", "50023301", "50023301", "50023301", "50011000", "50011000", "50023301", "50011000", "50023301", "50023301", "50011000", "50011000",
		"50011000", "50023301", "50011000", "50023301", "50023301", "50023301", "2002900", "3473320", "50011000", "1493350", "84087508", "84017501", "84247524"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeRecords)
	cardRecords = append(cardRecords, codeRecords...)

	for i := range cardRecords {
		arrayCode := cardRecords[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.MobilePaymentPeople += cardRecords[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				MobilePaymentPeople: cardRecords[i].Value,
			}
		}
	}
}

// 查询普通IC卡人次
func (c *CalcCorporationLineIcReport) queryNormalIcPeople(db *gorm.DB, thirdLineNo string) {
	//普通IC卡
	var cardRecords []thirdTableColumn
	var codeRecords []thirdTableColumn
	cardType := []string{"D130", "D101", "D1B1", "D1B4", "D1B0", "D1A4", "D1B3", "D1A9"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardRecords)

	codeQrId := []string{"84307530", "2113450", "84237523", "84057505"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeRecords)
	cardRecords = append(cardRecords, codeRecords...)

	for i := range cardRecords {
		arrayCode := cardRecords[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.NormalIcPeople += cardRecords[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				NormalIcPeople: cardRecords[i].Value,
			}
		}
	}
}

// 查询半价老年卡人次
func (c *CalcCorporationLineIcReport) queryHalfOlderIcPeople(db *gorm.DB, thirdLineNo string) {
	//半价老年卡
	var cardRecords []thirdTableColumn
	var codeRecords []thirdTableColumn
	cardType := []string{"D1A5", "E143"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardRecords)

	codeQrId := []string{"84437543"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeRecords)

	cardRecords = append(cardRecords, codeRecords...)

	for i := range cardRecords {
		arrayCode := cardRecords[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.HalfOlderIcPeople += cardRecords[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				HalfOlderIcPeople: cardRecords[i].Value,
			}
		}
	}
}

// 查询免费老年卡人次
func (c *CalcCorporationLineIcReport) queryFreeOlderIcPeople(db *gorm.DB, thirdLineNo string) {
	//免费老年卡
	var cardRecords []thirdTableColumn
	var codeRecords []thirdTableColumn
	cardType := []string{"D1B5", "E144"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardRecords)

	codeQrId := []string{"84447544"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeRecords)

	cardRecords = append(cardRecords, codeRecords...)

	for i := range cardRecords {
		arrayCode := cardRecords[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.FreeOlderIcPeople += cardRecords[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				FreeOlderIcPeople: cardRecords[i].Value,
			}
		}
	}
}

// 查询成人优待卡人次
func (c *CalcCorporationLineIcReport) queryAdultSpecialIcPeople(db *gorm.DB, thirdLineNo string) {
	//成人优待卡
	var cardRecords []thirdTableColumn
	var codeRecords []thirdTableColumn
	cardType := []string{"D1A3", "E145", "E146", "D1C6"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardRecords)

	codeQrId := []string{"84457545", "84327532", "84287528"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeRecords)

	cardRecords = append(cardRecords, codeRecords...)

	for i := range cardRecords {
		arrayCode := cardRecords[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.AdultSpecialIcPeople += cardRecords[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				AdultSpecialIcPeople: cardRecords[i].Value,
			}
		}
	}
}

// 查询免费换乘人次
func (c *CalcCorporationLineIcReport) queryFreeChangePeople(db *gorm.DB, thirdLineNo string, lineId int64) {
	//换乘人次
	var cardRecords []thirdTableColumn
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_PRE_CNT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ('D1A5', 'D130', 'D1B1', 'D1B4') AND `ERR_CD` IN ('0000', 'C21013') ").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardRecords)
	for i := range cardRecords {
		corporationId := lineId
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.FreeChangePeople += cardRecords[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				FreeChangePeople: cardRecords[i].Value,
			}
		}
	}
}

// 查询移动支付金额
func (c *CalcCorporationLineIcReport) queryMobilePaymentMoney(db *gorm.DB, thirdLineNo string) {
	//移动支付
	var cardMoneys []thirdTableColumn
	var codeMoneys []thirdTableColumn
	cardType := []string{"Y101", "Y102", "E100", "E101"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_AMT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardMoneys)

	codeQrId := []string{"84057505", "50023301", "********", "50023301", "50023301", "50023301", "50023301", "50023301", "50023301", "50011000", "50011000", "50023301", "50011000", "50023301", "50023301", "50011000", "50011000",
		"50011000", "50023301", "50011000", "50023301", "50023301", "50023301", "2002900", "3473320", "50011000", "1493350", "84087508", "84017501", "84247524"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_AMT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeMoneys)
	cardMoneys = append(cardMoneys, codeMoneys...)
	for i := range cardMoneys {
		arrayCode := cardMoneys[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.MobilePaymentMoney += cardMoneys[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				MobilePaymentMoney: cardMoneys[i].Value,
			}
		}
	}
}

// 查询普通IC卡金额
func (c *CalcCorporationLineIcReport) queryNormalIcMoney(db *gorm.DB, thirdLineNo string) {
	//普通IC卡
	var cardMoneys []thirdTableColumn
	var codeMoneys []thirdTableColumn
	cardType := []string{"D130", "D101", "D1B1", "D1B4", "D1B0", "D1A4", "D1B3", "D1A9"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_AMT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardMoneys)

	codeQrId := []string{"84307530", "2113450", "84237523", "84057505"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_AMT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeMoneys)

	cardMoneys = append(cardMoneys, codeMoneys...)

	for i := range cardMoneys {
		arrayCode := cardMoneys[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.NormalIcMoney += cardMoneys[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				NormalIcMoney: cardMoneys[i].Value,
			}
		}
	}
}

// 查询半价老年卡金额
func (c *CalcCorporationLineIcReport) queryHalfOlderIcMoney(db *gorm.DB, thirdLineNo string) {
	//半价老年卡
	var cardMoneys []thirdTableColumn
	var codeMoneys []thirdTableColumn
	cardType := []string{"D1A5", "E143"}
	db.Table("batch_card_fh").Select(c.buildSumColumn("TOT_TXN_AMT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("CARD_TYPE IN ?", cardType).
		Where("(ERR_CD in ('0000','C21029') AND CARD_ORG != '004' AND TXN_BRANCH_ID != 'F') OR (ERR_CD = '0000' AND CARD_ORG = '004')").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&cardMoneys)

	codeQrId := []string{"84437543"}
	db.Table("batch_code_fh").Select(c.buildSumColumn("TOT_TXN_AMT")).Scopes(c.commonWhere(thirdLineNo)).
		Where("QR_INST_ID IN ?", codeQrId).
		Where("ERR_CD='0000'").
		Group("TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&codeMoneys)

	cardMoneys = append(cardMoneys, codeMoneys...)

	for i := range cardMoneys {
		arrayCode := cardMoneys[i].TxnArrayId
		if _, ok := util.ThirdBranchToOetCorporationId[arrayCode]; !ok {
			continue
		}
		corporationId := util.ThirdBranchToOetCorporationId[arrayCode]
		if _, ok := c.Data[corporationId]; ok {
			data := c.Data[corporationId]
			data.HalfOlderIcMoney += cardMoneys[i].Value
			c.Data[corporationId] = data
		} else {
			c.Data[corporationId] = ticketModel.CorporationLineIncomeReport{
				HalfOlderIcMoney: cardMoneys[i].Value,
			}
		}
	}
}

// 票务v0.3.8

type thirdNewTableColumn struct {
	TxnLineId  string `json:"TXN_LINE_ID" gorm:"column:txn_line_id"`   //线路编号
	TxnArrayId string `json:"TXN_ARRAY_ID" gorm:"column:txn_array_id"` //车队编号
	CardName   string `json:"CardName" gorm:"column:card_name"`        // 卡名
	TotTxnAmt  int64  `json:"TOT_TXN_AMT" gorm:"column:tot_txn_amt"`   // 交易总金额
	TotTxnCnt  int64  `json:"TOT_TXN_CNT" gorm:"column:tot_txn_cnt"`   // 交易总笔数
	TotOrgAmt  int64  `json:"TOT_ORG_AMT" gorm:"column:tot_org_amt"`   // 交易优惠前总金额
	TotFavCnt  int64  `json:"TOT_FAV_CNT" gorm:"column:tot_fav_cnt"`   // 优惠笔数
}

// SettlementSql 获取计算sql语句
func (c *CalcCorporationLineIcReport) SettlementSql() string {
	return fmt.Sprintf(`
SELECT
	branch_name,
	txn_line_id,
	card_name,
	sum( tot_txn_amt ) AS tot_txn_amt,
	sum( tot_txn_cnt ) AS tot_txn_cnt 
FROM
	(
	SELECT
		ifnull( t2.branch_name, tt.txn_branch_id ) AS branch_name,
		tt.txn_line_id,
	CASE
			tt.txntype 
			WHEN 'card' THEN
			concat(
				'实体卡-',
			ifnull( t4.card_name, tt.card_type )) 
			WHEN 'code' THEN
			(
			CASE
					
					WHEN (
						tt.tot_org_amt > tt.tot_txn_amt 
						AND tt.qr_inst_id IN ( '50023301', '84057505' )) THEN
						concat(
							'虚拟卡-优惠-',
							ifnull( t5.card_iin, tt.qr_inst_id )) ELSE concat(
							'虚拟卡-',
						ifnull( t5.card_iin, tt.qr_inst_id )) 
					END 
					) 
					WHEN 'union' THEN
					'银联' 
				END AS card_name,
				tt.tot_txn_amt AS tot_txn_amt,
				tt.tot_txn_cnt AS tot_txn_cnt 
			FROM
				(
				SELECT
					t1.iss_inst_id,
					t1.sett_date,
					t1.txn_mchnt_id,
					t1.err_cd,
				CASE
						t1.card_org 
						WHEN '004' THEN
						'union' ELSE 'card' 
					END AS txntype,
					t1.inst_id,
					t1.txn_branch_id,
					t1.txn_line_id,
					t1.card_type,
					t1.bus_id,
					t1.tot_txn_amt,
					t1.tot_txn_cnt,
					t1.tot_org_amt,
					'' AS qr_inst_id 
				FROM
					batch_card_fh t1 
				WHERE
					( t1.err_cd IN ( '0000', 'C21029' ) AND t1.card_org != '004' AND t1.txn_branch_id != 'F' ) 
					OR ( t1.ERR_CD = '0000' AND t1.card_org = '004' ) UNION ALL
				SELECT
					t1.iss_inst_id,
					t1.sett_date,
					t1.txn_mchnt_id,
					t1.err_cd,
				CASE
						t1.pay_type 
						WHEN 'unionpay' THEN
						'union' ELSE 'code' 
					END AS txntype,
					t1.inst_id,
					t1.txn_branch_id,
					t1.txn_line_id,
					t1.card_type,
					t1.bus_id,
					t1.tot_txn_amt,
					t1.tot_txn_cnt,
					t1.tot_org_amt,
				CASE
						t1.qr_inst_id 
						WHEN '84247524' THEN
						'84237523' ELSE t1.qr_inst_id 
					END AS qr_inst_id 
				FROM
					batch_code_fh t1 
				WHERE
					( t1.ERR_CD = '0000' AND t1.PAY_TYPE != 'unionpay' ) 
					OR ( t1.ERR_CD = '0000' AND t1.PAY_TYPE = 'unionpay' ) 
				) tt
				LEFT JOIN bus_branch t2 ON tt.txn_branch_id = t2.branch_id
				LEFT JOIN bus_line t3 ON tt.txn_line_id = t3.line_id 
				AND tt.txn_branch_id = t3.branch_id
				LEFT JOIN card_par_cardtype t4 ON tt.card_type = t4.card_type 
				AND tt.inst_id = t4.inst_id
				LEFT JOIN hlht_bn t5 ON tt.iss_inst_id = t5.issue_inst_id 
				AND t5.card_org = '005' 
				AND tt.qr_inst_id = t5.qr_inst_id 
			WHERE
					SETT_DATE BETWEEN '%s' 
				AND '%s' 
				AND TXN_MCHNT_ID = '8000' 
			) t 
		GROUP BY
			branch_name,
		txn_line_id,
	card_name 
`, c.StartAt.Format("20060102"), c.EndAt.Format("20060102"))
}

// CompleteSettlement 整体结算
func (c *CalcCorporationLineIcReport) CompleteSettlement(db *gorm.DB) (result map[string][]thirdNewTableColumn) {
	var data []thirdNewTableColumn
	sql := c.SettlementSql()
	err := db.Raw(sql).Scan(&data).Error
	if err != nil {
		log.ErrorFields("CompleteSettlement Scan ERR", map[string]interface{}{"error": err})
		return
	}
	result = make(map[string][]thirdNewTableColumn)
	if data != nil {
		for _, v := range data {
			if result[v.TxnLineId] == nil {
				result[v.TxnLineId] = make([]thirdNewTableColumn, 0, 30)
			}
			result[v.TxnLineId] = append(result[v.TxnLineId], v)
		}
	}
	return
}

// FreeChangeSql 获取免费换乘的sql语句
func (c *CalcCorporationLineIcReport) FreeChangeSql() string {
	return fmt.Sprintf(`
	SELECT 
    COALESCE(b.BRANCH_NAME, aa.TXN_BRANCH_ID) AS BRANCH_NAME,
   aa.txn_line_id,
    SUM(aa.tot_pre_cnt) AS tot_txn_cnt 
FROM 
 (
    SELECT  t.TXN_BRANCH_ID, t.txn_line_id, t.CARD_TYPE, t.TOT_PRE_AMT, t.tot_pre_cnt 
    FROM batch_card_fh t
    WHERE  t.ERR_CD IN ('0000', 'C21029') 
    AND t.tot_pre_cnt > 0 
    AND SETT_DATE BETWEEN '%s' AND '%s'  
    AND TXN_MCHNT_ID = '8000'
) aa
LEFT JOIN bus_branch b ON b.BRANCH_ID = aa.TXN_BRANCH_ID
LEFT JOIN bus_line l ON l.LINE_ID = aa.TXN_LINE_ID AND l.BRANCH_ID = aa.TXN_BRANCH_ID
LEFT JOIN card_par_cardtype k ON k.CARD_TYPE = aa.CARD_TYPE
GROUP BY 
    COALESCE(b.BRANCH_NAME, aa.TXN_BRANCH_ID),
		aa.txn_line_id
	`, c.StartAt.Format("20060102"), c.EndAt.Format("20060102"))
}

func (c *CalcCorporationLineIcReport) CompleteFreeChangePeople(db *gorm.DB) (result map[string]int64) {
	var data []thirdNewTableColumn
	sql := c.FreeChangeSql()
	err := db.Raw(sql).Scan(&data).Error
	if err != nil {
		log.ErrorFields("CompleteFreeChangePeople Scan ERR", map[string]interface{}{"error": err})
		return
	}
	result = make(map[string]int64)
	if data != nil {
		for _, v := range data {
			result[v.TxnLineId] = v.TotTxnCnt
		}
	}
	return
}

type LineArrayPair struct {
	LineID  string `gorm:"column:line_id"`
	ArrayID string `gorm:"column:array_id"`
}

// LineArrayMap 获取 路线-车队的对应表
func (c *CalcCorporationLineIcReport) LineArrayMap(db *gorm.DB) (result map[string]string) {
	var pairs []LineArrayPair

	err := db.
		Table("bus_line").
		Select("line_id, array_id").
		Group("line_id, array_id").
		Find(&pairs).Error
	if err != nil {
		// 处理错误
		return
	}
	result = make(map[string]string)
	for _, pair := range pairs {
		result[pair.LineID] = pair.ArrayID
	}
	return
}

// 字段对应字典
var cardNameToRevenueMap = map[string]string{
	"实体卡-老年优惠卡":       "HalfOlderIcMoney",     // 半价老年卡
	"实体卡-D1A5":        "HalfOlderIcMoney",     // 半价老年卡
	"虚拟卡-老年优惠卡":       "HalfOlderIcMoney",     // 半价老年卡
	"虚拟卡-E143":        "HalfOlderIcMoney",     // 半价老年卡
	"虚拟卡-84437543":    "HalfOlderIcMoney",     // 半价老年卡
	"虚拟卡-乘车呗":         "MobilePaymentMoney",   // 移动支付
	"虚拟卡-84057505":    "MobilePaymentMoney",   // 移动支付
	"虚拟卡-新支付宝":        "MobilePaymentMoney",   // 移动支付
	"虚拟卡-50023301":    "MobilePaymentMoney",   // 移动支付
	"银联":              "MobilePaymentMoney",   // 移动支付
	"虚拟卡-互通二维码":       "MobilePaymentMoney",   // 移动支付
	"虚拟卡-50011000":    "MobilePaymentMoney",   // 移动支付
	"虚拟卡-2002900":     "MobilePaymentMoney",   // 移动支付
	"虚拟卡-3473320":     "MobilePaymentMoney",   // 移动支付
	"虚拟卡-1493350":     "MobilePaymentMoney",   // 移动支付
	"虚拟卡-翼支付":         "MobilePaymentMoney",   // 移动支付
	"实体卡-翼支付":         "MobilePaymentMoney",   // 移动支付
	"虚拟卡-掌上公交":        "MobilePaymentMoney",   // 移动支付
	"实体卡-掌上公交":        "MobilePaymentMoney",   // 移动支付
	"虚拟卡-普通码旧":        "MobilePaymentMoney",   // 移动支付
	"实体卡-普通码旧":        "MobilePaymentMoney",   // 移动支付
	"虚拟卡-E100":        "MobilePaymentMoney",   // 移动支付
	"实体卡-E100":        "MobilePaymentMoney",   // 移动支付
	"虚拟卡-普通码新":        "MobilePaymentMoney",   // 移动支付
	"实体卡-普通码新":        "MobilePaymentMoney",   // 移动支付
	"虚拟卡-E101":        "MobilePaymentMoney",   // 移动支付
	"实体卡-E101":        "MobilePaymentMoney",   // 移动支付
	"虚拟卡-优惠-乘车呗":      "NormalIcMoney",        // 普通ic卡
	"虚拟卡-优惠-84057505": "NormalIcMoney",        // 普通ic卡
	"实体卡-互联互通卡":       "NormalIcMoney",        // 普通ic卡
	"实体卡-D130":        "NormalIcMoney",        // 普通ic卡
	"实体卡-普通卡A":        "NormalIcMoney",        // 普通ic卡
	"实体卡-D101":        "NormalIcMoney",        // 普通ic卡
	"实体卡-系统员工卡G":      "NormalIcMoney",        // 普通ic卡
	"实体卡-D1B1":        "NormalIcMoney",        // 普通ic卡
	"实体卡-学生优惠卡":       "NormalIcMoney",        // 普通ic卡
	"实体卡-D1B4":        "NormalIcMoney",        // 普通ic卡
	"实体卡-畅行台州":        "NormalIcMoney",        // 普通ic卡
	"虚拟卡-畅行台州":        "NormalIcMoney",        // 普通ic卡
	"虚拟卡-台州出行":        "NormalIcMoney",        // 普通ic卡
	"实体卡-系统员工卡B0":     "NormalIcMoney",        // 普通ic卡
	"实体卡-84307530":    "NormalIcMoney",        // 普通ic卡
	"实体卡-互联互通码":       "NormalIcMoney",        // 普通ic卡
	"虚拟卡-互联互通码":       "NormalIcMoney",        // 普通ic卡
	"实体卡-2113450":     "NormalIcMoney",        // 普通ic卡
	"实体卡-台州出行":        "NormalIcMoney",        // 普通ic卡
	"实体卡-84237523":    "NormalIcMoney",        // 普通ic卡
	"虚拟卡-系统员工卡B0":     "NormalIcMoney",        // 普通ic卡
	"虚拟卡-D1B0":        "NormalIcMoney",        // 普通ic卡
	"实体卡-学生月票卡C":      "NormalIcMoney",        // 普通ic卡
	"虚拟卡-学生月票卡C":      "NormalIcMoney",        // 普通ic卡
	"实体卡-D1A4":        "NormalIcMoney",        // 普通ic卡
	"虚拟卡-D1A4":        "NormalIcMoney",        // 普通ic卡
	"实体卡-成人月票卡B":      "NormalIcMoney",        // 普通ic卡
	"虚拟卡-成人月票卡B":      "NormalIcMoney",        // 普通ic卡
	"实体卡-D1B3":        "NormalIcMoney",        // 普通ic卡
	"虚拟卡-D1B3":        "NormalIcMoney",        // 普通ic卡
	"实体卡-未知卡A9":       "NormalIcMoney",        // 普通ic卡
	"虚拟卡-未知卡A9":       "NormalIcMoney",        // 普通ic卡
	"实体卡-D1A9":        "NormalIcMoney",        // 普通ic卡
	"虚拟卡-D1A9":        "NormalIcMoney",        // 普通ic卡
	"实体卡-高龄免费卡":       "FreeOlderIcAllowance", //免费老年卡
	"实体卡-D1B5":        "FreeOlderIcAllowance", //免费老年卡
	"虚拟卡-高龄免费卡":       "FreeOlderIcAllowance", //免费老年卡
	"虚拟卡-E144":        "FreeOlderIcAllowance", //免费老年卡
	"虚拟卡-84447544":    "FreeOlderIcAllowance", //免费老年卡
}

var cardNameToPeopleMap = map[string]string{
	"实体卡-老年优惠卡":       "HalfOlderIcPeople",    // 半价老年卡
	"实体卡-D1A5":        "HalfOlderIcPeople",    // 半价老年卡
	"虚拟卡-老年优惠卡":       "HalfOlderIcPeople",    // 半价老年卡
	"虚拟卡-E143":        "HalfOlderIcPeople",    // 半价老年卡
	"虚拟卡-84437543":    "HalfOlderIcPeople",    // 半价老年卡
	"虚拟卡-乘车呗":         "MobilePaymentPeople",  // 移动支付
	"虚拟卡-84057505":    "MobilePaymentPeople",  // 移动支付
	"虚拟卡-新支付宝":        "MobilePaymentPeople",  // 移动支付
	"虚拟卡-50023301":    "MobilePaymentPeople",  // 移动支付
	"银联":              "MobilePaymentPeople",  // 移动支付
	"虚拟卡-互通二维码":       "MobilePaymentPeople",  // 移动支付
	"虚拟卡-50011000":    "MobilePaymentPeople",  // 移动支付
	"虚拟卡-2002900":     "MobilePaymentPeople",  // 移动支付
	"虚拟卡-3473320":     "MobilePaymentPeople",  // 移动支付
	"虚拟卡-1493350":     "MobilePaymentPeople",  // 移动支付
	"虚拟卡-翼支付":         "MobilePaymentPeople",  // 移动支付
	"实体卡-翼支付":         "MobilePaymentPeople",  // 移动支付
	"虚拟卡-掌上公交":        "MobilePaymentPeople",  // 移动支付
	"实体卡-掌上公交":        "MobilePaymentPeople",  // 移动支付
	"虚拟卡-普通码旧":        "MobilePaymentPeople",  // 移动支付
	"实体卡-普通码旧":        "MobilePaymentPeople",  // 移动支付
	"虚拟卡-E100":        "MobilePaymentPeople",  // 移动支付
	"实体卡-E100":        "MobilePaymentPeople",  // 移动支付
	"虚拟卡-普通码新":        "MobilePaymentPeople",  // 移动支付
	"实体卡-普通码新":        "MobilePaymentPeople",  // 移动支付
	"虚拟卡-E101":        "MobilePaymentPeople",  // 移动支付
	"实体卡-E101":        "MobilePaymentPeople",  // 移动支付
	"虚拟卡-互联互通码":       "NormalIcPeople",       // 普通ic卡
	"虚拟卡-优惠-乘车呗":      "NormalIcPeople",       // 普通ic卡
	"虚拟卡-优惠-84057505": "NormalIcPeople",       // 普通ic卡
	"实体卡-互联互通卡":       "NormalIcPeople",       // 普通ic卡
	"实体卡-D130":        "NormalIcPeople",       // 普通ic卡
	"实体卡-普通卡A":        "NormalIcPeople",       // 普通ic卡
	"实体卡-系统员工卡B0":     "NormalIcPeople",       // 普通ic卡
	"虚拟卡-台州出行":        "NormalIcPeople",       // 普通ic卡
	"实体卡-D101":        "NormalIcPeople",       // 普通ic卡
	"实体卡-系统员工卡G":      "NormalIcPeople",       // 普通ic卡
	"实体卡-D1B1":        "NormalIcPeople",       // 普通ic卡
	"实体卡-学生优惠卡":       "NormalIcPeople",       // 普通ic卡
	"实体卡-D1B4":        "NormalIcPeople",       // 普通ic卡
	"实体卡-畅行台州":        "NormalIcPeople",       // 普通ic卡
	"虚拟卡-畅行台州":        "NormalIcPeople",       // 普通ic卡
	"实体卡-84307530":    "NormalIcPeople",       // 普通ic卡
	"实体卡-互联互通码":       "NormalIcPeople",       // 普通ic卡
	"实体卡-2113450":     "NormalIcPeople",       // 普通ic卡
	"实体卡-台州出行":        "NormalIcPeople",       // 普通ic卡
	"实体卡-84237523":    "NormalIcPeople",       // 普通ic卡
	"虚拟卡-系统员工卡B0":     "NormalIcPeople",       // 普通ic卡
	"虚拟卡-D1B0":        "NormalIcPeople",       // 普通ic卡
	"实体卡-学生月票卡C":      "NormalIcPeople",       // 普通ic卡
	"虚拟卡-学生月票卡C":      "NormalIcPeople",       // 普通ic卡
	"实体卡-D1A4":        "NormalIcPeople",       // 普通ic卡
	"虚拟卡-D1A4":        "NormalIcPeople",       // 普通ic卡
	"实体卡-成人月票卡B":      "NormalIcPeople",       // 普通ic卡
	"虚拟卡-成人月票卡B":      "NormalIcPeople",       // 普通ic卡
	"实体卡-D1B3":        "NormalIcPeople",       // 普通ic卡
	"虚拟卡-D1B3":        "NormalIcPeople",       // 普通ic卡
	"实体卡-未知卡A9":       "NormalIcPeople",       // 普通ic卡
	"虚拟卡-未知卡A9":       "NormalIcPeople",       // 普通ic卡
	"实体卡-D1A9":        "NormalIcPeople",       // 普通ic卡
	"虚拟卡-D1A9":        "NormalIcPeople",       // 普通ic卡
	"实体卡-高龄免费卡":       "FreeOlderIcPeople",    //免费老年卡
	"实体卡-D1B5":        "FreeOlderIcPeople",    //免费老年卡
	"虚拟卡-高龄免费卡":       "FreeOlderIcPeople",    //免费老年卡
	"虚拟卡-E144":        "FreeOlderIcPeople",    //免费老年卡
	"虚拟卡-84447544":    "FreeOlderIcPeople",    //免费老年卡
	"实体卡-爱心卡":         "AdultSpecialIcPeople", // 成人优待
	"实体卡-D1A3":        "AdultSpecialIcPeople", // 成人优待
	"虚拟卡-老兵码":         "AdultSpecialIcPeople", // 成人优待
	"虚拟卡-E145":        "AdultSpecialIcPeople", // 成人优待
	"虚拟卡-84457545":    "AdultSpecialIcPeople", // 成人优待
	"虚拟卡-人才码":         "AdultSpecialIcPeople", // 成人优待
	"虚拟卡-E146":        "AdultSpecialIcPeople", // 成人优待
	"虚拟卡-84327532":    "AdultSpecialIcPeople", // 成人优待
	"虚拟卡-台州军休app":     "AdultSpecialIcPeople", // 成人优待
	"虚拟卡-84287528":    "AdultSpecialIcPeople", // 成人优待
	"实体卡-交通部军人卡":      "AdultSpecialIcPeople", // 成人优待
	"虚拟卡-交通部军人卡":      "AdultSpecialIcPeople", // 成人优待
	"实体卡-D1C6":        "AdultSpecialIcPeople", // 成人优待
	"虚拟卡-D1C6":        "AdultSpecialIcPeople", // 成人优待
}

func (c *CalcCorporationLineIcReport) queryCompleteMoney(cardMoneys []thirdNewTableColumn, key string) {
	for i := range cardMoneys {
		//lineCode := cardMoneys[i].TxnLineId
		//lineCodeNum, _ := strconv.Atoi(lineCode)
		corporationId := key
		fmt.Println("机构id_线路编号", corporationId)
		var data ticketModel.CorporationLineIncomeReport
		if _, ok := c.Data2[corporationId]; ok {
			data = c.Data2[corporationId]
		}
		cardName := cardMoneys[i].CardName
		cardName = strings.ReplaceAll(cardName, " ", "")
		fmt.Println("cardName:", cardName)
		moneyField := cardNameToRevenueMap[cardName]
		fmt.Println("cardName-Field:", moneyField)
		ve := reflect.ValueOf(&data).Elem()
		if moneyField != "" {
			moneyValue := ve.FieldByName(moneyField).Int()
			fmt.Println("计算票价：", cardMoneys[i].TotTxnAmt+moneyValue)
			ve.FieldByName(moneyField).SetInt(cardMoneys[i].TotTxnAmt + moneyValue)
		}
		peopleField := cardNameToPeopleMap[cardName]
		fmt.Println("cardName-peopleField:", peopleField)
		if peopleField != "" {
			peopleValue := ve.FieldByName(peopleField).Int()
			fmt.Println("计算人数：", cardMoneys[i].TotTxnCnt+peopleValue)
			ve.FieldByName(peopleField).SetInt(cardMoneys[i].TotTxnCnt + peopleValue)
		}
		c.Data2[corporationId] = data
		fmt.Println("loop - end ===================")
	}
}

func (c *CalcCorporationLineIcReport) SetFreeChangePeople(people int64, key string) {
	var data ticketModel.CorporationLineIncomeReport
	if _, ok := c.Data2[key]; ok {
		data = c.Data2[key]
	}
	data.FreeChangePeople += people
	c.Data2[key] = data
}

type VehicleICColumn struct {
	TxnDate     string `json:"TxnDate" gorm:"column:txn_date"`         //时间
	TxnTime     string `json:"TxnTime" gorm:"column:txn_time"`         //时间
	UpLineId    string `json:"UpLineId" gorm:"column:up_line_id"`      //线路编号
	LineName    string `json:"LineName" gorm:"column:line_name"`       //线路名称
	TxnAmt      int64  `json:"TxnAmt" gorm:"column:txn_amt"`           //金额
	BusNumpalte string `json:"BusNumpalte" gorm:"column:bus_numplate"` //车牌号
	//
	ErpLineId       int64  `json:"ErpLineId" ` //线路编号
	ErpLienName     string `json:"ErpLienName"`
	Mileage         int64  `json:"Mileage"`
	CompletedCircle int64  `json:"CompletedCircle"`
	VehicleId       int64  `json:"VehicleId"`
	LineNameToErp   string `json:"LineNameToErp"`
	UpLineToErp     int64  `json:"UpLineToErp" `
	PeopleCount     int64  `json:"PeopleCount"`
	CorporationName string `json:"CorporationName"`
	CorporationId   int64  `json:"CorporationId"`
}

func VehicleICColumnData(db *gorm.DB, startAt, endAt, license string) (cardData []VehicleICColumn) {
	where := ""
	if license != "" {
		where = "AND b.bus_numplate = '" + license + "'"
	}
	sql := fmt.Sprintf(`SELECT a.txn_date,a.txn_time,a.up_line_id,a.txn_amt,b.bus_numplate,c.line_name 
				FROM card_dtl a 
				LEFT JOIN bus_info b on a.UP_BUS_ID = b.BUS_ID 
				LEFT JOIN bus_line c on a.UP_LINE_ID = c.line_id
				where a.TXN_DATE >= '%s' and a.TXN_DATE <= '%s'  AND a.TXN_MCHNT_ID = '8000' %s`, startAt, endAt, where)
	err := db.Raw(sql).Scan(&cardData).Error
	if err != nil {
		fmt.Println(err)
		return
	}
	return
}
