package service

import (
	protoLine "app/org/scs/erpv2/api/proto/rpc/oetline"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
)

func GetLineIdsByLineAttr(topCorporationId int64, lineIds, lineAttrs []int64, userId int64) []int64 {
	lines, _ := rpc.GetLinesWithUserId(context.TODO(), &protoLine.GetLinesWithUserIdRequest{
		UserId:          userId,
		Limit:           10000,
		IsLineAttribute: 1,
	})

	var ids []int64
	for _, line := range lines {
		if len(lineIds) > 0 {
			if util.IncludeInt64(lineIds, line.Id) && util.IncludeInt64(lineAttrs, line.LineAttribute) {
				ids = append(ids, line.Id)
			}
		} else {
			if util.IncludeInt64(lineAttrs, line.LineAttribute) {
				ids = append(ids, line.Id)
			}
		}
	}

	return ids
}
