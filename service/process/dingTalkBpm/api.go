package dingTalkBpm

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/util/httpClient"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/url"
	"strings"
	"time"
)

type AccessToken struct {
	Token     string `json:"access_token"`
	ExpiredAt int64
	ExpiresIn int64 `json:"expires_in"`
}

var DingTalkAccessToken AccessToken

func GetAccessToken() (string, error) {
	if DingTalkAccessToken.Token != "" && DingTalkAccessToken.ExpiredAt > time.Now().Unix() {
		fmt.Printf("历史 TOKEN \n")
		return DingTalkAccessToken.Token, nil
	}
	fmt.Printf("新 TOKEN \n")
	client := &httpClient.Request{
		Timeout: 60 * time.Second,
		Url:     "https://oapi.dingtalk.com/gettoken",
		Query:   url.Values{"appkey": {config.Config.DingTalkBpm.AppKey}, "appsecret": {config.Config.DingTalkBpm.AppSecret}},
	}
	client.AddHeader("Content-Type", "application/json")

	resp, err := client.Do()
	if err != nil {
		log.ErrorFields("dingTalkBpm GetAccessToken error", map[string]interface{}{"err": err})
		return "", err
	}
	defer resp.Body.Close()

	err = resp.Body.FromToJson(&DingTalkAccessToken)

	if err != nil {
		log.ErrorFields("dingTalkBpm GetAccessToken resp.Body.FromToJson error", map[string]interface{}{"err": err})
		return "", err
	}
	DingTalkAccessToken.ExpiredAt = time.Now().Unix() + DingTalkAccessToken.ExpiresIn - 1800
	fmt.Printf("GetAccessToken DingTalkAccessToken.Token:%v \n", DingTalkAccessToken.Token)
	return DingTalkAccessToken.Token, nil
}

type UserGetByCodeResponse struct {
	UserId   string `json:"userid"`    // 用户的userId
	UnionId  string `json:"unionid"`   // 用户unionId
	Name     string `json:"name"`      // 用户名字
	Sys      bool   `json:"sys"`       // 是否是管理员： true：是；false：不是
	SysLevel int64  `json:"sys_level"` // 级别： 1：主管理员；2：子管理员；100：老板；0：其他（如普通员工）
	DeviceId string `json:"device_id"` // 设备ID
}

// https://open.dingtalk.com/document/orgapp/obtain-the-userid-of-a-user-by-using-the-log-free
func GetUserIdByCode(code, token string) (string, error) {
	var err error
	if token == "" {
		token, err = GetAccessToken()
		if err != nil {
			return "", err
		}
	}
	body, _ := json.Marshal(map[string]string{"code": code})
	client := &httpClient.Request{
		Url:    "https://oapi.dingtalk.com/topapi/v2/user/getuserinfo",
		Method: "POST",
		Query:  url.Values{"access_token": {token}},
		Body:   strings.NewReader(string(body)),
	}
	client.AddHeader("Content-Type", "application/json")

	resp, err := client.Do()
	if err != nil {
		log.ErrorFields("dingTalkBpm GetUserId[1] error", map[string]interface{}{"err": err})
		return "", err
	}
	defer resp.Body.Close()

	type UserInfo struct {
		Errcode int64                 `json:"errcode"`
		Errmsg  string                `json:"errmsg"`
		Result  UserGetByCodeResponse `json:"result"`
	}

	var dingUser UserInfo
	err = resp.Body.FromToJson(&dingUser)
	if err != nil {
		log.ErrorFields("dingTalkBpm GetUserIdByCode Body.FromToJson error", map[string]interface{}{"err": err})
		return "", err
	}

	if dingUser.Errcode != 0 {
		log.ErrorFields("dingTalkBpm GetUserId[2] error", map[string]interface{}{"errmsg": dingUser.Errmsg})
		return "", errors.New(dingUser.Errmsg)
	}

	return dingUser.Result.UserId, nil
}

func GetUserId(mobile, token string) (string, error) {
	var err error
	if token == "" {
		token, err = GetAccessToken()
		if err != nil {
			return "", err
		}
	}

	body, _ := json.Marshal(map[string]string{"mobile": mobile})
	client := &httpClient.Request{
		Url:    "https://oapi.dingtalk.com/topapi/v2/user/getbymobile",
		Method: "POST",
		Query:  url.Values{"access_token": {token}},
		Body:   strings.NewReader(string(body)),
	}
	client.AddHeader("Content-Type", "application/json")

	resp, err := client.Do()
	if err != nil {
		log.ErrorFields("dingTalkBpm GetUserId[1] error", map[string]interface{}{"err": err})
		return "", err
	}
	defer resp.Body.Close()

	type result struct {
		UserId string `json:"userid"`
	}
	type UserInfo struct {
		Errcode int64  `json:"errcode"`
		Errmsg  string `json:"errmsg"`
		Result  result `json:"result"`
	}

	var dingUser UserInfo
	err = resp.Body.FromToJson(&dingUser)
	if err != nil {
		log.ErrorFields("dingTalkBpm GetUserId Body.FromToJson error", map[string]interface{}{"err": err})
		return "", err
	}

	if dingUser.Errcode != 0 {
		log.ErrorFields("dingTalkBpm GetUserId[2] error", map[string]interface{}{"errmsg": dingUser.Errmsg})
		return "", errors.New(dingUser.Errmsg)
	}

	return dingUser.Result.UserId, nil

}

type DingUserInfo struct {
	UserId     string  `json:"userid"`
	UnionId    string  `json:"unionid"`
	Name       string  `json:"name"`
	Mobile     string  `json:"mobile"`
	JobNumber  string  `json:"job_number"`
	DeptIdList []int64 `json:"dept_id_list"`
}

func GetUserInfo(dingUserId, token string) (DingUserInfo, error) {
	var err error
	if token == "" {
		token, err = GetAccessToken()
		if err != nil {
			return DingUserInfo{}, err
		}
	}
	body, _ := json.Marshal(map[string]string{"userid": dingUserId})
	client := &httpClient.Request{
		Url:    "https://oapi.dingtalk.com/topapi/v2/user/get",
		Method: "POST",
		Query:  url.Values{"access_token": {token}},
		Body:   strings.NewReader(string(body)),
	}
	client.AddHeader("Content-Type", "application/json")

	resp, err := client.Do()
	if err != nil {
		log.ErrorFields("dingTalkBpm GetUserInfo error", map[string]interface{}{"err": err})
		return DingUserInfo{}, err
	}
	defer resp.Body.Close()

	type Result struct {
		Errcode int64        `json:"errcode"`
		Errmsg  string       `json:"errmsg"`
		Result  DingUserInfo `json:"result"`
	}

	bodyStr, _ := ioutil.ReadAll(resp.Body)
	log.ErrorFields("GetUserInfo ", map[string]interface{}{"resp Body": string(bodyStr)})

	var dingUser Result
	err = json.Unmarshal(bodyStr, &dingUser)
	if err != nil {
		log.ErrorFields("dingTalkBpm GetUserInfo error", map[string]interface{}{"err": err})
		return DingUserInfo{}, err
	}

	if dingUser.Errcode != 0 {
		log.ErrorFields("dingTalkBpm GetUserInfo error", map[string]interface{}{"errmsg": dingUser.Errmsg, "dingUserId": dingUserId})
		return DingUserInfo{}, errors.New(dingUser.Errmsg)
	}

	return dingUser.Result, nil

}

type DingDepartmentInfo struct {
	DeptId int64  `json:"dept_id"`
	Name   string `json:"name"`
}

func GetDepartmentInfo(dingDeptId int64, token string) (DingDepartmentInfo, error) {
	var err error
	if token == "" {
		token, err = GetAccessToken()
		if err != nil {
			return DingDepartmentInfo{}, err
		}
	}

	body, _ := json.Marshal(map[string]int64{"dept_id": dingDeptId})

	client := &httpClient.Request{
		Url:    "https://oapi.dingtalk.com/topapi/v2/department/get",
		Method: "POST",
		Query:  url.Values{"access_token": {token}},
		Body:   strings.NewReader(string(body)),
	}
	client.AddHeader("Content-Type", "application/json")

	resp, err := client.Do()
	if err != nil {
		log.ErrorFields("dingTalkBpm GetDepartmentInfo error", map[string]interface{}{"err": err})
		return DingDepartmentInfo{}, err
	}
	defer resp.Body.Close()

	type Result struct {
		Errcode int64              `json:"errcode"`
		Errmsg  string             `json:"errmsg"`
		Result  DingDepartmentInfo `json:"result"`
	}

	var dingUser Result
	err = resp.Body.FromToJson(&dingUser)
	if err != nil {
		log.ErrorFields("dingTalkBpm GetDepartmentInfo Body.FromToJson error", map[string]interface{}{"err": err})
		return DingDepartmentInfo{}, err
	}

	if dingUser.Errcode != 0 {
		log.ErrorFields("dingTalkBpm GetDepartmentInfo error", map[string]interface{}{"errmsg": dingUser.Errmsg})
		return DingDepartmentInfo{}, errors.New(dingUser.Errmsg)
	}

	return dingUser.Result, nil

}

// https://open.dingtalk.com/document/orgapp/create-an-approval-instance
func DispatchProcess(token string, body []byte) (string, error) {
	var err error
	if token == "" {
		token, err = GetAccessToken()
		if err != nil {
			return "", err
		}
	}

	postBody := strings.NewReader(string(body))

	client := &httpClient.Request{
		Url:    "https://api.dingtalk.com/v1.0/workflow/processInstances",
		Method: "POST",
		Body:   postBody,
	}
	client.AddHeader("Content-Type", "application/json")
	client.AddHeader("x-acs-dingtalk-access-token", token)

	resp, err := client.Do()
	if err != nil {
		log.ErrorFields("dingTalkBpm DispatchProcess error", map[string]interface{}{"err": err})
		return "", err
	}
	defer resp.Body.Close()

	type Instance struct {
		InstanceId string `json:"instanceId"`
	}

	bodyStr, _ := ioutil.ReadAll(resp.Body)
	log.ErrorFields("DispatchProcess==%+v", map[string]interface{}{"resp Body": string(bodyStr)})

	var processInstance Instance
	err = json.Unmarshal(bodyStr, &processInstance)
	if err != nil {
		log.ErrorFields("dingTalkBpm GetDepartmentInfo Body.FromToJson error", map[string]interface{}{"err": err})
		return "", err
	}

	if processInstance.InstanceId == "" {
		return "", errors.New("创建实例异常")
	}

	return processInstance.InstanceId, nil

}

type ProcessInstanceItem struct {
	Title              string
	BusinessId         string `json:"businessId"`
	FinishTime         string
	OriginatorUserId   string
	OriginatorDeptId   string
	OriginatorDeptName string
	Status             string `json:"status"` // NEW：新创建;RUNNING：审批中;TERMINATED：被终止;COMPLETED：完成;CANCELED：取消
	Result             string `json:"result"` // agree：同意 refuse：拒绝
	/**
	  status为COMPLETED且result为agree时，表示审批单完结并审批通过
	*/
	FormComponentValues []FormComponentValue `json:"formComponentValues"`
	OperationRecords    []OperationRecord    `json:"operationRecords"`
}

type FormComponentValue struct {
	Id            string
	Name          string
	Value         string
	ExtValue      string
	ComponentType string
	BizAlias      string
}

type OperationRecord struct {
	UserId string `json:"userId"`
	Type   string `json:"type"`
	Result string `json:"result"`
	Remark string `json:"remark"`
}

/**
  实例详情示例:
{"result":{"finishTime":"2023-06-02T17:58Z","attachedProcessInstanceIds":[],"businessId":"202306021757000184515","title":"林倍健提交的xxxxxx","originatorDeptId":"-1","operationRecords":[{"date":"2023-06-02T17:57Z","result":"NONE","type":"START_PROCESS_INSTANCE","userId":"221911063626140623"},{"date":"2023-06-02T17:58Z","result":"AGREE","remark":"已确认","type":"EXECUTE_TASK_NORMAL","userId":"manager640"}],"formComponentValues":[{"componentType":"TextField","name":"申请车辆","bizAlias":"","id":"TextField-K2AD4O5B","value":"浙D22623"},{"componentType":"TextField","name":"申请司机","bizAlias":"","id":"TextField_10QVEUADYUOW0","value":"徐江涛"},{"componentType":"TextField","name":"物料名称","bizAlias":"","id":"TextField_1COPBQDPL64G0","value":"砂锅d"},{"componentType":"NumberField","name":"*预领数量","bizAlias":"","id":"NumberField_QYJYV5PLXGG0","value":"2"},{"componentType":"NumberField","name":"*实领数量","bizAlias":"","id":"NumberField_1YQEZLXMQCU80","value":"0"},{"componentType":"TextField","name":"单位","bizAlias":"","id":"TextField_BNUQEP0KETS0","value":"拳"},{"componentType":"TextareaField","name":"备注","bizAlias":"","id":"TextareaField_2SALCQR9OGE0"}],"result":"agree","bizAction":"NONE","createTime":"2023-06-02T17:57Z","originatorUserId":"221911063626140623","tasks":[{"result":"AGREE","activityId":"8f5b_5f5d","finishTime":"2023-06-02T17:58Z","pcUrl":"aflow.dingtalk.com?procInsId=bUhcIuooRHicHTl2ZUrVyw01351685699838\u0026taskId=***********\u0026businessId=202306021757000184515","createTime":"2023-06-02T17:57Z","mobileUrl":"aflow.dingtalk.com?procInsId=bUhcIuooRHicHTl2ZUrVyw01351685699838\u0026taskId=***********\u0026businessId=202306021757000184515","userId":"manager640","taskId":***********,"status":"COMPLETED"}],"originatorDeptName":"test_xjt","status":"COMPLETED"},"success":true}
*/

const (
	ProcessInstance_OperationRecordType_Task_Normal = "EXECUTE_TASK_NORMAL" // 正常执行任务
	ProcessInstance_OperationRecordResult_Agree     = "AGREE"               // 同意
)

func GetProcessInstance(instanceId, token string) (ProcessInstanceItem, error) {
	var err error
	if token == "" {
		token, err = GetAccessToken()
		if err != nil {
			return ProcessInstanceItem{}, err
		}
	}

	client := &httpClient.Request{
		Url:   "https://api.dingtalk.com/v1.0/workflow/processInstances",
		Query: url.Values{"processInstanceId": {instanceId}},
	}
	client.AddHeader("Content-Type", "application/json")
	client.AddHeader("x-acs-dingtalk-access-token", token)

	resp, err := client.Do()
	if err != nil {
		log.ErrorFields("dingTalkBpm GetDepartmentInfo error", map[string]interface{}{"err": err})
		return ProcessInstanceItem{}, err
	}
	defer resp.Body.Close()

	type Result struct {
		Errcode int64               `json:"errcode"`
		Errmsg  string              `json:"errmsg"`
		Result  ProcessInstanceItem `json:"result"`
	}

	bodyStr, _ := ioutil.ReadAll(resp.Body)
	log.ErrorFields("GetProcessInstance==%+v", map[string]interface{}{"resp Body": string(bodyStr)})

	var process Result
	err = json.Unmarshal(bodyStr, &process)
	if err != nil {
		log.ErrorFields("dingTalkBpm GetDepartmentInfo Body.FromToJson error", map[string]interface{}{"err": err})
		return ProcessInstanceItem{}, err
	}

	if process.Errcode != 0 {
		log.ErrorFields("dingTalkBpm GetDepartmentInfo error", map[string]interface{}{"errmsg": process.Errmsg})
		return ProcessInstanceItem{}, errors.New(process.Errmsg)
	}

	return process.Result, nil

}

func PushWorkMessageToDingTalk(token string, dingUserIds []string, msg map[string]interface{}) error {

	body, _ := json.Marshal(map[string]interface{}{
		"agent_id":    config.Config.DingTalkBpm.AgentId,
		"userid_list": strings.Join(dingUserIds, ","),
		"msg":         msg,
	})
	fmt.Printf("msg:%v \n", string(body))
	client := &httpClient.Request{
		Url:    "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2",
		Method: "POST",
		Query:  url.Values{"access_token": {token}},
		Body:   strings.NewReader(string(body)),
	}
	client.AddHeader("Content-Type", "application/json")

	resp, err := client.Do()
	if err != nil {
		log.ErrorFields("dingTalkBpm PushWorkMessageToDingTalk error", map[string]interface{}{"err": err})
		return err
	}
	defer resp.Body.Close()

	type Result struct {
		Errcode   int64  `json:"errcode"`
		Errmsg    string `json:"errmsg"`
		RequestId string `json:"request_id"`
		TaskId    string `json:"task_id"`
	}

	bodyStr, _ := ioutil.ReadAll(resp.Body)
	log.ErrorFields("PushWorkMessageToDingTalk", map[string]interface{}{"resp Body": string(bodyStr)})

	return nil
}
