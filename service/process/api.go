package process

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/util/httpClient"
	"crypto/tls"
	"encoding/base64"

	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	"app/org/scs/erpv2/api/service/auth"
	"app/org/scs/erpv2/api/service/process/dingTalkBpm"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
)

// NewDispatchProcess 发起蓝凌审批流程
// NewDispatchProcess @creatorUser *MainUser 发起人信息
// NewDispatchProcess @templateFormId string 对应表单模板唯一标识
// NewDispatchProcess @processTitle string 流程标题
// NewDispatchProcess @itemId int 对应业务系统数据主键ID
// NewDispatchProcess @tableName string 对应业务系统的表单数据存的数据表名称
// NewDispatchProcess @tableStatusField string 对应业务系统表中的流程状态标识字段
// NewDispatchProcess @param string 对应业务系统的表单数据
// NewDispatchProcess @formData map[string]interface{} 流程中需要用到的参数字段以及对应的值
func NewDispatchProcess(creatorUser *auth.AuthUser, templateFormId, processTitle string, itemId int64, tableName, tableStatusField, param string, formData map[string]interface{}) (string, error) {
	//根据流程模板创建流程草稿
	var template map[string]string
	for i := range config.LbpmFormTemplates {
		if config.LbpmFormTemplates[i]["TemplateFormId"] == templateFormId {
			template = config.LbpmFormTemplates[i]
		}
	}

	if template == nil || len(template) == 0 {
		return "", errors.New("template not found")
	}

	formInstanceId := util.GenerateId()
	formId := lbpmApi.FormId{
		SysId:          config.Config.Lbpm.SysId,
		ModelId:        template["ModelId"],
		TemplateFormId: template["TemplateFormId"],
		FormInstanceId: fmt.Sprintf("%v", formInstanceId),
	}
	docSubject := processTitle + "/" + template["TemplateFormName"]
	processId, err := lbpmApi.CallLbpmCreateProcessService(formId, lbpmApi.Creator{LoginName: creatorUser.Phone}, lbpmApi.ExParam{DocSubject: docSubject})

	if err != nil {
		log.ErrorFields("lbpmService.CallLbpmCreateProcessService draft error", map[string]interface{}{"err": err})
		return "", errors.New("lbpmService.CallLbpmCreateProcessService draft error")
	}

	now := model.LocalTime(time.Now())
	processFieldValue, _ := json.Marshal(formData)
	var process = processModel.LbpmApplyProcess{
		FormInstanceId:       formInstanceId,
		TopCorporationId:     creatorUser.TopCorporationId,
		CorporationId:        creatorUser.CorporationId,
		CorporationName:      creatorUser.CorporationName,
		ApplyUserId:          creatorUser.Id,
		ApplyUserName:        creatorUser.Name,
		ApplyUserMobile:      creatorUser.Phone,
		ProcessId:            processId,
		SysId:                config.Config.Lbpm.SysId,
		ModelId:              formId.ModelId,
		ModelName:            template["ModelName"],
		TemplateFormId:       formId.TemplateFormId,
		TemplateFormName:     template["TemplateFormName"],
		Title:                docSubject,
		ItemId:               itemId,
		ItemTableName:        tableName,
		ItemTableStatusField: tableStatusField,
		Param:                []byte(param),
		ProcessFieldValue:    processFieldValue,
		ApplyAt:              &now,
		Status:               util.ProcessStatusForDoing,
	}

	tx := model.DB().Begin()
	err = process.TransactionCreate(tx)
	if err != nil {
		log.ErrorFields("process.Create error", map[string]interface{}{"err": err})
		tx.Rollback()
		return "", errors.New("process.Create error")
	}

	processParam, _ := json.Marshal(map[string]interface{}{
		"param":         map[string]interface{}{"auditNote": "", "operationName": ""},
		"processId":     processId,
		"activityType":  "draftWorkitem",
		"operationType": "drafter_submit",
	})

	//存入发起人到审批相关的处理人表
	processHandler := processModel.LbpmApplyProcessHasHandler{
		FormInstanceId: process.FormInstanceId,
		ProcessId:      process.ProcessId,
		NodeType:       util.ProcessNodeTypeForCreator,
		UserId:         process.ApplyUserId,
		UserName:       process.ApplyUserName,
		UserMobile:     process.ApplyUserMobile,
		Node:           util.ProcessNodeNameForCreate,
		ProcessParam:   processParam,
		Status:         util.ProcessNodeHandleStatusForDone,
		Result:         "SUCCESS",
		StartAt:        &now,
		EndAt:          &now,
	}
	err = processHandler.TransactionCreate(tx)
	if err != nil {
		tx.Rollback()
		return "", errors.New("processHandler.TransactionCreate error:" + err.Error())
	}
	tx.Commit()

	//提交草稿并发起流程审批
	err = lbpmApi.CallLbpmApproveProcessService(processId, string(process.ProcessFieldValue), formId, lbpmApi.Creator{
		LoginName: creatorUser.Phone,
	}, string(processParam))

	if err != nil {
		return "", err
	}

	return processId, nil
}

// RestartProcess 流程重新发起
// RestartProcess @creatorUser  发起人信息
// RestartProcess @processId string 流程唯一标识
// RestartProcess @param string 对应业务系统的表单数据
// RestartProcess @formData map[string]interface{} 流程中需要用到的参数字段以及对应的值
func RestartProcess(creatorUser *auth.AuthUser, processId, param string, formData map[string]interface{}) error {
	//根据流程ID获取流程
	var process processModel.LbpmApplyProcess
	err := process.GetProcess(processId, []int64{util.ProcessStatusForTerminate, util.ProcessStatusForRefuse})
	if err != nil {
		return err
	}
	if process.ApplyUserId != creatorUser.Id {
		return errors.New("process creator dont consistent with user")
	}

	process.Status = util.ProcessStatusForDoing
	process.Param = []byte(param)
	process.ProcessFieldValue, _ = json.Marshal(formData)

	tx := model.DB().Begin()
	err = process.TransactionUpdate(tx)
	if err != nil {
		log.ErrorFields("process.Restart Update error", map[string]interface{}{"err": err})
		tx.Rollback()
		return errors.New("process.Restart Update error")
	}

	processParam, _ := json.Marshal(map[string]interface{}{
		"param":         map[string]interface{}{"auditNote": "", "operationName": ""},
		"processId":     processId,
		"activityType":  "draftWorkitem",
		"operationType": "drafter_submit",
	})
	formId := lbpmApi.FormId{
		SysId:          config.Config.Lbpm.SysId,
		ModelId:        process.ModelId,
		TemplateFormId: process.TemplateFormId,
		FormInstanceId: fmt.Sprintf("%v", process.FormInstanceId),
	}
	//提交草稿并发起流程审批
	err = lbpmApi.CallLbpmApproveProcessService(processId, string(process.ProcessFieldValue), formId, lbpmApi.Creator{
		LoginName: creatorUser.Phone,
	}, string(processParam))

	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()

	return nil
}

type HandlerUser struct {
	Id     int64
	Name   string
	Mobile string
}

// HandleProcess 蓝凌流程的处理、审批
func HandleProcess(process processModel.LbpmApplyProcess, handler HandlerUser, processParam string) error {
	formId := lbpmApi.FormId{
		SysId:          process.SysId,
		ModelId:        process.ModelId,
		TemplateFormId: process.TemplateFormId,
		FormInstanceId: fmt.Sprintf("%v", process.FormInstanceId),
	}

	err := lbpmApi.CallLbpmApproveProcessService(process.ProcessId, string(process.ProcessFieldValue), formId, lbpmApi.Creator{
		LoginName: handler.Mobile,
	}, processParam)

	if err != nil {
		return err
	}

	return nil
}

type ProcessNodeInfo struct {
	NodeId      string `json:"nodeId"`
	ProcessType string `json:"processType"`
	WorkItems   []struct {
		Expecter string `json:"expecter"`
	} `json:"workitems"`
}

// CurrentProcessNode 流程当前节点信息
func CurrentProcessNode(processId string, formId lbpmApi.FormId) ProcessNodeInfo {
	nodeByte, err := lbpmApi.CallLbpmGetCurrentNodesInfoService(processId, formId)
	if err != nil {
		log.ErrorFields("CurrentProcessNode lbpmService.CallLbpmGetCurrentNodesInfoService error", map[string]interface{}{"err": err})
		return ProcessNodeInfo{}
	}
	fmt.Printf("nodeByte==================%+v \r\n", string(nodeByte))

	var nodeInfo []ProcessNodeInfo
	err = json.Unmarshal(nodeByte, &nodeInfo)
	if err != nil {
		log.ErrorFields("CurrentProcessNode json.Unmarshal error", map[string]interface{}{"err": err})
		return ProcessNodeInfo{}
	}
	if len(nodeInfo) == 0 {
		return ProcessNodeInfo{}
	}
	return nodeInfo[0]
}

type LbpmPage struct {
	TotalSize   int64 `json:"totalSize"`
	PageSize    int64 `json:"pageSize"`
	CurrentPage int64 `json:"currentPage"`
}
type ApprovingProcessItem struct {
	ProcessId      string `json:"processId"`
	DocSubject     string `json:"docSubject"`
	FormInstanceId string `json:"formInstanceId"`
}
type ApprovingProcess struct {
	Page  LbpmPage               `json:"page"`
	Datas []ApprovingProcessItem `json:"datas"`
}

// GetApprovingList 蓝领流程：获取用户正在审批的流程
func GetApprovingList(handler HandlerUser, keyword string) ([]ApprovingProcessItem, error) {
	var page int64 = 1
	var limit int64 = 20

	var processes []ApprovingProcessItem
loop:
	data, err := lbpmApi.CallLbpmGetUnApprovingListsService(lbpmApi.Creator{LoginName: handler.Mobile}, keyword, "", page, limit)
	if err != nil {
		log.ErrorFields("lbpmApi.CallLbpmGetUnApprovingListsService error", map[string]interface{}{"err": err})
		return nil, errors.New("CallLbpmGetUnApprovingListsService error")
	}
	fmt.Printf("GetApprovingList data=============%+v \r\n", string(data))

	var approvingProcess ApprovingProcess

	err = json.Unmarshal(data, &approvingProcess)
	if err != nil {
		log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
		return nil, errors.New("CallLbpmGetUnApprovingListsService json.Unmarshal error")
	}
	fmt.Printf("GetApprovingList approvingProcess=============%+v \r\n", approvingProcess)

	processes = append(processes, approvingProcess.Datas...)

	if int64(len(processes)) < approvingProcess.Page.TotalSize {
		page = page + 1
		goto loop
	}

	return processes, nil
}

// CheckIsProcessRelater 蓝凌流程：判断人员是否是流程的相关人员
func CheckIsProcessRelater(itemId int64, templateFormId string, userId int64) bool {
	if templateFormId == "" {
		return false
	}

	var process processModel.LbpmApplyProcess
	err := process.GetApproveProcess(templateFormId, itemId, []int64{})
	if err != nil {
		return false
	}

	//处理人
	var handler processModel.LbpmApplyProcessHasHandler
	return handler.IsProcessRelater(userId, process.FormInstanceId)
}

// ResetProcessFormFieldValue 重置流程审批过程中的条件字段值
func ResetProcessFormFieldValue(itemId int64, tableName string, params map[string]interface{}) error {
	var process processModel.LbpmApplyProcess
	err := process.GetProcessByItemId(itemId, tableName)
	if err != nil {
		return err
	}

	var fieldValues map[string]interface{}
	err = json.Unmarshal(process.ProcessFieldValue, &fieldValues)
	if err != nil {
		return err
	}

	for i := range fieldValues {
		if _, ok := params[i]; ok {
			fieldValues[i] = params[i]
		}
	}
	process.ProcessFieldValue, _ = json.Marshal(fieldValues)

	return process.UpdateProcessFieldValue()
}

func ResetProcessColumnValue(itemId int64, tableName, column string, value interface{}) error {
	var process processModel.LbpmApplyProcess
	err := process.GetProcessByItemId(itemId, tableName)
	if err != nil {
		return err
	}

	return process.UpdateColumn(column, value)
}

type ProcessOperationAuth struct {
	HasTerminateAuth bool `json:"HasTerminateAuth"` //是否有撤回权限
	HasAbandonAuth   bool `json:"HasAbandonAuth"`   //是否有废弃权限
	HasApprovalAuth  bool `json:"HasApprovalAuth"`  //是否有审批权限
	HasRestartAuth   bool `json:"HasRestartAuth"`   //是否有重新发起权限
	IsCreator        bool `json:"IsCreator"`        //是否是发起人
	IsHandler        bool `json:"IsHandler"`        //是否是审批人
	IsCurrentHandler bool `json:"IsCurrentHandler"` //是否是当前节点审批人
	IsNoticer        bool `json:"IsNoticer"`        //是否是抄送人
}

// LbpmProcessOperationAuth 人员的操作权限
func LbpmProcessOperationAuth(process processModel.LbpmApplyProcess, userId int64) ProcessOperationAuth {
	var operationAuth ProcessOperationAuth
	//是否是发起人
	operationAuth.IsCreator = process.ApplyUserId == userId
	//是否是处理人
	operationAuth.IsHandler = (&processModel.LbpmApplyProcessHasHandler{}).IsProcessHandler(userId, process.FormInstanceId, util.ProcessNodeTypeForApprove, 0)
	//是否是当前节点的处理人
	operationAuth.IsCurrentHandler = (&processModel.LbpmApplyProcessHasHandler{}).IsProcessHandler(userId, process.FormInstanceId, util.ProcessNodeTypeForApprove, util.ProcessNodeHandleStatusForDoing)
	//是否是抄送人
	operationAuth.IsNoticer = (&processModel.LbpmApplyProcessHasHandler{}).IsProcessHandler(userId, process.FormInstanceId, util.ProcessNodeTypeForNotice, 0)

	//流程已经审批完成、流程已经被废弃=》所有人都没有任何操作权限
	if process.Status == util.ProcessStatusForDone || process.Status == util.ProcessStatusForAbandon {
		return operationAuth
	}

	//流程在审批中
	if process.Status == util.ProcessStatusForDoing {
		//发起人=》有撤回权限、废弃权限
		if operationAuth.IsCreator {
			operationAuth.HasTerminateAuth = true
			operationAuth.HasAbandonAuth = true
		}
		if operationAuth.IsCurrentHandler {
			operationAuth.HasApprovalAuth = true
		}
	}

	//流程被驳回到起草节点、流程被撤回
	if process.Status == util.ProcessStatusForRefuse || process.Status == util.ProcessStatusForTerminate {
		//发起人=》有废弃权限、重新发起权限
		if operationAuth.IsCreator {
			operationAuth.HasAbandonAuth = true
			operationAuth.HasRestartAuth = true
		}
	}

	return operationAuth
}

func DispatchDingTalkProcess(topCorporationId, itemId int64, tableName, param, templateFormId string, applier HandlerUser, formFieldValue []map[string]string) error {
	//获取钉钉ACCESS_TOKEN
	token, err := dingTalkBpm.GetAccessToken()
	if err != nil {
		log.ErrorFields("dingTalkBpm.GetAccessToken error", map[string]interface{}{"err": err})
		return err
	}

	fmt.Printf("dingTalkBpm.GetAccessToken token==============%+v \n", token)

	//获取发起人在钉钉的UserId
	dingUserId, err := dingTalkBpm.GetUserId(applier.Mobile, token)
	if err != nil {
		log.ErrorFields("dingTalkBpm.GetUserId error", map[string]interface{}{"err": err})
		return err
	}

	dingUserInfo, err := dingTalkBpm.GetUserInfo(dingUserId, token)
	if err != nil || len(dingUserInfo.DeptIdList) == 0 {
		log.ErrorFields("dingTalkBpm.GetUserInfo error", map[string]interface{}{"err": err})
		return err
	}

	var processFieldValueMap = map[string]interface{}{
		"originatorUserId":    dingUserId,
		"deptId":              dingUserInfo.DeptIdList[0],
		"processCode":         config.DingTalkBpmProcessCodes[templateFormId],
		"microappAgentId":     config.Config.DingTalkBpm.AgentId,
		"formComponentValues": formFieldValue,
	}

	body, _ := json.Marshal(processFieldValueMap)

	processInstanceId, err := dingTalkBpm.DispatchProcess(token, body)
	if err != nil {
		return err
	}

	dingProcess, err := dingTalkBpm.GetProcessInstance(processInstanceId, token)
	if err != nil {
		return err
	}
	processFieldValue, _ := json.Marshal(processFieldValueMap)
	at := model.LocalTime(time.Now())
	//流程信息入库
	var process = processModel.DingTalkApplyProcess{
		ProcessInstanceId:    processInstanceId,
		ProcessInstanceTitle: dingProcess.Title,
		ProcessBusinessId:    dingProcess.BusinessId,
		TopCorporationId:     topCorporationId,
		ItemId:               itemId,
		ItemTableName:        tableName,
		Param:                []byte(param),
		ProcessFieldValue:    processFieldValue,
		ApplyUserId:          applier.Id,
		ApplyUserName:        applier.Name,
		ApplyUserMobile:      applier.Mobile,
		ApplyDingTalkUserId:  dingUserId,
		ProcessCode:          config.DingTalkBpmProcessCodes[templateFormId],
		TemplateFormId:       templateFormId,
		Status:               util.ApplyStatusForDoing,
		ApplyAt:              &at,
	}

	tx := model.DB().Begin()
	err = process.TransactionCreate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("process.TransactionCreate error", map[string]interface{}{"err": err})
		return err
	}

	//发起人入库
	var handler = processModel.DingTalkApplyProcessHasHandler{
		DingTalkApplyProcessId: process.Id,
		ProcessInstanceId:      processInstanceId,
		NodeType:               util.ProcessNodeTypeForCreator,
		UserId:                 applier.Id,
		UserName:               applier.Name,
		UserMobile:             applier.Mobile,
		UserDingTalkUserId:     dingUserId,
		Status:                 util.ProcessNodeHandleStatusForDone,
		Result:                 util.ProcessNodeHandleResultForPass,
		StartAt:                &at,
		EndAt:                  &at,
	}

	err = handler.TransactionCreate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("handler.TransactionCreate error", map[string]interface{}{"err": err})
		return err
	}

	tx.Commit()
	return nil
}

func BuildProcessHandler(process processModel.LbpmApplyProcess, user HandlerUser, messageType int64) {
	now := model.LocalTime(time.Now())

	//当流程被驳回到起草节点，流程的状态的是驳回状态，此时发起人可以重新发起流程
	if process.Status == util.ProcessStatusForRefuse {
		messageType = util.ProcessNodeTypeForReCreator
	}

	//var lbpmHandlers []string
	var nodeName string
	var approvalType = util.ProcessNodeApprovalTypeForSerial
	var isExistCurrentHandler bool
	//当消息类型是审批，需要获取当前审批节点类型：串行、并行、会审、会签
	if messageType == util.ProcessNodeTypeForApprove {
		node := CurrentProcessNode(process.ProcessId, lbpmApi.FormId{
			SysId:          process.SysId,
			ModelId:        process.ModelId,
			TemplateFormId: process.TemplateFormId,
			FormInstanceId: fmt.Sprintf("%v", process.FormInstanceId),
		})
		nodeName = node.NodeId

		//for _, w := range node.WorkItems {
		//	lbpmHandlers = append(lbpmHandlers, w.Expecter)
		//}
		//并行审批
		if node.ProcessType == "1" {
			approvalType = util.ProcessNodeApprovalTypeForConcurrence
		}

		//如果当前节点的当前审批人已经在数据库存在，不需要重复创建（催办无需重复创建审批人）
		isExistCurrentHandler = (&processModel.LbpmApplyProcessHasHandler{}).IsExistHandler(process.FormInstanceId, messageType, user.Id, util.ProcessNodeHandleStatusForDoing, nodeName)
	}

	if !isExistCurrentHandler {
		var handler = processModel.LbpmApplyProcessHasHandler{
			FormInstanceId: process.FormInstanceId,
			ProcessId:      process.ProcessId,
			NodeType:       messageType,
			Node:           nodeName,
			ApprovalType:   int64(approvalType),
			UserId:         user.Id,
			UserName:       user.Name,
			UserMobile:     user.Mobile,
			Status:         util.ProcessNodeHandleStatusForDoing,
			StartAt:        &now,
		}

		err := handler.Create()

		if err != nil {
			log.ErrorFields("LbpmPushMessage handler.Create error", map[string]interface{}{"err": err})
			return
		}
	}

	go UpdateProcessCurrentHandler(process)
	return
}

func UpdateProcessCurrentHandler(process processModel.LbpmApplyProcess) {
	time.Sleep(3 * time.Second)
	// 通过查询流程处理人表 获取流程当前处理人
	handlers := (&processModel.LbpmApplyProcessHasHandler{}).GetProcessHandlers(process.FormInstanceId)

	var userNames []string
	for _, h := range handlers {
		if h.NodeType == util.ProcessNodeTypeForApprove && h.Status == util.ProcessNodeHandleStatusForDoing {
			userNames = append(userNames, h.UserName)
		}
	}

	names := util.RemoveRepeatByMap(userNames)
	err := process.UpdateColumn("CurrentHandlerUserName", strings.Join(names, ","))
	if err != nil {
		log.ErrorFields("process.UpdateColumn error", map[string]interface{}{"err": err})
		return
	}
}

func HandleHistory() {
	var codes = []string{""}
	for i := range codes {
		workOrder := (&workOrderModel.WorkOrder{}).FindByCode(codes[i])
		if workOrder.Id > 0 {
			var process processModel.LbpmApplyProcess
			err := process.GetProcessByItemId(workOrder.Id, workOrder.TableName())
			if err == nil {
				UpdateProcessCurrentHandler(process)
			}
		}
	}
}

type SSOSessionIdResult struct {
	Result    bool   `json:"result"`
	ErrorMsg  string `json:"errorMsg"`
	SessionId string `json:"sessionId"`
}

func GetRestApiAuthToken() string {
	plainTxt := fmt.Sprintf("%s:%s", config.Config.Lbpm.RestApiAuthAccount, config.Config.Lbpm.RestApiAuthPassword)
	return "Basic " + base64.StdEncoding.EncodeToString([]byte(plainTxt))
}

func GetSSOSessionId(account string) (string, error) {
	request := httpClient.Request{
		Method:    "POST",
		Url:       fmt.Sprintf("%sapi/sys-authentication/loginService/getLoginSessionId?loginName=%s", config.Config.Lbpm.LbpmDomain, account),
		Insecure:  true,
		TlsConfig: &tls.Config{InsecureSkipVerify: true},
	}

	request.AddHeader("Authorization", GetRestApiAuthToken())
	fmt.Printf("GetSSOSessionId_request_param：%+v \n", map[string]interface{}{"param": request})
	resp, err := request.Do()
	if err != nil {
		log.ErrorFields("GetSSOSessionId error", map[string]interface{}{"err": err})
		return "", err
	}

	defer resp.Body.Close()
	var result SSOSessionIdResult
	err = resp.Body.FromToJson(&result)
	if err != nil {
		log.ErrorFields("GetSSOSessionId error", map[string]interface{}{"err": err})
		return "", err
	}
	log.ErrorFields("GetSSOSessionId Result=======", map[string]interface{}{"result": result})
	if result.Result {
		return result.SessionId, nil
	}
	return "", errors.New(result.ErrorMsg)
}
