package api

import "encoding/xml"

type GetApproverListRequest struct {
	XMLName   xml.Name `xml:"int:GetApproverList"`
	FormId    string   `xml:"formId"`    //FormId struct 转成字符串
	ProcessId string   `xml:"processId"` //流程实例ID 蓝凌系统生成
}

type GetApproverListResponse struct {
	XMLName xml.Name `xml:"GetApproverListResponse"`
	Return  string   `xml:"return"`
}

func (ps *ProcessService) GetApproverList(request *GetApproverListRequest) (*GetApproverListResponse, error) {
	response := new(GetApproverListResponse)
	err := ps.client.Call("GetApproverList", request, response)
	if err != nil {
		return nil, err
	}

	return response, nil
}
