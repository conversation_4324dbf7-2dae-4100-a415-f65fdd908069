package api

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	webservice "app/org/scs/erpv2/api/service/process/lbpm/webservice/client"
	"crypto/md5"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"time"
)

type LbpmProcess interface {
	CreateProcess(request *CreateProcessRequest) (*CreateProcessResponse, error)
	ApproveProcess(request *ApproveProcessRequest) (*ApproveProcessResponse, error)
	GetFlowTemplateList(request *GetFlowTemplateListRequest) (*GetFlowTemplateListResponse, error)
	GetCurrentNodesInfo(request *GetCurrentNodesInfoRequest) (*GetCurrentNodesInfoResponse, error)
	GetApproverList(request *GetApproverListRequest) (*GetApproverListResponse, error)
	GetUnApprovingLists(request *GetUnApprovingListsRequest) (*GetUnApprovingListsResponse, error)
}

type ProcessService struct {
	client *webservice.Client
}

// NewProcessService 实例化client  用于请求LBPM接口
func NewProcessService() *ProcessService {
	authPassword := md5.Sum([]byte(config.Config.Lbpm.AuthPassword))
	client := webservice.NewClient(
		config.Config.Lbpm.LbpmDomain+"sys/webservice/flowWebService",
		webservice.WithTimeout(time.Second*50),
		webservice.WithBasicAuth(config.Config.Lbpm.AuthAccount, fmt.Sprintf("%x", authPassword)),
		webservice.WithTLS(&tls.Config{InsecureSkipVerify: true}),
	)
	return &ProcessService{
		client: client,
	}
}

// CallLbpmCreateProcessService 调用蓝凌创建流程实例服务 生成流程实例  返回流程实例ID
func CallLbpmCreateProcessService(formId FormId, creator Creator, exParam ExParam) (string, error) {
	service := NewProcessService()
	//获取flowTemplateId
	//flowTemplate, err := CallLbpmGetFlowTemplateListService(service, GetFlowTemplateListFormId{SysId: formId.SysId, ModelId: formId.ModelId, TemplateFormId: formId.TemplateFormId}, creator.LoginName)
	//
	//if err != nil {
	//	return "", err
	//}

	formIdByte, _ := json.Marshal(formId)
	creatorByte, _ := json.Marshal(creator)
	exParamByte, _ := json.Marshal(exParam)

	var request = CreateProcessRequest{
		FlowTemplateId: "",
		FormId:         string(formIdByte),
		Creator:        string(creatorByte),
		ExParam:        string(exParamByte),
	}

	rsp, err := service.CreateProcess(&request)

	if err != nil {
		return "", err
	}

	fmt.Printf("=====service.CreateProcess: %+v,%+v \n", rsp.Return, err)
	if ok, data := formatReturn(rsp.Return); ok {
		return string(data), nil
	}

	return "", errors.New(rsp.Return)

}

// CallLbpmApproveProcessService 发起流程||处理流程审批
func CallLbpmApproveProcessService(processId, formData string, formId FormId, handler Creator, processParam string) error {
	service := NewProcessService()

	formIdByte, _ := json.Marshal(formId)
	handlerByte, _ := json.Marshal(handler)
	processParamByte, _ := json.Marshal(map[string]interface{}{"sysWfBusinessForm.fdParameterJson": processParam})

	var request = ApproveProcessRequest{
		FormId:       string(formIdByte),
		ProcessId:    processId,
		Handler:      string(handlerByte),
		FormData:     formData,
		ProcessParam: string(processParamByte),
	}

	rsp, err := service.ApproveProcess(&request)

	if err != nil {
		return err
	}

	if ok, _ := formatReturn(rsp.Return); !ok {
		return errors.New(rsp.Return)
	}

	return nil

}

// CallLbpmGetCurrentNodesInfoService 获取流程当前节点信息
func CallLbpmGetCurrentNodesInfoService(processId string, formId FormId) ([]byte, error) {
	service := NewProcessService()

	formIdByte, _ := json.Marshal(formId)

	var request = GetCurrentNodesInfoRequest{
		FormId:    string(formIdByte),
		ProcessId: processId,
	}

	rsp, err := service.GetCurrentNodesInfo(&request)

	if err != nil {
		return nil, err
	}
	ok, nodeByte := formatReturn(rsp.Return)
	if !ok {
		return nil, errors.New(rsp.Return)
	}

	return nodeByte, nil

}

// CallLbpmGetApproverListService 获取流程当前节点的审批人
func CallLbpmGetApproverListService(processId string, formId FormId) ([]byte, error) {
	service := NewProcessService()

	formIdByte, _ := json.Marshal(formId)

	var request = GetApproverListRequest{
		FormId:    string(formIdByte),
		ProcessId: processId,
	}

	rsp, err := service.GetApproverList(&request)

	if err != nil {
		return nil, err
	}
	ok, nodeByte := formatReturn(rsp.Return)
	if !ok {
		return nil, errors.New(rsp.Return)
	}

	return nodeByte, nil

}

// CallLbpmGetFlowTemplateListService 获取业务表单关联的流程模板的ID（只在一个业务表单对应多个流程模板时需要）
func CallLbpmGetFlowTemplateListService(service *ProcessService, formId GetFlowTemplateListFormId, actionUid string) (GetFlowTemplateListResponseVal, error) {
	formIdStr, err := json.Marshal(formId)
	if err != nil {
		return GetFlowTemplateListResponseVal{}, err
	}

	rsp, err := service.GetFlowTemplateList(&GetFlowTemplateListRequest{
		ActionUid: actionUid,
		FormId:    string(formIdStr),
	})

	if err != nil {
		return GetFlowTemplateListResponseVal{}, err
	}

	if ok, data := formatReturn(rsp.Return); ok {
		var values []GetFlowTemplateListResponseVal
		err := json.Unmarshal(data, &values)
		if err != nil {
			return GetFlowTemplateListResponseVal{}, err
		}

		return values[0], nil
	}

	return GetFlowTemplateListResponseVal{}, errors.New(rsp.Return)
}

// CallLbpmGetUnApprovingListsService 获取用户待审批的流程列表
func CallLbpmGetUnApprovingListsService(actionUid Creator, keyword string, status string, page, limit int64) ([]byte, error) {
	service := NewProcessService()
	actionUidStr, err := json.Marshal(actionUid)
	if err != nil {
		return nil, err
	}

	conditions := UnApprovingListCondition{
		DocSubject: keyword,
		DocStatus:  status,
	}

	conditionStr, _ := json.Marshal(conditions)

	rsp, err := service.GetUnApprovingLists(&GetUnApprovingListsRequest{
		ActionUid:  string(actionUidStr),
		Conditions: string(conditionStr),
		PageNo:     fmt.Sprintf("%v", page),
		PageSize:   fmt.Sprintf("%v", limit),
	})

	if err != nil {
		return nil, err
	}

	fmt.Printf("====service.GetUnApprovingLists===============%+v \n", rsp)

	ok, data := formatReturn(rsp.Return)
	if !ok {
		log.ErrorFields("GetUnApprovingLists response error", map[string]interface{}{"data": data})
		return nil, errors.New("GetUnApprovingLists response error")
	}

	return data, nil
}

//格式化接口返回值
func formatReturn(str string) (ok bool, res []byte) {
	returnByte := []byte(str)
	ok = string(returnByte[0]) == "T"
	if len(returnByte) > 1 {
		res = returnByte[2:]
	}

	return
}

func CallLbpmGetAuditOptionListService(actionUid Creator, processId string, formId FormId, page, limit int64) ([]byte, error) {
	service := NewProcessService()
	actionUidStr, err := json.Marshal(actionUid)
	if err != nil {
		return nil, err
	}

	formIdByte, _ := json.Marshal(formId)

	request := GetAuditOptionListRequest{
		ActionUid: string(actionUidStr),
		FormId:    string(formIdByte),
		ProcessId: processId,
		PageNo:    fmt.Sprintf("%v", page),
		PageSize:  fmt.Sprintf("%v", limit),
	}
	rsp, err := service.GetAuditOptionList(&request)

	if err != nil {
		return nil, err
	}

	fmt.Printf("====service.GetAuditOptionList===============%+v,request:%+v \n", rsp, request)

	ok, data := formatReturn(rsp.Return)
	if !ok {
		log.ErrorFields("GetAuditOptionList response error", map[string]interface{}{"data": data})
		return nil, errors.New("GetAuditOptionList response error")
	}

	return data, nil
}
