package api

import "encoding/xml"

type GetUnApprovingListsRequest struct {
	XMLName    xml.Name `xml:"int:GetUnApprovingLists"`
	ActionUid  string   `xml:"actionUid"`  //处理人
	Conditions string   `xml:"conditions"` //模糊查询条件
	PageNo     string   `xml:"pageNo"`     //页码 默认1
	PageSize   string   `xml:"pageSize"`   //每页数据 默认20
}

type UnApprovingListCondition struct {
	DocSubject string
	DocStatus  string
}

type GetUnApprovingListsResponse struct {
	XMLName xml.Name `xml:"GetUnApprovingListsResponse"`
	Return  string   `xml:"return"`
}

func (ps *ProcessService) GetUnApprovingLists(request *GetUnApprovingListsRequest) (*GetUnApprovingListsResponse, error) {
	response := new(GetUnApprovingListsResponse)
	err := ps.client.Call("GetUnApprovingLists", request, response)
	if err != nil {
		return nil, err
	}

	return response, nil
}
