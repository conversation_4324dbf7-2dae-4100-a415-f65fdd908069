package api

import (
	"encoding/xml"
)

type CreateProcessRequest struct {
	XMLName        xml.Name `xml:"int:CreateProcess"`
	FlowTemplateId string   `xml:"flowTemplateId"` // 流程模板ID 蓝凌系统获取
	FormId         string   `xml:"formId"`         // FormId struct 转成字符串
	Creator        string   `xml:"creator"`        // Creator struct 转成字符串
	ExParam        string   `xml:"exParam"`        // ExParam struct 转成字符串
}

type CreateProcessResponse struct {
	XMLName xml.Name `xml:"CreateProcessResponse"`
	Return  string   `xml:"return"`
}

// FormId 对接表单信息
type FormId struct {
	SysId          string `json:"sysId"`          //系统标识  蓝凌引擎中获取
	ModelId        string `json:"modelId"`        //业务模块的ID  业务系统自定义
	TemplateFormId string `json:"templateFormId"` //表单模板ID 业务系统自定义
	FormInstanceId string `json:"formInstanceId"` //表单实例ID 业务系统自定义
}

// Creator 创建人
type Creator struct {
	LoginName string `json:"LoginName"` //创建人账号 登录蓝凌系统的账号  业务系统需和蓝凌引擎人员打通，实现单点登录
}

// ExParam 扩展参数 目前可用来传递主题名称
type ExParam struct {
	DocSubject string `json:"docSubject"`
}

func (ps *ProcessService) CreateProcess(request *CreateProcessRequest) (*CreateProcessResponse, error) {
	response := new(CreateProcessResponse)
	err := ps.client.Call("CreateProcess", request, response)
	if err != nil {
		return nil, err
	}

	return response, nil
}
