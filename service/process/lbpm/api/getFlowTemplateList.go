package api

import "encoding/xml"

type GetFlowTemplateListRequest struct {
	XMLName    xml.Name `xml:"int:GetFlowTemplateList"`
	ActionUid  string   `xml:"actionUid"`            // 当前处理人登录名
	FormId     string   `xml:"formId"`               // GetFlowTemplateListFormId struct 转成字符串
	CategoryId string   `xml:"categoryId,omitempty"` // Creator struct 转成字符串
}

type GetFlowTemplateListResponse struct {
	XMLName xml.Name `xml:"GetFlowTemplateListResponse"`
	Return  string   `xml:"return"`
}

type GetFlowTemplateListFormId struct {
	SysId          string `json:"sysId"`          //系统标识  蓝凌引擎中获取
	ModelId        string `json:"modelId"`        //业务模块的ID  业务系统自定义
	TemplateFormId string `json:"templateFormId"` //表单模板ID 业务系统自定义
}

type GetFlowTemplateListResponseVal struct {
	FlowTemplateId   string `json:"flowTemplateId"`
	FlowTemplateName string `json:"flowTemplateName"`
	CategoryId       string `json:"categoryId"`
	FormUrl          string `json:"formUrl"`
}

func (ps *ProcessService) GetFlowTemplateList(request *GetFlowTemplateListRequest) (*GetFlowTemplateListResponse, error) {
	response := new(GetFlowTemplateListResponse)
	err := ps.client.Call("GetFlowTemplateList", request, response)
	if err != nil {
		return nil, err
	}

	return response, nil
}
