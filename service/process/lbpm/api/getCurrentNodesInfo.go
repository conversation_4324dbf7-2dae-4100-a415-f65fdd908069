package api

import "encoding/xml"

type GetCurrentNodesInfoRequest struct {
	XMLName   xml.Name `xml:"int:GetCurrentNodesInfo"`
	FormId    string   `xml:"formId"`    //FormId struct 转成字符串
	ProcessId string   `xml:"processId"` //流程实例ID 蓝凌系统生成
}

type GetCurrentNodesInfoResponse struct {
	XMLName xml.Name `xml:"GetCurrentNodesInfoResponse"`
	Return  string   `xml:"return"`
}

func (ps *ProcessService) GetCurrentNodesInfo(request *GetCurrentNodesInfoRequest) (*GetCurrentNodesInfoResponse, error) {
	response := new(GetCurrentNodesInfoResponse)
	err := ps.client.Call("GetCurrentNodesInfo", request, response)
	if err != nil {
		return nil, err
	}

	return response, nil
}
