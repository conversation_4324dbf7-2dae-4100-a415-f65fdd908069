package api

import "encoding/xml"

type ApproveProcessRequest struct {
	XMLName      xml.Name `xml:"int:ApproveProcess"`
	FormId       string   `xml:"formId"`       //FormId struct 转成字符串
	ProcessId    string   `xml:"processId"`    //流程实例ID 蓝凌系统生成
	Handler      string   `xml:"handler"`      //Creator struct 转成字符串
	FormData     string   `xml:"formData"`     // 运行时表单数据值  根据不同的表单传入不同的值
	ProcessParam string   `xml:"processParam"` //ProcessParam struct 转成字符串
}

type ApproveProcessResponse struct {
	XMLName xml.Name `xml:"ApproveProcessResponse"`
	Return  string   `xml:"return"`
}

// ProcessParam 审批参数
type ProcessParam struct {
	ProcessInstanceId string `json:"processInstanceId"`
	HandlerId         string `json:"handlerId"`
	TaskId            string `json:"taskId"`
	OperationType     string `json:"operationType"`
	AuditNote         string `json:"auditNote"`
	CancelHandler     string `json:"cancelHandler"`
}

func (ps *ProcessService) ApproveProcess(request *ApproveProcessRequest) (*ApproveProcessResponse, error) {
	response := new(ApproveProcessResponse)
	err := ps.client.Call("ApproveProcess", request, response)
	if err != nil {
		return nil, err
	}

	return response, nil
}
