package api

import "encoding/xml"

type GetAuditOptionListRequest struct {
	XMLName   xml.Name `xml:"int:GetAuditOptionList"`
	FormId    string   `xml:"formId"`    //FormId struct 转成字符串
	ProcessId string   `xml:"processId"` //流程实例ID 蓝凌系统生成
	ActionUid string   `xml:"actionUid"` //处理人
	PageNo    string   `xml:"pageNo"`    //页码 默认1
	PageSize  string   `xml:"pageSize"`  //每页数据 默认20
	Language  string   `xml:"language"`
}

type GetAuditOptionListResponse struct {
	XMLName xml.Name `xml:"GetAuditOptionListResponse"`
	Return  string   `xml:"return"`
}

func (ps *ProcessService) GetAuditOptionList(request *GetAuditOptionListRequest) (*GetAuditOptionListResponse, error) {
	response := new(GetAuditOptionListResponse)
	err := ps.client.Call("GetAuditOptionList", request, response)
	if err != nil {
		return nil, err
	}

	return response, nil
}
