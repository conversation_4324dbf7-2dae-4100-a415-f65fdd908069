package server

import (
	"bytes"
	"encoding/json"
	"encoding/xml"
	"errors"
	api "github.com/micro/go-micro/v2/api/proto"
	"reflect"
	"strings"
)

// IServer soap 服务
type IServer interface {
	// RegisterMethod 注册服务方法
	RegisterMethod(act Method) error

	// Handler http handler
	Handler(w *api.Response, r *api.Request)
}

type Method interface {
	// Name 名称
	Name() string
	// ReqStruct 请求结构体
	ReqStruct() interface{}
	// RespStruct 结果结构体
	RespStruct() interface{}
	// Do 处理请求
	Do(req interface{}, resp interface{}) error
}

type server struct {
	name      string            `description:"服务名称"`
	namespace string            `description:"wsdl文件的自主命名空间"`
	wsdl      *WsdlDefinitions  `description:"wsdl对象"`
	wsdlBytes []byte            `description:"wsdl序列化信息"`
	methods   map[string]Method `description:"服务方法"`
}

// NewServer 创建一个soap服务体
func NewServer(name string, namespace string) IServer {
	s := &server{
		name:      name,
		namespace: namespace,
		methods:   map[string]Method{},
	}
	s.buildWsdl()
	return s
}

func (s *server) buildWsdl() {
	def := &WsdlDefinitions{
		Tns:      s.namespace,
		TargetNs: s.namespace,
		Soap:     "http://schemas.xmlsoap.org/wsdl/soap/",
		SoapEnv:  "http://schemas.xmlsoap.org/soap/envelope/",
		Wsdl:     "http://schemas.xmlsoap.org/wsdl/",
		Xsd:      "http://www.w3.org/2001/XMLSchema",
		Xsi:      "http://www.w3.org/2001/XMLSchema-instance",
	}
	sch := XsdSchema{
		TargetNamespace: s.namespace,
		Import: []XsdImport{
			{Namespace: "http://schemas.xmlsoap.org/soap/encoding/"},
			{Namespace: "http://schemas.xmlsoap.org/wsdl/"}},
	}
	def.Types.Schemas = append(def.Types.Schemas, sch)

	def.PortType.Name = s.name + "PortType"

	def.Binding.Name = s.name + "Binding"
	def.Binding.Type = "tns:" + def.PortType.Name
	def.Binding.SoapBinding.Style = "rpc"
	def.Binding.SoapBinding.Transport = "http://schemas.xmlsoap.org/soap/http"

	def.Service.Name = s.name
	def.Service.Port = WsdlServicePort{
		Name:    s.name + "Port",
		Binding: "tns:" + def.Binding.Name,
		Address: WsdlServiceAddress{Location: s.namespace},
	}
	s.wsdl = def
}

func (s *server) RegisterMethod(m Method) error {
	s.wsdlBytes = nil
	// 方法名
	methodName := m.Name()
	if _, ok := s.methods[methodName]; ok {
		return errors.New("方法重复注册:" + methodName)
	}
	// message
	err := s.parseMessage(methodName+"Request", m.ReqStruct())
	if err != nil {
		return err
	}
	err = s.parseMessage(methodName+"Response", m.RespStruct())
	if err != nil {
		return err
	}
	s.regWsdl(methodName)
	s.methods[methodName] = m
	return nil
}

// 解析参数并转化为对应的wsdl message
func (s *server) parseMessage(name string, st interface{}) error {
	msg := WsdlMessage{Name: name}
	retype := reflect.TypeOf(st)
	if retype.Kind() == reflect.Ptr {
		retype = retype.Elem()
	} else if retype.Kind() == reflect.Slice {
		retype = retype.Elem()
	}
	if retype.Kind() != reflect.Struct {
		return errors.New(name + "必须是一个结构体")
	}
	// 遍历结构体参数列表
	for i := 0; i < retype.NumField(); i++ {
		name, _ := getTagsInfo(retype.Field(i))
		ik := retype.Field(i).Type.Kind()
		ts, err := checkBaseTypeKind(ik)
		// 如果非基本类型则转为自有命名空间的自定义类型
		if err != nil {
			ts = "tns:" + name + ik.String()
			tp := reflect.New(retype.Field(i).Type).Elem().Interface()
			_ = s.parseMessage(name+ik.String(), tp)
		}
		msg.Part = append(msg.Part, WsdlPart{Name: name, Type: ts})
	}
	s.wsdl.Message = append(s.wsdl.Message, msg)
	return nil
}

func (s *server) regWsdl(methodName string) {
	// portType
	op := WsdlPortTypeOperation{
		Name:   methodName,
		Input:  WsdlPortTypeOperationMessage{Message: "tns:" + methodName + "Request"},
		Output: WsdlPortTypeOperationMessage{Message: "tns:" + methodName + "Response"},
	}
	s.wsdl.PortType.Operations = append(s.wsdl.PortType.Operations, op)
	// binding
	soapIO := WsdlSoapBodyIO{SoapBody: WsdlSoapBody{Use: "encoded", EncodingStyle: "http://schemas.xmlsoap.org/soap/encoding/"}}
	bindOp := WsdlBindingOperation{
		Name: methodName, Input: soapIO,
		Output: soapIO,
		SoapOperation: WsdlSoapOperation{
			Style: "rpc", SoapAction: s.wsdl.Tns + "/" + methodName,
		},
	}
	s.wsdl.Binding.Operations = append(s.wsdl.Binding.Operations, bindOp)
}

func (s *server) Handler(w *api.Response, r *api.Request) {
	w.Header = make(map[string]*api.Pair)
	w.Header["Content-Type"] = &api.Pair{Key: "Content-Type", Values: []string{"text/xml; charset=utf-8"}}
	w.Header["Accept"] = &api.Pair{Key: "Accept", Values: []string{"text/xml"}}

	body := []byte(xml.Header)

	if r.Method == "GET" {
		// 网址带参数wsdl则显示wsdl文件
		uri := strings.Split(r.Url, "?")
		query := uri[len(uri)-1]
		if strings.EqualFold("wsdl", query) {
			if s.wsdlBytes == nil {
				b, err := xml.Marshal(s.wsdl)
				if err != nil {
					body = append(body, []byte("err:"+err.Error())...)
					bo, _ := json.Marshal(body)
					w.Body = string(bo)
					return
				}
				s.wsdlBytes = b
			}

			w.Body = string(s.wsdlBytes)
		} else {
			// 其他情况返回一个提示信息
			w.Body = string([]byte(r.Url))
		}
		return
	}

	// 转化为Envelope对象
	env := Envelope{}
	xml.Unmarshal([]byte(r.Body), &env)

	// 解析请求的方法名字
	var startEle *xml.StartElement
	reader := bytes.NewReader(env.Body.Content)
	de := xml.NewDecoder(reader)
	for {
		t, err := de.Token()
		if err != nil {
			break
		}
		if x, ok := t.(xml.StartElement); ok {
			startEle = &x
			break
		}
	}
	if startEle == nil {
		s.handlerResponseError("接受到的data无效", w)
		return
	}
	s.request(w, de, startEle)
}

func (s *server) handlerResponseError(errMsg string, w *api.Response, errCode ...int64) {
	fault := ErrResult{
		Code:      500,
		ErrString: errMsg,
	}
	if len(errCode) > 0 {
		fault.Code = errCode[0]
	}
	data, _ := xml.Marshal(fault)
	b, _ := xml.Marshal(NewEnvelope(data))
	w.Body = string(b)
}

func (s *server) request(w *api.Response, de *xml.Decoder, startEle *xml.StartElement) {
	methodName := startEle.Name.Local
	m, has := s.methods[methodName]
	if !has {
		s.handlerResponseError("["+methodName+"]不存在", w)
		return
	}
	// 解析入参
	params := m.ReqStruct()
	reqType := reflect.TypeOf(params)
	params = reflect.New(reqType).Elem().Addr().Interface()
	err := de.DecodeElement(params, startEle)
	if err != nil {
		s.handlerResponseError("参数错误:"+err.Error(), w)
		return
	}
	resp := m.RespStruct()
	retype := reflect.TypeOf(resp)
	resp = reflect.New(retype).Elem().Addr().Interface()
	err = m.Do(params, resp)
	if err != nil {
		s.handlerResponseError("请求失败:"+err.Error(), w)
		return
	}
	bs, e := xml.Marshal(resp)
	if e != nil {
		s.handlerResponseError("结果序列化错误:"+e.Error(), w)
		return
	}
	b, _ := xml.Marshal(NewResponseEnvelope(bs))
	w.Body = string(b)
}
