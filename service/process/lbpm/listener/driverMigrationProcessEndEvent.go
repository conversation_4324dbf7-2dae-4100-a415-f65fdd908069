package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"
)

type DriverMigrationProcessEndEvent struct {
}

// Handler 车辆调动审批结束监听事件
func (el DriverMigrationProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("DriverMigrationProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("DriverMigrationProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("DriverMigrationProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	err = process.UpdateStatus(util.ProcessStatusForDone)
	if err != nil {
		log.ErrorFields("DriverMigrationProcessEndEvent process.UpdateStatus error", map[string]interface{}{"err": err})
		return errors.New("DriverMigrationProcessEndEvent process.UpdateStatus error")
	}

	//更新业务数据表中的审批状态
	model.DB().Table(process.ItemTableName).Where("Id = ?", process.ItemId).Update(process.ItemTableStatusField, util.ApplyStatusForDone)

	var migration hrModel.DriverMigration
	err = json.Unmarshal(process.Param, &migration)
	if err != nil {
		log.ErrorFields("DriverMigrationProcessEndEvent DriverMigration FindBy error", map[string]interface{}{"err": err})
		return errors.New("DriverMigrationProcessEndEvent DriverMigration FindBy error")
	}

	//非车队发起的调动，审批通过后司机自动归入机动线路
	if migration.IsFleetApply == util.StatusForFalse {
		//获取车队的机动线路
		var motorLineId int64
		var motorLineName string
		setting := (&settingModel.GlobalSetting{}).GetBy(migration.TopCorporationId, 2)
		if setting.Id != 0 {
			var motorLines []settingModel.GlobalSettingItemForMotorLine
			_ = json.Unmarshal(setting.SettingItem, &motorLines)
			for _, motorLine := range motorLines {
				if motorLine.CorporationId == migration.InCorporationId {
					motorLineId = motorLine.LineId
					line, _ := rpc.GetLineWithId(context.TODO(), motorLineId)
					if line != nil {
						motorLineName = line.Name
					}
				}
			}
		}
		//接收人关联车辆和线路
		records := (&hrModel.DriverMigrationRecord{}).GetRecordsByMigrationId(migration.Id)
		for _, record := range records {
			record.InLineId = motorLineId
			record.InLine = motorLineName
			record.IsMotor = util.StatusForTrue
			err = record.UpdateInLine()
			if err != nil {
				log.ErrorFields("DriverMigrationProcessEndEvent UpdateInLine error", map[string]interface{}{"err": err})
			}
		}
	}
	//立即调动
	if migration.UseAt.ToTime().Unix() <= time.Now().Unix() {
		err = service.UpdateDriverToOet(migration)
		if err != nil {
			log.ErrorFields("DriverMigrationProcessEndEvent service.UpdateDriverToOet error", map[string]interface{}{"err": err})
			return errors.New("DriverMigrationProcessEndEvent service.UpdateDriverToOet error")
		}
	}

	go service.SendDriverMigrationProcessStatusChangeMsg(migration.Id)
	return nil
}
func (el DriverMigrationProcessEndEvent) Id() string {
	return "DriverMigrationProcessEndEvent"
}
func (el DriverMigrationProcessEndEvent) Name() string {
	return "司机调动流程结束事件监听方法"
}
func (el DriverMigrationProcessEndEvent) Desc() string {
	return "司机调动流程审批结束执行的方法"
}
func (el DriverMigrationProcessEndEvent) TemplateFormId() string {
	return config.DriverMigrationApplyFormTemplate
}
