package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	operationModel "app/org/scs/erpv2/api/model/operation"
	processModel "app/org/scs/erpv2/api/model/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"
)

type OperationReportApplyProcessEndEvent struct {
}

func (el OperationReportApplyProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("OperationReportApplyProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("OperationReportApplyProcessEndEvent ProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("ProcessEndEvent process.FindBy error")
	}

	if process.Status != util.ProcessStatusForAbandon {
		approval := (&operationModel.OperationApproval{}).FirstBy(process.ItemId)
		if approval.Id == 0 {
			return errors.New("NOT FOUND approval")
		}

		var relateReports []string
		err = json.Unmarshal(approval.RelateReport, &relateReports)
		if err != nil {
			log.ErrorFields("OperationReportApplyProcessEndEvent json.Unmarshal relateReports error", map[string]interface{}{"err": err})
			return errors.New("json.Unmarshal lineIds error")
		}

		var LineIdStr []string
		err = json.Unmarshal(approval.LineIds, &LineIdStr)
		if err != nil {
			log.ErrorFields("OperationReportApplyProcessEndEvent json.Unmarshal lineIds error", map[string]interface{}{"err": err})
			return errors.New("json.Unmarshal lineIds error")
		}
		reportAts := util.GetDateFromRangeTime(time.Time(approval.StartAt), time.Time(approval.EndAt))
		updateLineVehicleReport := util.Include(relateReports, "line_vehicle_operation_report") || util.Include(relateReports, "driver_work_report") || util.Include(relateReports, "line_salary_report")
		updateIrregularLineReport := util.Include(relateReports, "irregular_line_report")
		updateOutFrequencyAddWorkReport := util.Include(relateReports, "out_frequency_add_work_report")
		var lineIds []int64
		for i := range LineIdStr {
			lineId, _ := strconv.ParseInt(LineIdStr[i], 10, 64)
			lineIds = append(lineIds, lineId)
		}

		for i := range reportAts {
			reportAt, err := time.ParseInLocation(model.DateFormat, reportAts[i], time.Local)
			if err != nil {
				log.ErrorFields("time.ParseInLocation error time.ParseInLocation error", map[string]interface{}{"err": err})
				return errors.New("OperationReportApplyProcessEndEvent time.ParseInLocation error")
			}

			if updateLineVehicleReport {
				err = (&operationModel.LineVehicleMileageReport{}).TransactionUpdateApprovalStatus(model.DB(), approval.CorporationId, lineIds, reportAt, util.StatusForTrue)
				if err != nil {
					log.ErrorFields("OperationReportApplyProcessEndEvent LineVehicleMileageReport UpdateApprovalStatus error", map[string]interface{}{"err": err})
					return errors.New("LineVehicleMileageReport UpdateApprovalStatus error")
				}
			}

			if updateIrregularLineReport {
				err = (&operationModel.IrregularLineReport{}).TransactionUpdateApprovalStatus(model.DB(), approval.CorporationId, lineIds, reportAt, util.StatusForTrue)
				if err != nil {
					log.ErrorFields("OperationReportApplyProcessEndEvent IrregularLineReport UpdateApprovalStatus error", map[string]interface{}{"err": err})
					return errors.New("LineVehicleMileageReport UpdateApprovalStatus error")
				}
			}

			if updateOutFrequencyAddWorkReport {
				err = (&operationModel.OutFrequencyAddWorkReport{}).TransactionUpdateApprovalStatus(model.DB(), approval.CorporationId, lineIds, reportAt, util.StatusForTrue)
				if err != nil {
					log.ErrorFields("OperationReportApplyProcessEndEvent OutFrequencyAddWorkReport UpdateApprovalStatus error", map[string]interface{}{"err": err})
					return errors.New("LineVehicleMileageReport UpdateApprovalStatus error")
				}
			}
		}
	}

	return nil
}
func (el OperationReportApplyProcessEndEvent) Id() string {
	return "OperationReportApplyProcessEndEvent"
}
func (el OperationReportApplyProcessEndEvent) Name() string {
	return "运营报表数据审批流程结束事件"
}
func (el OperationReportApplyProcessEndEvent) Desc() string {
	return "运营报表数据审批流程结束执行的方法"
}
func (el OperationReportApplyProcessEndEvent) TemplateFormId() string {
	return config.OperationReportApplyFormTemplate
}
