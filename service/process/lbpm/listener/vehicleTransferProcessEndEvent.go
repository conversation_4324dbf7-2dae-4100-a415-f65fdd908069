package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/maintenance"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"
)

type VehicleTransferProcessEndEvent struct {
}

// Handler 车辆调动审批结束监听事件
func (el VehicleTransferProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("VehicleTransferProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("VehicleTransferProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("VehicleTransferProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	err = process.UpdateStatus(util.ProcessStatusForDone)

	if err != nil {
		log.ErrorFields("VehicleTransferProcessEndEvent process.UpdateStatus error", map[string]interface{}{"err": err})
		return errors.New("VehicleTransferProcessEndEvent process.UpdateStatus error")
	}

	//更新业务数据表中的审批状态
	model.DB().Table(process.ItemTableName).Where("Id = ?", process.ItemId).Update(process.ItemTableStatusField, util.ApplyStatusForDone)

	var transfer maintenance.VehicleMigration
	err = json.Unmarshal(process.Param, &transfer)
	if err != nil {
		log.ErrorFields("VehicleTransferProcessEndEvent VehicleTransfer FindBy error", map[string]interface{}{"err": err})
		return errors.New("VehicleTransferProcessEndEvent VehicleTransfer FindBy error")
	}
	//var transferAt time.Time
	//if transfer.TransferAt != nil {
	//	transferAt = time.Time(*transfer.TransferAt)
	//}

	//立即调动
	if transfer.UseAt.ToTime().Unix() <= time.Now().Unix() {
		err = service.UpdateVehicleToOet(transfer, util.VehicleTransferTypeForFleet, "")
		if err != nil {
			log.ErrorFields("VehicleTransferProcessEndEvent service.UpdateVehicleToOet error", map[string]interface{}{"err": err})
			return errors.New("VehicleTransferProcessEndEvent service.UpdateVehicleToOet error")
		}
	}

	go service.SendVehicleMigrationProcessStatusChangeMsg(process.ItemId)

	return nil
}
func (el VehicleTransferProcessEndEvent) Id() string {
	return "VehicleTransferProcessEndEvent"
}
func (el VehicleTransferProcessEndEvent) Name() string {
	return "车辆调动流程结束事件监听方法"
}
func (el VehicleTransferProcessEndEvent) Desc() string {
	return "车辆调动流程审批结束执行的方法"
}
func (el VehicleTransferProcessEndEvent) TemplateFormId() string {
	return config.VehicleMigrationApplyFormTemplate
}
