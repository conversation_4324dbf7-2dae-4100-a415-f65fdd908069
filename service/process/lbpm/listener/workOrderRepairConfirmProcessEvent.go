package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"
)

// 报修工单维修结束后进入发起人确认节点时的事件

type RepairConfirmProcessEvent struct {
}

func (el RepairConfirmProcessEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("RepairConfirmProcessEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("RepairConfirmProcessEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("RepairConfirmProcessEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}
	// 获取工单id
	var order workOrderModel.WorkOrder
	_ = json.Unmarshal(process.Param, &order)
	// 获取工单详情
	err = (&order).GetDetail(order.Id)
	if err != nil {
		log.ErrorFields("RepairConfirmProcessEvent GetDetail error", map[string]interface{}{"err": err})
		return errors.New("RepairConfirmProcessEvent GetDetail error")
	}

	order.UpdatesTx(model.DB(), map[string]interface{}{"repairconfirmstartat": time.Now().Format(model.TimeFormat)})

	return nil
}
func (el RepairConfirmProcessEvent) Id() string {
	return "RepairConfirmProcessEvent"
}
func (el RepairConfirmProcessEvent) Name() string {
	return "报修流程维修结束后进入发起人审核节点事件"
}
func (el RepairConfirmProcessEvent) Desc() string {
	return "报修流程维修结束后进入发起人审核节点执行的方法"
}
func (el RepairConfirmProcessEvent) TemplateFormId() string {
	return config.DeviceWorkOrderReportFormTemplate
}
