package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
)

type DriverBecomeWorkerProcessHandleEvent struct {
}

// Handler 司机转正车队长节点监听事件
func (el DriverBecomeWorkerProcessHandleEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("DriverBecomeWorkerProcessHandleEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("DriverBecomeWorkerProcessHandleEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("DriverBecomeWorkerProcessHandleEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	//更新业务数据表中的审批状态
	model.DB().Table(process.ItemTableName).Where("Id = ?", process.ItemId).Update("formstep", util.ProcessFormStepTwo)

	return nil
}
func (el DriverBecomeWorkerProcessHandleEvent) Id() string {
	return "DriverBecomeWorkerProcessHandleEvent"
}
func (el DriverBecomeWorkerProcessHandleEvent) Name() string {
	return "司机转正流程车队长节点监听方法"
}
func (el DriverBecomeWorkerProcessHandleEvent) Desc() string {
	return "司机转正流程车队长节点执行的方法"
}
func (el DriverBecomeWorkerProcessHandleEvent) TemplateFormId() string {
	return config.DriverBecomeWorkerApplyFormTemplate
}
