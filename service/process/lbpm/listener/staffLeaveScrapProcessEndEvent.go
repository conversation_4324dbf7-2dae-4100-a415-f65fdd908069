package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
	"time"
)

type StaffLeaveScrapProcessEndEvent struct {
}

func (el StaffLeaveScrapProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("StaffLeaveProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("StaffLeaveScrapProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("StaffLeaveScrapProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	//查询请假记录信息
	leaveRecord := (&hrModel.ApplyLeaveRecord{}).FirstBy(process.ItemId)
	leaveRecord.ScrapDate = model.LocalTime(time.Now())
	tx := model.DB().Begin()
	err = tx.Updates(&leaveRecord).Error
	if err != nil {
		log.ErrorFields("save error", map[string]interface{}{"err": err})
		tx.Rollback()
		return errors.New("updates error")
	}
	//请假管理这边【年休假】的请假类型作废后，要把请假的天数返还给对应年度的年休假
	if leaveRecord.LeaveType == util.LeaveTypeForAnnualLeave {
		var year int64
		recordDates := (&hrModel.ApplyLeaveRecordDate{}).GetByLeaveRecordId(leaveRecord.Id)
		if len(recordDates) > 0 {
			year = int64(time.Time(recordDates[0].Date).Year())
		}
		leaveManagement := (&hrModel.LeaveManagement{}).GetStaffLeaveByYear(leaveRecord.StaffId, int64(year))
		if leaveManagement.Id != 0 {
			leaveManagement.UsedDay -= leaveRecord.DayCount / 10
			err = tx.Model(&hrModel.LeaveManagement{}).Where("Id=?", leaveManagement.Id).Updates(map[string]interface{}{
				"UsedDay": leaveManagement.UsedDay,
			}).Error
			if err != nil {
				log.ErrorFields("save error", map[string]interface{}{"err": err})
				tx.Rollback()
				return errors.New("年假返还失败")
			}
		}
	}
	tx.Commit()
	return nil
}
func (el StaffLeaveScrapProcessEndEvent) Id() string {
	return "StaffLeaveScrapProcessEndEvent"
}
func (el StaffLeaveScrapProcessEndEvent) Name() string {
	return "员工请假作废流程结束事件监听方法"
}
func (el StaffLeaveScrapProcessEndEvent) Desc() string {
	return "员工请假作废流程结束后异步执行的事件"
}
func (el StaffLeaveScrapProcessEndEvent) TemplateFormId() string {
	return config.StaffLeaveScrapFormTemplate
}
