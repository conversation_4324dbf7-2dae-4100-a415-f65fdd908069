package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
)

type StaffArchiveEditProcessEndEvent struct {
}

func (el StaffArchiveEditProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("StaffArchiveEditProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("StaffArchiveEditProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("StaffArchiveEditProcessEndEvent process.FindBy error")
	}
	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	var staffArchive hrModel.StaffArchive
	err = json.Unmarshal(process.Param, &staffArchive)
	if err != nil {
		log.ErrorFields("StaffArchiveEditProcessEndEvent staffArchive json Unmarshal error", map[string]interface{}{"err": err})
		return errors.New("StaffArchiveEditProcessEndEvent staffArchive json Unmarshal error")
	}

	if staffArchive.IsMineEdit {
		err = service.UpdateMineArchive(staffArchive)
	} else {
		err = service.UpdateStaffArchiveInfo(process.ItemId, staffArchive)
	}

	if err != nil {
		log.ErrorFields("StaffArchiveEditProcessEndEvent UpdateStaffArchiveInfo error", map[string]interface{}{"err": err})
		return errors.New("StaffArchiveEditProcessEndEvent UpdateStaffArchiveInfo error")
	}

	return nil
}

func (el StaffArchiveEditProcessEndEvent) Id() string {
	return "StaffArchiveEditProcessEndEvent"
}
func (el StaffArchiveEditProcessEndEvent) Name() string {
	return "员工档案编辑流程结束事件监听方法"
}
func (el StaffArchiveEditProcessEndEvent) Desc() string {
	return "员工档案编辑流程结束事件"
}
func (el StaffArchiveEditProcessEndEvent) TemplateFormId() string {
	return config.StaffArchiveEditApplyFormTemplate
}
