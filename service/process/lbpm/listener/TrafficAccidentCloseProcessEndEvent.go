package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
	"time"
)

type TrafficAccidentCloseProcessEndEvent struct {
}

func (el TrafficAccidentCloseProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("TrafficAccidentCloseProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("TrafficAccidentCloseProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentCloseProcessEndEvent process.FindBy error")
	}
	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	var accident safetyModel.TrafficAccident
	err = accident.FindBy(process.ItemId)
	if err != nil {
		log.ErrorFields("TrafficAccidentCloseProcessEndEvent TrafficAccident.FindBy error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentCloseProcessEndEvent TrafficAccident.FindBy error: " + err.Error())
	}
	model.DB().Table(process.ItemTableName).Where("Id = ?", process.ItemId).Updates(map[string]interface{}{"isclosed": util.StatusForTrue, "closedat": time.Now().Format(model.TimeFormat)})

	// 事故结案 更新事故预警告警
	go service.CheckAccidentAlarm(accident.Id)
	return nil
}
func (el TrafficAccidentCloseProcessEndEvent) Id() string {
	return "TrafficAccidentCloseProcessEndEvent"
}
func (el TrafficAccidentCloseProcessEndEvent) Name() string {
	return "事故结案流程结束事件监听方法"
}
func (el TrafficAccidentCloseProcessEndEvent) Desc() string {
	return "事故结案流程审批结束执行的方法"
}
func (el TrafficAccidentCloseProcessEndEvent) TemplateFormId() string {
	return config.TrafficAccidentCloseFormTemplate
}
