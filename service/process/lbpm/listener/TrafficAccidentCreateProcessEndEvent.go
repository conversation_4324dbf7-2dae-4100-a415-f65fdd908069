package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/auth"
	processService "app/org/scs/erpv2/api/service/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
)

type TrafficAccidentCreateProcessEndEvent struct {
}

func (el TrafficAccidentCreateProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("TrafficAccidentCreateProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("TrafficAccidentCreateProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentCreateProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	var accident safetyModel.TrafficAccident
	err = accident.FindBy(process.ItemId)

	if err != nil {
		log.ErrorFields("TrafficAccidentCreateProcessEndEvent TrafficAccident.FindBy error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentCreateProcessEndEvent TrafficAccident.FindBy error: " + err.Error())
	}
	//当事故损伤情况为无车损无人伤，并且责任是明确的（有责，主责，同责，次责，无责）时，事故提交通过后自动结案（此逻辑已移除）
	//if accident.HurtStatus == util.AccidentHurtStatus_1 && accident.LiabilityType != util.AccidentLiabilityTypeForUnknown && accident.LiabilityType != util.AccidentLiabilityTypeForPending {
	//	UpdateMap["closedapplystatus"] = util.ApplyStatusForDone
	//	UpdateMap["isclosed"] = util.StatusForTrue
	//}

	err = (&safetyModel.TrafficAccident{}).UpdateByMap(process.ItemId, map[string]interface{}{"openstatus": util.AccidentOpenStatusForOpen})
	if err != nil {
		log.ErrorFields("TrafficAccidentCreateProcessEndEvent UpdateByMap OpenStatus error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentCreateProcessEndEvent UpdateByMap OpenStatus error")
	}

	var pay safetyModel.TrafficAccidentPaymentMoneyRecord

	var accidentParam safetyModel.TrafficAccident
	err = json.Unmarshal(process.Param, &accidentParam)
	if err != nil {
		log.ErrorFields("accidentParam Unmarshal error", map[string]interface{}{"err": err})
		return errors.New("accidentParam Unmarshal error")
	}

	// 如果选择直接发起付款 获取事故车损分支 发起付款流程
	if accidentParam.IsCreatePayment == util.StatusForTrue {
		//一个事故只能有一条通过或在审批中的付款申请
		if (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).ExistPaymentRecord(accidentParam.Id) {
			log.ErrorFields("ExistPaymentRecord", map[string]interface{}{"err": err})
			return errors.New("ExistPaymentRecord")
		}

		branches := (&safetyModel.TrafficAccidentRelaterBranch{}).GetByAccidentId(accidentParam.Id)
		for _, branch := range branches {
			if branch.BranchType == util.AccidentBranchTypeForSelfVehicleBroken {
				pay.TrafficAccidentRelaterBranchId = branch.Id
				pay.TrafficAccidentRelaterId = branch.TrafficAccidentRelaterId
			}
		}

		if pay.TrafficAccidentRelaterBranchId > 0 {
			pay.TrafficAccidentId = accidentParam.Id
			pay.FixOffice = accidentParam.TmpFixOffice
			pay.IsFull = accidentParam.TmpIsFull
			//pay.SolutionType = accidentParam.TmpSolutionType
			//pay.SolutionFilePath = accidentParam.TmpSolutionFilePath
			//pay.InsuranceCompanyPayMoney = accidentParam.TmpInsuranceCompanyPayMoney
			//pay.InsurancePayMoney = accidentParam.TmpInsurancePayMoney
			//pay.LossMoney = accidentParam.TmpLossMoney
			//pay.SolutionDesc = accidentParam.TmpSolutionDesc
			//pay.PayOrigin = accidentParam.TmpPayOrigin
			//pay.PersonalPayRatio = accidentParam.TmpPersonalPayRatio
			pay.OpUserId = accident.OpUserId
			pay.OpUserName = accident.OpUserName

			if pay.OpUserId == 0 {
				log.ErrorFields("pay.OpUserId == 0", map[string]interface{}{"err": nil})
				return errors.New("pay.OpUserId == 0")
			}

			var relater safetyModel.TrafficAccidentRelater
			err = relater.FindBy(pay.TrafficAccidentRelaterId)
			if err != nil {
				log.ErrorFields("TrafficAccidentRelater FindBy is error", map[string]interface{}{"err": err})
			}
			pay.TrafficAccidentRelaterName = relater.Name

			// 查询事故编号 存入param 消息中心使用
			var acc safetyModel.TrafficAccident
			err = acc.FindBy(pay.TrafficAccidentId)
			if err != nil {
				log.ErrorFields("TrafficAccident FindBy fail", map[string]interface{}{"err": err})
				return errors.New("TrafficAccident FindBy fail")
			}

			pay.TrafficAccidentCode = accident.Code

			pay.CorporationId, pay.CorporationName = accident.Corporations.GetCorporation()
			pay.License = accident.License
			pay.LineId = accident.LineId
			pay.LineName = accident.LineName
			pay.DriverName = accident.DriverName

			var tx = model.DB().Begin()
			err = pay.TransactionCreate(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("TrafficAccidentPaymentMoneyRecord.Create error", map[string]interface{}{"err": err})
				return errors.New("create error")
			}

			//发起流程审批
			if config.Config.Lbpm.Enable {
				byteParam, _ := json.Marshal(pay)
				formData := map[string]interface{}{"FixOffice": util.AccidentFixOfficeMap[pay.FixOffice], "PaymentMoney": 0}
				processTitle := service.BuildAccidentProcessTitle(&accident)

				user := auth.NewUserById(pay.OpUserId)
				_, err = processService.NewDispatchProcess(user, config.TrafficAccidentPaymentMoneyFormTemplate, processTitle, pay.Id, pay.TableName(), pay.ApplyStatusFieldName(), string(byteParam), formData)

				if err != nil {
					tx.Rollback()
					log.ErrorFields("processService.DispatchProcess error", map[string]interface{}{"err": err})
					return errors.New("processService.DispatchProcess error")
				}
			}

			tx.Commit()

		}
	}

	return nil
}
func (el TrafficAccidentCreateProcessEndEvent) Id() string {
	return "TrafficAccidentCreateProcessEndEvent"
}
func (el TrafficAccidentCreateProcessEndEvent) Name() string {
	return "事故提交流程结束事件监听方法"
}
func (el TrafficAccidentCreateProcessEndEvent) Desc() string {
	return "事故提交流程审批结束执行的方法"
}
func (el TrafficAccidentCreateProcessEndEvent) TemplateFormId() string {
	return config.TrafficAccidentReportFormTemplate
}

func CreateAccidentRelater(accident *safetyModel.TrafficAccident) (int64, error) {
	staff := rpc.GetStaffWithId(context.Background(), accident.DriverId)
	var driverName, driverPhone, identifyId string
	if staff != nil {
		driverPhone = staff.Phone
		identifyId = staff.IdentifyId
	}
	driverName = accident.DriverName

	var relater = safetyModel.TrafficAccidentRelater{
		TrafficAccidentId: accident.Id,
		Name:              driverName,
		Contact:           driverPhone,
		IdentifyId:        identifyId,
		LiabilityType:     accident.LiabilityType,
		License:           accident.License,
		InsuranceCompany:  accident.InsuranceCompany,
		IsSysCreate:       util.StatusForTrue,
		IsOwnSide:         util.StatusForTrue,
	}
	relater.OpUserId = accident.OpUserId
	relater.OpUserName = accident.OpUserName

	var err error
	//存在系统创建的当事人 则在此当事人上面更新  否则创建
	sysCreateRelater := relater.FindSysCreateRelater(accident.Id)
	if sysCreateRelater.Id > 0 {
		relater.Id = sysCreateRelater.Id
		err = relater.Update()
	} else {
		err = relater.Create()
	}

	if err != nil {
		log.ErrorFields("TrafficAccidentCreateProcessEndEvent relater.Create error", map[string]interface{}{"err": err})
		return 0, errors.New("TrafficAccidentCreateProcessEndEvent relater.Create error")
	}

	return relater.Id, nil
}

func CreateAccidentSelfVehicleBrokenBranch(accident *safetyModel.TrafficAccident, relaterId int64) error {
	var branch = safetyModel.TrafficAccidentRelaterBranch{
		TrafficAccidentId:        accident.Id,
		TrafficAccidentRelaterId: relaterId,
		BranchType:               util.AccidentBranchTypeForSelfVehicleBroken,
		BranchDetail:             accident.Desc,
		FilePath:                 accident.ReportFilePath,
	}
	branch.OpUserId = accident.OpUserId
	branch.OpUserName = accident.OpUserName
	err := branch.Create()
	if err != nil {
		log.ErrorFields("TrafficAccidentCreateProcessEndEvent branch.Create error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentCreateProcessEndEvent branch.Create error")
	}

	return nil
}

func CreateAccidentSelfVehicleBrokenBranchReturnId(accident *safetyModel.TrafficAccident, relaterId int64) (int64, error) {
	var branch = safetyModel.TrafficAccidentRelaterBranch{
		TrafficAccidentId:        accident.Id,
		TrafficAccidentRelaterId: relaterId,
		BranchType:               util.AccidentBranchTypeForSelfVehicleBroken,
		BranchDetail:             accident.Desc,
		FilePath:                 accident.ReportFilePath,
	}
	branch.OpUserId = accident.OpUserId
	branch.OpUserName = accident.OpUserName
	bid, err := branch.CreateReturnId()
	if err != nil {
		log.ErrorFields("TrafficAccidentCreateProcessEndEvent branch.Create error", map[string]interface{}{"err": err})
		return 0, errors.New("TrafficAccidentCreateProcessEndEvent branch.Create error")
	}

	return bid, nil
}

func CreateAccidentSelfPeopleHurtBranch(accident *safetyModel.TrafficAccident, relaterId int64) error {
	if (&safetyModel.TrafficAccidentRelaterBranch{}).IsExistBranchType(relaterId, util.AccidentBranchTypeForSelfPeopleHurt) {
		return nil
	}
	var branch = safetyModel.TrafficAccidentRelaterBranch{
		TrafficAccidentId:        accident.Id,
		TrafficAccidentRelaterId: relaterId,
		BranchType:               util.AccidentBranchTypeForSelfPeopleHurt,
		BranchDetail:             accident.Desc,
		FilePath:                 accident.ReportFilePath,
	}
	branch.OpUserId = accident.OpUserId
	branch.OpUserName = accident.OpUserName
	err := branch.Create()
	if err != nil {
		log.ErrorFields("CreateAccidentBranchTypeForSelfPeopleHurt branch.Create error", map[string]interface{}{"err": err})
		return errors.New("CreateAccidentBranchTypeForSelfPeopleHurt branch.Create error")
	}

	return nil
}
