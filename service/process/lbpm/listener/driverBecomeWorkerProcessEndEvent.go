package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"
)

type DriverBecomeWorkerProcessEndEvent struct {
}

// Handler 司机转正流程结束监听事件
func (el DriverBecomeWorkerProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("DriverBecomeWorkerProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("DriverBecomeWorkerProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("DriverBecomeWorkerProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	record := (&hrModel.DriverBecomeWorkerRecord{}).FirstBy(process.ItemId)
	if record.Id == 0 {
		return errors.New(fmt.Sprintf("DriverBecomeWorkerProcessEndEvent record.Id == 0"))
	}

	//正常转正： 更新员工状态为在职 更新试用期
	//延迟转正： 更新试用期
	//不予转正（辞退）：更新员工状态离职 更新新试用期  添加离职记录
	if record.FleetCheckStatus == util.BecomeWorkerCheckStatusForDone {
		oetStaff := rpc.GetStaffWithId(context.TODO(), record.StaffId)
		if oetStaff == nil {
			return errors.New("rpc.GetStaffWithId is nil")
		}
		oetStaff.WorkingState = util.JobStatusWorking
		err = rpc.EditOetStaff(context.TODO(), record.OpUserId, oetStaff)
		if err != nil {
			log.ErrorFields("rpc.EditOetStaff error", map[string]interface{}{"err": err})
		}
	}

	if record.FleetCheckStatus == util.BecomeWorkerCheckStatusForRefuse {
		oetStaff := rpc.GetStaffWithId(context.TODO(), record.StaffId)
		if oetStaff == nil {
			return errors.New("rpc.GetStaffWithId is nil")
		}
		oetStaff.WorkingState = util.JobStatusQuit
		err = rpc.EditOetStaff(context.TODO(), record.OpUserId, oetStaff)
		if err != nil {
			log.ErrorFields("rpc.EditOetStaff error", map[string]interface{}{"err": err})
		}

		//添加离职记录
		at := model.LocalTime(time.Unix(oetStaff.RegisterTime, 0))
		now := model.LocalTime(time.Now())
		var staffQuitRecord = hrModel.StaffQuitRecord{
			StaffQuitId:         0,
			StaffArchiveId:      record.StaffArchiveId,
			StaffId:             record.StaffId,
			WorkPostName:        "",
			ContractEndAt:       nil,
			JoinCompanyAt:       &at,
			RelieveContractAt:   &now,
			RelieveContractType: util.RelieveContractTypeForRefuseBecomeWorker,
		}
		staffQuitRecord.OpUser = record.OpUser

		staffQuit := hrModel.StaffQuit{
			Records: []hrModel.StaffQuitRecord{staffQuitRecord},
		}
		staffQuit.OpUser = record.OpUser

		err = staffQuit.Create()
		if err != nil {
			log.ErrorFields("staffQuit.StaffQuit error", map[string]interface{}{"err": err})
		}
	}

	var staffArchive hrModel.StaffArchive
	err = staffArchive.FindById(record.StaffArchiveId)
	if err != nil {
		log.ErrorFields("staffArchive.FindById error", map[string]interface{}{"err": err})
		return err
	}
	staffArchive.ProbationStartAt = &record.ProbationStartAt
	staffArchive.ProbationEndAt = &record.ProbationEndAt
	model.DB().Select("ProbationStartAt", "ProbationEndAt").Updates(&staffArchive)

	return nil
}
func (el DriverBecomeWorkerProcessEndEvent) Id() string {
	return "DriverBecomeWorkerProcessEndEvent"
}
func (el DriverBecomeWorkerProcessEndEvent) Name() string {
	return "司机转正流程结束监听方法"
}
func (el DriverBecomeWorkerProcessEndEvent) Desc() string {
	return "司机转正流程结束执行的方法"
}
func (el DriverBecomeWorkerProcessEndEvent) TemplateFormId() string {
	return config.DriverBecomeWorkerApplyFormTemplate
}
