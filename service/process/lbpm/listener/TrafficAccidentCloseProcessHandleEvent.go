package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
)

// TrafficAccidentCloseProcessHandleEvent 事故结案 变更提交表单信息
type TrafficAccidentCloseProcessHandleEvent struct {
}

func (el TrafficAccidentCloseProcessHandleEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("TrafficAccidentCloseProcessHandleEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("TrafficAccidentCloseProcessHandleEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentCloseProcessHandleEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	//var branch safetyModel.TrafficAccidentRelaterBranch
	//err = branch.FindBy(process.ItemId)
	//if err != nil {
	//	log.ErrorFields("TrafficAccidentCloseProcessHandleEvent TrafficAccident.FindBy error", map[string]interface{}{"err": err})
	//	return errors.New("TrafficAccidentCloseProcessHandleEvent TrafficAccident.FindBy error: " + err.Error())
	//}

	// 可以变更数据
	err = (&safetyModel.TrafficAccident{}).UpdateByMap(process.ItemId, map[string]interface{}{"IsUpdateForm": util.StatusForTrue})
	if err != nil {
		log.ErrorFields("TrafficAccidentCloseProcessHandleEvent UpdateColumns error", map[string]interface{}{"err": err})
	}
	return nil
}
func (el TrafficAccidentCloseProcessHandleEvent) Id() string {
	return "TrafficAccidentCloseProcessHandleEvent"
}
func (el TrafficAccidentCloseProcessHandleEvent) Name() string {
	return "事故结案流程处理（变更表单数据）事件监听方法"
}
func (el TrafficAccidentCloseProcessHandleEvent) Desc() string {
	return "事故结案流程审批处理（变更表单数据）执行的方法"
}
func (el TrafficAccidentCloseProcessHandleEvent) TemplateFormId() string {
	return config.TrafficAccidentCloseFormTemplate
}
