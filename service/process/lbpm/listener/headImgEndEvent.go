package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	protoStaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

// HeadImgProcessEndEvent 报修工单侦听流程是否进入节点
type HeadImgProcessEndEvent struct {
}

func (el HeadImgProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("RepairProcessEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("RepairProcessEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("RepairProcessEvent process.FindBy error")
	}
	var Approval hrModel.HeadImgApproval
	Approval.Id = process.ItemId
	err = Approval.FindById()
	if err != nil {
		log.ErrorFields("HeadImgApproval FindById error", map[string]interface{}{"err": err})
		return err
	}
	if process.Status == util.ProcessStatusForDone {
		if Approval.HeadImg != nil && len(Approval.HeadImg) > 0 {
			var HeadImg []hrModel.HeadImg
			err = json.Unmarshal(Approval.HeadImg, &HeadImg)
			if err != nil {
				log.ErrorFields("json.Unmarshal error", map[string]interface{}{"err": err})
				return err
			}
			headImg := HeadImg[0]
			fullPath := fmt.Sprintf("%s%s", config.Config.AbsDirPath, headImg.Path)
			base64Data, err := util.FileToBase64(fullPath)
			if err != nil {
				log.ErrorFields("FileToBase64 is err", map[string]interface{}{"err": err})
				return err
			}
			Approval.CorporationId, _ = Approval.GetCorporation()
			var topCorporationId int64
			corporation := rpc.GetTopCorporationById(context.Background(), Approval.CorporationId)
			if corporation != nil {
				topCorporationId = corporation.Id
			}
			pathArr := strings.Split(headImg.Path, "/")
			err = rpc.EditOetStaffAvatar(context.Background(), &protoStaff.UploadStaffProfileRequest{
				Id:               Approval.StaffId,
				TopCorporationId: topCorporationId,
				FileName:         pathArr[len(pathArr)-1],
				FileData:         base64Data,
			})
			if err != nil {
				log.ErrorFields("rpc.EditOetStaffAvatar is err", map[string]interface{}{"err": err})
				return err
			}
		}
	}
	return nil
}
func (el HeadImgProcessEndEvent) Id() string {
	return "HeadImgProcessEndEvent"
}
func (el HeadImgProcessEndEvent) Name() string {
	return "证件照结束节点事件监听方法"
}
func (el HeadImgProcessEndEvent) Desc() string {
	return "证件照结束节点事件监听方法"
}
func (el HeadImgProcessEndEvent) TemplateFormId() string {
	return config.HeadImgFormTemplate
}
