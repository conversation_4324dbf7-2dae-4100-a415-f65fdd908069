package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	processModel "app/org/scs/erpv2/api/model/process"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
)

// PetitionWorkOrderHandleProcessEvent 信访工单流程交办人处理节点进入事件
type PetitionWorkOrderHandleProcessEvent struct {
}

func (el PetitionWorkOrderHandleProcessEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("PetitionWorkOrderHandleProcessEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("PetitionWorkOrderHandleProcessEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("PetitionWorkOrderHandleProcessEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	err = (&workOrderModel.PetitionWorkOrder{}).UpdateColumn(process.ItemId, "FormStep", util.ProcessFormStepTwo)
	if err != nil {
		log.ErrorFields("PetitionWorkOrderHandleProcessEvent workOrder.UpdateColumn error", map[string]interface{}{"err": err})
		return errors.New("PetitionWorkOrderHandleProcessEvent workOrder.UpdateColumn error")
	}
	return nil
}
func (el PetitionWorkOrderHandleProcessEvent) Id() string {
	return "PetitionWorkOrderHandleProcessEvent"
}
func (el PetitionWorkOrderHandleProcessEvent) Name() string {
	return "信访工单流程交办人处理节点进入事件"
}
func (el PetitionWorkOrderHandleProcessEvent) Desc() string {
	return "信访工单流程交办人处理节点进入事件"
}
func (el PetitionWorkOrderHandleProcessEvent) TemplateFormId() string {
	return config.PetitionWorkOrderReportFormTemplate
}

// PetitionWorkOrderSecondHandleProcessEvent 信访工单交办人申诉进入事件
type PetitionWorkOrderSecondHandleProcessEvent struct {
}

func (el PetitionWorkOrderSecondHandleProcessEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("PetitionWorkOrderSecondHandleProcessEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("PetitionWorkOrderSecondHandleProcessEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("PetitionWorkOrderSecondHandleProcessEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	err = (&workOrderModel.PetitionWorkOrder{}).UpdateColumn(process.ItemId, "FormStep", util.ProcessFormStepSix)
	if err != nil {
		log.ErrorFields("PetitionWorkOrderSecondHandleProcessEvent workOrder.UpdateColumn error", map[string]interface{}{"err": err})
		return errors.New("PetitionWorkOrderSecondHandleProcessEvent workOrder.UpdateColumn error")
	}
	return nil
}
func (el PetitionWorkOrderSecondHandleProcessEvent) Id() string {
	return "PetitionWorkOrderSecondHandleProcessEvent"
}
func (el PetitionWorkOrderSecondHandleProcessEvent) Name() string {
	return "信访工单交办人申诉进入事件"
}
func (el PetitionWorkOrderSecondHandleProcessEvent) Desc() string {
	return "信访工单交办人申诉进入事件"
}
func (el PetitionWorkOrderSecondHandleProcessEvent) TemplateFormId() string {
	return config.PetitionWorkOrderReportFormTemplate
}

// PetitionWorkOrderReceiverFinalHandleProcessEvent 信访工单交办人申诉进入事件
type PetitionWorkOrderReceiverFinalHandleProcessEvent struct {
}

func (el PetitionWorkOrderReceiverFinalHandleProcessEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("PetitionWorkOrderReceiverFinalHandleProcessEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("PetitionWorkOrderReceiverFinalHandleProcessEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("PetitionWorkOrderReceiverFinalHandleProcessEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	err = (&workOrderModel.PetitionWorkOrder{}).UpdateColumn(process.ItemId, "FormStep", util.ProcessFormStepTen)
	if err != nil {
		log.ErrorFields("PetitionWorkOrderReceiverFinalHandleProcessEvent workOrder.UpdateColumn error", map[string]interface{}{"err": err})
		return errors.New("PetitionWorkOrderReceiverFinalHandleProcessEvent workOrder.UpdateColumn error")
	}
	return nil
}
func (el PetitionWorkOrderReceiverFinalHandleProcessEvent) Id() string {
	return "PetitionWorkOrderReceiverFinalHandleProcessEvent"
}
func (el PetitionWorkOrderReceiverFinalHandleProcessEvent) Name() string {
	return "信访工单交办人最终处理进入事件"
}
func (el PetitionWorkOrderReceiverFinalHandleProcessEvent) Desc() string {
	return "信访工单交办人最终处理进入事件"
}
func (el PetitionWorkOrderReceiverFinalHandleProcessEvent) TemplateFormId() string {
	return config.PetitionWorkOrderReportFormTemplate
}

// PetitionWorkOrderDepartmentReplyHandleProcessEvent 信访工单部门回复节点进入事件
type PetitionWorkOrderDepartmentReplyHandleProcessEvent struct {
}

func (el PetitionWorkOrderDepartmentReplyHandleProcessEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("PetitionWorkOrderDepartmentReplyHandleProcessEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("PetitionWorkOrderDepartmentReplyHandleProcessEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("PetitionWorkOrderDepartmentReplyHandleProcessEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	err = (&workOrderModel.PetitionWorkOrder{}).UpdateColumn(process.ItemId, "FormStep", util.ProcessFormStepFour)
	if err != nil {
		log.ErrorFields("PetitionWorkOrderDepartmentReplyHandleProcessEvent workOrder.UpdateColumn error", map[string]interface{}{"err": err})
		return errors.New("PetitionWorkOrderDepartmentReplyHandleProcessEvent workOrder.UpdateColumn error")
	}
	return nil
}
func (el PetitionWorkOrderDepartmentReplyHandleProcessEvent) Id() string {
	return "PetitionWorkOrderDepartmentReplyHandleProcessEvent"
}
func (el PetitionWorkOrderDepartmentReplyHandleProcessEvent) Name() string {
	return "信访工单部门回复节点进入事件"
}
func (el PetitionWorkOrderDepartmentReplyHandleProcessEvent) Desc() string {
	return "信访工单部门回复节点进入事件"
}
func (el PetitionWorkOrderDepartmentReplyHandleProcessEvent) TemplateFormId() string {
	return config.PetitionWorkOrderReportFormTemplate
}

// PetitionWorkOrderFinalReplyHandleProcessEvent 信访工单最终回复节点进入事件
type PetitionWorkOrderFinalReplyHandleProcessEvent struct {
}

func (el PetitionWorkOrderFinalReplyHandleProcessEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("PetitionWorkOrderFinalReplyHandleProcessEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("PetitionWorkOrderFinalReplyHandleProcessEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("PetitionWorkOrderFinalReplyHandleProcessEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	err = (&workOrderModel.PetitionWorkOrder{}).UpdateColumn(process.ItemId, "FormStep", util.ProcessFormStepEight)
	if err != nil {
		log.ErrorFields("PetitionWorkOrderFinalReplyHandleProcessEvent workOrder.UpdateColumn error", map[string]interface{}{"err": err})
		return errors.New("PetitionWorkOrderFinalReplyHandleProcessEvent workOrder.UpdateColumn error")
	}
	return nil
}
func (el PetitionWorkOrderFinalReplyHandleProcessEvent) Id() string {
	return "PetitionWorkOrderFinalReplyHandleProcessEvent"
}
func (el PetitionWorkOrderFinalReplyHandleProcessEvent) Name() string {
	return "信访工单最终回复节点进入事件"
}
func (el PetitionWorkOrderFinalReplyHandleProcessEvent) Desc() string {
	return "信访工单最终回复节点进入事件"
}
func (el PetitionWorkOrderFinalReplyHandleProcessEvent) TemplateFormId() string {
	return config.PetitionWorkOrderReportFormTemplate
}
