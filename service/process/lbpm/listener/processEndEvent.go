package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
	"time"
)

type ProcessEndEvent struct {
}

func (el ProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("ProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("ProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("ProcessEndEvent process.FindBy error")
	}

	if process.Status != util.ProcessStatusForAbandon {
		err = process.UpdateStatus(util.ProcessStatusForDone)

		if err != nil {
			log.ErrorFields("ProcessEndEvent process.UpdateStatus error", map[string]interface{}{"err": err})
			return errors.New("ProcessEndEvent process.UpdateStatus error")
		}

		//更新业务数据表中的审批状态
		model.DB().Table(process.ItemTableName).Where("Id = ?", process.ItemId).Update(process.ItemTableStatusField, util.ApplyStatusForDone)

		//事故结案、事故分支结案更新IsClosed、ClosedAt字段
		if util.Include([]string{config.TrafficAccidentBranchCloseFormTemplate}, formId.TemplateFormId) {
			model.DB().Table(process.ItemTableName).Where("Id = ?", process.ItemId).Updates(map[string]interface{}{"isclosed": util.StatusForTrue, "closedat": time.Now().Format(model.TimeFormat)})
			return nil
		}
	}

	go service.RemoveProcessCurrentHandler(formInstanceId)

	return nil
}
func (el ProcessEndEvent) Id() string {
	return "ProcessEndEvent"
}
func (el ProcessEndEvent) Name() string {
	return "流程结束事件监听方法"
}
func (el ProcessEndEvent) Desc() string {
	return "流程审批结束执行的方法"
}
func (el ProcessEndEvent) TemplateFormId() string {
	return config.PublicFormTemplate
}
