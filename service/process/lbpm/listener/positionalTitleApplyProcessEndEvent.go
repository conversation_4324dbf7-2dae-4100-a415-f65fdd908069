package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
)

type PositionalTitleApplyProcessEndEvent struct {
}

func (el PositionalTitleApplyProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("PositionalTitleApplyProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("PositionalTitleApplyProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("PositionalTitleApplyProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	var apply hrModel.PositionalTitleApply
	err = json.Unmarshal(process.Param, &apply)
	if err != nil {
		log.ErrorFields("PositionalTitleApplyProcessEndEvent process.Param json Unmarshal error", map[string]interface{}{"err": err})
		return errors.New("PositionalTitleApplyProcessEndEvent process.Param json Unmarshal error")
	}

	//更新员工档案中的职称
	var pt hrModel.StaffPositionalTitle
	pt.Name = apply.Name
	pt.StaffArchiveId = apply.StaffArchiveId
	pt.StaffId = apply.StaffId
	pt.More = apply.More
	pt.Level = apply.Level
	pt.FilePath = apply.FilePath
	_ = pt.Create()

	return nil
}
func (el PositionalTitleApplyProcessEndEvent) Id() string {
	return "PositionalTitleApplyProcessEndEvent"
}
func (el PositionalTitleApplyProcessEndEvent) Name() string {
	return "职称补贴申请流程结束事件监听方法"
}
func (el PositionalTitleApplyProcessEndEvent) Desc() string {
	return "职称补贴申请流程审批结束执行的方法"
}
func (el PositionalTitleApplyProcessEndEvent) TemplateFormId() string {
	return config.PositionalTitleApplyFormTemplate
}
