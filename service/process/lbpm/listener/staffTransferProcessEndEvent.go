package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service"
	messageService "app/org/scs/erpv2/api/service/message"
	processService "app/org/scs/erpv2/api/service/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
)

type StaffTransferProcessEndEvent struct {
}

func (el StaffTransferProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("StaffTransferProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("StaffTransferProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("StaffTransferProcessEndEvent process.FindBy error")
	}
	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	var staffTransfer hrModel.StaffTransfer
	err = json.Unmarshal(process.Param, &staffTransfer)
	if err != nil {
		log.ErrorFields("StaffTransferProcessEndEvent staffArchive json Unmarshal error", map[string]interface{}{"err": err})
		return errors.New("StaffTransferProcessEndEvent staffArchive json Unmarshal error")
	}

	tx := model.DB()
	for i := range staffTransfer.Records {
		var staffWork hrModel.StaffHasWorkPost
		posts := staffWork.GetByArchiveId(staffTransfer.Records[i].StaffArchiveId)
		beforeData, _ := json.Marshal(posts)

		err = service.StaffTransferAfterUpdateStaffArchive(tx, staffTransfer.Records[i])
		if err != nil {
			log.ErrorFields("StaffTransferAfterUpdateStaffArchive fail", map[string]interface{}{"err": err})
		}

		posts = staffWork.GetByArchiveId(staffTransfer.Records[i].StaffArchiveId)
		afterData, _ := json.Marshal(posts)
		go service.CreateStaffArchiveLogger(staffTransfer.Records[i].StaffArchiveId, staffTransfer.OpUserId, staffTransfer.OpUserName, hrModel.ArchiveLoggerSceneUpdate, staffTransfer.OpIp,
			hrModel.ArchiveLoggerModularForTransfer, beforeData, afterData, process.ProcessId)

	}

	//给知会人发消息
	var noticeUserIds []int64
	json.Unmarshal(staffTransfer.NoticeUserIds, &noticeUserIds)
	if len(noticeUserIds) > 0 {
		for i := range noticeUserIds {
			user := rpc.GetUserInfoById(context.Background(), noticeUserIds[i])
			if user != nil {
				var handlerUser = processService.HandlerUser{Id: user.Id, Name: user.Nickname, Mobile: user.Phone}
				//推送系统消息
				go messageService.BuildMessage(process, handlerUser, util.ProcessMessageTypeForNotice)
				//写入流程处理人表
				go processService.BuildProcessHandler(process, handlerUser, util.ProcessMessageTypeForNotice)
			}
		}
	}
	return nil
}

func (el StaffTransferProcessEndEvent) Id() string {
	return "StaffTransferProcessEndEvent"
}

func (el StaffTransferProcessEndEvent) Name() string {
	return "员工调动流程结束事件监听方法"
}
func (el StaffTransferProcessEndEvent) Desc() string {
	return "员工调动流程结束事件"
}
func (el StaffTransferProcessEndEvent) TemplateFormId() string {
	return config.StaffTransferApplyFormTemplate
}
