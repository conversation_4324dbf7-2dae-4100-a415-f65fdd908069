package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
)

// TrafficAccidentCashierNodeEvent 事故流程出纳环节监听事件
type TrafficAccidentCashierNodeEvent struct {
}

func (el TrafficAccidentCashierNodeEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("TrafficAccidentCashierNodeEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("ProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("ProcessEndEvent process.FindBy error")
	}
	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	model.DB().Table(process.ItemTableName).Where("Id = ?", process.ItemId).Update("formstep", util.ProcessFormStepForCashier)
	return nil
}

func (el TrafficAccidentCashierNodeEvent) Id() string {
	return "TrafficAccidentCashierNodeEvent"
}

func (el TrafficAccidentCashierNodeEvent) Name() string {
	return "事故相关流程出纳节点监听方法"
}

func (el TrafficAccidentCashierNodeEvent) Desc() string {
	return "事故相关流程出纳节点进入执行的方法"
}

func (el TrafficAccidentCashierNodeEvent) TemplateFormId() string {
	return config.PublicFormTemplate
}
