package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	commonModel "app/org/scs/erpv2/api/model/common"
	processModel "app/org/scs/erpv2/api/model/process"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	protooetvehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"
)

// 报修工单结束流程 更新设备明细出库状态

type RepairProcessEndEvent struct {
}

func (el RepairProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("RepairProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("RepairProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("RepairProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	// 获取工单id
	var order workOrderModel.WorkOrder
	_ = json.Unmarshal(process.Param, &order)
	// 获取工单详情
	err = (&order).GetDetail(order.Id)
	if err != nil {
		log.ErrorFields("RepairProcessEndEvent GetDetail error", map[string]interface{}{"err": err})
		return errors.New("RepairProcessEndEvent GetDetail error")
	}

	order.UpdatesTx(model.DB(), map[string]interface{}{"processdoneat": time.Now().Format(model.TimeFormat)})

	// 流程走完后，根据 MaintenanceType（维修方式）处理--->> 更换：进行原设备解绑，绑定新设备。维修：不做设备解绑。移除：解除设备和车辆绑定关系
	if process.Status == 2 {
		tx := model.DB().Begin()
		defer func() {
			if err == nil {
				tx.Commit()
			} else {
				tx.Rollback()
			}
		}()

		//根据MaintenanceType判断
		if order.MaintenanceType == util.MaintenanceTypeForReplace {
			//	更换
			//  先移除老设备，再绑定新设备
			err = (&workOrderModel.DeviceDetail{}).UnbindTx(tx, []int64{order.FkRepairDeviceId})
			if err != nil {
				log.ErrorFields("RepairProcessEndEvent UnbindTx error", map[string]interface{}{"err": err})
				return errors.New("RepairProcessEndEvent UnbindTx error")
			}

			// 绑定新设备
			var (
				assocType int64
				assocId   int64
				assocName string
			)

			deviceClass := (&commonModel.Dict{}).FirstById(order.RepairClassDictId)

			if deviceClass.ObjectType == util.DeviceAssociationObjectForVehicle {
				assocType = util.DeviceAssociationObjectForVehicle
				withLicense := rpc.GetVehicleWithLicense(context.Background(), &protooetvehicle.GetVehicleWithLicenseRequest{
					License:       order.License,
					CorporationId: order.GroupId,
				})
				assocId = withLicense.Id
				assocName = withLicense.License
			} else if deviceClass.ObjectType == util.DeviceAssociationObjectForParking {
				assocType = util.DeviceAssociationObjectForParking
				assocId = order.UpParkingId
				assocName = order.UpParking
			} else if deviceClass.ObjectType == util.DeviceAssociationObjectForStation {
				assocType = util.DeviceAssociationObjectForStation
				assocId = order.StationId
				assocName = order.Station
			}

			var spareParts []workOrderModel.WorkOrderRepairSparePart
			spareParts, err = (&workOrderModel.WorkOrderRepairSparePart{}).GetByWorkOrderId(order.Id)
			if err != nil {
				log.Error("GetByWorkOrderId err =", err)
				return errors.New("GetByWorkOrderId error")
			}

			var ids []int64

			for _, part := range spareParts {
				ids = append(ids, part.FkDevicesId)
			}

			err = (&workOrderModel.DeviceDetail{}).BindTx(tx, ids, assocType, assocId, assocName)
			if err != nil {
				log.ErrorFields("RepairProcessEndEvent UnbindTx error", map[string]interface{}{"err": err})
				return errors.New("RepairProcessEndEvent UnbindTx error")
			}

		}
		if order.MaintenanceType == util.MaintenanceTypeForRemove {
			//	移除
			err = (&workOrderModel.DeviceDetail{}).UnbindTx(tx, []int64{order.FkRepairDeviceId})
			if err != nil {
				log.ErrorFields("RepairProcessEndEvent UnbindTx error", map[string]interface{}{"err": err})
				return errors.New("RepairProcessEndEvent UnbindTx error")
			}
		}
		if order.MaintenanceType == util.MaintenanceTypeForReplaceChildDevice {
			//更换子设备
			replaceChildren := (&workOrderModel.WorkOrderReplaceChildDevice{}).GetByWorkOrderId(order.Id)
			var oldIds, newIds []int64
			for i := range replaceChildren {
				oldIds = append(oldIds, replaceChildren[i].OldChildDeviceId)
				newIds = append(newIds, replaceChildren[i].NewChildDeviceId)
			}
			_ = (&workOrderModel.ChildDevice{}).UpdateFreezeStatusAndAssociationIdByIds(oldIds, workOrderModel.ChildDeviceFreezeStatusForDefault, 0)
			_ = (&workOrderModel.ChildDevice{}).UpdateFreezeStatusAndAssociationIdByIds(newIds, workOrderModel.ChildDeviceFreezeStatusForDefault, order.FkRepairDeviceId)
		}

	}

	return nil
}

func (el RepairProcessEndEvent) Id() string {
	return "RepairProcessEndEvent"
}
func (el RepairProcessEndEvent) Name() string {
	return "报修流程处理结束事件监听方法"
}
func (el RepairProcessEndEvent) Desc() string {
	return "报修流程处理结束执行的方法"
}
func (el RepairProcessEndEvent) TemplateFormId() string {
	return config.DeviceWorkOrderReportFormTemplate
}
