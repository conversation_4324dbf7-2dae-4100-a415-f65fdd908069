package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
)

type StaffQuitProcessEndEvent struct {
}

func (el StaffQuitProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("StaffQuitProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("StaffQuitProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("StaffQuitProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}
	record := (&hrModel.StaffQuitRecord{}).FindBy(process.ItemId)
	err = service.StaffQuitAfterUpdateStaffArchive(model.DB(), record)
	if err != nil {
		log.ErrorFields("service.StaffQuitAfterUpdateStaffArchive error", map[string]interface{}{"err": err})
	}

	return nil
}
func (el StaffQuitProcessEndEvent) Id() string {
	return "StaffQuitProcessEndEvent"
}
func (el StaffQuitProcessEndEvent) Name() string {
	return "员工离职流程结束事件监听方法"
}
func (el StaffQuitProcessEndEvent) Desc() string {
	return "员工离职流程结束后异步执行的事件"
}
func (el StaffQuitProcessEndEvent) TemplateFormId() string {
	return config.StaffQuitLbpmApplyFormTemplate
}
