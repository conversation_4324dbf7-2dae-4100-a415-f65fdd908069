package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	processModel "app/org/scs/erpv2/api/model/process"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
)

// WorkOrderMainUserAssignRepairUserProcessEvent 报修工单主负责人指派维修人员进入事件
type WorkOrderMainUserAssignRepairUserProcessEvent struct {
}

func (el WorkOrderMainUserAssignRepairUserProcessEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("WorkOrderMainUserAssignRepairUserProcessEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("WorkOrderMainUserAssignRepairUserProcessEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("WorkOrderMainUserAssignRepairUserProcessEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	err = (&workOrderModel.WorkOrder{}).UpdateColumn(process.ItemId, "FormStep", util.ProcessFormStepFour)
	if err != nil {
		log.ErrorFields("WorkOrderMainUserAssignRepairUserProcessEvent workOrder.UpdateColumn error", map[string]interface{}{"err": err})
		return errors.New("WorkOrderMainUserAssignRepairUserProcessEvent workOrder.UpdateColumn error")
	}
	return nil
}

func (el WorkOrderMainUserAssignRepairUserProcessEvent) Id() string {
	return "WorkOrderMainUserAssignRepairUserProcessEvent"
}
func (el WorkOrderMainUserAssignRepairUserProcessEvent) Name() string {
	return "报修流程主负责人指派节点进入事件监听方法"
}
func (el WorkOrderMainUserAssignRepairUserProcessEvent) Desc() string {
	return "报修流程主负责人指派节点进入事件监听方法"
}
func (el WorkOrderMainUserAssignRepairUserProcessEvent) TemplateFormId() string {
	return config.DeviceWorkOrderReportFormTemplate
}

// WorkOrderFirstUserAssignRepairUserProcessEvent 报修工单一级指派人指派维修人员进入事件
type WorkOrderFirstUserAssignRepairUserProcessEvent struct {
}

func (el WorkOrderFirstUserAssignRepairUserProcessEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("WorkOrderFirstUserAssignRepairUserProcessEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("WorkOrderFirstUserAssignRepairUserProcessEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("WorkOrderFirstUserAssignRepairUserProcessEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	err = (&workOrderModel.WorkOrder{}).UpdateColumn(process.ItemId, "FormStep", util.ProcessFormStepSix)
	if err != nil {
		log.ErrorFields("WorkOrderFirstUserAssignRepairUserProcessEvent workOrder.UpdateColumn error", map[string]interface{}{"err": err})
		return errors.New("WorkOrderFirstUserAssignRepairUserProcessEvent workOrder.UpdateColumn error")
	}
	return nil
}

func (el WorkOrderFirstUserAssignRepairUserProcessEvent) Id() string {
	return "WorkOrderFirstUserAssignRepairUserProcessEvent"
}
func (el WorkOrderFirstUserAssignRepairUserProcessEvent) Name() string {
	return "报修流程一级指派人指派节点进入事件监听方法"
}
func (el WorkOrderFirstUserAssignRepairUserProcessEvent) Desc() string {
	return "报修流程一级指派人指派节点进入事件监听方法"
}
func (el WorkOrderFirstUserAssignRepairUserProcessEvent) TemplateFormId() string {
	return config.DeviceWorkOrderReportFormTemplate
}

// WorkOrderSecondUserAssignRepairUserProcessEvent 报修工单二级指派人指派维修人员进入事件
type WorkOrderSecondUserAssignRepairUserProcessEvent struct {
}

func (el WorkOrderSecondUserAssignRepairUserProcessEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("WorkOrderSecondUserAssignRepairUserProcessEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("WorkOrderSecondUserAssignRepairUserProcessEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("WorkOrderSecondUserAssignRepairUserProcessEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	err = (&workOrderModel.WorkOrder{}).UpdateColumn(process.ItemId, "FormStep", util.ProcessFormStepEight)
	if err != nil {
		log.ErrorFields("WorkOrderSecondUserAssignRepairUserProcessEvent workOrder.UpdateColumn error", map[string]interface{}{"err": err})
		return errors.New("WorkOrderSecondUserAssignRepairUserProcessEvent workOrder.UpdateColumn error")
	}
	return nil
}

func (el WorkOrderSecondUserAssignRepairUserProcessEvent) Id() string {
	return "WorkOrderSecondUserAssignRepairUserProcessEvent"
}
func (el WorkOrderSecondUserAssignRepairUserProcessEvent) Name() string {
	return "报修流程二级指派人指派节点进入事件监听方法"
}
func (el WorkOrderSecondUserAssignRepairUserProcessEvent) Desc() string {
	return "报修流程二级指派人指派节点进入事件监听方法"
}
func (el WorkOrderSecondUserAssignRepairUserProcessEvent) TemplateFormId() string {
	return config.DeviceWorkOrderReportFormTemplate
}
