package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"errors"
	"strconv"
	"time"
)

type StaffLeaveEditProcessEndEvent struct {
}

func (el StaffLeaveEditProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("StaffLeaveEditProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("StaffLeaveEditProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	//查询请假修改记录信息
	leaveEditRecord := (&hrModel.ApplyLeaveEditRecord{}).FirstBy(process.ItemId)
	// 请假新记录信息
	var newLeaveRecord hrModel.ApplyLeaveRecord
	bytes, _ := leaveEditRecord.NewData.MarshalJSON()
	err = json.Unmarshal(bytes, &newLeaveRecord)
	if err != nil {
		log.ErrorFields("解析失败", map[string]interface{}{"err": err})
		return errors.New("json.Unmarshal error")
	}
	//查询请假旧记录信息
	oldLeaveRecord := (&hrModel.ApplyLeaveRecord{}).FirstBy(leaveEditRecord.ApplyLeaveRecordId)
	// 开始修改数据
	newLeaveRecord.EditDate = model.LocalTime(time.Now())
	tx := model.DB().Begin()
	err = newLeaveRecord.TransactionUpdates(tx) // 修改请假数据
	if err != nil {
		tx.Rollback()
		log.ErrorFields("ApplyLeaveRecord.Updates error", map[string]interface{}{"err": err})
		return errors.New("ApplyLeaveRecord.Updates error")
	}
	err = (&hrModel.ApplyLeaveRecordDate{}).Clear(tx, newLeaveRecord.Id) // 清空请假天数
	if err != nil {
		tx.Rollback()
		log.ErrorFields("ApplyLeaveRecordDate.Clear error", map[string]interface{}{"err": err})
		return errors.New("ApplyLeaveRecordDate.Clear error")
	}
	var leaveRecordDates []hrModel.ApplyLeaveRecordDate
	for i := range newLeaveRecord.LeaveDates {
		leaveDate, err := time.ParseInLocation(model.DateFormat, newLeaveRecord.LeaveDates[i], time.Local)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("LeaveDates time.ParseInLocation error", map[string]interface{}{"err": err})
			return errors.New("LeaveDates time.ParseInLocation error")
		}
		leaveRecordDates = append(leaveRecordDates, hrModel.ApplyLeaveRecordDate{
			ApplyLeaveRecordId: newLeaveRecord.Id,
			Date:               model.LocalTime(leaveDate),
		})
	}
	err = (&hrModel.ApplyLeaveRecordDate{}).TransactionCreate(tx, leaveRecordDates) // 生成请假天数列表
	if err != nil {
		tx.Rollback()
		log.ErrorFields("ApplyLeaveRecordDate.TransactionCreate error", map[string]interface{}{"err": err})
		return errors.New("ApplyLeaveRecordDate.TransactionCreate error")
	}
	tx.Commit()
	// 旧数据是年休假 需要返还就数据的年休假天数
	var payBackManagement hrModel.LeaveManagement
	if leaveEditRecord.OldLeaveType == util.LeaveTypeForAnnualLeave {
		payBackManagement = (&hrModel.LeaveManagement{}).GetStaffLeaveByYear(oldLeaveRecord.StaffId, leaveEditRecord.OldLeaveYear)
		if payBackManagement.Id != 0 {
			payBackManagement.UsedDay -= leaveEditRecord.OldDayCount / 10
			err = model.DB().Model(&hrModel.LeaveManagement{}).Where("Id=?", payBackManagement.Id).Updates(map[string]interface{}{
				"UsedDay": payBackManagement.UsedDay,
			}).Error
			if err != nil {
				tx.Rollback()
				log.ErrorFields("save error", map[string]interface{}{"err": err})
				return errors.New("年假返还失败")
			}
		}
	}
	//年休假 请假审批通过后  需要更新年休假管理里假期使用时长
	if newLeaveRecord.LeaveType == util.LeaveTypeForAnnualLeave {
		leaveRecordDate := (&hrModel.ApplyLeaveRecordDate{}).GetByLeaveRecordId(newLeaveRecord.Id)
		var startAt time.Time
		if len(leaveRecordDate) > 0 {
			startAt = time.Time(leaveRecordDate[0].Date)
		} else {
			startAt = time.Time(newLeaveRecord.StartAt)
		}
		if startAt.IsZero() {
			startAt = time.Now().AddDate(0, 0, 1)
		}
		//查询请假开始时间之前的最近一次年休假发放记录
		annualLeave := (&hrModel.LeaveManagement{}).StaffLatestAnnualLeave(newLeaveRecord.StaffId, util.LeaveTypeForAnnualLeave, startAt)
		if annualLeave.Id > 0 {
			annualLeave.UsedDay += newLeaveRecord.DayCount / 10
			err = annualLeave.UpdateColumn("UsedDay", annualLeave.UsedDay)
			if err != nil {
				log.ErrorFields("StaffLeaveProcessEndEvent annualLeave.UpdateColumn error", map[string]interface{}{"err": err})
				return errors.New("StaffLeaveProcessEndEvent annualLeave.UpdateColumn error")
			}
		}
	}

	return nil
}
func (el StaffLeaveEditProcessEndEvent) Id() string {
	return "StaffLeaveProcessEndEvent"
}
func (el StaffLeaveEditProcessEndEvent) Name() string {
	return "员工请假修改流程结束事件监听方法"
}
func (el StaffLeaveEditProcessEndEvent) Desc() string {
	return "员工请假修改流程结束后异步执行的事件"
}
func (el StaffLeaveEditProcessEndEvent) TemplateFormId() string {
	return config.StaffLeaveEditFormTemplate
}
