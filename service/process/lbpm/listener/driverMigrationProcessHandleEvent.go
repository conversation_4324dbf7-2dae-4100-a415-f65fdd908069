package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
)

type DriverMigrationProcessHandleEvent struct {
}

// Handler 车辆调动调动接收人监听事件
func (el DriverMigrationProcessHandleEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("DriverMigrationProcessHandleEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("DriverMigrationProcessHandleEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("DriverMigrationProcessHandleEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	//更新业务数据表中的审批状态
	model.DB().Table(process.ItemTableName).Where("Id = ?", process.ItemId).Update("formstep", util.ProcessFormStepTwo)

	return nil
}
func (el DriverMigrationProcessHandleEvent) Id() string {
	return "DriverMigrationProcessHandleEvent"
}
func (el DriverMigrationProcessHandleEvent) Name() string {
	return "司机调动流程劳资节点监听方法"
}
func (el DriverMigrationProcessHandleEvent) Desc() string {
	return "司机调动流程劳资节点执行的方法"
}
func (el DriverMigrationProcessHandleEvent) TemplateFormId() string {
	return config.DriverMigrationApplyFormTemplate
}
