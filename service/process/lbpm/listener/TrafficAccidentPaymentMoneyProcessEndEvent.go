package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/service"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
)

// TrafficAccidentPaymentMoneyProcessEndEvent 事故付款流程结束时执行的方法
type TrafficAccidentPaymentMoneyProcessEndEvent struct {
}

func (el TrafficAccidentPaymentMoneyProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("TrafficAccidentPaymentMoneyProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("TrafficAccidentPaymentMoneyProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentPaymentMoneyProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	var payment safetyModel.TrafficAccidentPaymentMoneyRecord
	err = payment.FindBy(process.ItemId)
	if err != nil {
		log.ErrorFields("TrafficAccidentPaymentMoneyRecord not found", map[string]interface{}{"id": process.ItemId})
		return errors.New("TrafficAccidentPaymentMoneyRecord not found")
	}

	var paymentParam safetyModel.TrafficAccidentPaymentMoneyRecord
	err = json.Unmarshal(process.Param, &paymentParam)
	if err != nil {
		log.ErrorFields("TrafficAccidentEditProcessEndEvent accident json Unmarshal error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentEditProcessEndEvent accident json Unmarshal error")
	}

	// 不勾选"借款" -> 无其他分支 -> 流程结束 自动分支结案 自动事故结案
	// 不勾选"借款" -> 有其他分支 -> 流程结束 自动分支结案 手动事故结案

	if payment.IsLend == util.StatusForFalse {

		var branch safetyModel.TrafficAccidentRelaterBranch
		err = branch.FindBy(payment.TrafficAccidentRelaterBranchId)
		if err != nil {
			log.ErrorFields("branch.FindBy error", map[string]interface{}{"err": err})
			return errors.New("TrafficAccidentPaymentMoneyRecord not found")
		}
		branch.SolutionType = paymentParam.SolutionType
		branch.SolutionFilePath = paymentParam.SolutionFilePath
		branch.InsuranceCompanyPayMoney = paymentParam.InsuranceCompanyPayMoney
		branch.InsurancePayMoney = paymentParam.InsurancePayMoney
		branch.LossMoney = paymentParam.LossMoney
		branch.ClosedApplyStatus = util.ApplyStatusForDone
		branch.SolutionDesc = paymentParam.SolutionDesc
		branch.PayOrigin = paymentParam.PayOrigin

		tx := model.DB().Begin()
		// 查询事故分支
		branches := (&safetyModel.TrafficAccidentRelaterBranch{}).GetByAccidentId(payment.TrafficAccidentId)
		if len(branches) > 1 {
			// 有其他分支，自动分支结案

			err = branch.TransactionUpdateCloseInfo(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("TransactionUpdateCloseInfo error", map[string]interface{}{"err": err})
				return errors.New("TransactionUpdateCloseInfo error")
			}

			err = branch.TransactionUpdateClose(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("TransactionUpdateClose error", map[string]interface{}{"err": err})
				return errors.New("TransactionUpdateClose error")
			}

		} else {
			// 无其他分支，自动分支结案，自动事故结案
			err = branch.TransactionUpdateCloseInfo(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("TransactionUpdateCloseInfo error", map[string]interface{}{"err": err})
				return errors.New("TransactionUpdateCloseInfo error")
			}

			err = branch.TransactionUpdateClose(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("TransactionUpdateClose error", map[string]interface{}{"err": err})
				return errors.New("TransactionUpdateClose error")
			}

			// 事故自动结案
			var accident safetyModel.TrafficAccident
			err = accident.FindBy(branch.TrafficAccidentId)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("accident.FindBy error", map[string]interface{}{"id": branch.TrafficAccidentId})
				return errors.New("accident error")
			}

			accident.PersonalPayRatio = paymentParam.PersonalPayRatio
			accident.LossMoney = paymentParam.LossMoney
			accident.CloseDesc = paymentParam.SolutionDesc
			accident.InsuranceCompanyPayMoney = paymentParam.InsuranceCompanyPayMoney
			accident.InsurancePayMoney = paymentParam.InsurancePayMoney
			accident.CloseFilePath = paymentParam.SolutionFilePath
			accident.PayOrigin = paymentParam.PayOrigin
			accident.ClosedApplyStatus = util.ApplyStatusForDone

			err = accident.TransactionUpdateCloseInfo(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("accident.TransactionUpdateCloseInfo error", map[string]interface{}{"err": err})
				return errors.New("TransactionUpdateCloseInfo error")
			}
			err = accident.TransactionUpdateClose(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("accident.TransactionUpdateClose error", map[string]interface{}{"err": err})
				return errors.New("TransactionUpdateClose error")
			}
			// 事故结案 更新事故预警告警
			go service.CheckAccidentAlarm(accident.Id)
		}
		tx.Commit()
		return nil
	}

	return nil
}
func (el TrafficAccidentPaymentMoneyProcessEndEvent) Id() string {
	return "TrafficAccidentPaymentMoneyProcessEndEvent"
}
func (el TrafficAccidentPaymentMoneyProcessEndEvent) Name() string {
	return "事故付款流程结束事件监听方法"
}
func (el TrafficAccidentPaymentMoneyProcessEndEvent) Desc() string {
	return "事故付款流程结束时执行的方法"
}
func (el TrafficAccidentPaymentMoneyProcessEndEvent) TemplateFormId() string {
	return config.TrafficAccidentPaymentMoneyFormTemplate
}
