package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
)

// TrafficAccidentPaymentMoneyProcessEndEvent 事故付款流程结束时执行的方法
type TrafficAccidentPaymentMoneyProcessEndEvent struct {
}

func (el TrafficAccidentPaymentMoneyProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("TrafficAccidentPaymentMoneyProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("TrafficAccidentPaymentMoneyProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentPaymentMoneyProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	var payment safetyModel.TrafficAccidentPaymentMoneyRecord
	err = payment.FindBy(process.ItemId)
	if err != nil {
		log.ErrorFields("TrafficAccidentPaymentMoneyRecord not found", map[string]interface{}{"id": process.ItemId})
		return errors.New("TrafficAccidentPaymentMoneyRecord not found")
	}

	var paymentParam safetyModel.TrafficAccidentPaymentMoneyRecord
	err = json.Unmarshal(process.Param, &paymentParam)
	if err != nil {
		log.ErrorFields("TrafficAccidentEditProcessEndEvent accident json Unmarshal error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentEditProcessEndEvent accident json Unmarshal error")
	}

	return nil
}
func (el TrafficAccidentPaymentMoneyProcessEndEvent) Id() string {
	return "TrafficAccidentPaymentMoneyProcessEndEvent"
}
func (el TrafficAccidentPaymentMoneyProcessEndEvent) Name() string {
	return "事故付款流程结束事件监听方法"
}
func (el TrafficAccidentPaymentMoneyProcessEndEvent) Desc() string {
	return "事故付款流程结束时执行的方法"
}
func (el TrafficAccidentPaymentMoneyProcessEndEvent) TemplateFormId() string {
	return config.TrafficAccidentPaymentMoneyFormTemplate
}
