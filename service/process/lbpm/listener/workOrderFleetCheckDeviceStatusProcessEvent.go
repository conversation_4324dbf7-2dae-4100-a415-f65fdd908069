package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	processModel "app/org/scs/erpv2/api/model/process"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
)

// FleetCheckDeviceStatusProcessEvent 报修工单车队判断设备状态节点进入事件
type FleetCheckDeviceStatusProcessEvent struct {
}

func (el FleetCheckDeviceStatusProcessEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("FleetCheckDeviceStatusProcessEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("FleetCheckDeviceStatusProcessEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("FleetCheckDeviceStatusProcessEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	err = (&workOrderModel.WorkOrder{}).UpdateColumn(process.ItemId, "FormStep", util.ProcessFormStepTwo)
	if err != nil {
		log.ErrorFields("FleetCheckDeviceStatusProcessEvent workOrder.UpdateColumn error", map[string]interface{}{"err": err})
		return errors.New("FleetCheckDeviceStatusProcessEvent workOrder.UpdateColumn error")
	}
	return nil
}

func (el FleetCheckDeviceStatusProcessEvent) Id() string {
	return "FleetCheckDeviceStatusProcessEvent"
}
func (el FleetCheckDeviceStatusProcessEvent) Name() string {
	return "报修流程车队判断设备状态事件监听方法"
}
func (el FleetCheckDeviceStatusProcessEvent) Desc() string {
	return "报修流程车队判断设备状态事件监听方法"
}
func (el FleetCheckDeviceStatusProcessEvent) TemplateFormId() string {
	return config.DeviceWorkOrderReportFormTemplate
}
