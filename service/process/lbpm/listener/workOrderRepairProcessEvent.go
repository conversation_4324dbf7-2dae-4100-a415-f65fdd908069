package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	processModel "app/org/scs/erpv2/api/model/process"
	workOrderModel "app/org/scs/erpv2/api/model/workOrder"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
)

// 报修工单侦听流程是否进入节点
type RepairProcessEvent struct {
}

func (el RepairProcessEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("RepairProcessEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("RepairProcessEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("RepairProcessEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	// 允许选择备件
	err = (&workOrderModel.WorkOrder{}).UpdateColumns(process.ItemId, map[string]interface{}{
		"FormStep":          util.ProcessFormStepTen,
		"IsMaterialMissing": util.StatusForFalse,
	})
	if err != nil {
		log.ErrorFields("RepairProcessEvent WorkOrder.UpdateColumn error", map[string]interface{}{"err": err})
		return errors.New("RepairProcessEvent WorkOrder.UpdateColumn error")
	}

	return nil
}
func (el RepairProcessEvent) Id() string {
	return "RepairProcessEvent"
}
func (el RepairProcessEvent) Name() string {
	return "报修流程维修人节点进入事件监听方法"
}
func (el RepairProcessEvent) Desc() string {
	return "报修流程维修人节点进入事件监听方法"
}
func (el RepairProcessEvent) TemplateFormId() string {
	return config.DeviceWorkOrderReportFormTemplate
}
