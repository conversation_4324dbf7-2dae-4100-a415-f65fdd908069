package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model/maintenance"
	processModel "app/org/scs/erpv2/api/model/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"errors"
	"fmt"
	"strconv"
)

type GlassRepairAddRepairFormEvent struct {
}

func (gr GlassRepairAddRepairFormEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("GlassRepairAddRepairFormEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("GlassRepairAddRepairFormEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("GlassRepairAddRepairFormEvent process.FindBy error")
	}

	// 允许当前人员添加表单内容
	err = (&maintenance.GlassRepair{}).UpdateCurrentOperation(process.ItemId, maintenance.WRITE_GR_2, maintenance.REPAIR_GR_2)
	if err != nil {
		log.ErrorFields("ProcessEndEvent UpdateCurrentOperation error", map[string]interface{}{"err": err})
		return errors.New("ProcessEndEvent UpdateCurrentOperation error")
	}

	return nil
}
func (gr GlassRepairAddRepairFormEvent) Id() string {
	return "GlassRepairAddRepairFormEvent"
}
func (gr GlassRepairAddRepairFormEvent) Name() string {
	return "玻璃维修流程填写维修信息处理事件监听方法"
}
func (gr GlassRepairAddRepairFormEvent) Desc() string {
	return "玻璃维修流程流程填写维修信息处理执行的方法"
}
func (gr GlassRepairAddRepairFormEvent) TemplateFormId() string {
	return config.GlassRepairFormTemplate
}

type GlassRepairAddPaymentFormEvent struct {
}

func (gr GlassRepairAddPaymentFormEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("GlassRepairAddPaymentFormEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)
	if err != nil {
		log.ErrorFields("GlassRepairAddPaymentFormEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("GlassRepairAddPaymentFormEvent process.FindBy error")
	}

	// 允许当前人员添加表单内容
	err = (&maintenance.GlassRepair{}).UpdateCurrentOperation(process.ItemId, maintenance.WRITE_GR_2, maintenance.PAYMENT_GR_3)
	if err != nil {
		log.ErrorFields("ProcessEndEvent UpdateCurrentOperation error", map[string]interface{}{"err": err})
		return errors.New("ProcessEndEvent UpdateCurrentOperation error")
	}

	return nil
}
func (gr GlassRepairAddPaymentFormEvent) Id() string {
	return "GlassRepairAddPaymentFormEvent"
}
func (gr GlassRepairAddPaymentFormEvent) Name() string {
	return "玻璃维修流程填写付款信息处理事件监听方法"
}
func (gr GlassRepairAddPaymentFormEvent) Desc() string {
	return "玻璃维修流程填写付款信息流程处理执行的方法"
}
func (gr GlassRepairAddPaymentFormEvent) TemplateFormId() string {
	return config.GlassRepairFormTemplate
}
