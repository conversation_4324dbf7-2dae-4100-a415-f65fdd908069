package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
	"time"
)

type StaffLeaveProcessEndEvent struct {
}

func (el StaffLeaveProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("StaffLeaveProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("StaffLeaveProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("StaffLeaveProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	//查询请假记录信息
	leaveRecord := (&hrModel.ApplyLeaveRecord{}).FirstBy(process.ItemId)

	//年休假 请假审批通过后  需要更新年休假管理里假期使用时长
	if leaveRecord.LeaveType == util.LeaveTypeForAnnualLeave {
		leaveRecordDate := (&hrModel.ApplyLeaveRecordDate{}).GetByLeaveRecordId(leaveRecord.Id)
		var startAt time.Time
		if len(leaveRecordDate) > 0 {
			startAt = time.Time(leaveRecordDate[0].Date)
		} else {
			startAt = time.Time(leaveRecord.StartAt)
		}

		if startAt.IsZero() {
			startAt = time.Now().AddDate(0, 0, 1)
		}

		//查询请假开始时间之前的最近一次年休假发放记录
		annualLeave := (&hrModel.LeaveManagement{}).StaffLatestAnnualLeave(leaveRecord.StaffId, util.LeaveTypeForAnnualLeave, startAt)

		if annualLeave.Id > 0 {
			annualLeave.UsedDay += leaveRecord.DayCount / 10

			err = annualLeave.UpdateColumn("UsedDay", annualLeave.UsedDay)
			if err != nil {
				log.ErrorFields("StaffLeaveProcessEndEvent annualLeave.UpdateColumn error", map[string]interface{}{"err": err})
				return errors.New("StaffLeaveProcessEndEvent annualLeave.UpdateColumn error")
			}
		}
	}
	return nil
}
func (el StaffLeaveProcessEndEvent) Id() string {
	return "StaffLeaveProcessEndEvent"
}
func (el StaffLeaveProcessEndEvent) Name() string {
	return "员工请假流程结束事件监听方法"
}
func (el StaffLeaveProcessEndEvent) Desc() string {
	return "员工请假流程结束后异步执行的事件"
}
func (el StaffLeaveProcessEndEvent) TemplateFormId() string {
	return config.StaffLeaveFormTemplate
}

//func FixAnnualLeaveBug() {
//	var records []hrModel.ApplyLeaveRecord
//	model.DB().Model(&hrModel.ApplyLeaveRecord{}).Where("StartAt IS NULL AND ApplyStatus = 2 AND LeaveType = 1").Find(&records)
//
//	for _, leaveRecord := range records {
//		leaveRecordDate := (&hrModel.ApplyLeaveRecordDate{}).GetByLeaveRecordId(leaveRecord.Id)
//		var startAt time.Time
//		if len(leaveRecordDate) > 0 {
//			startAt = time.Time(leaveRecordDate[0].Date)
//		} else {
//			startAt = time.Time(leaveRecord.StartAt)
//		}
//
//		if startAt.IsZero() {
//			startAt = time.Now().AddDate(0, 0, 1)
//		}
//
//		//查询请假开始时间之前的最近一次年休假发放记录
//		annualLeave := (&hrModel.LeaveManagement{}).StaffLatestAnnualLeave(leaveRecord.StaffId, util.LeaveTypeForAnnualLeave, startAt)
//
//		if annualLeave.Id > 0 {
//			annualLeave.UsedDay += leaveRecord.DayCount / 10
//			err := annualLeave.UpdateColumn("UsedDay", annualLeave.UsedDay)
//			if err != nil {
//				log.ErrorFields("FixAnnualLeaveBug annualLeave.UpdateColumn error", map[string]interface{}{"err": err})
//			}
//		}
//	}
//}
