package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
)

type TrafficAccidentEditProcessEndEvent struct {
}

func (el TrafficAccidentEditProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("TrafficAccidentEditProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("TrafficAccidentEditProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentEditProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	var oldAccident safetyModel.TrafficAccident
	err = oldAccident.FindBy(process.ItemId)
	if err != nil {
		log.ErrorFields("TrafficAccidentEditProcessEndEvent TrafficAccident not found", map[string]interface{}{"id": process.ItemId})
		return errors.New("TrafficAccidentEditProcessEndEvent  TrafficAccident not found")
	}

	var accident safetyModel.TrafficAccident
	err = json.Unmarshal(process.Param, &accident)
	if err != nil {
		log.ErrorFields("TrafficAccidentEditProcessEndEvent accident json Unmarshal error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentEditProcessEndEvent accident json Unmarshal error")
	}

	tx := model.DB().Begin()

	//更新事故当事人
	//if len(accident.Relaters) > 0 {
	//	for i := range accident.Relaters {
	//		if accident.Relaters[i].ActionType == "edit" && accident.Relaters[i].Id > 0 {
	//			accident.Relaters[i].TrafficAccidentId = oldAccident.Id
	//			err = accident.Relaters[i].TransactionUpdate(tx)
	//			if err != nil {
	//				log.ErrorFields("TrafficAccidentEditProcessEndEvent accident.Relaters update error", map[string]interface{}{"err": err, "relater": accident.Relaters[i]})
	//			}
	//		}
	//		if accident.Relaters[i].ActionType == "create" {
	//			accident.Relaters[i].TrafficAccidentId = oldAccident.Id
	//			err = accident.Relaters[i].TransactionCreate(tx)
	//			if err != nil {
	//				log.ErrorFields("TrafficAccidentEditProcessEndEvent accident.Relaters create error", map[string]interface{}{"err": err, "relater": accident.Relaters[i]})
	//			}
	//		}
	//		if accident.Relaters[i].ActionType == "delete" && accident.Relaters[i].Id > 0 {
	//			err = accident.Relaters[i].TransactionDelete(tx, accident.Relaters[i].Id)
	//			if err != nil {
	//				log.ErrorFields("TrafficAccidentEditProcessEndEvent accident.Relaters delete error", map[string]interface{}{"err": err, "relater": accident.Relaters[i]})
	//			}
	//		}
	//	}
	//}

	//如果事故是无车损无人伤，并且责任已定，并且未结案 则自动结案（已移除）
	//if accident.HurtStatus == util.AccidentHurtStatus_1 &&
	//	accident.LiabilityType != util.AccidentLiabilityTypeForUnknown &&
	//	accident.LiabilityType != util.AccidentLiabilityTypeForPending &&
	//	oldAccident.IsClosed == util.StatusForFalse {
	//	accident.IsClosed = util.StatusForTrue
	//	accident.ClosedApplyStatus = util.ApplyStatusForDone
	//}

	//更新事故
	err = accident.TransactionUpdate(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("TrafficAccidentEditProcessEndEvent accident.Update error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentEditProcessEndEvent accident.Update error")
	}

	tx.Commit()

	//当事故车辆需要维修，并且没有车损分支时，系统需自动创建当事人和车损分支
	var relaterId int64
	if accident.IsMustFix == util.StatusForTrue && !accident.ExistSelfVehicleBrokenBranch(accident.Id) {
		relaterId, err = CreateAccidentRelater(&accident)
		if err != nil {
			return err
		}

		err = CreateAccidentSelfVehicleBrokenBranch(&accident, relaterId)
		if err != nil {
			return err
		}
	}

	var hasSelfVehicleBrokenBranchProcess bool
	//查询事故的己方车损分支
	branch := (&safetyModel.TrafficAccidentRelaterBranch{}).FindSelfVehicleBrokenBranchByAccidentId(accident.Id)
	hasSelfVehicleBrokenBranch := branch.Id > 0
	if hasSelfVehicleBrokenBranch {
		//判断己方车损分支是否有流程
		hasSelfVehicleBrokenBranchProcess = branch.IsExistNormalProcess(branch.Id)
	}

	//事故是否维修状态为否，并且有己方车损分支，并且己方车损分支未有相关流程  则删除己方车损分支
	if accident.IsMustFix == util.StatusForFalse && hasSelfVehicleBrokenBranch && !hasSelfVehicleBrokenBranchProcess {
		err = branch.Delete(branch.Id)
		if err != nil {
			log.ErrorFields("TrafficAccidentEditProcessEndEvent branch.Delete error", map[string]interface{}{"err": err})
		}
	}

	hasSelfPeopleHurt := strings.Contains(accident.OwnStatus, strconv.FormatInt(util.AccidentBranchTypeForSelfPeopleHurt, 10))
	//如果己方人伤被勾选 需创建己方人伤分支
	if hasSelfPeopleHurt {
		if relaterId == 0 {
			relaterId, err = CreateAccidentRelater(&accident)
			if err != nil {
				return err
			}
		}

		err = CreateAccidentSelfPeopleHurtBranch(&accident, relaterId)
		if err != nil {
			return err
		}
	}

	//如果己方人伤没有勾选，则需删除己方人伤分支以及分支上的无效流程
	if !hasSelfPeopleHurt {
		err = DeleteSelfPeopleHurtBranchAndProcess(accident.Id)
		if err != nil {
			log.ErrorFields("TrafficAccidentEditProcessEndEvent DeleteSelfPeopleHurtBranchAndProcess error", map[string]interface{}{"err": err})
		}
	}

	return nil
}
func (el TrafficAccidentEditProcessEndEvent) Id() string {
	return "TrafficAccidentEditProcessEndEvent"
}
func (el TrafficAccidentEditProcessEndEvent) Name() string {
	return "事故变更流程结束事件监听方法"
}
func (el TrafficAccidentEditProcessEndEvent) Desc() string {
	return "事故变更流程审批结束执行的方法"
}
func (el TrafficAccidentEditProcessEndEvent) TemplateFormId() string {
	return config.TrafficAccidentEditFormTemplate
}

func DeleteSelfPeopleHurtBranchAndProcess(accidentId int64) error {
	branch := (&safetyModel.TrafficAccidentRelaterBranch{}).FindSelfPeopleHurtBranchByAccidentId(accidentId)
	if branch.Id == 0 {
		return nil
	}

	if (&safetyModel.TrafficAccidentRelaterBranch{}).IsExistNormalProcess(branch.Id) {
		log.ErrorFields("TrafficAccidentEditProcessEndEvent DeleteSelfPeopleHurtBranch IsExistNormalProcess", nil)
		return nil
	}

	//查询借款记录
	records := (&safetyModel.TrafficAccidentLendMoneyRecord{}).GetByBranchIdForAll(branch.Id)
	tx := model.DB().Begin()
	var processes []processModel.LbpmApplyProcess
	for i := range records {
		//删除借款记录
		err := records[i].TransactionDelete(tx)
		if err != nil {
			tx.Rollback()
			return err
		}
		//查询流程
		lendMoneyProcesses := (&processModel.LbpmApplyProcess{}).GetProcessesByItemId(config.TrafficAccidentLendMoneyFormTemplate, records[i].Id)
		processes = append(processes, lendMoneyProcesses...)
	}

	//查询分支结案流程
	closedProcesses := (&processModel.LbpmApplyProcess{}).GetProcessesByItemId(config.TrafficAccidentBranchCloseFormTemplate, branch.Id)
	if len(closedProcesses) > 0 {
		processes = append(processes, closedProcesses...)
	}

	for j := range processes {
		//删除流程和流程相关的处理人
		err := processes[j].TransactionDelete(tx)
		if err != nil {
			tx.Rollback()
			return err
		}

		err = (&processModel.LbpmApplyProcessHasHandler{}).TransactionDelete(tx, processes[j].FormInstanceId)
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	err := branch.Delete(branch.Id)
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}
