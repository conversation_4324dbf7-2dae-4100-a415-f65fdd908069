package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	hrModel "app/org/scs/erpv2/api/model/hr"
	processModel "app/org/scs/erpv2/api/model/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"
)

type StaffRetireProcessEndEvent struct {
}

func (el StaffRetireProcessEndEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("StaffRetireProcessEndEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("StaffRetireProcessEndEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("StaffRetireProcessEndEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	//查询退休记录
	record := (&hrModel.StaffRetireApplyRecord{}).FirstBy(process.ItemId)

	oetStaff := rpc.GetStaffWithId(context.TODO(), record.StaffId)
	if oetStaff == nil {
		return errors.New("rpc.GetStaffWithId is nil")
	}
	oetStaff.WorkingState = util.JobStatusRetire
	oetStaff.RetireDate = time.Time(record.RetireAt).Unix()
	err = rpc.EditOetStaff(context.TODO(), record.OpUserId, oetStaff)
	if err != nil {
		log.ErrorFields("rpc.EditOetStaff error", map[string]interface{}{"err": err})
	}

	if record.RetireType == util.RetireTypeForEarly {
		//解除劳动合同
		err = (&hrModel.StaffLaborContract{}).UpdateStatusToRelieveByStaffArchiveId(record.StaffArchiveId, util.LaborContractRelieve, time.Time(record.RetireAt))
		if err != nil {
			log.ErrorFields("StaffLaborContract UpdateStatus error", map[string]interface{}{"err": err})
		}
	}

	err = (&hrModel.StaffArchive{}).UpdateRetireMoreByIds([]int64{record.StaffArchiveId}, record.RetireMore)
	if err != nil {
		log.ErrorFields("StaffArchive UpdateRetireMoreByIds error", map[string]interface{}{"err": err})
	}

	return nil
}
func (el StaffRetireProcessEndEvent) Id() string {
	return "StaffRetireProcessEndEvent"
}
func (el StaffRetireProcessEndEvent) Name() string {
	return "员工退休流程结束事件监听方法"
}
func (el StaffRetireProcessEndEvent) Desc() string {
	return "员工退休流程结束后异步执行的事件"
}
func (el StaffRetireProcessEndEvent) TemplateFormId() string {
	return config.StaffRetireApplyFormTemplate
}
