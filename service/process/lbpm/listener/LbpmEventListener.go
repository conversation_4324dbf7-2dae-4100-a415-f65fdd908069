package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"encoding/json"
	"errors"
	"sync"
)

var listeners = []LbpmListener{
	ProcessEndEvent{},                                      //流程结束监听器
	TrafficAccidentCashierNodeEvent{},                      //事故相关流程出纳环节进入事件监听器
	RepairProcessEvent{},                                   //维修工单审批人是否填写设备信息监听器
	RepairConfirmProcessEvent{},                            //设备报修工单维修结束后发起人审核节点进入监听器
	RepairProcessEndEvent{},                                //报修工单结束流程 更新设备明细出库状态
	StaffArchiveEditProcessEndEvent{},                      //人员档案编辑流程结束  更新档案信息
	StaffTransferProcessEndEvent{},                         //员工调动流程结束监听器
	PositionalTitleApplyProcessEndEvent{},                  //职称申请流程结束监听器
	TrafficAccidentCreateProcessEndEvent{},                 //事故提交流程结束事件
	TrafficAccidentEditProcessEndEvent{},                   //事故变更流程结束事件
	TrafficAccidentPaymentMoneyProcessHandleFixEvent{},     //事故付款流程处理表单事件（维修信息）
	TrafficAccidentPaymentMoneyProcessHandlePaymentEvent{}, //事故付款流程处理表单事件（付款信息）
	TrafficAccidentPaymentMoneyProcessEndEvent{},           //事故付款流程结束处理事件
	TrafficAccidentCloseProcessEndEvent{},                  //事故结案流程结束处理事件
	TrafficAccidentBranchCloseProcessHandleEvent{},         //事故分支结案流程结束处理事件（修改提交表单）
	TrafficAccidentCloseProcessHandleEvent{},               //事故结案流程结束处理事件（修改提交表单）
	GlassRepairAddRepairFormEvent{},                        //玻璃维修流程表单处理事件（维修信息）
	GlassRepairAddPaymentFormEvent{},                       //玻璃维修流程表单处理事件（付款信息）
	VehicleTransferProcessEndEvent{},                       //车辆调动流程结束监听事件
	VehicleTransferProcessHandleEvent{},                    //车辆调动流程接收人节点监听事件
	FleetCheckDeviceStatusProcessEvent{},                   //工单车队查看设备状态流程结束监听事件
	StaffLeaveProcessEndEvent{},                            //员工请假流程审批结束监听事件
	StaffLeaveScrapProcessEndEvent{},                       //员工请假废弃流程审批结束监听事件
	StaffLeaveEditProcessEndEvent{},                        //员工请假修改流程审批结束监听事件
	WorkOrderMainUserAssignRepairUserProcessEvent{},        //报修流程主负责人指派节点进入事件监听方法
	WorkOrderFirstUserAssignRepairUserProcessEvent{},       //报修流程一级指派人指派节点进入事件监听方法
	WorkOrderSecondUserAssignRepairUserProcessEvent{},      //报修流程二级指派人指派节点进入事件监听方法
	OperationReportApplyProcessEndEvent{},                  //运营报表数据审批流程结束事件
	PetitionWorkOrderHandleProcessEvent{},                  //信访工单流程处理节点进入事件
	PetitionWorkOrderSecondHandleProcessEvent{},            //信访工单交办人申诉进入事件
	PetitionWorkOrderReceiverFinalHandleProcessEvent{},     //信访工单交办人最终处理进入事件
	PetitionWorkOrderDepartmentReplyHandleProcessEvent{},   //信访工单部门回复节点进入事件gb
	PetitionWorkOrderFinalReplyHandleProcessEvent{},        //信访工单最终回复节点进入事件
	DriverMigrationProcessEndEvent{},                       //司机调动流程结束事件监听方法
	DriverMigrationProcessHandleEvent{},                    //司机调动流程老子节点监听事件
	DriverBecomeWorkerProcessHandleEvent{},                 //司机转正车队长节点监听事件
	DriverBecomeWorkerProcessEndEvent{},                    //司机转正流程结束监听事件
	HeadImgProcessEndEvent{},                               // 证件照审批结束监听事件
	StaffRetireProcessEndEvent{},                           //员工退休流程结束监听事件
	StaffQuitProcessEndEvent{},                             //员工离职流程结束监听事件
}

type ProcessData struct {
	ProcessId  string `json:"processId"`
	NodeFactId string `json:"nodeFactId"`
	DocStatus  string `json:"docStatus"`
}

type FunctionId struct {
	FunctionId string `json:"functionId"`
}

type LbpmListener interface {
	Handler(formId lbpmApi.FormId, data ProcessData) error
	Id() string
	Name() string
	Desc() string
	TemplateFormId() string
}

type EventListener struct {
	sync.RWMutex
	Method map[string]LbpmListener
}

func (el *EventListener) Read(key string) (LbpmListener, bool) {
	el.RLock()
	value, ok := el.Method[key]
	el.RUnlock()
	return value, ok
}
func (el *EventListener) Write(key string, value LbpmListener) {
	el.Lock()
	if el.Method == nil {
		el.Method = make(map[string]LbpmListener)
	}
	el.Method[key] = value
	el.Unlock()
}

func (el *EventListener) Methods() map[string]LbpmListener {
	return el.Method
}

var EventListenerMethod EventListener

func RegisterListener() {
	for i := range listeners {
		EventListenerMethod.Write(listeners[i].TemplateFormId()+"."+listeners[i].Id(), listeners[i])
	}
}

func DoListener(listenerId, formIdStr, processDataStr string) error {
	var functionId FunctionId
	err := json.Unmarshal([]byte(listenerId), &functionId)
	if err != nil {
		log.ErrorFields("DoListener json.Unmarshal functionId error", map[string]interface{}{"err": err})
		return errors.New("DoListener json.Unmarshal functionId error")
	}

	var formId lbpmApi.FormId
	err = json.Unmarshal([]byte(formIdStr), &formId)
	if err != nil {
		log.ErrorFields("DoListener json.Unmarshal formId error", map[string]interface{}{"err": err})
		return errors.New("DoListener json.Unmarshal formId error")
	}

	var processData ProcessData
	err = json.Unmarshal([]byte(processDataStr), &processData)
	if err != nil {
		log.ErrorFields("DoListener json.Unmarshal processData error", map[string]interface{}{"err": err})
		return errors.New("DoListener json.Unmarshal processData error")
	}
	listener, ok := EventListenerMethod.Read(formId.TemplateFormId + "." + functionId.FunctionId)

	if !ok {
		listener, ok = EventListenerMethod.Read(config.PublicFormTemplate + "." + functionId.FunctionId)
		if !ok {
			log.ErrorFields("DoListener EventListeners not fund function", map[string]interface{}{"functionId": functionId.FunctionId})
			return errors.New("DoListener json.Unmarshal processData error")
		}
	}
	return listener.Handler(formId, processData)
}
