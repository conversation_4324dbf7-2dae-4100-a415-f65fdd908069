package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
)

// TrafficAccidentPaymentMoneyProcessHandleFixEvent 事故付款流程中，维修表单开始填写时执行的事件
type TrafficAccidentPaymentMoneyProcessHandleFixEvent struct {
}

func (el TrafficAccidentPaymentMoneyProcessHandleFixEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("TrafficAccidentPaymentMoneyProcessHandleFixEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("TrafficAccidentPaymentMoneyProcessHandleFixEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentPaymentMoneyProcessHandleFixEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	err = (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).UpdateFormStep(process.ItemId, util.ProcessFormStepTwo)
	if err != nil {
		log.ErrorFields("TrafficAccidentPaymentMoneyProcessHandleFixEvent UpdateFormStep error", map[string]interface{}{"err": err})
	}
	return nil
}
func (el TrafficAccidentPaymentMoneyProcessHandleFixEvent) Id() string {
	return "TrafficAccidentPaymentMoneyProcessHandleFixEvent"
}
func (el TrafficAccidentPaymentMoneyProcessHandleFixEvent) Name() string {
	return "事故付款流程（维修信息填写）事件监听方法"
}
func (el TrafficAccidentPaymentMoneyProcessHandleFixEvent) Desc() string {
	return "事故付款流程（维修信息填写）开始时执行的方法"
}
func (el TrafficAccidentPaymentMoneyProcessHandleFixEvent) TemplateFormId() string {
	return config.TrafficAccidentPaymentMoneyFormTemplate
}

// TrafficAccidentPaymentMoneyProcessHandlePaymentEvent 事故付款流程中，付款信息表单开始填写时执行的事件
type TrafficAccidentPaymentMoneyProcessHandlePaymentEvent struct {
}

func (el TrafficAccidentPaymentMoneyProcessHandlePaymentEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("TrafficAccidentPaymentMoneyProcessHandlePaymentEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("TrafficAccidentPaymentMoneyProcessHandlePaymentEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("TrafficAccidentPaymentMoneyProcessHandlePaymentEvent process.FindBy error")
	}
	err = (&safetyModel.TrafficAccidentPaymentMoneyRecord{}).UpdateFormStep(process.ItemId, util.ProcessFormStepThree)
	if err != nil {
		log.ErrorFields("TrafficAccidentPaymentMoneyProcessHandleFixEvent UpdateFormStep error", map[string]interface{}{"err": err})
	}

	return nil
}
func (el TrafficAccidentPaymentMoneyProcessHandlePaymentEvent) Id() string {
	return "TrafficAccidentPaymentMoneyProcessHandlePaymentEvent"
}
func (el TrafficAccidentPaymentMoneyProcessHandlePaymentEvent) Name() string {
	return "事故付款流程（付款信息填写）事件监听方法"
}
func (el TrafficAccidentPaymentMoneyProcessHandlePaymentEvent) Desc() string {
	return "事故付款流程（付款信息填写）开始时执行的方法"
}
func (el TrafficAccidentPaymentMoneyProcessHandlePaymentEvent) TemplateFormId() string {
	return config.TrafficAccidentPaymentMoneyFormTemplate
}
