package listener

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	lbpmApi "app/org/scs/erpv2/api/service/process/lbpm/api"
	"app/org/scs/erpv2/api/util"
	"errors"
	"fmt"
	"strconv"
)

type VehicleTransferProcessHandleEvent struct {
}

// Handler 车辆调动调动接收人监听事件
func (el VehicleTransferProcessHandleEvent) Handler(formId lbpmApi.FormId, data ProcessData) error {
	fmt.Printf("VehicleTransferProcessHandleEvent handler===========%+v,%+v \r\n", formId, data)
	formInstanceId, _ := strconv.ParseInt(formId.FormInstanceId, 10, 64)
	var process processModel.LbpmApplyProcess
	err := process.FindBy(formInstanceId)

	if err != nil {
		log.ErrorFields("VehicleTransferProcessHandleEvent process.FindBy error", map[string]interface{}{"err": err})
		return errors.New("VehicleTransferProcessHandleEvent process.FindBy error")
	}

	if process.Status == util.ProcessStatusForAbandon {
		log.ErrorFields("process has abandoned!", nil)
		return nil
	}

	//更新业务数据表中的审批状态
	model.DB().Table(process.ItemTableName).Where("Id = ?", process.ItemId).Update("formstep", util.ProcessFormStepTwo)

	return nil
}
func (el VehicleTransferProcessHandleEvent) Id() string {
	return "VehicleTransferProcessHandleEvent"
}
func (el VehicleTransferProcessHandleEvent) Name() string {
	return "车辆调动流程接收人节点监听方法"
}
func (el VehicleTransferProcessHandleEvent) Desc() string {
	return "车辆调动流程接收人节点执行的方法"
}
func (el VehicleTransferProcessHandleEvent) TemplateFormId() string {
	return config.VehicleMigrationApplyFormTemplate
}
