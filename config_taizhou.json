{"# 规范要求": "参考http://www.oetlink.com/关于《软件程序运行配置文件》管理规范技术要求.pdf", "# 重点说明": "版本一旦发布之后, 配置文件处于受控状态, 不可轻易变更, 如需变更请走变更流程", "# 环境变量": "production:生产环境 local:本地环境 develop:开发环境", "Env": "production", "# 日志等级": "Info:显示所有日志 error:显示错误日志", "DebugLevel": "Info", "# 文件绝对路径前缀": "", "AbsDirPath": "/mnt/www/", "# 文件相对路径前缀": "", "WebRoot": "webroot", "# 文件访问域名端口": "", "StaticFileHttpPrefix": "https://ydd.tzgjjt.com:36085/", "# 是否关联调度": "true:关联调度 false:不关联调度", "IsRelationIss": true, "# 数据库配置": "Host:主机地址 Port:端口 DbName:数据库名称 User:登陆账号 Password:登陆密码", "Database": {"Host": "***********", "Port": 5432, "DbName": "erp", "User": "taizhou_erp", "Password": "taizhoubus123456atErp"}, "# ETCD配置": "Host:主机 Port:端口 Interval:心跳间隔  Ttl:失效时间", "Etcd": {"Host": "***********", "Port": 2379, "Interval": 10, "Ttl": 30}, "# micro配置": "ServiceName:服务名称", "Micro": {"ServiceName": "oet.scs.api.erp.v2"}, "# 小程序消息配置": "AppAccountId:对应的小程序ID DoorCheckTemplateId:门检消息模板ID Page:消息跳转的页面", "Mini": {"AppAccountId": 3, "DoorCheckTemplateId": "ayyjQYKdH2r8rzDKae-x7dyGcXagTX3HP8c-rjQU2d0", "Page": "erp/notice/index"}, "# 是否同步主数据的人员到ERP": "Enable:是否同步 CorporationIds:同步的顶级机构ID", "SyncMasterStaff": {"Enable": true, "CorporationIds": [1505961073969202177]}, "# 定时任务配置": "", "SchedulerJob": {"# 是否发送门检通知": "", "IsSendDoorCheckNotify": true, "# 是否发送劳动合同过期通知": "", "IsSendLaborContractExpireNotify": true, "# 是否发送试用期到期通知": "", "IsSendStaffProbationExpireNotify": true, "# 是否预先生成们门检记录": "", "IsPreGenerateDoorCheckRecord": true, "# 是否检测票务数据权限": "", "IsCheckTicketDataPermission": true, "# 是否流程报警": "", "IsProcessAlarm": true, "# 是否计算人资报表": "", "IsCalcStaffArchiveReport": true, "# 是否同步主数据线路人员归属关系数据": "", "IsTakeOetLineHasDriverRecord": true, "# 是否计算员工的年休假": "", "IsCalcStaffAnnualLeave": true, "# 是否定时获取台州IC卡交易结算数据": "", "IsTakeTaizhouThirdIcCardTradeData": true}, "# 顶级机构ID": "", "TopCorporationId": 1505961073969202177, "# 蓝凌流程配置": "Enable:是否开启 SysId:系统标识 AuthAccount:授权账号 AuthPassword:授权密码 ServicePrefix:ERP接口前缀 LbpmDomain:蓝凌接口前缀 ToDoPrefixDomain:待办跳转地址前缀", "Lbpm": {"Enable": true, "SysId": "erp-tz", "AuthAccount": "ekp", "AuthPassword": "ekp", "ServicePrefix": "http://10.10.14.1:5678/erp/v2/thirdparty/", "LbpmDomain": "https://10.10.10.10:9092/", "ToDoPrefixDomain": "https://erp.tzgjjt.com"}, "# 钉钉审批配置": "Enable:是否开启", "DingTalkBpm": {"Enable": false, "AgentId": 0, "AppKey": "", "AppSecret": "", "AesKey": "", "Token": ""}, "# 适用的权限": "taizhou:台州系统权限 daishan:岱山系统权限", "PermissionSys": "taizhou", "# 工单设备二维码跳转地址": "", "DeviceQrcodeUrl": "https://ydd.tzgjjt.com:34443/erp", "# 车辆二维码跳转地址": "", "VehicleQrcodeUrl": "https://ydd.tzgjjt.com:34443/vehicle", "# 票务模板银行对账时需要排除的线路": "", "TicketBankCheckExcludeLine": ["231", "253", "232", "255", "259", "258", "851", "257", "251", "256", "265", "862", "863", "860", "861A", "861B", "861C"], "# 人员档案报表/事故报表统计的机构ID": "", "StaffArchiveReportCorpId": 1728200467558695982, "# IC卡数据库连接配置": "", "ICMysqlDatabase": {"DbHost": "************:3306", "DbUser": "t<PERSON><PERSON><PERSON>", "DbPass": "Ujpyk653#k", "DbName": "tzy<PERSON>"}}