package scheduler

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/scheduler/command"
	"github.com/robfig/cron/v3"
)

func InitJobScheduler() {
	c := cron.New(cron.WithSeconds())
	var err error

	if config.Config.SchedulerJob.IsSendDoorCheckNotify {
		// 每天5查询门检异常未解决项 发送消息给今天车队长 0 0 5 * * ?
		_, err = c.AddFunc("0 0 5 * * ?", command.SendDoorCheckNotHandleNotify)
		if err != nil {
			log.ErrorFields("cron job [command.SendDoorCheckNotHandleNotify] error", map[string]interface{}{"err": err})
		}
	}

	if config.Config.SchedulerJob.IsPreGenerateDoorCheckRecord {
		// 每天2点预生成门检记录 0 0 2 * * ?
		_, err = c.AddFunc("0 0 2 * * ?", command.PreGenerateDoorCheckRecord)
		if err != nil {
			log.ErrorFields("cron job [command.PreGenerateDoorCheckRecord] error", map[string]interface{}{"err": err})
		}
	}

	if config.Config.SchedulerJob.IsSendLaborContractExpireNotify {
		//每天的3点查询是否有即将过期的劳动合同
		_, err = c.AddFunc("0 0 3 * * ?", command.GetCanNotifyContract)
		//_, err = c.AddFunc("0 */1 * * * ?", command.GetCanNotifyContract)
		if err != nil {
			log.ErrorFields("cron job [command.GetCanNotifyContract] error", map[string]interface{}{"err": err})
		}
		//每分钟检查下是否有合同即将到期并发送消息
		_, err = c.AddFunc("0 */1 * * * ?", command.SendNotifyForLaborContractExpire)
		//_, err = c.AddFunc("*/10 * * * * ?", command.SendNotifyForLaborContractExpire)
		if err != nil {
			log.ErrorFields("cron job [command.SendNotifyForLaborContractExpire] error", map[string]interface{}{"err": err})
		}
	}

	if config.Config.SchedulerJob.IsSendStaffProbationExpireNotify {
		//每天的3点查询是否有试用期即将到期的员工
		_, err = c.AddFunc("0 0 3 * * ?", command.GetCanNotifyStaffForProbationExpire)
		//_, err = c.AddFunc("0 */1 * * * ?", command.GetCanNotifyStaffForProbationExpire)
		if err != nil {
			log.ErrorFields("cron job [command.GetCanNotifyStaffForProbationExpire] error", map[string]interface{}{"err": err})
		}
		//每分钟检查下是否有需要通知的员工试用期到期消息
		_, err = c.AddFunc("0 */1 * * * ?", command.SendNotifyForStaffProbationExpire)
		//_, err = c.AddFunc("*/3 * * * * ?", command.SendNotifyForStaffProbationExpire)
		if err != nil {
			log.ErrorFields("cron job [command.SendNotifyForStaffProbationExpire] error", map[string]interface{}{"err": err})
		}
	}

	if config.Config.SchedulerJob.IsCheckTicketDataPermission {
		// 每分钟检查设置票务数据权限
		_, err = c.AddFunc("0 */1 * * * ?", command.CheckTicketDataPermission)
		if err != nil {
			log.ErrorFields("cron job [command.CheckTicketDataPermission] error", map[string]interface{}{"err": err})
		}
	}

	if config.Config.SchedulerJob.IsProcessAlarm {
		// 每天1点检查计算 事故超时告警
		_, err = c.AddFunc("0 0 1 * * ?", command.TrafficAccidentTimeoutAlarm)
		if err != nil {
			log.ErrorFields("cron job [command.TrafficAccidentTimeoutAlarm] error", map[string]interface{}{"err": err})
		}

		// 每天1:30点检查计算 流程超时告警
		_, err = c.AddFunc("0 30 1 * * ?", command.ProcessTimeoutAlarm)
		if err != nil {
			log.ErrorFields("cron job [command.ProcessTimeoutAlarm] error", map[string]interface{}{"err": err})
		}

		// 每天每天9:00发送预警告警消息 发送前检查是否处理完
		_, err = c.AddFunc("0 5 9 * * ?", command.ProcessTimeoutAlarmNotify)
		if err != nil {
			log.ErrorFields("cron job [command.ProcessTimeoutAlarmNotify] error", map[string]interface{}{"err": err})
		}
		_, err = c.AddFunc("0 0 9 * * ?", command.TrafficAccidentTimeoutAlarmNotify)
		if err != nil {
			log.ErrorFields("cron job [command.TrafficAccidentTimeoutAlarmNotify] error", map[string]interface{}{"err": err})
		}
	}

	if config.Config.SchedulerJob.IsCalcStaffArchiveReport {
		// 每天00:05执行一次人员报表的统计
		_, err = c.AddFunc("0 5 0 1,5,10,15,20,25,30 * ?", command.CalcStaffArchiveReport)
		if err != nil {
			log.ErrorFields("cron job [command.CalcStaffArchiveReport] error", map[string]interface{}{"err": err})
		}
	}

	if config.Config.SchedulerJob.IsCalcStaffAnnualLeave {
		// 每天1:00执行一次员工年假计算
		_, err = c.AddFunc("0 0 1 * * ?", command.CalcStaffAnnualLeaveInfo)
		if err != nil {
			log.ErrorFields("cron job [command.CalcStaffAnnualLeaveInfo] error", map[string]interface{}{"err": err})
		}
	}

	if config.Config.SchedulerJob.IsTakeOetLineHasDriverRecord {
		// 每天1:00执行一次同步主数据司机车辆归属关系
		_, err = c.AddFunc("0 0 1 * * ?", command.LineHasDriverRecordByDay)
		if err != nil {
			log.ErrorFields("cron job [command.LineHasDriverRecordByDay] error", map[string]interface{}{"err": err})
		}
	}

	if config.Config.SchedulerJob.IsTakeTaizhouThirdIcCardTradeData {
		// 每天3:00执行一次同步IC卡交易数据
		_, err = c.AddFunc("0 0 3 * * ?", command.TakeThirdIcCardTradeData)
		if err != nil {
			log.ErrorFields("cron job [command.TakeThirdIcCardTradeData] error", map[string]interface{}{"err": err})
		}
	}

	// 每天00:01执行一次检查是否有车辆调动的任务
	_, err = c.AddFunc("0 1 0 * * ?", command.CheckVehicleMigrationJob)
	if err != nil {
		log.ErrorFields("cron job [command.CheckVehicleMigrationJob] error", map[string]interface{}{"err": err})
	}

	// 每天00:02执行一次检查是否有司机调动的任务
	_, err = c.AddFunc("0 2 0 * * ?", command.CheckDriverMigrationJob)
	if err != nil {
		log.ErrorFields("cron job [command.CheckDriverMigrationJob] error", map[string]interface{}{"err": err})
	}

	_, err = c.AddFunc("0 0 9,15 * * ?", command.SendVehicleMigrationDingTalkNotify)
	if err != nil {
		log.ErrorFields("cron job [command.SendVehicleMigrationDingTalkNotify] error", map[string]interface{}{"err": err})
	}

	// 每天00:10执行一次线路同步
	_, err = c.AddFunc("0 10 0 * * ?", command.SyncOetLine)
	if err != nil {
		log.ErrorFields("cron job [command.SyncOetLine] error", map[string]interface{}{"err": err})
	}

	// 每月28号1:00执行一次线路人员营收报表数据
	_, err = c.AddFunc("0 0 1 28 * ?", command.CalcCorporationLineIncome)
	if err != nil {
		log.ErrorFields("cron job [command.CalcCorporationLineIncome] error", map[string]interface{}{"err": err})
	}

	c.Start()
	defer c.Stop()
	select {}
}

func StartScheduler() {
	//同步主数据的人员到ERP档案
	if config.Config.SyncMasterStaff.Enable {
		go command.SyncMasterStaffToErpArchive()
		//go command.FixHistoryQuitStaff()
	}
	if config.Config.DingTalkBpm.Enable {
		go command.SyncDingTalkApplyProcess()
	}

	go command.TakeThirdIcCardTradeData()
}
