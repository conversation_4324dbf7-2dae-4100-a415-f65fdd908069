package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/database"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/rpc"
	"context"
	"fmt"
	"strings"
	"time"
)

func AutoCaclVehicleLineRoadCalc() {
	db, err := database.InitIcMysqlConnect()
	if err != nil {
		log.ErrorFields("InitIcMysqlConnect  error", map[string]interface{}{"error": err.Error()})
		return
	}
	defer func() {
		gepDb, _ := db.DB()
		_ = gepDb.Close()
	}()
	// 1线路数据
	oetLines := rpc.GetLinesWithTopCorporationId(context.Background(), config.Config.TopCorporationId, 1)
	posLineNoMap := make(map[string]model.LinRoadInfo)
	if oetLines != nil {
		for _, oetLine := range oetLines {
			if oetLine.PosLineNo == "" {
				continue
			}
			thirdLines := strings.Split(oetLine.PosLineNo, ",")
			for j := range thirdLines {
				posLineNoMap[thirdLines[j]] = model.LinRoadInfo{LineId: oetLine.Id, LineName: oetLine.Name}
			}
		}
	}

	now := time.Now()
	txnDate := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, time.Local).Format("20060102")
	err = service.VehicleLineRoadReportCalc(context.Background(), db, txnDate, config.Config.TopCorporationId, posLineNoMap)
	if err != nil {
		log.ErrorFields("VehicleLineRoadReportCalc  error", map[string]interface{}{"error": err.Error()})
	}
	// 获取车辆信息车辆数据
	vehicles, count := rpc.GetVehiclesWithTopCorporationId(context.Background(), config.Config.TopCorporationId)
	fmt.Println("=======vehicles- count=======", count)
	if vehicles == nil || len(vehicles) == 0 {
		fmt.Println("未获取到车辆信息")
		return
	}
	err = service.VehicleLineRoadReportCalcReverse(context.Background(), db, txnDate, config.Config.TopCorporationId, vehicles, posLineNoMap)
	if err != nil {
		log.ErrorFields("VehicleLineRoadReportCalc  error", map[string]interface{}{"error": err.Error()})
	}

}
