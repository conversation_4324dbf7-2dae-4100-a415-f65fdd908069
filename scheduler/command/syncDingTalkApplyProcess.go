package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/handler/thirdParty"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	processModel "app/org/scs/erpv2/api/model/process"
	"app/org/scs/erpv2/api/service/process/dingTalkBpm"
	"app/org/scs/erpv2/api/util"
	"time"
)

func SyncDingTalkApplyProcess() {

	processItems := (&processModel.DingTalkApplyProcess{}).NotApproved()
	if len(processItems) == 0 {
		log.ErrorFields("DingTalkApplyProcess NotApproved not found", nil)
		return
	}

	if len(processItems) == 0 {
		log.ErrorFields("DingTalkApplyProcess NotApproved not found", nil)
		return
	}

	for i := 0; i < len(processItems); i++ {
		var processItem = processItems[i]
		if processItem.ProcessInstanceId == "" {
			continue
		}

		switch processItem.TemplateFormId {
		case config.StaffQuitApplyFormTemplate:
		case config.MaterialRequisitionApplyFormTemplate:
			MaterialRequisitionApply(processItem)
		}
	}
}

func MaterialRequisitionApply(processItem processModel.DingTalkApplyProcess) {

	var err error
	tx := model.DB().Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	dingProcessInfo, err := dingTalkBpm.GetProcessInstance(processItem.ProcessInstanceId, "")
	if err != nil {
		log.ErrorFields("dingTalkBpm.GetUserInfo error", map[string]interface{}{"err": err})
		return
	}

	if thirdParty.IsApproveSuccess(dingProcessInfo.Status, dingProcessInfo.Result) {
		at := model.LocalTime(time.Now())
		processItem.DoneAt = &at
		processItem.Status = util.ProcessStatusForDone
		processItem.Result = util.ProcessResultForPass

		//将审批状态更新到流程数据库
		err = processItem.UpdateStatus(tx)
		if err != nil {
			log.ErrorFields("BpmInstanceChange processInstance.UpdateStatus error", map[string]interface{}{"err": err})
			return
		}
		err = thirdParty.ApproveSuccessOutStock(tx, processItem)
		if err != nil {
			log.ErrorFields("thirdParty.ApproveSuccessOutStock error ", map[string]interface{}{"dingProcessInfo": dingProcessInfo})
		}
	} else {
		log.ErrorFields("dingTalkBpm.GetProcessInstance nonterminal state", map[string]interface{}{"dingProcessInfo": dingProcessInfo})
		return
	}
}
