package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	protoStaff "app/org/scs/erpv2/api/proto/rpc/oetstaff"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"strconv"
	"sync"
	"time"
)

type CorporationItem struct {
	Id            int64
	Name          string
	Type          int64
	ReportAt      time.Time
	Total         int64
	GroupTotal    map[int64]int64 //map[类型id]人数   类型ID:1管理  2驾驶员  3乘务员  4修理工  5辅助工  6劳务派遣
	GroupAddTotal map[int64]int64 //map[类型id]人数   类型ID:1管理  2驾驶员  3乘务员  4修理工  5辅助工  6劳务派遣
	GroupSubTotal map[int64]int64 //map[类型id]人数   类型ID:1管理  2驾驶员  3乘务员  4修理工  5辅助工  6劳务派遣
}

//类型ID:1管理  2驾驶员  3乘务员  4修理工  5辅助工  6劳务派遣
const (
	staffType_1 = 1
	staffType_2 = 2
	staffType_3 = 3
	staffType_4 = 4
	staffType_5 = 5
	staffType_6 = 6
)

func CalcStaffArchiveReport() {
	if config.Global.IsCalculatingStaffArchiveReportData {
		return
	}

	var corporations []CorporationItem
	GetAllCorporationId(config.Config.StaffArchiveReportCorpId, &corporations)

	year, month, day := time.Now().AddDate(0, 0, -1).Date()
	//year, month, day := date.Date()
	reportAt := time.Date(year, month, day, 0, 0, 0, 0, time.Local)
	config.Global.IsCalculatingStaffArchiveReportData = true
	var wg sync.WaitGroup
	for day := 0; day < 32; day++ {
		wg.Add(1)
		go DayStaffArchiveReport(&wg, corporations, reportAt.AddDate(0, 0, -day))
	}
	wg.Wait()
	config.Global.IsCalculatingStaffArchiveReportData = false
}

func DayStaffArchiveReport(wg *sync.WaitGroup, corporations []CorporationItem, reportAt time.Time) {
	//获取所有部门和车队
	var departments = make(map[int64]CorporationItem)
	for _, corporation := range corporations {
		corporation.ReportAt = reportAt
		corporation.GroupTotal = map[int64]int64{1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0}
		corporation.GroupAddTotal = map[int64]int64{1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0}
		corporation.GroupSubTotal = map[int64]int64{1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0}
		departments[corporation.Id] = corporation
	}

	//获取所有人员
	staffs := rpc.GetStaffsWithCorporationId(context.Background(), config.Config.StaffArchiveReportCorpId, "")
	for _, staff := range staffs {
		if _, ok := departments[staff.CorporationId]; !ok {
			log.ErrorFields("CalcStaffArchiveReport staff corporation not department!", map[string]interface{}{"staffId": staff.Id})
			continue
		}
		department := departments[staff.CorporationId]
		//离职
		if staff.WorkingState == util.JobStatusQuit {
			//查询当前用户最近的离职信息
			quitRecord := (&hrModel.StaffQuitRecord{}).GetLatestByStaffId(staff.Id)
			if quitRecord.RelieveContractAt != nil && time.Time(*quitRecord.RelieveContractAt).Format("20060102") == department.ReportAt.Format("20060102") {
				staffType := getStaffType(staff)
				department.GroupSubTotal[staffType] += 1
			}
			departments[staff.CorporationId] = department
			continue
		}
		//退休
		if staff.WorkingState == util.JobStatusRetire {
			retireAt := time.Unix(staff.RetireDate, 0)
			if retireAt.Format("20060102") == department.ReportAt.Format("20060102") {
				staffType := getStaffType(staff)
				department.GroupSubTotal[staffType] += 1
			}
			departments[staff.CorporationId] = department
			continue
		}

		department.Total += 1
		staffType := getStaffType(staff)
		department.GroupTotal[staffType] += 1
		if staff.WorkingState == util.JobStatusWorking || staff.WorkingState == util.JobStatusProbation {
			joinAt := time.Unix(staff.RegisterTime, 0)
			if joinAt.Format("20060102") == department.ReportAt.Format("20060102") {
				department.GroupAddTotal[staffType] += 1
			}
		}

		if staff.WorkingState == util.JobStatusRetireWork {
			//查询最近的退休返聘记录
			retireWork := (&hrModel.StaffRetireWorkRecord{}).LatestRecord(staff.Id)
			if time.Time(retireWork.RetireWorkAt).Format("20060102") == department.ReportAt.Format("20060102") {
				department.GroupAddTotal[staffType] += 1
			}
		}
		departments[staff.CorporationId] = department
	}

	for corporationId := range departments {
		groupTotal, _ := json.Marshal(departments[corporationId].GroupTotal)
		groupSubTotal, _ := json.Marshal(departments[corporationId].GroupSubTotal)
		groupAddTotal, _ := json.Marshal(departments[corporationId].GroupAddTotal)
		var staffArchiveDayReport = hrModel.StaffArchiveDayReport{
			Total:         departments[corporationId].Total,
			ReportAt:      model.LocalTime(departments[corporationId].ReportAt),
			GroupTotal:    groupTotal,
			GroupAddTotal: groupAddTotal,
			GroupSubTotal: groupSubTotal,
		}
		corporation := rpc.GetCorporationDetailById(context.Background(), corporationId)
		if corporation != nil {
			staffArchiveDayReport.GroupId = corporation.GroupId
			staffArchiveDayReport.CompanyId = corporation.CompanyId
			staffArchiveDayReport.BranchId = corporation.BranchId
			staffArchiveDayReport.DepartmentId = corporation.DepartmentId
			staffArchiveDayReport.FleetId = corporation.FleetId
		}
		report := staffArchiveDayReport.FirstRecord(reportAt)
		var err error
		var scene string
		if report.Id > 0 {
			staffArchiveDayReport.Id = report.Id
			err = staffArchiveDayReport.Update()
			scene = "UPDATE"
		} else {
			err = staffArchiveDayReport.Create()
			scene = "CREATE"
		}

		if err != nil {
			log.ErrorFields("staffArchiveDayReport ["+scene+"] error", map[string]interface{}{"err": err, "data": staffArchiveDayReport})
		}
		log.ErrorFields("staffArchiveDayReport ["+scene+"] success", map[string]interface{}{"reportAt": reportAt, "Id": staffArchiveDayReport.Id})
	}

	wg.Done()
}

func GetAllCorporationId(corporationId int64, items *[]CorporationItem) {
	corporations := rpc.CorporationList(context.Background(), corporationId)
	if len(corporations) > 0 {
		for i := range corporations {
			*items = append(*items, CorporationItem{Id: corporations[i].Id, Name: corporations[i].Name, Type: corporations[i].Type})
			GetAllCorporationId(corporations[i].Id, items)
		}
	}
}

func getStaffType(staff *protoStaff.OetStaffItem) int64 {
	if staff.WaysToEnter == strconv.FormatInt(util.JoinCompanyWay_13, 10) {
		return staffType_6
	}

	if _, ok := util.MasterToErp[staff.Occupation]; !ok {
		return staffType_5
	}

	workPostType := util.MasterToErp[staff.Occupation]
	//管理
	if workPostType == util.WorkPostType_1 || workPostType == util.WorkPostType_2 {
		return staffType_1
	}
	//驾驶员
	if workPostType == util.WorkPostType_3 {
		return staffType_2
	}
	//乘务员
	if workPostType == util.WorkPostType_4 {
		return staffType_3
	}
	//修理工
	if workPostType == util.WorkPostType_9 {
		return staffType_4
	}

	return staffType_5
}
