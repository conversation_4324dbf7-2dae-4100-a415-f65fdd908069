package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	accidentSettingModel "app/org/scs/erpv2/api/model/setting/accident"
	"app/org/scs/erpv2/api/service/message"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"math"
	"time"
)

func TrafficAccidentTimeoutAlarm() {
	//查询事故超时阈值设置
	var settingMap = make(map[int64]accidentSettingModel.AccidentTimeoutSetting)
	settings := (&accidentSettingModel.AccidentTimeoutSetting{}).GetAll(config.Config.TopCorporationId)

	if len(settings) == 0 {
		return
	}

	for i := range settings {
		settingMap[settings[i].AccidentCate] = settings[i]
	}

	//查询所有未结案的事故
	accidents := (&safetyModel.TrafficAccident{}).GetAllDontClosed(config.Config.TopCorporationId)

	for i := range accidents {
		if accidents[i].IsPushWarningNotify == util.StatusForFalse {
			continue
		}

		//计算事故的总金额  事故总借款-事故总退款
		//总借款
		totalLendMoney := (&safetyModel.TrafficAccidentLendMoneyRecord{}).GetTotalMoneyByAccidentId(accidents[i].Id)
		//总退款
		totalDrawbackMoney := (&safetyModel.TrafficAccidentDrawbackMoneyRecord{}).DoneTotalMoneyByAccidentId(accidents[i].Id)
		var totalMoney int64
		if totalLendMoney > 0 {
			totalMoney = totalLendMoney - totalDrawbackMoney
		}

		//判断事故类别有没有设置过不同金额区间的阈值  若没有设置  则已缺省阈值为准
		var warningValue, alarmValue int64
		var isExistSetting bool
		var usedSettingDetail accidentSettingModel.AccidentTimeoutSettingDetail
		if _, ok := settingMap[accidents[i].PeopleHurtCate]; ok {
			settingDetails := (&accidentSettingModel.AccidentTimeoutSettingDetail{}).GetBy(settingMap[accidents[i].PeopleHurtCate].Id)
			if len(settingDetails) > 0 {
				for j := range settingDetails {
					if totalMoney > settingDetails[j].MinMoney && totalMoney <= settingDetails[j].MaxMoney {
						isExistSetting = true
						warningValue = settingDetails[j].WarningValue
						alarmValue = settingDetails[j].AlarmValue
						usedSettingDetail = settingDetails[j]
						break
					}
				}
			}
		}

		if !isExistSetting {
			warningValue = settings[0].WarningValue
			alarmValue = settings[0].AlarmValue
		}

		if warningValue == 0 && alarmValue == 0 {
			continue
		}

		//计算事故滞留天数
		diffSecond := time.Now().Unix() - time.Time(accidents[i].CreatedAt).Unix()
		diffDay := int64(math.Ceil(float64(diffSecond) / float64(60*60*24)))
		if diffDay < warningValue {
			continue
		}

		var alarmType = util.AlarmTypeForWarning
		if diffDay >= alarmValue {
			alarmType = util.AlarmTypeForAlarm
		}

		//查询事故的报警记录
		alarmRecord := (&accidentSettingModel.AccidentTimeoutAlarmRecord{}).FindByAccidentId(accidents[i].Id)
		alarmRecord.AccidentCate = accidents[i].PeopleHurtCate
		alarmRecord.DriverId = accidents[i].DriverId
		alarmRecord.DriverName = accidents[i].DriverName
		alarmRecord.VehicleId = accidents[i].VehicleId
		alarmRecord.License = accidents[i].License
		alarmRecord.AccidentTotalMoney = totalMoney
		alarmRecord.WarningValue = warningValue
		alarmRecord.AlarmValue = alarmValue
		alarmRecord.CurrentValue = diffDay
		if isExistSetting {
			alarmRecord.AccidentAlarmCategorySettingId = usedSettingDetail.AccidentAlarmCategorySettingId
			category := (&accidentSettingModel.AccidentAlarmCategorySetting{}).FindBy(alarmRecord.AccidentAlarmCategorySettingId)
			alarmRecord.AccidentAlarmCategoryTitle = category.Title
		}
		alarmRecord.AlarmType = alarmType
		alarmRecord.FinishStatus = util.AlarmFinishStatusForDoing

		if alarmRecord.Id == 0 {
			alarmRecord.TopCorporationId = accidents[i].GroupId
			alarmRecord.TrafficAccidentId = accidents[i].Id
			alarmRecord.TrafficAccidentCode = accidents[i].Code
			alarmRecord.AccidentCreatedAt = accidents[i].CreatedAt
			alarmRecord.AccidentOpUserId = accidents[i].OpUserId
			alarmRecord.AccidentOpUserName = accidents[i].OpUserName

			err := alarmRecord.Create()
			if err != nil {
				log.ErrorFields("TrafficAccidentTimeoutAlarm alarmRecord.Create error", map[string]interface{}{"err": err})
				continue
			}
		} else {
			err := alarmRecord.Update()
			if err != nil {
				log.ErrorFields("TrafficAccidentTimeoutAlarm alarmRecord.Update error", map[string]interface{}{"err": err})
				continue
			}
		}
	}
}

func TrafficAccidentTimeoutAlarmNotify() {
	//查询所有正在进行的告警
	alarms := (&accidentSettingModel.AccidentTimeoutAlarmRecord{}).GetByFinishStatus(util.AlarmFinishStatusForDoing)

	for i := range alarms {
		user := rpc.GetUserInfoById(context.Background(), alarms[i].AccidentOpUserId)
		if user == nil {
			continue
		}
		msg := message.BuildAccidentTimeoutPushMsg(alarms[i])
		message.PushWorkNotifySendDingTalk(user.Phone, msg)

		//var accident safetyModel.TrafficAccident
		//_ = accident.FindBy(alarms[i].TrafficAccidentId)

		//go message.NewOfficialAccountMsg(
		//	fmt.Sprintf("事故%s", mapAlarm[alarms[i].AlarmType]),
		//	[]string{fmt.Sprintf(`事故编号:%s`, alarms[i].TrafficAccidentCode), fmt.Sprintf(`事故创建人：%s`, alarms[i].AccidentOpUserName)},
		//	fmt.Sprintf("【%s】/【%s】的事故已超过【%d】天未结案，请及时催办", accident.DriverName, util.TrafficAccidentCateMap[accident.Cate], alarms[i].CurrentValue),
		//	"",
		//	user.Phone,
		//).Send()
	}
}
