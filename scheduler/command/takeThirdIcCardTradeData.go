package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/database"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	"app/org/scs/erpv2/api/model/ticket"
	"app/org/scs/erpv2/api/model/ticket/mixReport"
	protoCorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"fmt"
	"gorm.io/gorm"
	"strings"
	"time"
)

//获取第三方IC卡交易数据（通过数据库直连的方式），和票务数据进行融合，生成日期、分公司、车队、线路、车辆为唯一键的结算结果表
//结果表按年存储
//每次计算当前日期前30天数据

type taizhouThirdIcCardTradeData struct {
	DB               *gorm.DB
	MCHNT_ID         string
	TopCorporationId int64
	ReportAt         time.Time
	//第三方车辆
	ThirdVehicleMap map[string]thirdVehicleInfo
	//第三方线路和平台线路的映射关系
	ThirdLineToOetLineMap map[string]oetLineInfo
	//平台车牌和ID映射关系
	OetVehicleMap map[string]int64
	//平台机构信息
	CorporationMap map[int64]*protoCorporation.GetCorporationDetailByIdResponse
}

type thirdVehicleInfo struct {
	BusId       string `json:"BUS_ID" gorm:"column:BUS_ID"`             //车辆ID
	BusNumPlate string `json:"BUS_NUMPLATE" gorm:"column:BUS_NUMPLATE"` //车牌号
	MchntId     string `json:"MCHNT_ID" gorm:"column:MCHNT_ID"`         //商户ID
	BranchId    string `json:"BRANCH_ID" gorm:"column:BRANCH_ID"`       //分公司ID
	ArrayId     string `json:"ARRAY_ID" gorm:"column:ARRAY_ID"`         //车队ID
	LineId      string `json:"LINE_ID" gorm:"column:LINE_ID"`           //线路ID
}

type oetLineInfo struct {
	LineId         int64
	LineName       string
	CorporationIds []int64
}

func (t *taizhouThirdIcCardTradeData) initDb() error {
	db, err := database.InitIcMysqlConnect()
	if err != nil {
		t.DB = nil
		return err
	}
	t.DB = db
	return nil
}

func (t *taizhouThirdIcCardTradeData) takeThirdVehicle() {
	var items []thirdVehicleInfo
	t.DB.Table("bus_info").Select("BUS_ID", "BUS_NUMPLATE", "MCHNT_ID", "BRANCH_ID", "ARRAY_ID", "LINE_ID").Where("MCHNT_ID = ?", t.MCHNT_ID).Scan(&items)

	var vehicleMap = make(map[string]thirdVehicleInfo)
	for i := range items {
		vehicleMap[items[i].BusId] = items[i]
	}

	t.ThirdVehicleMap = vehicleMap
}

func (t *taizhouThirdIcCardTradeData) takeOetVehicle() {
	oetVehicles, _ := rpc.GetVehiclesWithTopCorporationId(context.Background(), t.TopCorporationId)

	var vehicleMap = make(map[string]int64)
	for i := range oetVehicles {
		vehicleMap[oetVehicles[i].License] = oetVehicles[i].Id
	}

	t.OetVehicleMap = vehicleMap
}

func (t *taizhouThirdIcCardTradeData) takeOetLine() {
	var thirdLine2OetLine = make(map[string]oetLineInfo)
	oetLines := rpc.GetLinesWithTopCorporationId(context.Background(), t.TopCorporationId, 1)
	for i := range oetLines {
		if oetLines[i].PosLineNo != "" {
			thirdLines := strings.Split(oetLines[i].PosLineNo, ",")
			for j := range thirdLines {
				thirdLine2OetLine[thirdLines[j]] = oetLineInfo{
					LineId:         oetLines[i].Id,
					LineName:       oetLines[i].Name,
					CorporationIds: oetLines[i].SubCorporationIds,
				}
			}
		}
	}
	t.ThirdLineToOetLineMap = thirdLine2OetLine
}

func (t *taizhouThirdIcCardTradeData) GetCorporation(corporationId int64) *protoCorporation.GetCorporationDetailByIdResponse {
	if corp, ok := t.CorporationMap[corporationId]; ok {
		return corp
	} else {
		corporation := rpc.GetCorporationDetailById(context.Background(), corporationId)
		t.CorporationMap[corporationId] = corporation
		return corporation
	}
}

type thirdTableColumn struct {
	SettData    string `json:"SETT_DATE" gorm:"column:SETT_DATE"`         //结算日期
	BusId       string `json:"BUS_ID" gorm:"column:BUS_ID"`               //车辆编号
	TxnDate     string `json:"TXN_DATE" gorm:"column:TXN_DATE"`           //交易日期
	TxnMchntId  string `json:"TXN_MCHNT_ID" gorm:"column:TXN_MCHNT_ID"`   //商户号
	TxnBranchId string `json:"TXN_BRANCH_ID" gorm:"column:TXN_BRANCH_ID"` //分公司编号
	TxnLineId   string `json:"TXN_LINE_ID" gorm:"column:TXN_LINE_ID"`     //线路编号
	TxnArrayId  string `json:"TXN_ARRAY_ID" gorm:"column:TXN_ARRAY_ID"`   //车队编号
	TotTxnAmt   int64  `json:"TOT_TXN_AMT" gorm:"column:TOT_TXN_AMT"`     //结算金额
}

// ProcessThirdMoney 拉取第三方刷卡和扫码数据
func (t *taizhouThirdIcCardTradeData) ProcessThirdMoney(from string) {
	//获取第三方刷卡数据
	var thirdCardRecords []thirdTableColumn
	t.DB.Table(fmt.Sprintf("batch_%s_fh", from)).Select("BUS_ID", "TXN_DATE", "TXN_BRANCH_ID", "TXN_LINE_ID", "TXN_ARRAY_ID", "SUM(TOT_TXN_AMT) AS TOT_TXN_AMT").
		Where("SETT_DATE=?", t.ReportAt.Format("20060102")).
		Where("TXN_MCHNT_ID = ?", t.MCHNT_ID).
		Where("BUS_ID != ?", 'F').Where("TXN_LINE_ID != ?", 'F').
		Group("BUS_ID,TXN_DATE,TXN_BRANCH_ID,TXN_LINE_ID,TXN_ARRAY_ID").
		Scan(&thirdCardRecords)

	for i := range thirdCardRecords {
		var record mixReport.TicketMoneyMixReportRecord
		//车辆
		if thirdVehicle, ok := t.ThirdVehicleMap[thirdCardRecords[i].BusId]; ok {
			if vehicleId, ok := t.OetVehicleMap[thirdVehicle.BusNumPlate]; ok {
				record.VehicleId = vehicleId
				record.License = thirdVehicle.BusNumPlate
			}
		}
		//线路
		var lineCorporationIds []int64
		if oetLine, ok := t.ThirdLineToOetLineMap[thirdCardRecords[i].TxnLineId]; ok {
			record.LineId = oetLine.LineId
			record.LineName = oetLine.LineName
			lineCorporationIds = oetLine.CorporationIds
		}

		//分公司
		if branchId, ok := util.ThirdBranchToOetCorporationId[thirdCardRecords[i].TxnBranchId]; ok {
			record.BranchId = branchId
			if record.BranchId > 0 {
				corporation := t.GetCorporation(record.BranchId)
				record.BranchName = corporation.BranchName
				record.CompanyId = corporation.CompanyId
				record.CompanyName = corporation.CompanyName
				record.GroupId = corporation.GroupId
				record.GroupName = corporation.GroupName
			}
		}
		//车队
		if fleetId, ok := util.ThirdBranchToOetCorporationId[thirdCardRecords[i].TxnArrayId]; ok {
			record.FleetId = fleetId
			if record.FleetId > 0 {
				record.FleetName = t.GetCorporation(record.FleetId).FleetName
			}
		}

		//如果车队ID没有 则根据线路所属的机构（会有多个机构）以及分公司来确定车队（线路所属的多个机构必须在不同分公司下）
		if record.FleetId == 0 {
			if len(lineCorporationIds) == 1 {
				var corporation *protoCorporation.GetCorporationDetailByIdResponse
				if _, ok := t.CorporationMap[lineCorporationIds[0]]; ok {
					corporation = t.CorporationMap[lineCorporationIds[0]]
				} else {
					corporation = rpc.GetCorporationDetailById(context.Background(), lineCorporationIds[0])
					t.CorporationMap[lineCorporationIds[0]] = corporation
				}
				if corporation != nil {
					record.FleetId = corporation.FleetId
					record.FleetName = corporation.FleetName
					if record.BranchId == 0 {
						record.BranchId = corporation.BranchId
						record.BranchName = corporation.BranchName
					}
				}
			}

			if len(lineCorporationIds) > 1 {
				var corporations []*protoCorporation.GetCorporationDetailByIdResponse
				for j := range lineCorporationIds {
					if _, ok := t.CorporationMap[lineCorporationIds[j]]; ok {
						corporations = append(corporations, t.CorporationMap[lineCorporationIds[j]])
					} else {
						corporation := rpc.GetCorporationDetailById(context.Background(), lineCorporationIds[j])
						t.CorporationMap[lineCorporationIds[j]] = corporation
						corporations = append(corporations, corporation)
					}
				}

				if len(corporations) > 0 {
					for _, corporation := range corporations {
						//当一个线路归属于多个车队时，每个车队所在的分公司是不同的，通过加入分公司条件可以确定线路具体归属的车队
						if corporation != nil && corporation.BranchId == record.BranchId {
							record.FleetId = corporation.FleetId
							record.FleetName = corporation.FleetName
							break
						}
					}

					if record.FleetId == 0 {
						record.FleetId = corporations[0].FleetId
						record.FleetName = corporations[0].FleetName
						if record.BranchId == 0 {
							record.BranchId = corporations[0].BranchId
							record.BranchName = corporations[0].BranchName
						}
					}
				}
			}
		}

		var column = "cardmoney"
		if from == "card" {
			record.CardMoney = thirdCardRecords[i].TotTxnAmt
		}
		if from == "code" {
			record.CodeMoney = thirdCardRecords[i].TotTxnAmt
			column = "codemoney"
		}

		//结算日期
		record.SettleAt = model.LocalTime(t.ReportAt)

		//交易日期
		reportAt, _ := time.ParseInLocation("20060102", thirdCardRecords[i].TxnDate, time.Local)
		record.ReportAt = model.LocalTime(reportAt)

		if record.LineId == 0 && record.VehicleId == 0 {
			log.ErrorFields("taizhouThirdIcCardTradeData ProcessThirdMoney LineId/VehicleId is 0", map[string]interface{}{"thirdCardRecords": thirdCardRecords[i]})
			continue
		}
		err := record.Create()
		//log.ErrorFields("taizhouThirdIcCardTradeData ProcessThirdMoney create", map[string]interface{}{"err": err, "thirdCardRecords": thirdCardRecords[i], "record": record})
		if err != nil {
			if model.IsViolatesUniqueConstraint(err) {
				err = record.UpdateColumn(column, thirdCardRecords[i].TotTxnAmt)
				if err != nil {
					fmt.Printf("taizhouThirdIcCardTradeData ProcessThirdMoney[%s] UpdateColumn error:%v \n", from, err)
				}
			} else {
				fmt.Printf("taizhouThirdIcCardTradeData ProcessThirdMoney[%s] Create error:%v \n", from, err)
			}
		}
	}
}

type ticketMoneyRecord struct {
	mixReport.TicketMoneyMixReportRecord
	SettleAtStr string `json:"SettleAtStr" gorm:"column:settleatstr"`
	ReportAtStr string `json:"ReportAtStr" gorm:"column:reportatstr"`
}

// ProcessTicketMoney 获取平台票务数据
func (t *taizhouThirdIcCardTradeData) ProcessTicketMoney() {
	var records []ticketMoneyRecord
	model.DB().Model(&ticket.TicketCountMoneys{}).Select("to_char(ReportAt, 'yyyy-MM-dd') as SettleAtStr", "to_char(IncomeAt, 'yyyy-MM-dd') as ReportAtStr", "BranchId", "FleetId", "LineId", "Line AS LineName", "License", "sum(TotalAmount) AS TicketMoney").
		Where("ReportAt >= ? AND ReportAt < ?", t.ReportAt.Format(model.DateFormat), t.ReportAt.AddDate(0, 0, 1).Format(model.DateFormat)).
		Group("to_char(ReportAt, 'yyyy-MM-dd'),to_char(IncomeAt, 'yyyy-MM-dd'),BranchId,FleetId,LineId,Line,License").Scan(&records)

	for i := range records {
		record := records[i].TicketMoneyMixReportRecord
		settleAt, _ := time.ParseInLocation(model.DateFormat, records[i].SettleAtStr, time.Local)
		record.SettleAt = model.LocalTime(settleAt)

		reportAt, _ := time.ParseInLocation(model.DateFormat, records[i].ReportAtStr, time.Local)
		record.ReportAt = model.LocalTime(reportAt)

		fleetCorporation := t.GetCorporation(record.FleetId)
		if fleetCorporation != nil {
			record.FleetName = fleetCorporation.Item.Name
		}

		branchCorporation := t.GetCorporation(record.BranchId)
		if branchCorporation != nil {
			record.BranchName = branchCorporation.Item.Name
			record.CompanyId = branchCorporation.CompanyId
			record.CompanyName = branchCorporation.CompanyName
			record.GroupId = branchCorporation.GroupId
			record.GroupName = branchCorporation.GroupName
		}

		record.VehicleId = t.OetVehicleMap[record.License]

		err := record.Create()
		//log.ErrorFields("taizhouThirdIcCardTradeData ProcessTicketMoney create", map[string]interface{}{"err": err, "records[i]": records[i], "record": record})
		if err != nil {
			if model.IsViolatesUniqueConstraint(err) {
				err = record.UpdateColumn("ticketmoney", record.TicketMoney)
				if err != nil {
					log.Errorf("taizhouThirdIcCardTradeData ProcessTicketMoney UpdateColumn error:%v", err)
				}
			} else {
				log.Errorf("taizhouThirdIcCardTradeData ProcessTicketMoney Create error:%v", err)
			}
		}
	}
}

func TakeThirdIcCardTradeData() {
	var taizhouThirdIcCard = taizhouThirdIcCardTradeData{
		MCHNT_ID:         "8000",
		TopCorporationId: config.Config.TopCorporationId,
		CorporationMap:   make(map[int64]*protoCorporation.GetCorporationDetailByIdResponse),
	}
	err := taizhouThirdIcCard.initDb()
	if err != nil {
		log.Errorf("init taizhouThirdIcCardTradeData db error:%v", err)
	}

	if taizhouThirdIcCard.DB != nil {
		//获取第三方车辆
		taizhouThirdIcCard.takeThirdVehicle()
	}

	//获取平台车辆
	taizhouThirdIcCard.takeOetVehicle()
	//获取平台线路
	taizhouThirdIcCard.takeOetLine()
	var i = 1
	for {
		if i > 32 {
			break
		}

		reportAt := time.Now().AddDate(0, 0, -i)
		taizhouThirdIcCard.ReportAt = time.Date(reportAt.Year(), reportAt.Month(), reportAt.Day(), 0, 0, 0, 0, time.Local)
		//先删除这天的数据
		err = (&mixReport.TicketMoneyMixReportRecord{}).Delete(taizhouThirdIcCard.ReportAt, taizhouThirdIcCard.ReportAt.AddDate(0, 0, 1))
		if err != nil {
			log.Errorf("taizhouThirdIcCardTradeData Delete error:%+v,reportAt:%v", err, taizhouThirdIcCard.ReportAt)
		}
		if taizhouThirdIcCard.DB != nil {
			taizhouThirdIcCard.ProcessThirdMoney("card")
			taizhouThirdIcCard.ProcessThirdMoney("code")
		}
		taizhouThirdIcCard.ProcessTicketMoney()

		i++
	}
}
