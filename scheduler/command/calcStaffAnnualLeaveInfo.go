package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"fmt"
	"math"
	"math/big"
	"time"
)

// CalcStaffAnnualLeaveInfo 计算员工年休假信息
func CalcStaffAnnualLeaveInfo() {
	//获取机构年休假配置信息
	leaveSetting := (&hrModel.LeaveRuleSetting{}).FirstBy(config.Config.TopCorporationId, util.LeaveTypeForAnnualLeave)
	if leaveSetting.Id == 0 {
		return
	}

	var settingItem hrModel.AnnualLeaveSettingItem
	err := json.Unmarshal(leaveSetting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("CalcStaffAnnualLeaveInfo json.Unmarshal leaveSetting.SettingItem error", map[string]interface{}{"err": err})
		return
	}

	//根据设置的过期日期  将过期日期之前的年休假作废
	nowDate := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local)

	giveAtStr := fmt.Sprintf("%v-%s", time.Now().Year(), settingItem.GiveMonthDay)
	giveDate, err := time.ParseInLocation(model.DateFormat, giveAtStr, time.Local)
	if err != nil {
		log.ErrorFields("CalcStaffAnnualLeaveInfo giveAtStr time.ParseInLocation error", map[string]interface{}{"err": err, "str": giveAtStr})
	}

	if nowDate.Unix() == giveDate.Unix() {
		err = (&hrModel.LeaveManagement{}).SetStatusByBefore(int64(time.Now().Year()), util.LeaveTypeForAnnualLeave)
		if err != nil {
			log.ErrorFields("CalcStaffAnnualLeaveInfo LeaveManagement.SetStatusByBefore error", map[string]interface{}{"err": err})
		}
	}

	//分配新的年休假
	staffs := rpc.GetStaffsWithCorporationId(context.Background(), config.Config.TopCorporationId, "")
	for i := range staffs {
		//已经离职或者退休的人员不再计算年休假
		if staffs[i].WorkingState == util.JobStatusQuit || staffs[i].WorkingState == util.JobStatusRetire || staffs[i].WorkingState == util.JobStatusRetireWork {
			continue
		}

		//入职不满一年的不给年休假
		staffRegisterTime := time.Unix(staffs[i].RegisterTime, 0)
		registerTime := time.Date(staffRegisterTime.Year(), staffRegisterTime.Month(), staffRegisterTime.Day(), 0, 0, 0, 0, time.Local)
		if (time.Now().Sub(registerTime).Hours())/24 < 365 {
			continue
		}

		//工龄起算时间
		var startAtUnix = staffs[i].WorkTime
		workPostType := util.MasterToErp[staffs[i].Occupation]
		//if workPostType == util.WorkPostType_3 {
		//	startAtUnix = staffs[i].RegisterTime
		//}

		if startAtUnix <= 0 {
			continue
		}

		staffStartTime := time.Unix(startAtUnix, 0)
		startTime := time.Date(staffStartTime.Year(), staffStartTime.Month(), staffStartTime.Day(), 0, 0, 0, 0, time.Local)

		//如果今天是年休假发放日
		if nowDate.Unix() == giveDate.Unix() {
			var totalDay int64
			//今年年假还未发放的用户
			if !(&hrModel.LeaveManagement{}).IsExistByStaffIdAndYear(staffs[i].Id, int64(giveDate.Year())) {
				workDays := (time.Now().Sub(startTime).Hours()) / 24
				//工龄满一年且不到10年并且今年年假还未发放的用户发放5天年休假
				//if workDays >= 365 && workDays < 365*10 {
				//	totalDay = settingItem.Year1To10
				//}
				// 工龄 1-9 发 5天
				if workDays >= 365 && workDays < 365*9 {
					totalDay = settingItem.Year1To10
				}
				// 年休假 9-10 发 比例发放年假 在发放日就得发
				if workDays >= 9*365 && workDays < 365*10 {
					afterNineYear := startTime.AddDate(9, 0, 0)
					month := int(afterNineYear.Month())
					fiveDay := 0
					for i := 1; i < month; i++ {
						t := time.Date(afterNineYear.Year(), time.Month(i+1), 1, 0, 0, 0, 0, time.Local)
						t = t.AddDate(0, 0, -1)
						fiveDay += t.Day()
					}
					totalDay = calcYearDay(int64(fiveDay), 365, settingItem.Year1To10) + calcYearDay(int64(365-fiveDay), 365, settingItem.Year10To20)
				}
				//工龄满10年且不到19年发放10天年休假
				if workDays >= 365*10 && workDays < 365*19 {
					totalDay = settingItem.Year10To20
				}
				// 年休假 10-19 发 比例发放年假 在发放日就得发
				if workDays >= 19*365 && workDays < 365*20 {
					afterNineteenYear := startTime.AddDate(19, 0, 0)
					month := int(afterNineteenYear.Month())
					fiveDay := 0
					for i := 1; i < month; i++ {
						t := time.Date(afterNineteenYear.Year(), time.Month(i+1), 1, 0, 0, 0, 0, time.Local)
						t = t.AddDate(0, 0, -1)
						fiveDay += t.Day()
					}
					totalDay = calcYearDay(int64(fiveDay), 365, settingItem.Year10To20) + calcYearDay(int64(365-fiveDay), 365, settingItem.YearOver20)
				}
				//工龄大于20年发放15天年休假
				if workDays >= 365*20 {
					totalDay = settingItem.YearOver20
				}
			}

			if totalDay > 0 {
				var archive hrModel.StaffArchive
				_ = archive.FindByStaffId(staffs[i].Id)
				var leaveManagement = hrModel.LeaveManagement{
					StaffArchiveId: archive.Id,
					StaffId:        staffs[i].Id,
					StaffName:      staffs[i].Name,
					JobNumber:      staffs[i].StaffId,
					WorkPostType:   workPostType,
					CalcStartTime:  model.LocalTime(startTime),
					Year:           int64(giveDate.Year()),
					TotalDay:       totalDay,
					LeaveType:      util.LeaveTypeForAnnualLeave,
					Status:         util.StatusForTrue,
					GiveDate:       model.LocalTime(nowDate),
				}
				leaveManagement.Corporations.Build(staffs[i].CorporationId)

				err = leaveManagement.Create()
				if err != nil {
					log.ErrorFields("CalcStaffAnnualLeaveInfo leaveManagement.Create error", map[string]interface{}{"err": err})
				}
			}
		} else {
			//今天日期是用户刚满1年、10年、20年的日期 给他发放年假折算后的年假
			to1YearDate := startTime.AddDate(1, 0, 0)
			//to10YearDate := startTime.AddDate(10, 0, 0)
			//to20YearDate := startTime.AddDate(20, 0, 0)

			var nextGiveDate = giveDate
			var year = giveDate.Year() - 1
			//确定下一个发放日
			if giveDate.Unix() < nowDate.Unix() {
				nextGiveDate = giveDate.AddDate(1, 0, 0)
				//确定年休假年度（如果过了当年的发放年度，则发放的是当年的年假；如果没到当年的发放年度，则发放上一年的年假）
				year = giveDate.Year()
			}

			var totalDay int64
			if to1YearDate.Unix() == nowDate.Unix() {
				totalDay = int64(math.Floor(nextGiveDate.Sub(to1YearDate).Hours() / 24 / 365 * 5))
			}
			//if to10YearDate.Unix() == nowDate.Unix() {
			//	totalDay = int64(math.Floor(nextGiveDate.Sub(to10YearDate).Hours()/24/365*5)) + 5
			//}
			//if to20YearDate.Unix() == nowDate.Unix() {
			//	totalDay = int64(math.Floor(nextGiveDate.Sub(to20YearDate).Hours()/24/365*5)) + 10
			//}
			fmt.Printf("姓名：%s,Id:%v,岗位：%s,startTime:%v \n,totalDay:%v,calc:%s", staffs[i].Name, staffs[i].Id, util.ErpWorkPostTypeMap[workPostType], startTime, totalDay, time.Now().Format("2006-01-02"))

			if totalDay > 0 {
				if record := (&hrModel.LeaveManagement{}).GetByStaffIdAndYear(staffs[i].Id, int64(year)); record.Id > 0 {
					//对于满10年 还未到下一个发放日以及满20年还未到下一个发放日的人员  更新年度年假
					record.GiveDate = model.LocalTime(nowDate)
					record.TotalDay = totalDay
					record.WorkPostType = workPostType
					record.CalcStartTime = model.LocalTime(startTime)
					err = record.Update()
					if err != nil {
						log.ErrorFields("CalcStaffAnnualLeaveInfo leaveManagement.Update error", map[string]interface{}{"err": err})
					}
				} else {
					var archive hrModel.StaffArchive
					_ = archive.FindByStaffId(staffs[i].Id)
					var leaveManagement = hrModel.LeaveManagement{
						StaffArchiveId: archive.Id,
						StaffId:        staffs[i].Id,
						StaffName:      staffs[i].Name,
						JobNumber:      staffs[i].StaffId,
						WorkPostType:   workPostType,
						CalcStartTime:  model.LocalTime(startTime),
						Year:           int64(year),
						TotalDay:       totalDay,
						LeaveType:      util.LeaveTypeForAnnualLeave,
						Status:         util.StatusForTrue,
						GiveDate:       model.LocalTime(nowDate),
					}
					leaveManagement.Corporations.Build(staffs[i].CorporationId)
					err = leaveManagement.Create()
					if err != nil {
						log.ErrorFields("CalcStaffAnnualLeaveInfo leaveManagement.Create error", map[string]interface{}{"err": err})
					}
				}
			}
		}
	}
}

func calcYearDay(day int64, totalDay int64, yearDay int64) int64 {
	if totalDay == 0 {
		return 0
	}
	rat := big.NewRat(day, totalDay)
	result := new(big.Rat).Mul(rat, new(big.Rat).SetInt64(yearDay))
	r, _ := result.Float64()
	return int64(r)
}

func SyncCalcStaffAnnualLeaveInfo(now time.Time) {
	//获取机构年休假配置信息
	leaveSetting := (&hrModel.LeaveRuleSetting{}).FirstBy(config.Config.TopCorporationId, util.LeaveTypeForAnnualLeave)
	if leaveSetting.Id == 0 {
		return
	}

	var settingItem hrModel.AnnualLeaveSettingItem
	err := json.Unmarshal(leaveSetting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("CalcStaffAnnualLeaveInfo json.Unmarshal leaveSetting.SettingItem error", map[string]interface{}{"err": err})
		return
	}

	//根据设置的过期日期  将过期日期之前的年休假作废
	nowDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)

	giveAtStr := fmt.Sprintf("%v-%s", now.Year(), settingItem.GiveMonthDay)
	giveDate, err := time.ParseInLocation(model.DateFormat, giveAtStr, time.Local)
	if err != nil {
		log.ErrorFields("CalcStaffAnnualLeaveInfo giveAtStr time.ParseInLocation error", map[string]interface{}{"err": err, "str": giveAtStr})
	}

	if nowDate.Unix() == giveDate.Unix() {
		err = (&hrModel.LeaveManagement{}).SetStatusByBefore(int64(now.Year()), util.LeaveTypeForAnnualLeave)
		if err != nil {
			log.ErrorFields("CalcStaffAnnualLeaveInfo LeaveManagement.SetStatusByBefore error", map[string]interface{}{"err": err})
		}
	}

	//分配新的年休假
	staffs := rpc.GetStaffsWithCorporationId(context.Background(), config.Config.TopCorporationId, "")
	for i := range staffs {
		//已经离职或者退休的人员不再计算年休假
		if staffs[i].WorkingState == util.JobStatusQuit || staffs[i].WorkingState == util.JobStatusRetire || staffs[i].WorkingState == util.JobStatusRetireWork {
			continue
		}

		//入职不满一年的不给年休假
		staffRegisterTime := time.Unix(staffs[i].RegisterTime, 0)
		registerTime := time.Date(staffRegisterTime.Year(), staffRegisterTime.Month(), staffRegisterTime.Day(), 0, 0, 0, 0, time.Local)

		if (now.Sub(registerTime).Hours())/24 < 365 {
			continue
		}

		//工龄起算时间（司机按进公司时间计算  其他按工龄起算时间计算）
		var startAtUnix = staffs[i].WorkTime
		workPostType := util.MasterToErp[staffs[i].Occupation]
		if workPostType == util.WorkPostType_3 {
			startAtUnix = staffs[i].RegisterTime
		}

		//fmt.Printf("姓名：%s,Id:%v,岗位：%s,startUnix:%v \n", staffs[i].Name, staffs[i].Id, util.ErpWorkPostTypeMap[workPostType], startAtUnix)
		if startAtUnix <= 0 {
			continue
		}

		staffStartTime := time.Unix(startAtUnix, 0)
		startTime := time.Date(staffStartTime.Year(), staffStartTime.Month(), staffStartTime.Day(), 0, 0, 0, 0, time.Local)

		//如果今天是年休假发放日
		if nowDate.Unix() == giveDate.Unix() {
			var totalDay int64
			//今年年假还未发放的用户
			if !(&hrModel.LeaveManagement{}).IsExistByStaffIdAndYear(staffs[i].Id, int64(giveDate.Year())) {
				workDays := (now.Sub(startTime).Hours()) / 24
				//工龄满一年且不到10年并且今年年假还未发放的用户发放5年年休假
				if workDays >= 365 && workDays < 365*10 {
					totalDay = settingItem.Year1To10
				}
				//工龄满10年且不到20年发放10年年休假
				if workDays >= 365*10 && workDays < 365*20 {
					totalDay = settingItem.Year10To20
				}
				//工龄大于20年发放15年年休假
				if workDays >= 365*20 {
					totalDay = settingItem.YearOver20
				}
			}

			if totalDay > 0 {
				var archive hrModel.StaffArchive
				_ = archive.FindByStaffId(staffs[i].Id)
				var leaveManagement = hrModel.LeaveManagement{
					StaffArchiveId: archive.Id,
					StaffId:        staffs[i].Id,
					StaffName:      staffs[i].Name,
					JobNumber:      staffs[i].StaffId,
					WorkPostType:   workPostType,
					CalcStartTime:  model.LocalTime(startTime),
					Year:           int64(giveDate.Year()),
					TotalDay:       totalDay,
					LeaveType:      util.LeaveTypeForAnnualLeave,
					Status:         util.StatusForTrue,
					GiveDate:       model.LocalTime(nowDate),
				}
				leaveManagement.Corporations.Build(staffs[i].CorporationId)

				err = leaveManagement.Create()
				if err != nil {
					log.ErrorFields("CalcStaffAnnualLeaveInfo leaveManagement.Create error", map[string]interface{}{"err": err})
				}
			}
		} else {
			//今天日期是用户刚满1年、10年、20年的日期 给他发放年假折算后的年假
			to1YearDate := startTime.AddDate(1, 0, 0)
			to10YearDate := startTime.AddDate(10, 0, 0)
			to20YearDate := startTime.AddDate(20, 0, 0)

			var nextGiveDate = giveDate
			var year = giveDate.Year() - 1
			//确定下一个发放日
			if giveDate.Unix() < nowDate.Unix() {
				nextGiveDate = giveDate.AddDate(1, 0, 0)
				//确定年休假年度（如果过了当年的发放年度，则发放的是当年的年假；如果没到当年的发放年度，则发放上一年的年假）
				year = giveDate.Year()
			}

			var totalDay int64
			if to1YearDate.Unix() == nowDate.Unix() {
				totalDay = int64(math.Floor(nextGiveDate.Sub(to1YearDate).Hours() / 24 / 365 * 5))
			}
			if to10YearDate.Unix() == nowDate.Unix() {
				totalDay = int64(math.Floor(nextGiveDate.Sub(to10YearDate).Hours()/24/365*5)) + 5
			}
			if to20YearDate.Unix() == nowDate.Unix() {
				totalDay = int64(math.Floor(nextGiveDate.Sub(to20YearDate).Hours()/24/365*5)) + 10
			}
			fmt.Printf("姓名：%s,Id:%v,岗位：%s,startTime:%v \n,totalDay:%v,calc:%s", staffs[i].Name, staffs[i].Id, util.ErpWorkPostTypeMap[workPostType], startTime, totalDay, now.Format("2006-01-02"))

			if totalDay > 0 {
				if record := (&hrModel.LeaveManagement{}).GetByStaffIdAndYear(staffs[i].Id, int64(year)); record.Id > 0 {
					//对于满10年 还未到下一个发放日以及满20年还未到下一个发放日的人员  更新年度年假
					record.GiveDate = model.LocalTime(nowDate)
					record.TotalDay = totalDay
					record.WorkPostType = workPostType
					record.CalcStartTime = model.LocalTime(startTime)
					err = record.Update()
					if err != nil {
						log.ErrorFields("CalcStaffAnnualLeaveInfo leaveManagement.Update error", map[string]interface{}{"err": err})
					}
				} else {
					var archive hrModel.StaffArchive
					_ = archive.FindByStaffId(staffs[i].Id)
					var leaveManagement = hrModel.LeaveManagement{
						StaffArchiveId: archive.Id,
						StaffId:        staffs[i].Id,
						StaffName:      staffs[i].Name,
						JobNumber:      staffs[i].StaffId,
						WorkPostType:   workPostType,
						CalcStartTime:  model.LocalTime(startTime),
						Year:           int64(year),
						TotalDay:       totalDay,
						LeaveType:      util.LeaveTypeForAnnualLeave,
						Status:         util.StatusForTrue,
						GiveDate:       model.LocalTime(nowDate),
					}
					leaveManagement.Corporations.Build(staffs[i].CorporationId)
					err = leaveManagement.Create()
					if err != nil {
						log.ErrorFields("CalcStaffAnnualLeaveInfo leaveManagement.Create error", map[string]interface{}{"err": err})
					}
				}
			}
		}
	}
}
