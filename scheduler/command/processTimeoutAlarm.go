package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	processModel "app/org/scs/erpv2/api/model/process"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	settingModel "app/org/scs/erpv2/api/model/setting"
	accidentSettingModel "app/org/scs/erpv2/api/model/setting/accident"
	"app/org/scs/erpv2/api/service/message"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"
)

func ProcessTimeoutAlarm() {
	//查询流程超时阈值设置
	var settingMap = make(map[string]accidentSettingModel.AccidentProcessTimeoutSetting)
	settings := (&accidentSettingModel.AccidentProcessTimeoutSetting{}).GetAll(config.Config.TopCorporationId)

	if len(settings) == 0 {
		return
	}

	for i := range settings {
		settingMap[settings[i].TemplateFormId] = settings[i]
	}

	//查询所有安全相关正在审批的流程 剔除忽略的流程
	processes := (&processModel.LbpmApplyProcess{}).GetWarningProcessByModelId(config.Config.TopCorporationId, config.SafetyModelId, []int64{util.ProcessStatusForDoing})

	for i := range processes {
		//如果是事故流程 需要过滤掉删除、回收站、已结案的事故
		var accidentTemplateFormId = []string{config.TrafficAccidentReportFormTemplate, config.TrafficAccidentEditFormTemplate, config.TrafficAccidentCloseFormTemplate}
		var accidentRelateTemplateFormId = []string{config.TrafficAccidentLendMoneyFormTemplate, config.TrafficAccidentDrawbackMoneyFormTemplate, config.TrafficAccidentPaymentMoneyFormTemplate, config.TrafficAccidentBranchCloseFormTemplate}
		var accidentId int64
		var accidentCode string
		if util.Include(accidentTemplateFormId, processes[i].TemplateFormId) || util.Include(accidentRelateTemplateFormId, processes[i].TemplateFormId) {
			if util.Include(accidentTemplateFormId, processes[i].TemplateFormId) {
				accidentId = processes[i].ItemId
			}
			if util.Include(accidentRelateTemplateFormId, processes[i].TemplateFormId) {
				var param = struct {
					TrafficAccidentId int64
				}{}
				_ = json.Unmarshal(processes[i].Param, &param)
				accidentId = param.TrafficAccidentId
			}
			if accidentId > 0 {
				var accident safetyModel.TrafficAccident
				err := accident.FindBy(accidentId)
				if err != nil || accident.IsRecycle == util.StatusForTrue || accident.IsClosed == util.StatusForTrue {
					continue
				}
				accidentCode = accident.Code
			}
		}

		//查询当前流程的审批节点和审批人
		handlers := (&processModel.LbpmApplyProcessHasHandler{}).GetAllHandlerByFormInstanceId(processes[i].FormInstanceId)
		if len(handlers) == 0 {
			continue
		}
		var approvingHandlerIds []string
		var approvingHandlerNames []string
		var approvingHandlers []processModel.LbpmApplyProcessHasHandler
		currentNode := handlers[len(handlers)-1].Node

		//如果当前流程的当前节点被忽略 则不进行通知
		alarmRecord := (&settingModel.ProcessTimeoutAlarmRecord{}).FindByFormInstanceIdAndNode(processes[i].FormInstanceId, currentNode)
		if alarmRecord.Id > 0 && alarmRecord.IsPushWarningNotify == util.StatusForFalse {
			continue
		}

		for j := range handlers {
			if handlers[j].Node == currentNode && handlers[j].Status == util.ProcessNodeHandleStatusForDoing {
				approvingHandlers = append(approvingHandlers, handlers[j])
				approvingHandlerIds = append(approvingHandlerIds, fmt.Sprintf("%v", handlers[j].UserId))
				approvingHandlerNames = append(approvingHandlerNames, handlers[j].UserName)
			}
		}

		if len(approvingHandlers) == 0 {
			continue
		}

		var warningValue, alarmValue int64
		var isExistSetting bool
		var nodeSetting accidentSettingModel.AccidentProcessTimeoutSettingDetail
		if _, ok := settingMap[processes[i].TemplateFormId]; ok {
			//获取节点的设置阈值
			nodeSetting = (&accidentSettingModel.AccidentProcessTimeoutSettingDetail{}).FindByNode(settingMap[processes[i].TemplateFormId].Id, currentNode)
			if nodeSetting.Id > 0 {
				isExistSetting = true
				warningValue = nodeSetting.WarningValue
				alarmValue = nodeSetting.AlarmValue
			}
		}

		if !isExistSetting {
			warningValue = settings[0].WarningValue
			alarmValue = settings[0].AlarmValue
		}

		if warningValue == 0 && alarmValue == 0 {
			continue
		}

		//计算流程滞留天数
		var startAt = *approvingHandlers[0].StartAt
		if time.Time(startAt).IsZero() {
			startAt = approvingHandlers[0].CreatedAt
		}

		diffSecond := time.Now().Unix() - time.Time(startAt).Unix()
		diffDay := int64(math.Ceil(float64(diffSecond) / float64(60*60*24)))
		if diffDay < warningValue {
			continue
		}
		var alarmType = util.AlarmTypeForWarning
		if diffDay >= alarmValue {
			alarmType = util.AlarmTypeForAlarm
		}

		alarmRecord.ApproveStartAt = startAt
		alarmRecord.WarningValue = warningValue
		alarmRecord.AlarmValue = alarmValue
		alarmRecord.CurrentValue = diffDay
		if isExistSetting {
			alarmRecord.AccidentAlarmCategorySettingId = nodeSetting.AccidentAlarmCategorySettingId
			category := (&accidentSettingModel.AccidentAlarmCategorySetting{}).FindBy(alarmRecord.AccidentAlarmCategorySettingId)
			alarmRecord.AccidentAlarmCategoryTitle = category.Title
		}
		alarmRecord.AlarmType = alarmType
		alarmRecord.FinishStatus = util.AlarmFinishStatusForDoing
		alarmRecord.ApprovingUserId = strings.Join(approvingHandlerIds, ",")
		alarmRecord.ApprovingUserName = strings.Join(approvingHandlerNames, ",")
		alarmRecord.ProcessNode = currentNode
		alarmRecord.TrafficAccidentId = accidentId
		alarmRecord.TrafficAccidentCode = accidentCode

		if alarmRecord.Id == 0 {
			alarmRecord.TopCorporationId = processes[i].TopCorporationId
			alarmRecord.FormInstanceId = processes[i].FormInstanceId
			alarmRecord.ProcessId = processes[i].ProcessId
			alarmRecord.ProcessTitle = processes[i].Title
			alarmRecord.ModelId = processes[i].ModelId
			alarmRecord.TemplateFormId = processes[i].TemplateFormId
			alarmRecord.TemplateFormName = processes[i].TemplateFormName
			if processes[i].ApplyAt != nil {
				alarmRecord.ProcessCreatedAt = *(processes[i].ApplyAt)
			} else {
				alarmRecord.ProcessCreatedAt = processes[i].CreatedAt
			}

			err := alarmRecord.Create()
			if err != nil {
				log.ErrorFields("ProcessTimeoutAlarm alarmRecord.Create error", map[string]interface{}{"err": err})
				continue
			}
		} else {
			err := alarmRecord.Update()
			if err != nil {
				log.ErrorFields("ProcessTimeoutAlarm alarmRecord.Update error", map[string]interface{}{"err": err})
				continue
			}
		}
	}
}

func ProcessTimeoutAlarmNotify() {
	//查询所有正在通知中的告警
	alarms := (&settingModel.ProcessTimeoutAlarmRecord{}).GetEnablePushRecord(util.AlarmFinishStatusForDoing)

	for i := range alarms {
		//查询流程信息
		var process processModel.LbpmApplyProcess
		err := process.FindBy(alarms[i].FormInstanceId)
		if err != nil {
			log.ErrorFields("process.FindBy error", map[string]interface{}{"err": err})
			continue
		}
		if process.IsPushWarningNotify == util.StatusForFalse || process.Status != util.ProcessStatusForDoing {
			continue
		}

		userIds := strings.Split(alarms[i].ApprovingUserId, ",")
		for _, userIdStr := range userIds {
			userId, _ := strconv.ParseInt(userIdStr, 10, 64)
			user := rpc.GetUserInfoById(context.Background(), userId)
			if user == nil {
				continue
			}
			msg := message.BuildProcessPushMsg(alarms[i])
			message.PushWorkNotifySendDingTalk(user.Phone, msg)
		}

		//go message.NewOfficialAccountMsg(
		//	fmt.Sprintf("流程%s", mapAlarm[alarms[i].AlarmType]),
		//	[]string{fmt.Sprintf(`%s流程%s`, alarms[i].TemplateFormName, mapAlarm[alarms[i].AlarmType]), fmt.Sprintf(`相关事项：%s`, alarms[i].ProcessTitle)},
		//	fmt.Sprintf("已在你处停留【%d】天，请尽快到ERP中进行处理", alarms[i].CurrentValue),
		//	"",
		//	user.Phone,
		//).Send()
	}
}
