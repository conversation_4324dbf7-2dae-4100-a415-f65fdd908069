package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service/rpc"
	"context"
	"time"
)

func SyncOetLine() {
	lines := rpc.GetLinesWithTopCorporationId(context.Background(), config.Config.TopCorporationId, 1)
	for i := range lines {
		var lineAllowance = settingModel.LineAllowancePriceSetting{
			LineId:     lines[i].Id,
			LineName:   lines[i].Name,
			Price:      200,
			UseMonth:   model.LocalTime(time.Date(2024, 9, 1, 0, 0, 0, 0, time.Local)),
			UseStartAt: model.LocalTime(time.Date(2024, 8, 26, 0, 0, 0, 0, time.Local)),
		}
		if len(lines[i].SubCorporationIds) > 0 {
			for j := range lines[i].SubCorporationIds {
				if (&settingModel.LineAllowancePriceSetting{}).IsExist(lines[i].SubCorporationIds[j], lines[i].Id) {
					continue
				}
				lineAllowance.Corporations.Build(lines[i].SubCorporationIds[j])
				_ = lineAllowance.Create()
			}
		} else {
			if (&settingModel.LineAllowancePriceSetting{}).IsExist(lines[i].SubCorporationId, lines[i].Id) {
				continue
			}
			lineAllowance.Corporations.Build(lines[i].SubCorporationId)
			_ = lineAllowance.Create()
		}
	}
}
