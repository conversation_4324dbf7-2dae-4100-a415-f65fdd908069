package command

import (
	"app/org/scs/erpv2/api/log"
	maintenanceModel "app/org/scs/erpv2/api/model/maintenance"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/message"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
)

func CheckVehicleMigrationJob() {
	records := (&maintenanceModel.VehicleMigration{}).GetEnableMigrationRecord()
	if len(records) == 0 {
		return
	}

	for i := range records {
		err := service.UpdateVehicleToOet(records[i], util.VehicleTransferTypeForFleet, "")
		if err != nil {
			log.ErrorFields("CheckVehicleMigrationJob service.UpdateVehicleToOet error", map[string]interface{}{"err": err, "record": records[i]})
		}
	}
}

func SendVehicleMigrationDingTalkNotify() {
	records := (&maintenanceModel.VehicleMigration{}).GetVehicleMigrationForDoing()
	if len(records) == 0 {
		return
	}

	for i := range records {
		//获取调动接收人
		user := rpc.GetUserInfoById(context.TODO(), records[i].AcceptUserId)
		if user == nil {
			continue
		}

		msg := message.BuildVehicleMigrationPushMsg(records[i])
		message.PushWorkNotifySendDingTalk(user.Phone, msg)
	}
}
