package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/rpc"
	"context"
)

func AutoCalcDriverPayroll() {
	//查询所有司机
	staffs := rpc.GetDriversWithCorporationId(context.TODO(), config.Config.TopCorporationId, "")
	if len(staffs) == 0 {
		return
	}
	(&setting.StaffPayrollConfig{}).GetAllStaffPayrollConfigs("稳定排班")
	for _, staff := range staffs {
		//if staff.Name == "洪卫国" {
		//	fmt.Println("================================开始计算:", staff.Name)
		service.CalcDrivePayroll(staff)
		//}
	}
	(&setting.StaffPayrollConfig{}).ClearStaffPayrollConfigCache()
}
