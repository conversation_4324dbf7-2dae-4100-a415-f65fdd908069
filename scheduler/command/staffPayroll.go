package command

import (
	"app/org/scs/erpv2/api/config"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"sort"
)

func AutoCalcDriverPayroll() {
	{
		// 稳定排班 查询所有司机
		staffs := rpc.GetDriversWithCorporationId(context.TODO(), config.Config.TopCorporationId, "")
		if len(staffs) == 0 {
			return
		}
		(&setting.StaffPayrollConfig{}).GetAllStaffPayrollConfigs("稳定排班")
		for _, staff := range staffs {
			//if staff.Name == "洪卫国" {
			//	fmt.Println("================================开始计算:", staff.Name)
			service.CalcDrivePayroll(staff)
			//}
		}
		(&setting.StaffPayrollConfig{}).ClearStaffPayrollConfigCache()
	}
	month, startAt, endAt := util.GetBusCompanyNowMonth()
	{
		// 村村通
		staffs, _ := (&hrModel.StaffPayrollCalculation{}).FiledList(hrModel.DriverVillage, month)
		if len(staffs) == 0 {
			return
		}
		(&setting.StaffPayrollConfig{}).GetAllStaffPayrollConfigs("村村通")
		staffCollection := make(map[int64][]hrModel.StaffPayrollCalculation)
		for _, staff := range staffs {
			staffCollection[staff.StaffId] = append(staffCollection[staff.StaffId], staff)
		}
		for staffId, staffArr := range staffCollection {
			service.CalcDrivePayrollForVillage(staffId, staffArr, startAt, endAt)
		}

		(&setting.StaffPayrollConfig{}).ClearStaffPayrollConfigCache()
	}
	{
		// 定制公交
		staffs, _ := (&hrModel.StaffPayrollCalculation{}).FiledList(hrModel.DriverCustomerLine, month)
		if len(staffs) == 0 {
			return
		}
		(&setting.StaffPayrollConfig{}).GetAllStaffPayrollConfigs("定制公交")
		staffCollection := make(map[int64][]hrModel.StaffPayrollCalculation)
		for _, staff := range staffs {
			staffCollection[staff.StaffId] = append(staffCollection[staff.StaffId], staff)
		}
		for staffId, staffArr := range staffCollection {
			service.CalcDrivePayrollForCustomer(staffId, staffArr, startAt, endAt)
		}

		(&setting.StaffPayrollConfig{}).ClearStaffPayrollConfigCache()
	}
	{
		// 小蓝巴
		staffs, _ := (&hrModel.StaffPayrollCalculation{}).FiledList(hrModel.DriverBlueBus, month)
		if len(staffs) == 0 {
			return
		}
		(&setting.StaffPayrollConfig{}).GetAllStaffPayrollConfigs("小蓝巴")
		staffCollection := make(map[int64][]hrModel.StaffPayrollCalculation)
		staffReward := make(map[int64]bool)
		totalPassengers := make(map[int64]int64)
		for _, staff := range staffs {
			totalPassengers[staff.StaffId] += staff.PassengerNumber
			staffCollection[staff.StaffId] = append(staffCollection[staff.StaffId], staff)
		}
		type StaffTotal struct {
			StaffId        int64
			TotalPassenger int64
		}
		var staffTotals []StaffTotal
		for staffId, total := range totalPassengers {
			staffTotals = append(staffTotals, StaffTotal{
				StaffId:        staffId,
				TotalPassenger: total,
			})
		}
		// 按总乘客数降序排序
		sort.Slice(staffTotals, func(i, j int) bool {
			return staffTotals[i].TotalPassenger > staffTotals[j].TotalPassenger
		})
		halfIndex := len(staffTotals) / 2
		for i := 0; i < halfIndex; i++ {
			staffReward[staffTotals[i].StaffId] = true
		}

		for staffId, staffArr := range staffCollection {
			service.CalcDrivePayrollForBlueBus(staffId, staffArr, startAt, endAt, staffReward[staffId])
		}

		(&setting.StaffPayrollConfig{}).ClearStaffPayrollConfigCache()
	}
}
