package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	assessModel "app/org/scs/erpv2/api/model/safety/assess"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"github.com/shopspring/decimal"
	"strconv"
	"time"
)

// AutoCalcDriverSafeAssessReportForDevote 司机贡献考核
func AutoCalcDriverSafeAssessReportForDevote() {
	//查询所有司机
	staffs := rpc.GetDriversWithCorporationId(context.TODO(), config.Config.TopCorporationId, "")
	if len(staffs) == 0 {
		return
	}
	var end time.Time
	if time.Now().Day() <= 25 {
		end = time.Now().AddDate(0, -1, 0)
	} else {
		end = time.Now()
	}
	endAt := time.Date(end.Year(), end.Month(), 25, 23, 59, 59, 999, time.Local)
	start := endAt.AddDate(0, -1, 0)
	startAt := time.Date(start.Year(), start.Month(), 26, 0, 0, 0, 0, time.Local)
	for i := range staffs {
		var report = DriverSafeAssessReport{
			Staff: StaffArchiveInfo{
				StaffId:       staffs[i].Id,
				StaffName:     staffs[i].Name,
				CorporationId: staffs[i].CorporationId,
				LineId:        staffs[i].LineId,
			},
			StartAt: startAt,
			EndAt:   endAt,
		}
		month := endAt.Format("200601")
		report.Month, _ = strconv.ParseInt(month, 10, 64)
		staffArchive := (&hrModel.StaffArchive{}).FirstByStaffId(staffs[i].Id)
		if staffArchive.IsCctDriver == util.StatusForTrue {
			report.Staff.IsCctDriver = true
		} else {
			report.Staff.IsCctDriver = false
		}

		line, _ := rpc.GetLineWithId(context.TODO(), staffs[i].LineId)
		if line != nil {
			report.Staff.LineName = line.Name
		}

		report.ProcessJobSafeDevote()
		report.ProcessJobServiceDevote()
		go report.SaveDriverDevoteMoneyForMonth()
	}
}

func ManualCalcDriverSafeAssessReportForDevote(calcMonth int) {
	//查询所有司机
	staffs := rpc.GetDriversWithCorporationId(context.TODO(), config.Config.TopCorporationId, "")
	if len(staffs) == 0 {
		return
	}
	endAt := time.Date(2025, time.Month(calcMonth), 25, 23, 59, 59, 999, time.Local)
	start := endAt.AddDate(0, -1, 0)
	startAt := time.Date(start.Year(), start.Month(), 26, 0, 0, 0, 0, time.Local)
	for i := range staffs {
		var report = DriverSafeAssessReport{
			Staff: StaffArchiveInfo{
				StaffId:       staffs[i].Id,
				StaffName:     staffs[i].Name,
				CorporationId: staffs[i].CorporationId,
				LineId:        staffs[i].LineId,
			},
			StartAt: startAt,
			EndAt:   endAt,
		}
		month := endAt.Format("200601")
		report.Month, _ = strconv.ParseInt(month, 10, 64)
		staffArchive := (&hrModel.StaffArchive{}).FirstByStaffId(staffs[i].Id)
		if staffArchive.IsCctDriver == util.StatusForTrue {
			report.Staff.IsCctDriver = true
		} else {
			report.Staff.IsCctDriver = false
		}

		line, _ := rpc.GetLineWithId(context.TODO(), staffs[i].LineId)
		if line != nil {
			report.Staff.LineName = line.Name
		}

		report.ProcessJobSafeDevote()
		report.ProcessJobServiceDevote()
	}
}

func (r *DriverSafeAssessReport) ProcessJobSafeDevote() {
	//查询司机考核记录
	devoteRecord := (&assessModel.DriverDevoteReport{}).FirstBy(r.Staff.StaffId)
	if devoteRecord.Id == 0 {
		var isCct = util.StatusForFalse
		if r.Staff.IsCctDriver {
			isCct = util.StatusForTrue
		}
		record := assessModel.DriverDevoteReport{
			StaffId:                r.Staff.StaffId,
			StaffName:              r.Staff.StaffName,
			LineId:                 r.Staff.LineId,
			LineName:               r.Staff.LineName,
			IsCctDriver:            int64(isCct),
			SafeReportMonth:        202501,
			SafeStartMonth:         202501,
			SafeMonthSum:           0,
			SafeCurrentLevel:       1,
			SafeRewardBaseMoney:    0,
			ServiceReportMonth:     202501,
			ServiceStartMonth:      202501,
			ServiceMonthSum:        0,
			ServiceCurrentLevel:    1,
			ServiceRewardBaseMoney: 0,
		}
		record.Corporations.Build(r.Staff.CorporationId)
		_ = record.Create()
		return
	}

	//查询司机的违规数据
	violations := (&safetyModel.TrafficViolation{}).GetDriverAllViolation(r.Staff.StaffId, 0, r.StartAt, r.EndAt)

	var validViolations []safetyModel.TrafficViolation
	for _, violation := range violations {
		var standards []safetyModel.QualityAssessmentStandards
		err := json.Unmarshal(violation.Standards, &standards)
		if err != nil {
			continue
		}
		if violation.CateAttr != util.TrafficViolationCateAttrForSafe {
			continue
		}
		for i := range standards {
			categoryId := standards[i].CategoryId
			//查询分类的顶级分类ID
			category := (&safetyModel.ViolationCategory{}).FirstById(categoryId)
			if category.DevoteStatus == util.StatusForTrue {
				if category.ParentId > 0 {
					category = (&safetyModel.ViolationCategory{}).FirstById(category.ParentId)
					if category.ParentId > 0 {
						category = (&safetyModel.ViolationCategory{}).FirstById(category.ParentId)
					}
				}
				if category.LevelType >= util.ViolationCategoryLevelTypeFor2 {
					validViolations = append(validViolations, violation)
					break
				}
			}
		}
	}

	//查询事故
	accidents := (&safetyModel.TrafficAccident{}).GetDriverAccident(r.Staff.StaffId, r.StartAt, r.EndAt)
	var validAccidents []safetyModel.TrafficAccident
	var allValidAccidents []safetyModel.TrafficAccident
	for i := range accidents {
		if util.IncludeInt64([]int64{util.AccidentLiabilityTypeForAll, util.AccidentLiabilityTypeForMain, util.AccidentLiabilityTypeForEqual}, accidents[i].LiabilityType) ||
			accidents[i].PeopleHurtCate == util.AccidentPeopleHurtCateForInner {
			validAccidents = append(validAccidents, accidents[i])
		}
		if accidents[i].LiabilityType != util.AccidentLiabilityTypeForNull {
			allValidAccidents = append(validAccidents, accidents[i])
		}
	}

	//计算天数 （实际工作天数）
	runDay := CalcDriverRunDay(r.Staff.StaffId, r.Staff.IsCctDriver, r.StartAt, r.EndAt)
	//计算平均计划天数（基准天数）
	avgPlanDay, mainLineId, mainLineName := CalcAvgPlanDay(r.Staff.StaffId, r.StartAt, r.EndAt)

	//查询贡献考核奖金配置
	setting := (&settingModel.GlobalSetting{}).GetBy(config.Config.TopCorporationId, util.GlobalSettingTypeForSafeContribution)
	var settingItem settingModel.GlobalSettingItemForViolationSafeContributionAssess
	err := json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
	}

	var settingMap = make(map[int64]settingModel.GlobalSettingItemForContributionAssessItem)
	for _, st := range settingItem.Settings {
		settingMap[st.Level] = st
	}
	beforeLevel := devoteRecord.SafeCurrentLevel

	_ = devoteRecord.UpdateColumn("SafeReportMonth", r.Month)

	//升档
	if avgPlanDay <= decimal.NewFromInt(runDay).Div(decimal.NewFromInt(10)).InexactFloat64() {
		if len(validViolations) == 0 && len(allValidAccidents) == 0 {
			if beforeLevel >= 7 {
				return
			}
			devoteRecord.SafeMonthSum++

			tx := model.DB().Begin()
			//满足升档条件
			if devoteRecord.SafeMonthSum >= 6 {
				devoteRecord.SafeCurrentLevel += 1
				devoteRecord.SafeMonthSum = 0
				devoteRecord.SafeStartMonth = r.Month
				devoteRecord.SafeRewardBaseMoney = settingMap[devoteRecord.SafeCurrentLevel].Money

				//写入升档记录
				var logger = assessModel.DriverDevoteLogger{
					DriverDevoteReportId: devoteRecord.Id,
					ReportMonth:          r.Month,
					BeforeLevel:          beforeLevel,
					AfterLevel:           devoteRecord.SafeCurrentLevel,
					Action:               "up",
					Scene:                "safe",
				}
				err = logger.Create(tx)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("logger.Create error", map[string]interface{}{"error": err, "logger": logger})
					return
				}
			}

			err = devoteRecord.UpdateSafe(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("devoteRecord.UpdateSafe error", map[string]interface{}{"error": err, "devoteRecord": devoteRecord})
				return
			}
			tx.Commit()
			return
		}
	}

	//天数不满
	if avgPlanDay > decimal.NewFromInt(runDay).Div(decimal.NewFromInt(10)).InexactFloat64() {
		if len(validAccidents) == 0 {
			//写入天数不满记录
			var logger = assessModel.DriverDevoteLogger{
				DriverDevoteReportId: devoteRecord.Id,
				ReportMonth:          r.Month,
				BeforeLevel:          beforeLevel,
				AfterLevel:           beforeLevel,
				Action:               "under_day",
				Scene:                "safe",
			}
			var detail = map[string]interface{}{
				"Month":        r.Month,
				"MainLineId":   mainLineId,
				"MainLineName": mainLineName,
				"RunDay":       runDay * 10, //运营天数单位是天数*10，这里再*10  单位变成天数*100
				"AvgPlanDay":   decimal.NewFromFloat(avgPlanDay).Mul(decimal.NewFromInt(100)),
			}
			logger.Detail, _ = json.Marshal(detail)
			err = logger.Create(model.DB())
			if err != nil {
				log.ErrorFields("logger.Create error", map[string]interface{}{"error": err, "logger": logger})
				return
			}
			return
		}
	}

	//降档
	if len(validAccidents) > 0 {
		tx := model.DB().Begin()
		if beforeLevel > 1 {
			devoteRecord.SafeCurrentLevel = beforeLevel - int64(len(validAccidents))
			if devoteRecord.SafeCurrentLevel < 1 {
				devoteRecord.SafeCurrentLevel = 1
			}
		}
		devoteRecord.SafeMonthSum = 0
		devoteRecord.SafeStartMonth = r.Month
		devoteRecord.SafeRewardBaseMoney = settingMap[devoteRecord.SafeCurrentLevel].Money

		err = devoteRecord.UpdateSafe(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("devoteRecord.UpdateSafe error", map[string]interface{}{"error": err, "devoteRecord": devoteRecord})
			return
		}

		//写入降档记录
		var logger = assessModel.DriverDevoteLogger{
			DriverDevoteReportId: devoteRecord.Id,
			ReportMonth:          r.Month,
			BeforeLevel:          beforeLevel,
			AfterLevel:           devoteRecord.SafeCurrentLevel,
			Action:               "down",
			Scene:                "safe",
		}
		var detail []map[string]interface{}
		for _, acc := range validAccidents {
			detail = append(detail, map[string]interface{}{
				"Id":       acc.Id,
				"HappenAt": acc.HappenAt,
				"Scene":    "accident",
			})
		}
		logger.Detail, _ = json.Marshal(detail)
		err = logger.Create(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("logger.Create error", map[string]interface{}{"error": err, "logger": logger})
			return
		}

		tx.Commit()
		return
	}
}

func (r *DriverSafeAssessReport) ProcessJobServiceDevote() {
	//查询司机考核记录
	devoteRecord := (&assessModel.DriverDevoteReport{}).FirstBy(r.Staff.StaffId)

	//查询司机当月的违规数据
	violations := (&safetyModel.TrafficViolation{}).GetDriverAllViolation(r.Staff.StaffId, 0, r.StartAt, r.EndAt)

	var validViolations []safetyModel.TrafficViolation
	for _, violation := range violations {
		var standards []safetyModel.QualityAssessmentStandards
		err := json.Unmarshal(violation.Standards, &standards)
		if err != nil {
			continue
		}
		if violation.CateAttr != util.TrafficViolationCateAttrForService {
			continue
		}
		for i := range standards {
			categoryId := standards[i].CategoryId
			//查询分类的顶级分类ID
			category := (&safetyModel.ViolationCategory{}).FirstById(categoryId)
			if category.DevoteStatus == util.StatusForTrue {
				if category.ParentId > 0 {
					category = (&safetyModel.ViolationCategory{}).FirstById(category.ParentId)
					if category.ParentId > 0 {
						category = (&safetyModel.ViolationCategory{}).FirstById(category.ParentId)
					}
				}
				if category.LevelType >= util.ViolationCategoryLevelTypeFor2 {
					validViolations = append(validViolations, violation)
					break
				}
			}
		}
	}

	//计算天数 （实际工作天数）
	runDay := CalcDriverRunDay(r.Staff.StaffId, r.Staff.IsCctDriver, r.StartAt, r.EndAt)
	//计算平均计划天数（基准天数）
	avgPlanDay, mainLineId, mainLineName := CalcAvgPlanDay(r.Staff.StaffId, r.StartAt, r.EndAt)

	//查询贡献考核奖金配置
	setting := (&settingModel.GlobalSetting{}).GetBy(config.Config.TopCorporationId, util.GlobalSettingTypeForServiceContribution)
	var settingItem settingModel.GlobalSettingItemForServiceSafeContributionAssess
	err := json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
	}

	var settingMap = make(map[int64]settingModel.GlobalSettingItemForContributionAssessItem)
	for _, st := range settingItem.Settings {
		settingMap[st.Level] = st
	}
	beforeLevel := devoteRecord.ServiceCurrentLevel

	_ = devoteRecord.UpdateColumn("ServiceReportMonth", r.Month)

	//升档
	if avgPlanDay <= decimal.NewFromInt(runDay).Div(decimal.NewFromInt(10)).InexactFloat64() {
		if len(validViolations) == 0 {
			if beforeLevel >= 7 {
				return
			}
			devoteRecord.ServiceMonthSum++

			tx := model.DB().Begin()
			//满足升档条件
			if devoteRecord.ServiceMonthSum >= 6 {
				devoteRecord.ServiceCurrentLevel += 1
				devoteRecord.ServiceMonthSum = 0
				devoteRecord.ServiceStartMonth = r.Month
				devoteRecord.ServiceRewardBaseMoney = settingMap[devoteRecord.ServiceCurrentLevel].Money

				//写入升档记录
				var logger = assessModel.DriverDevoteLogger{
					DriverDevoteReportId: devoteRecord.Id,
					ReportMonth:          r.Month,
					BeforeLevel:          beforeLevel,
					AfterLevel:           devoteRecord.ServiceCurrentLevel,
					Action:               "up",
					Scene:                "service",
				}
				err = logger.Create(tx)
				if err != nil {
					tx.Rollback()
					log.ErrorFields("logger.Create error", map[string]interface{}{"error": err, "logger": logger})
					return
				}
			}

			err = devoteRecord.UpdateService(tx)
			if err != nil {
				tx.Rollback()
				log.ErrorFields("devoteRecord.UpdateService error", map[string]interface{}{"error": err, "devoteRecord": devoteRecord})
				return
			}
			tx.Commit()
			return
		}
	}

	//天数不满
	if avgPlanDay > decimal.NewFromInt(runDay).Div(decimal.NewFromInt(10)).InexactFloat64() {
		if len(validViolations) == 0 {
			//写入天数不满记录
			var logger = assessModel.DriverDevoteLogger{
				DriverDevoteReportId: devoteRecord.Id,
				ReportMonth:          r.Month,
				BeforeLevel:          beforeLevel,
				AfterLevel:           beforeLevel,
				Action:               "under_day",
				Scene:                "service",
			}
			var detail = map[string]interface{}{
				"Month":        r.Month,
				"MainLineId":   mainLineId,
				"MainLineName": mainLineName,
				"RunDay":       runDay * 10, //运营天数单位是天数*10，这里再*10  单位变成天数*100
				"AvgPlanDay":   decimal.NewFromFloat(avgPlanDay).Mul(decimal.NewFromInt(100)),
			}
			logger.Detail, _ = json.Marshal(detail)
			err = logger.Create(model.DB())
			if err != nil {
				log.ErrorFields("logger.Create error", map[string]interface{}{"error": err, "logger": logger})
				return
			}
			return
		}
	}

	start4 := devoteRecord.ServiceFourLevelStartAt.ToTime()
	if start4.IsZero() {
		start4 = time.Date(time.Now().Year()-1, 12, 26, 0, 0, 0, 0, time.Local)
	}
	validViolation4 := r.getStaffViolations(start4, r.EndAt, util.ViolationCategoryLevelTypeFor4)
	//降档
	if len(validViolation4) > 0 {
		tx := model.DB().Begin()
		if beforeLevel > 1 {
			devoteRecord.ServiceCurrentLevel = beforeLevel - int64(len(validViolation4))
			if devoteRecord.ServiceCurrentLevel < 1 {
				devoteRecord.ServiceCurrentLevel = 1
			}
		}
		devoteRecord.ServiceMonthSum = 0
		devoteRecord.ServiceStartMonth = r.Month
		devoteRecord.ServiceRewardBaseMoney = settingMap[devoteRecord.ServiceCurrentLevel].Money
		devoteRecord.ServiceFourLevelStartAt = model.LocalTime(r.StartAt.AddDate(0, 1, 0))

		err = devoteRecord.UpdateService(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("devoteRecord.Update error", map[string]interface{}{"error": err, "devoteRecord": devoteRecord})
			return
		}

		//写入降档记录
		var logger = assessModel.DriverDevoteLogger{
			DriverDevoteReportId: devoteRecord.Id,
			ReportMonth:          r.Month,
			BeforeLevel:          beforeLevel,
			AfterLevel:           devoteRecord.ServiceCurrentLevel,
			Action:               "down",
			Scene:                "service",
		}
		var detail []map[string]interface{}
		for _, acc := range validViolation4 {
			detail = append(detail, map[string]interface{}{
				"Id":       acc.Id,
				"HappenAt": acc.ReportAt,
				"Scene":    "safe",
			})
		}
		logger.Detail, _ = json.Marshal(detail)
		err = logger.Create(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("logger.Create error", map[string]interface{}{"error": err, "logger": logger})
			return
		}

		tx.Commit()
		return
	}

	start3 := devoteRecord.ServiceThreeLevelStartAt.ToTime()
	if start3.IsZero() {
		start3 = time.Date(time.Now().Year()-1, 12, 26, 0, 0, 0, 0, time.Local)
	}
	validViolation3 := r.getStaffViolations(start3, r.EndAt, util.ViolationCategoryLevelTypeFor3)
	if len(devoteRecord.ServiceThreeLevelRemain) > 0 {
		var ids []int64
		_ = json.Unmarshal(devoteRecord.ServiceThreeLevelRemain, &ids)
		remainViolations := (&safetyModel.TrafficViolation{}).GetViolationByIds(ids)
		validViolation3 = append(remainViolations, validViolation3...)
	}
	//降档
	if len(validViolation3) >= 2 {
		beforeLevel = devoteRecord.ServiceCurrentLevel
		tx := model.DB().Begin()
		if beforeLevel > 1 {
			devoteRecord.ServiceCurrentLevel = beforeLevel - int64(len(validViolation3)/2)
			if devoteRecord.ServiceCurrentLevel < 1 {
				devoteRecord.ServiceCurrentLevel = 1
			}
		}
		devoteRecord.ServiceMonthSum = 0
		devoteRecord.ServiceStartMonth = r.Month
		devoteRecord.ServiceRewardBaseMoney = settingMap[devoteRecord.ServiceCurrentLevel].Money
		devoteRecord.ServiceThreeLevelStartAt = model.LocalTime(r.StartAt.AddDate(0, 1, 0))
		if len(validViolation3)%2 > 0 {
			remainViolation := validViolation3[len(validViolation3)-1]
			devoteRecord.ServiceThreeLevelRemain, _ = json.Marshal([]int64{remainViolation.Id})
		} else {
			devoteRecord.ServiceThreeLevelRemain = nil
		}

		err = devoteRecord.UpdateService(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("devoteRecord.Update error", map[string]interface{}{"error": err, "devoteRecord": devoteRecord})
			return
		}

		//写入降档记录
		var logger = assessModel.DriverDevoteLogger{
			DriverDevoteReportId: devoteRecord.Id,
			ReportMonth:          r.Month,
			BeforeLevel:          beforeLevel,
			AfterLevel:           devoteRecord.ServiceCurrentLevel,
			Action:               "down",
			Scene:                "service",
		}
		var detail []map[string]interface{}
		endIndex := len(validViolation3) - (len(validViolation3) % 2)
		for _, va := range validViolation3[:endIndex] {
			detail = append(detail, map[string]interface{}{
				"Id":       va.Id,
				"HappenAt": va.ReportAt,
				"Scene":    "safe",
			})
		}
		logger.Detail, _ = json.Marshal(detail)
		err = logger.Create(tx)
		if err != nil {
			tx.Rollback()
			log.ErrorFields("logger.Create error", map[string]interface{}{"error": err, "logger": logger})
			return
		}

		tx.Commit()
		return
	}
}

func (r *DriverSafeAssessReport) getStaffViolations(startAt, endAt time.Time, violationCategoryLevelType int64) []safetyModel.TrafficViolation {
	//查询司机当年的违规数据
	violations := (&safetyModel.TrafficViolation{}).GetDriverAllViolation(r.Staff.StaffId, 0, startAt, endAt)

	var validViolations []safetyModel.TrafficViolation
	for _, violation := range violations {
		var standards []safetyModel.QualityAssessmentStandards
		err := json.Unmarshal(violation.Standards, &standards)
		if err != nil {
			continue
		}
		if violation.CateAttr != util.TrafficViolationCateAttrForService {
			continue
		}
		for i := range standards {
			categoryId := standards[i].CategoryId
			//查询分类的顶级分类ID
			category := (&safetyModel.ViolationCategory{}).FirstById(categoryId)
			if category.DevoteStatus == util.StatusForTrue {
				if category.ParentId > 0 {
					category = (&safetyModel.ViolationCategory{}).FirstById(category.ParentId)
					if category.ParentId > 0 {
						category = (&safetyModel.ViolationCategory{}).FirstById(category.ParentId)
					}
				}
				if category.LevelType == violationCategoryLevelType {
					validViolations = append(validViolations, violation)
					break
				}
			}
		}
	}

	return validViolations
}

func (r *DriverSafeAssessReport) SaveDriverDevoteMoneyForMonth() {
	baseReport := (&assessModel.DriverDevoteReport{}).FirstBy(r.Staff.StaffId)

	monthReport := (&assessModel.DriverDevoteMonthMoneyReport{}).GetDriverMonthReport(r.Staff.StaffId, r.Month)

	runDay := CalcDriverRunDay(r.Staff.StaffId, r.Staff.IsCctDriver, r.StartAt, r.EndAt)
	avgPlanDay, mainLineId, mainLineName := CalcAvgPlanDay(r.Staff.StaffId, r.StartAt, r.EndAt)

	monthReport.StaffId = r.Staff.StaffId
	monthReport.StaffName = r.Staff.StaffName
	monthReport.LineId = r.Staff.LineId
	monthReport.LineName = r.Staff.LineName
	monthReport.MainLineId = mainLineId
	monthReport.MainLineName = mainLineName

	if r.Staff.IsCctDriver {
		monthReport.IsCctDriver = util.StatusForTrue
	} else {
		monthReport.IsCctDriver = util.StatusForFalse
	}
	monthReport.Corporations.Build(r.Staff.CorporationId)
	monthReport.ReportMonth = r.Month
	monthReport.StartAt = model.LocalTime(r.StartAt)
	monthReport.EndAt = model.LocalTime(r.EndAt)
	monthReport.RunDay = runDay * 10 //运营天数单位是天数*10，这里再*10  单位变成天数*100
	monthReport.AvgPlanDay = decimal.NewFromFloat(avgPlanDay).Mul(decimal.NewFromInt(100)).IntPart()
	monthReport.SafeRewardBaseMoney = baseReport.SafeRewardBaseMoney
	monthReport.ServiceRewardBaseMoney = baseReport.ServiceRewardBaseMoney

	//计算系数
	var rate float64
	if avgPlanDay > 0 {
		rate = (decimal.NewFromInt(runDay).Div(decimal.NewFromInt(10))).Div(decimal.NewFromFloat(avgPlanDay)).Round(4).InexactFloat64()
	}
	if rate > 1 {
		rate = 1
	}
	//计算安全奖金
	monthReport.SafeDevoteMoney = decimal.NewFromInt(monthReport.SafeRewardBaseMoney).Mul(decimal.NewFromFloat(rate)).IntPart()

	//计算服务奖金
	monthReport.ServiceDevoteMoney = decimal.NewFromInt(monthReport.ServiceRewardBaseMoney).Mul(decimal.NewFromFloat(rate)).IntPart()

	if monthReport.Id == 0 {
		err := monthReport.Create()
		if err != nil {
			log.ErrorFields("DriverSafeAssessReport DriverDevoteMonthMoneyReport create error", map[string]interface{}{"err": err})
		}
	} else {
		err := monthReport.Update()
		if err != nil {
			log.ErrorFields("DriverSafeAssessReport DriverDevoteMonthMoneyReport update error", map[string]interface{}{"err": err})
		}
	}
}
