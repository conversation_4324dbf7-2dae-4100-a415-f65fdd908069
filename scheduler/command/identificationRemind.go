package command

import (
	"app/org/scs/erpv2/api/model"
	operationModel "app/org/scs/erpv2/api/model/operation"
	"fmt"
)

func IdentificationRemind() {
	qs := model.NewQs()
	qs.Add("CardStatus = ?", 1)
	normalVehicleIdentifications, _, _ := (&operationModel.VehicleIdentification{}).List(qs, 0, model.Paginator{Limit: 0})
	if normalVehicleIdentifications != nil {
		for _, v := range normalVehicleIdentifications {
			v.SetCardStatus()
			if v.CardStatus == 1 {
				continue
			}
			if v.CardStatus == 2 {
				var remindConfig operationModel.RemindStaffConfig
				_ = remindConfig.Find(1, v.CardType)
				if remindConfig.StaffIds == "" {
					continue
				}
				var record operationModel.IdentificationRemindRecord
				record.Type = 1
				record.DataId = v.Id
				record.CardNumber = v.CardNumber
				record.CardType = v.CardType
				record.StaffNames = remindConfig.StaffNames
				record.StaffIds = remindConfig.StaffIds
				record.Message = fmt.Sprintf("编号为:%s的%s证件即将过期，请及时处理", v.CardNumber, v.CardTypeText())
				_ = record.Create()
				//TODO: 钉钉提醒
			}
			_ = v.Updates()
		}
	}
	normalDriverIdentifications, _, _ := (&operationModel.DriverIdentification{}).List(qs, 0, model.Paginator{Limit: 0})
	if normalDriverIdentifications != nil {
		for _, v := range normalDriverIdentifications {
			v.SetCardStatus()
			if v.CardStatus == 1 {
				continue
			}
			if v.CardStatus == 2 {
				var remindConfig operationModel.RemindStaffConfig
				_ = remindConfig.Find(2, v.CardType)
				if remindConfig.StaffIds == "" {
					continue
				}
				var record operationModel.IdentificationRemindRecord
				record.Type = 2
				record.DataId = v.Id
				record.CardNumber = v.CardNumber
				record.CardType = v.CardType
				record.StaffNames = remindConfig.StaffNames
				record.StaffIds = remindConfig.StaffIds
				record.Message = fmt.Sprintf("编号为:%s的%s证件即将过期，司机为:%s，请及时处理", v.CardNumber, v.DriverName, v.CardTypeText())
				_ = record.Create()
				//TODO: 钉钉提醒 短信提醒
			}
			_ = v.Updates()
		}
	}
}
