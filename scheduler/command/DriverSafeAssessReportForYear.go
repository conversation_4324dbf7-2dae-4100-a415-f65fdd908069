package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/model/operation"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	"app/org/scs/erpv2/api/model/safety/assess"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"github.com/shopspring/decimal"
	"sort"
	"strconv"
	"time"
)

func AutoCalcDriverSafeAssessReportForYear() {
	//查询所有司机
	staffs := rpc.GetDriversWithCorporationId(context.TODO(), config.Config.TopCorporationId, "")
	if len(staffs) == 0 {
		return
	}
	var end time.Time
	if time.Now().Month() >= 12 && time.Now().Day() > 25 {
		end = time.Now()
	} else {
		end = time.Now().AddDate(-1, 0, 0)
	}
	endAt := time.Date(end.Year(), 12, 25, 23, 59, 59, 999, time.Local)
	start := endAt.AddDate(-1, 0, 0)
	startAt := time.Date(start.Year(), 12, 26, 0, 0, 0, 0, time.Local)

	for i := range staffs {
		var report = DriverSafeAssessReport{
			Staff: StaffArchiveInfo{
				StaffId:       staffs[i].Id,
				StaffName:     staffs[i].Name,
				CorporationId: staffs[i].CorporationId,
				LineId:        staffs[i].LineId,
			},
			StartAt: startAt,
			EndAt:   endAt,
		}
		report.Year = int64(endAt.Year())
		staffArchive := (&hrModel.StaffArchive{}).FirstByStaffId(staffs[i].Id)
		if staffArchive.IsCctDriver == util.StatusForTrue {
			report.Staff.IsCctDriver = true
		} else {
			report.Staff.IsCctDriver = false
		}

		line, _ := rpc.GetLineWithId(context.TODO(), staffs[i].LineId)
		if line != nil {
			report.Staff.LineName = line.Name
		}

		report.ProcessJobYear()
	}
}

func ManualCalcDriverSafeAssessReportForYear(topCorporationId int64, at time.Time) {
	//查询所有司机
	staffs := rpc.GetDriversWithCorporationId(context.TODO(), topCorporationId, "")
	if len(staffs) == 0 {
		return
	}

	endAt := time.Date(at.Year(), 12, 25, 23, 59, 59, 999, time.Local)
	start := endAt.AddDate(-1, 0, 0)
	startAt := time.Date(start.Year(), 12, 26, 0, 0, 0, 0, time.Local)

	for i := range staffs {
		var report = DriverSafeAssessReport{
			Staff: StaffArchiveInfo{
				StaffId:       staffs[i].Id,
				StaffName:     staffs[i].Name,
				CorporationId: staffs[i].CorporationId,
				LineId:        staffs[i].LineId,
			},
			StartAt: startAt,
			EndAt:   endAt,
		}
		report.Year = int64(endAt.Year())
		staffArchive := (&hrModel.StaffArchive{}).FirstByStaffId(staffs[i].Id)
		if staffArchive.IsCctDriver == util.StatusForTrue {
			report.Staff.IsCctDriver = true
		} else {
			report.Staff.IsCctDriver = false
		}

		line, _ := rpc.GetLineWithId(context.TODO(), staffs[i].LineId)
		if line != nil {
			report.Staff.LineName = line.Name
		}

		report.ProcessJobYear()
	}
}

func (r *DriverSafeAssessReport) ProcessJobYear() {
	//查询安全年考核配置  计算扣款金额
	setting := (&settingModel.GlobalSetting{}).GetBy(config.Config.TopCorporationId, util.GlobalSettingTypeForSafeYearAssess)
	var settingItem settingModel.GlobalSettingItemForViolationYearAssess
	err := json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
	}

	safeSt := settingItem.Line
	if r.Staff.IsCctDriver {
		safeSt = settingItem.CctLine
	}

	//查询服务年考核配置
	serviceSetting := (&settingModel.GlobalSetting{}).GetBy(config.Config.TopCorporationId, util.GlobalSettingTypeForServiceYearAssess)
	var serviceSettingItem settingModel.GlobalSettingItemForServiceYearAssess
	err = json.Unmarshal(serviceSetting.SettingItem, &serviceSettingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
	}
	serviceSt := serviceSettingItem.Line
	if r.Staff.IsCctDriver {
		serviceSt = serviceSettingItem.CctLine
	}

	//查询三方测评年考核配置
	thirdAssessSetting := (&settingModel.GlobalSetting{}).GetBy(config.Config.TopCorporationId, util.GlobalSettingTypeForServiceThirdPartyAssess)
	var thirdSt settingModel.GlobalSettingItemForServiceThirdPartyAssess
	err = json.Unmarshal(thirdAssessSetting.SettingItem, &thirdSt)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
	}

	var report = assess.DriverYearAssessReport{
		StaffId:    r.Staff.StaffId,
		StaffName:  r.Staff.StaffName,
		LineId:     r.Staff.LineId,
		LineName:   r.Staff.LineName,
		ReportYear: r.Year,
		StartAt:    model.LocalTime(r.StartAt),
		EndAt:      model.LocalTime(r.EndAt),
	}

	report.Corporations.Build(r.Staff.CorporationId)
	if r.Staff.IsCctDriver {
		report.IsCctDriver = util.StatusForTrue
	} else {
		report.IsCctDriver = util.StatusForFalse
	}

	//出勤记录
	driverRunRecords := r.calcMonthWorkRecord(safeSt, serviceSt)
	for i := range driverRunRecords {
		report.RunDay += driverRunRecords[i].RunDay
		report.AvgPlanDay += driverRunRecords[i].AvgPlanDay
		report.SafeWorkCutMoney += driverRunRecords[i].SafeCutMoney
		report.ServiceWorkCutMoney += driverRunRecords[i].ServiceCutMoney
	}
	var assessRate float64
	if report.AvgPlanDay > 0 {
		assessRate = decimal.NewFromInt(report.RunDay).Div(decimal.NewFromInt(report.AvgPlanDay)).Round(4).InexactFloat64()
	}

	if assessRate > 1 {
		assessRate = 1
	}

	report.AssessRate = decimal.NewFromFloat(assessRate).Mul(decimal.NewFromInt(10000)).IntPart()

	//事假记录
	casualLeaveRecords := r.calcMonthCasualLeaveRecord()
	var totalLeaveDay int64
	for i := range casualLeaveRecords {
		totalLeaveDay += casualLeaveRecords[i].LeaveDay
	}

	var safeCasualLeaveRate float64
	var serviceCasualLeaveRate float64
	if totalLeaveDay >= 30 && totalLeaveDay < 60 {
		safeCasualLeaveRate = 0.5
		serviceCasualLeaveRate = 0.5
	}
	if totalLeaveDay >= 60 && totalLeaveDay < 90 {
		safeCasualLeaveRate = 0.8
		serviceCasualLeaveRate = 0.8
	}
	if totalLeaveDay >= 90 {
		safeCasualLeaveRate = 1
		serviceCasualLeaveRate = 1
	}
	//安全事假
	report.SafeCasualLeaveRate = decimal.NewFromFloat(safeCasualLeaveRate).Mul(decimal.NewFromInt(10000)).IntPart()
	report.SafeCasualLeaveCutMoney = decimal.NewFromInt(safeSt.BaseMoney).Mul(decimal.NewFromFloat(assessRate)).Mul(decimal.NewFromFloat(safeCasualLeaveRate)).IntPart()
	//服务事假
	report.ServiceCasualLeaveRate = decimal.NewFromFloat(serviceCasualLeaveRate).Mul(decimal.NewFromInt(10000)).IntPart()
	report.ServiceCasualLeaveCutMoney = decimal.NewFromInt(serviceSt.BaseMoney).Mul(decimal.NewFromFloat(assessRate)).Mul(decimal.NewFromFloat(serviceCasualLeaveRate)).IntPart()

	//违规记录
	violationRecords := r.calcYearViolationRecord(assessRate, safeSt, serviceSt, thirdSt)
	for i := range violationRecords {
		if violationRecords[i].ViolationCateAttr == util.TrafficViolationCateAttrForService {
			report.ServiceViolationCutMoney += violationRecords[i].CutMoney
		} else {
			report.SafeViolationCutMoney += violationRecords[i].CutMoney
		}

		if violationRecords[i].ViolationOrigin == util.TrafficViolationOriginForThirdAssess {
			report.ThirdPartAssessCutMoney += violationRecords[i].ThirdAssessCutMoney
		}
	}

	//事故记录
	accidentRecords := r.calcYearAccidentRecord()
	var assessAccidents []assess.DriverAccidentYearAssessRecord
	for i := range accidentRecords {
		report.AccidentCutMoney += accidentRecords[i].CutMoney
		if accidentRecords[i].AccidentCheckResult != util.TrafficAccidentCheckHandleResultForNot {
			assessAccidents = append(assessAccidents, accidentRecords[i])
		}
	}

	//计算安全公里
	report.SafeMileage, report.SafeMileagePrice = r.calcYearSafeMileage(assessAccidents)
	report.SafeMileage = decimal.NewFromInt(report.SafeMileage).Div(decimal.NewFromInt(1000)).Mul(decimal.NewFromInt(report.SafeMileagePrice).Div(decimal.NewFromInt(100))).Round(2).Mul(decimal.NewFromInt(100)).IntPart()

	//安全总考核奖金
	report.SafeAssessReward = decimal.NewFromInt(safeSt.BaseMoney).
		Mul(decimal.NewFromFloat(assessRate)).
		Sub(decimal.NewFromInt(report.SafeWorkCutMoney)).
		Sub(decimal.NewFromInt(report.SafeCasualLeaveCutMoney)).
		Sub(decimal.NewFromInt(report.SafeViolationCutMoney)).
		Sub(decimal.NewFromInt(report.AccidentCutMoney)).
		IntPart()
	if report.SafeAssessReward < 0 {
		report.SafeAssessReward = 0
	}

	//服务总考核奖金
	report.ServiceAssessReward = decimal.NewFromInt(serviceSt.BaseMoney).
		Mul(decimal.NewFromFloat(assessRate)).
		Sub(decimal.NewFromInt(report.ServiceWorkCutMoney)).
		Sub(decimal.NewFromInt(report.ServiceCasualLeaveCutMoney)).
		Sub(decimal.NewFromInt(report.ServiceViolationCutMoney)).
		IntPart()
	if report.ServiceAssessReward < 0 {
		report.ServiceAssessReward = 0
	}

	//三方测评总考核奖金
	report.ThirdPartAssessReward = decimal.NewFromInt(thirdSt.BaseMoney).
		Mul(decimal.NewFromFloat(assessRate)).
		Sub(decimal.NewFromInt(report.ThirdPartAssessCutMoney)).
		IntPart()
	if report.ThirdPartAssessReward < 0 {
		report.ThirdPartAssessReward = 0
	}

	tx := model.DB().Begin()
	err = report.Create(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("DriverSafeAssessReport DriverYearAssessReport error", map[string]interface{}{"err": err})
		return
	}
	for i := range driverRunRecords {
		driverRunRecords[i].ReportId = report.Id
	}
	err = (&assess.DriverMonthWorkRecord{}).Create(tx, driverRunRecords)
	if err != nil {
		log.ErrorFields("DriverSafeAssessReport DriverMonthWorkRecord error", map[string]interface{}{"err": err})
	}

	for i := range casualLeaveRecords {
		casualLeaveRecords[i].ReportId = report.Id
	}
	err = (&assess.DriverCasualLeaveRecord{}).Create(tx, casualLeaveRecords)
	if err != nil {
		log.ErrorFields("DriverSafeAssessReport DriverCasualLeaveRecord error", map[string]interface{}{"err": err})
	}

	for i := range violationRecords {
		violationRecords[i].ReportId = report.Id
	}
	err = (&assess.DriverViolationYearAssessRecord{}).Create(tx, violationRecords)
	if err != nil {
		log.ErrorFields("DriverSafeAssessReport DriverViolationYearAssessRecord error", map[string]interface{}{"err": err})
	}

	for i := range accidentRecords {
		accidentRecords[i].ReportId = report.Id
	}
	err = (&assess.DriverAccidentYearAssessRecord{}).Create(tx, accidentRecords)
	if err != nil {
		log.ErrorFields("DriverSafeAssessReport DriverAccidentYearAssessRecord error", map[string]interface{}{"err": err})
	}

	tx.Commit()
}

// 计算司机每月出勤考核
func (r *DriverSafeAssessReport) calcMonthWorkRecord(safeSt settingModel.GlobalSettingItemForViolationYearAssessItem, serviceSt settingModel.GlobalSettingItemForServiceYearAssessItem) []assess.DriverMonthWorkRecord {
	var records []assess.DriverMonthWorkRecord
	for month := 1; month <= 12; month++ {
		endAt := time.Date(int(r.Year), time.Month(month), 25, 23, 59, 59, 999, time.Local)
		start := endAt.AddDate(0, -1, 0)
		startAt := time.Date(start.Year(), start.Month(), 26, 0, 0, 0, 0, time.Local)
		//主营线路
		mainLine := service.GetDriverMainRunLineInfo(r.Staff.StaffId, startAt, endAt)
		//计算天数 （实际工作天数）
		runDay := (&operation.LineVehicleMileageReport{}).GetDriverRunCountDataGroupByReportAt(util.StatusForTrue, []int64{}, 0, 0, r.Staff.StaffId, 0, startAt, endAt)
		//平均计划天数（基准天数）=主营线路总计划天数/计划司机数量
		planDay := (&operation.PlanScheduleReport{}).GetDriverFrequencyIndexCount(mainLine.CorporationId, mainLine.LineId, 0, startAt, endAt)
		planDriverCount := (&operation.PlanScheduleReport{}).GetPlanDriverCount(mainLine.CorporationId, mainLine.LineId, startAt, endAt)
		var avgPlanDay float64
		if planDriverCount > 0 {
			avgPlanDay = decimal.NewFromInt(planDay).Div(decimal.NewFromInt(planDriverCount)).Round(2).InexactFloat64()
		}
		if avgPlanDay > 22 {
			avgPlanDay = 22
		}
		//计算系数
		var assessRate float64
		if avgPlanDay > 0 {
			assessRate = decimal.NewFromInt(runDay).Div(decimal.NewFromFloat(avgPlanDay).Mul(decimal.NewFromInt(10))).Round(4).InexactFloat64()
		}
		if assessRate > 1 {
			assessRate = 1
		}
		monthStr := endAt.Format("200601")
		reportMonth, _ := strconv.ParseInt(monthStr, 10, 64)
		record := assess.DriverMonthWorkRecord{
			StaffId:      r.Staff.StaffId,
			StaffName:    r.Staff.StaffName,
			MainLineId:   mainLine.LineId,
			MainLineName: mainLine.LineName,
			ReportMonth:  reportMonth,
			StartAt:      model.LocalTime(startAt),
			EndAt:        model.LocalTime(endAt),
			RunDay:       runDay * 10, //runDay在线路公里明细表中已经*10 在这里只需*10
			AvgPlanDay:   decimal.NewFromFloat(avgPlanDay).Mul(decimal.NewFromInt(100)).IntPart(),
			AssessRate:   decimal.NewFromFloat(assessRate).Mul(decimal.NewFromInt(10000)).IntPart(),
		}

		if assessRate < 1 {
			record.SafeCutMoney = safeSt.LessThanDayDeductMoney
			record.ServiceCutMoney = serviceSt.LessThanDayDeductMoney
		}

		records = append(records, record)
	}

	return records
}

// 计算每月事假记录
func (r *DriverSafeAssessReport) calcMonthCasualLeaveRecord() []assess.DriverCasualLeaveRecord {
	var records []assess.DriverCasualLeaveRecord
	for month := 1; month <= 12; month++ {
		endAt := time.Date(int(r.Year), time.Month(month), 25, 23, 59, 59, 999, time.Local)
		start := endAt.AddDate(0, -1, 0)
		startAt := time.Date(start.Year(), start.Month(), 26, 0, 0, 0, 0, time.Local)
		monthStr := endAt.Format("200601")
		reportMonth, _ := strconv.ParseInt(monthStr, 10, 64)
		var record = assess.DriverCasualLeaveRecord{
			StaffId:     r.Staff.StaffId,
			StaffName:   r.Staff.StaffName,
			ReportMonth: reportMonth,
			StartAt:     model.LocalTime(startAt),
			EndAt:       model.LocalTime(endAt),
			LeaveDay:    (&hrModel.ApplyLeaveRecord{}).StaffLeaveDay(r.Staff.StaffId, util.LeaveTypeForCasualLeave, startAt, endAt),
		}

		records = append(records, record)
	}

	return records
}

// 计算年违规记录(安全、服务、三方测评)
func (r *DriverSafeAssessReport) calcYearViolationRecord(assessRate float64,
	safeSt settingModel.GlobalSettingItemForViolationYearAssessItem,
	serviceSt settingModel.GlobalSettingItemForServiceYearAssessItem,
	thirdSt settingModel.GlobalSettingItemForServiceThirdPartyAssess) []assess.DriverViolationYearAssessRecord {
	//查询安全违规记录
	violations := (&safetyModel.TrafficViolation{}).GetDriverAllViolation(r.Staff.StaffId, 0, r.StartAt, r.EndAt)
	var categoryViolationMap = make(map[int64][]safetyModel.QualityAssessmentStandards)
	for _, violation := range violations {
		var standards []safetyModel.QualityAssessmentStandards
		err := json.Unmarshal(violation.Standards, &standards)
		if err != nil {
			continue
		}
		for i := range standards {
			standards[i].ViolationId = violation.Id
			standards[i].ViolationOrigin = violation.Origin
			standards[i].ViolationReportAt = violation.ReportAt.ToTime().Unix()
			categoryViolationMap[standards[i].CategoryId] = append(categoryViolationMap[standards[i].CategoryId], standards[i])
		}
	}

	var settingMap = make(map[int64]settingModel.GlobalSettingItemForYearAssessArr)
	for i := range safeSt.Settings {
		settingMap[safeSt.Settings[i].ViolationCategoryId] = safeSt.Settings[i]
	}

	var serviceSettingMap = make(map[int64]settingModel.GlobalSettingItemForYearAssessArr)
	for i := range serviceSt.Settings {
		serviceSettingMap[serviceSt.Settings[i].ViolationCategoryId] = serviceSt.Settings[i]
	}

	var thirdSettingMap = make(map[int64]settingModel.GlobalSettingItemForYearAssessArr)
	for i := range thirdSt.Settings {
		thirdSettingMap[thirdSt.Settings[i].ViolationCategoryId] = thirdSt.Settings[i]
	}

	var records []assess.DriverViolationYearAssessRecord
	for categoryId, standards := range categoryViolationMap {
		//将违规的标准按照违规发生时间升序排
		sort.Slice(standards, func(i, j int) bool {
			return standards[i].ViolationReportAt < standards[j].ViolationReportAt
		})

		//查询分类的顶级分类ID
		category := (&safetyModel.ViolationCategory{}).FirstById(categoryId)
		if category.ParentId > 0 {
			category = (&safetyModel.ViolationCategory{}).FirstById(category.ParentId)
			if category.ParentId > 0 {
				category = (&safetyModel.ViolationCategory{}).FirstById(category.ParentId)
			}
		}

		cate := (&safetyModel.QualityAssessmentCate{}).FindBy(category.QualityAssessmentCateId)
		var cutMoney int64
		var cateAttr int64
		if cate.Attr == util.QualityAssessmentCateAttrForService {
			cateAttr = util.TrafficViolationCateAttrForService
		} else {
			cateAttr = util.TrafficViolationCateAttrForSafe
		}

		if cateAttr == util.TrafficViolationCateAttrForService {
			if v, ok := serviceSettingMap[category.Id]; ok {
				if v.CheckType == util.ViolationAssessCheckTypeForPercent {
					//基数*系数*比例
					cutMoney = decimal.NewFromInt(serviceSt.BaseMoney).Mul(decimal.NewFromFloat(assessRate)).Mul(decimal.NewFromInt(v.Value).Div(decimal.NewFromInt(10000))).IntPart()
				} else {
					cutMoney = v.Value
				}
			}
		} else {
			if v, ok := settingMap[category.Id]; ok {
				if v.CheckType == util.ViolationAssessCheckTypeForPercent {
					//基数*系数*比例
					cutMoney = decimal.NewFromInt(safeSt.BaseMoney).Mul(decimal.NewFromFloat(assessRate)).Mul(decimal.NewFromInt(v.Value).Div(decimal.NewFromInt(10000))).IntPart()
				} else {
					cutMoney = v.Value
				}
			}
		}

		for _, sd := range standards {
			record := assess.DriverViolationYearAssessRecord{
				StaffId:           r.Staff.StaffId,
				ReportYear:        r.Year,
				StartAt:           model.LocalTime(r.StartAt),
				EndAt:             model.LocalTime(r.EndAt),
				ViolationId:       sd.ViolationId,
				ViolationCateAttr: cateAttr,
				ViolationOrigin:   sd.ViolationOrigin,
				ReportAt:          model.LocalTime(time.Unix(sd.ViolationReportAt, 0)),
				CategoryId:        categoryId,
				CategoryTopId:     category.Id,
				StandardId:        sd.Id,
				CutMoney:          cutMoney,
			}

			if cateAttr == util.TrafficViolationCateAttrForService && sd.ViolationOrigin == util.TrafficViolationOriginForThirdAssess {
				if v, ok := thirdSettingMap[category.Id]; ok {
					if v.CheckType == util.ViolationAssessCheckTypeForPercent {
						//基数*系数*比例
						record.ThirdAssessCutMoney = decimal.NewFromInt(thirdSt.BaseMoney).Mul(decimal.NewFromFloat(assessRate)).Mul(decimal.NewFromInt(v.Value).Div(decimal.NewFromInt(10000))).IntPart()
					} else {
						record.ThirdAssessCutMoney = v.Value
					}
				}
			}
			records = append(records, record)
		}
	}
	return records
}

// 计算事故记录
func (r *DriverSafeAssessReport) calcYearAccidentRecord() []assess.DriverAccidentYearAssessRecord {
	//查询司机所有事故
	accidents := (&safetyModel.TrafficAccident{}).GetDriverAccident(r.Staff.StaffId, r.StartAt, r.EndAt)
	//查询年考核配置  计算扣款金额
	setting := (&settingModel.GlobalSetting{}).GetBy(config.Config.TopCorporationId, util.GlobalSettingTypeForSafeYearAssess)
	var settingItem settingModel.GlobalSettingItemForViolationYearAssess
	err := json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
	}

	st := settingItem.Line
	if r.Staff.IsCctDriver {
		st = settingItem.CctLine
	}

	var records []assess.DriverAccidentYearAssessRecord
	for i := range accidents {
		record := assess.DriverAccidentYearAssessRecord{
			StaffId:             r.Staff.StaffId,
			ReportYear:          r.Year,
			StartAt:             model.LocalTime(r.StartAt),
			EndAt:               model.LocalTime(r.EndAt),
			AccidentId:          accidents[i].Id,
			HappenAt:            accidents[i].HappenAt,
			AccidentCheckResult: accidents[i].CheckHandleResult,
		}

		if accidents[i].LiabilityType == util.AccidentLiabilityTypeForNull || accidents[i].CheckHandleResult == util.TrafficAccidentCheckHandleResultForNot {
			record.CutMoney = 0
		} else {
			record.CutMoney = st.AccidentDeductMoney
		}

		records = append(records, record)
	}

	return records
}

// 计算安全公里
func (r *DriverSafeAssessReport) calcYearSafeMileage(accidents []assess.DriverAccidentYearAssessRecord) (int64, int64) {
	//线路公里明细表公里
	mileage1 := (&operation.LineVehicleMileageReport{}).GetDriverRunMileage(util.StatusForTrue, r.Staff.StaffId, r.StartAt, r.EndAt)
	//班制外加班明细表公里
	mileage2 := (&operation.OutFrequencyAddWorkReport{}).GetDriverTotalMileage(util.StatusForTrue, r.Staff.StaffId, r.StartAt, r.EndAt)
	//定制线路明细表的运营公里+自定义公里
	mileage3 := (&operation.IrregularLineReport{}).GetDriverTotalMileage(util.StatusForTrue, r.Staff.StaffId, r.StartAt, r.EndAt)

	//查询安全公里考核配置
	setting := (&settingModel.GlobalSetting{}).GetBy(config.Config.TopCorporationId, util.GlobalSettingTypeForSafeMileage)
	var settingItem settingModel.GlobalSettingItemForSafeMileageAssess
	err := json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
	}

	st := settingItem.Line
	if r.Staff.IsCctDriver {
		st = settingItem.CctLine
	}

	sort.SliceStable(st, func(i, j int) bool {
		return st[i].AccidentCount < st[j].AccidentCount
	})

	var mileageMoney int64
	if len(st) > 0 {
		if int64(len(accidents)) >= st[len(st)-1].AccidentCount {
			mileageMoney = st[len(st)-1].KmMoney
		} else {
			if len(accidents) == 0 {
				for i := range st {
					if st[i].AccidentCount == int64(len(accidents)) {
						mileageMoney = st[i].KmMoney
						break
					}
				}
			} else {
				for i := range st {
					if st[i].AccidentCount == int64(len(accidents)) {
						var isLess bool
						for j := range accidents {
							if accidents[j].AccidentCheckResult == util.TrafficAccidentCheckHandleResultForLess {
								isLess = true
								break
							}
						}
						if (isLess && st[i].IsReduce == util.StatusForTrue) || (!isLess && st[i].IsReduce == util.StatusForFalse) {
							mileageMoney = st[i].KmMoney
						}
					}
				}
			}
		}
	}

	return mileage1 + mileage2 + mileage3, mileageMoney
}
