package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	protoschedule "app/org/scs/erpv2/api/proto/rpc/iss"
	"app/org/scs/erpv2/api/service/message"
	"app/org/scs/erpv2/api/service/rpc"
	"context"
	"errors"
	"time"
)

// 每天 2点 预生成当天门检记录
// TODO 写入失败通知， 失败后记录车牌号，提供手动写入
//

func PreGenerateDoorCheckRecordHandler() ([]string, error) {
	var errLicenses []string
	now := time.Now()
	s := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	e := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 9999, time.Local)

	topCorpId := config.Config.TopCorporationId
	if topCorpId == 0 {
		log.Error("topCorpId == 0")
		return nil, errors.New("topCorpId == 0")
	}

	iss := config.Config.IsRelationIss

	// 获取机构下所有车辆
	vehicleItems, _ := rpc.GetVehiclesWithTopCorporationId(context.Background(), topCorpId)

	// 有调度系统： 线路 司机 字段值根据调度系统
	// 无调度系统： 线路 司机 字段值使用车属线路，车辆关联司机

	for _, item := range vehicleItems {

		// 出场门检记录
		outRecord := safetyModel.DoorCheckRecords{
			PkId:                  model.PkId{Id: model.Id()},
			GroupCorporation:      model.GroupCorporation{GroupId: topCorpId},
			CompanyCorporation:    model.CompanyCorporation{},
			BranchCorporation:     model.BranchCorporation{},
			DepartmentCorporation: model.DepartmentCorporation{},
			FleetCorporation:      model.FleetCorporation{},
			License:               item.License,
			VehicleCode:           item.Code,
			LineId:                0,
			Line:                  "",
			Type:                  1,
			CheckForm:             1,
			Result:                0,
			StatusCount:           "0,0,0,0,0",
			HandleStatus:          safetyModel.PRE_GENERATE_1,
			Schedule:              safetyModel.NO_SCHEDULE_2,
			Timestamp:             model.Timestamp{},
		}

		// 回场门检记录
		backRecord := safetyModel.DoorCheckRecords{
			PkId:                  model.PkId{Id: model.Id()},
			GroupCorporation:      model.GroupCorporation{GroupId: topCorpId},
			CompanyCorporation:    model.CompanyCorporation{},
			BranchCorporation:     model.BranchCorporation{},
			DepartmentCorporation: model.DepartmentCorporation{},
			FleetCorporation:      model.FleetCorporation{},
			License:               item.License,
			VehicleCode:           item.Code,
			LineId:                0,
			Line:                  "",
			Type:                  1,
			CheckForm:             2,
			Result:                0,
			StatusCount:           "0,0,0,0,0",
			HandleStatus:          safetyModel.PRE_GENERATE_1,
			Schedule:              safetyModel.NO_SCHEDULE_2,
			Timestamp:             model.Timestamp{},
		}

		if iss {
			firstAndLast := rpc.GetFirstLastPlanScheduleWithVehicleId(context.Background(), &protoschedule.GetFirstLastPlanScheduleWithVehicleIdRequest{
				VehicleId:         item.Id,
				ExpectDepartAtGte: s.Unix(),
				ExpectDepartAtLte: e.Unix(),
				Status:            0,
				PlanSchType:       -1,
			})

			if firstAndLast != nil {
				outRecord.Schedule = safetyModel.YES_SCHEDULE_1
				backRecord.Schedule = safetyModel.YES_SCHEDULE_1
			}

			for _, idItem := range firstAndLast {
				if idItem.Type == 1 { // 早班
					outRecord.StaffId = idItem.PlanDriverId // 计划司机

					staff := rpc.GetStaffWithId(context.Background(), outRecord.StaffId)
					if staff != nil {
						outRecord.StaffName = staff.Name
					}

					outRecord.LineId = idItem.LineId

				} else if idItem.Type == 2 { // 晚班
					backRecord.StaffId = idItem.PlanDriverId

					staff := rpc.GetStaffWithId(context.Background(), backRecord.StaffId)
					if staff != nil {
						backRecord.StaffName = staff.Name
					}

					backRecord.LineId = idItem.LineId

				}
			}

		}

		// 存数据库
		err := (&safetyModel.DoorCheckRecords{}).Adds([]safetyModel.DoorCheckRecords{outRecord, backRecord})
		if err != nil {
			log.Error("DoorCheckRecords Adds err ==", err)
			errLicenses = append(errLicenses, item.License)
			continue
		}
	}
	return errLicenses, nil

}

func PreGenerateDoorCheckRecord() {
	errLicenses, err := PreGenerateDoorCheckRecordHandler()
	if err != nil {
		_ = message.NewAlarm("定时任务-预生成门检记录，发生错误终止").Send()
	}
	if len(errLicenses) > 0 {
		_ = message.NewAlarm("定时任务-预生成门检记录，有车辆数据未成功生成").Send()
	}
}
