package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	lineDriverModel "app/org/scs/erpv2/api/model/lineDriver"
	protoLine "app/org/scs/erpv2/api/proto/rpc/oetline"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"time"
)

func LineHasDriverRecordByDay() {
	staffs := rpc.GetStaffsWithCorporationId(context.Background(), config.Config.TopCorporationId, "")

	var lineInfoMap = make(map[int64]*protoLine.OetLineItem)
	nowDay := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local)

	var records []lineDriverModel.LineHasDriver
	for i := range staffs {
		if util.MasterToErp[staffs[i].Occupation] == util.WorkPostType_3 && staffs[i].LineId > 0 {
			record := lineDriverModel.LineHasDriver{
				LineId:      staffs[i].LineId,
				LineName:    "",
				DriverId:    staffs[i].Id,
				DriverName:  staffs[i].Name,
				DriverPhone: staffs[i].Phone,
				EndAt:       model.LocalTime(nowDay),
			}

			if _, ok := lineInfoMap[staffs[i].LineId]; ok {
				record.LineName = lineInfoMap[staffs[i].LineId].Name
			} else {
				line, _ := rpc.GetLineWithId(context.Background(), staffs[i].LineId)
				if line != nil {
					record.LineName = line.Name
					lineInfoMap[staffs[i].LineId] = line
				}
			}

			record.Build(staffs[i].CorporationId)
			records = append(records, record)
		}
	}

	for i := range records {
		record := (&lineDriverModel.LineHasDriver{}).LatestFirstBy(records[i].LineId, records[i].DriverId)
		if record.Id > 0 {
			records[i].Id = record.Id
			err := records[i].Updates()
			if err != nil {
				log.ErrorFields("LineHasDriver Updates error", map[string]interface{}{"err": err})
			}
		} else {
			records[i].StartAt = model.LocalTime(nowDay)
			err := records[i].Create()
			if err != nil {
				log.ErrorFields("LineHasDriver Create error", map[string]interface{}{"err": err})
			}
		}
	}
}
