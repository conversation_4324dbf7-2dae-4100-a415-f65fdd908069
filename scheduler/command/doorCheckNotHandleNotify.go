package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/handler/safety/doorcheck"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model/safety"
	protocorporation "app/org/scs/erpv2/api/proto/rpc/corporation"
	protooetvehicle "app/org/scs/erpv2/api/proto/rpc/oetvehicle"
	protoUser "app/org/scs/erpv2/api/proto/rpc/user"
	"app/org/scs/erpv2/api/service/message"
	"app/org/scs/erpv2/api/service/rpc"
	"context"
	"fmt"
	"strings"
	"time"
)

// 查询门检异常未解决项 发送消息给今天车队长

func SendDoorCheckNotHandleNotify() {
	// 查询 昨天和前天 未处理的异常
	now := time.Now()
	todayStartAt := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)                // 今天 0 点
	todayEndAt := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, 1) // 明天 0 点

	startAt := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, -2)
	endAt := todayStartAt

	results, err := (&safety.DoorCheckItemResult{}).GetWithTimeStatus2(startAt, endAt)
	if err != nil {
		log.Error("GetWithTimeStatus2 err=", err)
	}

	mini := config.Config.Mini
	rpcTmpOetUser := make(map[int64]*protoUser.MainUser)                               // map[userId]
	rpcTmpVehicle := make(map[string]*protooetvehicle.OetVehicleItem)                  // map[License]
	rpcTmpDetail := make(map[int64]*protocorporation.GetCorporationDetailByIdResponse) // map[corpId]
	rpcTmpInspector := make(map[int64][]doorcheck.InspectorInfo)                       // map[fleetId]
	rpcTmpUserName := make(map[int64][]string)                                         // map[fleetId]

	// 查询今天值班车队长
	for _, result := range results {

		var vehicle *protooetvehicle.OetVehicleItem
		var detailById *protocorporation.GetCorporationDetailByIdResponse

		if vehicleItem, ok := rpcTmpVehicle[result.License]; ok {
			vehicle = vehicleItem
		} else {
			// 获取车辆归属车队值班车队长
			oetVehicleItem := rpc.GetVehicleWithLicense(context.Background(), &protooetvehicle.GetVehicleWithLicenseRequest{
				License:       result.License,
				CorporationId: result.GroupId,
			})

			if oetVehicleItem == nil {
				log.Error("rpc.GetVehicleWithLicense vehicleItem == nil", err)
				continue
			} else {
				rpcTmpVehicle[result.License] = oetVehicleItem
				vehicle = vehicleItem
			}
		}

		if detail, ok := rpcTmpDetail[vehicle.SonCorporationId]; ok {
			detailById = detail
		} else {
			d := rpc.GetCorporationDetailById(context.Background(), vehicle.SonCorporationId)
			if d == nil {
				log.Error("rpc.GetCorporationDetailById detailById == nil, corpId=", vehicle.SonCorporationId)
				continue
			} else {
				rpcTmpDetail[vehicle.SonCorporationId] = d
				detailById = d
			}
		}

		var inspectorList []doorcheck.InspectorInfo
		var nameList []string

		if iList, ok := rpcTmpInspector[detailById.FleetId]; ok {
			inspectorList = iList
			nameList = rpcTmpUserName[detailById.FleetId]
		} else {
			var inspector safety.DoorCheckInspectors
			inspector.FleetId = detailById.FleetId
			list, err := (&inspector).List(todayStartAt, todayEndAt)
			if err != nil {
				log.Error("List err=", err.Error())
				continue
			}
			if list == nil {
				log.Error("List == nil")
				continue
			} else {

				// 过滤重复人员
				tmpUser := make(map[int64]byte) // map[人员id]

				for _, i := range list {

					if _, ok := tmpUser[i.UserId]; ok {
						continue
					} else {
						tmpUser[i.UserId] = 0
					}

					var user *protoUser.MainUser

					// 获取车队长手机号
					if s, ok := rpcTmpOetUser[i.UserId]; ok {
						user = s
					} else {
						sta := rpc.GetUserInfoById(context.Background(), i.UserId)
						if sta == nil || sta.Phone == "" {
							log.Error("sta == nil || sta.Phone == '' sta == ", sta)
							continue
						} else {
							rpcTmpOetUser[i.UserId] = sta
							user = sta
						}
					}

					inspectorList = append(inspectorList, doorcheck.InspectorInfo{
						DoorCheckInspectors: i,
						Phone:               user.Phone,
					})
					nameList = append(nameList, i.Name)

				}

				rpcTmpInspector[detailById.FleetId] = inspectorList
				rpcTmpUserName[detailById.FleetId] = nameList
			}
		}

		for _, info := range inspectorList {
			pagePath := fmt.Sprintf(`%s?Id=%d&ItemId=%d&NoticeId=%d&Type=%d`, mini.Page, result.FkRecordId, result.FkItemId, 0, doorcheck.ABNORMAL_1)

			message.NewOfficialAccountMsg(
				"台州公交车辆检查通知",
				[]string{
					fmt.Sprintf(`[异常处理] %s，%s，%s`, result.Line, result.License, result.StaffName),
					fmt.Sprintf(`%s`, strings.Join(nameList, ",")),
				},
				"请尽快进行处理",
				pagePath,
				info.Phone,
			).Send()

		}

	}

}
