package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"fmt"
	"time"
)

func SyncMasterStaffToErpArchive() {
	if !config.Config.SyncMasterStaff.Enable || len(config.Config.SyncMasterStaff.CorporationIds) == 0 {
		return
	}

	for _, corporationId := range config.Config.SyncMasterStaff.CorporationIds {
		oetStaffs := rpc.GetStaffsWithCorporationId(context.Background(), corporationId, "")
		if len(oetStaffs) == 0 {
			return
		}

		for i := range oetStaffs {

			var archive hrModel.StaffArchive
			err := archive.FindByStaffId(oetStaffs[i].Id)
			if archive.Id > 0 {
				continue
			}

			corporation := rpc.GetCorporationDetailById(context.Background(), oetStaffs[i].CorporationId)
			if corporation != nil {
				archive.GroupId = corporation.GroupId
				archive.CompanyId = corporation.CompanyId
				archive.BranchId = corporation.BranchId
				archive.DepartmentId = corporation.DepartmentId
				archive.FleetId = corporation.FleetId
			}

			archive.StaffId = oetStaffs[i].Id
			if oetStaffs[i].IdentifyId != "" && len(oetStaffs[i].IdentifyId) > 14 {
				date := []byte(oetStaffs[i].IdentifyId)[6:14]
				birthDate, _ := time.ParseInLocation("20060102", string(date), time.Local)
				archive.Age = util.GetAge(birthDate)
			}

			err = archive.UpdateOrCreate(map[string]bool{})
			if err != nil {
				log.ErrorFields("SyncMasterStaffToErpArchive error", map[string]interface{}{"err": err, "archive": archive})
			}
		}
	}
}

func FixHistoryQuitStaff() {
	fmt.Println("FixHistoryQuitStaff START")

	oetStaffs := rpc.GetStaffsWithCorporationId(context.Background(), config.Config.TopCorporationId, "")

	quitRecords := (&hrModel.StaffQuitRecord{}).GetAll()
	var quitMap = make(map[int64]hrModel.StaffQuitRecord)
	for i := range quitRecords {
		quitMap[quitRecords[i].StaffId] = quitRecords[i]
	}

	var records []hrModel.StaffQuitRecord
	for i := range oetStaffs {
		if oetStaffs[i].WorkingState != 2 {
			continue
		}

		if _, ok := quitMap[oetStaffs[i].Id]; ok {
			continue
		}

		at, _ := time.ParseInLocation("2006-01-02", "2023-05-10", time.Local)
		atL := model.LocalTime(at)
		var archive hrModel.StaffArchive
		_ = archive.FindByStaffId(oetStaffs[i].Id)
		var workPostName string
		var workPostId int64
		if len(archive.WorkPosts) > 0 {
			workPostName = archive.WorkPosts[0].WorkPostName
			workPostId = archive.WorkPosts[0].Id
		}
		var joinAt model.LocalTime
		if oetStaffs[i].RegisterTime > 0 {
			joinAt = model.LocalTime(time.Unix(oetStaffs[i].RegisterTime, 0))
		}
		records = append(records, hrModel.StaffQuitRecord{
			WorkPostName:        workPostName,
			WorkPostId:          workPostId,
			StaffArchiveId:      archive.Id,
			StaffId:             oetStaffs[i].Id,
			RelieveContractType: 1,
			RelieveContractAt:   &atL,
			ApplyResult:         1,
			ApplyStatus:         2,
			JoinCompanyAt:       &joinAt,
		})
	}

	if len(records) > 0 {
		err := (&hrModel.StaffQuitRecord{}).BatchCreate(records)
		fmt.Printf("FixHistoryQuitStaff StaffQuitRecord:%+v \n", err)
	}
	fmt.Println("FixHistoryQuitStaff END")

}
