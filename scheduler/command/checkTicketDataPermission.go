package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	ticketModel "app/org/scs/erpv2/api/model/ticket"
	"app/org/scs/erpv2/api/util"
	"time"
)

// CheckTicketDataPermission 每分钟检查更新票务数据权限状态
func CheckTicketDataPermission() {
	log.Info("ticketDataPermission scheduler start!")
	// 读取数据库中【进行中】的票务数据权限记录【ticketDataPermissionRecord】
	// 可以优化为只读取对应GroupId的票务数据权限记录

	permissionRecords, _, _ := (&ticketModel.TicketDataPermissionRecord{}).GetDoing()

	var (
		updateIds []int64
	)

	hasPermission := util.StatusForFalse // 此刻是否持有票务数据权限权限

	now := time.Now()
	for _, record := range permissionRecords {

		s := time.Time(record.StartAt)
		e := time.Time(record.EndAt)

		s = time.Date(s.Year(), s.Month(), s.Day(), s.Hour(), s.Minute(), s.Second(), s.Nanosecond(), time.Local)
		e = time.Date(e.Year(), e.Month(), e.Day(), e.Hour(), e.Minute(), e.Second(), e.Nanosecond(), time.Local)

		// 开始时间 <= 当前时间 <= 结束时间  ===>  进行中
		if now.Sub(s) >= 0 && e.Sub(now) >= 0 {
			// 进行中
			hasPermission = util.StatusForTrue
			continue
		} else {
			// 失效
			updateIds = append(updateIds, record.Id)
		}
	}

	// 修改记录状态
	if len(updateIds) > 0 {
		err := (&ticketModel.TicketDataPermissionRecord{}).BatchToDone(updateIds)
		if err != nil {
			log.Error("BatchToDone error =", err)
			return
		}
	}
	if config.Config.TopCorporationId == 0 {
		log.Error("config.Config.TopCorporationId == 0 scheduler failed")
		return
	}

	// 更新票务数据权限
	err := (&ticketModel.TicketDataPermission{}).UpdatePermission(config.Config.TopCorporationId, hasPermission)
	if err != nil {
		log.Error("UpdatePermission error =", err)
		return
	}
	log.Info("ticketDataPermission scheduler end!")
}
