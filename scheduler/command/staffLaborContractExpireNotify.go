package command

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	messageModel "app/org/scs/erpv2/api/model/message"
	schedulerModel "app/org/scs/erpv2/api/model/scheduler"
	"app/org/scs/erpv2/api/service"
	messageService "app/org/scs/erpv2/api/service/message"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"
)

type LaborContractStaffInfo struct {
	StaffId         int64
	Name            string
	JobNumber       string
	Phone           string
	LaborContractId int64
	EndAt           model.LocalTime
	CorporationId   int64
}

type StaffContract struct {
	SendStaffs    []LaborContractStaffInfo
	ReceiveStaffs []LaborContractStaffInfo
}

type CanNotifyContract struct {
	sync.RWMutex
	Records map[string][]StaffContract
}

var CanNotifyContractRecord CanNotifyContract

func (c *CanNotifyContract) Read(key string) ([]StaffContract, bool) {
	c.RLock()
	value, ok := c.Records[key]
	c.RUnlock()
	return value, ok
}

func (c *CanNotifyContract) Write(key string, value []StaffContract) {
	c.Lock()
	if c.Records == nil {
		c.Records = make(map[string][]StaffContract)
	}
	c.Records[key] = value
	ddd, _ := json.Marshal(c.Records)
	fmt.Printf("CanNotifyContract Records==========%+v \n", string(ddd))
	c.Unlock()
}

func (c *CanNotifyContract) Remove(key string) {
	c.RLock()
	delete(c.Records, key)
	c.RUnlock()
}

type SchedulerLaborContractParam struct {
	WorkPostTypeArr []int64
	CorporationIds  []int64
	WorkPostIds     []int64
}

func GetCanNotifyContract() {
	fmt.Printf("GetCanNotifyContract start!! \n")
	var scheduler schedulerModel.Scheduler
	schedulers := scheduler.GetByScene((&hrModel.StaffLaborContract{}).ExpireMessageType())
	if len(schedulers) == 0 {
		return
	}

	var notifyRecords = make(map[string][]StaffContract)
	for i := range schedulers {
		date := time.Now().AddDate(0, 0, int(schedulers[i].BeforeDay))
		var param SchedulerLaborContractParam
		err := json.Unmarshal(schedulers[i].Param, &param)
		if err != nil {
			log.ErrorFields("GetCanNotifyContract json.Unmarshal error", map[string]interface{}{"err": err, "param": schedulers[i].Param})
			continue
		}

		//获取符合条件的员工ID
		staffs, staffIds, _ := service.SelectOetStaffByMultiWhere(context.Background(), param.CorporationIds, service.OetWhere{WorkPostTypeArr: param.WorkPostTypeArr}, false, 0, 0)
		if len(staffIds) == 0 {
			log.ErrorFields("GetCanNotifyContract Get Oet Staff is nil", map[string]interface{}{"param": param})
			continue
		}

		//根据员工ID获取即将到期的合同
		var laborContract hrModel.StaffLaborContract
		contracts, _ := laborContract.GetBy(staffIds, 0, util.LaborContractValid, 0, time.Now(), date, model.Paginator{Limit: 0})
		if len(contracts) == 0 {
			log.ErrorFields("GetCanNotifyContract laborContract.GetBy not fund", map[string]interface{}{"staffIds": staffIds, "start": time.Now(), "end": date})
			continue
		}

		var expireStaffInfo []LaborContractStaffInfo
		for j := range contracts {
			staff := staffs[contracts[j].StaffId]
			expireStaffInfo = append(expireStaffInfo, LaborContractStaffInfo{
				StaffId:         staff.Id,
				Name:            staff.Name,
				JobNumber:       staff.StaffId,
				Phone:           staff.Phone,
				LaborContractId: contracts[j].Id,
				EndAt:           *(contracts[j].EndAt),
			})
		}

		//获取接收人信息
		var staffWorkPost hrModel.StaffHasWorkPost
		receivers := staffWorkPost.GetStaffIdByWorkPostId(param.WorkPostIds)

		receiveStaffs := rpc.GetStaffWithIds(context.Background(), receivers)
		var receiveStaffInfo []LaborContractStaffInfo
		for j := range receiveStaffs {
			receiveStaffInfo = append(receiveStaffInfo, LaborContractStaffInfo{
				StaffId:       receiveStaffs[j].Id,
				Name:          receiveStaffs[j].Name,
				JobNumber:     receiveStaffs[j].StaffId,
				Phone:         receiveStaffs[j].Phone,
				CorporationId: receiveStaffs[j].CorporationId,
			})
		}

		nowMinute := int64(time.Now().Hour()*60 + time.Now().Minute())
		var key string
		if nowMinute < schedulers[i].NotifyTime {
			key = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), int(schedulers[i].NotifyTime/60), int(schedulers[i].NotifyTime%60), 0, 0, time.Local).Format(model.TimeFormat)
		} else {
			key = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day()+1, int(schedulers[i].NotifyTime/60), int(schedulers[i].NotifyTime%60), 0, 0, time.Local).Format(model.TimeFormat)
		}
		notifyRecords[key] = append(notifyRecords[key], StaffContract{SendStaffs: expireStaffInfo, ReceiveStaffs: receiveStaffInfo})

	}

	for key := range notifyRecords {
		CanNotifyContractRecord.Write(key, notifyRecords[key])
	}
}

func SendNotifyForLaborContractExpire() {
	fmt.Printf("SendNotifyForLaborContractExpire start!! \n")
	if len(CanNotifyContractRecord.Records) == 0 {
		return
	}

	key := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute(), 0, 0, time.Local).Format(model.TimeFormat)

	records, ok := CanNotifyContractRecord.Read(key)
	if !ok {
		return
	}

	CanNotifyContractRecord.Remove(key)

	for i := range records {
		var staffNames []string
		for j := range records[i].SendStaffs {
			staffNames = append(staffNames, fmt.Sprintf("%s/%s", records[i].SendStaffs[j].Name, records[i].SendStaffs[j].JobNumber))
		}

		messageParam, _ := json.Marshal(records[i].SendStaffs)

		for j := range records[i].ReceiveStaffs {
			var title = "劳动合同即将到期提醒"
			//发送公众号消息
			var msg = messageService.OfficialAccountMsg{
				First:          title,
				Keywords:       []string{"以下员工的劳动合同即将到期", strings.Join(staffNames, "、")},
				Remark:         "请尽快登录ERP系统查看并处理",
				ReceiverMobile: records[i].ReceiveStaffs[j].Phone,
			}

			user := rpc.GetUserInfoByPhone(context.Background(), records[i].ReceiveStaffs[j].CorporationId, records[i].ReceiveStaffs[j].Phone)
			if user != nil {
				//发送ERP系统消息
				var message = messageModel.Message{
					Type:              (&hrModel.StaffLaborContract{}).ExpireMessageType(),
					Origin:            (&hrModel.StaffLaborContract{}).ExpireMessageType(),
					RelationTableName: (&hrModel.StaffLaborContract{}).TableName(),
					RelationParam:     messageParam,
					RecvUserId:        user.Id,
					RecvUserName:      user.Nickname,
					ReadStatus:        messageModel.UNREAD_1,
					ReadType:          messageModel.MESSAGE_READ_1,
					Kind:              messageModel.REMIND_2,
					Title:             title,
				}

				staffWithId := rpc.GetStaffWithId(context.Background(), message.RecvUserId)
				if staffWithId != nil {
					message.GroupId = staffWithId.TopCorporationId

				}
				messageService.NewSendMessage(message, messageService.WithWxMessage(msg.First, msg.Keywords, msg.Remark, "", msg.ReceiverMobile)).Send()

			}
			log.PrintFields("SendNotifyForLaborContractExpire end", map[string]interface{}{"err": nil})
		}
	}
}
