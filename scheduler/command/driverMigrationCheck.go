package command

import (
	"app/org/scs/erpv2/api/log"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/service"
)

func CheckDriverMigrationJob() {
	records := (&hrModel.DriverMigration{}).GetEnableMigrationRecord()
	if len(records) == 0 {
		return
	}

	for i := range records {
		err := service.UpdateDriverToOet(records[i])
		if err != nil {
			log.ErrorFields("CheckDriverMigrationJob service.UpdateDriverToOet error", map[string]interface{}{"err": err, "record": records[i]})
		}
	}
}
