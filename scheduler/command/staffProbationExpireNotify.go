package command

import (
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	messageModel "app/org/scs/erpv2/api/model/message"
	schedulerModel "app/org/scs/erpv2/api/model/scheduler"
	"app/org/scs/erpv2/api/service"
	messageService "app/org/scs/erpv2/api/service/message"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"
)

type ProbationStaffInfo struct {
	StaffId       int64
	Name          string
	JobNumber     string
	Phone         string
	ArchiveId     int64
	EndAt         model.LocalTime
	CorporationId int64
}

type StaffProbation struct {
	SendStaffs    []ProbationStaffInfo
	ReceiveStaffs []ProbationStaffInfo
}

type CanNotifyProbationStaff struct {
	sync.RWMutex
	Records map[string][]StaffProbation
}

var CanNotifyProbationStaffRecord CanNotifyProbationStaff

func (c *CanNotifyProbationStaff) Read(key string) ([]StaffProbation, bool) {
	c.RLock()
	value, ok := c.Records[key]
	c.RUnlock()
	return value, ok
}
func (c *CanNotifyProbationStaff) Write(key string, value []StaffProbation) {
	c.Lock()
	if c.Records == nil {
		c.Records = make(map[string][]StaffProbation)
	}
	c.Records[key] = value
	ddd, _ := json.Marshal(c.Records)
	fmt.Printf("CanNotifyProbationStaff Records==========%+v \n", string(ddd))
	c.Unlock()
}
func (c *CanNotifyProbationStaff) Remove(key string) {
	c.RLock()
	delete(c.Records, key)
	c.RUnlock()
}

type ScheduleProbationParam struct {
	WorkPostTypeArr []int64
	CorporationIds  []int64
	WorkPostIds     []int64
}

func GetCanNotifyStaffForProbationExpire() {
	fmt.Printf("GetCanNotifyStaffForProbationExpire start!! \n")

	var scheduler schedulerModel.Scheduler
	schedulers := scheduler.GetByScene((&hrModel.StaffArchive{}).ProbationExpireMessageType())
	if len(schedulers) == 0 {
		return
	}

	var notifyRecords = make(map[string][]StaffProbation)
	for i := range schedulers {
		date := time.Now().AddDate(0, 0, int(schedulers[i].BeforeDay))
		var param ScheduleProbationParam
		err := json.Unmarshal(schedulers[i].Param, &param)
		if err != nil {
			log.ErrorFields("GetCanNotifyStaffForProbationExpire json.Unmarshal error", map[string]interface{}{"err": err, "param": schedulers[i].Param})
			continue
		}

		//获取符合条件的员工ID
		staffs, staffIds, _ := service.SelectOetStaffByMultiWhere(context.Background(), param.CorporationIds, service.OetWhere{WorkPostTypeArr: param.WorkPostTypeArr, JobStatusArr: []int64{util.JobStatusProbation}}, false, 0, 0)
		if len(staffIds) == 0 {
			log.ErrorFields("GetCanNotifyStaffForProbationExpire Get Oet Staff is nil", map[string]interface{}{"param": param})
			continue
		}

		//根据员工ID获取试用期即将到期的员工
		var archive hrModel.StaffArchive
		archives := archive.GetProbationExpireStaff(staffIds, time.Now(), date)
		if len(archives) == 0 {
			log.ErrorFields("GetCanNotifyStaffForProbationExpire archive.GetProbationExpireStaff not fund", map[string]interface{}{"staffIds": staffIds, "start": time.Now(), "end": date})
			continue
		}

		var expireStaffInfo []ProbationStaffInfo
		for j := range archives {
			staff := staffs[archives[j].StaffId]
			expireStaffInfo = append(expireStaffInfo, ProbationStaffInfo{
				StaffId:   staff.Id,
				Name:      staff.Name,
				JobNumber: staff.StaffId,
				Phone:     staff.Phone,
				ArchiveId: archives[j].Id,
				EndAt:     *(archives[j].ProbationEndAt),
			})
		}

		//获取接收人信息
		var staffWorkPost hrModel.StaffHasWorkPost
		receivers := staffWorkPost.GetStaffIdByWorkPostId(param.WorkPostIds)

		receiveStaffs := rpc.GetStaffWithIds(context.Background(), receivers)
		var receiveStaffInfo []ProbationStaffInfo
		for j := range receiveStaffs {
			receiveStaffInfo = append(receiveStaffInfo, ProbationStaffInfo{
				StaffId:       receiveStaffs[j].Id,
				Name:          receiveStaffs[j].Name,
				JobNumber:     receiveStaffs[j].StaffId,
				Phone:         receiveStaffs[j].Phone,
				CorporationId: receiveStaffs[j].CorporationId,
			})
		}

		nowMinute := int64(time.Now().Hour()*60 + time.Now().Minute())
		var key string
		if nowMinute < schedulers[i].NotifyTime {
			key = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), int(schedulers[i].NotifyTime/60), int(schedulers[i].NotifyTime%60), 0, 0, time.Local).Format(model.TimeFormat)
		} else {
			key = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day()+1, int(schedulers[i].NotifyTime/60), int(schedulers[i].NotifyTime%60), 0, 0, time.Local).Format(model.TimeFormat)
		}

		notifyRecords[key] = append(notifyRecords[key], StaffProbation{SendStaffs: expireStaffInfo, ReceiveStaffs: receiveStaffInfo})
	}

	for key := range notifyRecords {
		CanNotifyProbationStaffRecord.Write(key, notifyRecords[key])
	}

}

func SendNotifyForStaffProbationExpire() {
	fmt.Printf("SendNotifyForStaffProbationExpire start!! \n")

	if len(CanNotifyProbationStaffRecord.Records) == 0 {
		return
	}

	key := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute(), 0, 0, time.Local).Format(model.TimeFormat)

	records, ok := CanNotifyProbationStaffRecord.Read(key)
	if !ok {
		return
	}
	//删除已发送的KEY
	CanNotifyProbationStaffRecord.Remove(key)

	for i := range records {
		var staffNames []string
		for j := range records[i].SendStaffs {
			staffNames = append(staffNames, fmt.Sprintf("%s/%s", records[i].SendStaffs[j].Name, records[i].SendStaffs[j].JobNumber))
		}

		messageParam, _ := json.Marshal(records[i].SendStaffs)

		for j := range records[i].ReceiveStaffs {
			var title = "试用期即将到期提醒"
			//发送公众号消息
			var msg = messageService.OfficialAccountMsg{
				First:          title,
				Keywords:       []string{"以下员工的试用期即将到期", strings.Join(staffNames, "、")},
				Remark:         "请尽快登录ERP系统查看并处理",
				ReceiverMobile: records[i].ReceiveStaffs[j].Phone,
			}
			user := rpc.GetUserInfoByPhone(context.Background(), records[i].ReceiveStaffs[j].CorporationId, records[i].ReceiveStaffs[j].Phone)
			if user != nil {
				//发送ERP系统消息
				var message = messageModel.Message{
					Type:              (&hrModel.StaffArchive{}).ProbationExpireMessageType(),
					Origin:            (&hrModel.StaffArchive{}).ProbationExpireMessageType(),
					RelationTableName: (&hrModel.StaffArchive{}).TableName(),
					RelationParam:     messageParam,
					RecvUserId:        user.Id,
					RecvUserName:      user.Nickname,
					ReadStatus:        messageModel.UNREAD_1,
					ReadType:          messageModel.MESSAGE_READ_1,
					Kind:              messageModel.REMIND_2,
					Title:             title,
				}
				staffWithId := rpc.GetStaffWithId(context.Background(), message.RecvUserId)
				if staffWithId != nil {
					message.GroupId = staffWithId.TopCorporationId

				}

				messageService.NewSendMessage(message, messageService.WithWxMessage(msg.First, msg.Keywords, msg.Remark, "", msg.ReceiverMobile)).Send()

			}

			log.PrintFields("SendNotifyForStaffProbationExpire end", map[string]interface{}{"err": nil})
		}
	}
}
