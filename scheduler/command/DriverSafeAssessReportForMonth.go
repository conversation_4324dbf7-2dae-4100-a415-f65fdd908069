package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/log"
	"app/org/scs/erpv2/api/model"
	hrModel "app/org/scs/erpv2/api/model/hr"
	"app/org/scs/erpv2/api/model/operation"
	safetyModel "app/org/scs/erpv2/api/model/safety"
	assessModel "app/org/scs/erpv2/api/model/safety/assess"
	settingModel "app/org/scs/erpv2/api/model/setting"
	"app/org/scs/erpv2/api/service"
	"app/org/scs/erpv2/api/service/rpc"
	"app/org/scs/erpv2/api/util"
	"context"
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"sort"
	"strconv"
	"time"
)

func AutoCalcDriverSafeAssessReportForMonth() {
	//查询所有司机
	staffs := rpc.GetDriversWithCorporationId(context.TODO(), config.Config.TopCorporationId, "")
	if len(staffs) == 0 {
		return
	}
	var end time.Time
	if time.Now().Day() <= 25 {
		end = time.Now().AddDate(0, -1, 0)
	} else {
		end = time.Now()
	}
	endAt := time.Date(end.Year(), end.Month(), 25, 23, 59, 59, 999, time.Local)
	start := endAt.AddDate(0, -1, 0)
	startAt := time.Date(start.Year(), start.Month(), 26, 0, 0, 0, 0, time.Local)

	for i := range staffs {
		var report = DriverSafeAssessReport{
			Staff: StaffArchiveInfo{
				StaffId:       staffs[i].Id,
				StaffName:     staffs[i].Name,
				CorporationId: staffs[i].CorporationId,
				LineId:        staffs[i].LineId,
			},
			StartAt: startAt,
			EndAt:   endAt,
		}
		month := endAt.Format("200601")
		report.Month, _ = strconv.ParseInt(month, 10, 64)
		staffArchive := (&hrModel.StaffArchive{}).FirstByStaffId(staffs[i].Id)
		if staffArchive.IsCctDriver == util.StatusForTrue {
			report.Staff.IsCctDriver = true
		} else {
			report.Staff.IsCctDriver = false
		}

		line, _ := rpc.GetLineWithId(context.TODO(), staffs[i].LineId)
		if line != nil {
			report.Staff.LineName = line.Name
		}

		report.ProcessJobMonth()
	}
}

func ManualCalcDriverSafeAssessReportForMonth(topCorporationId int64, at time.Time) {
	//查询所有司机
	staffs := rpc.GetDriversWithCorporationId(context.TODO(), topCorporationId, "")
	if len(staffs) == 0 {
		return
	}

	endAt := time.Date(at.Year(), at.Month(), 25, 23, 59, 59, 999, time.Local)
	start := endAt.AddDate(0, -1, 0)
	startAt := time.Date(start.Year(), start.Month(), 26, 0, 0, 0, 0, time.Local)

	for i := range staffs {
		if staffs[i].RegisterTime > endAt.Unix() {
			continue
		}

		var report = DriverSafeAssessReport{
			Staff: StaffArchiveInfo{
				StaffId:       staffs[i].Id,
				StaffName:     staffs[i].Name,
				CorporationId: staffs[i].CorporationId,
				LineId:        staffs[i].LineId,
			},
			StartAt: startAt,
			EndAt:   endAt,
		}
		month := endAt.Format("200601")
		report.Month, _ = strconv.ParseInt(month, 10, 64)
		staffArchive := (&hrModel.StaffArchive{}).FirstByStaffId(staffs[i].Id)
		if staffArchive.IsCctDriver == util.StatusForTrue {
			report.Staff.IsCctDriver = true
		} else {
			report.Staff.IsCctDriver = false
		}

		line, _ := rpc.GetLineWithId(context.TODO(), staffs[i].LineId)
		if line != nil {
			report.Staff.LineName = line.Name
		}

		report.ProcessJobMonth()

		//重新检查司机贡献奖金是否变化
		go report.recalcDriverDevoteMoney()
	}
}

type StaffArchiveInfo struct {
	StaffId       int64
	StaffName     string
	IsCctDriver   bool
	CorporationId int64
	LineId        int64
	LineName      string
}

type DriverSafeAssessReport struct {
	Staff   StaffArchiveInfo
	Month   int64
	Year    int64
	StartAt time.Time
	EndAt   time.Time
}

func (r *DriverSafeAssessReport) ProcessJobMonth() {
	//删除历史数据
	_ = (&assessModel.DriverViolationMonthAssessReportItem{}).Delete(r.Staff.StaffId, r.Month)
	_ = (&assessModel.DriverAccidentMonthAssessReportItem{}).Delete(r.Staff.StaffId, r.Month)
	_ = (&assessModel.DriverMonthAssessReport{}).Delete(r.Staff.StaffId, r.Month)

	//查询司机的违规数据
	violations := (&safetyModel.TrafficViolation{}).GetDriverAllViolation(r.Staff.StaffId, 0, r.StartAt, r.EndAt)

	var categoryViolationMap = make(map[int64][]safetyModel.QualityAssessmentStandards)
	for _, violation := range violations {
		var standards []safetyModel.QualityAssessmentStandards
		err := json.Unmarshal(violation.Standards, &standards)
		if err != nil {
			continue
		}
		for i := range standards {
			standards[i].ViolationId = violation.Id
			standards[i].ViolationReportAt = violation.ReportAt.ToTime().Unix()
			categoryViolationMap[standards[i].CategoryId] = append(categoryViolationMap[standards[i].CategoryId], standards[i])
		}
	}

	log.ErrorFields("========categoryViolationMap=======", map[string]interface{}{"categoryViolationMap": categoryViolationMap})

	var safeAssessCutMoney, specialAssessCutMoney, serviceAssessCutMoney int64
	var violationCutMoneyDetails []assessModel.DriverViolationMonthAssessReportItem
	for categoryId, standards := range categoryViolationMap {
		//将违规的标准按照违规发生时间升序排
		sort.Slice(standards, func(i, j int) bool {
			return standards[i].ViolationReportAt < standards[j].ViolationReportAt
		})

		log.ErrorFields("======categoryId, standards=====", map[string]interface{}{"categoryId": categoryId, "standards": standards})

		//查询违规类型对应的奖金基数
		category := (&safetyModel.ViolationCategory{}).FirstById(categoryId)
		assessCate := (&safetyModel.QualityAssessmentCate{}).FindBy(category.QualityAssessmentCateId)

		//查询违规类型的处罚配置
		punishSettings := (&safetyModel.ViolationCategoryPunishSetting{}).FirstBy(categoryId)
		var settings []safetyModel.ViolationCategoryPunishSettingItem
		if len(punishSettings) > 0 {
			for i := range punishSettings {
				if r.Staff.IsCctDriver {
					if punishSettings[i].DriverAttr == util.CctLine {
						settings = append(settings, punishSettings[i])
					}
				} else {
					if punishSettings[i].DriverAttr == util.RegularLine {
						settings = append(settings, punishSettings[i])
					}
				}
			}
		}
		var safeCutMoney, specialCutMoney, serviceCutMoney int64
		var details []assessModel.DriverViolationMonthAssessReportItem
		if assessCate.Attr == util.QualityAssessmentCateAttrForService {
			serviceCutMoney, details = r.calcServiceViolationCutMoney(standards, settings, categoryId, assessCate)
		} else {
			safeCutMoney, specialCutMoney, details = r.calcSafeViolationCutMoney(standards, settings, assessCate)
		}
		safeAssessCutMoney += safeCutMoney
		specialAssessCutMoney += specialCutMoney
		serviceAssessCutMoney += serviceCutMoney
		violationCutMoneyDetails = append(violationCutMoneyDetails, details...)
	}

	//计算天数 （实际工作天数）
	runDay := CalcDriverRunDay(r.Staff.StaffId, r.Staff.IsCctDriver, r.StartAt, r.EndAt)
	//计算平均计划天数
	avgPlanDay, mainLineId, mainLineName := CalcAvgPlanDay(r.Staff.StaffId, r.StartAt, r.EndAt)

	var assessRate float64
	//计算系数
	if avgPlanDay > 0 {
		//由于线路公里明细表存储的运营天数单位是天*10  所以这里要先/10 之后再运算
		assessRate = (decimal.NewFromInt(runDay).Div(decimal.NewFromInt(10))).Div(decimal.NewFromFloat(avgPlanDay)).Round(4).InexactFloat64()
	}
	if assessRate > 1 {
		assessRate = 1
	}

	//查询安全行为、专项考核、服务质量考核的奖金基数
	safeAssessCate := (&safetyModel.QualityAssessmentCate{}).GetAttrBaseReward(util.QualityAssessmentCateAttrForSafeAction)
	safeAssessBaseReward := safeAssessCate.LineBaseReward
	if r.Staff.IsCctDriver {
		safeAssessBaseReward = safeAssessCate.CctLineBaseReward
	}

	specialAssessCate := (&safetyModel.QualityAssessmentCate{}).GetAttrBaseReward(util.QualityAssessmentCateAttrForSpecial)
	specialAssessBaseReward := specialAssessCate.LineBaseReward
	if r.Staff.IsCctDriver {
		specialAssessBaseReward = specialAssessCate.CctLineBaseReward
	}

	serviceAssessCate := (&safetyModel.QualityAssessmentCate{}).GetAttrBaseReward(util.QualityAssessmentCateAttrForService)
	serviceAssessBaseReward := serviceAssessCate.LineBaseReward
	if r.Staff.IsCctDriver {
		serviceAssessBaseReward = serviceAssessCate.CctLineBaseReward
	}

	safeAssessReward := decimal.NewFromInt(safeAssessBaseReward).Mul(decimal.NewFromFloat(assessRate)).Sub(decimal.NewFromInt(safeAssessCutMoney)).IntPart()
	if safeAssessReward < 0 {
		safeAssessReward = 0
	}
	specialAssessReward := decimal.NewFromInt(specialAssessBaseReward).Mul(decimal.NewFromFloat(assessRate)).Sub(decimal.NewFromInt(specialAssessCutMoney)).IntPart()
	if specialAssessReward < 0 {
		specialAssessReward = 0
	}

	fmt.Printf("********serviceAssessBaseReward:%+v,%+v,%v", serviceAssessBaseReward, assessRate, serviceAssessCutMoney)

	serviceAssessReward := decimal.NewFromInt(serviceAssessBaseReward).Mul(decimal.NewFromFloat(assessRate)).Sub(decimal.NewFromInt(serviceAssessCutMoney)).IntPart()

	if serviceAssessReward < 0 {
		serviceAssessReward = 0
	}
	//计算事故考核
	accidentBaseReward, accidentCutMoney, accidentCutMoneyDetails := r.calcAccidentCutMoney()
	accidentAssessReward := decimal.NewFromInt(accidentBaseReward).Mul(decimal.NewFromFloat(assessRate)).Sub(decimal.NewFromInt(accidentCutMoney)).IntPart()
	if accidentAssessReward < 0 {
		accidentAssessReward = 0
	}
	var report = assessModel.DriverMonthAssessReport{
		StaffId:              r.Staff.StaffId,
		StaffName:            r.Staff.StaffName,
		LineId:               r.Staff.LineId,
		LineName:             r.Staff.LineName,
		MainLineId:           mainLineId,
		MainLineName:         mainLineName,
		ReportMonth:          r.Month,
		StartAt:              model.LocalTime(r.StartAt),
		EndAt:                model.LocalTime(r.EndAt),
		RunDay:               runDay * 10, //线路公里明细中天数单位是：天*10，这里只需要再*10即可
		AvgPlanDay:           decimal.NewFromFloat(avgPlanDay).Mul(decimal.NewFromInt(100)).IntPart(),
		AssessRate:           decimal.NewFromFloat(assessRate).Mul(decimal.NewFromInt(10000)).IntPart(),
		SafeAssessReward:     safeAssessReward,
		SpecialAssessReward:  specialAssessReward,
		ServiceAssessReward:  serviceAssessReward,
		AccidentAssessReward: accidentAssessReward,
	}

	report.Corporations.Build(r.Staff.CorporationId)
	if r.Staff.IsCctDriver {
		report.IsCctDriver = util.StatusForTrue
	} else {
		report.IsCctDriver = util.StatusForFalse
	}
	tx := model.DB().Begin()
	//创建
	err := report.Create(tx)
	if err != nil {
		tx.Rollback()
		log.ErrorFields("DriverSafeAssessReport DriverMonthAssessReport create error", map[string]interface{}{"err": err})
		return
	}

	if len(violationCutMoneyDetails) > 0 {
		for i := range violationCutMoneyDetails {
			violationCutMoneyDetails[i].ReportId = report.Id
		}

		err = (&assessModel.DriverViolationMonthAssessReportItem{}).Create(tx, violationCutMoneyDetails)
		if err != nil {
			log.ErrorFields("DriverSafeAssessReport DriverViolationMonthAssessReportItem create error", map[string]interface{}{"err": err})
		}
	}

	if len(accidentCutMoneyDetails) > 0 {
		for i := range accidentCutMoneyDetails {
			accidentCutMoneyDetails[i].ReportId = report.Id
		}

		err = (&assessModel.DriverAccidentMonthAssessReportItem{}).Create(tx, accidentCutMoneyDetails)
		if err != nil {
			log.ErrorFields("DriverSafeAssessReport DriverAccidentMonthAssessReportItem create error", map[string]interface{}{"err": err})
		}
	}

	tx.Commit()
}

func (r *DriverSafeAssessReport) calcSafeViolationCutMoney(standards []safetyModel.QualityAssessmentStandards, punishSettings []safetyModel.ViolationCategoryPunishSettingItem, cate safetyModel.QualityAssessmentCate) (int64, int64, []assessModel.DriverViolationMonthAssessReportItem) {
	if len(standards) == 0 || len(punishSettings) == 0 {
		return 0, 0, nil
	}

	equalRules := make(map[int64]safetyModel.ViolationCategoryPunishSettingItem)
	var greaterRules []safetyModel.ViolationCategoryPunishSettingItem

	// 分离规则到等于和大于两类
	for _, rule := range punishSettings {
		if rule.Symbol == 1 {
			equalRules[rule.MonthCount] = rule
		} else if rule.Symbol == 2 {
			greaterRules = append(greaterRules, rule)
		}
	}

	// 将大于类型的规则按数量降序排列，以便优先匹配最大的阈值
	sort.Slice(greaterRules, func(i, j int) bool {
		return greaterRules[i].MonthCount > greaterRules[j].MonthCount
	})

	var safeAssessCutMoney, specialAssessCutMoney int64
	var cutMoneyDetails []assessModel.DriverViolationMonthAssessReportItem
	// 遍历每一次违规，累计扣费金额
	for i := 1; i <= len(standards); i++ {
		standard := standards[i-1]
		var cutMoney int64
		if standard.IsDeductMoney == util.StatusForTrue {
			if item, ok := equalRules[int64(i)]; ok {
				if item.CheckType == util.ViolationAssessCheckTypeForPercent {
					percent := decimal.NewFromInt(item.Value).Div(decimal.NewFromInt(100)).Div(decimal.NewFromInt(100))
					var baseReward = cate.LineBaseReward
					if r.Staff.IsCctDriver {
						baseReward = cate.CctLineBaseReward
					}
					cutMoney = decimal.NewFromInt(baseReward).Mul(percent).IntPart()
				} else {
					cutMoney = item.Value
				}
			} else {
				// 寻找匹配的大于规则（最大的且小于当前次数的阈值）
				for _, gr := range greaterRules {
					if gr.MonthCount < int64(i) {
						if gr.CheckType == util.ViolationAssessCheckTypeForPercent {
							percent := decimal.NewFromInt(gr.Value).Div(decimal.NewFromInt(100)).Div(decimal.NewFromInt(100))
							var baseReward = cate.LineBaseReward
							if r.Staff.IsCctDriver {
								baseReward = cate.CctLineBaseReward
							}
							cutMoney = decimal.NewFromInt(baseReward).Mul(percent).IntPart()
						} else {
							cutMoney = gr.Value
						}
						break // 找到最大的有效规则后终止查找
					}
				}
			}

			if cate.Attr == util.QualityAssessmentCateAttrForSafeAction {
				safeAssessCutMoney += cutMoney
			}

			if cate.Attr == util.QualityAssessmentCateAttrForSpecial {
				specialAssessCutMoney += cutMoney
			}
		}

		cutMoneyDetails = append(cutMoneyDetails, assessModel.DriverViolationMonthAssessReportItem{
			StaffId:                   r.Staff.StaffId,
			ReportMonth:               r.Month,
			StartAt:                   model.LocalTime(r.StartAt),
			EndAt:                     model.LocalTime(r.EndAt),
			ViolationId:               standard.ViolationId,
			QualityAssessmentCateId:   cate.Id,
			QualityAssessmentCateAttr: cate.Attr,
			CategoryId:                standard.CategoryId,
			StandardId:                standard.Id,
			CutMoney:                  cutMoney,
			ReportAt:                  model.LocalTime(time.Unix(standard.ViolationReportAt, 0)),
		})
	}
	log.ErrorFields("======calcSafeViolationCutMoney cutMoneyDetails========", map[string]interface{}{"cutMoneyDetails": cutMoneyDetails})
	fmt.Printf("(calcSafeViolationCutMoney: %v,%v) \r", safeAssessCutMoney, specialAssessCutMoney)

	return safeAssessCutMoney, specialAssessCutMoney, cutMoneyDetails
}

func (r *DriverSafeAssessReport) calcServiceViolationCutMoney(standards []safetyModel.QualityAssessmentStandards, punishSettings []safetyModel.ViolationCategoryPunishSettingItem, categoryId int64, cate safetyModel.QualityAssessmentCate) (int64, []assessModel.DriverViolationMonthAssessReportItem) {
	if len(standards) == 0 || len(punishSettings) == 0 {
		return 0, nil
	}
	if r.Staff.StaffId == 6111 {
		log.ErrorFields("======standards========", map[string]interface{}{"standards": standards})
	}
	equalRules := make(map[int64]safetyModel.ViolationCategoryPunishSettingItem)
	var greaterRules []safetyModel.ViolationCategoryPunishSettingItem

	// 分离规则到等于和大于两类
	for _, rule := range punishSettings {
		if rule.Symbol == 1 {
			equalRules[rule.YearCount] = rule
		} else if rule.Symbol == 2 {
			greaterRules = append(greaterRules, rule)
		}
	}

	// 将大于类型的规则按数量降序排列，以便优先匹配最大的阈值
	sort.Slice(greaterRules, func(i, j int) bool {
		return greaterRules[i].YearCount > greaterRules[j].YearCount
	})

	var cutMoneyDetails []assessModel.DriverViolationMonthAssessReportItem
	//判断当月发生的属于这个分类下的服务违规是今年的第几次，根据第几次确定按哪个配置扣费，仅扣一次，不累计扣费
	//查询今年的服务违规记录数量
	startYearAt := time.Date(r.EndAt.Year()-1, 12, 26, 0, 0, 0, 0, time.Local)
	endYearAt := r.EndAt
	yearViolations := (&safetyModel.TrafficViolation{}).GetDriverAllViolation(r.Staff.StaffId, util.TrafficViolationCateAttrForService, startYearAt, endYearAt)
	if r.Staff.StaffId == 6111 {
		log.ErrorFields("======yearViolations========", map[string]interface{}{"yearViolations": yearViolations})
	}

	var validStandards []safetyModel.QualityAssessmentStandards
	for _, violation := range yearViolations {
		var allStandards []safetyModel.QualityAssessmentStandards
		err := json.Unmarshal(violation.Standards, &allStandards)
		if err != nil {
			continue
		}
		for _, standard := range allStandards {
			if standard.CategoryId == categoryId && standard.IsDeductMoney == util.StatusForTrue {
				validStandards = append(validStandards, standard)
			}
		}
	}
	if r.Staff.StaffId == 6111 {
		log.ErrorFields("======validStandards========", map[string]interface{}{"validStandards": validStandards})
	}

	currentYearTimes := len(validStandards)

	var currentMonthHasValid bool
	for _, standard := range standards {
		if standard.IsDeductMoney == util.StatusForTrue {
			currentMonthHasValid = true
			break
		}
	}

	if r.Staff.StaffId == 6111 {
		log.ErrorFields("======currentYearTimes========", map[string]interface{}{"currentYearTimes": currentYearTimes, "currentMonthHasValid": currentMonthHasValid})
	}

	if r.Staff.StaffId == 6111 {
		log.ErrorFields("======equalRules greaterRules========", map[string]interface{}{"equalRules": equalRules, "greaterRules": greaterRules})
	}
	var cutMoney int64
	if currentMonthHasValid {
		if item, ok := equalRules[int64(currentYearTimes)]; ok {
			if r.Staff.StaffId == 6111 {
				log.ErrorFields("======item111111111111111========", map[string]interface{}{"item": item})
			}
			if item.CheckType == util.ViolationAssessCheckTypeForPercent {
				percent := decimal.NewFromInt(item.Value).Div(decimal.NewFromInt(100)).Div(decimal.NewFromInt(100))
				var baseReward = cate.LineBaseReward
				if r.Staff.IsCctDriver {
					baseReward = cate.CctLineBaseReward
				}
				cutMoney = decimal.NewFromInt(baseReward).Mul(percent).IntPart()
			} else {
				cutMoney = item.Value
			}
		} else {
			if r.Staff.StaffId == 6111 {
				log.ErrorFields("======2222222222222222========", map[string]interface{}{})
			}
			// 寻找匹配的大于规则（最大的且小于当前次数的阈值）
			for _, gr := range greaterRules {
				if gr.YearCount < int64(currentYearTimes) {
					if r.Staff.StaffId == 6111 {
						log.ErrorFields("======gr1111111111111========", map[string]interface{}{"gr": gr})
					}
					if gr.CheckType == util.ViolationAssessCheckTypeForPercent {
						percent := decimal.NewFromInt(gr.Value).Div(decimal.NewFromInt(100)).Div(decimal.NewFromInt(100))
						var baseReward = cate.LineBaseReward
						if r.Staff.IsCctDriver {
							baseReward = cate.CctLineBaseReward
						}
						cutMoney = decimal.NewFromInt(baseReward).Mul(percent).IntPart()
					} else {
						cutMoney = gr.Value
					}
					break // 找到最大的有效规则后终止查找
				}
			}
		}
	}
	if r.Staff.StaffId == 6111 {
		log.ErrorFields("======cutMoney1111111111111========", map[string]interface{}{"cutMoney": cutMoney})
	}

	for _, standard := range standards {
		cutMoneyDetails = append(cutMoneyDetails, assessModel.DriverViolationMonthAssessReportItem{
			StaffId:                   r.Staff.StaffId,
			ReportMonth:               r.Month,
			StartAt:                   model.LocalTime(r.StartAt),
			EndAt:                     model.LocalTime(r.EndAt),
			ViolationId:               standard.ViolationId,
			QualityAssessmentCateId:   cate.Id,
			QualityAssessmentCateAttr: cate.Attr,
			CategoryId:                standard.CategoryId,
			StandardId:                standard.Id,
			CutMoney:                  cutMoney,
			ReportAt:                  model.LocalTime(time.Unix(standard.ViolationReportAt, 0)),
		})
	}

	log.ErrorFields("======calcServiceViolationCutMoney cutMoneyDetails========", map[string]interface{}{"cutMoneyDetails": cutMoneyDetails})
	fmt.Printf("(calcServiceViolationCutMoney: %v) \r", cutMoney)

	return cutMoney, cutMoneyDetails
}

func (r *DriverSafeAssessReport) calcAccidentCutMoney() (int64, int64, []assessModel.DriverAccidentMonthAssessReportItem) {
	//查询事故
	accidents := (&safetyModel.TrafficAccident{}).GetDriverAccident(r.Staff.StaffId, r.StartAt, r.EndAt)
	log.ErrorFields("=========accidents===========", map[string]interface{}{"accidents": accidents})
	//获取事故考核配置
	setting := (&settingModel.GlobalSetting{}).GetBy(config.Config.TopCorporationId, util.GlobalSettingTypeForAccidentAssess)

	var settingItem settingModel.GlobalSettingItemForAccidentAssess
	err := json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
	}

	accidentSetting := settingItem.Line
	if r.Staff.IsCctDriver {
		accidentSetting = settingItem.CctLine
	}

	var settingMap = make(map[string]settingModel.GlobalSettingItemForAccidentAssessArr)
	for _, st := range accidentSetting.Settings {
		key := fmt.Sprintf("%v_%v", st.IsPeopleHurt, st.LiabilityType)
		settingMap[key] = st
	}

	var cutMoney int64
	var cutDetails []assessModel.DriverAccidentMonthAssessReportItem
	for _, accident := range accidents {
		if accident.LiabilityType >= util.AccidentLiabilityTypeForPending || accident.LiabilityType <= util.AccidentLiabilityTypeForUnknown {
			accident.LiabilityType = util.AccidentLiabilityTypeForAll
		}
		var isPeopleHurt int64
		if accident.PeopleHurtCate == util.AccidentHurtStatus_1 {
			isPeopleHurt = util.StatusForFalse
		} else {
			isPeopleHurt = util.StatusForTrue
		}

		key := fmt.Sprintf("%v_%v", isPeopleHurt, accident.LiabilityType)

		detail := assessModel.DriverAccidentMonthAssessReportItem{
			StaffId:    r.Staff.StaffId,
			Month:      r.Month,
			StartAt:    model.LocalTime(r.StartAt),
			EndAt:      model.LocalTime(r.EndAt),
			AccidentId: accident.Id,
			HappenAt:   accident.HappenAt,
		}
		if st, ok := settingMap[key]; ok {
			cutMoney += st.Money
			detail.CutMoney = st.Money
		}
		cutDetails = append(cutDetails, detail)
	}

	return accidentSetting.BaseMoney, cutMoney, cutDetails
}

func CalcDriverRunDay(staffId int64, isCctDriver bool, startAt, endAt time.Time) int64 {
	runReportAts := (&operation.LineVehicleMileageReport{}).GetDriverRunReportAt(util.StatusForTrue, []int64{}, 0, 0, staffId, 0, startAt, endAt)

	reportTimeMap := make(map[string]int64)
	if runReportAts != nil {
		for _, v := range runReportAts {
			reportTimeMap[v] = 1
		}
	}
	//查询额外计算天数配置信息
	setting := (&settingModel.GlobalSetting{}).GetBy(config.Config.TopCorporationId, util.GlobalSettingTypeForExtraCalcDayCount)
	var settingItem settingModel.GlobalSettingItemForExtraCalcDayCount
	err := json.Unmarshal(setting.SettingItem, &settingItem)
	if err != nil {
		log.ErrorFields("setting.SettingItem json.Unmarshal error", map[string]interface{}{"error": err})
		return int64(len(reportTimeMap)) * 10
	}

	var lineItem = settingItem.Line
	if isCctDriver {
		lineItem = settingItem.CctLine
	}
	for _, item := range lineItem {
		otherReportTimeMap := getDriverExtraRunDay(staffId, startAt, endAt, item)
		for k, v := range otherReportTimeMap {
			reportTimeMap[k] = v
		}
	}
	return int64(len(reportTimeMap)) * 10
}

func getDriverExtraRunDay(staffId int64, startAt, endAt time.Time, settingItem settingModel.GlobalSettingItemForExtraCalcDayCountItem) map[string]int64 {
	var reportTimeMap = make(map[string]int64)
	if len(settingItem.LeaveTypes) > 0 {
		//请假表
		LeaveReportAts := (&hrModel.ApplyLeaveRecordDate{}).GetDriverLeaveRecordReportAt(staffId, settingItem.LeaveTypes, startAt, endAt)
		if LeaveReportAts != nil {
			for _, v := range LeaveReportAts {
				reportTimeMap[v] = 1
			}
		}
	}

	//班制外加班表
	if len(settingItem.OutFrequencyAddWorkTypes) > 0 {
		OutFrequencyAddWorkReportAts := (&operation.OutFrequencyAddWorkReport{}).GetReportAtByDriver(util.StatusForTrue, []int64{}, 0, 0, staffId, settingItem.OutFrequencyAddWorkTypes, startAt, endAt)
		if OutFrequencyAddWorkReportAts != nil {
			for _, v := range OutFrequencyAddWorkReportAts {
				reportTimeMap[v] = 1
			}
		}
	}

	if len(settingItem.IrregularLineLineAttrs) > 0 {
		//定制线路明细表
		IrregularLineReportReportAts := (&operation.IrregularLineReport{}).GetReportAtByDriver(util.StatusForTrue, []int64{}, 0, 0, staffId, settingItem.IrregularLineLineAttrs, startAt, endAt)
		if IrregularLineReportReportAts != nil {
			for _, v := range IrregularLineReportReportAts {
				reportTimeMap[v] = 1
			}
		}
	}

	return reportTimeMap
}

func CalcAvgPlanDay(staffId int64, startAt, endAt time.Time) (float64, int64, string) {
	//主营线路
	mainLine := service.GetDriverMainRunLineInfo(staffId, startAt, endAt)
	var avgPlanDay float64
	if mainLine.LineId == 0 {
		avgPlanDay = 22
	} else {
		//平均计划天数（基准天数）=主营线路总计划天数/计划司机数量
		//planDay := (&operation.PlanScheduleReport{}).GetLineTotalReportAtCount(mainLine.CorporationId, mainLine.LineId, 0, startAt, endAt)
		//planDriverCount := (&operation.PlanScheduleReport{}).GetPlanDriverCount(mainLine.CorporationId, mainLine.LineId, startAt, endAt)
		//
		//if planDay == 0 {
		//	avgPlanDay = 22
		//}
		//
		//if planDriverCount > 0 {
		//	avgPlanDay = decimal.NewFromInt(planDay).Div(decimal.NewFromInt(planDriverCount)).Round(2).InexactFloat64()
		//}
		//if avgPlanDay > 22 {
		//	avgPlanDay = 22
		//}
		baseDayConfig := (&operation.LineBaseDayConfig{}).FindLatest(mainLine.LineId)
		if baseDayConfig.Id == 0 {
			avgPlanDay = 22
		} else {
			avgPlanDay, _ = decimal.NewFromInt(baseDayConfig.BaseDay).Div(decimal.NewFromInt(10)).Float64()
		}
	}

	return avgPlanDay, mainLine.LineId, mainLine.LineName
}

// 重新计算司机月考核报表时，需要查询下司机的贡献奖中的出勤天数是否有变化，有变化则更新贡献奖金，没变化则不更新
func (r *DriverSafeAssessReport) recalcDriverDevoteMoney() {
	monthReport := (&assessModel.DriverDevoteMonthMoneyReport{}).GetDriverMonthReport(r.Staff.StaffId, r.Month)
	if monthReport.Id == 0 {
		return
	}

	runDay := CalcDriverRunDay(r.Staff.StaffId, r.Staff.IsCctDriver, r.StartAt, r.EndAt)

	avgPlanDay, mainLineId, mainLineName := CalcAvgPlanDay(r.Staff.StaffId, r.StartAt, r.EndAt)
	avgPlanDay100 := decimal.NewFromFloat(avgPlanDay).Mul(decimal.NewFromInt(100)).IntPart()
	//出勤天数、基准天数、主营线路有变化则需要重新计算
	if monthReport.RunDay == runDay*10 &&
		monthReport.AvgPlanDay == avgPlanDay100 &&
		monthReport.MainLineId == mainLineId {
		return
	}
	if monthReport.RunDay != runDay*10 || monthReport.AvgPlanDay != avgPlanDay100 {
		//计算系数
		var rate float64
		if avgPlanDay > 0 {
			rate = (decimal.NewFromInt(runDay).Div(decimal.NewFromInt(10))).Div(decimal.NewFromFloat(avgPlanDay)).Round(4).InexactFloat64()
		}
		if rate > 1 {
			rate = 1
		}
		//计算安全奖金
		monthReport.SafeDevoteMoney = decimal.NewFromInt(monthReport.SafeRewardBaseMoney).Mul(decimal.NewFromFloat(rate)).IntPart()

		//计算服务奖金
		monthReport.ServiceDevoteMoney = decimal.NewFromInt(monthReport.ServiceRewardBaseMoney).Mul(decimal.NewFromFloat(rate)).IntPart()

		monthReport.RunDay = runDay * 10
		monthReport.AvgPlanDay = avgPlanDay100
	}
	monthReport.MainLineId = mainLineId
	monthReport.MainLineName = mainLineName
	err := monthReport.UpdateColumns()
	if err != nil {
		log.ErrorFields("monthReport.Update error", map[string]interface{}{"error": err, "monthReport": monthReport})
	}
}
