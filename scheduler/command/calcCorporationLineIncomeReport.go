package command

import (
	"app/org/scs/erpv2/api/config"
	"app/org/scs/erpv2/api/service"
	"time"
)

func CalcCorporationLineIncome() {
	year, month, _ := time.Now().Date()
	reportMonth := time.Date(year, month, 1, 0, 0, 0, 0, time.Local)
	StartAt := time.Date(year, month-1, 26, 0, 0, 0, 0, time.Local)
	EndAt := time.Date(year, month, 25, 0, 0, 0, 0, time.Local)
	var calcReport = service.CalcCorporationLineIcReport{
		TopCorporationId: config.Config.TopCorporationId,
		CorporationId:    0,
		ReportMonth:      reportMonth,
		StartAt:          StartAt,
		EndAt:            EndAt,
	}

	go calcReport.Process()
}
